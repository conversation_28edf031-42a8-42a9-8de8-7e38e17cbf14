<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.sysuser.SysUserMapper">
    <select id="findPageList" parameterType="com.msun.csm.model.dto.user.SysUserDTO"
            resultType="com.msun.csm.model.vo.user.SysUserVO">
        select su.*,
        sd.dept_name
        from csm.sys_user su
        left join csm.sys_dept sd on su.dept_id = sd.sys_dept_id
        <where>
            <if test="userName != null and userName != ''">
                su.user_name like concat('%',#{userName},'%')
            </if>
        </where>
    </select>

    <select id="findRolesByUser" parameterType="com.msun.csm.model.dto.user.SysUserDTO"
            resultType="com.msun.csm.model.vo.user.UserVsRoleVO">
        select uvr.id,
               uvr.user_id,
               su.user_name,
               uvr.role_id,
               sr.role_name,
               sr.role_code,
               uvr.default_flag
        from csm.sys_role sr
                 left join csm.sys_user_vs_role uvr on
            sr.sys_role_id = uvr.role_id
                 left join csm.sys_user su on
            su.sys_user_id = uvr.user_id
        where uvr.is_deleted = 0
          and sr.is_deleted = 0
          and su.is_deleted = 0
          and su.sys_user_id = #{sysUserId}
    </select>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.sys_user
        (sys_user_id,
        user_yunying_id,
        user_name,
        account,
        password,
        email,
        phone,
        sex,
        dept_id,
        his_user_flag,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.sysUserId},
            #{item.userYunyingId},
            #{item.userName},
            #{item.account},
            #{item.password},
            #{item.email},
            #{item.phone},
            #{item.sex},
            #{item.deptId},
            #{item.hisUserFlag},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.sys_user
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_yunying_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userYunyingId != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.userYunyingId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userName != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.userName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="account = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.account != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.account}
                    </if>
                </foreach>
            </trim>
            <trim prefix="password = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.password != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.password}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.email != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.email}
                    </if>
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.phone}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sex = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sex != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.sex}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deptId != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.deptId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_user_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisUserFlag != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.hisUserFlag}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then #{item.isDeleted}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when sys_user_id = #{item.sysUserId,jdbcType=INTEGER} then
                        to_timestamp(#{item.updateTime},'yyyy-MM-DD hh24:mi:ss')
                    </if>
                </foreach>
            </trim>
        </trim>
        where sys_user_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.sysUserId}
        </foreach>
    </update>


    <select id="selectUserList" parameterType="com.msun.csm.model.dto.user.SysUserDTO"
            resultType="com.msun.csm.model.vo.user.SysUserVO">
        select
        su.* ,
        sd.dept_name as "deptName"
        from
        csm.sys_user su
        left join csm.sys_dept sd on
        su.dept_id = sd.dept_yunying_id
        and sd.is_deleted = 0
        where su.is_deleted = 0
        <if test="deptId != null and deptId != ''">
            and su.dept_id = #{deptId}
        </if>
        <if test="userName != null and userName != ''">
            and su.user_name like concat('%',#{userName},'%')
        </if>
    </select>
    <select id="selectUserByRoleId" resultType="com.msun.csm.dao.entity.SysUser" parameterType="long">
        select t.user_name, t.account, t.sys_user_id, t.user_yunying_id
        from csm.sys_user t
                 inner join csm.sys_user_vs_role t1
                            on t1.user_id = t.sys_user_id and t.is_deleted = 0
                 inner join csm.sys_role t2 on t2.sys_role_id = t1.role_id and t2.is_deleted = 0
        where t2.sys_role_id = #{roleId}
          and t1.is_deleted = 0
    </select>
    <select id="getDeptLeaderAccountByDeptId" resultType="com.msun.csm.dao.entity.SysUser"
            parameterType="long">
        select account
        from csm.sys_user t
                 inner join csm.sys_dept t1
                            on t1.dept_leader_yunying_id = t.user_yunying_id and t1.sys_dept_id = #{deptId}
    </select>
    <select id="getUserById" resultType="com.msun.csm.dao.entity.SysUser" parameterType="long">
        select *
        from csm.sys_user t
        where sys_user_id = #{userId}
    </select>
    <select id="getUserByIdByIsDeleted" resultType="com.msun.csm.dao.entity.SysUser" parameterType="long">
        select sys_user_id, user_name, phone, account, user_yunying_id
        from csm.sys_user t
        where sys_user_id = #{userId}
        and is_deleted in
        <foreach item="item" collection="isDeletedList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectUsersByDeptId" resultType="com.msun.csm.dao.entity.SysUser" parameterType="long">
        select account, sys_user_id
        from csm.sys_user t
        where dept_id = #{deptId, jdbcType=BIGINT}
    </select>

    <select id="selectUserByYYDeptId" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user t
        where dept_id = #{deptId}
          and is_deleted = 0
    </select>

    <select id="selectUserIdByYungyingId" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user t
        where t.user_yunying_id = #{yunyingId,jdbcType=VARCHAR}
          and t.is_deleted = 0
    </select>

    <select id="selectByAccount" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user t
        where t.account = #{account}
          and is_deleted = 0
    </select>

    <select id="selectUserByPage" parameterType="com.msun.csm.model.dto.user.SysUserDTO"
            resultType="com.msun.csm.model.vo.user.SysUserPageVO">
        select distinct su.* from csm.sys_user su
        <if test=" sysRoleId != null">
            left join csm.sys_user_vs_role suvr on su.sys_user_id = suvr.user_id
        </if>
        <where>
            su.is_deleted = 0
            <if test="sysRoleId != null">
                and suvr.is_deleted = 0
            </if>
            <if test="userName != null and userName != ''">
                and (su.user_name like concat('%',#{userName},'%') or su.account like concat('%',#{userName},'%'))
            </if>
            <if test="sysRoleId != null">
                and suvr.role_id = #{sysRoleId}
            </if>
        </where>
    </select>

    <select id="selectAllUser" parameterType="com.msun.csm.model.dto.user.SysUserDTO"
            resultType="com.msun.csm.dao.entity.SysUser">
        select su.* from csm.sys_user su
        <where>
            su.account = #{account}
        </where>
    </select>
    <select id="selectUserIdByAccount" resultType="java.lang.Long">
        select sys_user_id
        from csm.sys_user
        where account in ('liuzhongzhi', 'wanghaihua', 'zhangdi1')
    </select>


    <select id="selectUserByImspUserId" resultType="com.msun.csm.dao.entity.SysUser" parameterType="long">
        select t.*
        from csm.sys_user t
                 left join platform.sys_user p on t.user_yunying_id = p.user_yunying_id::varchar and p.status = 'ENABLE'
        where t.is_deleted = 0
          and p.user_id = #{imspUserId}
        limit 1
    </select>

    <select id="selectUserIdByYungyingIdIgnoreDeleted" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user t
        where t.user_yunying_id = #{yunyingId,jdbcType=VARCHAR}
        order by update_time desc
        limit 1
    </select>

    <update id="mfaRemoveData" parameterType="string">
        update csm.sys_user
        set secret_key = null
        where account = #{account}
    </update>
    <update id="updateDataById">
        update csm.sys_user
        set sys_user_id = #{newId}
        where sys_user_id = #{oldId}
    </update>
    <update id="updateUserRole">
        INSERT INTO csm.sys_user_vs_role
        (id, user_id, role_id, default_flag, is_deleted, creater_id, create_time, updater_id, update_time)

        select su.sys_user_id + floor(random() * (100000 - 10000 + 1)) + 10000,
               su.sys_user_id,
               (select sys_role_id
                from csm.sys_role sr
                where sr.role_code = 'OperationsEngineer' and is_deleted = 0
                limit 1),
               0,
               0,
               -1,
               now(),
               -1,
               now()
        from csm.sys_user su
                 left join
             (SELECT suvr.user_id, sr.sys_role_id
              FROM csm.sys_user_vs_role suvr
                       inner join csm.sys_role sr
                                  on suvr.role_id = sr.sys_role_id and sr.role_code = 'OperationsEngineer'
              where suvr.is_deleted = 0
                and sr.is_deleted = 0) sur on su.sys_user_id = sur.user_id
        where su.dept_id = 10300167
          and su.is_deleted = 0
          and sur.user_id is null
    </update>

    <select id="selectSysUsersByRoleIds" resultType="java.lang.Long">
        select distinct (su.sys_user_id)
        from csm.sys_user_vs_role sur
        left JOIN csm.sys_user su on su.sys_user_id = sur.user_id
        where
        role_id in
        <foreach item="item" collection="roleIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and su.is_deleted = 0
        and sur.is_deleted = 0
    </select>
    <select id="getUserVluesByYunyingId" resultType="com.msun.csm.dao.entity.SysUser"
            parameterType="java.lang.String">
        select distinct su.*
        from csm.sys_user su
                 inner join csm.sys_user_vs_role sur on su.sys_user_id = sur.user_id
                 inner join csm.sys_role sr on sur.role_id = sr.sys_role_id
                 left join (SELECT unnest(REGEXP_SPLIT_TO_ARRAY(config_value, ','))::varchar AS config_value
                            FROM csm.sys_config
                            WHERE config_code = 'role_update_project_member') sc on sr.role_code = sc.config_value
        where su.user_yunying_id = #{userYunyingId}
          and case when sc.config_value is null then sr.role_code = '1' else 1 = 1 end
    </select>
    <select id="selectByFeedbackId" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user su
                 inner join platform.sys_user p on p.user_yunying_id = su.user_yunying_id::int8
                 inner join knowledge.know_question_feedback f on f.accept_id_hd = p.user_id
        where su.is_deleted = 0
          and p.status = 'ENABLE'
          and f.id = #{feedbackId}
        limit 1
    </select>
    <select id="selectSendSysUserIds" resultType="java.lang.Long" parameterType="java.lang.Long">
        select project_leader_id
        from csm.sys_user su
                 inner join csm.proj_project_info ppi on su.sys_user_id = ppi.project_leader_id
        where ppi.project_info_id = #{projectInfoId}

        union

        select su2.sys_user_id
        from csm.sys_user su
                 inner join csm.proj_project_info ppi on su.sys_user_id = ppi.project_leader_id
                 inner join csm.sys_dept sd on sd.dept_yunying_id = su.dept_id
                 left join csm.sys_dept sd2 on sd2.dept_yunying_id = sd.pid
                 left join csm.sys_user su2 on su2.user_yunying_id = sd2.dept_leader_yunying_id
        where ppi.project_info_id = #{projectInfoId}
        union
        SELECT suvr.user_id
        FROM csm.sys_role x
                 inner join csm.sys_user_vs_role suvr on x.sys_role_id = suvr.role_id and suvr.is_deleted = 0
                 inner join csm.sys_user su on suvr.user_id = su.sys_user_id and su.is_deleted = 0
        where x.role_code in ('1', '11', '21')


    </select>

    <select id="selectBySysUserId" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user su
        where su.is_deleted = 0
          and su.sys_user_id = #{sysUserId}
    </select>

    <select id="selectByRoleCode" resultType="com.msun.csm.dao.entity.SysUser">

        SELECT su.*
        FROM csm.sys_role x
                 inner join csm.sys_user_vs_role suvr on x.sys_role_id = suvr.role_id and suvr.is_deleted = 0
                 inner join csm.sys_user su on suvr.user_id = su.sys_user_id and su.is_deleted = 0
        where x.role_code = #{roleCode}
    </select>

    <select id="selectListByUserId">
        select count(*)
        from csm.sys_user su
        where su.is_deleted = 0
          and su.sys_user_id = #{sysUserId}
          and su.dept_id in (WITH RECURSIVE dept_parents AS (SELECT dept_yunying_id, pid, dept_name
                                                             FROM csm.sys_dept
                                                             WHERE dept_yunying_id = 10300167

                                                             UNION ALL

                                                             SELECT d.dept_yunying_id, d.pid, d.dept_name
                                                             FROM csm.sys_dept d
                                                                      INNER JOIN dept_parents dp ON d.pid = dp.dept_yunying_id)
                             SELECT dept_yunying_id
                             FROM dept_parents)
    </select>
</mapper>
