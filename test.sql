create table public.bd_import_msun_cloud_record
(
    id             bigserial
        primary key,
    project_number varchar(255) not null,
    hospital_id    bigint       not null,
    job_name       varchar(255) not null,
    uuid           varchar(255) not null,
    status         integer   default 0,
    errors         int4      default 0,
    err_msg        text,
    is_deleted     smallint  default 0,
    creater_id     bigint    default '-1'::integer,
    create_time    timestamp default CURRENT_TIMESTAMP,
    updater_id     bigint    default '-1'::integer,
    update_time    timestamp default CURRENT_TIMESTAMP
);

comment on table public.bd_import_msun_cloud_record is '基础数据导入平台记录表';

comment on column public.bd_import_msun_cloud_record.project_number is '项目编号';

comment on column public.bd_import_msun_cloud_record.hospital_id is '医院ID';

comment on column public.bd_import_msun_cloud_record.job_name is '作业名称';

comment on column public.bd_import_msun_cloud_record.uuid is 'UUID';

comment on column public.bd_import_msun_cloud_record.status is '0待执行 1执行中 2成功 3失败';

comment on column public.bd_import_msun_cloud_record.err_msg is '导入错误信息';

comment on column public.bd_import_msun_cloud_record.errors is '总数据量';

comment on column public.bd_import_msun_cloud_record.is_deleted is '逻辑删除【0：否；1：是】';

comment on column public.bd_import_msun_cloud_record.creater_id is '创建人员ID';

comment on column public.bd_import_msun_cloud_record.create_time is '创建时间';

comment on column public.bd_import_msun_cloud_record.updater_id is '更新人员ID';

comment on column public.bd_import_msun_cloud_record.update_time is '更新时间';

