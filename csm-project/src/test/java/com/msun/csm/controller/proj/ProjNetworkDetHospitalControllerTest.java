package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetHospitalDTO;
import com.msun.csm.service.proj.ProjNetworkDetHospitalService;

class ProjNetworkDetHospitalControllerTest extends BaseTest {

    @Resource
    private ProjNetworkDetHospitalService service;

    @Test
    void updateFrontMachine() {
        ProjNetworkDetHospitalDTO dto = new ProjNetworkDetHospitalDTO();
        dto.setExternalSshPort("2323");
        dto.setIntranetSshPort("3232");
        dto.setId("457980319613407232");
        dto.setProjectInfoId("454622997323141120");
        System.out.println(service.updateFrontMachineWithLinux(dto));
    }

    @Test
    void testUpdateFrontMachine() {

    }
}