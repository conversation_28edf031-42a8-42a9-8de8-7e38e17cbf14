package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.service.proj.ProjMilestoneInfoService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjMilestoneInfoControllerTest extends BaseTest {

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Test
    void compMilestoneSpetial() {
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(479991695046205442L);
        updateMilestoneDTO.setMilestoneStatus(1);
        milestoneInfoService.compMilestoneSpetial(updateMilestoneDTO);
    }
}