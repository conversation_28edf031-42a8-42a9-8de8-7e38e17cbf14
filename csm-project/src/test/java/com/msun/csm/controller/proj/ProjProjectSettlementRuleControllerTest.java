package com.msun.csm.controller.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleDelFileDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleQueryDTO;
import com.msun.csm.service.proj.ProjProjectSettlementRuleService;

@Slf4j
class ProjProjectSettlementRuleControllerTest extends BaseTest {

    @Resource
    private ProjProjectSettlementRuleService projectSettlementRuleService;

    @Test
    void delSettlementFile() {
        ProjProjectSettlementRuleDelFileDTO dto = new ProjProjectSettlementRuleDelFileDTO();
        dto.setProjectInfoId("464478840982343680");
        dto.setProjectRuleCode("SETTLEMENT_CLOUD_CONFIRM_FORM");
        Result result = projectSettlementRuleService.delSettlementFile(dto);
        log.info(result.toString());
    }
}