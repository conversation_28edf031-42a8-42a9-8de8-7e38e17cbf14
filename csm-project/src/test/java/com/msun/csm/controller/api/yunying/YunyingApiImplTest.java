package com.msun.csm.controller.api.yunying;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.api.yunwei.CloudTypeEnum;
import com.msun.csm.model.imsp.SyncYunInfoDTO;
import com.msun.csm.service.yunying.YunYingService;

@Slf4j
class YunyingApiImplTest extends BaseTest {

    @Resource
    private YunYingService yunYingService;

    @Test
    void syncYunInfo() {
        SyncYunInfoDTO dto = SyncYunInfoDTO.builder()
                .type(CloudTypeEnum.CLOUD_RESOURCE.getOrderType())
                .customerId(659L)
                .pemcusssolType(1)
                .yyWoId(112771L)
                .subscribeTerm(12)
//                .planStartTime(DateUtils.parseDate("2024-11-06 00:00:00"))
//                .subscribeEndTime(DateUtils.parseDate("2025-11-05 00:00:00"))
                .build();
        yunYingService.syncCloudInfo(dto);
    }
}