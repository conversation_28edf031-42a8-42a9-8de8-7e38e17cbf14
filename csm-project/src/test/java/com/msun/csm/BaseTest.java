package com.msun.csm;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.msun.csm.dao.entity.proj.EarlyWarningAndPenaltyMessages;
import com.msun.csm.model.req.project.GenerateEarlyWarningAndPenaltyMessageArgs;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigReq;
import com.msun.csm.model.resp.qywechat.UrlTokenResp;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.wechat.IWechatUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.text.ParseException;
import java.util.*;

@Slf4j
@SpringBootTest(classes = CsmApplication.class)
public class BaseTest {

    @Autowired
    private IWechatUserService iWechatUserService;
    @Autowired
    private SendMessageService sendMessageService;

    @Test
    void cronTest() throws ParseException {
        String cronExpr = "0 0 * * * ?";
        Date now = new Date();
        Date timeAfter = new CronExpression(cronExpr).getTimeAfter(now);
        String nowStr = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
        String format = DateUtil.format(timeAfter, "yyyy-MM-dd HH:mm:ss");

        log.info("now: {}, after: {}", nowStr, format);
    }

    @Test
    void testVx() {
        UrlTokenResp urlTokenResp = iWechatUserService.redirectUrl(
                StrUtil.format("{}confirmPlanOnlineTime?projectInfoId={}", "https://imsp-test.msuncloud.com/csm-front-mobile/", "488062084736569345"));
        sendMessageService.sendEnterpriseWeChatMessageForOnePeople("填报计划上线时间", StrUtil.format("请确认您的项目【{}】计划上线时间是否有变化", "黑龙江康沅专科门诊有限公司项目"), Collections.singletonList("guijie"), urlTokenResp.getUrl(), null);
    }

    @Test
    void testJson() {

        Map<String, Object> map = new HashMap<>();
        map.put("memory", "4343");
        String jsonString = JSON.toJSONString(map);

        ProjHospitalTerminalConfigReq req = JSON.parseObject(jsonString, ProjHospitalTerminalConfigReq.class);
        System.out.println(req);
    }

    @Test
    void timeTest() {
        String t1 = "08:30";
        String t2 = "09:00";

        long l = DateUtil.betweenMs(DateUtil.parse(t2, "HH:mm"), DateUtil.parse(t1, "HH:mm"));
        System.out.println(l);
    }

    @Autowired
    SendBusinessMessageService sendBusinessMessageService;

    @Test
    void rerer() {
        GenerateEarlyWarningAndPenaltyMessageArgs args = new GenerateEarlyWarningAndPenaltyMessageArgs();
        args.setReviewTypeCode("tjbbcd");
        args.setProjectInfoId(559443641645236224L);
        args.setBusinessId(559443641645236224L);
        args.setBusinessTable("proj_project_info");
        args.setBusinessName("测试业务名称");
        args.setPenaltyPersonId(464993509692100610L);
        args.setPenaltyReason("测试处罚原因");
        args.setPenaltyStartTime(new Date());
        List<EarlyWarningAndPenaltyMessages> earlyWarningAndPenaltyMessages = sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage(args);
        System.out.println(earlyWarningAndPenaltyMessages);
    }

}
