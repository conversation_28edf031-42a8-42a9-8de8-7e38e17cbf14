package com.msun.csm.dao.mapper.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.model.dto.AimsEquipSelectDTO;
import com.msun.csm.model.vo.ProjEquipRecordVsAimsVO;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjEquipRecordVsAimsMapperTest extends BaseTest {

    @Resource
    private ProjEquipRecordVsAimsMapper equipRecordVsAimsMapper;

    @Test
    void selectEquipRecordVsAimsList() {
        AimsEquipSelectDTO selectDTO = new AimsEquipSelectDTO();
        selectDTO.setRequiredFlag(NumberEnum.NO_1.num());
        selectDTO.setProjectInfoId(513003555524661249L);
        List<ProjEquipRecordVsAimsVO> vsAimsVOS = equipRecordVsAimsMapper.selectEquipRecordVsAimsList(selectDTO);
        log.info(JSONUtil.toJsonStr(vsAimsVOS));
    }
}