package com.msun.csm.dao.mapper.proj;

import cn.hutool.core.bean.BeanUtil;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjNetworkDetClient;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetClientDTO;

class ProjNetworkDetClientMapperTest extends BaseTest {

    @Resource
    private ProjNetworkDetClientMapper networkDetClientMapper;

    @Test
    void findOtherClientNotThisDet() {
        ProjNetworkDetClientDTO networkDetClientDTO = new ProjNetworkDetClientDTO();
        networkDetClientDTO.setHospitalInfoId("12");
        networkDetClientDTO.setRandomCode("2323");
        ProjNetworkDetClient client = BeanUtil.copyProperties(networkDetClientDTO, ProjNetworkDetClient.class);
        List<ProjNetworkDetClient> list = networkDetClientMapper.findClient(client);
    }
}