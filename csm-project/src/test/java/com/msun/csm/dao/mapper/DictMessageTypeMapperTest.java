package com.msun.csm.dao.mapper;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.sys.DictMessageType;
import com.msun.csm.dao.mapper.dict.DictMessageTypeMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class DictMessageTypeMapperTest extends BaseTest {

    @Resource
    private DictMessageTypeMapper dictMessageTypeMapper;

    @Test
    void selectById() {
        DictMessageType type = dictMessageTypeMapper.selectById(1001);
        log.info(type.toString());
    }

    @Test
    void testSelectById() {

    }
}
