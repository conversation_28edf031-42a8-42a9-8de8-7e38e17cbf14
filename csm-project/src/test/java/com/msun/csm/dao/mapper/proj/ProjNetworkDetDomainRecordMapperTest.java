package com.msun.csm.dao.mapper.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjNetworkDetDnsRecord;
import com.msun.csm.dao.entity.proj.ProjNetworkDetDomainRecord;
import com.msun.csm.model.param.ProjNetworkDetDnsRecordParam;
import com.msun.csm.model.param.ProjNetworkDetDomainRecordParam;

@Slf4j
class ProjNetworkDetDomainRecordMapperTest extends BaseTest {


    @Resource
    private ProjNetworkDetDomainRecordMapper domainRecordMapper;

    @Test
    void findNetworkDetDomainInfoList() {
        ProjNetworkDetDomainRecordParam param = new ProjNetworkDetDomainRecordParam();
        param.setLogId(1L);
        param.setDetectStartTime(new Date());
        param.setLocalIpAddress("12");
        param.setRandomCode(1L);
        List<ProjNetworkDetDomainRecord> list = domainRecordMapper.findNetworkDetDomainInfoList(param);
        for (ProjNetworkDetDomainRecord record : list) {
            log.info(record.toString());
        }
    }

    @Test
    void deleteByLocalIpAndLogId() {
        domainRecordMapper.deleteByLocalIpAndLogId(1L, "32");
    }
}