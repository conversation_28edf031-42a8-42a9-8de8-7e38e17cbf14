package com.msun.csm.dao.mapper.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjNetworkDetDnsRecord;
import com.msun.csm.model.param.ProjNetworkDetDnsRecordParam;

@Slf4j
class ProjNetworkDetDnsRecordMapperTest extends BaseTest {

    @Resource
    private ProjNetworkDetDnsRecordMapper recordMapper;

    @Test
    void findNetworkDetDnsInfoList() {
        ProjNetworkDetDnsRecordParam param = new ProjNetworkDetDnsRecordParam();
        param.setLogId(1L);
        param.setDetectStartTime(new Date());
        param.setLocalIpAddress("12");
        param.setRandomCode(1L);
        List<ProjNetworkDetDnsRecord> list = recordMapper.findNetworkDetDnsInfoList(param);
        for (ProjNetworkDetDnsRecord record : list) {
            log.info(record.toString());
        }
    }
}