package com.msun.csm.dao.mapper.proj;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Collections;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospital;

class ProjNetworkDetHospitalMapperTest extends BaseTest {

    @Resource
    private ProjNetworkDetHospitalMapper networkDetHospitalMapper;

    @Test
    void findDistinctDomainByHospitalInfoIds() {
        networkDetHospitalMapper.findDistinctDomainByHospitalInfoIds(Collections.singletonList(1L));
    }

    @Test
    void updateByHospitalInfoId() {
        ProjNetworkDetHospital networkDetHospital = new ProjNetworkDetHospital();
        networkDetHospital.setSysType(1);
        networkDetHospital.setCallCloudStatus(1);
        networkDetHospital.setHospitalInfoId(862058296756543488L);
        networkDetHospitalMapper.updateByHospitalInfoId(networkDetHospital);
    }
}