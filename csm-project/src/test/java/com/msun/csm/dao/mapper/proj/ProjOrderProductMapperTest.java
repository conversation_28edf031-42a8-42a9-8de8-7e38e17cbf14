package com.msun.csm.dao.mapper.proj;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;

class ProjOrderProductMapperTest extends BaseTest {

    @Resource
    private ProjOrderProductMapper projOrderProductMapper;

    @Test
    void updateBatchByYyOrderProductIdsAndProjectInfoId() {
        projOrderProductMapper.updateBatchByYyOrderProductIdsAndProjectInfoId(ProductOpenStatusEnum.OPENED.getCode(), Collections.singletonList(1L), 1L);
    }

    @Test
    void findDictProductByProjectInfoId() {
        projOrderProductMapper.findDictProductByProjectInfoId(1L);
    }

    @Test
    void updateBatchSelective() {
        List<ProjOrderProduct> orderProductList = new ArrayList<>();
        ProjOrderProduct orderProduct = new ProjOrderProduct();
        orderProduct.setProductSubscribeTime(new Date());
//        orderProduct.setUpdateTime(new Date());
        orderProduct.setOrderProductId(475365795319992325L);
        orderProductList.add(orderProduct);
        projOrderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("product_subscribe_time", orderProduct.getProductSubscribeTime()));
        projOrderProductMapper.updateByIds(orderProductList);
//        projOrderProductMapper.updateBatchSelective()
    }

    @Test
    void updateExcutionStatusByIds() {
    }
}