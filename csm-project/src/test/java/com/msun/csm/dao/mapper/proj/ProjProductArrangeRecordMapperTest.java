package com.msun.csm.dao.mapper.proj;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProductInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProductArrangeRecordMapperTest extends BaseTest {

    @Resource
    private ProjProductArrangeRecordMapper productArrangeRecordMapper;

    @Test
    void findByYyOrderProductIdList() {
        List<ProductInfo> products = new ArrayList<>();
        ProductInfo product = new ProductInfo();
        product.setYyOrderProductId(1L);
        ProductInfo product2 = new ProductInfo();
        product2.setYyOrderProductId(12L);
        products.add(product);
        products.add(product2);
        productArrangeRecordMapper.findByYyOrderProductIdList(products, 3L);
    }
}