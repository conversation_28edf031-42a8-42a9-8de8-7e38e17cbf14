package com.msun.csm.dao.mapper.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.projsettlement.ProjProjectSettlementCheckSale;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleDTO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementMapperTest extends BaseTest {

    @Resource
    private ProjProjectSettlementMapper settlementMapper;

    @Test
    void findCloudServiceOrder() {
        ProjProjectSettlementCheckSaleDTO settlementCheckSaleDTO = new ProjProjectSettlementCheckSaleDTO();
        ProjProjectSettlementCheckSale settlementCheckSale = BeanUtil.copyProperties(settlementCheckSaleDTO, ProjProjectSettlementCheckSale.class);
//        List<ProjCustomCloudServiceSale> services = settlementMapper.findCloudServiceOrder(settlementCheckSale);
//        log.info(services.toString());
    }

    @Test
    void selectBailingAppOpenStatus() {
        List<Integer> list = settlementMapper.selectBailingAppOpenStatus(23097L, CollUtil.newArrayList(7062L));
        log.info("list.size: {}", list.size());
    }
}