package com.msun.csm.dao.mapper.oldimsp;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class OldCustomerInfoMapperTest {

    @Resource
    private OldCustomerInfoMapper oldCustomerInfoMapper;

    @Test
    void updateBatchByHospitalInfoListByCsmHospitalId() {
        OldCustomerInfo oldCustomerInfo = new OldCustomerInfo();
        oldCustomerInfo.setEnvId(1L);
        oldCustomerInfo.setEnvName("环境");
        oldCustomerInfo.setEnvStatus(StrUtil.toString(HospitalOpenStatusEnum.OPENED.getOldCode()));
        List<Long> hosIds = CollUtil.toList(851918808800100352L);
        int count = oldCustomerInfoMapper.updateBatchByHospitalInfoListByCsmHospitalId(oldCustomerInfo, hosIds);
        log.info(count + "");
    }
}