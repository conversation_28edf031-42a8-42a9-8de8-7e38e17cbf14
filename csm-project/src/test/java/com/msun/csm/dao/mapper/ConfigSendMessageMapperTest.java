package com.msun.csm.dao.mapper;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.sys.ConfigSendMessage;
import com.msun.csm.dao.mapper.config.ConfigSendMessageMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ConfigSendMessageMapperTest extends BaseTest {

    @Resource
    private ConfigSendMessageMapper configSendMessageMapper;

    @Test
    void selectById() {
        ConfigSendMessage message = configSendMessageMapper.selectById(3355991591171921155L);
        log.info(message.toString());
    }

    @Test
    void testSelectById() {
    }
}
