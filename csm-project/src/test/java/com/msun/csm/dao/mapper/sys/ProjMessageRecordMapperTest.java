package com.msun.csm.dao.mapper.sys;

import java.util.Date;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.sys.ProjMessageRecord;
import com.msun.csm.dao.mapper.proj.ProjMessageRecordMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;

class ProjMessageRecordMapperTest extends BaseTest {

    @Resource
    private ProjMessageRecordMapper projMessageRecordMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Test
    void insertBatch() {
        ProjMessageRecord projMessageRecord = new ProjMessageRecord();
        projMessageRecord.setId(SnowFlakeUtil.getId());
//        projMessageRecord.setMessageToId(12L);
        projMessageRecord.setMessageToCategory(1);
        projMessageRecord.setMessageTitle("tit");
        projMessageRecord.setMessageTypeId(1L);
        projMessageRecord.setMessageContent("con");
        projMessageRecord.setMessageContentParam("url");
        projMessageRecord.setSourceProjectInfoId(1L);
        projMessageRecord.setSourceUserId(21L);
        projMessageRecord.setExpiredTime(DateUtil.offsetMinute(new Date(), 5));
        projMessageRecordMapper.insert(projMessageRecord);
    }

    @Test
    void getUser() {
        sysUserMapper.getUserByIdByIsDeleted(1L, CollUtil.newArrayList(1, 0));
    }
}
