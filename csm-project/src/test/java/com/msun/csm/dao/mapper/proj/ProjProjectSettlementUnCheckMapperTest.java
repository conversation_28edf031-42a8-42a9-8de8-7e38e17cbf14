package com.msun.csm.dao.mapper.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheckRelative;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementUnCheckRelativeDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementUnCheckMapperTest extends BaseTest {

    @Resource
    private ProjProjectSettlementUnCheckMapper settlementUnCheckMapper;

    @Test
    void findUncheckData() {
        long userid = 449204713212723200L;
        ProjProjectSettlementUnCheckRelativeDTO relativeDTO = new ProjProjectSettlementUnCheckRelativeDTO();
        relativeDTO.setCheckResult(2);
        relativeDTO.setPageNum(1);
        relativeDTO.setPageSize(10);
        relativeDTO.setUnCheckUserId(userid);
        List<ProjProjectSettlementUnCheckRelative> list = settlementUnCheckMapper.findUncheckData(relativeDTO);
        log.info(list.toString());
    }
}