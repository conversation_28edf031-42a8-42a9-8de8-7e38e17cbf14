package com.msun.csm.dao.mapper.oldimsp;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Collections;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;

class OldProductRelationMapperTest extends BaseTest {

    @Resource
    private OldProductRelationMapper oldProductRelationMapper;

    @Test
    void updateBatchByYyProductidAndProjectInfoId() {
        long projectInfoId = 1L;
        oldProductRelationMapper.updateBatchByYyProductidAndProjectInfoId(1, projectInfoId, Collections.singletonList(1L));
    }

    @Test
    void findByYyProductids() {
        oldProductRelationMapper.findByYyProductids(Collections.singletonList(1L));
    }
}