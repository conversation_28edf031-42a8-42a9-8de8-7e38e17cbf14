package com.msun.csm.dao.mapper.proj;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjMessageRecordPeople;
import com.msun.csm.dao.entity.sys.ProjMessageRecordPpRelative;
import com.msun.csm.model.dto.ProjMessageRecordPpRelativeDTO;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.date.DateUtil;

class ProjMessageRecordPeopleMapperTest extends BaseTest {

    @Resource
    private ProjMessageRecordPeopleMapper projMessageRecordPeopleMapper;

    @Test
    void insertBatch() {
        projMessageRecordPeopleMapper.insertBatch(new ArrayList() {{
            ProjMessageRecordPeople projMessageRecordPeople = new ProjMessageRecordPeople();
            projMessageRecordPeople.setId(SnowFlakeUtil.getId());
            projMessageRecordPeople.setMessageStatus(1);
//            projMessageRecordPeople.setMessageToId(1L);
//            projMessageRecordPeople.setExpiredTime(DateUtil.offsetMinute(new Date(), 5));
            add(projMessageRecordPeople);
        }});
    }

    @Test
    void updateBatch() {
        projMessageRecordPeopleMapper.updateBatch(new ArrayList() {{
            ProjMessageRecordPeople projMessageRecordPeople = new ProjMessageRecordPeople();
//            projMessageRecordPeople.setMessageToId(2L);
            projMessageRecordPeople.setRecordId(1L);
            projMessageRecordPeople.setTipTimes(11);
            projMessageRecordPeople.setLastTipTime(DateUtil.date());
            add(projMessageRecordPeople);
        }});
    }

    @Test
    void selectByMsgRecordPpRelative() {
        ProjMessageRecordPpRelativeDTO dto = new ProjMessageRecordPpRelativeDTO();
        dto.setStartTime("2024-01-01 01:01:01");
        dto.setEndTime("2024-01-01 01:01:01");
        dto.setIsSuccess("1");
        dto.setMessageTypeId("2");
        List<ProjMessageRecordPpRelative> list2 = projMessageRecordPeopleMapper.selectByMsgRecordPpRelative(dto);
        list2.forEach(e -> System.out.println(e.toString()));
    }

    @Test
    void testInsertBatchABC() {
        List<ProjMessageRecordPeople> projMessageRecordPeopleSet = new ArrayList<>();
        ProjMessageRecordPeople people = new ProjMessageRecordPeople();
        people.setRecordId(1L);
        people.setAccount("1");
        people.setMessageStatus(1);
        people.setMessageUrl("2");
        people.setId(SnowFlakeUtil.getId());
        people.setExpiredFlag(1);
        people.setReadTime(new Date());
        people.setLastTipTime(new Date());
        people.setReadUserId(1L);
        people.setTipTimes(1);
        people.setReadTime(new Date());
        people.setCreaterId(1L);
        people.setUpdaterId(1L);
        projMessageRecordPeopleSet.add(people);
        projMessageRecordPeopleMapper.insertBatch(projMessageRecordPeopleSet);
    }
}
