package com.msun.csm.dao.mapper.proj;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.model.dto.ProjResearchPlanResSourceDTO;
import com.msun.csm.util.SnowFlakeUtil;

@Slf4j
class ProjMilestoneTaskContentMapperTest extends BaseTest {

    @Resource
    private ProjResearchPlanResSourceMapper mapper;

    @Test
    void deleteByPlanIdList() {

//        mapper.deleteByPlanIdList(new ArrayList(){{
//            add(455749341159444480L);
//        }});
    }

    @Test
    void insertBatch() {

        List list = new ArrayList(){{
            ProjResearchPlanResSourceDTO dto = new ProjResearchPlanResSourceDTO();
            dto.setId(SnowFlakeUtil.getId());
            dto.setSourceContent("1212");
            dto.setPlanId(1L);
            dto.setSourceKey(12L);
            add(dto);
        }};
        mapper.insertBatch(list);
    }

    @Test
    void deleteByPlanIds() {
        List<ProjMilestoneTask> plans = new ArrayList<>();
        ProjMilestoneTask projMilestoneTask = new ProjMilestoneTask();
        //projMilestoneTask.setProjResearchPlanId(2L);
        plans.add(projMilestoneTask);
//        int count = contentMapper.deleteByPlanIds(plans);
//        log.info("count: {}", count);
    }
}