package com.msun.csm.dao.mapper.proj;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecord;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.model.dto.projreview.ProjReviewQueryDTO;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectReviewRecordMapperTest extends BaseTest {

    @Resource
    private ProjProjectReviewRecordMapper reviewRecordMapper;

    @Resource
    private RuleProjectRuleConfigMapper ruleConfigMapper;

    @Test
    void insertBatch() {
        RuleProjectRuleConfig ruleProjectRuleConfig = ruleConfigMapper.selectById(3382804550011327878L);
        List<ProjProjectReviewRecord> list = new ArrayList<>();
        ProjProjectReviewRecord reviewRecord = BeanUtil.copyProperties(ruleProjectRuleConfig, ProjProjectReviewRecord.class);
//        ProjProjectReviewRecord reviewRecord = BeanUtil.copyProperties(ruleProjectRuleConfig, ProjProjectReviewRecord.class);
        reviewRecord.setProjectInfoId(1212L);
        reviewRecord.setProjectStageCode("121");
        reviewRecord.setProjectReviewRecordId(SnowFlakeUtil.getId());
        reviewRecord.setProjectReviewInfoId(1L);
        reviewRecord.setClassCode("1212");
        reviewRecord.setClassName("1212343");
        reviewRecord.setItemCode("ccc");
        reviewRecord.setItemName("dfd2121");
        reviewRecord.setProjectRuleCode("212");
        reviewRecord.setProjectRuleContent("3cdd");
        reviewRecord.setIsPublic(1);
        reviewRecord.setTemplateFlag(1);
        reviewRecord.setProjectReviewRecordId(SnowFlakeUtil.getId());
        reviewRecord.setUpdaterId(1L);
        reviewRecord.setUpdateTime(new Date());
        reviewRecord.setCreaterId(1L);
        reviewRecord.setCreateTime(new Date());
        reviewRecord.setProjectStageId(1L);
        list.add(reviewRecord);
        int count = reviewRecordMapper.insertBatch(list);
        log.info(count + "");
    }

    @Test
    void selectListByRelation() {
        ProjReviewQueryDTO dto = new ProjReviewQueryDTO();
        dto.setSceneCode("project_review");
        dto.setProjectStageCode("stage_survey");
        dto.setProjectInfoId(467399778818281472L);
        reviewRecordMapper.selectListByRelation(dto);
    }
}