package com.msun.csm.dao.mapper.proj;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecordDetail;
import com.msun.csm.util.SnowFlakeUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectReviewRecordDetailMapperTest extends BaseTest {

    @Resource
    private ProjProjectReviewRecordDetailMapper reviewRecordDetailMapper;

    @Test
    void insertBatch() {
        List<ProjProjectReviewRecordDetail> reviewRecordDetailList = new ArrayList<>();
        ProjProjectReviewRecordDetail reviewRecordDetail = new ProjProjectReviewRecordDetail();
        reviewRecordDetail.setProjectReviewRecordDetailId(SnowFlakeUtil.getId());
        reviewRecordDetail.setProjectReviewRecordId(480072236685676544L);
        reviewRecordDetail.setChildRuleCode("121");
        reviewRecordDetail.setChildRuleContent("323cc");
        reviewRecordDetail.setParentItemCode("123");
        reviewRecordDetail.setUpdaterId(1L);
        reviewRecordDetail.setUpdateTime(new Date());
        reviewRecordDetail.setCreaterId(1L);
        reviewRecordDetail.setIsDeleted(0);
        reviewRecordDetail.setCreateTime(new Date());
        reviewRecordDetailList.add(reviewRecordDetail);
        reviewRecordDetailMapper.insertBatch(reviewRecordDetailList);
    }
}