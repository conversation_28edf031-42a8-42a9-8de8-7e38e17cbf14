package com.msun.csm.dao.mapper.proj;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Collections;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;

class ProjApplyOrderMapperTest extends BaseTest {

    @Resource
    private ProjApplyOrderMapper projApplyOrderMapper;

    @Test
    void findProjApplyOrderInfoList() {
        ProjApplyOrder applyOrder = new ProjApplyOrder();
        applyOrder.setCustomInfoId(855101658504429568L);
        applyOrder.setProjectInfoId(457522126760136704L);
        projApplyOrderMapper.findProjApplyOrderInfoList(applyOrder);
    }

    @Test
    void updateDeleteByProductRecordIdList() {
        projApplyOrderMapper.updateDeleteByProductRecordIdList(Collections.singletonList(1L));
    }
}