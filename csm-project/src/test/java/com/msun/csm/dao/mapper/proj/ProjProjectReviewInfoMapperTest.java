package com.msun.csm.dao.mapper.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjProjectReviewInfoRelative;
import com.msun.csm.model.dto.projreview.ProjPmoReviewQueryDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectReviewInfoMapperTest extends BaseTest {

    @Resource
    private ProjProjectReviewInfoMapper projectReviewInfoMapper;

    @Test
    void selectByCondition() {
        ProjPmoReviewQueryDTO dto = new ProjPmoReviewQueryDTO();
        dto.setCustomInfoId(467399778797309952L);
//        dto.setStageSurveyStatus(0);
//        dto.setStageEntryStatus(0);
//        dto.setStagePreparatStatus(2);
        dto.setCommitEndTime("2024-07-24 00:00:00");
        List<ProjProjectReviewInfoRelative> list = projectReviewInfoMapper.selectByCondition(dto);
        log.info(list.size() + "," + list.toString());
    }
}