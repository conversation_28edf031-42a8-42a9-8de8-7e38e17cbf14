package com.msun.csm.schedule;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.feign.entity.yunwei.resp.SyncCustomerInfoVO;

import cn.hutool.core.collection.CollUtil;

class TaskScheduleTest extends BaseTest {

    @Resource
    private TaskSchedule taskSchedule;

    @Test
    void updateEnvInfo() {
        SyncCustomerInfoVO vo = new SyncCustomerInfoVO();
        vo.setEnvId(1L);
        vo.setEnvName("yi");
        vo.setOrgId(121L);
        vo.setHospitalId(1L);
        SyncCustomerInfoVO vo2 = new SyncCustomerInfoVO();
        vo2.setOrgId(123L);
        vo2.setHospitalId(3L);
        vo2.setEnvId(2L);
        vo2.setEnvName("er");
        List<SyncCustomerInfoVO> syncCustomerInfoVOS = CollUtil.newArrayList(vo, vo2);
        taskSchedule.updateEnvInfo(syncCustomerInfoVOS);
    }

    @Test
    void syncEnvInfoTask() {
        taskSchedule.syncEnvInfoTask();
    }
}