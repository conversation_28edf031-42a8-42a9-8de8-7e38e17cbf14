package com.msun.csm.service.proj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.model.dto.autocheck.*;
import com.msun.csm.model.dto.autocheck.testuse.DeptListObj;
import com.msun.csm.model.dto.autocheck.testuse.SelectYfListObj;
import com.msun.csm.model.dto.autocheck.testuse.YfItem;

@Slf4j
class AutoCheckServiceImplTest extends BaseTest {

    @Resource
    private AutoCheckService autoCheckService;

    @Test
    void getDeptUserInfo() {
        // readydeptlist
        ReadyDeptDTO readyDeptDTO = ReadyDeptDTO.builder()
                .modelDeptId(1)
                .deptName("自动化-耳鼻喉科病房")
                .loginUser("")
                .userName(CollUtil.newArrayList("自动化-耳鼻喉科病房", "张三"))
                .accountName("zdh")
                .description("")
                .roleName("住院主任医生")
                .ruleName("login-user-住院主任医生")
                .systemName("医保结算清单")
                .build();
        // sureYf
        Map<String, DeptYfDTO> sureYfMap = MapUtil.newHashMap();
//        sureYfMap.put("外科", DeptYfDTO.builder()
//                .zhYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31L)
//                        .categoryId("5")
//                        .yfId(16L)
//                        .yfName("中药房（基层测试医院）")
//                        .ykId(723276543656263680L)
//                        .ykName("中药库")
//                        .ykType("药库")
//                        .build()))
//                .enYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31L)
//                        .categoryId("5")
//                        .yfId(6)
//                        .yfName("门诊药房")
//                        .ykId(723276543681429504L)
//                        .ykName("药剂科")
//                        .ykType("药库")
//                        .build()))
//                .build());
//        sureYfMap.put("内科", DeptYfDTO.builder()
//                .zhYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(723276543656263680L)
//                        .bfId(31L)
//                        .categoryId("5")
//                        .yfId(16)
//                        .yfName("中药房（基层测试医院）")
//                        .ykId(723276543656263680L)
//                        .ykName("中药库")
//                        .ykType("药库")
//                        .build()))
//                .enYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(6)
//                        .yfName("门诊药房")
//                        .ykId(723276543681429504L)
//                        .ykName("药剂科")
//                        .ykType("药库")
//                        .build()))
//                .build());
//        sureYfMap.put("耳鼻喉科", DeptYfDTO.builder()
//                .zhYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(723276543656263680L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(16)
//                        .yfName("中药房（基层测试医院）")
//                        .ykId(723276543656263680L)
//                        .ykName("中药库")
//                        .ykType("药库")
//                        .build()))
//                .enYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(6)
//                        .yfName("门诊药房")
//                        .ykId(723276543681429504L)
//                        .ykName("药剂科")
//                        .ykType("药库")
//                        .build()))
//                .build());
//        AutoDeptDTO autoDeptDTO = AutoDeptDTO.builder()
//                .readyDeptList(readyDeptDTOS)
//                .sureYf(sureYfMap)
//                .build();
//        List<AutoDeptDTO> autoDeptDTOS = CollUtil.newArrayList(autoDeptDTO);
//        // 拼接主体参数
//        GenLoginUserDTO dto = GenLoginUserDTO.builder()
//                .cloudDomain("test.msunhis.com")
//                .orgId(1003L)
//                .cloudHospitalId(10033001L)
//                .operatorUser("张三")
//                .projectInfoId(1212L)
//                .hospitalName("增量测试")
//                .hospitalInfoId(1111111L)
//                .deptList(autoDeptDTOS)
//                .build();
//        JSONObject jsonObject = autoCheckService.getDeptUserInfo(dto);
//        log.info("" + jsonObject);
    }

//    public Map<String, DeptYfDTO> getSureYfMap() {
//        Map<String, DeptYfDTO> sureYfMap = MapUtil.newHashMap();
//        sureYfMap.put("外科", DeptYfDTO.builder()
//                .zhYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(16)
//                        .yfName("中药房（基层测试医院）")
//                        .ykId(723276543656263680L)
//                        .ykName("中药库")
//                        .ykType("药库")
//                        .build()))
//                .enYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(6)
//                        .yfName("门诊药房")
//                        .ykId(723276543681429504L)
//                        .ykName("药剂科")
//                        .ykType("药库")
//                        .build()))
//                .build());
//        sureYfMap.put("内科", DeptYfDTO.builder()
//                .zhYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(723276543656263680L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(16)
//                        .yfName("中药房（基层测试医院）")
//                        .ykId(723276543656263680L)
//                        .ykName("中药库")
//                        .ykType("药库")
//                        .build()))
//                .enYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(6)
//                        .yfName("门诊药房")
//                        .ykId(723276543681429504L)
//                        .ykName("药剂科")
//                        .ykType("药库")
//                        .build()))
//                .build());
//        sureYfMap.put("耳鼻喉科", DeptYfDTO.builder()
//                .zhYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(723276543656263680L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(16)
//                        .yfName("中药房（基层测试医院）")
//                        .ykId(723276543656263680L)
//                        .ykName("中药库")
//                        .ykType("药库")
//                        .build()))
//                .enYfList(CollUtil.newArrayList(YfDTO.builder()
//                        .deptId(5692683434112584705L)
//                        .bfId(31)
//                        .categoryId("5")
//                        .yfId(6)
//                        .yfName("门诊药房")
//                        .ykId(723276543681429504L)
//                        .ykName("药剂科")
//                        .ykType("药库")
//                        .build()))
//                .build());
//        return sureYfMap;
//    }

    public static final Long orgId = 10007L;
    public static final Long hospitalId = 10007101L;
    public static final String hospitalName = "交付测试医院";
    public static final String cloudDomain = "kongku-deliver.msunhis.com";

    @Test
    public void testCreateLogoinUser() {
        JSONObject jsonObject = autoCheckService.getAutoCheckDeptList(hospitalId, hospitalName);
        List<DeptListObj> deptList = AutoCheckServiceImpl.jsonToJava(jsonObject);
        // 获取选择模板信息
        DeptListObj deptListObj = deptList.get(0);
        deptListObj.getSelectYfList().forEach(e -> {
            Map<String, List<YfItem>> yfmap = MapUtil.newHashMap();
            e.getSelectYf().keySet().forEach(f -> {
                if (f.equals("中心药房")) {
                    yfmap.put("zh_yf", CollUtil.newArrayList(e.getSelectYf().get(f)));
                } else if (f.equals("门诊中药房")) {
                    yfmap.put("en_yf", CollUtil.newArrayList(e.getSelectYf().get(f)));
                }
            });
            e.setSelectYf(yfmap);
        });
        for (SelectYfListObj selectYfListObj : deptListObj.getSelectYfList()) {
            selectYfListObj.getSelectYf().remove("中心药房");
            selectYfListObj.getSelectYf().remove("门诊中药房");
        }
        // 拼接参数
        FieldTransDTO fieldTransDTO = FieldTransDTO.builder()
                .deptListObj(deptListObj)
                .hospitalName(hospitalName)
                .clouddomain(cloudDomain)
                .hospitalId(hospitalId)
                .orgId(orgId)
                .operatorUser("张三")
                .projectInfoId(112L)
                .hospitalInfoId(111L)
                .build();
        GenLoginUserTransferDTO dto = AutoCheckServiceImpl.getGenLoginUserTransferDto(fieldTransDTO);
        JSONObject jsonObjectResult = autoCheckService.getDeptUserInfo(dto);
        log.info("" + jsonObjectResult);
    }

    @Test
    void applyToAutoCheck() {
//        http:
//10.68.18.25:8082/csm/autoCheck/applyToAutoCheck
        AutoCheckHospitalInfoDTO autoCheckHospitalInfoDTO = new AutoCheckHospitalInfoDTO();
        autoCheckHospitalInfoDTO.setHospitalInfoId(889882826596483072L);
        autoCheckHospitalInfoDTO.setHospitalName("平阴医院");
        autoCheckHospitalInfoDTO.setCloudHospitalId(10031001L);
        autoCheckHospitalInfoDTO.setOrgId(10031L);
        autoCheckHospitalInfoDTO.setCloudDomain("test.msunhis.com");
        JSONObject jsonObject = autoCheckService.applyToAutoCheck(autoCheckHospitalInfoDTO);
        log.error(jsonObject.toString());
    }
}