package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetDomainDTO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetDomainVO;

@Slf4j
class ProjNetworkDetHospitalServiceTest extends BaseTest {

    @Resource
    private ProjNetworkDetHospitalService networkDetHospitalService;

    @Test
    void getDomainAddressList() {
        ProjNetworkDetDomainDTO networkDetDomainDTO = new ProjNetworkDetDomainDTO();
        networkDetDomainDTO.setProjectInfoId("464478765430345728");
        ProjNetworkDetDomainVO detDomainVO = networkDetHospitalService.getDomainAddressList(networkDetDomainDTO);
        log.info(detDomainVO.toString());
    }
}