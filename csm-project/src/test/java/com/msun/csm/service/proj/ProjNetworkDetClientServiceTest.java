package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospital;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetClientDTO;

@Slf4j
class ProjNetworkDetClientServiceTest extends BaseTest {

    @Resource
    private ProjNetworkDetClientService networkDetClientService;

    @Test
    void getCurrentDetClientCount() {
        ProjNetworkDetClientDTO dto = new ProjNetworkDetClientDTO();
        dto.setRandomCode("942021740776271");
        dto.setLogId("862058296756543488");
        int count = networkDetClientService.getCurrentDetClientCount(dto);
        log.info("count: {}", count);
    }
}