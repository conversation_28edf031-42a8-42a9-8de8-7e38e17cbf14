package com.msun.csm.service.common;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryInfo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class CommonServiceTest extends BaseTest {

    @Resource
    private CommonService commonService;

    @Test
    void sendMessageToSysPerson() {
//        commonService.sendMessageToSysPerson();
    }

    @Test
    void testSendMessageToSysPerson() {
        commonService.sendMessageToSysPerson(DictMessageTypeEnum.DISASTER_RECOVERY_APPLY.getId(), null, -1L,
                null, MsgToCategory.SINGLE_PERSON.getCode(),
                "审核首付款, 运营平台合同号为: " + "212");
    }

    @Test
    void paysignage() {
        boolean a = commonService.paysignage(541360600179900416L, CollUtil.newArrayList(24871L));
    }

    @Test
    void syncIsMsunCloudDisRecover() {
        ProjDisasterRecoveryInfo info = new ProjDisasterRecoveryInfo();
        info.setResponsiblePersonId(117L);
        info.setCustomInfoId(476884293961863169L);
        info.setOrderInfoId(510560061749153793L);
        commonService.syncIsMsunCloudDisRecover(info, "众阳云", "1");
    }
}