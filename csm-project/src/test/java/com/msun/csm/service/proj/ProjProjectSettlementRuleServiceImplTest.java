package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleQueryDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleSaveDTO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementRuleVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementSubmitEntryVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementRuleServiceImplTest extends BaseTest {

    @Resource
    private ProjProjectSettlementRuleService projProjectSettlementRuleService;

    @Test
    void findProjProjectSettlementRuleList() {
        ProjProjectSettlementRuleQueryDTO projectSettlementRuleQueryDTO = new ProjProjectSettlementRuleQueryDTO();
        projectSettlementRuleQueryDTO.setProjectInfoId("464478840982343680");
        Result<ProjProjectSettlementSubmitEntryVO> result =
                projProjectSettlementRuleService.findProjProjectSettlementRuleList(projectSettlementRuleQueryDTO);
        log.info(result.getData().toString());
    }

    @Test
    void saveSettlement() {
//        List<ProjProjectSettlementRuleDTO> projectSettlementRuleDTOList = new ArrayList<>();
//        ProjProjectSettlementRuleDTO ruleDTO = new ProjProjectSettlementRuleDTO();
//        ruleDTO.setProjectSettlementRuleId(null);
//        ruleDTO.setProjectRuleCode("121");
//        ruleDTO.setVerityWay("no");
//        ruleDTO.setProjectInfoId("457523156549849088");
//        ruleDTO.setProjectRuleContent("1212");
//        ruleDTO.setProjectSettlementId("23");
//        ruleDTO.setRequiredFlag(1);
//        ruleDTO.setTemplateFlag(1);
//        projectSettlementRuleDTOList.add(ruleDTO);
        ProjProjectSettlementRuleSaveDTO saveDTO = new ProjProjectSettlementRuleSaveDTO();
        saveDTO.setProjectInfoId("457523156549849088");
        projProjectSettlementRuleService.saveSettlement(saveDTO);
    }

    @Test
    void testSaveSettlement() {
        ProjProjectSettlementRuleSaveDTO saveDTO = new ProjProjectSettlementRuleSaveDTO();
        saveDTO.setProjectInfoId("457523156549849088");
        projProjectSettlementRuleService.saveSettlement(saveDTO);
    }

    @Test
    void transformHistoryVO() {
        List<ProjProjectSettlementRuleVO> voList = new ArrayList<>();
        String projectInfoId = "457523156549849088";
        int settlement = 0;
        ProjProjectSettlementRuleVO ruleVO = new ProjProjectSettlementRuleVO();
        ruleVO.setProjectRuleCode(SettlementRuleCodeEnum.SETTLEMENT_HARDWARE_LIST_FILE.getCode());
        voList.add(ruleVO);
        projProjectSettlementRuleService.transformHistoryVO(voList, projectInfoId, settlement);
    }

    @Test
    void splitProject() {
        ProjProjectInfo projectInfo = new ProjProjectInfo();
        projectInfo.setProjectInfoId(532967033740820480L);
        projProjectSettlementRuleService.splitProject(projectInfo);
    }
}