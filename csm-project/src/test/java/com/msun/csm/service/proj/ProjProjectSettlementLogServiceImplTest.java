package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.model.vo.user.SysUserVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementLogServiceImplTest extends BaseTest {
    @Resource
    private ProjProjectSettlementLogService settlementLogService;

    @Test
    void insert() {
        ProjProjectSettlement settlement = new ProjProjectSettlement();
        settlement.setProjectInfoId(1L);
        settlement.setProjectSettlementId(34L);
        String operateContent = "1232";
        CheckNodeEnum checkNodeEnum = CheckNodeEnum.ENSURE_SETTLE_IN;
        SysUserVO sysUserVO = new SysUserVO();
        sysUserVO.setUserName("sdsf");
        sysUserVO.setSysUserId(121L);
        int settlementStatus = 41;
        settlementLogService.insert(settlement, operateContent, checkNodeEnum, sysUserVO, settlementStatus);
    }
}