package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;

class ProjEquipSummaryServiceImplTest extends BaseTest {

    @Resource
    private ProjEquipSummaryService equipSummaryService;

    @Test
    void aimsEquipAutoCheck() {
        EquipAutoCheckDTO dto = new EquipAutoCheckDTO();
        dto.setCloudEquipId(517395651989688379L);
        dto.setCloudEquipName("2323");
        dto.setCsmEquipId(-1L);
        dto.setEquipmentState(5);
        dto.setHospitalId(10033001L);
        ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
        hospitalInfo.setHospitalInfoId(910583088046735360L);
        hospitalInfo.setHospitalName("日照医院");
        equipSummaryService.aimsEquipAutoCheck(dto, hospitalInfo);
    }

    @Test
    void pacsEquipAutoCheck() {
        EquipAutoCheckDTO dto = new EquipAutoCheckDTO();
        dto.setCloudEquipId(517395651989688375L);
        dto.setCloudEquipName("pacs设备");
        dto.setCsmEquipId(-1L);
        dto.setEquipmentState(5);
        dto.setHospitalId(10033001L);
        ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
        hospitalInfo.setHospitalInfoId(910583088046735360L);
        hospitalInfo.setHospitalName("日照医院");
        equipSummaryService.pacsEquipAutoCheck(dto, hospitalInfo);
    }

    @Test
    void lisEquipAutoCheck() {
        EquipAutoCheckDTO dto = new EquipAutoCheckDTO();
        dto.setCloudEquipId(517395651989688380L);
        dto.setCloudEquipName("注射器");
        dto.setCsmEquipId(-1L);
        dto.setEquipmentState(5);
        dto.setHospitalId(10033001L);
        dto.setProgress("0.3");
        ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
        hospitalInfo.setHospitalInfoId(910583088046735360L);
        hospitalInfo.setHospitalName("日照医院");
        equipSummaryService.lisEquipAutoCheck(dto, hospitalInfo);
    }
}