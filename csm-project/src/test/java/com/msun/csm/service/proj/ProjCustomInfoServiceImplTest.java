package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.model.dto.ProjCustomInfoDTO;

@SpringBootTest
class ProjCustomInfoServiceImplTest {


    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Test
    void selectCustomInfoList() {
        ProjCustomInfoDTO projCustomInfoDTO = new ProjCustomInfoDTO();
        projCustomInfoDTO.setCustomDeptIdList(new ArrayList() {{
            add(2);
        }});
        List<ProjCustomInfo> list = projCustomInfoMapper.findProjCustomInfoList(projCustomInfoDTO, 449204461118275600L);
        System.out.println(list.size());
    }
}
