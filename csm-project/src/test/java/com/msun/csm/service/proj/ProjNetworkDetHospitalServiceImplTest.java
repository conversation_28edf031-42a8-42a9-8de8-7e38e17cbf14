package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospitalRelative;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetHospitalDTO;

@Slf4j
class ProjNetworkDetHospitalServiceImplTest extends BaseTest {

    @Resource
    private ProjNetworkDetHospitalService projNetworkDetHospitalService;

    @Test
    void findNetworkDetHospitalInfoList() {
//        ProjNetworkDetHospitalDTO dto = new ProjNetworkDetHospitalDTO();
//        dto.setPageNum(1);
//        dto.setPageSize(10);
//        dto.setHospitalInfoId(1L);
//        Result<PageInfo<ProjNetworkDetHospitalRelativeVO>> result = projNetworkDetHospitalService.findNetworkDetHospitalInfoList(dto);
//        if (result.getData().getList() != null) {
//            for (ProjNetworkDetHospitalRelativeVO projNetworkDetHospitalVO : result.getData().getList()) {
//                log.info(projNetworkDetHospitalVO.toString());
//            }
//        }
    }

    @Test
    void testFindNetworkDetHospitalInfoList() {
        ProjNetworkDetHospitalDTO dto = new ProjNetworkDetHospitalDTO();
        dto.setProjectInfoId("1");
        dto.setSysType(1);
        dto.setHospitalInfoId("1");
        List<ProjNetworkDetHospitalRelative> result = projNetworkDetHospitalService.findNetworkDetHospitalInfoListImpl(dto);
        if (result != null) {
            for (ProjNetworkDetHospitalRelative projNetworkDetHospitalVO : result) {
                log.info(projNetworkDetHospitalVO.toString());
            }
        }
    }

    @Test
    void deleteFrontMachine() {
        ProjNetworkDetHospitalDTO dto = new ProjNetworkDetHospitalDTO();
        dto.setId("12");
        System.out.println(projNetworkDetHospitalService.deleteFrontMachine(dto));
    }

    @Test
    void getTestDomain() {
        String testDomain = projNetworkDetHospitalService.getTestDomain();
        log.info(testDomain);
    }
}