package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementLog;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckApplyEnterDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleDTO;
import com.msun.csm.model.vo.projsettlement.ApplyEnterMainInfoVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckApplyEnterVO;
import com.msun.csm.model.yunying.SyncRiskAuditContractDTO;
import com.msun.csm.model.yunying.SyncRiskAuditDTO;
import com.msun.csm.model.yunying.SyncYunAuditDto;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementCheckServiceImplTest extends BaseTest {

    @Resource
    private ProjProjectSettlementCheckSaleService projectSettlementCheckSaleService;
    @Resource
    private ProjProjectSettlementCheckService projectSettlementCheckService;

    @Test
    void getSaleApplyFormMainInfo() {
        ProjProjectSettlementCheckSaleDTO projectSettlementCheckSaleDTO = new ProjProjectSettlementCheckSaleDTO();
        projectSettlementCheckSaleDTO.setProjectInfoId("457523156549849088");
//        projectSettlementCheckSaleDTO.setUserId("23123");
        projectSettlementCheckSaleService.getSaleApplyFormMainInfo(projectSettlementCheckSaleDTO);
    }

    @Test
    void getSaleFileTempUrl() {
//        log.info(projectSettlementCheckSaleService.getSaleFileTempUrl(460529578619392000L));
    }


    @Test
    void getApplyEnterData() {
        ProjProjectSettlementCheckApplyEnterDTO applyEnterDTO = new ProjProjectSettlementCheckApplyEnterDTO();
        applyEnterDTO.setProjectInfoId("464478840982343680");
        Result<ProjProjectSettlementCheckApplyEnterVO> result =
                projectSettlementCheckService.getApplyEnterData(applyEnterDTO);
        log.info(result.toString());
    }

    @Test
    void setStatus() {
        ProjProjectSettlementCheckApplyEnterVO applyEnterVO = new ProjProjectSettlementCheckApplyEnterVO();
        applyEnterVO.setApplyEnterVo(new ApplyEnterMainInfoVO());
        List<ProjProjectSettlementLog> settlementLogs = new ArrayList<>();
        ProjProjectSettlementLog log1 = new ProjProjectSettlementLog();
        log1.setOperateNode(1);
        log1.setSettlementStatus(null);
        settlementLogs.add(log1);
        ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO = new ProjProjectSettlementCheckApplyEnterDTO();
        checkApplyEnterDTO.setProjectInfoId("457522126760136704");
        projectSettlementCheckService.setFrontElement(applyEnterVO, 457522126760136704L, null);
        log.info(applyEnterVO + "");
    }

    @Test
    void setFrontElement() {
    }

    @Test
    void updateResult() {
        projectSettlementCheckService.updateResult(469558052965404672L, 475031473133043712L, "12",
                CheckNodeEnum.PMO_AUDIT, CheckResultEnum.AUDIT_FAIL);
    }

    @Test
    void getRiskDTO() {
        projectSettlementCheckService.getRiskDTO(539130319305801728L, CheckResultEnum.AUDIT_PASS, "石磊", "", 2);
    }

    @Test
    void syncRiskAuditImpl() {
        SyncRiskAuditDTO syncRiskAuditDTO = new SyncRiskAuditDTO();
        SyncRiskAuditContractDTO contractDTO = new SyncRiskAuditContractDTO();
        contractDTO.setContractNum(24849L);
        contractDTO.setCode(1);
        syncRiskAuditDTO.setContractList(CollUtil.newArrayList(contractDTO));
        syncRiskAuditDTO.setYyOrderId(113023L);
        syncRiskAuditDTO.setUserNam("石磊");
        projectSettlementCheckService.syncRiskAudit(syncRiskAuditDTO);
    }

    @Test
    void syncYunAudit() {
        SyncYunAuditDto auditDto = new SyncYunAuditDto();
        auditDto.setCloudProjId(113024L);
        auditDto.setDate("2024-12-27");
        auditDto.setRemark("");
        auditDto.setResult(0);
        auditDto.setUserName("石磊");
        projectSettlementCheckService.syncYunAudit(auditDto);
    }
}
