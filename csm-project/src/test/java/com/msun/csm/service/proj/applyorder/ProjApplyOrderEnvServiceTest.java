package com.msun.csm.service.proj.applyorder;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderEnvServiceTest extends BaseTest {

    @Resource
    private ProjApplyOrderEnvService applyOrderEnvService;

    @Resource
    private ProjApplyOrderMapper applyOrderMapper;


    @Test
    void syncYunweiCloudTime() {
        ProjApplyOrder applyOrder = applyOrderMapper.selectById(471819305293283328L);
//        applyOrderEnvService.syncYunweiCloudTimeImpl(applyOrder);
    }

    @Test
    void updateCloudSubTime() {
        applyOrderEnvService.updateCloudSubTime(464994521173676032L, "2024-07-29 01:09:09");
    }

    @Test
    void setMessageContent() {
        ProjApplyOrder applyOrder = new ProjApplyOrder();
        applyOrder.setProjectInfoId(467399778818281472L);
        applyOrder.setApplyNum(1121212L);
        log.info(applyOrderEnvService.setMessageContent(applyOrder, ""));
    }
}