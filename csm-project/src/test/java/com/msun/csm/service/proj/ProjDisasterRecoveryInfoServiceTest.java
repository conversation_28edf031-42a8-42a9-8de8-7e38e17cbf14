package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryApplyOpenDTO;
import com.msun.csm.service.proj.disastercloud.ProjDisasterRecoveryInfoService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjDisasterRecoveryInfoServiceTest extends BaseTest {

    @Resource
    private ProjDisasterRecoveryInfoService recoveryInfoService;

    @Test
    void applyDisasterRecoveryOpen() {
        ProjDisasterRecoveryApplyOpenDTO applyOpenDTO = new ProjDisasterRecoveryApplyOpenDTO();
        applyOpenDTO.setProjDisasterRecoveryInfoId(512600907795197952L);
        Result<String> result = recoveryInfoService.applyDisasterRecoveryOpen(applyOpenDTO);
        log.info(result.toString());
    }
}