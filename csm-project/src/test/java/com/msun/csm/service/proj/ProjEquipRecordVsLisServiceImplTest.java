package com.msun.csm.service.proj;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.config.ConfigEquipSurvey;
import com.msun.csm.dao.mapper.config.ConfigEquipSurveyMapper;
import com.msun.csm.model.dto.BaseCodeNameDTO;
import com.msun.csm.model.dto.LisEquipFileExtendDTO;
import com.msun.csm.model.dto.ProjEquipFilesDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisExtendDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisSaveDTO;
import com.msun.csm.model.dto.ViewProjEquipRecordDTO;
import com.msun.csm.model.dto.device.EquipDetailItemDTO;
import com.msun.csm.model.vo.BaseCodeNameVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisWrapperVO;
import com.msun.csm.model.vo.device.EquipLisDetailItemVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjEquipRecordVsLisServiceImplTest extends BaseTest {

    @Resource
    private ProjEquipRecordVsLisService equipRecordVsLisService;

    @Resource
    private ConfigEquipSurveyMapper configEquipSurveyMapper;

    @Test
    void getEquipToLis() {
        ViewProjEquipRecordDTO dto = new ViewProjEquipRecordDTO();
//        dto.setEquipRecordVsLisId(543791417376210944L);
        Result<ProjEquipRecordVsLisWrapperVO> result = equipRecordVsLisService.getEquipToLis(dto);
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void save() {
        ProjEquipRecordVsLisSaveDTO saveDTO = new ProjEquipRecordVsLisSaveDTO();
        saveDTO.setIsMobile(0);
        ProjEquipRecordVsLisExtendDTO extendDTO = new ProjEquipRecordVsLisExtendDTO();
        saveDTO.setEquipRecordVsLisExtendDTO(extendDTO);
        extendDTO.setDuplexFlag(1);
        extendDTO.setQualityControlFlag(1);
        extendDTO.setQuaBarCode("quabarcode");
        extendDTO.setQuaResult("quares");
        extendDTO.setItemChannel01("01");
        extendDTO.setItemChannel02("02");
        extendDTO.setItemName01("01n");
        extendDTO.setItemName02("02n");
        extendDTO.setIsRecheckFlag(1);
        extendDTO.setEmergencyFlag(1);
        extendDTO.setBarCode("barcode");
        extendDTO.setReportWithImgFlag(1);
        extendDTO.setProjectInfoId(476884251670695936L);
        extendDTO.setHospitalInfoId(477129681100820480L);
        ProjEquipFilesDTO equipFilesDTO1 = new ProjEquipFilesDTO();
        equipFilesDTO1.setFileItemCode("lis_device");
        equipFilesDTO1.setEquipFilesId(544472922747392000L);
        LisEquipFileExtendDTO deviceFullImg = new LisEquipFileExtendDTO();
        deviceFullImg.setProjectFileId(504045706018770944L);
        deviceFullImg.setEquipFilesDTO(equipFilesDTO1);
        extendDTO.setDeviceFullImg(CollUtil.newArrayList(deviceFullImg));
        LisEquipFileExtendDTO deviceConfig = new LisEquipFileExtendDTO();
        ProjEquipFilesDTO equipFilesDTO2 = new ProjEquipFilesDTO();
        equipFilesDTO2.setFileItemCode("lis_device");
        equipFilesDTO2.setEquipFilesId(544472923091324928L);
        deviceConfig.setProjectFileId(504045706018770944L);
        deviceConfig.setEquipFilesDTO(equipFilesDTO2);
        extendDTO.setDeviceConfigImg(CollUtil.newArrayList(deviceConfig));
        LisEquipFileExtendDTO commDoc = new LisEquipFileExtendDTO();
        ProjEquipFilesDTO equipFilesDTO3 = new ProjEquipFilesDTO();
        equipFilesDTO3.setFileItemCode("lis_device");
        equipFilesDTO3.setEquipFilesId(544472923200376832L);
        commDoc.setProjectFileId(504045706018770944L);
        commDoc.setEquipFilesDTO(equipFilesDTO3);
        extendDTO.setCommunicationProtocolDoc(CollUtil.newArrayList(commDoc));
        LisEquipFileExtendDTO commLog = new LisEquipFileExtendDTO();
        ProjEquipFilesDTO equipFilesDTO4 = new ProjEquipFilesDTO();
        equipFilesDTO4.setFileItemCode("lis_device");
        equipFilesDTO4.setEquipFilesId(544472923301040128L);
        commLog.setProjectFileId(504045706018770944L);
        commLog.setEquipFilesDTO(equipFilesDTO4);
        extendDTO.setDuplexCommunicationLog(CollUtil.newArrayList(commLog));
        LisEquipFileExtendDTO normalStr = new LisEquipFileExtendDTO();
        ProjEquipFilesDTO equipFilesDTO5 = new ProjEquipFilesDTO();
        equipFilesDTO5.setFileItemCode("lis_device");
        equipFilesDTO5.setEquipFilesId(544472923405897728L);
        normalStr.setProjectFileId(504045706018770944L);
        normalStr.setEquipFilesDTO(equipFilesDTO5);
        extendDTO.setNormalResultString(CollUtil.newArrayList(normalStr));
        LisEquipFileExtendDTO quaResStr = new LisEquipFileExtendDTO();
        ProjEquipFilesDTO equipFilesDTO6 = new ProjEquipFilesDTO();
        equipFilesDTO6.setFileItemCode("lis_device");
        equipFilesDTO6.setEquipFilesId(544472923535921152L);
        quaResStr.setProjectFileId(504045706018770944L);
        quaResStr.setEquipFilesDTO(equipFilesDTO6);
        extendDTO.setQualityControlResultString(CollUtil.newArrayList(quaResStr));
        extendDTO.setEquipRecordVsLisId(543791417376210944L);
        extendDTO.setEquipRecordId(543791417367822336L);
        extendDTO.setRequiredFlag(1);
        BaseCodeNameDTO nameDTO = new BaseCodeNameDTO();
        nameDTO.setId("serialPort");
        nameDTO.setName("串口通讯");
        List<ConfigEquipSurvey> configEquipSurveys =
                configEquipSurveyMapper.selectList(new QueryWrapper<ConfigEquipSurvey>().eq("yy_product_id", 4073L).like("sence_code", "serialPort"));
        List<String> configEquipSurveyDTOS = Convert.toList(String.class,
                configEquipSurveys.stream().map(ConfigEquipSurvey::getFieldCode).collect(Collectors.toList()));
        nameDTO.setSurveyConfigFields(configEquipSurveyDTOS);
        extendDTO.setCommModeObjExtend(nameDTO);
        Result<String> result = equipRecordVsLisService.saveDevice(saveDTO);
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void selectSurveyAttributesInfo() {
        Result<List<BaseCodeNameVO>> result = equipRecordVsLisService.selectSurveyAttributesInfo("communt_mode");
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void findEquipDetail() {
        EquipDetailItemDTO dto = new EquipDetailItemDTO();
        dto.setEquipTypeName("ABL80");
        Result<EquipLisDetailItemVO> result = equipRecordVsLisService.findEquipDetail(dto);
        log.info(JSONUtil.toJsonStr(result));
    }
}