package com.msun.csm.service.proj.disastercloud;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjDisasterRecoveryInfoServiceImplTest extends BaseTest {

    @Resource
    private ProjDisasterRecoveryInfoService disasterRecoveryInfoService;

    @Test
    void getCloudDomain() {
        ProjCustomInfo customInfo = new ProjCustomInfo();
        customInfo.setCustomInfoId(476884319333208064L);
        customInfo.setCustomName("平阴县中医医院");
        String domain = disasterRecoveryInfoService.getCloudDomain(customInfo, 1);
        log.info(domain);
    }
}