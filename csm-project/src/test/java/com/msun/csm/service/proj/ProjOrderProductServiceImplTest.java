package com.msun.csm.service.proj;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.alibaba.fastjson.JSONObject;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertGeneralDTO;
import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.exception.OperationProductMisMatchingException;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.productauth.ApplyOpenProductBatchDTO;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjOrderProductServiceImplTest extends BaseTest {

    @Resource
    private ProjOrderProductService orderProductService;

    @Test
    void applyOpenProductImplManu() {
        ApplyOpenProductBatchDTO applyOpenProductBatchDTO = new ApplyOpenProductBatchDTO();
        applyOpenProductBatchDTO.setOrderProductIdList(CollUtil.newArrayList(457522428968128524L, 454353944453009408L));
        applyOpenProductBatchDTO.setProjectInfoId(457522428850688000L);
        applyOpenProductBatchDTO.setCustomerInfoId(1L);
        applyOpenProductBatchDTO.setHospitalInfoId(855102330440318976L);
        orderProductService.applyOpenProductImplManu(applyOpenProductBatchDTO);
    }

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Test
    void productEmpower() {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(479991607938899968L);
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        ProjProjectInfo projectInfo = new ProjProjectInfo();
        projectInfo.setProjectInfoId(479991607938899968L);
        List<ProjHospitalInfoRelative> hospitalInfoList =
                applyOrderService.findApplyHospitalInfoRelative(projectInfo.getProjectInfoId(), true);
        orderProductService.productEmpower(productInfos, projectInfo, hospitalInfoList);
    }

    @Test
    void updateApplyOrderResult() {
        orderProductService.updateApplyOrderResult(487039320409853952L);
    }

    @Test
    void updateApplyOrderProductRecord() {
        List<Long> ids = CollUtil.newArrayList(376827128954740736L);
        orderProductService.updateApplyOrderProductRecord(ids, 479991607938899968L);
    }

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Test
    void handleCallResult() {
        ProjProjectInfo projectInfo = new ProjProjectInfo();
        projectInfo.setProjectInfoId(520319322076053504L);
        ProductInfoDTO infoDTO = new ProductInfoDTO();
        infoDTO.setProjectInfoId(projectInfo.getProjectInfoId());
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(infoDTO);
        String jsonStr = "{\n" +
                "\t\"envApplyRes\": [{\n" +
                "\t\t\"body\": {\n" +
                "\t\t\t\"telesalesFlag\": \"\",\n" +
                "\t\t\t\"isPrivate\": \"众阳云\",\n" +
                "\t\t\t\"deployType\": \"新产品部署\",\n" +
                "\t\t\t\"deployApplyProductVOList\": [{\n" +
                "\t\t\t\t\"customerProductId\": 6781,\n" +
                "\t\t\t\t\"customerProductCode\": \"yglyyjxkhxt\",\n" +
                "\t\t\t\t\"customerProductName\": \"(云)公立医院绩效考核系统\"\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"id\": 1437,\n" +
                "\t\t\t\"countryCount\": 1,\n" +
                "\t\t\t\"examName\": \"陈吉彬\",\n" +
                "\t\t\t\"deployResourceApplyCountryVOList\": [],\n" +
                "\t\t\t\"envId\": 1569255870338957312,\n" +
                "\t\t\t\"customerName\": \"平阴县人民医院\",\n" +
                "\t\t\t\"deliverPlatformApplyId\": 202501130014,\n" +
                "\t\t\t\"deployMod\": \"单体医院\",\n" +
                "\t\t\t\"submitName\": \"徐丛强\",\n" +
                "\t\t\t\"depolyApplyId\": -1,\n" +
                "\t\t\t\"envName\": \"华为北京第一集群\",\n" +
                "\t\t\t\"deployResourceApplyProductVOList\": [{\n" +
                "\t\t\t\t\"id\": 28043,\n" +
                "\t\t\t\t\"deployResourceApplyId\": 1437,\n" +
                "\t\t\t\t\"customerProductId\": 7522,\n" +
                "\t\t\t\t\"customerProductCode\": \"YGLYYDJPSXT\",\n" +
                "\t\t\t\t\"customerProductName\": \"(云)公立医院等级评审系统\"\n" +
                "\t\t\t}],\n" +
                "\t\t\t\"status\": \"0\",\n" +
                "\t\t\t\"unCheckApplyProductVOList\": []\n" +
                "\t\t},\n" +
                "\t\t\"headers\": {\n" +
                "\t\t\t\"Server\": [\"nginx\"],\n" +
                "\t\t\t\"Date\": [\"Mon, 13 Jan 2025 07:49:36 GMT\"],\n" +
                "\t\t\t\"Content-Type\": [\"application/json;charset=UTF-8\"],\n" +
                "\t\t\t\"Transfer-Encoding\": [\"chunked\"],\n" +
                "\t\t\t\"Connection\": [\"keep-alive\"],\n" +
                "\t\t\t\"Vary\": [\"Accept-Encoding\", \"Origin\", \"Access-Control-Request-Method\", " +
                "\"Access-Control-Request-Headers\"],\n" +
                "\t\t\t\"X-Content-Type-Options\": [\"nosniff\"],\n" +
                "\t\t\t\"X-XSS-Protection\": [\"1; mode=block\"],\n" +
                "\t\t\t\"Cache-Control\": [\"no-cache, no-store, max-age=0, must-revalidate\"],\n" +
                "\t\t\t\"Pragma\": [\"no-cache\"],\n" +
                "\t\t\t\"Expires\": [\"0\"]\n" +
                "\t\t},\n" +
                "\t\t\"statusCode\": \"OK\",\n" +
                "\t\t\"statusCodeValue\": 200\n" +
                "\t}]\n" +
                "}";
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        orderProductService.handleCallResult(projectInfo, productInfos, jsonObject);
    }

    @Test
    void setExceptionMessage() {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(480451976008425472L);
        ProductInfoDTO dtp = new ProductInfoDTO();
        dtp.setProjectInfoId(480451976008425472L);
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(dtp);
        OperationProductMisMatchingException operationProductMisMatchingException =
                ProjOrderProductServiceImpl.setExceptionMessage(projectInfo, productInfos);
        log.error(operationProductMisMatchingException.toString());
    }

    @Test
    void singleProductAuthImpl() {
        ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
        hospitalInfo.setDictHospitalOrgId(1L);
        hospitalInfo.setHospitalName("2323");
        hospitalInfo.setProvinceId(370000L);
        Map<Long, String> errMap = MapUtil.newHashMap();
        orderProductService.singleProductAuthImpl("121", CollUtil.newArrayList(5721L, 5680L, 5676L), hospitalInfo,
                "123231", errMap);
        System.out.println(errMap);
    }

    @Test
    void testSingleProductAuthImpl() {
        ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
        Map<Long, String> map = MapUtil.newHashMap();
        orderProductService.singleProductAuthImpl("12", CollUtil.newArrayList(5721L, 5680L, 5676L), hospitalInfo, "12"
                , map);
    }

    @Test
    void getRemoteHospitalInfo() {
        ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
        hospitalInfo.setCloudHospitalId(10033001L);
        hospitalInfo.setOrgId(10033L);
        hospitalInfo.setCloudDomain("http://imsp-graytest.msunhis.com");
//        orderProductService.refreshDomain(hospitalInfo);
        BatchInsertGeneralDTO<Long> param = new BatchInsertGeneralDTO<>();
        param.setHospitalId(10033001L);
        param.setHisOrgId(10033L);
        orderProductService.getRemoteHospitalInfo(param, "1-1-2");
    }

    @Test
    void handleDepolyedProduct() {
    }

    @Test
    void testHandleDepolyedProduct() {
        ProjProjectInfo projectInfo = new ProjProjectInfo();
        projectInfo.setProjectInfoId(520319322076053504L);
        String jsonStr = "{\n" +
                "\t\"envApplyRes\": {\n" +
                "\t\t\"telesalesFlag\": \"\",\n" +
                "\t\t\"isPrivate\": \"众阳云\",\n" +
                "\t\t\"deployType\": \"新产品部署\",\n" +
                "\t\t\"deployApplyProductVOList\": [{\n" +
                "\t\t\t\"customerProductId\": 67811,\n" +
                "\t\t\t\"customerProductCode\": \"yglyyjxkhxt\",\n" +
                "\t\t\t\"customerProductName\": \"(云)公立医院绩效考核系统\"\n" +
                "\t\t}],\n" +
                "\t\t\"id\": 1437,\n" +
                "\t\t\"countryCount\": 1,\n" +
                "\t\t\"examName\": \"陈吉彬\",\n" +
                "\t\t\"deployResourceApplyCountryVOList\": [],\n" +
                "\t\t\"envId\": 1569255870338957312,\n" +
                "\t\t\"customerName\": \"平阴县人民医院\",\n" +
                "\t\t\"deliverPlatformApplyId\": 202501130014,\n" +
                "\t\t\"deployMod\": \"单体医院\",\n" +
                "\t\t\"submitName\": \"徐丛强\",\n" +
                "\t\t\"depolyApplyId\": -1,\n" +
                "\t\t\"envName\": \"华为北京第一集群\",\n" +
                "\t\t\"deployResourceApplyProductVOList\": [{\n" +
                "\t\t\t\"id\": 28043,\n" +
                "\t\t\t\"deployResourceApplyId\": 1437,\n" +
                "\t\t\t\"customerProductId\": 7522,\n" +
                "\t\t\t\"customerProductCode\": \"YGLYYDJPSXT\",\n" +
                "\t\t\t\"customerProductName\": \"(云)公立医院等级评审系统\"\n" +
                "\t\t}],\n" +
                "\t\t\"status\": \"0\",\n" +
                "\t\t\"unCheckApplyProductVOList\": []\n" +
                "\t}\n" +
                "}";
//        JSON jsonObject = JSONUtil.parseObj(jsonStr);
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        orderProductService.handleDepolyedProduct(projectInfo, jsonObject);
    }
}