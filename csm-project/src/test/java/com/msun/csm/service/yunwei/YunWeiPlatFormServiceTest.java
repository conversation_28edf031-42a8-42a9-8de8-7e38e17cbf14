package com.msun.csm.service.yunwei;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeHospitalReq;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeReq;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class YunWeiPlatFormServiceTest extends BaseTest {

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;

    @Test
    void sendTimeToYunWei() {
        SyncCloudTimeReq syncCloudTimeReq = new SyncCloudTimeReq(1, 2);
        SyncCloudTimeHospitalReq req = new SyncCloudTimeHospitalReq();
        req.setHospitalId(32L);
        req.setEndTime("2024-09-09 00:00:00");
        syncCloudTimeReq.setHospitals(CollUtil.newArrayList(req));
        yunWeiPlatFormService.sendTimeToYunWei(syncCloudTimeReq);
    }
}