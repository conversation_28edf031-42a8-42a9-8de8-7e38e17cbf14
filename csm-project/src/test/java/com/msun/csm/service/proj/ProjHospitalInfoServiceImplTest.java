package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.model.vo.ProjHospitalInfoVO;

@Slf4j
class ProjHospitalInfoServiceImplTest extends BaseTest {

    @Resource
    private ProjHospitalInfoService projHospitalInfoService;

    @Test
    void findHospitalInfoByProjIdAndCustomerId() {
        Result<List<ProjHospitalInfoVO>> result = projHospitalInfoService.findHospitalInfoByProjIdAndCustomerId("1", "454353944352346112");
        result.getData().forEach(e->log.info(e.toString()));
    }

    @Test
    void updateMilestoneStatus() {
        ProjApplyOrder applyOrder = new ProjApplyOrder();
        applyOrder.setProjectInfoId(460417081665081344L);
//        projHospitalInfoService.updateMilestoneStatus(applyOrder);

    }
}