package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordDto;
import com.msun.csm.model.vo.dict.DictValue;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProductEmpowerAddRecordServiceImplTest extends BaseTest {

    @Resource
    private ProjProductEmpowerAddRecordService productEmpowerAddRecordService;

//    @Test
//    public void find() {
//        ProjProductEmpowerAddRecordDto dto = new ProjProductEmpowerAddRecordDto();
//        dto.setPageNum(10);
//        dto.setPageSize(1);
//        Result<PageInfo<ProjProductEmpowerAddRecordVO>> result =
//                productEmpowerAddRecordService.findProductEmpowerAddRecord(dto);
//
//    }

    @Test
    void findProductEmpowerAddRecord() {
        List<ProductInfo> productInfos = productEmpowerAddRecordService.findProduct(1, 476880313325051904L);
        log.info(CollUtil.isNotEmpty(productInfos) ? productInfos.toString() : StrUtil.EMPTY);
        log.info("共:" + productInfos.size());
    }

    @Test
    void findProductModuleNeedEmpower() {
        Result<List<DictValue>> result = productEmpowerAddRecordService.findProductModuleNeedEmpower();
        log.info(result.getData().toString());
        log.info(result.getData().size() + "");
    }

    @Test
    void testFindProductEmpowerAddRecord() {
        ProjProductEmpowerAddRecordDto dto = new ProjProductEmpowerAddRecordDto();
        dto.setPageSize(10);
        dto.setPageNum(1);
        productEmpowerAddRecordService.findProductEmpowerAddRecord(dto);
    }
}