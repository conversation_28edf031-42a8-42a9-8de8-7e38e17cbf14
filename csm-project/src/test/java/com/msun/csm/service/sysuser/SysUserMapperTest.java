package com.msun.csm.service.sysuser;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class SysUserMapperTest extends BaseTest {

    @Resource
    private SysUserMapper sysUserMapper;

    @Test
    void selectUserByRoleId() {
        List<SysUser> list = sysUserMapper.selectUserByRoleId(447447825206853633L);
        list.forEach(e -> System.out.println(e.toString()));
    }
}
