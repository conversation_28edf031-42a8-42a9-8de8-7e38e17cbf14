package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.github.pagehelper.PageInfo;
import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementUnCheckDTO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementUnCheckRelativeVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementUnCheckServiceImplTest extends BaseTest {

    @Resource
    private ProjProjectSettlementUnCheckService settlementUnCheckService;

    @Test
    void findSettlementUnCheckData() {
        ProjProjectSettlementUnCheckDTO unCheckDTO = new ProjProjectSettlementUnCheckDTO();
        unCheckDTO.setPageNum(10);
        unCheckDTO.setPageSize(1);
        Result<PageInfo<ProjProjectSettlementUnCheckRelativeVO>> result = settlementUnCheckService.findSettlementUnCheckData(unCheckDTO);
        log.info(result.getData().getList().toString());
    }

    @Test
    void settlementUnCheckExportExcel() {
        ProjProjectSettlementUnCheckDTO unCheckDTO = new ProjProjectSettlementUnCheckDTO();
        settlementUnCheckService.settlementUnCheckExportExcel(unCheckDTO, null);
    }
}