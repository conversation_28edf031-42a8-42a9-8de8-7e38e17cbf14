package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projreview.ProjReviewNodeEnum;
import com.msun.csm.common.enums.projreview.ProjReviewResultEnum;
import com.msun.csm.common.enums.projreview.ProjectReviewSceneEnum;
import com.msun.csm.common.enums.projreview.SelfReviewResultEnum;
import com.msun.csm.model.dto.projreview.ProjReviewDTO;
import com.msun.csm.model.dto.projreview.ProjReviewQueryDTO;
import com.msun.csm.model.dto.projreview.ProjReviewRecordDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectReviewServiceTest extends BaseTest {

    @Resource
    private ProjProjectReviewService reviewService;

    @Test
    void findProjReviewContent() {
        ProjReviewQueryDTO reviewQueryDTO = new ProjReviewQueryDTO();
        reviewQueryDTO.setProjectStageCode("stage_survey");
        reviewQueryDTO.setProjectInfoId(467399778818281472L);
        reviewQueryDTO.setSceneCode(ProjectReviewSceneEnum.PROJECT_REVIEW.getCode());
        reviewService.findProjReviewContent(reviewQueryDTO);
    }

    @Test
    void uploadFile() {
    }

    @Test
    void saveReview() {
    }

    @Test
    void testSaveReview() {
        ProjReviewDTO reviewDTO = new ProjReviewDTO();
        reviewDTO.setReviewNodeType(ProjReviewNodeEnum.PROJ_MANAGER.getCode());
        reviewDTO.setProjectInfoId(467399778818281472L);
        reviewDTO.setMilestoneInfoId(469859989294370819L);
        reviewDTO.setReviewResult(ProjReviewResultEnum.PROJ_MGR_APPLYIED.getNodeCode());
        List<ProjReviewRecordDTO> reviewRecordDTOS = new ArrayList<>();
        ProjReviewRecordDTO reviewRecordDTO = new ProjReviewRecordDTO();
        reviewRecordDTO.setSelfReviewMemo("添加注释");
        reviewRecordDTO.setProjectReviewRecordId(481881417487044608L);
        reviewRecordDTO.setSelfReviewResult(SelfReviewResultEnum.SATISFIED.getCode());
        reviewRecordDTOS.add(reviewRecordDTO);
        reviewDTO.setReviewRecordDTOList(reviewRecordDTOS);
        reviewService.saveReview(reviewDTO);
    }
}