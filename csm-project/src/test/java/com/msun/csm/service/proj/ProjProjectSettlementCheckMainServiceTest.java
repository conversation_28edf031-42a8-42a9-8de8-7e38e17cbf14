package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.dao.entity.SysUser;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementCheckMainServiceTest extends BaseTest {

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Test
    void getSysUserByProjectInfoId() {
        SysUser sysUser = mainService.getSysUserByProjectInfoId(457522126760136704L);
        log.info(sysUser.toString());
    }

    @Test
    void getProjectTypeEnum() {
        ProjectTypeEnums typeEnums = mainService.getProjectTypeEnum(476880379423088640L);
        System.out.println(typeEnums.getCode());
    }
}