package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderMainInfoDTO;
import com.msun.csm.model.vo.applyorder.HospitalMainInfoVO;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderDescService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderDescServiceImplTest extends BaseTest {

    @Resource
    private ProjApplyOrderDescService applyOrderDescService;

    @Test
    void getHospitalMainInfo() {
        ProjApplyOrderMainInfoDTO applyOrderDTO = new ProjApplyOrderMainInfoDTO();
        applyOrderDTO.setProjectInfoId("461943699579867136");
        HospitalMainInfoVO hospitalMainInfoVO = applyOrderDescService.getHospitalMainInfo(applyOrderDTO);
        log.info(hospitalMainInfoVO.toString());
    }

    @Test
    void getDeliveryFilePath() {
//        Result<String> filePath = applyOrderDescService.getDeliveryFilePath("464427640408592384");
//        log.info(filePath.getData());
    }
}