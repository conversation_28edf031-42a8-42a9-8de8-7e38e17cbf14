package com.msun.csm.service.proj.applyorder;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderProductServiceTest extends BaseTest {

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;

    @Test
    void updateProductOpenStatus() {
        applyOrderProductService.updateProductOpenStatus(ProductOpenStatusEnum.OPENED, CollUtil.newArrayList(5728L), 476827129474834432L);
    }
}