package com.msun.csm.service.proj.applyorder;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderProductServiceImplTest extends BaseTest {

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;

    @Test
    void updateProductOpenStatus() {
        ProductOpenStatusEnum productOpenStatusEnum = ProductOpenStatusEnum.OPENED;
        int count = applyOrderProductService.updateOrderProductOpenStatus(productOpenStatusEnum, CollUtil.newArrayList(4063L, 4074L), 475365838886227968L);
        log.info("count: {}", count);
    }
}