package com.msun.csm.service.config;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.conf.ConfigProjResearchVsProj;

@Slf4j
class ConfigProjResearchVsProjServiceImplTest extends BaseTest {

    @Resource
    private ConfigProjResearchVsProjService configProjResearchVsProjService;

    @Test
    void selectProjSearchConfigVsProjList() {
        List<ConfigProjResearchVsProj> list = configProjResearchVsProjService.selectProjSearchConfigVsProjList(3L);
        for (ConfigProjResearchVsProj configProjResearchVsProj : list) {
            log.info(configProjResearchVsProj.toString());
        }
    }
}