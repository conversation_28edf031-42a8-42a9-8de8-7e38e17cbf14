package com.msun.csm.service.proj.applyorder;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProduct;
import com.msun.csm.model.resp.applyorder.DepResourceApplyCountryResult;
import com.msun.csm.model.resp.applyorder.DepResourceApplyHospitalResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderServiceImplTest extends BaseTest {

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Test
    void findByYyOrderProductIdList() {
        List<ProductInfo> products = new ArrayList<>();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setYyOrderProductId(4067L);
        ProductInfo productInfo2 = new ProductInfo();
        productInfo2.setYyOrderProductId(5728L);
        products.add(productInfo);
        products.add(productInfo2);
        List<ProjApplyOrderProduct> projApplyOrderProducts = applyOrderService.findArrangeProduct(products, 1L);
        log.info(projApplyOrderProducts.toString());
    }

    @Test
    void updateHospital() {
        DepResourceApplyCountryResult applyCountryResult = new DepResourceApplyCountryResult();
        List<DepResourceApplyHospitalResult> hospitalResults = new ArrayList<>();
        applyCountryResult.setDeployResourceApplyHospitalVOList(hospitalResults);
        DepResourceApplyHospitalResult hospitalResult = new DepResourceApplyHospitalResult();
        hospitalResult.setHospitalId(12L);
        hospitalResult.setOrgId(34L);
        hospitalResult.setHospitalOpenStatus(HospitalOpenStatusEnum.OPENING.getCode());
        hospitalResults.add(hospitalResult);
        hospitalResult.setCustomerInfoId(8591963515567554567L);
        applyOrderService.updateHospital(applyCountryResult);
    }

    @Test
    void updateMilestoneStatus() {
        applyOrderService.updateMilestoneStatus(479991607938899968L, MilestoneNodeEnum.PRODUCT_IMPOWER);
    }

    @Test
    void findOpendProductForCustomInfo() {
        List<ProductInfo> productInfos = applyOrderService.findOpendProductForCustomInfo(476884355685240832L);
        log.info(productInfos.toString());
    }
}