package com.msun.csm.service.config;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.config.SerialNumType;
import com.msun.csm.dao.mapper.config.ConfigSerialNumberMapper;

class ConfigSerialNumberServiceImplTest extends BaseTest {

    @Resource
    private ConfigSerialNumberMapper configSerialNumberMapper;

    @Resource
    private ConfigSerialNumberService configSerialNumberService;

    @Test
    void createNum() {
        configSerialNumberMapper.getNumByKeyCode("applyOrder");
    }

    @Test
    void testCreateNum() {
        String num = configSerialNumberService.createNum(SerialNumType.DISASTER_CLOUD_ORDER);
        System.out.println(num);
    }
}