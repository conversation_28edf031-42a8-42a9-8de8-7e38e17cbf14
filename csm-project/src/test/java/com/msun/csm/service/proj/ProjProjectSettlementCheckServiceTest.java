package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaveDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementCheckServiceTest extends BaseTest {

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Test
    void getProjectInfoIds() {
        Long contractInfoId = 457519497367736320L;
        List<ProjOrderInfo> orderInfos = orderInfoMapper.selectList(new QueryWrapper<ProjOrderInfo>().eq("contract_info_id", contractInfoId));
        settlementCheckService.getProjectInfoIds(orderInfos);
    }

    @Test
    void saveSettlement() {
        ProjProjectSettlementCheckSaveDTO saveDTO = new ProjProjectSettlementCheckSaveDTO();
        saveDTO.setCheckNode(CheckNodeEnum.RISK_AUDIT.getCode());
        saveDTO.setCheckResult(CheckResultEnum.AUDIT_FAIL.getCode());
        saveDTO.setCheckContent("1212");
        saveDTO.setProjectInfoId("457522126760136704");
        saveDTO.setUserId("475031473133043712");
        settlementCheckService.saveSettlement(saveDTO);
    }
}