package com.msun.csm.service.proj.applyorder;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderHospitalServiceImplTest extends BaseTest {

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Test
    void saveOrUpdateHospitalNewFunData() {
        List<ProjHospitalInfoRelative> infoList = hospitalInfoMapper.findHospitalByCloudHospitalId(99980001L);
        applyOrderHospitalService.saveOrUpdateHospitalNewFunData(infoList.get(0));
    }

    @Test
    void updateHospitalRelativeInfoByCloudHospitalId() {
        OldCustomerInfo oldCustomerInfoParam = new OldCustomerInfo();
        oldCustomerInfoParam.setEnvId(1L);
        oldCustomerInfoParam.setEnvName("12");
        ProjHospitalInfo copyHospitalInfoParam = new ProjHospitalInfo();
        copyHospitalInfoParam.setEnvName("212");
        copyHospitalInfoParam.setEnvId(1L);
        List<Long> cloudHospitalIds = CollUtil.newArrayList(10887001L);
        applyOrderHospitalService.updateHospitalRelativeInfoByCloudHospitalId(oldCustomerInfoParam, copyHospitalInfoParam, cloudHospitalIds);
    }
}