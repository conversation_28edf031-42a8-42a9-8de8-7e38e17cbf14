package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementLog;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementCheckMainServiceImplTest extends BaseTest {

    @Resource
    private ProjProjectSettlementCheckMainService settlementCheckMainService;

    @Test
    void isBailingAppOpen() {
        log.info("" + settlementCheckMainService.isBailingAppOpen(4575221267601367045L));
    }

    @Test
    void dispatchCloudForm() {
        log.info("" + settlementCheckMainService.dispatchCloudForm(457522126760136704L));
    }

    @Test
    void findSettlementCheckLog() {
        List<ProjProjectSettlementLog> settlementLogList =
                settlementCheckMainService.findSettlementCheckLogCreateTimeAsc(467399778818281472L);
        log.info(settlementLogList.toString());
    }

    @Test
    void hasBailingProduct() {
        boolean flag = settlementCheckMainService.hasBailingProduct(475367108850176000L);
        log.info("flag: {}", flag);
    }

    @Test
    void findSettlementOrderInfo() {
        List<ProjSettlementOrderInfo> list = settlementCheckMainService.findSettlementOrderInfo(482605925519974400L);
        log.info(list.toString());
    }

    @Test
    void isOldSettlement() {
        boolean a = settlementCheckMainService.isOldSettlement(467399778818281472L);
        Assert.isFalse(a);
        a = settlementCheckMainService.isOldSettlement(479639574165708800L);
        Assert.isTrue(a);
    }

    @Test
    void hasPurchaseSoftOrderInfo() {
        boolean is = settlementCheckMainService.hasPurchaseSoftOrderInfo(513819465042841600L);
        log.info(is + "");
    }
}