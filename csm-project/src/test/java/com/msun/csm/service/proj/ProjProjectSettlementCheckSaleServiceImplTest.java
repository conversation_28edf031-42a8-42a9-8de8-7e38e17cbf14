package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleCloudNodeDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProjectSettlementCheckSaleServiceImplTest extends BaseTest {

    @Resource
    private ProjProjectSettlementCheckSaleService settlementCheckSaleService;

    @Test
    void findCloudServiceOrder() {
        ProjProjectSettlementCheckSaleDTO settlementCheckSaleDTO = new ProjProjectSettlementCheckSaleDTO();
        Result result = settlementCheckSaleService.findCloudServiceOrder(settlementCheckSaleDTO);
        log.info(result.getData().toString());
    }

    @Test
    void findCloudNode() {
        ProjProjectSettlementCheckSaleCloudNodeDTO checkSaleCloudNodeDTO = new ProjProjectSettlementCheckSaleCloudNodeDTO();
        checkSaleCloudNodeDTO.setMsunCloudFlag(2);
        Result result = settlementCheckSaleService.findCloudNode(checkSaleCloudNodeDTO);
        log.info(result.getData().toString());
    }
}