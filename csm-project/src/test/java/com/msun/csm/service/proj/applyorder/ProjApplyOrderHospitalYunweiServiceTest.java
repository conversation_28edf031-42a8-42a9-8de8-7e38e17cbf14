package com.msun.csm.service.proj.applyorder;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderHospitalYunweiServiceTest extends BaseTest {

    @Resource
    private ProjApplyOrderHospitalYunweiService yunweiService;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Test
    void getMainHospital() {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(476880289925029888L);
        ProjHospitalInfo hospitalInfo = yunweiService.getMainHospital(projectInfo);
        log.info(hospitalInfo.toString());
    }

    @Test
    void testGetMainHospital() {
        yunweiService.getMainHospital(mainService.getProjectInfo(479991607938899968L));
    }
}