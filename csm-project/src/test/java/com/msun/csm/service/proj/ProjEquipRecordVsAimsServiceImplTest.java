package com.msun.csm.service.proj;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.AimsCloudEquipSelectDTO;
import com.msun.csm.model.dto.AimsComparedEquipSelectDTO;
import com.msun.csm.model.dto.AimsEquipSelectDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsAimsDTO;
import com.msun.csm.model.dto.UpdateAimsCloudEquipDTO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.ProjEquipRecordVsAimsVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjEquipRecordVsAimsServiceImplTest extends BaseTest {

    @Resource
    private ProjEquipRecordVsAimsService aimsService;

    @Test
    void selectCloudEquipData() {
        AimsCloudEquipSelectDTO aimsCloudEquipSelectDTO = new AimsCloudEquipSelectDTO();
        aimsCloudEquipSelectDTO.setCustomInfoId(476884302648266752L);
        aimsCloudEquipSelectDTO.setProjectType(2);
        Result<Map<String, List<CloudEquipVO>>> result =
                aimsService.selectCloudEquipDataTransfer(aimsCloudEquipSelectDTO);
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void compareEquipmentToAims() {
        AimsComparedEquipSelectDTO comparedEquipSelectDTO = new AimsComparedEquipSelectDTO();
        comparedEquipSelectDTO.setCustomInfoId(513003555512078336L);
        comparedEquipSelectDTO.setProjectInfoId(513003555524661249L);
        Result<List<ProjEquipRecordVsAimsVO>> result = aimsService.compareEquipmentToAims(comparedEquipSelectDTO);
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void updateCloudEquipToAims() {
        List<UpdateAimsCloudEquipDTO> dtoList = CollUtil.newArrayList();
        UpdateAimsCloudEquipDTO cloudEquipDTO = new UpdateAimsCloudEquipDTO();
        CloudEquipVO cloudEquipVO = new CloudEquipVO();
        cloudEquipVO.setCloudEquipName("测试1");
        cloudEquipVO.setCloudEquipId(1234554312L);
        cloudEquipVO.setCloudEquipNameAndHospital("测试1医院");
        cloudEquipDTO.setCloudEquipVO(cloudEquipVO);
        cloudEquipDTO.setEquipRecordVsAimsId(540175315010158592L);
        dtoList.add(cloudEquipDTO);
        Result<String> result = aimsService.updateCloudEquipToAims(dtoList);
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void aimsEquipCheckForLisAnaly() {
        //{"customInfoId":"513003555512078336","projectInfoId":"513003555524661249","hospitalInfoId":"",
        // "equipRecordVsAimsId":"540613873328029696"}
        AimsEquipSelectDTO dto = new AimsEquipSelectDTO();
        dto.setEquipRecordVsAimsId(533726280392998912L);
        dto.setCustomInfoId(513003555512078336L);
        dto.setProjectInfoId(513003555524661249L);
        Result<String> result = aimsService.aimsEquipCheckForLisAnaly(dto);
        log.info(JSONUtil.toJsonStr(result));
    }

    @Test
    void saveOrUpdateAimsEquip() {
        String req = "{\n" +
                "\t\"customInfoId\": \"476884251649724416\",\n" +
                "\t\"projectInfoId\": \"476884251670695936\",\n" +
                "\t\"equipModelName\": \"G40\",\n" +
                "\t\"equipTypeName\": \"监护仪\",\n" +
                "\t\"equipFactoryName\": \"飞利浦PHILIPS\",\n" +
                "\t\"commModeObj\": {\n" +
                "\t\t\"id\": \"socket\",\n" +
                "\t\t\"name\": \"网口通讯\"\n" +
                "\t},\n" +
                "\t\"hospitalInfoId\": \"477129681100820480\",\n" +
                "\t\"equipFactoryPhone\": \"11\",\n" +
                "\t\"requiredFlag\": 1,\n" +
                "\t\"equipPositionFlag\": 1,\n" +
                "\t\"equipPositionName\": \"11\",\n" +
                "\t\"memo\": \"11\",\n" +
                "\t\"equipFileList\": [{\n" +
                "\t\t\"fileItemCode\": \"aims_device\",\n" +
                "\t\t\"fileTypeCode\": \"equip_nameplate\",\n" +
                "\t\t\"fileTypeName\": \"仪器铭牌图片\",\n" +
                "\t\t\"limitType\": \"jpg,jpeg,png,gif,svg,webp\",\n" +
                "\t\t\"limitTypeArray\": [\"jpg\", \"jpeg\", \"png\", \"gif\", \"svg\", \"webp\"],\n" +
                "\t\t\"orderNo\": 13,\n" +
                "\t\t\"requiredFlag\": 0\n" +
                "\t}, {\n" +
                "\t\t\"fileItemCode\": \"aims_device\",\n" +
                "\t\t\"fileTypeCode\": \"yuanshichuan\",\n" +
                "\t\t\"fileTypeName\": \"原始串\",\n" +
                "\t\t\"limitType\": \"txt,XML,CSV,XLS,XLSX,DAT,ini,HL7,hpr\",\n" +
                "\t\t\"limitTypeArray\": [\"txt\", \"XML\", \"CSV\", \"XLS\", \"XLSX\", \"DAT\", \"ini\", \"HL7\", " +
                "\"hpr\"],\n" +
                "\t\t\"orderNo\": 13,\n" +
                "\t\t\"requiredFlag\": 0\n" +
                "\t}, {\n" +
                "\t\t\"fileItemCode\": \"aims_device\",\n" +
                "\t\t\"fileTypeCode\": \"agreement_text\",\n" +
                "\t\t\"fileTypeName\": \"协议文档\",\n" +
                "\t\t\"limitType\": \"doc,docx,pdf,txt,md,wps\",\n" +
                "\t\t\"limitTypeArray\": [\"doc\", \"docx\", \"pdf\", \"txt\", \"md\", \"wps\"],\n" +
                "\t\t\"orderNo\": 13,\n" +
                "\t\t\"requiredFlag\": 1,\n" +
                "\t\t\"projectFileId\": \"546696476900556800\",\n" +
                "\t\t\"fileName\": \"交付调用运维接口对接参数梳理.doc\",\n" +
                "\t\t\"filePath\": \"https://chis-other.obs.cn-north-4.myhuaweicloud" +
                ".com:443/imsp/csm/test/476884251670695936/preparat_device/%E4%BA%A4%E4%BB%98%E8%B0%83%E7%94%A8%E8%BF" +
                "%90%E7%BB%B4%E6%8E%A5%E5%8F%A3%E5%AF%B9%E6%8E%A5%E5%8F%82%E6%95%B0%E6%A2%B3%E7%90%86_1737084073109" +
                ".doc?AccessKeyId=XDDNEUWDM2DALIM7XIP5&Expires=1737087673&Signature=C6hNJnQe3RpWn8Knn%2BdVdTc9qUo%3D" +
                "\"\n" +
                "\t}]\n" +
                "}";
        ProjEquipRecordVsAimsDTO aimsDTO = JSONUtil.toBean(req, ProjEquipRecordVsAimsDTO.class);
        aimsDTO.setCenterMonDeviceFlag(1);
        aimsDTO.setCenterMonDeviceId(132323L);
        aimsDTO.setMonitorCode("ssdd");
        aimsDTO.setEquipIp("*******");
        aimsDTO.setMonDeviceConfigPort(3332323);
        aimsService.saveOrUpdateAimsEquip(aimsDTO);
        log.info(JSONUtil.toJsonStr(aimsDTO));
    }
}