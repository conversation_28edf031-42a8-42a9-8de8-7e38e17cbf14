package com.msun.csm.service.sysuser;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.SysUser;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class SysUserServiceTest extends BaseTest {

    @Resource
    private SysUserService sysUserService;

    @Test
    void selectBySysUserIdList() {
        List<SysUser> sysUserList = sysUserService.selectBySysUserIdList(new ArrayList<Long>() {{
            add(449197348795039744L);
            add(449197348795039744L);
        }});
        log.info(sysUserList.size() + "");
    }
}
