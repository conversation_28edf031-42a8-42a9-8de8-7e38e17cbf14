package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.github.pagehelper.PageInfo;
import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryInfoQueryDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryMainInfoQueryDTO;
import com.msun.csm.model.vo.ProjDisasterRecoveryInfoRelativeVO;
import com.msun.csm.service.proj.disastercloud.ProjDisasterRecoveryInfoService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjDisasterRecoveryInfoServiceImplTest extends BaseTest {

    @Resource
    private ProjDisasterRecoveryInfoService disasterRecoveryInfoService;

    @Test
    void projDisasterRecoveryInfoService() {
        ProjDisasterRecoveryInfoQueryDTO dto = new ProjDisasterRecoveryInfoQueryDTO();
        dto.setPageNum(10);
        dto.setPageSize(1);
        dto.setDeliveryOrderNo("1212");
        Result<PageInfo<ProjDisasterRecoveryInfoRelativeVO>> result =
                disasterRecoveryInfoService.projDisasterRecoveryInfoService(dto);
        log.info(result.toString());
    }

    @Test
    void getDisasterRecoveryMainInfo() {
        ProjDisasterRecoveryMainInfoQueryDTO dto = new ProjDisasterRecoveryMainInfoQueryDTO();
        disasterRecoveryInfoService.getDisasterRecoveryMainInfo(dto);
    }

    @Test
    void testGetDisasterRecoveryMainInfo() {
        ProjDisasterRecoveryMainInfoQueryDTO dto = new ProjDisasterRecoveryMainInfoQueryDTO();
        dto.setProjDisasterRecoveryInfoId(502823828690133004L);
        disasterRecoveryInfoService.getDisasterRecoveryMainInfo(dto);
    }
}