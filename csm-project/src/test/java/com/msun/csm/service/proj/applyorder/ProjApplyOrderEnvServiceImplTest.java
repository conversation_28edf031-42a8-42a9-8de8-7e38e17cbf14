package com.msun.csm.service.proj.applyorder;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderEnvServiceImplTest extends BaseTest {

    @Resource
    private ProjApplyOrderEnvService applyOrderEnvService;

    @Test
    void getMainHospitalInfo() {
        ProjHospitalInfo info = applyOrderEnvService.getMainHospitalInfo(855102330440318976L);
        log.info("1....:{}", info);
        info = applyOrderEnvService.getMainHospitalInfo(null);
        log.info("1....:{}", info);
        info = applyOrderEnvService.getMainHospitalInfo(510233044031897L);
        log.info("1....:{}", info);
    }

    @Test
    void sendToSystemManager() {
        ProjApplyOrder applyOrder = new ProjApplyOrder();
        applyOrder.setProjectInfoId(1L);
        applyOrder.setApplyNum(1212L);
        HospitalUpdateStatusAndProductDeployDTO dto = new HospitalUpdateStatusAndProductDeployDTO();
        applyOrderEnvService.sendToSystemManager(applyOrder);
    }

    @Test
    void syncIsMsunCloud() {
        ProjApplyOrder applyOrder = new ProjApplyOrder();
        applyOrder.setCustomInfoId(494928019960201216L);
        applyOrder.setProjectInfoId(513809884964028416L);
        HospitalUpdateStatusAndProductDeployDTO dto = new HospitalUpdateStatusAndProductDeployDTO();
        dto.setIsMsunPay(1);
        dto.setDeployCloudVendor("华为云云");
        applyOrderEnvService.syncIsMsunCloud(applyOrder, dto);
    }
}