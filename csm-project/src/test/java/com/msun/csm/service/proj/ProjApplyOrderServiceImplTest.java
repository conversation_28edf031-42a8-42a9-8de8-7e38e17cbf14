package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderListDTO;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjApplyOrderServiceImplTest extends BaseTest {

    @Resource
    private ProjApplyOrderService projApplyOrderService;

    @Test
    void findProjApplyOrderInfoList() {
        ProjApplyOrderListDTO projApplyOrderListDTO = new ProjApplyOrderListDTO();
        projApplyOrderListDTO.setCustomInfoId("855101658504429568");
        projApplyOrderListDTO.setProjectInfoId("457522126760136704");
        projApplyOrderListDTO.setPageNum(1);
        projApplyOrderListDTO.setPageSize(10);
        projApplyOrderService.findProjApplyOrderInfoList(projApplyOrderListDTO);
    }
//    @Test
//    void downloadPreResPlanFile() {
//        log.info(projApplyOrderService.downloadPreResPlanFile().getData());
//    }

    @Test
    void applyOrderDetailsView() {
        projApplyOrderService.applyOrderDetailsView("1");
    }

    @Test
    void domainDetection() {
        Result result = projApplyOrderService.domainDetection("test0523");
        log.info(result.toString());
    }

    @Test
    void applyOrderSubmit() {
        ProjApplyOrderDTO dto = new ProjApplyOrderDTO();
        dto.setApplyDomain("www.iii.com");
//        dto.setCustomInfoId("451105569574158336");
//        dto.setProjectInfoId("455040070440407040");
        dto.setCloudEnv(0);
        dto.setResourceFilePath("/imsp/预资源规划附件.xlsx");
//        projApplyOrderService.applyOrderSubmit(dto);
    }

    @Test
    void revokeApplyOrder() {
//        projApplyOrderService.revokeApplyOrder("2323");
    }
}