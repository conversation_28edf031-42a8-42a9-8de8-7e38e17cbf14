package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO;

class ProjEquipRecordVsEcgServiceImplTest extends BaseTest {

    @Resource
    private ProjEquipRecordVsEcgService equipRecordVsEcgService;

    @Test
    void selectEcgEquipData() {
        //{"customInfoId":"513003555512078336","projectInfoId":"513003555524661249","equipModelOrFactory":"",
        // "hospitalInfoId":"","requiredFlag":null,"equipStatus":null}
        ProjEquipRecordVsEcgSelectDTO selectDTO = new ProjEquipRecordVsEcgSelectDTO();
        selectDTO.setCustomInfoId(513003555512078336L);
        selectDTO.setProjectInfoId(513003555524661249L);
        equipRecordVsEcgService.selectEcgEquipData(selectDTO);
    }

    @Test
    void ecgEquipSendCloudEquip() {
        ProjEquipRecordVsEcgSelectDTO dto = new ProjEquipRecordVsEcgSelectDTO();
        equipRecordVsEcgService.ecgEquipSendCloudEquip(dto);
    }
}