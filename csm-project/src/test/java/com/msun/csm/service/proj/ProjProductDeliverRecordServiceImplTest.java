package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;

@Slf4j
class ProjProductDeliverRecordServiceImplTest extends BaseTest {

    @Resource
    private ProjProductDeliverRecordService projProductDeliverRecordService;

    @Test
    void findByProjInfoIdAndCustomerInfoId() {
        List<ProjProductDeliverRecord> list = projProductDeliverRecordService.findByProjInfoIdAndCustomerInfoId("451097745414619136","451097745091657728");
        for (ProjProductDeliverRecord projProductDeliverRecord : list) {
            log.info(projProductDeliverRecord.toString());
        }
    }
}