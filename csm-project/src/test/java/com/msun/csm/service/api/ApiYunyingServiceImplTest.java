package com.msun.csm.service.api;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.enums.api.yunwei.CloudFeedbackEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.imsp.SyncYunRenewalApplicationRenewDTO;
import com.msun.csm.model.yunying.HospitalInfoDTO;
import com.msun.csm.model.yunying.resp.HospitalInfo;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ApiYunyingServiceImplTest extends BaseTest {

    @Resource
    private ApiYunyingService apiYunyingService;

    @Test
    void syncYunRenew() {
        SyncYunRenewDTO dto = new SyncYunRenewDTO();
        dto.setType(OrderTypeEnums.CLOUD_RESOURCE.getCode());
        dto.setCustomerId(4544L);
        dto.setLen(30);
        dto.setYyWoId(11223l);
        dto.setCloudFeedback(CloudFeedbackEnum.OTHER.getCode());
        dto.setPemcusssolType(ProjectTypeEnums.REGION.getCode());
        apiYunyingService.syncYunRenew(dto);
    }

    @Test
    void getMainHospitalInfo() {
        ProjHospitalInfo hospitalInfo = apiYunyingService.getMainHospitalInfo(2, 467399778797309952L);
        log.info(hospitalInfo.toString());
    }

    @Test
    void syncYunRenewalApplicationRenewCloudTime() {
        SyncYunRenewalApplicationRenewDTO dto = null;
        ProjCustomCloudService cloudService = null;
        List<ProjHospitalInfo> hospitalInfos = null;
        Integer projectType = null;
        apiYunyingService.syncYunRenewalApplicationRenewCloudTime(dto, cloudService, hospitalInfos, projectType);
    }

    @Test
    void findHospitalInfo() {
        Result<List<HospitalInfo>> result = apiYunyingService.findHospitalInfo(HospitalInfoDTO.builder()
                .yyCustomerId(253L)
                .solutionType(4)
                .build());
        log.info(JSONUtil.toJsonStr(result));
    }
}