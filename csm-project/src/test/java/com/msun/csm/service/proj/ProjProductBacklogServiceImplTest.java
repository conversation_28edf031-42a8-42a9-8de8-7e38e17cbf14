package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;

import com.msun.csm.BaseTest;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjProductBacklogOneClickDetectDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickImportDTO;
import com.msun.csm.model.vo.OneClickDetectionVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ProjProductBacklogServiceImplTest extends BaseTest {

    @Resource
    private ProjProductBacklogService productBacklogService;

    @Test
    void oneClickImportCloudHealth() {

    }

    @Test
    void oneClickDetection() {
        ProjProductBacklogOneClickDetectDTO projProductBacklogOneClickDTO = new ProjProductBacklogOneClickDetectDTO();
        projProductBacklogOneClickDTO.setMilestoneInfoId(493063195131215872L);
        projProductBacklogOneClickDTO.setProjectInfoId(492405384181284864L);
        Result<OneClickDetectionVO> result = productBacklogService.oneClickDetection(projProductBacklogOneClickDTO);
    }

    @Test
    void testOneClickImportCloudHealth() {
        ProjProductBacklogOneClickImportDTO importDTO = new ProjProductBacklogOneClickImportDTO();
        importDTO.setProjectInfoId(497874990001913857L);
        Result<List<String>> result = productBacklogService.oneClickImportCloudHealth(importDTO);
        log.info(result.toString());
    }
}