package com.msun.csm;

import java.net.InetAddress;
import java.net.UnknownHostException;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@EnableScheduling
@EnableFeignClients ("com.msun.csm.feign.client.*")
@SpringBootApplication
@MapperScan ({"com.msun.csm.dao.mapper", "com.msun.csm.common.dao.mapper", "com.msun.csm.mobile.dao.mapper", "com.msun.csm.datamigration.dao.mapper"})
public class CsmApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext applicationContext = SpringApplication.run(CsmApplication.class, args);
        Environment env = applicationContext.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        log.info("\n\t----------------------------------------------------------\n\tApplication is running! Access "
                + "URLs:\n\tSwagger: \thttp://{}:{}/csm/doc.html\n\t"
                + "----------------------------------------------------------", ip, port);
    }
}
