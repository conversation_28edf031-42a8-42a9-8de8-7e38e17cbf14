package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.param.custombackendlimit.AddDataParam;
import com.msun.csm.model.param.custombackendlimit.QueryDataReq;
import com.msun.csm.model.req.custombackendlimit.UpdateDataReq;
import com.msun.csm.model.resp.custombackendlimit.QueryProjectDataResp;
import com.msun.csm.service.report.ConfigCustomBackendLimitService;

import lombok.extern.slf4j.Slf4j;

/**
 * 小前端大后端项目数据表
 *
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/14
 */
@Slf4j
@RestController
@RequestMapping("/customBackendLimit")
public class ConfigCustomBackendLimitController {

    @Resource
    private ConfigCustomBackendLimitService configCustomBackendLimitService;

    /**
     * 分页查询查询小前端大后端项目数据
     *
     * @return
     */
    @PostMapping("/queryData")
    public Result<PageInfo<QueryProjectDataResp>> queryData(@RequestBody QueryDataReq req) {
        log.info("分页查询查询小前端大后端项目数据");
        if (req.getPageNum() == null || req.getPageSize() == null) {
            return Result.fail("分页参数不能为空");
        }
        return configCustomBackendLimitService.queryData(req);
    }

    /**
     * 更新小前端大后端项目数据-是否开启限制
     */
    @PostMapping("/updateData")
    public Result<Void> updateData(@RequestBody @Valid UpdateDataReq req) {
        log.info("更新小前端大后端项目数据，参数={}", JSON.toJSONString(req));
        try {
            return configCustomBackendLimitService.updateData(req);
        } catch (Exception e) {
            log.error("更新小前端大后端项目数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 新增小前端大后端项目配置
     */
    @PostMapping("/addData")
    public Result<Void> addData(@RequestBody @Valid AddDataParam param) {
        log.info("新增小前端大后端项目配置，参数={}", JSON.toJSONString(param));
        try {
            return configCustomBackendLimitService.addData(param);
        } catch (Exception e) {
            log.error("新增小前端大后端项目配置，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 查询后端项目经理
     */
    @PostMapping("/queryBackendManager")
    public Result<List<BaseCodeNameResp>> queryBackendManager() {
        log.info("查询后端项目经理");
        try {
            return Result.success(configCustomBackendLimitService.queryBackendManager());
        } catch (Exception e) {
            log.error("查询后端项目经理，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }
}
