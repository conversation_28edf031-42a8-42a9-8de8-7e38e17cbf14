package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.core.springcloud.api.validator.group.SaveGroup;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.InitProductSupplementaryRecordDTO;
import com.msun.csm.model.dto.ProjProductSupplementaryRecordDTO;
import com.msun.csm.model.vo.ProjProductSupplementaryRecordVO;
import com.msun.csm.model.vo.product.InitProductSupplementaryRecordVO;
import com.msun.csm.service.proj.ProjProductSupplementaryRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 特批/工单产品补录记录表(ProjProductSupplementaryRecord)控制器
 *
 * <AUTHOR>
 * @since 2024-07-04 16:43:18
 */

@RestController
@Api(tags = "特批/工单产品补录记录表")
@RequestMapping("/proj_product_supplementary_record")
public class ProjProductSupplementaryRecordController {

    @Resource
    private ProjProductSupplementaryRecordService projProductSupplementaryRecordService;

    /**
     * 说明: 产品信息-特批产品、补录工单产品
     *
     * @param supplementaryRecordDTO
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.product.InitProductSupplementaryRecordVO>
     * @author: Yhongmin
     * @createAt: 2024/7/5 9:14
     * @remark: Copyright
     */
    @Log(operName = "产品信息", operDetail = "产品信息-补录工单特批产品初始化接口", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "产品信息-补录工单特批产品初始化接口")
    @ResponseBody
    @ApiOperation("产品信息-特批产品、补录工单产品")
    @PostMapping(value = "/initProductSupplementaryRecord")
    Result<InitProductSupplementaryRecordVO> initProductSupplementaryRecord(@RequestBody InitProductSupplementaryRecordDTO supplementaryRecordDTO) {
        return projProductSupplementaryRecordService.initProductSupplementaryRecord(supplementaryRecordDTO);
    }

    /**
     * 说明: 产品信息-根据客户id查询所有工单信息
     *
     * @param customInfoId
     * @return:com.msun.csm.common.model.Result<com.msun.csm.common.model.BaseIdNameResp>
     * @author: Yhongmin
     * @createAt: 2024/7/5 9:04
     * @remark: Copyright
     */
    @Log(operName = "产品信息", operDetail = "产品信息-根据客户id查询所有工单信息", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "产品信息-根据客户id查询所有工单信息")
    @ResponseBody
    @ApiOperation("产品信息-根据客户id查询所有工单信息")
    @GetMapping(value = "/getOrderInfoDeliveryOrderNo")
    public Result<List<InitProductSupplementaryRecordVO.DictSupplementaryRecord>> getOrderInfoDeliveryOrderNo(@RequestParam(value = "customInfoId") @ApiParam(value = "customInfoId", example = "客户id", required = true) Long customInfoId) {
        return projProductSupplementaryRecordService.getOrderInfoDeliveryOrderNo(customInfoId);
    }

    /**
     * 保存
     *
     * @param dto 数据传输对象
     */
    @Log(operName = "产品信息", operDetail = "产品信息-补录工单特批产品保存或更新", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "产品信息-补录工单特批产品保存或更新")
    @ApiOperation("保存或更新")
    @PostMapping("/save")
    public Result<String> save(@ApiParam(value = "信息", example = "信息", required = true) @Validated(SaveGroup.class) @RequestBody ProjProductSupplementaryRecordDTO dto) {
        return this.projProductSupplementaryRecordService.save(dto);
    }

    /**
     * 根据id查询信息
     *
     * @param id id
     * @return vo 对象
     */
    @ApiOperation("根据id查询信息")
    @GetMapping("/getProjProductSupplementaryRecordById")
    public ProjProductSupplementaryRecordVO getProjProductSupplementaryRecordById(@ApiParam(value = "id", example = "id", required = true) Long id) {
        return this.projProductSupplementaryRecordService.getProjProductSupplementaryRecordById(id);
    }

    /**
     * 根据id查询信息
     *
     * @param id id
     * @return vo 对象
     */
    @ApiOperation("根据id删除")
    @GetMapping("/delete")
    public Result delete(@ApiParam(value = "id", required = true) Long id) {
        return this.projProductSupplementaryRecordService.delete(id);
    }
}
