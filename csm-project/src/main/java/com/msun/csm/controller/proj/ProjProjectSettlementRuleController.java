package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckUploadDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleDelFileDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleQueryDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleSaveDTO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementSubmitEntryVO;
import com.msun.csm.service.proj.ProjProjectSettlementRuleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */
@Slf4j
@RestController
@RequestMapping("/projProjectSettlementRule")
@Api(tags = "入驻申请-项目入驻规则记录表相关接口")
public class ProjProjectSettlementRuleController {

    @Resource
    private ProjProjectSettlementRuleService projProjectSettlementRuleService;


    @ApiOperation("入驻申请条件查询")
    @Log(operName = "入驻申请条件查询", operDetail = "入驻申请条件查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "入驻项目规则查询")
    @PostMapping("findProjProjectSettlementConditionList")
    public Result<ProjProjectSettlementSubmitEntryVO> findProjProjectSettlementConditionList(@RequestBody ProjProjectSettlementRuleQueryDTO projectSettlementConditionDTO) {
        return projProjectSettlementRuleService.findProjProjectSettlementRuleList(projectSettlementConditionDTO);
    }

    @Log(operName = "入驻申请条件保存", operDetail = "入驻申请条件保存", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "入驻申请条件保存", saveParam = true)
    @PostMapping("saveSettlement")
    public Result<Boolean> saveSettlement(@RequestBody ProjProjectSettlementRuleSaveDTO settlementRuleSaveDTO) {
        return projProjectSettlementRuleService.saveSettlement(settlementRuleSaveDTO);
    }

    @Log(operName = "删除入场条件上传的文件", operDetail = "删除入场条件上传的文件", operLogType = Log.LogOperType.DEL,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "删除入场条件上传的文件")
    @PostMapping("delSettlementFile")
    public Result<Boolean> delSettlementFile(@RequestBody ProjProjectSettlementRuleDelFileDTO settlementRuleDelFileDTO) {
        return projProjectSettlementRuleService.delSettlementFile(settlementRuleDelFileDTO);
    }

    @Log(operName = "上传调研报告", operDetail = "上传调研报告", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "上传调研报告")
    @PostMapping("uploadFile")
    public Result<Boolean> uploadFile(ProjProjectSettlementCheckUploadDTO settlementCheckUploadDTO) {
        return projProjectSettlementRuleService.uploadFile(settlementCheckUploadDTO);
    }

}
