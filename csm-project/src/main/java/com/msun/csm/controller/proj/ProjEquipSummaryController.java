package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjEquipDocDTO;
import com.msun.csm.model.dto.ProjEquipSummaryDTO;
import com.msun.csm.model.dto.UrgeProcessingDTO;
import com.msun.csm.model.vo.DictEquipInfoVO;
import com.msun.csm.model.vo.EquipSummaryVo;
import com.msun.csm.model.vo.OldToEquipConfigVO;
import com.msun.csm.model.vo.ProjEquipDocVO;
import com.msun.csm.model.vo.ProjEquipReadySummaryVO;
import com.msun.csm.model.vo.ProjEquipRecordLogVO;
import com.msun.csm.model.vo.ProjEquipSummaryVO;
import com.msun.csm.model.vo.ProjProjectFileRuleVO;
import com.msun.csm.service.proj.ProjEquipSummaryService;

import feign.Param;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @description: 设备调研汇总Controller
 * @fileName: ProjEquipSummaryController.java
 * @author: lius3
 * @createAt: 2024/10/11 9:18
 * @updateBy: lius3
 * @remark: Copyright
 */
@Api (tags = "设备调研汇总")
@RestController
@RequestMapping ("/projEquipSummary")
public class ProjEquipSummaryController {

    @Resource
    private ProjEquipSummaryService projEquipSummaryService;

    /**
     * 设备调研汇总信息
     *
     * @param projEquipSummaryDTO
     * @return
     */
    @Log (
            operName = "设备调研汇总信息", operDetail = "设备调研汇总信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "设备调研汇总信息"
    )
    @ApiOperation ("设备调研汇总信息")
    @PostMapping (value = "/findProjEquipSummaryInfo")
    Result<List<ProjEquipSummaryVO>> findProjEquipSummaryInfo(@RequestBody ProjEquipSummaryDTO projEquipSummaryDTO) {
        return this.projEquipSummaryService.findProjEquipSummaryInfo(projEquipSummaryDTO);
    }

    /**
     * 获取设备左侧动态菜单
     *
     * @param projectInfoId
     * @return
     */
    @ApiOperation ("获取设备左侧动态菜单")
    @Log (operName = "设备", operDetail = "获取设备左侧动态菜单", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备左侧动态菜单")
    @GetMapping (value = "/selectSurveyEquipMenu")
    Result<List<EquipSummaryVo>> selectSurveyEquipMenu(@RequestParam ("projectInfoId") Long projectInfoId) {
        return projEquipSummaryService.selectSurveyEquipMenu(projectInfoId);
    }

    /**
     * 更新设备调研 / 设备准备里程碑节点状态
     *
     * @param projEquipSummaryDTO
     * @return
     */
    @ApiOperation ("更新设备调研 / 设备准备里程碑节点状态")
    @Log (operName = "设备", operDetail = "更新设备调研 / 设备准备里程碑节点状态", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "更新设备调研 / 设备准备里程碑节点状态")
    @PostMapping (value = "/updateEquipMilestoneInfo")
    Result updateEquipMilestoneInfo(@RequestBody ProjEquipSummaryDTO projEquipSummaryDTO) {
        return projEquipSummaryService.updateMilestoneInfo(projEquipSummaryDTO.getMilestoneInfoId());
    }

    @ApiOperation ("获取设备分类")
    @Log (operName = "设备", operDetail = "获取设备分类", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备分类")
    @GetMapping (value = "/selectSurveyEquipClass")
    Result<List<BaseIdNameResp>> selectSurveyEquipClass(@RequestParam ("projectInfoId") Long projectInfoId) {
        return projEquipSummaryService.selectSurveyEquipClass(projectInfoId);
    }

    @ApiOperation ("获取设备厂商")
    @Log (operName = "设备", operDetail = "获取设备厂商", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备厂商")
    @GetMapping (value = "/selectSurveyEquipFactory")
    Result<List<BaseIdNameResp>> selectSurveyEquipFactory(@RequestParam ("productCode") String productCode) {
        return projEquipSummaryService.selectSurveyEquipFactory(productCode, null);
    }

    @ApiOperation ("获取设备类型")
    @Log (operName = "设备", operDetail = "获取设备类型", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备类型")
    @GetMapping (value = "/selectSurveyEquipType")
    Result<List<BaseIdNameResp>> selectSurveyEquipType(@RequestParam("productCode") String productCode) {
        return projEquipSummaryService.selectSurveyEquipType(productCode, null);
    }

    @ApiOperation ("获取设备字典")
    @Log (operName = "设备", operDetail = "获取设备字典", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备字典")
    @GetMapping (value = "/selectSurveyEquipInfo")
    Result<List<DictEquipInfoVO>> selectSurveyEquipInfo(@RequestParam("productCode") String productCode) {
        return projEquipSummaryService.selectSurveyEquipInfo(productCode, null);
    }

    @ApiOperation ("获取设备属性字典")
    @Log (operName = "设备", operDetail = "获取设备属性字典", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备属性字典")
    @GetMapping (value = "/selectSurveyAttributesInfo")
    Result<List<BaseCodeNameResp>> selectSurveyAttributesInfo(@RequestParam ("equipAttributesCode") String equipAttributesCode) {
        return projEquipSummaryService.selectSurveyAttributesInfo(equipAttributesCode);
    }

    /**
     * 查询附件配置信息
     *
     * @param sceneCode
     * @param isMobile
     * @return
     */
    @ApiOperation ("查询附件配置信息")
    @Log (operName = "设备", operDetail = "查询附件配置信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询附件配置信息")
    @GetMapping (value = "/selectProjectFileConfig")
    Result<List<ProjProjectFileRuleVO>> selectProjectFileConfig(@RequestParam ("sceneCode") String sceneCode, Integer isMobile) {
        return projEquipSummaryService.selectProjectFileConfig(sceneCode, isMobile);
    }

    /**
     * 设备准备汇总信息
     *
     * @param projEquipSummaryDTO
     * @return
     */
    @Log (
            operName = "设备准备汇总信息", operDetail = "设备准备汇总信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "设备准备汇总信息"
    )
    @ApiOperation ("设备准备汇总信息")
    @PostMapping (value = "/findProjEquipReadySummaryInfo")
    Result<List<ProjEquipReadySummaryVO>> findProjEquipReadySummaryInfo(@RequestBody ProjEquipSummaryDTO projEquipSummaryDTO) {
        return projEquipSummaryService.findProjEquipReadySummaryInfo(projEquipSummaryDTO);
    }

    /**
     * 查询设备文档
     *
     * @param dto
     * @return
     */
    @ApiOperation ("查询设备文档")
    @Log (operName = "设备", operDetail = "查询设备文档", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询设备文档")
    @PostMapping (value = "/findProjEquipDoc")
    Result<List<ProjEquipDocVO>> findProjEquipDoc(@RequestBody ProjEquipDocDTO dto) {
        return projEquipSummaryService.findProjEquipDoc(dto);
    }

    /**
     * 查询老换新设备字典配置信息
     *
     * @param customInfoId
     * @return
     */
    @ApiOperation ("查询老换新设备字典配置信息")
    @Log (operName = "设备", operDetail = "查询老换新设备字典配置信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询老换新设备字典配置信息")
    @GetMapping (value = "/getOldToEquipConfig")
    Result<OldToEquipConfigVO> getOldToEquipConfig(@RequestParam Long customInfoId) {
        return projEquipSummaryService.getOldToEquipConfig(customInfoId);
    }

    /**
     * 客户的首期项目上没有Lis、Pacs时特批添加
     *
     * @param customInfoId
     * @return
     */
    @ApiOperation ("客户的首期项目上没有Lis、Pacs时特批添加")
    @Log (operName = "设备", operDetail = "客户的首期项目上没有Lis、Pacs时特批添加", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "客户的首期项目上没有Lis、Pacs时特批添加")
    @GetMapping (value = "/saveProductFroHisProjectInfo")
    Result saveProductFroHisProjectInfo(@RequestParam("customInfoId") Long customInfoId) {
        return projEquipSummaryService.saveProductFroHisProjectInfo(customInfoId);
    }

    /**
     * 查询设备操作日志
     * @param equipRecordId
     * @param equipRecordBusinessId
     * @return
     */
    @ApiOperation ("查询设备操作日志")
    @Log (operName = "设备", operDetail = "查询设备操作日志", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询设备操作日志")
    @GetMapping (value = "/findEquipOperateLog")
    Result<List<ProjEquipRecordLogVO>> findEquipOperateLog(@Param("equipRecordId") Long equipRecordId, @Param("equipRecordBusinessId") Long equipRecordBusinessId) {
        return projEquipSummaryService.findEquipOperateLog(equipRecordId, equipRecordBusinessId);
    }

    /**
     * 催办
     *
     * @param dto
     * @return
     */
    @ApiOperation ("催办")
    @Log (operName = "设备", operDetail = "催办", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "催办")
    @PostMapping (value = "/urgeProcessing")
    Result urgeProcessing(@RequestBody UrgeProcessingDTO dto) {
        return projEquipSummaryService.urgeProcessing(dto);
    }
}
