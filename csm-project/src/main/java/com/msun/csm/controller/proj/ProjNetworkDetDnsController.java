package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetDnsRecordDTO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetDnsRecordVO;
import com.msun.csm.service.proj.ProjNetworkDetDnsRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-05-16 04:01:06
 */
@Slf4j
@RestController
@RequestMapping("/projNetworkDetDns")
@Api(tags = "终端Dns网络检测情况一览表相关接口")
public class ProjNetworkDetDnsController {

    @Resource
    private ProjNetworkDetDnsRecordService projNetworkDetDnsRecordService;

    /**
     * 医院Dns网络检测情况列表
     *
     * @return
     */
    @Log(
            operName = "终端网络检Dns测情况列表", operDetail = "终端网络检Dns测情况列表",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "终端网络检Dns测情况列表"
    )
    @ApiOperation("终端网络检Dns测情况列表-分页查询")
    @PostMapping(value = "/findNetworkDetDnsInfoList")
    Result<PageInfo<ProjNetworkDetDnsRecordVO>> findNetworkDetDnsInfoList(
            @RequestBody ProjNetworkDetDnsRecordDTO projNetworkDetDnsRecordDTO) {
        return projNetworkDetDnsRecordService.findNetworkDetDnsInfoList(projNetworkDetDnsRecordDTO);
    }
}
