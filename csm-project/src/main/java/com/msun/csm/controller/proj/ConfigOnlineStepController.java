package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ConfigOnlineStepDTO;
import com.msun.csm.model.dto.ConfigOnlineStepSelectDTO;
import com.msun.csm.model.vo.ConfigOnlineStepVO;
import com.msun.csm.service.config.ConfigOnlineStepService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/06/16:05
 */
@Slf4j
@Api (tags = "项目上线步骤配置管理Controller")
@RestController
@RequestMapping ("/configOnlineStep")
public class ConfigOnlineStepController {

    @Resource
    private ConfigOnlineStepService configOnlineStepService;

    /**
     * 项目上线步骤配置-查询上线步骤配置列表
     */
    @ApiOperation ("项目上线步骤配置-查询上线步骤配置列表")
    @Log (operName = "项目上线步骤配置-查询上线步骤配置列表", operDetail = "项目上线步骤配置-查询上线步骤配置列表",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目上线步骤配置-查询上线步骤配置列表")
    @PostMapping (value = "/selectConfigOnlineStepList")
    Result<List<ConfigOnlineStepVO>> selectConfigOnlineStepList(@RequestBody ConfigOnlineStepSelectDTO dto) {
        return configOnlineStepService.selectConfigOnlineStepList(dto);
    }

    /**
     * 项目上线步骤配置-修改上线步骤配置信息
     */
    @ApiOperation ("项目上线步骤配置-修改上线步骤配置信息")
    @Log (operName = "项目上线步骤配置-修改上线步骤配置信息", operDetail = "项目上线步骤配置-修改上线步骤配置信息",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目上线步骤配置-修改上线步骤配置信息")
    @PostMapping (value = "/updateConfigOnlineStep")
    Result updateConfigOnlineStep(@RequestBody ConfigOnlineStepDTO dto) {
        return configOnlineStepService.updateConfigOnlineStep(dto);
    }

    /**
     * 项目上线步骤配置-删除上线步骤配置信息
     */
    @ApiOperation ("项目上线步骤配置-删除上线步骤配置信息")
    @Log (operName = "项目上线步骤配置-删除上线步骤配置信息", operDetail = "项目上线步骤配置-删除上线步骤配置信息",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目上线步骤配置-删除上线步骤配置信息")
    @PostMapping (value = "/deleteConfigOnlineStep")
    Result deleteConfigOnlineStep(@RequestBody ConfigOnlineStepDTO dto) {
        return configOnlineStepService.deleteConfigOnlineStep(dto.getConfigOnlineStepId());
    }

    /**
     * 项目上线步骤配置-查询上线步骤字典数据
     */
    @ApiOperation ("项目上线步骤配置-查询上线步骤字典数据")
    @Log (operName = "项目上线步骤配置-查询上线步骤字典数据", operDetail = "项目上线步骤配置-查询上线步骤字典数据",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目上线步骤配置-查询上线步骤字典数据")
    @PostMapping (value = "/selectDictOnlineStepData")
    Result<List<BaseIdNameResp>> selectDictOnlineStepData() {
        return configOnlineStepService.selectDictOnlineStepData();
    }
}
