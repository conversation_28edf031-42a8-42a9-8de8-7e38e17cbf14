package com.msun.csm.controller.api.lis;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.enums.ReviewTypeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.controller.lis.LisAnalyManagerApi;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsAims;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsHd;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsAimsMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsHdMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsLisMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.lis.UpdateEquipStatusDTO;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.proj.ProjEquipRecordLogService;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/31/14:04
 */
@Slf4j
@RestController
public class LisAnalyManagerApiImpl implements LisAnalyManagerApi {

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjEquipRecordVsLisMapper equipRecordVsLisMapper;

    @Resource
    private ProjEquipRecordVsHdMapper equipRecordVsHdMapper;

    @Resource
    private ProjEquipRecordMapper equipRecordMapper;

    @Resource
    private ProjEquipRecordVsAimsMapper equipRecordVsAimsMapper;

    @Resource
    private ProjEquipRecordLogService projEquipRecordLogService;

    @Resource
    private SendBusinessMessageService sendBusinessMessageService;

    // 设备发布回调交付平台更新状态
    @Override
    @CsmSign
    @Transactional(rollbackFor = Exception.class)
    public Result updateEquipStatus(Map<String, Object> param) {
        UpdateEquipStatusDTO updateEquipStatusDTO = new UpdateEquipStatusDTO();
        updateEquipStatusDTO.setProductCode(param.get("productCode").toString());
        updateEquipStatusDTO.setEquipId(Convert.toLong(param.get("equipId")));
        updateEquipStatusDTO.setEquipStatus(Convert.toInt(param.get("equipStatus")));
        updateEquipStatusDTO.setRejectReason(Convert.toStr(param.get("rejectReason")));
        updateEquipStatusDTO.setAccount(Convert.toStr(param.get("account")));
        Date date = new Date();
        log.info("Lis解析平台调用参数=========param:======{}", param);
        // 查询操作人信息
        SysUser user = sysUserMapper.selectOne(new QueryWrapper<SysUser>()
                .eq("account", updateEquipStatusDTO.getAccount())
        );
        if (user == null) {
            return Result.fail("操作人信息不存在");
        }
        Long recordId = -1L;
        // 根据产品code判断 哪个产品进行修改设备状态
        switch (updateEquipStatusDTO.getProductCode()) {
            case "Lis":
                log.info("更新Lis设备状态=========param:======{}", updateEquipStatusDTO);
                // 更新lis设备的状态
                ProjEquipRecordVsLis projEquipRecordVsLis = equipRecordVsLisMapper.selectById(
                        updateEquipStatusDTO.getEquipId());
                recordId = projEquipRecordVsLis.getEquipRecordId();
                ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(recordId);
                // 驳回
                if (updateEquipStatusDTO.getEquipStatus() == 2) {
//                    sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_SUBMIT_AUDIT.getBusinessTable());
                    // 单工
                    if (Integer.valueOf(0).equals(projEquipRecordVsLis.getDuplexFlag())) {
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_DG.getBusinessTable());
                    } else {
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_SG.getBusinessTable());
                    }

                    sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
                            ReviewTypeEnum.LIS_EQUIP_RESUBMIT_FOR_REVIEW,
                            projEquipRecord.getProjectInfoId(),
                            projEquipRecord.getEquipRecordId(),
                            sendBusinessMessageService.getPenaltyPerson(projEquipRecord.getCreaterId(), projEquipRecord.getProjectInfoId()),
                            String.format("LIS设备提交审核超期【%s/%s】", projEquipRecord.getEquipFactoryName(), projEquipRecord.getEquipTypeName()),
                            new Date()
                    );
                }
                // 审核通过，接收申请
//                if (updateEquipStatusDTO.getEquipStatus() == 3) {
//                    sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_SUBMIT_AUDIT.getBusinessTable());
//                }

                // 制作完成
                if (updateEquipStatusDTO.getEquipStatus() == 4) {
                    // 双工
                    if (Integer.valueOf(0).equals(projEquipRecordVsLis.getDuplexFlag())) {
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_DG.getBusinessTable());
                    } else {
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_SG.getBusinessTable());
                    }
                }
                break;
            case "Hd":
                // 更新血透设备状态
                log.info("更新血透设备状态=========param:======{}", updateEquipStatusDTO);
                ProjEquipRecordVsHd projEquipRecordVsHd = equipRecordVsHdMapper.selectById(
                        updateEquipStatusDTO.getEquipId());
                recordId = projEquipRecordVsHd.getEquipRecordId();
                break;
            case "Aims":
                log.info("更新Aims设备状态=========param:======{}", updateEquipStatusDTO);
                ProjEquipRecordVsAims projEquipRecordVsAims = equipRecordVsAimsMapper.selectById(updateEquipStatusDTO.getEquipId());
                recordId = projEquipRecordVsAims.getEquipRecordId();
                break;
            default:
                return Result.fail("产品code不存在");
        }
        // 查询公共表数据 【修改状态需要修改公共表的状态值】
        ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(recordId);
        projEquipRecord.setEquipStatus(updateEquipStatusDTO.getEquipStatus());
        projEquipRecord.setUpdaterId(user.getSysUserId());
        projEquipRecord.setUpdateTime(date);
        projEquipRecord.setRejectReason(updateEquipStatusDTO.getRejectReason());
        equipRecordMapper.updateById(projEquipRecord);
        //保存操作日志
        ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
        projEquipRecordLog.setEquipRecordBusinessId(updateEquipStatusDTO.getEquipId());
        projEquipRecordLog.setYyProductId(projEquipRecord.getYyProductId());
        projEquipRecordLog.setOperatorId(user.getSysUserId());
        projEquipRecordLog.setOperatorName(user.getUserName());
        projEquipRecordLog.setTel(user.getPhone());
        if (updateEquipStatusDTO.getEquipStatus() == 2) {
            //审核驳回
            projEquipRecordLog.setOperateName("审核驳回");
            projEquipRecordLog.setOperateContent(updateEquipStatusDTO.getRejectReason());
        } else if (updateEquipStatusDTO.getEquipStatus() == 3) {
            //审核通过
            projEquipRecordLog.setOperateName("审核通过");
        } else if (updateEquipStatusDTO.getEquipStatus() == 4) {
            //发布--研发完成
            projEquipRecordLog.setOperateName("研发完成");
        }
        projEquipRecordLogService.saveLog(projEquipRecordLog);
        // 业务处理
        return Result.success();
    }
}
