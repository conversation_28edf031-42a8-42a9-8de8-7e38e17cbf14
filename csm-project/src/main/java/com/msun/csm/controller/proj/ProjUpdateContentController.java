package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjMilestoneManualUpdateLog;
import com.msun.csm.model.dto.ProjMilestoneManualUpdateLogQueryDto;
import com.msun.csm.model.req.project.UpdateProjectContentReq;
import com.msun.csm.service.proj.ProjMilestoneInfoService;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/10/9
 */
@Api(tags = "更新项目状态")
@RestController
@RequestMapping("/projUpdateContent")
@Slf4j
public class ProjUpdateContentController {

    @Resource
    private ProjMilestoneInfoService projMilestoneInfoService;

    /**
     * 查询所需参数
     *
     * @param updateProjectContentReq
     * @return
     */
    @Log(operName = "查询所需参数", operDetail = "查询所需参数", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询所需参数")
    @ApiOperation("查询所需参数")
    @PostMapping(value = "/getMilestoneParamer")
    Result<List<BaseIdNameResp>> getMilstoneAndProductParamer(@RequestBody UpdateProjectContentReq updateProjectContentReq) {
        return projMilestoneInfoService.getMilstoneAndProductParamer(updateProjectContentReq);
    }


    /**
     * 更新项目里程碑/产品信息
     *
     * @param updateProjectContentReq
     * @return
     */
    @Log(operName = "里程碑/产品更新", operDetail = "里程碑/产品更新", intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑/产品更新")
    @ApiOperation("里程碑/产品更新")
    @PostMapping(value = "/updateProjectMilestoneProductData")
    Result updateProjectMilestoneProductData(@RequestBody UpdateProjectContentReq updateProjectContentReq) {
        return projMilestoneInfoService.updateProjectMilestoneProductData(updateProjectContentReq);
    }

    /**
     * 获取项目里程碑手动更新操作日志的分页列表
     *
     * @param queryDto
     * @return
     */
    @Log(operName = "里程碑/操作日志列表查询", operDetail = "里程碑/操作日志列表查询", intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑/操作日志列表查询")
    @ApiOperation("里程碑/获取项目里程碑手动更新操作日志的分页列表")
    @PostMapping(value = "/queryMilestoneStatusOperateLogPageList")
    Result<PageInfo<ProjMilestoneManualUpdateLog>> queryMilestoneStatusOperateLogPageList(@RequestBody ProjMilestoneManualUpdateLogQueryDto queryDto) {
        return projMilestoneInfoService.queryMilestoneStatusOperateLogPageList(queryDto);
    }

}
