package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.empower.EmpowerRecord;
import com.msun.csm.model.dto.empower.ProductEmpowerAddRecordProductDto;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordDto;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordSaveDto;
import com.msun.csm.model.vo.dict.DictValue;
import com.msun.csm.model.vo.productempower.ProductEmpowerResultVO;
import com.msun.csm.model.vo.productempower.ProjProductEmpowerAddRecordVO;
import com.msun.csm.service.proj.ProjProductEmpowerAddRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-09-27 10:20:26
 */
@Slf4j
@RestController
@RequestMapping("/projProductEmpowerAddRecord")
@Api(tags = "产品授权补录记录表相关接口")
public class ProjProductEmpowerAddRecordController {

    @Resource
    private ProjProductEmpowerAddRecordService projProductEmpowerAddRecordService;

    @Log(
            operName = "产品授权补录列表查询", operDetail = "产品授权补录列表查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "产品授权补录列表查询"
    )
    @ApiOperation("产品授权补录列表查询")
    @PostMapping(value = "/findProductEmpowerAddRecord")
    Result<ProductEmpowerResultVO> findProductEmpowerAddRecord(@RequestBody ProjProductEmpowerAddRecordDto dto) {
        log.info("请求的参数, dto: {}", dto);
        Result<PageInfo<ProjProductEmpowerAddRecordVO>> result =
                projProductEmpowerAddRecordService.findProductEmpowerAddRecord(dto);
        ProductEmpowerResultVO resultVO = new ProductEmpowerResultVO();
        resultVO.setAddRecordVOPageInfo(result.getData());
        resultVO.setCustomInfoList(projProductEmpowerAddRecordService.findCustomInfo());
        return Result.success(resultVO);
    }

    @Log(
            operName = "获取待授权产品", operDetail = "待授权产品包含该客户下对应的项目类型中所有产品, 含所有状态", operLogType =
            Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取待授权产品"
    )
    @ApiOperation("获取待授权产品")
    @PostMapping(value = "/findProductNeedEmpower")
    Result<List<DictValue>> findProductNeedEmpower(@RequestBody ProductEmpowerAddRecordProductDto dto) {
        log.info("请求的参数, dto: {}", dto);
        return projProductEmpowerAddRecordService.findProductNeedEmpower(dto);
    }

    @Log(
            operName = "获取待授权产品菜单", operDetail = "获取待授权产品菜单", operLogType =
            Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取待授权产品菜单"
    )
    @ApiOperation("获取待授权产品模块")
    @PostMapping(value = "/findProductModuleNeedEmpower")
    Result<List<DictValue>> findProductModuleNeedEmpower() {
        return projProductEmpowerAddRecordService.findProductModuleNeedEmpower();
    }

    @Log(
            operName = "产品", operDetail = "产品授权补录新增编辑", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "产品授权补录新增编辑"
    )
    @ApiOperation("产品授权补录新增编辑")
    @PostMapping(value = "/save")
    Result<List<EmpowerRecord>> save(@Valid @RequestBody ProjProductEmpowerAddRecordSaveDto dto) {
        return projProductEmpowerAddRecordService.save(dto);
    }

}
