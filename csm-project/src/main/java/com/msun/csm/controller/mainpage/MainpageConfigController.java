package com.msun.csm.controller.mainpage;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.BasePageDTO;
import com.msun.csm.model.dto.mainpage.DictMainpageModuleDTO;
import com.msun.csm.model.dto.mainpage.MainpageRoleVsModuleDTO;
import com.msun.csm.model.dto.mainpage.SaveRoleVsModuleDTO;
import com.msun.csm.model.vo.mainpage.DictMainpageModuleVO;
import com.msun.csm.model.vo.mainpage.MainpageRoleVsModuleVO;
import com.msun.csm.service.mainpage.MainpageConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/mainPageConfig")
@Api(tags = "首页展示模块配置")
public class MainpageConfigController {

    @Resource
    private MainpageConfigService mainpageConfigService;

    /**
     * 查询首页模块字典
     * @param dto
     * @return
     */
    @ApiOperation("查询首页模块字典")
    @PostMapping(value = "/findDictModules")
    Result<List<DictMainpageModuleVO>> findDictModules(@RequestBody DictMainpageModuleDTO dto) {
        return mainpageConfigService.findDictModules(dto);
    }

    /**
     * 保存首页模块字典
     * @param dto
     * @return
     */
    @ApiOperation("保存首页模块字典")
    @PostMapping(value = "/saveDictModule")
    Result saveDictModule(@RequestBody DictMainpageModuleDTO dto) {
        return mainpageConfigService.saveDictModule(dto);
    }

    /**
     * 查询角色模块配置
     * @param dto
     * @return
     */
    @ApiOperation("查询角色模块配置")
    @PostMapping(value = "/findRoleVsModules")
    Result<List<MainpageRoleVsModuleVO>> findRoleVsModules(@RequestBody MainpageRoleVsModuleDTO dto) {
        return mainpageConfigService.findRoleVsModules(dto);
    }

    /**
     * 保存角色模块配置
     * @param dto
     * @return
     */
    @ApiOperation("保存角色模块配置")
    @PostMapping(value = "/saveRoleVsModules")
    Result saveRoleVsModules(@RequestBody SaveRoleVsModuleDTO dto) {
        return mainpageConfigService.saveRoleVsModules(dto);
    }

    /**
     * 查询当前登录用户的首页配置模块
     * @return
     */
    @ApiOperation("查询当前登录用户的首页配置模块")
    @PostMapping(value = "/findUserMainPageModules")
    Result<List<MainpageRoleVsModuleVO>> findUserMainpageModules() {
        return mainpageConfigService.findUserMainpageModules();
    }

    /**
     * 测试方法
     * @return
     */
    @ApiOperation("测试方法")
    @PostMapping(value = "/testMethod")
    Result testMethod() {
        return mainpageConfigService.testMethod();
    }

    /**
     * 测试方法
     * @param pageDTO
     * @return
     */
    @ApiOperation("测试方法")
    @PostMapping(value = "/testMethodByPage")
    Result<PageInfo<Map<String, Object>>> testMethodByPage(@RequestBody BasePageDTO pageDTO) {
        return mainpageConfigService.testMethodByPage(pageDTO);
    }
}
