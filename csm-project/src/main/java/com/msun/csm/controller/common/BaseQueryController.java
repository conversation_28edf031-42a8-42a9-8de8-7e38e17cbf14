package com.msun.csm.controller.common;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseHospitalNameResp;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameExtendResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.BaseProjectInfoResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SelectOption;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectMemberRoleInfo;
import com.msun.csm.model.dto.DirUserDTO;
import com.msun.csm.model.dto.TeamTypeCodeParam;
import com.msun.csm.model.param.GetSearchPeopleTypeParam;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormLookParamerReq;
import com.msun.csm.model.req.projectreview.QueryInfoReq;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormLookResp;
import com.msun.csm.model.resp.projectreview.UserModelAllResp;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.config.projectreview.ConfigProjectReviewTypeUserService;
import com.msun.csm.service.formlibrary.AimsHospitalFormService;
import com.msun.csm.service.proj.projform.PrintReportLimitService;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */
@RestController
@RequestMapping("/baseQuery")
@Api(tags = "前端下拉基础数据查询")
@Slf4j
public class BaseQueryController {

    @Resource
    private BaseQueryService baseQueryService;

    @Resource
    private AimsHospitalFormService aimsHospitalFormService;

    @Resource
    private PrintReportLimitService printReportLimitService;

    @Resource
    private ConfigProjectReviewTypeUserService configProjectReviewTypeUserService;


    /**
     * 基础查询--查询所有合同客户。返回Id、name基础信息
     */
    @GetMapping("/queryAllContractCustomer")
    public Result<List<BaseIdNameResp>> queryAllContractCustomer(@RequestParam(required = false) String keyword) {
        List<BaseIdNameResp> list = baseQueryService.queryAllContractCustomer(keyword);
        return Result.success(list);
    }

    /**
     * 基础查询--查询所有客户。返回Id、name基础信息
     */
    @GetMapping("/queryAllCustomer")
    public Result<List<BaseIdNameResp>> queryAllCustomer(@RequestParam(required = false) String keyword) {
        List<BaseIdNameResp> list = baseQueryService.queryAllCustomer(keyword);
        return Result.success(list);
    }

    /**
     * 基础查询--查询项目。返回Id、name基础信息
     */
    @GetMapping("/queryProject")
    public Result<List<BaseIdNameResp>> queryProject(@RequestParam(required = false) String keyword,
                                                     @RequestParam(required = false) Long customInfoId) {
        List<BaseIdNameResp> list = baseQueryService.queryProject(keyword, customInfoId);
        return Result.success(list);
    }

    /**
     * 基础查询--查询产品。返回Id、name基础信息
     */
    @GetMapping("/queryProduct")
    public Result<List<BaseIdNameResp>> queryProduct(@RequestParam(required = false) String keyword) {
        List<BaseIdNameResp> list = baseQueryService.queryProduct(keyword);
        return Result.success(list);
    }

    /**
     * 基础查询--查询部门。返回Id、name基础信息
     */
    @GetMapping("/queryDept")
    public Result<List<BaseIdNameResp>> queryDept(@RequestParam(required = false) String keyword) {
        List<BaseIdNameResp> list = baseQueryService.queryDept(keyword);
        return Result.success(list);
    }


    /**
     * 查询项目类型-单体/区域
     */
    @GetMapping("/getProjectType")
    public Result<List<BaseIdNameResp>> getProjectType() {
        List<BaseIdNameResp> list = baseQueryService.getProjectType();
        return Result.success(list);
    }

    /**
     * 查询项目升级方式-老换新/新客户
     */
    @GetMapping("/getProjectUpgradation")
    public Result<List<BaseIdNameResp>> getProjectUpgradation() {
        List<BaseIdNameResp> list = baseQueryService.getProjectUpgradation();
        return Result.success(list);
    }

    /**
     * 基础查询--查询医院。返回Id、name基础信息
     *
     * @param keyword
     * @param isOnlyDomain
     * @param customInfoId
     * @return
     */
    @GetMapping("/queryHospital")
    public Result<List<BaseHospitalNameResp>> queryHospital(@RequestParam(required = false) String keyword,
                                                            @RequestParam(required = false) Boolean isOnlyDomain,
                                                            @RequestParam(required = false) Long customInfoId,
                                                            @RequestParam(required = false) Long projectInfoId) {
        List<BaseHospitalNameResp> list = baseQueryService.queryHospital(keyword, isOnlyDomain, customInfoId, projectInfoId);
        return Result.success(list);
    }


    /**
     * 基础查询--查询表单资源库表单枚举类型。返回Id、name基础信息
     *
     * @return
     */
    @GetMapping("/queryLibFormType")
    public Result<List<BaseCodeNameResp>> queryLibFormType() {
        List<BaseCodeNameResp> list = baseQueryService.queryLibFormType();
        return Result.success(list);
    }

    /**
     * 查询验收后超30天客户限制
     *
     * @param projectInfoId
     * @param limitType     默认：0 接口  1打印报表 2统计报表 3表单 4设备
     * @return
     */
    @GetMapping("/queryAcceptanceExceedTimeLimit")
    public Result<Integer> queryAcceptanceExceedTimeLimit(@RequestParam(required = false) Long projectInfoId,
                                                          @RequestParam(required = false) Long hospitalInfoId,
                                                          @RequestParam(required = false) Integer limitType) {
        Integer count = baseQueryService.queryAcceptanceExceedTimeLimit(projectInfoId, hospitalInfoId, limitType);
        return Result.success(count);
    }


    /**
     * 查询项目下里程碑信息
     *
     * @param projectInfoId
     * @return
     */
    @GetMapping("/queryProjectMilestone")
    public Result<List<BaseIdNameExtendResp>> queryProjectMilestone(@RequestParam(required = false) Long projectInfoId) {
        List<BaseIdNameExtendResp> list = baseQueryService.queryProjectMilestone(projectInfoId);
        return Result.success(list);
    }

    /**
     * 查询项目下上线步骤信息
     *
     * @param projectInfoId
     * @return
     */
    @GetMapping("/queryProjectOnlineStep")
    public Result<List<BaseIdNameExtendResp>> queryProjectOnlineStep(@RequestParam(required = false) Long projectInfoId) {
        List<BaseIdNameExtendResp> list = baseQueryService.queryProjectOnlineStep(projectInfoId);
        return Result.success(list);
    }

    /**
     * 根据项目id查询项目信息
     *
     * @param projectInfoId
     * @return
     */
    @GetMapping("/getProjectByProjectInfoId")
    public Result<ProjProjectInfo> getProjectByProjectInfoId(@RequestParam(required = false) Long projectInfoId) {
        ProjProjectInfo projectInfo = baseQueryService.getProjectByProjectInfoId(projectInfoId);
        return Result.success(projectInfo);
    }

    /**
     * 病历编辑器预览样式
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "病历编辑器预览样式", operDetail = "病历编辑器预览样式", intLogType = Log.IntLogType.SELF_SYS, cnName = "选择手麻数据")
    @ApiOperation("病历编辑器预览样式")
    @PostMapping(value = "/previewEditorPdf")
    public Result<AimsHospitalFormLookResp> previewEditorPdf(@RequestBody AimsHospitalFormLookParamerReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.lookAimsForm(aimsHospitalFormParamerReq);
    }

    /**
     * 基础查询--查询首期项目。返回Id、name基础信息
     */
    @GetMapping("/queryFirstProject")
    @Log(operName = "基础查询--查询首期项目", operDetail = "基础查询--查询首期项目", intLogType = Log.IntLogType.SELF_SYS, cnName = "基础查询--查询首期项目")
    @ApiOperation("基础查询--查询首期项目")
    public Result<List<BaseIdNameResp>> queryFirstProject(@RequestParam(required = false) Long customInfoId) {
        List<BaseIdNameResp> list = baseQueryService.queryFirstProject(customInfoId);
        return Result.success(list);
    }


    /**
     * 查询客户类型
     *
     * @return
     */
    @Log(operName = "查询客户类型", operDetail = "基查询客户类型", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询客户类型")
    @ApiOperation("查询客户类型")
    @GetMapping("/queryCustomType")
    public Result<List<BaseIdNameResp>> queryCustomType() {
        List<BaseIdNameResp> list = baseQueryService.queryCustomType();
        return Result.success(list);
    }

    /**
     * 查询项目进度
     *
     * @return
     */
    @Log(operName = "查询项目进度", operDetail = "查询项目进度", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目进度")
    @ApiOperation("查询项目进度")
    @GetMapping("/queryProjectProgress")
    public Result<List<BaseIdNameResp>> queryProjectProgress() {
        List<BaseIdNameResp> list = baseQueryService.queryProjectProgress();
        return Result.success(list);
    }

    /**
     * 查询部门
     *
     * @param deptType 1 客服  2 销售  3 客服实施团队
     * @return
     */
    @Log(operName = "查询部门", operDetail = "查询部门", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询部门")
    @ApiOperation("查询部门")
    @GetMapping("/queryDeptByParamer")
    public Result<List<BaseIdNameResp>> queryDeptByParamer(@RequestParam(required = false) Integer deptType) {
        log.info("查询部门，deptType={}", deptType);
        List<BaseIdNameResp> list = baseQueryService.queryDeptByParamer(deptType);
        return Result.success(list);
    }


    /**
     * 查询小前端大后端使用的获取项目信息的下拉框
     *
     * @param customInfoId 客户ID
     */
    @GetMapping("/queryBackendProjectInfo")
    public Result<List<BaseProjectInfoResp>> queryBackendProjectInfo(Long customInfoId) {
        List<BaseProjectInfoResp> list = baseQueryService.queryBackendProjectInfo(customInfoId);
        return Result.success(list);
    }

    /**
     * 查询项目下代办类型数据
     *
     * @param projectInfoId 客户ID
     */
    @Log(operName = "查询项目下代办类型数据", operDetail = "查询项目下代办类型数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目下代办类型数据")
    @ApiOperation("查询项目下代办类型数据")
    @GetMapping("/queryProjectTodoTypeData")
    public Result<List<BaseIdNameResp>> queryProjectTodoTypeData(@RequestParam(required = false) Long projectInfoId) {
        List<BaseIdNameResp> list = baseQueryService.queryProjectTodoTypeData(projectInfoId);
        return Result.success(list);
    }

    /**
     * 查询业务状态下拉
     * businessCode 业务场景编码：surveyPlan-产品业务调研
     */
    @Log(operName = "查询业务状态下拉", operDetail = "查询业务状态下拉", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询业务状态下拉")
    @ApiOperation("查询业务状态下拉")
    @GetMapping("/getBusinessStatusList")
    public Result<List<BaseIdCodeNameResp>> getBusinessStatusList(@RequestParam(required = false) String businessCode, Long projectInfoId) {
        List<BaseIdCodeNameResp> list = baseQueryService.getBusinessStatusList(businessCode, projectInfoId);
        return Result.success(list);
    }

    /**
     * 查询项目计划阶段字典
     *
     * @return
     */
    @GetMapping("/getAllPlanStage")
    public Result<List<BaseCodeNameResp>> getAllPlanStage() {
        List<BaseCodeNameResp> list = baseQueryService.getAllPlanStage();
        return Result.success(list);
    }

    /**
     * 查询报表责任人下拉数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询责任人下拉数据", operDetail = "查询报表责任人下拉数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询报表责任人下拉数据")
    @ApiOperation("查询责任人下拉数据")
    @PostMapping(value = "/getDirUserList")
    Result<List<BaseIdNameResp>> getDirUserList(@RequestBody DirUserDTO dto) {
        List<BaseIdNameResp> dirUserList = printReportLimitService.getDirUserList(dto).getData().getDirPersonList();
        return Result.success(dirUserList);
    }

    /**
     * 根据后端服务团队类型查询后端服务团队
     *
     * @param param 团队类型编码：bustype-业务服务团队；datatype-数据服务团队；interfacetype-接口服务团队
     * @return 运维平台的后端团队信息
     */
    @Log(operName = "根据后端服务团队类型查询后端服务团队", operDetail = "根据后端服务团队类型查询后端服务团队", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据后端服务团队类型查询后端服务团队")
    @PostMapping(value = "/getBackendTeamByTypeCode")
    Result<List<BaseCodeNameResp>> getBackendTeamByTypeCode(@RequestBody TeamTypeCodeParam param) {
        return Result.success(baseQueryService.getBackendTeamByTypeCode(param.getTeamTypeCode()));
    }

    /**
     * 根据项目ID查询当前登陆人是否为对应的的前端项目经理或者后端项目经理
     *
     * @param param 项目ID
     * @return 是否为项目经理的标记
     */
    @Log(operName = "根据项目ID查询当前登陆人是否为对应的的前端项目经理或者后端项目经理", operDetail = "根据项目ID查询当前登陆人是否为对应的的前端项目经理或者后端项目经理", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据项目ID查询当前登陆人是否为对应的的前端项目经理或者后端项目经理")
    @PostMapping(value = "/getProjectMemberRoleInfo")
    Result<ProjectMemberRoleInfo> getProjectMemberRoleInfo(@RequestBody ProjectInfoId param) {
        return Result.success(baseQueryService.getProjectMemberRoleInfo(param.getProjectInfoId()));
    }

    /**
     * 查询众阳下下所有人
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询众阳下下所有人", operDetail = "查询众阳下下所有人", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询众阳下下所有人")
    @ApiOperation("查询众阳下下所有人")
    @PostMapping(value = "/getAllUserList")
    Result<List<BaseIdNameResp>> getAllUserList(@RequestBody DirUserDTO dto) {
        List<BaseIdNameResp> dirUserList = baseQueryService.getDirUserList(dto);
        return Result.success(dirUserList);
    }

    /**
     * 查询后端服务团队
     *
     * @return 后端服务团队
     */
    @PostMapping(value = "/getAllBackendTeam")
    Result<SelectOption> getAllBackendTeam() {
        return Result.success(baseQueryService.getAllBackendTeam());
    }

    /**
     * 获取智能助手进行问答
     *
     * @param dictAgentChatReq
     * @return
     */
    @PostMapping("/sendChatMessage")
    @ResponseBody
    @Log(operName = "获取智能助手进行问答", operDetail = "获取智能助手进行问答", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取智能助手进行问答")
    public Result sendChatMessage(@RequestBody DictAgentChatReq dictAgentChatReq) {
        return baseQueryService.sendChartMessage(dictAgentChatReq);
    }

    /**
     * 查询审核人集合
     * 取项目审核模式配置表关联配置， 前台维护页面：项目审核配置
     *
     * @param dto 入参: reviewTypeCode, projectInfoId
     *            业务类型编码：reviewTypeCode 取 dict_project_review_type字段 dict_code
     * @return
     */
    @PostMapping("/findUserModel")
    @ResponseBody
    @Log(operName = "查询审核人集合", operDetail = "查询审核人集合", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询审核人集合")
    public Result<UserModelAllResp> sendChatMessage(@RequestBody QueryInfoReq dto) {
        return Result.success(configProjectReviewTypeUserService.findUserModel(dto));
    }


    /**
     * 查询打印报表、统计报表、表单的调研、审核、制作页面的责任人类型
     */
    @PostMapping(value = "/getSearchPeopleType")
    Result<SelectOption> getSearchPeopleType(GetSearchPeopleTypeParam param) {
        return Result.success(baseQueryService.getSearchPeopleType(param));
    }


    /**
     * 查询打印报表、统计报表、表单的验证人下拉框
     *
     * @param projectInfoId 参数
     */
    @Log(operName = "查询打印报表、统计报表、表单的验证人下拉框", operDetail = "查询打印报表、统计报表、表单的验证人下拉框", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询打印报表、统计报表、表单的验证人下拉框")
    @PostMapping(value = "/queryIdentifierList")
    Result<List<BaseCodeNameResp>> queryIdentifierList(@RequestBody ProjectInfoId projectInfoId) {
        if (projectInfoId.getProjectInfoId() == null) {
            return Result.success(new ArrayList<>());
        }
        return baseQueryService.queryIdentifierList(projectInfoId);
    }

    /**
     * 查询纸张尺寸，用于打印报表纸张尺寸下拉
     *
     * @param printCode
     * @return
     */
    @Log(operName = "查询纸张尺寸，用于打印报表纸张尺寸下拉", operDetail = "查询纸张尺寸，用于打印报表纸张尺寸下拉", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询纸张尺寸，用于打印报表纸张尺寸下拉")
    @ApiOperation("查询纸张尺寸，用于打印报表纸张尺寸下拉")
    @GetMapping("/getPaperSizeList")
    Result<List<BaseIdCodeNameResp>> getPaperSizeList(@RequestParam(required = false) String printCode) {
        List<BaseIdCodeNameResp> dirUserList = baseQueryService.getPaperSizeList(printCode);
        return Result.success(dirUserList);
    }

    /**
     * 查询上线必备公共方法
     *
     * @param busCode 参数
     */
    @Log(operName = "查询上线必备公共方法", operDetail = "查询上线必备公共方法", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询上线必备公共方法")
    @GetMapping("/getOnlineStatusPublicData")
    Result<List<BaseIdNameResp>> getOnlineStatusPublicData(@RequestParam(required = true) String busCode) {
        return baseQueryService.getOnlineStatusPublicData(busCode);
    }


    /**
     * 查询项目下实施产品
     *
     * @param projectInfoId 项目ID
     * @return 项目下实施产品下拉
     */
    @ResponseBody
    @GetMapping(value = "/queryDeliverProductByProject")
    Result<List<BaseIdNameResp>> queryDeliverProductByProject(@RequestParam("projectInfoId") Long projectInfoId) {
        try {
            return baseQueryService.queryDeliverProductByProject(projectInfoId);
        } catch (Exception e) {
            log.error("查询项目下实施产品，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询项目下实施产品，发生错误：" + e.getMessage());
        }
    }


}
