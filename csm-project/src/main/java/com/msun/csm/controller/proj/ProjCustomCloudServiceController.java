package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeReq;
import com.msun.csm.model.dto.ProjCustomCloudServiceDTO;
import com.msun.csm.service.proj.ProjCustomCloudServiceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */
@Slf4j
@RestController
@RequestMapping("/projCustomCloudService")
@Api(tags = "客户云服务信息表相关接口")
public class ProjCustomCloudServiceController {

    @Resource
    private ProjCustomCloudServiceService projCustomCloudServiceService;

    /**
     * 说明: 云资源台账业务-根据云资源台账表向运维推送系统管理云资源截止时间
     *
     * @return:com.msun.csm.common.model.Result<java.lang.Boolean>
     * @author: Yhongmin
     * @createAt: 2024/8/20 16:13
     * @remark: Copyright
     */
    @Log(operName = "云资源台账业务", operDetail = "云资源台账业务-向运维推送云资源截止时间-补偿接口",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云资源台账业务--向运维推送云资源截止时间-补偿接口")
    @ApiOperation("云资源台账业务--向运维推送云资源截止时间-补偿接口")
    @PostMapping("/batchSendTimeToYunWei")
    public Result<SyncCloudTimeReq> batchSendTimeToYunWei(@Valid @RequestBody ProjCustomCloudServiceDTO dto) {
        return this.projCustomCloudServiceService.syncSendTimeToYunWei(dto);
    }


}
