package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.CompSurveyProductMilestone;
import com.msun.csm.model.dto.projsimulation.ProjSimulationUserDTO;
import com.msun.csm.model.dto.projsimulation.SaveProjSimulationRoleDTO;
import com.msun.csm.model.dto.projsimulation.SimulationResultDTO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationDetailVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationResultVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationRoleVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationUserVO;
import com.msun.csm.service.proj.ProjSimulationService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "全院流程模拟")
@RestController
@RequestMapping("/simulation")
public class ProjSimulationController {

    @Resource
    private ProjSimulationService projSimulationService;

    /**
     * 获取模拟角色
     * @param dto
     * @return
     */
    @ApiOperation("获取模拟角色")
    @PostMapping("/findSimulationRole")
    Result<List<ProjSimulationRoleVO>> findSimulationRole(@RequestBody ProjSimulationUserDTO dto) {
        return projSimulationService.findSimulationRole(dto);
    }

    /**
     * 获取模拟用户数据
     * @param dto
     * @return
     */
    @ApiOperation("获取模拟用户数据")
    @PostMapping("/findSimulationUser")
    Result<ProjSimulationUserVO> findSimulationUser(@RequestBody ProjSimulationUserDTO dto) {
        return projSimulationService.findSimulationUser(dto);
    }

    /**
     * 保存模拟用户数据
     * @param dto
     * @return
     */
    @ApiOperation("保存模拟用户数据")
    @PostMapping("/saveSimulationUser")
    Result saveSimulationUser(@RequestBody SaveProjSimulationRoleDTO dto) {
        return projSimulationService.saveSimulationUser(dto);
    }

    /**
     * 检验模拟用例是否发生变化
     * @param dto
     * @return
     */
    @ApiOperation("检验模拟用例是否发生变化")
    @GetMapping("/checkSimulationBusiness")
    Result<Boolean> checkSimulationBusiness(ProjSimulationUserDTO dto) {
        return projSimulationService.checkSimulationBusiness(dto);
    }

    /**
     * 导出模拟用例
     * @param response
     * @param dto
     */
    @ApiOperation("导出模拟用例")
    @PostMapping("/exportSimulationBusiness")
    void exportSimulationBusiness(HttpServletResponse response, @RequestBody ProjSimulationUserDTO dto) {
        projSimulationService.exportSimulationBusiness(response, dto);
    }

    /**
     * 查询模拟结果
     * @param dto
     * @return
     */
    @ApiOperation("查询模拟结果")
    @PostMapping("/findSimulationResult")
    Result<List<ProjSimulationResultVO>> findSimulationResult(@RequestBody SimulationResultDTO dto) {
        return projSimulationService.findSimulationResult(dto);
    }

    /**
     * 查询模拟结果明细
     * @param projSimulationResultId
     * @return
     */
    @ApiOperation("查询模拟结果明细")
    @GetMapping("/findSimulationDetail")
    Result<List<ProjSimulationDetailVO>> findSimulationDetail(@RequestParam("projSimulationResultId") Long projSimulationResultId, @RequestParam("simulationType") Long simulationType) {
        return projSimulationService.findSimulationDetail(projSimulationResultId, simulationType);
    }

    /**
     * 确认完成校验
     * @param dto
     * @return
     */
    @ApiOperation("确认完成校验")
    @PostMapping("/checkComplete")
    Result<Boolean> checkComplete(@RequestBody CompSurveyProductMilestone dto) {
        return projSimulationService.checkComplete(dto);
    }
}
