package com.msun.csm.controller.proj;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.msun.core.component.implementation.api.imsp.dto.AvailableLabQueryDTO;
import com.msun.core.component.implementation.api.imsp.dto.LisEquipmentItemDTO;
import com.msun.csm.common.annotation.BizDesc;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis;
import com.msun.csm.model.dto.LisCloudEquipSelectDTO;
import com.msun.csm.model.dto.LisEquipSelectDTO;
import com.msun.csm.model.dto.OldEquipImportDTO;
import com.msun.csm.model.dto.OldLisEquipImgDTO;
import com.msun.csm.model.dto.OldLisImportDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisListSaveDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisSaveDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.UpdateLisCloudEquipDTO;
import com.msun.csm.model.dto.ViewProjEquipRecordDTO;
import com.msun.csm.model.dto.device.EquipDetailItemDTO;
import com.msun.csm.model.dto.tip.ProjTipRecordDto;
import com.msun.csm.model.vo.BaseCodeNameVO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.LisEquipVo;
import com.msun.csm.model.vo.ProjEquipCheckVsLisVO;
import com.msun.csm.model.vo.ProjEquipRecordLisResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisWrapperVO;
import com.msun.csm.model.vo.device.EquipLisDetailItemVO;
import com.msun.csm.service.proj.ProjEquipRecordVsLisService;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * Lis设备Controller
 *
 * @Author: duxu
 * @Date: 2024/10/10/17:49
 */
@Slf4j
@Api(tags = "Lis设备Controller")
@RestController
@RequestMapping("/projEquipRecordVsLis")
public class ProjEquipRecordVsLisController {

    @Resource
    private ProjEquipRecordVsLisService projEquipRecordVsLisService;


    /**
     * 查询Lis设备列表数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询Lis设备列表数据")
    @Log(operName = "设备", operDetail = "查询Lis设备列表数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询Lis设备列表数据")
    @PostMapping(value = "/selectLisEquipData")
    Result<ProjEquipRecordLisResultVO> selectLisEquipData(@RequestBody LisEquipSelectDTO dto) {
        return projEquipRecordVsLisService.selectLisEquipData(dto);
    }

    /**
     * 设备信息发送到LIS解析平台
     *
     * @param dto
     * @return
     */
    @ApiOperation("设备信息发送到LIS解析平台")
    @Log(operName = "设备", operDetail = "设备信息发送到LIS解析平台", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "设备信息发送到LIS解析平台")
    @PostMapping(value = "/equipSendToLis")
    Result equipSendToLis(@RequestBody ProjEquipRecordVsLisDTO dto) {
        return projEquipRecordVsLisService.equipSendToLis(dto.getEquipRecordVsLisIds());
    }

    @ApiOperation("Lis平台回调分配接口制作人")
    @ACROSS
    @Log(operName = "Lis平台回调分配接口制作人", operDetail = "Lis平台回调分配接口制作人", operLogType = Log.LogOperType.NONE_MATCH,
            intLogType = Log.IntLogType.OTHER, cnName = "Lis平台回调分配接口制作人")
    @BizDesc(value = "Lis平台回调分配接口制作人", detail = "Lis平台回调分配接口制作人", person = "桂杰")
    @GetMapping(value = "/lisApiDevAssignCallback")
    public Result<?> lisApiDevAssignCallback(@RequestParam("equipRecordVsLisId") Long equipRecordVsLisId, @RequestParam("userYunyingId") String userYunyingId) {
        return projEquipRecordVsLisService.lisApiDevAssignCallback(equipRecordVsLisId, userYunyingId);
    }

    @ApiOperation("获取设备属性字典")
    @Log(operName = "设备", operDetail = "获取设备属性字典", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取设备属性字典")
    @GetMapping(value = "/selectSurveyAttributesInfo")
    Result<List<BaseCodeNameVO>> selectSurveyAttributesInfo(@RequestParam("equipAttributesCode") String equipAttributesCode) {
        return projEquipRecordVsLisService.selectSurveyAttributesInfo(equipAttributesCode);
    }

    /**
     * 删除LIS设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("删除LIS设备信息")
    @Log(operName = "设备", operDetail = "删除LIS设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "删除LIS设备信息")
    @PostMapping(value = "/deleteEquipToLis")
    Result deleteEquipToLis(@RequestBody ProjEquipRecordVsLisDTO dto) {
        return projEquipRecordVsLisService.deleteEquipToLis(dto.getEquipRecordVsLisId());
    }

    /**
     * 撤销LIS接口申请
     *
     * @param projEquipRecordVsLis
     * @return
     */
    @ApiOperation("撤销LIS接口申请")
    @Log(operName = "设备", operDetail = "撤销LIS接口申请", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "撤销LIS接口申请")
    @PostMapping(value = "/equipRevokeToLis")
    Result equipRevokeToLis(@RequestBody ProjEquipRecordVsLis projEquipRecordVsLis) {
        return projEquipRecordVsLisService.equipRevokeToLis(projEquipRecordVsLis.getEquipRecordVsLisId());
    }

    /**
     * 新增/编辑LIS设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("新增/编辑LIS设备信息")
    @Log(operName = "设备", operDetail = "新增/编辑LIS设备信息", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "新增/编辑LIS设备信息")
    @PostMapping(value = "/saveOrUpdateEquipToLis")
    Result saveOrUpdateEquipToLis(@RequestBody ProjEquipRecordVsLisDTO dto) {
        return projEquipRecordVsLisService.saveOrUpdateEquipToLis(dto);
    }

    /**
     * 保存LIS设备信息
     *
     * @param dto 请求保存内容, 含是否是手机端标识
     * @return 结果, 返回失败成功
     */
    @ACROSS
    @ApiOperation("保存LIS设备信息")
    @Log(operName = "设备", operDetail = "保存LIS设备信息", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "保存LIS设备信息")
    @PostMapping(value = "/saveDevice")
    Result<String> saveDevice(@RequestBody ProjEquipRecordVsLisSaveDTO dto) {
        log.info("保存LIS设备信息. dto: {}", JSONUtil.toJsonStr(dto));
        return projEquipRecordVsLisService.saveDevice(dto);
    }


    /**
     * lis保存列表设备信息
     *
     * @param dto 请求保存内容, 含是否是手机端标识
     * @return 结果, 返回失败成功
     */
    @ApiOperation("保存LIS设备信息-lis列表保存用")
    @Log(operName = "设备", operDetail = "保存LIS设备信息-lis列表保存用", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "保存LIS设备信息-lis列表保存用")
    @PostMapping(value = "/saveDeviceList")
    Result<String> saveDeviceList(@RequestBody ProjEquipRecordVsLisListSaveDTO dto) {
        log.info("lis保存列表设备信息. dto: {}", JSONUtil.toJsonStr(dto));
        return projEquipRecordVsLisService.saveDeviceList(dto);
    }

    /**
     * 查看LIS设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("查看LIS设备信息")
    @Log(operName = "设备", operDetail = "查看LIS设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查看LIS设备信息")
    @PostMapping(value = "/viewEquipToLis")
    Result<ProjEquipRecordVsLisVO> viewEquipToLis(@RequestBody ViewProjEquipRecordDTO dto) {
        return projEquipRecordVsLisService.viewEquipToLis(dto);
    }


    /**
     * 获取LIS设备信息
     *
     * @param dto 请求值
     * @return 结果, 带数据, 新增、编辑使用
     */
    @ApiOperation("获取LIS设备信息")
    @Log(operName = "设备", operDetail = "获取LIS设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取LIS设备信息")
    @PostMapping(value = "/getEquipToLis")
    public Result<ProjEquipRecordVsLisWrapperVO> getEquipToLis(@RequestBody ViewProjEquipRecordDTO dto) {
        return projEquipRecordVsLisService.getEquipToLis(dto);
    }

    /**
     * 获取LIS设备明细项
     *
     * @param equipDetailItemDTO 型号名称
     * @return 明细项
     */
    @ApiOperation("获取LIS设备明细项")
    @Log(operName = "设备", operDetail = "获取LIS设备明细项", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取LIS设备明细项")
    @PostMapping(value = "/findEquipDetail")
    public Result<EquipLisDetailItemVO> findEquipDetail(@Valid @RequestBody EquipDetailItemDTO equipDetailItemDTO) {
        log.info("入参: {}", JSONUtil.toJsonStr(equipDetailItemDTO));
        return projEquipRecordVsLisService.findEquipDetail(equipDetailItemDTO);
    }

//    /**
//     * 查询Lis系统云健康设备数据
//     *
//     * @param dto
//     * @return
//     */
//    @ApiOperation("查询Lis系统云健康设备数据")
//    @Log(operName = "设备", operDetail = "查询Lis系统云健康设备数据", operLogType = Log.LogOperType.SEARCH,
//            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询Lis系统云健康设备数据")
//    @PostMapping(value = "/selectCloudEquipData")
//    Result<List<CloudEquipVO>> selectCloudEquipData(@RequestBody LisEquipSelectDTO dto) {
//        return projEquipRecordVsLisService.selectCloudEquipData(dto);
//    }

    /**
     * 查询Lis设备列表数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询(Lis)系统云健康设备数据")
    @Log(operName = "设备", operDetail = "查询手麻(Lis)系统云健康设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询手麻(Lis)系统云健康设备数据")
    @PostMapping(value = "/selectCloudEquipData")
    Result<Map<String, List<CloudEquipVO>>> selectCloudEquipData(@RequestBody LisCloudEquipSelectDTO dto) {
        return projEquipRecordVsLisService.selectCloudEquipDataTransfer(dto);
    }

    /**
     * Lis设备自动对照云健康设备信息（根据设备名称进行匹配）
     *
     * @param dto
     * @return
     */
    @ApiOperation("Lis设备自动对照云健康设备信息（根据设备名称进行匹配）")
    @Log(operName = "设备", operDetail = "Lis设备自动对照云健康设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "Lis设备自动对照云健康设备信息")
    @PostMapping(value = "/compareEquipmentToLis")
    Result<List<LisEquipVo>> compareEquipmentToLis(@RequestBody LisEquipSelectDTO dto) {
        return projEquipRecordVsLisService.compareEquipmentToLis(dto);
    }

    /**
     * 调研阶段提交完成
     *
     * @return
     */
    @ApiOperation("调研阶段提交完成")
    @Log(operName = "设备", operDetail = "调研阶段提交完成", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "调研阶段提交完成")
    @PostMapping(value = "/commitFinish")
    Result commitFinish(@RequestBody ProjEquipVsProductFinishDTO dto) {
        return projEquipRecordVsLisService.commitFinish(dto);
    }

    /**
     * Lis设备调用开放平台进行设备检测
     *
     * @param dto
     * @return
     */
    @ApiOperation("Lis设备调用开放平台进行设备检测")
    @Log(operName = "设备", operDetail = "Lis设备调用开放平台进行设备检测", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "Lis设备调用开放平台进行设备检测")
    @PostMapping(value = "/checkEquipToLisAnalyManager")
    Result checkEquipToLisAnalyManager(@RequestBody ProjEquipRecordVsLisDTO dto) {
        return projEquipRecordVsLisService.checkEquipToLisAnalyManager(dto.getEquipRecordVsLisIds());
    }

    /**
     * LIS系统修改云健康对照设备
     *
     * @param dtoList
     * @return
     */
    @ApiOperation("LIS系统修改云健康对照设备")
    @Log(operName = "设备", operDetail = "LIS系统修改云健康对照设备", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "LIS系统修改云健康对照设备")
    @PostMapping(value = "/updateCloudEquipToLis")
    Result<String> updateCloudEquipToLis(@RequestBody List<UpdateLisCloudEquipDTO> dtoList) {
        return projEquipRecordVsLisService.updateCloudEquipToLis(dtoList);
    }

    /**
     * Lis设备一键检测、检测
     *
     * @return
     */
    @ApiOperation("一键检测、检测")
    @Log(operName = "设备", operDetail = "一键检测、检测", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "一键检测、检测")
    @PostMapping(value = "/checkSendToMsunVsLis")
    Result checkSendToMsunVsLis(@RequestBody ProjEquipSendToCloudDTO dto) {
        return projEquipRecordVsLisService.checkSendToMsunVsLis(dto);
    }

    /**
     * 查看检测进度
     *
     * @return
     */
    @ApiOperation("查看检测进度")
    @Log(operName = "设备", operDetail = "查看检测进度", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查看检测进度")
    @PostMapping(value = "/viewCheckProgress")
    Result<List<ProjEquipCheckVsLisVO>> viewCheckProgress(@RequestBody ProjEquipSendToCloudDTO dto) {
        return projEquipRecordVsLisService.viewCheckProgress(dto);
    }

    /**
     * 下载模版
     */
    @ApiOperation("下载模版")
    @Log(operName = "设备", operDetail = "下载模版", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "下载模版")
    @RequestMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, @RequestParam("projectInfoId") Long projectInfoId) {
        projEquipRecordVsLisService.downloadTemplate(response, projectInfoId);
    }

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @ApiOperation("导入模版数据")
    @Log(operName = "设备", operDetail = "导入模版数据", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "导入模版数据")
    @PostMapping(value = "/importExcelDatas")
    Result importExcelDatas(@RequestParam("file") MultipartFile multipartFile,
                            @RequestParam("customInfoId") Long customInfoId,
                            @RequestParam("projectInfoId") Long projectInfoId) {
        return projEquipRecordVsLisService.importExcelDatas(multipartFile, customInfoId, projectInfoId);
    }

    /**
     * 导出数据
     */
    @ApiOperation("导出数据")
    @Log(operName = "设备", operDetail = "下载模版", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "下载模版")
    @PostMapping(value = "/exportExcelDatas")
    public void exportExcelDatas(@RequestBody LisEquipSelectDTO dto, HttpServletResponse response) {
        projEquipRecordVsLisService.exportExcelDatas(dto, response);
    }

    /**
     * 老换新设备字典获取执行
     *
     * @param dto
     * @return
     */
    @ApiOperation("老换新设备字典获取执行")
    @Log(operName = "设备", operDetail = "老换新设备字典获取执行", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老换新设备字典获取执行")
    @PostMapping(value = "/executeImport")
    Result executeImport(@RequestBody OldEquipImportDTO dto) {
        return projEquipRecordVsLisService.executeImport(dto);
    }

    /**
     * 老系统Lis设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    @ApiOperation("老系统Lis设备迁移到新系统")
    @Log(operName = "设备", operDetail = "老系统Lis设备迁移到新系统", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老系统Lis设备迁移到新系统")
    @GetMapping(value = "/sendEquipToOldLis")
    Result sendEquipToOldLis(@RequestParam("projectInfoId") Long projectInfoId,
                             @RequestParam("oldEquipId") Long oldEquipId) {
        return projEquipRecordVsLisService.sendEquipToOldLis(projectInfoId, oldEquipId);
    }

    /**
     * 老系统Lis设备图片迁移到新系统
     *
     * @param dto
     * @return
     */
    @ApiOperation("老系统Lis设备图片迁移到新系统")
    @Log(operName = "设备", operDetail = "老系统Lis设备图片迁移到新系统", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老系统Lis设备图片迁移到新系统")
    @PostMapping(value = "/sendEquipImgToOldLis")
    Result sendEquipImgToOldLis(@RequestBody OldLisEquipImgDTO dto) {
        return projEquipRecordVsLisService.sendEquipImgToOldLis(dto);
    }

    /**
     * Lis老换新导入的设备确认资源库设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("Lis老换新导入的设备确认资源库设备信息")
    @Log(operName = "设备", operDetail = "Lis老换新导入的设备确认资源库设备信息", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "Lis老换新导入的设备确认资源库设备信息")
    @PostMapping(value = "/updateResourceEquipData")
    Result updateResourceEquipData(@RequestBody OldLisImportDTO dto) {
        return projEquipRecordVsLisService.updateResourceEquipData(dto);
    }

    /**
     * 保存老换新设备字典获取操作已读
     *
     * @param dto
     * @return
     */
    @ApiOperation("保存老换新设备字典获取操作已读")
    @Log(operName = "设备", operDetail = "保存老换新设备字典获取操作已读", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "保存老换新设备字典获取操作已读")
    @PostMapping(value = "/saveManualRead")
    Result saveManualRead(@RequestBody ProjTipRecordDto dto) {
        return projEquipRecordVsLisService.saveManualRead(dto);
    }

    /**
     * 查询老换新设备字典获取操作是否已读
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询老换新设备字典获取操作是否已读")
    @Log(operName = "设备", operDetail = "查询老换新设备字典获取操作是否已读", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询老换新设备字典获取操作是否已读")
    @PostMapping(value = "/getManualRead")
    Result<Boolean> getManualRead(@RequestBody ProjTipRecordDto dto) {
        return projEquipRecordVsLisService.getManualRead(dto);
    }

    @ApiOperation("获取Lis推荐检测项目")
    @ACROSS
    @Log(operName = "获取Lis推荐检测项目", operDetail = "获取Lis推荐检测项目", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取Lis推荐检测项目")
    @PostMapping(value = "/queryLabItemByEquipment")
    Result<List<LisEquipmentItemDTO>> queryLabItemByEquipment(@RequestBody AvailableLabQueryDTO dto) {
        return projEquipRecordVsLisService.queryLabItemByEquipment(dto);
    }
}
