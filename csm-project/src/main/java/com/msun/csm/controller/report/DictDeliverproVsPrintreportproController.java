package com.msun.csm.controller.report;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.report.DictDeliverproVsPrintreportpro;
import com.msun.csm.model.req.projreport.DictDeliverVsReportProductReq;
import com.msun.csm.model.resp.projreport.DictDeliverproVsPrintreportproResp;
import com.msun.csm.service.report.DictDeliverproVsPrintreportproService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/11/7
 */
@RestController
@Api(tags = "实施产品与报表产品对照")
@RequestMapping("/dictDeliverproVsPrintProduct")
public class DictDeliverproVsPrintreportproController {

    @Resource
    private DictDeliverproVsPrintreportproService dictDeliverproVsPrintreportproService;

    /**
     * 查询报表打印平台产品
     * @return
     */
    @GetMapping("/queryPrintProduct")
    public Result<List<BaseCodeNameResp>> queryPrintProduct() {
        List<BaseCodeNameResp> list = dictDeliverproVsPrintreportproService.queryPrintProduct();
        return Result.success(list);
    }


    /**
     * 查询实施产品
     * @return
     */
    @GetMapping("/findProductAllDeliver")
    public Result<List<BaseIdNameResp>> findProductAllDeliver() {
        List<BaseIdNameResp> list = dictDeliverproVsPrintreportproService.findProductAllDeliver();
        return Result.success(list);
    }


    /**
     * 查询实施产品与报表产品对照
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询实施产品与报表产品对照", operDetail = "查询实施产品与报表产品对照", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询实施产品与报表产品对照")
    @ApiOperation("查询实施产品与报表产品对照")
    @PostMapping(value = "/findReportPage")
    Result<PageInfo<DictDeliverproVsPrintreportproResp>> findDeliverVsPrintProduct(@RequestBody DictDeliverVsReportProductReq dto) {
        return dictDeliverproVsPrintreportproService.findDeliverVsPrintProduct(dto);
    }

    /**
     * 新增修改对照字典
     * @param dto
     * @return
     */
    @Log(
            operName = "新增修改对照字典", operDetail = "新增修改对照字典",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "新增修改对照字典"
    )
    @ApiOperation("新增修改对照字典")
    @PostMapping(value = "/saveVsProduct")
    Result saveVsProduct(@RequestBody DictDeliverproVsPrintreportpro dto) {
        return dictDeliverproVsPrintreportproService.saveVsProduct(dto);
    }

    /**
     * 删除对照字典
     * @param dto
     * @return
     */
    @Log(
            operName = "删除对照字典", operDetail = "删除对照字典",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "删除对照字典"
    )
    @ApiOperation("删除对照字典")
    @PostMapping(value = "/deleteVsProduct")
    Result deleteVsProduct(@RequestBody DictDeliverproVsPrintreportpro dto) {
        return dictDeliverproVsPrintreportproService.deleteVsProduct(dto);
    }

}
