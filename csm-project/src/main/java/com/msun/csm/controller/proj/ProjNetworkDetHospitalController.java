package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetDomainDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetHospitalDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetHospitalToolParamDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetLogFileDTO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetDomainVO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetHospitalRelativeVO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetHospitalStatusVO;
import com.msun.csm.service.proj.ProjNetworkDetHospitalService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-05-16 09:02:04
 */

@Slf4j
@RestController
@RequestMapping("/projNetworkDetHospital")
@Api(tags = "医院网络检测情况一览表相关接口")
public class ProjNetworkDetHospitalController {

    @Resource
    private ProjNetworkDetHospitalService projNetworkDetHospitalService;

    /**
     * 医院网络检测情况列表
     *
     * @return
     */
    @Log(
            operName = "医院网络检测情况列表", operDetail = "医院网络检测情况列表", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院网络检测情况列表"
    )
    @ApiOperation("医院网络检测情况列表-分页查询")
    @PostMapping(value = "/findNetworkDetHospitalInfoList")
    Result<PageInfo<ProjNetworkDetHospitalRelativeVO>> findNetworkDetHospitalInfoList(
            @RequestBody ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO) {
        return projNetworkDetHospitalService.findNetworkDetHospitalInfoList(projNetworkDetHospitalDTO);
    }

    /**
     * 医院网络检测情况列表
     *
     * @return
     */
    @Log(
            operName = "医院网络检测统计情况", operDetail = "医院网络检测统计情况", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院网络检测统计情况"
    )
    @ApiOperation("医院网络检测统计情况")
    @PostMapping(value = "/getNetworkDetHospitalStatusInfo")
    Result<ProjNetworkDetHospitalStatusVO> getNetworkDetHospitalStatusInfo(
            @RequestBody ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO) {
        return projNetworkDetHospitalService.getNetworkDetHospitalStatusInfo(projNetworkDetHospitalDTO);
    }

    /**
     * 医院网络检测情况列表
     *
     * @return
     */
    @Log(
            operName = "网络检测工具下载", operDetail = "网络检测工具下载", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "网络检测工具下载"
    )
    @ApiOperation("网络检测工具下载")
    @PostMapping(value = "/downloadNetworkDetectTool")
    void downloadNetworkDetectTool(@RequestBody ProjNetworkDetHospitalToolParamDTO toolParamDTO,
                                   HttpServletResponse response) {
        projNetworkDetHospitalService.downloadNetworkDetectTool(toolParamDTO, response);
    }

    /**
     * 日志文件下载
     *
     * @return
     */
    @Log(
            operName = "网络检测日志下载", operDetail = "网络检测日志下载", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "网络检测日志下载"
    )
    @ApiOperation("网络检测日志下载")
    @PostMapping(value = "/downloadNetworkLogFile")
    void downloadNetworkLogFile(@RequestBody ProjNetworkDetLogFileDTO dto, HttpServletResponse response) {
        projNetworkDetHospitalService.downloadNetworkLogFile(dto, response);
    }

    @Log(
            operName = "Linux说明文件下载", operDetail = "Linux说明文件下载", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "Linux说明文件下载"
    )
    @ApiOperation("Linux说明文件下载")
    @PostMapping(value = "/downloadNetworkDetDescTxt")
    void downloadNetworkDetDescTxt(HttpServletResponse response) {
        projNetworkDetHospitalService.downloadNetworkDetDescTxt(response);
    }

    /*--------------------------------  前置机 新增修改编辑 ---------------------------------*/


    /**
     * 网络检测linux前置机更新
     *
     * @return
     */
    @Log(
            operName = "网络检测linux前置机更新", operDetail = "网络检测linux前置机更新",
            operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "网络检测linux前置机更新"
    )
    @ApiOperation("网络检测linux前置机更新")
    @PostMapping(value = "/updateFrontMachine")
    Result updateFrontMachine(@RequestBody ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO) {
        if (ObjectUtil.isNotEmpty(projNetworkDetHospitalDTO)) {
            if (StrUtil.isEmpty(projNetworkDetHospitalDTO.getId())) {
                projNetworkDetHospitalDTO.setId(null);
            }
        }
        int count = projNetworkDetHospitalService.updateFrontMachineWithLinux(projNetworkDetHospitalDTO);
        return count > 0 ? Result.success() : Result.fail();
    }

    /**
     * 网络检测linux前置机更新
     *
     * @return
     */
    @Log(
            operName = "网络检测linux前置机更新", operDetail = "网络检测linux前置机更新",
            operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "网络检测linux前置机更新"
    )
    @ApiOperation("网络检测linux前置机更新")
    @PostMapping(value = "/getFrontMachineById")
    Result getFrontMachineById(@RequestBody ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO) {
        return projNetworkDetHospitalService.getFrontMachineById(projNetworkDetHospitalDTO);
    }

    /**
     * 删除网络检测linux前置机信息
     *
     * @return
     */
    @Log(
            operName = "删除-网络检测linux前置机", operDetail = "删除-网络检测linux前置机",
            operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "删除-网络检测linux前置机"
    )
    @ApiOperation("删除-网络检测linux前置机")
    @PostMapping(value = "/deleteFrontMachine")
    Result deleteFrontMachine(@RequestBody ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO) {
        int count = projNetworkDetHospitalService.deleteFrontMachine(projNetworkDetHospitalDTO);
        return count > 0 ? Result.success() : Result.fail();
    }

    /**
     * Window网络检测获取主院域名
     *
     * @return
     */
    @Log(
            operName = "Window网络检测获取主院域名", operDetail = "Window网络检测获取主院域名",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "Window网络检测获取主院域名"
    )
    @ApiOperation("Window网络检测获取主院域名")
    @PostMapping(value = "/getDomainAddress")
    Result getDomainAddress(@RequestBody ProjNetworkDetHospitalDTO hospitalDTO) {
        Result<PageInfo<ProjNetworkDetHospitalRelativeVO>> result =
                projNetworkDetHospitalService.findNetworkDetHospitalInfoList(hospitalDTO);
        PageInfo<ProjNetworkDetHospitalRelativeVO> pageInfo = result.getData();
        if (ObjectUtil.isNotEmpty(pageInfo) && CollUtil.isNotEmpty(pageInfo.getList())) {
            return Result.success(pageInfo.getList().get(0).getDetectDomain());
        }
        return Result.success(StrUtil.EMPTY);
    }

    @Log(
            operName = "获取可用域名列表", operDetail = "获取可用域名列表",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取可用域名列表"
    )
    @ApiOperation("获取可用域名列表")
    @PostMapping(value = "/getDomainAddressList")
    public Result<ProjNetworkDetDomainVO> getDomainAddressList(@RequestBody ProjNetworkDetDomainDTO domainDTO) {
        return Result.success(projNetworkDetHospitalService.getDomainAddressList(domainDTO));
    }


}
