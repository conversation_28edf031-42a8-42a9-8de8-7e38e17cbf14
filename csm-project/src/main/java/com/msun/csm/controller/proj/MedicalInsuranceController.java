package com.msun.csm.controller.proj;

import java.io.UnsupportedEncodingException;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.core.component.implementation.api.medinsur.dto.MedInsurInSettleResultDTO;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.param.OnlineValidateParam;
import com.msun.csm.service.proj.MedicalInsuranceService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/medicalInsurance")
public class MedicalInsuranceController {

    @Resource
    private MedicalInsuranceService yiBaoHeXinService;

    @GetMapping(value = "/jumpMedicalInsurance")
    Result<String> jumpMedicalInsurance(Long customInfoId, Long projectInfoId, Long hospitalInfoId) throws UnsupportedEncodingException {
        String result = yiBaoHeXinService.jumpMedicalInsurance(customInfoId, projectInfoId, hospitalInfoId);
        if ("noaccess".equals(result)) {
            return Result.fail("暂无医保接口开发权限！");
        }
        return Result.success(result);
    }

    @GetMapping(value = "/updateHospitalId")
    Result<String> updateHospitalRelativeInfo(Long newHospitalId, Long oldHospitalId) {
        log.info("手动更新医保核心医院ID，newHospitalId={}，oldHospitalId={}", newHospitalId, oldHospitalId);
        String result;
        try {
            result = yiBaoHeXinService.updateHospitalId(newHospitalId, oldHospitalId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("手动更新医保核心医院ID，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            yiBaoHeXinService.sendErrorMessage(e.getMessage(), newHospitalId, oldHospitalId);
            return Result.success(e.getMessage());
        }
    }

    @PostMapping(value = "/onlineVerification")
    Result<Boolean> onlineVerification(@RequestBody OnlineValidateParam param) {
        log.info("入驻前校验是否存在医保二开平台的代码，参数={}", JSON.toJSONString(param));
        boolean result = yiBaoHeXinService.onlineVerification(param);
        return Result.success(result);
    }


    @PostMapping(value = "/onlineVerificationTest")
    Result<List<MedInsurInSettleResultDTO>> onlineVerificationTest(Long hospitalInfoId) {
        log.info("准备阶段校验是否存在医保二开平台的代码，hospitalInfoId={}", hospitalInfoId);
        List<MedInsurInSettleResultDTO> result = yiBaoHeXinService.onlineVerification2(hospitalInfoId);
        return Result.success(result);
    }

}
