package com.msun.csm.controller.basedata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.msun.core.commons.id.IdGenerator;
import com.msun.core.component.implementation.api.knowledgeplatform.DeptsApi;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.basedata.*;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.basedata.*;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/5/26 14:37
 */
@Slf4j
@Api(tags = "前端展示数据查询API")
@RestController
@RequestMapping("/webData/")
public class WebDataController {
    @Autowired
    private BdImportPlatformRecordMapper bdImportPlatformRecordMapper;
    @Autowired
    private ProjCustomInfoMapper customInfoMapper;
    @Autowired
    private ProjProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Autowired
    private BdKtJobDependTableMapper bdKtJobDependTableMapper;
    @Autowired
    private BdTableInfoMapper bdTableInfoMapper;
    @Autowired
    private BdTableColInfoMapper bdTableColInfoMapper;
    @Autowired
    private BdKtJobMapper bdKtJobMapper;
    @Autowired
    private BdKtJobDependMapper bdKtJobDependMapper;
    @Autowired
    private BdDictTypeMapper bdDictTypeMapper;
    @Autowired
    private BdDictDataMapper bdDictDataMapper;

    @Autowired
    private BdDatasourceMapper datasourceMapper;
    @Autowired
    private DictProductMapper dictProductMapper;

    @Autowired
    private BdKtVarsMapper bdKtVarsMapper;
    @Qualifier("com.msun.core.component.implementation.api.knowledgeplatform.DeptsApi")
    @Autowired
    private DeptsApi deptsApi;


    @ApiOperation(value = "获取客户项目的级联选择器选项数据")
    @ACROSS
    @GetMapping(value = "getCustomProjectOptions")
    public Result<?> getCustomProjectOptions() {
        List<ProjCustomInfo> customs = new LambdaQueryChainWrapper<>(customInfoMapper)
                .select(ProjCustomInfo::getCustomName, ProjCustomInfo::getCustomInfoId)
                .eq(ProjCustomInfo::getIsDeleted, 0)
                .orderByDesc(ProjCustomInfo::getCustomInfoId)
                .list();
        List<ProjProjectInfo> projects = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .select(ProjProjectInfo::getProjectName, ProjProjectInfo::getProjectNumber, ProjProjectInfo::getProjectType, ProjProjectInfo::getCustomInfoId, ProjProjectInfo::getHisFlag)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .orderByDesc(ProjProjectInfo::getProjectInfoId)
                .list();
        Map<Long, List<ProjProjectInfo>> projectMap = projects.stream().collect(Collectors.groupingBy(ProjProjectInfo::getCustomInfoId));
        List<LinkedHashMap<String, Object>> options = customs.stream().map(i -> {
            LinkedHashMap<String, Object> obj = new LinkedHashMap<>();
            obj.put("label", i.getCustomName());
            obj.put("value", i.getCustomInfoId());
            List<ProjProjectInfo> projs = projectMap.getOrDefault(i.getCustomInfoId(), new ArrayList<>());
            obj.put("children", projs.stream().map(j -> {
                LinkedHashMap<String, Object> proj = new LinkedHashMap<>();
                proj.put("label", StrUtil.format("{}-{}({}{})", j.getProjectName(), j.getProjectNumber(), Objects.equals(j.getProjectType(), 1) ? "单体" : "区域", Objects.equals(j.getHisFlag(), 1) ? "首期" : "非首期"));
                proj.put("value", j.getProjectNumber());
                return proj;
            }).collect(Collectors.toList()));
            return obj;
        }).collect(Collectors.toList());
        return Result.success(options);
    }


    @ApiOperation(value = "获取当前医院项目医院信息")
    @ACROSS
    @GetMapping(value = "getAllHospital")
    public Result<List<ProjHospitalInfo>> getAllHospital(@RequestParam("projectNumber") String projectNumber) {
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .select(ProjProjectInfo::getCustomInfoId)
                .eq(ProjProjectInfo::getProjectNumber, projectNumber)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        List<ProjHospitalInfo> hospitalInfos = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                .eq(ProjHospitalInfo::getCustomInfoId, proj.getCustomInfoId())
                .eq(ProjHospitalInfo::getIsDeleted, 0)
                .list();
        return Result.success(hospitalInfos);
    }

    @ApiOperation(value = "获取当前医院所有导入记录")
    @ACROSS
    @GetMapping(value = "getAllImportRecords")
    public Result<IPage<BdImportPlatformRecord>> getAllImportRecords(
            @RequestParam("projectNumber") String projectNumber,
            @RequestParam(value = "hospitalId", required = false) Long hospitalId,
            @RequestParam("importStatus") Integer importStatus,
            @RequestParam("validStatus") Integer validStatus,
            @RequestParam("pageNo") Integer pageNo,
            @RequestParam("pageSize") Integer pageSize) {
        BdImportPlatformRecord args = new BdImportPlatformRecord();
        args.setProjectNumber(projectNumber);
        args.setHospitalId(hospitalId);
        if (importStatus != null && importStatus > 0) {
            args.setSuccess(importStatus == 1);
        } else {
            args.setSuccess(null);
        }
        if (validStatus != null && validStatus > -1) {
            args.setValidStatus(validStatus);
        } else {
            args.setValidStatus(null);
        }
        return Result.success(bdImportPlatformRecordMapper.findList(new Page<>(pageNo, pageSize, true), args));
    }

    @ApiOperation(value = "获取表定义列表")
    @ACROSS
    @GetMapping(value = "getTableDefs")
    public Result<IPage<BdTableInfo>> getTableDefs(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "keyword", required = false) String keyword) {
        return Result.success(new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                .eq(BdTableInfo::getIsDeleted, 0)
                .and(StrUtil.isNotBlank(keyword), i -> i.like(BdTableInfo::getTableNameCn, keyword).or().like(BdTableInfo::getTableNameEn, keyword))
                .orderByDesc(BdTableInfo::getCreateTime)
                .page(new Page<>(pageNo, pageSize, true)));
    }

    @ApiOperation(value = "获取表字段定义列表")
    @ACROSS
    @GetMapping(value = "getTableColDefs")
    public Result<List<BdTableColInfo>> getTableColDefs(@RequestParam("tableNameEn") String tableNameEn, @RequestParam(value = "keyword", required = false) String keyword) {
        return Result.success(new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                .eq(BdTableColInfo::getTableNameEn, tableNameEn)
                .and(StrUtil.isNotBlank(keyword), i -> i.like(BdTableColInfo::getColNameEn, keyword).or().like(BdTableColInfo::getColNameCn, keyword))
                .eq(BdTableColInfo::getIsDeleted, 0)
                .list());
    }

    @ApiOperation(value = "编辑表定义")
    @ACROSS
    @PostMapping(value = "editTableDef")
    public Result<?> editTableDef(@RequestBody BdTableInfo tableInfo) {
        if (tableInfo.getId() == null || tableInfo.getId() <= 0) {
            if (new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                    .eq(BdTableInfo::getIsDeleted, 0)
                    .eq(BdTableInfo::getTableNameEn, tableInfo.getTableNameEn())
                    .count() > 0) {
                return Result.fail("表定义已存在，请勿重复添加");
            }
            tableInfo.setId(IdGenerator.ins().generator());
            bdTableInfoMapper.insert(tableInfo);
        } else {
            bdTableInfoMapper.updateById(tableInfo);
        }
        return Result.success();
    }

    @ApiOperation(value = "编辑表字段定义")
    @ACROSS
    @PostMapping(value = "editTableColDef")
    public Result<?> editTableColDef(@RequestBody BdTableColInfo colInfo) {
        if (colInfo.getId() == null || colInfo.getId() <= 0) {
            if (new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                    .eq(BdTableColInfo::getIsDeleted, 0)
                    .eq(BdTableColInfo::getTableNameEn, colInfo.getTableNameEn())
                    .eq(BdTableColInfo::getColNameEn, colInfo.getColNameEn())
                    .count() > 0) {
                return Result.fail("表字段定义已存在，请勿重复添加");
            }
            colInfo.setId(IdGenerator.ins().generator());
            bdTableColInfoMapper.insert(colInfo);
        } else {
            bdTableColInfoMapper.updateById(colInfo);
        }
        return Result.success();
    }

    @ApiOperation(value = "删除表定义")
    @ACROSS
    @Transactional(rollbackFor = Exception.class)
    @DeleteMapping(value = "delTableDef/{id}")
    public Result<?> delTableDef(@PathVariable("id") Long id) {
        if (id == null || id <= 0) {
            return Result.fail("表定义ID不能为空");
        }
        BdTableInfo tableInfo = bdTableInfoMapper.selectById(id);
        if (tableInfo == null) {
            return Result.fail("表定义不存在");
        }
        if (new LambdaQueryChainWrapper<>(bdKtJobDependTableMapper)
                .eq(BdKtJobDependTable::getTableNameEn, tableInfo.getTableNameEn())
                .eq(BdKtJobDependTable::getIsDeleted, 0)
                .count() > 0) {
            return Result.fail("该表定义已被Kettle作业依赖，无法删除");
        }
        bdTableInfoMapper.delete(Wrappers.lambdaQuery(BdTableInfo.class).eq(BdTableInfo::getId, id));
        // 删除相关的字段定义
        bdTableColInfoMapper.delete(Wrappers.lambdaQuery(BdTableColInfo.class)
                .eq(BdTableColInfo::getTableNameEn, tableInfo.getTableNameEn()));
        //删除作业依赖
        List<BdKtJobDependTable> dependTables = new LambdaQueryChainWrapper<>(bdKtJobDependTableMapper)
                .eq(BdKtJobDependTable::getTableNameEn, tableInfo.getTableNameEn())
                .list();
        for (BdKtJobDependTable dependTable : dependTables) {
            String jobName = dependTable.getKtJobName();
            BdKtJob job = bdKtJobMapper.selectOne(Wrappers.lambdaQuery(BdKtJob.class).eq(BdKtJob::getJobName, jobName));
            if (job != null) {
                String dependTableNameEn = job.getDependTableNameEn();
                if (StrUtil.isNotBlank(dependTableNameEn)) {
                    List<String> tableNames = StrUtil.split(dependTableNameEn, ",");
                    tableNames.remove(tableInfo.getTableNameEn());
                    new LambdaUpdateChainWrapper<>(bdKtJobMapper)
                            .eq(BdKtJob::getId, job.getId())
                            .set(BdKtJob::getDependTableNameEn, String.join(",", tableNames))
                            .update();
                }
            }
        }
        bdKtJobDependTableMapper.delete(Wrappers.lambdaQuery(BdKtJobDependTable.class)
                .eq(BdKtJobDependTable::getTableNameEn, tableInfo.getTableNameEn()));
        return Result.success();

    }

    @ApiOperation(value = "删除表字段定义")
    @ACROSS
    @DeleteMapping(value = "delTableColDef/{id}")
    public Result<?> delTableColDef(@PathVariable("id") Long id) {
        bdTableColInfoMapper.delete(Wrappers.lambdaQuery(BdTableColInfo.class).eq(BdTableColInfo::getId, id));
        return Result.success();
    }


    @ApiOperation(value = "编辑kettle作业")
    @ACROSS
    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "editKettleJob")
    public Result<?> editKettleJob(@RequestBody BdKtJob args) {
        if (args.getId() == null || args.getId() <= 0) {
            if (new LambdaQueryChainWrapper<>(bdKtJobMapper)
                    .eq(BdKtJob::getIsDeleted, 0)
                    .eq(BdKtJob::getJobName, args.getJobName())
                    .count() > 0) {
                return Result.fail("Kettle作业已存在，请勿重复添加");
            }
            args.setId(IdGenerator.ins().generator());
            bdKtJobMapper.insert(args);
        } else {
            bdKtJobMapper.updateById(args);
        }
        //添加作业依赖的表
        String dependTableNameEn = args.getDependTableNameEn();
        bdKtJobDependTableMapper.delete(Wrappers.lambdaQuery(BdKtJobDependTable.class).eq(BdKtJobDependTable::getKtJobName, args.getJobName()));
        if (StrUtil.isNotBlank(dependTableNameEn)) {
            List<String> tableNames = StrUtil.split(dependTableNameEn, ",");
            List<BdKtJobDependTable> dependTables = new ArrayList<>();
            for (String tableName : tableNames) {
                if (StrUtil.isNotBlank(tableName)) {
                    //确认表定义存在
                    if (new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                            .eq(BdTableInfo::getIsDeleted, 0)
                            .eq(BdTableInfo::getTableNameEn, tableName)
                            .count() <= 0) {
                        continue;
                    }
                    BdKtJobDependTable dependTable = new BdKtJobDependTable();
                    dependTable.setKtJobName(args.getJobName());
                    dependTable.setTableNameEn(tableName);
                    dependTable.setIsDeleted(0);
                    dependTables.add(dependTable);
                }
            }
            if (CollUtil.isNotEmpty(dependTables)) {
                bdKtJobDependTableMapper.insertBatch(dependTables);
            }
        }
        //添加作业依赖的作业
        String parentJobNames = args.getParentJobNames();
        bdKtJobDependMapper.delete(Wrappers.lambdaQuery(BdKtJobDepend.class).eq(BdKtJobDepend::getKtJobName, args.getJobName()));
        List<BdKtJobDepend> depends = new ArrayList<>();
        List<String> split = StrUtil.split(parentJobNames, ",");
        for (String jobName : split) {
            if (StrUtil.isNotBlank(jobName)) {
                //确认作业名称存在
                if (new LambdaQueryChainWrapper<>(bdKtJobMapper)
                        .eq(BdKtJob::getIsDeleted, 0)
                        .eq(BdKtJob::getJobName, jobName)
                        .count() <= 0) {
                    continue;
                }
                BdKtJobDepend depend = new BdKtJobDepend();
                depend.setKtJobName(args.getJobName());
                depend.setParentKtJobName(jobName);
                depend.setIsDeleted(0);
                depends.add(depend);
            }
        }
        if (CollUtil.isNotEmpty(depends)) {
            bdKtJobDependMapper.insertBatch(depends);
        }
        return Result.success();
    }


    @ApiOperation(value = "获取kettle作业列表")
    @ACROSS
    @GetMapping(value = "getKettleJobList")
    public Result<IPage<BdKtJob>> getKettleJobList(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "keyword", required = false) String keyword) {
        return Result.success(new LambdaQueryChainWrapper<>(bdKtJobMapper)
                .eq(BdKtJob::getIsDeleted, 0)
                .like(StrUtil.isNotBlank(keyword), BdKtJob::getJobName, keyword)
                .orderByAsc(BdKtJob::getIdx)
                .page(new Page<>(pageNo, pageSize, true)));
    }

    @ApiOperation(value = "删除Kettle作业")
    @ACROSS
    @Transactional(rollbackFor = Exception.class)
    @DeleteMapping(value = "delKettleJob/{id}")
    public Result<?> delKettleJob(@PathVariable("id") Long id) {
        BdKtJob bdKtJob = bdKtJobMapper.selectById(id);
        if (bdKtJob == null) {
            return Result.fail("Kettle作业不存在");
        }
        //删除依赖的作业
        bdKtJobDependMapper.delete(Wrappers.lambdaQuery(BdKtJobDepend.class).eq(BdKtJobDepend::getKtJobName, bdKtJob.getJobName()));
        //删除被依赖的作业
        List<BdKtJobDepend> list = new LambdaQueryChainWrapper<>(bdKtJobDependMapper)
                .eq(BdKtJobDepend::getParentKtJobName, bdKtJob.getJobName())
                .list();
        for (BdKtJobDepend depend : list) {
            String jobName = depend.getKtJobName();
            BdKtJob job = bdKtJobMapper.selectOne(Wrappers.lambdaQuery(BdKtJob.class).eq(BdKtJob::getJobName, jobName));
            String parentJobNames = job.getParentJobNames();
            if (StrUtil.isNotBlank(parentJobNames)) {
                List<String> jobNames = StrUtil.split(parentJobNames, ",");
                jobNames.remove(bdKtJob.getJobName());
                new LambdaUpdateChainWrapper<>(bdKtJobMapper)
                        .eq(BdKtJob::getId, job.getId())
                        .set(BdKtJob::getParentJobNames, String.join(",", jobNames))
                        .update();
            }
        }
        bdKtJobDependMapper.delete(Wrappers.lambdaQuery(BdKtJobDepend.class).eq(BdKtJobDepend::getParentKtJobName, bdKtJob.getJobName()));
        bdKtJobMapper.delete(Wrappers.lambdaQuery(BdKtJob.class).eq(BdKtJob::getId, id));
        return Result.success();
    }

    @ApiOperation(value = "编辑Kettle变量")
    @ACROSS
    @PostMapping(value = "editKettleVars")
    public Result<?> editKettleVars(@RequestBody List<BdKtVars> args) {
        if (CollUtil.isNotEmpty(args)) {
            bdKtVarsMapper.insertUpdate(args);
        }
        return Result.success();
    }

    @ApiOperation(value = "获取kettle变量列表")
    @ACROSS
    @GetMapping(value = "getKettleVarList")
    public Result<IPage<BdKtVars>> getKettleVarList(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "projectNumber", required = false) String projectNumber) {
        return Result.success(new LambdaQueryChainWrapper<>(bdKtVarsMapper)
                .eq(BdKtVars::getIsDeleted, 0)
                .like(StrUtil.isNotBlank(projectNumber), BdKtVars::getProjectNumber, projectNumber)
                .orderByDesc(BdKtVars::getId)
                .page(new Page<>(pageNo, pageSize, true)));
    }

    //获取表的Options
    @ApiOperation(value = "获取表定义的Options")
    @ACROSS
    @GetMapping(value = "getTableOptions")
    public Result<LinkedHashMap<String, String>> getTableOptions() {
        LinkedHashMap<String, String> tableNameMap = new LinkedHashMap<>();
        List<BdTableInfo> bdTableInfos = new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                .select(BdTableInfo::getTableNameEn, BdTableInfo::getTableNameCn)
                .eq(BdTableInfo::getIsDeleted, 0)
                .orderByDesc(BdTableInfo::getCreateTime)
                .list();
        if (CollUtil.isEmpty(bdTableInfos)) {
            return Result.success(tableNameMap);
        }
        for (BdTableInfo bdTableInfo : bdTableInfos) {
            tableNameMap.put(bdTableInfo.getTableNameEn(), bdTableInfo.getTableNameCn());
        }
        return Result.success(tableNameMap);
    }

    //获取作业的Options
    @ApiOperation(value = "获取作业依赖的表列表")
    @ACROSS
    @GetMapping(value = "getKtJobOptions")
    public Result<LinkedHashMap<String, String>> getKtJobOptions() {
        LinkedHashMap<String, String> tableNameMap = new LinkedHashMap<>();
        List<BdKtJob> bdKtJobs = new LambdaQueryChainWrapper<>(bdKtJobMapper)
                .select(BdKtJob::getJobName)
                .eq(BdKtJob::getIsDeleted, 0)
                .orderByAsc(BdKtJob::getIdx)
                .list();
        if (CollUtil.isEmpty(bdKtJobs)) {
            return Result.success(tableNameMap);
        }
        for (BdKtJob bdKtJob : bdKtJobs) {
            tableNameMap.put(bdKtJob.getJobName(), bdKtJob.getJobName());
        }
        return Result.success(tableNameMap);
    }

    @ApiOperation(value = "编辑字典类型")
    @ACROSS
    @PostMapping(value = "editDictType")
    public Result<?> editDictType(@RequestBody BdDictType args) {
        if (args.getId() != null && args.getId() > 0) {
            bdDictTypeMapper.updateById(args);
        } else {
            bdDictTypeMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation(value = "获取字典类型列表")
    @ACROSS
    @GetMapping(value = "getDictTypeList")
    public Result<IPage<BdDictType>> getDictTypeList(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "keyword", required = false) String keyword) {
        Page<BdDictType> page = new LambdaQueryChainWrapper<>(bdDictTypeMapper)
                .eq(BdDictType::getIsDeleted, 0)
                .and(StrUtil.isNotBlank(keyword), i -> i.like(BdDictType::getDictName, keyword).or().like(BdDictType::getDictCode, keyword))
                .orderByAsc(BdDictType::getId)
                .page(new Page<>(pageNo, pageSize, true));
        return Result.success(page);
    }

    @ApiOperation(value = "删除字典类型")
    @ACROSS
    @DeleteMapping(value = "deleteDictType/{dictCode}")
    public Result<?> deleteDictType(@PathVariable("dictCode") String dictCode) {
        bdDictDataMapper.delete(Wrappers.lambdaQuery(BdDictData.class).eq(BdDictData::getDictCode, dictCode));
        bdDictTypeMapper.delete(Wrappers.lambdaQuery(BdDictType.class).eq(BdDictType::getDictCode, dictCode));
        return Result.success();
    }

    @ApiOperation(value = "编辑字典数据")
    @ACROSS
    @PostMapping(value = "editDictData")
    public Result<?> editDictData(@RequestBody BdDictData args) {
        if (args.getId() != null && args.getId() > 0) {
            bdDictDataMapper.updateById(args);
        } else {
            bdDictDataMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation(value = "获取字典类型列表")
    @ACROSS
    @GetMapping(value = "getDictDataList")
    public Result<IPage<BdDictData>> getDictDataList(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "dictCode", required = false) String dictCode, @RequestParam(value = "keyword", required = false) String keyword) {
        if (StrUtil.isBlank(dictCode)) {
            return Result.fail("字典编码不能为空");
        }
        IPage<BdDictData> page = new LambdaQueryChainWrapper<>(bdDictDataMapper)
                .eq(BdDictData::getIsDeleted, 0)
                .eq(BdDictData::getDictCode, dictCode)
                .and(StrUtil.isNotBlank(keyword), i -> i.like(BdDictData::getDictValue, keyword).or().like(BdDictData::getDictCode, keyword))
                .orderByAsc(BdDictData::getId)
                .page(new Page<>(pageNo, pageSize, true));
        return Result.success(page);
    }

    @ApiOperation(value = "删除字典数据")
    @ACROSS
    @DeleteMapping(value = "deleteDictData/{id}")
    public Result<?> deleteDictData(@PathVariable("id") Long id) {
        if (id == null || id <= 0) {
            return Result.fail("字典数据ID不能为空");
        }
        BdDictData bdDictData = bdDictDataMapper.selectById(id);
        if (bdDictData == null) {
            return Result.fail("字典数据不存在");
        }
        bdDictDataMapper.delete(Wrappers.lambdaQuery(BdDictData.class).eq(BdDictData::getId, id));
        return Result.success();
    }

    @ApiOperation(value = "获取字段分类选项")
    @ACROSS
    @GetMapping(value = "getDictTypeOptions")
    public Result<LinkedHashMap<String, String>> getDictTypeOptions() {
        LinkedHashMap<String, String> dictTypeMap = new LinkedHashMap<>();
        List<BdDictType> bdDictTypes = new LambdaQueryChainWrapper<>(bdDictTypeMapper)
                .select(BdDictType::getDictCode, BdDictType::getDictName)
                .eq(BdDictType::getIsDeleted, 0)
                .orderByAsc(BdDictType::getId)
                .list();
        if (CollUtil.isEmpty(bdDictTypes)) {
            return Result.success(dictTypeMap);
        }
        for (BdDictType bdDictType : bdDictTypes) {
            dictTypeMap.put(bdDictType.getDictCode(), bdDictType.getDictName());
        }
        return Result.success(dictTypeMap);
    }


    @ApiOperation(value = "获取字段值选项")
    @ACROSS
    @GetMapping(value = "getDictDataOptions/{dictCode}")
    public Result<LinkedHashMap<String, String>> getDictDataOptions(@PathVariable("dictCode") String dictCode) {
        LinkedHashMap<String, String> dictDataMap = new LinkedHashMap<>();
        if (StrUtil.isBlank(dictCode)) {
            return Result.success(dictDataMap);
        }
        List<BdDictData> bdDictDatas = new LambdaQueryChainWrapper<>(bdDictDataMapper)
                .select(BdDictData::getDictValue)
                .eq(BdDictData::getIsDeleted, 0)
                .eq(BdDictData::getDictCode, dictCode)
                .orderByAsc(BdDictData::getId)
                .list();
        if (CollUtil.isEmpty(bdDictDatas)) {
            return Result.success(dictDataMap);
        }
        for (BdDictData bdDictData : bdDictDatas) {
            dictDataMap.put(bdDictData.getDictValue(), bdDictData.getDictValue());
        }
        return Result.success(dictDataMap);
    }

    @ApiOperation(value = "获取产品选项")
    @ACROSS
    @GetMapping(value = "getProductOptions")
    public Result<LinkedHashMap<String, String>> getProductOptions() {
        LinkedHashMap<String, String> productMap = new LinkedHashMap<>();
        List<DictProduct> products = new LambdaQueryChainWrapper<>(dictProductMapper)
                .select(DictProduct::getProductName)
                .eq(DictProduct::getIsDeleted, 0)
                .eq(DictProduct::getYyIsCloud, 1)
                .list();
        if (CollUtil.isEmpty(products)) {
            return Result.success(productMap);
        }
        for (DictProduct product : products) {
            productMap.put(product.getProductName(), product.getProductName());
        }
        return Result.success(productMap);
    }

    @ApiOperation(value = "编辑数据源")
    @ACROSS
    @PostMapping(value = "editDatasource")
    public Result<?> editDatasource(@RequestBody BdDatasource args) {
        String projectNumber = args.getProjectNumber();
        if (StrUtil.isBlank(projectNumber)) {
            return Result.fail("项目编号不能为空");
        }
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .eq(ProjProjectInfo::getProjectNumber, projectNumber)
                .one();
        if (proj == null) {
            return Result.fail("项目不存在");
        }
        ProjCustomInfo custom = new LambdaQueryChainWrapper<>(customInfoMapper)
                .eq(ProjCustomInfo::getIsDeleted, 0)
                .eq(ProjCustomInfo::getCustomInfoId, proj.getCustomInfoId())
                .one();
        if (custom == null) {
            return Result.fail("客户不存在");
        }
        args.setCustomInfoId(custom.getCustomInfoId());
        args.setCustomName(custom.getCustomName());
        args.setProjectName(proj.getProjectName());
        BdDatasource one = new LambdaQueryChainWrapper<>(datasourceMapper)
                .eq(BdDatasource::getCustomInfoId, custom.getCustomInfoId())
                .eq(BdDatasource::getProjectNumber, projectNumber)
                .one();
        if (one != null) {
            args.setId(one.getId());
        }
        args.setIsDeleted(0);
        args.setCreaterId(-1L);
        args.setCreateTime(new Date());
        args.setUpdaterId(-1L);
        args.setUpdateTime(new Date());
        if (args.getId() != null && args.getId() > 0) {
            datasourceMapper.updateById(args);
        } else {
            datasourceMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation(value = "获取数据源")
    @ACROSS
    @GetMapping(value = "getDatasourceList")
    public Result<IPage<BdDatasource>> getDatasourceList(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam(value = "keyword", required = false) String keyword) {
        IPage<BdDatasource> page = new LambdaQueryChainWrapper<>(datasourceMapper)
                .eq(BdDatasource::getIsDeleted, 0)
                .and(StrUtil.isNotBlank(keyword), i -> i.like(BdDatasource::getProjectNumber, keyword).or().like(BdDatasource::getProjectName, keyword).or().like(BdDatasource::getCustomName, keyword))
                .orderByDesc(BdDatasource::getId)
                .page(new Page<>(pageNo, pageSize, true));
        return Result.success(page);
    }

    @ApiOperation(value = "删除数据源")
    @ACROSS
    @DeleteMapping(value = "deleteDatasource/{id}")
    public Result<?> deleteDatasource(@PathVariable("id") Long id) {
        datasourceMapper.delete(Wrappers.lambdaQuery(BdDatasource.class).eq(BdDatasource::getId, id));
        return Result.success();
    }


}
