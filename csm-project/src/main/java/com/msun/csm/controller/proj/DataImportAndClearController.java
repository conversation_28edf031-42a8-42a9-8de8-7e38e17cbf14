package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.param.ConfirmImportDataParam;
import com.msun.csm.model.param.ImportDataParam;
import com.msun.csm.model.vo.ImportDataVO;
import com.msun.csm.service.proj.DataImportAndClearService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "准备工作检查及审核")
@Slf4j
@RestController
@RequestMapping("/dataImportAndClear")
public class DataImportAndClearController {


    @Resource
    private DataImportAndClearService dataExportAndClearService;

    @Value("${project.feign.oldImsp.front-url}")
    private String domain;

    /**
     * 查询导出数据页面列表数据
     *
     * @param param 参数
     * @return 准备工作列表
     */
    @Log(operName = "查询导出数据页面列表数据", operDetail = "查询导出数据页面列表数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询导出数据页面列表数据")
    @ApiOperation("查询导出数据页面列表数据")
    @PostMapping(value = "/getImportDataList")
    Result<List<ImportDataVO>> getImportDataList(@RequestBody ImportDataParam param) {
        log.info("查询导出数据页面列表数据，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(dataExportAndClearService.getImportDataList(param));
        } catch (Exception e) {
            log.error("查询导出数据页面列表数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }


    @Log(operName = "批量确认", operDetail = "批量确认", operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS, cnName = "批量确认")
    @ApiOperation("批量确认")
    @RequestMapping("/confirmImportData")
    Result<Boolean> confirmImportData(@RequestBody ConfirmImportDataParam param) {
        log.info("批量确认，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(dataExportAndClearService.confirmImportData(param));
        } catch (Exception e) {
            log.error("批量确认，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }


    @Log(operName = "进入导入数据的老系统页面", operDetail = "进入导入数据的老系统页面", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "进入导入数据的老系统页面")
    @ApiOperation("进入导入数据的老系统页面")
    @PostMapping(value = "/importDataPage")
    Result<String> getAuditResults(Long projectInfoId, String id, String productId) {
        log.info("进入导入数据的老系统页面，projectInfoId={}，id={}，productId={}", projectInfoId, id, productId);
        if (StringUtils.isBlank(id)) {
            return Result.fail("参数id不可为空");
        }
        String domainInfo = StringUtils.isBlank(domain) ? "https://imsp.msuncloud.com/imsp-cloud/" : domain;
        // 药房
        if ("81".equals(id)) {
            String url = domainInfo + "/chis/pharmacyStock/drugPharmacyIndex";
            return Result.success(url);
        } else if ("82".equals(id)) {
            // 药库
            String url = domainInfo + "/chis/drug/drugStorageIndex";
            return Result.success(url);
        }
        if (StringUtils.isBlank(productId)) {
            productId = "-1";
        }
        try {
            String url = domainInfo + "/flowWork/goWorkLink?id=" + id + "&productId=" + productId;
            return Result.success(url);
        } catch (Exception e) {
            log.error("进入导入数据的老系统页面，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }


    @Log(operName = "进入清除数据的老系统页面", operDetail = "进入清除数据的老系统页面", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "进入清除数据的老系统页面")
    @ApiOperation("进入清除数据的老系统页面")
    @PostMapping(value = "/clearDataPage")
    Result<String> getAuditResults(Long projectInfoId) {
        log.info("进入清除数据的老系统页面，projectInfoId={}", projectInfoId);
        try {
            String domainInfo = StringUtils.isBlank(domain) ? "https://imsp.msuncloud.com/imsp-cloud/" : domain;
            String url = domainInfo + "/delServiceCategory/delServuceDatView?flag=1";
            return Result.success(url);
        } catch (Exception e) {
            log.error("进入清除数据的老系统页面，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }


}
