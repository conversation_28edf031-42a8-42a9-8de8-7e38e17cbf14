package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.autotest.ProjHospitalPharmacyRecordDTO;
import com.msun.csm.service.proj.ProjHospitalPharmacyRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2025-02-20 06:47:42
 */
@Slf4j
@RestController
@RequestMapping("/projHospitalPharmacyRecord")
@Api(tags = "药房科室与医院对照相关接口")
public class ProjHospitalPharmacyRecordController {

    @Resource
    private ProjHospitalPharmacyRecordService projHospitalPharmacyRecordService;

    /**
     * 保存科药房室信息.
     *
     * @param dto 科室信息, 含4个科室, 中西药房住院门诊科室
     * @return 失败和成功回复
     */
    @ApiOperation("savePharmacy")
    @Log(operName = "保存科药房室信息", operDetail = "查询项目详情", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询项目详情")
    @PostMapping("/savePharmacy")
    Result<String> savePharmacy(@Valid @RequestBody ProjHospitalPharmacyRecordDTO dto) {
        return projHospitalPharmacyRecordService.savePharmacy(dto);
    }
}
