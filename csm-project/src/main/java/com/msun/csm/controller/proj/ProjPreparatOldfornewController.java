package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectQualityCheck;
import com.msun.csm.model.dto.SavePreparatOldfornewTableDTO;
import com.msun.csm.model.req.project.ProjProjectQualityCheckAddReq;
import com.msun.csm.model.req.project.ProjProjectQualityCheckPageReq;
import com.msun.csm.model.vo.ProjPreparatOldfornewTableVO;
import com.msun.csm.service.proj.ProjPreparatOldfornewService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @version : V1.52.0
 * @ClassName: ProjPreparatOldfornew
 * @Description: 老换新准备工作
 * @Author: Yhongmin
 * @Date: 14:18 2024/6/17
 */
@Slf4j
@RestController
@RequestMapping("/projPreparatOldfornew")
@Api(tags = "老换新准备工作")
public class ProjPreparatOldfornewController {
    @Resource
    private ProjPreparatOldfornewService preparatOldfornewService;

    /**
     * 获取老换新版本检测表格数据
     *
     * @return
     */
    @Log(operName = "老换新准备工作", operDetail = "获取老换新版本检测表格数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取老换新版本检测表格数据")
    @ApiOperation("获取老换新版本检测表格数据")
    @GetMapping(value = "/getProjPreparatOldfornewTable")
    Result<List<ProjPreparatOldfornewTableVO>> getProjPreparatOldfornewTable(Long projectInfoId) {
        return preparatOldfornewService.getProjPreparatOldfornewTable(projectInfoId);
    }

    @Log(operName = "老换新准备工作", operDetail = "替换lis和his版本", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "替换lis和his版本")
    @ApiOperation("替换lis和his版本")
    @PostMapping(value = "/updateProjPreparatOldfornewTable")
    Result<List<ProjPreparatOldfornewTableVO>> updateProjPreparatOldfornewTable(@RequestBody SavePreparatOldfornewTableDTO savePreparatOldfornewTable) {
        return preparatOldfornewService.updateProjPreparatOldfornewTable(savePreparatOldfornewTable);
    }

    /**
     * 保存数据质量检测结果统计
     *
     * @param req
     * @return
     */
    @Log(operName = "保存数据质量检测结果统计", operDetail = "保存数据质量检测结果统计", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "保存数据质量检测结果统计")
    @ApiOperation("保存数据质量检测结果统计")
    @PostMapping(value = "/saveStatisticsDataResults")
    Result saveStatisticsDataResults(@RequestBody ProjProjectQualityCheckAddReq req) {
        return preparatOldfornewService.saveStatisticsDataResults(req, 0);
    }

    /**
     * 保存字典对照结果统计
     *
     * @param req
     * @return
     */
    @Log(operName = "保存字典对照结果统计", operDetail = "保存字典对照结果统计", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "保存数据质量检测结果统计")
    @ApiOperation("保存字典对照结果统计")
    @PostMapping(value = "/saveDictCompareDataResults")
    Result saveDictCompareDataResults(@RequestBody ProjProjectQualityCheckAddReq req) {
        return preparatOldfornewService.saveStatisticsDataResults(req, 1);
    }

    /**
     * 获取项目质量检测
     * @param req
     * @return
     */
    @Log(operName = "获取数据质量检测结果统计", operDetail = "获取数据质量检测结果统计", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取项目质量检测")
    @ApiOperation("获取数据质量检测结果统计")
    @PostMapping(value = "/getProjProjectQualityCheck")
    Result<PageInfo<ProjProjectQualityCheck>> getProjProjectQualityCheck(@RequestBody ProjProjectQualityCheckPageReq req) {
        req.setCheckType(0);
        return preparatOldfornewService.getProjProjectQualityCheck(req);
    }

    /**
     * 获取字典对照结果统计
     * @param req
     * @return
     */
    @Log(operName = "获取字典对照结果统计", operDetail = "获取字典对照结果统计", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取字典对照结果统计")
    @ApiOperation("获取字典对照结果统计")
    @PostMapping(value = "/getDictCompareDataResults")
    Result<PageInfo<ProjProjectQualityCheck>> getDictCompareDataResults(@RequestBody ProjProjectQualityCheckPageReq req) {
        req.setCheckType(1);
        return preparatOldfornewService.getProjProjectQualityCheck(req);
    }
}
