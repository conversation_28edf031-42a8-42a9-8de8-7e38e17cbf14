package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.vo.ProjEquipRecordEcgResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsEcgVO;
import com.msun.csm.service.proj.ProjEquipRecordVsEcgService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/12/9:56
 */
@Api(tags = "心电设备Controller")
@RestController
@RequestMapping("/projEquipRecordVsEcg")
public class ProjEquipRecordVsEcgController {

    @Resource
    private ProjEquipRecordVsEcgService equipRecordVsEcgService;


    /**
     * 新增或修改心电设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("新增或修改心电设备数据")
    @Log(operName = "设备", operDetail = "新增或修改心电设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "新增或修改心电设备数据")
    @PostMapping(value = "/saveOrUpdateEquipToEcg")
    Result saveOrUpdateEquipToEcg(@RequestBody ProjEquipRecordVsEcgDTO dto) {
        return equipRecordVsEcgService.saveOrUpdateEquipToEcg(dto);
    }

    /**
     * 查询心电设备列表数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询列表数据")
    @Log(operName = "设备", operDetail = "查询列表数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询列表数据")
    @PostMapping(value = "/selectEcgEquipData")
    Result<ProjEquipRecordEcgResultVO> selectEcgEquipData(@RequestBody ProjEquipRecordVsEcgSelectDTO dto) {
        return equipRecordVsEcgService.selectEcgEquipData(dto);
    }

    /**
     * 删除心电设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("删除心电设备数据")
    @Log(operName = "设备", operDetail = "删除心电设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "删除心电设备数据")
    @PostMapping(value = "/deleteEquipToEcg")
    Result deleteEquipToEcg(@RequestBody ProjEquipRecordVsEcgSelectDTO dto) {
        return equipRecordVsEcgService.deleteEquipToEcg(dto);
    }

    /**
     * 查询单个心电设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询单个心电设备数据")
    @Log(operName = "设备", operDetail = "查询单个心电设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询单个心电设备数据")
    @PostMapping(value = "/viewEquipToEcg")
    Result<ProjEquipRecordVsEcgVO> viewEquipToEcg(@RequestBody ProjEquipRecordVsEcgSelectDTO dto) {
        return equipRecordVsEcgService.viewEquipToEcg(dto);
    }

    /**
     * 心电设备发送到云健康
     *
     * @return
     */
    @ApiOperation("心电设备发送到云健康")
    @Log(operName = "设备", operDetail = "心电设备发送到云健康", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "心电设备发送到云健康")
    @PostMapping(value = "/ecgEquipSendCloudEquip")
    Result ecgEquipSendCloudEquip(@RequestBody ProjEquipRecordVsEcgSelectDTO dto) {
        return equipRecordVsEcgService.ecgEquipSendCloudEquip(dto);
    }

    /**
     * 心电设备一键检测
     *
     * @return
     */
    @ApiOperation("心电设备一键检测")
    @Log(operName = "设备", operDetail = "心电设备一键检测", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "心电设备一键检测")
    @PostMapping(value = "/ecgEquipCheckForCloud")
    Result ecgEquipCheckForCloud(@RequestBody ProjEquipRecordVsEcgSelectDTO dto) {
        return equipRecordVsEcgService.ecgEquipCheckForCloud(dto);
    }

    /**
     * 心电设备下载模版
     *
     * @param dto
     * @return
     */
    @ApiOperation("心电设备下载模版")
    @Log(operName = "设备", operDetail = "心电设备下载模版", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "心电设备下载模版")
    @PostMapping(value = "/downloadTemplateForEcg")
    void downloadTemplateForEcg(HttpServletResponse response, @RequestBody ProjEquipRecordVsEcgSelectDTO dto) {
        equipRecordVsEcgService.downloadTemplateForEcg(response, dto.getProjectInfoId());
    }

    /**
     * 心电设备导入模版数据
     *
     * @return
     */
    @ApiOperation("心电设备导入模版数据")
    @Log(operName = "设备", operDetail = "心电设备导入模版数据", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "心电设备导入模版数据")
    @PostMapping(value = "/importExcelDatas")
    Result importExcelDatas(@RequestParam("file") MultipartFile multipartFile,
                            @RequestParam("customInfoId") Long customInfoId,
                            @RequestParam("projectInfoId") Long projectInfoId) {
        return equipRecordVsEcgService.importExcelDatas(multipartFile, customInfoId, projectInfoId);
    }

    /**
     * 心电设备导出数据
     */
    @ApiOperation("心电设备导出数据")
    @Log(operName = "设备", operDetail = "心电设备导出数据", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "心电设备导出数据")
    @RequestMapping(value = "/exportExcelDatas")
    void exportExcelDatas(@RequestBody ProjEquipRecordVsEcgSelectDTO dto, HttpServletResponse response) {
        equipRecordVsEcgService.exportExcelDatas(dto, response);
    }

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    @ApiOperation("提交完成")
    @Log(operName = "设备", operDetail = "提交完成", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "提交完成")
    @RequestMapping(value = "/commitFinish")
    Result commitFinish(@RequestBody ProjEquipVsProductFinishDTO dto) {
        return equipRecordVsEcgService.commitFinish(dto);
    }

}
