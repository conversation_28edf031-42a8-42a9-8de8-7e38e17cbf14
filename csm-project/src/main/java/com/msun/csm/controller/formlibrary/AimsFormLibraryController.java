package com.msun.csm.controller.formlibrary;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormImportParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormSelectParamerReq;
import com.msun.csm.model.req.formlibrary.ProjSelectApplicationFormPageReq;
import com.msun.csm.model.req.formlibrary.ProjSelectApplicationFormUpdateReq;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormImportResultResp;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormResp;
import com.msun.csm.model.resp.formlibrary.ProjSelectApplicationFormResp;
import com.msun.csm.service.formlibrary.AimsHospitalFormService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */
@Api(tags = "手麻表单资源库")
@RestController
@RequestMapping("/aimsFormLibrary")
@Slf4j
public class AimsFormLibraryController {

    @Resource
    private AimsHospitalFormService aimsHospitalFormService;

    /**
     * 拉取手麻现场数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "拉取手麻现场数据", operDetail = "拉取手麻现场数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "拉取手麻现场数据")
    @ApiOperation("拉取手麻现场数据")
    @PostMapping(value = "/selectAimsHospitalData")
    public Result<PageInfo<AimsHospitalFormResp>> selectAimsHospitalData(@RequestBody AimsHospitalFormParamerReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.selectAimsHospitalData(aimsHospitalFormParamerReq);
    }

    /**
     * 查询手麻资源库数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "查询手麻资源库数据", operDetail = "查询手麻资源库数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询手麻资源库数据")
    @ApiOperation("查询手麻资源库数据")
    @PostMapping(value = "/selectAimsHospitalFormLibData")
    public Result<PageInfo<AimsHospitalFormResp>> selectAimsHospitalFormLibData(@RequestBody AimsHospitalFormParamerReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.selectAimsHospitalFormLibData(aimsHospitalFormParamerReq);
    }

    /**
     * 导入手麻数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "导入手麻数据", operDetail = "导入手麻数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "导入手麻数据")
    @ApiOperation("导入手麻数据")
    @PostMapping(value = "/importAimsFormData")
    public Result<AimsHospitalFormImportResultResp> importAimsFormData(@RequestBody AimsHospitalFormImportParamerReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.importAimsFormData(aimsHospitalFormParamerReq);
    }

    /**
     * 查询选择的手麻数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "查询选择的手麻数据", operDetail = "查询选择的手麻数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询选择的手麻数据")
    @ApiOperation("查询选择的手麻数据")
    @PostMapping(value = "/selectAimsSelectFormHospitalData")
    public Result<PageInfo<ProjSelectApplicationFormResp>> selectAimsSelectFormHospitalData(@RequestBody ProjSelectApplicationFormPageReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.selectAimsSelectFormHospitalData(aimsHospitalFormParamerReq);
    }

    /**
     * 移除数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "移除数据", operDetail = "移除数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "移除数据")
    @ApiOperation("移除数据")
    @PostMapping(value = "/removeAimsSelectFormHospitalData")
    public Result removeAimsSelectFormHospitalData(@RequestBody ProjSelectApplicationFormUpdateReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.removeAimsSelectFormHospitalData(aimsHospitalFormParamerReq);
    }

    /**
     * 选择手麻数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Log(operName = "选择手麻数据", operDetail = "选择手麻数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "选择手麻数据")
    @ApiOperation("选择手麻数据")
    @PostMapping(value = "/seleltAimsFormListToCsmData")
    public Result seleltAimsFormListToCsmData(@RequestBody AimsHospitalFormSelectParamerReq aimsHospitalFormParamerReq) {
        return aimsHospitalFormService.seleltAimsFormListToCsmData(aimsHospitalFormParamerReq);
    }

    /**
     * 查询表单类型接口
     *
     * @return
     */
    @GetMapping("/queryAimsLibFormType")
    public Result<List<BaseCodeNameResp>> queryAimsLibFormType() {
        List<BaseCodeNameResp> list = aimsHospitalFormService.queryAimsLibFormType();
        return Result.success(list);
    }

}
