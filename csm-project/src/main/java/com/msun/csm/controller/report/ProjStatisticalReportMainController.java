package com.msun.csm.controller.report;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.BaseLableValueResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.model.req.projreport.statis.*;
import com.msun.csm.model.resp.projreport.MenuVO;
import com.msun.csm.model.resp.projreport.MsunReportMain;
import com.msun.csm.model.resp.statis.ConfigProductionMethodResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainSelectResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainUpdateResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportPageResp;
import com.msun.csm.service.report.statis.ProjStatisticalReportMainService;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/11/7
 */
@Slf4j
@RestController
@Api(tags = "统计报表")
@RequestMapping("/statisticalReportMain")
public class ProjStatisticalReportMainController {

    @Resource
    private ProjStatisticalReportMainService projStatisticalReportMainService;


    /**
     * 批量删除
     *
     * @param projStatisticalReportMainDelete
     */
    @ApiOperation("批量删除")
    @PostMapping("/batchDeleteByIds")
    @Log(operName = "作废", operDetail = "作废", intLogType = Log.IntLogType.SELF_SYS, cnName = "作废")
    public Result<String> batchDeleteByIds(@RequestBody ProjStatisticalReportMainDeleteReq projStatisticalReportMainDelete) {
        return this.projStatisticalReportMainService.batchDeleteByIds(projStatisticalReportMainDelete);
    }

    /**
     * 查询科室数据
     */
    @GetMapping("/queryHospitalDeptData")
    @ApiOperation("查询科室数据")
    public Result<List<BaseIdNameResp>> queryHospitalDeptData(@RequestParam(required = false) Long projectInfoId) {
        List<BaseIdNameResp> list = projStatisticalReportMainService.queryHospitalDeptData(projectInfoId);
        return Result.success(list);
    }


    /**
     * 查询频率数据
     */
    @GetMapping("/queryFrequencyData")
    @ApiOperation("查询频率数据")
    public Result<List<BaseIdNameResp>> queryFrequencyData() {
        List<BaseIdNameResp> list = projStatisticalReportMainService.queryFrequencyData();
        return Result.success(list);
    }

    /**
     * 查询用途数据
     *
     * @param projectInfoId
     * @return
     */
    @GetMapping("/queryPurposeData")
    @ApiOperation("查询用途数据")
    public Result<List<BaseIdNameResp>> queryPurposeData(@RequestParam(required = false) Long projectInfoId) {
        List<BaseIdNameResp> list = projStatisticalReportMainService.queryPurposeData(projectInfoId);
        return Result.success(list);
    }

    /**
     * 查询状态数据
     *
     * @return
     */
    @GetMapping("/queryStatusData")
    @ApiOperation("查询状态数据")
    public Result<List<BaseIdNameResp>> queryStatusData() {
        List<BaseIdNameResp> list = projStatisticalReportMainService.queryStausData();
        return Result.success(list);
    }

    /**
     * 查询制作方式接口
     *
     * @param projectInfoId
     * @return
     */
    @GetMapping("/queryConfigProductionMethodData")
    @ApiOperation("查询制作方式接口")
    public Result<List<ConfigProductionMethodResp>> queryConfigProductionMethodData(@RequestParam(required = false) Long projectInfoId) {
        List<ConfigProductionMethodResp> list = projStatisticalReportMainService.queryConfigProductionMethodData(projectInfoId);
        return Result.success(list);
    }


    /**
     * 保存
     *
     * @param projSurveyFormAddReqs
     * @return
     */
    @Log(operName = "保存", operDetail = "保存", intLogType = Log.IntLogType.SELF_SYS, cnName = "保存")
    @ApiOperation("保存")
    @PostMapping(value = "/saveReportData")
    Result saveReportData(@RequestBody ProjStatisticalReportMainAddReq projSurveyFormAddReqs) {
        return projStatisticalReportMainService.saveReportData(projSurveyFormAddReqs);
    }

    /**
     * 统计报表数据分页查询
     *
     * @param dto
     * @return
     */
    @Log(operName = "分页查询", operDetail = "统计报表数据分页查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "统计报表数据分页查询")
    @ApiOperation("统计报表数据分页查询")
    @PostMapping(value = "/findReportPage")
    Result<ProjStatisticalReportPageResp<ProjStatisticalReportMainSelectResp>> findReportPage(@RequestBody ProjStatisticalReportMainPageReq dto) {
        dto.setAdjudicationFlag(0);
        return projStatisticalReportMainService.findReportPage(dto);
    }

    /**
     * 统计报表数据分页查询(裁定页)
     *
     * @param dto
     * @return
     */
    @Log(operName = "统计报表数据分页查询(裁定页)", operDetail = "统计报表数据分页查询(裁定页)", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "统计报表数据分页查询(裁定页)")
    @ApiOperation("统计报表数据分页查询(裁定页)")
    @PostMapping(value = "/findReportAdjudicationPage")
    Result<ProjStatisticalReportPageResp<ProjStatisticalReportMainSelectResp>> findReportAdjudicationPage(@RequestBody ProjStatisticalReportMainPageReq dto) {
        dto.setAdjudicationFlag(1);
        return projStatisticalReportMainService.findReportPage(dto);
    }

    /**
     * 修改前根据id查询数据
     *
     * @param statisticalReportMainId
     * @return
     */
    @GetMapping("/queryStatisticalReportById")
    @ApiOperation("修改前根据id查询数据")
    public Result<ProjStatisticalReportMainUpdateResp> queryStatisticalReportById(@RequestParam(required = true) Long statisticalReportMainId) {
        return Result.success(projStatisticalReportMainService.queryStatisticalReportById(statisticalReportMainId));
    }

    /**
     * 行内修改报表
     *
     * @param dto
     * @return
     */
    @Log(operName = "行内修改报表", operDetail = "行内修改报表", intLogType = Log.IntLogType.SELF_SYS, cnName = "行内修改报表")
    @ApiOperation("行内修改报表")
    @PostMapping(value = "/updateReportMainRowDataById")
    public Result<String> updateReportMainRowDataById(@RequestBody ProjStatisticalReportMainUpdateReq dto) {
        return projStatisticalReportMainService.updateReportMainDataById(dto, "row");
    }

    /**
     * 申请裁定，一键申请裁定
     *
     * @param dto
     * @return
     */
    @Log(operName = "申请裁定，一键申请裁定", operDetail = "申请裁定，一键申请裁定", intLogType = Log.IntLogType.SELF_SYS, cnName = "申请裁定，一键申请裁定")
    @ApiOperation("申请裁定，一键申请裁定")
    @PostMapping(value = "/updateApplyAdjudication")
    public Result<String> updateApplyAdjudication(@RequestBody ProjStatisticalReportMainAdjudicationReq dto) {
        return projStatisticalReportMainService.updateApplyAdjudication(dto);
    }

    /**
     * 重新申请裁定/修改制作方式
     *
     * @param dto
     * @return
     */
    @Log(operName = "重新申请裁定/修改制作方式", operDetail = "重新申请裁定/修改制作方式", intLogType = Log.IntLogType.SELF_SYS, cnName = "重新申请裁定/修改制作方式")
    @ApiOperation("重新申请裁定/修改制作方式")
    @PostMapping(value = "/updateReapplyAdjudication")
    public Result<String> updateReapplyAdjudication(@RequestBody ProjStatisticalReportMainReapplyAdjudicationReq dto) {
        return projStatisticalReportMainService.updateReapplyAdjudication(dto);
    }

    /**
     * 撤销裁定
     *
     * @param dto
     * @return
     */
    @Log(operName = "撤销裁定", operDetail = "撤销裁定", intLogType = Log.IntLogType.SELF_SYS, cnName = "撤销裁定")
    @ApiOperation("撤销裁定")
    @PostMapping(value = "/updateRevokeAdjudication")
    public Result<String> updateRevokeAdjudication(@RequestBody ProjStatisticalReportMainReapplyAdjudicationReq dto) {
        return projStatisticalReportMainService.updateRevokeAdjudication(dto);
    }

    /**
     * 分配责任人
     *
     * @param dto
     * @return
     */
    @Log(operName = "分配责任人", operDetail = "分配责任人", intLogType = Log.IntLogType.SELF_SYS, cnName = "分配责任人")
    @ApiOperation("分配责任人(可批量)")
    @PostMapping(value = "/updateAssignPersonsById")
    public Result<String> updateAssignPersonsById(@RequestBody ProjStatisticalReportMainAssignPersonsReq dto) {
        return projStatisticalReportMainService.updateAssignPersonsById(dto);
    }

    /**
     * 批量裁定通过/裁定驳回
     *
     * @param dto
     * @return
     */
    @Log(operName = "批量裁定通过/批量裁定驳回", operDetail = "批量裁定通过/批量裁定驳回", intLogType = Log.IntLogType.SELF_SYS, cnName = "裁定通过/裁定驳回")
    @ApiOperation("批量裁定通过/批量裁定驳回")
    @PostMapping(value = "/updateRulingAdjudication")
    public Result<String> updateRulingAdjudication(@RequestBody ProjStatisticalReportMainReapplyRulingReq dto) {
        return projStatisticalReportMainService.updateRulingAdjudication(dto, null);
    }


    /**
     * 单个裁定通过/单个裁定驳回
     *
     * @param dto
     * @return
     */
    @Log(operName = "单个裁定通过/单个裁定驳回", operDetail = "单个裁定通过/单个裁定驳回", intLogType = Log.IntLogType.SELF_SYS, cnName = "裁定通过/裁定驳回")
    @ApiOperation("单个裁定通过/单个裁定驳回")
    @PostMapping(value = "/updateRulingAdjudicationSingle")
    public Result<String> updateRulingAdjudicationSingle(@RequestBody ProjStatisticalReportMainReapplyRulingReq dto) {
        return projStatisticalReportMainService.updateRulingAdjudication(dto, "single");
    }


    @Log(operName = "导出模板", operDetail = "导出模板", intLogType = Log.IntLogType.SELF_SYS, cnName = "导出模板")
    @ApiOperation("导出模板")
    @GetMapping("/download")
    public void exportTemplate(HttpServletResponse response, @RequestParam("projectInfoId") Long projectInfoId) throws IOException {
        projStatisticalReportMainService.download(response, projectInfoId);
    }

    /**
     * 导入报表
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量导入报表")
    @Log(operName = "批量导入报表", operDetail = "批量导入报表", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量导入报表")
    @PostMapping(value = "/importExcel")
    public Result<String> importExcel(ProjStatisticalReportMainImportReq param, HttpServletRequest request) throws Exception {
        return projStatisticalReportMainService.importExcel(param.getFile(), request, param);
    }


    /**
     * 后端运维使用：导出统计报表Excel之后分配完责任人再通过此方法导入，达到分配责任人的目的
     */
    @PostMapping(value = "/importStatisticalReportFromExcel")
    Result<Void> importStatisticalReportFromExcel(@RequestParam("file") MultipartFile multipartFile) {
        log.info("导入统计报表的责任人信息");
        return projStatisticalReportMainService.importStatisticalReportFromExcel(multipartFile);
    }

    /**
     * 报表设计
     *
     * @param dto
     * @return
     */
    @Log(operName = "报表设计", operDetail = "报表设计", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表设计")
    @ApiOperation("报表设计")
    @PostMapping(value = "/reportDesignById")
    public Result<String> reportDesignById(@RequestBody ProjStatisticalReportMainReportDesignReq dto) {
        return projStatisticalReportMainService.reportDesignById(dto, null);
    }

    /**
     * 报表预览
     *
     * @param dto
     * @return
     */
    @Log(operName = "报表预览", operDetail = "报表预览", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表预览")
    @ApiOperation("报表预览")
    @PostMapping(value = "/reportReviewById")
    public Result<String> reportReviewById(@RequestBody ProjStatisticalReportMainReportDesignReq dto) {
        return projStatisticalReportMainService.reportReviewById(dto);
    }

    /**
     * 查询指标
     * @param projectInfoId
     * @return
     */
    @Log(operName = "查询指标", operDetail = "查询指标", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询指标")
    @ApiOperation("查询指标")
    @GetMapping(value = "/getAllReportZbData")
    public Result<List<BaseLableValueResp>> getAllReportZbData(@RequestParam("projectInfoId") Long projectInfoId) {
        List<BaseLableValueResp> list = projStatisticalReportMainService.getAllReportZbDetailData(projectInfoId);
        return Result.success(list);
    }


    /**
     * 根据医院id获取项目信息
     * @param cloudHospitalId
     * @return
     */
    @Log(operName = "根据医院id获取项目信息", operDetail = "根据医院id获取项目信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据医院id获取项目信息")
    @ApiOperation("根据医院id获取项目信息")
    @GetMapping(value = "/getProjectInfoByCloudHospitalId")
    public Result<ProjProjectInfo> getProjectInfoByCloudHospitalId(@RequestParam("cloudHospitalId") Long cloudHospitalId) {
        ProjProjectInfo info = projStatisticalReportMainService.getProjectInfoByCloudHospitalId(cloudHospitalId);
        return Result.success(info);
    }

    /**
     *运维平台传入文件交付保存
     * @param req
     * @return
     */
    @Log(operName = "运维平台传入文件交付保存", operDetail = "运维平台传入文件交付保存", intLogType = Log.IntLogType.SELF_SYS, cnName = "运维平台传入文件交付保存")
    @ApiOperation("运维平台传入文件交付保存")
    @PostMapping(value = "/getFileNames")
    public Result<List<ProjFileReq>> getFileNames(@RequestBody ProjStatisticalStyleFileReq req) {
        List<ProjFileReq> list = projStatisticalReportMainService.getFileNames(req);
        return Result.success(list);
    }


    /**
     * 报表下沉
     *
     * @param dto
     * @return
     */
    @Log(operName = "报表下沉", operDetail = "报表下沉", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表下沉")
    @ApiOperation("报表下沉")
    @PostMapping(value = "/jfDevolveDelivery")
    public Result<String> jfDevolveDelivery(@RequestBody ProjStatisticalReportMainReapplyAdjudicationReq dto) {
        return projStatisticalReportMainService.jfDevolveDelivery(dto);
    }

    /**
     * 报表发布
     *
     * @param dto
     * @return
     */
    @Log(operName = "报表发布", operDetail = "报表发布", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表发布")
    @ApiOperation("报表发布")
    @PostMapping(value = "/jfSaveMenuDelivery")
    public Result<String> jfSaveMenuDelivery(@RequestBody ProjStatisticalReportMainDeliveryReq dto) {
        return projStatisticalReportMainService.jfSaveMenuDelivery(dto);
    }

    /**
     * 查询报表平台报表
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询报表平台报表", operDetail = "查询报表平台报表", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询报表平台报表")
    @ApiOperation("查询报表平台报表")
    @PostMapping(value = "/selectReportListDelivery")
    public Result<List<MsunReportMain>> selectReprtListDelivery(@RequestBody ProjStatisticalReportMainDeliveryReq dto) {
        return projStatisticalReportMainService.selectReprtListDelivery(dto);
    }

    /**
     * 报表对照
     *
     * @param dto
     * @return
     */
    @Log(operName = "报表对照", operDetail = "报表对照", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表对照")
    @ApiOperation("报表对照")
    @PostMapping(value = "/reportComparison")
    public Result reportComparison(@RequestBody ProjStatisticalReportComparisonReq dto) {
        return projStatisticalReportMainService.reportComparison(dto);
    }


    /**
     * 查询报表平台报表
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询报表平台云健康菜单", operDetail = "查询报表平台云健康菜单", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询报表平台云健康菜单")
    @ApiOperation("查询报表平台云健康菜单")
    @PostMapping(value = "/jfFindMenuByHosId")
    public Result<List<MenuVO>> jfFindMenuByHosId(@RequestBody ProjStatisticalReportMainDeliveryReq dto) {
        return projStatisticalReportMainService.jfFindMenuByHosId(dto);
    }

    /**
     * 制作完成
     *
     * @param dto
     * @return
     */
    @Log(operName = "制作完成", operDetail = "制作完成", intLogType = Log.IntLogType.SELF_SYS, cnName = "制作完成")
    @ApiOperation("制作完成")
    @PostMapping(value = "/makeFinishReport")
    public Result<String> makeFinishReport(@RequestBody ProjStatisticalReportMainReportDesignReq dto) {
        return projStatisticalReportMainService.makeFinishReport(dto);
    }


    /**
     * 导出报表数据
     *
     * @param response
     * @param dto
     */
    @Log(operName = "导出报表数据", operDetail = "导出报表数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "导出报表数据")
    @ApiOperation("导出报表数据")
    @PostMapping(value = "/reportExportExcel")
    void reportExportExcel(HttpServletResponse response, @RequestBody ProjStatisticalReportMainPageReq dto) {
        dto.setAdjudicationFlag(0);
        projStatisticalReportMainService.reportExportExcel(response, dto);
    }

    /**
     * 修改责任人后导入统计报表
     */
    @PostMapping(value = "/updateAllocateUserByImport")
    Result<Void> updateAllocateUserByImport(@RequestParam("file") MultipartFile multipartFile) {
        log.info("修改责任人后导入统计报表");
        return projStatisticalReportMainService.updateAllocateUserByImport(multipartFile);
    }

    /**
     * 批量分配前端验证人
     *
     * @param param 参数
     */
    @Log(operName = "批量分配前端验证人", operDetail = "批量分配前端验证人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配前端验证人")
    @PostMapping(value = "/updateStatisticalReportIdentifier")
    public Result<Void> updateStatisticalReportIdentifier(@RequestBody ProjStatisticalReportMainAssignPersonsReq2 param) {
        return projStatisticalReportMainService.updateStatisticalReportIdentifier(param);
    }

    /**
     * 统计报表验证通过或者驳回
     *
     * @param param 参数
     */
    @Log(operName = "统计报表验证通过或者驳回", operDetail = "统计报表验证通过或者驳回", intLogType = Log.IntLogType.SELF_SYS, cnName = "统计报表验证通过或者驳回")
    @PostMapping(value = "/verificationPassed")
    Result<Void> verificationPassed(@RequestBody ProjStatisticalReportMainAssignPersonsReq2 param) {
        if (CollectionUtils.isEmpty(param.getMainIds())) {
            return Result.fail("请选择要验证通过的统计报表");
        }
        boolean result = projStatisticalReportMainService.verificationPassed(param);
        if (result) {
            return Result.success(null, "统计报表验证通过");
        }
        return Result.fail("统计报表验证通过，操作失败");
    }
}
