package com.msun.csm.controller.formlibnew;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq;
import com.msun.csm.model.req.formlibnew.DictFormlibStandardSaveReq;
import com.msun.csm.model.req.formlibnew.DictFormlibStandardTypeSaveReq;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardTypePageReq;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardResp;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTteeResp;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTypeSelectResp;
import com.msun.csm.service.formlibnew.DictFormlibStandardService;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: zhangdi
 * @Date: 2025/07/14/9:40
 */
@Api(tags = "表单资源库分类大类维护")
@RestController
@RequestMapping("/DictFormlibStandard")
public class DictFormlibStandardController {

    @Resource
    private DictFormlibStandardService dictFormlibStandardService;


    /**
     * 查询分类树形数据(列表)
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询分类树形数据(列表)", operDetail = "查询分类树形数据(列表)", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询分类树形数据(列表)")
    @ApiOperation("查询分类树形数据(列表)")
    @PostMapping(value = "/findDictDataPage")
    Result<List<DictFormlibStandardResp>> findDictDataPage(@RequestBody DictFormLibStandardPageReq dto) {
        return dictFormlibStandardService.findDictDataPage(dto);
    }

    @Log(operName = "查询分类树形数据(下拉)", operDetail = "查询树形数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询树形数据")
    @ApiOperation("查询分类树形数据(下拉)")
    @PostMapping(value = "/findStandardTreeData")
    Result<List<DictFormlibStandardTteeResp>> findStandardTreeData(@RequestBody DictFormLibStandardPageReq dto) {
        return dictFormlibStandardService.findStandardTreeData(dto);
    }


    @Log(operName = "保存分类数据", operDetail = "保存分类数据", operLogType = Log.LogOperType.ADD, intLogType = Log.IntLogType.SELF_SYS, cnName = "保存分类数据")
    @ApiOperation("保存分类数据")
    @PostMapping(value = "/saveData")
    Result<String> saveData(@RequestBody DictFormlibStandardSaveReq dto) {
        return dictFormlibStandardService.saveData(dto);
    }

    @Log(operName = "分类禁用启用", operDetail = "分类禁用启用", operLogType = Log.LogOperType.ADD, intLogType = Log.IntLogType.SELF_SYS, cnName = "分类禁用启用")
    @ApiOperation("分类禁用启用")
    @PostMapping(value = "/updateIsStandard")
    Result<String> updateIsStandard(@RequestBody DictFormlibStandardSaveReq dto) {
        return dictFormlibStandardService.updateIsStandard(dto);
    }

    /**
     * 查询分类字典数据分页
     *
     * @param dto
     * @return
     */
    @Log(operName = "分页查询分类字典数据", operDetail = "查询分类字典数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询分类字典数据")
    @ApiOperation("分页查询分类字典数据")
    @PostMapping(value = "/selectStandardTypeData")
    public Result<PageInfo<DictFormlibStandardTypeSelectResp>> selectStandardTypeData(@RequestBody DictFormLibStandardTypePageReq dto) {
        return dictFormlibStandardService.selectStandardTypeData(dto);
    }


    /**
     * 字典禁用启用
     *
     * @param dto
     * @return
     */
    @Log(operName = "字典禁用启用", operDetail = "字典禁用启用", operLogType = Log.LogOperType.ADD, intLogType = Log.IntLogType.SELF_SYS, cnName = "字典禁用启用")
    @ApiOperation("字典禁用启用")
    @PostMapping(value = "/updateIsStandardType")
    Result<String> updateIsStandardType(@RequestBody DictFormLibStandardTypePageReq dto) {
        return dictFormlibStandardService.updateIsStandardType(dto);
    }

    /**
     * 保存字典数据
     * @param dto
     * @return
     */
    @Log(operName = "保存字典数据", operDetail = "保存字典数据", operLogType = Log.LogOperType.ADD, intLogType = Log.IntLogType.SELF_SYS, cnName = "保存字典数据")
    @ApiOperation("保存字典数据")
    @PostMapping(value = "/saveDictData")
    Result<String> saveDictData(@RequestBody DictFormlibStandardTypeSaveReq dto) {
        return dictFormlibStandardService.saveDictData(dto);
    }


    @Log(operName = "查询字典数据，用于资源库下拉选项", operDetail = "查询字典数据，用于资源库下拉选项", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询树形数据")
    @ApiOperation("查询字典数据，用于资源库下拉选项")
    @PostMapping(value = "/findStandardTypeDictData")
    Result<List<BaseCodeNameResp>> findStandardTypeDictData(@RequestBody DictFormLibStandardPageReq dto) {
        return dictFormlibStandardService.findStandardTypeDictData(dto);
    }

    @Log(operName = "查询路径数据用于字典维护下拉选项", operDetail = "查询路径数据用于字典维护下拉选项", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询路径数据用于字典维护下拉选项")
    @ApiOperation("查询路径数据用于字典维护下拉选项")
    @PostMapping(value = "/selectDictFormTypeList")
    Result<List<BaseCodeNameResp>> selectDictFormTypeList(@RequestBody DictFormLibStandardPageReq dto) {
        return dictFormlibStandardService.selectDictFormTypeList(dto);
    }



}
