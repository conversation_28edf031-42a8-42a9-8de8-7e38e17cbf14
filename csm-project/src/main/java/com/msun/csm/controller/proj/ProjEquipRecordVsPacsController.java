package com.msun.csm.controller.proj;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.PacsCloudEquipSelectDTO;
import com.msun.csm.model.dto.PacsEquipSelectDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsPacsDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.UpdatePacsCloudEquipDTO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.PacsEquipCompareVO;
import com.msun.csm.model.vo.ProjEquipRecordPacsResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsPacsVO;
import com.msun.csm.service.proj.ProjEquipRecordVsPacsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/12/9:56
 */
@Api(tags = "PACS设备Controller")
@RestController
@RequestMapping("/projEquipRecordVsPacs")
public class ProjEquipRecordVsPacsController {

    @Resource
    private ProjEquipRecordVsPacsService equipRecordVsPacsService;


    /**
     * 新增或修改PACS设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("新增或修改PACS设备信息")
    @Log(operName = "设备", operDetail = "新增或修改PACS设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "新增或修改PACS设备信息")
    @PostMapping(value = "/saveOrUpdateEquipToPacs")
    Result saveOrUpdateEquipToPacs(@RequestBody ProjEquipRecordVsPacsDTO dto) {
        return equipRecordVsPacsService.saveOrUpdateEquipToPacs(dto);
    }

    /**
     * 查询单个Pacs设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询单个Pacs设备数据")
    @Log(operName = "设备", operDetail = "查询单个Pacs设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询单个Pacs设备数据")
    @PostMapping(value = "/getEquipRecordVsPacs")
    Result<ProjEquipRecordVsPacsVO> getEquipRecordVsPacs(@RequestBody PacsEquipSelectDTO dto) {
        return equipRecordVsPacsService.getEquipRecordVsPacs(dto.getEquipRecordVsPacsId(), dto.getIsMobile());
    }

    /**
     * 删除PACS设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("删除PACS设备数据")
    @Log(operName = "设备", operDetail = "删除PACS设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "删除PACS设备数据")
    @PostMapping(value = "/deleteEquipRecordVsPacs")
    Result deleteEquipRecordVsPacs(@RequestBody ProjEquipRecordVsPacsDTO dto) {
        return equipRecordVsPacsService.deleteEquipRecordVsPacs(dto.getEquipRecordVsPacsId());
    }

    /**
     * 查询Pacs设备列表数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询Pacs设备列表数据")
    @Log(operName = "设备", operDetail = "查询Pacs设备列表数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询Pacs设备列表数据")
    @PostMapping(value = "/selectPacsEquipData")
    Result<ProjEquipRecordPacsResultVO> selectPacsEquipData(@RequestBody PacsEquipSelectDTO dto) {
        return equipRecordVsPacsService.selectPacsEquipData(dto);
    }

    /**
     * 提交完成
     *
     * @return
     */
    @ApiOperation("提交完成")
    @Log(operName = "设备", operDetail = "提交完成", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "提交完成")
    @PostMapping(value = "/commitFinish")
    Result commitFinish(@RequestBody ProjEquipVsProductFinishDTO dto) {
        return equipRecordVsPacsService.commitFinish(dto);
    }

    /**
     * 发送到云健康
     *
     * @return
     */
    @ApiOperation("发送到云健康")
    @Log(operName = "设备", operDetail = "发送到云健康", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "发送到云健康")
    @PostMapping(value = "/sendToMsunVsPacs")
    Result sendToMsunVsPacs(@RequestBody ProjEquipSendToCloudDTO dto) {
        return equipRecordVsPacsService.sendToMsunVsPacs(dto);
    }

    /**
     * 查询PACS系统云健康设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询PACS系统云健康设备数据")
    @Log(operName = "设备", operDetail = "查询PACS系统云健康设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询PACS系统云健康设备数据")
    @PostMapping(value = "/selectCloudEquipDataToPacs")
    Result<Map<String, List<CloudEquipVO>>> selectCloudEquipDataToPacs(@RequestBody PacsCloudEquipSelectDTO dto) {
        return equipRecordVsPacsService.selectCloudEquipDataTransfer(dto);
    }

    /**
     * 一键检测、检测
     *
     * @return
     */
    @ApiOperation("一键检测、检测")
    @Log(operName = "设备", operDetail = "一键检测、检测", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "一键检测、检测")
    @PostMapping(value = "/checkSendToMsunVsPacs")
    Result checkSendToMsunVsPacs(@RequestBody ProjEquipSendToCloudDTO dto) {
        return equipRecordVsPacsService.checkSendToMsunVsPacs(dto);
    }

    /**
     * PACS下载模版
     */
    @ApiOperation("PACS下载模版")
    @Log(operName = "设备", operDetail = "PACS下载模版", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "PACS下载模版")
    @RequestMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, @RequestParam("projectInfoId") Long projectInfoId) {
        equipRecordVsPacsService.downloadTemplate(response, projectInfoId);
    }

    /**
     * PACS导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @ApiOperation("PACS导入模版数据")
    @Log(operName = "设备", operDetail = "PACS导入模版数据", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "PACS导入模版数据")
    @PostMapping(value = "/importExcelDatas")
    Result importExcelDatas(@RequestParam("file") MultipartFile multipartFile,
                            @RequestParam("customInfoId") Long customInfoId,
                            @RequestParam("projectInfoId") Long projectInfoId) {
        return equipRecordVsPacsService.importExcelDatas(multipartFile, customInfoId, projectInfoId);
    }

    /**
     * PACS设备导出数据
     *
     * @param dto
     * @param response
     */
    @ApiOperation("PACS设备导出数据")
    @Log(operName = "设备", operDetail = "PACS设备导出数据", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "PACS设备导出数据")
    @PostMapping(value = "/exportExcelDataVsPacs")
    public void exportExcelDatas(@RequestBody PacsEquipSelectDTO dto, HttpServletResponse response) {
        equipRecordVsPacsService.exportExcelDataVsPacs(dto, response);
    }

    /**
     * 老系统Pacs设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    @ApiOperation("老系统Pacs设备迁移到新系统")
    @Log(operName = "设备", operDetail = "老系统Pacs设备迁移到新系统", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老系统Pacs设备迁移到新系统")
    @GetMapping(value = "/sendEquipToOldPacs")
    Result sendEquipToOldPacs(@RequestParam("projectInfoId") Long projectInfoId,
                              @RequestParam("oldEquipId") Long oldEquipId) {
        return equipRecordVsPacsService.sendEquipToOldPacs(projectInfoId, oldEquipId);
    }

    /**
     * 获取Pacs设备对比列表
     *
     * @param dto
     * @return
     */
    @ApiOperation("获取Pacs设备对比列表")
    @Log(operName = "设备", operDetail = "获取Pacs设备对比列表", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取Pacs设备对比列表")
    @PostMapping(value = "/selectCompareListToPacs")
    Result<List<PacsEquipCompareVO>> selectCompareListToPacs(@RequestBody PacsEquipSelectDTO dto) {
        return equipRecordVsPacsService.selectCompareListToPacs(dto);
    }

    /**
     * Pacs设备对照云健康设备后保存
     *
     * @param dtoList
     * @return
     */
    @ApiOperation("Pacs设备对照云健康设备后保存")
    @Log(operName = "设备", operDetail = "Pacs设备对照云健康设备后保存", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "Pacs设备对照云健康设备后保存")
    @PostMapping(value = "/updateCloudEquipToPacs")
    Result updateCloudEquipToPacs(@RequestBody List<UpdatePacsCloudEquipDTO> dtoList) {
        return equipRecordVsPacsService.updateCloudEquipToPacs(dtoList);
    }
}
