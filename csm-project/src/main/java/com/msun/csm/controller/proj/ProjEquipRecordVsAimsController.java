package com.msun.csm.controller.proj;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.AimsCloudEquipSelectDTO;
import com.msun.csm.model.dto.AimsComparedEquipSelectDTO;
import com.msun.csm.model.dto.AimsEquipSelectDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsAimsDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.UpdateAimsCloudEquipDTO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.ProjEquipRecordAimsResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsAimsVO;
import com.msun.csm.service.proj.ProjEquipRecordVsAimsService;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 血透设备Controller
 *
 * @Author: duxu
 * @Date: 2024/10/12/14:06
 */
@Slf4j
@Api(tags = "手麻设备Controller")
@RestController
@RequestMapping("/projEquipRecordVsAims")
public class ProjEquipRecordVsAimsController {

    @Resource
    private ProjEquipRecordVsAimsService projEquipRecordVsAimsService;

    /**
     * 查询手麻系统云健康设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询手麻(Aims)系统云健康设备数据")
    @Log(operName = "设备", operDetail = "查询手麻(Aims)系统云健康设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询手麻(Aims)系统云健康设备数据")
    @PostMapping(value = "/selectCloudEquipData")
    Result<Map<String, List<CloudEquipVO>>> selectCloudEquipData(@Valid @RequestBody AimsCloudEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.selectCloudEquipDataTransfer(dto);
    }

    /**
     * 手麻设备自动对照云健康设备信息（根据设备名称进行匹配）
     *
     * @param dto
     * @return
     */
    @ApiOperation("手麻设备自动对照云健康设备信息（根据设备名称进行匹配）")
    @Log(operName = "设备", operDetail = "手麻设备自动对照云健康设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "手麻设备自动对照云健康设备信息")
    @PostMapping(value = "/compareEquipmentToAims")
    Result<List<ProjEquipRecordVsAimsVO>> compareEquipmentToAims(@Valid @RequestBody AimsComparedEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.compareEquipmentToAims(dto);
    }

    /**
     * 手麻系统修改云健康对照设备
     *
     * @param dtoList
     * @return
     */
    @ApiOperation("手麻系统修改云健康对照设备")
    @Log(operName = "设备", operDetail = "手麻系统修改云健康对照设备", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "手麻系统修改云健康对照设备")
    @PostMapping(value = "/updateCloudEquipToAims")
    Result<String> updateCloudEquipToAims(@RequestBody List<UpdateAimsCloudEquipDTO> dtoList) {
        log.info("手麻系统修改云健康对照设备. 入参: {}", JSONUtil.toJsonStr(dtoList));
        return projEquipRecordVsAimsService.updateCloudEquipToAims(dtoList);
    }

    /**
     * 查询手麻设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询手麻设备数据")
    @Log(operName = "设备", operDetail = "查询手麻设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询手麻设备数据")
    @PostMapping(value = "/selectEquipRecordVsAimsList")
    Result<ProjEquipRecordAimsResultVO> selectEquipRecordVsAimsList(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.selectEquipRecordVsAimsList(dto);
    }

    /**
     * 根据手麻设备记录id查询手麻设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("根据手麻设备记录id查询手麻设备数据")
    @Log(operName = "设备", operDetail = "根据手麻设备记录id查询手麻设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "根据手麻设备记录id查询手麻设备数据")
    @PostMapping(value = "/getEquipRecordVsAimsById")
    Result<ProjEquipRecordVsAimsVO> getEquipRecordVsAimsById(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.getEquipRecordVsAimsById(dto.getEquipRecordVsAimsId(), dto.getIsMobile());
    }

    /**
     * 新增或更新保存手麻设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("新增或更新保存手麻设备信息")
    @Log(operName = "设备", operDetail = "新增或更新保存手麻设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "新增或更新保存手麻设备信息")
    @PostMapping(value = "/saveOrUpdateAimsEquip")
    Result saveOrUpdateAimsEquip(@RequestBody ProjEquipRecordVsAimsDTO dto) {
        return projEquipRecordVsAimsService.saveOrUpdateAimsEquip(dto);
    }

    /**
     * 根据主键id删除手麻设备
     *
     * @param dto
     * @return
     */
    @ApiOperation("根据主键id删除手麻设备")
    @Log(operName = "设备", operDetail = "根据主键id删除手麻设备", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "根据主键id删除手麻设备")
    @PostMapping(value = "/deleteAimsEquip")
    Result deleteAimsEquip(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.deleteAimsEquip(dto.getEquipRecordVsAimsId());
    }

    /**
     * 手麻设备申请/一键提交申请
     *
     * @param dto
     * @return
     */
    @ApiOperation("手麻设备申请/一键提交申请")
    @Log(operName = "设备", operDetail = "手麻设备申请/一键提交申请", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手麻设备申请/一键提交申请")
    @PostMapping(value = "/equipSendToLisAnaly")
    Result equipSendToAimsAnaly(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.equipSendToAimsAnaly(dto);
    }

    /**
     * 撤销手麻接口申请
     *
     * @param dto
     * @return
     */
    @ApiOperation("撤销手麻接口申请")
    @Log(operName = "设备", operDetail = "撤销手麻接口申请", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "撤销手麻接口申请")
    @PostMapping(value = "/equipRevokeToAims")
    Result equipRevokeToAims(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.equipRevokeToAims(dto.getEquipRecordVsAimsId());
    }

    /**
     * 手麻设备一键检测
     *
     * @param dto
     * @return
     */
    @ApiOperation("手麻设备一键检测")
    @Log(operName = "设备", operDetail = "手麻设备一键检测", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手麻设备一键检测")
    @PostMapping(value = "/aimsEquipCheckForLisAnaly")
    Result aimsEquipCheckForLisAnaly(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.aimsEquipCheckForLisAnaly(dto);
    }

    /**
     * 手麻设备-提交完成
     *
     * @param dto
     * @return
     */
    @ApiOperation("手麻设备-提交完成")
    @Log(operName = "设备", operDetail = "手麻设备-提交完成", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手麻设备-提交完成")
    @PostMapping(value = "/commitFinish")
    Result commitFinish(@RequestBody ProjEquipVsProductFinishDTO dto) {
        return projEquipRecordVsAimsService.commitFinish(dto);
    }

    /**
     * 手麻设备发送到云健康
     *
     * @param dto
     * @return
     */
    @ApiOperation("手麻设备发送到云健康")
    @Log(operName = "设备", operDetail = "手麻设备发送到云健康", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手麻设备发送到云健康")
    @PostMapping(value = "/aimsEquipSendCloudEquip")
    Result<String> aimsEquipSendCloudEquip(@RequestBody AimsEquipSelectDTO dto) {
        return projEquipRecordVsAimsService.aimsEquipSendCloudEquip(dto);
    }

    /**
     * 手麻设备下载模版
     *
     * @param dto
     * @return
     */
    @ApiOperation("手麻设备下载模版")
    @Log(operName = "设备", operDetail = "手麻设备下载模版", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手麻设备下载模版")
    @PostMapping(value = "/downloadTemplateForAims")
    void downloadTemplateForAims(HttpServletResponse response, @RequestBody AimsEquipSelectDTO dto) {
        projEquipRecordVsAimsService.downloadTemplateForAims(response, dto.getProjectInfoId());
    }

    /**
     * 手麻设备导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @ApiOperation("手麻设备导入模版数据")
    @Log(operName = "设备", operDetail = "手麻设备导入模版数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手麻设备导入模版数据")
    @PostMapping(value = "/importExcelDatasForAims")
    Result importExcelDatasForAims(@RequestParam("file") MultipartFile multipartFile,
                                   @RequestParam("customInfoId") Long customInfoId,
                                   @RequestParam("projectInfoId") Long projectInfoId) {
        return projEquipRecordVsAimsService.importExcelDatas(multipartFile, customInfoId, projectInfoId);
    }

    /**
     * 手麻设备导出数据
     */
    @ApiOperation("手麻设备导出数据")
    @Log(operName = "设备", operDetail = "手麻设备导出数据", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "手麻设备导出数据")
    @PostMapping(value = "/exportExcelDatasForAims")
    public void exportExcelDatasForAims(@RequestBody AimsEquipSelectDTO dto, HttpServletResponse response) {
        projEquipRecordVsAimsService.exportExcelDatas(dto, response);
    }

}
