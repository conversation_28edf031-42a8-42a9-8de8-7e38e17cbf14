package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryApplyCheckDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryApplyOpenDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryDownloadDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryInfoQueryDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryMainInfoQueryDTO;
import com.msun.csm.model.vo.DisasterRecoveryInfoMainInfoVO;
import com.msun.csm.model.vo.ProjDisasterRecoveryInfoRelativeVO;
import com.msun.csm.service.proj.disastercloud.ProjDisasterRecoveryInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-10-11 08:39:18
 */
@Slf4j
@RestController
@RequestMapping("/projDisasterRecoveryInfo")
@Api(tags = "云容灾详情相关接口")
public class ProjDisasterRecoveryInfoController {

    @Resource
    private ProjDisasterRecoveryInfoService projDisasterRecoveryInfoService;

    @Log(operName = "云容灾信息列表查询", operDetail = "云容灾信息列表查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云容灾信息列表查询")
    @ApiOperation("云容灾信息列表查询")
    @PostMapping("findDisasterRecoveryInfo")
    public Result<PageInfo<ProjDisasterRecoveryInfoRelativeVO>> findDisasterRecoveryInfo(@RequestBody ProjDisasterRecoveryInfoQueryDTO dto) {
        return projDisasterRecoveryInfoService.projDisasterRecoveryInfoService(dto);
    }

    @Log(operName = "客户云容灾开通详情", operDetail = "含基础信息, 开通及验收及审核操作节点信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "客户云容灾开通详情")
    @ApiOperation("客户云容灾开通详情")
    @PostMapping("getDisasterRecoveryMainInfo")
    public Result<DisasterRecoveryInfoMainInfoVO> getDisasterRecoveryMainInfo(@Valid @RequestBody ProjDisasterRecoveryMainInfoQueryDTO dto) {
        return projDisasterRecoveryInfoService.getDisasterRecoveryMainInfo(dto);
    }

    @Log(operName = "云容灾申请开通", operDetail = "云容灾申请开通", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云容灾申请开通")
    @ApiOperation("云容灾申请开通")
    @PostMapping("applyDisasterRecoveryOpen")
    public Result<String> applyDisasterRecoveryOpen(@Valid @RequestBody ProjDisasterRecoveryApplyOpenDTO dto) {
        return projDisasterRecoveryInfoService.applyDisasterRecoveryOpen(dto);
    }

    @Log(operName = "云容灾申请验收", operDetail = "云容灾申请验收", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云容灾申请验收")
    @ApiOperation("云容灾申请验收")
    @PostMapping("applyDisasterRecoveryCheck")
    public Result<String> applyDisasterRecoveryCheck(@Valid @RequestBody ProjDisasterRecoveryApplyCheckDTO dto) {
        return projDisasterRecoveryInfoService.applyDisasterRecoveryCheck(dto);
    }

    @Log(
            operName = "上传回执单", operDetail = "上传回执单", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "上传回执单"
    )
    @ApiOperation("上传回执单")
    @PostMapping(value = "/uploadRecieptFile")
    Result<String> uploadApplyOrderPrePlanResFile(@Valid @RequestParam("projDisasterRecoveryInfoId") Long projDisasterRecoveryInfoId,
                                                  @Valid @RequestParam("file") MultipartFile file) {
        return projDisasterRecoveryInfoService.uploadRecieptFile(projDisasterRecoveryInfoId, file);
    }

    @Log(
            operName = "下载文件", operDetail = "下载云容灾文件", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "下载文件"
    )
    @ApiOperation("下载文件")
    @PostMapping(value = "/downloadFile")
    void downloadFile(@Valid @RequestBody ProjDisasterRecoveryDownloadDTO downloadDTO,
                      HttpServletResponse response) {
        log.info("param: {}", downloadDTO);
        projDisasterRecoveryInfoService.downloadFile(response, downloadDTO);
    }
}
