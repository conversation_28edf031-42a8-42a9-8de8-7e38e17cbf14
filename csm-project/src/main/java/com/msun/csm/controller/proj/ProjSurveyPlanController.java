package com.msun.csm.controller.proj;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.SurveyPlanInitPageData;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.param.CopySurveyResultParam;
import com.msun.csm.model.param.DifferenceProductParam;
import com.msun.csm.model.param.SurveyPlanResultParam;
import com.msun.csm.model.req.surveyplan.AddSurveyPlanReq;
import com.msun.csm.model.req.surveyplan.UpdateSurveyPlanReq;
import com.msun.csm.model.resp.surveyplan.DifferenceProductVO;
import com.msun.csm.model.resp.surveyplan.SurveyPlanResult;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProductSurveyPlanMessageVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanTaskRespVO;
import com.msun.csm.service.proj.ProjOrderInfoService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjSurveyPlanService;
import com.msun.csm.service.proj.SurveyPlanService;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/19
 */
@Slf4j
@RestController
@RequestMapping("/surveyPlan")
@Api(tags = "项目调研计划相关接口")
public class ProjSurveyPlanController {
    @Resource
    private ProjSurveyPlanService projSurveyPlanService;
    @Resource
    private ProjSurveyPlanService surveyPlanService;
    @Resource
    private ProjOrderInfoService prodOrderInfoService;
    @Resource
    private ProjProjectInfoService projProjectInfoService;

    @Resource
    private SurveyPlanService surveyPlanServiceNew;

    /**
     * 获取项目调研计划任务列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/findSurveyPlanTask")
    public Result<SurveyPlanTaskRespVO> findSurveyPlanTask(@RequestBody ProjSurveyPlanDTO dto) {
        Result<SurveyPlanTaskRespVO> surveyPlanTask = surveyPlanService.findSurveyPlanTask(dto);
        return surveyPlanTask;
    }


    /**
     * 取消确认最终结果
     *
     * @param param 参数
     */
    @PostMapping("/cancelFinalResult")
    public Result<Boolean> cancelFinalResult(@RequestBody SimpleId param) {
        try {
            Boolean result = surveyPlanService.cancelFinalResult(param.getId());
            return new Result<>(true, 0, "取消成功", result);
        } catch (Exception e) {
            log.error("取消确认最终结果，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return new Result<>(false, 701, "取消确认最终结果，预期之外的错误：" + e.getMessage(), false);
        }
    }

    /**
     * 说明: 获取项目调研计划任务-医院列表
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.util.List<com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO>>
     * @author: Yhongmin
     * @createAt: 2024/8/29 11:16
     * @remark: Copyright
     */
    @ApiOperation("获取项目调研计划任务-医院列表")
    @PostMapping("/findSurveyPlanHospitalProduct")
    public Result<SurveyPlanInitVO> findSurveyPlanHospitalProduct(@RequestBody ProjSurveyPlanDTO dto) {
        return surveyPlanService.findSurveyPlanHospitalProduct(dto);
    }

    /**
     * 添加项目调研计划任务
     *
     * @param addSurveyPlanReq
     * @return
     */
    @PostMapping("/addSurveyPlanTask")
    public Result addSurveyPlanTask(@RequestBody @Valid AddSurveyPlanReq addSurveyPlanReq) {
        return surveyPlanService.addSurveyPlanTask(addSurveyPlanReq);
    }

    /**
     * 批量添加项目调研计划任务
     *
     * @param req
     * @return
     */
    @PostMapping("/saveSurveyPlanTaskList")
    public Result saveSurveyPlanTaskList(@RequestBody @Valid AddSurveyPlanReq req) {
        try {
            return surveyPlanService.saveSurveyPlanTaskList(req);
        } catch (Exception e) {
            log.error("批量分配调研计划，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(ResultEnum.FAIL);
        }
    }

    /**
     * 删除项目调研计划任务
     *
     * @param dto
     * @return
     */
    @PostMapping("/deleteSurveyPlanTask")
    public Result deleteSurveyPlanTask(@RequestBody @Valid ProjSurveyPlanDTO dto) {
        return surveyPlanService.deleteSurveyPlanTask(dto);
    }

    /**
     * 更新项目调研计划任务
     *
     * @param updateSurveyPlanReq
     * @return
     */
    @PostMapping("/updateSurveyPlanTask")
    public Result updateSurveyPlanTask(@RequestBody UpdateSurveyPlanReq updateSurveyPlanReq) {
        if (updateSurveyPlanReq.getSurveyPlanId() == null) {
            return Result.fail(ResultEnum.VALIDATE_NULL_OR_EMPTY);
        }
        try {
            return surveyPlanService.updateSurveyPlanTask(updateSurveyPlanReq);
        } catch (Exception e) {
            log.error("修改调研计划，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 批量更新项目调研计划任务
     *
     * @param req
     * @return
     */
    @PostMapping("/updateSurveyPlanTaskList")
    public Result updateSurveyPlanTaskList(@RequestBody UpdateSurveyPlanReq req) {
        try {
            return surveyPlanService.updateSurveyPlanTaskList(req);
        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(ResultEnum.FAIL);
        }
    }


    /**
     * 调研结果总结
     *
     * @param param 参数
     */
    @PostMapping("/surveyPlanResult")
    public Result<List<SurveyPlanResult>> surveyPlanResult(@RequestBody @Valid SurveyPlanResultParam param) {
        try {
            ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(param.getProjectInfoId());
            ProjOrderInfo orderInfo = prodOrderInfoService.selectByPrimaryKey(projProjectInfo.getOrderInfoId());
            if (orderInfo == null) {
                throw new RuntimeException("修改项目自身状态-上线状态-同步运营平台节点数据后更新, 未查询到运营平台工单信息");
            }
            Result<List<SurveyPlanResult>> result = surveyPlanService.surveyPlanResult(param);
            if (result.isSuccess() && CollUtil.isNotEmpty(result.getData())) {
                //校验分院模式下分配的是否调研完成或者外采类的订单
                if (projSurveyPlanService.isConfirmData(param.getProjectInfoId()) || NumberEnum.NO_8.num().equals(orderInfo.getDeliveryOrderType())) {
                    result.getData().forEach(item -> item.setCompleteCode(true));
                    if (NumberEnum.NO_8.num().equals(orderInfo.getDeliveryOrderType())) {
                        result.getData().forEach(item -> item.setStatus("无需调研"));
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.error("调研结果总结，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 引用调研结果
     */
    @PostMapping("/copySurveyResult")
    public Result<Boolean> copySurveyResult(@RequestBody @Valid CopySurveyResultParam param, BindingResult bindingResult) {
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("引用调研结果，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            Result<Boolean> result = new Result<>();
            result.setCode(ResultEnum.PARAM_INVALID.getIndex());
            result.setSuccess(false);
            result.setMsg(msg.toString());
            return result;
        }
        try {
            return surveyPlanService.copySurveyResult(param);
        } catch (Exception e) {
            log.error("调研结果总结，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            Result<Boolean> result = new Result<>();
            result.setCode(ResultEnum.FAIL.getIndex());
            result.setSuccess(false);
            result.setMsg("发生异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 确认完成展示左侧列表
     */
    @PostMapping(value = "/showDifferenceProduct")
    Result<List<DifferenceProductVO>> showDifferenceProduct(@RequestBody DifferenceProductParam param) {
        log.info("确认完成展示左侧列表，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(surveyPlanService.showDifferenceProduct(param));
        } catch (Exception e) {
            log.error("确认完成展示左侧列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 查询产品调研节点-产品专项调研子页面数据
     */
    @PostMapping(value = "/selectSurveyProductJobMenuDetail")
    public Result<List<ProductBacklogUrlVO>> selectSurveyProductJobMenuDetail(@RequestBody ProjProductBacklogDTO dto) {
        log.info("查询产品调研节点-产品专项调研子页面数据，参数={}", JSON.toJSONString(dto));
        try {
            return surveyPlanService.selectSurveyProductJobMenuDetail(dto);
        } catch (Exception e) {
            log.error("查询产品专项调研子页面数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }


    /**
     * 产品业务调研消息提醒
     */
    @PostMapping(value = "/productSurveyPlanMessage")
    public Result<ProductSurveyPlanMessageVO> productSurveyPlanMessage(@RequestBody ProjSurveyPlanDTO dto) {
        try {
            return surveyPlanService.productSurveyPlanMessage(dto);
        } catch (Exception e) {
            log.error("产品业务调研消息提醒，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("产品业务调研消息提醒异常：" + e.getMessage());
        }
    }

    /**
     * 提交审核（支持批量）
     */
    @PostMapping("/submitAudit")
    public Result<Void> submitAudit(@RequestBody UpdateSurveyPlanReq req) {
        try {
            boolean result = surveyPlanService.submitAudit(req);
            if (result) {
                return Result.success(null, "提交审核成功");
            }
            return Result.fail("提交审核失败");
        } catch (Exception e) {
            log.error("提交审核，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 获取产品业务调研页面初始化需要的参数
     */
    @PostMapping("/initSurveyPlanData")
    public Result<SurveyPlanInitPageData> initSurveyPlanData(@RequestBody ProjectInfoId req) {
        try {
            SurveyPlanInitPageData surveyPlanInitPageData = surveyPlanServiceNew.initSurveyPlanData(req.getProjectInfoId());
            return Result.success(surveyPlanInitPageData, "成功");
        } catch (Exception e) {
            log.error("获取产品业务调研页面初始化需要的参数，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 撤销提交审核
     */
    @PostMapping("/revertSubmitAudit")
    public Result<Void> revertSubmitAudit(@RequestBody UpdateSurveyPlanReq req) {
        try {
            boolean result = surveyPlanService.revertSubmitAudit(req.getSurveyPlanId());
            if (result) {
                return Result.success(null, "撤销提交审核成功");
            }
            return Result.fail("撤销提交审核失败");
        } catch (Exception e) {
            log.error("撤销提交审核，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 撤销确认
     */
    @PostMapping("/revertConfirm")
    public Result<Void> revertConfirm(@RequestBody UpdateSurveyPlanReq req) {
        try {
            boolean result = surveyPlanService.revertConfirm(req.getSurveyPlanId());
            if (result) {
                return Result.success(null, "撤销确认成功");
            }
            return Result.fail("撤销确认失败");
        } catch (Exception e) {
            log.error("撤销确认，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 导出调研计划数据
     *
     * @param response
     * @param dto
     */
    @Log(operName = "导出调研计划数据", operDetail = "导出调研计划数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "导出调研计划数据")
    @ApiOperation("导出调研计划数据")
    @PostMapping(value = "/surveyPlanExportExcel")
    void surveyPlanExportExcel(HttpServletResponse response, @RequestBody ProjSurveyPlanDTO dto) {
        surveyPlanService.surveyPlanExportExcel(response, dto);
    }

}
