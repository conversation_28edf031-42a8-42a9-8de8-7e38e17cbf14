package com.msun.csm.controller.common;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFShape;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/aaa")
@Api(tags = "前端下拉基础数据查询")
public class CeShi {

    @Value("${project.obs.prePath}")
    private String prePath;

    /**
     * 打印单元格数据（支持不同类型的单元格）
     *
     * @param cell 单元格
     */
    private static void printCellData(Cell cell) {
        // 获取单元格的类型并打印对应的内容
        switch (cell.getCellType()) {
            case NUMERIC:
                log.info("数字类型：{}", cell.getNumericCellValue());
                break;
            case STRING:
                log.info("字符串类型：{}", cell.getStringCellValue());
                break;
            case BOOLEAN:
                log.info("布尔类型：{}", cell.getBooleanCellValue());
                break;
            case FORMULA:
                log.info("公式类型：{}", cell.getCellFormula());
                break;
            default:
                log.info("未知类型");
                break;
        }
    }

    /**
     * 解析 Excel 第一个工作表的所有数据，并保存其中的图片
     *
     * @param excelFilePath Excel 文件路径
     * @param imageSaveDir  图片保存的文件夹路径
     * @throws IOException
     */
    public void parseExcel(String excelFilePath, String imageSaveDir) throws IOException {
        // 打开 Excel 文件
        try (FileInputStream fis = new FileInputStream(excelFilePath); XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            // 获取第一个工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            File file = new File(excelFilePath);
//            MultipartFile multipartFile = new CustomMultipartFile(file);
            // 遍历工作表中的所有行和列，打印每个单元格的内容
            log.info("Excel 文件数据:");
            Iterator<Row> rowIterator = sheet.iterator();
            List<String> dispStrList = new ArrayList<>();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                Iterator<Cell> cellIterator = row.cellIterator();
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    printCellData(cell);
                    if (cell.getCellType().equals(CellType.FORMULA)) {
                        // 获取公式结果
                        String formulaResult = cell.getStringCellValue();
                        dispStrList.add(formulaResult);
                        // 假设头像是一个嵌入型超链接
                        XSSFDrawing drawing = (XSSFDrawing) cell.getSheet().createDrawingPatriarch();
                        List<XSSFShape> shapes = sheet.getDrawingPatriarch().getShapes();

                    }
                }
               /* Map<String, WpsImg> getWpsImgs = WpsImgUtil.getWpsImgs(dispStrList, multipartFile);
                dispStrList.stream().forEach(v -> {
                    if (StrUtil.isNotBlank(v)) {
                        WpsImg vs = getWpsImgs.get(v);
                        if (Objects.nonNull(vs)) {
                            XSSFPictureData pa = vs.getPictureData();
                            if (Objects.nonNull(pa)) {
                                saveImageToFile(pa.getData(), vs.getImgName());
                            }
                        }
                        log.info("图片保存完成！" + getWpsImgs.get(v));
                    }
                });*/
            }
            // 获取工作表中的所有图片
            List<XSSFPictureData> pictures = workbook.getAllPictures();
            for (int i = 0; i < pictures.size(); i++) {
                XSSFPictureData picture = pictures.get(i);
                // 图片的字节数据
                byte[] imageData = picture.getData();
                String imageFileName = "image" + (i + 1) + "." + picture.suggestFileExtension();
            }
            log.info("图片保存完成！");
        }
    }

    /**
     * 将图片字节数据保存到指定的文件路径
     */
    private void saveImageToFile(byte[] imageBytes, String imageFileName) {
        // 将 byte 数组转换为 InputStream
        InputStream inputStream = new ByteArrayInputStream(imageBytes);
        try {
            String objectKey = prePath + StrUtil.SLASH + "report" + new SimpleDateFormat("yyyyMMddHHmmss").format(System.currentTimeMillis()) + StrUtil.SLASH + imageFileName;
            log.info("图片保存路径：" + objectKey);
            PutObjectResult p = OBSClientUtils.uploadFileStream(inputStream, objectKey, true);
            log.info("图片保存路径：" + p);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/bbb")
    @ACROSS
    public void queryAllContractCustomer(@RequestParam(required = false) String keyword) {
        String excelFilePath = "D:/msun/aaa.xlsx";  // Excel 文件路径
        String imageSaveDir = "D:\\";  // 图片保存路径
        try {
            // 解析 Excel 数据并保存图片
            parseExcel(excelFilePath, imageSaveDir);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) {
        String cloudDomain = "https://imsp-test.msunhis.com";
        cloudDomain = cloudDomain.replace("https://", "");
        cloudDomain = cloudDomain.replace("http://", "");
        if (cloudDomain.contains(".msunhis")) {
            cloudDomain = cloudDomain.substring(0, cloudDomain.indexOf(".msunhis", 1));
        }
        log.error(cloudDomain);
    }
}