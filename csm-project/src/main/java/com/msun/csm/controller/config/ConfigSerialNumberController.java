//package com.msun.csm.controller;
//
//import com.msun.csm.dao.entity.config.ConfigSerialNumber;
//import com.msun.csm.service.config.ConfigSerialNumberService;
//
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.Api;
//
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import javax.annotation.Resource;
//import javax.validation.Valid;
//
//import lombok.extern.slf4j.Slf4j;
//
//import jakarta.annotation.Resource;
//import io.swagger.v3.oas.annotations.Parameter;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.core.toolkit.IdWorker;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.msun.csm.common.annotation.Log;
//import cn.hutool.core.collection.CollUtil;
//
//import com.msun.systemimage.vo.CommonJsonResponseVO;
//import com.msun.systemimage.utils.DataToolUtil;
//import com.msun.systemimage.enumeration.AppReturnCodeEnum;
//import com.msun.systemimage.dto.PageRequestDTO;
//import com.msun.systemimage.utils.PageUtil;
//
//
//
//
///**
// * <AUTHOR>
// * @since 2024-05-24 09:45:08
// */
//@Slf4j
//@RestController
//@RequestMapping("/configSerialNumber")
//@Api (tags = "编号创建规则相关接口")
//    public class ConfigSerialNumberController {
//
//    @Resource
//    private ConfigSerialNumberService configSerialNumberService;
//
//    /**
//     * 查询编号创建规则信息
//     *
//     * @param dto
//     * @return
//     */
//    @ApiOperation("查询编号创建规则信息")
//    @Log(operName = "查询编号创建规则信息", operDetail = "查询编号创建规则信息",
//            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询编号创建规则信息")
//    @RequestMapping("/findconfigSerialNumberInfo")
//    public Result<PageInfo<configSerialNumberVO>> findconfigSerialNumberInfo(@Valid @RequestBody configSerialNumberDTO dto) {
//        log.info("查询项目信息：{}", JSON.toJSONString(dto));
//        return configSerialNumberService.findconfigSerialNumberInfo(dto);
//    }
//}
