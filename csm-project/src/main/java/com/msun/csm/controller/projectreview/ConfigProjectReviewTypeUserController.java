package com.msun.csm.controller.projectreview;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserReq;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserSaveReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewTypeUserResp;
import com.msun.csm.service.config.projectreview.ConfigProjectReviewTypeUserService;


/**
 * 项目审核人员配置表(configProjectReviewTypeUser)控制器
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */

@RestController
@Api(tags = "项目审核人员配置表")
@RequestMapping("/configProjectReviewTypeUser")
public class ConfigProjectReviewTypeUserController {

    @Resource
    private ConfigProjectReviewTypeUserService configProjectReviewTypeUserService;

    /**
     * 项目审核类型字典 分页查询
     *
     * @param dto
     */
    @Log(operName = "项目审核人员配置表 分页查询", operDetail = "项目审核人员配置表 分页查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核人员配置表 分页查询")
    @ApiOperation("项目审核人员配置表 分页查询")
    @PostMapping(value = "/findDataPage")
    Result<PageInfo<ConfigProjectReviewTypeUserResp>> findDataPage(@RequestBody ConfigProjectReviewTypeUserReq dto) {
        return configProjectReviewTypeUserService.findDataPage(dto);
    }

    /**
     * 项目审核人员配置表保存修改
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目审核人员配置表保存修改", operDetail = "项目审核人员配置表保存修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核人员配置表保存修改")
    @ApiOperation("项目审核人员配置表保存修改")
    @PostMapping(value = "/saveData")
    Result<String> saveData(@RequestBody ConfigProjectReviewTypeUserSaveReq dto) {
        return configProjectReviewTypeUserService.saveData(dto);
    }

    /**
     * 项目审核人员配置表删除
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目审核人员配置表删除", operDetail = "项目审核人员配置表删除", intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核人员配置表删除")
    @ApiOperation("项目审核人员配置表删除")
    @PostMapping(value = "/delData")
    Result<String> delData(@RequestBody ConfigProjectReviewTypeUserReq dto) {
        return configProjectReviewTypeUserService.delData(dto);
    }

}
