package com.msun.csm.controller.common;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.schedule.TaskSchedule;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/29 16:16
 */
@Api(tags = "公共方法")
@Slf4j
@RestController
@RequestMapping("/comm")
public class CommonController {

    @Autowired
    private TaskSchedule taskSchedule;


    @ApiOperation("删除服务器老文件")
    @ACROSS
    @ResponseBody
    @GetMapping(value = "/recClearLonglongAgoLogFiles")
    public Result<?> recClearLonglongAgoLogFiles() {
        taskSchedule.doClearLonglongAgoLogFiles();
        return Result.success();
    }
}
