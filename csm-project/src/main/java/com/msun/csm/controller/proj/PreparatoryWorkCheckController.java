package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.PreparatoryWorkDTO;
import com.msun.csm.model.param.AuditResultsParam;
import com.msun.csm.model.param.PreparatoryWorkListParam;
import com.msun.csm.model.param.UpdateProjMilestoneInfoAfterAuditPmoParam;
import com.msun.csm.service.proj.PreparatoryWorkService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "准备工作检查及审核")
@Slf4j
@Controller
@RequestMapping("preparatoryWorkCheck")
public class PreparatoryWorkCheckController {

    @Resource
    private PreparatoryWorkService preparatoryWorkService;

    @Value("${project.feign.oldImsp.front-url}")
    private String domain;


    /**
     * 准备工作列表
     *
     * @param preparatoryWorkListParam 参数
     * @return 准备工作列表
     */
    @Log(operName = "准备工作列表查询", operDetail = "准备工作检查及审核查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "准备工作检查及审核查询")
    @ApiOperation("获取准备工作列表")
    @ResponseBody
    @PostMapping(value = "/getPreparatoryWorkList")
    Result<PreparatoryWorkDTO> getPreparatoryWorkList(@RequestBody PreparatoryWorkListParam preparatoryWorkListParam) {
        log.info("获取准备工作列表，参数={}", JSON.toJSONString(preparatoryWorkListParam));
        try {
            return Result.success(
                    preparatoryWorkService.getPreparatoryWorkList(preparatoryWorkListParam.getProjectInfoId(),
                            preparatoryWorkListParam.getHospitalInfoId()));
        } catch (Exception e) {
            log.error("获取准备工作列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }

    /**
     * 导出准备工作列表
     *
     * @param projectInfoId  项目信息ID
     * @param hospitalInfoId 医院信息ID
     * @param response       请求响应
     */
    @Log(operName = "准备工作列表", operDetail = "准备工作列表导出", operLogType = Log.LogOperType.EXP,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "准备工作列表导出")
    @ApiOperation("导出准备工作列表")
    @RequestMapping("/exportPreparatoryWorkListExcel")
    void hospitalExportExcel(String projectInfoId, String hospitalInfoId, HttpServletResponse response) {
        log.info("导出准备工作列表，projectInfoId={}，hospitalInfoId={}", projectInfoId, hospitalInfoId);
        Long hospitalId;
        if (StringUtils.isBlank(hospitalInfoId) || "null".equalsIgnoreCase(hospitalInfoId)) {
            hospitalId = null;
        } else {
            hospitalId = Long.valueOf(hospitalInfoId);
        }
        try {
            preparatoryWorkService.exportPreparatoryWorkListExcel(Long.valueOf(projectInfoId), hospitalId, response);
        } catch (NumberFormatException e) {
            log.error("导出准备工作列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }


    @Log(operName = "准备工作列表", operDetail = "查看审核结果", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查看审核结果")
    @ApiOperation("查看审核结果")
    @ResponseBody
    @PostMapping(value = "/getAuditResults")
    Result<String> getAuditResults(@RequestBody AuditResultsParam param) {
        log.info("查看审核结果，detailId={}", param.getDetailId());
        if (StringUtils.isBlank(param.getDetailId())) {
            return Result.fail(1, "参数detailId不可为空");
        }
        try {
            String domainInfo = StringUtils.isBlank(domain) ? "https://imsp.msuncloud.com/imsp-cloud/" : domain;
            String url = domainInfo + "/pmoReadyWorkController/developmentCompletedHistoryView?detailId="
                    + param.getDetailId();
            return Result.success(url);
        } catch (Exception e) {
            log.error("查看审核结果，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }

    @CsmSign
    @Log(operName = "更新里程碑信息", operDetail = "PMO审核成功之后更新里程碑信息", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "PMO审核成功之后更新里程碑信息")
    @ApiOperation("PMO审核成功之后更新里程碑信息")
    @ResponseBody
    @PostMapping(value = "/updateProjMilestoneInfoAfterAuditPmo")
    Result<Boolean> updateProjMilestoneInfoAfterAuditPmo(@RequestBody UpdateProjMilestoneInfoAfterAuditPmoParam param) {
        log.info("PMO审核成功之后更新里程碑信息，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(preparatoryWorkService.updateProjMilestoneInfoAfterAuditPmo(param));
        } catch (Exception e) {
            log.error("PMO审核成功之后更新里程碑信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }

}
