package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.dao.entity.proj.GetProjectDailyReportParam;
import com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParam;
import com.msun.csm.dao.entity.proj.InitProjectDailyReport;
import com.msun.csm.dao.entity.proj.ProjectDailyReportInfo;
import com.msun.csm.dao.entity.proj.ProjectDailyReportRecordVO;
import com.msun.csm.dao.entity.proj.SaveProjProjectDailyReportCommentsParam;
import com.msun.csm.dao.entity.proj.SaveProjectDailyReportParam;
import com.msun.csm.service.proj.ProjectDailyRecordService;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目经理填写项目日报
 */
@Slf4j
@RestController
@RequestMapping("/projectDailyRecord")
public class ProjectDailyRecordController {

    @Resource
    private ProjectDailyRecordService projectDailyRecordService;

    /**
     * 初始化项目日报填写页面所需参数
     */
    @PostMapping("/initProjectDailyReportDetailPage")
    Result<InitProjectDailyReport> initProjectDailyReportDetailPage(@RequestBody ProjectInfoId projectInfoId) {
        try {
            InitProjectDailyReport initProjectDailyReport = projectDailyRecordService.initProjectDailyReportDetailPage(projectInfoId.getProjectInfoId());
            log.info("初始化项目日报填写页面所需参数，projectInfoId={}，结果={}", projectInfoId.getProjectInfoId(), JSON.toJSONString(initProjectDailyReport));
            return Result.success(initProjectDailyReport);
        } catch (Exception e) {
            log.error("初始化项目日报填写页面所需参数，发生异常，projectInfoId={}，errMsg={}，stackInfo=", projectInfoId.getProjectInfoId(), e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询项目日报内容
     *
     * @param param 参数
     */
    @PostMapping("/getProjectDailyReportDetail")
    Result<ProjectDailyReportInfo> getProjectDailyReportDetail(@RequestBody GetProjectDailyReportParam param) {
        try {
            ProjectDailyReportInfo projectDailyReportDetail = projectDailyRecordService.getProjectDailyReportDetail(param);
            log.info("查询项目日报内容，projectInfoId={}，reportSendTime={}，结果={}", param.getProjectInfoId(), param.getReportSendTime(), JSON.toJSONString(projectDailyReportDetail));
            return Result.success(projectDailyReportDetail);
        } catch (Exception e) {
            log.error("保存项目日报草稿/发送项目日报，发生异常，projectInfoId={}，reportSendTime={}，errMsg={}，stackInfo=", param.getProjectInfoId(), param.getReportSendTime(), e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存项目日报草稿/发送项目日报
     *
     * @param param 参数
     */
    @PostMapping("/saveProjectDailyReportDetail")
    Result<Void> saveProjectDailyReportDetail(@RequestBody SaveProjectDailyReportParam param) {
        try {
            boolean result = projectDailyRecordService.saveProjectDailyReportDetail(param);
            return Result.success();
        } catch (Exception e) {
            log.error("保存项目日报草稿/发送项目日报，发生异常，参数={}，errMsg={}，stackInfo=", JSON.toJSONString(param), e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 发表评论
     *
     * @param param 参数
     */
    @PostMapping("/saveProjectDailyReportComments")
    Result<Void> saveProjectDailyReportComments(@RequestBody SaveProjProjectDailyReportCommentsParam param) {
        try {
            boolean result = projectDailyRecordService.saveProjectDailyReportComments(param);
            return Result.success();
        } catch (Exception e) {
            log.error("发表评论，发生异常，projectDailyReportRecordId={}，reportCommentsContent={}，errMsg={}，stackInfo=", param.getProjectDailyReportRecordId(), param.getReportCommentsContent(), e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }


    /**
     * 查询项目日报列表
     *
     * @param param 参数
     */
    @PostMapping("/getProjectDailyReportRecord")
    Result<PageList<ProjectDailyReportRecordVO>> getProjectDailyReportRecord(@RequestBody GetProjectDailyReportRecordParam param) {
        return Result.success(projectDailyRecordService.getProjectDailyReportRecord(param));
    }

    /**
     * 执行项目日报定时任务
     */
    @PostMapping("/sendProjectDailyReportMessage")
    Result<Void> getProjectDailyReportRecord() {
        projectDailyRecordService.sendProjectDailyReportMessage();
        return Result.success();
    }
}
