package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.dict.DictInterface;
import com.msun.csm.dao.entity.dict.DictInterfaceFirm;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjTaskProgress;
import com.msun.csm.dao.entity.proj.ThirdInterfaceMemberDept;
import com.msun.csm.model.dto.CreateAppAuthorizationDTO;
import com.msun.csm.model.dto.InterfaceMemberParamDTO;
import com.msun.csm.model.dto.InterfaceReviewPageDTO;
import com.msun.csm.model.dto.ProjThirdInterfaceDTO;
import com.msun.csm.model.dto.ProjThirdInterfacePageDTO;
import com.msun.csm.model.dto.SaveDeployDeviceInfoDTO;
import com.msun.csm.model.req.thirdinterface.GetInterfaceApplyDetailReq;
import com.msun.csm.model.req.thirdinterface.InterfaceBaseReq;
import com.msun.csm.model.req.thirdinterface.UpdateAuthFileReq;
import com.msun.csm.model.vo.DeployDeviceInfoVO;
import com.msun.csm.model.vo.InterfaceReviewVO;
import com.msun.csm.model.vo.ProjInterfaceRecordLogVo;
import com.msun.csm.model.vo.ProjThirdInterfaceVO;
import com.msun.csm.model.vo.ThirdInterfaceEntryCheckVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.service.proj.ProjThirdInterfaceService;


/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */
@Slf4j
@RestController
@RequestMapping ("/thirdInterface")
@Api (tags = "三方接口")
public class ProjThirdInterfaceController {

    @Resource
    private ProjThirdInterfaceService projThirdInterfaceService;

    /**
     * 分页查询三方接口列表信息
     *
     * @param dto
     * @return
     */
    @Log (operName = "分页查询三方接口列表信息", operDetail = "分页查询三方接口列表信息",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "分页查询三方接口列表信息")
    @PostMapping ("/selectByPage")
    public Result<PageInfo<ProjThirdInterfaceVO>> selectByPage(@RequestBody ProjThirdInterfacePageDTO dto) {
        return projThirdInterfaceService.selectByPage(dto);
    }

    /**
     * 保存接口的进度详情
     *
     * @param taskProgress
     * @return
     */
    @Log (operName = "保存接口的进度详情", operDetail = "保存接口的进度详情",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "保存接口的进度详情")
    @PostMapping ("/saveInterfaceTaskProgress")
    public Result<PageInfo<ProjThirdInterfaceVO>> saveInterfaceTaskProgress(@RequestBody ProjTaskProgress taskProgress) {
        return projThirdInterfaceService.saveInterfaceTaskProgress(taskProgress);
    }

    /**
     * 查询接口名称字典数据
     *
     * @return
     */
    @Log (operName = "查询接口名称字典数据", operDetail = "查询接口名称字典数据",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询接口名称字典数据")
    @PostMapping ("/selectDictInterface")
    public Result<List<DictInterface>> selectDictInterface() {
        return projThirdInterfaceService.selectDictInterface();
    }

    /**
     * 查询接口厂商字典数据
     *
     * @return
     */
    @Log (operName = "查询接口厂商字典数据", operDetail = "查询接口厂商字典数据",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询接口厂商字典数据")
    @PostMapping ("/selectDictInterfaceFirm")
    public Result<List<DictInterfaceFirm>> selectDictInterfaceFirm() {
        return projThirdInterfaceService.selectDictInterfaceFirm();
    }

    /**
     * 查询接口产品字典数据
     *
     * @return
     */
    @Log (operName = "查询接口产品字典数据", operDetail = "查询接口产品字典数据",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询接口产品字典数据")
    @PostMapping ("/selectDictProductForInterface")
    public Result<List<DictProduct>> selectDictProductForInterface() {
        return projThirdInterfaceService.selectDictProductForInterface();
    }

    /**
     * 保存三方接口信息
     *
     * @param dto
     * @return
     */
    @Log (operName = "保存三方接口信息", operDetail = "保存三方接口信息",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "保存三方接口信息")
    @PostMapping ("/saveInterface")
    public Result saveInterface(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.saveInterface(dto);
    }

    /**
     * 修改/裁定三方接口信息【根据前端传入的标识区分 modifyFlag】
     *
     * @param dto
     * @return
     */
    @Log (operName = "修改/裁定三方接口信息", operDetail = "修改/裁定三方接口信息",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "修改/裁定三方接口信息")
    @PostMapping ("/updateInterface")
    public Result updateInterface(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.updateInterface(dto);
    }

    /**
     * 根据主键id删除三方接口信息
     *
     * @param dto
     * @return
     */
    @Log (operName = "根据主键id删除三方接口信息", operDetail = "根据主键id删除三方接口信息",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "根据主键id删除三方接口信息")
    @PostMapping ("/deleteInterfaceById")
    public Result deleteInterfaceById(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.deleteInterfaceById(dto.getThirdInterfaceIdList());
    }

    /**
     * 下载授权书模板
     *
     * @param req
     * @return
     */
    @Log (operName = "下载授权书模板", operDetail = "下载授权书模板",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "下载授权书模板")
    @PostMapping ("/downloadAuthTemplate")
    public void downloadAuthTemplate(@RequestBody @Valid InterfaceBaseReq req, HttpServletResponse response) {
        projThirdInterfaceService.downloadAuthTemplate(req, response);
    }

    /**
     * 三方接口导入模板下载
     *
     * @param response
     */
    @Log (operName = "三方接口导入模板下载", operDetail = "三方接口导入模板下载", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口导入模板下载")
    @ApiOperation ("三方接口导入模板下载")
    @PostMapping (value = "/downloadThirdInterfaceTemplate")
    void downloadThirdInterfaceTemplate(HttpServletResponse response, @RequestBody ProjThirdInterfaceDTO dto) {
        projThirdInterfaceService.downloadThirdInterfaceTemplate(response, dto.getProjectInfoId());
    }

    /**
     * 选中接口批量上传授权书后更新数据
     *
     * @param req
     * @return
     */
    @Log (operName = "选中接口批量上传授权书后更新数据", operDetail = "选中接口批量上传授权书后更新数据",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "选中接口批量上传授权书后更新数据")
    @PostMapping ("/updateAuthLetterFiles")
    public Result updateAuthLetterFiles(@RequestBody @Valid UpdateAuthFileReq req) {
        return projThirdInterfaceService.updateAuthLetterFiles(req);
    }

    /**
     * 导入三方接口信息
     *
     * @param file
     * @param customInfoId
     * @param projectType
     * @param projectInfoId
     * @return
     */
    @Log (
            operName = "导入三方接口信息", operDetail = "导入三方接口信息", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "导入三方接口信息"
    )
    @ApiOperation ("导入三方接口信息")
    @PostMapping ("/excelImportThirdInterface")
    Result excelImportThirdInterface(@RequestParam ("file") MultipartFile file,
                                     @RequestParam ("customInfoId") Long customInfoId,
                                     @RequestParam ("projectType") Integer projectType,
                                     @RequestParam ("projectInfoId") Long projectInfoId) {
        return projThirdInterfaceService.excelImportThirdInterface(file, customInfoId, projectType, projectInfoId);
    }

    /**
     * 三方接口导出Excel
     *
     * @param dto
     * @return
     */
    @Log (
            operName = "三方接口导出Excel", operDetail = "三方接口导出Excel", operLogType = Log.LogOperType.EXP,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口导出Excel"
    )
    @ApiOperation ("三方接口导出Excel")
    @PostMapping ("/thirdInterfaceExportExcel")
    void thirdInterfaceExportExcel(HttpServletResponse response, @RequestBody ProjThirdInterfaceDTO dto) {
        projThirdInterfaceService.thirdInterfaceExportExcel(response, dto);
    }

    /**
     * 查询三方接口明细数据
     *
     * @param thirdInterfaceId
     * @return
     */
    @Log (
            operName = "查询三方接口明细数据", operDetail = "查询三方接口明细数据", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询三方接口明细数据"
    )
    @ApiOperation ("查询三方接口明细数据")
    @GetMapping ("/selectThirdInterfaceDetail")
    Result selectThirdInterfaceDetail(@RequestParam ("thirdInterfaceId") Long thirdInterfaceId) {
        return projThirdInterfaceService.selectThirdInterfaceDetail(thirdInterfaceId);
    }

    /**
     * 指定责任人，当责任人属于接口分公司时。转到运营平台走接口实施流程
     *
     * @param dto
     * @return
     */
    @Log (
            operName = "三方接口指定责任人", operDetail = "三方接口指定责任人", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口指定责任人"
    )
    @ApiOperation ("三方接口指定责任人")
    @PostMapping ("/updateDirPerson")
    Result updateDirPerson(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.updateDirPerson(dto);
    }

    /**
     * 三方接口申请裁定
     *
     * @return
     */
    @Log (operName = "三方接口申请裁定", operDetail = "三方接口申请裁定",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "三方接口申请裁定")
    @PostMapping ("/interfaceApplyRuled")
    public Result interfaceApplyRuled(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.interfaceApplyRuled(dto.getThirdInterfaceIdList());
    }

    /**
     * 三方接口裁定查询列表
     *
     * @return
     */
    @Log (operName = "三方接口裁定查询列表", operDetail = "三方接口裁定查询列表",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "三方接口裁定查询列表")
    @PostMapping ("/selectPageForReviewInterface")
    public Result<PageInfo<InterfaceReviewVO>> selectPageForReviewInterface(@RequestBody InterfaceReviewPageDTO dto) {
        return projThirdInterfaceService.selectPageForReviewInterface(dto);
    }

    /**
     * 批量申请正式环境授权
     *
     * @return
     */
    @Log (operName = "批量申请正式环境授权", operDetail = "批量申请正式环境授权",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "批量申请正式环境授权")
    @PostMapping ("/authInterFace")
    public Result authInterFace(@RequestBody @Valid List<Long> thirdInterfaceIds) {
        return projThirdInterfaceService.authInterFace(thirdInterfaceIds);
    }

    /**
     * 检测接口测试环境是否已完成
     *
     * @param item
     * @return
     */
    @Log (operName = "检测接口测试环境是否已完成", operDetail = "检测接口测试环境是否已完成",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "检测接口测试环境是否已完成")
    @PostMapping ("/checkApp")
    public Result checkApp(@RequestBody GetInterfaceApplyDetailReq item) {
        return projThirdInterfaceService.checkApp(item);
    }

    /**
     * 三方接口调研阶段完成
     *
     * @param milestoneInfoId
     * @return
     */
    @Log (operName = "三方接口调研阶段完成", operDetail = "三方接口调研阶段完成", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口调研阶段完成")
    @GetMapping ("/thirdInterfaceSurveyFinish")
    public Result thirdInterfaceSurveyFinish(@RequestParam ("milestoneInfoId") Long milestoneInfoId) {
        return projThirdInterfaceService.thirdInterfaceSurveyFinish(milestoneInfoId);
    }

    /**
     * 三方接口入驻阶段检测
     *
     * @param projectInfoId
     * @return
     */
    @Log (operName = "三方接口入驻阶段检测", operDetail = "三方接口入驻阶段检测", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口入驻阶段检测")
    @GetMapping ("/thirdInterfaceEntryCheck")
    public Result<ThirdInterfaceEntryCheckVO> thirdInterfaceEntryCheck(@RequestParam ("projectInfoId") Long projectInfoId) {
        return projThirdInterfaceService.thirdInterfaceEntryCheck(projectInfoId);
    }

    /**
     * 三方接口准备阶段完成
     *
     * @param milestoneInfoId
     * @return
     */
    @Log (operName = "三方接口准备阶段完成", operDetail = "三方接口准备阶段完成", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口准备阶段完成")
    @GetMapping ("/thirdInterfacePrepareFinish")
    public Result thirdInterfacePrepareFinish(@RequestParam ("milestoneInfoId") Long milestoneInfoId) {
        return projThirdInterfaceService.thirdInterfacePrepareFinish(milestoneInfoId);
    }

    /**
     * 三方接口老系统的历史数据处理
     *
     * @return
     */
    @Log (operName = "三方接口老系统的历史数据处理", operDetail = "三方接口老系统的历史数据处理", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口老系统的历史数据处理")

    @GetMapping ("/imspInterfaceForNew")
    public Result imspInterfaceForNew(@RequestParam ("projectInfoId") Long projectInfoId,
                                      @RequestParam ("thirdInterfaceId") Long thirdInterfaceId) {
        return projThirdInterfaceService.imspInterfaceForNew(projectInfoId, thirdInterfaceId);
    }

    /**
     * 老系统接口文件上传补偿接口
     *
     * @return
     */
    @Log (operName = "老系统接口文件上传补偿接口", operDetail = "老系统接口文件上传补偿接口", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老系统接口文件上传补偿接口")
    @GetMapping ("/thirdInterfaceFileUpload")
    public Result thirdInterfaceFileUpload(@RequestParam ("thirdInterfaceId") Long thirdInterfaceId) {
        return projThirdInterfaceService.thirdInterfaceFileUpload(thirdInterfaceId);
    }

    /**
     * 查询三方接口操作日志信息
     *
     * @return
     */
    @Log (operName = "查询三方接口操作日志信息", operDetail = "查询三方接口操作日志信息", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询三方接口操作日志信息")
    @GetMapping ("/selectInterfaceRecordLog")
    public Result<List<ProjInterfaceRecordLogVo>> selectInterfaceRecordLog(Long thirdInterfaceId) {
        return projThirdInterfaceService.selectInterfaceRecordLog(thirdInterfaceId);
    }

    /**
     * 三方接口部署授权申请
     *
     * @return
     */
    @Log (operName = "三方接口部署授权申请", operDetail = "三方接口部署授权申请", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口部署授权申请")
    @PostMapping ("/saveDeployDeviceInfo")
    public Result saveDeployDeviceInfo(@RequestBody SaveDeployDeviceInfoDTO dto) {
        return projThirdInterfaceService.saveDeployDeviceInfo(dto);
    }

    /**
     * 三方接口申请国密授权
     *
     * @return
     */
    @Log (operName = "三方接口申请国密授权", operDetail = "三方接口申请国密授权", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口申请国密授权")
    @GetMapping ("/createAppAuthorization")
    public Result createAppAuthorization(CreateAppAuthorizationDTO dto) {
        return projThirdInterfaceService.createAppAuthorization(dto);
    }

    /**
     * 修改数据集
     *
     * @return
     */
    @Log (operName = "修改数据集", operDetail = "修改数据集", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "修改数据集")
    @PostMapping ("/updateDataSet")
    public Result updateDataSet(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.updateDataSet(dto);
    }

    /**
     * 查询部署申请信息
     *
     * @return
     */
    @Log (operName = "查询部署申请信息", operDetail = "查询部署申请信息", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询部署申请信息")
    @PostMapping ("/getDeployDeviceInfo")
    public Result<SaveDeployDeviceInfoDTO> getDeployDeviceInfo(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.getDeployDeviceInfo(dto.getThirdInterfaceId());
    }

    /**
     * 三方接口下载SDK
     *
     * @return
     */
    @Log (operName = "三方接口下载SDK", operDetail = "三方接口下载SDK", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口下载SDK")
    @PostMapping ("/downloadSDK")
    public Result<String> downloadSDK(@RequestBody SysFile file) {
        return projThirdInterfaceService.downloadSDK(file.getFileCode());
    }

    /**
     * 操作手册
     *
     * @return
     */
    @Log (operName = "操作手册", operDetail = "操作手册", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "操作手册")
    @PostMapping ("/operateDoc")
    public Result<String> operateDoc(@RequestBody SysFile file) {
        return projThirdInterfaceService.operateDoc(file.getFileCode());
    }


    /**
     * 选中接口批量上传三方合同后更新数据
     *
     * @param req
     * @return
     */
    @Log (operName = "选中接口批量上传三方合同后更新数据", operDetail = "选中接口批量上传三方合同后更新数据",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "选中接口批量上传三方合同后更新数据")
    @PostMapping ("/updateContractFiles")
    public Result updateContractFiles(@RequestBody @Valid UpdateAuthFileReq req) {
        return projThirdInterfaceService.updateContractFiles(req);
    }

    /**
     * 验证三方接口
     *
     * @param dto
     * @return
     */
    @Log (operName = "验证三方接口", operDetail = "验证三方接口",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "验证三方接口")
    @PostMapping ("/verifyInterface")
    public Result verifyInterface(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.verifyInterface(dto);
    }

    /**
     * 获取三方接口授权服务器设备信息
     * @param dto
     * @return
     */
    @Log (operName = "获取三方接口授权服务器设备信息", operDetail = "获取三方接口授权服务器设备信息",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "获取三方接口授权服务器设备信息")
    @PostMapping ("/findDeployDeviceInfo")
    public Result<List<DeployDeviceInfoVO>> findDeployDeviceInfo(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.findDeployDeviceInfo(dto);
    }

    /**
     * 查询三方接口在运维平台是否是后端运维的客户
     *
     * @param dto
     * @return 1 属于后端运维 0 不属于后端运维
     */
    @Log (operName = "查询三方接口在运维平台是否是后端运维的客户", operDetail = "查询三方接口在运维平台是否是后端运维的客户",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询三方接口在运维平台是否是后端运维的客户")
    @PostMapping ("/selectInterfaceInOtherSystemInfo")
    public Result selectInterfaceInOtherSystemInfo(@RequestBody ProjThirdInterfaceDTO dto) {
        return projThirdInterfaceService.selectInterfaceInOtherSystemInfo(dto);
    }

    /**
     * 获取分配三方接口负责人时查询条件的部门下拉框
     *
     * @param param 项目ID
     * @return 分配三方接口负责人时查询条件的部门下拉框
     */
    @Log(operName = "获取分配三方接口负责人时查询条件的部门下拉框", operDetail = "获取分配三方接口负责人时查询条件的部门下拉框", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取分配三方接口负责人时查询条件的部门下拉框")
    @PostMapping(value = "/thirdInterfaceMemberDept")
    Result<ThirdInterfaceMemberDept> thirdInterfaceMemberDept(@RequestBody ProjectInfoId param) {
        return Result.success(projThirdInterfaceService.thirdInterfaceMemberDept(param));
    }

    /**
     * 获取分配三方接口时的负责人
     *
     * @param dto 参数
     * @return 分配三方接口时的负责人
     */
    @Log(operName = "获取分配三方接口时的负责人", operDetail = "获取分配三方接口时的负责人", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取分配三方接口时的负责人")
    @PostMapping(value = "/thirdInterfaceMember")
    Result<PageInfo<SysUserVO>> thirdInterfaceMember(@RequestBody InterfaceMemberParamDTO dto) {
        return projThirdInterfaceService.thirdInterfaceMember(dto);
    }

    /**
     * 分页查询三方接口列表信息给后端运维使用
     *
     * @param dto
     * @return
     */
    @Log (operName = "分页查询三方接口列表信息给后端运维使用", operDetail = "分页查询三方接口列表信息给后端运维使用",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "分页查询三方接口列表信息给后端运维使用")
    @PostMapping ("/selectByPageToMake")
    public Result<PageInfo<ProjThirdInterfaceVO>> selectByPageToMake(@RequestBody ProjThirdInterfacePageDTO dto) {
        return projThirdInterfaceService.selectByPageToMake(dto);
    }
    @Log (operName = "查询接口分类", operDetail = "查询接口分类",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询接口分类")
    @PostMapping ("/selectInterfaceCategory")
    public Result<List<BaseIdCodeNameResp>> selectInterfaceCategory() {
        return projThirdInterfaceService.selectInterfaceCategory();
    }


}
