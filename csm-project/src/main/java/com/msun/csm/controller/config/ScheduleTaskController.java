package com.msun.csm.controller.config;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ScheduleTaskDTO;
import com.msun.csm.service.config.ScheduleTaskService;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 定时任务Controller
 *
 * @Author: duxu
 * @Date: 2024/06/25/14:13
 */
@Slf4j
@ApiOperation ("定时任务Controller")
@RestController
@RequestMapping ("/scheduleTask")
public class ScheduleTaskController {

    @Resource
    private ScheduleTaskService scheduleTaskService;


    /**
     * 查询定时任务数据
     *
     * @param dto
     * @return
     */
    @ApiOperation ("查询定时任务数据")
    @PostMapping ("/selectScheduleTask")
    Result selectScheduleTask(@RequestBody ScheduleTaskDTO dto) {
        return scheduleTaskService.selectScheduleTask(dto);
    }

    /**
     * 修改定时任务数据
     *
     * @param dto
     * @return
     */
    @ApiOperation ("修改定时任务数据")
    @PostMapping ("/updateScheduleTask")
    Result updateScheduleTask(@RequestBody ScheduleTaskDTO dto) {
        return scheduleTaskService.updateScheduleTask(dto);
    }

    /**
     * 手动触发定时任务
     *
     * @param dto
     * @return
     */
    @ApiOperation ("手动触发定时任务")
    @PostMapping ("/triggerScheduleTask")
    Result triggerScheduleTask(@RequestBody ScheduleTaskDTO dto) {
        return scheduleTaskService.triggerScheduleTask(dto);
    }

}
