package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementUnCheckDTO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementUnCheckRelativeVO;
import com.msun.csm.service.proj.ProjProjectSettlementUnCheckService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-28 04:46:47
 */
@Slf4j
@RestController
@RequestMapping("/projProjectSettlementUnCheck")
@Api(tags = "入驻申请待审核记录表相关接口")
public class ProjProjectSettlementUnCheckController {

    @Resource
    private ProjProjectSettlementUnCheckService settlementUnCheckService;

    @Log(operName = "获取待审核列表", operDetail = "获取待审核列表", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取待审核列表")
    @PostMapping("findSettlementUnCheckData")
    public Result<PageInfo<ProjProjectSettlementUnCheckRelativeVO>> findSettlementUnCheckData(@RequestBody ProjProjectSettlementUnCheckDTO unCheckDTO) {
        return settlementUnCheckService.findSettlementUnCheckData(unCheckDTO);
    }

    @Log(
            operName = "待审核列表-导出", operDetail = "待审核列表-导出", operLogType = Log.LogOperType.EXP,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "待审核列表-导出"
    )
    @ApiOperation("待审核列表-导出")
    @PostMapping("/settlementUnCheckExportExcel")
    void settlementUnCheckExportExcel(@RequestBody ProjProjectSettlementUnCheckDTO unCheckDTO,
                                      HttpServletResponse response) {
        settlementUnCheckService.settlementUnCheckExportExcel(unCheckDTO, response);
    }
}
