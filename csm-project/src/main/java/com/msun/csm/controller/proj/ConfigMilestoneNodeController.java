package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ConfigMilestoneNodeDTO;
import com.msun.csm.model.dto.ConfigMilestoneNodeSelectDTO;
import com.msun.csm.model.vo.ConfigMilestoneNodeVO;
import com.msun.csm.service.config.ConfigMilestoneNodeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/06/16:05
 */
@Slf4j
@Api (tags = "里程碑配置管理Controller")
@RestController
@RequestMapping ("/configMilestoneNode")
public class ConfigMilestoneNodeController {

    @Resource
    private ConfigMilestoneNodeService configMilestoneNodeService;

    /**
     * 里程碑配置管理-查询项目阶段下拉
     */
    @ApiOperation ("里程碑配置管理-查询项目阶段下拉")
    @Log (operName = "里程碑配置管理-查询项目阶段下拉", operDetail = "里程碑配置管理-查询项目阶段下拉",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑配置管理-查询项目阶段下拉")
    @PostMapping (value = "/selectDictProjectStageData")
    Result<List<BaseIdNameResp>> selectDictProjectStageData() {
        return configMilestoneNodeService.selectDictProjectStageData();
    }

    /**
     * 里程碑配置管理-查询里程碑节点下拉
     */
    @ApiOperation ("里程碑配置管理-查询里程碑节点下拉")
    @Log (operName = "里程碑配置管理-查询里程碑节点下拉", operDetail = "里程碑配置管理-查询里程碑节点下拉",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑配置管理-查询里程碑节点下拉")
    @PostMapping (value = "/selectDictMilestoneNodeData")
    Result<List<BaseIdNameResp>> selectDictMilestoneNodeData() {
        return configMilestoneNodeService.selectDictMilestoneNodeData();
    }

    /**
     * 里程碑配置管理-里程碑配置查询列表数据
     */
    @ApiOperation ("里程碑配置管理-里程碑配置查询列表数据")
    @Log (operName = "里程碑配置管理-里程碑配置查询列表数据", operDetail = "里程碑配置管理-里程碑配置查询列表数据",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑配置管理-里程碑配置查询列表数据")
    @PostMapping (value = "/selectMilestoneNodeList")
    Result<List<ConfigMilestoneNodeVO>> selectMilestoneNodeList(@RequestBody ConfigMilestoneNodeSelectDTO dto) {
        return configMilestoneNodeService.selectMilestoneNodeList(dto);
    }

    /**
     * 里程碑配置管理-修改里程碑节点数据
     */
    @ApiOperation ("里程碑配置管理-修改里程碑节点数据")
    @Log (operName = "里程碑配置管理-查询项目阶段下拉", operDetail = "里程碑配置管理-修改里程碑节点数据",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑配置管理-修改里程碑节点数据")
    @PostMapping (value = "/updateMilestoneNodeData")
    Result updateMilestoneNodeData(@RequestBody ConfigMilestoneNodeDTO dto) {
        return configMilestoneNodeService.updateMilestoneNodeData(dto);
    }

    /**
     * 里程碑配置管理-删除里程碑节点数据
     */
    @ApiOperation ("里程碑配置管理-删除里程碑节点数据")
    @Log (operName = "里程碑配置管理-查询项目阶段下拉", operDetail = "里程碑配置管理-删除里程碑节点数据",
            operLogType = Log.LogOperType.DOWNLOAD, intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑配置管理-删除里程碑节点数据")
    @PostMapping (value = "/deleteMilestoneNodeData")
    Result deleteMilestoneNodeData(@RequestBody ConfigMilestoneNodeSelectDTO dto) {
        return configMilestoneNodeService.deleteMilestoneNodeData(dto);
    }
}
