package com.msun.csm.controller.dict;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.config.ConfigProjectSchema;
import com.msun.csm.dao.entity.dict.DictBizType;
import com.msun.csm.dao.entity.dict.DictDeliveryOrderType;
import com.msun.csm.dao.entity.dict.DictProductType;
import com.msun.csm.dao.entity.dict.DictProjectPlanItem;
import com.msun.csm.dao.entity.dict.DictProjectPlanItemTask;
import com.msun.csm.dao.entity.dict.DictProjectPlanStage;
import com.msun.csm.dao.entity.dict.DictProjectSchema;
import com.msun.csm.dao.entity.dict.DictProjectSchemaVsPlanItem;
import com.msun.csm.dao.mapper.config.ConfigProjectSchemaMapper;
import com.msun.csm.dao.mapper.dict.DictBizTypeMapper;
import com.msun.csm.dao.mapper.dict.DictDeliveryOrderTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProductTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemTaskMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper;
import com.msun.csm.dao.mapper.dict.DictProjectSchemaMapper;
import com.msun.csm.dao.mapper.dict.DictProjectSchemaVsPlanItemMapper;
import com.msun.csm.model.SelectOption;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.util.PageHelperUtil;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/30 14:32
 */
@Api(tags = "项目模式")
@RestController
@RequestMapping("/dictProjectSchema")
public class DictProjectSchemaController {
    @Autowired
    private ConfigProjectSchemaMapper configProjectSchemaMapper;
    @Autowired
    private DictDeliveryOrderTypeMapper dictDeliveryOrderTypeMapper;
    @Autowired
    private DictProductTypeMapper dictProductTypeMapper;
    @Autowired
    private DictProjectPlanItemMapper dictProjectPlanItemMapper;
    @Autowired
    private DictProjectPlanItemTaskMapper dictProjectPlanItemTaskMapper;
    @Autowired
    private DictProjectPlanStageMapper dictProjectPlanStageMapper;
    @Autowired
    private DictProjectSchemaMapper dictProjectSchemaMapper;
    @Autowired
    private DictProjectSchemaVsPlanItemMapper dictProjectSchemaVsPlanItemMapper;
    @Autowired
    private UserHelper userHelper;
    @Autowired
    private DictBizTypeMapper dictBizTypeMapper;

    @Autowired
    private CommonService commonService;

    //项目计划阶段
    @ApiOperation("编辑项目计划阶段")
    @PostMapping(value = "/editDictProjectPlanStage")
    public Result<?> editDictProjectPlanStage(@RequestBody DictProjectPlanStage args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getProjectPlanStageId() != null && args.getProjectPlanStageId() > 0) {
            DictProjectPlanStage one = new LambdaQueryChainWrapper<>(dictProjectPlanStageMapper)
                    .eq(DictProjectPlanStage::getProjectPlanStageCode, args.getProjectPlanStageCode())
                    .eq(DictProjectPlanStage::getIsDeleted, 0)
                    .last("limit 1")
                    .one();
            if (one != null && !Objects.equals(args.getProjectPlanStageId(), one.getProjectPlanStageId())) {
                throw new CustomException("项目计划阶段编码重复");
            }
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectPlanStageMapper.updateById(args);
        } else {
            args.setProjectPlanStageId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectPlanStageMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除项目计划阶段")
    @DeleteMapping(value = "/delDictProjectPlanStage/{id}")
    public Result<?> delDictProjectPlanStage(@PathVariable("id") Long id) {
        //判断是否有引用关系如果有引用关系不允许删除
        if (dictProjectSchemaVsPlanItemMapper.checkProjectPlanStageLinked(id) > 0) {
            throw new CustomException("该项目计划阶段有被引用无法删除");
        }
        new LambdaUpdateChainWrapper<>(dictProjectPlanStageMapper)
                .eq(DictProjectPlanStage::getProjectPlanStageId, id)
                .set(DictProjectPlanStage::getIsDeleted, 1)
                .set(DictProjectPlanStage::getUpdateTime, new Date())
                .set(DictProjectPlanStage::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取项目计划阶段")
    @GetMapping(value = "/getDictProjectPlanStage")
    public Result<PageInfo<DictProjectPlanStage>> getDictProjectPlanStage(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictProjectPlanStage> list = new LambdaQueryChainWrapper<>(dictProjectPlanStageMapper)
                    .eq(DictProjectPlanStage::getIsDeleted, 0)
                    .and(StrUtil.isNotBlank(keyword), i -> i.like(DictProjectPlanStage::getProjectPlanStageName, keyword).or().like(DictProjectPlanStage::getProjectPlanStageCode, keyword))
                    .orderByAsc(DictProjectPlanStage::getSort)
                    .list();
            List<Long> uids = new ArrayList<>();
            for (DictProjectPlanStage item : list) {
                uids.add(item.getCreaterId());
                uids.add(item.getUpdaterId());
            }
            Map<Long, String> userMap = commonService.getUserNameMap(uids);
            for (DictProjectPlanStage item : list) {
                item.setCreateName(userMap.get(item.getCreaterId()));
                item.setUpdateName(userMap.get(item.getUpdaterId()));
            }
            return Result.success(new PageInfo<>(list));
        });
    }

    @ApiOperation("获取项目计划阶段选项")
    @GetMapping(value = "/getDictProjectPlanStageOptions")
    public Result<?> getDictProjectPlanStageOptions() {
        return Result.success(new LambdaQueryChainWrapper<>(dictProjectPlanStageMapper)
                .eq(DictProjectPlanStage::getIsDeleted, 0)
                .orderByAsc(DictProjectPlanStage::getSort)
                .list().stream()
                .map(i -> new SelectOption(i.getProjectPlanStageName(), i.getProjectPlanStageCode()))
                .collect(Collectors.toList()));
    }

    //项目计划节点

    @ApiOperation("编辑项目计划节点")
    @PostMapping(value = "/editDictProjectPlanItem")
    public Result<?> editDictProjectPlanItem(@RequestBody DictProjectPlanItem args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getProjectPlanItemId() != null && args.getProjectPlanItemId() > 0) {
            DictProjectPlanItem one = new LambdaQueryChainWrapper<>(dictProjectPlanItemMapper)
                    .eq(DictProjectPlanItem::getProjectPlanItemCode, args.getProjectPlanItemCode())
                    .eq(DictProjectPlanItem::getIsDeleted, 0)
                    .one();
            if (one != null && !Objects.equals(args.getProjectPlanItemId(), one.getProjectPlanItemId())) {
                throw new CustomException("项目计划节点编码重复");
            }
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectPlanItemMapper.updateById(args);
        } else {
            args.setProjectPlanItemId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectPlanItemMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除项目计划节点")
    @DeleteMapping(value = "/delDictProjectPlanItem/{id}")
    public Result<?> delDictProjectPlanItem(@PathVariable("id") Long id) {
        if (dictProjectSchemaVsPlanItemMapper.checkProjectPlanItemLinked(id) > 0) {
            throw new CustomException("该项目计划节点有被引用无法删除");
        }
        new LambdaUpdateChainWrapper<>(dictProjectPlanItemMapper)
                .eq(DictProjectPlanItem::getProjectPlanItemId, id)
                .set(DictProjectPlanItem::getIsDeleted, 1)
                .set(DictProjectPlanItem::getUpdateTime, new Date())
                .set(DictProjectPlanItem::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取项目计划节点")
    @GetMapping(value = "/getDictProjectPlanItem")
    public Result<PageInfo<DictProjectPlanItem>> getDictProjectPlanItem(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictProjectPlanItem> list = new LambdaQueryChainWrapper<>(dictProjectPlanItemMapper)
                    .eq(DictProjectPlanItem::getIsDeleted, 0)
                    .and(StrUtil.isNotBlank(keyword), i -> i.like(DictProjectPlanItem::getProjectPlanItemName, keyword).or().like(DictProjectPlanItem::getProjectPlanItemCode, keyword))
                    .orderByAsc(DictProjectPlanItem::getSort)
                    .list();
            List<Long> uids = new ArrayList<>();
            for (DictProjectPlanItem item : list) {
                uids.add(item.getCreaterId());
                uids.add(item.getUpdaterId());
            }
            Map<Long, String> userMap = commonService.getUserNameMap(uids);
            for (DictProjectPlanItem item : list) {
                item.setCreateName(userMap.get(item.getCreaterId()));
                item.setUpdateName(userMap.get(item.getUpdaterId()));
            }
            return Result.success(new PageInfo<>(list));
        });
    }

    @ApiOperation("获取项目计划节点选项")
    @GetMapping(value = "/getDictProjectPlanItemOptions")
    public Result<?> getDictProjectPlanItemOptions() {
        return Result.success(new LambdaQueryChainWrapper<>(dictProjectPlanItemMapper)
                .eq(DictProjectPlanItem::getIsDeleted, 0)
                .orderByAsc(DictProjectPlanItem::getSort)
                .list().stream()
                .map(i -> new SelectOption(i.getProjectPlanItemName(), i.getProjectPlanItemCode()))
                .collect(Collectors.toList()));
    }

    //项目计划节点任务
    @ApiOperation("编辑项目计划节点任务")
    @PostMapping(value = "/editDictProjectPlanItemTask")
    public Result<?> editDictProjectPlanItemTask(@RequestBody DictProjectPlanItemTask args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        DictProjectPlanItemTask one = new LambdaQueryChainWrapper<>(dictProjectPlanItemTaskMapper)
                .eq(DictProjectPlanItemTask::getProjectPlanItemCode, args.getProjectPlanItemCode())
                .eq(DictProjectPlanItemTask::getIsDeleted, 0)
                .one();
        if (one == null) {
            one = new DictProjectPlanItemTask();
            BeanUtils.copyProperties(args, one);
            one.setId(IdGenerator.ins().generator());
            one.setCreateTime(new Date());
            one.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectPlanItemTaskMapper.insert(one);
        } else {
            Long id = one.getId();
            BeanUtils.copyProperties(args, one);
            one.setId(id);
            one.setUpdateTime(new Date());
            one.setUpdaterId(currentUser.getSysUserId());
            dictProjectPlanItemTaskMapper.updateById(one);
        }
        return Result.success();
    }

    @ApiOperation("获取项目计划节点任务")
    @GetMapping(value = "/getDictProjectPlanItemTask")
    public Result<?> getDictProjectPlanItemTask(@RequestParam("projectPlanItemCode") String projectPlanItemCode) {
        DictProjectPlanItemTask task = new LambdaQueryChainWrapper<>(dictProjectPlanItemTaskMapper)
                .eq(DictProjectPlanItemTask::getProjectPlanItemCode, projectPlanItemCode)
                .eq(DictProjectPlanItemTask::getIsDeleted, 0)
                .one();
        if (task == null) {
            task = new DictProjectPlanItemTask();
            task.setProjectPlanItemCode(projectPlanItemCode);
            task.setTaskDesc("");
        }
        return Result.success(task);
    }

    //业务类型

    @ApiOperation("编辑业务类型")
    @PostMapping(value = "/editDictBizType")
    public Result<?> editDictBizType(@RequestBody DictBizType args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getId() != null && args.getId() > 0) {
            DictBizType one = new LambdaQueryChainWrapper<>(dictBizTypeMapper)
                    .eq(DictBizType::getBizTypeCode, args.getBizTypeCode())
                    .eq(DictBizType::getIsDeleted, 0)
                    .one();
            if (one != null && !Objects.equals(args.getId(), one.getId())) {
                throw new CustomException("业务类型编码重复");
            }
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictBizTypeMapper.updateById(args);
        } else {
            args.setId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictBizTypeMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除业务类型")
    @DeleteMapping(value = "/delDictBizType/{id}")
    public Result<?> delDictBizType(@PathVariable("id") Long id) {
        new LambdaUpdateChainWrapper<>(dictBizTypeMapper)
                .eq(DictBizType::getId, id)
                .set(DictBizType::getIsDeleted, 1)
                .set(DictBizType::getUpdateTime, new Date())
                .set(DictBizType::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取业务类型")
    @GetMapping(value = "/getDictBizType")
    public Result<?> getDictBizType(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictBizType> list = new LambdaQueryChainWrapper<>(dictBizTypeMapper)
                    .eq(DictBizType::getIsDeleted, 0)
                    .and(StrUtil.isNotBlank(keyword), i -> i.like(DictBizType::getBizTypeName, keyword).or().like(DictBizType::getBizTypeCode, keyword))
                    .orderByAsc(DictBizType::getBizTypeName)
                    .list();
            List<Long> uids = new ArrayList<>();
            for (DictBizType item : list) {
                uids.add(item.getCreaterId());
                uids.add(item.getUpdaterId());
            }
            Map<Long, String> userMap = commonService.getUserNameMap(uids);
            for (DictBizType item : list) {
                item.setCreateName(userMap.get(item.getCreaterId()));
                item.setUpdateName(userMap.get(item.getUpdaterId()));
            }
            return Result.success(new PageInfo<>(list));
        });
    }

    @ApiOperation("获取业务类型选项")
    @GetMapping(value = "/getDictBizTypeOptions")
    public Result<?> getDictBizTypeOptions() {
        return Result.success(new LambdaQueryChainWrapper<>(dictBizTypeMapper)
                .eq(DictBizType::getIsDeleted, 0)
                .orderByAsc(DictBizType::getBizTypeName)
                .list().stream()
                .map(i -> new SelectOption(i.getBizTypeName(), i.getBizTypeCode()))
                .collect(Collectors.toList()));
    }

    //产品类型

    @ApiOperation("编辑产品类型")
    @PostMapping(value = "/editDictProductType")
    public Result<?> editDictProductType(@RequestBody DictProductType args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getId() != null && args.getId() > 0) {
            DictProductType one = new LambdaQueryChainWrapper<>(dictProductTypeMapper)
                    .eq(DictProductType::getProductTypeCode, args.getProductTypeCode())
                    .eq(DictProductType::getIsDeleted, 0)
                    .one();
            if (one != null && !Objects.equals(args.getId(), one.getId())) {
                throw new CustomException("产品类型编码重复");
            }
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProductTypeMapper.updateById(args);
        } else {
            args.setId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProductTypeMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除产品类型")
    @DeleteMapping(value = "/delDictProductType/{id}")
    public Result<?> delDictProductType(@PathVariable("id") Long id) {
        new LambdaUpdateChainWrapper<>(dictProductTypeMapper)
                .eq(DictProductType::getId, id)
                .set(DictProductType::getIsDeleted, 1)
                .set(DictProductType::getUpdateTime, new Date())
                .set(DictProductType::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取产品类型")
    @GetMapping(value = "/getDictProductType")
    public Result<?> getDictProductType(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictProductType> list = new LambdaQueryChainWrapper<>(dictProductTypeMapper)
                    .eq(DictProductType::getIsDeleted, 0)
                    .and(StrUtil.isNotBlank(keyword), i -> i.like(DictProductType::getProductTypeName, keyword).or().like(DictProductType::getProductTypeCode, keyword))
                    .orderByAsc(DictProductType::getProductTypeName)
                    .list();
            List<Long> uids = new ArrayList<>();
            for (DictProductType item : list) {
                uids.add(item.getCreaterId());
                uids.add(item.getUpdaterId());
            }
            Map<Long, String> userMap = commonService.getUserNameMap(uids);
            for (DictProductType item : list) {
                item.setCreateName(userMap.get(item.getCreaterId()));
                item.setUpdateName(userMap.get(item.getUpdaterId()));
            }
            return Result.success(new PageInfo<>(list));
        });
    }

    @ApiOperation("获取产品类型选项")
    @GetMapping(value = "/getDictProductTypeOptions")
    public Result<?> getDictProductTypeOptions() {
        return Result.success(new LambdaQueryChainWrapper<>(dictProductTypeMapper)
                .eq(DictProductType::getIsDeleted, 0)
                .orderByAsc(DictProductType::getProductTypeName)
                .list().stream()
                .map(i -> new SelectOption(i.getProductTypeName(), i.getProductTypeCode()))
                .collect(Collectors.toList()));
    }

    //工单类型
    @ApiOperation("编辑工单类型")
    @PostMapping(value = "/editDictDeliveryOrderType")
    public Result<?> editDictDeliveryOrderType(@RequestBody DictDeliveryOrderType args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getId() != null && args.getId() > 0) {
            DictDeliveryOrderType one = new LambdaQueryChainWrapper<>(dictDeliveryOrderTypeMapper)
                    .eq(DictDeliveryOrderType::getDeliveryOrderTypeCode, args.getDeliveryOrderTypeCode())
                    .eq(DictDeliveryOrderType::getIsDeleted, 0)
                    .one();
            if (one != null && !Objects.equals(args.getId(), one.getId())) {
                throw new CustomException("工单类型编码重复");
            }
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictDeliveryOrderTypeMapper.updateById(args);
        } else {
            args.setId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictDeliveryOrderTypeMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除工单类型")
    @DeleteMapping(value = "/delDictDeliveryOrderType/{id}")
    public Result<?> delDictDeliveryOrderType(@PathVariable("id") Long id) {
        new LambdaUpdateChainWrapper<>(dictDeliveryOrderTypeMapper)
                .eq(DictDeliveryOrderType::getId, id)
                .set(DictDeliveryOrderType::getIsDeleted, 1)
                .set(DictDeliveryOrderType::getUpdateTime, new Date())
                .set(DictDeliveryOrderType::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取工单类型")
    @GetMapping(value = "/getDictDeliveryOrderType")
    public Result<?> getDictDeliveryOrderType(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictDeliveryOrderType> list = new LambdaQueryChainWrapper<>(dictDeliveryOrderTypeMapper)
                    .eq(DictDeliveryOrderType::getIsDeleted, 0)
                    .and(StrUtil.isNotBlank(keyword), i -> i.like(DictDeliveryOrderType::getDeliveryOrderTypeCode, keyword).or().like(DictDeliveryOrderType::getDeliveryOrderTypeName, keyword))
                    .orderByAsc(DictDeliveryOrderType::getDeliveryOrderTypeName)
                    .list();
            List<Long> uids = new ArrayList<>();
            for (DictDeliveryOrderType item : list) {
                uids.add(item.getCreaterId());
                uids.add(item.getUpdaterId());
            }
            Map<Long, String> userMap = commonService.getUserNameMap(uids);
            for (DictDeliveryOrderType item : list) {
                item.setCreateName(userMap.get(item.getCreaterId()));
                item.setUpdateName(userMap.get(item.getUpdaterId()));
            }
            return Result.success(new PageInfo<>(list));
        });
    }

    @ApiOperation("获取工单类型选项")
    @GetMapping(value = "/getDictDeliveryOrderTypeOptions")
    public Result<?> getDictDeliveryOrderTypeOptions() {
        return Result.success(new LambdaQueryChainWrapper<>(dictDeliveryOrderTypeMapper)
                .eq(DictDeliveryOrderType::getIsDeleted, 0)
                .orderByAsc(DictDeliveryOrderType::getDeliveryOrderTypeName)
                .list().stream()
                .map(i -> new SelectOption(i.getDeliveryOrderTypeName(), i.getDeliveryOrderTypeCode()))
                .collect(Collectors.toList()));
    }


    //项目模式
    @ApiOperation("编辑项目模式")
    @PostMapping(value = "/editDictProjectSchema")
    public Result<?> editDictProjectSchema(@RequestBody DictProjectSchema args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getId() != null && args.getId() > 0) {
            DictProjectSchema one = new LambdaQueryChainWrapper<>(dictProjectSchemaMapper)
                    .eq(DictProjectSchema::getSchemaCode, args.getSchemaCode())
                    .eq(DictProjectSchema::getIsDeleted, 0)
                    .one();
            if (one != null && !Objects.equals(args.getId(), one.getId())) {
                throw new CustomException("工单类型编码重复");
            }
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectSchemaMapper.updateById(args);
        } else {
            args.setId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectSchemaMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除项目模式")
    @DeleteMapping(value = "/delDictProjectSchema/{id}")
    public Result<?> delDictProjectSchema(@PathVariable("id") Long id) {
        if (dictProjectSchemaVsPlanItemMapper.checkProjectSchemaLinked(id) > 0) {
            throw new CustomException("该项目模式有被引用无法删除");
        }
        new LambdaUpdateChainWrapper<>(dictProjectSchemaMapper)
                .eq(DictProjectSchema::getId, id)
                .set(DictProjectSchema::getIsDeleted, 1)
                .set(DictProjectSchema::getUpdateTime, new Date())
                .set(DictProjectSchema::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取项目模式")
    @GetMapping(value = "/getDictProjectSchema")
    public Result<?> getDictProjectSchema(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictProjectSchema> list = new LambdaQueryChainWrapper<>(dictProjectSchemaMapper)
                    .eq(DictProjectSchema::getIsDeleted, 0)
                    .and(StrUtil.isNotBlank(keyword), i -> i.like(DictProjectSchema::getSchemaCode, keyword).or().like(DictProjectSchema::getSchemaName, keyword))
                    .orderByAsc(DictProjectSchema::getSchemaName)
                    .list();
            List<Long> uids = new ArrayList<>();
            for (DictProjectSchema item : list) {
                uids.add(item.getCreaterId());
                uids.add(item.getUpdaterId());
            }
            Map<Long, String> userMap = commonService.getUserNameMap(uids);
            for (DictProjectSchema item : list) {
                item.setCreateName(userMap.get(item.getCreaterId()));
                item.setUpdateName(userMap.get(item.getUpdaterId()));
            }
            return Result.success(new PageInfo<>(list));
        });
    }

    @ApiOperation("获取项目模式选项")
    @GetMapping(value = "/getDictProjectSchemaOptions")
    public Result<?> getDictProjectSchemaOptions() {
        return Result.success(new LambdaQueryChainWrapper<>(dictProjectSchemaMapper)
                .eq(DictProjectSchema::getIsDeleted, 0)
                .orderByAsc(DictProjectSchema::getSchemaName)
                .list().stream()
                .map(i -> new SelectOption(i.getSchemaName(), i.getSchemaCode()))
                .collect(Collectors.toList()));
    }

    //项目模式关联项目计划
    @ApiOperation("编辑项目模式关联项目计划")
    @PostMapping(value = "/editDictProjectSchemaVsPlanItem")
    public Result<?> editDictProjectSchemaVsPlanItem(@RequestBody DictProjectSchemaVsPlanItem args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getId() != null && args.getId() > 0) {
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectSchemaVsPlanItemMapper.updateById(args);
        } else {
            args.setId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            dictProjectSchemaVsPlanItemMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除项目模式关联项目计划")
    @DeleteMapping(value = "/delDictProjectSchemaVsPlanItem/{id}")
    public Result<?> delDictProjectSchemaVsPlanItem(@PathVariable("id") Long id) {
        new LambdaUpdateChainWrapper<>(dictProjectSchemaVsPlanItemMapper)
                .eq(DictProjectSchemaVsPlanItem::getId, id)
                .set(DictProjectSchemaVsPlanItem::getIsDeleted, 1)
                .set(DictProjectSchemaVsPlanItem::getUpdateTime, new Date())
                .set(DictProjectSchemaVsPlanItem::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取项目模式关联项目计划")
    @GetMapping(value = "/getDictProjectSchemaVsPlanItem")
    public Result<?> getDictProjectSchemaVsPlanItem(@RequestParam("projectSchemaCode") String projectSchemaCode, @RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<DictProjectSchemaVsPlanItem> list = dictProjectSchemaVsPlanItemMapper.getList(projectSchemaCode, keyword);
            return Result.success(new PageInfo<>(list));
        });
    }


    //配置项目模式关联产品类型、工单类型、业务场景
    @ApiOperation("编辑项目模式配置")
    @PostMapping(value = "/editConfigProjectSchema")
    public Result<?> editConfigProjectSchema(@RequestBody ConfigProjectSchema args) {
        args.setIsDeleted(0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (args.getId() != null && args.getId() > 0) {
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            configProjectSchemaMapper.updateById(args);
        } else {
            args.setId(IdGenerator.ins().generator());
            args.setCreateTime(new Date());
            args.setCreaterId(currentUser.getSysUserId());
            args.setUpdateTime(new Date());
            args.setUpdaterId(currentUser.getSysUserId());
            configProjectSchemaMapper.insert(args);
        }
        return Result.success();
    }

    @ApiOperation("删除项目模式配置")
    @DeleteMapping(value = "/delConfigProjectSchema/{id}")
    public Result<?> delConfigProjectSchema(@PathVariable("id") Long id) {
        new LambdaUpdateChainWrapper<>(configProjectSchemaMapper)
                .eq(ConfigProjectSchema::getId, id)
                .set(ConfigProjectSchema::getIsDeleted, 1)
                .set(ConfigProjectSchema::getUpdateTime, new Date())
                .set(ConfigProjectSchema::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .update();
        return Result.success();
    }

    @ApiOperation("获取项目模式配置")
    @GetMapping(value = "/getConfigProjectSchema")
    public Result<?> getConfigProjectSchema(@RequestParam(value = "keyword", required = false) String keyword, @RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize) {
        return PageHelperUtil.queryPage(pageNo, pageSize, page -> {
            List<ConfigProjectSchema> list = configProjectSchemaMapper.getList(keyword);
            return Result.success(new PageInfo<>(list));
        });
    }
}
