package com.msun.csm.controller.proj;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.BizDesc;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.model.dto.projreview.ProjPmoReviewQueryDTO;
import com.msun.csm.model.dto.projreview.ProjReviewDTO;
import com.msun.csm.model.dto.projreview.ProjReviewExcludePmoDTO;
import com.msun.csm.model.dto.projreview.ProjReviewLogDTO;
import com.msun.csm.model.dto.projreview.ProjReviewQueryDTO;
import com.msun.csm.model.dto.projreview.ProjReviewUploadDTO;
import com.msun.csm.model.vo.projprojectreview.ProjProjectReviewInfoRelativeVO;
import com.msun.csm.model.vo.projprojectreview.ProjProjectReviewMainVO;
import com.msun.csm.model.vo.projprojectreview.ProjResearchReviewUploadResultVO;
import com.msun.csm.model.vo.projsettlement.ProjReviewLogVO;
import com.msun.csm.service.proj.ProjProjectReviewService;

/**
 * 调研阶段审核
 */
@Api(tags = "调研阶段审核")
@RestController
@RequestMapping("/projectReview")
public class ProjProjectReviewController {

    @Resource
    private ProjProjectReviewService projProjectReviewService;

    @Log(operName = "获取调研内容", operDetail = "获取调研内容（生成项目的调研内容）", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取调研内容")
    @ApiOperation("获取调研内容（生成项目的调研内容）")
    @PostMapping("findProjReviewContent")
    public Result<ProjProjectReviewMainVO> findProjRevtiewContent(@Valid @RequestBody ProjReviewQueryDTO researchReviewDTO) {
        return projProjectReviewService.findProjReviewContent(researchReviewDTO);
    }

    @Log(operName = "PMO审核列表", operDetail = "PMO审核列表", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取调研内容")
    @ApiOperation("PMO审核列表")
    @PostMapping("findPmoReview")
    public Result<PageInfo<ProjProjectReviewInfoRelativeVO>> findPmoReview(@RequestBody ProjPmoReviewQueryDTO pmoReviewQueryDTO) {
        return projProjectReviewService.findPmoReview(pmoReviewQueryDTO);
    }

    @Log(operName = "上传文件", operDetail = "上传文件", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "上传文件")
    @ApiOperation("上传文件")
    @PostMapping("uploadFile")
    public Result<ProjResearchReviewUploadResultVO> uploadFile(ProjReviewUploadDTO reviewUploadDTO) {
        return projProjectReviewService.uploadFile(reviewUploadDTO);
    }

    @Log(operName = "项目经理/PMO提交审核", operDetail = "项目经理提交审核", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目经理提交审核")
    @ApiOperation("项目经理/PMO提交审核")
    @PostMapping("saveReview")
    public Result<ProjResearchReviewUploadResultVO> saveReview(@RequestBody ProjReviewDTO researchReviewDTO) {
        return projProjectReviewService.saveReview(researchReviewDTO);
    }

    @BizDesc(value = "PMO提交审核/编辑计划上线时间", detail = "PMO提交审核时需要弹窗编辑计划上线时间，如果不明确上线时间需要特殊说明", person = "桂杰")
    @Log(operName = "PMO提交审核/编辑计划上线时间", operDetail = "PMO提交审核编辑计划上线时间", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "PMO提交审核编辑计划上线时间")
    @ApiOperation("PMO提交审核/编辑计划上线时间")
    @PostMapping("editPlanOnlineTime")
    public Result<Boolean> editPlanOnlineTime(@RequestBody ProjReviewDTO researchReviewDTO) {
        if (researchReviewDTO.getPlanOnlineTime() == null && StrUtil.isBlank(researchReviewDTO.getWhyUnknownOnlineTime())) {
            throw new CustomException("请选择计划上线时间或者填写不明确上线时间的原因");
        }
        projProjectReviewService.editPlanOnlineTime(researchReviewDTO);
        return Result.success(true);
    }

    @BizDesc(value = "通过项目ID查询项目信息", detail = "当前业务用于移动端查询计划上线时间", person = "桂杰")
    @ApiOperation("通过项目ID查询项目信息")
    @GetMapping("getProjectInfoById")
    public Result<ProjProjectInfo> getProjectInfoById(@RequestParam("projectInfoId") Long projectInfoId) {
        return projProjectReviewService.getProjectInfoById(projectInfoId);
    }

    @Log(operName = "后续模块去pmo节点确认完成", operDetail = "后续模块去pmo节点确认完成", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "后续模块去pmo节点确认完成")
    @ApiOperation("后续模块去pmo节点确认完成")
    @PostMapping("saveReviewExcludePmo")
    public Result<String> saveReviewExcludePmo(@RequestBody ProjReviewExcludePmoDTO reviewExcludePmoDTO) {
        return projProjectReviewService.saveReviewExcludePmo(reviewExcludePmoDTO);
    }

    @Log(operName = "审核日志查询", operDetail = "审核日志查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "审核日志查询")
    @ApiOperation("审核日志查询")
    @PostMapping("findReviewLogs")
    public Result<List<ProjReviewLogVO>> findReviewLogs(@RequestBody ProjReviewLogDTO reviewLogDTO) {
        return projProjectReviewService.findReviewLogs(reviewLogDTO);
    }

    @Log(operName = "APP端提交项目主要阶段审核", operDetail = "APP端提交项目主要阶段审核", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "APP端提交项目主要阶段审核")
    @ApiOperation("APP端提交项目主要阶段审核")
    @PostMapping("saveAppReview")
    public Result<ProjResearchReviewUploadResultVO> saveAppReview(@RequestBody ProjReviewDTO researchReviewDTO) {
        // 设置为PMO审核(现有流程是项目主要阶段审核)
        researchReviewDTO.setReviewNodeType(2);
        // 固定值
        researchReviewDTO.setSceneCode("project_review");
        return projProjectReviewService.saveReview(researchReviewDTO);
    }


    @Log(operName = "项目主要阶段催办", operDetail = "项目主要阶段催办", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目主要阶段催办")
    @ApiOperation("项目主要阶段催办")
    @PostMapping("urgeReview")
    public Result<String> urgeReview(@RequestBody ProjReviewDTO researchReviewDTO) {
        return projProjectReviewService.urgeReview(researchReviewDTO);
    }


}
