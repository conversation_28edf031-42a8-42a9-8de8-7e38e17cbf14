package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.thirdinterface.GetInterfaceApplyDetailReq;
import com.msun.csm.model.vo.ProjInterfaceGroupApplyVo;
import com.msun.csm.service.proj.ProjInterfaceGroupApplyDetailService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/13
 */
@Slf4j
@RestController
@RequestMapping ("/interfaceApply")
@Api (tags = "三方接口分组详情管理")
public class ProjInterfaceGroupApplyDetailController {

    @Resource
    private ProjInterfaceGroupApplyDetailService interfaceGroupApplyDetailService;

    /**
     * 获取接口授权详情
     *
     * @return
     */
    @Log (operName = "获取接口授权详情", operDetail = "获取接口授权详情",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "获取接口授权详情")
    @PostMapping ("/getInterfaceApplyDetail")
    public Result<ProjInterfaceGroupApplyVo> getInterfaceApplyDetail(@RequestBody GetInterfaceApplyDetailReq req) {
        ProjInterfaceGroupApplyVo groupApplyVo = interfaceGroupApplyDetailService.getInterfaceApplyDetails(req);
        return Result.success(groupApplyVo);
    }

}
