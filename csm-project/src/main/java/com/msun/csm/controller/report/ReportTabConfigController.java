package com.msun.csm.controller.report;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.BaseUrlResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.service.report.ReportTabConfigService;

import io.swagger.annotations.Api;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/7
 */
@RestController
@Api(tags = "报表统计tab配置")
@RequestMapping("/reportTabConfig")
public class ReportTabConfigController {

    @Resource
    private ReportTabConfigService reportTabConfigService;

    @GetMapping("/getReportTabConfigByPath")
    public Result getReportTabConfigByPath(String path) {
        List<BaseUrlResp> list = reportTabConfigService.getReportTabConfigByPath(path);
        return Result.success(list);
    }
}
