package com.msun.csm.controller.report;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigExportReq;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigPageReq;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigSaveReq;
import com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigPageResp;
import com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigResp;
import com.msun.csm.service.report.ProjHospitalTerminalConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 客户数据统计表(ReportCustomInfo)控制器
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */

@RestController
@Api(tags = "客户数据统计表")
@RequestMapping("/hospitalTerminalConfig")
public class ProjHospitalTerminalConfigController {
    @Resource
    private ProjHospitalTerminalConfigService projHospitalTerminalConfigService;

    /**
     * 拉取数据
     * 先删除后新增
     *
     * @param dto
     */
    @Log(operName = "拉取数据", operDetail = "拉取数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "拉取数据")
    @ApiOperation("拉取数据")
    @PostMapping(value = "/saveHospitalTerminalConfigData")
    Result saveHospitalTerminalConfigData(@RequestBody ProjHospitalTerminalConfigSaveReq dto) {
        return projHospitalTerminalConfigService.saveHospitalTerminalConfigData(dto);
    }

    /**
     * 医院信息-分页查询
     *
     * @param dto
     */
    @Log(operName = "分页查询", operDetail = "分页查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询")
    @ApiOperation("分页查询")
    @PostMapping(value = "/findDataInfoPage")
    Result<ProjHospitalTerminalConfigPageResp<ProjHospitalTerminalConfigResp>> findDataInfoPage(@RequestBody ProjHospitalTerminalConfigPageReq dto) {
        return projHospitalTerminalConfigService.findDataInfoPage(dto);
    }

    @Log(operName = "列表-导出", operDetail = "列表-导出", operLogType = Log.LogOperType.EXP, intLogType = Log.IntLogType.SELF_SYS, cnName = "列表-导出")
    @ApiOperation("列表-导出")
    @GetMapping("/exportHospitalFile")
    void exportHospitalFile(@Param("projectInfoId") Long projectInfoId, HttpServletResponse response) {
        ProjHospitalTerminalConfigExportReq unCheckDTO = new ProjHospitalTerminalConfigExportReq();
        unCheckDTO.setProjectInfoId(projectInfoId);
        projHospitalTerminalConfigService.exportHospitalFile(unCheckDTO, response);
    }

}
