
package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjMilestoneInfoFileInitializeDTO;
import com.msun.csm.service.proj.ProjMilestoneInfoFileInitializeService;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/17:58
 */
@Slf4j
@Api (tags = "里程碑文件加载初始化接口")
@RestController
@RequestMapping ("/projMilestoneInfoFileInitialize")
public class ProjMilestoneInfoFileInitializeController {

    @Resource
    private ProjMilestoneInfoFileInitializeService proposalInfoFileInitializeService;


    /**
     * 里程碑-入驻阶段-数据导入准备工作-老换新数据准备工作
     */
    @ApiOperation ("里程碑-准备阶段-数据导入工作")
    @PostMapping (value = "/obtainDataPreparation")
    Result dataImportPreparationWork(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.dataImportPreparationWork(dto);
    }

    /**
     * 里程碑-入驻阶段-数据导入准备工作
     */
    @ApiOperation ("里程碑-入驻阶段-数据导入准备工作")
    @PostMapping (value = "/dataPrepare")
    Result dataPrepare(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.dataPrepare(dto);
    }

    /**
     * 准备阶段节点-报表制作节点
     */
    @ApiOperation ("准备阶段节点-表单制作节点")
    @PostMapping (value = "/preparatForm")
    Result preparatForm(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.preparatForm(dto);
    }

    /**
     * 准备阶段节点-报表制作节点
     */
    @ApiOperation ("准备阶段节点-报表制作节点")
    @PostMapping (value = "/preparatReport")
    Result preparatReport(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.preparatReport(dto);
    }

    /**
     * 报表制作节点-区域初始化--替换医院地址信息
     *
     * @param hospitalInfoId
     * @param projectInfoId
     * @return
     */
    @ApiOperation ("报表制作节点-区域初始化--替换医院地址信息")
    @GetMapping (value = "/reportReplaceUrl")
    Result reportReplaceUrl(Long hospitalInfoId, Long projectInfoId) {
        return proposalInfoFileInitializeService.reportReplaceUrl(hospitalInfoId, projectInfoId);
    }

    /**
     * 全员流程模拟-全员流程模拟引导
     */
    @ApiOperation ("全员流程模拟-全员流程模拟引导")
    @PostMapping (value = "/productTestCaseResult")
    Result productTestCaseResult(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.productTestCaseResult(dto);
    }

    /**
     * 里程碑-调研阶段-制定网络改造方案
     */
    @ApiOperation ("调研阶段-制定网络改造方案")
    @PostMapping (value = "/schemeNewwork")
    Result schemeNewwork(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.schemeNewwork(dto);
    }

    /**
     * 里程碑-调研阶段-制定网络改造方案
     */
    @ApiOperation ("调研阶段-制定切换方案")
    @PostMapping (value = "/schemeProject")
    Result schemeProject(@RequestBody ProjMilestoneInfoFileInitializeDTO dto) {
        return proposalInfoFileInitializeService.schemeProject(dto);
    }


    /**
     * 明: 项目工具--报表制作--授权地址
     *
     * @param url
     * @return
     */
    @ApiOperation("明: 项目工具--报表制作--授权地址")
    @GetMapping(value = "/authReportUrl")
    Result authReportUrl(@RequestParam String url) {
        return proposalInfoFileInitializeService.authReportUrl(url);
    }

    /**
     * 根据项目id获取基础老平台基础数据地址
     *
     * @param projectInfoId
     * @return
     */
    @ApiOperation("根据项目id获取基础老平台基础数据地址")
    @GetMapping(value = "/getBaseDataUrlByProjectInfoId")
    Result getBaseDataUrlByProjectInfoId(@RequestParam Long projectInfoId) {
        return proposalInfoFileInitializeService.getBaseDataUrlByProjectInfoId(projectInfoId);
    }

}
