package com.msun.csm.controller.basedata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.BasedataConfig;
import com.msun.csm.dao.entity.basedata.*;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.basedata.*;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.jdbc.EasyExcelUtils;
import com.msun.csm.jdbc.PgsqlConn;
import com.msun.csm.jdbc.PgsqlExecutor;
import com.msun.csm.jdbc.PgsqlGenerator;
import com.msun.csm.jdbc.pojo.DsCfg;
import com.msun.csm.util.AesUtils;
import com.msun.csm.util.ErrUtils;
import com.msun.csm.util.LargeZipFileHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 7630
 * @description: 数据导入，包含导入平台，导入云健康
 * @date 2025/5/23 18:41
 */
@Slf4j
@Api(tags = "数据导入API")
@RestController
@RequestMapping("/importData/")
public class ImportDataController {

    private final OkHttpClient client;
    private static final ConcurrentHashMap<String, Thread> runningJobs = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, String> runningJobUuid = new ConcurrentHashMap<>();
    private static final ReentrantLock lock = new ReentrantLock();
    private static final String KETTLE_DB = "kettle_rg";
    @Autowired
    private BasedataConfig basedataConfig;
    @Autowired
    private BdKtJobMapper bdKtJobMapper;
    @Autowired
    private BdTableInfoMapper bdTableInfoMapper;
    @Autowired
    private BdTableColInfoMapper bdTableColInfoMapper;
    @Autowired
    private BdKtJobDependTableMapper bdKtJobDependTableMapper;
    @Autowired
    private BdImportPlatformRecordMapper bdImportPlatformRecordMapper;
    @Autowired
    private BdImportMsunCloudRecordMapper bdImportMsunCloudRecordMapper;
    @Autowired
    private ProjProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Autowired
    private BdDatasourceMapper bdDatasourceMapper;

    public ImportDataController() {
        client = new OkHttpClient.Builder()
                .connectTimeout(86400, TimeUnit.SECONDS) // 设置连接超时时间
                .readTimeout(86400, TimeUnit.SECONDS)   // 设置读取超时时间
                .writeTimeout(86400, TimeUnit.SECONDS)  // 设置写入超时时间
                .build();
    }


    @ApiOperation(value = "上传数据Excel并落库")
    @ACROSS
    @PostMapping(value = "uploadDataExcel/{projectNumber}")
    public synchronized Result<?> uploadDataExcel(@RequestParam("file") MultipartFile file, @PathVariable("projectNumber") String projectNumber) throws IOException {
        String outputDir = System.getProperty("java.io.tmpdir") + File.separator + "uploadTemplates-" + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        File outputDirectory = new File(outputDir);
        if (!outputDirectory.exists() && !outputDirectory.mkdirs()) {
            throw new CustomException("创建临时目录失败");
        }

        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .select(ProjProjectInfo::getCustomInfoId)
                .eq(ProjProjectInfo::getProjectNumber, projectNumber)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        List<ProjHospitalInfo> hospitals = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                .eq(ProjHospitalInfo::getCustomInfoId, proj.getCustomInfoId())
                .eq(ProjHospitalInfo::getIsDeleted, 0)
                .list();

        List<BdTableInfo> tables = new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                .eq(BdTableInfo::getOpened, true)
                .eq(BdTableInfo::getIsDeleted, 0)
                .list();

        try (InputStream inputStream = file.getInputStream()) {
            // 解压缩 ZIP 文件
            LargeZipFileHandler.processZipFile(inputStream, outputDir);

            hospitals.forEach(hospital -> {
                File dir = findDir(outputDirectory, hospital.getHospitalName());
                if (dir == null) {
                    return;
                }
                //<文件名称：文件>
                Map<String, File> fileMap = new LinkedHashMap<>();
                parseAllDataExcels(dir, fileMap);
                if (CollUtil.isEmpty(fileMap)) {
                    return;
                }
                for (BdTableInfo table : tables) {
                    String fileName = StrUtil.format("{}-{}.xlsx", table.getTableNameCn(), table.getTableNameEn());
                    File f = fileMap.get(fileName);
                    if (f == null) {
                        continue;
                    }
                    BdImportPlatformRecord importRecord = parseDataExcelFile(f, projectNumber, PinyinUtil.getFirstLetter(hospital.getHospitalName(), "") + "_" + projectNumber.toLowerCase(), hospital.getCloudHospitalId(), hospital.getOrgId(), hospital.getHospitalName(), table.getTableNameCn());
                    if (importRecord == null || Boolean.FALSE.equals(importRecord.getSuccess())) {
                        throw new CustomException(StrUtil.format("医院【{}】导入表【{}】失败，错误信息：{}", hospital.getHospitalName(), table.getTableNameCn(), importRecord == null ? "未知错误" : importRecord.getErrMsg()));
                    }
                }
            });
        } finally {
            EasyExcelUtils.deleteDirectory(outputDirectory);
        }
        return Result.success();
    }

    private File findDir(File rootDir, String dirName) {
        File[] files = rootDir.listFiles();
        if (files == null) {
            return null;
        }
        for (File file : files) {
            if (file.isDirectory() && file.getName().equals(dirName)) {
                return file;
            } else if (file.isDirectory()) {
                // 递归查找子目录
                File foundDir = findDir(file, dirName);
                if (foundDir != null) {
                    return foundDir;
                }
            }
        }
        return null;
    }

    //递归模板数据的Excel文件
    private void parseAllDataExcels(File directory, Map<String, File> fileMap) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归处理子目录
                parseAllDataExcels(file, fileMap);
            } else if (file.getName().endsWith(".xlsx")) {
                fileMap.put(file.getName(), file);
            }
        }
    }

    //解析模板数据Excel文件并落库
    private BdImportPlatformRecord parseDataExcelFile(File file, String projectNumber, String dataSchema, Long hospitalId, Long orgId, String hospitalName, String tableNameCn) {
        try (ExcelReader excelReader = EasyExcel.read(file).build()) {
            // 获取所有页签
            List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
            //构建数据源配置
            DsCfg dsCfg = new DsCfg();
            dsCfg.setHost(basedataConfig.getDbHost());
            dsCfg.setPort(basedataConfig.getDbPort());
            dsCfg.setUser(basedataConfig.getDbUname());
            dsCfg.setPasswd(basedataConfig.getDbPasswd());
            dsCfg.setDb(basedataConfig.getDbName());
            dsCfg.setSchema(dataSchema);
            dsCfg.setUrl(PgsqlGenerator.buildUrl(dsCfg));
            Connection conn = PgsqlConn.getConn(dsCfg, false);
            try {
                AtomicReference<Throwable> errorRef = new AtomicReference<>();
                AtomicInteger importTotal = new AtomicInteger(0);
                AtomicInteger sucTotal = new AtomicInteger(0);
                //优先获取元数据
                AtomicReference<LinkedHashMap<String, String>> metadataRef = new AtomicReference<>();
                for (ReadSheet sheet : sheets) {
                    if ("metadata".equalsIgnoreCase(sheet.getSheetName())) {
                        //读取metadata信息
                        EasyExcel.read(file, LinkedHashMap.class, new EasyExcelUtils.ExcelListener(2, subList -> metadataRef.set(CollUtil.getFirst(subList)), 100))
                                .excelType(ExcelTypeEnum.XLSX)
                                .useDefaultListener(false)
                                .sheet(sheet.getSheetNo())
                                .doRead();
                        break;
                    }
                }
                LinkedHashMap<String, String> metadata = metadataRef.get();
                if (metadata == null) {
                    throw new CustomException(StrUtil.format("{}没有元数据信息", file.getName()));
                }
                String tableNameEn = metadata.get("tableNameEn");
                if (!projectNumber.equals(metadata.get("projectNumber"))) {
                    throw new CustomException(StrUtil.format("当前上传的模板不属于该项目，当前项目编号【{}】，模板所属项目编号【{}】", projectNumber, metadata.get("projectNumber")));
                }
                for (ReadSheet sheet : sheets) {
                    if ("metadata".equalsIgnoreCase(sheet.getSheetName())) {
                        continue;
                    }
                    List<BdTableColInfo> cols = new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                            .eq(BdTableColInfo::getTableNameEn, tableNameEn)
                            .eq(BdTableColInfo::getOpened, true)
                            .eq(BdTableColInfo::getIsDeleted, 0)
                            .orderByAsc(BdTableColInfo::getId)
                            .list();
                    LinkedHashMap<String, String> colTypes = new LinkedHashMap<>();
                    colTypes.put("project_number", "varchar");
                    colTypes.put("hospital_id", "int8");
                    colTypes.put("org_id", "int8");
                    List<String> fields = new ArrayList<>();
                    fields.add("project_number");
                    fields.add("hospital_id");
                    fields.add("org_id");
                    List<String> pks = new ArrayList<>();
                    for (BdTableColInfo colInfo : cols) {
                        fields.add(colInfo.getColNameEn());
                        if (Boolean.TRUE.equals(colInfo.getIsPk())) {
                            pks.add(colInfo.getColNameEn());
                        }
                        colTypes.put(colInfo.getColNameEn(), colInfo.getColType());
                    }
                    String insertSql = PgsqlGenerator.generateInsertSql(dataSchema, tableNameEn, fields, pks);
                    EasyExcelUtils.ExcelListener excelListener = new EasyExcelUtils.ExcelListener(2, subList -> {
                        importTotal.addAndGet(subList.size());
                        //单独处理一下hospital_id跟org_id
                        for (LinkedHashMap<String, String> item : subList) {
                            item.put("project_number", projectNumber);
                            item.put("hospital_id", String.valueOf(hospitalId));
                            item.put("org_id", String.valueOf(orgId));
                        }
                        List<LinkedHashMap<String, Object>> targetData = new ArrayList<>();
                        for (LinkedHashMap<String, String> item : subList) {
                            LinkedHashMap<String, Object> obj = new LinkedHashMap<>();
                            targetData.add(obj);
                            for (String fieldName : colTypes.keySet()) {
                                String targetType = colTypes.get(fieldName);
                                if (item.get(fieldName) == null) {
                                    obj.put(fieldName, null);
                                    continue;
                                }
                                switch (targetType) {
                                    case "varchar":
                                    case "text":
                                        obj.put(fieldName, item.get(fieldName));
                                        break;
                                    case "int8":
                                        obj.put(fieldName, new BigDecimal(item.get(fieldName)).longValue());
                                        break;
                                    case "int4":
                                    case "int2":
                                        obj.put(fieldName, new BigDecimal(item.get(fieldName)).intValue());
                                        break;
                                    case "decimal":
                                        obj.put(fieldName, new BigDecimal(item.get(fieldName)));
                                        break;
                                    case "boolean":
                                        String trueStr = "true", shiStr = "是", yStr = "y", yesStr = "yes", tStr = "t";
                                        obj.put(fieldName, StrUtil.containsAnyIgnoreCase(item.get(fieldName), trueStr, shiStr, yStr, yesStr, tStr));
                                        break;
                                    case "timestamp":
                                        //处理掉毫秒
                                        Date ts = DateUtil.parse(DateUtil.format(org.apache.poi.ss.usermodel.DateUtil.getJavaDate(new BigDecimal(item.get(fieldName)).doubleValue()), "yyyy-MM-dd HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                                        obj.put(fieldName, new java.sql.Timestamp(ts.getTime()));
                                        break;
                                    case "date":
                                        //处理掉时分秒毫秒
                                        Date dt = DateUtil.parse(DateUtil.format(org.apache.poi.ss.usermodel.DateUtil.getJavaDate(new BigDecimal(item.get(fieldName)).doubleValue()), "yyyy-MM-dd"), "yyyy-MM-dd");
                                        obj.put(fieldName, new java.sql.Date(dt.getTime()));
                                        break;
                                    case "time":
                                        //处理掉年月日毫秒
                                        Date tm = DateUtil.parse(DateUtil.format(org.apache.poi.ss.usermodel.DateUtil.getJavaDate(new BigDecimal(item.get(fieldName)).doubleValue()), "HH:mm:ss"), "HH:mm:ss");
                                        obj.put(fieldName, new java.sql.Time(tm.getTime()));
                                        break;
                                    default:
                                        throw new RuntimeException("不支持的数据类型：" + targetType);
                                }
                            }
                        }
                        AtomicInteger total = new AtomicInteger(0);
                        PgsqlExecutor.binaryInsert(conn, insertSql, colTypes, targetData, 0, targetData.size(), errorRef, total);
                        sucTotal.addAndGet(total.get());
                    }, 100);
                    EasyExcel.read(file, LinkedHashMap.class, excelListener)
                            .excelType(ExcelTypeEnum.XLSX)
                            .useDefaultListener(false)
                            .sheet(sheet.getSheetNo())
                            .doRead();
                }
                conn.commit();
                BdImportPlatformRecord record = new BdImportPlatformRecord();
                record.setId(IdGenerator.ins().generator());
                record.setProjectNumber(projectNumber);
                record.setHospitalName(hospitalName);
                record.setHospitalId(hospitalId);
                record.setOrgId(orgId);
                record.setTableNameEn(tableNameEn);
                record.setTableNameCn(tableNameCn);
                record.setDataSchema(dataSchema);
                record.setSuccess(errorRef.get() == null);
                record.setErrMsg(ErrUtils.toSimpleString(errorRef.get()));
                record.setTotal((long) importTotal.get());
                record.setSucTotal((long) sucTotal.get());
                bdImportPlatformRecordMapper.insertUpdate(Collections.singletonList(record));
                return record;
            } catch (Throwable e) {
                try {
                    conn.rollback();
                } catch (Throwable ignored) {
                }
                return null;
            } finally {
                PgsqlConn.close(conn);
            }
        }
    }

    //删除平台数据
    @ApiOperation(value = "删除平台数据")
    @ACROSS
    @DeleteMapping(value = "deletePlatformData/{projectNumber}")
    public synchronized Result<?> deletePlatformData(
            @PathVariable("projectNumber") String projectNumber,
            @RequestParam(value = "hospitalId", required = false) Long hospitalId,
            @RequestParam(value = "tableNameEn", required = false) String tableNameEn
    ) throws IOException {
        List<BdImportPlatformRecord> records = new LambdaQueryChainWrapper<>(bdImportPlatformRecordMapper)
                .eq(BdImportPlatformRecord::getProjectNumber, projectNumber)
                .eq(hospitalId != null && hospitalId > 0, BdImportPlatformRecord::getHospitalId, hospitalId)
                .eq(StrUtil.isNotBlank(tableNameEn), BdImportPlatformRecord::getTableNameEn, tableNameEn)
                .eq(BdImportPlatformRecord::getIsDeleted, 0)
                .orderByAsc(BdImportPlatformRecord::getCreateTime)
                .list();
        if (CollUtil.isEmpty(records)) {
            return Result.success("没有需要删除的数据");
        }
        //批量删除
        DsCfg dsCfg = new DsCfg();
        dsCfg.setHost(basedataConfig.getDbHost());
        dsCfg.setPort(basedataConfig.getDbPort());
        dsCfg.setUser(basedataConfig.getDbUname());
        dsCfg.setPasswd(basedataConfig.getDbPasswd());
        dsCfg.setDb(basedataConfig.getDbName());
        dsCfg.setUrl(PgsqlGenerator.buildUrl(dsCfg));
        try (Connection conn = PgsqlConn.getConn(dsCfg, false)) {
            records.forEach(record -> {
                BdTableColInfo pkCol = new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                        .eq(BdTableColInfo::getTableNameEn, record.getTableNameEn())
                        .eq(BdTableColInfo::getIsPk, true)
                        .eq(BdTableColInfo::getIsDeleted, 0)
                        .one();
                PgsqlExecutor.batchDelete(conn, record.getDataSchema(), record.getTableNameEn(), pkCol.getColNameEn(), record.getProjectNumber(), record.getHospitalId(), record.getOrgId(), 1000);
                bdImportPlatformRecordMapper.deleteById(record.getId());
            });
        } catch (Throwable e) {
            throw new CustomException("删除数据失败", e);
        }
        return Result.success("数据删除成功");
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LogBean {
        private String uuid;
        private String jobName;
        private String logText;
        private String logLevel;
        private Boolean finished = false;
        private Integer errors;
    }

    @Data
    public static class ExecKettleJobArgs {
        @NotBlank
        private String projectNumber;
        private Long hospitalId;
        @NotBlank
        @ApiModelProperty("insert、delete、deleteAndInsert")
        private String jobType;
        private String jobName;
        private String tableNameEn;
        private String productName;
        private String logLevel = "Basic";
    }

    //执行kettle作业
    @ApiOperation(value = "导入云健康数据")
    @ACROSS
    @GetMapping(value = "execKettleJob/{params}")
    public Result<?> execKettleJob(@PathVariable("params") String params) {
        lock.lock();
        try {
            String decrypt = AesUtils.decrypt(params);
            ExecKettleJobArgs args = JSON.parseObject(decrypt, ExecKettleJobArgs.class);
            if (runningJobs.containsKey(args.getProjectNumber())) {
                throw new CustomException("当前项目的kettle作业正在执行中，请稍后再试");
            }
            //查询出数据源
            BdDatasource datasource = new LambdaQueryChainWrapper<>(bdDatasourceMapper)
                    .eq(BdDatasource::getProjectNumber, args.getProjectNumber())
                    .eq(BdDatasource::getIsDeleted, 0)
                    .one();
            if (datasource == null) {
                throw new CustomException("请申请云健康数据源连接");
            }

            List<BdKtJob> ktJobs = new ArrayList<>();

            if (StrUtil.isBlank(args.getTableNameEn()) && StrUtil.isBlank(args.getProductName())) {
                //获取到所有的job
                ktJobs = new LambdaQueryChainWrapper<>(bdKtJobMapper)
                        .eq(BdKtJob::getJobType, args.getJobType())
                        .eq(StrUtil.isNotBlank(args.getJobName()), BdKtJob::getJobName, args.getJobName())
                        .eq(BdKtJob::getIsDeleted, 0)
                        .eq(BdKtJob::getOpened, true)
                        .orderByAsc(BdKtJob::getIdx)
                        .list();
            } else if (StrUtil.isNotBlank(args.getTableNameEn())) {
                ktJobs = bdKtJobMapper.selectJobByTableName(args.getTableNameEn(), args.getTableNameEn(), args.getJobName());
            } else if (StrUtil.isNotBlank(args.getProductName())) {
                ktJobs = new LambdaQueryChainWrapper<>(bdKtJobMapper)
                        .eq(BdKtJob::getProductName, args.getProductName())
                        .eq(BdKtJob::getJobType, args.getJobType())
                        .eq(StrUtil.isNotBlank(args.getJobName()), BdKtJob::getJobName, args.getJobName())
                        .eq(BdKtJob::getIsDeleted, 0)
                        .eq(BdKtJob::getOpened, true)
                        .orderByAsc(BdKtJob::getIdx)
                        .list();
            }
            if (CollUtil.isEmpty(ktJobs)) {
                throw new CustomException(StrUtil.format("没有找到对应的kettle作业，参数：{}", StrUtil.format("tableNameEn={}, productName={}, jobType={}", args.getTableNameEn(), args.getProductName(), args.getJobType())));
            }

            Map<String, Object> ktDb = new HashMap<>();
            ktDb.put("name", KETTLE_DB);
            ktDb.put("type", "POSTGRESQL");
            ktDb.put("access", "Native");
            ktDb.put("host", basedataConfig.getDbHost());
            ktDb.put("port", basedataConfig.getDbPort());
            ktDb.put("db", KETTLE_DB);
            ktDb.put("user", basedataConfig.getDbUname());
            ktDb.put("pass", basedataConfig.getDbPasswd());

            Map<String, Object> ktRepo = new HashMap<>();
            ktRepo.put("database", ktDb);
            ktRepo.put("name", KETTLE_DB);
            ktRepo.put("username", "admin");
            ktRepo.put("password", "admin");

            //查询出所有的医院
            List<ProjHospitalInfo> hospitals = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                    .eq(ProjHospitalInfo::getCustomInfoId, new LambdaQueryChainWrapper<>(projectInfoMapper)
                            .select(ProjProjectInfo::getCustomInfoId)
                            .eq(ProjProjectInfo::getProjectNumber, args.getProjectNumber())
                            .eq(ProjProjectInfo::getIsDeleted, 0)
                            .one().getCustomInfoId())
                    .eq(args.getHospitalId() != null && args.getHospitalId() > 0, ProjHospitalInfo::getCloudHospitalId, args.getHospitalId())
                    .eq(ProjHospitalInfo::getIsDeleted, 0)
                    .list();
            List<BdKtJob> finalKtJobs = ktJobs;
            Thread thread = new Thread(() -> {
                try {
                    Semaphore limit = new Semaphore(1);
                    for (ProjHospitalInfo hospital : hospitals) {
                        //先检查kettle依赖的表是否已经导入成功
                        for (BdKtJob ktJob : finalKtJobs) {
                            List<String> dependTables = StrUtil.split(ktJob.getDependTableNameEn(), ",");
                            List<String> importTableNames = new LambdaQueryChainWrapper<>(bdImportPlatformRecordMapper)
                                    .select(BdImportPlatformRecord::getTableNameEn)
                                    .eq(BdImportPlatformRecord::getProjectNumber, projectInfoMapper)
                                    .eq(BdImportPlatformRecord::getHospitalId, hospital.getCloudHospitalId())
                                    .eq(BdImportPlatformRecord::getSuccess, true)
                                    .eq(BdImportPlatformRecord::getValidStatus, 1)
                                    .in(BdImportPlatformRecord::getTableNameEn, dependTables)
                                    .eq(BdImportPlatformRecord::getIsDeleted, 0)
                                    .list().stream().map(BdImportPlatformRecord::getTableNameEn).collect(Collectors.toList());
                            dependTables.removeAll(importTableNames);
                            if (CollUtil.isNotEmpty(dependTables)) {
                                throw new CustomException(StrUtil.format("医院【{}】的kettle作业【{}】依赖的表【{}】没有导入或者验证成功，请先导入依赖的表", hospital.getHospitalName(), ktJob.getJobName(), String.join(",", dependTables)));
                            }
                        }

                        String dataSchema = PinyinUtil.getFirstLetter(hospital.getHospitalName(), "") + "_" + args.getProjectNumber().toLowerCase();
                        ;
                        //调用Kettle接口
                        for (BdKtJob ktJob : finalKtJobs) {
                            String uuid = UUID.randomUUID().toString().toUpperCase();
                            //解析变量
                            Map<String, Object> variables = new HashMap<>();
                            variables.put("hospital_id", hospital.getCloudHospitalId());
                            variables.put("org_id", hospital.getOrgId());
                            variables.put("schema", dataSchema);
                            variables.put("chis_host", datasource.getChisHost());
                            variables.put("chis_port", datasource.getChisPort());
                            variables.put("chis_user", datasource.getChisUser());
                            variables.put("chis_pass", datasource.getChisPassword());
                            variables.put("chisapp_host", datasource.getChisappHost());
                            variables.put("chisapp_port", datasource.getChisappPort());
                            variables.put("chisapp_user", datasource.getChisappUser());
                            variables.put("chisapp_pass", datasource.getChisappPassword());
                            Map<String, Object> runArgs = new HashMap<>();
                            runArgs.put("repository", ktRepo);
                            runArgs.put("uuid", uuid);
                            runArgs.put("jobName", ktJob.getJobName());
                            runArgs.put("vars", variables);
                            runArgs.put("logLevel", args.getLogLevel());
                            String runArgsJson = JSON.toJSONString(runArgs);

                            String runArgsJsonStr = AesUtils.encrypt(runArgsJson);
                            //启动作业
                            BdImportMsunCloudRecord record = new LambdaQueryChainWrapper<>(bdImportMsunCloudRecordMapper)
                                    .eq(BdImportMsunCloudRecord::getProjectNumber, args.getProjectNumber())
                                    .eq(BdImportMsunCloudRecord::getHospitalId, hospital.getCloudHospitalId())
                                    .eq(BdImportMsunCloudRecord::getJobName, ktJob.getJobName())
                                    .eq(BdImportMsunCloudRecord::getIsDeleted, 0)
                                    .one();
                            if (record == null) {
                                record = new BdImportMsunCloudRecord();
                                record.setProjectNumber(args.getProjectNumber());
                                record.setHospitalId(hospital.getCloudHospitalId());
                                record.setJobName(ktJob.getJobName());
                                record.setUuid(uuid);
                                record.setStatus(0);
                                record.setErrors(0);
                                record.setErrMsg("");
                                record.setCreateTime(new Date());
                                bdImportMsunCloudRecordMapper.insert(record);
                            } else {
                                record.setUuid(uuid);
                                record.setStatus(0);
                                record.setErrors(0);
                                record.setErrMsg("");
                                record.setUpdateTime(new Date());
                                bdImportMsunCloudRecordMapper.updateById(record);
                            }

                            log.warn("开始执行kettle作业【{}】, hospitalId: {}, uuid: {}", ktJob.getJobName(), hospital.getCloudHospitalId(), uuid);
                            runningJobUuid.put(args.getProjectNumber(), uuid);
                            limit.acquire();
                            Request request = new Request.Builder()
                                    .url(StrUtil.format("{}/schedule/runJob/{}", basedataConfig.getKettleHost(), runArgsJsonStr))
                                    .build();

                            EventSource.Factory factory = EventSources.createFactory(client);
                            BdImportMsunCloudRecord finalRecord = record;
                            factory.newEventSource(request, new EventSourceListener() {
                                @Override
                                public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                                    // SSE连接已打开
                                    finalRecord.setStatus(1);
                                    bdImportMsunCloudRecordMapper.updateById(finalRecord);
                                }

                                @Override
                                public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
                                    LogBean logBean = JSON.parseObject(data, LogBean.class);
                                    if (logBean.getLogLevel().equals("Error")) {
                                        log.error("kettle作业【{}】项目编号：【{}】医院ID：【{}】日志：{}", ktJob.getJobName(), args.getProjectNumber(), hospital.getCloudHospitalId(), logBean.getLogText());
                                    } else {
                                        log.info("kettle作业【{}】项目编号：【{}】医院ID：【{}】日志：{}", ktJob.getJobName(), args.getProjectNumber(), hospital.getCloudHospitalId(), logBean.getLogText());
                                    }
                                    if (logBean.getFinished()) {
                                        Integer errors = logBean.getErrors();
                                        if (errors != null && errors > 0) {
                                            finalRecord.setStatus(3);
                                            finalRecord.setErrors(errors);
                                            finalRecord.setErrMsg(logBean.getLogText());
                                            log.error("kettle作业【{}】执行失败：项目编号：【{}】医院ID：【{}】执行失败【{}】日志：{}", ktJob.getJobName(), args.getProjectNumber(), hospital.getCloudHospitalId(), logBean.getErrors(), logBean.getLogText());
                                        } else {
                                            finalRecord.setStatus(2);
                                            finalRecord.setErrors(0);
                                            finalRecord.setErrMsg("");
                                            log.info("kettle作业【{}】执行成功：项目编号：【{}】医院ID：【{}】", ktJob.getJobName(), args.getProjectNumber(), hospital.getCloudHospitalId());
                                        }
                                        bdImportMsunCloudRecordMapper.updateById(finalRecord);
                                    }

                                }

                                @Override
                                public void onClosed(@NotNull EventSource eventSource) {
                                    // SSE连接已关闭
                                    limit.release();
                                }

                                @Override
                                public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
                                    limit.release();
                                    // SSE连接失败
                                    finalRecord.setStatus(3);
                                    finalRecord.setErrors(0);
                                    finalRecord.setErrMsg(StrUtil.format("连接SSE失败：{}", ErrUtils.toString(t)));
                                    log.error("kettle作业【{}】连接SSE失败：项目编号：【{}】医院ID：【{}】日志：{}", ktJob.getJobName(), args.getProjectNumber(), hospital.getCloudHospitalId(), ErrUtils.toString(t));
                                }
                            });
                        }
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } finally {
                    //清理运行中的作业
                    runningJobs.remove(args.getProjectNumber());
                    runningJobUuid.remove(args.getProjectNumber());
                }
            });
            thread.setName("kettle-job-exec-" + args.getProjectNumber());
            runningJobs.put(args.getProjectNumber(), thread);
            thread.start();
            return Result.success();
        } catch (Throwable e) {
            log.error("执行kettle作业失败", e);
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

    @ACROSS
    @GetMapping(value = "execTest/{args}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter execTest(@PathVariable("args") String args) {
        String url = StrUtil.format("http://localhost:10086/kettle/schedule/runJob/{}", args);
        SseEmitter emitter = new SseEmitter(86400000L);
        emitter.onCompletion(() -> log.info("发送SSE消息完成"));
        emitter.onTimeout(() -> log.warn("发送SSE消息超时"));
        new Thread(() -> {
            Request request = new Request.Builder()
                    .url(url)
                    .build();

            EventSource.Factory factory = EventSources.createFactory(client);
            factory.newEventSource(request, new EventSourceListener() {
                @Override
                public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                    // SSE连接已打开
                    log.info("准备调度kettle作业，URL: {}", url);
                }

                @Override
                public void onEvent(@NotNull EventSource eventSource, String id, String type, @NotNull String data) {
                    try {
                        // 接收到日志数据，透传给SseEmitter
                        LogBean logBean = JSON.parseObject(data, LogBean.class);
                        log.info("id: {}, type: {}, data: {}", id, type, logBean);

                        emitter.send(SseEmitter.event().name("message").data(logBean).build());
//                        emitter.send(logBean, MediaType.APPLICATION_JSON);
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                }

                @Override
                public void onClosed(@NotNull EventSource eventSource) {
                    // SSE连接已关闭
                    log.info("SSE连接已关闭");
                    emitter.complete();
                }

                @Override
                public void onFailure(@NotNull EventSource eventSource, Throwable t, Response response) {
                    // SSE连接失败
                    log.error("SSE连接失败", t);
                    emitter.completeWithError(t);
                }
            });
        }).start();

        return emitter;
    }

    //停止任务
    @ApiOperation(value = "停止kettle作业")
    @ACROSS
    @GetMapping(value = "stopKettleJob/{projectNumber}")
    public Result<?> stopKettleJob(@PathVariable("projectNumber") String projectNumber) {
        lock.lock();
        try {
            Thread thread = runningJobs.remove(projectNumber);
            String uuid = runningJobUuid.remove(projectNumber);
            if (thread != null) {
                thread.interrupt();
                String resp = HttpUtil.get(StrUtil.format("{}/schedule/stopJob/{}", basedataConfig.getKettleHost(), uuid));
                log.warn("停止作业【{}】返回值：{}", projectNumber, resp);
                return Result.success("已停止kettle作业");
            } else {
                return Result.success("没有正在运行的kettle作业");
            }
        } catch (Throwable e) {
            log.error("停止kettle作业失败", e);
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

}
