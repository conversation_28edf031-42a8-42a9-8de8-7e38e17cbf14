package com.msun.csm.controller.projtool;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.dao.mapper.config.ConfigCustomFormLimitMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.model.req.projtool.ProjProjectInfoUpdateReq;
import com.msun.csm.model.req.projtool.ProjToolConfigDataReq;
import com.msun.csm.model.req.projtool.ProjToolCustomLimitDataReq;
import com.msun.csm.model.req.projtool.ProjToolCustomLimitUpdateDataReq;
import com.msun.csm.model.req.projtool.ProjToolUpdateReq;
import com.msun.csm.model.req.projtool.SysConfigReq;
import com.msun.csm.model.resp.projtool.ProjToolCustomLimitDataResp;
import com.msun.csm.service.projtool.ProjectToolUpdateService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目工具使用
 *
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */
@Api(tags = "项目工具使用")
@RestController
@RequestMapping("/projectToolUseUpdate")
@Slf4j
public class ProjectToolUseUpdateController {

    @Resource
    private ProjectToolUpdateService projectToolUpdateService;

    @Autowired
    private ProjCustomInfoMapper projCustomInfoMapper;
    @Autowired
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Autowired
    private ConfigCustomFormLimitMapper configCustomFormLimitMapper;
    ;
    @Autowired
    private ProjProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjOrderProductMapper projOrderProductMapper;
    @Autowired
    private ProjProjectInfoMapper projProjectInfoMapper;

    /**
     * 分页查询日志信息
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "分页查询日志信息", operDetail = "分页查询日志信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询日志信息")
    @ApiOperation("分页查询日志信息")
    @PostMapping(value = "/selectLibFormLogByPage")
    public Result<PageInfo<ProjUpdateDataLog>> selectLibFormLogByPage(@RequestBody ProjToolUpdateReq libFormReq) {
        return projectToolUpdateService.selectUpdateLogByPage(libFormReq);
    }

    /**
     * 更新账号信息
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "更新账号信息", operDetail = "更新账号信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "更新账号信息")
    @ApiOperation("更新账号信息")
    @PostMapping(value = "/updateAccountData")
    public Result updateAccountData(@RequestBody ProjToolUpdateReq libFormReq) {
        return projectToolUpdateService.updateAccountData(libFormReq);
    }


    /**
     * 后台管理-项目配置信息修改
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "后台管理-项目配置信息修改", operDetail = "后台管理-项目配置信息修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "后台管理-项目配置信息修改")
    @ApiOperation("后台管理-项目配置信息修改")
    @PostMapping(value = "/updateProjectConfigInfo")
    public Result updateProjectConfigInfo(@RequestBody ProjProjectInfoUpdateReq libFormReq) {
        return projectToolUpdateService.updateProjectConfigInfo(libFormReq);
    }

    @Log(operName = "配置信息查询-报表限制", operDetail = "配置信息查询", intLogType = Log.IntLogType.SELF_SYS, cnName = "配置信息查询")
    @ApiOperation("配置信息查询-报表限制")
    @PostMapping(value = "/selectConfigList")
    public Result<List<SysConfig>> selectConfigList(@RequestBody ProjToolConfigDataReq libFormReq) {
        return projectToolUpdateService.selectConfigList(libFormReq);
    }

    /**
     * 配置信息修改-报表限制
     *
     * @param sysConfigReq
     * @return
     */
    @Log(operName = "配置信息修改", operDetail = "后台管理-项目配置信息修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "后台管理-项目配置信息修改")
    @ApiOperation("配置信息修改-报表限制")
    @PostMapping(value = "/updateSysConfigInfo")
    public Result updateSysConfigInfo(@RequestBody SysConfigReq sysConfigReq) {
        return projectToolUpdateService.updateSysConfigInfo(sysConfigReq);
    }

    /**
     * 分页查询日志信息
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "分页查询客户限制配置信息", operDetail = "分页查询客户限制配置信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询客户限制配置信息")
    @ApiOperation("分页查询客户限制配置信息")
    @PostMapping(value = "/selectCustomerLimitListByPage")
    public Result<PageInfo<ProjToolCustomLimitDataResp>> selectCustomerLimitListByPage(@RequestBody ProjToolCustomLimitDataReq libFormReq) {
        return projectToolUpdateService.selectCustomerLimitListByPage(libFormReq);
    }

    @Log(operName = "手动触发同步系统管理医院是否开启表单开启功能限制", operDetail = "手动触发同步系统管理医院是否开启表单开启功能限制", intLogType = Log.IntLogType.SELF_SYS, cnName = "手动触发同步系统管理医院是否开启表单开启功能限制")
    @ApiOperation("手动触发同步系统管理医院是否开启表单开启功能限制")
    @GetMapping(value = "/apiSyncCustomFormCtrlTask")
    public Result<?> apiSyncCustomFormCtrlTask(@RequestParam("customInfoId") Long customInfoId, @RequestParam("hospitalInfoId") Long hospitalInfoId) {
        projectToolUpdateService.apiSyncCustomFormCtrlTask(customInfoId, hospitalInfoId);
        return Result.success();
    }

    /**
     * 客服工作限制配置: 修改
     *
     * @param projToolCustomLimitUpdateDataReq
     * @return
     */
    @Log(operName = "客服工作限制配置: 修改", operDetail = "客服工作限制配置: 修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "客服工作限制配置: 修改")
    @ApiOperation("客服工作限制配置: 修改")
    @PostMapping(value = "/updateCustomWorkLimitConfig")
    public Result updateCustomWorkLimitConfig(@RequestBody ProjToolCustomLimitUpdateDataReq projToolCustomLimitUpdateDataReq) {
        return projectToolUpdateService.updateCustomWorkLimitConfig(projToolCustomLimitUpdateDataReq);
    }

    @Log(operName = "客服工作限制批量配置: 修改", operDetail = "客服工作限制批量配置: 修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "客服工作限制批量配置: 修改")
    @ApiOperation("客服工作限制批量配置: 修改")
    @PostMapping(value = "/batchUpdateCustomWorkLimitConfig")
    public Result batchUpdateCustomWorkLimitConfig(@RequestBody List<ProjToolCustomLimitUpdateDataReq> reqs) {
        List<ProjToolCustomLimitUpdateDataReq> errs = new ArrayList<>();
        for (ProjToolCustomLimitUpdateDataReq req : reqs) {
            try {
                projectToolUpdateService.updateCustomWorkLimitConfig(req);
            } catch (Throwable e) {
                errs.add(req);
                log.error("修改30天限制报错：{},{}", e.getMessage(), JSON.toJSONString(req), e);
            }
        }
        return Result.success(errs);
    }

    @Data
    public static class UpdateCustomReportLimitConfigBatchArgs {
        private List<Long> yyCustomerIds;
        private Integer reportlimitFlag;
    }

    @Log(operName = "批量客服工作限制配置: 统计报表修改", operDetail = "批量客服工作限制配置: 统计报表修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量客服工作限制配置: 统计报表修改")
    @ApiOperation("批量客服工作限制配置: 统计报表修改")
    @ACROSS
    @PostMapping(value = "/updateCustomReportLimitConfigBatch")
    public Result<?> updateCustomWorkLimitConfigBatch(@RequestBody UpdateCustomReportLimitConfigBatchArgs args) {
        List<ProjCustomInfo> customs = new LambdaQueryChainWrapper<>(projCustomInfoMapper)
                .select(ProjCustomInfo::getCustomInfoId, ProjCustomInfo::getYyCustomerId, ProjCustomInfo::getCustomName)
                .in(ProjCustomInfo::getYyCustomerId, args.getYyCustomerIds())
                .eq(ProjCustomInfo::getIsDeleted, 0)
                .list();
        for (ProjCustomInfo custom : customs) {
            List<ProjHospitalInfo> hospitals = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                    .select(ProjHospitalInfo::getHospitalInfoId, ProjHospitalInfo::getHospitalName)
                    .eq(ProjHospitalInfo::getCustomInfoId, custom.getCustomInfoId())
                    .eq(ProjHospitalInfo::getIsDeleted, 0)
                    .list();
            for (ProjHospitalInfo hospital : hospitals) {
                ProjToolCustomLimitUpdateDataReq req = new ProjToolCustomLimitUpdateDataReq();
                req.setCustomInfoId(custom.getCustomInfoId());
                req.setHospitalInfoId(hospital.getHospitalInfoId());
                req.setReportlimitFlag(args.getReportlimitFlag());
                req.setCustomSwitchFlag(0);
                try {
                    projectToolUpdateService.updateCustomWorkLimitConfig(req);
                } catch (Throwable e) {
                    log.error("批量修改统计报表限制错误：customInfoId：{}#{}，hospitalInfoId：{}#{}，reportlimitFlag：{}", req.getCustomInfoId(), custom.getCustomName(), req.getHospitalInfoId(), hospital.getHospitalName(), req.getReportlimitFlag(), e);
                }
            }
        }
        return Result.success();
    }

    //验证30天限制是否生效
    @ApiOperation("验证30天限制是否生效")
    @ACROSS
    @GetMapping(value = "/checkLimitAvailable/{code}")
    public Result<?> checkLimitAvailable(@PathVariable("code") String code) {
        List<Long> customInfoIds = projCustomInfoMapper.getAllBackendCustomInfoId();
        List<ConcurrentHashMap<String, Object>> reqs = new ArrayList<>();
        for (Long customInfoId : customInfoIds) {
            List<ProjHospitalInfo> hospitals = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                    .select(ProjHospitalInfo::getHospitalInfoId, ProjHospitalInfo::getHospitalName)
                    .eq(ProjHospitalInfo::getCustomInfoId, customInfoId)
                    .eq(ProjHospitalInfo::getIsDeleted, 0)
                    .list();
            for (ProjHospitalInfo hospital : hospitals) {
                if (Boolean.FALSE.equals(configCustomFormLimitMapper.checkLimitAvailable(hospital.getHospitalInfoId(), code))) {
                    ConcurrentHashMap<String, Object> obj = new ConcurrentHashMap<>();
                    obj.put("customInfoId", customInfoId);
                    obj.put("hospitalInfoId", hospital.getHospitalInfoId());
                    obj.put("customSwitchFlag", 0);
                    obj.put("hospitalName", hospital.getHospitalName());
                    reqs.add(obj);
                    switch (code) {
                        case "interfacelimit":
                            obj.put("interfacelimitFlag", 0);
                            break;
                        case "reportlimit":
                            obj.put("reportlimitFlag", 0);
                            break;
                        case "baseprint":
                            obj.put("baseprintFlag", 0);
                            break;
                        case "reportweb":
                            obj.put("reportwebFlag", 0);
                            break;
                        case "hulidanyuan":
                            obj.put("hulidanyuanFlag", 0);
                            break;
                        case "aims":
                            obj.put("aimsFormFlag", 0);
                            break;
                        case "icuisnew":
                            obj.put("icuFormFlag", 0);
                            break;
                        case "emis":
                            obj.put("emisFormFlag", 0);
                            break;
                        default:
                            continue; // 如果code不匹配，跳过
                    }
                }
            }
        }
       return Result.success(reqs);
    }

    /**
     * 变更客户是否为电销
     *
     * @param projectInfoId 项目ID
     */
    @Log(operName = "变更客户是否为电销", operDetail = "变更客户是否为电销", intLogType = Log.IntLogType.SELF_SYS, cnName = "变更客户是否为电销")
    @PostMapping("/changeCustomTypeModel")
    Result changeCustomTypeModel(@RequestBody ProjectInfoId projectInfoId) {
        return projectToolUpdateService.changeCustomTypeModel(projectInfoId.getProjectInfoId());
    }


}
