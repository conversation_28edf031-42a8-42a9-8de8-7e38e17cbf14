package com.msun.csm.controller.proj;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.model.req.GetProjectFileRuleReq;
import com.msun.csm.model.req.UpdateUseFlagReq;
import com.msun.csm.model.req.projectfile.DeleteFileReq;
import com.msun.csm.model.req.projectfile.UploadFileReq2;
import com.msun.csm.model.vo.FileUploadRuleVO;
import com.msun.csm.service.proj.ProjectCommonFileService;

@Slf4j
@RestController
@RequestMapping("/projectCommonFile")
public class ProjectCommonFileController {

    @Resource
    private ProjectCommonFileService projectCommonFileService;

    /**
     * 查询各节点文件上传规则及已上传的文件
     */
    @PostMapping("/getProjectCommonFileRule")
    Result<FileUploadRuleVO> getProjectCommonFileRule(@RequestBody GetProjectFileRuleReq req) {
        FileUploadRuleVO dirUserList = projectCommonFileService.getProjectFileRule(req);
        return Result.success(dirUserList);
    }

    /**
     * 基于文件上传规则的文件上传
     *
     * @param req 数据传输对象
     */
    @PostMapping("/uploadFileWithRule")
    public Result<ProjProjectFileExtend> uploadFileWithRule(UploadFileReq2 req, HttpServletRequest request) {
        return projectCommonFileService.uploadFileWithRule(req, request);
    }

    /**
     * 删除已经上传的文件
     *
     * @param req 数据传输对象
     */
    @PostMapping("/deleteFile")
    public Result<Void> deleteFile(@RequestBody DeleteFileReq req) {
        return projectCommonFileService.deleteFile(req);
    }

    /**
     * 更新使用标识
     *
     * @param req 参数
     */
    @PostMapping("/updateUseFlag")
    public Result<Void> updateUseFlag(UpdateUseFlagReq req) {
        return projectCommonFileService.updateUseFlag(req);
    }

    /**
     * 保存并根据规则检测上传的图片
     */
    @PostMapping("/saveProjectCommonFile")
    Result<Void> saveProjectCommonFile(@RequestBody GetProjectFileRuleReq req) {
        return projectCommonFileService.saveProjectCommonFile(req);
    }


}
