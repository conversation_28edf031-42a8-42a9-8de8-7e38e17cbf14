package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.AutoCheckForTask;
import com.msun.csm.model.dto.SubmitAccountInfoDTO;
import com.msun.csm.model.dto.autocheck.AutoCheckHospitalInfoDTO;
import com.msun.csm.model.dto.autocheck.GenLoginUserDTO;
import com.msun.csm.model.resp.project.AutoCheckHospitalInfoResp;
import com.msun.csm.model.vo.ProjAutocheckInfoVO;
import com.msun.csm.service.proj.AutoCheckService;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;

/**
 * @description:
 * @fileName:
 * @author:zhouzhaoyu
 * @updateBy:
 * @Date:Created in 18:01 2024/7/8
 * @remark:
 */
@Api(tags = "自动化测试平台数据控制层")
@RestController
@RequestMapping("/autoCheck")
public class AutoCheckHospitalController {

    @Resource
    private AutoCheckService autoCheckService;

    /**
     * 查询自动化测试数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "自动化测试-查询自动化测试数据", operDetail = "自动化测试-查询自动化测试数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "自动化测试-查询自动化测试数据")
    @PostMapping("/selectAutoCheckInfoData")
    Result<ProjAutocheckInfoVO> selectAutoCheckInfoData(@RequestBody AutoCheckHospitalInfoDTO dto) {
        return autoCheckService.selectAutoCheckInfoData(dto);
    }

    /**
     * 自动化测试平台页面展示医院信息
     *
     * @param projectInfoId
     * @return
     */
    @Log(operName = "自动化测试-医院列表", operDetail = "自动化测试-医院列表", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "自动化测试-医院列表")
    @GetMapping("/getHospitalInfo")
    Result<AutoCheckHospitalInfoResp> getHospitalInfo(@RequestParam("projectInfoId") Long projectInfoId) {
        return Result.success(autoCheckService.getHospitalInfo(projectInfoId));

    }

    /**
     * 调用自动化测试平台，获取返回的数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/applyToAutoCheck")
    Result<JSONObject> applyToAutoCheck(@RequestBody AutoCheckHospitalInfoDTO dto) {
        return Result.success(autoCheckService.applyToAutoCheck(dto));
    }


    /**
     * 调用自动化测试平台，获取账号信息
     *
     * @param dto 请求创建账号的模板信息
     * @return Result<JSONObject> 创建后的账号信息
     */
    @PostMapping("/getDeptUserInfo")
    Result<JSONObject> getDeptUserInfo(@RequestBody GenLoginUserDTO dto) {
        return Result.success(autoCheckService.getDeptUserInfoImpl(dto));
    }

    /**
     * 调用自动化测试平台  返回部门模版信息
     *
     * @param hospitalInfoId 交付平台主键id
     * @return 返回结果（含数据结构）
     */
    @Log(operName = "向自动化测试系统获取模块信息", operDetail = "向自动化测试系统获取模块信息", intLogType = Log.IntLogType.SELF_SYS, cnName =
            "向自动化测试系统获取模块信息",
            saveParam = true)
    @GetMapping("/getAutoCheckDeptList")
    Result<JSONObject> getAutoCheckDeptList(@Valid @NotNull(message = "医院id不能为空") Long hospitalInfoId) {
        return Result.success(autoCheckService.getAutoCheckDeptList(hospitalInfoId));
    }


    /**
     * 将选中的账号信息提交给自动化测试平台
     *
     * @return
     */
    @PostMapping("/submitAccountInfo")
    Result<JSONObject> submitAccountInfo(@RequestBody SubmitAccountInfoDTO dto) {
        return Result.success(autoCheckService.submitAccountInfo(dto));
    }


    /**
     * 调用自动化测试平台，运行场景任务
     *
     * @param taskType      【0：基础数据任务；1：业务数据】
     * @param projectInfoId
     * @return
     */
    @GetMapping("/autoCheckTaskRun")
    Result autoCheckTaskRun(@RequestParam("taskType") Integer taskType,
                            @RequestParam("projectInfoId") Long projectInfoId) {
        return autoCheckService.autoCheckTaskRun(taskType, projectInfoId);
    }


    /**
     * 将场景任务置为不可运行状态并且在在线医院列表中新增一条
     *
     * @return
     */
    @PostMapping("/taskClose")
    Result<JSONObject> taskClose(@RequestBody AutoCheckForTask dto) {
        return Result.success(autoCheckService.taskClose(dto));
    }


}
