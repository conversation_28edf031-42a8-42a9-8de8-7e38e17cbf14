package com.msun.csm.controller.config;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.service.proj.ConfigSendMessageService;

import io.swagger.v3.oas.annotations.tags.Tag;


/**
 * <AUTHOR>
 * @since 2024-05-07 04:52:00
 */

@RestController
@RequestMapping("/configSendMessage")
@Tag(name = "发送消息配置表相关接口")
public class ConfigSendMessageController {

    @Resource
    private ConfigSendMessageService configSendMessageService;

}
