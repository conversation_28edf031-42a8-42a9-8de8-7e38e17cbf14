package com.msun.csm.controller.proj;

import java.io.UnsupportedEncodingException;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.model.dto.ProjectAcceptanceDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyAcceptanceDTO;
import com.msun.csm.model.vo.AcceptanceResVO;
import com.msun.csm.model.vo.AcceptanceTimesResVO;
import com.msun.csm.model.vo.ProjApplyAcceptanceVO;
import com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO;
import com.msun.csm.service.proj.ProjProjectAcceptanceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @description:
 * @fileName:
 * @author:zhouzhaoyu
 * @updateBy:
 * @Date:Created in 14:37 2024/5/22
 * @remark:
 */
@Api(value = "项目验收信息")
@RestController
@RequestMapping("/acceptance")
public class ProjectAcceptanceController {

    @Resource
    private ProjProjectAcceptanceService projProjectAcceptanceService;


    /**
     * 项目验收初始化页面信息展示
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询项目验收次数和验收日志")
    @Log(
            operName = "项目验收", operDetail = "项目验收-查询项目验收次数和验收日志", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-查询项目验收次数和验收日志"
    )
    @PostMapping("/getTimesAndlogs")
    Result<AcceptanceTimesResVO> getTimesAndlogs(@RequestBody ProjectAcceptanceDTO dto) {
        return Result.success(projProjectAcceptanceService.getTimesAndlogs(dto));
    }


    /**
     * 项目验收初始化页面信息展示
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询附件信息")
    @Log(
            operName = "项目验收", operDetail = "项目验收-查询附件信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-查询附件信息"
    )
    @PostMapping("/getAcceptanceRules")
    Result<List<ProjProjectAcceptanceRuleVO>> getAcceptanceRules(@RequestBody ProjectAcceptanceDTO dto) {
        return Result.success(projProjectAcceptanceService.getAcceptanceRules(dto));
    }


    /**
     * 项目验收初始化页面信息展示
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目验收初始化页面信息展示")
    @Log(
            operName = "项目验收", operDetail = "项目验收-初始化页面信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-初始化页面信息"
    )
    @PostMapping("/getAcceptance")
    Result<ProjApplyAcceptanceVO> getAcceptanceByProjectId(@RequestBody ProjectAcceptanceDTO dto) {
        return Result.success(projProjectAcceptanceService.getAcceptanceByProjectId(dto));
    }

    /**
     * 项目验收初始化页面信息展示
     *
     * @param dto
     * @return
     */
    @ApiOperation("申请项目验收")
    @Log(
            operName = "项目验收", operDetail = "项目验收-申请项目验收", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-申请项目验收"
    )
    @PostMapping("/applyAcceptance")
    Result<AcceptanceResVO> applyAcceptance(@RequestBody ProjApplyAcceptanceDTO dto) throws UnsupportedEncodingException {
        return projProjectAcceptanceService.applyAcceptance(dto);
    }

    /**
     * 验收文件保存
     *
     * @param dto
     * @return
     */
    @ApiOperation("验收文件保存")
    @Log(operName = "验收文件保存", operDetail = "项目验收-验收文件保存", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-验收文件保存")
    @PostMapping("/tempStorageFile")
    Result tempStorageFile(@RequestBody ProjApplyAcceptanceDTO dto) {
        return projProjectAcceptanceService.tempStorageFile(dto);
    }

    /**
     * 删除文件
     *
     * @param acceptanceRuleId
     * @return
     */
    @ApiOperation("删除文件")
    @Log(operName = "删除文件", operDetail = "项目验收-删除文件", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-删除文件")
    @GetMapping("/deleteTemp")
    Result tempStorageFile(@RequestParam("acceptanceRuleId") Long acceptanceRuleId) {
        return projProjectAcceptanceService.deleteTemp(acceptanceRuleId);
    }

    /**
     * 老系统迁移验收数据到csm
     *
     * @param projectInfoId
     * @return
     */
    @ApiOperation("老系统迁移验收数据到csm")
    @Log(operName = "老系统迁移验收数据到csm", operDetail = "项目验收-老系统迁移验收数据到csm", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-老系统迁移验收数据到csm")
    @GetMapping("/sendOldAcceptFileDataForCsm")
     Result sendOldAcceptFileDataForCsm(Long projectInfoId) {
        return projProjectAcceptanceService.sendOldAcceptFileDataForCsm(projectInfoId);
    }

    /**
     * 删除项目成员标识
     *
     * @param projectInfo
     * @return
     */
    @ApiOperation("删除项目成员标识")
    @Log(operName = "删除项目成员标识", operDetail = "删除项目成员标识", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目验收-老系统迁移验收数据到csm")
    @GetMapping("/deleteProjectMemberFlag")
    Result deleteProjectMemberFlag(@RequestBody ProjProjectInfo projectInfo) {
        projProjectAcceptanceService.deleteProjectMemberFlag(projectInfo);
        return Result.success();
    }
}
