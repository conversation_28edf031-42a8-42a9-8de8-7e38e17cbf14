package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.service.proj.ProjContractInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @version : V1.52.0
 * @ClassName: ProjContractInfoController
 * @Description:合同
 * @Author: Yhongmin
 * @Date: 17:52 2024/7/18
 */
@Api(tags = "合同信息")
@RestController
@RequestMapping("/proj_contract_info")
@Slf4j
public class ProjContractInfoController {
    @Resource
    private ProjContractInfoService projContractInfoService;

    /**
     * 项目验收初始化页面信息展示
     *
     * @param file
     * @return
     */
    @ApiOperation("导入合同同步合同信息-工单信息，云资源信息")
    @Log(
            operName = "合同信息", operDetail = "合同信息-导入合同同步合同信息-工单信息，云资源信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "合同信息-导入合同同步合同信息-工单信息，云资源信息"
    )
    @PostMapping("/excelImportSyncProjContractInfo")
    Result excelImportSyncProjContractInfo(@RequestParam("file") MultipartFile file) {
        return Result.success(projContractInfoService.excelImportSyncProjContractInfo(file));
    }
}
