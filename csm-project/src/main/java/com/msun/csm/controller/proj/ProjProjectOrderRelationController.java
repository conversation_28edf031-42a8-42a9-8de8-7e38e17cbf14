package com.msun.csm.controller.proj;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-19 05:38:43
 */
@Slf4j
@RestController
@RequestMapping("/projProjectOrderRelation")
@Api(tags = "项目与工单绑定关系表(主要指云服务工单、小硬件工单、耗材工单等)相关接口")
public class ProjProjectOrderRelationController {

}
