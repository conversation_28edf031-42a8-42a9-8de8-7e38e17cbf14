package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.core.springcloud.api.validator.group.SaveGroup;
import com.msun.core.springcloud.api.validator.group.UpdateGroup;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjFormResourceConfigDTO;
import com.msun.csm.model.dto.ProjFormResourceConfigListDTO;
import com.msun.csm.model.dto.ProjFormResourceConfigPageDTO;
import com.msun.csm.model.vo.ProjFormResourceConfigVO;
import com.msun.csm.service.proj.ProjFormResourceConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 项目表单资源配置表(ProjFormResourceConfig)控制器
 *
 * <AUTHOR>
 * @since 2024-05-20 13:44:38
 */

@RestController
@Api(tags = "项目表单资源配置表")
@RequestMapping("/proj_form_resource_config")
public class ProjFormResourceConfigController {

    @Resource
    private ProjFormResourceConfigService projFormResourceConfigService;

    /**
     * 获取产品信息列表
     *
     * @param dto
     * @return
     */
    @ApiOperation("分页查询所有项目表单资源配置表")
    @Log(operName = "项目表单资源配置表", operDetail = "项目表单资源配置表-分页查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "项目表单资源配置表-分页查询")
    @PostMapping("/findByPage")
    Result<PageInfo<ProjFormResourceConfigVO>> findByPage(@RequestBody ProjFormResourceConfigPageDTO dto) {
        //先初始化接口
        projFormResourceConfigService.initialization(dto.getProjectInfoId());
        return projFormResourceConfigService.findByPage(dto);
    }

    /**
     * 获取产品信息列表
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询所有项目表单资源配置表")
    @Log(operName = "项目表单资源配置表", operDetail = "项目表单资源配置表-查询项目表单资源配置表", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "项目表单资源配置表-查询项目表单资源配置表")
    @PostMapping("/findAll")
    Result<List<ProjFormResourceConfigVO>> findAll(@RequestBody ProjFormResourceConfigPageDTO dto) {
        return projFormResourceConfigService.findAll(dto);
    }

    /**
     * 保存
     *
     * @param dto 数据传输对象
     */
    @ApiOperation("保存")
    @PostMapping("/save")
    public Result<String> save(@ApiParam(value = "信息", example = "信息", required = true) @Validated(SaveGroup.class) @RequestBody ProjFormResourceConfigDTO dto) {
        return this.projFormResourceConfigService.save(dto);
    }

    /**
     * 更新
     *
     * @param dto
     */
    @ApiOperation("更新")
    @PostMapping("/update")
    public Result<String> update(@ApiParam(value = "信息", example = "信息", required = true) @Validated(UpdateGroup.class) @RequestBody ProjFormResourceConfigDTO dto) {
        return this.projFormResourceConfigService.update(dto);
    }

    /**
     * 说明: 批量修改报表状态
     *
     * @param configList
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/20 19:31
     * @remark: Copyright
     */
    @ApiOperation("批量修改报表状态")
    @PostMapping("/batchUpdateById")
    public Result<String> batchUpdateById(@Validated @RequestBody ProjFormResourceConfigListDTO configList) {
        return this.projFormResourceConfigService.batchUpdateById(configList);
    }

    /**
     * 作废
     *
     * @param id 作废业务ID
     */
    @ApiOperation("作废")
    @GetMapping("/updateMcInvalid")
    public Result<String> updateMcInvalid(@ApiParam(value = "id业务主键", example = "id业务主键", required = true) Long id) {
        return this.projFormResourceConfigService.updateMcInvalid(id);
    }

    /**
     * 根据id查询信息
     *
     * @param id id
     * @return vo 对象
     */
    @ApiOperation("根据id查询信息")
    @GetMapping("/getProjFormResourceConfigById")
    public ProjFormResourceConfigVO getProjFormResourceConfigById(@ApiParam(value = "id", example = "id", required = true) Long id) {
        return this.projFormResourceConfigService.getProjFormResourceConfigById(id);
    }
}
