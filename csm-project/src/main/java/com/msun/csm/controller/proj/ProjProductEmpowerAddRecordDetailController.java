package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.service.proj.ProjProductEmpowerAddRecordDetailService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-09-27 10:20:26
 */
@Slf4j
@RestController
@RequestMapping("/projProductEmpowerAddRecordDetail")
@Api(tags = "产品授权补录-明细表相关接口")
public class ProjProductEmpowerAddRecordDetailController {

    @Resource
    private ProjProductEmpowerAddRecordDetailService projProductEmpowerAddRecordDetailService;

}
