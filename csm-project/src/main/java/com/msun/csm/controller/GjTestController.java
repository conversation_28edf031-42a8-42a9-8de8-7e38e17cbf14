package com.msun.csm.controller;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewMapper;
import com.msun.csm.model.req.project.GenerateEarlyWarningAndPenaltyMessageArgs;
import com.msun.csm.service.message.SendBusinessMessageService;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/10 18:21
 */
@Slf4j
@RestController
@RequestMapping("/gj")
@Api(tags = "gj测试")
public class GjTestController {

    @Autowired
    private SendBusinessMessageService service;
    @Autowired
    private ConfigProjectReviewMapper configProjectReviewMapper;

    @GetMapping("/generatePenaltyEndTime")
    @ACROSS
    public Result<?> generatePenaltyEndTime(@RequestParam("cfgId") Long cfgId, @RequestParam("time") String time) {
        ConfigProjectReview cfg = configProjectReviewMapper.selectById(cfgId);
        Date endTime = service.generatePenaltyEndTime(cfg, DateUtil.parse(time, "yyyy-MM-dd HH:mm:ss"), true);
        List<Date> warningTime = service.generateWarningTime(cfg, endTime, true);
        JSONObject obj = new JSONObject();
        obj.put("time", time);
        obj.put("penaltyTime", DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"));
        obj.put("warningTime", warningTime);
        return Result.success(obj);
    }

    @ApiOperation("生成罚单消息")
    @PostMapping("/generateEarlyWarningAndPenaltyMessage")
    @ACROSS
    public Result<?> generateEarlyWarningAndPenaltyMessage(@RequestBody GenerateEarlyWarningAndPenaltyMessageArgs args) {
        return Result.success(service.generateEarlyWarningAndPenaltyMessage(args));
    }
}
