package com.msun.csm.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewMapper;
import com.msun.csm.service.message.SendBusinessMessageService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/10 18:21
 */
@Slf4j
@RestController
@RequestMapping("/gj")
@Api(tags = "gj测试")
public class GjTestController {

    @Autowired
    private SendBusinessMessageService service;
    @Autowired
    private ConfigProjectReviewMapper configProjectReviewMapper;

    @GetMapping("/generatePenaltyEndTime")
    @ACROSS
    public Result<?> generatePenaltyEndTime(@RequestParam("cfgId") Long cfgId, @RequestParam("time") String time) {
        ConfigProjectReview cfg = configProjectReviewMapper.selectById(cfgId);
        Date endTime = service.generatePenaltyEndTime(cfg, DateUtil.parse(time, "yyyy-MM-dd HH:mm:ss"));
        List<Date> warningTime = service.generateWarningTime(cfg, endTime);
        JSONObject obj = new JSONObject();
        obj.put("time", time);
        obj.put("penaltyTime", DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"));
        obj.put("warningTime", warningTime);
        return Result.success(obj);
    }
}
