package com.msun.csm.controller.config;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.service.config.ConfigTipDetailService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-09-25 04:06:00
 */
@Slf4j
@RestController
@RequestMapping("/configTipDetail")
@Api(tags = "提示配置明细相关接口")
public class ConfigTipDetailController {

    @Resource
    private ConfigTipDetailService configTipDetailService;

}
