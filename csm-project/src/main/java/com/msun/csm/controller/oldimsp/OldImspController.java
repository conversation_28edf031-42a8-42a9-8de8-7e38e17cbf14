package com.msun.csm.controller.oldimsp;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.project.TransOldProjectReq;
import com.msun.csm.service.oldimsp.OldImspService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/6/18
 */
@Api(tags = "老项目关联接口")
@RestController
@RequestMapping("/oldImsp")
public class OldImspController {

    @Resource
    private OldImspService oldImspService;

    /**
     * 导数据接口
     *
     * @return
     */
    @ApiOperation("迁移数据")
    @PostMapping("/transDatas")
    public Result transDatas(@RequestBody @Valid TransOldProjectReq req) {
        return oldImspService.transDatas(req);
    }


    /**
     * 处理合同客户
     *
     * @return
     */
    @ApiOperation("处理合同客户")
    @PostMapping("/dealContractCustomer")
    public Result dealContractCustomer() {
        return oldImspService.dealContractCustomer();
    }


    /**
     * 处理医院地理区划信息
     *
     * @return
     */
    @ApiOperation("处理医院地理区划信息")
    @PostMapping("/dealHospitalGeographic")
    public Result dealHospitalGeographic() {
        return oldImspService.dealHospitalGeographic();
    }

    /**
     * 处理合同对用的方案分公司数据
     *
     * @return
     */
    @ApiOperation("处理合同对用的方案分公司数据")
    @PostMapping("/dealContractPreSaleId")
    public Result dealContractPreSaleId() {
        return oldImspService.dealContractPreSaleId();
    }


    /**
     * 处理老平台医院数据
     *
     * @return
     */
    @ApiOperation("处理老平台医院数据")
    @PostMapping("/dealCommCustomerInfo")
    public Result dealCommCustomerInfo() {
        return oldImspService.dealCommCustomerInfo();
    }

    /**
     * 修复里程碑时间
     *
     * @return
     */
    @ApiOperation("修复里程碑时间")
    @PostMapping("/fixMilestoneTime")
    public Result fixMilestoneTime(@RequestBody List<Long> ids) {
        return oldImspService.fixMilestoneTime(ids);
    }

    /**
     * 修复里程碑时间
     *
     * @return
     */
    @ApiOperation("修复老平台字典初始化")
    @PostMapping("/fixAddDictData")
    public Result fixAddDictData(@RequestBody List<Long> ids) {
        return oldImspService.fixAddDictData(ids);
    }
}
