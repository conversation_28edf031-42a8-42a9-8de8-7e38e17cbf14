package com.msun.csm.controller.config;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.config.ConfigProjResearch;
import com.msun.csm.service.config.ConfigProjResearchService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * <AUTHOR>
 * @since 2024-05-10 02:23:49
 */

@RestController
@RequestMapping("/configProjResearch")
@Api(tags = "调研计划内容配置相关接口")
public class ConfigProjResearchController {

    @Resource
    private ConfigProjResearchService configProjResearchService;

    @Log(operName = "调研内容配置查询, 结果返回list", operDetail = "调研内容配置查询",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "调研内容配置查询")
    @ApiOperation("调研内容配置查询")
    @GetMapping(value = "/selectProjSearchConfigList")
    Result<List<ConfigProjResearch>> selectProjSearchConfigList() {
        return configProjResearchService.selectProjSearchConfigListResult();
    }

}
