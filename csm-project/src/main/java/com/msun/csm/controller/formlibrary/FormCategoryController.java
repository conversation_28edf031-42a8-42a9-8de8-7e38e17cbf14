package com.msun.csm.controller.formlibrary;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.oas.FormCategoryFindDTO;
import com.msun.csm.service.oas.FormCategoryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2024/12/20 16:38
 */
@Api(value = "表单类型API")
@RestController
@RequestMapping(value = "/form/category")
public class FormCategoryController {

    @Resource
    private FormCategoryService formCategoryService;

    /**
     * 查询表单类型数据
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("查询表单类型数据")
    @PostMapping("/findSystem")
    public Result<?> findSystemProcess(@RequestBody @Valid FormCategoryFindDTO dto) {
        return formCategoryService.findSystemProcess(dto);
    }
}
