package com.msun.csm.controller.api.yunying;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.common.model.YyProductId;
import com.msun.csm.controller.yunying.YunyingApi;
import com.msun.csm.dao.entity.proj.ProjInterfaceRecordLog;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.mapper.dict.DictProductVsEmpowerMapper;
import com.msun.csm.dao.mapper.proj.ProjInterfaceRecordLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.model.imsp.ChangeCustomTeamDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamResultDTO;
import com.msun.csm.model.imsp.SyncYunInfoDTO;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.yunying.ApproveLogCallbackArgs;
import com.msun.csm.model.yunying.ConvertContractDTO;
import com.msun.csm.model.yunying.HospitalInfoDTO;
import com.msun.csm.model.yunying.HospitalReminderResult;
import com.msun.csm.model.yunying.HospitalReminderSwapDTO;
import com.msun.csm.model.yunying.ReplaceOrderProductDTO;
import com.msun.csm.model.yunying.SyncContractDTO;
import com.msun.csm.model.yunying.SyncDisasterRecoveryCheckDTO;
import com.msun.csm.model.yunying.SyncMidOrderAuditDTO;
import com.msun.csm.model.yunying.SyncRiskAuditDTO;
import com.msun.csm.model.yunying.SyncYunAuditDto;
import com.msun.csm.model.yunying.ThirdInterfaceForYunyingDTO;
import com.msun.csm.model.yunying.UpdateCustomInfoDTO;
import com.msun.csm.model.yunying.YunyingPaysignageReq;
import com.msun.csm.model.yunying.resp.HospitalInfo;
import com.msun.csm.service.api.ApiYunyingService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckService;
import com.msun.csm.service.proj.disastercloud.ProjDisasterRecoveryApplyService;
import com.msun.csm.service.proj.entry.exception.SettlementEntryException;
import com.msun.csm.service.yunying.YunYingService;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/22
 */
@Api(tags = "第三方调用接口-运营平台调用接口")
@RestController
@Slf4j
public class YunyingApiImpl implements YunyingApi {

    @Resource
    private ApiYunyingService apiYunyingService;

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;

    @Resource
    private YunYingService yunYingService;

    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;

    @Resource
    private ProjInterfaceRecordLogMapper projInterfaceRecordLogMapper;
    @Resource
    private ProjDisasterRecoveryApplyService disasterRecoveryApplyService;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private DictProductVsEmpowerMapper dictProductVsEmpowerMapper;

    /**
     * 派工单同步
     *
     * @param syncContractDTO
     * @return
     */
    @Override
    @Log(operName = "派工单同步", operDetail = "运管平台派工单到交付平台", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "运管平台派工单到交付平台", saveParam = true)
    @CsmSign
    public Result syncWorkOrder(SyncContractDTO syncContractDTO) {
        log.info("运管平台派工单到交付平台，params={}", JSON.toJSONString(syncContractDTO));
        return apiYunyingService.syncWorkOrder(syncContractDTO);
    }

    @Log(operName = "设置医院是否到期提醒", operDetail = "设置医院是否到期提醒", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "设置医院是否到期提醒", saveParam = true)
    @CsmSign
    @Override
    public Result<List<HospitalReminderResult>> setHospitalReminder(HospitalReminderSwapDTO hospitalReminderDTO) {
        return Result.fail("已作废");
    }

    /**
     * 部门修改接口
     *
     * @param object
     * @return
     */
    @Override
    @Log(operName = "部门修改接口", operDetail = "运管平台修改部门信息", operLogType = Log.LogOperType.FIX,
            intLogType =
                    Log.IntLogType.OPER_SYSTEM, cnName = "运管平台修改部门信息")
    @CsmSign
    public Result modifyDept(SyncContractDTO object) {
        return Result.success();
    }

    /**
     * 人员修改接口
     *
     * @param syncContractDTO
     * @return
     */
    @Override
    public Result modifyUser(SyncContractDTO syncContractDTO) {
        return null;
    }

    /**
     * 客户修改接口
     *
     * @param syncContractDTO
     * @return
     */
    @Override
    public Result modifyCustomer(SyncContractDTO syncContractDTO) {
        return null;
    }

    /**
     * 同步运营部审核结果
     *
     * @param syncRiskAuditDTO 参数
     * @return Result<String>
     */
    @Log(operName = "同步运营部审核结果", operDetail = "同步运营部审核结果", operLogType = Log.LogOperType.FIX,
            intLogType =
                    Log.IntLogType.OPER_SYSTEM, cnName = "同步运营部审核结果", saveParam = true)
    @CsmSign
    @Override
    public Result<String> syncRiskAudit(SyncRiskAuditDTO syncRiskAuditDTO) {
        try {
            return settlementCheckService.syncRiskAudit(syncRiskAuditDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL,
                    "运营部审核首付款同步接口异常. 审核内容: " + syncRiskAuditDTO);
        }
    }

    @Override
    @CsmSign
    public Result<String> syncDisasterRecoveryCheck(SyncDisasterRecoveryCheckDTO dto) {
        log.info("云容灾运营验收回调参数, param: {}", dto);
        return disasterRecoveryApplyService.syncDisasterRecoveryCheck(dto);
    }


    @CsmSign
    @Log(operName = "运营部审核,云资源开通时间", operDetail = "运营部审核,云资源开通时间",
            operLogType = Log.LogOperType.FIX,
            intLogType =
                    Log.IntLogType.OPER_SYSTEM, cnName = "运营部审核,云资源开通时间", saveParam = true)
    @Override
    public Result<String> syncYunAudit(SyncYunAuditDto dto) {
        try {
            return settlementCheckService.syncYunAudit(dto);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "运营部审核云资源开通时间异常. 审核内容: " + dto);
        }
    }

    /**
     * 风控审核免中间件派工单
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    @Override
    @Log(operName = "风控审核免中间件派工单", operDetail = "风控审核免中间件派工单", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "风控审核免中间件派工单", saveParam = true)
    @CsmSign
    public Result<String> syncMidOrderAudit(SyncMidOrderAuditDTO dto) {
        try {
            return settlementCheckService.syncMidOrderAudit(dto);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "运营部审免中间件服务工单异常. 审核内容: " + dto);
        }
    }


    @Override
    @Log(operName = "云资源发函签回", operDetail = "运管平台派工单到交付平台", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "运管平台派工单到交付平台", saveParam = true)
    @CsmSign
    public Result<String> syncYunLetterDate(SyncYunRenewDTO dto) {
        return Result.fail("已作废");
    }

    /**
     * 接口实施流程修改三方接口状态
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    @Transactional(rollbackFor = Exception.class)
    public Result updateThirdInterface(ThirdInterfaceForYunyingDTO dto) {
        log.info("接口实施流程 修改三方接口状态参数信息, param ================, {}", JSONUtil.toJsonStr(dto));
        List<ProjThirdInterface> projThirdInterfaces = projThirdInterfaceMapper.selectList(
                new QueryWrapper<ProjThirdInterface>()
                        .eq("main_id", dto.getId())
        );
        if (CollectionUtil.isEmpty(projThirdInterfaces)) {
            return Result.fail("未查询到接口信息");
        }
        ProjThirdInterface thirdInterface = projThirdInterfaces.get(0);
        String str = "";
        //推送企业微信消息标识
        Boolean msgBol = false;
        if (dto.getStatus() == 9) {
            str = "接口分公司已接收";
        } else if (dto.getStatus() == 10) {
            str = "接口分公司已完成";
        } else if (dto.getStatus() == 34) {
            msgBol = true;
            if (thirdInterface.getStatus() != 32 && thirdInterface.getStatus() != 36) {
                //status=32正式环境授权通过；status=36验证不通过，只有这两种状态可以再申请验证
                return Result.fail("只允许正式环境授权通过后，提交验证申请！");
            }
            str = "申请验证";
            //只要申请验证时才更新接口状态为34，其他操作都不更新接口状态
            ProjThirdInterface interfaces = new ProjThirdInterface();
            interfaces.setThirdInterfaceId(thirdInterface.getThirdInterfaceId());
            interfaces.setStatus(dto.getStatus());
            log.info("接口实施流程修改三方接口状态：{}", JSON.toJSONString(interfaces));
            projThirdInterfaceMapper.updateById(interfaces);
        } else {
            msgBol = true;
            str = "接口分公司驳回";
            //将接口的责任人和责任团队清空
            projThirdInterfaceMapper.updateInterfaceDir(thirdInterface.getThirdInterfaceId());
        }
        //保存操作日志
        ProjInterfaceRecordLog projInterfaceRecordLog = new ProjInterfaceRecordLog();
        projInterfaceRecordLog.setInterfaceRecordLogId(SnowFlakeUtil.getId());
        projInterfaceRecordLog.setRecordName(str);
        projInterfaceRecordLog.setComments(dto.getComments());
        projInterfaceRecordLog.setThirdInterfaceId(thirdInterface.getThirdInterfaceId());
        projInterfaceRecordLog.setCreaterId(-1L);
        projInterfaceRecordLog.setCreaterName("运营平台");
        projInterfaceRecordLog.setCreateTime(new Date());
        projInterfaceRecordLog.setOperateUserPhone("");
        projInterfaceRecordLogMapper.insert(projInterfaceRecordLog);
        //正式环境，申请验收时，向提出人和项目经理发送消息提醒
        if ("prod".equals(activeProfiles) && msgBol) {
            try {
                MessageParam messageParam = new MessageParam();
                List<Long> sysUserIds = new ArrayList<>();
                // 指定发送人为 提出人和项目经理
                sysUserIds.add(thirdInterface.getCreaterId());
                // 指定项目经理
                ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(thirdInterface.getProjectInfoId());
                sysUserIds.add(projProjectInfo.getProjectLeaderId());
                messageParam.setSysUserIds(sysUserIds);
                messageParam.setMessageToCategory(-1);
                messageParam.setMessageTypeId(3001L);
                messageParam.setProjectInfoId(thirdInterface.getProjectInfoId());
                messageParam.setTitle("三方接口");
                String msgContent = "";
                if (dto.getStatus() == 34) {
                    msgContent = "【" + thirdInterface.getDictInterfaceName() + "】提交了验证申请，请知悉。";
                }
                if (dto.getStatus() == 14) {
                    msgContent = "【" + thirdInterface.getDictInterfaceName() + "】已被接口分公司驳回，请知悉。";
                }
                messageParam.setContent(msgContent);
                log.info("运营平台三方接口，发送企业微信通知提交人与项目经理 , {}", JSONUtil.toJsonStr(messageParam));
                sendMessageService.sendMessage(messageParam, true);
            } catch (Exception e) {
                log.error("运营平台三方接口，发送企业微信通知发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
        return Result.success();
    }

    @Log(operName = "更新云资源台账信息", operDetail = "更新云资源台账信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "更新云资源台账信息", saveParam = true)
    @Override
    @CsmSign
    public Result<String> syncCloudInfo(SyncYunInfoDTO dto) {
        return Result.fail("已作废");
    }

    @Log(operName = "客户团队变更", operDetail = "客户团队变更",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "客户团队变更", saveParam = true)
    @CsmSign
    @Override
    public Result<ChangeCustomTeamResultDTO> changeCustomTeam(ChangeCustomTeamDTO dto) {
        return apiYunyingService.changeCustomTeam(dto);
    }

    /**
     * 合同转正式
     *
     * @param dto 请求参数
     * @return result<String>
     */
    @CsmSign
    @Override
    public Result convertFormalContract(ConvertContractDTO dto) {
        return apiYunyingService.convertFormalContract(dto);
    }

    @CsmSign
    @Override
    public Result<List<HospitalInfo>> findHospitalInfo(HospitalInfoDTO dto) {
        return apiYunyingService.findHospitalInfo(dto);
    }

    /**
     * 首付款是否满足回调-
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    @Override
    @CsmSign
    public Result<String> paySignageIsEnoughFuction(YunyingPaysignageReq dto) {
        return apiYunyingService.paySignageIsEnoughFuction(dto);
    }

    /**
     * 客服特殊事项审批日志回调
     *
     * @param args
     * @return
     */
    @Override
    @CsmSign
    public Result<?> kfSpecialApproveLogCallback(@Valid @RequestBody ApproveLogCallbackArgs args) {
        return apiYunyingService.kfSpecialApproveLogCallback(args);
    }

    /**
     * 获取所有产品授权码对照
     * @param yyProductId
     * @return
     */
    @Override
    @CsmSign
    public Result<?> getAllProductVsEmpowerList(YyProductId args) {
        log.info("获取所有产品授权码对照，参数={}", JSON.toJSONString(args));
        try {
            return Result.success(dictProductVsEmpowerMapper.getAllProductVsEmpowerList(args.getYyProductId()));
        } catch (Exception e) {
            log.error("获取所有产品授权码对照，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }

    /**
     * 手动处理项目产品对照
     *
     * @param projectInfoId
     * @return
     */
    @Log(operName = "手动处理项目产品对照", operDetail = "手动处理项目产品对照",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "手动处理项目产品对照")
    @RequestMapping("/dealProducts")
    public Result dealProducts(@RequestParam Long projectInfoId) {
        log.info("项目信息：{}", projectInfoId);
        return apiYunyingService.dealProducts(projectInfoId);
    }


    /**
     * 云资源、云产品、云订阅 (运营平台调用此接口)
     *
     * @param dto
     * @return
     */
    @Log(operName = "云资源延期", operDetail = "云资源延期",
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "云资源延期", saveParam = true)
    @ApiOperation(value = "云资源延期", notes = "云资源延期")
    @RequestMapping(value = "/syncYunRenew")
    @ResponseBody
    public ResponseData<String> syncYunRenew(@Valid @RequestBody SyncYunRenewDTO dto) {
        //业务处理return apiYunyingService.syncYunRenew(dto);
        return null;
    }

    /**
     * 作废工单
     *
     * @param simpleId 请求参数
     * @return result<String>
     */
    @Override
    @CsmSign
    public Result deleteOrder(SimpleId simpleId) {
        log.info("作废工单, param: {}", JSON.toJSONString(simpleId));
        return apiYunyingService.deleteOrder(simpleId);
    }

    /**
     * 实施地变更
     *
     * @param updateCustomInfoDTO 请求参数
     * @return result
     */
    @Override
    @CsmSign
    public Result updateCustomInfo(UpdateCustomInfoDTO updateCustomInfoDTO) {
        return apiYunyingService.updateCustomInfo(updateCustomInfoDTO);
    }

    /**
     * 置换工单产品
     *
     * @param replaceOrderProductDTO
     * @return result
     */
    @Override
    @CsmSign
    public Result replaceOrderProduct(ReplaceOrderProductDTO replaceOrderProductDTO) {
        return apiYunyingService.replaceOrderProduct(replaceOrderProductDTO);
    }
}
