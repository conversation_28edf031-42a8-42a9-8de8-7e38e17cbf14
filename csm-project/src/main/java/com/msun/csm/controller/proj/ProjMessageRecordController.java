package com.msun.csm.controller.proj;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.OldSystemMessageParamDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.service.message.SendMessageService;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-05-07 11:52:52
 */
@Slf4j
@Api(tags = "消息记录表相关接口")
@RestController
@RequestMapping("/api/projMessageRecord")
@Tag(name = "消息记录表相关接口")
public class ProjMessageRecordController {

    @Resource
    private SendMessageService sendMessageService;

    /**
     * 发送消息
     *
     * @return
     */
    @Log(operName = "新系统内部直接发送消息", operDetail = "新系统内部直接发送消息",
            operLogType = Log.LogOperType.OTHER, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "新系统内部直接发送消息")
    @ApiOperation("新系统内部直接发送消息")
    @PostMapping("/sendMessage")
    public Result<Void> sendMessage(@Validated @RequestBody MessageParam messageParam) {
        log.info("新系统内部直接发送消息，参数={}", JSON.toJSONString(messageParam));
        try {
            sendMessageService.sendMessage(messageParam);
            return Result.success();
        } catch (Exception e) {
            log.error("新系统内部直接发送消息，发生异常，参数={}，errMsg={}，stackInfo=", JSON.toJSONString(messageParam),
                    e.getMessage(), e);
            return Result.fail("发生异常：" + e.getMessage());
        }
    }

    /**
     * 提供给老系统发送消息的接口
     *
     * @return code为0发送成功；其他值发送失败
     */
    @CsmSign
    @Log(operName = "提供给老系统发送消息的接口", operDetail = "提供给老系统发送消息的接口",
            operLogType = Log.LogOperType.OTHER, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "提供给老系统发送消息的接口")
    @ApiOperation("提供给老系统发送消息的接口")
    @PostMapping("/sendMessageForOldSystem")
    public Result<Void> sendMessageForOldSystem(@Validated @RequestBody OldSystemMessageParamDTO paramDTO) {
        log.info("提供给老系统发送消息的接口，参数={}", JSON.toJSONString(paramDTO));
        try {
            sendMessageService.sendMessageForOldSystem(paramDTO);
            return Result.success();
        } catch (Exception e) {
            log.error("提供给老系统发送消息的接口，发生异常，参数={}，errMsg={}，stackInfo=", JSON.toJSONString(paramDTO),
                    e.getMessage(), e);
            return Result.fail("发生异常：" + e.getMessage());
        }
    }

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    ProjOrderProductMapper projOrderProductMapper;

    @PostMapping("/testMessage")
    public Result<Void> testMessage(Long projectInfoId) {
        log.info("消息测试，projectInfoId={}", projectInfoId);
        try {
            // TODO 项目派工发送消息
            ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(
                    new QueryWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId));
            Integer type = projProjectInfo.getUpgradationType();
            String typeName;
            if (Integer.valueOf("1").equals(type)) {
                typeName = "老换新";
            } else {
                typeName = "新客户";
            }
            SysDept sysDept = sysDeptMapper.selectOne(
                    new QueryWrapper<SysDept>().eq("dept_yunying_id", projProjectInfo.getProjectTeamId()));
            SysUser sysUser2 = sysUserMapper.selectOne(
                    new QueryWrapper<SysUser>().eq("sys_user_id", projProjectInfo.getProjectLeaderId()));
            ProductInfoDTO productInfoDTO = new ProductInfoDTO();
            productInfoDTO.setProjectInfoId(projProjectInfo.getProjectInfoId());
            //组装产品列表信息
            List<ProductInfo> allList = projOrderProductMapper.findAllList(productInfoDTO);
            List<ProductInfo> resList = allList.stream()
                    .filter(v -> !com.msun.csm.util.StringUtils.isEmpty(v.getProductName()))
                    .collect(Collectors.toList());
            // xxx项目（老换新）已派工，实施团队xxx，项目经理xxx，本次上线xx个产品，请您知晓！
            String content = projProjectInfo.getProjectName() + "(" + typeName + ")" + "已派工，派工时间"
                    + DateUtil.formatDateTime(new Date()) + "，实施团队" + sysDept.getDeptName() + "，项目经理"
                    + sysUser2.getUserName() + "，本次上线" + resList.size() + "个产品，请您知晓！";
            MessageParam messageParam = new MessageParam();
            messageParam.setProjectInfoId(projectInfoId);
            messageParam.setContent(content);
            messageParam.setMessageTypeId(4002L);
            messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
            sendMessageService.sendMessage(messageParam);
            // 派工消息
            MessageParam messageParam2 = new MessageParam();
            messageParam2.setProjectInfoId(projectInfoId);
            messageParam2.setContent(content);
            messageParam2.setMessageTypeId(2001L);
            messageParam2.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
            sendMessageService.sendMessage(messageParam2);
            return Result.success();
        } catch (Exception e) {
            return Result.fail("发生异常：" + e.getMessage());
        }
    }
}
