package com.msun.csm.controller.basedata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.BasedataConfig;
import com.msun.csm.dao.entity.basedata.BdDictData;
import com.msun.csm.dao.entity.basedata.BdImportPlatformRecord;
import com.msun.csm.dao.entity.basedata.BdTableColInfo;
import com.msun.csm.dao.entity.basedata.BdTableInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.basedata.BdDictDataMapper;
import com.msun.csm.dao.mapper.basedata.BdImportPlatformRecordMapper;
import com.msun.csm.dao.mapper.basedata.BdTableColInfoMapper;
import com.msun.csm.dao.mapper.basedata.BdTableInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.jdbc.Consts;
import com.msun.csm.jdbc.PgsqlConn;
import com.msun.csm.jdbc.PgsqlExecutor;
import com.msun.csm.jdbc.PgsqlGenerator;
import com.msun.csm.jdbc.pojo.DsCfg;
import com.msun.csm.util.ErrUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "数据校验API")
@RestController
@RequestMapping("/dataValid/")
public class DataValidController {

    @Autowired
    private BasedataConfig basedataConfig;
    @Autowired
    private BdTableInfoMapper bdTableInfoMapper;
    @Autowired
    private BdTableColInfoMapper bdTableColInfoMapper;
    @Autowired
    private BdImportPlatformRecordMapper bdImportPlatformRecordMapper;
    @Autowired
    private ProjProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Autowired
    private BdDictDataMapper bdDictDataMapper;

    @ApiOperation(value = "平台数据校验")
    @ACROSS
    @GetMapping(value = "platformValid")
    public Result<?> platformValid(@RequestParam("projectNumber") String projectNumber, @RequestParam(value = "hospitalId", required = false) Long hospitalId, @RequestParam(value = "tableNameEn", required = false) String tableNameEn, @RequestParam(value = "productName", required = false) String productName) {
        List<BdTableInfo> tables = new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                .eq(StrUtil.isNotBlank(tableNameEn), BdTableInfo::getTableNameEn, tableNameEn)
                .eq(StrUtil.isNotBlank(productName), BdTableInfo::getProductName, productName)
                .eq(BdTableInfo::getOpened, true)
                .eq(BdTableInfo::getIsDeleted, 0)
                .list();
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .select(ProjProjectInfo::getCustomInfoId)
                .eq(ProjProjectInfo::getProjectNumber, projectNumber)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        List<ProjHospitalInfo> hospitals = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                .eq(ProjHospitalInfo::getCustomInfoId, proj.getCustomInfoId())
                .eq(hospitalId != null && hospitalId > 0, ProjHospitalInfo::getCloudHospitalId, hospitalId)
                .eq(ProjHospitalInfo::getIsDeleted, 0)
                .list();
        //<表名称：<主键值：错误信息>>
        LinkedHashMap<String, LinkedHashMap<String, String>> errMsgs = new LinkedHashMap<>();
        for (ProjHospitalInfo hos : hospitals) {
            String dataSchema = PinyinUtil.getFirstLetter(hos.getHospitalName(), "") + "_" + projectNumber.toLowerCase();;
            tables.forEach(table -> {
                String tableName = table.getTableNameEn();
                BdImportPlatformRecord importRecord = new LambdaQueryChainWrapper<>(bdImportPlatformRecordMapper)
                        .eq(BdImportPlatformRecord::getProjectNumber, projectNumber)
                        .eq(BdImportPlatformRecord::getHospitalId, hos.getCloudHospitalId())
                        .eq(BdImportPlatformRecord::getTableNameEn, tableName)
                        .eq(BdImportPlatformRecord::getIsDeleted, 0)
                        .one();
                if (importRecord == null) {
                    return;
                }
                //<主键值：错误信息>
                LinkedHashMap<String, String> errMsg = new LinkedHashMap<>();
                List<BdTableColInfo> cols = new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                        .eq(BdTableColInfo::getTableNameEn, table.getTableNameEn())
                        .eq(BdTableColInfo::getOpened, true)
                        .eq(BdTableColInfo::getIsDeleted, 0)
                        .orderByAsc(BdTableColInfo::getId)
                        .list();
                List<String> fields = new ArrayList<>();
                LinkedHashMap<String, BdTableColInfo> colDefMap = new LinkedHashMap<>();
                String pkName = "";
                //<字段名称: [枚举值列表]>
                HashMap<String, Set<String>> enumVals = new HashMap<>();
                for (BdTableColInfo col : cols) {
                    if (Boolean.TRUE.equals(col.getIsPk())) {
                        pkName = col.getTableNameEn();
                    }
                    fields.add(col.getColNameEn());
                    colDefMap.put(col.getColNameEn(), col);
                    //处理枚举值
                    if (StrUtil.isNotBlank(col.getDictCode())) {
                        Set<String> collect = new LambdaQueryChainWrapper<>(bdDictDataMapper)
                                .eq(BdDictData::getDictCode, col.getDictCode())
                                .eq(BdDictData::getIsDeleted, 0)
                                .eq(BdDictData::getOpened, true)
                                .list().stream().map(BdDictData::getDictValue).collect(Collectors.toSet());
                        enumVals.put(col.getColNameEn(), collect);
                    }
                }
                String sql = PgsqlGenerator.generateSelectSql(dataSchema, tableName, fields, projectNumber, hos.getCloudHospitalId(), hos.getOrgId());
                DsCfg dsCfg = new DsCfg();
                dsCfg.setHost(basedataConfig.getDbHost());
                dsCfg.setPort(basedataConfig.getDbPort());
                dsCfg.setUser(basedataConfig.getDbUname());
                dsCfg.setPasswd(basedataConfig.getDbPasswd());
                dsCfg.setDb(basedataConfig.getDbName());
                dsCfg.setSchema(dataSchema);
                dsCfg.setUrl(PgsqlGenerator.buildUrl(dsCfg));
                Connection conn = PgsqlConn.getConn(dsCfg, false);
                try {
                    // 执行 SQL 查询
                    String finalPkName = pkName;
                    PgsqlExecutor.query(conn, sql, 100, subList -> {
                        for (LinkedHashMap<String, Object> obj : subList) {
                            ArrayList<String> colNames = new ArrayList<>(obj.keySet());
                            for (String colName : colNames) {
                                BdTableColInfo colDef = colDefMap.get(colName);
                                Object colVal = obj.get(colName);
                                if (colVal == null) {
                                    if (Boolean.TRUE.equals(colDef.getNotNull())) {
                                        String er = errMsg.getOrDefault(finalPkName, "");
                                        er += StrUtil.format("医院【{}】表【{}】字段{}不能为空；", hos.getHospitalName(), table.getTableNameEn(), colDef.getColNameEn());
                                        errMsg.put(colName, er);
                                        continue;
                                    }
                                }
                                //目前也就字符串类型的数据需要校验
                                if (StrUtil.containsAnyIgnoreCase(colDef.getColType(), "varchar", "text")) {
                                    //判断长度
                                    if (colDef.getColLength() > 0 && String.valueOf(colVal).length() > colDef.getColLength()) {
                                        String er = errMsg.getOrDefault(finalPkName, "");
                                        er += StrUtil.format("医院【{}】表【{}】字段{}长度超过限制；", hos.getHospitalName(), table.getTableNameEn(), colDef.getColNameEn());
                                        errMsg.put(colName, er);
                                        continue;
                                    }
                                    //判断是否唯一
                                    if (Boolean.TRUE.equals(colDef.getIsUnique())) {
                                        String countSql = StrUtil.format("SELECT COUNT(1) FROM {}.{} WHERE {} = '{}' and project_number = '{}'", dataSchema, tableName, colName, colVal, projectNumber);
                                        PgsqlExecutor.query(conn, countSql, 2, sl -> {
                                            for (LinkedHashMap<String, Object> o : sl) {
                                                long count = Long.parseLong(String.valueOf(CollUtil.getFirst(o.values())));
                                                if (count > 1) {
                                                    String er = errMsg.getOrDefault(finalPkName, "");
                                                    er += StrUtil.format("医院【{}】表【{}】字段{}值不唯一；", hos.getHospitalName(), table.getTableNameEn(), colDef.getColNameEn());
                                                    errMsg.put(colName, er);
                                                }
                                            }
                                        });
                                        continue;
                                    }
                                    //判断是否在枚举字段内
                                    if (CollUtil.isNotEmpty(enumVals.get(colName))) {
                                        Set<String> vals = enumVals.get(colName);
                                        if (!vals.contains(String.valueOf(colVal))) {
                                            String er = errMsg.getOrDefault(finalPkName, "");
                                            er += StrUtil.format("医院【{}】表【{}】字段{}值不在枚举值列表内；", hos.getHospitalName(), table.getTableNameEn(), colDef.getColNameEn());
                                            errMsg.put(colName, er);
                                            continue;
                                        }
                                    }
                                }

                            }
                        }
                    });
                } catch (Exception e) {
                    errMsg.put("执行查询数据SQL查询失败", ErrUtils.toSimpleString(e));
                    log.error("执行查询数据SQL查询失败：", e);
                } finally {
                    PgsqlConn.close(conn);
                }

                if (CollUtil.isNotEmpty(errMsg)) {
                    importRecord.setValidStatus(2);
                    importRecord.setValidErrMsg(JSON.toJSONString(errMsg));
                    errMsgs.put(tableName, errMsg);
                } else {
                    importRecord.setValidStatus(1);
                    importRecord.setValidErrMsg("");
                }
                bdImportPlatformRecordMapper.updateById(importRecord);
            });
        }


        if (CollUtil.isNotEmpty(errMsgs)) {
            return Result.fail(errMsgs);
        } else {
            return Result.success("校验成功");
        }
    }
}
