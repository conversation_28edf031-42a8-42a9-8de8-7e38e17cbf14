package com.msun.csm.controller.proj;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjCustomInfoDTO;
import com.msun.csm.model.vo.OldToEquipConfigVO;
import com.msun.csm.model.vo.ProjCustomInfoVO;
import com.msun.csm.schedule.TaskSchedule;
import com.msun.csm.service.proj.ProjCustomInfoService;
import com.msun.csm.service.proj.ProjEquipSummaryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "客户信息管理")
@RestController
@RequestMapping("custominfo")
public class CustomInfoController {
    @Resource
    private ProjCustomInfoService projCustomInfoService;

    @Resource
    private TaskSchedule taskScheduler;

    @Lazy
    @Resource
    private ProjEquipSummaryService projEquipSummaryService;

    @Autowired
    private ProjCustomInfoMapper customInfoMapper;

    @Autowired
    private ProjProjectInfoMapper projectInfoMapper;

    @ApiOperation(value = "获取客户项目的级联选择器选项数据")
    @ACROSS
    @GetMapping(value = "/getCustomProjectCascaderOptions")
    public Result<?> getCustomProjectCascaderOptions() {
        List<ProjCustomInfo> customs = new LambdaQueryChainWrapper<>(customInfoMapper)
                .select(ProjCustomInfo::getCustomName, ProjCustomInfo::getCustomInfoId)
                .eq(ProjCustomInfo::getIsDeleted, 0)
                .orderByDesc(ProjCustomInfo::getCustomInfoId)
                .list();
        List<ProjProjectInfo> projects = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .select(ProjProjectInfo::getProjectName, ProjProjectInfo::getProjectNumber, ProjProjectInfo::getProjectType, ProjProjectInfo::getCustomInfoId, ProjProjectInfo::getHisFlag)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .orderByDesc(ProjProjectInfo::getProjectInfoId)
                .list();
        Map<Long, List<ProjProjectInfo>> projectMap = projects.stream().collect(Collectors.groupingBy(ProjProjectInfo::getCustomInfoId));
        List<LinkedHashMap<String, Object>> options = customs.stream().map(i -> {
            LinkedHashMap<String, Object> obj = new LinkedHashMap<>();
            obj.put("label", i.getCustomName());
            obj.put("value", i.getCustomInfoId());
            List<ProjProjectInfo> projs = projectMap.getOrDefault(i.getCustomInfoId(), new ArrayList<>());
            obj.put("children", projs.stream().map(j -> {
                LinkedHashMap<String, Object> proj = new LinkedHashMap<>();
                proj.put("label", StrUtil.format("{}-{}({}{})", j.getProjectName(), j.getProjectNumber(), Objects.equals(j.getProjectType(), 1) ? "单体" : "区域", Objects.equals(j.getHisFlag(), 1) ? "首期" : "非首期"));
                proj.put("value", j.getProjectNumber());
                return proj;
            }).collect(Collectors.toList()));
            return obj;
        }).collect(Collectors.toList());
        return Result.success(options);
    }

    /**
     * 查询客户信息
     *
     * @return
     */
    @Log(operName = "客户列表查询", operDetail = "客户列表查询（左侧列表）", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "客户列表查询")
    @ApiOperation("接口日志-分页查询")
    @PostMapping(value = "/selectCustomInfoList")
    Result<PageInfo<ProjCustomInfoVO>> selectCustomInfoList(@RequestBody ProjCustomInfoDTO projCustomInfoDTO) {
        return projCustomInfoService.selectCustomInfoList(projCustomInfoDTO);
    }

    /**
     * 查询客户信息
     *
     * @return
     */
    @Log(operName = "客户信息查询(用于下拉列表, 根据用户角色过滤)",
            operDetail = "客户信息查询(用于下拉列表, 根据用户角色过滤)", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "客户信息查询(用于下拉列表, 根据用户角色过滤)")
    @ApiOperation("客户信息查询(用于下拉列表, 根据用户角色过滤)")
    @PostMapping(value = "/findCustomInfoListByQuery")
    Result<List<ProjCustomInfoVO>> findCustomInfoListByQuery() {
        return projCustomInfoService.findCustomInfoListByQuery();
    }

    /**
     * 实施地客户-保存ip和端口
     *
     * @return
     */
    @ApiOperation("实施地客户-保存ip和端口")
    @PostMapping(value = "/saveIpAndPort")
    Result saveIpAndPort(@RequestBody ProjCustomInfoDTO projCustomInfoDTO) {
        return projCustomInfoService.saveIpAndPort(projCustomInfoDTO);
    }


    /**
     * 定时任务手动触发
     *
     * @return
     */
    @Log(operName = "人员信息", operDetail = "人员信息-人员定时",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-人员定时")
    @ApiOperation("人员信息--定时任务手动触发")
    @PostMapping(value = "/userTask")
    public Result userTask() {
        taskScheduler.userTask();
        return Result.success();
    }

    /**
     * 定时任务手动触发
     *
     * @return
     */
    @Log(operName = "同步部署环境节点--定时任务手动触发", operDetail = "同步部署环境节点--定时任务手动触发",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "同步部署环境节点--定时任务手动触发")
    @ApiOperation("同步部署环境节点--定时任务手动触发")
    @PostMapping(value = "/syncEnvInfoTask")
    public Result<String> syncEnvInfoTask() {
        taskScheduler.syncEnvInfoTask(true);
        return Result.success();
    }

    /**
     * 定时任务手动触发
     *
     * @return
     */
    @Log(operName = "部门信息", operDetail = "部门信息-定时部门",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "定时部门")
    @ApiOperation("部门信息--定时任务手动触发")
    @PostMapping(value = "/deptTask")
    public Result deptTask() {
        taskScheduler.deptTask();
        return Result.success();
    }

    /**
     * 定时任务手动触发
     *
     * @return
     */
    @Log(operName = "产品信息", operDetail = "产品信息-定时",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "产品信息-定时")
    @ApiOperation("产品信息--定时任务手动触发")
    @PostMapping(value = "/productsTask")
    public Result productsTask() {
        taskScheduler.productsTask();
        return Result.success();
    }

    /**
     * 定时任务手动触发
     *
     * @return
     */
    @Log(operName = "同步客户基础信息", operDetail = "同步客户基础信息-定时任务手动触发",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "同步客户基础信息-定时任务手动触发")
    @ApiOperation("同步客户基础信息--定时任务手动触发")
    @PostMapping(value = "/customersTask")
    public Result customersTask() {
        taskScheduler.customersTask();
        return Result.success();
    }

    /**
     * 查询客户信息，===== 项目工具
     *
     * @return
     */
    @Log(operName = "查询客户信息，===== 项目工具", operDetail = "查询客户信息，===== 项目工具",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询客户信息，===== 项目工具")
    @ApiOperation("查询客户信息，===== 项目工具")
    @PostMapping(value = "/projectToolsSelectCustomInfoList")
    public Result projectToolsSelectCustomInfoList() {
        return projCustomInfoService.projectToolsSelectCustomInfoList();
    }


    /**
     * 查询老换新ip,端口，下载路径
     *
     * @param customInfoId
     * @return
     */
    @ApiOperation("查询老换新ip,端口，下载路径")
    @Log(operName = "查询老换新ip,端口，下载路径", operDetail = "查询老换新ip,端口，下载路径", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询老换新ip,端口，下载路径")
    @GetMapping(value = "/getOldPlatformToolConfig")
    Result<OldToEquipConfigVO> getOldPlatformToolConfig(@RequestParam("customInfoId") Long customInfoId) {
        return projEquipSummaryService.getOldPlatformToolConfig(customInfoId);
    }
}
