package com.msun.csm.controller.proj;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.model.DictDeductionTypeVO;
import com.msun.csm.model.param.*;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.issue.SaveDeductionClassificationDictType;
import com.msun.csm.model.req.issue.SaveIssueReq;
import com.msun.csm.model.resp.issue.IssueDataResp2;
import com.msun.csm.service.proj.SupervisionCenterService;

/**
 * 监管中心-项目验收
 */
@Slf4j
@RestController
@RequestMapping
public class SupervisionCenterController {

    @Resource
    private SupervisionCenterService supervisionCenterService;

    /**
     * 获取验收评分分类
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/getAcceptanceClassification")
    Result<List<BaseCodeNameResp>> getAcceptanceClassification() {
        try {
            return Result.success(supervisionCenterService.getAcceptanceClassification());
        } catch (Exception e) {
            log.error("获取验收评分分类，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 获取问题分类
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/getIssueClassification")
    Result<List<BaseCodeNameResp>> getIssueClassification(@Validated @RequestBody GetIssueClassificationParam param, BindingResult bindingResult) {
        log.info("获取问题分类，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("获取问题分类，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(supervisionCenterService.getIssueClassification(param));
        } catch (Exception e) {
            log.error("获取问题分类，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 获取扣分项分类
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/getDeductionClassification")
    Result<List<DeductionClassificationVO>> getDeductionClassification(@Validated @RequestBody GetDeductionClassificationParam param, BindingResult bindingResult) {
        log.info("获取扣分项分类，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("获取扣分项分类，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(supervisionCenterService.getDeductionClassification(param));
        } catch (Exception e) {
            log.error("获取扣分项分类，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存问题
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/saveIssue")
    public Result<Void> saveIssue(@RequestBody @Valid SaveIssueReq req) {
        boolean b = supervisionCenterService.saveIssue(req);
        if (b) {
            return Result.success();
        }
        return Result.fail("保存失败");
    }

    /**
     * 查询问题记录
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/queryIssue")
    public Result<List<IssueDataResp2>> queryIssue(@RequestBody @Valid QueryIssueReq req) {
        return supervisionCenterService.queryIssue(req);
    }


    /**
     * 保存问题类型字典
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/saveDeductionClassificationDict")
    public Result<Void> saveDeductionClassificationDict(@RequestBody @Valid SaveDeductionClassificationDictType req) {
        boolean result = supervisionCenterService.saveDeductionClassificationDict(req);
        if (result) {
            return Result.success();
        }
        return Result.fail("保存失败");
    }

    /**
     * 删除问题类型字典
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/deleteDeductionClassificationDict")
    public Result<Void> deleteDeductionClassificationDict(@RequestBody @Valid SimpleId req) {
        boolean result = supervisionCenterService.deleteDeductionClassificationDict(req.getId());
        if (result) {
            return Result.success();
        }
        return Result.fail("删除失败");
    }

    /**
     * 查询问题类型字典列表
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/queryDeductionClassificationDict")
    public Result<List<DictDeductionTypeVO>> queryDeductionClassificationDict(@RequestBody @Valid QueryDeductionClassificationDictParam req) {
        return Result.success(supervisionCenterService.queryDeductionClassificationDict(req));
    }

    /**
     * 查询服务团队类型字典
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/queryServerTypeDict")
    public Result<List<BaseCodeNameResp>> queryServerTypeDict() {
        return Result.success(supervisionCenterService.queryServerTypeDict());
    }

    /**
     * 质管操作：查询后端服务团队的得分记录
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/queryBackendTeamDeductionRecord")
    public Result<List<BackendTeamDeductionRecordVO>> queryBackendTeamDeductionRecord(@RequestBody QueryServerTeamDeductionRecordParam param) {
        return Result.success(supervisionCenterService.queryServerTeamDeductionRecordForBackendTeam(param));
    }

    /**
     * 质管操作：发送扣分记录确认单给后端服务团队进行确认，支持单个发送或批量发送
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/sendConfirmMessage")
    public Result<Void> sendConfirmation(@RequestBody SendConfirmationParam param) {
        boolean result = supervisionCenterService.sendConfirmation(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("发送失败");
    }

    /**
     * 质管操作：对已发送的扣分记录确认单进行撤销操作，将状态回退为未发送
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/revertSendConfirm")
    public Result<Void> revertConfirmation(@RequestBody RevertConfirmationParam param) {
        boolean result = supervisionCenterService.revertConfirmation(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("撤销失败");
    }


    /**
     * 质管和后端服务团队操作：查询后端扣分确认记录明细
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/queryBackendTeamDeductionDetail")
    public Result<BackendTeamDeductionDetailVO> queryBackendTeamDeductionDetail(@RequestBody @Valid QueryBackendDeductionRecordParam param) {
        return Result.success(supervisionCenterService.queryBackendTeamDeductionDetail(param));
    }

    /**
     * 质管操作：作废扣分
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/invalidDeduction")
    public Result<Void> invalidDeduction(@RequestBody @Valid DeductionDetailIdParam param) {
        boolean result = supervisionCenterService.invalidDeduction(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("作废扣分失败");
    }

    /**
     * 质管操作：提交确认
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/submitConfirm")
    public Result<Void> submitConfirm(@RequestBody @Valid DeductionDetailIdParam param) {
        boolean result = supervisionCenterService.submitConfirm(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("提交确认失败");
    }

    /**
     * 质管操作：行编辑扣分
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/changeDeduction")
    public Result<Void> changeDeduction(@RequestBody @Valid ChangeDeductionParam param) {
        boolean result = supervisionCenterService.changeDeduction(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("修改失败");
    }

    /**
     * 后端运维操作：查询后端服务团队扣分记录
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/queryServerTeamDeductionRecord")
    public Result<List<ServerTeamDeductionRecordForBackendTeamVO>> queryServerTeamDeductionRecord(@RequestBody QueryServerTeamDeductionRecordForBackendTeamParam param) {
        return Result.success(supervisionCenterService.queryServerTeamDeductionRecordForBackendTeam(param));
    }

    /**
     * 后端运维操作：后端确认/后端驳回/后端撤销，支持批量操作
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/backendOperation")
    public Result<Void> backendOperation(@RequestBody BackendOperationParam param) {
        boolean result = supervisionCenterService.backendOperation(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("操作失败");
    }

    /**
     * 后端运维操作：驳回或者提交确认单
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/backendConfirmOrReject")
    public Result<Void> backendConfirmOrReject(@RequestBody BackendOperationParam2 param) {
        boolean result = supervisionCenterService.backendConfirmOrReject(param);
        if (result) {
            return Result.success();
        }
        return Result.fail("操作失败");
    }

    /**
     * 校验质管中心是否已经接收验收申请
     */
    @ResponseBody
    @PostMapping(value = "/supervisionCenter/checkAcceptStatus")
    public Result<Void> checkAcceptStatus(@RequestBody ProjectInfoId param) {
        return supervisionCenterService.checkAcceptStatus(param.getProjectInfoId());
    }



}
