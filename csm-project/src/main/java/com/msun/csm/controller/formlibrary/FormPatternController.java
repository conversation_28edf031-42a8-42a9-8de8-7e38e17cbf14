package com.msun.csm.controller.formlibrary;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.oas.FormPatternDeleteDTO;
import com.msun.csm.model.dto.oas.FormPatternFindByPageDTO;
import com.msun.csm.model.dto.oas.FormPatternHandleDataDTO;
import com.msun.csm.model.dto.oas.FormPatternSaveDTO;
import com.msun.csm.model.dto.oas.FormPatternUpdateDTO;
import com.msun.csm.model.dto.oas.ImportDataDTO;
import com.msun.csm.model.dto.oas.form.pattern.FindByIdAndTypeDTO;
import com.msun.csm.model.vo.oas.FormPatternFindByPageVO;
import com.msun.csm.model.vo.oas.FormPatternVO;
import com.msun.csm.service.oas.FormPatternService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2024/12/20 16:38
 */
@Api(value = "表单样式API")
@RestController
@RequestMapping(value = "/form/pattern")
public class FormPatternController {

    @Resource
    private FormPatternService formPatternService;

    /**
     * 新增接口
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("新增接口")
    @PostMapping("/save")
    public Result<?> save(@RequestBody @Valid FormPatternSaveDTO dto) {
        return formPatternService.save(dto);
    }

    /**
     * 删除接口
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("删除接口")
    @PostMapping("/delete")
    public Result<?> delete(@RequestBody @Valid FormPatternDeleteDTO dto) {
        return formPatternService.delete(dto);
    }

    /**
     * 更新接口
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("更新接口")
    @PostMapping("/updateById")
    public Result<?> updateById(@RequestBody @Valid FormPatternUpdateDTO dto) {
        return formPatternService.updateById(dto);
    }

    /**
     * 分页查询接口
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("分页查询接口")
    @PostMapping("/findByPage")
    @ACROSS
    public Result<PageInfo<FormPatternFindByPageVO>> findByPage(@RequestBody @Valid FormPatternFindByPageDTO dto) {
        return formPatternService.findByPage(dto);
    }

    /**
     * 根据主键ID和样式类型查询接口
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("根据主键ID和样式类型查询接口")
    @PostMapping("/findByIdAndType")
    public Result<FormPatternVO> findByIdAndType(@RequestBody @Validated FindByIdAndTypeDTO dto) {
        return formPatternService.findByIdAndType(dto);
    }

    /**
     * 处理资源库样式数据
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("处理资源库样式数据")
    @PostMapping("/handleData")
    @ACROSS
    public Result<?> handleData(@RequestBody @Valid FormPatternHandleDataDTO dto) {
        return formPatternService.handleData(dto);
    }

    /**
     * 导入数据到资源库接口
     * 该接口根据表单类型编码集合formCategoryCode以及医院ID查询表单样式数据
     * 然后根据formCategoryCode进行过滤，找出已经导入过的样式数据，然后先把已经导入过的删除，再全部新增
     *
     * @param dto 参数
     * @return Result
     */
    @ApiOperation("导入接口")
    @PostMapping("/import")
    @ACROSS
    public Result<?> importData(@RequestBody @Valid ImportDataDTO dto) {
        return formPatternService.importData(dto);
    }

}
