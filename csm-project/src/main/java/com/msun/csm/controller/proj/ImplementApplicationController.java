package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.model.param.*;
import com.msun.csm.service.proj.ImplementApplicationService;

/**
 * 监管中心-项目验收
 */
@Api(tags = "监管中心-项目验收-快速实施应用")
@Slf4j
@RestController
@RequestMapping("/implementApplication")
public class ImplementApplicationController {

    @Resource
    private ImplementApplicationService implementApplicationService;

    /**
     * 初始化快速实施应用得分明细
     */
    @PostMapping(value = "/initImplementApplicationPage")
    Result<InitImplementApplicationResultVO> initImplementApplicationPage(@Validated @RequestBody InitImplementApplicationPageParamVO param, BindingResult bindingResult) {
        log.info("初始化快速实施应用得分明细，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("初始化快速实施应用得分明细，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(implementApplicationService.initImplementApplicationPage(param));
        } catch (Exception e) {
            log.error("初始化快速实施应用得分明细，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询快速实施应用的项目文档扣分明细
     */
    @PostMapping(value = "/queryDocumentScoreRecord")
    Result<List<DocumentScoreRecordVO>> queryDocumentScoreRecord(@Validated @RequestBody QueryDocumentScoreRecordParam param, BindingResult bindingResult) {
        log.info("查询快速实施应用的项目文档扣分明细，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询快速实施应用的项目文档扣分明细，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(implementApplicationService.queryDocumentScoreRecord(param));
        } catch (Exception e) {
            log.error("查询快速实施应用的项目文档扣分明细，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 修改快速实施应用项目文档的扣分和扣分说明
     */
    @PostMapping(value = "/saveDocumentDeduction")
    Result<Boolean> saveDocumentDeduction(@Validated @RequestBody SaveDocumentDeductionParam param, BindingResult bindingResult) {
        log.info("修改快速实施应用项目文档的扣分和扣分说明，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("修改快速实施应用项目文档的扣分和扣分说明，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            ProjDeductionDetailDocument projDeductionDetailDocument = implementApplicationService.saveDocumentDeduction(param);
            return Result.success(true);
        } catch (Exception e) {
            log.error("修改快速实施应用项目文档的扣分和扣分说明，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询快速实施应用的项目过程扣分明细
     */
    @PostMapping(value = "/queryProcessScoreRecord")
    Result<List<ProcessScoreRecordVO>> queryProcessScoreRecord(@Validated @RequestBody QueryProcessScoreRecordParam param, BindingResult bindingResult) {
        log.info("查询快速实施应用的项目过程扣分明细，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询快速实施应用的项目过程扣分明细，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(implementApplicationService.queryProcessScoreRecord(param));
        } catch (Exception e) {
            log.error("查询快速实施应用的项目过程扣分明细，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询新增扣分项的扣分类型
     */
    @PostMapping(value = "/queryDeductionClassification")
    Result<AddItemInfoVO> queryDeductionClassification(@Validated @RequestBody QueryDeductionTypeParam param, BindingResult bindingResult) {
        log.info("查询新增扣分项的扣分类型，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询新增扣分项的扣分类型，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(implementApplicationService.queryDeductionClassification(param));
        } catch (Exception e) {
            log.error("查询新增扣分项的扣分类型，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 快速实施应用的项目过程新增扣分项/修改扣分项内容/修改扣分及扣分说明
     */
    @PostMapping(value = "/saveProcessDeduction")
    Result<Boolean> saveProcessDeduction(@Validated @RequestBody SaveProcessDeductionParam param, BindingResult bindingResult) {
        log.info("快速实施应用的项目过程新增扣分项/修改扣分项内容/修改扣分及扣分说明，参数={}", JSON.toJSONString(param));
        return Result.success(true);
//        // 参数校验，拼接校验信息
//        if (bindingResult.hasErrors()) {
//            StringBuilder msg = new StringBuilder("快速实施应用的项目过程新增扣分项/修改扣分项内容/修改扣分及扣分说明，");
//            // 遍历校验结果信息，拼接校验信息到msg中
//            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
//            return new Result<>(702, msg.toString(), null);
//        }
//
//        try {
//            implementApplicationService.saveProcessDeduction(param);
//            return Result.success(true);
//        } catch (Exception e) {
//            log.error("项快速实施应用的项目过程新增扣分项/修改扣分项内容/修改扣分及扣分说明，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
//            return Result.fail(e.getMessage());
//        }
    }

    /**
     * 删除快速实施应用的项目过程的扣分项
     */
    @PostMapping(value = "/deleteProcessDeduction")
    Result<Boolean> deleteProcessDeduction(@Validated @RequestBody DeleteProcessDeductionParam param, BindingResult bindingResult) {
        log.info("删除快速实施应用的项目过程的扣分项，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("删除快速实施应用的项目过程的扣分项，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(implementApplicationService.deleteProcessDeduction(param));
        } catch (Exception e) {
            log.error("删除快速实施应用的项目过程的扣分项，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询快速实施应用的线下培训/设备接口对接/数据统计的扣分明细
     */
    @PostMapping(value = "/queryCommonScoreRecord")
    Result<List<CommonScoreRecordVO>> queryCommonScoreRecord(@Validated @RequestBody QueryProcessScoreRecordParam param, BindingResult bindingResult) {
        log.info("查询快速实施应用的线下培训/设备接口对接/数据统计的扣分明细，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询快速实施应用的线下培训/设备接口对接/数据统计的扣分明细，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(implementApplicationService.queryCommonScoreRecord(param));
        } catch (Exception e) {
            log.error("查询快速实施应用的线下培训/设备接口对接/数据统计的扣分明细，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 快速实施应用的线下培训/设备接口对接/数据统计的新增扣分项/修改扣分项内容/修改扣分及扣分说明
     */
    @PostMapping(value = "/saveCommonDeduction")
    Result<Boolean> saveCommonDeduction(@Validated @RequestBody SaveCommonDeductionParam param, BindingResult bindingResult) {
        log.info("快速实施应用的线下培训/设备接口对接/数据统计的新增扣分项/修改扣分项内容/修改扣分及扣分说明，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("快速实施应用的线下培训/设备接口对接/数据统计的新增扣分项/修改扣分项内容/修改扣分及扣分说明，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            implementApplicationService.saveCommonDeduction(param);
            return Result.success(true);
        } catch (Exception e) {
            log.error("快速实施应用的线下培训/设备接口对接/数据统计的新增扣分项/修改扣分项内容/修改扣分及扣分说明，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 删除快速实施应用的线下培训/设备接口对接/数据统计的扣分项
     */
    @PostMapping(value = "/deleteCommonDeduction")
    Result<Boolean> deleteCommonDeduction(@Validated @RequestBody DeleteCommonDeductionParam param, BindingResult bindingResult) {
        log.info("删除快速实施应用的线下培训/设备接口对接/数据统计的扣分项，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("删除快速实施应用的线下培训/设备接口对接/数据统计的扣分项，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(implementApplicationService.deleteCommonDeduction(param));
        } catch (Exception e) {
            log.error("删除快速实施应用的线下培训/设备接口对接/数据统计的扣分项，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }


    /**
     * 满意度调查评分初始化
     */
    @PostMapping(value = "/initSatisfactionSurvey")
    Result<InitSatisfactionSurveyResultVO> initSatisfactionSurvey(@Validated @RequestBody InitSatisfactionSurveyParamVO param, BindingResult bindingResult) {
        log.info("满意度调查评分初始化，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("满意度调查评分初始化，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(implementApplicationService.initSatisfactionSurvey(param));
        } catch (Exception e) {
            log.error("满意度调查评分初始化，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询满意度调查的扣分明细
     */
    @PostMapping(value = "/querySatisfactionSurveyScoreRecord")
    Result<List<SatisfactionSurveyScoreRecordVO>> querySatisfactionSurveyScoreRecord(@Validated @RequestBody QuerySatisfactionSurveyScoreRecordParam param, BindingResult bindingResult) {
        log.info("查询满意度调查的扣分明细，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("满意度调查评分初始化，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(implementApplicationService.querySatisfactionSurveyScoreRecord(param));
        } catch (Exception e) {
            log.error("查询满意度调查的扣分明细，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 修改满意度调查的得分记录
     */
    @PostMapping(value = "/saveSatisfactionDeduction")
    Result<Boolean> saveSatisfactionDeduction(@Validated @RequestBody SaveSatisfactionDeductionParam param, BindingResult bindingResult) {
        log.info("修改满意度调查的得分记录，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("修改满意度调查的得分记录，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(implementApplicationService.saveSatisfactionDeduction(param));
        } catch (Exception e) {
            log.error("修改满意度调查的得分记录，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

}
