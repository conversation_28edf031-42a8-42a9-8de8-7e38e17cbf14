package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projectfile.DelUploadFileReq;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.service.proj.ProjProjectFileService;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */
@Api(tags = "项目文件")
@RestController
@RequestMapping("/projectFile")
@Slf4j
public class ProjProjectFileController {

    @Resource
    private ProjProjectFileService projectFileService;


    /**
     * 文件上传
     *
     * @param req 数据传输对象
     */
    @ApiOperation("文件上传")
    @PostMapping("/uploadFile")
    public Result uploadFile(UploadFileReq req, HttpServletRequest request) {
        return projectFileService.uploadFile(req, request);
    }
    /**
     * 删除项目文件
     *
     * @param req 数据传输对象
     */
    @ApiOperation ("删除项目文件")
    @PostMapping ("/delUploadFile")
    public Result delUploadFile(@RequestBody DelUploadFileReq req) {
        return projectFileService.delUploadFile(req);
    }

    /**
     * 文件上传带验证逻辑
     *
     * @param req 数据传输对象
     */
    @ApiOperation("文件上传带验证逻辑")
    @PostMapping("/uploadFileWithCheck")
    public Result uploadFileWithCheck(UploadFileReq req, HttpServletRequest request) {
        return projectFileService.uploadFileWithCheck(req, request);
    }
}
