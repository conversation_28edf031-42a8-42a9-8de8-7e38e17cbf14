package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ExamImportStepDTO;
import com.msun.csm.model.dto.FindExamImportStepDTO;
import com.msun.csm.model.dto.ProjExamVsDeptDTO;
import com.msun.csm.model.dto.ProjHospitalInfoExamDTO;
import com.msun.csm.model.vo.ExamHospitalVO;
import com.msun.csm.model.vo.ExamOnlineDocVO;
import com.msun.csm.model.vo.ProjExamHospitalStepVO;
import com.msun.csm.model.vo.ProjExamVsDeptVO;
import com.msun.csm.service.proj.ProjExamService;

@Api(tags = "客户培训")
@RestController
@RequestMapping("/exam")
public class ProjExamController {

    @Resource
    private ProjExamService projExamService;

    /**
     * 查询客户下医院申请培训环境情况
     * @param examDTO
     * @return
     */
    @ApiOperation("查询客户下医院申请培训环境情况")
    @PostMapping("/findApplyExamEnvironment")
    Result<ExamHospitalVO> findApplyExamEnvironment(@RequestBody ProjHospitalInfoExamDTO examDTO) {
        return projExamService.findApplyExamEnvironment(examDTO);
    }

    /**
     * 一键申请培训环境
     * @param dto
     * @return
     */
    @ApiOperation("一键申请培训环境")
    @PostMapping("/applyExamEnvironment")
    Result applyExamEnvironment(@RequestBody ProjHospitalInfoExamDTO dto) {
        return projExamService.applyExamEnvironment(dto);
    }

    /**
     * 获取在线文档路径
     * @return
     */
    @ApiOperation("获取在线文档路径")
    @GetMapping("/getOnlineDoc")
    Result<ExamOnlineDocVO> getOnlineDoc() {
        return projExamService.getOnlineDoc();
    }

    /**
     * 查询医院导数据状态
     * @param dto
     * @return
     */
    @ApiOperation("查询医院导数据状态")
    @PostMapping("/findExamHospitalStepList")
    Result<List<ProjExamHospitalStepVO>> findExamHospitalStepList(@RequestBody FindExamImportStepDTO dto) {
        return projExamService.findExamHospitalStepList(dto);
    }

    /**
     * 查询科室对照
     * @param examHospitalInfoId
     * @return
     */
    @ApiOperation("查询科室对照")
    @GetMapping("/findExamVsDept")
    Result<ProjExamVsDeptVO> findExamVsDept(@Validated @RequestParam("examHospitalInfoId") Long examHospitalInfoId) {
        return projExamService.findExamVsDept(examHospitalInfoId);
    }

    /**
     * 保存科室对照
     * @param dto
     * @return
     */
    @ApiOperation("保存科室对照")
    @PostMapping("saveExamVsDept")
    Result saveExamVsDept(@RequestBody ProjExamVsDeptDTO dto) {
        return projExamService.saveExamVsDept(dto);
    }

    /**
     * 执行SQL脚本
     * @param dto
     * @return
     */
    @ApiOperation("执行SQL脚本")
    @PostMapping("/doSqltext")
    Result doSqltext(@RequestBody ExamImportStepDTO dto) {
        return projExamService.doSqltext(dto);
    }

    /**
     * 执行SQL脚本-Test
     * @param dto
     * @return
     */
    @ApiOperation("执行SQL脚本-Test")
    @PostMapping("/doTestSqltext")
    Result doTestSqltext(@RequestBody ExamImportStepDTO dto) {
        return projExamService.doTestSqltext(dto);
    }

    /**
     * 回收学习环境数据
     * @param dto
     * @return
     */
    @ApiOperation("回收学习环境数据")
    @PostMapping("/recycleExamData")
    Result recycleExamData(@RequestBody ExamImportStepDTO dto) {
        return projExamService.recycleExamData(dto);
    }
}
