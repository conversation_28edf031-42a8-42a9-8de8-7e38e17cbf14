package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.service.proj.ProjMessageRecordPeopleService;

import io.swagger.v3.oas.annotations.tags.Tag;


/**
 * <AUTHOR>
 * @since 2024-05-09 08:45:01
 */

@RestController
@RequestMapping ("/projMessageRecordPeople")
@Tag (name = "消息记录表子表,记录到人相关接口")
public class ProjMessageRecordPeopleController {

    @Resource
    private ProjMessageRecordPeopleService projMessageRecordPeopleService;
}
