package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjMessageRecordPpRelativeDTO;
import com.msun.csm.model.vo.ProjMessageRecordPpRelativeVO;
import com.msun.csm.service.proj.ProjMessageRecordPeopleRelativeService;

import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.tags.Tag;


/**
 * <AUTHOR>
 * @since 2024-05-09 08:45:01
 */

@RestController
@RequestMapping ("/projMessageRecordPpRelative")
@Tag (name = "消息记录表子表,记录到人相关接口")
public class ProjMessageRecordPpRelativeController {

    @Resource
    private ProjMessageRecordPeopleRelativeService recordPeopleRelativeService;

    /**
     * 分页查询操作日志
     *
     * @param dto
     * @return
     */
    @Log (operName = "列表查询", operDetail = "消息日志列表查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "消息日志列表查询")
    @ApiOperation ("消息日志-分页查询")
    @PostMapping (value = "/selectRelativeInfoList")
    Result<PageInfo<ProjMessageRecordPpRelativeVO>> selectRelativeInfoList(
            @RequestBody ProjMessageRecordPpRelativeDTO dto) {
        return recordPeopleRelativeService.selectRelativeInfoList(dto);
    }
}
