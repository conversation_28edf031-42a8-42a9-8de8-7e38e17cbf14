package com.msun.csm.controller.projectreview;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projectreview.DictProjectReviewTypeParam;
import com.msun.csm.model.resp.projectreview.DictProjectReviewTypeResp;
import com.msun.csm.service.config.projectreview.DictProjectReviewTypeService;


/**
 * 项目审核类型字典表(DictProjectReviewType)控制器
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */

@RestController
@Api(tags = "项目审核类型字典表")
@RequestMapping("/dictProjectReviewType")
public class DictProjectReviewTypeController {

    @Resource
    private DictProjectReviewTypeService dictProjectReviewTypeService;

    /**
     * 项目审核类型字典 分页查询
     *
     * @param dto
     */
    @Log(operName = "项目审核类型字典 分页查询", operDetail = "项目审核类型字典 分页查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核类型字典 分页查询")
    @ApiOperation("项目审核类型字典 分页查询")
    @PostMapping(value = "/findDataPage")
    Result<PageInfo<DictProjectReviewTypeResp>> findDataPage(@RequestBody DictProjectReviewTypeParam dto) {
        return dictProjectReviewTypeService.findDataPage(dto);
    }

    /**
     * 项目审核类型字典保存修改
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目审核类型字典保存修改", operDetail = "项目审核类型字典保存", intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核类型字典保存")
    @ApiOperation("项目审核类型字典保存修改")
    @PostMapping(value = "/saveData")
    Result<String> saveData(@RequestBody DictProjectReviewTypeParam dto) {
        return dictProjectReviewTypeService.saveData(dto);
    }

    /**
     * 项目审核类型字典删除
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目审核类型字典删除", operDetail = "项目审核类型字典删除", intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核类型字典删除")
    @ApiOperation("项目审核类型字典删除")
    @PostMapping(value = "/delData")
    Result<String> delData(@RequestBody DictProjectReviewTypeParam dto) {
        return dictProjectReviewTypeService.delData(dto);
    }

}
