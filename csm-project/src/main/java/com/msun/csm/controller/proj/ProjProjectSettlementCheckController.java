package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckApplyEnterDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckProgressDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaveDTO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckApplyEnterVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckProcessVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckProgressVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.proj.ProjProjectSettlementCheckService;
import com.msun.csm.service.proj.entry.exception.SettlementEntryException;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */
@Slf4j
@RestController
@RequestMapping("/projProjectSettlementCheck")
@Api(tags = "入驻申请-项目入驻审核表相关接口-审核")
public class ProjProjectSettlementCheckController {

    @Resource
    private ProjProjectSettlementCheckService checkService;

    @Resource
    private UserHelper userHelper;

    @Log(operName = "入驻审核更新", operDetail = "入驻审核更新", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "入驻审核更新", saveParam = true)
    @PostMapping("saveSettlement")
    @ApiOperation("入驻审核更新")
    public Result<Boolean> saveSettlement(@RequestBody ProjProjectSettlementCheckSaveDTO settlementRuleSaveDTO) {
        try {
            // 重置审核用户
            settlementRuleSaveDTO.setUserId(StrUtil.toString(userHelper.getCurrentUser().getSysUserId()));
            return checkService.saveSettlement(settlementRuleSaveDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "入驻审核异常",
                    Long.parseLong(settlementRuleSaveDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "获取进度", operDetail = "获取进度", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取进度")
    @ApiOperation("获取进度")
    @PostMapping("findCurrentCheckNode")
    public Result<List<ProjProjectSettlementCheckProgressVO>> findCurrentCheckNode(@RequestBody ProjProjectSettlementCheckProgressDTO checkProgressDTO) {
        try {
            return checkService.findCurrentCheckNode(checkProgressDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "获取进度异常",
                    Long.parseLong(checkProgressDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "获取入驻申请详情", operDetail = "获取入驻申请详情(含审核日志流程)", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取入驻申请详情")
    @ApiOperation("获取入驻申请详情")
    @PostMapping("getApplyEnterData")
    public Result<ProjProjectSettlementCheckApplyEnterVO> getApplyEnterData(@RequestBody ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        try {
            return checkService.getApplyEnterData(checkApplyEnterDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "获取入驻申请详情异常",
                    Long.parseLong(checkApplyEnterDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "获取审核环节", operDetail = "获取审核环节", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取审核环节")
    @ApiOperation("获取审核环节")
    @PostMapping("getSettlementCheckProcess")
    public Result<ProjProjectSettlementCheckProcessVO> getSettlementCheckProcess(@RequestBody ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        try {
            return checkService.getSettlementCheckProcess(checkApplyEnterDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "获取审核节点异常",
                    Long.parseLong(checkApplyEnterDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "催办", operDetail = "催办", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "催办")
    @ApiOperation("催办")
    @PostMapping("settlementUrging")
    public Result<String> settlementUrging(@RequestBody ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        try {
            return checkService.settlementUrging(checkApplyEnterDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "催办接口异常",
                    Long.parseLong(checkApplyEnterDTO.getProjectInfoId()));
        }
    }


}
