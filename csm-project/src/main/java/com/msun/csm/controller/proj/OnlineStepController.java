package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.ProjOnlineStepDetailDTO;
import com.msun.csm.model.dto.ProjOnlineStepSendDTO;
import com.msun.csm.model.req.project.ProjectOnlineBeforeReq;
import com.msun.csm.model.vo.ProjOnlineStepSuccessVO;
import com.msun.csm.model.vo.ProjectOnlineToHospitalVO;
import com.msun.csm.service.proj.OnlineStepService;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/22/14:23
 */
@Api(value = "确认上线")
@RestController
@RequestMapping("/onlineStep")
public class OnlineStepController {

    @Resource
    private OnlineStepService onlineStepService;

    /**
     * 查询上线步骤数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("确认上线-查询上线步骤数据")
    @PostMapping("/selectOnlineStepList")
    Result selectOnlineStepList(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.selectOnlineStepList(dto);
    }

    /**
     * 打开页面时调用接口进行保存
     *
     * @param dto
     * @return
     */
    @ApiOperation("确认上线-打开页面时调用接口进行保存")
    @PostMapping("/saveOnlineStep")
    Result saveOnlineStep(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.saveOnlineStep(dto);
    }

    /**
     * 修改上线步骤状态
     *
     * @param dto
     * @return
     */
    @ApiOperation("确认上线-修改上线步骤状态")
    @PostMapping("/updateOnlineStep")
    Result updateOnlineStep(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.updateOnlineStep(dto);
    }

    /**
     * 修改上线步骤状态
     *
     * @param dto
     * @return
     */
    @ApiOperation("确认上线-判断当前步骤是否可执行")
    @PostMapping("/judgeStepExecute")
    Result judgeStepExecute(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.judgeStepExecute(dto);
    }

    /**
     * 测试交付平台连接到云健康接口
     *
     * @param dto
     * @return
     */
    @ApiOperation("确认上线-测试交付平台连接到云健康接口")
    @PostMapping("/testCsmToAPI")
    Result testCsmToAPI(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.testCsmToAPI(dto);
    }

    /**
     * 项目上线--查询医院上线信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目上线--查询医院上线信息")
    @PostMapping("/projectOnlineForShowHospital")
    Result<PageInfo<ProjectOnlineToHospitalVO>> projectOnlineForShowHospital(@RequestBody ProjOnlineStepDetailDTO dto) {
        return onlineStepService.projectOnlineForShowHospital(dto);
    }

    /**
     * 项目上线--根据主键列表 批量更新明细数据
     *
     * @param dtoList
     * @return
     */
    @ApiOperation("项目上线--根据主键列表 批量更新明细数据")
    @PostMapping("/updateProjOnlineStepDetail")
    Result<ProjOnlineStepSuccessVO> updateProjOnlineStepDetail(@RequestBody List<ProjOnlineStepDetailDTO> dtoList) {
        return onlineStepService.updateProjOnlineStepDetail(dtoList);
    }

    /**
     * 说明: 项目上线--批量修改云健康密码
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.ProjOnlineStepSuccessVO>
     * @author: Yhongmin
     * @createAt: 2024/5/24 9:38
     * @remark: Copyright
     */
    @ApiOperation("项目上线--批量修改云健康密码")
    @PostMapping("/changePassword")
    Result<ProjOnlineStepSuccessVO> changePassword(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.changePassword(dto);
    }

    /**
     * 说明: 项目上线--同步宽表数据
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.ProjOnlineStepSuccessVO>
     * @author: Yhongmin
     * @createAt: 2024/5/24 9:38
     * @remark: Copyright
     */
    @ApiOperation("项目上线--同步宽表数据")
    @PostMapping("/syncWidthTableData")
    Result syncWidthTableData(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.syncWidthTableData(dto);
    }

    /**
     * 项目上线-同步宽表数据-查询主日志信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目上线-同步宽表数据-查询主日志信息")
    @PostMapping("/selectLogAllByPage")
    Result<PageInfo> selectLogAllByPage(@RequestBody ProjOnlineStepDetailDTO dto) {
        return onlineStepService.selectLogAllByPage(dto);
    }

    /**
     * 项目上线 -- 单体医院项目上线
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目上线 -- 单体医院项目上线")
    @PostMapping("/projectOnlineForNotArea")
    Result<PageInfo> projectOnlineForNotArea(@RequestBody ProjOnlineStepDTO dto) throws Exception {
        return onlineStepService.projectOnlineForNotArea(dto);
    }

    /**
     * 项目上线 -- 停用患者智能服务老站点
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目上线 -- 停用患者智能服务老站点")
    @Log(operName = "停用患者智能服务老站点", operDetail = "停用患者智能服务老站点", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "停用患者智能服务老站点")
    @PostMapping("/projectOnlineForStopOldSite")
    Result projectOnlineForStopOldSite(@RequestBody ProjOnlineStepDTO dto) throws Exception {
        return onlineStepService.projectOnlineForStopOldSite(dto);
    }

    /**
     * 项目上线 -- 停用患者智能服务老站点
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目上线 -- 患者智能服务发布")
    @Log(operName = "患者智能服务发布", operDetail = "患者智能服务发布", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "患者智能服务发布")
    @PostMapping("/publishPublicNo")
    Result projectOnlineForPublishPublicNo(@RequestBody ProjOnlineStepDTO dto) throws Exception {
        return onlineStepService.projectOnlineForPublishPublicNo(dto);
    }

    /**
     * 说明: 云健康数据库权限下放/收回
     *
     * @param projectInfoId
     * @param type
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/8/1 17:18
     * @remark: Copyright
     */
    @ApiOperation("项目上线 -- 云健康数据库权限下放/收回")
    @Log(operName = "云健康数据库权限下放/收回", operDetail = "云健康数据库权限下放/收回", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.OTHER, cnName = "云健康数据库权限下放/收回")
    @GetMapping("/updateSchemaGrant")
    Result updateSchemaGrant(@RequestParam("projectInfoId") Long projectInfoId,
                             @RequestParam("cloudHospitalIds") List<Long> cloudHospitalIds,
                             @RequestParam("productInfos") List<Long> productInfos,
                             @RequestParam("type") String type) {
        return onlineStepService.updateSchemaGrant(projectInfoId, cloudHospitalIds, productInfos, type);
    }


    /**
     * 说明: 项目上线--处理云健康档案浏览器
     * @param dto
     * @return
     */
    @ApiOperation("项目上线--处理云健康档案浏览器")
    @PostMapping("/saveInitializationData")
    Result saveInitializationData(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.saveInitializationData(dto);
    }

    /**
     * 说明: 项目上线--处理合理用药抽数据
     * @param dto
     * @return
     */
    @ApiOperation("项目上线--处理合理用药抽数据")
    @PostMapping("/fetchReasonableMedicationData")
    Result fetchReasonableMedicationData(@RequestBody ProjOnlineStepDTO dto) {
        return onlineStepService.fetchReasonableMedicationData(dto);
    }

    /**
     * 项目上线-处理合理用药抽数据-查询主日志信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("项目上线-处理合理用药抽数据-查询主日志信息")
    @PostMapping("/selectReasonableMedicationLogAllByPage")
    Result<PageInfo> selectReasonableMedicationLogAllByPage(@RequestBody ProjOnlineStepDetailDTO dto) {
        return onlineStepService.selectReasonableMedicationLogAllByPage(dto);
    }

    /**
     * 说明: 项目上线--清除测试数据消息
     * @param stepDTO
     * @return
     */
    @ApiOperation("项目上线--清除测试数据消息")
    @PostMapping("/clearMessageData")
    Result clearMessageData(@RequestBody ProjOnlineStepDTO stepDTO) {
        if (stepDTO == null || stepDTO.getHospitalInfoIdList() == null || stepDTO.getHospitalInfoIdList().size() == 0) {
            return Result.fail("参数校验失败");
        }
        List<Long> hospitalInfoIdList = stepDTO.getHospitalInfoIdList();
        onlineStepService.clearMessageData(hospitalInfoIdList);
        return Result.success();
    }

    /**
     * 说明: 项目上线--查询上线阶段---上线前准备文件
     * @param stepDTO
     * @return
     */
    @ApiOperation("项目上线--查询上线阶段---上线前准备文件")
    @PostMapping("/selectOnlineBeforeFile")
    Result selectOnlineBeforFile(@RequestBody ProjOnlineStepDTO stepDTO) {
        return Result.success(onlineStepService.selectOnlineBeforFile(stepDTO));
    }

    /**
     * 说明: 项目上线--查询上线阶段---上线前准备ip +host
     * @param stepDTO
     * @return
     */
    @ApiOperation("项目上线--查询上线阶段---上线前准备ip +host")
    @PostMapping("/selectOnlineBeforeIpHost")
    Result selectOnlineBeforeIpHost(@RequestBody ProjOnlineStepDTO stepDTO) {
        return Result.success(onlineStepService.selectOnlineBeforeIpHost(stepDTO));
    }

    /**
     * 说明: 项目上线--查询上线阶段---上线前准备ip +host
     * @param stepDTO
     * @return
     */
    @ApiOperation("项目上线-手动解除30分钟限制")
    @PostMapping("/sendSysManageBatch")
    Result sendSysManageBatch(@RequestBody ProjOnlineStepSendDTO stepDTO) {
        onlineStepService.sendSysManageBatch(stepDTO.getDtoList(), stepDTO.getProjProjectInfo());
        return Result.success();
    }


    /**
     * 说明: 同步云健康用户到运维平台
     * @param stepDTO
     * @return
     */
    @ApiOperation("同步云健康用户到运维平台")
    @PostMapping("/syncSysUserToUnwed")
    Result syncSysUserToUnwed(@RequestBody ProjectOnlineBeforeReq stepDTO) {
        return onlineStepService.syncSysUserToUnwed(stepDTO, null);
    }

    /**
     * 根据客户id查询运维平台的云健康科室信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "根据客户id查询运维平台的云健康科室信息", operDetail = "根据客户id查询运维平台的云健康科室信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据客户id查询运维平台的云健康科室信息")
    @ApiOperation("根据客户id查询运维平台的云健康科室信息")
    @PostMapping(value = "/getUnwedDeptHospitalityInfoId")
    Result<List<BaseIdNameResp>> getUnwedDeptHospitalityInfoId(@RequestBody ProjectOnlineBeforeReq dto) {
        List<BaseIdNameResp> dirUserList = onlineStepService.getUnwedDeptHospitalityInfoId(dto);
        return Result.success(dirUserList);
    }

    /**
     * 根据运营科室id查询团队下实施人员
     *
     * @param dto
     * @return
     */
    @Log(operName = "根据运营科室id查询团队下实施人员", operDetail = "根据运营科室id查询团队下实施人员", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据运营科室id查询团队下实施人员")
    @ApiOperation("根据运营科室id查询团队下实施人员")
    @PostMapping(value = "/getUserListByDeptId")
    Result<List<BaseIdNameResp>> getUserListByDeptId(@RequestBody ProjectOnlineBeforeReq dto) {
        List<BaseIdNameResp> dirUserList = onlineStepService.getUserListByDeptId(dto);
        return Result.success(dirUserList);
    }

    /**
     * 查询运维平台的角色数据
     *
     * @return
     */
    @Log(operName = "查询运维平台的角色数据", operDetail = "查询运维平台的角色数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询运维平台的角色数据")
    @ApiOperation("查询运维平台的角色数据")
    @PostMapping(value = "/getUnwedRoles")
    Result<List<BaseIdNameResp>> getUnwedRoles() {
        List<BaseIdNameResp> dirUserList = onlineStepService.getUnwedRoles();
        return Result.success(dirUserList);
    }


    /**
     * 设置转众阳角色
     *
     * @param dto
     * @return
     */
    @Log(operName = "设置转众阳角色", operDetail = "设置转众阳角色", intLogType = Log.IntLogType.SELF_SYS, cnName = "设置转众阳角色")
    @ApiOperation("设置转众阳角色")
    @PostMapping(value = "/updateUnwedRolesByParam")
    Result<String> updateUnwedRolesByParam(@RequestBody ProjectOnlineBeforeReq dto) {
        return onlineStepService.updateUnwedRolesByParam(dto);
    }


    /**
     * 开启运维
     * 1. 设置客户服务团队及专员
     * 2. 设置转众阳角色
     *
     * @param dto
     * @return
     */
    @Log(operName = "开启运维:设置客户服务团队及专员/设置转众阳角色", operDetail = "开启运维:设置客户服务团队及专员/设置转众阳角色", intLogType = Log.IntLogType.SELF_SYS, cnName = "开启运维:设置客户服务团队及专员/设置转众阳角色")
    @ApiOperation("开启运维:设置客户服务团队及专员/设置转众阳角色")
    @PostMapping(value = "/updateUnwedDataByParam")
    Result<String> updateUnwedDataByParam(@RequestBody ProjectOnlineBeforeReq dto) {
        return onlineStepService.updateUnwedDataByParam(dto);
    }


    /**
     * 上线后发送消息通知
     *
     * @param dto
     * @return
     */
    @Log(operName = "上线后发送消息通知", operDetail = "上线后发送消息通知", intLogType = Log.IntLogType.SELF_SYS, cnName = "上线后发送消息通知")
    @ApiOperation("上线后发送消息通知")
    @PostMapping(value = "/onlineSendMessageInfo")
    Result<String> onlineSendMessageInfo(@RequestBody ProjProjectInfo dto) {
        onlineStepService.sendMessageInfo(dto);
        return Result.success();
    }

}
