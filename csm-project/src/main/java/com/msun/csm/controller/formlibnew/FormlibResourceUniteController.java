package com.msun.csm.controller.formlibnew;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.formlibnew.FormLibResourceUnitePageReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceReferenceReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectOneReq;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUniteSelectOneResp;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUniteUseResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.service.formlibnew.FormlibResourceUniteService;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: zhangdi
 * @Date: 2025/07/14/9:40
 */
@Api(tags = "表单资源库维护")
@RestController
@RequestMapping("/formlibResourceUnite")
public class FormlibResourceUniteController {

    @Resource
    private FormlibResourceUniteService formlibResourceUniteService;


    /**
     * 分页查询所有表单资源库数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "分页查询所有表单资源库数据", operDetail = "分页查询所有表单资源库数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询所有表单资源库数据")
    @ApiOperation("分页查询所有表单资源库数据")
    @PostMapping(value = "/findFormLibDataPage")
    Result<PageInfo<FormlibResourceUnitePageResp>> findDictDataPage(@RequestBody FormLibResourceUnitePageReq dto) {
        return formlibResourceUniteService.findFormLibDataPage(dto);
    }

    /**
     * 表单资源库数据删除
     * @param dto
     * @return
     */
    @Log(operName = "表单资源库数据删除", operDetail = "表单资源库数据删除", operLogType = Log.LogOperType.DEL, intLogType = Log.IntLogType.SELF_SYS, cnName = "表单资源库数据删除")
    @ApiOperation("表单资源库数据删除")
    @PostMapping(value = "/deleteFormLibDataOne")
    Result<String> deleteFormLibDataOne(@RequestBody FormLibResourceUnitePageReq dto) {
        return formlibResourceUniteService.deleteFormLibDataOne(dto);
    }

    /**
     * 表单资源库数据查询
     * @param dto
     * @return
     */
    @Log(operName = "表单资源库数据查询", operDetail = "表单资源库数据查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "表单资源库数据查询")
    @ApiOperation("表单资源库数据查询")
    @PostMapping(value = "/findFormLibDataOne")
    Result<FormlibResourceUniteSelectOneResp> findFormLibDataOne(@RequestBody FormLibResourceUnitePageReq dto) {
        return formlibResourceUniteService.findFormLibDataOne(dto);
    }

    /**
     * 表单入库/维护
     * @param dto
     * @return
     */
    @Log(operName = "表单入库/维护", operDetail = "表单入库/维护", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "表单入库/维护")
    @ApiOperation("表单入库/维护")
    @PostMapping(value = "/formStoreUpdate")
    Result<String> formStoreUpdate(@RequestBody FormlibResourceUniteSelectOneReq dto) {
        return formlibResourceUniteService.formStoreUpdate(dto);
    }

    /**
     * 根据标准名称及分类查询表单数据
     * 用于调研引用资源库， 调研引用时只能引用通用库里的内容
     *
     * @param dto
     * @return
     */
    @Log(operName = "根据标准名称及分类查询表单数据（调研引用查询列表）", operDetail = "根据标准名称及分类查询表单数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "根据标准名称及分类查询表单数据")
    @ApiOperation("根据标准名称及分类查询表单数据")
    @PostMapping(value = "/findFormLibDataList")
    Result<List<FormlibResourceUnitePageResp>> findFormLibDataList(@RequestBody FormlibResourceUniteSelectListReq dto) {
        return formlibResourceUniteService.findFormLibDataList(dto);
    }

    /**
     * 表单引用资源库
     *
     * @param dto
     * @return
     */
    @Log(operName = "表单引用(调研使用)", operDetail = "表单引用资源库", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "表单引用资源库")
    @ApiOperation("表单引用资源库")
    @PostMapping(value = "/saveFormlibReference")
    Result<String> saveFormlibReference(@RequestBody FormlibResourceReferenceReq dto) {
        return formlibResourceUniteService.formlibReference(dto);
    }

    /**
     * 表单资源库拉取数据
     * @param dto
     * @return
     */
    @Log(operName = "表单资源库拉取数据", operDetail = "表单资源库拉取数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "表单资源库拉取数据")
    @ApiOperation("表单资源库拉取数据")
    @PostMapping(value = "/getFormlibByProductId")
    Result<String> getFormlibByProductId(@RequestBody FormlibResourceUniteSelectListReq dto) {
        return formlibResourceUniteService.getFormlibByProductId(dto);
    }

    /**
     * 项目表单导入生产
     * @param dto
     * @return
     */
    @Log(operName = "项目表单导入生产", operDetail = "项目表单导入生产", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目表单导入生产")
    @ApiOperation("项目表单导入生产")
    @PostMapping(value = "/importFormlibToYjk")
    Result<String> importFormlibToYjk(@RequestBody FormlibResourceUniteSelectListReq dto) {
        return formlibResourceUniteService.importFormlibToYjk(dto);
    }

    /**
     * 根据标准名称及分类查询表单数据(准备设计使用)
     * 用于调研引用资源库， 调研引用时只能引用通用库里的内容
     *
     * @param dto
     * @return
     */
    @Log(operName = "根据标准名称及分类查询表单数据（调研引用查询列表）", operDetail = "根据标准名称及分类查询表单数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "根据标准名称及分类查询表单数据")
    @ApiOperation("根据标准名称及分类查询表单数据(准备设计推荐)")
    @PostMapping(value = "/findFormLibDataListToRecommend")
    Result<List<FormlibResourceUnitePageResp>> findFormLibDataListToRecommend(@RequestBody FormlibResourceUniteSelectListReq dto) {
        return formlibResourceUniteService.findFormLibDataListToRecommend(dto);
    }

    /**
     * 根据是否部署跳转中心端/云健康
     *
     * @param dto
     * @return
     */
    @Log(operName = "设计表单(根据部署跳转中心端或云健康)[进行选用及设计跳转]", operDetail = "设计表单", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "设计表单")
    @ApiOperation("设计表单(根据部署跳转中心端或云健康)[引用及新建跳转]")
    @PostMapping(value = "/designFormByParam")
    Result<String> designFormByParam(@RequestBody FormlibResourceReferenceReq dto) {
        return formlibResourceUniteService.designFormByParam(dto);
    }


    /**
     * 资源库跳转中心端进行设计
     *
     * @param dto
     * @return
     */
    @Log(operName = "资源库跳转中心端进行设计", operDetail = "资源库跳转中心端进行设计", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "资源库跳转中心端进行设计")
    @ApiOperation("资源库跳转中心端进行设计")
    @PostMapping(value = "/designFormResLibByParam")
    Result<String> designFormResLibByParam(@RequestBody FormlibResourceReferenceReq dto) {
        return formlibResourceUniteService.designFormResLibByParam(dto);
    }


    /**
     * 根据是否部署跳转中心端/云健康
     *
     * @param dto
     * @return
     */
    @Log(operName = "设计表单(根据部署跳转中心端或云健康)【只用于跳转】", operDetail = "设计表单", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "设计表单")
    @ApiOperation("设计表单(根据部署跳转中心端或云健康)【只用于跳转】")
    @PostMapping(value = "/jumpFormByParam")
    Result<String> jumpFormByParam(@RequestBody FormlibResourceReferenceReq dto) {
        return formlibResourceUniteService.jumpFormByParam(dto);
    }


    /**
     * 准备设计当前应用及推荐列表
     *
     * @param dto
     * @return
     */
    @Log(operName = "准备设计当前应用及推荐列表", operDetail = "准备设计当前应用及推荐列表", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "准备设计当前应用及推荐列表")
    @ApiOperation("准备设计当前应用及推荐列表")
    @PostMapping(value = "/findFormLibDataOneAndListToRecommend")
    Result<FormlibResourceUniteUseResp> findFormLibDataOneAndListToRecommend(@RequestBody FormlibResourceUniteSelectListReq dto) {
        return formlibResourceUniteService.findFormLibDataOneAndListToRecommend(dto);
    }


    /**
     * 根据主键查询表单数据（应用于设计页面提交完成按钮）
     *
     * @param dto
     * @return
     */
    @Log(operName = "根据主键查询表单数据（应用于设计页面提交完成按钮）", operDetail = "根据主键查询表单数据（应用于设计页面提交完成按钮）", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "根据主键查询表单数据（应用于设计页面提交完成按钮）")
    @ApiOperation("根据主键查询表单数据（应用于设计页面提交完成按钮）")
    @PostMapping(value = "/findSurveyFormDataById")
    Result<ProjSurveyFormResp> findSurveyFormDataById(@RequestBody FormlibResourceUniteSelectListReq dto) {
        return formlibResourceUniteService.findSurveyFormDataById(dto);
    }
}
