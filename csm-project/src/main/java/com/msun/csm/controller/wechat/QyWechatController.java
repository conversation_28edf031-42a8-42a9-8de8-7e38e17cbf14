package com.msun.csm.controller.wechat;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.MobileVsUserDTO;
import com.msun.csm.model.resp.qywechat.UrlTokenResp;
import com.msun.csm.service.wechat.IWechatUserService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/28/9:54
 */
@Slf4j
@RestController
@Api (tags = "企业微信")
@RequestMapping ("/qyWeChat")
public class QyWechatController {

    @Resource
    private IWechatUserService wechatUserService;

    /**
     * 企业微信 应用入口
     *
     * @param response
     */
    @RequestMapping ("/msunHealthQy")
    @ResponseBody
    @ACROSS
    public void msunHealthQy(HttpServletResponse response, String state) {
        log.info("企业微信 应用入口配置请求地址 - ");
        try {
            String url = wechatUserService.getWechatCodeUrlQy(state);
            response.sendRedirect(url);
        } catch (IOException e) {
            log.error("企业微信 应用入口配置请求地址 - ，请求错误,{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 企业微信 跳转路径，打开对应页面
     *
     * @param request
     * @param response
     */
    @RequestMapping ("/redirectQy")
    @ResponseBody
    @ACROSS
    public void redirectQy(HttpServletRequest request, HttpServletResponse response) {
        // 获取code
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        log.warn("企业微信重定向进入 -> code = {}, state = {}", code, state);
        try {
            UrlTokenResp urlToken = wechatUserService.redirectQy(code, state);
            // 创建Cookie对象
            Cookie cookie = new Cookie("token", urlToken.getToken());
            // 设置Cookie的路径
            cookie.setPath("/");
            cookie.setDomain("msuncloud.com");
            cookie.setMaxAge(3600);
            // 在响应中添加Cookie
            response.addCookie(cookie);
            response.sendRedirect(urlToken.getUrl());
        } catch (IOException e) {
            log.error("打开菜单失败，{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业微信调用，获取登录人的信息
     *
     */
    @PostMapping ("/getCsmUserInfo")
    @ResponseBody
    public Result getCsmUserInfo() {
        try {
            //token在cookie里面-userHelper统一处理
            return wechatUserService.getCsmUserInfo();
        } catch (Exception e) {
            log.error("获取人员信息失败，{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业微信调用，保存当前登录人的项目信息
     *
     * @param dto
     */
    @PostMapping ("/saveUserChooseProject")
    @ResponseBody
    public Result saveUserChooseProject(@RequestBody MobileVsUserDTO dto) {
        try {
            //token在cookie里面-userHelper统一处理
            return wechatUserService.saveUserChooseProject(dto);
        } catch (Exception e) {
            log.error("保存当前人员信息失败，{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 企业微信 跳转路径，打开对应页面
     *
     * @param request
     * @param response
     */
    @RequestMapping ("/redirectUrl")
    @ResponseBody
    @ACROSS
    public void redirectUrl(HttpServletRequest request, HttpServletResponse response) {
        log.info("企业微信重定向进入");
        // 获取code
        String url = request.getParameter("url");
        log.info("url:{}", url);
        byte[] decodedBytes = Base64.decodeBase64(url);
        String decodedString = new String(decodedBytes);
        log.info("decodedString:{}", decodedString);
        try {
            UrlTokenResp urlToken = wechatUserService.redirectUrl(decodedString);
            response.sendRedirect(urlToken.getUrl());
        } catch (IOException e) {
            log.error("跳转失败，{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
