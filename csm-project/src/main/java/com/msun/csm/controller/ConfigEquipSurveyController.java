package com.msun.csm.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.service.config.ConfigEquipSurveyService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2025-01-09 02:48:56
 */
@Slf4j
@RestController
@RequestMapping("/configEquipSurvey")
@Api(tags = "设备表单配置(会根据产品id, 场景编码, 是否展示查询需要展示的字段)相关接口")
public class ConfigEquipSurveyController {

    @Resource
    private ConfigEquipSurveyService configEquipSurveyService;

}
