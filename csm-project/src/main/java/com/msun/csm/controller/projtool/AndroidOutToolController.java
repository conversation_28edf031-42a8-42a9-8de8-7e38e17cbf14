package com.msun.csm.controller.projtool;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projtool.AndroidOutToolReq;
import com.msun.csm.service.projtool.AndroidOutApiService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目工具使用
 *
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */
@Api(tags = "项目工具使用")
@RestController
@RequestMapping("/androidOutToolController")
@Slf4j
public class AndroidOutToolController {

    @Resource
    private AndroidOutApiService androidOutApiService;

    /**
     * 下载安装包-导出
     * @param androidOutToolReq
     * @param response
     */
    @Log(operName = "下载安装包-导出", operDetail = "下载安装包-导出", operLogType = Log.LogOperType.EXP, intLogType = Log.IntLogType.SELF_SYS, cnName = "下载安装包-导出")
    @ApiOperation("下载安装包-导出")
    @PostMapping("/downloadOfLinux")
    Result<String> downloadOfLinux(@RequestBody AndroidOutToolReq androidOutToolReq, HttpServletResponse response) {
        return androidOutApiService.downloadOfLinux(androidOutToolReq, response);
    }

    /**
     * 打包
     * @param androidOutToolReq
     * @param response
     */
    @Log(operName = "打包", operDetail = "打包", operLogType = Log.LogOperType.EXP, intLogType = Log.IntLogType.SELF_SYS, cnName = "打包")
    @ApiOperation("打包")
    @PostMapping("/reviseHospitalConfiguration")
    Result<String> reviseHospitalConfiguration(@RequestBody AndroidOutToolReq androidOutToolReq, HttpServletResponse response) {
        return androidOutApiService.reviseHospitalConfiguration(androidOutToolReq, response);
    }

    /**
     * 批量下载
     * @param androidOutToolReq
     * @param response
     */
    @Log(operName = "批量下载", operDetail = "批量下载", operLogType = Log.LogOperType.EXP, intLogType = Log.IntLogType.SELF_SYS, cnName = "批量下载")
    @ApiOperation("批量下载")
    @PostMapping("/batchInstallationTool")
    Result<String> batchInstallationTool(@RequestBody AndroidOutToolReq androidOutToolReq, HttpServletResponse response) {
        return androidOutApiService.batchInstallationTool(androidOutToolReq, response);
    }

}
