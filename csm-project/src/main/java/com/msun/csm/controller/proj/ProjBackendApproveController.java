package com.msun.csm.controller.proj;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.enums.ProjectMemberRoleEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.knowledge.BackendTeamInfo;
import com.msun.csm.dao.entity.proj.ImplementationDept;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveNode;
import com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveRecord;
import com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveType;
import com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveTypeUser;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveNodeMapper;
import com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveRecordMapper;
import com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveTypeMapper;
import com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveTypeUserMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.resp.qywechat.UrlTokenResp;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.report.ConfigCustomBackendLimitService;
import com.msun.csm.service.wechat.IWechatUserService;
import com.msun.csm.util.StreamUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;


@Api(tags = "小前端大后端审批流")
@RestController
@RequestMapping("/projFlowableBackend/")
public class ProjBackendApproveController {
    private static final String TITLE = "项目申请实施模式";
    private static final int WAITING = 0; //待审批
    private static final int APPROVING = 1; //审批中
    private static final int PASS = 2; //审批通过
    private static final int TRANSFER = 3; //移交给候选人
    private static final int TURN_PREV = 4; //驳回上一步
    private static final int ABORT = 5; //审批拒绝
    private static final int CANCEL = 6; //撤销

    @Autowired
    private UserHelper userHelper;
    @Autowired
    private ProjBackendApproveRecordMapper projBackendApproveRecordMapper;
    @Autowired
    private ProjBackendApproveNodeMapper projBackendApproveNodeMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private IWechatUserService iWechatUserService;
    @Autowired
    private SendMessageService sendMessageService;
    @Value("${project.current.url}")
    private String mobileUrl;
    @Autowired
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private ProjBackendApproveTypeMapper projBackendApproveTypeMapper;
    @Autowired
    private ProjBackendApproveTypeUserMapper projBackendApproveTypeUserMapper;
    @Autowired
    private ProjProjectMemberMapper projProjectMemberMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Resource
    private ConfigCustomBackendLimitService configCustomBackendLimitService;

    @ApiOperation("获取项目的计划上线时间")
    @GetMapping(value = "getProjectPlanOnlineTime/{projectInfoId}")
    public Result<String> getProjectPlanOnlineTime(@PathVariable("projectInfoId") Long projectInfoId) {
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                .select(ProjProjectInfo::getPlanOnlineTime)
                .eq(ProjProjectInfo::getProjectInfoId, projectInfoId)
                .one();
        if (proj == null || proj.getPlanOnlineTime() == null) {
            return Result.success(DateUtil.now());
        } else {
            return Result.success(DateUtil.format(proj.getPlanOnlineTime(), "yyyy-MM-dd HH:mm:ss"));
        }
    }

    //编辑审批类型
    @ApiOperation("编辑审批类型")
    @PostMapping(value = "editApproveType")
    public Result<?> editApproveType(@RequestBody ProjBackendApproveType approveType) {
        if (approveType.getApproveTypeId() != null && approveType.getApproveTypeId() > 0) {
            approveType.setUpdateTime(new Date());
            approveType.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            projBackendApproveTypeMapper.updateById(approveType);
        } else {
            approveType.setApproveTypeId(IdGenerator.ins().generator());
            approveType.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            approveType.setCreateTime(new Date());
            projBackendApproveTypeMapper.insert(approveType);
        }
        return Result.success();
    }

    //编辑审批类型下面的审批人员
    @ApiOperation("编辑审批类型下面的审批人员")
    @PostMapping(value = "editApproveTypeUser")
    public Result<?> editApproveTypeUser(@RequestBody ProjBackendApproveTypeUser approveTypeUser) {
        if (approveTypeUser.getApproveTypeUserId() != null && approveTypeUser.getApproveTypeUserId() > 0) {
            approveTypeUser.setUpdateTime(new Date());
            approveTypeUser.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            projBackendApproveTypeUserMapper.updateById(approveTypeUser);
        } else {
            approveTypeUser.setApproveTypeUserId(IdGenerator.ins().generator());
            approveTypeUser.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            approveTypeUser.setCreateTime(new Date());
            projBackendApproveTypeUserMapper.insert(approveTypeUser);
        }
        return Result.success();
    }

    //删除审批类型下面的审批人员
    @ApiOperation("删除审批类型下面的审批人员")
    @DeleteMapping(value = "deleteApproveTypeUser/{approveTypeUserId}")
    public Result<?> deleteApproveTypeUser(@PathVariable Long approveTypeUserId) {
        projBackendApproveTypeUserMapper.deleteById(approveTypeUserId);
        return Result.success();
    }

    //获取审批分类列表
    @ApiOperation("获取审批分类列表")
    @GetMapping(value = "getApproveTypeList")
    public Result<List<ProjBackendApproveType>> getApproveTypeList() {
        List<ProjBackendApproveType> list = new LambdaQueryChainWrapper<>(projBackendApproveTypeMapper)
                .eq(ProjBackendApproveType::getIsDeleted, 0)
                .orderByAsc(ProjBackendApproveType::getCreateTime)
                .list();
        return Result.success(list);
    }

    //获取审批分类下的所有用户
    @ApiOperation("获取审批分类下的所有用户")
    @GetMapping(value = "getApproveTypeUserList")
    public Result<List<ProjBackendApproveTypeUser>> getApproveTypeUserList(@RequestParam Long approveRecordId) {
        ProjBackendApproveRecord approveType = new LambdaQueryChainWrapper<>(projBackendApproveRecordMapper)
                .eq(ProjBackendApproveRecord::getIsDeleted, 0)
                .eq(ProjBackendApproveRecord::getApproveRecordId, approveRecordId)
                .one();
        if (approveType == null) {
            throw new CustomException("无效审批类型");
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        if (approveType.getApproveTypeCode().equals("unionImpl")) {
            //联合实施模式
            SysDept dept = new LambdaQueryChainWrapper<>(sysDeptMapper)
                    .eq(SysDept::getDeptYunyingId, currentUser.getDeptId())
                    .one();
            List<ProjBackendApproveTypeUser> users = new LambdaQueryChainWrapper<>(sysUserMapper)
                    .eq(SysUser::getDeptId, currentUser.getDeptId())
                    .eq(SysUser::getIsDeleted, 0)
                    .list().stream().map(item -> {
                        ProjBackendApproveTypeUser user = new ProjBackendApproveTypeUser();
                        user.setApproveTypeUserId(item.getSysUserId());
                        user.setApproveTypeCode(approveType.getApproveTypeCode());
                        user.setSysUserId(item.getSysUserId());
                        user.setDeptYunyingId(item.getDeptId());
                        user.setUserYunyingId(user.getUserYunyingId());
                        user.setAccount(item.getAccount());
                        user.setUserName(item.getUserName());
                        user.setDefaultPicker(dept.getDeptLeaderYunyingId().equals(item.getUserYunyingId()));
                        return user;
                    }).collect(Collectors.toList());
            return Result.success(users);
        } else if (approveType.getApproveTypeCode().equals("frontBackendImpl")) {
            List<ProjBackendApproveTypeUser> users = sysRoleMapper.selectUsersByRoleCodes(Arrays.asList("OperationsManager", "OperationsEngineer")).stream().map(item -> {
                ProjBackendApproveTypeUser user = new ProjBackendApproveTypeUser();
                user.setApproveTypeUserId(item.getSysUserId());
                user.setApproveTypeCode(approveType.getApproveTypeCode());
                user.setSysUserId(item.getSysUserId());
                user.setDeptYunyingId(item.getRoleId());
                user.setUserYunyingId(user.getUserYunyingId());
                user.setAccount(item.getAccount());
                user.setUserName(item.getUserName());
                user.setDefaultPicker(item.getIsLeader());
                return user;
            }).collect(Collectors.toList());
            return Result.success(users);
        } else {
            throw new CustomException("无效审批类型");
        }
    }

    //获取当前用户所属的部门
    @ApiOperation("获取当前用户所属的部门-只有联合实施会用到")
    @GetMapping(value = "getMyDept")
    public Result<SysDept> getMyDept() {
        Long uid = userHelper.getCurrentUser().getSysUserId();
        SysDept dept = sysDeptMapper.getMyDeptByUid(uid);
        return Result.success(dept);
    }

    //获取所有部门选项
    @ApiOperation("获取所有部门选项-只有联合实施会用到")
    @GetMapping(value = "getAllDept")
    public Result<List<SysDept>> getAllDept() {
        List<SysDept> options = sysDeptMapper.selectDeptOptions();
        return Result.success(options);
    }

    @ApiOperation("获取部门下的所有成员")
    @GetMapping(value = "getDeptUsers")
    public Result<List<SysUser>> getDeptUsers(@RequestParam("approveTypeCode") String approveTypeCode, @RequestParam("deptId") Long deptId) {
        List<SysUser> users = new ArrayList<>();
        if (approveTypeCode.equals("unionImpl")) {
            users = new LambdaQueryChainWrapper<>(sysUserMapper)
                    .select(SysUser::getSysUserId, SysUser::getAccount, SysUser::getUserName, SysUser::getUserYunyingId, SysUser::getDeptId)
                    .eq(SysUser::getDeptId, deptId)
                    .eq(SysUser::getIsDeleted, 0)
                    .list();
        } else if (approveTypeCode.equals("frontBackendImpl")) {
            users = sysRoleMapper.selectUsersByRoleCodes(Arrays.asList("OperationsManager", "OperationsEngineer"));
        } else {
            throw new CustomException("无效审批类型");
        }
        return Result.success(users);
    }

    @Data
    public static class NewApproveByTypeArgs {

        /**
         * 项目ID
         */
        private Long projectInfoId;

        /**
         * 审批类型编码
         */
        private String approveTypeCode;

        /**
         * 计划上线时间
         */
        private Date planOnlineTime;

        /**
         * 联合实施时是部门名称拼接
         */
        private String deptNames;

        /**
         * 联合实施以及前后端实施模式的备注
         */
        private String details;

        /**
         * 联合实施的运营平台ID、部门名称、分成比例
         */
        private String formData;

        /**
         * 申请超时时间
         */
        private Date timeout;

        /**
         * 业务服务团队运营平台部门ID
         */
        private Long businessTeamId;

        /**
         * 数据服务团队运营平台部门ID
         */
        private Long dataTeamId;

        /**
         * 接口服务团队运营平台部门ID
         */
        private Long interfaceTeamId;

        /**
         * 勾选的后端服务团队：bustype-业务服务团队；datatype-数据服务团队；interfacetype-接口服务团队
         */
        private List<String> serverType;
    }


    @ApiOperation("通过审批类型发起审批")
    @Transactional(rollbackFor = Throwable.class)
    @PostMapping(value = "newApproveByType")
    public Result<?> newApproveByType(@RequestBody NewApproveByTypeArgs args) {
        // 前后端实施，改为每种服务类型仅允许申请一次
        if ("frontBackendImpl".equals(args.getApproveTypeCode())) {
            // 申请业务服务
            if (args.getServerType().contains(BackendTeamTypeEnum.BUSINESS_TEAM.getCode())) {
                Integer backendApproveRecordServerType = projBackendApproveRecordMapper.getBackendApproveRecordServerType(args.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
                if (backendApproveRecordServerType > 0) {
                    throw new CustomException("该项目已经申请了业务服务，请勿重新申请");
                }
            }

            // 申请数据服务
            if (args.getServerType().contains(BackendTeamTypeEnum.DATA_TEAM.getCode())) {
                Integer backendApproveRecordServerType = projBackendApproveRecordMapper.getBackendApproveRecordServerType(args.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM.getCode());
                if (backendApproveRecordServerType > 0) {
                    throw new CustomException("该项目已经申请了数据服务，请勿重新申请");
                }
            }

            // 申请接口服务
            if (args.getServerType().contains(BackendTeamTypeEnum.INTERFACE_TEAM.getCode())) {
                Integer backendApproveRecordServerType = projBackendApproveRecordMapper.getBackendApproveRecordServerType(args.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
                if (backendApproveRecordServerType > 0) {
                    throw new CustomException("该项目已经申请了接口服务，请勿重新申请");
                }
            }
        }
        // 非前后端实施模式的判断逻辑不变
        if (projBackendApproveRecordMapper.existsApproveTypeCode(args.getProjectInfoId(), args.getApproveTypeCode()) > 0) {
            throw new CustomException(StrUtil.format("该项目已存在审批类型【{}】无法重复申请", args.getApproveTypeCode()));
        }
        // 获取项目信息
        ProjProjectInfo project = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                .eq(ProjProjectInfo::getProjectInfoId, args.getProjectInfoId())
                .one();

        final Date now = new Date();

        ProjBackendApproveRecord record = new ProjBackendApproveRecord();
        record.setApproveRecordId(IdGenerator.ins().generator());
        record.setProjectInfoId(args.getProjectInfoId());
        record.setProjectName(project.getProjectName());
        record.setProjectNumber(project.getProjectNumber());
        record.setApproveTypeId(0L);
        record.setApproveTypeName(StrUtil.equals(args.getApproveTypeCode(), "unionImpl") ? "联合实施" : "前后端实施");
        record.setApproveTypeCode(args.getApproveTypeCode());
        record.setDetails(args.getDetails());
        record.setCompleteStatus(APPROVING);
        record.setCompleted(false);
        record.setPlanOnlineTime(args.getPlanOnlineTime());
        record.setDeptNames(args.getDeptNames());
        record.setFormData(args.getFormData());
        record.setTimeoutAt(args.getTimeout() != null ? args.getTimeout() : new Date(System.currentTimeMillis() + 86400000));
        record.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        record.setCreateTime(now);

        List<ProjBackendApproveNode> nodes = new ArrayList<>();

        // 联合实施
        if (StrUtil.equals(args.getApproveTypeCode(), "unionImpl")) {
            List<ImplementationDept> implementationDeptList = JSON.parseArray(args.getFormData(), ImplementationDept.class);
            List<Long> deptIds = implementationDeptList.stream().map(ImplementationDept::getDeptYunyingId).collect(Collectors.toList());
            List<SysDept> sysDeptList = new LambdaQueryChainWrapper<>(sysDeptMapper)
                    .eq(SysDept::getIsDeleted, 0)
                    .in(SysDept::getDeptYunyingId, deptIds)
                    .list();
            int deptLen = sysDeptList.size();
            if (deptLen != deptIds.size()) {
                throw new CustomException("部分部门已解散，请确认");
            }
            int n = 0;
            Set<Long> deptLeaderSet = new HashSet<>();
            for (int i = 0; i < deptLen; i++) {
                n++;
                SysDept dept = sysDeptList.get(i);
                SysUser leader = new LambdaQueryChainWrapper<>(sysUserMapper)
                        .eq(SysUser::getIsDeleted, 0)
                        .eq(SysUser::getUserYunyingId, dept.getDeptLeaderYunyingId())
                        .one();
                if (leader == null) {
                    throw new CustomException(StrUtil.format("部门【{}】缺少负责人", dept.getDeptName()));
                }
                if (!deptLeaderSet.contains(leader.getSysUserId()) && !leader.getSysUserId().equals(userHelper.getCurrentUser().getSysUserId())) {
                    ProjBackendApproveNode node = new ProjBackendApproveNode();
                    nodes.add(node);
                    node.setApproveNodeId(IdGenerator.ins().generator());
                    node.setProjectInfoId(record.getProjectInfoId());
                    node.setApproveRecordId(record.getApproveRecordId());
                    node.setIndexNo(n);
                    node.setApproveUserId(leader.getSysUserId());
                    node.setApproveUserAccount(leader.getAccount());
                    node.setApproveUserName(StrUtil.format("{} - {}", dept.getDeptName(), leader.getUserName()));
                    node.setLeaderUserId(0L);
                    node.setIsDeleted(0);
                    if (i == 0) {
                        node.setStatus(APPROVING);
                    } else {
                        node.setStatus(WAITING);
                    }
                    node.setDetails("");
                    deptLeaderSet.add(leader.getSysUserId());
                    //将部门经理也加入审批节点里面
                    if (dept.getPid() != null && dept.getPid() > 0) {
                        SysDept parentDept = new LambdaQueryChainWrapper<>(sysDeptMapper)
                                .eq(SysDept::getIsDeleted, 0)
                                .eq(SysDept::getDeptYunyingId, dept.getPid())
                                .one();
                        if (parentDept == null) {
                            continue;
                        }
                        // 董姐说从政总不审批，所以只到部门这一级别
                        if (Integer.valueOf(3).equals(parentDept.getDeptLevel())) {
                            SysUser ld = new LambdaQueryChainWrapper<>(sysUserMapper)
                                    .eq(SysUser::getUserYunyingId, parentDept.getDeptLeaderYunyingId())
                                    .eq(SysUser::getIsDeleted, 0)
                                    .one();
                            if (ld == null) {
                                throw new CustomException("部门【" + parentDept.getDeptName() + "】缺少负责人");
                            }
                            if (!deptLeaderSet.contains(ld.getSysUserId()) && !ld.getSysUserId().equals(userHelper.getCurrentUser().getSysUserId())) {
                                n++;
                                ProjBackendApproveNode ldNode = new ProjBackendApproveNode();
                                nodes.add(ldNode);
                                ldNode.setApproveNodeId(IdGenerator.ins().generator());
                                ldNode.setProjectInfoId(record.getProjectInfoId());
                                ldNode.setApproveRecordId(record.getApproveRecordId());
                                ldNode.setIndexNo(n);
                                ldNode.setApproveUserId(ld.getSysUserId());
                                ldNode.setApproveUserAccount(ld.getAccount());
                                ldNode.setApproveUserName(StrUtil.format("{} - {}", parentDept.getDeptName(), ld.getUserName()));
                                ldNode.setLeaderUserId(0L);
                                ldNode.setStatus(WAITING);
                                ldNode.setDetails("");
                                ldNode.setIsDeleted(0);
                                deptLeaderSet.add(ld.getSysUserId());
                            }
                        }
                    }
                }
            }

            projBackendApproveRecordMapper.insert(record);
            projBackendApproveNodeMapper.insertBatch(nodes);
            //通知第一个审核人去审核
            //激活第一个节点
            String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", record.getProjectName(), record.getApproveTypeName(), record.getDetails());
            String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, record.getProjectInfoId(), record.getApproveRecordId(), CollUtil.getFirst(nodes).getApproveNodeId());
            alertUser(CollUtil.getFirst(nodes).getApproveUserAccount(), TITLE, content, url);
            return Result.success();
        } else if (StrUtil.equals(args.getApproveTypeCode(), "frontBackendImpl")) {
            List<BackendTeamInfo> backendTeamInfoList = new ArrayList<>();
            // 有业务服务
            if (args.getServerType().contains(BackendTeamTypeEnum.BUSINESS_TEAM.getCode())) {
                BackendTeamInfo businessTeamInfo = new BackendTeamInfo();
                businessTeamInfo.setTeamTypeCode(BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
                businessTeamInfo.setYyDeptId(args.getBusinessTeamId());
                backendTeamInfoList.add(businessTeamInfo);
            }
            // 有数据服务
            if (args.getServerType().contains(BackendTeamTypeEnum.DATA_TEAM.getCode())) {
                BackendTeamInfo dataTeamInfo = new BackendTeamInfo();
                dataTeamInfo.setTeamTypeCode(BackendTeamTypeEnum.DATA_TEAM.getCode());
                dataTeamInfo.setYyDeptId(args.getDataTeamId());
                backendTeamInfoList.add(dataTeamInfo);

            }
            // 有接口服务
            if (args.getServerType().contains(BackendTeamTypeEnum.INTERFACE_TEAM.getCode())) {
                BackendTeamInfo interfaceTeamInfo = new BackendTeamInfo();
                interfaceTeamInfo.setTeamTypeCode(BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
                interfaceTeamInfo.setYyDeptId(args.getInterfaceTeamId());
                backendTeamInfoList.add(interfaceTeamInfo);

            }

            for (BackendTeamInfo backendTeamInfo : backendTeamInfoList) {
                Long deptId;
                if (BackendTeamTypeEnum.BUSINESS_TEAM.getCode().equals(backendTeamInfo.getTeamTypeCode())) {
                    deptId = args.getBusinessTeamId();
                } else if (BackendTeamTypeEnum.DATA_TEAM.getCode().equals(backendTeamInfo.getTeamTypeCode())) {
                    deptId = args.getDataTeamId();
                } else {
                    deptId = args.getInterfaceTeamId();
                }
                // 部门信息
                SysDept sysDept = sysDeptMapper.selectYunYingId(deptId);

                // 添加申请记录
                ProjBackendApproveRecord backendRecord = new ProjBackendApproveRecord();
                backendRecord.setApproveRecordId(IdGenerator.ins().generator());
                backendRecord.setProjectInfoId(args.getProjectInfoId());
                backendRecord.setProjectName(project.getProjectName());
                backendRecord.setApproveTypeId(0L);
                backendRecord.setApproveTypeName("前后端实施");
                backendRecord.setDetails(args.getDetails());
                backendRecord.setLastOpNodeId(null);
                backendRecord.setCompleteStatus(APPROVING);
                backendRecord.setCompleted(false);
                backendRecord.setIsDeleted(0);
                backendRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                backendRecord.setCreateTime(now);
                backendRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                backendRecord.setUpdateTime(now);
                backendRecord.setDeptNames(sysDept.getDeptName());
                backendRecord.setPlanOnlineTime(args.getPlanOnlineTime());
                backendRecord.setFormData(args.getFormData());
                backendRecord.setTimeoutAt(args.getTimeout() != null ? args.getTimeout() : new Date(System.currentTimeMillis() + 86400000));
                backendRecord.setProjectNumber(project.getProjectNumber());
                backendRecord.setApproveTypeCode(args.getApproveTypeCode());
                backendRecord.setServerType(backendTeamInfo.getTeamTypeCode());
                backendRecord.setYyBackendTeamId(backendTeamInfo.getYyDeptId());
                projBackendApproveRecordMapper.insert(backendRecord);

                // 部门负责人
                SysUser deptLeader = sysUserMapper.selectUserIdByYungyingId(sysDept.getDeptLeaderYunyingId());
                //找出一个人作为项目负责人
                ProjBackendApproveNode node = new ProjBackendApproveNode();
                node.setApproveNodeId(IdGenerator.ins().generator());
                node.setProjectInfoId(backendRecord.getProjectInfoId());
                node.setApproveRecordId(backendRecord.getApproveRecordId());
                node.setIndexNo(1);
                node.setApproveUserId(deptLeader.getSysUserId());
                node.setLeaderUserId(deptLeader.getSysUserId());
                node.setApproveUserAccount(deptLeader.getAccount());
                node.setApproveUserName(deptLeader.getUserName());
                node.setStatus(APPROVING);
                node.setDetails("");
                node.setApproveAt(null);
                node.setIsDeleted(0);
                node.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                node.setCreateTime(now);
                node.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                node.setUpdateTime(now);

                List<ProjBackendApproveNode> nodeList = new ArrayList<>();
                nodeList.add(node);

                projBackendApproveNodeMapper.insertBatch(nodeList);
                //通知第一个审核人去审核
                //激活第一个节点
                String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", backendRecord.getProjectName(), backendRecord.getApproveTypeName(), backendRecord.getDetails());
                String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, backendRecord.getProjectInfoId(), backendRecord.getApproveRecordId(), CollUtil.getFirst(nodeList).getApproveNodeId());
                alertUser(CollUtil.getFirst(nodeList).getApproveUserAccount(), TITLE, content, url);
            }
            return Result.success();
        }
        return Result.success();
    }

    @ApiOperation("重新发起审批")
    @GetMapping(value = "reNewApprove/{recordId}")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> reNewApprove(@PathVariable("recordId") Long recordId) {
        ProjBackendApproveRecord record = projBackendApproveRecordMapper.getApproveRecordById(recordId);
        if (record.getCompleteStatus() <= PASS) {
            throw new CustomException("审批未完成或者审批通过的审批无法重新发起审批");
        }
        List<ProjBackendApproveNode> nodes = record.getNodes();
        record.setApproveRecordId(IdGenerator.ins().generator());
        record.setLastOpNodeId(0L);
        record.setCompleteStatus(APPROVING);
        record.setCompleted(false);
        record.setCreateTime(new Date());
        record.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        Set<Long> uids = new LambdaQueryChainWrapper<>(projBackendApproveTypeUserMapper)
                .select(ProjBackendApproveTypeUser::getSysUserId)
                .eq(ProjBackendApproveTypeUser::getApproveTypeCode, record.getApproveTypeCode())
                .eq(ProjBackendApproveTypeUser::getIsDeleted, 0)
                .eq(ProjBackendApproveTypeUser::getDefaultPicker, true)
                .orderByAsc(ProjBackendApproveTypeUser::getIndexNo)
                .list().stream()
                .map(ProjBackendApproveTypeUser::getSysUserId)
                .collect(Collectors.toSet());
        List<ProjBackendApproveNode> newNodes = new ArrayList<>();
        int i = 0;
        for (ProjBackendApproveNode node : nodes) {
            if (!uids.contains(node.getApproveUserId())) {
                continue;
            }
            uids.remove(node.getApproveUserId());
            i++;
            node.setApproveNodeId(IdGenerator.ins().generator());
            node.setApproveRecordId(record.getApproveRecordId());
            node.setIndexNo(i);
            if (i == 1) {
                node.setStatus(APPROVING);
            } else {
                node.setStatus(WAITING);
            }
            node.setDetails("");
            node.setApproveAt(null);
            record.setCreateTime(new Date());
            record.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            newNodes.add(node);
        }
        projBackendApproveRecordMapper.insert(record);
        projBackendApproveNodeMapper.insertBatch(newNodes);
        String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", record.getProjectName(), record.getApproveTypeName(), record.getDetails());
        String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, record.getProjectInfoId(), record.getApproveRecordId(), CollUtil.getFirst(nodes).getApproveNodeId());
        alertUser(CollUtil.getFirst(newNodes).getApproveUserAccount(), TITLE, content, url);
        return Result.success();
    }

    @Data
    public static class ApproveArgs {
        private Long approveNodeId; //审批节点ID
        private Integer status; //审批状态
        private Long leaderUserId; //负责人ID
        private Long transferUid; //转交审批人ID
        private String details = ""; //审批备注
    }

    //审批
    @ApiOperation("审批")
    @PostMapping(value = "approve")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> approve(@RequestBody ApproveArgs args) {
        ProjBackendApproveNode node = new LambdaQueryChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getApproveNodeId, args.getApproveNodeId())
                .one();
//        if (!Objects.equals(node.getApproveUserId(), userHelper.getCurrentUser().getSysUserId())) {
//            throw new CustomException("无权审批");
//        }
        ProjBackendApproveRecord record = new LambdaQueryChainWrapper<>(projBackendApproveRecordMapper)
                .eq(ProjBackendApproveRecord::getApproveRecordId, node.getApproveRecordId())
                .one();
        node.setStatus(args.getStatus());
        node.setDetails(args.getDetails());
        node.setApproveAt(new Date());
        if (args.getLeaderUserId() != null && args.getLeaderUserId() > 0) {
            node.setLeaderUserId(args.getLeaderUserId());
        }
        node.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        switch (args.getStatus()) {
            case PASS:
                //审批通过
                onPass(args, record, node);
                break;
            case TRANSFER:
                //审批转交
                onTransfer(args, record, node);
                break;
            case TURN_PREV:
                //驳回上一个节点
                onTurnPrev(args, record, node);
                break;
            case ABORT:
                //审批拒绝
                onAbort(args, record, node);
                break;
            default:
                throw new CustomException("无效审批类型");
        }
        projBackendApproveNodeMapper.updateById(node);
        record.setLastOpNodeId(node.getApproveNodeId());
        record.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        record.setUpdateTime(new Date());
        projBackendApproveRecordMapper.updateById(record);
        return Result.success();
    }

    private void onPass(ApproveArgs args, ProjBackendApproveRecord record, ProjBackendApproveNode node) {
        if (node.getLeaderUserId() != null && node.getLeaderUserId() > 0) {
            //如果设置了项目经理那么就将项目经理设置到每个节点上
            new LambdaUpdateChainWrapper<>(projBackendApproveNodeMapper)
                    .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                    .set(ProjBackendApproveNode::getLeaderUserId, node.getLeaderUserId())
                    .update();
        }
        ProjBackendApproveNode nextNode = new LambdaQueryChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getProjectInfoId, record.getProjectInfoId())
                .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                .gt(ProjBackendApproveNode::getIndexNo, node.getIndexNo())
                .eq(ProjBackendApproveNode::getStatus, WAITING)
                .orderByAsc(ProjBackendApproveNode::getIndexNo)
                .last("limit 1")
                .one();
        if (nextNode == null) {
            //完成审批
            record.setCompleteStatus(args.getStatus());
            record.setCompleted(true);
            ProjProjectInfo project = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                    .eq(ProjProjectInfo::getProjectInfoId, record.getProjectInfoId())
                    .one();
            Set<Long> leaderUids = new LambdaQueryChainWrapper<>(projBackendApproveNodeMapper)
                    .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                    .list().stream().filter(n -> node.getLeaderUserId() != null && node.getLeaderUserId() > 0)
                    .map(ProjBackendApproveNode::getLeaderUserId).collect(Collectors.toSet());
            List<ProjProjectMember> members = new ArrayList<>();
            //开启审批
            if ("unionImpl".equals(record.getApproveTypeCode())) {
                //TODO 需要给质量管理等特定人员发消息


                //把表单配置的团队成员添加到项目成员里面
                String formData = record.getFormData();
                JSONArray deptForms = JSON.parseArray(formData);
                int size = deptForms.size();
                for (int i = 0; i < size; i++) {
                    JSONObject deptForm = deptForms.getJSONObject(i);
                    long deptYunyingId = deptForm.getLongValue("deptYunyingId");
                    String deptName = deptForm.getString("deptName");
//                int proportion = deptForm.getIntValue("proportion");
                    List<SysUser> users = new LambdaQueryChainWrapper<>(sysUserMapper)
                            .eq(SysUser::getDeptId, deptYunyingId)
                            .eq(SysUser::getIsDeleted, 0)
                            .list();

                    for (SysUser user : users) {
                        if (new LambdaQueryChainWrapper<>(projProjectMemberMapper)
                                .eq(ProjProjectMember::getProjectInfoId, record.getProjectInfoId())
                                .eq(ProjProjectMember::getProjectMemberId, user.getSysUserId())
                                .eq(ProjProjectMember::getIsDeleted, 0)
                                .exists()) {
                            continue;
                        }
                        ProjProjectMember member = new ProjProjectMember();
                        member.setProjectMemberInfoId(IdGenerator.ins().generator());
                        member.setProjectInfoId(record.getProjectInfoId());
                        member.setProjectMemberId(user.getSysUserId());
                        member.setProjectMemberName(user.getUserName());
                        member.setProjectTeamId(user.getDeptId());
                        member.setProjectTeamName(deptName);
                        member.setIsDeleted(0);
                        member.setCreateTime(new Date());
                        member.setUpdateTime(new Date());
                        member.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                        member.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                        if (leaderUids.contains(user.getSysUserId())) {
                            //项目经理
                            member.setProjectMemberRoleId((long) ProjectMemberRoleEnums.LEADER.getCode());
                        } else {
                            //项目团队成员
                            member.setProjectMemberRoleId((long) ProjectMemberRoleEnums.MEMBER.getCode());
                        }
                        member.setPhone(user.getPhone());
                        members.add(member);
                    }
                }
                StreamUtil.chunked(members, 100, (subList) -> projProjectMemberMapper.insertBatch(subList));
            } else if ("frontBackendImpl".equals(record.getApproveTypeCode())) {
                // 前后端实施模式
                configCustomBackendLimitService.openBackendServer(project, record);
            }
            //更新计划上线时间
            new LambdaUpdateChainWrapper<>(projProjectInfoMapper)
                    .eq(ProjProjectInfo::getProjectInfoId, record.getProjectInfoId())
                    .set(ProjProjectInfo::getPlanOnlineTime, record.getPlanOnlineTime())
                    .set(ProjProjectInfo::getUpdateTime, new Date())
                    .set(ProjProjectInfo::getUpdaterId, record.getCreaterId())
                    .update();
            //通知发起人
            String content = StrUtil.format("项目【{}】申请【{}】实施模式审批通过！、\n{}", record.getProjectName(), record.getApproveTypeName(), args.getDetails());
            alertUser(sysUserMapper.getUserById(record.getCreaterId()).getAccount(), TITLE, content, null);
        } else {
            //激活下一个人
            nextNode.setStatus(APPROVING);
            nextNode.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            nextNode.setUpdateTime(new Date());
            projBackendApproveNodeMapper.updateById(nextNode);
            //通知下一个审批人
            String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", record.getProjectName(), record.getApproveTypeName(), record.getDetails());
            String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, record.getProjectInfoId(), record.getApproveRecordId(), nextNode.getApproveNodeId());
            alertUser(nextNode.getApproveUserAccount(), TITLE, content, url);
        }
    }

    private ProjBackendApproveNode copyNode(ProjBackendApproveNode node, int indexNo, Long approveUserId, int status) {
        ProjBackendApproveNode newNode = new ProjBackendApproveNode();
        BeanUtils.copyProperties(node, newNode);
        newNode.setApproveNodeId(IdGenerator.ins().generator());
        newNode.setDetails("");
        newNode.setApproveAt(null);
        newNode.setApproveUserId(approveUserId);
        SysUser user = sysUserMapper.getUserById(approveUserId);
        newNode.setApproveUserAccount(user.getAccount());
        newNode.setApproveUserName(user.getUserName());
        newNode.setIndexNo(indexNo);
        newNode.setStatus(status);
        newNode.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        newNode.setUpdaterId(-1L);
        newNode.setUpdateTime(new Date());
        projBackendApproveNodeMapper.insert(newNode);
        return newNode;
    }

    private void onTransfer(ApproveArgs args, ProjBackendApproveRecord record, ProjBackendApproveNode node) {
        //在当前节点后面添加一个新的审批节点 后面的节点索引同步平移一位
        new LambdaUpdateChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getProjectInfoId, record.getProjectInfoId())
                .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                .gt(ProjBackendApproveNode::getIndexNo, node.getIndexNo())
                .setSql("index_no = index_no + 1")
                .update();

        ProjBackendApproveNode newNode = copyNode(node, node.getIndexNo() + 1, args.getTransferUid(), APPROVING);

        //通知新的审批人
        String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", record.getProjectName(), record.getApproveTypeName(), record.getDetails());
        String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, record.getProjectInfoId(), record.getApproveRecordId(), newNode.getApproveNodeId());
        alertUser(newNode.getApproveUserAccount(), TITLE, content, url);
    }

    private void onTurnPrev(ApproveArgs args, ProjBackendApproveRecord record, ProjBackendApproveNode node) {
        //找到上一个审批节点
        ProjBackendApproveNode prevNode = new LambdaQueryChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getProjectInfoId, record.getProjectInfoId())
                .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                .lt(ProjBackendApproveNode::getIndexNo, node.getIndexNo())
                .orderByDesc(ProjBackendApproveNode::getIndexNo)
                .last("limit 1")
                .one();
        if (prevNode == null) {
            throw new CustomException("您是第一个审批人无法驳回，您感觉该审批不合理可以直接拒绝让发起人修正后重新发起");
        }

        //在当前后面添加上一个审批人  然后再添加一个当前审批人
        new LambdaUpdateChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getProjectInfoId, record.getProjectInfoId())
                .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                .gt(ProjBackendApproveNode::getIndexNo, node.getIndexNo())
                .setSql("index_no = index_no + 2")
                .update();

        //将上一个审批节点加入当前节点后面
        ProjBackendApproveNode newNode = copyNode(prevNode, node.getIndexNo() + 1, prevNode.getApproveUserId(), APPROVING);

        //再把当前节点复制一个加入队列
        copyNode(node, node.getIndexNo() + 2, node.getApproveUserId(), WAITING);

        //通知新的审批人
        String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", record.getProjectName(), record.getApproveTypeName(), record.getDetails());
        String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, record.getProjectInfoId(), record.getApproveRecordId(), newNode.getApproveNodeId());
        alertUser(newNode.getApproveUserAccount(), TITLE, content, url);
    }

    private void onAbort(ApproveArgs args, ProjBackendApproveRecord record, ProjBackendApproveNode node) {
        //拒绝审批，直接将审批流结束
        new LambdaUpdateChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getApproveRecordId, record.getApproveRecordId())
                .gt(ProjBackendApproveNode::getIndexNo, node.getIndexNo())
                .set(ProjBackendApproveNode::getStatus, CANCEL)
                .update();
        record.setCompleteStatus(args.getStatus());
        record.setCompleted(true);
        String content = StrUtil.format("项目【{}】申请【{}】实施模式审批未通过！\n{}", record.getProjectName(), record.getApproveTypeName(), args.getDetails());
        alertUser(sysUserMapper.getUserById(record.getCreaterId()).getAccount(), TITLE, content, null);
    }


    private void alertUser(String account, String title, String content, String url) {
        try {
            String redirectUrl = null;
            if (StrUtil.isNotBlank(url)) {
                UrlTokenResp urlTokenResp = iWechatUserService.redirectUrl(url);
                redirectUrl = urlTokenResp.getUrl();
            }
            sendMessageService.sendEnterpriseWeChatMessageForOnePeople(title, content, Collections.singletonList(account), redirectUrl, null);
        } catch (Throwable e) {
            throw new CustomException("调用运营平台发送企业微信消息报错了：" + e.getMessage(), e);
        }
    }

    //撤销审批
    @ApiOperation("撤销审批")
    @DeleteMapping(value = "cancel/{recordId}")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> cancel(@PathVariable("recordId") Long recordId) {
        ProjBackendApproveRecord record = new LambdaQueryChainWrapper<>(projBackendApproveRecordMapper)
                .eq(ProjBackendApproveRecord::getApproveRecordId, recordId)
                .one();
        if (!Objects.equals(record.getCreaterId(), userHelper.getCurrentUser().getSysUserId())) {
            throw new CustomException("无权操作");
        }
        new LambdaUpdateChainWrapper<>(projBackendApproveRecordMapper)
                .eq(ProjBackendApproveRecord::getApproveRecordId, recordId)
                .set(ProjBackendApproveRecord::getCompleteStatus, CANCEL)
                .set(ProjBackendApproveRecord::getCompleted, true)
                .set(ProjBackendApproveRecord::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .set(ProjBackendApproveRecord::getUpdateTime, new Date())
                .update();

        ProjBackendApproveNode node = new LambdaQueryChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getApproveRecordId, recordId)
                .eq(ProjBackendApproveNode::getStatus, APPROVING)
                .last("limit 1")
                .one();

        new LambdaUpdateChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getApproveRecordId, recordId)
                .le(ProjBackendApproveNode::getStatus, APPROVING)
                .set(ProjBackendApproveNode::getStatus, CANCEL)
                .set(ProjBackendApproveNode::getUpdaterId, userHelper.getCurrentUser().getSysUserId())
                .set(ProjBackendApproveNode::getUpdateTime, new Date())
                .update();

        if (node != null) {
            //通知当前审批人，审批已撤销
            String content = StrUtil.format("项目【{}】申请【{}】实施模式已撤销！", record.getProjectName(), record.getApproveTypeName());
            alertUser(node.getApproveUserAccount(), TITLE, content, null);
        }
        return Result.success();
    }

    //获取历史审批记录
    @ApiOperation("获取历史审批记录")
    @GetMapping(value = "getHistoryByProjectId")
    public Result<IPage<ProjBackendApproveRecord>> getHistoryByProjectInfoId(
            @RequestParam("projectInfoId") Long projectInfoId,
            @RequestParam("pageNo") Integer pageNo,
            @RequestParam("pageSize") Integer pageSize
    ) {
        IPage<ProjBackendApproveRecord> page = projBackendApproveRecordMapper.getHistoryByProjectInfoId(new Page<>(pageNo, pageSize, true), projectInfoId);
        List<ProjBackendApproveRecord> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().forEach(item -> {
                if ("frontBackendImpl".equals(item.getApproveTypeCode())) {
                    if (StringUtils.isNotBlank(item.getLeaderName())) {
                        item.setDeptNames(item.getDeptNames() + "—" + item.getLeaderName());
                    }
                }
            });
        }
        return Result.success(page);
    }

    @ApiOperation("根据ID获取审批记录")
    @GetMapping(value = "getApproveRecordById")
    public Result<ProjBackendApproveRecord> getApproveRecordById(@RequestParam("id") Long id) {
        return Result.success(projBackendApproveRecordMapper.getApproveRecordById(id));
    }

    //督办
    @ApiOperation("督办")
    @GetMapping(value = "alert")
    public Result<?> alert(@RequestParam("recordId") Long recordId) {
        ProjBackendApproveRecord record = new LambdaQueryChainWrapper<>(projBackendApproveRecordMapper)
                .eq(ProjBackendApproveRecord::getApproveRecordId, recordId)
                .one();
        ProjBackendApproveNode node = new LambdaQueryChainWrapper<>(projBackendApproveNodeMapper)
                .eq(ProjBackendApproveNode::getApproveRecordId, recordId)
                .eq(ProjBackendApproveNode::getStatus, APPROVING)
                .last("limit 1")
                .one();
        ProjProjectInfo project = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                .eq(ProjProjectInfo::getProjectInfoId, record.getProjectInfoId())
                .one();
        String content = StrUtil.format("项目【{}】申请【{}】实施模式。\n{}", project.getProjectName(), record.getApproveTypeName(), record.getDetails());
        String url = StrUtil.format("{}backendApprove?projectInfoId={}&approveId={}&nodeId={}", mobileUrl, record.getProjectInfoId(), record.getApproveRecordId(), node.getApproveNodeId());
        alertUser(node.getApproveUserAccount(), TITLE, content, url);

        return Result.success();
    }
}
