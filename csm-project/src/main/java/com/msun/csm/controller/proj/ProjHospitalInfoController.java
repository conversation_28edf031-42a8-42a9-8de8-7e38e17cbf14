package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.feign.entity.oldimsp.req.CopySurveyReq;
import com.msun.csm.model.dto.FindHospitalInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoPageDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.SelectSourceHospitalDTO;
import com.msun.csm.model.dto.SysFileDTO;
import com.msun.csm.model.req.ProjCustomVsProjectTypeReq;
import com.msun.csm.model.resp.ProjHospitalInfoSelectPageResp;
import com.msun.csm.model.vo.HospitalPlanVO;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderProductService;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/24/16:25
 */
@Api(tags = "医院信息")
@RestController
@RequestMapping("/hospitalInfo")
public class ProjHospitalInfoController {

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;

    /**
     * 医院信息-根据客户信息查询, 不分页
     *
     * @param customerId
     */
    @Log(
            operName = "医院信息-根据客户信息查询, 不分页", operDetail = "医院信息-根据客户信息查询, 不分页",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-根据客户信息查询, 不分页"
    )
    @ApiOperation("医院信息-根据客户信息查询, 不分页")
    @GetMapping(value = "/findHospitalInfoByCustomerId")
    Result<List<ProjHospitalInfoVO>> findHospitalInfoByCustomerId(@RequestParam("customerId") String customerId) {
        return Result.success(hospitalInfoService.getHospitalInfoByCustomeInfoId(Long.valueOf(customerId)));
    }

    /**
     * 医院信息-分页查询
     *
     * @param dto
     */
    @Log(
            operName = "分页查询", operDetail = "医院信息-分页查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-根据客户信息查询"
    )
    @ApiOperation("医院信息-分页查询")
    @PostMapping(value = "/findHospitalInfoPage")
    Result<ProjHospitalInfoSelectPageResp<ProjHospitalInfoVO>> findHospitalInfoPage(@RequestBody ProjHospitalInfoPageDTO dto) {
        return hospitalInfoService.findHospitalInfoPage(dto);
    }

    /**
     * 医院信息-查询单个医院信息
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-查询单个医院信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-查询单个医院信息"
    )
    @ApiOperation("医院信息-查询单个医院信息")
    @PostMapping(value = "/selectHospitalInfo")
    Result<ProjHospitalInfoVO> selectHospitalInfo(@RequestBody ProjHospitalInfoDTO dto) {
        return hospitalInfoService.selectHospitalInfo(dto);
    }

    /**
     * 根据客户与项目id查询医院信息列
     *
     * @param customInfoId
     * @param projectId
     * @return
     */
    @Log(
            operName = "医院信息查询-根据客户与项目id查询医院信息列",
            operDetail = "医院信息-根据客户与项目id查询医院信息列", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-根据客户与项目id查询医院信息列"
    )
    @ApiOperation("医院信息-根据客户与项目id查询医院信息列")
    @GetMapping(value = "/getHospitalInfoByProjIdAndCustomerId")
    Result<List<ProjHospitalInfoVO>> findHospitalInfoByProjIdAndCustomerId(
            @RequestParam("customInfoId") String customInfoId,
            @RequestParam("projectId") String projectId) {
        return hospitalInfoService.findHospitalInfoByProjIdAndCustomerId(customInfoId, projectId);
    }

    /**
     * 医院信息-删除医院
     *
     * @param hospitalId
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-删除医院", operLogType = Log.LogOperType.DEL,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-删除医院"
    )
    @ApiOperation("医院信息-删除医院")
    @GetMapping(value = "/deleteByHospitalId")
    Result deleteByHospitalId(Long hospitalId) {
        return hospitalInfoService.deleteByHospitalId(hospitalId);
    }

    /**
     * 医院信息-修改医院信息
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-修改医院信息", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-修改医院信息"
    )
    @ApiOperation("医院信息-修改医院信息")
    @PostMapping(value = "/updateHospitalInfo")
    Result updateHospitalInfo(@RequestBody ProjHospitalInfoDTO dto) {
        return hospitalInfoService.updateHospitalInfo(dto);
    }

    /**
     * 医院信息-添加医院信息
     *
     * @param list
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-添加医院信息", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-添加医院信息"
    )
    @ApiOperation("医院信息-添加医院信息")
    @PostMapping(value = "/hospitalInfoBatchInsert")
    Result hospitalInfoBatchInsert(@RequestBody List<ProjHospitalInfoDTO> list) {
        return hospitalInfoService.batchInsert(list);
    }

    /**
     * 医院信息-批量申请开通
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-批量申请开通", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-批量申请开通"
    )
    @ACROSS
    @ApiOperation("医院信息-批量申请开通")
    @PostMapping("/applyToOpenOnlineHospitalList")
    Result<String> applyToOpenOnlineHospitalList(@RequestBody ProjHospitalInfoDTO dto) {
        Result<Boolean> canOpenResult = applyOrderProductService.canOpenHospital(dto.getProjectInfoId());
        if (!canOpenResult.isSuccess() || !canOpenResult.getData()) {
            return Result.fail(canOpenResult.getMsg());
        }
        return applyOrderHospitalService.batchOpenHospital(dto);
    }

    /**
     * 医院信息-导入
     *
     * @param dto
     * @param customInfoId
     * @param projectType
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-导入", operLogType = Log.LogOperType.OTHER,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-导入"
    )
    @ApiOperation("医院信息-导入")
    @PostMapping("/excelImportHospitalInfo")
    Result excelImportHospitalInfo(@RequestParam("file") MultipartFile dto,
                                   @RequestParam("customInfoId") Long customInfoId,
                                   @RequestParam("projectType") Integer projectType) {
        return hospitalInfoService.excelImportHospitalInfo(dto, customInfoId, projectType);
    }

    /**
     * 医院信息-导出
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "医院信息", operDetail = "医院信息-导出", operLogType = Log.LogOperType.EXP,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-导出"
    )
    @ApiOperation("医院信息-导出")
    @PostMapping("/hospitalExportExcel")
    void hospitalExportExcel(HttpServletResponse response, @RequestBody ProjHospitalInfoDTO dto) {
        hospitalInfoService.hospitalExportExcel(response, dto);
    }

    /**
     * 产品业务调研，查询条件下拉
     *
     * @param selectHospitalDTO
     * @return
     */
    @Log(
            operName = "产品业务调研，查询条件下拉", operDetail = "产品业务调研，查询条件下拉",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "产品业务调研，查询条件下拉"
    )
    @ApiOperation("产品业务调研，查询条件下拉")
    @PostMapping("/findHospitalInfoList")
    Result<List<BaseIdNameResp>> findHospitalInfoList(@RequestBody SelectHospitalDTO selectHospitalDTO) {
       /* if (ObjectUtil.isEmpty(selectHospitalDTO.getProjectInfoId())) {
            throw new CustomException(ResultEnum.VALIDATE_NULL_OR_EMPTY);
        }*/
        return hospitalInfoService.findHospitalBaseList(selectHospitalDTO);
    }

    /**
     * 产品业务调研，按照卫生院查询列表数据
     *
     * @param selectHospitalDTO
     * @return
     */
    @Log(
            operName = "产品业务调研，按照卫生院查询列表数据", operDetail = "产品业务调研，按照卫生院查询列表数据",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "产品业务调研，按照卫生院查询列表数据"
    )
    @ApiOperation("产品业务调研，按照卫生院查询列表数据")
    @PostMapping("/findHospitalPlan")
    Result<List<HospitalPlanVO>> findHospitalPlan(@RequestBody SelectHospitalDTO selectHospitalDTO) {
        if (ObjectUtil.isEmpty(selectHospitalDTO.getProjectInfoId())
                || ObjectUtil.isEmpty(selectHospitalDTO.getStage())) {
            throw new CustomException(ResultEnum.VALIDATE_NULL_OR_EMPTY);
        }
        return hospitalInfoService.findHospitalPlan(selectHospitalDTO);
    }

    /**
     * 产品业务调研，复制查询源卫生院
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "产品业务调研，复制查询源卫生院", operDetail = "产品业务调研，复制查询源卫生院",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "产品业务调研，复制查询源卫生院"
    )
    @ApiOperation("产品业务调研，复制查询源卫生院")
    @PostMapping("/findSourceHospitals")
    Result<List<BaseIdNameResp>> findSourceHospitals(@RequestBody SelectSourceHospitalDTO dto) {
        if (ObjectUtil.isEmpty(dto.getProjectInfoId())) {
            throw new CustomException(ResultEnum.VALIDATE_NULL_OR_EMPTY);
        }
        return hospitalInfoService.findSourceHospitals(dto);
    }


    /**
     * 产品业务调研，复制调研内容
     *
     * @param copySurveyReq
     * @return
     */
    @Log(
            operName = "产品业务调研，复制调研内容", operDetail = "产品业务调研，复制调研内容",
            operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "产品业务调研，复制调研内容"
    )
    @ApiOperation("产品业务调研，复制调研内容")
    @PostMapping("/copySurvey")
    Result copySurvey(@RequestBody CopySurveyReq copySurveyReq) {
        return hospitalInfoService.copySurvey(copySurveyReq);
    }

    /**
     * 根据项目id查询医院信息
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "根据项目id查询医院信息", operDetail = "根据项目id查询医院信息",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "根据项目id查询医院信息"
    )
    @ApiOperation("产品业务调研，复制调研内容")
    @PostMapping("/getHospitalInfoByProjectId")
    List<ProjHospitalInfo> getHospitalInfoByProjectId(SelectHospitalDTO dto) {
        return hospitalInfoService.getHospitalInfoByProjectId(dto);
    }

    /**
     * 下载医院模版
     *
     * @param projectInfoId
     * @param fileCode
     * @param response
     */
    @Log(operName = "下载医院模版", operDetail = "下载医院模版", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "下载医院模版")
    @ApiOperation("下载医院模版")
    @GetMapping(value = "/downloadhospitalInfoTemplate")
    void downloadhospitalInfoTemplate(Long projectInfoId, String fileCode, HttpServletResponse response) {
        SysFileDTO dto = new SysFileDTO();
        dto.setFileCode(fileCode);
        dto.setProjectInfoId(projectInfoId);
        hospitalInfoService.downloadhospitalInfoTemplate(dto, response);
    }

    /**
     * 项目工具==查询医院下拉数据
     *
     * @param dto
     */
    @Log(operName = "项目列表==查询医院下拉数据", operDetail = "项目列表==查询医院下拉数据",
            operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目列表==查询医院下拉数据")
    @ApiOperation("项目列表==查询医院下拉数据")
    @PostMapping(value = "/selectProjectToolsOptionsForHospitalInfo")
    Result selectProjectToolsOptionsForHospitalInfo(@RequestBody ProjHospitalInfoDTO dto) {
        return hospitalInfoService.selectProjectToolsOptionsForHospitalInfo(dto);
    }

    /**
     * 项目工具==查询医院下拉数据
     *
     * @param dto
     */
    @Log(operName = "查询医院信息", operDetail = "查询医院信息-手动授权新增页面查询",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询医院信息-手动授权新增页面查询")
    @ApiOperation("查询医院信息")
    @PostMapping(value = "/findHospitalInfo")
    Result<List<BaseIdNameResp>> findHospitalInfo(@Valid @RequestBody FindHospitalInfoDTO dto) {
        return hospitalInfoService.findHospitalInfo(dto);
    }

    /**
     * 医院信息维护
     * @param dto
     * @return
     */
    @Log(operName = "医院信息维护-客户信息", operDetail = "医院信息维护-客户信息",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息维护-客户信息")
    @ApiOperation("医院信息维护-客户信息")
    @PostMapping(value = "/updateHospitalCustomInfo")
    Result<String> updateHospitalCustomInfo(@Valid @RequestBody ProjCustomVsProjectTypeReq dto) {
        return hospitalInfoService.updateHospitalCustomInfo(dto);
    }

    /**
     * 申请域名
     * @param dto
     * @return
     */
    @Log(operName = "申请域名", operDetail = "申请域名",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "申请域名")
    @ApiOperation("申请域名")
    @PostMapping(value = "/applyHospitalCloudDomain")
    Result<String> applyHospitalCloudDomain(@Valid @RequestBody ProjCustomVsProjectTypeReq dto) {
        return hospitalInfoService.applyHospitalCloudDomain(dto);
    }

    /**
     * 医院信息-上半部分基础信息
     *
     * @param dto
     */
    @Log(
            operName = "医院信息-上半部分基础信息", operDetail = "医院信息-上半部分基础信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "医院信息-上半部分基础信息"
    )
    @ApiOperation("医院信息-上半部分基础信息")
    @PostMapping(value = "/findHospitalBaseInfo")
    Result<ProjHospitalInfoSelectPageResp> findHospitalBaseInfo(@RequestBody ProjHospitalInfoPageDTO dto) {
        return hospitalInfoService.findHospitalBaseeInfo(dto);
    }

}
