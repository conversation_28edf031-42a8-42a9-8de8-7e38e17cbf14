package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjMilestoneTaskDTO;
import com.msun.csm.model.req.ProjResearchPlanRelativeOuterReq;
import com.msun.csm.model.vo.ProjResearchPlanRelativeOuterVO;
import com.msun.csm.service.proj.ProjMilestoneTaskService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-05-10 11:17:54
 */

@Slf4j
@RestController
@RequestMapping("/projResearchPlan")
@Api(tags = "项目调研计划相关接口")
public class ProjMilestoneTaskController {

    @Resource
    private ProjMilestoneTaskService projMilestoneTaskService;


    /**
     * 查询调研计划信息
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询-制定调研计划")
    @Log(
            operName = "查询-制定调研计划", operDetail = "查询-制定调研计划", intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询-制定调研计划"
    )
    @RequestMapping("/findProjResearchPlanRelativeInfo")
    public Result<ProjResearchPlanRelativeOuterVO> findProjResearchPlanRelativeInfo(
            @Valid @RequestBody ProjMilestoneTaskDTO dto) {
        log.info("查询-制定调研计划：{}", JSON.toJSONString(dto));
        return projMilestoneTaskService.findProjResearchPlanRelativeInfo(dto);
    }

    /**
     * 更新调研计划结果
     *
     * @param req
     * @return
     */
    @ApiOperation("更新调研计划")
    @Log(
            operName = "更新调研计划", operDetail = "更新调研计划", intLogType = Log.IntLogType.SELF_SYS,
            cnName = "更新调研计划"
    )
    @RequestMapping("/updateProjResearchPlan")
    public Result updateProjResearchPlan(@Valid @RequestBody ProjResearchPlanRelativeOuterReq req) {
        log.info("更新调研计划：{}", JSON.toJSONString(req));
        return projMilestoneTaskService.updateProjResearchPlan(req);
    }

    /**
     * 获取团队成员根据项目id
     *
     * @param projectInfoId
     * @return
     */
    @ApiOperation("获取团队成员根据项目id")
    @Log(
            operName = "获取团队成员根据项目id", operDetail = "获取团队成员根据项目id",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取团队成员根据项目id"
    )
    @GetMapping("/getProjMemberListByProjectId")
    public Result getProjMemberListByProjectId(@NotNull @RequestParam("projectInfoId") String projectInfoId) {
        log.info("获取团队成员根据项目id：{}", projectInfoId);
        return projMilestoneTaskService.getProjMemberListByProjectId(projectInfoId);
    }

}
