package com.msun.csm.controller.report;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.report.ProjectParamerPageDTO;
import com.msun.csm.model.dto.report.ReportCustomInfoPageDTO;
import com.msun.csm.model.dto.report.ReportCustomInfoStaticStatisticsDTO;
import com.msun.csm.model.req.projreport.ReportCustomInfoDeleteReq;
import com.msun.csm.model.req.projreport.ReportCustomInfoReq;
import com.msun.csm.model.req.projreport.ReportCustomInfoSaveOrUpdateReq;
import com.msun.csm.model.resp.projreport.ReportCustomInfoProjectInfoResp;
import com.msun.csm.model.resp.projreport.ReportCustomInfoResp;
import com.msun.csm.model.vo.report.ReportCustomInfoPageVO;
import com.msun.csm.model.vo.report.ReportCustomInfoStatisticsFocusVO;
import com.msun.csm.model.vo.report.ReportCustomInfoStatisticsVO;
import com.msun.csm.model.vo.report.ReportCustomInfoVO;
import com.msun.csm.service.report.ReportCustomInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;


/**
 * 客户数据统计表(ReportCustomInfo)控制器
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */

@RestController
@Api(tags = "客户数据统计表")
@RequestMapping("/reportCustomInfo")
public class ReportCustomInfoController {

    @Resource
    private ReportCustomInfoService reportCustomInfoService;


    /**
     * 根据id查询信息
     *
     * @param id id
     * @return vo 对象
     */
    @ApiOperation("根据id查询信息")
    @GetMapping("/getReportCustomInfoById")
    public ReportCustomInfoVO getReportCustomInfoById(@ApiParam(value = "id", example = "id", required = true) Long id) {
        return this.reportCustomInfoService.getReportCustomInfoById(id);
    }

    /**
     * 医院信息-分页查询
     *
     * @param dto
     */
    @Log(operName = "分页查询", operDetail = "客户数据统计表-分页查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "客户数据统计表-根据客户信息查询")
    @ApiOperation("客户数据统计表-分页查询")
    @PostMapping(value = "/findReportCustomInfoPage")
    Result<PageInfo<ReportCustomInfoPageVO>> findReportCustomInfoPage(@RequestBody ReportCustomInfoPageDTO dto) {
        return reportCustomInfoService.findReportCustomInfoPage(dto);
    }


    /**
     * 客户数据统计表修改-分页查询
     *
     * @param dto
     */
    @Log(operName = "分页查询", operDetail = "客户数据统计表修改-分页查询", operLogType = Log.LogOperType.SEARCH, intLogType =
            Log.IntLogType.SELF_SYS, cnName = "客户数据统计表修改-根据客户信息查询")
    @ApiOperation("客户数据统计表修改-分页查询")
    @PostMapping(value = "/findReportCustomInfoUpdatePage")
    Result<PageInfo<ReportCustomInfoResp>> findReportCustomInfoUpdatePage(@RequestBody ProjectParamerPageDTO dto) {
        return reportCustomInfoService.findReportCustomInfoUpdatePage(dto);
    }

    /**
     * 客户数据统计表修改-修改数据
     *
     * @param dto
     */
    @Log(operName = "修改数据", operDetail = "客户数据统计表修改-修改数据", operLogType = Log.LogOperType.SEARCH, intLogType =
            Log.IntLogType.SELF_SYS, cnName = "客户数据统计表修改-修改数据")
    @ApiOperation("客户数据统计表修改-修改数据")
    @PostMapping(value = "/updateReportCustomInfo")
    Result updateReportCustomInfo(@RequestBody ReportCustomInfoReq dto) {
        return reportCustomInfoService.updateReportCustomInfo(dto);
    }


    /**
     * 分页查询客户
     *
     * @param dto
     */
    @Log(operName = "分页查询客户", operDetail = "分页查询客户", cnName = "分页查询客户")
    @ApiOperation("客户数据统计表-分页查询")
    @PostMapping(value = "/findReportCustomInfoPageNew")
    Result<PageInfo<ReportCustomInfoPageVO>> findReportCustomInfoPageNew(@RequestBody ReportCustomInfoPageDTO dto) {
        return reportCustomInfoService.findReportCustomInfoPageNew(dto);
    }

    @Log(operName = "新增or修改数据", operDetail = "新增or修改数据", operLogType = Log.LogOperType.SEARCH, intLogType =
            Log.IntLogType.SELF_SYS, cnName = "新增or修改数据")
    @ApiOperation("客户数据统计表-分页查询")
    @PostMapping(value = "/insertOrUpdateReportCustomInfo")
    Result<String> insertOrUpdateReportCustomInfo(@RequestBody ReportCustomInfoSaveOrUpdateReq dto) {
        return reportCustomInfoService.insertOrUpdateReportCustomInfo(dto);
    }

    @Log(operName = "查询项目信息", operDetail = "查询项目信息", operLogType = Log.LogOperType.SEARCH, intLogType =
            Log.IntLogType.SELF_SYS, cnName = "查询项目信息")
    @ApiOperation("查询项目信息")
    @GetMapping(value = "/getProjectInfoById")
    Result<ReportCustomInfoProjectInfoResp> getProjectInfoById(@RequestParam(name = "projectInfoId", required = false) Long projectInfoId) {
        return reportCustomInfoService.getProjectInfoById(projectInfoId);
    }

    /**
     * 查询项目交付数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "云健康项目交付数据", operDetail = "云健康项目交付数据", cnName = "云健康项目交付数据")
    @ApiOperation("云健康项目交付数据")
    @PostMapping(value = "/findCloudProjectData")
    Result<List<ReportCustomInfoStatisticsVO>> findCloudProjectData(@RequestBody ReportCustomInfoStaticStatisticsDTO dto) {
        return reportCustomInfoService.findCloudProjectData(dto);
    }

    /**
     * 客户数据统计表修改-分页查询
     *
     * @param dto
     */
    @Log(operName = "云健康项目交付数据分页查询(明细)", operDetail = "云健康项目交付数据分页查询(明细)", operLogType = Log.LogOperType.SEARCH, intLogType =
            Log.IntLogType.SELF_SYS, cnName = "云健康项目交付数据分页查询(明细)")
    @ApiOperation("云健康项目交付数据分页查询(明细)")
    @PostMapping(value = "/findCloudProjectDetailData")
    Result<PageInfo<ReportCustomInfoPageVO>> findCloudProjectDetailData(@RequestBody ReportCustomInfoPageDTO dto) {
        if (dto.getSelectEndTime() != null || dto.getSelectStartTime() != null) {
            dto.setDateType(3);
        }
        dto.setDetailType(1);
        return reportCustomInfoService.findReportCustomInfoPageNew(dto);
    }

    /**
     * 重点关注项目
     *
     * @param dto
     * @return
     */
    @Log(operName = "重点关注项目", operDetail = "重点关注项目", cnName = "重点关注项目")
    @ApiOperation("重点关注项目")
    @PostMapping(value = "/findKeyFocusProjectsData")
    Result<List<ReportCustomInfoStatisticsFocusVO>> findKeyFocusProjectsData(@RequestBody ReportCustomInfoStaticStatisticsDTO dto) {
        return reportCustomInfoService.findKeyFocusProjectsData(dto);
    }

    @Log(operName = "作废取消作废数据", operDetail = "作废取消作废数据", operLogType = Log.LogOperType.SEARCH, intLogType =
            Log.IntLogType.SELF_SYS, cnName = "作废取消作废数据")
    @ApiOperation("作废取消作废数据")
    @PostMapping(value = "/deleteOrCancelDeleteData")
    Result<String> deleteOrCancelDeleteData(@RequestBody ReportCustomInfoDeleteReq dto) {
        return reportCustomInfoService.deleteOrCancelDeleteData(dto);
    }

    /**
     * 数据导出
     *
     * @param dto
     * @return
     */
    @Log(
            operName = "数据导出", operDetail = "数据导出", operLogType = Log.LogOperType.EXP,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "数据导出"
    )
    @ApiOperation("数据导出")
    @PostMapping("/customerExportExcel")
    void customerExportExcel(HttpServletResponse response, @RequestBody ReportCustomInfoPageDTO dto) {
        reportCustomInfoService.customerExportExcel(response, dto);
    }
}
