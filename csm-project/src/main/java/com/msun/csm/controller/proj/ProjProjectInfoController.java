package com.msun.csm.controller.proj;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjManualCreateLog;
import com.msun.csm.model.dto.ProjManualCreateLogQueryDto;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.req.project.CustomProjectReq;
import com.msun.csm.model.req.project.FindProjectParamReq;
import com.msun.csm.model.req.project.ProjectSelectNotOnlineReq;
import com.msun.csm.model.resp.project.FindProjectParamResp;
import com.msun.csm.model.resp.project.FindProjectResp;
import com.msun.csm.model.resp.project.ProjectFiscalYearResp;
import com.msun.csm.model.resp.project.ProjectNotOnlineResp;
import com.msun.csm.model.resp.project.ProjectThisWeekResp;
import com.msun.csm.model.resp.qywechat.UrlTokenResp;
import com.msun.csm.model.vo.ProjectToolsOptionsForProjectInfoVO;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.SynchronizeDurationService;
import com.msun.csm.service.wechat.IWechatUserService;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Api(tags = "项目信息")
@RestController
@RequestMapping("/projectInfo")
@Slf4j
public class ProjProjectInfoController {

    @Resource
    private ProjProjectInfoService projProjectInfoService;


    /**
     * 根据项目id查询当前登录人员是否是此项目的项目经理
     */
    @Log(operName = "根据项目id查询当前登录人员是否是此项目的项目经理",
            operDetail = "根据项目id查询当前登录人员是否是此项目的项目经理",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "根据项目id查询当前登录人员是否是此项目的项目经理")
    @GetMapping("/isProjectLeader")
    public Result isProjectLeader(@RequestParam(value = "projectInfoId") String projectInfoId) {
        log.info("根据项目id查询当前登录人员是否是此项目的项目经理. projectInfoId: {}", projectInfoId);
        return projProjectInfoService.isProjectLeader(projectInfoId);
    }

    @Autowired
    private IWechatUserService iWechatUserService;
    @Autowired
    private SendMessageService sendMessageService;



    @GetMapping("/test11/{uid}")
    @ACROSS
    public Result test11(@PathVariable("uid") String uid) {
        UrlTokenResp urlTokenResp = iWechatUserService.redirectUrl(
                StrUtil.format("{}confirmPlanOnlineTime?projectInfoId={}", "https://imsp-test.msuncloud.com/csm-front-mobile/", "488062084736569345"));
        sendMessageService.sendEnterpriseWeChatMessageForOnePeople("填报计划上线时间", StrUtil.format("请确认您的项目【{}】计划上线时间是否有变化", "黑龙江康沅专科门诊有限公司项目"), Collections.singletonList(uid), urlTokenResp.getUrl(), null);
        return Result.success();
    }


    /**
     * 获取项目查询参数
     *
     * @return
     * <AUTHOR>
     * @date 2024/4/25
     */
    @Log(operName = "获取项目查询参数", operDetail = "获取项目查询参数-实施类型",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取项目查询参数")
    @PostMapping("/getParam")
    public Result<FindProjectParamResp> getParam(@RequestBody FindProjectParamReq findProjectParamReq) {
        log.info("获取项目查询参数");
        return projProjectInfoService.getParam(findProjectParamReq);
    }

    /**
     * 查询项目信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询项目信息", operDetail = "查询项目信息卡片",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目信息")
    @RequestMapping("/findProjectInfo")
    public Result<FindProjectResp> findProjectInfo(@Valid @RequestBody ProjProjectInfoDTO dto) {
        log.info("查询项目信息：{}", JSON.toJSONString(dto));
        return projProjectInfoService.findProjectInfo(dto);
    }

    /**
     * 查询项目信息
     *
     * @param projectInfoId
     * @return
     */
    @Log(operName = "一键批量同步四大模块所有配置到云健康", operDetail = "一键批量同步四大模块所有配置到云健康",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "一键批量同步四大模块所有配置到云健康")
    @RequestMapping("/syncCommConfigArea")
    public Result<FindProjectResp> syncCommConfigArea(@RequestParam("projectInfoId") Long projectInfoId) {
        return projProjectInfoService.syncCommConfigArea(projectInfoId);
    }

    /**
     * 项目工具==查询项目信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目工具==查询项目信息", operDetail = "项目工具==查询项目信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目工具==查询项目信息")
    @RequestMapping("/selectProjectToolsOptionsForProjectInfo")
    public Result<List<ProjectToolsOptionsForProjectInfoVO>> selectProjectToolsOptionsForProjectInfo(
            @RequestBody ProjProjectInfoDTO dto) {
        return projProjectInfoService.selectProjectToolsOptionsForProjectInfo(dto);
    }

    /**
     * 后台管理-创建自定义项目
     *
     * @param req
     * @return
     */
    @Log(operName = "后台管理-创建自定义项目", operDetail = "后台管理-创建自定义项目",
            intLogType = Log.IntLogType.SELF_SYS,
            cnName = "后台管理-创建自定义项目")
    @RequestMapping("/createCustomProject")
    public Result createCustomProject(@RequestBody CustomProjectReq req) {
        return projProjectInfoService.createCustomProject(req);
    }

    /**
     * 获取手动创建自定义项目操作日志的分页列表
     *
     * @param queryDto
     * @return
     */
    @Log(operName = "后台管理/获取手动创建自定义项目操作日志的分页列表", operDetail = "里程碑/获取手动创建自定义项目操作日志的分页列表", intLogType = Log.IntLogType.SELF_SYS, cnName = "里程碑/获取手动创建自定义项目操作日志的分页列表")
    @ApiOperation("里程碑/获取手动创建自定义项目操作日志的分页列表")
    @PostMapping(value = "/queryProjManualCreateLogPageList")
    Result<PageInfo<ProjManualCreateLog>> queryProjManualCreateLogPageList(@RequestBody ProjManualCreateLogQueryDto queryDto) {
        return projProjectInfoService.queryProjManualCreateLogPageList(queryDto);
    }

    @Resource
    private SynchronizeDurationService synchronizeDurationService;

    /**
     * 手动调用运营平台接口获取数据同步项目工期数据的接口
     */
    @PostMapping(value = "/synchronizeDuration")
    Result<Boolean> synchronizeDuration() {
        try {
            return Result.success(synchronizeDurationService.synchronizeDuration());
        } catch (Exception e) {
            log.error("同步运营平台工期数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询未上线项目
     *
     * @param req
     * @return
     */
    @Log(operName = "查询未上线项目", operDetail = "查询未上线项目", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询未上线项目")
    @RequestMapping("/selectNotOnlineProjectList")
    public Result<List<ProjectNotOnlineResp>> selectNotOnlineProjectList(@RequestBody ProjectSelectNotOnlineReq req) {
        return projProjectInfoService.selectNotOnlineProjectList(req);
    }

    /**
     * 查询财年项目
     *
     * @param req
     * @return
     */
    @Log(operName = "查询财年项目", operDetail = "查询财年项目", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询财年项目")
    @RequestMapping("/selectYearFiscalProjectList")
    public Result<ProjectFiscalYearResp> selectYearFiscalProjectList(@RequestBody ProjectSelectNotOnlineReq req) {
        return projProjectInfoService.selectYearFiscalProjectList(req);
    }
    /**
     * 查询本周项目
     *
     * @param req
     * @return
     */
    @Log(operName = "查询本周项目", operDetail = "查询本周项目", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询本周项目")
    @RequestMapping("/selectThisWeekProjectList")
    public Result<ProjectThisWeekResp> selectThisWeekProjectList(@RequestBody ProjectSelectNotOnlineReq req) {
        return projProjectInfoService.selectThisWeekProjectList(req);
    }

    /**
     * 查询近期上线项目
     *
     * @param req
     * @return
     */
    @Log(operName = "查询近期上线项目", operDetail = "查询近期上线项目", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询近期上线项目")
    @RequestMapping("/selectOnlineProjectList")
    @ACROSS
    public Result<List<ProjectNotOnlineResp>> selectOnlineProjectList(@RequestBody ProjectSelectNotOnlineReq req) {
        return projProjectInfoService.selectOnlineProjectList(req);
    }

}
