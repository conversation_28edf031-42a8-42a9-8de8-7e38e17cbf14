package com.msun.csm.controller.api.csmapi;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.error.BaseErrorCode;
import com.msun.core.component.implementation.api.csm.dto.CsmDTO;
import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDataOneSaveDto;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDeliverToYjkDTO;
import com.msun.core.component.implementation.api.formlib.dto.FormlibProductDTO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductPageVO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibDataVO;
import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsDTO;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsListDTO;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.controller.csmapi.CsmOutApi;
import com.msun.csm.model.csm.CsmParamerDTO;
import com.msun.csm.model.csm.HisLoginCsmVo;
import com.msun.csm.model.csm.LongUrlToShortUrlDTO;
import com.msun.csm.model.csm.PrintInfoMainCsmDTO;
import com.msun.csm.model.csm.PrintInfoSendMessageDTO;
import com.msun.csm.model.imsp.CustomerNotOnlineVO;
import com.msun.csm.model.imsp.HospitalNotOnlineVO;
import com.msun.csm.model.statis.ProjCsmHostReq;
import com.msun.csm.model.statis.ProjCustomReq;
import com.msun.csm.model.statis.ProjStatisticalReportMainUpdateStatusReq;
import com.msun.csm.service.common.BaseLongUrlToShortUrlService;
import com.msun.csm.service.formlibnew.FormlibResourceUniteService;
import com.msun.csm.service.proj.ProjEquipSummaryService;
import com.msun.csm.service.proj.ProjProjectReviewService;
import com.msun.csm.service.proj.ProjSurveyReportService;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;
import com.msun.csm.service.report.statis.ProjStatisticalReportMainService;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/23
 */
@Slf4j
@RestController
public class CsmOutApiImpl implements CsmOutApi {

    @Resource
    private ProjProductTaskService projProductTaskService;

    @Resource
    private ProjEquipSummaryService equipSummaryService;

    @Resource
    private ProjStatisticalReportMainService projStatisticalReportMainService;

    @Resource
    private BaseLongUrlToShortUrlService baseLongUrlToShortUrlService;

    @Resource
    @Lazy
    private ProjSurveyReportService projSurveyReportService;

    @Resource
    @Lazy
    private ProjProjectReviewService projProjectReviewService;

    @Resource
    @Lazy
    private FormlibResourceUniteService formlibResourceUniteService;

    /**
     * @param param
     * @return
     */
    @Override
    @CsmSign
    public Result<HisLoginCsmVo> verifyLoginBeforeCloudHealthRedirect(CsmParamerDTO param) {
        return projProductTaskService.verifyLoginBeforeCloudHealthRedirect(param);
    }

    /**
     * 补偿接口
     *
     * @return
     */
    @Override
    @CsmSign
    public Result compensationInterface() {
        return projProductTaskService.compensationInterface();
    }

    /**
     * @param csmDTO
     * @return
     */
    @Override
    @CsmSign
    public ResponseData getPrintCodeResultListByParamer(CsmDTO csmDTO) {
        return projProductTaskService.getPrintCodeResultListByParamer(csmDTO);
    }

    /**
     * @param csmDTO
     * @return
     */
    @Override
    @CsmSign
    public ResponseData sendReportPrintMsg(CsmDTO csmDTO) {
        return projProductTaskService.sendReportPrintMsg(csmDTO);
    }

    /**
     * @param csmDTO
     * @return
     */
    @Override
    @CsmSign
    public ResponseData getPersonByParamer(CsmDTO csmDTO) {
        return projProductTaskService.getPersonByParamer(csmDTO);
    }

    /**
     * 上传单据打印效果
     *
     * @param reportRenderingsDTO
     * @return
     */
    @Override
    @CsmSign
    public ResponseData saveMedicalPrinterConfigRenderings(ReportRenderingsDTO reportRenderingsDTO) {
        return projProductTaskService.saveMedicalPrinterConfigRenderings(reportRenderingsDTO);
    }

    /**
     * 各产品设备自动检测
     *
     * @param dto
     * @return
     */
    @Log(operName = "各产品设备自动检测", operDetail = "各产品设备自动检测", operLogType = Log.LogOperType.FIX,
            intLogType =
                    Log.IntLogType.OTHER, cnName = "各产品设备自动检测", saveParam = true)
    @Override
    @CsmSign
    public ResponseResult<String> csmAutoCheckEquip(EquipAutoCheckDTO dto) {
        Result<String> result = equipSummaryService.csmAutoCheckEquip(dto);
        if (result.isSuccess()) {
            return ResponseResult.success();
        } else {
            return ResponseResult.error(BaseErrorCode.FALLBACK, result.getMsg());
        }

    }

    /**
     * 上传协助记录附件
     *
     * @param reportRenderingsDTO
     * @return
     */
    @Override
    @CsmSign
    public ResponseData uploadAssistAttachment(ReportRenderingsListDTO reportRenderingsDTO) {
        return projProductTaskService.uploadAssistAttachment(reportRenderingsDTO);
    }

    /**
     * 报表平台下沉后，回更交付平台报表状态为制作完成
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseData updateStatisticalReportStatus(ProjStatisticalReportMainUpdateStatusReq dto) {
        return projStatisticalReportMainService.updateStatisticalReportStatus(dto);
    }

    /**
     * 报表平台保存后，回更交付平台报表状态为制作中
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseData updateStatisticalReportStatusBySave(ProjStatisticalReportMainUpdateStatusReq dto) {
        return projStatisticalReportMainService.updateStatisticalReportStatus(dto);
    }

    /**
     * 报表平台发布后，回更交付平台报表状态为发布中
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseData updateStatisticalReportStatusByRelease(ProjStatisticalReportMainUpdateStatusReq dto) {
        dto.setIsRelease(true);
        return projStatisticalReportMainService.updateStatisticalReportStatus(dto);
    }

    /**
     * 删除关联关系
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseData deleteRelationshipByParamer(ProjStatisticalReportMainUpdateStatusReq dto) {
        return projStatisticalReportMainService.deleteRelationshipByParamer(dto);
    }

    /**
     * 长链接转短链接
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public Result<String> longUrlToShortUrlFunction(LongUrlToShortUrlDTO dto) {
        return baseLongUrlToShortUrlService.longUrlToShortUrlFunction(dto);
    }

    /**
     * 短链接跳转
     * @param shortCode
     * @return
     */
    @Override
    @ACROSS
    public ResponseEntity<Void> getLongUrl(String shortCode) {
        return baseLongUrlToShortUrlService.getLongUrl(shortCode);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseData selectHospitalOnlineProductByParamer(ProjStatisticalReportMainUpdateStatusReq dto) {
        return projStatisticalReportMainService.selectHospitalOnlineProductByParamer(dto);
    }

    @Override
    @CsmSign
    public Result<String> getStatisticsReportAddUrl(String operationStatisticsReportId, String account) {
        return projStatisticalReportMainService.getStatisticsReportAddUrl(operationStatisticsReportId, account);
    }

    @Override
    @CsmSign
    public ResponseData getCsmHost(ProjCsmHostReq dto) {
        return baseLongUrlToShortUrlService.getCsmHost(dto);
    }

    /**
     * 获取未上线客户
     * @return
     */
    @Override
    @CsmSign
    public Result<List<CustomerNotOnlineVO>> getNotOnlineCustomer() {
        return baseLongUrlToShortUrlService.getNotOnlineCustomer();
    }

    /**
     * 获取未上线医院数据
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public Result<List<HospitalNotOnlineVO>> getNotOnlineHospitalData(ProjCustomReq dto) {
        return baseLongUrlToShortUrlService.getNotOnlineHospitalData(dto);
    }

    @Override
    @CsmSign
    public Result<String> updatePrintReportStatusData(ProjCustomReq dto) {
        return projSurveyReportService.updatePrintReportStatusData(dto);
    }

    /**
     * 保存打印信息
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseResult<String> savePrintInfo(PrintInfoMainCsmDTO dto) {
        return projSurveyReportService.savePrintInfo(dto);
    }

    /**
     * 报表平台查看单个报表数据
     *
     * @return
     * @param dto
     */
    @Override
    @CsmSign
    public ResponseData getReportById(ProjStatisticalReportMainUpdateStatusReq dto) {
        return projStatisticalReportMainService.getReportById(dto);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseData getReportByParamer(ProjStatisticalReportMainUpdateStatusReq dto) {
        return projStatisticalReportMainService.getReportByParamer(dto);
    }

    /**
     * 打印平台下沉接口完成/失败后进行发送消息
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseResult sendMessageByPrint(PrintInfoSendMessageDTO dto) {
        return projProjectReviewService.sendMessageByPrint(dto);
    }

    /**
     * 根据资源库id查询资源库数据
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseResult<FormlibDataVO> getFormDataByJfId(FormlibProductDTO dto) {
        return formlibResourceUniteService.getFormDataByJfId(dto);
    }

    /**
     * 产品保存到交付平台
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseResult<String> saveFormOneDataToDeliver(FormlibDataOneSaveDto dto) {
        return formlibResourceUniteService.saveFormOneDataToDeliver(dto);
    }

    /**
     * 从交付根据类型获取资源库数据
     *
     * @param dto
     * @return
     */
    @Override
    @CsmSign
    public ResponseResult<FormlibComeToProductPageVO> getFormDataByDeliverToYjk(FormlibDeliverToYjkDTO dto) {
        return formlibResourceUniteService.getFormDataByDeliverToYjk(dto);
    }
}
