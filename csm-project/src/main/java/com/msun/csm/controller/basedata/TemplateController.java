package com.msun.csm.controller.basedata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.BasedataConfig;
import com.msun.csm.dao.entity.basedata.BdDictData;
import com.msun.csm.dao.entity.basedata.BdDictType;
import com.msun.csm.dao.entity.basedata.BdTableColInfo;
import com.msun.csm.dao.entity.basedata.BdTableInfo;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.basedata.*;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.jdbc.*;
import com.msun.csm.jdbc.pojo.DsCfg;
import com.msun.csm.jdbc.pojo.TableDef;
import com.msun.csm.util.LargeZipFileHandler;
import com.msun.csm.util.ListUtils;
import com.msun.csm.util.RequestAndResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/6/2 12:25
 */
@Slf4j
@Api(tags = "模板管理API")
@RestController
@RequestMapping("/template/")
public class TemplateController {

    @Autowired
    private BasedataConfig basedataConfig;
    @Autowired
    private BdTableInfoMapper bdTableInfoMapper;
    @Autowired
    private BdTableColInfoMapper bdTableColInfoMapper;
    @Autowired
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Autowired
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Autowired
    private BdDictTypeMapper bdDictTypeMapper;
    @Autowired
    private BdDictDataMapper bdDictDataMapper;
    @Autowired
    private DictProductMapper dictProductMapper;

    /**
     * 下载模板定制的Excel表格
     * 该表格包含如下几个页签
     * 1、医院基本信息：主要包含医院名称、hosId、orgId、数据存储所属模式、云健康chis、chisapp的数据库连接信息
     * 2、模板基本信息：模板中文名、英文名、所属产品
     * 3、模板字段信息：字段名称，校验规则，枚举值等等
     * 4、Kettle作业维护：kettle作业名称，执行顺序
     */
    @ApiOperation(value = "下载模板定制的Excel表格")
    @ACROSS
    @GetMapping(value = "downloadExcelDef")
    public void downloadExcelDef() {
        List<EasyExcelUtils.ExcelSheetDef> sheetDefs = new ArrayList<>();
        sheetDefs.add(buildTableInfoSheet());
        sheetDefs.add(buildTableColInfoSheet());
        try {
            HttpServletResponse response = RequestAndResponse.getResponse();
            String dataFormat = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            assert response != null;
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = StrUtil.format("模板定义表格-{}.xlsx", dataFormat);
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
                EasyExcelUtils.buildWriteSheet(excelWriter, sheetDefs);
            }
        } catch (Exception e) {
            log.error("导出模板定义表格失败：", e);
            throw new CustomException("导出模板定义表格失败：" + e.getMessage());
        }
    }


    //构建模板基础信息页签
    private EasyExcelUtils.ExcelSheetDef buildTableInfoSheet() {
        List<String> collect = new LambdaQueryChainWrapper<>(dictProductMapper)
                .eq(DictProduct::getIsDeleted, 0)
                .eq(DictProduct::getYyIsCloud, 1)
                .list().stream().map(DictProduct::getProductName).collect(Collectors.toList());
        String[] array = collect.toArray(new String[0]);
        return new EasyExcelUtils.ExcelSheetDef("模板定义", false, Arrays.asList(
                new EasyExcelUtils.ExcelColDef(
                        "模板中文名",
                        "请输入模板中文名",
                        false,
                        null,
                        null,
                        null,
                        new Long[]{1L, 255L},
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "模板英文名",
                        "请输入模板英文名",
                        false,
                        null,
                        null,
                        null,
                        new Long[]{1L, 255L},
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "所属产品",
                        "请输入所属产品",
                        false,
                        array,
                        null,
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "是否发布",
                        "请选择是否发布",
                        false,
                        new String[]{"是", "否"},
                        null,
                        null,
                        null,
                        null
                )
        ), new ArrayList<>());
    }

    //构建表字段配置页签
    private EasyExcelUtils.ExcelSheetDef buildTableColInfoSheet() {
        List<String> dictCodes = new LambdaQueryChainWrapper<>(bdDictTypeMapper)
                .select(BdDictType::getDictCode)
                .eq(BdDictType::getIsDeleted, 0)
                .eq(BdDictType::getOpened, true)
                .list().stream().map(BdDictType::getDictCode).collect(Collectors.toList());
        String[] dictCodesArray = dictCodes.toArray(new String[0]);
        return new EasyExcelUtils.ExcelSheetDef("模板字段", false, Arrays.asList(
                new EasyExcelUtils.ExcelColDef(
                        "字段中文名",
                        "请输入字段中文名",
                        false,
                        null,
                        null,
                        null,
                        new Long[]{1L, 255L},
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "字段英文名",
                        "请输入字段英文名",
                        false,
                        null,
                        null,
                        null,
                        new Long[]{1L, 255L},
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "字段类型",
                        "请选择字段类型",
                        false,
                        new String[]{"varchar", "text", "int8", "int4", "int2", "decimal", "boolean", "timestamp", "date", "time"},
                        null,
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "字段长度",
                        "请输入字段长度",
                        false,
                        null,
                        new Long[]{1L, Long.MAX_VALUE},
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "是否必填",
                        "请选择是否必填",
                        false,
                        new String[]{"是", "否"},
                        null,
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "是否唯一",
                        "请选择是否唯一",
                        false,
                        new String[]{"是", "否"},
                        null,
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "是否主键",
                        "请选择是否主键",
                        false,
                        new String[]{"是", "否"},
                        null,
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "字典编码",
                        "请选择字典编码",
                        false,
                        dictCodesArray,
                        null,
                        null,
                        null,
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "字段描述",
                        "请输入字段描述",
                        false,
                        null,
                        null,
                        null,
                        new Long[]{0L, 255L},
                        null
                ),
                new EasyExcelUtils.ExcelColDef(
                        "是否发布",
                        "请选择是否发布",
                        false,
                        new String[]{"是", "否"},
                        null,
                        null,
                        null,
                        null
                )
        ), new ArrayList<>());
    }


    /**
     * 上传模板定制的Excel表格
     * 也就是downloadExcelDef这个接口下载的定制模版信息上传上来后，对里面的四个sheet进行解析落库
     *
     * @param file
     * @throws IOException
     */
    @ApiOperation(value = "上传模板定制的Excel表格")
    @ACROSS
    @PostMapping(value = "uploadExcelDef")
    public synchronized Result<?> uploadExcelDef(@RequestParam("file") MultipartFile file) throws IOException {
        String outputDir = System.getProperty("java.io.tmpdir") + File.separator + "uploadedTableDef-" + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        File outputDirectory = new File(outputDir);
        if (!outputDirectory.exists() && !outputDirectory.mkdirs()) {
            throw new CustomException("创建临时目录失败");
        }

        try (InputStream inputStream = file.getInputStream()) {
            // 解压缩 ZIP 文件
            LargeZipFileHandler.processZipFile(inputStream, outputDir);
            // 遍历解压后的文件，解析所有 xlsx 文件
            List<BdTableInfo> tables = new ArrayList<>();
            List<BdTableColInfo> tableCols = new ArrayList<>();

            EasyExcelUtils.readExcelFile(outputDirectory, f -> {
                //解析Excel文件
                try (ExcelReader excelReader = EasyExcel.read(f).build()) {
                    List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
                    ReadSheet tableDef = null;
                    ReadSheet tableColDef = null;
                    for (ReadSheet sheet : sheets) {
                        String sheetName = sheet.getSheetName();
                        if ("模板定义".equals(sheetName)) {
                            //解析模板定义
                            tableDef = sheet;
                        } else if ("模板字段".equals(sheetName)) {
                            //解析模版字段
                            tableColDef = sheet;
                        }
                    }
                    if (tableDef == null) {
                        throw new CustomException(StrUtil.format("文件【{}】缺少模板定义页签", f.getName()));
                    }
                    if (tableColDef == null) {
                        throw new CustomException(StrUtil.format("文件【{}】缺少模板字段页签", f.getName()));
                    }
                    List<BdTableInfo> tableInfos = parseTableInfoSheet(f, tableDef);
                    if (CollUtil.isEmpty(tableInfos)) {
                        throw new CustomException(StrUtil.format("文件【{}】没有有效的模板定义数据", f.getName()));
                    }
                    BdTableInfo tableInfo = CollUtil.getFirst(tableInfos);
                    tables.add(tableInfo);
                    tableCols.addAll(parseTableColInfoSheet(f, tableColDef, tableInfo));
                }
            });
            ListUtils.chunkedWithSubIndex(tables, 100, subList -> bdTableInfoMapper.insertUpdate(subList));
            ListUtils.chunkedWithSubIndex(tableCols, 100, subList -> bdTableColInfoMapper.insertUpdate(subList));
        } finally {
            // 清理解压目录
            EasyExcelUtils.deleteDirectory(outputDirectory);
        }
        return Result.success();
    }

    private List<BdTableInfo> parseTableInfoSheet(File file, ReadSheet sheet) {
        List<BdTableInfo> tableInfos = new ArrayList<>();
        // 解析模板定义页签
        EasyExcel.read(file, LinkedHashMap.class, new EasyExcelUtils.ExcelListener(2, subList -> {
                    for (LinkedHashMap<String, String> item : subList) {
                        BdTableInfo tbl = new BdTableInfo();
                        tbl.setId(IdGenerator.ins().generator());
                        tbl.setTableNameCn(item.get("模板中文名"));
                        if (StrUtil.isEmpty(tbl.getTableNameCn())) {
                            throw new CustomException(StrUtil.format("文件【{}】缺失模板中文名", file.getName()));
                        }
                        tbl.setTableNameEn(item.get("模板英文名"));
                        if (StrUtil.isEmpty(tbl.getTableNameEn())) {
                            throw new CustomException(StrUtil.format("文件【{}】缺失模板英文名", file.getName()));
                        }
                        tbl.setProductName(item.get("所属产品"));
                        if (!new LambdaQueryChainWrapper<>(dictProductMapper)
                                .eq(DictProduct::getIsDeleted, 0)
                                .eq(DictProduct::getYyIsCloud, 1)
                                .eq(DictProduct::getProductName, tbl.getProductName())
                                .exists()) {
                            throw new CustomException(StrUtil.format("模板【{}】所属产品【{}】不存在", tbl.getTableNameEn(), tbl.getProductName()));
                        }
                        tbl.setOpened("是".equals(item.get("是否发布")));
                        tableInfos.add(tbl);
                    }
                }, 100))
                .excelType(ExcelTypeEnum.XLSX)
                .useDefaultListener(false)
                .sheet(sheet.getSheetNo())
                .doRead();
        return tableInfos;
    }

    private List<BdTableColInfo> parseTableColInfoSheet(File file, ReadSheet sheet, BdTableInfo tableInfo) {
        List<BdTableColInfo> colInfos = new ArrayList<>();
        String tableNameEn = tableInfo.getTableNameEn();
        Set<String> colNames = new HashSet<>();
        // 解析模板定义页签
        AtomicBoolean hasPk = new AtomicBoolean(false);
        EasyExcel.read(file, LinkedHashMap.class, new EasyExcelUtils.ExcelListener(2, subList -> {
                    for (LinkedHashMap<String, String> item : subList) {
                        BdTableColInfo col = new BdTableColInfo();
                        col.setId(IdGenerator.ins().generator());
                        col.setTableNameEn(tableNameEn);
                        col.setColNameCn(item.get("字段中文名"));
                        col.setColNameEn(item.get("字段英文名"));
                        if (StrUtil.isEmpty(col.getColNameEn())) {
                            throw new CustomException(StrUtil.format("表【{}】字段【{}】缺失字段英文名", tableNameEn, col.getColNameEn()));
                        }
                        if (colNames.contains(col.getColNameEn())) {
                            throw new CustomException(StrUtil.format("表【{}】字段英文名【{}】重复", tableNameEn, col.getColNameEn()));
                        }
                        colNames.add(col.getColNameEn());
                        col.setColType(item.get("字段类型"));
                        if (!StrUtil.containsAnyIgnoreCase(col.getColType(), "varchar", "text", "int8", "int4", "int2", "decimal", "boolean", "timestamp", "date", "time")) {
                            throw new CustomException(StrUtil.format("表【{}】字段【{}】类型【{}】不合法", tableNameEn, col.getColNameEn(), col.getColType()));
                        }
                        if (StrUtil.isNotBlank(item.get("字段长度")) && "varchar".equalsIgnoreCase(col.getColType())) {
                            try {
                                col.setColLength(new BigDecimal(item.get("字段长度")).longValue());
                            } catch (Throwable ignored) {
                            }
                        }
                        col.setNotNull("是".equals(item.get("是否必填")));
                        col.setIsUnique("是".equals(item.get("是否唯一")));
                        col.setIsPk("是".equals(item.get("是否主键")));
                        if (Boolean.TRUE.equals(col.getIsPk())) {
                            col.setNotNull(true);
                            hasPk.set(true);
                        }
                        col.setDictCode(item.get("字典编码"));
                        if (StrUtil.isNotBlank(col.getDictCode())) {
                            if (!new LambdaQueryChainWrapper<>(bdDictTypeMapper)
                                    .select(BdDictType::getDictCode)
                                    .eq(BdDictType::getIsDeleted, 0)
                                    .eq(BdDictType::getOpened, true)
                                    .eq(BdDictType::getDictCode, col.getDictCode())
                                    .exists()) {
                                throw new CustomException(StrUtil.format("模板【{}】字段【{}】字典编码【{}】不存在", tableNameEn, col.getColNameEn(), col.getDictCode()));
                            }
                        }
                        col.setColDesc(item.get("字段描述"));
                        col.setOpened("是".equals(item.get("是否发布")));
                        colInfos.add(col);
                    }
                }, 100))
                .excelType(ExcelTypeEnum.XLSX)
                .useDefaultListener(false)
                .sheet(sheet.getSheetNo())
                .doRead();
        return colInfos;
    }


    /**
     * 将前端传过来的dataSchema所包含的所有模板（表格）在basedata数据库下创建出来
     *
     * @param projectNumber
     * @return
     */
    @ApiOperation(value = "将定制的Excel初始化到数据库")
    @ACROSS
    @GetMapping(value = "syncTmpTable")
    public synchronized Result<?> syncTmpTable(@RequestParam("projectNumber") String projectNumber, @RequestParam(value = "hospitalId", required = false) Long hospitalId) {

        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                .select(ProjProjectInfo::getCustomInfoId)
                .eq(ProjProjectInfo::getProjectNumber, projectNumber)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        List<ProjHospitalInfo> hospitalInfos = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                .eq(ProjHospitalInfo::getCustomInfoId, proj.getCustomInfoId())
                .eq(hospitalId != null && hospitalId > 0, ProjHospitalInfo::getCloudHospitalId, hospitalId)
                .eq(ProjHospitalInfo::getIsDeleted, 0)
                .list();

        DsCfg dsCfg = new DsCfg();
        dsCfg.setHost(basedataConfig.getDbHost());
        dsCfg.setPort(basedataConfig.getDbPort());
        dsCfg.setUser(basedataConfig.getDbUname());
        dsCfg.setPasswd(basedataConfig.getDbPasswd());
        dsCfg.setDb(basedataConfig.getDbName());
        try (Connection conn = PgsqlConn.getConn(dsCfg, true)) {
            for (ProjHospitalInfo hos : hospitalInfos) {
                String dataSchema = PinyinUtil.getFirstLetter(hos.getHospitalName(), "") + "_" + projectNumber.toLowerCase();
                if (StrUtil.isBlank(dataSchema)) {
                    throw new CustomException(StrUtil.format("医院【{}】的名称为空，无法创建模式", hos.getHospitalName()));
                }
                //判断模式是否已经创建，如果没创建就创建模式
                if (!PgsqlExecutor.schemaIsCreated(conn, dataSchema)) {
                    //创建模式
                    PgsqlExecutor.createSchema(conn, dataSchema);
                    log.info("医院【{}】的模式【{}】创建成功", hos.getHospitalName(), dataSchema);
                } else {
                    log.info("医院【{}】的模式【{}】已存在", hos.getHospitalName(), dataSchema);
                }
                List<BdTableInfo> tables = new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                        .eq(BdTableInfo::getOpened, true)
                        .eq(BdTableInfo::getIsDeleted, 0)
                        .list();
                if (CollUtil.isEmpty(tables)) {
                    continue;
                }
                dsCfg.setSchema(dataSchema);
                dsCfg.setUrl(PgsqlGenerator.buildUrl(dsCfg));
                Connection conn1 = PgsqlConn.getConn(dsCfg, false);
                try {
                    for (BdTableInfo table : tables) {
                        //获取设计的表里的字段
                        List<BdTableColInfo> cols = new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                                .eq(BdTableColInfo::getTableNameEn, table.getTableNameEn())
                                .eq(BdTableColInfo::getOpened, true)
                                .eq(BdTableColInfo::getIsDeleted, 0)
                                .orderByAsc(BdTableColInfo::getId)
                                .list();
                        //将字段整合成com.msun.csm.jdbc.pojo.TableDef
                        TableDef tableDef = new TableDef();
                        tableDef.setSchema(dataSchema);
                        tableDef.setName(table.getTableNameEn());
                        tableDef.setComments(table.getTableNameCn());
                        //构建字段定义
                        List<TableDef.TableColDef> colDefs = new ArrayList<>();

                        TableDef.TableColDef projectNumberCol = new TableDef.TableColDef();
                        projectNumberCol.setName("project_number");
                        projectNumberCol.setType("varchar");
                        projectNumberCol.setSize(100);
                        projectNumberCol.setNullable(false);
                        projectNumberCol.setComments("项目编号");
                        colDefs.add(projectNumberCol);
                        //判断cols里面失败包含hospital_id跟org_id
                        boolean hasHospitalId = false;
                        boolean hasOrgId = false;
                        for (BdTableColInfo col : cols) {
                            if ("hospital_id".equalsIgnoreCase(col.getColNameEn())) {
                                hasHospitalId = true;
                            }
                            if ("org_id".equalsIgnoreCase(col.getColNameEn())) {
                                hasOrgId = true;
                            }
                        }
                        if (!hasHospitalId) {
                            TableDef.TableColDef hospitalIdCol = new TableDef.TableColDef();
                            hospitalIdCol.setName("hospital_id");
                            hospitalIdCol.setType("int8");
                            hospitalIdCol.setNullable(false);
                            hospitalIdCol.setComments("云健康医院ID");
                            colDefs.add(hospitalIdCol);
                        }
                        if (!hasOrgId) {
                            TableDef.TableColDef orgIdCol = new TableDef.TableColDef();
                            orgIdCol.setName("org_id");
                            orgIdCol.setType("int8");
                            orgIdCol.setNullable(false);
                            orgIdCol.setComments("云健康ORG_ID");
                            colDefs.add(orgIdCol);
                        }
                        colDefs.addAll(cols.stream().map(col -> {
                            TableDef.TableColDef colDef = new TableDef.TableColDef();
                            colDef.setName(col.getColNameEn());
                            colDef.setType(col.getColType());
                            colDef.setSize(col.getColLength() != null ? col.getColLength().intValue() : null);
                            colDef.setDecimalDigits(null); // 如果需要支持小数位数，可从 `BdTableColInfo` 中扩展字段
                            colDef.setNullable(Boolean.FALSE.equals(col.getNotNull()));
                            colDef.setDefaultValue(null); // 如果有默认值字段，可从 `BdTableColInfo` 中扩展字段
                            String comments = col.getColNameCn();
                            if (StrUtil.isNotBlank(col.getColDesc())) {
                                comments += ":" + col.getColDesc();
                            }
                            comments = comments.replace(";", "；");
                            colDef.setComments(comments);
                            return colDef;
                        }).collect(Collectors.toList()));
                        tableDef.setColDefs(colDefs);
                        // 设置主键
                        List<String> pks = cols.stream()
                                .filter(BdTableColInfo::getIsPk)
                                .map(BdTableColInfo::getColNameEn)
                                .collect(Collectors.toList());
                        tableDef.setPks(pks);

                        //判断当前表是否已经存在
                        if (!PgsqlExecutor.tableIsCreated(conn1, dataSchema, table.getTableNameEn())) {
                            //不存在就建表
                            String ddl = PgsqlGenerator.generatorDDL(tableDef);
                            PgsqlExecutor.executeSql(conn1, ddl);
                        } else {
                            //否则就获取源表的TableDef然后跟当前设计的TableDef字段进行对比，发现不一致的地方就要调整，比如缺字段就创建字段，字段长度、字段类型、字段描述不一致就更新
                            TableDef oldTableDef = PgsqlExecutor.getTableDef(conn1, dataSchema, table.getTableNameEn());
                            //比较tableDef跟oldTableDef，发现不一致的地方就要调整，比如缺字段就创建字段，字段长度、字段类型、字段描述不一致就更新
                            List<TableDef.TableColDef> newCols = tableDef.getColDefs();
                            List<TableDef.TableColDef> oldCols = oldTableDef.getColDefs();

                            // 将旧表字段转换为 Map，方便查找
                            Map<String, TableDef.TableColDef> oldColMap = oldCols.stream()
                                    .collect(Collectors.toMap(TableDef.TableColDef::getName, col -> col));
                            // 遍历新表字段
                            for (TableDef.TableColDef newCol : newCols) {
                                TableDef.TableColDef oldCol = oldColMap.get(newCol.getName());
                                String ddl = PgsqlGenerator.compareColDefGenerateDdl(tableDef, oldCol, newCol);
                                // 执行生成的 SQL
                                if (StrUtil.isNotBlank(ddl)) {
                                    PgsqlExecutor.executeSql(conn, ddl);
                                }
                            }
                        }
                    }
                    conn1.commit();
                } catch (Throwable e) {
                    try {
                        conn1.rollback();
                    } catch (Throwable ignored) {
                    }
                    throw new CustomException("同步模板错误", e);
                } finally {
                    PgsqlConn.close(conn1);
                }
            }
        } catch (Throwable e) {
            throw new CustomException("创建模式失败", e);
        }
        return Result.success();
    }

    @ApiOperation(value = "下载数据模板Excel")
    @ACROSS
    @GetMapping(value = "downloadDataExcel")
    public synchronized void downloadDataExcel(@RequestParam("projectNumber") String projectNumber, @RequestParam(value = "hospitalId", required = false) Long hospitalId) {
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                .select(ProjProjectInfo::getCustomInfoId)
                .eq(ProjProjectInfo::getProjectNumber, projectNumber)
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        List<ProjHospitalInfo> hospitalInfos = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                .eq(ProjHospitalInfo::getCustomInfoId, proj.getCustomInfoId())
                .eq(ProjHospitalInfo::getIsDeleted, 0)
                .list();

        List<BdTableInfo> tables = new LambdaQueryChainWrapper<>(bdTableInfoMapper)
                .eq(BdTableInfo::getOpened, true)
                .eq(BdTableInfo::getIsDeleted, 0)
                .list();

        Map<String, List<BdTableColInfo>> colMap = new HashMap<>();
        for (BdTableInfo table : tables) {
            List<BdTableColInfo> cols = new LambdaQueryChainWrapper<>(bdTableColInfoMapper)
                    .eq(BdTableColInfo::getTableNameEn, table.getTableNameEn())
                    .eq(BdTableColInfo::getOpened, true)
                    .eq(BdTableColInfo::getIsDeleted, 0)
                    .orderByAsc(BdTableColInfo::getId)
                    .list();
            colMap.put(table.getTableNameEn(), cols);
        }

        String now = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        String zipFilePath = System.getProperty("java.io.tmpdir") + File.separator + StrUtil.format("templates-{}.zip", now);
        String tmpDir = System.getProperty("java.io.tmpdir") + File.separator + "excels-" + now;
        File tmpDirectory = new File(tmpDir);
        if (!tmpDirectory.exists() && !tmpDirectory.mkdirs()) {
            throw new CustomException("创建临时目录失败");
        }

        try {
            hospitalInfos.forEach(hospital -> {
                String excelDir = StrUtil.format("{}{}{}", tmpDir, File.separator, hospital.getHospitalName());
                File excelDirectory = new File(excelDir);
                if (!excelDirectory.exists() && !excelDirectory.mkdirs()) {
                    throw new CustomException("创建临时目录失败");
                }
                tables.forEach(table -> {
                    List<BdTableColInfo> cols = colMap.getOrDefault(table.getTableNameEn(), null);
                    if (CollUtil.isEmpty(cols)) {
                        return;
                    }
                    List<EasyExcelUtils.ExcelColDef> excelColDefs = new ArrayList<>();
                    for (BdTableColInfo col : cols) {
                        EasyExcelUtils.ExcelColDef excelColDef = new EasyExcelUtils.ExcelColDef(
                                col.getColNameEn(),
                                StrUtil.format("{}#{}", col.getColNameCn(), col.getColType()),
                                false,
                                null,
                                null,
                                null,
                                null,
                                null
                        );
                        if (StrUtil.isNotBlank(col.getDictCode())) {
                            List<String> collect = new LambdaQueryChainWrapper<>(bdDictDataMapper)
                                    .eq(BdDictData::getDictCode, col.getDictCode())
                                    .eq(BdDictData::getIsDeleted, 0)
                                    .eq(BdDictData::getOpened, true)
                                    .list().stream().map(BdDictData::getDictValue).collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(collect)) {
                                excelColDef.setOptions(collect.toArray(new String[0]));
                            }
                        }
                        if (Arrays.asList("int8", "int4", "int2").contains(col.getColType())) {
                            excelColDef.setNumberRange(new Long[]{Long.MIN_VALUE, Long.MAX_VALUE});
                        } else if ("timestamp".equalsIgnoreCase(col.getColType())) {
                            excelColDef.setDateFormat("yyyy-MM-dd HH:mm:ss");
                        } else if ("data".equalsIgnoreCase(col.getColType())) {
                            excelColDef.setDateFormat("yyyy-MM-dd");
                        } else if ("time".equalsIgnoreCase(col.getColType())) {
                            excelColDef.setDateFormat("HH:mm:ss");
                        } else if (Arrays.asList("varchar", "text").contains(col.getColType()) && col.getColLength() != null && col.getColLength() > 0) {
                            excelColDef.setTextLengthLimit(new Long[]{0L, col.getColLength()});
                        }
                        excelColDefs.add(excelColDef);
                    }
                    List<EasyExcelUtils.ExcelSheetDef> sheetDefs = new ArrayList<>();
                    sheetDefs.add(new EasyExcelUtils.ExcelSheetDef(table.getTableNameEn(), false, excelColDefs, new ArrayList<>()));
                    //将元数据写入表里，并且隐藏
                    sheetDefs.add(new EasyExcelUtils.ExcelSheetDef("metadata", true, Arrays.asList(
                            new EasyExcelUtils.ExcelColDef(
                                    "projectNumber",
                                    "项目编号",
                                    false,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                            ),
                            new EasyExcelUtils.ExcelColDef(
                                    "schema",
                                    "模式",
                                    false,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                            ),
                            new EasyExcelUtils.ExcelColDef(
                                    "tableNameEn",
                                    "表名称",
                                    false,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                            ),
                            new EasyExcelUtils.ExcelColDef(
                                    "hospitalId",
                                    "医院ID",
                                    false,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                            ),
                            new EasyExcelUtils.ExcelColDef(
                                    "orgId",
                                    "区域ID",
                                    false,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null
                            )
                    ), Collections.singletonList(
                            Arrays.asList(projectNumber, PinyinUtil.getFirstLetter(hospital.getHospitalName(), "") + "_" + projectNumber.toLowerCase(), table.getTableNameEn(), String.valueOf(hospital.getCloudHospitalId()), String.valueOf(hospital.getOrgId()))
                    )));

                    //请将上面的sheetDefs写入outputDirectory下的test.xlsx文件里
                    try (ExcelWriter excelWriter = EasyExcel.write(excelDir + File.separator + StrUtil.format("{}-{}.xlsx", table.getTableNameCn(), table.getTableNameEn())).build()) {
                        EasyExcelUtils.buildWriteSheet(excelWriter, sheetDefs);
                    }

                });
            });

            // 定义压缩包路径

            ZipFile zipFile = new ZipFile(zipFilePath);

            // 压缩目录
            zipFile.addFolder(new File(tmpDir));

            // 设置 HTTP 响应头
            HttpServletResponse response = RequestAndResponse.getResponse();
            assert response != null;
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("templates.zip", "UTF-8"));

            // 将 ZIP 文件写入响应输出流
            try (InputStream inputStream = Files.newInputStream(Paths.get(zipFilePath));
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            // 清理目录
            EasyExcelUtils.deleteDirectory(new File(zipFilePath));
            EasyExcelUtils.deleteDirectory(new File(tmpDir));
        }
    }
}
