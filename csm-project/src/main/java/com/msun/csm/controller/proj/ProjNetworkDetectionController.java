package com.msun.csm.controller.proj;

import static com.msun.csm.service.proj.ProjNetworkDetHospitalServiceImpl.DEFAULT_TMP_PATH;

import java.io.File;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.ObsNetDetectFile;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.ProjNetworkDetectResultReq;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.ProjNetworkDetHospitalServiceImpl;
import com.msun.csm.service.proj.ProjNetworkDetectService;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 网络检测回传结果调用
 */
@Slf4j
@Api(tags = "网络检测回传结果调用")
@RestController
@RequestMapping("networkDetection")
public class ProjNetworkDetectionController {

    @Resource
    private ProjNetworkDetectService projNetworkDetectService;


    @Resource
    private SysOperLogService sysOperLogService;

    @Log(
            operName = "网络测试回传接口", operDetail = "网络测试回传接口, 包含域名检测与DNS检测",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "网络测试回传接口"
    )
    @ApiOperation("网络测试回传接口")
    @PostMapping("uploadDetectionResult")
    @CsmSign
    public Result uploadDetectionResult(@RequestBody ProjNetworkDetectResultReq networkDetectResultReq) {
        log.info("网络测试回传接口. req: {}", networkDetectResultReq);
        projNetworkDetectService.insertDetectResult(networkDetectResultReq);
        return Result.success();
    }

    /**
     * 文件上传
     *
     * @param multipartFile 数据传输对象
     */
    @ApiOperation("日志文件上传")
    @PostMapping("/uploadFile")
    @CsmSign
    public Result uploadFiles(@RequestParam("file") MultipartFile multipartFile)
            throws Exception {
        log.info("fileName: {}", multipartFile.getOriginalFilename());
        sysOperLogService.apiOperLogInsert(multipartFile, "接收网络检测回传日志文件-入参", "接收网络检测回传日志文件", Log.LogOperType.ADD.getCode());
        String fileName = multipartFile.getOriginalFilename();
        String tmpPath = DEFAULT_TMP_PATH + StrUtil.SLASH + "networkdetectlog" + StrUtil.SLASH + fileName;
        File logTmpFile = new File(tmpPath);
        FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), logTmpFile);
        if (!logTmpFile.exists()) {
            throw new RuntimeException("上传日志文件异常");
        }
        String obsFilePath = ProjNetworkDetHospitalServiceImpl.getObsNetworkTestLogPath() + fileName;
        OBSClientUtils.uploadFile(logTmpFile, obsFilePath);
        FileUtils.forceDelete(logTmpFile);
        return Result.success();
    }

    /**
     * 文件上传
     *
     * @param multipartFile 数据传输对象
     */
    @ApiOperation("上传obs网路检测文件")
    @PostMapping("/uploadObsFilesByHandle")
    @CsmSign
    public Result uploadObsFilesByHandle(@RequestParam("sysType") int sysType,
                                         @RequestParam("file") MultipartFile multipartFile)
            throws Exception {
        log.info("fileName: {}", multipartFile.getOriginalFilename());
        String fileName = multipartFile.getOriginalFilename();
        String tmpPath = DEFAULT_TMP_PATH + StrUtil.SLASH + "networkdetectlog" + StrUtil.SLASH + fileName;
        File logTmpFile = new File(tmpPath);
        FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), logTmpFile);
        if (!logTmpFile.exists()) {
            throw new RuntimeException("上传日志文件异常");
        }
        String obsFilePath = ObsNetDetectFile.getEnumByCode(sysType).getPath();
        OBSClientUtils.uploadFile(logTmpFile, obsFilePath);
        FileUtils.forceDelete(logTmpFile);
        return Result.success();
    }
}
