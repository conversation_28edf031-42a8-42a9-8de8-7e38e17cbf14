package com.msun.csm.controller.formlibrary;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.vo.DocumentBase64VO;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.formlibrary.LibFormParamerReq;
import com.msun.csm.model.req.formlibrary.LibFormReq;
import com.msun.csm.model.req.formlibrary.PreviewStyleReq;
import com.msun.csm.model.req.formlibrary.ToolLimitReq;
import com.msun.csm.model.resp.formlibrary.LibProductFormPageResp;
import com.msun.csm.model.resp.formlibrary.LibProductFormResp;
import com.msun.csm.model.resp.formlibrary.ProjProductFormLogResp;
import com.msun.csm.service.formlibrary.LibProductFormService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */
@Api(tags = "表单资源库")
@RestController
@RequestMapping("/productFormLibrary")
@Slf4j
public class ProductFormLibraryController {

    @Resource
    private LibProductFormService libProductFormService;

    /**
     * 分页查询资源库信息
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "分页查询资源库信息", operDetail = "分页查询资源库信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询资源库信息")
    @ApiOperation("分页查询资源库信息")
    @PostMapping(value = "/selectLibFormByPage")
    public Result<LibProductFormPageResp<LibProductFormResp>> selectLibFormByPage(@RequestBody LibFormReq libFormReq) {
        return libProductFormService.selectLibFormByPage(libFormReq);
    }

    /**
     * 分页查询日志信息
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "分页查询日志信息", operDetail = "分页查询日志信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询日志信息")
    @ApiOperation("分页查询日志信息")
    @PostMapping(value = "/selectLibFormLogByPage")
    public Result<PageInfo<ProjProductFormLogResp>> selectLibFormLogByPage(@RequestBody LibFormReq libFormReq) {
        return libProductFormService.selectLibFormLogByPage(libFormReq);
    }

    /**
     * 拉取资源库数据
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "拉取资源库数据", operDetail = "拉取资源库数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "拉取资源库数据")
    @ApiOperation("拉取资源库数据")
    @PostMapping(value = "/updateLibFormData")
    public Result updateLibFormData(@RequestBody LibFormParamerReq libFormReq) {
        return libProductFormService.updateLibFormData(libFormReq);
    }

    /**
     * 发送资源库数据
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "发送资源库数据", operDetail = "发送资源库数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "发送资源库数据")
    @ApiOperation("发送资源库数据")
    @PostMapping(value = "/sendLibFormData")
    public Result sendLibFormData(@RequestBody LibFormParamerReq libFormReq) {
        return libProductFormService.sendLibFormData(libFormReq);
    }

    /**
     * 获取跳转路径数据
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "获取跳转路径数据", operDetail = "获取跳转路径数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取跳转路径数据")
    @ApiOperation("获取跳转路径数据")
    @PostMapping(value = "/getLibFormJumpPathData")
    public Result getLibFormJumpPathData(@RequestBody LibFormParamerReq libFormReq) {
        return libProductFormService.getLibFormJumpPathData(libFormReq);
    }

    /**
     * 获取限制项目工具列表数据
     *
     * @param libFormReq
     * @return
     */
    @Log(operName = "获取限制项目工具列表数据", operDetail = "获取限制项目工具列表数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取跳转路径数据")
    @ApiOperation("获取限制项目工具列表数据")
    @PostMapping(value = "/getIsToolLimit")
    public Result getIsToolLimit(@RequestBody ToolLimitReq libFormReq) {
        return libProductFormService.getIsToolLimit(libFormReq);
    }

    @Log(operName = "预览样式", operDetail = "预览样式", intLogType = Log.IntLogType.SELF_SYS, cnName = "预览样式")
    @ApiOperation("预览样式")
    @PostMapping(value = "/getPreviewStyle")
    public Result<DocumentBase64VO> getPreviewStyle(@RequestBody PreviewStyleReq previewStyleReq) {
        return libProductFormService.getPreviewStyle(previewStyleReq);
    }

}
