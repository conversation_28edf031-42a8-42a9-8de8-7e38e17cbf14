package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.GetBoardRecordParam;
import com.msun.csm.model.dto.BackendOperationBoardInfo;
import com.msun.csm.service.proj.BackendOperationBoardService;

import lombok.extern.slf4j.Slf4j;

/**
 * 后端运维项目进度看板
 */
@Slf4j
@RestController
@RequestMapping("/backendOperationBoard")
public class BackendOperationBoardController {

    @Resource
    private BackendOperationBoardService backendOperationBoardService;

    /**
     * 获取看板数据列表
     *
     * @param param 参数
     * @return 看板数据列表
     */
    @PostMapping("/getBoardRecord")
    public Result<List<BackendOperationBoardInfo>> getBoardRecord(@RequestBody GetBoardRecordParam param) {
        try {
            List<BackendOperationBoardInfo> boardRecord = backendOperationBoardService.getBoardRecord(param);
            return Result.success(boardRecord, "成功");
        } catch (Exception e) {
            log.error("获取看板数据列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

}
