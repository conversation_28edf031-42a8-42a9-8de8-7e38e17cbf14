package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.dao.entity.proj.ClassificationScoreVO;
import com.msun.csm.dao.entity.proj.CommonScoreRecordVO;
import com.msun.csm.dao.entity.proj.InitProductFunctionResultVO;
import com.msun.csm.dao.entity.proj.ProductFunctionUseInfoVO;
import com.msun.csm.dao.entity.proj.ProjectAcceptanceMenuResultVO;
import com.msun.csm.dao.entity.proj.ProjectAcceptanceRecordVO;
import com.msun.csm.model.SimulationStatisticalDataVO;
import com.msun.csm.model.param.DeleteProcessDeductionParam;
import com.msun.csm.model.param.InitProductFunctionParam;
import com.msun.csm.model.param.ProjectAndCustomParam;
import com.msun.csm.model.param.QueryClassificationScoreParam;
import com.msun.csm.model.param.QueryProductFunctionScoreParam2;
import com.msun.csm.model.param.QueryProductFunctionScoreParam3;
import com.msun.csm.model.param.QueryProjectAcceptanceMenuParam;
import com.msun.csm.model.param.QueryProjectAcceptanceRecordParam;
import com.msun.csm.model.param.QueryScoreSummaryParam;
import com.msun.csm.model.param.SaveClassificationScoreParam;
import com.msun.csm.model.param.SaveDeductionParam;
import com.msun.csm.model.param.StartCheckUseCountParam2;
import com.msun.csm.service.proj.ProjectCheckAndAcceptService;

/**
 * 监管中心-项目验收
 */
@Api(tags = "监管中心-项目验收")
@Slf4j
@RestController
@RequestMapping("/projectCheckAndAccept")
public class ProjectCheckAndAcceptController {

    @Resource
    private ProjectCheckAndAcceptService projectCheckAndAcceptService;

    /**
     * 查询项目验收记录
     */
    @PostMapping(value = "/queryProjectAcceptanceRecord")
    Result<PageList<ProjectAcceptanceRecordVO>> queryProjectAcceptanceRecord(@Validated @RequestBody QueryProjectAcceptanceRecordParam param, BindingResult bindingResult) {
        log.info("查询项目验收记录，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询项目验收记录，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(this.projectCheckAndAcceptService.queryProjectAcceptanceRecord(param));
        } catch (Exception e) {
            log.error("查询项目验收记录，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询左侧菜单
     */
    @PostMapping(value = "/queryMenu")
    Result<ProjectAcceptanceMenuResultVO> queryMenu(@Validated @RequestBody QueryProjectAcceptanceMenuParam param, BindingResult bindingResult) {
        log.info("查询左侧菜单，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询左侧菜单，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(projectCheckAndAcceptService.queryMenu(param));
        } catch (Exception e) {
            log.error("查询左侧菜单，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 初始化产品功能应用
     */
    @PostMapping(value = "/initProductFunctionDetail")
    Result<InitProductFunctionResultVO> initProductFunctionDetail(@Validated @RequestBody InitProductFunctionParam param, BindingResult bindingResult) {
        log.info("初始化产品功能应用，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("初始化产品功能应用，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(projectCheckAndAcceptService.initProductFunctionDetail(param));
        } catch (Exception e) {
            log.error("初始化产品功能应用，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询产品应用功能点使用情况
     */
    @PostMapping(value = "/queryProductFunctionUseInfo")
    Result<List<ProductFunctionUseInfoVO>> queryProductFunctionUseInfo(@Validated @RequestBody QueryProductFunctionScoreParam3 param, BindingResult bindingResult) {
        log.info("查询产品应用功能点使用情况，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询产品应用功能点使用情况，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(this.projectCheckAndAcceptService.queryProductFunctionUseInfo(param));
        } catch (Exception e) {
            log.error("查询产品应用功能点使用情况，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询产品功能应用扣分明细
     */
    @PostMapping(value = "/queryProductFunctionDeduction")
    Result<List<CommonScoreRecordVO>> queryProductFunctionDeduction(@Validated @RequestBody QueryProductFunctionScoreParam2 param, BindingResult bindingResult) {
        log.info("查询产品功能应用扣分明细，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询产品功能应用扣分明细，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(this.projectCheckAndAcceptService.queryProductFunctionDeduction(param));
        } catch (Exception e) {
            log.error("查询产品功能应用扣分明细，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 人工扣分
     */
    @PostMapping(value = "/saveDeduction")
    Result<Boolean> saveDeduction(@Validated @RequestBody SaveDeductionParam param, BindingResult bindingResult) {
        log.info("人工扣分，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("人工扣分，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(this.projectCheckAndAcceptService.saveDeduction(param));
        } catch (NumberFormatException e) {
            log.error("人工扣分，数字转换异常，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("扣分仅允许输入数字");
        } catch (Exception e) {
            log.error("人工扣分，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询产品功能应用评分
     */
    @PostMapping(value = "/queryClassificationScore")
    Result<ClassificationScoreVO> queryClassificationScore(@Validated @RequestBody QueryClassificationScoreParam param, BindingResult bindingResult) {
        log.info("查询产品功能应用评分，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询产品功能应用评分，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(this.projectCheckAndAcceptService.queryClassificationScore(param));
        } catch (Exception e) {
            log.error("查询产品功能应用评分，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存产品功能应用评分
     */
    @PostMapping(value = "/saveClassificationScore")
    Result<Boolean> saveClassificationScore(@Validated @RequestBody SaveClassificationScoreParam param, BindingResult bindingResult) {
        log.info("保存产品功能应用评分，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("保存产品功能应用评分，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(projectCheckAndAcceptService.saveClassificationScore(param));
        } catch (Exception e) {
            log.error("保存产品功能应用评分，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询评分汇总
     */
    @PostMapping(value = "/queryScoreSummary")
    Result<SimulationStatisticalDataVO> queryScoreSummary(@Validated @RequestBody QueryScoreSummaryParam param, BindingResult bindingResult) {
        log.info("查询评分汇总，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询评分汇总，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(projectCheckAndAcceptService.queryScoreSummary(param));
        } catch (Exception e) {
            log.error("查询评分汇总，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 开始检测云健康应用功能点使用次数
     */
    @PostMapping(value = "/startCheckUseCount")
    Result<Boolean> startCheckUseCount(@Validated @RequestBody ProjectAndCustomParam param, BindingResult bindingResult) {
        log.info("开始检测云健康应用功能点使用次数，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("开始检测云健康应用功能点使用次数，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(this.projectCheckAndAcceptService.startCheckUseCount2(param.getProjectInfoId(), param.getCustomInfoId()));
        } catch (Exception e) {
            log.error("开始检测云健康应用功能点使用次数，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 新增产品应用功能点扣分
     */
    @PostMapping(value = "/addProductFunctionDeduction")
    Result<Boolean> addProductFunctionDeduction(@Validated @RequestBody StartCheckUseCountParam2 param, BindingResult bindingResult) {
        log.info("新增产品应用功能点扣分，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("新增产品应用功能点扣分，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(this.projectCheckAndAcceptService.addProductFunctionDeduction(param));
        } catch (Exception e) {
            log.error("新增产品应用功能点扣分，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 删除产品应用功能点扣分
     */
    @PostMapping(value = "/deleteProductFunctionDeduction")
    Result<Boolean> deleteProductFunctionDeduction(@Validated @RequestBody DeleteProcessDeductionParam param, BindingResult bindingResult) {
        log.info("删除产品应用功能点扣分，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("删除产品应用功能点扣分，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        try {
            return Result.success(this.projectCheckAndAcceptService.deleteProductFunctionDeduction(param));
        } catch (Exception e) {
            log.error("删除产品应用功能点扣分，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

}
