package com.msun.csm.controller.proj;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.CsmPageResult;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.YyProductId;
import com.msun.csm.dao.entity.proj.ProductFunctionOperationLogVO;
import com.msun.csm.dao.entity.proj.ProductFunctionVO;
import com.msun.csm.model.param.QueryProductFunctionListParam;
import com.msun.csm.model.param.QueryProductFunctionOperationLogParam;
import com.msun.csm.model.param.SaveProductFunctionParam;
import com.msun.csm.service.proj.ProductFunctionMaintainService;

/**
 * 产品应用功能点维护
 */
@Slf4j
@RestController
@RequestMapping("/productFunctionMaintain")
public class ProductFunctionMaintainController {

    @Resource
    private ProductFunctionMaintainService productFunctionMaintainService;

    /**
     * 查询医院类型
     */
    @ResponseBody
    @PostMapping(value = "/queryHospitalType")
    Result<List<BaseCodeNameResp>> queryHospitalType() {
        try {
            return Result.success(productFunctionMaintainService.queryHospitalType());
        } catch (Exception e) {
            log.error("查询医院类型，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存功能点信息（新增or修改）
     */
    @ResponseBody
    @PostMapping(value = "/saveProductFunction")
    Result<Void> saveProductFunction(@RequestBody SaveProductFunctionParam param) {
        log.info("保存功能点信息，参数={}", JSON.toJSONString(param));
        try {
            boolean result = productFunctionMaintainService.saveProductFunction(param);
            return Result.success();
        } catch (Exception e) {
            log.error("保存功能点信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询功能点列表
     */
    @ResponseBody
    @PostMapping(value = "/queryProductFunctionList")
    Result<CsmPageResult<ProductFunctionVO>> queryProductFunctionList(@RequestBody QueryProductFunctionListParam param) {
        log.info("查询功能点列表，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(productFunctionMaintainService.queryProductFunctionList(param));
        } catch (Exception e) {
            log.error("查询功能点列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }


    /**
     * 导出产品应用功能点
     *
     * @param param    医院信息ID
     * @param response 请求响应
     */
    @RequestMapping("/exportProductFunctionExcel")
    void exportProductFunctionExcel(@RequestBody QueryProductFunctionListParam param, HttpServletResponse response) {
        log.info("导出产品应用功能点，参数={}", JSON.toJSONString(param));
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            String name = "产品应用功能点" + "-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + ".xls";
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name));
            productFunctionMaintainService.exportProductFunctionExcel(param, outputStream);
        } catch (Exception e) {
            log.error("导出产品应用功能点，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 查询功能点操作日志
     */
    @ResponseBody
    @PostMapping(value = "/queryProductFunctionOperationLog")
    Result<CsmPageResult<ProductFunctionOperationLogVO>> queryProductFunctionOperationLog(@RequestBody QueryProductFunctionOperationLogParam param) {
        log.info("查询功能点操作日志，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(productFunctionMaintainService.queryProductFunctionOperationLog(param));
        } catch (Exception e) {
            log.error("查询功能点操作日志，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 下载产品应用功能点导入模板
     *
     * @param param    产品ID
     * @param response 请求响应
     */
    @RequestMapping("/downloadProductFunctionExcelTemplate")
    void downloadProductFunctionExcelTemplate(@RequestBody YyProductId param, HttpServletResponse response) {
        log.info("下载产品应用功能点导入模板");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            String name = "产品应用功能点导入模板" + "-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + ".xls";
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name));
            productFunctionMaintainService.downloadProductFunctionExcelTemplate(param, outputStream);
        } catch (Exception e) {
            log.error("下载产品应用功能点导入模板，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 导入产品应用功能点
     */
    @PostMapping(value = "/importProductFunctionFromExcel")
    Result<Void> importProductFunctionFromExcel(@RequestParam("file") MultipartFile multipartFile) {
        log.info("导入产品应用功能点");
        return productFunctionMaintainService.importProductFunctionFromExcel(multipartFile);
    }

}
