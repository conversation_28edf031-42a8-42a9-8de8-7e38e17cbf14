package com.msun.csm.controller.api.tduck;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.controller.tduck.TduckApi;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.tduck.req.CanEditFormReq;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq2;
import com.msun.csm.model.vo.ProjProductBacklogDataVO;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.service.proj.ProjProductBacklogService;
import com.msun.csm.service.proj.ProjProjectConfigService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectPlanService;
import com.msun.csm.service.proj.ProjSurveyPlanService;
import com.msun.csm.service.proj.SupervisionCenterService;

/**
 * @Author: MengChuAn
 * @Date: 2024/7/29 15:36
 */
@Slf4j
@RestController
public class TduckApiImpl implements TduckApi {


    @Resource
    private ProjSurveyPlanService surveyPlanService;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjProjectConfigService projectConfigService;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private SupervisionCenterService supervisionCenterService;

    /**
     * 调研提交完成调研计划
     *
     * @param updateSurveyPlanStatus
     * @return
     */
    @CsmSign
    @Override
    public Result updateSurveyPlanStatus(UpdateSurveyPlanStatusReq updateSurveyPlanStatus) {
        return surveyPlanService.updateSurveyPlanStatus(updateSurveyPlanStatus);
    }

    @ACROSS
    @Override
    public Result<Void> updateSatisfactionSurveyStatus(UpdateSurveyPlanStatusReq2 updateSurveyPlanStatus) {
        try {
            return supervisionCenterService.updateSatisfactionSurveyStatus(updateSurveyPlanStatus);
        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存待处理任务和配置数据
     *
     * @param saveBackLogAndDetailReq
     * @return
     */
    @CsmSign
    @Override
    public Result saveBackLogAndDetail(SaveBackLogAndDetailReq saveBackLogAndDetailReq) {
        log.info("填鸭系统调用生成待办，参数：{}", JSON.toJSONString(saveBackLogAndDetailReq));
        try {
            return surveyPlanService.saveBackLogAndDetail(saveBackLogAndDetailReq);
        } catch (Exception e) {
            log.error("填鸭系统调用生成待办，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("生成待办失败" + e.getMessage());
        }
    }


    @CsmSign
    @Override
    public Result<Boolean> canEditProductForm(CanEditFormReq param) {
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(param.getProjectInfoId());

        boolean planModel = projectConfigService.isPlanModel(param.getProjectInfoId());

        boolean smallFrontBigBack = projectInfoService.isSmallFrontBigBack(param.getProjectInfoId());
        // 提交产品业务调研
        if ("survey".equals(param.getSource())) {
            // 当前登录人是前端调研责任人
            if (param.getCurrentSysUserId().equals(param.getSurveyUserId())) {
                return Result.success(true, "成功");
            }
            // 当前登录人是前端项目经理
            if (param.getCurrentSysUserId().equals(projProjectInfo.getProjectLeaderId())) {
                return Result.success(true, "成功");
            }
            return Result.success(false, "成功");
        }

        // 提交项目经理确认后的最终调研结果
        if ("config".equals(param.getSource())) {
            // 当前登录人是前端调研责任人
            if (param.getCurrentSysUserId().equals(param.getSurveyUserId())) {
                return Result.success(true, "成功");
            }
            // 当前登录人是前端项目经理
            if (param.getCurrentSysUserId().equals(projProjectInfo.getProjectLeaderId())) {
                return Result.success(true, "成功");
            }
            // 当前登录人是准备阶段前端负责人
            if (param.getCurrentSysUserId().equals(this.getReadUser(param.getProjectInfoId(), param.getProjectInfoId(), param.getYyProductId()))) {
                return Result.success(true, "成功");
            }
            // 项目计划模式并且是小前端大后端
            if (planModel && smallFrontBigBack) {
                ProjProjectMember backLeader = projectInfoService.getBackLeader(param.getProjectInfoId());
                // 后端项目经理
                if (param.getCurrentSysUserId().equals(backLeader.getProjectMemberId())) {
                    return Result.success(true, "成功");
                }
                ProjProjectPlan surveyProductProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
                // 产品业务调研的后端专项负责人
                if (surveyProductProjectPlan != null && param.getCurrentSysUserId().equals(surveyProductProjectPlan.getBackendEngineerId())) {
                    return Result.success(true, "成功");
                }

                ProjProjectPlan preparatProductProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_PRODUCT);
                // 各产品准备工作的后端专项负责人
                if (preparatProductProjectPlan != null && param.getCurrentSysUserId().equals(preparatProductProjectPlan.getBackendEngineerId())) {
                    return Result.success(true, "成功");
                }

                ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectOne(new QueryWrapper<ProjSurveyPlan>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", param.getProjectInfoId())
                        .eq("hospital_info_id", param.getHospitalInfoId())
                        .eq("yy_product_id", param.getYyProductId())
                        .eq("survey_user_id", param.getSurveyUserId())
                        .eq("dept_name", param.getDeptName())
                );
                // 调研的后端审核人
                if (projSurveyPlan != null && param.getCurrentSysUserId().equals(projSurveyPlan.getAuditSysUserId())) {
                    return Result.success(true, "成功");
                }
            }

        }
        return Result.success(false, "成功");
    }

    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;
    @Resource
    private ProjProductBacklogService productBacklogService;

    private Long getReadUser(Long projectInfoId, Long hospitalInfoId, Long yyProductId) {
        ProjMilestoneInfo milestoneInfo = projMilestoneInfoMapper.selectOne(
                new QueryWrapper<ProjMilestoneInfo>()
                        .eq("project_info_id", projectInfoId)
                        .eq("milestone_node_code", "preparat_product")
                        .eq("invalid_flag", 0)
        );

        // 还没走到准备阶段，人员信息取项目经理
        if (milestoneInfo == null) {
            return null;
        }

        // 获取准备阶段任务计划
        ProjProductBacklogDTO dto = new ProjProductBacklogDTO();
        dto.setHospitalInfoId(hospitalInfoId);
        dto.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
        dto.setProjectInfoId(projectInfoId);
        try {
            ProjProductBacklogDataVO projProductBacklogDataVO = productBacklogService.selectProductBacklog(dto);
            List<ProjProductBacklogVO> backlogList = projProductBacklogDataVO.getBacklogList();
            // 准备阶段任务明细为空
            if (CollectionUtils.isEmpty(backlogList)) {
                return null;
            }
            // 先匹配产品
            ProjProductBacklogVO productTask = backlogList.stream().filter(item -> yyProductId.equals(item.getYyProductId())).findFirst().orElse(null);
            // 匹配到准备阶段产品责任人
            if (productTask != null && productTask.getUserId() != null) {
                return productTask.getUserId();
            }
            // 再匹配模块
            ProjProductBacklogVO moduleTask = backlogList.stream().filter(item -> yyProductId.equals(item.getYyProductModuleId())).findFirst().orElse(null);
            // 匹配到准备阶段产品责任人
            if (moduleTask != null && moduleTask.getUserId() != null) {
                return moduleTask.getUserId();
            }
            // 没匹配到准备阶段责任，取项目经理
            return null;
        } catch (Exception e) {
            log.error("判断调研问卷填写权限时获取准备阶段责任人信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return null;
        }
    }
}
