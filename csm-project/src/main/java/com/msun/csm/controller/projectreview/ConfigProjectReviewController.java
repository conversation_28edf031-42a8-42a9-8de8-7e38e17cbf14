package com.msun.csm.controller.projectreview;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projreview.ProjReviewAppDTO;
import com.msun.csm.model.dto.projreview.ProjReviewDTO;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewResp;
import com.msun.csm.service.config.projectreview.ConfigProjectReviewService;
import com.msun.csm.service.config.projectreview.DictProjectReviewTypeService;


/**
 * 项目审核模式配置表(ConfigProjectReview)控制器
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */

@RestController
@Api(tags = "项目审核模式配置表")
@RequestMapping("/configProjectReview")
@Slf4j
public class ConfigProjectReviewController {

    @Resource
    private ConfigProjectReviewService configProjectReviewService;
    @Resource
    private DictProjectReviewTypeService dictProjectReviewTypeService;

    /**
     * 项目审核配置 分页查询
     *
     * @param dto
     */
    @Log(operName = "项目审核配置 分页查询", operDetail = "项目审核配置 分页查询", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核配置 分页查询")
    @ApiOperation("项目审核配置 分页查询")
    @PostMapping(value = "/findDataPage")
    Result<PageInfo<ConfigProjectReviewResp>> findDataPage(@RequestBody ConfigProjectReviewReq dto) {
        return configProjectReviewService.findDataPage(dto);
    }

    /**
     * 项目审核配置字典保存修改
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目审核配置字典保存修改", operDetail = "项目审核配置字典保存修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核配置字典保存修改")
    @ApiOperation("项目审核配置字典保存修改")
    @PostMapping(value = "/saveData")
    Result<String> saveData(@RequestBody ConfigProjectReviewReq dto) {
        return configProjectReviewService.saveData(dto);
    }

    /**
     * 项目审核配置启用作废
     *
     * @param dto
     * @return
     */
    @Log(operName = "项目审核配置启用作废", operDetail = "项目审核配置启用作废", intLogType = Log.IntLogType.SELF_SYS, cnName = "项目审核配置启用作废")
    @ApiOperation("项目审核配置启用作废")
    @PostMapping(value = "/updateDelData")
    Result<String> updateDelData(@RequestBody ConfigProjectReviewReq dto) {
        return configProjectReviewService.updateDelData(dto);
    }

    /**
     * 查询审核方式字典
     * @return
     */
    @Log(operName = "查询审核方式字典", operDetail = "查询审核方式字典", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询审核方式字典")
    @ApiOperation("查询审核方式字典")
    @PostMapping(value = "/findReviewMethodTypeDict")
    Result<List<BaseIdNameResp>> findReviewMethodTypeDict() {
        return dictProjectReviewTypeService.findReviewMethodTypeDict();
    }

    /**
     * 查询全部审核类型字典
     * @return
     */
    @Log(operName = "查询全部审核类型字典", operDetail = "查询全部审核类型字典", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询全部审核类型字典")
    @ApiOperation("查询全部审核类型字典")
    @PostMapping(value = "/findAllReviewTypeDict")
    Result<List<BaseIdNameResp>> findAllReviewTypeDict() {
        return dictProjectReviewTypeService.findAllReviewTypeDict();
    }


    /**
     * 查询项目审核各个阶段数据集合
     *
     * @param dto 参数
     * @return 查询项目审核各个阶段数据集合
     */
    @Log (operName = "查询项目审核各个阶段数据集合", operDetail = "查询项目审核各个阶段数据集合", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目审核各个阶段数据集合")
    @ApiOperation ("查询项目审核各个阶段数据集合")
    @ResponseBody
    @PostMapping (value = "/selectDataByParam")
    Result<ProjReviewAppDTO> selectDataByParam(@RequestBody ProjReviewDTO dto) {
        try {
            return dictProjectReviewTypeService.selectDataByParam(dto);
        } catch (Exception e) {
            log.error("查询项目审核各个阶段数据集合，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询项目审核各个阶段数据集合 , " + e.getMessage());
        }
    }

}
