package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjTaskProgress;
import com.msun.csm.service.proj.ProjTaskProgressService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/19/10:39
 */
@Slf4j
@RestController
@RequestMapping ("/taskProgress")
@Api (tags = "进度详情")
public class ProjTaskProgressController {

    @Resource
    private ProjTaskProgressService projTaskProgressService;


    /**
     * 根据具体的数据id查询进度详情信息
     *
     * @return
     */
    @Log (operName = "根据具体的数据id查询进度详情信息", operDetail = "根据具体的数据id查询进度详情信息",
            operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "根据具体的数据id查询进度详情信息")
    @GetMapping ("/selectTaskProgressList")
    Result<List<ProjTaskProgress>> selectTaskProgressList(@RequestParam ("sourceTypeId") Long sourceTypeId) {
        return projTaskProgressService.selectTaskProgressList(sourceTypeId);
    }
}
