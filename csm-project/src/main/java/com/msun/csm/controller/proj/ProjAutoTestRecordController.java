package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.core.component.implementation.api.autotest.vo.AutoTestPharmacyDeptInfoVO;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.autotest.ExecuteDetectDTO;
import com.msun.csm.model.dto.autotest.ManuChangeStatusDTO;
import com.msun.csm.model.dto.autotest.ProjAutoTestRecordDTO;
import com.msun.csm.model.dto.autotest.ProjAutoTestRecordProjMainDTO;
import com.msun.csm.model.vo.AutoTestProjectMainVO;
import com.msun.csm.model.vo.ProjAutoTestRecordVO;
import com.msun.csm.service.proj.ProjAutoTestRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2025-02-18 03:07:31
 */
@Slf4j
@RestController
@RequestMapping("/projAutoTestRecord")
@Api(tags = "自动化测试记录, 含基础数据测试与业务测试相关接口")
public class ProjAutoTestRecordController {

    @Resource
    private ProjAutoTestRecordService projAutoTestRecordService;

    /**
     * 查询医院测试合集
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询医院测试合集")
    @Log(operName = "查询医院测试合集", operDetail = "查询医院测试合集", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询医院测试合集")
    @PostMapping("/findAutoTestDataList")
    Result<PageInfo<ProjAutoTestRecordVO>> findAutoTestDataList(@Valid @RequestBody ProjAutoTestRecordDTO dto) {
        return projAutoTestRecordService.findAutoTestDataList(dto);
    }

    /**
     * 查询项目详情
     *
     * @param dto 含客户id, 项目类型
     * @return 含域名等项目信息, 提供页面显示
     */
    @ApiOperation("查询项目详情")
    @Log(operName = "查询项目详情", operDetail = "查询项目详情", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询项目详情")
    @PostMapping("/getProjectMainInfo")
    Result<AutoTestProjectMainVO> getProjectMainInfo(@Valid @RequestBody ProjAutoTestRecordProjMainDTO dto) {
        return projAutoTestRecordService.getProjectMainInfo(dto);
    }

    /**
     * 查询云健康科室信息, 提供前端选择科室使用
     *
     * @param hospitalInfoId 医院id
     * @return 含域名等项目信息, 提供页面显示
     */
    @ApiOperation("查询云健康科室信息")
    @Log(operName = "查询云健康科室信息", operDetail = "查询云健康科室信息, 提供前端选择科室使用", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询云健康科室信息")
    @GetMapping("/findPharmacyDeptInfo")
    Result<List<AutoTestPharmacyDeptInfoVO>> findPharmacyDeptInfo(@NotNull @RequestParam("hospitalInfoId") Long hospitalInfoId) {
        return projAutoTestRecordService.findPharmacyDeptInfo(hospitalInfoId);
    }

    /**
     * 手动执行医院检测. 支持单个医院检测和一键执行医院检测,
     * 会根据cloudHospitalId不为空进行判断, 若有值对单个医院进行检测, 与此同时, 若projAutoTestRecordId不为空,
     * 会查询对应数据进行检测, 若为空, 会根据cloudHospitalId进行检测
     *
     * @param dto 含客户id, 项目类型, 云健康医院id等信息
     * @return 成功失败提示
     */
    @ApiOperation(value = "手动执行医院检测")
    @Log(operName = "查询项目详情", operDetail = "手动执行单个医院检测", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "手动执行单个医院检测")
    @PostMapping("/executeDetect")
    Result<String> executeDetect(@Valid @RequestBody ExecuteDetectDTO dto) {
        return projAutoTestRecordService.executeDetect(dto);
    }

    /**
     * 人工判定测试结果
     *
     * @param dto 含测试记录主键、状态标识等
     * @return 成功失败提示
     */
    @ApiOperation("manuChangeDetectResult")
    @Log(operName = "人工判定测试结果", operDetail = "人工判定测试结果", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "人工判定测试结果")
    @PostMapping("/manuChangeDetectResult")
    Result<String> manuChangeDetectResult(@Valid @RequestBody ManuChangeStatusDTO dto) {
        return projAutoTestRecordService.manuChangeDetectResult(dto);
    }

}
