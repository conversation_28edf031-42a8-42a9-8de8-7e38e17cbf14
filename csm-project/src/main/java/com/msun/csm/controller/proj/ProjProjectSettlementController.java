package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.util.obs.OBSClientUtils;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-17 05:11:00
 */
@Slf4j
@RestController
@RequestMapping("/projProjectSettlement")
@Api(tags = "入驻申请-项目入驻信息表相关接口")
public class ProjProjectSettlementController {

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Log(operName = "获取云资源开通确认单证明文件", operDetail = "获取云资源开通确认单证明文件", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "获取云资源开通确认单证明文件", saveParam = true)
    @GetMapping("getCloudFirmDownload/{fileName}")
    public void getCloudFirmDownload(HttpServletResponse response, @NotNull @RequestParam("projectFileId") String projectFileId) {
        ProjProjectFile projectFile = projectFileMapper.selectByPrimaryKey(Long.parseLong(projectFileId));
        OBSClientUtils.downloadDirectFile(response, projectFile.getFilePath(), projectFile.getFileName());
    }

}
