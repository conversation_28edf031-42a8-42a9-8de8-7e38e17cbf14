package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.CompSurveyProductMilestone;
import com.msun.csm.model.dto.ProjMilestoneInfoDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.param.UpdateProjMilestoneInfoAfterAuditPmoParam;
import com.msun.csm.model.resp.project.MileStoneInfoResp;
import com.msun.csm.service.proj.ProjMilestoneInfoService;

/**
 * @description:
 * @fileName:
 * @author:zhouzhaoyu
 * @updateBy:
 * @Date:Created in 17:26 2024/5/6
 * @remark:
 */

@Api(tags = "里程碑信息")
@RestController
@RequestMapping("/milestone")
@Slf4j
public class ProjMilestoneInfoController {


    @Resource
    private ProjMilestoneInfoService projMilestoneInfoService;

    /**
     * 初始化里程碑节点信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "初始化里程碑节点信息", operDetail = "初始化里程碑节点信息-里程碑",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "初始化里程碑节点信息")
    @ApiOperation("初始化里程碑节点信息")
    @PostMapping("/generateMilestoneInfo")
    Result<MileStoneInfoResp> getProductList(@RequestBody ProjMilestoneInfoDTO dto) {
        return projMilestoneInfoService.generateMilestoneInfo(dto);
    }

    /**
     * 说明: 校验当前节点能否操作
     *
     * @param id
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.resp.project.MileStoneInfoReq>
     * @author: Yhongmin
     * @createAt: 2024/5/10 11:44
     * @remark: Copyright
     */
    @Log(operName = "校验当前节点能否操作", operDetail = "校验当前节点能否操作-里程碑",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "校验当前节点能否操作")
    @ApiOperation("校验当前节点能否操作")
    @GetMapping("/verifyProjMilestoneInfo")
    Result<MileStoneInfoResp> verifyProjMilestoneInfo(
            @ApiParam(value = "节点id", required = true) @RequestParam(value = "id") Long id) {
        return projMilestoneInfoService.verifyProjMilestoneInfoVO(id);
    }

    /**
     * 说明: 是否完成入驻
     *
     * @param projectInfoId
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.resp.project.MileStoneInfoReq>
     * @author: Yhongmin
     * @createAt: 2024/5/10 11:44
     * @remark: Copyright
     */
    @Log(operName = "是否完成PMO审核", operDetail = "是否完成PMO审核-里程碑",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "是否完成PMO审核")
    @ApiOperation("是否完成PMO审核,false:未完成;true:已完成")
    @GetMapping("/verifyStageEntryPmo")
    Result<Boolean> verifyStageEntryPmo(
            @ApiParam(value = "项目id", required = true) @RequestParam(value = "projectInfoId") Long projectInfoId) {
        return projMilestoneInfoService.verifyStageEntry(projectInfoId);
    }

    /**
     * 更改里程碑节点信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "更改里程碑节点信息", operDetail = "更改里程碑节点信息-里程碑",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "更改里程碑节点信息")
    @ApiOperation("更改里程碑节点信息")
    @PostMapping("/updateMilestone")
    Result<Boolean> updateMilestone(@RequestBody @Valid UpdateMilestoneDTO dto) {
        log.info("ProjMilestoneInfoController.updateMilestone更新里程碑节点，参数={}", JSON.toJSONString(dto));
        return Result.success(projMilestoneInfoService.updateMilestone(dto));
    }

    /**
     * 更改里程碑节点信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "完成里程碑节点信息", operDetail = "完成里程碑节点信息-里程碑",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "更改里程碑节点信息")
    @ApiOperation("完成里程碑节点信息")
    @PostMapping("/compMilestone")
    Result<Boolean> compMilestone(@RequestBody @Valid UpdateMilestoneDTO dto) {
        return projMilestoneInfoService.compMilestone(dto);
    }

    /**
     * 提交调研完成回更里程碑节点信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "提交调研完成回更里程碑节点信息", operDetail = "提交调研完成回更里程碑节点信息-里程碑",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "提交调研完成回更里程碑节点信息")
    @ApiOperation("提交调研完成回更里程碑节点信息")
    @PostMapping("/compSurveyProductMilestone")
    Result<Boolean> compSurveyProductMilestone(@RequestBody @Valid CompSurveyProductMilestone dto) {
        try {
            return projMilestoneInfoService.compSurveyProductMilestone(dto);
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 更改里程碑节点信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "完成里程碑节点信息-特殊节点更新", operDetail = "完成里程碑节点信息-特殊节点更新",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "完成里程碑节点信息-特殊节点更新")
    @ApiOperation("完成里程碑节点信息-特殊节点更新")
    @PostMapping("/compMilestoneSpetial")
    Result<Boolean> compMilestoneSpetial(@RequestBody @Valid UpdateMilestoneDTO dto) {
        return projMilestoneInfoService.compMilestoneSpetial(dto);
    }


    @CsmSign
    @Log(operName = "回退里程碑节点状态", operDetail = "回退里程碑节点状态", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "回退里程碑节点状态")
    @ApiOperation("老系统通知运营平台更新状态失败后调用此接口回退里程碑节点状态")
    @PostMapping(value = "/revertProjMilestoneInfoStatus")
    Result<Boolean> revertProjMilestoneInfoStatus(@RequestBody UpdateProjMilestoneInfoAfterAuditPmoParam param) {
        log.info("老系统通知运营平台更新状态失败后调用此接口回退里程碑节点状态，参数={}", JSON.toJSONString(param));
        try {
            return Result.success(projMilestoneInfoService.revertProjMilestoneInfoStatus(param));
        } catch (Exception e) {
            log.error("老系统通知运营平台更新状态失败后调用此接口回退里程碑节点状态，发生异常，errMsg={}，stackInfo=",
                    e.getMessage(), e);
            return Result.fail();
        }
    }

    /**
     * 说明: 查询当前项目状态，校验当前节点可进行的操作 （查看权/ 操作权）
     * * 改为判断已入驻并且已部署的 状态1，才允许进行操作，否则只能查看
     * * 已部署未入驻 状态2
     * * 已入驻未部署 状态 3
     * * 未入驻未部署 状态 0
     * 返回结果是1已入驻并且已部署的可操作
     *
     * @param projectInfoId
     * @return
     */
    @Log(operName = "查询当前项目状态，校验当前节点可进行的操作", operDetail = "查询当前项目状态，校验当前节点可进行的操作",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询当前项目状态，校验当前节点可进行的操作")
    @ApiOperation("查询当前项目状态，校验当前节点可进行的操作")
    @GetMapping("/verifyProjMilestoneInfoIsCanUseType")
    Result<Integer> verifyProjMilestoneInfoIsCanUseType(@RequestParam(value = "projectInfoId") Long projectInfoId) {
        return projMilestoneInfoService.verifyProjMilestoneInfoIsCanUseType(projectInfoId);
    }

    /**
     * 表单制作、打印报表制作、统计报表制作点击确认完成之前的校验
     */
    @Log(operName = "完成里程碑节点信息", operDetail = "完成里程碑节点信息-里程碑", intLogType = Log.IntLogType.SELF_SYS, cnName = "更改里程碑节点信息")
    @PostMapping("/preCompleteMilestoneValidate")
    Result<Boolean> preCompleteMilestoneValidate(@RequestBody @Valid UpdateMilestoneDTO dto) {
        return projMilestoneInfoService.preCompleteMilestoneValidate(dto);
    }
}
