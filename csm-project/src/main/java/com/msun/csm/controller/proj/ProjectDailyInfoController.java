package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.ProjectAndHospitalInfoId;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dailyreport.resp.RiskProjectResp;
import com.msun.csm.dao.entity.oldimsp.ThirdInterfaceProgress;
import com.msun.csm.dao.entity.proj.ProjectDailyInfo;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.service.message.ProjectDailyInfoService;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目日报
 */
@Slf4j
@RestController
@RequestMapping("/projectDailyInfo")
public class ProjectDailyInfoController {

    @Resource
    private ProjectDailyInfoService projectDailyInfoService;

    /**
     * 获取项目日报内容
     *
     * @param param 参数
     * @return 项目日报内容
     */
    @Log(operName = "获取项目日报内容", operDetail = "获取项目日报内容", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取项目日报内容")
    @PostMapping("/getProjectDailyInfo")
    Result<ProjectDailyInfo> getProjectDailyInfo(@RequestBody ProjectInfoId param) {
        return Result.success(projectDailyInfoService.getProjectDailyInfo(param.getProjectInfoId()));
    }

    /**
     * 手动发送项目进度日报
     */
    @RequestMapping("/sendProjectDailyMessage")
    Result<Void> sendProjectDailyMessage() {
        try {
            projectDailyInfoService.sendProjectDailyMessage();
            return Result.success();
        } catch (Exception e) {
            log.error("手动发送项目进度日报，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @RequestMapping("/clearFinishedFlag")
    Result<Boolean> clearFinishedFlag(Long projectInfoId) {
        try {
            return Result.success(projectDailyInfoService.clearFinishedFlag(projectInfoId));
        } catch (Exception e) {
            log.error("手动发送项目进度日报，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 获取三方接口进度
     *
     * @param param 参数
     * @return 三方接口进度
     */
    @Log(operName = "获取三方接口进度", operDetail = "获取三方接口进度", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取三方接口进度")
    @PostMapping("/getThirdInterfaceProgress")
    Result<List<ThirdInterfaceProgress>> getThirdInterfaceProgress(@RequestBody ProjectInfoId param) {
        try {
            return Result.success(projectDailyInfoService.getThirdInterfaceProgress(param.getProjectInfoId()));
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
    }


    /**
     * 获取各产品准备工作进度
     *
     * @param param 参数
     * @return 各产品准备工作进度
     */
    @Log(operName = "获取各产品准备工作进度", operDetail = "获取各产品准备工作进度", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取各产品准备工作进度")
    @PostMapping("/getProductBacklogProgress")
    Result<List<ProjProductBacklogVO>> getProductBacklogProgress(@RequestBody ProjectAndHospitalInfoId param) {
        return Result.success(projectDailyInfoService.getProductBacklogProgress(param.getProjectInfoId(), param.getHospitalInfoId()));
    }

    /**
     * 查询入驻、上线有时间风险的项目数据
     * 入驻四天没上线的、上线三天没申请验收的首期项目
     *
     * @return 三方接口进度
     */
    @Log(operName = "查询入驻、上线有时间风险的项目数据", operDetail = "查询入驻、上线有时间风险的项目数据",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "查询入驻、上线有时间风险的项目数据")
    @PostMapping("/getRiskProjects")
    Result<RiskProjectResp> getRiskProjects() {
        return Result.success(projectDailyInfoService.getRiskProjects());
    }


}
