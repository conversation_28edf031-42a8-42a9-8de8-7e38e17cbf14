package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.model.req.project.AddSplitProcessReq;
import com.msun.csm.model.req.project.AuditSplitProcessReq;
import com.msun.csm.model.req.project.ProjectSplitReq;
import com.msun.csm.model.resp.project.SplitProcessDetailResp;
import com.msun.csm.service.proj.ProjProjectSplitProcessService;
import com.msun.csm.service.proj.ProjProjectSplitTmpDataService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */
@Api(tags = "项目拆分流程信息")
@RestController
@RequestMapping("/projectSplitProcess")
@Slf4j
public class ProjProjectSplitProcessController {

    @Resource
    private ProjProjectSplitTmpDataService splitTmpDataService;

    @Resource
    private ProjProjectSplitProcessService splitProcessService;


    /**
     * 暂存数据
     *
     * @param req
     * @return
     */
    @Log(
            operName = "项目拆分暂存拆分数据", operDetail = "项目拆分暂存拆分数据",
            operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目拆分暂存拆分数据"
    )
    @RequestMapping("tempStage")
    public Result tempStage(@RequestBody @Validated ProjectSplitReq req) {
        return splitTmpDataService.tempStage(req);
    }

    /**
     * 提交数据
     *
     * @param req
     * @return
     */
    @Log(
            operName = "项目拆分新增审核申请", operDetail = "项目拆分新增审核申请",
            operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目拆分新增审核申请"
    )
    @RequestMapping("addProcess")
    public Result addProcess(@RequestBody @Validated AddSplitProcessReq req) {
        return splitProcessService.addProcess(req);
    }

    /**
     * 获取申请单详情
     *
     * @param id
     * @return
     */
    @Log(
            operName = "项目拆分查询审核申请详情", operDetail = "项目拆分查询审核申请详情",
            operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目拆分查询审核申请详情"
    )
    @RequestMapping("getProcessDetail")
    public Result<SplitProcessDetailResp> getProcessDetail(@RequestBody @Validated SimpleId id) {
        return splitProcessService.getProcessDetail(id);
    }

    /**
     * 审核流程
     *
     * @param req
     * @return
     */
    @Log(
            operName = "项目拆分PMO、质管审核申请", operDetail = "项目拆分PMO、质管审核申请",
            operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目拆分PMO、质管审核申请"
    )
    @RequestMapping("auditProcess")
    public Result auditProcess(@RequestBody @Validated AuditSplitProcessReq req) {
        return splitProcessService.auditProcess(req);
    }

    /**
     * 取消申请流程
     *
     * @param id
     * @return
     */
    @Log(
            operName = "项目拆分取消申请", operDetail = "项目拆分取消申请",
            operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目拆分取消申请"
    )
    @RequestMapping("cancelProcess")
    public Result cancelProcess(@RequestBody @Validated SimpleId id) {
        return splitProcessService.cancelProcess(id);
    }
}
