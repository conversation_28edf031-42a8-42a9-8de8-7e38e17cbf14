package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.BizDesc;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.feign.entity.dataapplication.ResDataResp;
import com.msun.csm.feign.entity.openapi.resp.ApiGroupResp;
import com.msun.csm.feign.entity.openapi.resp.ApiInterfaceResp;
import com.msun.csm.service.proj.ProjInterfaceVsAuthorService;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */
@Slf4j
@RestController
@RequestMapping("/interfaceAuth")
@Api(tags = "三方接口授权管理")
public class ProjInterfaceVsAuthController {

    @Resource
    private ProjInterfaceVsAuthorService interfaceVsAuthorService;

    /**
     * 获取接口分组列表
     *
     * @return
     */
    @Log(operName = "获取接口分组列表", operDetail = "获取接口分组列表", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取接口分组列表")
    @GetMapping("/getApiGroupList")
    public Result getApiGroupList() {
        List<ApiGroupResp> apiGroupList = interfaceVsAuthorService.getApiGroupList();
        return Result.success(apiGroupList);
    }


    /**
     * 获取接口列表
     *
     * @return
     */
    @Log(operName = "获取接口列表", operDetail = "获取接口列表", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取接口列表")
    @GetMapping("/getApiList")
    public Result getApiList(@RequestParam Long groupId) {
        if (groupId == null) {
            return Result.fail("参数不能为空");
        }
        List<ApiInterfaceResp> apiList = interfaceVsAuthorService.getApiList(groupId);
        return Result.success(apiList);
    }

    /**
     * 数据中台-获取数据集
     *
     * @return
     */
    @Log(operName = "获取数据集", operDetail = "获取数据集", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取数据集")
    @GetMapping("/getResDataList")
    public Result getResDataList() {
        List<ResDataResp> dataList = interfaceVsAuthorService.getResDataList();
        return Result.success(dataList);
    }

    /**
     * 获取埋点下拉数据--系统管理接口
     *
     * @return
     */
    @Log(operName = "获取埋点下拉数据--系统管理接口", operDetail = "获取埋点下拉数据--系统管理接口", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "获取埋点下拉数据--系统管理接口")
    @GetMapping("/getWorkflowDataList")
    @BizDesc(value = "获取埋点下拉数据--系统管理接口", detail = "获取埋点下拉数据--系统管理接口", person = "张迪")
    public Result getWorkflowDataList(@RequestParam(required = false) Long projectInfoId) {
        return interfaceVsAuthorService.getWorkflowDataList(projectInfoId);
    }

    /**
     * 三方接口接口分类历史数据处理
     *
     * @return
     */
    @Log(operName = "三方接口接口分类历史数据处理", operDetail = "三方接口接口分类历史数据处理", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "三方接口接口分类历史数据处理")
    @GetMapping("/updateInterfaceCategory")
    public Result updateInterfaceCategory() {
        return interfaceVsAuthorService.updateInterfaceCategory();
    }
}
