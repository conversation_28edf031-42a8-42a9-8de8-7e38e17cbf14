package com.msun.csm.controller.proj;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveLog;
import com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveRecord;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.flowable.KfSpecialApproveLogMapper;
import com.msun.csm.dao.mapper.proj.flowable.KfSpecialApproveRecordMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.req.project.SubmitSpecialApprovalReq;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.yunying.YunYingServiceImpl;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/14 16:01
 */
@Api(tags = "客服特殊事项审批")
@Slf4j
@RestController
@RequestMapping("/kfSpecialApprove/")
public class KfSpecialApproveController {
    /**
     * 系统管理员、客服中心总经理、项目管理办公室、质量管理办公室，查询所有客户
     */
    public static final String[] ROLE_CODE_ADMIN = {"1", "11", "21", "22"};
    /**
     * 客服部门经理，查询本部门客户及自己参与的客户
     */
    public static final String[] ROLE_CODE_DEPT = {"13"};

    /**
     * 客服分公司经理、实施工程师，查询本分公司客户及自己参与的客户
     */
    public static final String[] ROLE_CODE_TEAM = {"12", "14"};
    @Value("${project.obs.prePath}")
    private String prePath;
    @Value("${project.feign.oa.url}")
    private String yyHost;
    @Autowired
    private KfSpecialApproveLogMapper kfSpecialApproveLogMapper;
    @Autowired
    private KfSpecialApproveRecordMapper kfSpecialApproveRecordMapper;
    @Autowired
    private ProjProjectInfoMapper projectInfoMapper;
    @Autowired
    private ProjOrderInfoMapper projOrderInfoMapper;
    @Autowired
    private UserHelper userHelper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private YunyingFeignClient yunyingFeignClient;

    @Data
    public static class StartApproveArgs {
        @NotBlank
        @ApiModelProperty("审批编码")
        private String approveCode;
        @NotBlank
        @ApiModelProperty("项目编号")
        private String projectNumber;
        @NotBlank
        @ApiModelProperty("审批说明")
        private String details;
        @ApiModelProperty("文件路径，json数组[{fileName: '文件名', fileUrl: '文件路径'}]")
        private String fileList;
    }

    @ApiOperation("提交审批")
    @PostMapping(value = "startApprove")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> startApprove(@RequestBody @Valid StartApproveArgs args) {
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                .eq(ProjProjectInfo::getProjectNumber, args.getProjectNumber())
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        if (proj == null) {
            return Result.fail("无效项目");
        }
        ProjOrderInfo order = new LambdaQueryChainWrapper<>(projOrderInfoMapper)
                .eq(ProjOrderInfo::getOrderInfoId, proj.getOrderInfoId())
                .eq(ProjOrderInfo::getIsDeleted, 0)
                .one();
        if (order == null) {
            return Result.fail("无效工单");
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        KfSpecialApproveRecord record = new KfSpecialApproveRecord();
        record.setApproveCode(args.getApproveCode());
        record.setCustomInfoId(proj.getCustomInfoId());
        record.setProjectInfoId(proj.getProjectInfoId());
        record.setProjectNumber(args.getProjectNumber());
        record.setDetails(args.getDetails());
        record.setFileList(args.getFileList());
        record.setStatus(1);
        record.setResult("");
        record.setIsDeleted(0);
        record.setCreateTime(new Date());
        record.setCreaterId(currentUser.getSysUserId());
        record.setDeptId(currentUser.getDeptId());

        //调用运营平台发起审批
        SubmitSpecialApprovalReq req = new SubmitSpecialApprovalReq();
        req.setToken(YunYingServiceImpl.TOKEN);
        req.setIssueCategory(SubmitSpecialApprovalReq.Category.fromCode(args.getApproveCode()).getValue());
        req.setBusinessId(order.getYyOrderId());
        req.setRequirementDesc(record.getDetails());
        req.setApplicantUserId(Long.parseLong(currentUser.getUserYunyingId()));
        req.setApplicantOrgId(currentUser.getDeptId());
        List<SubmitSpecialApprovalReq.FileListItem> fileList = Lists.newArrayList();
        List<JSONObject> orgFileList = JSON.parseArray(record.getFileList(), JSONObject.class);
        for (JSONObject jsonObject : orgFileList) {
            fileList.add(new SubmitSpecialApprovalReq.FileListItem(jsonObject.getString("name"), jsonObject.getString("url")));
        }
        req.setFileList(fileList);
        // TODO 调用运营平台的接口发起审批
        String yyUrl = StrUtil.format("{}imsp/proj/submitSpecialApproval?userLoginname={}&token=imsp", yyHost, currentUser.getAccount());
        String post;
        try {
            post = HttpUtil.post(yyUrl, JSON.toJSONString(req));
            JSONObject res = JSON.parseObject(post);
            log.warn("调用运营平台发起审批接口，url={}，参数={}，返回={}", yyUrl, JSON.toJSONString(req), res);
            if (res == null) {
                log.error("运营平台发起审批失败，参数={}", JSON.toJSONString(req));
                return Result.fail("运营平台发起审批失败，请稍后再试");
            } else if (res.getIntValue("code") != 200) {
                log.error("运营平台发起审批失败，参数={}，返回={}", JSON.toJSONString(req), res);
                return Result.fail("运营平台发起审批失败，" + res.getString("msg"));
            } else {
                log.info("运营平台发起审批成功，参数={}，返回={}", JSON.toJSONString(req), res);
            }
            // 将运营平台返回的审批ID保存到本地
            record.setId(res.getJSONObject("obj").getLongValue("specialId"));
        } catch (Throwable e) {
            throw new CustomException("运营平台发起审批失败，请稍后再试", e);
        }
        // 保存记录
        kfSpecialApproveRecordMapper.insert(record);
        return Result.success("审批发起成功");
    }

    /**
     * 获取客服特殊事项审批列表
     *
     * @param args
     * @return
     */
    @ApiOperation("获取审批列表")
    @PostMapping(value = "getKfSepecialApproveList")
    public Result<?> getKfSepecialApproveList(@RequestBody KfSpecialApproveRecordMapper.GetKfSpecialApproveListParam args) {
        // 当前登录用户
        SysUserVO currentUser = userHelper.getCurrentUser();
        args.setUid(currentUser.getSysUserId());
        // 当前登录用户的角色
        List<SysRoleDTO> currentUserRoleList = sysRoleMapper.selectRoleByUserId(currentUser.getSysUserId());
        // 当前登录用户的角色编码
        Set<String> currentUserRoleCodeSet = currentUserRoleList.stream().map(SysRoleDTO::getRoleCode)
                .collect(Collectors.toSet());
        // 查询范围
        String queryScope = this.getQueryScope(currentUserRoleCodeSet);
        args.setQueryScope(queryScope);
        Set<Long> deptIdList = new HashSet<>();
        if ("dept".equals(queryScope)) {
            //查询当前本人参与跟部门、子部门的审批
            deptIdList.add(currentUser.getDeptId());
            getAllChildDeptId(currentUser.getDeptId(), deptIdList);
            args.setDeptIdList(deptIdList);
            //获取到参与的审批记录
            Set<Long> joinedApproveId = new LambdaQueryChainWrapper<>(kfSpecialApproveLogMapper)
                    .select(KfSpecialApproveLog::getApproveId)
                    .eq(KfSpecialApproveLog::getIsDeleted, 0)
                    .eq(KfSpecialApproveLog::getCreaterId, currentUser.getSysUserId())
                    .list().stream().map(KfSpecialApproveLog::getApproveId).collect(Collectors.toSet());

            args.setJoinedApproveIds(joinedApproveId);
        } else if ("team".equals(queryScope)) {
            //查询当前本人参与跟所在部门的审批
            deptIdList.add(currentUser.getDeptId());
            args.setDeptIdList(deptIdList);
            Set<Long> joinedApproveId = new LambdaQueryChainWrapper<>(kfSpecialApproveLogMapper)
                    .select(KfSpecialApproveLog::getApproveId)
                    .eq(KfSpecialApproveLog::getIsDeleted, 0)
                    .eq(KfSpecialApproveLog::getCreaterId, currentUser.getSysUserId())
                    .list().stream().map(KfSpecialApproveLog::getApproveId).collect(Collectors.toSet());
            args.setJoinedApproveIds(joinedApproveId);
        }
        return PageHelperUtil.queryPage(args.getPageNo(), args.getPageSize(), page -> Result.success(new PageInfo<>(kfSpecialApproveRecordMapper.getKfSpecialApproveList(args))));
    }

    private void getAllChildDeptId(Long deptId, Set<Long> deptIdList) {
        if (deptId <= 0) {
            return;
        }
        // 获取当前部门的所有下属部门
        List<SysDept> childDepts = new LambdaQueryChainWrapper<>(sysDeptMapper)
                .eq(SysDept::getPid, deptId)
                .eq(SysDept::getIsDeleted, 0)
                .list();
        if (CollectionUtils.isEmpty(childDepts)) {
            return;
        }
        for (SysDept childDept : childDepts) {
            deptIdList.add(childDept.getDeptYunyingId());
            // 递归获取下级部门
            getAllChildDeptId(childDept.getDeptYunyingId(), deptIdList);
        }
    }

    /**
     * 根据用户角色获取查询的客户范围
     *
     * @param userRoleCodeSet 用户角色编码集合
     * @return all-查询所有客户、dept-查询本部门客户、team-查询本客服分公司客户
     */
    private String getQueryScope(Set<String> userRoleCodeSet) {
        // 系统管理员、客服中心总经理、项目管理办公室，查询所有客户
        if (Arrays.stream(ROLE_CODE_ADMIN).anyMatch(userRoleCodeSet::contains)) {
            return "all";
        }
        // 客服部门经理，查询本部门客户
        if (Arrays.stream(ROLE_CODE_DEPT).anyMatch(userRoleCodeSet::contains)) {
            return "dept";
        }
        // 客服分公司经理、实施工程师，查询本分公司客户
        if (Arrays.stream(ROLE_CODE_TEAM).anyMatch(userRoleCodeSet::contains)) {
            return "team";
        }
        return "other";
    }

    @ApiOperation("获取客服特殊事项审批日志列表")
    @GetMapping(value = "getKfSpecialApproveLogList")
    public Result<List<KfSpecialApproveLog>> getKfSpecialApproveLogList(@RequestParam("approveId") Long approveId) {
        return Result.success(kfSpecialApproveLogMapper.getKfSpecialApproveLogList(approveId));
    }

    @ApiOperation("上传特批文件")
    @PostMapping(value = "uploadSpecialApproveFile")
    public Result<List<JSONObject>> uploadSpecialApproveFile(@RequestParam("files") MultipartFile[] files) throws Exception {
        List<JSONObject> list = new ArrayList<>();
        for (MultipartFile file : files) {
            // 构建路径
            String fileId = String.valueOf(IdGenerator.ins().generator());
            String path = prePath + "kfSpecialApprove" + StrUtil.SLASH + DateUtil.format(new Date(), "yyyyMMdd") + StrUtil.SLASH
                    + file.getOriginalFilename();
            // 获取文件名（不包括路径）
            String fileName = path.substring(path.lastIndexOf(StrUtil.SLASH) + 1);
            // 获取文件名（不包括扩展名）
            String fileNameWithoutExtension = fileName.contains(StrUtil.DOT) ? fileName.substring(0, fileName.lastIndexOf(StrUtil.DOT)) : fileName;
            // 拼接时间戳和文件扩展名
            String newFileName =
                    fileNameWithoutExtension + "_" + fileId + StrUtil.DOT + (fileName.contains(
                            StrUtil.DOT) ? fileName.substring(fileName.lastIndexOf(StrUtil.DOT) + 1) : "");
            // 构建新的完整路径
            String objectKey = path.substring(0, path.lastIndexOf(StrUtil.SLASH) + 1) + newFileName;
            PutObjectResult res = OBSClientUtils.uploadMultipartFile(file, objectKey, true);
            String url = res.getObjectUrl();
            JSONObject obj = new JSONObject();
            obj.put("id", fileId);
            obj.put("name", fileName);
            obj.put("url", url);
            list.add(obj);
        }
        return Result.success(list);
    }

}
