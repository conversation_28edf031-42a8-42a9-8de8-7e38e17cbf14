package com.msun.csm.controller.proj;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.HdEquipSelectDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsHdDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.vo.ProjEquipRecordHdResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsHdVO;
import com.msun.csm.service.proj.ProjEquipRecordVsHdService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 血透设备Controller
 *
 * @Author: duxu
 * @Date: 2024/10/12/14:06
 */
@Api (tags = "血透设备COntroller")
@RestController
@RequestMapping ("/projEquipRecordVsHd")
public class ProjEquipRecordVsHdController {

    @Resource
    private ProjEquipRecordVsHdService projEquipRecordVsHdService;

    /**
     * 保存或修改血透设备信息
     *
     * @param dto
     * @return
     */
    @ApiOperation ("保存或修改血透设备信息")
    @Log (operName = "设备", operDetail = "保存或修改血透设备信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "保存或修改血透设备信息")
    @PostMapping (value = "/saveOrUpdateEquipToHd")
    Result saveOrUpdateEquipToHd(@RequestBody ProjEquipRecordVsHdDTO dto) {
        return projEquipRecordVsHdService.saveOrUpdateEquipToHd(dto);
    }

    /**
     * 查询单个血透设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation ("查询单个血透设备数据")
    @Log (operName = "设备", operDetail = "查询单个血透设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询单个血透设备数据")
    @PostMapping (value = "/getEquipRecordVsHd")
    Result<ProjEquipRecordVsHdVO> getEquipRecordVsHd(@RequestBody ProjEquipRecordVsHdDTO dto) {
        return projEquipRecordVsHdService.getEquipRecordVsHd(dto.getEquipRecordVsHdId(), dto.getIsMobile());
    }

    /**
     * 删除血透设备数据
     *
     * @param dto
     * @return
     */
    @ApiOperation ("删除血透设备数据")
    @Log (operName = "设备", operDetail = "删除血透设备数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "删除血透设备数据")
    @PostMapping (value = "/deleteEquipRecordVsHd")
    Result deleteEquipRecordVsHd(@RequestBody ProjEquipRecordVsHdDTO dto) {
        return projEquipRecordVsHdService.deleteEquipRecordVsHd(dto.getEquipRecordVsHdId());
    }

    /**
     * 查询血透设备列表数据
     *
     * @param dto
     * @return
     */
    @ApiOperation ("查询血透设备列表数据")
    @Log (operName = "设备", operDetail = "查询血透设备列表数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "查询血透设备列表数据")
    @PostMapping (value = "/selectHdEquipData")
    Result<ProjEquipRecordHdResultVO> selectHdEquipData(@RequestBody HdEquipSelectDTO dto) {
        return projEquipRecordVsHdService.selectHdEquipData(dto);
    }

    /**
     * 设备信息发送到LIS解析平台
     *
     * @param dto
     * @return
     */
    @ApiOperation ("设备信息发送到LIS解析平台")
    @Log (operName = "设备", operDetail = "设备信息发送到LIS解析平台", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "设备信息发送到LIS解析平台")
    @PostMapping (value = "/equipSendToLis")
    Result equipSendToLis(@RequestBody ProjEquipRecordVsHdDTO dto) {
        return projEquipRecordVsHdService.equipSendToLis(dto.getEquipRecordVsHdIds());
    }

    /**
     * 撤销血透设备接口申请
     *
     * @param dto
     * @return
     */
    @ApiOperation ("撤销血透设备接口申请")
    @Log (operName = "设备", operDetail = "撤销血透设备接口申请", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "撤销血透设备接口申请")
    @PostMapping (value = "/equipRevokeToHd")
    Result equipRevokeToHd(@RequestBody HdEquipSelectDTO  dto) {
        return projEquipRecordVsHdService.equipRevokeToHd(dto.getEquipRecordVsHdId());
    }

    /**
     * 提交完成
     *
     * @return
     */
    @ApiOperation ("提交完成")
    @Log (operName = "设备", operDetail = "提交完成", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "提交完成")
    @PostMapping (value = "/commitFinish")
    Result commitFinish(@RequestBody ProjEquipVsProductFinishDTO dto) {
        return projEquipRecordVsHdService.commitFinish(dto);
    }

    /**
     * 发送到云健康
     *
     * @return
     */
    @ApiOperation ("发送到云健康")
    @Log (operName = "设备", operDetail = "发送到云健康", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "发送到云健康")
    @PostMapping (value = "/sendToMsunVsHd")
    Result sendToMsunVsHd(@RequestBody ProjEquipSendToCloudDTO dto) {
        return projEquipRecordVsHdService.sendToMsunVsHd(dto);
    }

    /**
     * 一键检测、检测
     *
     * @return
     */
    @ApiOperation ("一键检测、检测")
    @Log (operName = "设备", operDetail = "一键检测、检测", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "一键检测、检测")
    @PostMapping (value = "/checkSendToMsunVsHd")
    Result checkSendToMsunVsHd(@RequestBody ProjEquipSendToCloudDTO dto) {
        return projEquipRecordVsHdService.checkSendToMsunVsHd(dto);
    }

    /**
     * 血透下载模版
     */
    @ApiOperation ("血透下载模版")
    @Log (operName = "设备", operDetail = "血透下载模版", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "血透下载模版")
    @RequestMapping (value = "/downloadTemplateVsHD")
    public void downloadTemplateVsHD(HttpServletResponse response, @RequestParam ("projectInfoId") Long projectInfoId) {
        projEquipRecordVsHdService.downloadTemplateVsHD(response, projectInfoId);
    }

    /**
     * 血透导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @ApiOperation ("血透导入模版数据")
    @Log (operName = "设备", operDetail = "血透导入模版数据", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "血透导入模版数据")
    @PostMapping (value = "/importExcelDataVsHD")
    Result importExcelDataVsHD(@RequestParam ("file") MultipartFile multipartFile,
                               @RequestParam ("customInfoId") Long customInfoId,
                               @RequestParam ("projectInfoId") Long projectInfoId) {
        return projEquipRecordVsHdService.importExcelDataVsHD(multipartFile, customInfoId, projectInfoId);
    }

    /**
     * 血透设备导出数据
     */
    @ApiOperation ("血透设备导出数据")
    @Log (operName = "设备", operDetail = "血透设备导出数据", operLogType = Log.LogOperType.DOWNLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "血透设备导出数据")
    @PostMapping (value = "/exportExcelDataVsHD")
    public void exportExcelDataVsHD(@RequestBody HdEquipSelectDTO dto, HttpServletResponse response) {
        projEquipRecordVsHdService.exportExcelDataVsHD(dto, response);
    }

    /**
     * 老系统血透设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    @ApiOperation ("老系统血透设备迁移到新系统")
    @Log (operName = "设备", operDetail = "老系统血透设备迁移到新系统", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老系统血透设备迁移到新系统")
    @GetMapping (value = "/sendEquipToOldHD")
    Result sendEquipToOldHD(@RequestParam ("projectInfoId") Long projectInfoId, @RequestParam ("oldEquipId") Long oldEquipId) {
        return projEquipRecordVsHdService.sendEquipToOldHD(projectInfoId, oldEquipId);
    }
}
