package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementMiddlewareOrderInfo;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleCloudNodeDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleMiddlewareDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleSaveDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleUploadDTO;
import com.msun.csm.model.vo.projsettlement.DictCloudEnvironmentsSaleVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckSaleMainVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckSaleUploadResultVO;
import com.msun.csm.service.proj.ProjProjectSettlementCheckSaleService;
import com.msun.csm.service.proj.entry.exception.SettlementEntryException;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */
@Slf4j
@RestController
@RequestMapping("/projProjectSettlementCheckSale")
@Api(tags = "入驻申请-项目入驻审核表相关接口-销售申请")
public class ProjProjectSettlementCheckSaleController {

    @Resource
    private ProjProjectSettlementCheckSaleService projProjectSettlementCheckSaleService;


    @Log(operName = "销售入驻申请基本信息查询", operDetail = "销售入驻申请基本信息查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "销售入驻申请基本信息查询")
    @PostMapping("getSaleApplyFormMainInfo")
    public Result<ProjProjectSettlementCheckSaleMainVO> getSaleApplyFormMainInfo(@Valid @RequestBody ProjProjectSettlementCheckSaleDTO projectSettlementCheckSaleDTO) {
        try {
            return projProjectSettlementCheckSaleService.getSaleApplyFormMainInfo(projectSettlementCheckSaleDTO);
        } catch (Throwable e) {
            log.error("销售提交入驻申请, 获取基本信息接口异常. message: {}, e=", e.getMessage(), e);
            throw new SettlementEntryException(e, ResultEnum.FAIL, "销售提交入驻申请, 获取基本信息接口异常",
                    Long.parseLong(projectSettlementCheckSaleDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "销售提交入驻申请", operDetail = "销售提交入驻申请", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "销售提交入驻申请", saveParam = true)
    @PostMapping("saveSettlementSale")
    public Result<Boolean> saveSettlementSale(@Valid @RequestBody ProjProjectSettlementCheckSaleSaveDTO settlementRuleSaveDTO) {
        try {
            return projProjectSettlementCheckSaleService.saveSettlementSale(settlementRuleSaveDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, "销售提交入驻申请异常",
                    Long.parseLong(settlementRuleSaveDTO.getProjectInfoId()));
        }

    }

    @Log(operName = "销售入驻申请中间件服务工单查询", operDetail = "销售入驻申请中间件服务工单查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "销售入驻申请中间件服务工单查询")
    @PostMapping("findMiddlewareOrderInfo")
    public Result<List<ProjSettlementMiddlewareOrderInfo>> findMiddlewareOrderInfo(@Valid @RequestBody ProjProjectSettlementCheckSaleMiddlewareDTO dto) {
        try {
            return projProjectSettlementCheckSaleService.findMiddlewareOrderInfo(dto);
        } catch (Throwable e) {
            log.error("销售入驻申请中间件服务工单查询异常. message: {}, e=", e.getMessage(), e);
            throw new SettlementEntryException(e, ResultEnum.FAIL, "销售入驻申请中间件服务工单查询异常", dto.getProjectInfoId());
        }
    }

    @Log(operName = "销售获取申请云资源工单", operDetail = "销售获取申请资源工单", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "销售获取申请资源工单")
    @PostMapping("findCloudServiceOrder")
    public Result<List<ProjSettlementOrderInfo>> findCloudServiceOrder(@RequestBody ProjProjectSettlementCheckSaleDTO projectSettlementCheckSaleDTO) {
        try {
            return projProjectSettlementCheckSaleService.findCloudServiceOrder(projectSettlementCheckSaleDTO);
        } catch (Throwable e) {
            log.error("销售获取申请云资源工单异常. message: {}, e=", e.getMessage(), e);
            throw new SettlementEntryException(e, ResultEnum.FAIL, "销售获取申请云资源工单异常",
                    Long.parseLong(projectSettlementCheckSaleDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "销售获取部署节点", operDetail = "销售获取部署节点", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "销售获取部署节点", saveParam = true)
    @PostMapping("findCloudNode")
    public Result<List<DictCloudEnvironmentsSaleVO>> findCloudNode(@RequestBody ProjProjectSettlementCheckSaleCloudNodeDTO saleCloudNodeDTO) {
        try {
            return projProjectSettlementCheckSaleService.findCloudNode(saleCloudNodeDTO);
        } catch (Throwable e) {
            log.error("销售获取部署节点异常. message: {}, e=", e.getMessage(), e);
            throw new SettlementEntryException(e, ResultEnum.FAIL, "销售获取部署节点异常",
                    Long.parseLong(saleCloudNodeDTO.getProjectInfoId()));
        }
    }

    @Log(operName = "销售上传文件", operDetail = "销售上传文件", operLogType = Log.LogOperType.UPLOAD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "销售上传文件")
    @PostMapping("mobileUploadFile")
    public Result<ProjProjectSettlementCheckSaleUploadResultVO> mobileUploadFile(@RequestBody ProjProjectSettlementCheckSaleUploadDTO checkSaleUploadDTO) {
        try {
            return projProjectSettlementCheckSaleService.mobileUploadFile(checkSaleUploadDTO);
        } catch (Throwable e) {
            throw new SettlementEntryException(e, ResultEnum.FAIL, e.getMessage(),
                    checkSaleUploadDTO.getProjectInfoId());
        }
    }

}
