package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.projreport.ProjPrinterConfigInfo;
import com.msun.csm.model.dto.DirUserDTO;
import com.msun.csm.model.req.projform.PrintReportLimitDataReq;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projreport.PrintReportVerificationPassedParam;
import com.msun.csm.model.req.projreport.ProjSurveyReportDeleteReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportDetailReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportExamineReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintParmerReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReviewExamineReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportTagReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateListReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainImportReq;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormPageResp;
import com.msun.csm.model.resp.projreport.ProjProjectFormTerminalResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportMenuResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportPrintNodeCodeResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReprotDetailPageResp;
import com.msun.csm.model.statis.ProjCustomReq;
import com.msun.csm.model.vo.DirPersonVO;
import com.msun.csm.service.proj.ProjSurveyReportService;
import com.msun.csm.service.proj.projform.PrintReportLimitService;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */
@Api(tags = "打印报表")
@RestController
@RequestMapping("/projSurveyReport")
@Slf4j
public class ProjSurveyReportController {

    @Resource
    private ProjSurveyReportService projSurveyReportService;

    @Resource
    private PrintReportLimitService printReportLimitService;

    /**
     * 分页查询报表信息
     *
     * @param projSurveyReportReq
     * @return
     */
    @Log(operName = "分页查询报表信息", operDetail = "分页查询报表信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询报表信息")
    @ApiOperation("分页查询报表信息")
    @PostMapping(value = "/selectSurveyReportByPage")
    public Result<ProjSurveyReprotFormPageResp<ProjSurveyReportResp>> selectSurveyReportByPage(@RequestBody ProjSurveyReportReq projSurveyReportReq) {
        return projSurveyReportService.selectSurveyReportByPage(projSurveyReportReq);
    }

    /**
     * 根据id查询报表对象信息
     *
     * @param projSurveyReportReq
     * @return
     */
    @Log(operName = "根据id查询报表对象信息", operDetail = "根据id查询报表对象信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据id查询报表对象信息")
    @ApiOperation("根据id查询报表对象信息")
    @PostMapping(value = "/selectSurveyReportById")
    public Result<ProjSurveyReportResp> selectSurveyReportById(@RequestBody ProjSurveyReportReq projSurveyReportReq) {
        return Result.success(projSurveyReportService.selectSurveyReportById(projSurveyReportReq));
    }

    /**
     * 报表审核驳回
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "报表审核驳回", operDetail = "报表审核驳回", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表审核驳回")
    @ApiOperation("报表审核驳回")
    @PostMapping(value = "/updateExamineReportStatus")
    Result updateExamineReportStatus(@RequestBody ProjSurveyReportExamineReq projSurveyReportExamineReq) {
        projSurveyReportExamineReq.setFinishStatus(2);
        return projSurveyReportService.updateExamineReportStatus(projSurveyReportExamineReq);
    }

    /**
     * 修改报表
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "修改报表", operDetail = "修改报表", intLogType = Log.IntLogType.SELF_SYS, cnName = "修改报表")
    @ApiOperation("修改报表")
    @PostMapping(value = "/updateReport")
    Result updateReport(@RequestBody ProjSurveyReportUpdateReq projSurveyReportExamineReq) {
        return projSurveyReportService.updateReport(projSurveyReportExamineReq);
    }

    /**
     * 单个报表提交完成
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "单个报表提交完成", operDetail = "单个报表提交完成", intLogType = Log.IntLogType.SELF_SYS, cnName = "单个报表提交完成")
    @ApiOperation("单个报表提交完成")
    @PostMapping(value = "/updateReportFinishStatus")
    Result updateReportFinishStatus(@RequestBody ProjSurveyReportUpdateReq projSurveyReportExamineReq) {
        projSurveyReportExamineReq.setFinishStatus(1);
        return projSurveyReportService.updateReportFinishStatus(projSurveyReportExamineReq);
    }

    /**
     * 设计制作
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "设计制作", operDetail = "设计制作", intLogType = Log.IntLogType.SELF_SYS, cnName = "设计制作")
    @ApiOperation("设计制作")
    @PostMapping(value = "/reportMark")
    Result reprotMake(@RequestBody ProjSurveyReportExamineReq projSurveyReportExamineReq) {
        return projSurveyReportService.reprotMake(projSurveyReportExamineReq);
    }

    /**
     * 单个保存
     *
     * @param saveModel
     * @return
     */
    @Log(operName = "单个保存", operDetail = "单个保存", intLogType = Log.IntLogType.SELF_SYS, cnName = "单个保存")
    @ApiOperation("单个保存")
    @PostMapping(value = "/saveReprotSave")
    Result saveReprotSave(@RequestBody ProjSurveyReportUpdateReq saveModel) {
        return projSurveyReportService.batchReprotSave(new ArrayList<>(Collections.singletonList(saveModel)));
    }

    /**
     * 批量保存
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "批量保存", operDetail = "批量保存", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量保存")
    @ApiOperation("批量保存")
    @PostMapping(value = "/batchReportSave")
    Result batchReprotSave(@RequestBody List<ProjSurveyReportUpdateReq> projSurveyReportExamineReq) {
        return projSurveyReportService.batchReprotSave(projSurveyReportExamineReq);
    }

    /**
     * 根据报表打印节点查询报表标识
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "根据报表打印节点查询报表标识", operDetail = "根据报表打印节点查询报表标识", intLogType = Log.IntLogType.SELF_SYS, cnName = "根据报表打印节点查询报表标识")
    @ApiOperation("根据报表打印节点查询报表标识")
    @PostMapping(value = "/getReportFileTagByPrintCode")
    Result getReportFileTagByPrintCode(@RequestBody ProjSurveyReportTagReq projSurveyReportExamineReq) {
        return projSurveyReportService.getReportFileTagByPrintCode(projSurveyReportExamineReq);
    }

    /**
     * 查询项目下报表内容模块数据
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "查询项目下报表内容模块数据", operDetail = "查询项目下报表内容模块数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目下报表内容模块数据")
    @ApiOperation("查询项目下报表内容模块数据")
    @PostMapping(value = "/getReportMenuByProjectId")
    Result<List<ProjSurveyReportMenuResp>> getReportMenuByProjectId(@RequestBody ProjSurveyReportTagReq projSurveyReportExamineReq) {
        return projSurveyReportService.getReportMenuByProjectId(projSurveyReportExamineReq);
    }

    /**
     * 查询PACS初始化路径
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "获取产品初始化菜单", operDetail = "获取产品初始化菜单", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取产品初始化菜单")
    @ApiOperation("获取产品初始化菜单")
    @PostMapping(value = "/getPacsStartPath")
    Result<String> getPacsStartPath(@RequestBody ProjSurveyReportTagReq projSurveyReportExamineReq) {
        return projSurveyReportService.getPacsStartPath(projSurveyReportExamineReq);
    }

    /**
     * 批量分配责任人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "批量分配责任人", operDetail = "批量分配责任人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配责任人")
    @ApiOperation("批量分配责任人")
    @PostMapping(value = "/updateReportResponsibilities")
    Result updateReportResponsibilities(@RequestBody ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        return projSurveyReportService.updateReportResponsibilities(projSurveyFormReq);
    }

    /**
     * 更新客户 医院
     *
     * @param applyOrder
     * @return
     */
    @Log(operName = "打印报表使用众阳设计器 客户 医院", operDetail = "打印报表使用众阳设计器 客户 医院", intLogType = Log.IntLogType.SELF_SYS, cnName = "打印报表使用众阳设计器 客户 医院")
    @ApiOperation("打印报表使用众阳设计器 客户 医院")
    @PostMapping(value = "/printReportCustomHospitalLimitDataUpdate")
    Result printReportCustomHospitalLimitDataUpdate(@RequestBody ProjApplyOrder applyOrder) {
        return printReportLimitService.printReportLimitDataUpdate(applyOrder);
    }

    /**
     * 更新客户
     *
     * @param printReportLimitDataReq
     * @return
     */
    @Log(operName = "打印报表使用众阳设计器 客户", operDetail = "打印报表使用众阳设计器 客户", intLogType = Log.IntLogType.SELF_SYS, cnName = "打印报表使用众阳设计器 客户")
    @ApiOperation("打印报表使用众阳设计器 客户")
    @PostMapping(value = "/printReportCustomLimitDataUpdate")
    Result printReportCustomLimitDataUpdate(@RequestBody PrintReportLimitDataReq printReportLimitDataReq) {
        return printReportLimitService.printReportCustomLimitDataUpdate(printReportLimitDataReq);
    }

    /**
     * 更新客户
     *
     * @param printReportLimitDataReq
     * @return
     */
    @Log(operName = "打印报表使用众阳设计器 医院", operDetail = "打印报表使用众阳设计器 医院", intLogType = Log.IntLogType.SELF_SYS, cnName = "打印报表使用众阳设计器 医院")
    @ApiOperation("打印报表使用众阳设计器 医院")
    @PostMapping(value = "/printReportHospitalLimitDataUpdate")
    Result printReportHospitalLimitDataUpdate(@RequestBody PrintReportLimitDataReq printReportLimitDataReq) {
        return printReportLimitService.printReportHospitalLimitDataUpdate(printReportLimitDataReq);
    }

    /**
     * 报表修改责任人按钮校验
     *
     * @param dto
     * @return
     */
    @Log(operName = "报表修改责任人按钮校验", operDetail = "报表修改责任人按钮校验", intLogType = Log.IntLogType.SELF_SYS, cnName = "报表修改责任人按钮校验")
    @ApiOperation("报表修改责任人按钮校验")
    @PostMapping(value = "/reportUpdateDirUserCheck")
    Result reportUpdateDirUserCheck(@RequestBody DirUserDTO dto) {
        return printReportLimitService.reportUpdateDirUserCheck(dto);
    }

    /**
     * 查询报表责任人下拉数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询报表责任人下拉数据", operDetail = "查询报表责任人下拉数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询报表责任人下拉数据")
    @ApiOperation("查询报表责任人下拉数据")
    @PostMapping(value = "/getDirUserList")
    Result<DirPersonVO> getDirUserList(@RequestBody DirUserDTO dto) {
        return printReportLimitService.getDirUserList(dto);
    }

    /**
     * 刷新报表终端明细
     *
     * @param projSurveyReportReq
     * @return
     */
    @Log(operName = "刷新报表终端明细", operDetail = "刷新报表终端明细", intLogType = Log.IntLogType.SELF_SYS, cnName = "刷新报表终端明细")
    @ApiOperation("刷新报表终端明细")
    @PostMapping(value = "/syncReportTerminalData")
    public Result syncReportTerminalData(@RequestBody ProjSurveyReportReq projSurveyReportReq) {
        return projSurveyReportService.syncReportTerminalData(projSurveyReportReq);
    }

    /**
     * 分页查询报表明细信息
     *
     * @param projSurveyReportReq
     * @return
     */
    @Log(operName = "分页查询报表明细信息", operDetail = "分页查询报表明细信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询报表明细信息")
    @ApiOperation("分页查询报表明细信息")
    @PostMapping(value = "/selectSurveyReportDetailByPage")
    public Result<ProjSurveyReprotDetailPageResp<ProjProjectFormTerminalResp>> selectSurveyReportDetailByPage(@RequestBody ProjSurveyReportDetailReq projSurveyReportReq) {
        return projSurveyReportService.selectSurveyReportDetailByPage(projSurveyReportReq);
    }

    /**
     * 查询报表明细状态
     *
     * @param projSurveyReportReq
     * @return
     */
    @Log(operName = "查询报表明细状态", operDetail = "查询报表明细状态", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询报表明细状态")
    @ApiOperation("查询报表明细状态")
    @PostMapping(value = "/selectStatusDictList")
    public Result<List<BaseCodeNameResp>> selectStatusDictList(@RequestBody ProjSurveyReportDetailReq projSurveyReportReq) {
        return projSurveyReportService.selectSatusDictList(projSurveyReportReq);
    }

    @Log(operName = "导出模板", operDetail = "导出模板", intLogType = Log.IntLogType.SELF_SYS, cnName = "导出模板")
    @ApiOperation("导出模板")
    @GetMapping("/download")
    public void exportTemplate(HttpServletResponse response, @RequestParam("projectInfoId") Long projectInfoId) throws IOException {
        projSurveyReportService.download(response, projectInfoId);
    }


    /**
     * 导入打印报表
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导入打印报表")
    @Log(operName = "导入打印报表", operDetail = "导入打印报表", intLogType = Log.IntLogType.SELF_SYS, cnName = "导入打印报表")
    @PostMapping(value = "/importExcel")
    public Result<String> importExcel(ProjStatisticalReportMainImportReq param, HttpServletRequest request) throws Exception {
        return projSurveyReportService.importExcel(param.getFile(), request, param);
    }

    /**
     * 批量分配审核人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "批量分配审核人", operDetail = "批量分配审核人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配审核人")
    @ApiOperation("批量分配审核人")
    @PostMapping(value = "/updateReportReviewer")
    Result updateReportReviewer(@RequestBody ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        return projSurveyReportService.updateReportReviewer(projSurveyFormReq);
    }

    /**
     * 批量审核
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "批量审核", operDetail = "批量审核", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量审核")
    @ApiOperation("批量审核")
    @PostMapping(value = "/updateReportExamine")
    Result updateReportExeReport(@RequestBody ProjSurveyReportReviewExamineReq projSurveyFormReq) {
        return projSurveyReportService.updateReportExamine(projSurveyFormReq);
    }

    /**
     * 提交运维审核
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "提交运维审核", operDetail = "提交运维审核", intLogType = Log.IntLogType.SELF_SYS, cnName = "提交运维审核")
    @ApiOperation("提交运维审核")
    @PostMapping(value = "/updateReportExamineStatus")
    Result updateReportExamineStatus(@RequestBody ProjSurveyReportReviewExamineReq projSurveyReportExamineReq) {
        return projSurveyReportService.updateReportExamineStatus(projSurveyReportExamineReq);
    }

    /**
     * 查询报表审核人下拉数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询审核人下拉数据", operDetail = "查询审核人下拉数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询审核人下拉数据")
    @ApiOperation("查询审核人下拉数据")
    @PostMapping(value = "/getExamineUserList")
    Result<List<BaseIdNameResp>> getExamineUserList(@RequestBody DirUserDTO dto) {
        return printReportLimitService.getExamineUserList(dto);
    }

    /**
     * 单个或者批量删除打印报表
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "单个或者批量删除打印报表", operDetail = "单个或者批量删除打印报表", intLogType = Log.IntLogType.SELF_SYS, cnName = "单个或者批量删除打印报表")
    @ApiOperation("单个或者批量删除打印报表")
    @PostMapping(value = "/deleteReport")
    Result<Void> deleteReport(@RequestBody ProjSurveyReportDeleteReq projSurveyFormReq) {
        return projSurveyReportService.deleteReport(projSurveyFormReq);
    }

    @Log(operName = "创建预上线医院打印报表模板信息", operDetail = "创建预上线医院打印报表模板信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "创建预上线医院打印报表模板信息")
    @ApiOperation("创建预上线医院打印报表模板信息")
    @PostMapping(value = "/sendStartPrintReportData")
    Result<String> sendStartPrintReportData(@RequestBody ProjCustomReq dto) {
        return Result.success(projSurveyReportService.sendStartPrintReportData(dto.getCustomInfoId()));
    }

    @Log(operName = "环境交付后进行调用中心端接口制作模板的报表节点下发到云健康", operDetail = "环境交付后进行调用中心端接口制作模板的报表节点下发到云健康", intLogType = Log.IntLogType.SELF_SYS, cnName = "环境交付后进行调用中心端接口制作模板的报表节点下发到云健康")
    @ApiOperation("创建预上线医院打印报表模板信息")
    @PostMapping(value = "/preLaunchHospitalPushOnSite")
    Result<String> preLaunchHospitalPushOnSite(@RequestBody ProjCustomReq dto) {
        return Result.success(projSurveyReportService.preLaunchHospitalPushOnSite(dto.getCustomInfoId(), dto.getHospitalId(), dto.getHisOrgId()));
    }

    /**
     * 查询打印机信息
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询打印机信息", operDetail = "查询打印机信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询打印机信息")
    @ApiOperation("查询打印机信息")
    @PostMapping(value = "/queryPrintInfo")
    Result<PageInfo<ProjPrinterConfigInfo>> queryPrintInfo(@RequestBody ProjSurveyReportPrintReq dto) {
        return Result.success(projSurveyReportService.queryPrintInfoByJf(dto));
    }

    @Log(operName = "查询所有报表节点", operDetail = "查询所有报表节点", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询所有报表节点")
    @ApiOperation("查询所有报表节点")
    @PostMapping(value = "/getAllNode")
    Result<List<ProjSurveyReportPrintNodeCodeResp>> getAllNode(@RequestBody ProjSurveyReportPrintParmerReq model) {
        return projSurveyReportService.getAllNode(model);
    }


    /**
     * 改造后单个或批量保存报表
     *
     * @param saveModel
     * @return
     */
    @Log(operName = "改造后批量保存报表节点信息", operDetail = "改造后批量保存报表节点信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "改造后批量保存报表节点信息")
    @ApiOperation("改造后批量保存报表节点信息")
    @PostMapping(value = "/saveReportSaveList")
    Result saveReportSaveList(@RequestBody ProjSurveyReportUpdateListReq saveModel) {
        return projSurveyReportService.saveReprotSaveList(saveModel);
    }

    /**
     * 改造后单个或批量保存报表
     *
     * @param saveModel
     * @return
     */
    @Log(operName = "改造后单个保存报表数据", operDetail = "改造后单个保存报表数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "改造后单个保存报表数据")
    @ApiOperation("改造后单个保存报表数据")
    @PostMapping(value = "/saveReportSingleSaveList")
    Result saveReportSingleSaveList(@RequestBody ProjSurveyReportUpdateReq saveModel) {
        return projSurveyReportService.updateReport(saveModel);
    }

    /**
     * 交付挂载统计报表资源库页面
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log(operName = "交付挂载统计报表资源库页面", operDetail = "交付挂载统计报表资源库页面", intLogType = Log.IntLogType.SELF_SYS, cnName = "交付挂载统计报表资源库页面")
    @ApiOperation("交付挂载统计报表资源库页面")
    @PostMapping(value = "/getReportLibraryPageLink")
    Result getReportLibraryPageLink(@RequestBody ProjSurveyReportExamineReq projSurveyReportExamineReq) {
        return projSurveyReportService.getReportLibraryPageLink(projSurveyReportExamineReq);
    }

    /**
     * 批量分配前端验证人
     *
     * @param param 参数
     */
    @Log(operName = "批量分配前端验证人", operDetail = "批量分配前端验证人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配前端验证人")
    @PostMapping(value = "/updateReportIdentifier")
    Result<Void> updateReportIdentifier(@RequestBody ProjSurveyFormResponsibilitiesReq param) {
        return projSurveyReportService.updateReportIdentifier(param);
    }

    /**
     * 前端项目成员验证打印报表通过
     *
     * @param param 参数
     */
    @Log(operName = "前端项目成员验证打印报表通过", operDetail = "前端项目成员验证打印报表通过", intLogType = Log.IntLogType.SELF_SYS, cnName = "前端项目成员验证打印报表通过")
    @PostMapping(value = "/verificationPassed")
    Result<Void> verificationPassed(@RequestBody PrintReportVerificationPassedParam param) {
        if (CollectionUtils.isEmpty(param.getSurveyReportIdList())) {
            return Result.fail("请选择要验证通过的打印报表");
        }
        boolean result = projSurveyReportService.verificationPassed(param);
        if (result) {
            return Result.success(null, "打印报表验证通过");
        }
        return Result.fail("打印报表验证通过，操作失败");
    }

    /**
     * 迁移打印报表驳回日志
     */
    @PostMapping(value = "/logRemove")
    Result<Void> logRemove() {
        try {
            projSurveyReportService.logRemove();
            return Result.success(null, "迁移成功");
        } catch (Exception e) {
            log.error("迁移打印报表驳回日志，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 导出报表数据
     *
     * @param response
     * @param dto
     */
    @Log(operName = "导出报表数据", operDetail = "导出报表数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "导出报表数据")
    @ApiOperation("导出报表数据")
    @PostMapping(value = "/reportPrintExportExcel")
    void reportPrintExportExcel(HttpServletResponse response, @RequestBody ProjSurveyReportReq dto) {
        projSurveyReportService.reportPrintExportExcel(response, dto);
    }

}
