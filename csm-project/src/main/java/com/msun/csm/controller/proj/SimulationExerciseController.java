package com.msun.csm.controller.proj;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.SimulationEnvironmentSelectedVO;
import com.msun.csm.dao.entity.proj.SimulationHospital;
import com.msun.csm.dao.entity.proj.SimulationHospitalDTO;
import com.msun.csm.dao.entity.proj.SimulationHospitalVO;
import com.msun.csm.dao.entity.proj.SimulationRoleVO;
import com.msun.csm.model.SimulationStatisticalDataVO;
import com.msun.csm.model.param.ExportSimulationStatisticalDataParam;
import com.msun.csm.model.param.QuerySimulationEnvironmentParam;
import com.msun.csm.model.param.QuerySimulationHospitalOptionParam;
import com.msun.csm.model.param.QuerySimulationHospitalParam;
import com.msun.csm.model.param.QuerySimulationRoleParam;
import com.msun.csm.model.param.QuerySimulationStatisticalDataParam;
import com.msun.csm.model.param.SaveSimulationHospitalParam;
import com.msun.csm.service.proj.SimulationExerciseService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户模拟练习
 */
@Api(tags = "客户模拟练习")
@Slf4j
@RestController
@RequestMapping("simulationExercise")
public class SimulationExerciseController {

    @Resource
    private SimulationExerciseService simulationExerciseService;


    /**
     * 查询客户模拟练习的环境信息
     */
    @PostMapping(value = "/querySimulationEnvironment")
    Result<SimulationEnvironmentSelectedVO> querySimulationEnvironment(@Validated @RequestBody QuerySimulationEnvironmentParam param, BindingResult bindingResult) {
        log.info("查询客户模拟练习的环境信息，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询客户模拟练习的环境信息，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(simulationExerciseService.querySimulationEnvironment(param));
        } catch (Exception e) {
            log.error("查询客户模拟练习的环境信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询客户模拟练习的医院信息
     */
    @PostMapping(value = "/querySimulationHospital")
    Result<List<SimulationHospitalDTO>> querySimulationHospital(@Validated @RequestBody QuerySimulationHospitalParam param, BindingResult bindingResult) {
        log.info("查询客户模拟练习的医院信息，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询客户模拟练习的医院信息，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(simulationExerciseService.querySimulationHospital(param));
        } catch (Exception e) {
            log.error("查询客户模拟练习的医院信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 保存客户模拟练习的医院信息
     */
    @PostMapping(value = "/saveSimulationHospital")
    Result<Void> saveSimulationHospital(@Validated @RequestBody SaveSimulationHospitalParam param, BindingResult bindingResult) {
        log.info("保存客户模拟练习的医院信息，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("保存客户模拟练习的医院信息，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            Boolean saveResult = simulationExerciseService.saveSimulationHospital(param);
            if (saveResult) {
                return Result.success();
            }
            return Result.fail("保存失败");
        } catch (Exception e) {
            log.error("保存客户模拟练习的医院信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询客户模拟练习的医院下拉框
     */
    @PostMapping(value = "/querySimulationHospitalOption")
    Result<List<SimulationHospitalVO>> querySimulationHospitalOption(@Validated @RequestBody QuerySimulationHospitalOptionParam param, BindingResult bindingResult) {
        log.info("查询客户模拟练习的医院下拉框，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询客户模拟练习的医院下拉框，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(simulationExerciseService.querySimulationHospitalOption(param));
        } catch (Exception e) {
            log.error("查询客户模拟练习的医院下拉框，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询客户模拟练习的角色信息
     */
    @PostMapping(value = "/querySimulationRole")
    Result<List<SimulationRoleVO>> querySimulationRole(@Validated @RequestBody QuerySimulationRoleParam param, BindingResult bindingResult) {
        log.info("查询客户模拟练习的角色信息，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询客户模拟练习的角色信息，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }

        try {
            return Result.success(simulationExerciseService.querySimulationRole(param));
        } catch (Exception e) {
            log.error("查询客户模拟练习的角色信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 查询客户模拟练习的统计数据
     */
    @PostMapping(value = "/querySimulationStatisticalData")
    Result<SimulationStatisticalDataVO> querySimulationStatisticalData(@Validated @RequestBody QuerySimulationStatisticalDataParam param, BindingResult bindingResult) {
        log.info("查询客户模拟练习的统计数据，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("查询客户模拟练习的统计数据，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            return new Result<>(702, msg.toString(), null);
        }
        //学习统计时需要先校验当前医院域名是否已开通
        if (ObjectUtil.isNotEmpty(param.getBusinessCode()) && "study".equals(param.getBusinessCode())) {
            List<SimulationHospital> notOpenHospital = simulationExerciseService.findNotOpenHospital(param.getCustomInfoId());
            if (CollectionUtil.isNotEmpty(notOpenHospital)) {
                return Result.fail("当前项目下存在还未开通域名的医院，暂无法查询统计数据！");
            }
        }

        try {
            return Result.success(simulationExerciseService.querySimulationStatisticalData(param));
        } catch (Exception e) {
            log.error("查询客户模拟练习的统计数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 导出客户模拟练习的统计数据
     */
    @PostMapping(value = "/exportSimulationStatisticalData")
    void exportSimulationStatisticalData(@Validated @RequestBody ExportSimulationStatisticalDataParam param, BindingResult bindingResult, HttpServletResponse response) throws IOException {
        log.info("导出学习与练习统计数据，参数={}", JSON.toJSONString(param));
        // 参数校验，拼接校验信息
        if (bindingResult.hasErrors()) {
            StringBuilder msg = new StringBuilder("导出学习与练习统计数据，");
            // 遍历校验结果信息，拼接校验信息到msg中
            bindingResult.getFieldErrors().forEach(fieldError -> msg.append(fieldError.getDefaultMessage()).append("；"));
            response.getWriter().println(JSON.toJSONString(new Result<>(702, msg.toString(), null)));
            return;
        }

        List<ProjHospitalInfo> queryHospital = simulationExerciseService.getQueryHospital(param.getEnvironment(), param.getCustomInfoId(), param.getProjectInfoId(), param.getCloudHospitalId());
        String name;
        if (queryHospital.size() == 1) {
            name = queryHospital.get(0).getHospitalName() + "学习与练习数据统计" + "-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + ".xlsx";
        } else {
            name = "学习与练习数据统计" + "-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) + ".zip";
        }

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.reset();
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(name));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            /*response.setContentType("application/octet-stream;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(name.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8)));*/
            simulationExerciseService.exportSimulationStatisticalData(param, outputStream, queryHospital);
        } catch (Exception e) {
            log.error("导出学习与练习统计数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            response.getWriter().println(e.getMessage());
        }
    }

}
