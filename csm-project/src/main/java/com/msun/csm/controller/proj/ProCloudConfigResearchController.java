package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.cloudconfigresearch.ConfigEvaluationInfoListDTO;
import com.msun.csm.model.vo.cloudconfigresearch.CerifyPromptPopupVO;
import com.msun.csm.model.vo.cloudconfigresearch.CloudConfigUrlVO;
import com.msun.csm.service.proj.cloudconfigresearch.ProCloudConfigResearchService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 云健康产品配置可读性调研(ProCloudConfigResearch)控制器
 *
 * <AUTHOR>
 * @since 2024-08-20 15:13:54
 */

@RestController
@Api(tags = "云健康产品配置可读性调研")
@RequestMapping("/pro_cloud_config_research")
public class ProCloudConfigResearchController {

    @Resource
    private ProCloudConfigResearchService proCloudConfigResearchService;
    /**
     * 保存
     *
     * @param dto 数据传输对象

     @ApiOperation("保存")
     @PostMapping("/save") public Result<String> save(@ApiParam(value = "信息", example = "信息", required = true)
     @Validated(SaveGroup.class) @RequestBody ProCloudConfigResearchDTO dto) {
     return this.proCloudConfigResearchService.save(dto);
     }
     */
    /**
     * 更新
     *
     * @param dto

     @ApiOperation("更新")
     @PostMapping("/update") public Result<String> update(@ApiParam(value = "信息", example = "信息", required = true)
     @Validated(UpdateGroup.class) @RequestBody ProCloudConfigResearchDTO dto) {
     return this.proCloudConfigResearchService.update(dto);
     }*/
    /**
     * 删除信息
     *
     * @param dto 信息
     @ApiOperation("删除信息")
     @PostMapping("/delete") public Result<String> delete(@ApiParam(value = "信息", example = "信息", required = true)
     @Validated @RequestBody DeleteProCloudConfigResearchDTO dto) {
     return this.proCloudConfigResearchService.delete(dto);
     }*/
    /**
     * 作废
     *
     * @param id 作废业务ID
     @ApiOperation("作废")
     @GetMapping("/updateMcInvalid") public Result<String> updateMcInvalid(@ApiParam(value = "id业务主键", example =
     "id业务主键", required = true) Long id) {
     return this.proCloudConfigResearchService.updateMcInvalid(id);
     }*/
    /**
     * 根据id查询信息
     *
     * @param id id
     * @return vo 对象

     @ApiOperation("根据id查询信息")
     @GetMapping("/getProCloudConfigResearchById") public ProCloudConfigResearchVO getProCloudConfigResearchById
     (@ApiParam(value = "id", example = "id", required = true) Long id) {
     return this.proCloudConfigResearchService.getProCloudConfigResearchById(id);
     }*/

    /**
     * 说明: 云健康产品配置可读性调研-判断当前用户是否提示调研弹窗
     *
     * @param
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.cloudconfigresearch.CerifyPromptPopupVO>
     * @author: Yhongmin
     * @createAt: 2024/8/21 11:27
     * @remark: Copyright
     */
    @Log(operName = "云健康产品配置可读性调研", operDetail = "云健康产品配置可读性调研-判断当前用户是否提示调研弹窗",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云健康产品配置可读性调研-判断当前用户是否提示调研弹窗")
    @ApiOperation("判断当前用户是否提示调研弹窗")
    @GetMapping("/verifyPromptPopup")
    public Result<CerifyPromptPopupVO> verifyPromptPopup() {
        return this.proCloudConfigResearchService.verifyPromptPopup();
    }

    /**
     * 说明: 云健康产品配置可读性调研-获取云健康配置页面地址信息
     *
     * @return:com.msun.csm.common.model.Result<java.lang.Boolean>
     * @author: Yhongmin
     * @createAt: 2024/8/20 16:13
     * @remark: Copyright
     */
    @Log(operName = "云健康产品配置可读性调研", operDetail = "云健康产品配置可读性调研-获取云健康配置页面地址信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云健康产品配置可读性调研-获取云健康配置页面地址信息")
    @ApiOperation("获取云健康配置页面地址信息")
    @GetMapping("/getCloudConfigUrl")
    public List<CloudConfigUrlVO> getCloudConfigUrl() {
        return this.proCloudConfigResearchService.getCloudConfigUrl();
    }

    /**
     * 说明: 云健康产品配置可读性调研-提交到云健康系统管理
     *
     * @param evaluationInfoDTOList
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/8/21 16:32
     * @remark: Copyright
     */
    @ResponseBody
    @Log(operName = "云健康产品配置可读性调研", operDetail = "云健康产品配置可读性调研-提交到云健康系统管理",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "云健康产品配置可读性调研-提交到云健康系统管理")
    @ApiOperation("提交到云健康系统管理")
    @PostMapping("/saveConfigEvaluationInfo")
    public Result saveConfigEvaluationInfo(@RequestBody ConfigEvaluationInfoListDTO evaluationInfoDTOList) {
        return this.proCloudConfigResearchService.saveConfigEvaluationInfo(
                evaluationInfoDTOList.getEvaluationInfoDTOList());
    }
}
