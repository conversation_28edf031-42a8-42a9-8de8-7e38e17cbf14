package com.msun.csm.controller.config;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.service.config.ConfigTipService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2024-09-25 04:04:37
 */
@Slf4j
@RestController
@RequestMapping("/configTip")
@Api(tags = "提示配置相关接口")
public class ConfigTipController {

    @Resource
    private ConfigTipService configTipService;

}
