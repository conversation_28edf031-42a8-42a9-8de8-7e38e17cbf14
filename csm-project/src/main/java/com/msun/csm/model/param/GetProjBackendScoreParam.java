package com.msun.csm.model.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

import com.msun.csm.feign.entity.yunying.req.RewardInfo;

@Data
public class GetProjBackendScoreParam {

    /**
     * 当前登录人
     */
    private String userLoginName;

    /**
     * 项目编号
     */
    private Long projId;

    /**
     * 后端部门类型：1-数据组、2-接口组、3-业务组
     */
    private Integer orgType;

    /**
     * 后端部门ID
     */
    private Long backendOrgId;

    /**
     * 验收分数
     */
    private BigDecimal checkScore;

    /**
     * 扣的分数(负数)
     */
    private BigDecimal deductScore;

    /**
     * 扣分明细（有扣分分数时必填）
     */
    private List<RewardInfo> projBackendDetailList;
}
