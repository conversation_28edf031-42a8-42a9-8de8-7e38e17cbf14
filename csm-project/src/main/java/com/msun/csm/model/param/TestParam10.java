package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestParam10 {

    /**
     * 主键集合
     */
    @NotNull
    private List<Long> productSatisfactionSurveyRecordIdList;

}
