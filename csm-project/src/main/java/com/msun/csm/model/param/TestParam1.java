package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.msun.csm.dao.entity.proj.CloudDeptInfo;
import com.msun.csm.dao.entity.proj.CloudUserAccount;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestParam1 {

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 产品ID
     */
    private List<Long> yyProductIdList;

    /**
     * 医院ID
     */
    private Long hospitalInfoId;

    /**
     * 部门信息
     */
    private CloudDeptInfo dept;

    /**
     * 用户信息
     */
    private List<CloudUserAccount> userList;


}
