package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_unite
 */
@Data
public class FormlibResourceUniteSelectListReq {

    /**
     * 表单结构表主键
     */
    @ApiModelProperty(value = "表单结构表主键")
    private Long formlibResourceUniteId;

    /**
     * 运营产品id
     */
    @ApiModelProperty(value = "运营产品id")
    private Long yyProductId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "分类id")
    private Long formlibStandardId;

    @ApiModelProperty(value = "分类字典名称id")
    private Long formlibStandardTypeId;

    @ApiModelProperty(value = "路径id")
    private Long pathId;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "调研表单的主键")
    private Long surveyFormId;
}