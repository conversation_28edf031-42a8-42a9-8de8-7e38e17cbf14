package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单资源库分类大类维护")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class DictFormLibStandardTypePageReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long formlibStandardTypeId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 树形id
     */
    @ApiModelProperty(value = "树形id")
    private Long formlibStandardId;

    /**
     * 树形id
     */
    @ApiModelProperty(value = "树形id")
    private List<Long> ids;

    @ApiModelProperty("逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    @ApiModelProperty(value = "路径id")
    private Long pathId;

}
