package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetProjectFileRuleParam {

    /**
     * 首期非首期可用标识：-1.通用；0.非首期可用；1.首期可用
     */
    private Integer hisFlag;

    /**
     * 项目实施类型：-1.通用；1.老换新可用；2.新上线可用
     */
    private Integer upgradationType;

    /**
     * 单体是否可用：0.否；1.是
     */
    private Integer monomerFlag;

    /**
     * 区域是否可用：0.否；1.是
     */
    private Integer regionFlag;

    /**
     * 电销是否可用：0.否；1.是
     */
    private Integer telesalesFlag;

    /**
     * 是否关联里程碑节点：0.否；1.是
     */
    private Integer milestoneNodeFlag;

    /**
     * 关联的里程碑节点编码
     */
    private String milestoneNodeCode;

    /**
     * 项目ID
     */
    private Long projectInfoId;
}
