package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单资源库分类大类维护")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class DictFormLibStandardPageReq {
    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 树形id
     */
    @ApiModelProperty(value = "分类id")
    private Long formlibStandardId;

    @ApiModelProperty(value = "父级id")
    private Long pid;

    @ApiModelProperty(value = "是否作废 表单资源库筛选时必须传 0，查询启用的数据")
    private Integer isDeleted;

    @ApiModelProperty(value = "路径id")
    private Long pathId;

}
