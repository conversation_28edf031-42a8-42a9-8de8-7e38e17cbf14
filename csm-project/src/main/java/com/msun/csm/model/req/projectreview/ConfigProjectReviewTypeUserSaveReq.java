package com.msun.csm.model.req.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 项目审核类型对应人员配置表
 *
 * <AUTHOR>
 * @TableName config_project_review_type_user
 */
@Data
public class ConfigProjectReviewTypeUserSaveReq {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectReviewUserId;

    /**
     * 项目审核类型字典表dict_project_review_type主键
     */
    @ApiModelProperty(value = "项目审核类型字典表dict_project_review_type主键")
    private Long projectReviewTypeId;

    /**
     * 审核方式id 取字典表
     */
    @ApiModelProperty(value = "审核方式id 取字典表")
    private Long reviewMethodId;

    /**
     * 服务团队/部门 部门id
     */
    @ApiModelProperty(value = "服务团队/部门 部门id")
    private Long reviewDeptId;

    @ApiModelProperty(value = "主负责人")
    private Long reviewLeaderUserId;

    /**
     * 人员id多选，
     */
    @ApiModelProperty("人员id集合 新增/修改入参")
    private List<Long> reviewUserListIds;

}
