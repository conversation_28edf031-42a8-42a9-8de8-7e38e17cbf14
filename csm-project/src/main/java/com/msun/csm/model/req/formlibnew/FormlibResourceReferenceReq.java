package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_unite
 */
@Data
public class FormlibResourceReferenceReq {
    @ApiModelProperty(value = "调研表单的主键")
    private Long surveyFormId;

    /**
     * 引用资源库的id
     */
    @ApiModelProperty(value = "表单结构表主键")
    private Long formlibResourceUniteId;

    @ApiModelProperty(value = "是否包含路径（后端区分设计还是调用使用） 1是 0否")
    private Integer isIncludePath;

}