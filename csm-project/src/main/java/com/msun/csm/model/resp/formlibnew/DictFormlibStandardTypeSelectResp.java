package com.msun.csm.model.resp.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.msun.csm.common.model.po.BasePO;

/**
 * 表单标准名称字典
 *
 * @TableName dict_formlib_standard_type
 */
@Data
public class DictFormlibStandardTypeSelectResp extends BasePO {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formlibStandardTypeId;

    /**
     * 运营产品id
     */
    @ApiModelProperty("运营产品id")
    private Long yyProductId;

    @ApiModelProperty("运营产品名称")
    private String yyProductName;

    /**
     * dict_formlib_standard分大类id
     */
    @ApiModelProperty("dict_formlib_standard分大类id 前端应用该值")
    private String formlibStandardId;

    @ApiModelProperty("dict_formlib_standard分大类id 原始值Long")
    private Long formlibStandardIdOld;

    /**
     * 编码
     */
    @ApiModelProperty("编码")
    private String formlibStandardTypeCode;

    /**
     * 名称
     */
    @ApiModelProperty("修改展示名称")
    private String formlibStandardTypeName;

    /**
     * 名称
     */
    @ApiModelProperty("列表展示名称")
    private String formlibStandardTypeNameSelectShow;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 上线必备
     */
    @ApiModelProperty("上线必备")
    private Integer onlineEssential;


    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("更新人")
    private String updateUserName;

    @ApiModelProperty(value = "路径id")
    private String pathId;

    @ApiModelProperty(value = "路径name")
    private String pathName;

}