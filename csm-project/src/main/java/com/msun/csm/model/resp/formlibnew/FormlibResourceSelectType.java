package com.msun.csm.model.resp.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_type
 */
@TableName(value = "formlib_resource_type")
@Data
public class FormlibResourceSelectType extends BasePO {
    /**
     * 表单资源库表入库记录表主键
     */
    @TableId(type = IdType.INPUT)
    private Long formlibResourceTypeId;

    /**
     * formlib_resource_unite的id
     */
    @TableField(value = "formlib_resource_unite_id")
    @ApiModelProperty(value = "分类字典的id")
    private Long formlibResourceUniteId;

    @ApiModelProperty(value = "标准分类的id")
    private Long formlibStandardId;

    @ApiModelProperty(value = "分类字典名称")
    private String formlibStandardName;

    /**
     * dict_formlib_standard_type分标准字典id
     */
    @TableField(value = "formlib_standard_type_id")
    @ApiModelProperty(value = "标准字典的id")
    private Long formlibStandardTypeId;

    @ApiModelProperty(value = "字典名称")
    private String formlibStandardTypeName;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("更新人")
    private String updateUserName;

    @ApiModelProperty(value = "路径id")
    private Long pathId;

    @ApiModelProperty(value = "路径name")
    private String pathName;

}