package com.msun.csm.model.resp.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核类型对应人员配置表
 *
 * <AUTHOR>
 * @TableName config_project_review_type_user
 */
@Data
public class ConfigProjectReviewTypeUserResp extends BasePO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectReviewUserId;

    /**
     * 项目审核类型字典表dict_project_review_type主键
     */
    @ApiModelProperty(value = "项目审核类型字典表dict_project_review_type主键")
    private Long projectReviewTypeId;

    @ApiModelProperty("项目审核类型名称")
    private String projectReviewTypeName;

    /**
     * 审核方式id 取字典表
     */
    @ApiModelProperty(value = "审核方式id 取字典表")
    private Long reviewMethodId;

    @ApiModelProperty("审核方式名称")
    private String reviewMethodName;

    /**
     * 服务团队/部门 部门id
     */
    @ApiModelProperty("服务团队/部门 部门id")
    private Long reviewDeptId;

    @ApiModelProperty("服务团队/部门名称")
    private String reviewDeptName;

    /**
     * 人员id多选，sys_user表sys_user_id
     */
    @ApiModelProperty("人员id多选，sys_user表sys_user_id")
    private String reviewUserId;

    @ApiModelProperty("人员名称 多选")
    private String reviewUserNames;

    @ApiModelProperty(value = "主负责人")
    private Long reviewLeaderUserId;

    @ApiModelProperty("主负责人名称")
    private String reviewLeaderUserName;

    @ApiModelProperty(value = "创建人")
    private String createrName;
    @ApiModelProperty(value = "更新人")
    private String updaterName;

    /**
     * 人员id多选，
     */
    @ApiModelProperty("人员id集合 用于修改回显")
    private List<String> reviewUserListIds;

}
