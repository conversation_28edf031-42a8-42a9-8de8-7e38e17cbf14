package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

import com.msun.core.component.implementation.api.imsp.vo.ThridWorkflowResourceQueryVO;
import com.msun.csm.common.model.BaseIdNameResp;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/12/10:30
 */
@Data
public class ProjThirdInterfaceDTO {

    @ApiModelProperty ("接口id集合")
    List<Long> thirdInterfaceIdList;
    /**
     * 三方接口主键id
     */
    @ApiModelProperty (value = "三方接口主键id")
    private Long thirdInterfaceId;
    @ApiModelProperty ("前端标志 【1-裁定通过 2-裁定驳回 3-待评审 4-转需求 5-重新裁定】")
    private Integer flag;

    /**
     * 实施地客户id
     */
    @ApiModelProperty (value = "实施地客户id")
    private Long customInfoId;

    /**
     * 项目类型【1：单体；2：区域】
     */
    @ApiModelProperty (value = "项目类型【1：单体；2：区域】")
    private Integer projectType;

    /**
     * 项目id
     */
    @ApiModelProperty (value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty (value = "医院id")
    private Long hospitalInfoId;

    /**
     * 接口字典id
     */
    @ApiModelProperty (value = "接口字典id")
    private Long dictInterfaceId;

    /**
     * 接口字典名称
     */
    @ApiModelProperty (value = "接口字典名称")
    private String dictInterfaceName;

    /**
     * 接口厂商id
     */
    @ApiModelProperty (value = "接口厂商id")
    private Long dictInterfaceFirmId;

    /**
     * 接口厂商名称
     */
    @ApiModelProperty (value = "接口厂商名称")
    private String dictInterfaceFirmName;

    /**
     * 接口版本
     */
    @ApiModelProperty (value = "接口版本")
    private String thirdInterfaceVersion;

    /**
     * 厂商联系人
     */
    @ApiModelProperty (value = "厂商联系人")
    private String firmContactName;

    /**
     * 厂商联系人电话
     */
    @ApiModelProperty (value = "厂商联系人电话")
    private String firmContactPhone;

    /**
     * 上线必备标识【0：否；1：是】
     */
    @ApiModelProperty (value = "上线必备标识【0：否；1：是】")
    private Integer onlineFlag;

    /**
     * 期望完成时间
     */
    @ApiModelProperty (value = "期望完成时间")
    private Date expectTime;

    /**
     * 业务场景描述
     */
    @ApiModelProperty (value = "业务场景描述")
    private String businessDesc;

    /**
     * 接口产品【页面叫 ‘定价分类’】
     */
    @ApiModelProperty (value = "接口产品【页面叫 ‘定价分类’】")
    private Long yyProductId;

    /**
     * 接口状态【
     * 初始阶段： 0：未申请；1：提交裁定；
     * 裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回
     * 测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；
     * 正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】
     * 50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】
     * 】
     */
    @ApiModelProperty (
            value = "接口状态【\n"
                    + "初始阶段： 0：未申请；1：提交裁定；"
                    + "裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回"
                    + "测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；"
                    + "正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】；34：申请验收；35：验证通过；36：验证不通过"
                    + "50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】\n"
                    + "】")
    private Integer status;

    /**
     * 实现方式【0：三方接口对接；1：产品对接；2：项目组对接】
     */
    @ApiModelProperty (value = "实现方式【0：三方接口对接；1：产品对接；2：项目组对接】")
    private Integer implementsType;

    /**
     * 责任团队id
     */
    @ApiModelProperty (value = "责任团队id")
    private Long dirTeamId;

    /**
     * 责任人id
     */
    @ApiModelProperty (value = "责任人id")
    private Long dirPersonId;

    /**
     * 接口委托书文件【文件数据存储为proj_project_file主键id】,多个用逗号隔开
     */
    @ApiModelProperty (value = "接口委托书文件【文件数据存储为proj_project_file主键id】,多个用逗号隔开")
    private String authLetterFiles;

    /**
     * 三方接口合同文件【文件数据存储为proj_project_file主键id】，多个用逗号隔开
     */
    @ApiModelProperty (value = "三方接口合同文件【文件数据存储为proj_project_file主键id】，多个用逗号隔开")
    private String thirdContractFiles;

    /**
     * 三方接口文档【文件数据存储为proj_project_file主键id】，多个用逗号隔开
     */
    @ApiModelProperty (value = "三方接口文档【文件数据存储为proj_project_file主键id】，多个用逗号隔开")
    private String thirdInterfaceFiles;

    /**
     * 数据集id【裁定时选择的数据集信息】
     */
    @ApiModelProperty (value = "数据集id【裁定时选择的数据集信息】")
    private Long dataSetId;

    /**
     * 数据集名称
     */
    @ApiModelProperty (value = "数据集名称")
    private String dataSetName;

    /**
     * 接口类型【0：上传类；1：交互类】
     */
    @ApiModelProperty (value = "接口类型【0：上传类；1：交互类 2 埋点类】")
    private Integer interfaceType;


    @ApiModelProperty ("转接口分公司时，运营平台接口实施流程返回的关联id")
    private Long mainId;

    @ApiModelProperty ("分组授权DTO")
    private List<ProjInterfaceGroupApplyDetailDTO> interfaceGroupApplyDetailDTOList;

    /**
     * 裁定时确定的分组Id信息【裁定专用】
     */
    private List<BaseIdNameResp> interfaceGroupList;

    @ApiModelProperty ("修改标识 【1：修改；2：裁定】")
    private Integer modifyFlag;

    /**
     * 审核意见
     */
    private String comments;

    /**
     * 运维平台反馈单id
     */
    @ApiModelProperty("运维平台反馈单id")
    private Long feedbackId;

    @ApiModelProperty ("ip信息")
    private String ip;

    @ApiModelProperty ("mac信息")
    private String mac;

    /**
     * 是否需要上传三方接口合同：0否1是
     */
    @ApiModelProperty("是否需要上传三方接口合同：0否1是")
    private Integer contractTag;

    /**
     * 选择的埋点接口数据
     */
    @ApiModelProperty ("选择的埋点接口数据")
    private List<ThridWorkflowResourceQueryVO> groupList;

    @ApiModelProperty("接口分类，来源于数据中台字典")
    private String interfaceCategory;

    @ApiModelProperty("接口分类编码，来源于数据中台字典")
    private String interfaceCategoryCode;

    @ApiModelProperty("字典接口编码")
    private String dictInterfaceCode;
}
