package com.msun.csm.model.resp.projreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.net.URL;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.util.ExcelUrlConverterUtil;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "报表数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportResp {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "调研收集的打印样式路径")
    @ExcelIgnore
    List<ProjProjectFile> surveyImgsList;
    @ApiModelProperty(value = "完成结果图片")
    @ExcelIgnore
    List<ProjProjectFile> finishImgsList;
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    @ExcelIgnore
    private Long surveyReportId;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    @ExcelIgnore
    private Long customerInfoId;

    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称", index = 0)
    @ColumnWidth(20)
    private String customName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelIgnore
    private Long projectInfoId;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 1)
    @ColumnWidth(20)
    private String projectName;
    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    @ExcelIgnore
    private Long hospitalInfoId;

    @ApiModelProperty(value = "医院名称")
    @ExcelProperty(value = "医院名称", index = 2)
    @ColumnWidth(20)
    private String hospitalName;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @ExcelIgnore
    private Long yyProductId;

    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称", index = 3)
    @ColumnWidth(20)
    private String productName;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    @ExcelIgnore
    private Long yyModuleId;
    /**
     * 报表名称
     */
    @ApiModelProperty(value = "报表名称")
    @ExcelProperty(value = "报表名称", index = 4)
    @ColumnWidth(20)
    private String reportName;
    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    @ExcelIgnore
    private Integer onlineEssential;
    /**
     * 完成状态：
     * <p>7：调研信息待补充（批量新增之后待完善信息）</p>
     * <p>0：已保存</p>
     * <p>4：提交调研审核</p>
     * <p>6：调研审核驳回</p>
     * <p>5：调研审核通过</p>
     * <p>1：制作完成</p>
     * <p>2：制作完成已驳回（验证不通过）</p>
     * <p>8：制作完成验证通过</p>
     */
    @ApiModelProperty(value = "完成状态：")
    @ExcelIgnore
    private Integer finishStatus;

    @ApiModelProperty(value = "完成状态")
    @ExcelProperty(value = "完成状态", index = 7)
    @ColumnWidth(20)
    private String finishStatusStr;

    /**
     * 调研收集的打印样式路径，多个样式名称应使用英文逗号（,）分隔（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "调研收集的打印样式路径，多个样式名称应使用英文逗号（,）分隔")
    @ExcelIgnore
    private String surveyImgs;
    /**
     * 补充图片
     */
    @ApiModelProperty(value = "补充图片")
    @ExcelIgnore
    private String supplementImgs;
    /**
     * 完成结果图片
     */
    @ApiModelProperty(value = "完成结果图片")
    @ExcelIgnore
    private String finishImgs;
    /**
     * 打印节点
     */
    @ApiModelProperty(value = "打印节点")
    @ExcelProperty(value = "打印节点", index = 8)
    @ColumnWidth(20)
    private String printDataCode;
    /**
     * 报表标识用于报表平台中指定具体要处理的报表
     */
    @ApiModelProperty(value = "报表标识用于报表平台中指定具体要处理的报表")
    @ExcelIgnore
    private String reportFileTag;
    /**
     * 报表制作平台支持预览和跳转功能，当前实现中包括锐浪和众阳两种报表类型
     */
    @ApiModelProperty(value = "报表制作平台支持预览和跳转功能，当前实现中包括锐浪和众阳两种报表类型")
    @ExcelIgnore
    private String reportMakePlatform;
    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    @ExcelProperty(value = "审核意见", index = 21)
    @ColumnWidth(20)
    private String examineOpinion;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelIgnore
    private String remark;
    @ApiModelProperty(value = "展示设计制作，验收后不展示此按钮")
    @ExcelIgnore
    private Boolean isDesignMakeFlag;

    /**
     * 调研负责人
     */
    @ApiModelProperty(value = "调研负责人")
    @ExcelIgnore
    private Long surveyUserId;
    /**
     * 调研完成时间
     */
    @ApiModelProperty(value = "调研完成时间")
    @ExcelProperty(value = "调研完成时间", index = 15)
    @ColumnWidth(20)
    private Timestamp surveyFinishTime;
    /**
     * 制作负责人
     */
    @ApiModelProperty(value = "制作负责人")
    @ExcelIgnore
    private Long makeUserId;

    @ApiModelProperty(value = "制作责任人")
    @ExcelProperty(value = "制作责任人", index = 16)
    @ColumnWidth(20)
    private String makeUserName;

    /**
     * 提交制作完成时间
     */
    @ApiModelProperty(value = "提交制作完成时间")
    @ExcelProperty(value = "提交制作完成时间", index = 19)
    @ColumnWidth(20)
    private Date commitFinishTime;
    /**
     * 确认完成时间
     */
    @ApiModelProperty(value = "确认完成时间")
    @ExcelProperty(value = "确认完成时间", index = 20)
    @ColumnWidth(20)
    private Date makeFinishTime;

    @ApiModelProperty(value = "确认是否是负责人或项目经理")
    @ExcelIgnore
    private Boolean isLeaderOrProjectManagerFlag;

    @ApiModelProperty(value = "需打印终端数据")
    @ExcelIgnore
    private Integer needPrintCount;

    @ApiModelProperty(value = "完成比例")
    @ExcelIgnore
    private String finishRatio;

    @ApiModelProperty(value = "打印纸张大小")
    @ExcelProperty(value = "打印纸张大小", index = 9)
    @ColumnWidth(20)
    private String reportPaperSize;

    /**
     * 是否是默认选项 1默认 0 非默认
     */
    @ApiModelProperty(value = "是否是默认选项 1系统默认 0 自定义")
    @ExcelIgnore
    private Integer defaultFlag;

    @ApiModelProperty(value = "是否可提交")
    @ExcelIgnore
    private Boolean isNeedAuditorFlag;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "运维审核意见")
    @ExcelProperty(value = "运维审核意见", index = 12)
    @ColumnWidth(20)
    private String operationExamineOpinion;

    @ApiModelProperty(value = "运维审核状态 0 待审核 1 通过 2 驳回")
    @ExcelIgnore
    private String operationExamineStatusStr;
    @ApiModelProperty(value = "运维审核状态 0 待审核 1 通过 2 驳回")
    @ExcelIgnore
    private Integer operationExamineStatus;
    /**
     * 审核人
     */
    @ApiModelProperty(value = "分配审核人")
    @ExcelIgnore
    private Long reviewerUserId;

    @ApiModelProperty(value = "调研审核人")
    @ExcelProperty(value = "调研审核人", index = 11)
    @ColumnWidth(20)
    private String reviewerUserName;

    @ApiModelProperty(value = "调研负责人")
    @ExcelProperty(value = "调研负责人", index = 10)
    @ColumnWidth(20)
    private String surveyUserName;

    @ApiModelProperty(value = "创建人员id")
    @ExcelIgnore
    private Long createrId;

    @ApiModelProperty(value = "是否是前后项目")
    @ExcelIgnore
    private Boolean isBeforeAndAfterProjectsFlag;

    /**
     * 是否可以进行验证通过/验证驳回操作
     */
    @ExcelIgnore
    private Boolean canVerification;

    /**
     * 制作驳回次数
     */
    @ExcelProperty(value = "制作驳回次数", index = 17)
    @ColumnWidth(20)
    private Integer rejectCount;

    /**
     * 制作驳回原因
     */
    @ExcelProperty(value = "制作驳回原因", index = 18)
    @ColumnWidth(20)
    private String rejectReason;

    /**
     * 验证人
     */
    @ExcelIgnore
    private String identifierUserName;


    @ApiModelProperty(value = "调研图片")
    @ExcelProperty(value = "调研图片", index = 5, converter = ExcelUrlConverterUtil.class)
    private List<URL> surveyImgStrList;

    @ApiModelProperty(value = "制作完成图片")
    @ColumnWidth(30)
    @ExcelProperty(value = "制作完成图片", index = 6, converter = ExcelUrlConverterUtil.class)
    private List<URL> makeImgStrList;

    /**
     * 调研驳回次数
     */
    @ExcelProperty(value = "调研驳回次数", index = 13)
    @ColumnWidth(20)
    private Integer surveyRejectCount;

    /**
     * 调研驳回原因
     */
    @ExcelProperty(value = "调研驳回原因", index = 14)
    @ColumnWidth(20)
    private String surveyRejectReason;
}
