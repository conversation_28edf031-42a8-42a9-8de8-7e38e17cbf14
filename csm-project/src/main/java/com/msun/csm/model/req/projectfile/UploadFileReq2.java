package com.msun.csm.model.req.projectfile;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadFileReq2 {

    /**
     * 项目ID
     */
    @NotNull
    private Long projectInfoId;

    /**
     * 项目计划/里程碑节点编码，项目文件表的milestone_node_code
     */
    @NotNull
    private String nodeCode;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 项目文件规则编码
     */
    private String projectRuleCode;

    /**
     * 项目文件规则分类编码
     */
    private String classCode;

    /**
     * 项目文件规则分类下子项编码
     */
    private String itemCode;

    /**
     * 文件是否公开，默认为false
     */
    @NotNull
    private Boolean isPublic = false;

    /**
     * 上传的文件
     */
    @NotNull
    private MultipartFile file;

    /**
     * 主键
     */
    private String projectCommonFileId;

}
