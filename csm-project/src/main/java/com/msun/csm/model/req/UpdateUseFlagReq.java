package com.msun.csm.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUseFlagReq {

    /**
     * 项目ID
     */
    @NotNull
    private Long projectInfoId;

    /**
     * 项目计划/里程碑节点编码，项目文件表的milestone_node_code
     */
    @NotNull
    private String nodeCode;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 项目文件规则编码
     */
    private String projectRuleCode;

    /**
     * 项目文件规则分类编码
     */
    private String classCode;

    /**
     * 项目文件规则分类下子项编码
     */
    private String itemCode;

    /**
     * 是否使用：true-使用；false-不使用
     */
    @NotNull
    private Boolean useFlag;

    /**
     * 主键
     */
    private String projectCommonFileId;

}
