package com.msun.csm.model.req.project;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 7630
 * @description: 老升新边界问题特殊审批 （交付平台调用运营平台）参数
 * @date 2025/7/15 17:53
 */
@Data
public class SubmitSpecialApprovalReq {
    @Getter
    public enum Category {
        PRODUCT(1, "product", "产品特殊事项审批"),
        ;

        private final int value;
        private final String code;
        private final String desc;

        Category(int value, String code, String desc) {
            this.value = value;
            this.code = code;
            this.desc = desc;
        }
        public static Category fromCode(String code) {
            for (Category category : Category.values()) {
                if (StrUtil.equals(category.code, code)) {
                    return category;
                }
            }
            throw new IllegalArgumentException("Unknown Category value: " + code);
        }
    }

    private String token = "imsp"; //默认imsp
    private Long businessId; //业务编号（该接口传交付工单编号即可）
    private Integer issueCategory = 1; //就是上面的 Category.PRODUCT.value
    private String requirementDesc; //需求描述
    private Long applicantUserId; //申请人编号
    private Long applicantOrgId; //申请部门编号

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileListItem {
        private String fileName;
        private String fileUrl;
    }

    private List<FileListItem> fileList; //特批文件列表
}
