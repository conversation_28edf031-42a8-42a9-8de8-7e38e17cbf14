package com.msun.csm.model.dto.projsimulation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimulationUserDTO {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long id;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String name;

    /**
     * 用户账号
     */
    @ApiModelProperty("用户账号")
    private String account;

    /**
     * 云健康身份ID
     */
    private Long identityId;
}
