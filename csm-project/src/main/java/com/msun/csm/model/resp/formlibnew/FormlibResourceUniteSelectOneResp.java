package com.msun.csm.model.resp.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_unite
 */
@TableName(value = "formlib_resource_unite")
@Data
public class FormlibResourceUniteSelectOneResp extends BasePO {
    /**
     * 表单结构表主键
     */
    @ApiModelProperty(value = "表单结构表主键")
    private Long formlibResourceUniteId;

    /**
     * 运营产品id
     */
    @ApiModelProperty(value = "运营产品id")
    private Long yyProductId;

    /**
     * 运营产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String yyProductName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 表单来源  0 通用库 1 项目库
     */
    @ApiModelProperty(value = "表单来源  0 通用库 1 项目库")
    private Integer formSource;

    /**
     * 表单保存唯一id， 用于调中心端进行设计
     */
    @ApiModelProperty(value = "表单保存唯一id， 用于调中心端进行设计")
    private Long formSaveId;

    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formName;

    /**
     * 表单样式图片存储路径
     */
    @ApiModelProperty(value = "表单样式图片存储路径")
    private String formPicturePaths;

    /**
     * 引用次数
     */
    @ApiModelProperty(value = "引用次数")
    private Integer useCount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 表单结构(表单结构的Json对象数据)
     */
    @TableField(value = "form_structure")
    @ApiModelProperty(value = "表单结构(表单结构的Json对象数据)")
    private String formStructure;

    /**
     * 表单预览文件流(锐浪)
     */
    @TableField(value = "grf")
    @ApiModelProperty(value = "表单预览文件流(锐浪)")
    private String grf;

    /**
     * 众阳设计器
     */
    @TableField(value = "designer_grf")
    @ApiModelProperty(value = "众阳设计器")
    private String designerGrf;

    /**
     * 预留其他设计器文本
     */
    @ApiModelProperty(value = "预留其他设计器文本")
    private String designerGrfApp;

    /**
     * 预留其他设计器文本
     */
    @ApiModelProperty(value = "预留其他设计器文本")
    private String designerGrfPad;

    /**
     * 机构id
     */
    @TableField(value = "his_org_id")
    @ApiModelProperty(value = "机构id")
    private Long hisOrgId;

    /**
     * 医院id
     */
    @TableField(value = "source_hospital_id")
    @ApiModelProperty(value = "医院id")
    private Long sourceHospitalId;

    /**
     * 医院名称
     */
    @TableField(value = "source_hospital_name")
    @ApiModelProperty(value = "医院名称")
    private String sourceHospitalName;

    /**
     * 众阳设计器模板解析后文字（原用于推荐内容数据）
     */
    @TableField(value = "parsed_text")
    @ApiModelProperty(value = "众阳设计器模板解析后文字（原用于推荐内容数据）")
    private String parsedText;

    @ApiModelProperty(value = "资源库标签")
    private List<FormlibResourceSelectType> formlibResourceTypes;

    /**
     * 产品属性表单类型(如评估单、记录单、知情文件等) 手麻流程节点
     */
    @TableField(value = "form_type")
    private String formType;

    /**
     * pc端格式配置
     */
    private String formConfigurationPc;

    /**
     * app端格式配置
     */
    private String formConfigurationApp;

    /**
     * pad端格式配置
     */
    private String formConfigurationPad;

}