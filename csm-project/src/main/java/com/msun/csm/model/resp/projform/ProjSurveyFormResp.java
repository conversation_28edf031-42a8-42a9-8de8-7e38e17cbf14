package com.msun.csm.model.resp.projform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.net.URL;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.util.ExcelUrlConverterUtil;

/**
 * 表单主表
 *
 * @TableName proj_survey_form
 */
@TableName(value = "proj_survey_form", schema = "csm")
@Data
public class ProjSurveyFormResp extends BasePO implements Serializable {

    @ExcelIgnore
    @ApiModelProperty(value = "调研收集的打印样式路径")
    List<ProjProjectFile> surveyImgsList;
    @ExcelIgnore
    @ApiModelProperty(value = "完成结果图片")
    List<ProjProjectFile> finishImgsList;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @ExcelIgnore
    private Long surveyFormId;
    /**
     * 客户id(proj_customer_info)
     */
    @ApiModelProperty(value = "客户id(proj_customer_info)")
    @ExcelIgnore
    private Long customerInfoId;

    /**
     * 项目id(proj_project_info)
     */
    @ApiModelProperty(value = "项目id(proj_project_info)")
    @ExcelIgnore
    private Long projectInfoId;

    /**
     * 医院编号(proj_hospital_info)（交付平台id）
     */
    @ApiModelProperty(value = "医院编号(proj_hospital_info)（交付平台id）")
    @ExcelIgnore
    private Long hospitalInfoId;

    /**
     * 产品id， 区分哪个产品(dict_product)
     */
    @ApiModelProperty(value = "产品id， 区分哪个产品(dict_product)")
    @ExcelIgnore
    private Long yyProductId;
    /**
     * 模块id(dict_product_vs_modules)
     */
    @ApiModelProperty(value = "模块id(dict_product_vs_modules)")
    @ExcelIgnore
    private Long yyModuleId;

    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    @ExcelIgnore
    private Integer onlineEssential;
    /**
     * 完成状态：
     * <p>0：未开始</p>
     * <p>4：提交调研审核</p>
     * <p>6：调研审核驳回</p>
     * <p>5：调研审核通过</p>
     * <p>1：制作完成</p>
     * <p>2：制作完成已驳回</p>
     * <p>8：制作完成验证通过</p>
     */
    @ApiModelProperty(value = "完成状态：0: 未完成1: 客服提交审核   （已提交审核）")
    @ExcelIgnore
    private Integer finishStatus;
    /**
     * 调研收集的样式路径，多个样式名称应使用英文逗号（,）分隔（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "调研收集的样式路径，多个样式名称应使用英文")
    @ExcelIgnore
    private String surveyImgs;
    /**
     * 预留后期补充调研表单图片（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "预留后期补充调研表单图片")
    @ExcelIgnore
    private String supplementImgs;
    /**
     * 在客服完成操作后上传的图片路径，多个图片路径应使用英文逗号（,）分隔；（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "在客服完成操作后上传的图片路径，")
    @ExcelIgnore
    private String finishImgs;
    /**
     * 表单分类（dict_form_type 表 type_code）
     */
    @ApiModelProperty(value = "表单分类（dict_form_type 表 type_code）")
    @ExcelIgnore
    private String typeCode;
    /**
     * 调研负责人
     */
    @ApiModelProperty(value = "调研负责人")
    @ExcelIgnore
    private Long surveyUserId;

    /**
     * 制作负责人
     */
    @ApiModelProperty(value = "制作负责人")
    @ExcelIgnore
    private Long makeUserId;

    /**
     * 表单来源（（产品调研、老系统、新增)）
     */
    @ApiModelProperty(value = "表单来源（（cpdy: 产品调研、lxt:老系统、xz:新增)）")
    @ExcelIgnore
    private String formSource;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    @ExcelIgnore
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @ExcelIgnore
    private Long createrId;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新")
    @ExcelIgnore
    private Long updaterId;

    @ApiModelProperty(value = "是否是设计制作")
    @ExcelIgnore
    private Boolean isDesignMakeFlag;


    @ApiModelProperty(value = "是否是项目主管或项目经理")
    @ExcelIgnore
    private Boolean isLeaderOrProjectManagerFlag;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    @ExcelIgnore
    private Long reviewerUserId;

    @ApiModelProperty(value = "运维审核状态 0 待审核 1 通过 2 驳回")
    @ExcelIgnore
    private Integer operationExamineStatus;

    @ApiModelProperty(value = "是否需要审核人")
    @ExcelIgnore
    private Boolean isNeedAuditorFlag;

    @ApiModelProperty(value = "是否显示按钮")
    @ExcelIgnore
    private Boolean isShowRecommendBtn;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见(原项目经理审核提交完成审核意见)")
    @ExcelIgnore
    private String examineOpinion;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelIgnore
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @ExcelIgnore
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @ExcelIgnore
    private Timestamp updateTime;

    /**********导出字段***************/

    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称", index = 0)
    @ColumnWidth(20)
    private String customName;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 1)
    @ColumnWidth(20)
    private String projectName;
    @ApiModelProperty(value = "医院名称")
    @ExcelProperty(value = "医院名称", index = 2)
    @ColumnWidth(20)
    private String hospitalName;
    @ApiModelProperty(value = "产品名称")
    @ExcelProperty(value = "产品名称", index = 3)
    @ColumnWidth(20)
    private String productName;
    /**
     * 表单名称
     */
    @ExcelProperty(value = "表单名称", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "调研图片")
    @ExcelProperty(value = "调研图片", index = 5, converter = ExcelUrlConverterUtil.class)
    private List<URL> surveyImgStrList;

    @ApiModelProperty(value = "制作完成图片")
    @ColumnWidth(30)
    @ExcelProperty(value = "制作完成图片", index = 6, converter = ExcelUrlConverterUtil.class)
    private List<URL> makeImgStrList;

    /**
     * 调研完成时间
     */
    @ApiModelProperty(value = "调研完成时间")
    @ExcelProperty(value = "调研完成时间", index = 7)
    @ColumnWidth(20)
    private Date surveyFinishTime;

    /**
     * 提交制作完成时间
     */
    @ApiModelProperty(value = "提交制作完成时间")
    @ExcelProperty(value = "提交制作完成时间", index = 8)
    @ColumnWidth(20)
    private Date commitFinishTime;
    /**
     * 确认完成时间
     */
    @ApiModelProperty(value = "确认完成时间")
    @ExcelProperty(value = "确认完成时间", index = 9)
    @ColumnWidth(20)
    private Date makeFinishTime;

    @ApiModelProperty(value = "完成状态")
    @ExcelProperty(value = "完成状态", index = 10)
    @ColumnWidth(20)
    private String finishStatusStr;

    /**
     * 临时存储跳转路径
     */
    @ApiModelProperty(value = "临时存储跳转路径")
    @ExcelProperty(value = "临时存储跳转路径", index = 11)
    @ColumnWidth(20)
    private String formPageUrl;
    /**
     * （云健康产品编码）
     */
    @ApiModelProperty(value = "（云健康产品编码）")
    @ExcelProperty(value = "云健康产品编码", index = 12)
    @ColumnWidth(20)
    private String cloudProductCode;

    @ApiModelProperty(value = "制作责任人")
    @ExcelProperty(value = "制作责任人", index = 13)
    @ColumnWidth(20)
    private String makeUserName;


    @ApiModelProperty(value = "来源")
    @ExcelProperty(value = "来源", index = 14)
    @ColumnWidth(20)
    private String formSourceStr;

    @ApiModelProperty(value = "调研负责人")
    @ExcelProperty(value = "调研负责人", index = 15)
    @ColumnWidth(20)
    private String surveyUserName;


    @ApiModelProperty(value = "调研审核人")
    @ExcelProperty(value = "调研审核人", index = 16)
    @ColumnWidth(20)
    private String reviewerUserName;


    @ApiModelProperty(value = "运维审核状态 0 待审核 1 通过 2 驳回")
    @ExcelProperty(value = "运维审核状态", index = 17)
    @ColumnWidth(20)
    private String operationExamineStatusStr;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "运维审核意见")
    @ExcelProperty(value = "运维审核意见", index = 18)
    @ColumnWidth(20)
    private String operationExamineOpinion;

    /**
     * 调研驳回次数
     */
    @ExcelProperty(value = "调研驳回次数", index = 19)
    @ColumnWidth(20)
    private Integer surveyRejectCount;

    /**
     * 调研驳回原因
     */
    @ExcelProperty(value = "调研驳回原因", index = 20)
    @ColumnWidth(20)
    private String surveyRejectReason;

    /**
     * 制作驳回次数
     */
    @ExcelProperty(value = "制作驳回次数", index = 21)
    @ColumnWidth(20)
    private Integer rejectCount;

    /**
     * 制作驳回原因
     */
    @ExcelProperty(value = "制作驳回原因", index = 22)
    @ColumnWidth(25)
    private String rejectReason;

    /**
     * 是否可以进行验证通过/验证驳回操作
     */
    @ExcelIgnore
    private Boolean canVerification;

    /**
     * 验证人ID
     */
    @ExcelIgnore
    private Long identifierUserId;

    /**
     * 验证人
     */
    @ExcelIgnore
    private String identifierUserName;

/**********************************************************************************************************************/
    @ApiModelProperty(value = "表单分类：dict_formlib_standard分大类id")
    private String formlibStandardId;

    @ApiModelProperty(value = "表单分类")
    private String formlibStandardName;

    @ApiModelProperty(value = "表单标准名称：dict_formlib_standard_type分标准字典id")
    private String formlibStandardTypeId;

    @ApiModelProperty(value = "表单标准名称")
    private String formlibStandardTypeName;

    @ApiModelProperty(value = "表单尺寸")
    private String formPaperSize;

    @ApiModelProperty(value = "调研方式")
    private String formSurveyMethod;

    @ApiModelProperty(value = "引用项目资源库标记id")
    private String formlibResourceUniteId;

    @ApiModelProperty(value = "资源库图片")
    private String formPicturePaths;

    @ExcelIgnore
    @ApiModelProperty(value = "资源库图片")
    List<ProjProjectFile> formlibImgsList;

    @ApiModelProperty(value = "导入云健康状态 -1 默认0 导入成功 1 导入失败; 不等于-1时跳转云健康")
    private Integer importCloudStatus;

    @ApiModelProperty(value = "导入云健康导入备注信息")
    private String importMsg;
/********************************************************************************************************************/

}