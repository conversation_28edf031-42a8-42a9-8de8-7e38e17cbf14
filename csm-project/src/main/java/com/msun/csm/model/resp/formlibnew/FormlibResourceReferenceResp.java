package com.msun.csm.model.resp.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_unite
 */
@Data
public class FormlibResourceReferenceResp {
    /**
     * 表单结构表主键
     */
    @ApiModelProperty(value = "表单结构表主键")
    private Long formlibResourceUniteId;

    /**
     * 运营产品id
     */
    @ApiModelProperty(value = "运营产品id")
    private Long yyProductId;

    /**
     * 运营产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String yyProductName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

}