package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestParam8 {

    /**
     * 主键
     */
    @NotNull(message = "参数【productSatisfactionSurveyRecordId】不可为null")
    private Long productSatisfactionSurveyRecordId;



}
