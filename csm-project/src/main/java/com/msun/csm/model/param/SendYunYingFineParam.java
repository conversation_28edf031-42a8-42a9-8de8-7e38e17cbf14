package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendYunYingFineParam {

    /**
     * UUID字符串
     */
    private String uuid;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息描述
     */
    private String description;

    /**
     * 点击消息后跳转的链接
     */
    private String url;

    /**
     * 图文消息的图片链接，支持JPG、PNG格式，尺寸最佳效果为640*320，缺少此字段时在客户端不显示图片
     */
    private String picurl;

    /**
     * 消息接收人的登录名，多个人用英文逗号隔开
     */
    private String toUser;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 开始时间
     */
    private String startedDate;

    /**
     * 标准结束时间
     */
    private String standardEndDate;

    /**
     * 消息类型1罚单 2预警单
     */
    private String messageType;

    /**
     * 罚单说明
     */
    private String remarks;

    /**
     * 功能模块编码
     */
    private String funValue;

    /**
     * 监控点
     */
    private String monitorPoint;

    /**
     * 消息来源1001交付平台 1002知识库 1003 LIS开放平台
     */
    private String source = "1001";
}
