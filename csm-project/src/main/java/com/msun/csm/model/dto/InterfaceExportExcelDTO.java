package com.msun.csm.model.dto;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;

import lombok.Data;

/**
 * 三方接口导出模板
 *
 * @Author: duxu
 * @Date: 2024/09/20/9:22
 */
@Data
public class InterfaceExportExcelDTO {

    /**
     * 接口名称
     */
    @ExcelProperty (value = "接口名称")
    @ColumnWidth (20)
    private String dictInterfaceName;

    /**
     * 接口类型【0：上传类；1：交互类】
     */
    @ExcelProperty (value = "接口类型")
    @ColumnWidth (12)
    private String interfaceTypeName;

    /**
     * 接口分类
     */
    @ExcelProperty (value = "接口分类")
    @ColumnWidth (16)
    private String interfaceClassName;

    /**
     * 数据集名称
     */
    @ExcelProperty (value = "数据集名称")
    @ColumnWidth (16)
    private String dataSetName;

    /**
     * 接口厂商名称
     */
    @ExcelProperty (value = "厂商名称")
    @ColumnWidth (16)
    private String dictInterfaceFirmName;

    /**
     * 接口版本
     */
    @ExcelProperty (value = "接口版本")
    @ColumnWidth (12)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String thirdInterfaceVersion;

    /**
     * 厂商联系人
     */
    @ExcelProperty (value = "厂商联系人")
    @ColumnWidth (14)
    private String firmContactName;

    /**
     * 厂商联系人电话
     */
    @ExcelProperty (value = "厂商联系人电话")
    @ColumnWidth (20)
    private String firmContactPhone;

    /**
     * 上线必备标识【0：否；1：是】
     */
    @ExcelProperty (value = "上线必备")
    @ColumnWidth (12)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String onlineFlag;

    /**
     * 期望完成时间
     */
    @ExcelProperty (value = "期望完成时间")
    @ColumnWidth (20)
    private Date expectTime;

    /**
     * 业务场景描述
     */
    @ExcelProperty (value = "业务场景描述")
    @ColumnWidth (50)
    private String businessDesc;

    /**
     * 接口产品【页面叫 ‘定价分类’】
     */
    @ExcelProperty (value = "定价分类")
    @ColumnWidth (16)
    private String productName;

    /**
     * 接口状态【
     * 初始阶段： 0：未申请；1：提交裁定；
     * 裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回
     * 测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；
     * 正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】
     * 50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】
     * 】
     */
    @ExcelProperty (value = "接口状态")
    @ColumnWidth (20)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String statusName;

    /**
     * 实现方式【0：三方接口对接；1：产品对接；2：项目组对接】
     */
    @ExcelProperty (value = "实现方式")
    @ColumnWidth (12)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String implementsTypeName;

    /**
     * 责任团队名称
     */
    @ExcelProperty (value = "责任团队名称")
    @ColumnWidth (18)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String dirTeamName;

    /**
     * 责任人姓名
     */
    @ExcelProperty (value = "责任人姓名")
    @ColumnWidth (14)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String dirPersonName;

    /**
     * 来源医院
     */
    @ExcelProperty (value = "来源医院")
    @ColumnWidth (20)
    private String hospitalName;

    /**
     * 调研来源
     */
    @ExcelProperty (value = "调研来源")
    @ColumnWidth (12)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String interfaceSource;
}
