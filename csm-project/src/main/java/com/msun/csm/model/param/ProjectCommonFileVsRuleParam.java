package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectCommonFileVsRuleParam {

    /**
     * 主键
     */
    private Long projectCommonFileId;

    /**
     * 作废标识【0.正常；1.作废】
     */
    private Integer isDeleted;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 项目文件规则编码
     */
    private String projectRuleCode;

    /**
     * 项目计划/里程碑节点编码
     */
    private String nodeCode;

    /**
     * 项目文件规则分类编码
     */
    private String classCode;

    /**
     * 项目文件规则分类下子项编码
     */
    private String itemCode;

    /**
     * 项目ID
     */
    private Long projectInfoId;

}
