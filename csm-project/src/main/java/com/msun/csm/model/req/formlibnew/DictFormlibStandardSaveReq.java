package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库标准字典
 *
 * @TableName dict_formlib_standard
 */
@TableName(value = "dict_formlib_standard", schema = "csm")
@Data
public class DictFormlibStandardSaveReq extends BasePO {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long formlibStandardId;

    /**
     * 父级id
     */
    @ApiModelProperty("父级id")
    private Long parentId;

    /**
     * 运营产品id
     */
    @ApiModelProperty("运营产品id")
    private Long yyProductId;

    /**
     * 字典分类编码
     */
    @ApiModelProperty("字典分类编码")
    private String formlibStandardCode;

    /**
     * 字典分类名称
     */
    @ApiModelProperty("字典分类名称")
    private String formlibStandardName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 标准分类1是 0否
     */
    @ApiModelProperty("标准分类1是 0否")
    private Integer isStandard;

    @ApiModelProperty(value = "路径id")
    private Long pathId;
}