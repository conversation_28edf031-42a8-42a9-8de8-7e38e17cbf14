package com.msun.csm.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/30 19:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelectOption {
    private String label;
    private Object value;
    private List<SelectOption> children;

    public SelectOption(String label, Object value) {
        this.label = label;
        this.value = value;
    }
}
