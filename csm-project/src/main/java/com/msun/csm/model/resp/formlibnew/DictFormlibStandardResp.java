package com.msun.csm.model.resp.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库标准字典
 *
 * @TableName dict_formlib_standard
 */
@TableName(value = "dict_formlib_standard", schema = "csm")
@Data
public class DictFormlibStandardResp extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long formlibStandardId;

    /**
     * 父级id
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 运营产品id
     */
    private Long yyProductId;

    @ApiModelProperty("产品名称")
    private String yyProductName;

    /**
     * 字典分类编码
     */
    @ApiModelProperty("字典分类编码")
    private String formlibStandardCode;

    /**
     * 字典分类名称
     */
    @TableField(value = "formlib_standard_name")
    @ApiModelProperty("字典分类名称")
    private String formlibStandardName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 排序
     */
    @TableField(value = "order_num")
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 标准分类1是 0否
     */
    @TableField(value = "is_standard")
    private Integer isStandard;

    @ApiModelProperty("创建人")
    private String createUserName;

    @ApiModelProperty("更新人")
    private String updateUserName;

    @ApiModelProperty("明细数量")
    private Integer detailCount;

    @ApiModelProperty("层级")
    private Integer level;

    @ApiModelProperty(value = "路径id")
    private String pathId;

    @ApiModelProperty(value = "路径name")
    private String pathName;

    List<DictFormlibStandardResp> children;
}