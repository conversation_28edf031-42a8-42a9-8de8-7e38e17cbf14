package com.msun.csm.model.resp.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核模式配置表
 *
 * <AUTHOR>
 * @TableName config_project_review
 */
@Data
public class ConfigProjectReviewResp extends BasePO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectReviewId;

    /**
     * 审核类型id 取字典表 dict_project_review_type
     */
    @ApiModelProperty(value = "审核类型id 取字典表 dict_project_review_type")
    private Long reviewTypeId;

    @ApiModelProperty(value = "审核类型名称")
    private String reviewTypeName;

    /**
     * 客户类型 -1 通用 1单体 2区域
     */
    @ApiModelProperty(value = "客户类型 -1 通用 1单体 2区域")
    private Integer customType;

    @ApiModelProperty(value = "客户类型")
    private String customTypeName;


    /**
     * 电销属性 -1 通用 1 电销 0非电销
     */
    @ApiModelProperty(value = "电销属性 -1 通用 1 电销 0非电销")
    private Integer telesalesFlag;

    @ApiModelProperty(value = "电销属性")
    private String telesalesFlagName;

    /**
     * 交付模式 -1 通用 1 前后端模式  0非前后端模式
     */
    @ApiModelProperty(value = "交付模式 -1 通用 1 前后端模式  0非前后端模式")
    private Integer deliveryModel;

    @ApiModelProperty(value = "交付模式")
    private String deliveryModelName;

    /**
     * 项目类型 -1 通用 1 首期 2 非首期
     */
    @ApiModelProperty(value = "项目类型 -1 通用 1 首期 2 非首期")
    private Integer projectType;

    @ApiModelProperty(value = "项目类型")
    private String projectTypeName;

    /**
     * 审核方式id 取字典表dict_review_method_type
     */
    @ApiModelProperty(value = "审核方式id 取字典表dict_review_method_type")
    private Long reviewMethodId;

    @ApiModelProperty(value = "审核方式名称")
    private String reviewMethodName;
    /**
     * 时间单位：分钟、小时、天
     */
    @ApiModelProperty(value = "分钟、小时、天")
    private String reviewUnit;

    /**
     * 审核周期
     */
    @ApiModelProperty(value = "审核周期")
    private String reviewPeriod;

    /**
     * 审核结束前N（分钟/小时/天）发送预警，逗号分割数字
     */
    @ApiModelProperty(value = "审核结束前N（分钟/小时/天）发送预警，逗号分割数字")
    private String warningBeforeReviewEnd;

    /**
     * 是否产生罚单 0.否 1.是
     */
    @ApiModelProperty(value = "是否产生罚单 0.否 1.是")
    private Integer isFineFlag;

    /**
     * 罚款金额
     */
    @ApiModelProperty(value = "罚款金额")
    private Integer fineMoney;

    /**
     * 是否排除节假日【true：排除；false：不排除】
     */
    @ApiModelProperty("是否排除节假日【true：排除；false：不排除】")
    private Boolean excludeHolidaysFlag;
    /**
     * 是否排除周末【true：排除；false：不排除】
     */
    @ApiModelProperty("是否排除周末【true：排除；false：不排除】")
    private Boolean excludeWeekendsFlag;
    /**
     * 是否排除非工作时间【true：排除；false：不排除】
     * 排除非工作时间也就是只是在工作时间计算，不包含下班后
     */
    @ApiModelProperty("是否排除非工作时间【true：排除；false：不排除】")
    private Boolean excludeNonWorkingHoursFlag;
    /**
     * 处罚性质【1：一次性处罚；2：周期性处罚】
     */
    @ApiModelProperty("处罚性质【1：一次性处罚；2：周期性处罚】")
    private Integer penaltyType;

    /**
     * 处罚周期单位：秒、分、时、天
     */
    @ApiModelProperty("处罚周期单位：秒、分、时、天")
    private String penaltyPeriodUnit;

    /**
     * 处罚周期【0：一次性处罚（penalty_type值为1时）；大于0：处罚周期天数（penalty_type值为2时）】
     */
    @ApiModelProperty("处罚周期【0：一次性处罚（penalty_type值为1时）；大于0：处罚周期天数（penalty_type值为2时）】")
    private String penaltyPeriod;

    /**
     * 处罚前N（分钟/小时/天）发送预警，逗号分割数字
     */
    @ApiModelProperty("处罚前N（分钟/小时/天）发送预警，逗号分割数字")
    private String warningBeforePenalty;
    /**
     * 调用运营平台出罚单/预警单的funValue参数，可以区分罚款金额及处罚场景，是监控点monitorPoint的下级分类
     */
    @ApiModelProperty("调用运营平台出罚单/预警单的funValue参数，可以区分罚款金额及处罚场景，是监控点monitorPoint的下级分类")
    private String yyFunValue;
    /**
     * 调用运营平台出罚单/预警单的monitorPoint参数，可以区分罚款金额及处罚场景
     */
    @ApiModelProperty("调用运营平台出罚单/预警单的monitorPoint参数，可以区分罚款金额及处罚场景")
    private String yyMonitorPoint;

    @ApiModelProperty(value = "创建人")
    private String createrName;
    @ApiModelProperty(value = "更新人")
    private String updaterName;
}
