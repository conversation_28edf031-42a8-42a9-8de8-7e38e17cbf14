package com.msun.csm.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SurveyPlanAuditInfo {

    /**
     * 调研计划唯一主键
     */
    private Long surveyPlanId;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 项目工单号
     */
    private String projectNumber;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 调研人
     */
    private String surveyUserName;

    /**
     * 调研完成时间
     */
    private String surveyCompleteTime;

    /**
     * 审核人ID
     */
    private Long auditSysUserId;

    /**
     * 审核人
     */
    private String auditUserName;

    /**
     * 审核计划完成时间
     */
    private String planAuditTime;

    /**
     * 审核实际完成时间
     */
    private String actualAuditTime;

    /**
     * 状态的数字编码
     */
    private Integer statusId;

    /**
     * 状态的字符编码
     */
    private String statusCode;

    /**
     * 状态的中文描述
     */
    private String statusDescription;

    /**
     * 状态对应的前端颜色
     */
    private String statusColor;

    /**
     * 状态整体属于哪种大状态：1-未开始/未完成；2-进行中；3-已完成
     */
    private Integer statusClass;

    /**
     * 调研驳回次数
     */
    private Integer surveyRejectCount;

    /**
     * 调研驳回原因
     */
    private String surveyRejectReason;
}
