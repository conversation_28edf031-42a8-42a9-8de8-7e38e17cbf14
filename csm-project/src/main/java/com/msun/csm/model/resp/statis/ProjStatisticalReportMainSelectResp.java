package com.msun.csm.model.resp.statis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.model.req.projreport.statis.ProjFileReq;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "分页")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjStatisticalReportMainSelectResp {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long statisticalReportMainId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;

    @ApiModelProperty(value = "客户名称")
    private String customName;

    /**
     * 项目id
     */
    private Long projectInfoId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 报表名称： 字符串
     */
    @ApiModelProperty(value = "报表名称： 字符串")
    private String reportName;

    @ApiModelProperty(value = "统计口径(全)")
    private String statisticalCalibrationNames;

    @ApiModelProperty(value = "统计口径(截取)")
    private String calibrationIdFirstFour;

    /**
     * "报表样式，存储到sys_file的主键，多个以','分割。
     * 通过file_path 获取到路径对应的图片"
     */
    private String reportStyle;

    @ApiModelProperty(value = "报表样式")
    private List<ProjFileReq> reportStyleList;

    /**
     * 制作方式： 下拉（当前4种）
     */
    private Long productionMethodId;
    private String productionMethodName;

    /**
     * "状态（枚举）：
     * <p>1：待申请裁定</p>
     * <p>11：裁定中</p>
     * <p>12：裁定驳回</p>
     * <p>13：裁定通过</p>
     * <p>21：制作中</p>
     * <p>22：制作完成</p>
     * <p>31：已下沉</p>
     * <p>41：已发布</p>
     */
    @ApiModelProperty(value = "状态（枚举）/1待申请裁定/11裁定中/12裁定驳回/13裁定通过/21制作中/31已下沉")
    private Integer reportStatus;

    /**
     * 使用科室 id： 关联 dict_hospital_dept 主键
     */
    @ApiModelProperty(value = "使用科室 id： 关联 dict_hospital_dept 主键")
    private Long hospitalDeptId;

    /**
     * 使用科室名称 ：
     */
    @ApiModelProperty(value = "使用科室名称 ：")
    private String hospitalDeptName;

    /**
     * 用途分类id 关联dict_report_purpose 主键
     */
    @ApiModelProperty(value = "用途分类id 关联dict_report_purpose 主键")
    private Long reportPurposeId;

    /**
     * 用途分类名称
     */
    @ApiModelProperty(value = "用途分类名称")
    private String reportPurposeName;

    /**
     * 使用频次id dict_report_frequency 主键
     */
    @ApiModelProperty(value = "使用频次id dict_report_frequency 主键")
    private Long reportFrequencyId;

    /**
     * 使用频次名称
     */
    @ApiModelProperty(value = "使用频次名称")
    private String reportFrequencyName;

    /**
     * 上线必备（1是 0否） 默认1
     */
    @ApiModelProperty(value = "上线必备（1是 0否） 默认1")
    private Integer onlineFlag;

    @ApiModelProperty(value = "上线必备false/true")
    private Boolean onlineFlagBoolean;

    /**
     * 统计报表备注
     */
    @ApiModelProperty(value = "统计报表备注")
    private String remarks;

    /**
     * 调研人
     */
    private Long surveyUserId;
    @ApiModelProperty(value = "调研人名称")
    private String surveyUserName;

    /**
     * 调研时间
     */
    @ApiModelProperty(value = "调研时间")
    private Timestamp surveyTime;

    /**
     * 裁定审核人
     */
    @ApiModelProperty(value = "裁定审核人")
    private Long auditUserId;
    @ApiModelProperty(value = "裁定审核人名称")
    private String auditUserName;

    /**
     * 裁定审核时间
     */
    @ApiModelProperty(value = "裁定审核时间")
    private Timestamp auditTime;

    /**
     * 分配负责人
     */
    @ApiModelProperty(value = "分配负责人")
    private Long allocateUserId;

    @ApiModelProperty(value = "分配负责人名称")
    private String allocateUserName;

    /**
     * 分配时间
     */
    @ApiModelProperty(value = "分配时间")
    private Timestamp allocateTime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private String planFinishTime;

    /**
     * 下沉人（完成人）
     */
    @ApiModelProperty(value = "下沉人（完成人）")
    private Long finishUserId;

    @ApiModelProperty(value = "下沉人（完成人）名称")
    private String finishUserName;

    /**
     * 下沉时间（完成时间）
     */
    @ApiModelProperty(value = "下沉时间（完成时间）")
    private Timestamp finishTime;

    @ApiModelProperty(value = "统计报表主表id")
    private String reportMainId;
    @ApiModelProperty(value = "统计报表主表名称")
    private String reportMainName;


    @ApiModelProperty(value = "医院信息ID")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    @ApiModelProperty(value = "裁定指标")
    private String reportTargets;

    @ApiModelProperty(value = "裁定指标集合code")
    private String reportTargetsCodeSplic;

    /**
     * 裁定备注，驳回必传
     */
    @ApiModelProperty(value = "裁定备注，驳回必传")
    private String operContent;

    @ApiModelProperty(value = "挂载路径")
    private String mountPath;

    @ApiModelProperty(value = "指标集合报表使用")
    private List<String> reportTargetsList;

    @ApiModelProperty(value = "裁定指标集合")
    private List<BaseIdCodeNameResp> reportTargetsCodes;

    @ApiModelProperty(value = "运维平台唯一标识")
    private String operationStatisticsReportId;

    @ApiModelProperty(value = "裁定产品名称")
    private String operationProductName;

    @ApiModelProperty(value = "裁定产品id")
    private Long operationProductId;

    /**
     * 是否可以进行验证通过/验证驳回操作
     */
    private Boolean canVerification;

    /**
     * 验证状态：1-验证通过；2-验证不通过
     */
    private Integer verificationStatus;

    /**
     * 验证状态名称
     */
    private String verificationStatusName;

    /**
     * 裁定驳回次数
     */
    private Integer surveyRejectCount;

    /**
     * 裁定驳回原因
     */
    private String surveyRejectReason;

    /**
     * 制作驳回次数
     */
    private Integer rejectCount;

    /**
     * 制作驳回原因
     */
    private String rejectReason;

    /**
     * 验证人
     */
    private String identifierUserName;
}
