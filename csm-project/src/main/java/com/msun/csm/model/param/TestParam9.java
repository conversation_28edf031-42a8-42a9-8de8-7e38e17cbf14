package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestParam9 {

    /**
     * 主键
     */
    @NotNull(message = "参数【productSatisfactionSurveyRecordId】不可为null")
    private Long productSatisfactionSurveyRecordId;

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 备注
     */
    private String remark;

}
