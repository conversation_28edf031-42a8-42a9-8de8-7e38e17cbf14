package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单标准名称字典
 *
 * @TableName dict_formlib_standard_type
 */
@TableName(value = "dict_formlib_standard_type", schema = "csm")
@Data
public class DictFormlibStandardTypeSaveReq extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long formlibStandardTypeId;

    /**
     * 运营产品id
     */
    @TableField(value = "yy_product_id")
    private Long yyProductId;

    /**
     * dict_formlib_standard分大类id
     */
    @TableField(value = "formlib_standard_id")
    private Long formlibStandardId;


    /**
     * 编码
     */
    @TableField(value = "formlib_standard_type_code")
    private String formlibStandardTypeCode;

    /**
     * 名称
     */
    @TableField(value = "formlib_standard_type_name")
    private String formlibStandardTypeName;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 排序
     */
    @TableField(value = "order_num")
    private Integer orderNum;

    /**
     * 上线必备
     */
    @TableField(value = "online_essential")
    private Integer onlineEssential;

    @ApiModelProperty(value = "路径id")
    private Long pathId;
}