package com.msun.csm.model.vo;

import lombok.Data;

import java.util.List;

@Data
public class ProjectFileUploadRuleVO {

    /**
     * 规则表主键
     */
    private Long ruleProjectRuleConfigId;

    /**
     * 规则表场景编码
     */
    private String sceneCode;

    /**
     * 项目文件规则编码
     */
    private String projectRuleCode;

    /**
     * 项目文件规则内容
     */
    private String projectRuleContent;

    /**
     * 验证方式：no-不验证；upload-必须上传文件；prompt-仅提示必须满足
     */
    private String verityWay;

    /**
     * 是否必须此条件：0.否；1.是
     */
    private Integer requiredFlag;

    /**
     * 上传条件类型
     * <p>1：必须上传且必须上传文件</p>
     * <p>2：必须上传但是可以勾选不使用</p>
     * <p>3：非必传</p>
     */
    private Integer requiredType;

    /**
     * 是否使用：true-使用；false-不使用
     */
    private Boolean useFlag;

    /**
     * 是否有模版文件：0.否；1.是
     */
    private Integer templateFlag;

    /**
     * 模版文件对应系统文件表文件编码
     */
    private String templateFileCode;

    /**
     * 模板文件路径
     */
    private String templateFilePath;

    /**
     * 排序号
     */
    private Integer orderNo;

    /**
     * 项目文件规则分类编码
     */
    private String classCode;

    /**
     * 项目文件规则分类下子项编码
     */
    private String itemCode;

    /**
     * 项目计划/里程碑节点编码
     */
    private String nodeCode;

    /**
     * 是否公开查看：0-PMO可查看；1-公开查看；2-隐藏
     */
    private Integer isPublic;

    /**
     * 是否关联子项标识：0-不关联子项；1-关联子项
     */
    private Integer containChildrenFlag;

    /**
     * 上传文件类型字符串，以逗号进行分割
     */
    private String limitType;

    /**
     * 上传文件类型数组
     */
    private List<String> fileLimitType;

    /**
     * 文件列表
     */
    private List<ProjProjectFileVO> fileList;

    /**
     * 规则表对应的上传的文件的ID
     */
    private Long projectCommonFileId;

    /**
     * 检测结果：1-未检测；2-检测通过；3-检测不通过
     */
    private Integer checkResult;

    /**
     * 检测结果文本
     */
    private String checkResultText;

}
