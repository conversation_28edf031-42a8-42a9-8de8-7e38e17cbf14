package com.msun.csm.model.req.formlibnew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单资源库分类大类维护")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class FormLibResourceUnitePageReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 表单结构表主键
     */
    @ApiModelProperty(value = "表单结构表主键")
    private Long formlibResourceUniteId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 树形id
     */
    @ApiModelProperty(value = "分类id")
    private List<Long> formlibStandardIds;

    @ApiModelProperty(value = "树形id 后端标记，不传值")
    private List<Long> ids;

    /**
     * 树形id
     */
    @ApiModelProperty(value = "字典id")
    private List<Long> formlibStandardTypeIds;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "表单来源  0 通用库 1 项目库")
    private Integer formSource;

    @ApiModelProperty(value = "作废标记")
    private Integer isDeleted;
}
