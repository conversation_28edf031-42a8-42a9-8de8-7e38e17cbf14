package com.msun.csm.feign.entity.dataapplication;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/13
 */
@Data
public class ResDataResp {

    @ApiModelProperty("数据类ID")
    private String dataClassId;
    @ApiModelProperty("数据类名称")
    private String dataClassName;

    /**
     * 说明
     */
    @ApiModelProperty("说明")
    private String classDesc;

    @ApiModelProperty("接口编码")
    private String dataClassCode;
    @ApiModelProperty("一级分类名称")
    private String dataClassFirstName;
    @ApiModelProperty("一级分类编码")
    private String dataClassFirstCode;
    @ApiModelProperty("二级分类名称")
    private String dataClassSecondName;
    @ApiModelProperty("二级分类编码")
    private String dataClassSecondCode;

}
