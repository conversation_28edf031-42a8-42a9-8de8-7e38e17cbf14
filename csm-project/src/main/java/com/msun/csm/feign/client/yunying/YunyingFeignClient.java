package com.msun.csm.feign.client.yunying;

import feign.Request;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.msun.csm.model.req.project.SubmitSpecialApprovalReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.yunying.YunyingResult;
import com.msun.csm.feign.entity.yunying.YyResult;
import com.msun.csm.feign.entity.yunying.req.*;
import com.msun.csm.feign.entity.yunying.resp.GetPreSaleUserResp;
import com.msun.csm.feign.entity.yunying.resp.YunYingMsgResp;
import com.msun.csm.model.dto.InterfaceMainDto;
import com.msun.csm.model.dto.UpdateYunyingDto;
import com.msun.csm.model.req.project.ProjectMemberToYunYingReq;


/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-09-26
 */
@FeignClient(url = "${project.feign.oa.url}", name = "YunyingFeignClient")
public interface YunyingFeignClient {

    /**
     * 软件验收申请
     *
     * @param options
     * @param softwareReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.check-apply-software}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String checkApplySoftware(Request.Options options, @RequestParam("userLoginname") String userLoginname,
                              @RequestBody CheckApplySoftwareReq softwareReq);

    /**
     * 硬件验收申请
     *
     * @param softwareReq
     * @return
     */
    @PostMapping(value = "${OA-config.check-apply-hardware}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String checkApplyHardware(@RequestParam("userLoginname") String userLoginname,
                              @RequestBody CheckApplyHardwareReq softwareReq);

    /**
     * #预付款回执
     * #通知运营平台
     *
     * @param baseReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.notify_pmo_audit}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String prePaymentFeedBack(@RequestParam("userLoginname") String userLoginname,
                              @RequestBody YyPayFeedBackDto baseReq);

    /**
     * #项目拆分
     *
     * @param baseReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.init-order}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String initWorkOrder(@RequestParam("userLoginname") String userLoginname, @RequestBody TokenReq baseReq);

    /**
     * #项目拆分
     *
     * @param splitReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.split-order}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String splitWorkOrder(@RequestParam("userLoginname") String userLoginname, @RequestBody OrderSplitReq splitReq);

    /**
     * #项目拆分
     *
     * @param mergeReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.merge-order}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String mergeWorkOrder(@RequestParam("userLoginname") String userLoginname, @RequestBody OrderMergeReq mergeReq);


    /**
     * 发送消息
     *
     * @param title
     * @param description
     * @param url
     * @param picurl
     * @param toUser
     * @return
     */
    @PostMapping(value = "${project.feign.oa.send-message}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    YunYingMsgResp sentMessage(@RequestParam("title") String title, @RequestParam("description") String description,
                               @RequestParam("url") String url, @RequestParam("picurl") String picurl,
                               @RequestParam("toUser") String toUser);


    /**
     * 同步云资源开通时间
     *
     * @param userLoginname
     * @param req
     * @return
     */
    @PostMapping(value = "${project.feign.oa.yun-open}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    Result<String> syncYzyOpen(@RequestParam("userLoginname") String userLoginname, @RequestBody YunOpenDTO req);

    /**
     * 云容灾验收申请
     *
     * @param userLoginname 操作用户
     * @param req           请求参数
     * @return Result<String>
     */
    @PostMapping(value = "${project.feign.oa.disaster_recovery_cloud_check_apply}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    Result<String> disasterCloudCheckApply(@RequestParam("userLoginname") String userLoginname,
                                           @RequestBody YunyingDisasterCloudCheckApplyDTO req);

    /**
     * 获取云容灾首付款缴纳信息
     *
     * @param req 请求参数
     * @return Result<String>
     */
    @PostMapping(value = "${project.feign.oa.get_disaster_cloud_paysignage}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    YunyingResult<List<YunyingPaysignageDataDTO>> getDisasterCloudPaysignage(@RequestParam("userLoginname") String userLoginname,
                                                                             @RequestBody YunyingDisasterCloudPaysignageDTO req);

    /**
     * 同步运营平台厂商数据
     *
     * @param req 请求参数
     * @return Result<String>
     */
    @PostMapping(value = "${project.feign.oa.syncIsMsunCloud}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    YunyingResult<String> syncIsMsunCloud(@RequestParam("userLoginname") String userLoginname,
                                          @RequestBody SyncIsMsunCloudDTO req);

    /**
     * 查询运营平台部门信息
     *
     * @return
     */
    @GetMapping(value = "${project.feign.oa.deptTask-method}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8",
            headers = {"accept=*/*", "connection=Keep-Alive",
                    "user-agent=Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)"})
    YyResult deptTask();

    /**
     * 查询运营平台人员信息
     *
     * @return
     */
    @GetMapping(value = "${project.feign.oa.userTask-method}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    @ResponseBody
    YyResult userTask();

    /**
     * 查询运营产品信息
     *
     * @return
     */
    @GetMapping(value = "${project.feign.oa.productsTask-method}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    @ResponseBody
    YyResult productsTask();

    /**
     * 说明: 同步客户基础信息
     *
     * @return:com.msun.csm.feign.entity.yunying.YyResult
     * @author: Yhongmin
     * @createAt: 2024/6/20 20:09
     * @remark: Copyright
     */
    @GetMapping(value = "${project.feign.oa.customersTask-method}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    @ResponseBody
    YyResult customersTask();

    /**
     * 说明: 同步客户基础信息
     *
     * @return:com.msun.csm.feign.entity.yunying.YyResult
     * @author: Yhongmin
     * @createAt: 2024/6/20 20:09
     * @remark: Copyright
     */
    @GetMapping(value = "${project.feign.oa.divisionProvinceTask-method}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    @ResponseBody
    YyResult divisionProvinceTask();

    /**
     * 说明: 获取单个客户的详细信息
     *
     * @param customerId
     * @return:com.msun.csm.feign.entity.yunying.YyResult
     * @author: Yhongmin
     * @createAt: 2024/6/20 20:31
     * @remark: Copyright
     */
    @GetMapping(value = "${project.feign.oa.customersDetailTask-method}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    @ResponseBody
    YyResult customersDetailTask(@RequestParam("customerId") Long customerId);

    /**
     * 更新反馈单状态
     *
     * @param statusReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.node-status-sync}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String updateNodeStatus(@RequestParam("userLoginname") String userLoginname,
                            @RequestBody UpdateNodeStatusDTO statusReq);

    /**
     * 同步云平台产品 开通时间
     *
     * @param userLoginname
     * @param dto
     * @return
     */
    @PostMapping(value = "${project.feign.oa.yun-product-open}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    ResponseData<String> syncCloudProductOpen(@RequestParam("userLoginname") String userLoginname,
                                              @RequestBody CloudProductTimeWDto dto);

    /**
     * 接口实施流程提交数据
     *
     * @param interfaceMainDto
     * @return
     */
    @PostMapping(value = "${project.feign.oa.interface-main-method}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    Map<String, Object> interfaceMain(@RequestBody InterfaceMainDto interfaceMainDto);

    /**
     * 接口实施流程提交数据
     *
     * @param transDemandReq
     * @return
     */
    @PostMapping(value = "${project.feign.oa.transferDemand}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String transferDemand(@RequestBody TransDemandReq transDemandReq);

    /**
     * 三方接口验证回写运营平台
     *
     * @param updateYunyingDto
     * @return
     */
    @PostMapping(value = "${project.feign.oa.interface-verify}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    Map<String, Object> interfaceVerify(@RequestBody UpdateYunyingDto updateYunyingDto);


    /**
     * 同步运营平台的项目验收申请时间、工期减免、入驻时间
     */
    @PostMapping(value = "${project.feign.oa.synchronizeDuration}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String synchronizeDuration(@RequestParam("userLoginname") String userLoginname,
                               @RequestBody SynchronizeDurationParam baseReq);

    /**
     * 有云免中间件部署申请
     */
    @PostMapping(value = "${project.feign.oa.middlewareApply}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    Result<String> middlewareApply(@RequestParam("userLoginname") String salePersonAccount,
                                   @RequestBody YunyingMidOrderDTO midOrderDTO);

    /**
     * 原方案分公司经理查询
     */
    @PostMapping(value = "${project.feign.oa.getPreSaleUser}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    GetPreSaleUserResp getPreSaleUser(@RequestParam("userLoginname") String userLoginname,
                                      @RequestBody GetPreSaleUserParam baseReq);


    /**
     * 企业微信消息推送(预警单 罚单)
     *
     * @param title
     * @param description
     * @param url
     * @param picurl
     * @param toUser
     * @param businessNo
     * @param businessId
     * @param startedDate
     * @param standardEndDate
     * @param messageType
     * @param remarks
     * @param funValue
     * @param monitorPoint
     * @param source
     * @return
     */
    @PostMapping(value = "${project.feign.oa.sendFineMessage}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    YunYingMsgResp sendFineMessage(@RequestParam("title") String title, @RequestParam("description") String description,
                                   @RequestParam("url") String url, @RequestParam("picurl") String picurl, @RequestParam("toUser") String toUser,
                                   @RequestParam("businessNo") String businessNo, @RequestParam("businessId") String businessId,
                                   @RequestParam("startedDate") String startedDate, @RequestParam("standardEndDate") String standardEndDate,
                                   @RequestParam("messageType") String messageType, @RequestParam("remarks") String remarks,
                                   @RequestParam("funValue") String funValue, @RequestParam("monitorPoint") String monitorPoint,
                                   @RequestParam("source") String source);

    /**
     * 查询是否是项目经理
     */
    @PostMapping(value = "${project.feign.oa.selectProjectMemberFlag}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    String selectProjectMemberFlag(@RequestParam("userLoginname") String userLoginname, @RequestBody ProjectMemberToYunYingReq req);

    /**
     * 作废项目经理
     */
    @PostMapping(value = "${project.feign.oa.deleteProjectMemberFlag}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    String deleteProjectMemberFlag(@RequestParam("userLoginname") String userLoginname, @RequestBody ProjectMemberToYunYingReq req);

    /**
     * 提交特殊事项审批
     *
     * @param userLoginname 用户登录名
     * @param req           提交特殊事项审批请求参数
     * @return String 返回结果{code: 200, msg: "success", specialId: 32323}
     */
    @PostMapping(value = "${project.feign.oa.submitSpecialApproval}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    JSONObject submitSpecialApproval(@RequestParam("userLoginname") String userLoginname, @RequestBody SubmitSpecialApprovalReq req);

    /**
     * <p>接口功能：同步后端得分信息给运营平台</p>
     * <p>运营平台文档标题：获取后端交付考核单据</p>
     *
     * @param userLoginname 当前登录人的运营平台账号
     * @param req
     * @return
     */
    @PostMapping(value = "${project.feign.oa.getProjBackendScore}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, consumes = "application/json;charset=UTF-8")
    String getProjBackendScore(@RequestParam("userLoginname") String userLoginname, @RequestBody GetProjBackendScoreReq req);

}
