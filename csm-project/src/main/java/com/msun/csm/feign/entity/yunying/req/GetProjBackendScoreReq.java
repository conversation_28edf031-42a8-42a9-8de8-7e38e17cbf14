package com.msun.csm.feign.entity.yunying.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 开通云资源
 */
@Data
public class GetProjBackendScoreReq {


    /**
     * 调用当前接口的密钥（安全考虑）,固定为imsp
     */
    private String token = "imsp";

    /**
     * 项目编号
     */
    private Long projId;

    /**
     * 后端部门类型：1-数据组、2-接口组、3-业务组
     */
    private Integer orgType;

    /**
     * 后端部门ID
     */
    private Long backendOrgId;

    /**
     * 验收分数
     */
    private BigDecimal checkScore;

    /**
     * 扣的分数(负数)
     */
    private BigDecimal deductScore;

    /**
     * 扣分明细（有扣分分数时必填）
     */
    private List<RewardInfo> projBackendDetailList;

}
