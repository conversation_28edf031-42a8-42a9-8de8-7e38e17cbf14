package com.msun.csm.feign.client.aotocheck;


import cn.hutool.json.JSONObject;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.msun.csm.common.model.AutoTestResult;
import com.msun.csm.model.dto.autotest.feign.GenerateLoginUserDTO;
import com.msun.csm.model.dto.autotest.feign.TaskBatchRunDTO;
import com.msun.csm.model.dto.autotest.feign.TaskBatchRunErrorContentDTO;

/**
 * @description:
 * @fileName:
 * @author:zhouzhaoyu
 * @updateBy:
 * @Date:Created in 16:14 2024/7/8
 * @remark:
 */

@FeignClient(url = "${project.feign.autoCheck.url}", name = "AutoCheckClient")
public interface AutoCheckClient {

    /**
     * 调用自动化测试平台  自动化测试平台添加医院、域名等信息
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "${project.feign.autoCheck.hospital}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject getHospitalInfo(@RequestBody JSONObject jsonObject);

    /**
     * 调用自动化平台  获取部门模版信息
     *
     * @return
     */
    @GetMapping(value = "${project.feign.autoCheck.deptModel}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject getDeptModel();

    /**
     * 调用自动化平台,获取部门模版信息（二期）
     *
     * @return 结果
     */
    @GetMapping(value = "${project.feign.autoCheck.generate_dept_model}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject generateDeptModel(@RequestBody JSONObject jsonObject);

    /**
     * 给自动化测试平台  推送测试人员账号信息
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "${project.feign.autoCheck.dept_list}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject submitAccountInfo(@RequestBody JSONObject jsonObject);

    /**
     * 调用自动化测试平台  获取医院人员部门人员账号信息
     *
     * @param hospitalid
     * @param hospitalName
     * @return
     */
    @GetMapping(value = "${project.feign.autoCheck.hospital_user_info}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject getHospitalUserInfo(@RequestParam("hospital_id") Long hospitalid,
                                   @RequestParam("hospital_name") String hospitalName);

    /**
     * 调用自动化测试平台, 获取生成的测试账号
     *
     * @param jsonObject 请求参数
     * @return 返回值
     */
    @PostMapping(value = "${project.feign.autoCheck.generate_login_user}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject generateLoginUser(@RequestBody JSONObject jsonObject);

    /**
     * 调用自动化测试平台, 生成测试账号, 用于批量测试时使用
     *
     * @param generateLoginUserDTO 请求参数. 含科室, 用户等信息
     * @return 成功或失败, 不含账号信息, 若执行异常会返回错误内容
     */
    @PostMapping(value = "${project.feign.autoCheck.generateLoginUser}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    AutoTestResult<Object, String> generateLoginUser(@RequestBody GenerateLoginUserDTO generateLoginUserDTO);

    /**
     * 批量运行测试
     *
     * @param taskBatchRunDTO 需要测试的医院参数
     * @return 成功或失败, 不含账号信息, 若执行异常会返回错误内容
     */
    @PostMapping(value = "${project.feign.autoCheck.taskBatchRun}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    AutoTestResult<Object, TaskBatchRunErrorContentDTO> taskBatchRun(@RequestBody TaskBatchRunDTO taskBatchRunDTO);


    /**
     * 将选中的账号信息传递给自动化测试平台
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "${project.feign.autoCheck.dept_list}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject sendDeptList(@RequestBody JSONObject jsonObject);

    /**
     * 自动化测试平台组建场景任务
     *
     * @param jsonObject
     * @return
     */
    @GetMapping(value = "${project.feign.autoCheck.assemble_task}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject assembleTask(@RequestParam JSONObject jsonObject);

    /**
     * 交付平台运行场景任务，需要校验任务是否组建完任务，运行状态不能为运行中，场景任务状态不能为测试通过
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "${project.feign.autoCheck.task_run}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject taskRun(@RequestBody JSONObject jsonObject);

    /**
     * 描述：将场景任务置为不可运行状态并且在在线医院列表中新增一条
     *
     * @param jsonObject
     * @return
     */
    @GetMapping(value = "${project.feign.autoCheck.task_close}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject taskClose(@RequestBody JSONObject jsonObject);

    /**
     * 调用自动化测试平台  获取SummaryReport
     *
     * @param reportId
     * @return
     */
    @GetMapping(value = "${project.feign.autoCheck.summary_report}", produces =
            MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject getSummaryReport(@RequestParam("report_id") String reportId);

    /**
     * 交付平台运行场景任务，需要校验任务是否组建完任务，运行状态不能为运行中，场景任务状态不能为测试通过
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "${project.feign.autoCheck.summary_report_mark}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8", headers = {"Authorization=aB3Kp5Q9wE6yU0R7I4O2N8M1SxVzLjHg"})
    JSONObject saveSummaryReport(@RequestBody JSONObject jsonObject);
}
