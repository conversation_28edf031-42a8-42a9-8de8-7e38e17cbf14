package com.msun.csm.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import static cn.hutool.core.date.DateUnit.MINUTE;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.utils.AesUtil;
import com.msun.core.component.implementation.api.port.DataPreparationApi;
import com.msun.core.component.implementation.api.port.HospitalApi;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlSwitchDTO;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlSysCode;
import com.msun.core.component.implementation.api.port.dto.SqlCheckApiDTO;
import com.msun.core.component.implementation.api.port.dto.SqlSelectLoginDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysDeptSub;
import com.msun.csm.dao.entity.SysRole;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.UserVsRole;
import com.msun.csm.dao.entity.config.ConfigCustomFormLimit;
import com.msun.csm.dao.entity.config.ScheduleTask;
import com.msun.csm.dao.entity.config.ScheduleTaskLog;
import com.msun.csm.dao.entity.dict.DictHardwareProduct;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.proj.EarlyWarningAndPenaltyMessages;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyFineConfig;
import com.msun.csm.dao.entity.proj.ProjSurveyNotice;
import com.msun.csm.dao.entity.proj.SimulationHospital;
import com.msun.csm.dao.mapper.config.ConfigCustomFormLimitMapper;
import com.msun.csm.dao.mapper.config.ScheduleTaskLogMapper;
import com.msun.csm.dao.mapper.config.ScheduleTaskMapper;
import com.msun.csm.dao.mapper.dict.DictHardwareProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.oldimsp.OldCustomerInfoMapper;
import com.msun.csm.dao.mapper.platform.DocMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyFineConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyNoticeMapper;
import com.msun.csm.dao.mapper.proj.SimulationHospitalMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.sysuser.UserVsRoleMapper;
import com.msun.csm.feign.client.oldimsp.OldImspFeignClient;
import com.msun.csm.feign.client.yunwei.YunweiFeignClient;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunwei.resp.SyncCustomerInfoVO;
import com.msun.csm.feign.entity.yunying.YyResult;
import com.msun.csm.feign.entity.yunying.resp.YunYingCustomersResp;
import com.msun.csm.feign.entity.yunying.resp.YunYingDeptResp;
import com.msun.csm.feign.entity.yunying.resp.YunYingDeptSubResp;
import com.msun.csm.feign.entity.yunying.resp.YunYingProductModulesResp;
import com.msun.csm.feign.entity.yunying.resp.YunYingUserResp;
import com.msun.csm.feign.entity.yunying.resp.YunyingProductResp;
import com.msun.csm.model.convert.SysDeptConvert;
import com.msun.csm.model.convert.SysUserConvert;
import com.msun.csm.model.dto.UserVsRoleSaveDTO;
import com.msun.csm.model.dto.dept.SysDeptDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.param.SendYunYingFineParam;
import com.msun.csm.model.param.SendYunYingMessageParam;
import com.msun.csm.model.req.project.GenerateEarlyWarningAndPenaltyMessageArgs;
import com.msun.csm.model.vo.dept.SysDeptVO;
import com.msun.csm.model.vo.role.SysRoleVO;
import com.msun.csm.service.dict.DictDateOfHolidaysService;
import com.msun.csm.service.dict.DictProvinceService;
import com.msun.csm.service.formlibrary.LibProductFormService;
import com.msun.csm.service.message.ProjectDailyInfoService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.ProjCustomInfoService;
import com.msun.csm.service.proj.ProjProductEmpowerAddRecordService;
import com.msun.csm.service.proj.ProjectDailyRecordService;
import com.msun.csm.service.proj.SynchronizeDurationService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;
import com.msun.csm.service.projtool.ProjectToolUpdateService;
import com.msun.csm.service.report.ReportAuthorizationStatisticsService;
import com.msun.csm.service.sysdept.SysDeptService;
import com.msun.csm.service.sysrole.SysRoleService;
import com.msun.csm.service.sysuser.SysUserService;
import com.msun.csm.service.tmp.TmpProjectNewVsOldService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.service.yunying.YunYingService;
import com.msun.csm.util.CsmSignUtil;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.FileUtil;
import com.msun.csm.util.FilterUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/22/8:58
 */
@Slf4j
@Component
public class TaskSchedule {
    @Value("${project.log-path}")
    private String logPath;

    @Value("${project.data-manager-path}")
    private String dataManagerPath;

    @Value("${project.basedata-excel-path}")
    private String basedataExcelPath;

    @Value("${project.doc-path}")
    private String docPath;

    @Value("${project.chis-data}")
    private String chisData;

    @Lazy
    @Autowired
    private TaskSchedule self;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysDeptConvert deptConvert;

    @Resource
    private SysUserConvert userConvert;

    @Resource
    private SysRoleService roleService;

    @Resource
    private DictProductMapper dictProductMapper;
    @Resource
    private YunyingFeignClient yunyingFeignClient;
    @Resource
    private DictProductVsModulesMapper dictProductVsModulesMapper;
    @Resource
    private ProjCustomInfoService projCustomInfoService;
    @Resource
    private ScheduleTaskMapper scheduleTaskMapper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ScheduleTaskLogMapper scheduleTaskLogMapper;
    @Resource
    private UserVsRoleMapper userVsRoleMapper;
    @Resource
    private DictHardwareProductMapper hardwareProductMapper;
    @Resource
    private ConfigCustomFormLimitMapper configCustomFormLimitMapper;

    @Resource
    private SynchronizeDurationService synchronizeDurationService;

    @Qualifier("com.msun.core.component.implementation.api.port.HospitalApi")
    @Resource
    private HospitalApi hospitalApi;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Autowired
    private ProjectToolUpdateService projectToolUpdateService;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private DictProvinceService dictProvinceService;
    @Resource
    private ProjectDailyInfoService projectDailyInfoService;
    @Resource
    private ReportAuthorizationStatisticsService reportAuthorizationStatisticsService;
    @Resource
    private TmpProjectNewVsOldService tmpProjectNewVsOldService;

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;

    @Resource
    private YunweiFeignClient yunweiFeignClient;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private ProjProductEmpowerAddRecordService productEmpowerAddRecordService;

    @Resource
    private OldImspFeignClient oldImspFeignClient;

    @Resource
    private SimulationHospitalMapper simulationHospitalMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private DataPreparationApi dataPreparationApi;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjSurveyNoticeMapper projSurveyNoticeMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjOrderInfoMapper projOrderInfoMapper;

    @Resource
    private YunYingService yunYingService;

    @Resource
    private ProjSurveyFineConfigMapper projSurveyFineConfigMapper;

    @Resource
    private OldCustomerInfoMapper oldCustomerInfoMapper;

    @Resource
    private DictDateOfHolidaysService dictDateOfHolidaysService;

    @Lazy
    @Resource
    private ProjApplyOrderService projApplyOrderService;

    @Lazy
    @Resource
    private LibProductFormService libProductFormService;

    @Autowired
    private SendBusinessMessageService sendBusinessMessageService;
    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private DocMapper docMapper;

    private static @NotNull
    CustFormCtrlSwitchDTO getSwitchDTO(Integer flag,
                                       List<ConfigCustomFormLimit> list,
                                       ProjHospitalInfo hospitalInfo) {
        CustFormCtrlSwitchDTO switchDTO = new CustFormCtrlSwitchDTO();
        List<CustFormCtrlSysCode> sysList = new ArrayList<>();
        Set<String> codeSet = list.stream().map(a -> a.getMsunHealthModuleCode())
                .collect(Collectors.toSet());
        CustFormCtrlSysCode sysCode = new CustFormCtrlSysCode();
        sysCode.setHospitalId(hospitalInfo.getCloudHospitalId().intValue());
        sysCode.setSystemCodeList(codeSet.toArray(new String[codeSet.size()]));
        sysList.add(sysCode);
        switchDTO.setSwitchFlag(flag);
        switchDTO.setSysList(sysList);
        switchDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        switchDTO.setHisOrgId(hospitalInfo.getOrgId());
        return switchDTO;
    }

    /**
     * 定时同步人员信息
     */
    //    @Scheduled (cron = "20 * * * * * ")
    @Async()
//    @Scheduled(cron = "0 5 1 * * * ")
    @Scheduled(cron = "0 0/30 * * * ?")
    public void userTask() {
        // 先同步部门
        deptTask();

        redisUtil.set("user_task", "user_task", 20, TimeUnit.MINUTES);
        YyResult result = yunyingFeignClient.userTask();
        log.info("人员同步开始=====");
        List<SysUserDTO> addUserList = new ArrayList<>();
        if (result.isSuccess() && result.getObj() != null) {
            // 强转为人员表数据
            List<YunYingUserResp> userResps = JSONArray.parseArray(JSON.toJSONString(result.getObj()),
                    YunYingUserResp.class);
            for (YunYingUserResp userResp : userResps) {
                SysUserDTO sysUserDTO = new SysUserDTO();
                sysUserDTO.setAccount(userResp.getLoginName());
                log.info("查询人员是否存在 =====   , {}", JSONUtil.toJsonStr(userResp));
                List<SysUser> userList = sysUserService.selectAllUser(sysUserDTO);
                if (CollectionUtil.isNotEmpty(userList)) {
                    for (SysUser user : userList) {
                        // 已经存在的 进行更新
                        final SysUserDTO updateUserDTO = getSysUserDTO(userResp, user);
                        sysUserService.updateUser(updateUserDTO);
                    }
                } else {
                    SysUser addUser = new SysUser();
                    // 不存在的 添加人员信息 ， 同步添加默认角色
                    addUser.setSysUserId(SnowFlakeUtil.getId());
                    final SysUserDTO addUserDTO = getSysUserDTO(userResp, addUser);
                    addUserList.add(addUserDTO);
                }
            }
            if (CollectionUtil.isNotEmpty(addUserList)) {
                // 当人员时新添加时 ，判断所属部门是否为客服中心 客服默认添加实施工程师角色
                List<Long> deptIdList = new LambdaQueryChainWrapper<>(sysDeptMapper)
                        .eq(SysDept::getDeptCategory, "custService")
                        .eq(SysDept::getIsDeleted, 0)
                        .list().stream().map(SysDept::getDeptYunyingId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(deptIdList)) {
                    // 根据code 查询实施工程师角色
                    SysRole role = new LambdaQueryChainWrapper<>(sysRoleMapper)
                            .eq(SysRole::getRoleCode, "14")
                            .eq(SysRole::getIsDeleted, 0)
                            .one();
                    // 当新增的人员所属部门包含在客服中心时，添加角色
                    for (SysUserDTO userDTO : addUserList) {
                        // 保存人员
                        sysUserService.saveUser(userDTO);
                        if (deptIdList.contains(userDTO.getDeptId())) {
                            //调用授权
                            UserVsRoleSaveDTO dto = new UserVsRoleSaveDTO();
                            dto.setUserId(userDTO.getSysUserId());
                            dto.setRoleIdList(Collections.singletonList(role.getSysRoleId()));
                            dto.setIsMainRoleId(role.getSysRoleId());
                            sysUserService.saveRoleByUserId(dto);
                        }
                    }
                }
            }

            // 同步老平台
            try {
                String auth = CsmSignUtil.getHeader();
                oldImspFeignClient.syncDeptAndUserTask(auth);
            } catch (Exception e) {
                log.error("老平台人员同步失败=====", e);
            }
            // 将云健康小组的人员添加运维工程师角色
            try {
                sysUserService.updateUserRole();
            } catch (Exception e) {
                log.error("将云健康小组的人员添加运维工程师角色=====", e);
            }
        }
        redisUtil.del("user_task");
        log.info("人员同步结束=====");
    }

    /**
     * 人员信息封装
     *
     * @param userResp
     * @param updateUser
     * @return
     */
    private SysUserDTO getSysUserDTO(YunYingUserResp userResp, SysUser updateUser) {
        updateUser.setUserYunyingId(userResp.getId());
        updateUser.setUserName(userResp.getName());
        updateUser.setAccount(userResp.getLoginName());
        updateUser.setEmail(StringUtils.isEmpty(userResp.getEmail()) ? null
                : userResp.getEmail());
        updateUser.setPhone(StringUtils.isEmpty(userResp.getMobile()) ? null
                : userResp.getMobile());
        updateUser.setSex(userResp.getSexName());
        updateUser.setDeptId(userResp.getOrgId());
        updateUser.setIsDeleted(ObjectUtil.isNotEmpty(userResp.getStatus()) && userResp.getStatus() == 1 ? 0 : 1);
        // 同步的数据不是院内用户
        updateUser.setHisUserFlag(0);
        SysUserDTO updateUserDTO = userConvert.po2Dto(updateUser);
        // 新增时 给id进行赋值
        updateUserDTO.setSysUserId(
                ObjectUtil.isNull(updateUser.getSysUserId()) ? SnowFlakeUtil.getId() : updateUserDTO.getSysUserId());
        return updateUserDTO;
    }

    /**
     * 定时同步部门信息
     */
    @Async()
    @Scheduled(cron = "0 12 1 * * * ")
    public void deptTask() {
        redisUtil.set("dept_task", "dept_task", 20, TimeUnit.MINUTES);
        List<UserVsRole> userVsRoles = new ArrayList<>();
        YyResult result = yunyingFeignClient.deptTask();
        log.info("部门同步开始=====");
        if (result.isSuccess() && result.getObj() != null) {
            // 强转为部门表数据
            List<YunYingDeptResp> deptResps = JSONArray.parseArray(JSON.toJSONString(result.getObj()),
                    YunYingDeptResp.class);
            // 获取客户服务中心下的部门信息
            List<Long> longs = saveRoleDeptIdList();
            for (YunYingDeptResp deptResp : deptResps) {
                SysDeptDTO sysDeptDTO = new SysDeptDTO();
                sysDeptDTO.setDeptYunyingId(deptResp.getId());
                Result<SysDeptVO> existUser = sysDeptService.getOne(sysDeptDTO);
                if (existUser.getData() != null) {
                    // 已经存在的 进行更新
                    SysDeptVO updateDept = existUser.getData();
                    final SysDeptDTO updateDeptDTO = getSysDeptDTO(deptResp, updateDept);
                    sysDeptService.updateDept(updateDeptDTO);
                } else {
                    SysDeptVO addDept = new SysDeptVO();
                    addDept.setSysDeptId(SnowFlakeUtil.getId());
                    final SysDeptDTO addDto = getSysDeptDTO(deptResp, addDept);
                    sysDeptService.insertDept(addDto);
                }
                // 只给客户服务中心下的部门进行赋值角色
                // 拼装客户服务中心下的部门id集合，当resp中存在时 继续走，不存在说明不属于客户服务中心，不走下面逻辑
                UserVsRole userVsRole = null;
                if (longs.contains(deptResp.getId())) {
                    userVsRole = infoDeptManagerForRole(deptResp);
                }
                log.info("定时任务同步部门信息，运营平台部门ID={}，运营平台部门名称={}，根据部门层级赋值部门负责人的角色={}", deptResp.getId(), deptResp.getName(), JSON.toJSONString(userVsRole));
                if (ObjectUtil.isNotEmpty(userVsRole)) {
                    // 判断人员角色表是否已存在该数据。当存在时跳过
                    List<UserVsRole> userVsRoles1 = userVsRoleMapper.selectList(new QueryWrapper<UserVsRole>()
                            .eq("user_id", userVsRole.getUserId())
                            .eq("role_id", userVsRole.getRoleId())
                    );
                    if (CollectionUtil.isEmpty(userVsRoles1)) {
                        userVsRoles.add(userVsRole);
                    }
                }
                // 副职
                YunYingDeptSubResp object = deptResp.getSubfunction();

                if (object != null) {
                    YunYingDeptSubResp subDept = JSONObject.parseObject(JSON.toJSONString(object), YunYingDeptSubResp.class);
                    String subOrgCategory = subDept.getSubOrgCategory();
                    Long subCeoUserId = subDept.getSubCeoUserId();
                    Long centerUserId = subDept.getSubCenterUserId();
                    Long subPidUserId = subDept.getSubPidUserId();
                    SysDeptSub deptSub = new SysDeptSub();
                    deptSub.setSysDeptSubId(SnowFlakeUtil.getId());
                    deptSub.setSysDeptId(deptResp.getId());    // 运营平台部门id
                    deptSub.setIsDeleted(0);
                    deptSub.setSubCenterUserId(centerUserId);
                    deptSub.setSubCeoUserId(subCeoUserId);
                    deptSub.setSubPidUserId(subPidUserId);
                    deptSub.setSubOrgCategory(subOrgCategory);
                    sysDeptService.saveSubDept(deptSub);
                }

                // 批量保存部门负责人的角色
                log.info("定时任务同步部门信息，运营平台部门ID={}，运营平台部门名称={}，保存负责人的角色信息={}", deptResp.getId(), deptResp.getName(), JSONUtil.toJsonStr(userVsRoles));
                if (CollectionUtil.isNotEmpty(userVsRoles)) {
                    // 防止数据重复
                    List<UserVsRole> resultList = userVsRoles.stream()
                            .filter(FilterUtil.distinctByKeys(p -> Arrays.asList(p.getRoleId(), p.getUserId())))
                            .collect(Collectors.toList());
                    // 解决批量插入用户角色信息报错的问题
                    if (CollectionUtils.isNotEmpty(resultList)) {
                        resultList.forEach(item -> {
                            item.setIsMainRole(0);
                        });
                    }
                    log.info("定时任务同步部门信息，运营平台部门ID={}，运营平台部门名称={}，用户角色={}", deptResp.getId(), deptResp.getName(), JSON.toJSONString(resultList));
                    userVsRoleMapper.batchInsert(resultList);
                }
                redisUtil.del("dept_task");
                log.info("部门同步结束=====");
            }
        }
        log.info("同步部门信息定时任务正常执行结束");
    }

    private SysDeptDTO getSysDeptDTO(YunYingDeptResp deptResp, SysDeptVO updateDept) {
        updateDept.setDeptYunyingId(deptResp.getId());
        updateDept.setDeptName(deptResp.getName());
        updateDept.setDeptLeaderYunyingId(deptResp.getOrgUserId());
        updateDept.setDeptLeaderYunyingName(deptResp.getOrgUserLoginname());
        updateDept.setDeptCategory(deptResp.getOrgCategory());
        updateDept.setPid(deptResp.getPid());
        updateDept.setPid(deptResp.getPid());
        updateDept.setDeptLevel(deptResp.getLevels());
        SysDeptDTO updateDeptDTO = deptConvert.vo2Dto(updateDept);
        return updateDeptDTO;
    }

    /**
     * 根据部门层级赋值部门负责人的角色
     *
     * @param resp
     */
    UserVsRole infoDeptManagerForRole(YunYingDeptResp resp) {
        SysRoleDTO sysRoleDTO = new SysRoleDTO();
        //中心级别赋部门负责人中心总权限、分公司级别赋分公司经理权限、组级别赋部门经理权限
        switch (resp.getLevels()) {
            case 2:
                // 获取中心总权限
                sysRoleDTO.setRoleCode("11");
                break;
            case 3:
                // 组级部门
                sysRoleDTO.setRoleCode("13");
                break;
            case 4:
                sysRoleDTO.setRoleCode("12");
                break;
            default:
                break;
        }
        UserVsRole userVsRole = new UserVsRole();
        SysUserDTO userDTO = new SysUserDTO();
        userDTO.setUserYunyingId(resp.getOrgUserId());
        userDTO.setAccount(resp.getOrgUserLoginname());
        Result<SysUser> csmUser = sysUserService.getOne(userDTO);
        if (resp.getLevels() != 1 && ObjectUtil.isNotEmpty(csmUser.getData())) {
            Result<SysRoleVO> role = roleService.getRole(sysRoleDTO);
            userVsRole.setId(SnowFlakeUtil.getId());
            userVsRole.setRoleId(role.getData().getSysRoleId());
            userVsRole.setUserId(csmUser.getData().getSysUserId());
            userVsRole.setDefaultFlag(0);
            userVsRole.setIsDeleted(0);
            userVsRole.setCreaterId(-1L);
            userVsRole.setCreateTime(new Date());
            userVsRole.setUpdaterId(-1L);
            userVsRole.setUpdateTime(new Date());
            return userVsRole;
        } else {
            return null;
        }

    }

    /**
     * 查询客户服务中心下的二级、三级、四级  部门id
     *
     * @return
     */
    List<Long> saveRoleDeptIdList() {
        // 1030 为客户服务中心 。 二级部门
        // 三级
        SysDeptDTO sysDeptDTO2 = new SysDeptDTO();
        sysDeptDTO2.setPid(1030L);
        Result<List<SysDept>> listResult3 = sysDeptService.selectDeptList(sysDeptDTO2);
        List<Long> pidList3 =
                listResult3.getData().stream().map(vo -> vo.getDeptYunyingId()).collect(Collectors.toList());
        // 四级
        SysDeptDTO sysDeptDTO4 = new SysDeptDTO();
        sysDeptDTO4.setPidList(pidList3);
        Result<List<SysDept>> listResult4 = sysDeptService.selectDeptList(sysDeptDTO4);
        List<Long> pidList4 =
                listResult4.getData().stream().map(vo -> vo.getDeptYunyingId()).collect(Collectors.toList());
        // 组合成同一个id集合
        List<Long> result = Stream.of(pidList3, pidList4)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        result.add(1030L);
        return result;
    }

    /**
     * 定时同步产品信息
     */
    //    @Scheduled (cron = "20 * * * * * ")
    @Async()
    @Scheduled(cron = "0 20 1 * * * ")
    public void productsTask() {
        redisUtil.set("product_task", "product_task", 20, TimeUnit.MINUTES);
        YyResult result = yunyingFeignClient.productsTask();
        log.info("产品同步开始=====");
        if (result.isSuccess() && result.getObj() != null) {
            // 强转为产品表数据
            List<YunyingProductResp> productResps = JSONArray.parseArray(JSON.toJSONString(result.getObj()),
                    YunyingProductResp.class);
            log.info("产品信息解析，=== , {}", JSONUtil.toJsonStr(productResps));
            // 判断是否存在模块
            for (YunyingProductResp yunyingProductResp : productResps) {
                if (yunyingProductResp.getProductCategory() == 2 || yunyingProductResp.getProductCategory() == 3) {
                    // 硬件、耗材产品单独保存
                    saveHardWareProduct(yunyingProductResp);
                }
                // 只保留软件产品 【保留软件、外采、接口产品】
                if (yunyingProductResp.getProductCategory() != 1 && yunyingProductResp.getProductCategory() != 8
                        && yunyingProductResp.getProductCategory() != 4) {
                    continue;
                }
                if (CollectionUtil.isNotEmpty(yunyingProductResp.getModules())) {
                    // 存在时，向模块表中增加模块数据
                    for (YunYingProductModulesResp modulesResp : yunyingProductResp.getModules()) {
                        // 判断模块是否存在，当存在时 进行更新。不存在进行新增
                        List<DictProductVsModules> dictProductVsModules1 = dictProductVsModulesMapper.selectList(
                                new QueryWrapper<DictProductVsModules>()
                                        .eq("yy_product_id", yunyingProductResp.getId())
                                        .eq("yy_module_id", modulesResp.getId())
                                        .eq("is_deleted", 0)
                        );
                        if (CollectionUtil.isNotEmpty(dictProductVsModules1)) {
                            DictProductVsModules dictProductVsModules = new DictProductVsModules();
                            dictProductVsModules.setProductVsModuleId(
                                    dictProductVsModules1.get(0).getProductVsModuleId());
                            dictProductVsModules.setYyProductId(yunyingProductResp.getId());
                            dictProductVsModules.setYyModuleId(modulesResp.getId());
                            dictProductVsModules.setYyModuleName(modulesResp.getModuleName());
                            dictProductVsModulesMapper.updateById(dictProductVsModules);
                        } else {
                            DictProductVsModules dictProductVsModules = new DictProductVsModules();
                            dictProductVsModules.setProductVsModuleId(SnowFlakeUtil.getId());
                            dictProductVsModules.setYyProductId(yunyingProductResp.getId());
                            dictProductVsModules.setYyModuleId(modulesResp.getId());
                            dictProductVsModules.setYyModuleName(modulesResp.getModuleName());
                            dictProductVsModulesMapper.insert(dictProductVsModules);
                        }

                    }

                }
                //同步产品数据
                // 根据条件查询 当前产品是否已经存在。当存在时候进行更新，不存在 进行新增
                DictProduct product = dictProductMapper.selectOne(
                        new QueryWrapper<DictProduct>()
                                .eq("yy_product_id", yunyingProductResp.getId())
                                .eq("is_deleted", 0)
                );
                if (ObjectUtil.isNotEmpty(product)) {
                    //更新产品
                    DictProduct dictProduct = new DictProduct();
                    dictProduct.setProductDictId(product.getProductDictId());
                    dictProduct.setYyProductId(yunyingProductResp.getId());
                    dictProduct.setProductName(yunyingProductResp.getName());
                    dictProduct.setYyProductCode(yunyingProductResp.getCode());
                    dictProduct.setYyProductFullName(yunyingProductResp.getFullName());
                    dictProduct.setYyIsHaveModule(CollectionUtil.isNotEmpty(yunyingProductResp.getModules()) ? 1 : 0);
                    dictProduct.setProductLeaderYyId(yunyingProductResp.getProductManager());
                    dictProduct.setProductTeamId(yunyingProductResp.getOrgId());
                    dictProduct.setProductType(yunyingProductResp.getProductCategory().toString());
                    dictProduct.setUpdateTime(new Date());
                    dictProduct.setYyIsCloud(yunyingProductResp.getIsCloudProduct() == null ? 0
                            : yunyingProductResp.getIsCloudProduct());
                    dictProductMapper.updateById(dictProduct);
                } else {
                    // 新增产品
                    DictProduct dictProduct = new DictProduct();
                    dictProduct.setProductDictId(SnowFlakeUtil.getId());
                    dictProduct.setYyProductId(yunyingProductResp.getId());
                    dictProduct.setProductName(yunyingProductResp.getName());
                    dictProduct.setYyProductCode(ObjectUtil.isNotEmpty(yunyingProductResp.getCode())
                            ? yunyingProductResp.getCode() : "-1");
                    dictProduct.setYyProductFullName(yunyingProductResp.getFullName());
                    dictProduct.setYyIsHaveModule(CollectionUtil.isNotEmpty(yunyingProductResp.getModules()) ? 1 : 0);
                    dictProduct.setProductLeaderYyId(yunyingProductResp.getProductManager());
                    dictProduct.setProductTeamId(yunyingProductResp.getOrgId());
                    dictProduct.setProductType(yunyingProductResp.getProductCategory().toString());
                    dictProduct.setYyIsCloud(yunyingProductResp.getIsCloudProduct() == null ? 0
                            : yunyingProductResp.getIsCloudProduct());
                    dictProductMapper.insert(dictProduct);
                }
            }
        }
        redisUtil.del("product_task");
        log.info("产品同步结束=====");
    }

    /**
     * 保存硬件产品和耗材产品
     *
     * @param yunyingProductResp
     */
    void saveHardWareProduct(YunyingProductResp yunyingProductResp) {
        try {
            // 拼装数据
            DictHardwareProduct dictHardwareProduct = new DictHardwareProduct();
            dictHardwareProduct.setProductId(SnowFlakeUtil.getId());
            dictHardwareProduct.setProductCode(ObjectUtil.isNotEmpty(yunyingProductResp.getCode())
                    ? yunyingProductResp.getCode() : "-1");
            dictHardwareProduct.setProductName(yunyingProductResp.getName());
            dictHardwareProduct.setProductFullName(yunyingProductResp.getFullName());
            dictHardwareProduct.setProductCategory(yunyingProductResp.getProductCategory());
            dictHardwareProduct.setIsSelfIntegration(yunyingProductResp.getIsCloudProduct());
            dictHardwareProduct.setYyProductId(yunyingProductResp.getId());
            // 根据产品code判断产品是否已经存在  当存在时候进行更新  不存在进行新增
            DictHardwareProduct dictHardwareProduct1 = hardwareProductMapper.selectOne(
                    new QueryWrapper<DictHardwareProduct>()
                            .eq("yy_product_id", yunyingProductResp.getId())
            );
            // 调用接口
            if (ObjectUtil.isNotEmpty(dictHardwareProduct1)) {
                dictHardwareProduct.setProductId(dictHardwareProduct1.getProductId());
                hardwareProductMapper.updateById(dictHardwareProduct);
            } else {
                hardwareProductMapper.insert(dictHardwareProduct);
            }
        } catch (Exception e) {
            log.error("同步硬件产品失败=====yunyingProductResp={}，errMsg={}，stackInfo=",
                    JSON.toJSONString(yunyingProductResp), e.getMessage(), e);
        }


    }

    /**
     * 说明: 同步客户基础信息
     *
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/6/19 10:56
     * @remark: Copyright
     */
    /*@Scheduled(cron = "0 0 1 * * ?")*/
    @Async()
    @Scheduled(cron = "0 40 1 * * * ")
    public void customersTask() {
        redisUtil.set("custom_task", "custom_task", 20, TimeUnit.MINUTES);
        YyResult result = yunyingFeignClient.customersTask();
        //log.info("同步客户基础信息=====result={}", result);
        if (result.isSuccess() && result.getObj() != null) {
            // 强转为部门表数据
            List<YunYingCustomersResp> customersResps = JSONArray.parseArray(JSON.toJSONString(result.getObj()),
                    YunYingCustomersResp.class);
            Result result1 = projCustomInfoService.updateCustomInfoAndCustomInfoDetail(customersResps);
            log.info("接口返回结果result1={}", result1);
        }
        redisUtil.del("custom_task");
        log.info("同步客户基础信息=====");
    }

    /**
     * 说明: 同步行政区划信息
     *
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/6/19 10:56
     * @remark: Copyright
     */
    @Async()
    @Scheduled(cron = "0 30 4 ? * SAT")
    public void divisionProvinceTask() {
        redisUtil.set("division_province_task", "division_province_task", 20, TimeUnit.MINUTES);
        dictProvinceService.divisionProvinceTask();
        redisUtil.del("division_province_task");
        log.info("同步行政区划信息=====");
    }

    // 每分钟执行一次查询；定时查询数据库中 定时任务表数据。 判断表中的定时任务，是否需要执行。
    @Scheduled(cron = "1 1 * * * ?")
    public void selectTask() {
        Date now = new Date();
        log.info("定时任务开始执行=====");
        List<String> codeList = new ArrayList<>();
        List<Long> taskIdList = new ArrayList<>();
        List<ScheduleTask> scheduleTasks = scheduleTaskMapper.selectList(new QueryWrapper<ScheduleTask>()
                .eq("enable_flag", 1)
        );
        // 遍历定时任务数据，查询需要执行的定时任务
        for (ScheduleTask scheduleTask : scheduleTasks) {
            // 判断当前时间是否在定时任务时间段内 (当前时间 > 计划执行时间 && 计划时间 < 当前时间 +一小时)
            Date planRunTime = scheduleTask.getPlanRunTime();
            // 检查两个日期之间的差值是否小于1小时
            if (DateUtil.between(planRunTime, now, MINUTE) < 60) {
                codeList.add(scheduleTask.getTaskCode());
                taskIdList.add(scheduleTask.getScheduleTaskId());
            }

        }
        // 当list中存在code时 判断code类型进行执行定时任务
        if (CollectionUtil.isNotEmpty(codeList)) {
            for (String code : codeList) {
                // 获取数据的任务id集合
                switch (code) {
                    case "product_task":
                        productsTask();
                        break;
                    case "custom_task":
                        customersTask();
                        break;
                    case "user_task":
                        userTask();
                        break;
                    case "dept_task":
                        deptTask();
                        break;
                    case "division_province_task":
                        divisionProvinceTask();
                        break;
                    case "sync_env_info":
                        syncEnvInfoTask();
                        break;
                    case "cancel_auth":
                        scheduleCancelAuth();
                        break;
                    case "exam_recycle_task":
                        recycleExamHospital();
                        break;
                    default:
                        break;
                }
            }
            // 更新定时任务表的最后执行时间
            for (Long taskId : taskIdList) {
                ScheduleTask scheduleTask = new ScheduleTask();
                scheduleTask.setScheduleTaskId(taskId);
                scheduleTask.setLastRunTime(now);
                ScheduleTask scheduleTask1 = scheduleTaskMapper.selectById(taskId);
                // 计划执行时间天数加一
                scheduleTask1.getPlanRunTime().setDate(scheduleTask1.getPlanRunTime().getDate() + 1);
                scheduleTask.setPlanRunTime(scheduleTask1.getPlanRunTime());
                scheduleTaskMapper.updateById(scheduleTask);
                // 执行成功后增加日志信息
                ScheduleTaskLog scheduleTaskLog = new ScheduleTaskLog();
                scheduleTaskLog.setScheduleTaskLogId(SnowFlakeUtil.getId());
                scheduleTaskLog.setTaskName(scheduleTask1.getTaskName());
                scheduleTaskLog.setLogMessage(scheduleTask1.getPlanRunTime()
                        + "执行了定时任务【" + scheduleTask1.getTaskName() + "】,执行成功");
                scheduleTaskLogMapper.insert(scheduleTaskLog);
            }

        }
    }

    /**
     * 定时任务同步系统管理医院是否开启表单开启功能限制
     */
    @Scheduled(cron = "0 33 1 * * ?")
    public void syncCustomFormCtrlTask() {
        redisUtil.set("sync_form_ctrl_task", "sync_form_ctrl_task", 20, TimeUnit.MINUTES);
        List<ConfigCustomFormLimit> formLimitList = configCustomFormLimitMapper.selectNoDealList();
        log.info("查询需要处理的表单限制数据数量:{}", formLimitList.size());
        if (CollectionUtil.isEmpty(formLimitList)) {
            redisUtil.del("sync_form_ctrl_task");
            return;
        }
        projectToolUpdateService.syncCustomFormCtrlTask(formLimitList);
        redisUtil.del("sync_form_ctrl_task");
    }

    /**
     * 每天18点发送项目日报
     */
    @Scheduled(cron = "0 0 18 * * ?")
    public void sendProjectDailyMessageTask() {
        try {
            projectDailyInfoService.sendProjectDailyMessage();
        } catch (Exception e) {
            log.error("定时任务发送项目进度日报，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
        }
    }

    /**
     * 每天18点05发送项目风险提示
     */
    @Scheduled(cron = "0 5 18 * * ?")
    public void sendRiskProjectInfosMessageTask() {
        redisUtil.set("risk_project_info", "risk_project_info", 20, TimeUnit.MINUTES);
        try {
            log.info("定时任务发送风险项目提醒开始");
            projectDailyInfoService.sendRiskProjectInfosMessageTask();
            redisUtil.del("risk_project_info");
            log.info("定时任务发送风险项目提醒完成");
        } catch (Exception e) {
            log.error("定时任务发送风险项目提醒，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
            redisUtil.del("risk_project_info");
        }
    }

    /**
     * 每天2点33分执行,同步云健康授权统计数据
     */
    @Scheduled(cron = "0 33 2 * * ?")
    public void syncReportAuthorizationStatistics() {
        redisUtil.set("report_authorization_statistics", "report_authorization_statistics", 20, TimeUnit.MINUTES);
        try {
            reportAuthorizationStatisticsService.syncReportAuthorizationStatistics();
            redisUtil.del("report_authorization_statistics");
        } catch (Exception e) {
            redisUtil.del("report_authorization_statistics");
            log.error("同步云健康授权统计数据，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
        }
    }


    /**
     * 每天早上7点30分执行,查询缺失的项目数据
     */
    @Scheduled(cron = "0 30 7 * * ?")
    public void queryLoseProjectData() {
        redisUtil.set("query_lose_project_data", "query_lose_project_data", 20, TimeUnit.MINUTES);
        try {
            tmpProjectNewVsOldService.queryLoseProjectData();
            redisUtil.del("query_lose_project_data");
        } catch (Exception e) {
            redisUtil.del("query_lose_project_data");
            log.error("查询缺失的项目数据，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
        }
    }

    /**
     * 自动更新环境节点信息
     * <p>平台定时两点钟</p>
     * <p>平台定时两点钟</p>
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncEnvInfoTask() {
        syncEnvInfoTask(false);
    }

    /**
     * 更新环境节点信息
     * <p>具体实现</p>
     *
     * @param manul 是否手动. true:手动, false:自动
     */
    public void syncEnvInfoTask(Boolean manul) {
        // 是否手动触发.
        String manulDesc = ObjectUtil.isEmpty(manul) ? "自动" : (manul ? "手动" : "自动");
        String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
        sysOperLogService.apiOperLogInsertObjAry("同步环境节点信息-" + manulDesc + "-请求参数-时间:" + DateUtil.now(), uuid,
                Log.LogOperType.SEARCH.getCode(),
                StrUtil.EMPTY);
        List<SyncCustomerInfoVO> syncCustomerInfoVOS = CollUtil.newArrayList();
        try {
            syncCustomerInfoVOS =
                    yunweiFeignClient.syncEnvInfo(yunWeiPlatFormService.getAuthorization());
            sysOperLogService.apiOperLogInsertObjAry("同步环境节点信息-" + manulDesc + "-返回值", uuid,
                    Log.LogOperType.SEARCH.getCode(),
                    syncCustomerInfoVOS);
        } catch (Throwable e) {
            log.error("同步环境节点信息同步有异常. message: {}, e=", e.getMessage(), e);
            sysOperLogService.apiOperLogInsertObjAry("同步环境节点信息-" + manulDesc + "异常", uuid,
                    Log.LogOperType.SEARCH.getCode(),
                    e.getMessage());
        }
        if (CollUtil.isEmpty(syncCustomerInfoVOS)) {
            log.info("同步环境节点信息, 未获取到需要更新的医院.");
            return;
        }
        // 更新节点
        updateEnvInfo(syncCustomerInfoVOS);
    }

    /**
     * 更新节点信息
     *
     * @param syncCustomerInfoVOS 要更新的医院信息
     */
    public void updateEnvInfo(List<SyncCustomerInfoVO> syncCustomerInfoVOS) {
        int total = 0;
        for (SyncCustomerInfoVO vo : syncCustomerInfoVOS) {
            if (ObjectUtil.isEmpty(vo.getHospitalId()) || ObjectUtil.isEmpty(vo.getOrgId()) || ObjectUtil.isEmpty(vo.getEnvId()) || ObjectUtil.isEmpty(vo.getEnvName())) {
                log.warn("获取到的医院信息为空. vo: {}", vo);
                continue;
            }
            ProjHospitalInfo updateInfo = new ProjHospitalInfo();
            updateInfo.setEnvId(vo.getEnvId());
            updateInfo.setEnvName(vo.getEnvName());
            int count = hospitalInfoMapper.update(updateInfo, new QueryWrapper<ProjHospitalInfo>()
                    .eq("cloud_hospital_id", vo.getHospitalId())
                    .eq("org_id", vo.getOrgId()));
            total += count;
            log.info("更新医院. count: {}, hospitalId: {}, orgId: {}, envId: {}, envName: {}", count, vo.getHospitalId(),
                    vo.getOrgId(),
                    vo.getEnvId(), vo.getEnvName());
        }
        log.info("共查询到医院. count: {}, 更新数量为. count: {}", syncCustomerInfoVOS.size(), total);
    }

    /**
     * 定时取消授权
     */
//    @Scheduled(cron = "0 0 3 * * ?")
//    @Scheduled(cron = "0 0/1 * * * *")
    public void scheduleCancelAuth() {
        scheduleCancelAuth(false);
    }

    /**
     * 定时取消手动授权的产品或菜单, 自动更新
     * <p>具体实现</p>
     *
     * @param manul 是否手动. true:手动, false:自动
     */
    public void scheduleCancelAuth(Boolean manul) {
        String manulDesc = ObjectUtil.isEmpty(manul) ? "自动" : (manul ? "手动" : "自动");
        String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
        sysOperLogService.apiOperLogInsertObjAry("定时取消授权-" + manulDesc + "-请求参数-时间:" + DateUtil.now(), uuid,
                Log.LogOperType.ADD.getCode(),
                StrUtil.EMPTY);
        // 查询存在的产品和菜单授权记录, 找到存在时间期限的授权记录
        productEmpowerAddRecordService.cancelAuth();
    }

    /**
     * 每天22点调用运营平台接口同步验收申请时间和浮动工期
     */
    @Scheduled(cron = "0 0 22 * * ?")
    public void synchronizeDuration() {
        try {
            synchronizeDurationService.synchronizeDuration();
        } catch (Exception e) {
            log.error("定时任务同步运营平台工期信息，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
        }
    }

    @Async()
    @Scheduled(cron = "0 0 1 * * ?")
    public void recycleExamHospital() {
        log.info("定时任务开始执行回收学习环境医院数据定时任务");
        redisUtil.set("exam_recycle_task", "exam_recycle_task", 20, TimeUnit.MINUTES);
        try {
            LambdaQueryWrapper<SimulationHospital> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SimulationHospital::getStatus, 2);
            queryWrapper.eq(SimulationHospital::getIsDelete, 0);
            List<SimulationHospital> simulationHospitalList = simulationHospitalMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(simulationHospitalList)) {
                for (SimulationHospital simulationHospital : simulationHospitalList) {
                    try {
                        self.doRecycleExamHospital(simulationHospital, true);
                    } catch (Throwable e) {
                        log.error("定时任务回收学习环境报错：{}", JSON.toJSONString(simulationHospital), e);
                    }
                }
            } else {
                log.info("定时任务学习环境无可回收的医院数据");
            }
        } catch (Exception e) {
            log.error("定时任务回收学习环境数据，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
        } finally {
            redisUtil.del("exam_recycle_task");
        }
        log.info("定时任务回收学习环境医院数据定时任务执行结束");
    }


    @Transactional(rollbackFor = Exception.class)
    public void doRecycleExamHospital(SimulationHospital simulationHospital, boolean destroy) throws InterruptedException {
        try {
            SqlSelectLoginDTO sqlSelectLoginDTO = new SqlSelectLoginDTO();
            SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(simulationHospital.getExamHospitalInfoId());
            // 调用API时设置domain信息
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            // 组装调用API的参数
            sqlSelectLoginDTO.setHospitalInfoId(projHospitalInfo.getHospitalInfoId());
            sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
            sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
            sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
            //一次性执行所有的清数据脚本会超时，循环调用chis库的16个清除数据的函数脚本
            for (int i = 1; i < 17; i++) {
                String functionName = "public.delete_business_data" + i;
                sqlSelectLoginDTO.setSqlStr(AesUtil.encrypt("SELECT " + functionName + "(" + projHospitalInfo.getCloudHospitalId() + ")"));
                sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
                log.info("开始执行chis库第{}个函数", i);
                ResponseResult responseResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
                if (responseResult == null) {
                    log.info("执行chis库第{}个函数，接口超时，任务等待4分钟", i);
                    Thread.sleep(4 * 60 * 1000);
                } else if (!responseResult.isSuccess()) {
                    redisUtil.del("exam_recycle_task");
                    throw new RuntimeException("执行chis库第" + i + "个函数失败，失败信息：" + responseResult.getMessage());
                } else {
                    log.info("执行chis库第{}个函数成功", i);
                }
            }
            //一次性执行所有的清数据脚本会超时，循环调用chisapp库的9个清除数据的函数脚本
            for (int i = 1; i < 10; i++) {
                String functionName = "public.delete_business_data" + i;
                sqlSelectLoginDTO.setSqlStr(AesUtil.encrypt("SELECT " + functionName + "(" + projHospitalInfo.getCloudHospitalId() + ")"));
                sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
                log.info("开始执行chisapp库第{}个函数", i);
                ResponseResult responseResult = dataPreparationApi.executeChisappDb(sqlCheckApiDTO);
                if (responseResult == null) {
                    log.info("执行chisapp库第" + i + "个函数，接口超时，任务等待4分钟");
                    Thread.sleep(4 * 60 * 1000);
                } else if (!responseResult.isSuccess()) {
                    redisUtil.del("exam_recycle_task");
                    throw new RuntimeException("执行chisapp库第" + i + "个函数失败，失败信息：" + responseResult.getMessage());
                } else {
                    log.info("执行chisapp库第{}个函数成功", i);
                }
            }
            if (Boolean.TRUE.equals(destroy)) {
                //更新学习环境医院状态为已回收
                simulationHospital.setStatus(3);
                simulationHospitalMapper.updateById(simulationHospital);
                // 更新医院状态为已作废
                projHospitalInfoMapper.deleteById(projHospitalInfo);
                //删除老平台医院数据
                oldCustomerInfoMapper.deleteOldHospital(projHospitalInfo.getHospitalInfoId());
            } else {
                //select invalid_flag ,* from comm.hospital
                String sql = StrUtil.format("update comm.hospital set invalid_flag = 0 where hospital_id = {} and his_org_id = {}", projHospitalInfo.getCloudHospitalId(), projHospitalInfo.getOrgId());
                sqlSelectLoginDTO.setSqlStr(AesUtil.encrypt(sql));
                sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
                ResponseResult responseResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
                if (Boolean.FALSE.equals(responseResult.isSuccess())) {
                    log.error("启用学习环境医院失败 -> sql：{}\n结果：{}", sql, responseResult);
                } else {
                    log.info("启用学习环境医院成功 -> sql：{}\n结果：{}", sql, responseResult);
                }
            }
        } catch (Throwable e) {
            JSONObject obj = new JSONObject();
            obj.put("simulationHospital", simulationHospital);
            obj.put("destroy", destroy);
            log.error(StrUtil.format("回收学习环境失败：{}", obj.toJSONString()), e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 执行项目调研管控定时任务
     * 每天的每小时的第十分钟执行定时任务
     */
    @Async()
    @Scheduled(cron = "0 10 8-20 * * ?")
    public void projectSurveyControlTask() {
        log.info("开始执行项目调研管控定时任务");
        redisUtil.set("project_survey_controller_task", "project_survey_controller_task", 20, TimeUnit.MINUTES);
        try {
            boolean returnFlag = false;
            //查询处罚配置信息
            ProjSurveyFineConfig projSurveyFineConfig = projSurveyFineConfigMapper.selectOne(new QueryWrapper<>());
            //开启了项目调研监控才发送消息提醒
            if (ObjectUtil.isEmpty(projSurveyFineConfig) || projSurveyFineConfig.getOpenFlag() != 1) {
                log.info("未开启项目调研管控，项目调研管控定时任务执行结束");
                returnFlag = true;
            }
            Date now = new Date();
            //查询未完成调研里程碑节点的项目（调研阶段--调研完成）
            List<ProjProjectInfo> projectInfoList = projProjectInfoMapper.findSurveyNotComplete(projSurveyFineConfig.getBeginControlTime());
            if (CollectionUtil.isEmpty(projectInfoList)) {
                log.info("无可管控的项目调研，项目调研管控定时任务执行结束");
                returnFlag = true;
            }
            if (returnFlag) {
                redisUtil.del("project_survey_controller_task");
                return;
            }
            //项目调研超期天数
            Integer completeDay = projSurveyFineConfig.getCompleteDay();
            //项目调研预警天数
            Integer warningDay = projSurveyFineConfig.getWarningDay();
            // 项目调研重复处罚周期天数
            Integer repeatFineCycle = projSurveyFineConfig.getRepetFineCycle();
            //项目调研超期处罚金额
            double fine = Double.parseDouble(projSurveyFineConfig.getFineMoney());
            for (ProjProjectInfo projectInfo : projectInfoList) {
                String typeName;
                String messageType = "0";
                String title;
                String description;
                if (Integer.valueOf("1").equals(projectInfo.getUpgradationType())) {
                    typeName = "老换新";
                } else {
                    typeName = "新客户";
                }
                //查询项目调研管控已经推送的消息提醒记录
                List<ProjSurveyNotice> surveyNoticeList = projSurveyNoticeMapper.selectList(
                        new QueryWrapper<ProjSurveyNotice>()
                                .eq("project_info_id", projectInfo.getProjectInfoId())
                                .orderByDesc("create_time")
                );
                ProjSurveyNotice projSurveyNotice = new ProjSurveyNotice();
                projSurveyNotice.setProjectInfoId(projectInfo.getProjectInfoId());
                // 当前项目调研考核开始时间
                Date projectControlTime = projectInfo.getControlTime();
                // 当前项目调研考核超期时间
                Date projectOverdueDateTime = dictDateOfHolidaysService.getOverdueDateTime(projectControlTime, completeDay);
                //查询项目经理账号
                SysUser projLeaderUser = sysUserMapper.selectById(projectInfo.getProjectLeaderId());
                log.info("发送调研管控消息，项目ID={}，项目经理信息={}", projectInfo.getProjectInfoId(), JSON.toJSONString(projLeaderUser));
                //查询项目所属的分公司经理
                SysUser teamLeaderUser = null;
                ProjCustomInfo projCustomInfo = projCustomInfoService.selectByPrimaryKey(projectInfo.getCustomInfoId());
                if (projCustomInfo != null) {
                    // 实施地客户归属客服团队
                    SysDept sysDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>().eq("dept_yunying_id", projCustomInfo.getCustomTeamId()));
                    log.info("发送调研管控消息，项目ID={}，实施地客户归属客服团队={}", projectInfo.getProjectInfoId(), JSON.toJSONString(sysDept));
                    if (sysDept != null) {
                        teamLeaderUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("user_yunying_id", sysDept.getDeptLeaderYunyingId()));
                        log.info("发送调研管控消息，项目ID={}，实施地客户归属客服团队负责人={}", projectInfo.getProjectInfoId(), JSON.toJSONString(teamLeaderUser));
                    }
                }

                //查询项目的工单信息
                ProjOrderInfo orderInfo = projOrderInfoMapper.selectById(projectInfo.getOrderInfoId());

                // 项目派工已经超过7天
                if (dictDateOfHolidaysService.chaoQi(projectControlTime, completeDay, now)) {
                    log.info("超过管控的天数了，天数={}，项目ID={}", completeDay, projectInfo.getProjectInfoId());
                    //给项目经理推送消息提醒（每天推送一次）
                    // 没有当天的推送记录，推送项目经理的消息
                    if (CollectionUtil.isEmpty(surveyNoticeList)
                            || CollectionUtil.isEmpty(surveyNoticeList.stream().filter(
                                    surveyNotice -> surveyNotice.getNoticeType() == 1
                                            && DateFormatUtils.format(surveyNotice.getCreateTime(), "yyyy-MM-dd").equals(DateFormatUtils.format(now, "yyyy-MM-dd")))
                            .collect(Collectors.toList()))) {
                        log.info("当天没有推送过消息，进行推送，项目ID={}", projectInfo.getProjectInfoId());
                        if (projLeaderUser != null) {
                            String noticeContent = this.getName(projectInfo) + "（" + typeName + "）的调研已超" + completeDay + "天（"
                                    + DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss") + " 至 "
                                    + DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss")
                                    + "）的限期，请尽快完成，否则会对客服经理产生持续罚款！";
                            // TODO 判断是否重复处罚，如果是，文案增加本次考核周期
                            SendYunYingMessageParam messageParam = SendYunYingMessageParam.builder()
                                    .title("项目调研超期")
                                    .url("")
                                    .picurl("")
                                    .description(noticeContent)
                                    .toUser(projLeaderUser.getAccount())
                                    .build();
                            Boolean messageResult = yunYingService.sendYunYingMessageWithResult(messageParam);
                            //消息发送成功保存发送记录
                            if (messageResult) {
                                projSurveyNotice.setProjSurveyNoticeId(SnowFlakeUtil.getId());
                                projSurveyNotice.setNoticeType(1);
                                projSurveyNotice.setMessageType(Integer.valueOf(messageType));
                                projSurveyNotice.setNoticeAccount(projLeaderUser.getAccount());
                                projSurveyNotice.setNoticeContent(noticeContent);
                                projSurveyNoticeMapper.insert(projSurveyNotice);
                            }
                        }
                    }

                    //给客服经理推送罚单、预警单
                    String remarks = projSurveyFineConfig.getFineTemplate()
                            .replace("#{projectName}", this.getName(projectInfo))
                            .replace("#{projectType}", typeName)
                            .replace("#{completeDay}", projSurveyFineConfig.getCompleteDay().toString())
                            .replace("#{startTime}", DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss"))
                            .replace("#{endTime}", DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss"));
                    if (projSurveyFineConfig.getRepetFineFlag() == 1) {
                        remarks = remarks.replace("#{cycleRemark}", "，如不处理，后期会每隔" + repeatFineCycle + "天进行一次罚款，请知悉。");
                    } else {
                        remarks = remarks.replace("#{cycleRemark}", "。");
                    }
                    if (fine > 0) {
                        title = "处罚单";
                        messageType = "1";
                        description = this.getName(projectInfo) + "（" + typeName + "）的调研已超期，请于7天内上缴罚款，如有异议请在7天内提出申诉";
                    } else {
                        title = "预警单";
                        messageType = "2";
                        description = this.getName(projectInfo) + "（" + typeName + "）的调研已超期，请尽快完成";
                    }
                    Boolean fineResult = false;
                    if (teamLeaderUser != null) {
                        //推送罚单参数
                        SendYunYingFineParam fineParam = SendYunYingFineParam.builder()
                                .title(title)
                                .url("")
                                .picurl("")
                                .description(description)
                                .toUser(teamLeaderUser.getAccount())
                                .businessNo(orderInfo.getDeliveryOrderNo())
                                .businessId(String.valueOf(orderInfo.getOrderInfoId()))
                                .startedDate(DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss"))
                                .standardEndDate(DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss"))
                                .messageType(messageType)
                                .remarks(remarks)
                                .funValue("213")
                                .monitorPoint("10")
                                .source("1001")
                                .build();
                        if (CollectionUtil.isEmpty(surveyNoticeList)
                                || CollectionUtil.isEmpty(surveyNoticeList.stream().filter(surveyNotice -> surveyNotice.getNoticeType() == 4).collect(Collectors.toList()))) {
                            //调用运营平台接口给客服经理推送罚单或者预警单
                            fineResult = yunYingService.sendYunYingFineWithResult(fineParam);
                        } else if (projSurveyFineConfig.getRepetFineFlag() == 1 && fine > 0) {
                            //取出最新的一条罚单消息，判断是否已经超过了7天
                            ProjSurveyNotice surveyNotice = projSurveyNoticeMapper.selectList(new QueryWrapper<ProjSurveyNotice>()
                                    .eq("project_info_id", projectInfo.getProjectInfoId())
                                    .eq("notice_type", 4)
                                    .orderByDesc("create_time")).get(0);

                            if (dictDateOfHolidaysService.chaoQi(surveyNotice.getCreateTime(), repeatFineCycle - 3, now) && !dictDateOfHolidaysService.chaoQi(surveyNotice.getCreateTime(), repeatFineCycle, now)) {
                                boolean sendNoticeFlag = false;
                                description = projSurveyFineConfig.getRepeatWarningTemplate()
                                        .replace("#{projectName}", this.getName(projectInfo))
                                        .replace("#{projectType}", typeName)
                                        .replace("#{completeDay}", String.valueOf(repeatFineCycle))
                                        .replace("#{startTime}", DateFormatUtils.format(surveyNotice.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                                        .replace("#{endTime}", DateFormatUtils.format(dictDateOfHolidaysService.getOverdueDateTime(surveyNotice.getCreateTime(), repeatFineCycle), "yyyy-MM-dd HH:mm:ss"));
                                if (dictDateOfHolidaysService.chaoQi(surveyNotice.getCreateTime(), repeatFineCycle - 1, now)) {
                                    description = description.replace("#{day}", "1");
                                    messageType = "3";
                                    //倒计时1天，给客服经理推送消息提醒（只推送一次）
                                    if (this.canSendDaoJiShi(surveyNoticeList, now, 1)) {
                                        sendNoticeFlag = true;
                                    }
                                } else {
                                    description = description.replace("#{day}", "3");
                                    messageType = "2";
                                    //倒计时3天，给客服经理推送消息提醒（只推送一次）
                                    if (this.canSendDaoJiShi(surveyNoticeList, now, 2)) {
                                        sendNoticeFlag = true;
                                    }
                                }
                                if (sendNoticeFlag) {
                                    //推送预警单参数
                                    SendYunYingFineParam fineParam2 = SendYunYingFineParam.builder()
                                            .title("预警单")
                                            .url("")
                                            .picurl("")
                                            .description(description)
                                            .toUser(teamLeaderUser.getAccount())
                                            .businessNo(orderInfo.getDeliveryOrderNo())
                                            .businessId(String.valueOf(orderInfo.getOrderInfoId()))
                                            .startedDate(DateFormatUtils.format(surveyNotice.getCreateTime(), "yyyy-MM-dd HH:mm:ss"))
                                            .standardEndDate(DateFormatUtils.format(dictDateOfHolidaysService.getOverdueDateTime(surveyNotice.getCreateTime(), repeatFineCycle), "yyyy-MM-dd HH:mm:ss"))
                                            .messageType("2")
                                            .remarks(description)
                                            .funValue("213")
                                            .monitorPoint("10")
                                            .source("1001")
                                            .build();
                                    Boolean messageResult = yunYingService.sendYunYingFineWithResult(fineParam2);
                                    //消息发送成功保存发送记录
                                    if (messageResult) {
                                        projSurveyNotice.setProjSurveyNoticeId(SnowFlakeUtil.getId());
                                        projSurveyNotice.setNoticeType(Integer.valueOf(messageType));
                                        projSurveyNotice.setMessageType(2);
                                        projSurveyNotice.setNoticeAccount(teamLeaderUser.getAccount());
                                        projSurveyNotice.setNoticeContent(description);
                                        projSurveyNoticeMapper.insert(projSurveyNotice);
                                    }
                                }
                            }

                            if (dictDateOfHolidaysService.chaoQi(surveyNotice.getCreateTime(), repeatFineCycle, now)) {
                                //调用运营平台接口给客服经理推送罚单或者预警单
                                fineResult = yunYingService.sendYunYingFineWithResult(fineParam);
                            }
                        }

                    }
                    //消息发送成功保存发送记录
                    if (fineResult) {
                        projSurveyNotice.setProjSurveyNoticeId(SnowFlakeUtil.getId());
                        projSurveyNotice.setNoticeType(4);
                        projSurveyNotice.setMessageType(Integer.valueOf(messageType));
                        projSurveyNotice.setNoticeAccount(teamLeaderUser.getAccount());
                        projSurveyNotice.setNoticeContent(remarks);
                        projSurveyNoticeMapper.insert(projSurveyNotice);
                    }
                } else if (dictDateOfHolidaysService.chaoQi(projectControlTime, 3, now)) {
                    //项目派工3天后，给项目经理推送消息提醒（每天推送一次）
                    if (CollectionUtil.isEmpty(surveyNoticeList)
                            || CollectionUtil.isEmpty(surveyNoticeList.stream().filter(
                                    surveyNotice -> surveyNotice.getNoticeType() == 1
                                            && DateFormatUtils.format(surveyNotice.getCreateTime(), "yyyy-MM-dd").equals(DateFormatUtils.format(now, "yyyy-MM-dd")))
                            .collect(Collectors.toList()))) {
                        description = projSurveyFineConfig.getProjManagerTemplate().replace("#{projectName}", this.getName(projectInfo))
                                .replace("#{projectType}", typeName)
                                .replace("#{startTime}", DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss"));
                        if (projectInfo.getHisFlag() == 1) {
                            description = description
                                    .replace("#{completeDay}", projSurveyFineConfig.getWarningDay().toString())
                                    .replace("#{endTime}", DateFormatUtils.format(DateUtil.offsetDay(projectControlTime, warningDay), "yyyy-MM-dd HH:mm:ss"))
                                    .replace("#{firstRemark}", "并于" + completeDay + "天内（"
                                            + DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss") + " 至 "
                                            + DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss")
                                            + "）完成通过调研审核，");
                        } else {
                            description = description.replace("#{firstRemark}", "")
                                    .replace("#{completeDay}", projSurveyFineConfig.getCompleteDay().toString())
                                    .replace("#{endTime}", DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss"));
                        }
                        if (projLeaderUser != null) {
                            SendYunYingMessageParam messageParam = SendYunYingMessageParam.builder()
                                    .title("项目调研即将超期")
                                    .url("")
                                    .picurl("")
                                    .description(description)
                                    .toUser(projLeaderUser.getAccount())
                                    .build();
                            Boolean messageResult = yunYingService.sendYunYingMessageWithResult(messageParam);
                            //消息发送成功保存发送记录
                            if (messageResult) {
                                projSurveyNotice.setProjSurveyNoticeId(SnowFlakeUtil.getId());
                                projSurveyNotice.setNoticeType(1);
                                projSurveyNotice.setMessageType(0);
                                projSurveyNotice.setNoticeAccount(projLeaderUser.getAccount());
                                projSurveyNotice.setNoticeContent(description);
                                projSurveyNoticeMapper.insert(projSurveyNotice);
                            }
                        }

                    }
                }
                //项目派工后，倒计时3天提醒、倒计时1天都对客服经理进行提醒
                if (dictDateOfHolidaysService.chaoQi(projectControlTime, completeDay - 3, now) && !dictDateOfHolidaysService.chaoQi(projectControlTime, completeDay, now)) {
                    title = "预警单";
                    boolean sendNoticeFlag = false;
                    description = projSurveyFineConfig.getWarningTemplate().replace("#{projectName}", this.getName(projectInfo))
                            .replace("#{projectType}", typeName)
                            .replace("#{completeDay}", projSurveyFineConfig.getCompleteDay().toString())
                            .replace("#{startTime}", DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss"))
                            .replace("#{endTime}", DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss"));
                    if (dictDateOfHolidaysService.chaoQi(projectControlTime, completeDay - 1, now)) {
                        description = description.replace("#{day}", "1");
                        messageType = "3";
                        //倒计时1天，给客服经理推送消息提醒（只推送一次）
                        if (CollectionUtil.isEmpty(surveyNoticeList) || CollectionUtil.isEmpty(surveyNoticeList.stream().filter(
                                surveyNotice -> surveyNotice.getNoticeType() == 3).collect(Collectors.toList()))) {
                            sendNoticeFlag = true;
                        }
                    } else {
                        description = description.replace("#{day}", "3");
                        messageType = "2";
                        //倒计时3天，给客服经理推送消息提醒（只推送一次）
                        if (CollectionUtil.isEmpty(surveyNoticeList) || CollectionUtil.isEmpty(surveyNoticeList.stream().filter(
                                surveyNotice -> surveyNotice.getNoticeType() == 2).collect(Collectors.toList()))) {
                            sendNoticeFlag = true;
                        }
                    }
                    if (sendNoticeFlag) {
                        if (teamLeaderUser != null) {
                            //推送预警单参数
                            SendYunYingFineParam fineParam = SendYunYingFineParam.builder()
                                    .title(title)
                                    .url("")
                                    .picurl("")
                                    .description(description)
                                    .toUser(teamLeaderUser.getAccount())
                                    .businessNo(orderInfo.getDeliveryOrderNo())
                                    .businessId(String.valueOf(orderInfo.getOrderInfoId()))
                                    .startedDate(DateFormatUtils.format(projectControlTime, "yyyy-MM-dd HH:mm:ss"))
                                    .standardEndDate(DateFormatUtils.format(projectOverdueDateTime, "yyyy-MM-dd HH:mm:ss"))
                                    .messageType("2")
                                    .remarks(description)
                                    .funValue("213")
                                    .monitorPoint("10")
                                    .source("1001")
                                    .build();
                            Boolean messageResult = yunYingService.sendYunYingFineWithResult(fineParam);
                            //消息发送成功保存发送记录
                            if (messageResult) {
                                projSurveyNotice.setProjSurveyNoticeId(SnowFlakeUtil.getId());
                                projSurveyNotice.setNoticeType(Integer.valueOf(messageType));
                                projSurveyNotice.setMessageType(2);
                                projSurveyNotice.setNoticeAccount(teamLeaderUser.getAccount());
                                projSurveyNotice.setNoticeContent(description);
                                projSurveyNoticeMapper.insert(projSurveyNotice);
                            }
                        }

                    }
                }
            }
            redisUtil.del("project_survey_controller_task");
        } catch (Exception e) {
            redisUtil.del("project_survey_controller_task");
            log.error("执行项目调研管控定时任务，发生异常，errMsg={}，stackInfo={}", e.getMessage(), e);
            throw new RuntimeException("执行项目调研管控定时任务，发生异常，errMsg=" + e.getMessage());
        }
        log.info("执行项目调研管控定时任务结束");
    }


    private String getName(ProjProjectInfo projectInfo) {
        ProjCustomInfo projCustomInfo = null;
        try {
            projCustomInfo = projCustomInfoService.selectByPrimaryKey(projectInfo.getCustomInfoId());
        } catch (Exception e) {
            log.error("发送罚单和预警单消息时获取客户名称，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (projCustomInfo == null) {
            String substring = projectInfo.getProjectName().substring(0, projectInfo.getProjectName().indexOf("-"));
            return substring + "-" + projectInfo.getProjectNumber();
        }
        return projCustomInfo.getCustomName() + "-" + projectInfo.getProjectNumber();
    }

    /**
     * 执行审核预警提醒
     */
    @Async()
    @Scheduled(cron = "0/50 * 6-23 * * ?")
    public void voidTimeSendEarlywarMsg() {
        try {
            projApplyOrderService.voidTimeSendEarlywarMsg();
        } catch (Exception e) {
            log.error("执行出错" + e);
        }
    }

    /**
     * 执行审核后部署预警
     */
    @Async()
    @Scheduled(cron = "0/50 * 6-23 * * ?")
    public void voidTimeSendEarlywarToYunweiMsg() {
        try {
            projApplyOrderService.voidTimeSendEarlywarToYunweiMsg();
        } catch (Exception e) {
            log.error("执行出错" + e);
        }
    }

    /**
     * 是否发送倒计时1天的预警单消息
     *
     * @param surveyNoticeList 预警单/罚单消息记录
     * @param now              当前日期
     * @param type             1-倒计时1天提醒；2-倒计时3天提醒
     * @return true-可以发送；false-不可发送
     */
    private boolean canSendDaoJiShi(List<ProjSurveyNotice> surveyNoticeList, Date now, Integer type) {
        if (CollectionUtil.isEmpty(surveyNoticeList)) {
            return true;
        }
        List<ProjSurveyNotice> yiTianTiXing;
        if (Integer.valueOf(1).equals(type)) {
            yiTianTiXing = surveyNoticeList.stream().filter(surveyNotice -> Integer.valueOf(3).equals(surveyNotice.getNoticeType())).collect(Collectors.toList());
        } else {
            yiTianTiXing = surveyNoticeList.stream().filter(surveyNotice -> Integer.valueOf(2).equals(surveyNotice.getNoticeType())).collect(Collectors.toList());
        }

        if (CollectionUtil.isEmpty(yiTianTiXing)) {
            return true;
        }
        ProjSurveyNotice projSurveyNotice = yiTianTiXing.stream().max(Comparator.comparing(ProjSurveyNotice::getCreateTime)).orElse(null);
        if (projSurveyNotice == null) {
            return true;
        }
        boolean sameDay = DateUtil.isSameDay(projSurveyNotice.getCreateTime(), now);
        return !sameDay;
    }


    @Resource
    private ProjectDailyRecordService projectDailyRecordService;

    /**
     * 执行项目日报提交提醒定时任务
     * 每天的每小时的整点执行
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void sendProjectDailyReportMessage() {
        log.info("执行项目日报提交提醒定时任务");
        projectDailyRecordService.sendProjectDailyReportMessage();
    }

    /**
     * 云护理表单资源库匹配定时任务
     * 每50s运行一次
     * 因推荐匹配时间较长，需提前运行匹配
     */
    @Scheduled(cron = "0/50 * * * * ?")
    public void regularlyConvertData() {
        log.info("定时转换数据,将资源库grf转为text");
        libProductFormService.regularlyConvertData();
    }

    /**
     * 定时根据任务, 预处理打印报表资源库推荐
     */
    @Scheduled(cron = "0/50 * * * * ?")
    public void regularlyPrintReportConvertData() {
        log.info("定时根据任务, 预处理打印报表资源库推荐");
        libProductFormService.regularlyPrintReportConvertData();
    }

    /**
     * 每分钟执行一次发送预警单或者罚单
     */
    @Scheduled(cron = "0 * * * * ?")
    public void sendEarlyWarningAndPenaltyMessages() {
        Date now = new Date();
        List<EarlyWarningAndPenaltyMessages> warnings = sendBusinessMessageService.cronFindNotSendEarlyWarningOrPenaltyMessages("warning", now);
        for (EarlyWarningAndPenaltyMessages warning : warnings) {
            //TODO 发送预警消息
            SysUser user = sysUserMapper.getUserById(warning.getPenaltyPersonId());
            SendYunYingFineParam fineParam = SendYunYingFineParam.builder()
                    .title(StrUtil.format("{}({})-{}预警单", warning.getProjectName(), warning.getProjectNumber(), warning.getBusinessName()))
                    .url("")
                    .picurl("")
                    .description(warning.getPenaltyReason())
                    .toUser(user.getAccount())
                    .businessNo(StrUtil.format("{}#{}", warning.getProjectName(), warning.getProjectNumber()))
                    .businessId(String.valueOf(warning.getBusinessId()))
                    .startedDate(DateFormatUtils.format(warning.getPenaltyStartTime(), "yyyy-MM-dd HH:mm:ss"))
                    .standardEndDate(DateFormatUtils.format(warning.getPenaltyEndTime(), "yyyy-MM-dd HH:mm:ss"))
                    .messageType("2")
                    .remarks(warning.getRemark())
                    .funValue(warning.getYyFunValue())
                    .monitorPoint(warning.getYyMonitorPoint())
                    .source("1001")
                    .build();
            if (Boolean.FALSE.equals(yunYingService.sendYunYingFineWithResult(fineParam))) {
                log.error("发送预警单失败，预警单ID={}, 业务ID={}, 业务表={}, 罚款金额={}",
                        warning.getEarlyWarningAndPenaltyMessagesId(), warning.getBusinessId(), warning.getBusinessTable(), warning.getPenaltyAmount());
            }
        }
        //将预警单状态改为已发送
        sendBusinessMessageService.changeEarlyWarningAndPenaltyMessagesSendFlag(warnings.stream().map(EarlyWarningAndPenaltyMessages::getEarlyWarningAndPenaltyMessagesId).collect(Collectors.toList()), true);
        List<EarlyWarningAndPenaltyMessages> penaltys = sendBusinessMessageService.cronFindNotSendEarlyWarningOrPenaltyMessages("penalty", now);
        for (EarlyWarningAndPenaltyMessages penalty : penaltys) {
            //先确认该罚单有没有发送预警单，如果没发送那么就需要发送
            List<EarlyWarningAndPenaltyMessages> earlyWarningAndPenaltyMessages = sendBusinessMessageService.cronFindNotSendEarlyWarningMessagesByPenaltyId(penalty.getEarlyWarningAndPenaltyMessagesId());
            if (CollUtil.isNotEmpty(earlyWarningAndPenaltyMessages)) {
                for (EarlyWarningAndPenaltyMessages warningMsg : earlyWarningAndPenaltyMessages) {
                    //TODO 发送罚单下的预警消息
                    SysUser user = sysUserMapper.getUserById(warningMsg.getPenaltyPersonId());
                    SendYunYingFineParam fineParam = SendYunYingFineParam.builder()
                            .title(StrUtil.format("{}({})-{}预警单", warningMsg.getProjectName(), warningMsg.getProjectNumber(), warningMsg.getBusinessName()))
                            .url("")
                            .picurl("")
                            .description(warningMsg.getPenaltyReason())
                            .toUser(user.getAccount())
                            .businessNo(StrUtil.format("{}#{}", warningMsg.getProjectName(), warningMsg.getProjectNumber()))
                            .businessId(String.valueOf(warningMsg.getBusinessId()))
                            .startedDate(DateFormatUtils.format(warningMsg.getPenaltyStartTime(), "yyyy-MM-dd HH:mm:ss"))
                            .standardEndDate(DateFormatUtils.format(warningMsg.getPenaltyEndTime(), "yyyy-MM-dd HH:mm:ss"))
                            .messageType("2")
                            .remarks(warningMsg.getRemark())
                            .funValue(warningMsg.getYyFunValue())
                            .monitorPoint(warningMsg.getYyMonitorPoint())
                            .source("1001")
                            .build();
                    if (Boolean.FALSE.equals(yunYingService.sendYunYingFineWithResult(fineParam))) {
                        log.error("发送预警单失败，预警单ID={}, 业务ID={}, 业务表={}, 罚款金额={}",
                                warningMsg.getEarlyWarningAndPenaltyMessagesId(), warningMsg.getBusinessId(), warningMsg.getBusinessTable(), warningMsg.getPenaltyAmount());
                    }
                }
                //将预警单状态改为已发送
                sendBusinessMessageService.changeEarlyWarningAndPenaltyMessagesSendFlag(earlyWarningAndPenaltyMessages.stream().map(EarlyWarningAndPenaltyMessages::getEarlyWarningAndPenaltyMessagesId).collect(Collectors.toList()), true);
            }
            //TODO 再发送罚单
            SysUser user = sysUserMapper.getUserById(penalty.getPenaltyPersonId());
            SendYunYingFineParam fineParam = SendYunYingFineParam.builder()
                    .title(StrUtil.format("{}({})-{}罚单", penalty.getProjectName(), penalty.getProjectNumber(), penalty.getBusinessName()))
                    .url("")
                    .picurl("")
                    .description(penalty.getPenaltyReason())
                    .toUser(user.getAccount())
                    .businessNo(StrUtil.format("{}#{}", penalty.getProjectName(), penalty.getProjectNumber()))
                    .businessId(String.valueOf(penalty.getBusinessId()))
                    .startedDate(DateFormatUtils.format(penalty.getPenaltyStartTime(), "yyyy-MM-dd HH:mm:ss"))
                    .standardEndDate(DateFormatUtils.format(penalty.getPenaltyEndTime(), "yyyy-MM-dd HH:mm:ss"))
                    .messageType("1")
                    .remarks(penalty.getRemark())
                    .funValue(penalty.getYyFunValue())
                    .monitorPoint(penalty.getYyMonitorPoint())
                    .source("1001")
                    .build();
            if (Boolean.FALSE.equals(yunYingService.sendYunYingFineWithResult(fineParam))) {
                log.error("发送罚单失败，预警单ID={}, 业务ID={}, 业务表={}, 罚款金额={}",
                        penalty.getEarlyWarningAndPenaltyMessagesId(), penalty.getBusinessId(), penalty.getBusinessTable(), penalty.getPenaltyAmount());
            }

            if (penalty.getPenaltyType() == 2) {
                //再次生成罚单跟预警单
                GenerateEarlyWarningAndPenaltyMessageArgs args = new GenerateEarlyWarningAndPenaltyMessageArgs();
                args.setReviewTypeCode(penalty.getReviewTypeCode());
                args.setProjectInfoId(penalty.getProjectInfoId());
                args.setBusinessId(penalty.getBusinessId());
                args.setBusinessTable(penalty.getBusinessTable());
                args.setBusinessName(penalty.getBusinessName());
                args.setPenaltyPersonId(penalty.getPenaltyPersonId());
                args.setPenaltyReason(penalty.getPenaltyReason());
                args.setPenaltyStartTime(now);
                args.setEntryAtCron(true);
                sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage(args);
            }
        }
        //将罚单消息改为已发送
        sendBusinessMessageService.changeEarlyWarningAndPenaltyMessagesSendFlag(penaltys.stream().map(EarlyWarningAndPenaltyMessages::getEarlyWarningAndPenaltyMessagesId).collect(Collectors.toList()), true);
    }

//    public void migrationDocFileToObs() {
//
//    }

    /**
     * 定时清理很久以前的日志文件
     */
//    @Scheduled(cron = "0 * * * * ?")
    @Scheduled(cron = "0 0 * * * ?")
    public void clearLonglongAgoLogFiles() {
        doClearLonglongAgoLogFiles();
    }

    public void doClearLonglongAgoLogFiles() {
        /**
         * 6天前的日志文件可以删除了
         */
        try {
            FileUtil.recClearLonglongAgoLogFiles(logPath, LocalDateTime.now().minus(4, ChronoUnit.DAYS));
            log.info("已成功删除六天前的日志：{}", logPath);
        } catch (Throwable e) {
            log.error("删除六天前的日志报错：", e);
        }

        /**
         * 一年前的基础数据结果文件可以删除了
         */
        try {
            FileUtil.recClearLonglongAgoLogFiles(dataManagerPath, LocalDateTime.now().minus(365, ChronoUnit.DAYS), "^\\d+$");
            log.info("已成功删除一年前的基础数据结果文件：{}", dataManagerPath);
        } catch (Throwable e) {
            log.error("删除一年前的基础数据结果文件失败：", e);
        }


        /**
         * 一年前的基础数据上传文件可以删除了
         */
        try {
            FileUtil.recClearLonglongAgoLogFiles(basedataExcelPath, LocalDateTime.now().minus(365, ChronoUnit.DAYS));
            log.info("已成功删除一年前的基础数据上传文件：{}", basedataExcelPath);
        } catch (Throwable e) {
            log.error("删除一年前的基础数据上传文件失败：", e);
        }


        /**
         * 一年前的CHIS_DATA文件可以删除了
         */
        try {
            FileUtil.recClearLonglongAgoLogFiles(chisData, LocalDateTime.now().minus(365, ChronoUnit.DAYS));
            log.info("已成功删除一年前的CHIS_DATA数据上传文件：{}", chisData);
        } catch (Throwable e) {
            log.error("删除一年前的CHIS_DATA数据上传文件失败：", e);
        }


        /**
         * TODO只需要保留最新的一个版本，老版本的删除,这个先不做处理
         */
//        try {
//            List<NewestProductDocDao> newestProductVersions = docMapper.getNewestProductVersions();
//            List<Long> docIds = new ArrayList<>();
//            List<Long> docFileIds = new ArrayList<>();
//            for (NewestProductDocDao newestProductVersion : newestProductVersions) {
//                List<ProductDocDao> allOldProductDoc = docMapper.getAllOldProductDoc(newestProductVersion.getProductName(), newestProductVersion.getDocVersion());
//                for (ProductDocDao productDocDao : allOldProductDoc) {
//                    docIds.add(productDocDao.getId());
//                    docFileIds.add(productDocDao.getDocId());
//                    String docPath = productDocDao.getDocPath();
//                    try {
//                        File file = new File(docPath);
//                        file.deleteOnExit();
//                    } catch (Throwable e) {
//                        log.error("删除历史操作文档【{}】失败：", docPath, e);
//                    }
//                }
//            }
//            if (CollUtil.isNotEmpty(docIds)) {
//                docMapper.deleteDoc(docIds);
//            }
//            if (CollUtil.isNotEmpty(docFileIds)) {
//                docMapper.deleteDocFile(docFileIds);
//            }
//            log.info("已成功删除老版本的操作手册");
//        } catch (Throwable e) {
//            log.error("删除老版本的操作文档失败：", e);
//        }
//        List<ProductDocDao> allOldProductDoc = docMapper.getAllOldProductDoc(null, null);
//        Set<String> filePaths = allOldProductDoc.stream().map(ProductDocDao::getDocPath).collect(Collectors.toSet());
//        FileUtil.recDeleteFileConfirm(docPath, f -> Boolean.FALSE.equals(filePaths.contains(f.getAbsolutePath())));
    }
}
