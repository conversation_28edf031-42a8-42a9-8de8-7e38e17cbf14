package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/5
 */

/**
 * 交付工单产品信息
 */
@ApiModel(description = "交付工单产品信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjOrderProduct extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 工单产品信息ID
     */
    @ApiModelProperty(value = "工单产品信息ID")
    @TableId(type = IdType.INPUT)
    private Long orderProductId;

    /**
     * 项目信息ID
     */
    @ApiModelProperty(value = "项目信息ID")
    private Long projectInfoId;

    /**
     * 交付工单ID
     */
    @ApiModelProperty(value = "交付工单ID")
    private Long orderInfoId;

    /**
     * 产品购买模式 0特批
     */
    @ApiModelProperty(value = "产品购买模式")
    private Integer productBuyMode;

    /**
     * 产品结算状态
     */
    @ApiModelProperty(value = "产品结算状态")
    private String productSettleStatus;

    /**
     * 产品结算比例
     */
    @ApiModelProperty(value = "产品结算比例")
    private BigDecimal productSettleProp;

    /**
     * 产品应结算金额
     */
    @ApiModelProperty(value = "产品应结算金额")
    private BigDecimal productSettleAmount;

    /**
     * 运营平台工单产品字典ID
     */
    @ApiModelProperty(value = "运营平台工单产品字典ID")
    private Long yyOrderProductId;

    /**
     * 产品订阅期限
     */
    @ApiModelProperty(value = "产品订阅期限")
    private Long productSubscribeTerm;

    /**
     * 产品订阅开始日期
     */
    @ApiModelProperty(value = "产品订阅开始日期")
    private Date productSubscribeTime;

    /**
     * 产品订阅状态
     */
    @ApiModelProperty(value = "产品订阅状态")
    private Integer productSubscribeStatus;

    /**
     * 产品订阅类型
     */
    @ApiModelProperty(value = "产品订阅类型")
    private Integer productSubscribeType;

    /**
     * 产品实施状态：0.未上线；1.已上线；2.验收未通过；3.验收通过
     */
    @ApiModelProperty(value = "产品实施状态：0.未上线；1.已上线；2.验收未通过；3.验收通过")
    private Integer productExcutionStatus;

    /**
     * 产品开通状态  0未开通    11处理中    21已开通
     */
    @ApiModelProperty(value = "产品开通状态  0未开通    11处理中    21已开通")
    private Integer productOpenStatus;

    /**
     * 产品解决方案类型ID：1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    @ApiModelProperty(
            value = "产品解决方案类型ID：1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务")
    private Long productResolveTypeId;
    /**
     * 部署状态：0未申请  1已部署  2  处理中
     */
    @ApiModelProperty(value = "部署状态：0未申请  1已部署  2  处理中")
    private Integer arrangeStatus;

    /**
     * 授权状态
     */
    @ApiModelProperty(value = "授权状态")
    private Integer empowerStatus;

    /**
     * 运营平台特殊产品ID 全系统唯一
     */
    @ApiModelProperty(value = "运营平台特殊产品ID 全系统唯一")
    private Integer yyProjId;

    /**
     * 运营平台工单产品数量
     */
    @ApiModelProperty(value = "运营平台工单产品数量")
    private Integer yyOrderProductNumber;

    /**
     * 运营平台合同ID
     */
    @ApiModelProperty(value = "运营平台合同ID")
    private Long yyContractId;

    @ApiModelProperty(value = "是否特批产品")
    private Boolean isSpecial = false;
    @ApiModelProperty(value = "特批ID")
    private Long specialId = 0L;
}
