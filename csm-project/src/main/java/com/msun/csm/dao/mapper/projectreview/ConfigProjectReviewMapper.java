package com.msun.csm.dao.mapper.projectreview;

import java.util.List;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewResp;

/**
 * <AUTHOR>
 * @description 针对表【config_project_review(项目审核模式配置表)】的数据库操作Mapper
 * @createDate 2025-06-18 08:30:31
 * @Entity jiaofuceshi.domain.ConfigProjectReview
 */
public interface ConfigProjectReviewMapper extends RootMapper<ConfigProjectReview> {

    /**
     * 分页查询项目审核模式配置
     * @param dto
     * @return
     */
    List<ConfigProjectReviewResp> findDataPage(ConfigProjectReviewReq dto);

    /**
     *  重复校验
     * @param dto
     * @return
     */
    List<ConfigProjectReview> selectListByParamer(ConfigProjectReviewReq dto);

    /**
     * 修改
     * @param configProjectReview
     */
    void updateByParamer(ConfigProjectReview configProjectReview);

    /**
     * 根据id查询
     * @param projectReviewId
     * @return
     */
    ConfigProjectReview selectByIdHand(@Param("projectReviewId") Long projectReviewId);
}




