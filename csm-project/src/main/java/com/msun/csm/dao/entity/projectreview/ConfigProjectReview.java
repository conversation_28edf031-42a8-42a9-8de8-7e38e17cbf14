package com.msun.csm.dao.entity.projectreview;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核模式配置表
 *
 * <AUTHOR>
 * @TableName config_project_review
 */
@TableName(value = "config_project_review", schema = "csm")
@Data
public class ConfigProjectReview extends BasePO {
    /**
     * 主键
     */
    @TableId
    private Long projectReviewId;

    /**
     * 审核类型id 取字典表 dict_project_review_type
     */
    private Long reviewTypeId;

    /**
     * 客户类型 -1 通用 1单体 2区域
     */
    private Integer customType;

    /**
     * 电销属性 -1 通用 1 电销 0非电销
     */
    private Integer telesalesFlag;

    /**
     * 交付模式 -1 通用 1 前后端模式  0非前后端模式
     */
    private Integer deliveryModel;

    /**
     * 项目类型 -1 通用 1 首期 2 非首期
     */
    private Integer projectType;

    /**
     * 审核方式id 取字典表dict_review_method_type
     */
    private Long reviewMethodId;

    /**
     * 时间单位：分钟、小时、天
     */
    private String reviewUnit;

    /**
     * 审核周期
     */
    private String reviewPeriod;

    /**
     * 审核结束前N（分钟/小时/天）发送预警，逗号分割数字
     */
    private String warningBeforeReviewEnd;

    /**
     * 是否产生罚单 0.否 1.是
     */
    private Integer isFineFlag;

    /**
     * 罚款金额
     */
    private Integer fineMoney;
    /**
     * 是否排除节假日【true：排除；false：不排除】
     */
    private Boolean excludeHolidaysFlag;
    /**
     * 是否排除周末【true：排除；false：不排除】
     */
    private Boolean excludeWeekendsFlag;
    /**
     * 是否排除非工作时间【true：排除；false：不排除】
     * 排除非工作时间也就是只是在工作时间计算，不包含下班后
     */
    private Boolean excludeNonWorkingHoursFlag;
    /**
     * 处罚性质【1：一次性处罚；2：周期性处罚】
     */
    private Integer penaltyType;

    /**
     * 处罚周期单位：秒、分、时、天
     */
    private String penaltyPeriodUnit;

    /**
     * 处罚周期【0：一次性处罚（penalty_type值为1时）；大于0：处罚周期天数（penalty_type值为2时）】
     */
    private String penaltyPeriod;

    /**
     * 处罚前N（分钟/小时/天）发送预警，逗号分割数字
     */
    private String warningBeforePenalty;

    /**
     * 调用运营平台出罚单/预警单的funValue参数，可以区分罚款金额及处罚场景，是监控点monitorPoint的下级分类
     */
    private String yyFunValue;
    /**
     * 调用运营平台出罚单/预警单的monitorPoint参数，可以区分罚款金额及处罚场景
     */
    private String yyMonitorPoint;
}
