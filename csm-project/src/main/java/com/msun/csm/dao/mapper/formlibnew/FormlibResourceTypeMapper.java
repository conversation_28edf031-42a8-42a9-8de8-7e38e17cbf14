package com.msun.csm.dao.mapper.formlibnew;


import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.formlibnew.FormlibResourceType;
import com.msun.csm.dao.entity.proj.projform.DictFormType;
import com.msun.csm.model.resp.formlibnew.FormlibResourceSelectType;

/**
 * <AUTHOR>
 * @description 针对表【formlib_resource_type(表单资源库)】的数据库操作Mapper
 * @createDate 2025-07-14 13:47:02
 * @Entity jiaofuceshi.autoceshi.FormlibResourceType
 */
public interface FormlibResourceTypeMapper extends BaseMapper<FormlibResourceType> {

    /**
     * 根据标准id查询标准类型
     * @param formlibStandardTypeId
     * @return
     */
    List<DictFormType> getFormlibStandardTypeByParam(@Param("formlibStandardTypeId") Long formlibStandardTypeId);


    /**
     * 查询当前资源库关联的所有标签
     * @param formlibResourceUniteId
     * @return
     */
    List<FormlibResourceSelectType> selectListByPid(@Param("formlibResourceUniteId") Long formlibResourceUniteId);
}




