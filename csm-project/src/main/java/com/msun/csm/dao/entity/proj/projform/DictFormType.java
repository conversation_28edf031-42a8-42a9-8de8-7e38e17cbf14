package com.msun.csm.dao.entity.proj.projform;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单类型字典表
 *
 * @TableName dict_form_type
 */
@TableName(value = "dict_form_type", schema = "csm")
@Data
public class DictFormType extends BasePO implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "dict_form_type_id", type = IdType.INPUT)
    private Long dictFormTypeId;
    /**
     * 产品
     */
    @TableField(value = "yy_product_id")
    private Long yyProductId;
    /**
     * 模块
     */
    @TableField(value = "yy_module_id")
    private Long yyModuleId;
    /**
     * 类型编码
     */
    @TableField(value = "type_code")
    private String typeCode;
    /**
     * 类型名称
     */
    @TableField(value = "type_name")
    private String typeName;
    /**
     * 跳转路径
     */
    @TableField(value = "form_page_url")
    private String formPageUrl;
    /**
     * （云健康产品编码）
     */
    @TableField(value = "cloud_product_code")
    private String cloudProductCode;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @TableField(value = "creater_id")
    private Long createrId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;
    /**
     * 更新人id
     */
    @TableField(value = "updater_id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;
    /**
     * 跳转路径(中心端)
     */
    @TableField(value = "form_page_center_url")
    private String formPageCenterUrl;
}