package com.msun.csm.dao.entity.formlibnew;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_unite
 */
@TableName(value = "formlib_resource_unite")
@Data
public class FormlibResourceUnite extends BasePO {
    /**
     * 表单结构表主键
     */
    @TableId(type = IdType.INPUT)
    private Long formlibResourceUniteId;

    /**
     * 运营产品id
     */
    @TableField(value = "yy_product_id")
    private Long yyProductId;

    /**
     * 项目id
     */
    @TableField(value = "project_info_id")
    private Long projectInfoId;

    /**
     * 表单来源  0 通用库 1 项目库
     */
    @TableField(value = "form_source")
    private Integer formSource;

    /**
     * 表单保存唯一id， 用于调中心端进行设计
     */
    @TableField(value = "form_save_id")
    private Long formSaveId;

    /**
     * 表单名称
     */
    @TableField(value = "form_name")
    private String formName;

    /**
     * 表单样式图片存储路径
     */
    @TableField(value = "form_picture_paths")
    private String formPicturePaths;

    /**
     * 引用次数
     */
    @TableField(value = "use_count")
    private Integer useCount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 表单结构(表单结构的Json对象数据)
     */
    @TableField(value = "form_structure")
    private String formStructure;

    /**
     * 表单预览文件流(锐浪)
     */
    @TableField(value = "grf")
    private String grf;

    /**
     * 众阳设计器 pc样式
     */
    @TableField(value = "designer_grf")
    private String designerGrf;

    /**
     * 预留其他设计器文本
     */
    private String designerGrfApp;

    /**
     * 预留其他设计器文本
     */
    private String designerGrfPad;

    /**
     * 机构id
     */
    @TableField(value = "his_org_id")
    private Long hisOrgId;

    /**
     * 医院id
     */
    @TableField(value = "source_hospital_id")
    private Long sourceHospitalId;

    /**
     * 医院名称
     */
    @TableField(value = "source_hospital_name")
    private String sourceHospitalName;

    /**
     * 众阳设计器模板解析后文字（原用于推荐内容数据）
     */
    @TableField(value = "parsed_text")
    private String parsedText;


    /**
     * 产品属性表单类型(如评估单、记录单、知情文件等) 手麻流程节点
     */
    @TableField(value = "form_type")
    private String formType;

    /**
     * pc端格式配置
     */
    private String formConfigurationPc;
}