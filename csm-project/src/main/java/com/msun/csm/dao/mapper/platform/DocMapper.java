package com.msun.csm.dao.mapper.platform;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.platform.NewestProductDocDao;
import com.msun.csm.dao.entity.platform.ProductDocDao;

@Mapper
public interface DocMapper {
    List<NewestProductDocDao> getNewestProductVersions();

    List<ProductDocDao> getAllOldProductDoc(@Param("productName") String productName, @Param("newestVersion") String newestVersion);

    void deleteDoc(@Param("ids") List<Long> id);

    void deleteDocFile(@Param("ids") List<Long> id);

    void updateFilePath(@Param("id") Long id, @Param("filePath") String filePath);
}
