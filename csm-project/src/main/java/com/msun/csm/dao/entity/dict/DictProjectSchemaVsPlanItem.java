package com.msun.csm.dao.entity.dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/30 15:21
 */
@ApiModel(description = "项目模式字典")
@Data
@TableName(schema = "csm", value = "dict_project_schema_vs_plan_item")
public class DictProjectSchemaVsPlanItem {

    @TableId
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("项目模式编码")
    private String schemaCode;

    @TableField(exist = false)
    private String schemaName;

    @ApiModelProperty("项目计划阶段编码")
    private String planStageCode;

    @TableField(exist = false)
    private String planStageName;

    @ApiModelProperty("项目计划节点编码")
    private String planItemCode;

    @TableField(exist = false)
    private String planItemName;

    @ApiModelProperty("前置节点编码，逗号分割")
    private String prePlanItemCode;

    @TableField(exist = false)
    private String prePlanItemName;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();

    @TableField(exist = false)
    private String createName;
    @TableField(exist = false)
    private String updateName;
}
