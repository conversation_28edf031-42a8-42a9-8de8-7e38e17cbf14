package com.msun.csm.dao.entity.basedata;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/5/24 15:24
 */
@ApiModel("基础数据导入平台记录")
@Data
@TableName("csm.bd_import_platform_record")
public class BdImportPlatformRecord {
    @TableId
    private Long id;
    private String projectNumber;
    private String hospitalName;
    private Long hospitalId;
    private Long orgId;
    private String tableNameEn;
    private String tableNameCn;
    private String dataSchema;
    @ApiModelProperty("导入是否成功")
    private Boolean success = false;
    @ApiModelProperty("导入错误信息")
    private String errMsg = "";
    private Long total = 0L;
    private Long sucTotal = 0L;
    @ApiModelProperty("0待校验 1成功 2失败")
    private Integer validStatus = 0;
    private String validErrMsg = "";
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();
}
