package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSatisfactionSurveyRecord implements Serializable {

    /**
     * 主键
     */
    private Long productSatisfactionSurveyRecordId;

    /**
     * 创建人sys_user_id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人sys_user_id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 作废标识【0.正常；1.作废】
     */
    private Integer isDeleted;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 状态 1.未发放 2.收集中 3.已停止
     */
    private Integer status;

    /**
     * 产品评分
     */
    private BigDecimal score;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 发放人ID
     */
    private Long sendUserId;

    /**
     * 评分时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scoreTime;

    /**
     * 评分人ID
     */
    private Long scoreUserId;

    /**
     * 停止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stopTime;

    /**
     * 停止人ID
     */
    private Long stopUserId;

    /**
     * 项目ID
     */
    private Long projectInfoId;


}
