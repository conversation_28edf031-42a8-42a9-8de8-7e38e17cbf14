package com.msun.csm.dao.entity.proj;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductFunctionScoreRecordPO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long customInfoId;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 功能编码
     */
    private String functionCode;

    /**
     * 运营平台产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 功能点
     */
    private String functionName;

    /**
     * 功能描述
     */
    private String functionDesc;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 三级医院是否必选，0-非必选，1-必选
     */
    private Integer tertiaryHospitalFlag;

    /**
     * 二级医院是否必选，0-非必选，1-必选
     */
    private Integer secondHospitalFlag;

    /**
     * 一级医院是否必选，0-非必选，1-必选
     */
    private Integer firstHospitalFlag;

    /**
     * 妇幼保健院是否必选，0-非必选，1-必选
     */
    private Integer maternalChildHospitalFlag;

    /**
     * 其他专科医院（眼科医院、口腔医院）是否必选，0-非必选，1-必选
     */
    private Integer otherHospitalFlag;

    /**
     * 是否常用必备业务，比如挂号、缴费等可设置为常用必备业务，0-非常用，1-常用
     */
    private Integer commonFunctionFlag;

    /**
     * 预估扣分
     */
    private BigDecimal estimatedDeduction;

    /**
     * 实际扣分
     */
    private BigDecimal practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扣分类型字典表编码
     */
    private String deductionType;

    /**
     * 扣分类型描述
     */
    private String deductionTypeDesc;

    /**
     * 附件ID集合，多个以英文逗号分割
     */
    private String attachmentId;

    /**
     * 人民医院可用：0-不可用；1-可用
     */
    private Integer peoplesHospitalFlag;

    /**
     * 中医院可用：0-不可用；1-可用
     */
    private Integer chineseHospitalFlag;

    /**
     * 肿瘤医院可用：0-不可用；1-可用
     */
    private Integer tumorHospitalFlag;

    /**
     * 口腔医院可用：0-不可用；1-可用
     */
    private Integer stomatologyHospitalFlag;

    /**
     * 眼科医院可用：0-不可用；1-可用
     */
    private Integer eyeHospitalFlag;



    /**
     * 根据医院等级判断当前功能是否是必备功能
     *
     * @param hospitalLevel 医院等级
     * @return true-当前医院等级下的必备功能；false-当前医院等级下的可选功能
     */
    public boolean isRequiredFunction(Integer hospitalLevel) {
        // 医院等级是否必选标记
        boolean hospitalLevelFlag;
        if (Integer.valueOf("1").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(tertiaryHospitalFlag);
        } else if (Integer.valueOf("2").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(secondHospitalFlag);
        } else if (Integer.valueOf("3").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(firstHospitalFlag);
        } else if (Integer.valueOf("4").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(maternalChildHospitalFlag);
        } else {
            hospitalLevelFlag = Integer.valueOf("1").equals(otherHospitalFlag);
        }
        return hospitalLevelFlag;
    }

}
