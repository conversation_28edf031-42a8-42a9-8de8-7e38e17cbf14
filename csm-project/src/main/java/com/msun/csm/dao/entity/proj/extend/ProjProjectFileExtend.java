package com.msun.csm.dao.entity.proj.extend;

import lombok.AllArgsConstructor;
import lombok.Data;

import com.msun.csm.common.enums.projectfile.ProjectFileTypeEnums;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.req.projectfile.UploadFileReq2;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/24
 */
@Data
@AllArgsConstructor
public class ProjProjectFileExtend extends ProjProjectFile {

    /**
     * 文件访问预览地址
     */
    private String fileUrl;

    public ProjProjectFileExtend(UploadFileReq2 req, Long id, String objectKey, String desc) {
        this.setProjectFileId(id);
        this.setProjectInfoId(req.getProjectInfoId());
        this.setMilestoneNodeCode(req.getNodeCode());
        this.setProjectStageCode(ProjectFileTypeEnums.getEnum(req.getNodeCode()) == null ? "" : ProjectFileTypeEnums.getEnum(req.getNodeCode()).getStage());
        this.setFilePath(objectKey);
        this.setFileName(req.getFile().getOriginalFilename());
        this.setFileDesc(desc);
        this.setIsDeleted(0);
    }


    public ProjProjectFileExtend(UploadFileReq req, Long id, String objectKey, String desc) {
        this.setProjectFileId(id);
        this.setProjectInfoId(req.getProjectInfoId());
        this.setMilestoneNodeCode(req.getMilestoneCode() == null ? "" : req.getMilestoneCode());
        this.setProjectStageCode(ProjectFileTypeEnums.getEnum(req.getMilestoneCode()) == null ? ""
                : ProjectFileTypeEnums.getEnum(req.getMilestoneCode()).getStage());
        this.setFilePath(objectKey);
        this.setFileName(req.getFile().getOriginalFilename());
        this.setFileDesc(desc);
        this.setIsDeleted(0);
    }

    public ProjProjectFileExtend(UploadFileReq req, String originalFilename, Long id, String objectKey, String desc) {
        this.setProjectFileId(id);
        this.setProjectInfoId(req.getProjectInfoId());
        this.setMilestoneNodeCode(req.getMilestoneCode());
        this.setProjectStageCode(ProjectFileTypeEnums.getEnum(req.getMilestoneCode()) == null ? ""
                : ProjectFileTypeEnums.getEnum(req.getMilestoneCode()).getStage());
        this.setFilePath(objectKey);
        this.setFileName(originalFilename);
        this.setFileDesc(desc);
    }

    public ProjProjectFileExtend(UploadFileReq req, String projectStageCode, String originalFilename, Long id,
                                 String objectKey, String desc) {
        this.setProjectFileId(id);
        this.setProjectInfoId(req.getProjectInfoId());
        this.setMilestoneNodeCode(req.getMilestoneCode());
        this.setProjectStageCode(projectStageCode);
        this.setFilePath(objectKey);
        this.setFileName(originalFilename);
        this.setFileDesc(desc);
    }
}
