package com.msun.csm.dao.mapper.basedata;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.basedata.BdDictType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("bd")
public interface BdDictTypeMapper extends RootMapper<BdDictType> {
    void insertUpdate(@Param("data") List<BdDictType> data);
}
