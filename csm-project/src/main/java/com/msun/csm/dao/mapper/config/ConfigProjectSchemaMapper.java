package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.config.ConfigProjectSchema;

@Mapper
public interface ConfigProjectSchemaMapper extends RootMapper<ConfigProjectSchema> {
    List<ConfigProjectSchema> getList(@Param("keyword") String keyword);
}
