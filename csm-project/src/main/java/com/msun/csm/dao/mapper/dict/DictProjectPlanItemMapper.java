package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.dict.DictProjectPlanItem;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/3
 */

@Mapper
public interface DictProjectPlanItemMapper extends RootMapper<DictProjectPlanItem> {
    /**
     * delete by primary key
     *
     * @param projectPlanItemId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long projectPlanItemId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(DictProjectPlanItem record);

    int insertOrUpdate(DictProjectPlanItem record);

    int insertOrUpdateSelective(DictProjectPlanItem record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DictProjectPlanItem record);

    /**
     * select by primary key
     *
     * @param projectPlanItemId primary key
     * @return object by primary key
     */
    DictProjectPlanItem selectByPrimaryKey(Long projectPlanItemId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DictProjectPlanItem record);

    int batchInsert(@Param("list") List<DictProjectPlanItem> list);

    DictProjectPlanItem selectByCode(@Param("projectPlanItemCode") String projectPlanItemCode);
}
