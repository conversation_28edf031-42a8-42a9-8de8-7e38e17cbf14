package com.msun.csm.dao.mapper.proj.flowable;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveRecord;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface KfSpecialApproveRecordMapper extends RootMapper<KfSpecialApproveRecord> {

    @Data
    class GetKfSpecialApproveListParam {
        private String queryScope;
        private Iterable<Long> deptIdList;
        private Iterable<Long> joinedApproveIds;
        private Long uid;
        private String keyword;
        private String projectNumber;
        private Integer pageNo;
        private Integer pageSize;
    }

    /**
     * 查询客服特殊审批记录,如果传入了uid就说明不是查询全部的
     * @param param
     * @return
     */
    List<KfSpecialApproveRecord> getKfSpecialApproveList(GetKfSpecialApproveListParam param);
}
