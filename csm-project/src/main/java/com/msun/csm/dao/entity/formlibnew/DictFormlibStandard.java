package com.msun.csm.dao.entity.formlibnew;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库标准字典
 *
 * @TableName dict_formlib_standard
 */
@TableName(value = "dict_formlib_standard", schema = "csm")
@Data
public class DictFormlibStandard extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long formlibStandardId;

    /**
     * 父级id
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 运营产品id
     */
    @TableField(value = "yy_product_id")
    private Long yyProductId;

    /**
     * 字典分类编码
     */
    @TableField(value = "formlib_standard_code")
    private String formlibStandardCode;

    /**
     * 字典分类名称
     */
    @TableField(value = "formlib_standard_name")
    private String formlibStandardName;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 排序
     */
    @TableField(value = "order_num")
    private Integer orderNum;

    /**
     * 标准分类1是 0否
     */
    @TableField(value = "is_standard")
    private Integer isStandard;

    @ApiModelProperty(value = "路径id")
    private Long pathId;

}