package com.msun.csm.dao.entity.proj.flowable;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/14 15:43
 */
@ApiModel("特殊审批记录")
@Data
@TableName(value = "kf_special_approve_record", schema = "csm")
public class KfSpecialApproveRecord {
    @TableId
    private Long id;
    @ApiModelProperty("审批编码、自定义审批编码，标识不同类别的特殊事项审批")
    private String approveCode;
    @ApiModelProperty("客户ID")
    private Long customInfoId;
    @TableField(exist = false)
    private String customName;
    @ApiModelProperty("项目ID")
    private Long projectInfoId;
    @TableField(exist = false)
    private String projectName;
    @ApiModelProperty("项目编号")
    private String projectNumber;
    @ApiModelProperty("审批说明")
    private String details;
    @ApiModelProperty("审批携带的上传文件，JSON数组格式")
    private String fileList;
    @ApiModelProperty("最终审批状态")
    private Integer status;
    @ApiModelProperty("最终审批结果")
    private String result;
    @TableField(exist = false)
    private String deptName;
    @ApiModelProperty("申请人部门ID")
    private Long deptId;
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @TableField(exist = false)
    private String createrName;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @TableField(exist = false)
    private String updaterName;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();


    @TableField(exist = false)
    private Boolean canEdit = false; // 是否可以编辑该记录，前端使用
}
