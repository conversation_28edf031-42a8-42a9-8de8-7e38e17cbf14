package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.core.component.implementation.api.csm.dto.CsmDTO;
import com.msun.core.component.implementation.api.csm.vo.HospitalPrintCodeVO;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount;
import com.msun.csm.model.resp.projreport.ProjProjectSendMsgDataResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */

public interface ProjSurveyReportMapper extends BaseMapper<ProjSurveyReport> {

    /**
     * 查询报表数据
     *
     * @param projSurveyReportReq
     * @return
     */
    List<ProjSurveyReportResp> selectSurveyReportByPage(ProjSurveyReportReq projSurveyReportReq);

    /**
     * 查询报表数据总数
     *
     * @param projSurveyReportReq
     * @return
     */
    List<ProjSurveyReprotFormCount> selectSurveyReportCount(ProjSurveyReportReq projSurveyReportReq);

    /**
     * 更新报表状态
     *
     * @param projSurveyReport
     */
    void updateReprotStatusData(ProjSurveyReport projSurveyReport);

    /**
     * 项目拆分合并处理数据
     *
     * @param oldProjectId
     * @param newProjectId
     * @param changeProductList
     */
    int updateByProjectId(@Param("oldProjectId") Long oldProjectId, @Param("newProjectId") Long newProjectId, @Param("changeProductList") List<Long> changeProductList);

    /**
     * 查询打印节点结果
     *
     * @param csmDTO
     * @return
     */
    List<HospitalPrintCodeVO> getPrintCodeResultListByParamer(CsmDTO csmDTO);

    /**
     * 查询医院打印节点结果
     *
     * @param csmDTO
     * @return
     */
    List<ProjSurveyReport> selectSurveyReportByHospitalIdAndPrintCode(CsmDTO csmDTO);

    /**
     * 说明: 根据客户id修改云资源台账
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param projectInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId, @Param("newCustomInfoId") Long newCustomInfoId, @Param("projectInfoIds") List<Long> projectInfoIds);

    /**
     * 根据项目id和产品id删除
     *
     * @param projectInfoId
     * @param oldProductIds
     * @return
     */
    int deleteByProjectAndProductIds(@Param("projectInfoId") Long projectInfoId, @Param("oldProductIds") List<Long> oldProductIds);

    void udpateReprotByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void udpateReprotMakeByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void updateProductBacklogByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void updateProductBacklogDetailByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void updateFmUserFormDataByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void updateSurveyPlanByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void updateThirdInterfaceByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void updateThirdInterfaceDirPersonByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    /**
     * 查询发送消息数据
     * @return
     */
    List<ProjProjectSendMsgDataResp> selectSendMsgData();

    int deleteSurveyReport(@Param("surveyReportIdList") List<Long> surveyReportIdList, @Param("sysUserId") Long sysUserId);

    ProjSurveyReport selectBySurveyReportId(@Param("surveyReportId") Long surveyReportId);

    /**
     * 更新打印节点状态
     * @param customInfoId
     */
    void updatePrintReportStatusData(@Param("customInfoId") Long customInfoId);

    List<ProjSurveyReport> selectSurveyReport(@Param("projectInfoId")Long projectInfoId, @Param("hospitalInfoId")Long hospitalInfoId, @Param("yyProductIdList")List<Long> yyProductIdList);

    /**
     * 查询所有未对照的数据
     * @return
     */
    List<ProjSurveyForm> selectListAllNotCompar();

    /**
     * 查询所有未处理工单数据
     * @return
     */
    List<ProjSurveyForm> selectListAllNotWorkProcssCompar();

    /**
     * 查询所有未传给报表中台的报表数据
     * @return
     */
    List<ProjSurveyReport> selectListAllReprtNotCompar();

    /**
     * 查询所有无图片未上线的报表数据
     * @return
     */
    List<ProjSurveyReport> selectListAllReprtNotImgByNodeCode();

    /**
     *
     * @param projectInfoId
     * @param yyProductId
     * @param hospitalInfoId
     * @param finishedStatus 完成状态编码
     * @param onlineEssential 1-只查询上线必备
     * @return
     */
    int getFinishedReportCount(@Param("projectInfoId") Long projectInfoId, @Param("yyProductId") Long yyProductId, @Param("hospitalInfoId") Long hospitalInfoId, @Param("finishedStatus") List<Integer> finishedStatus,@Param("onlineEssential") Integer onlineEssential);

    /**
     * 查询客户是否已下沉
     * @param projectInfoId
     * @return
     */
    Boolean getPrintStatusByProjectInfoId(Long projectInfoId);
}
