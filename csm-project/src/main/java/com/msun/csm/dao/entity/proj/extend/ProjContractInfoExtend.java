package com.msun.csm.dao.entity.proj.extend;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import static com.msun.csm.common.staticvariable.StaticPara.DEFAULT_LONG;

import java.util.Date;

import com.msun.csm.dao.entity.oldimsp.TbContract;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.model.yunying.ConvertContractDTO;
import com.msun.csm.model.yunying.SyncContractDTO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */
@Data
@AllArgsConstructor
public class ProjContractInfoExtend extends ProjContractInfo {

    /**
     * 构造函数
     *
     * @param syncContractDTO
     * @param contractInfoId
     * @param leaderId
     * @param now
     */
    public ProjContractInfoExtend(SyncContractDTO syncContractDTO, Long contractInfoId,
                                  Long leaderId, Date now) {
        this.setContractInfoId(contractInfoId);
        this.setYyContractId(syncContractDTO.getContractNum());
        this.setYyContractCustomId(syncContractDTO.getPrincipalCustomer().getCustomerId().longValue());
        this.setContractNo(syncContractDTO.getContractStr());
        this.setContractName(syncContractDTO.getContractName());
        this.setContractType(syncContractDTO.getContractPreType());
        //Todo 缺少首付款状态
        this.setContractSignPersonId(syncContractDTO.getSaleUserId().longValue());
        this.setContractSignTeamId(syncContractDTO.getSaleOrgId().longValue());
        this.setCreaterId(leaderId);
        this.setUpdaterId(leaderId);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
        //方案分公司id
        if (syncContractDTO.getPreSaleOrgId() != null) {
            this.setContractPresaleteamId(syncContractDTO.getPreSaleOrgId().longValue());
        }
    }

    /**
     * 数据迁移老项目合同转新项目合同
     *
     * @param contract
     * @param now
     */
    public ProjContractInfoExtend(Long contractInfoId, TbContract contract, Date now) {
        this.setContractInfoId(contractInfoId);
        this.setYyContractId(contract.getConId());
        //合同客户
        this.setYyContractCustomId(DEFAULT_LONG);
        this.setContractNo(contract.getConName());
        this.setContractName(StrUtil.DASHED);
        this.setContractType(contract.getConType());
        this.setContractSignPersonId(contract.getSaleUserId().longValue());
        this.setContractSignTeamId(contract.getSaleOrgId().longValue());
        //Todo 缺少首付款状态
        // 新合同  默认首付款状态 -1 非正式合同， 后期运营平台会自动同步
        this.setPaySignage(-1);
        this.setCreaterId(DEFAULT_LONG);
        this.setUpdaterId(DEFAULT_LONG);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
    }

    public ProjContractInfoExtend(ConvertContractDTO dto, Long contractInfoId, long leaderId, Date now) {
        this.setContractInfoId(contractInfoId);
        this.setYyContractId(dto.getContractNum());
        this.setYyContractCustomId(dto.getContractCustomerId());
        this.setContractNo(dto.getContractStr());
        this.setContractName(dto.getContractName());
        this.setContractType(dto.getContractType());
        this.setContractSignPersonId(dto.getContractSaleUserId());
        this.setContractSignTeamId(dto.getContractSaleOrgId());
        this.setCreaterId(leaderId);
        this.setUpdaterId(leaderId);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
        //方案分公司id
        this.setContractPresaleteamId(dto.getContractPreSaleOrgId());
        // 新合同  默认首付款状态 -1 非正式合同， 后期运营平台会自动同步
        this.setPaySignage(-1);
    }
}
