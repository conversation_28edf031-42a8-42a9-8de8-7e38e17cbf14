package com.msun.csm.dao.entity.projectreview;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核类型字典表
 *
 * <AUTHOR>
 * @TableName dict_project_review_type
 */
@TableName(value = "dict_project_review_type", schema = "csm")
@Data
public class DictProjectReviewType extends BasePO {
    /**
     * 主键
     */
    @TableId
    private Long projectReviewTypeId;

    /**
     * 类型编码
     */
    private String dictCode;

    /**
     * 类型名称
     */
    private String dictName;

    /**
     * 审核业务描述
     */
    private String remark;
}
