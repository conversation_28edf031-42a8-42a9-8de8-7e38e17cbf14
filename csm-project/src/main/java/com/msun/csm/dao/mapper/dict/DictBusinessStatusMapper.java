package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictBusinessStatus;

@Mapper
public interface DictBusinessStatusMapper extends BaseMapper<DictBusinessStatus> {

    /**
     * @param businessCode 业务编码
     *                     <p>survey_product：产品业务调研</p>
     *                     <p>survey_form：表单调研</p>
     *                     <p>survey_report：打印报表调研</p>
     *                     <p>survey_statistics_report：统计报表调研</p>
     *                     <p>survey_third_part：三方接口调研</p>
     *                     <p>preparat_report：打印报表制作</p>
     * @return 业务状态
     */
    List<DictBusinessStatus> getBusinessStatusByBusinessCode(@Param("businessCode") String businessCode);


    /**
     * @param businessCode 业务编码
     *                     <p>survey_product：产品业务调研</p>
     *                     <p>survey_form：表单调研</p>
     *                     <p>survey_report：打印报表调研</p>
     *                     <p>survey_statistics_report：统计报表调研</p>
     *                     <p>survey_third_part：三方接口调研</p>
     *                     <p>preparat_report：打印报表制作</p>
     * @param statusId     状态编码
     * @return 业务状态
     */
    DictBusinessStatus getBusinessStatusByBusinessCodeAndStatusId(@Param("businessCode") String businessCode, @Param("statusId") Integer statusId);


    /**
     * @param businessCode 业务编码
     *                     <p>survey_product：产品业务调研</p>
     *                     <p>survey_form：表单调研</p>
     *                     <p>survey_report：打印报表调研</p>
     *                     <p>survey_statistics_report：统计报表调研</p>
     *                     <p>survey_third_part：三方接口调研</p>
     *                     <p>preparat_report：打印报表制作</p>
     * @param statusClass  状态分类
     *                     <p>1：未开始/未完成</p>
     *                     <p>2：进行中</p>
     *                     <p>3：已完成</p>
     * @return 业务状态
     */
    List<DictBusinessStatus> getBusinessStatusByBusinessCodeAndStatusClass(@Param("businessCode") String businessCode, @Param("statusClass") Integer statusClass);

}
