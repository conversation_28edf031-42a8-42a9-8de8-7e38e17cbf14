package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.dict.DictProjectSchemaVsPlanItem;

@Mapper
public interface DictProjectSchemaVsPlanItemMapper extends RootMapper<DictProjectSchemaVsPlanItem> {
    int checkProjectPlanStageLinked(@Param("id") Long id);

    int checkProjectPlanItemLinked(@Param("id") Long id);

    int checkProjectSchemaLinked(@Param("id") Long id);

    List<DictProjectSchemaVsPlanItem> getList(@Param("projectSchemaCode") String projectSchemaCode, @Param("keyword") String keyword);

}
