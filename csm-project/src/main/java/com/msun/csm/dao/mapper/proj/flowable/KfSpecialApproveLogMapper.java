package com.msun.csm.dao.mapper.proj.flowable;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveLog;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface KfSpecialApproveLogMapper extends RootMapper<KfSpecialApproveLog> {
    List<KfSpecialApproveLog> getKfSpecialApproveLogList(Long approveRecordId);
}
