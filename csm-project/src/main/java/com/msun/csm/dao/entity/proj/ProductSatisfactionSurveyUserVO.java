package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSatisfactionSurveyUserVO implements Serializable {

    /**
     * 主键
     */
    private Long productSatisfactionSurveyUserId;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String yyProductName;

    /**
     * 医院ID
     */
    private Long hospitalInfoId;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 云健康科室ID
     */
    private String cloudHospitalDeptId;

    /**
     * 云健康科室名称
     */
    private String cloudHospitalDeptName;


    /**
     * 云健康用户ID
     */
    private Long cloudHospitalUserId;

    /**
     * 云健康用户账户
     */
    private String cloudHospitalUserAccount;

    /**
     * 云健康用户名称
     */
    private String cloudHospitalUserName;

    /**
     * 添加时间
     */
    private String createTime;

    /**
     * 添加人
     */
    private String  createrUserName;

    /**
     * 状态 1.未发放 2.已发放待填写 3.已发放已填写
     */
    private Integer status;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 调研问卷地址
     */
    private String surveyUrl;



}
