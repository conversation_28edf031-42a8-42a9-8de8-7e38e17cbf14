package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.dao.entity.dict.DictProjectPlanStage;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/3
 */

@Mapper
public interface DictProjectPlanStageMapper extends RootMapper<DictProjectPlanStage> {

    /**
     * 查询所有数据
     *
     * @return
     */
    List<DictProjectPlanStage> getList();

    /**
     * 查询项目计划阶段字典
     * @return
     */
    List<BaseCodeNameResp> getAllPlanStage();
}
