package com.msun.csm.dao.mapper.projform;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.req.projform.ProjSurveyFormAddReq;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount;

/**
 * <AUTHOR>
 * @description 针对表【proj_survey_form(表单主表)】的数据库操作Mapper
 * @createDate 2024-09-14 15:15:49
 * @Entity generator.domain.ProjSurveyForm
 */
@Mapper
public interface ProjSurveyFormMapper extends BaseMapper<ProjSurveyForm> {


    /**
     * 分页查询表单信息
     *
     * @param projSurveyFormReq
     * @return
     */
    List<ProjSurveyFormResp> selectSurveyFormByPage(ProjSurveyFormReq projSurveyFormReq);

    /**
     * 查询表单统计数据
     *
     * @param projSurveyFormReq
     * @return
     */
    List<ProjSurveyReprotFormCount> selectSurveyFormCount(ProjSurveyFormReq projSurveyFormReq);

    /**
     * 更新表单状态
     *
     * @param projSurveyForm
     */
    void updateFormStatusData(ProjSurveyForm projSurveyForm);

    /**
     * 项目拆分合并处理数据
     *
     * @param oldProjectId
     * @param newProjectId
     * @param changeProductList
     */
    int updateByProjectId(@Param("oldProjectId") Long oldProjectId, @Param("newProjectId") Long newProjectId,
                          @Param("changeProductList") List<Long> changeProductList);

    /**
     * 说明: 根据客户id修改云资源台账
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param projectInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId,
                             @Param("projectInfoIds") List<Long> projectInfoIds);

    /**
     * 根据项目id和产品id删除
     *
     * @param projectInfoId
     * @param oldProductIds
     * @return
     */
    int deleteByProjectAndProductIds(@Param("projectInfoId") Long projectInfoId,
                                     @Param("oldProductIds") List<Long> oldProductIds);

    void udpateReprotByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    void udpateReprotMakeByOldId(@Param("newOldId") Long newOldId, @Param("oldId") Long oldId);

    List<ProjSurveyForm> selectSurveyForm(@Param("projectInfoId") Long projectInfoId, @Param("hospitalInfoId") Long hospitalInfoId, @Param("yyProductIdList") List<Long> yyProductIdList);


    //----------------------------------------------------

    /**
     * 根据表单主键查询表单信息
     *
     * @param surveyFormId 表单主键
     * @return 表单信息
     */
    ProjSurveyForm getSurveyFormById(@Param("surveyFormId") Long surveyFormId);

    /**
     * 根据表单主键更新推荐模板
     *
     * @param surveyFormId         表单主键
     * @param recommendTemplateIds 推荐模板ID列表，以逗号分隔
     * @return 更新成功的数据条数
     */
    int updateRecommendTemplateIds(@Param("surveyFormId") Long surveyFormId, @Param("recommendTemplateIds") String recommendTemplateIds);

    /**
     * 根据表单主键更新打印平台任务ID
     *
     * @param surveyFormId 表单主键
     * @param reportTaskId 打印平台任务id
     * @return 更新成功的数据条数
     */
    int updateReportTaskId(@Param("surveyFormId") Long surveyFormId, @Param("reportTaskId") String reportTaskId);

    /**
     * 批量查询要初始化标准模板表单数据
     * @param productIds
     * @param projectInfoId
     * @return
     */
    List<ProjSurveyForm> selectInitFormData(@Param("productIds") List<Long> productIds, @Param("projectInfoId") Long projectInfoId);

    /**
     * 查询项目下所有产品信息
     * @param productParam
     * @return
     */
    List<ProductInfo> findAllProjectProductList(ProductInfoDTO productParam);

    /**
     * 检测是否有标准模板表单
     * @param projSurveyFormReq
     * @return
     */
    List<ProjSurveyForm> selectListByStandardTypeInfo(ProjSurveyFormAddReq projSurveyFormReq);

    /**
     * 更新表单导入状态
     * @param vo
     */
    void updateDataByParamer(ProjSurveyForm vo);

    /**
     * 更新表单数据
     * @param projectInfoId
     */
    void updateFormByProjectId(@Param("projectInfoId") Long projectInfoId);

    /**
     * 是否不是或下沉
     * @param projectInfoId
     * @return
     */
    Boolean getPrintStatusByProjectInfoId(Long projectInfoId);
}
