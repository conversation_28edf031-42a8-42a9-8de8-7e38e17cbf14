package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSatisfactionSurveyRecordVO {

    /**
     * 主键
     */
    private Long productSatisfactionSurveyRecordId;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String yyProductName;

    /**
     * 状态 1.未发放 2.收集中 3.已停止
     */
    private Integer status;

    /**
     * 待发放数量
     */
    private Integer unsentCount;

    /**
     * 已发放数量
     */
    private Integer sentCount;

    /**
     * 已回收数量
     */
    private Integer recycledCount;

    /**
     * 已回收数量跳转链接
     */
    private String recycledCountUrl;

    /**
     * 回收比率
     */
    private BigDecimal recycledRadio;

    /**
     * 产品评分
     */
    private BigDecimal score;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发放时间
     */
    private Date sendTime;

    /**
     * 发放人ID
     */
    private Long sendUserId;

    /**
     * 发放人姓名
     */
    private String sendUserName;

    /**
     * 评分时间
     */
    private Date scoreTime;

    /**
     * 评分人ID
     */
    private Long scoreUserId;

    /**
     * 评分人姓名
     */
    private String scoreUserName;

    /**
     * 项目ID
     */
    private Long projectInfoId;

}
