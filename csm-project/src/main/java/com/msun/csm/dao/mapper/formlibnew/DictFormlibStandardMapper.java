package com.msun.csm.dao.mapper.formlibnew;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.dao.entity.formlibnew.DictFormlibStandard;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardResp;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTteeResp;

/**
 * <AUTHOR>
 * @description 针对表【dict_formlib_standard(表单资源库标准字典)】的数据库操作Mapper
 * @createDate 2025-07-14 13:47:02
 * @Entity jiaofuceshi.autoceshi.DictFormlibStandard
 */
public interface DictFormlibStandardMapper extends BaseMapper<DictFormlibStandard> {

    /**
     * 查询所有根节点
     * @param dto
     * @return
     */
    List<DictFormlibStandardTteeResp> getRootNodes(DictFormLibStandardPageReq dto);

    /**
     * 根据父节点id查询子节点
     * @param pid
     * @return
     */
    List<DictFormlibStandardTteeResp> getChildrenById(@Param("pid") Long pid, @Param("isDeleted") Integer isDeleted);

    /**
     * 根据DTO查询根节点
     * @param dto
     * @return
     */
    List<DictFormlibStandardResp> getNodesByDto(DictFormLibStandardPageReq dto);

    /**
     * 根据父节点id查询父节点code
     * @param id
     * @return
     */
    String getParentCode(@Param("id") Long id);

    void updateDataById(DictFormlibStandard entity);

    /**
     * 根据父节点id查询所有子节点id
     * @param id
     * @return
     */
    List<Long> getAllChildrenIdByPid(@Param("id") Long id, @Param("isDeleted") Integer isDeleted);

    /**
     * 根据id查询表单标准名称
     * @param id
     * @return
     */
    String getFormlibStandardName(@Param("id") Long id);

    /**
     * 查询表单标准类型列表(路径)
     * @param dto
     * @return
     */
    List<BaseCodeNameResp> selectDictFormTypeList(DictFormLibStandardPageReq dto);
}




