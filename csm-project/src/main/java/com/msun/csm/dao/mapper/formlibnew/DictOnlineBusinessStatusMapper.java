package com.msun.csm.dao.mapper.formlibnew;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.formlibnew.DictOnlineBusinessStatus;

/**
 * <AUTHOR>
 * @description 针对表【dict_formlib_standard(表单资源库标准字典)】的数据库操作Mapper
 * @createDate 2025-07-14 13:47:02
 * @Entity jiaofuceshi.autoceshi.DictFormlibStandard
 */
public interface DictOnlineBusinessStatusMapper extends BaseMapper<DictOnlineBusinessStatus> {

    /**
     * 查询业务上线必备状态列表
     * @param busCode
     * @return
     */
    List<BaseIdNameResp> getOnlineStatusPublicData(@Param("busCode") String busCode);
}




