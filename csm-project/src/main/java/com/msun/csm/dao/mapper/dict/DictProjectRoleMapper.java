package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictProjectRole;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Mapper
public interface DictProjectRoleMapper extends BaseMapper<DictProjectRole> {
    int deleteByPrimaryKey(Long projectRoleId);

    int insert(DictProjectRole record);

    int insertOrUpdate(DictProjectRole record);

    int insertOrUpdateSelective(DictProjectRole record);

    int insertSelective(DictProjectRole record);

    DictProjectRole selectByPrimaryKey(Long projectRoleId);

    int updateByPrimaryKeySelective(DictProjectRole record);

    int updateByPrimaryKey(DictProjectRole record);

    int updateBatch(List<DictProjectRole> list);

    int updateBatchSelective(List<DictProjectRole> list);

    int batchInsert(@Param ("list") List<DictProjectRole> list);

    List<DictProjectRole> selectDictProjectRole(DictProjectRole record);

    DictProjectRole getProjectRoleByCode(@Param("projectRoleCode") String projectRoleCode);
}
