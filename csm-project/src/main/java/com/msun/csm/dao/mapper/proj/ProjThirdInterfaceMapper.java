package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.dao.entity.proj.InterfaceAfterSurvey;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.model.dto.InterfaceReviewPageDTO;
import com.msun.csm.model.dto.ProjThirdInterfaceDTO;
import com.msun.csm.model.dto.ProjThirdInterfacePageDTO;
import com.msun.csm.model.imsp.ThirdInterfaceVO;
import com.msun.csm.model.req.projtool.ProjProjectInfoUpdateReq;
import com.msun.csm.model.vo.ImspInterfaceAuthorDataVO;
import com.msun.csm.model.vo.ImspInterfaceDataVO;
import com.msun.csm.model.vo.InterfaceReviewVO;
import com.msun.csm.model.vo.OldImspGroupInterfaceVO;
import com.msun.csm.model.vo.ProjThirdInterfaceVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */
@Mapper
public interface ProjThirdInterfaceMapper extends BaseMapper<ProjThirdInterface> {
    /**
     * delete by primary key
     *
     * @param thirdInterfaceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long thirdInterfaceId);


    int insertOrUpdate(ProjThirdInterface record);

    int insertOrUpdateSelective(ProjThirdInterface record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ProjThirdInterface record);

    /**
     * select by primary key
     *
     * @param thirdInterfaceId primary key
     * @return object by primary key
     */
    ProjThirdInterface selectByPrimaryKey(Long thirdInterfaceId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjThirdInterface record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ProjThirdInterface record);

    int updateBatch(List<ProjThirdInterface> list);

    int updateBatchSelective(List<ProjThirdInterface> list);

    int batchInsert(@Param("list") List<ProjThirdInterface> list);

    /**
     * 查询三方接口列表数据
     *
     * @param dto
     * @return
     */
    List<ProjThirdInterfaceVO> selectThirdInterface(ProjThirdInterfacePageDTO dto);

    /**
     * 根据id列表查询三方接口
     *
     * @param interfaceIdList
     * @return
     */
    List<ProjThirdInterface> selectByIds(@Param("interfaceIdList") List<Long> interfaceIdList);

    /**
     * 更新授权文件
     *
     * @param fileId
     * @param thirdInterfaceIdList
     * @return
     */
    int updateAuthLetterFiles(@Param("fileId") String fileId, @Param("idList") List<Long> thirdInterfaceIdList);

    /**
     * 接口裁定列表查询
     *
     * @param dto
     * @return
     */
    List<InterfaceReviewVO> selectPageForReviewInterface(InterfaceReviewPageDTO dto);

    /**
     * 根据id列表更新状态
     *
     * @param idList
     * @param status
     * @return
     */
    int updateStatusByIds(@Param("idList") List<Long> idList, @Param("status") int status);

    /**
     * 老系统接口数据查询
     *
     * @param projectInfoId
     * @param thirdInterfaceId
     * @return
     */
    List<ImspInterfaceDataVO> selectImspInterfaceData(@Param("projectInfoId") Long projectInfoId,
                                                      @Param("thirdInterfaceId") Long thirdInterfaceId);

    /**
     * 老系统接口授权数据查询 -- 旧流程
     *
     * @param thirdInterfaceId
     * @return
     */
    List<ImspInterfaceAuthorDataVO> selectImspInterfaceAuthorDataForOld(
            @Param("thirdInterfaceId") Long thirdInterfaceId);

    /**
     * 老系统接口授权数据查询 -- 新流程
     *
     * @param thirdInterfaceId
     * @return
     */
    List<ImspInterfaceAuthorDataVO> selectImspInterfaceAuthorDataForNew(
            @Param("thirdInterfaceId") Long thirdInterfaceId);

    /**
     * 更新老系统数据同步标识
     *
     * @param id
     */
    void updateCsmImportFlag(@Param("id") Long id);

    /**
     * 查询老系统接口分组授权数据
     *
     * @param id
     * @return
     */
    List<OldImspGroupInterfaceVO> selectOldImspInterfaceGroupData(@Param("id") Long id);

    /**
     * 查询老系统接口文件数据
     *
     * @param newInterfaceId
     * @return
     */
    List<ImspInterfaceDataVO> selectOldInterfaceForFile(@Param("newInterfaceId") Long newInterfaceId);

    /**
     * 运维平台查询交付平台三方接口列表
     *
     * @param hospitalId
     * @return
     */
    List<ThirdInterfaceVO> findThirdInterfaceList(@Param("hospitalId") Long hospitalId);

    /**
     * 获取项目下所有上线必备的三方接口
     *
     * @param projectInfoId 项目ID
     * @return 所有上线必备的三方接口
     */
    List<ProjThirdInterface> getRequiredInterface(@Param("projectInfoId") Long projectInfoId);

    /**
     * 更新三方合同文件
     *
     * @param fileId
     * @param thirdInterfaceIdList
     * @return
     */
    int updateContractFiles(@Param("fileId") String fileId, @Param("idList") List<Long> thirdInterfaceIdList);

    /**
     * 更新三方接口的责任人和责任团队
     *
     * @param interfaceId
     * @return
     */
    int updateInterfaceDir(@Param("interfaceId") Long interfaceId);

    /**
     * 更新三方接口项目类型
     *
     * @param libFormReq
     */
    void updateByProjectInfoIdAndProjectType(ProjProjectInfoUpdateReq libFormReq);

    /**
     * 说明: 根据客户id修改云资源台账
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param projectInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId,
                             @Param("projectInfoIds") List<Long> projectInfoIds);


    List<InterfaceAfterSurvey> getInterfaceAfterSurvey(@Param("projectInfoId") Long projectInfoId, @Param("completeTime") String completeTime);

    /**
     * 查询需要同步到新流程的接口
     * @return
     */
    List<ProjThirdInterface> selectListToNewTime();

    /**
     * 查询三方接口在运维平台是否是后端运维的客户
     * @param dto
     * @return
     */
    Integer selectInterfaceInOtherSystemInfo(ProjThirdInterfaceDTO dto);

    /**
     * 根据参数更新三方接口的部门id和用户id
     * @param dto
     */
    void updateDeptIdAndUserIdByParamers(ProjThirdInterfaceDTO dto);

    /**
     * 根据三方接口id更新反馈单状态
     * @param thirdInterfaceId
     * @param status
     */
    void updateFeedbackByInterfaceId(@Param("thirdInterfaceId") Long thirdInterfaceId,  @Param("status") int status);

    /**
     * 查询三方接口的分类
     * @return
     */
    List<BaseIdCodeNameResp> selectInterfaceCategory();

    int getInterfaceCount(@Param("projectInfoId") Long projectInfoId, @Param("statusList") List<Integer> statusList);
}
