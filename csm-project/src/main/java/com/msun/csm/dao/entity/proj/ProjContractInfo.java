package com.msun.csm.dao.entity.proj;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/26
 */

/**
 * 合同信息
 */
@ApiModel (description = "合同信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjContractInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同id
     */
    @TableId(type = IdType.INPUT)
    @Schema(description = "合同id")
    private Long contractInfoId;
    /**
     * 运营平台合同ID
     */
    @Schema(description = "运营平台合同ID")
    private Long yyContractId;
    /**
     * 合同号
     */
    @Schema(description = "合同号")
    private String contractNo;
    /**
     * 合同名
     */
    @Schema(description = "合同名")
    private String contractName;
    /**
     * 合同类型 1标准合同2借货合同3新产品试用协议4客户协议5其他
     */
    @Schema(description = "合同类型 1标准合同2借货合同3新产品试用协议4客户协议5其他")
    private Integer contractType;
    /**
     * 合同签订人员ID
     */
    @Schema(description = "合同签订人员ID")
    private Long contractSignPersonId;
    /**
     * 合同签订团队ID
     */
    @Schema(description = "合同签订团队ID")
    private Long contractSignTeamId;
    /**
     * 首付款标识   0首付款未支付    1首付款已支付
     */
    @Schema(description = "首付款标识   0首付款未支付    1首付款已支付  -1 初始（非正式合同）")
    private Integer paySignage;
    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
    /**
     * 更新人id
     */
    @Schema(description = "更新人id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @Schema(description = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
    /**
     * 运营平台合同客户ID（甲方）
     */
    @Schema(description = "运营平台合同客户ID（甲方）")
    private Long yyContractCustomId;

    @Schema(description = "合同签订配合的售前团队id（运营平台团队id）")
    private Long contractPresaleteamId;

    @Schema(description = "未通过具体情况描述")
    private String conPreDesc;
}
