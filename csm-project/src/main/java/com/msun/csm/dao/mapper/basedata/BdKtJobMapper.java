package com.msun.csm.dao.mapper.basedata;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.basedata.BdKtJob;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BdKtJobMapper extends RootMapper<BdKtJob> {
    void insertUpdate(@Param("list") List<BdKtJob> list);

    List<BdKtJob> selectJobByTableName(@Param("jobType") String jobType, @Param("tableNameEn") String tableNameEn, @Param("jobName") String jobName);
}
