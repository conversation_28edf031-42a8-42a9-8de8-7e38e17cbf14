package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSatisfactionSurveyUser implements Serializable {

    /**
     * 主键
     */
    private Long productSatisfactionSurveyUserId;

    /**
     * 创建人sys_user_id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人sys_user_id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 作废标识【0.正常；1.作废】
     */
    private Integer isDeleted;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 医院ID
     */
    private Long hospitalInfoId;

    /**
     * 云健康部门ID
     */
    private String cloudHospitalDeptId;

    /**
     * 云健康部门名称
     */
    private String cloudHospitalDeptName;

    /**
     * 云健康用户ID
     */
    private Long cloudHospitalUserId;

    /**
     * 云健康用户名称
     */
    private String cloudHospitalUserName;

    /**
     * 状态 1.未发放 2.已发放待填写 3.已发放已填写
     */
    private Integer status;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 云健康用户账户
     */
    private String cloudHospitalUserAccount;

    /**
     * 云健康用户身份ID
     */
    private Long cloudIdentityId;

    /**
     * 调研问卷地址
     */
    private String surveyUrl;

}
