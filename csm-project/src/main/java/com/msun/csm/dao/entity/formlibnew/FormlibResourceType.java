package com.msun.csm.dao.entity.formlibnew;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单资源库
 *
 * @TableName formlib_resource_type
 */
@TableName(value = "formlib_resource_type")
@Data
public class FormlibResourceType extends BasePO {
    /**
     * 表单资源库表入库记录表主键
     */
    @TableId(type = IdType.INPUT)
    private Long formlibResourceTypeId;

    /**
     * formlib_resource_unite的id
     */
    @TableField(value = "formlib_resource_unite_id")
    private Long formlibResourceUniteId;


    /**
     * dict_formlib_standard分类id
     */
    @TableField(value = "formlib_standard_id")
    private Long formlibStandardId;

    /**
     * dict_formlib_standard_type分标准字典id
     */
    @TableField(value = "formlib_standard_type_id")
    private Long formlibStandardTypeId;
}