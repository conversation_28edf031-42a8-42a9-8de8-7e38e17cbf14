package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictProductVsEmpower;
import com.msun.csm.dao.entity.dict.DictProductVsEmpowerRelative;
import com.msun.csm.model.dto.ProductIdContrastDTO;
import com.msun.csm.model.dto.yunying.ProductVsEmpowerDto;
import com.msun.csm.model.vo.dict.DictProductVsEmpowerVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/28
 */

public interface DictProductVsEmpowerMapper extends BaseMapper<DictProductVsEmpower> {
    int deleteByPrimaryKey(Long productVsEmpowerId);

    int insertOrUpdate(DictProductVsEmpower record);

    int insertOrUpdateSelective(DictProductVsEmpower record);

    int insertSelective(DictProductVsEmpower record);

    DictProductVsEmpower selectByPrimaryKey(Long productVsEmpowerId);

    int updateByPrimaryKeySelective(DictProductVsEmpower record);

    int updateByPrimaryKey(DictProductVsEmpower record);

    int updateBatch(List<DictProductVsEmpower> list);

    int updateBatchSelective(List<DictProductVsEmpower> list);

    int batchInsert(@Param("list") List<DictProductVsEmpower> list);

    // 根据产品id查询产品对照关系
    List<ProductIdContrastDTO> selectByProductIds(List<Long> productIds);

    /**
     * 查询关联详情
     *
     * @return List<DictProductVsEmpowerRelative>
     */
    List<DictProductVsEmpowerRelative> selectListRelative(List<Long> productIds);

    /**查询工单产品对照授权菜单列表
     *
     * @param orderProductName
     * @param msunHealthModuleCode
     * @param msunHealthModule
     * @return
     */
    List<DictProductVsEmpowerVO> findDictProductVsEmpowerList(@Param("orderProductName") String orderProductName,
                                                              @Param("msunHealthModuleCode") String msunHealthModuleCode,
                                                              @Param("msunHealthModule") String msunHealthModule);


    /**
     * 获取全量的产品授权码对照
     * @param yyProductId
     * @return
     */
    List<ProductVsEmpowerDto> getAllProductVsEmpowerList(@Param("yyProductId") Long yyProductId);


}
