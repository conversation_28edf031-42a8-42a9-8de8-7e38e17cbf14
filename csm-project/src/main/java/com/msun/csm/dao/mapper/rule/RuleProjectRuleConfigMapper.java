package com.msun.csm.dao.mapper.rule;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.model.param.GetProjectFileRuleParam;
import com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO;
import com.msun.csm.model.vo.ProjectFileUploadRuleVO;
import com.msun.csm.model.vo.ProjProjectFileRuleVO;

/**
 * @Entity .domain.RuleProjectRuleConfig
 */
@Mapper
public interface RuleProjectRuleConfigMapper extends BaseMapper<RuleProjectRuleConfig> {

    /**
     * 项目验收的时候查询模板数据
     *
     * @return List<ProjApplyAcceptanceVO>
     */
    List<ProjProjectAcceptanceRuleVO> getTemplateByCode(@Param("project") ProjProjectInfo project);

    /**
     * 根据产品编码查询附件配置
     * @param sceneCode
     * @param isMobile
     * @return
     */
    List<ProjProjectFileRuleVO> getTemplateByProductCode(@Param("sceneCode") String sceneCode,
                                                         @Param("isMobile") Integer isMobile);

    /**
     * 查询项目上传文件规则配置
     */
    List<ProjectFileUploadRuleVO> getProjectFileRule(GetProjectFileRuleParam params);
}




