package com.msun.csm.dao.entity.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/30 14:34
 */
@ApiModel(description = "项目模式配置")
@Data
@TableName(schema = "csm", value = "config_project_schema")
public class ConfigProjectSchema {

    @TableId
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("项目模式字典编码")
    private String dictProjectSchemaCode;

    @TableField(exist = false)
    private String dictProjectSchemaName;

    @ApiModelProperty("项目模式配置方式类型：工单类型、产品分类、业务场景")
    private String configType;

    @ApiModelProperty("项目模式配置方式类型编码：工单类型编码、产品分类编码、业务场景编码")
    private String configTypeCode;

    @TableField(exist = false)
    private String configTypeName;

    @ApiModelProperty(value = "实施类型：-1.通用；1.老换新；2.新上线")
    private Integer upgradationType = -1;

    @ApiModelProperty(value = "项目首期标识：-1.通用；0.否；1.是")
    private Integer hisFlag = -1;

    @ApiModelProperty(value = "电销是否可用：-1.通用；0.否；1.是")
    private Integer telesalesFlag = -1;

    @ApiModelProperty(value = "单体是否可用：-1.通用；0.否；1.是")
    private Integer monomerFlag = -1;

    @ApiModelProperty(value = "区域是否可用：-1.通用；0.否；1.是")
    private Integer regionFlag = -1;

    @ApiModelProperty(value = "配置描述")
    private String configDesc;

    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();

    @TableField(exist = false)
    private String createName;
    @TableField(exist = false)
    private String updateName;
}
