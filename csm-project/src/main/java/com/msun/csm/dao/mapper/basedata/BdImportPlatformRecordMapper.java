package com.msun.csm.dao.mapper.basedata;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.basedata.BdImportPlatformRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BdImportPlatformRecordMapper extends RootMapper<BdImportPlatformRecord> {
    void insertUpdate(@Param("list") List<BdImportPlatformRecord> list);

    IPage<BdImportPlatformRecord> findList(IPage<?> page, @Param("args") BdImportPlatformRecord args);
}
