package com.msun.csm.dao.entity.dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/3
 */

/**
 * 项目计划工作项字典表
 */
@ApiModel(description = "项目计划工作项字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm", value = "dict_project_plan_item")
public class DictProjectPlanItem {
    /**
     * 主键-字典表
     */
    @TableId
    @ApiModelProperty(value = "主键-字典表")
    private Long projectPlanItemId;

    /**
     * 项目计划工作项code
     */
    @ApiModelProperty(value = "项目计划工作项code")
    private String projectPlanItemCode;

    /**
     * 项目计划工作项名称
     */
    @ApiModelProperty(value = "项目计划工作项名称")
    private String projectPlanItemName;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 节点路径，点击跳转的页面路径
     */
    @ApiModelProperty(value = "节点路径，点击跳转的页面路径")
    private String url;

    /**
     * vue组件标识名称(前端使用)
     */
    @ApiModelProperty(value = "vue组件标识名称(前端使用)")
    private String isComponent;

    /**
     * 是否是里程碑标记 0 否 1 是
     */
    @ApiModelProperty(value = "是否是里程碑标记 0 否 1 是")
    private Integer milestoneFlag;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 通过我的待办查看某个产品的明细页时前端需要的编码
     */
    private String webDetailCode;

    /**
     * 进度展示方式：none-不展示；total-只展示总数；ratio-展示比例，格式为完成数/总数
     */
    private String progressDisplayMode;

    @TableField(exist = false)
    private String createName;
    @TableField(exist = false)
    private String updateName;
}
