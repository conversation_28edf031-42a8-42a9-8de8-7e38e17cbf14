package com.msun.csm.dao.entity.proj.flowable;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/14 15:44
 */
@ApiModel("特殊审批日志")
@Data
@TableName(value = "kf_special_approve_log", schema = "csm")
public class KfSpecialApproveLog {
    @TableId
    private Long id;
    @ApiModelProperty("审批记录ID，一个审批记录包含N个审批日志")
    private Long approveId;
    @ApiModelProperty("是否结束节点，如果是结束节点就去更新审批记录表")
    private Boolean isFinish;
    @ApiModelProperty("审批状态")
    private Integer status;
    @ApiModelProperty("审批结果")
    private String result;
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @TableField(exist = false)
    private String createrName;

    @TableField(exist = false)
    private String phone;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();
}
