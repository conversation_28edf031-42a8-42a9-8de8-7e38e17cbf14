package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectCommonFileVsRule2 implements Serializable {

    /**
     * 主键
     */
    private Long projectCommonFileId;

    /**
     * 创建人sys_user_id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人sys_user_id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 作废标识【0.正常；1.作废】
     */
    private Integer isDeleted;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 项目文件规则编码
     */
    private String projectRuleCode;

    /**
     * 项目计划/里程碑节点编码
     */
    private String nodeCode;

    /**
     * 项目文件规则分类编码
     */
    private String classCode;

    /**
     * 项目文件规则分类下子项编码
     */
    private String itemCode;

    /**
     * 项目文件主键，proj_project_file表主键
     */
    private String projectFileIds;

    /**
     * 是否使用：true-使用；false-不使用
     */
    private Boolean useFlag;

    /**
     * 检测结果：1-未检测；2-检测通过；3-检测不通过
     */
    private Integer checkResult;

    /**
     * 检测结果文本
     */
    private String checkResultText;

    /**
     * 项目ID
     */
    private Long projectInfoId;


}
