package com.msun.csm.dao.entity.proj;

import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @fileName:
 * @author:zzy
 * @updateBy:
 * @Date:Created in 15:35 2024/4/24
 * @remark:
 */
@ApiModel(description = "根据医院获取产品列表信息")
@Data
public class ProductInfo extends BasePO {

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private Long productId;
    /**
     * 工单产品主键ID
     */
    @ApiModelProperty(value = "工单产品主键ID")
    private String orderProductId;


    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;


    /**
     * 交付工单号
     */
    @ApiModelProperty(value = "交付工单号")
    private String deliveryOrderNo;


    /**
     * 实施状态
     */
    @ApiModelProperty(value = "实施状态")
    private Integer productExcutionStatus;

    /**
     * 实施状态转化字段
     */
    @ApiModelProperty(value = "实施状态")
    private String excutionStatus;


    /**
     * 开通状态
     */
    @ApiModelProperty(value = "开通状态")
    private Integer productOpenStatus;
    /**
     * 开通状态转化字段
     */
    @ApiModelProperty(value = "开通状态")
    private String openStatus;
    /**
     * 文档地址
     */
    @ApiModelProperty(value = "文档地址")
    private String filePath;
    /**
     * 运营平台工单产品字典ID
     */
    @ApiModelProperty(value = "运营平台工单产品字典ID")
    private Long yyOrderProductId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;
    /**
     * 解决方案id
     */
    @ApiModelProperty(value = "解决方案id")
    private Long productResolveTypeId;
    /**
     * 部署产品名称
     */
    @ApiModelProperty(value = "部署产品名称")
    private String reProductName;
    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;

    private Long yyProductId;

    @ApiModelProperty(value = "是否特批产品")
    private Boolean isSpecial;

}
