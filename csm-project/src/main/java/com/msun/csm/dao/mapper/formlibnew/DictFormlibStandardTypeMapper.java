package com.msun.csm.dao.mapper.formlibnew;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.dao.entity.formlibnew.DictFormlibStandardType;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardTypePageReq;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTypeSelectResp;

/**
 * <AUTHOR>
 * @description 针对表【dict_formlib_standard_type(表单标准名称字典)】的数据库操作Mapper
 * @createDate 2025-07-14 13:47:02
 * @Entity jiaofuceshi.autoceshi.DictFormlibStandardType
 */
public interface DictFormlibStandardTypeMapper extends BaseMapper<DictFormlibStandardType> {

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    List<DictFormlibStandardTypeSelectResp> selectPageByDto(DictFormLibStandardTypePageReq dto);

    /**
     * 更新数据
     * @param entity
     */
    void updateDataById(DictFormlibStandardType entity);

    /**
     * 查询标准类型字典数据
     * @param dto
     * @return
     */
    List<BaseCodeNameResp> findStandardTypeDictData(DictFormLibStandardPageReq dto);
}




