package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUser;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUserVO;


public interface ProductSatisfactionSurveyUserMapper {

    /**
     * @param projectInfoId
     * @param yyProductId
     * @param status        状态 1.未发放 2.已发放待填写 3.已发放已填写
     * @return
     */
    int getCountByProjectAndProductAndStatus(@Param("projectInfoId") Long projectInfoId, @Param("yyProductId") Long yyProductId, @Param("status") Integer status);

    int saveProductSatisfactionSurveyUser(ProductSatisfactionSurveyUser record);

    int abandonUserById(@Param("id") Long id);

    List<ProductSatisfactionSurveyUser> getProductSatisfactionSurveyUser(@Param("projectInfoId") Long projectInfoId, @Param("yyProductId") Long yyProductId, @Param("statusList") List<Integer> statusList);


    List<ProductSatisfactionSurveyUserVO> getProductSatisfactionUser(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据主键更新状态
     *
     * @param id     主键
     * @param status 状态：1.未发放；2.已发放待填写；3.已发放已填写；
     * @return 更新成功数据条数
     */
    int updateStatusById(@Param("id") Long id, @Param("status") Integer status);


    int updateStatus(@Param("status") Integer status, @Param("projectInfoId") Long projectInfoId, @Param("cloudUserId") String cloudUserId, @Param("cloudDeptName") String cloudDeptName, @Param("hospitalInfoId") Long hospitalInfoId, @Param("yyProductId") Long yyProductId);

    int updateSurveyUrl(@Param("id") Long id, @Param("surveyUrl") String surveyUrl);

}




