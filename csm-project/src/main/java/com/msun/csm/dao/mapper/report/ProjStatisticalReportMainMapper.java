package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainPageReq;
import com.msun.csm.model.resp.statis.ProjProductResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainExportResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainSelectResp;

/**
 * 统计报表主表
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ProjStatisticalReportMainMapper extends BaseMapper<ProjStatisticalReportMainEntity> {

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    List<ProjStatisticalReportMainSelectResp> findReportPage(ProjStatisticalReportMainPageReq dto);

    /**
     * 根据项目id和报表状态查询
     *
     * @param projectInfoId
     * @param reportStatus
     * @return
     */
    List<ProjStatisticalReportMainEntity> selectListByProjectIdAndStatus(@Param("projectInfoId") Long projectInfoId, @Param("reportStatus") Integer reportStatus);

    /**
     * 根据项目id和报表状态查询
     *
     * @param projectInfoId
     * @param statusList 状态集合
     * @return
     */
    int getCountByProjectIdAndStatus(@Param("projectInfoId") Long projectInfoId, @Param("statusList") List<Integer> statusList);

    /**
     *
     *
     * @param hospitalId
     * @return
     */
    List<ProjProductResp> getProductList(@Param("hospitalId") Long hospitalId);

    /**
     * 根据运营报表id查询
     * @param operationStatisticsReportId
     * @return
     */
    ProjStatisticalReportMainEntity selectByOperationStatisticsReportId(@Param("operationStatisticsReportId") String operationStatisticsReportId);

    /**
     * 导出报表数据查询
     * @param dto
     * @return
     */
    List<ProjStatisticalReportMainExportResp> findExportReportPage(ProjStatisticalReportMainPageReq dto);

    /**
     * 根据主键更改负责人信息
     * @param allocateUserId
     * @param statisticalReportMainId
     * @return
     */
    int updateAllocateUserByImport(@Param("allocateUserId") Long allocateUserId, @Param("statisticalReportMainId") Long statisticalReportMainId);
}
