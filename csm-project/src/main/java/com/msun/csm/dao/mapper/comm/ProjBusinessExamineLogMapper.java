package com.msun.csm.dao.mapper.comm;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: zhangdi
 * @Date: 2025/03/11/10:53
 */
@Mapper
public interface ProjBusinessExamineLogMapper {

    int saveBusinessLog(ProjBusinessExamineLog record);

    /**
     * 根据业务主键查询操作日志
     *
     * @param id 业务主键
     * @return 操作日志
     */
    List<ProjBusinessExamineLogResp> selectLogById(@Param("id") Long id, @Param("examineStatus") Integer examineStatus);

    /**
     * 根据业务id查询最后一笔是已完成的
     *
     * @param businessIds
     * @return
     */
    List<ProjBusinessExamineLog> selectListByIds(@Param("businessIds") List<Long> businessIds);

    /**
     * 根据业务id和状态查询
     *
     * @param businessIds
     * @param status
     * @return
     */
    List<ProjBusinessExamineLog> selectListByParamer(@Param("businessIds") List<Long> businessIds, @Param("status") List<Integer> status);

    /**
     * 根据业务类型、业务ID、状态获取日志
     *
     * @param businessType  业务类型
     *                      <p>survey-产品业务调研</p>
     *                      <p>printreport-打印报表</p>
     *                      <p>form-表单</p>
     * @param examineStatus 审核状态
     *                      <p>0-产品业务调研、表单、打印报表提交后端审核，待后端审核</p>
     *                      <p>1-产品业务调研、表单、打印报表后端审核通过</p>
     *                      <p>2-产品业务调研、表单、打印报表后端审核驳回</p>
     *                      <p>11-产品业务调研撤销确认最终结果</p>
     *                      <p>12-产品业务调研撤销提交后端审核</p>
     *                      <p>20-表单、打印报表制作完成提交前端人员验证</p>
     *                      <p>21-表单、打印报表前端人员验证通过</p>
     *                      <p>22-表单、打印报表前端人员验证驳回</p>
     *                      <p>31-表单设置不使用</p>
     * @param businessId    业务主键
     * @return 操作日志
     */
    List<ProjBusinessExamineLog> getLogByBusinessAndStatus(@Param("businessType") String businessType, @Param("examineStatus") Integer examineStatus, @Param("businessId") Long businessId);
}
