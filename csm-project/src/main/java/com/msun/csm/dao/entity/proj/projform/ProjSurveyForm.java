package com.msun.csm.dao.entity.proj.projform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 表单主表
 *
 * @TableName proj_survey_form
 */
@TableName(value = "proj_survey_form", schema = "csm")
@Data
public class ProjSurveyForm extends BasePO implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "survey_form_id", type = IdType.INPUT)
    private Long surveyFormId;
    /**
     * 客户id(proj_customer_info)
     */
    @TableField(value = "customer_info_id")
    private Long customerInfoId;
    /**
     * 项目id(proj_project_info)
     */
    @TableField(value = "project_info_id")
    private Long projectInfoId;
    /**
     * 医院编号(proj_hospital_info)（交付平台id）
     */
    @TableField(value = "hospital_info_id")
    private Long hospitalInfoId;
    /**
     * 产品id， 区分哪个产品(dict_product)
     */
    @TableField(value = "yy_product_id")
    private Long yyProductId;
    /**
     * 模块id(dict_product_vs_modules)
     */
    @TableField(value = "yy_module_id")
    private Long yyModuleId;
    /**
     * 表单名称
     */
    @TableField(value = "form_name")
    private String formName;
    /**
     * 上线必备 0： 否 1: 是
     */
    @TableField(value = "online_essential")
    private Integer onlineEssential;
    /**
     * 完成状态：
     * <p>0：未开始</p>
     * <p>4：提交调研审核</p>
     * <p>6：调研审核驳回</p>
     * <p>5：调研审核通过</p>
     * <p>1：制作完成</p>
     * <p>2：制作完成已驳回</p>
     * <p>8：制作完成验证通过</p>
     */
    @TableField(value = "finish_status")
    private Integer finishStatus;
    /**
     * 调研收集的样式路径，多个样式名称应使用英文逗号（,）分隔（proj_project_file表project_file_id集）
     */
    @TableField(value = "survey_imgs")
    private String surveyImgs;
    /**
     * 预留后期补充调研表单图片（proj_project_file表project_file_id集）
     */
    @TableField(value = "supplement_imgs")
    private String supplementImgs;
    /**
     * 在客服完成操作后上传的图片路径，多个图片路径应使用英文逗号（,）分隔；（proj_project_file表project_file_id集）
     */
    @TableField(value = "finish_imgs")
    private String finishImgs;
    /**
     * 表单分类（dict_form_type 表 type_code）
     */
    @TableField(value = "type_code")
    private String typeCode;
    /**
     * 调研负责人
     */
    @TableField(value = "survey_user_id")
    private Long surveyUserId;
    /**
     * 调研完成时间
     */
    @TableField(value = "survey_finish_time")
    private Timestamp surveyFinishTime;
    /**
     * 制作负责人
     */
    @TableField(value = "make_user_id")
    private Long makeUserId;
    /**
     * 提交制作完成时间
     */
    @TableField(value = "commit_finish_time")
    private Timestamp commitFinishTime;
    /**
     * 确认完成时间
     */
    @TableField(value = "make_finish_time")
    private Timestamp makeFinishTime;
    /**
     * 表单来源（（产品调研、老系统、新增)）
     */
    @TableField(value = "form_source")
    private String formSource;
    /**
     * 临时存储跳转路径
     */
    @TableField(value = "form_page_url")
    private String formPageUrl;
    /**
     * （云健康产品编码）
     */
    @TableField(value = "cloud_product_code")
    private String cloudProductCode;
    /**
     * 审核意见
     */
    @TableField(value = "examine_opinion")
    private String examineOpinion;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @TableField(value = "creater_id")
    private Long createrId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;
    /**
     * 更新人id
     */
    @TableField(value = "updater_id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "分配审核人")
    private Long reviewerUserId;

    @ApiModelProperty(value = "打印平台任务id")
    private String reportTaskId;

    @ApiModelProperty(value = "推荐模板ids")
    private String recommendTemplateIds;

    /**
     * 前端验证负责人
     */
    private Long identifierUserId;

    /*****************************新加字段 start***************************************/
    @ApiModelProperty(value = "表单分类：dict_formlib_standard分大类id")
    private Long formlibStandardId;

    @ApiModelProperty(value = "表单标准名称：dict_formlib_standard_type分标准字典id")
    private Long formlibStandardTypeId;

    @ApiModelProperty(value = "表单尺寸")
    private String formPaperSize;

    @ApiModelProperty(value = "调研方式")
    private String formSurveyMethod;

    @ApiModelProperty(value = "引用项目资源库标记id")
    private Long formlibResourceUniteId;

    /**
     * 导入云健康状态 0 默认 1 已下发
     */
    @ApiModelProperty(value = "导入云健康状态 0 默认 1 已下发")
    private Integer importCloudStatus;

    @ApiModelProperty(value = "导入云健康状态信息")
    private String importMsg;
    /*****************************新加字段 end***************************************/
}