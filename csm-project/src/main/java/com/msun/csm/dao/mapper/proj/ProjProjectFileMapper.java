package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.model.vo.ProjProjectFileVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */
@Mapper
public interface ProjProjectFileMapper extends RootMapper<ProjProjectFile> {
    int deleteByPrimaryKey(Long projectFileId);

    int insertOrUpdate(ProjProjectFile record);

    int insertOrUpdateSelective(ProjProjectFile record);

    int insertSelective(ProjProjectFile record);

    ProjProjectFile selectByPrimaryKey(Long projectFileId);

    int updateByPrimaryKeySelective(ProjProjectFile record);

    int updateByPrimaryKey(ProjProjectFile record);

    int updateBatch(List<ProjProjectFile> list);

    int updateBatchSelective(List<ProjProjectFile> list);

    int batchInsert(@Param ("list") List<ProjProjectFile> list);

    List<ProjProjectFileVO> findProjectFiles(
            @Param("projectInfoId") Long projectInfoId,
            @Param("milestoneNodeCode") String milestoneNodeCode,
            @Param("projectStageCode") String projectStageCode);

    List<ProjProjectFile> getProjectFilesByProjectInfoId(@Param("projectInfoId") Long projectInfoId);

    List<ProjProjectFile> selectProjectFileByIds(@Param("projectFileIds") List<Long> projectFileIds);
}
