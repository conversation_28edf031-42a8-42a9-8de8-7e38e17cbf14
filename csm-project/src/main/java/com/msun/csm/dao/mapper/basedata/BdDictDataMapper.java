package com.msun.csm.dao.mapper.basedata;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.dao.entity.basedata.BdDictData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/7 10:49
 */
@Mapper
@DS("bd")
public interface BdDictDataMapper extends RootMapper<BdDictData> {

    void insertUpdate(@Param("data")List<BdDictData> data);
}
