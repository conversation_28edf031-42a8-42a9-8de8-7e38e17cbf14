package com.msun.csm.dao.entity.basedata;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/6/17 11:06
 */
@ApiModel("基础数据kettle变量")
@Data
@TableName("csm.bd_kt_vars")
public class BdKtVars {
    public static final String SCHEMA = "schema"; //源数据所在的schema
    public static final String CHIS_HOST = "chis_host"; //云健康chis数据库域名
    public static final String CHIS_PORT = "chis_port"; //云健康chis数据库端口
    public static final String CHIS_USER = "chis_user"; //云健康chis用户名
    public static final String CHIS_PASSWORD = "chis_password"; //云健康chis密码
    public static final String CHISAPP_HOST = "chisapp_host"; //云健康chisapp域名
    public static final String CHISAPP_PORT = "chisapp_port"; //云健康chisapp端口
    public static final String CHISAPP_USER = "chisapp_user"; //云健康chisapp用户名
    public static final String CHISAPP_PASSWORD = "chisapp_password"; //云健康chisapp密码
    public static final String HOSPITAL_ID = "hospital_id"; //云健康医院ID(这个不存库)
    public static final String ORG_ID = "org_id"; //云健康机构ID（资格不存库）

    @TableId
    private Long id;
    private String varKey;
    private String varVal;
    private String projectNumber;
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();
}
