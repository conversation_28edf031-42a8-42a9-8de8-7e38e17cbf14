package com.msun.csm.dao.mapper.proj;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecord;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecordVO;

public interface ProductSatisfactionSurveyRecordMapper {

    List<ProductSatisfactionSurveyRecordVO> getProductSatisfactionRecord(Long projectInfoId);

    int saveProductSatisfactionSurveyRecord(ProductSatisfactionSurveyRecord record);

    int updateScoreById(@Param("id") Long id, @Param("score") BigDecimal score, @Param("scoreUserId") Long scoreUserId, @Param("remark") String remark);

    ProductSatisfactionSurveyRecord getProductSatisfactionRecordById(@Param("id") Long id);

    /**
     * 更新状态
     *
     * @param id         主键
     * @param status     状态 1.未发放 2.收集中 3.已停止
     * @param operatorId 操作人
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("operatorId") Long operatorId);


}




