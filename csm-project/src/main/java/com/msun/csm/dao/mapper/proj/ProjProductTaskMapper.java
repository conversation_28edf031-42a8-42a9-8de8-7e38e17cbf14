package com.msun.csm.dao.mapper.proj;


import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.resp.producttask.ProjProductTaskResp;
import com.msun.csm.model.tduck.req.TDuckTask;

/**
 * <AUTHOR>
 * @description 针对表【proj_product_task(产品待处理任务表)】的数据库操作Mapper
 * @createDate 2024-07-25 18:14:54
 * @Entity generator.domain.ProjProductTask
 */
public interface ProjProductTaskMapper extends RootMapper<ProjProductTask> {

    /**
     * 查询数据
     *
     * @param dto
     * @return
     */
    List<ProjProductTaskResp> selectProductTaskList(ProjProductTaskParam dto);

    /**
     * 查询文件
     *
     * @param dto
     * @return
     */
    List<ProjProjectFile> selectAllFlieList(ProjProductTaskResp dto);

    /**
     * 修改数据
     *
     * @param resultMap
     */
    int updateTaskStatus(Map<String, Object> resultMap);

    /**
     * 批量插入
     *
     * @param taskList
     */
    void insertBatch(@Param("taskList") List<ProjProductTask> taskList);

    /**
     * 删除数据
     *
     * @param projectInfoId
     * @param hospitalInfoId
     * @param yyProductIdList
     */
    void deleteByParam(@Param("projectInfoId") Long projectInfoId, @Param("hospitalInfoId") Long hospitalInfoId,
                       @Param("yyProductIdList") List<BaseIdNameResp> yyProductIdList);


    /**
     * 批量插入
     *
     * @param task
     */
    int insertOrUpdate(@Param("task") ProjProductTask task);

    /**
     * 删除数据
     *
     * @param projectInfoId
     * @param hospitalInfoId
     * @param taskList
     */
    int deleteDataByParam(@Param("projectInfoId") Long projectInfoId, @Param("hospitalInfoId") Long hospitalInfoId, @Param("yyProductIdList") List<TDuckTask> taskList);

    /**
     * 项目拆分合并处理数据
     *
     * @param oldProjectId
     * @param newProjectId
     * @param changeProductList
     */
    int updateByProjectId(@Param("oldProjectId") Long oldProjectId, @Param("newProjectId") Long newProjectId,
                          @Param("changeProductList") List<Long> changeProductList);

    /**
     * 根据项目id和产品id删除
     *
     * @param projectInfoId
     * @param oldProductIds
     * @return
     */
    int deleteByProjectAndProductIds(@Param("projectInfoId") Long projectInfoId,
                                     @Param("oldProductIds") List<Long> oldProductIds);

    int deleteByProjectHospitalProduct(@Param("projectInfoId") Long projectInfoId, @Param("hospitalInfoId") Long hospitalInfoId, @Param("yyProductId") Long yyProductId);
}
