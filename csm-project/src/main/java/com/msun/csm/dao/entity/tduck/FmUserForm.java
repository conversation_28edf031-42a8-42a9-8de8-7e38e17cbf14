package com.msun.csm.dao.entity.tduck;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.config.BooleanTypeHandler;
import com.msun.csm.common.enums.tduck.FormSourceTypeEnum;
import com.msun.csm.common.enums.tduck.FormStatusEnum;
import com.msun.csm.common.enums.tduck.FormTypeEnum;
import com.msun.csm.util.HtmlUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/7
 */

/**
 * 临时表 用来记录新老系统项目信息对照
 */
@ApiModel(description = "临时表 用来记录新老系统项目信息对照")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "tduckpro", value = "fm_user_form")
public class FmUserForm {

    private static final long serialVersionUID = 1L;

    /**
     * 新老系统项目信息对照主键ID
     */
    @TableId
    private Long id;

    /**
     * 表单code
     */
    private String formKey;

    /**
     * 来源ID
     */
    private String sourceId;

    /**
     * 表单来源
     */
    private FormSourceTypeEnum sourceType;

    /**
     * 表单名称
     */
    private String name;

    /**
     * 纯文本表单名称
     */
    private String textName;

    /**
     * 表单描述
     */
    private String description;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 表单类型
     */
    private FormTypeEnum type;

    /***
     * 状态
     */
    private FormStatusEnum status;

    @TableField(value = "is_deleted", typeHandler = BooleanTypeHandler.class)
    private Boolean deleted;

    /**
     * 是否是文件夹
     */
    @TableField(value = "is_folder", typeHandler = BooleanTypeHandler.class)
    private Boolean folder;

    /**
     * 父级文件夹ID
     */
    private Long folderId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 移除html标签
     *
     * @return 文本
     */
    public String getTextName() {
        if (StrUtil.isBlank(name)) {
            return null;
        }
        // 标题是富文本 去除html 标签
        return HtmlUtils.cleanHtmlTag(name);
    }

    /**
     * 运营平台产品ID
     */
    private String yunYingProductId;

    /**
     * 产品类型，部署的是产品还是产品中的某个模块：
     * module-模块；product-产品；none-数据初始化的默认值，是非法值
     */
    private String productType;

    /**
     * 客户类型：single-单体；area-区域；telemarketing-电销；none-数据初始化的默认值，是非法值
     */
    private String customerType;

    /**
     * 实施类型：lhx-老换新；xkh-新客户；none-数据初始化的默认值，是非法值
     */
    private String implementationType;

    /**
     * 新系统项目ID
     */
    private Long projectInfoId;

    /**
     * 是否为模板，-1：创建数据的默认值；0-是；1-不是
     */
    private Integer isTemplate;

    /**
     * 单体可用 0 否 1是
     */
    private Integer monomerFlag;
    /**
     * 区域可用 0 否，1是
     */
    private Integer regionFlag;
    /**
     * 老换新可用 0 否，1是
     */
    private Integer replaceOldNew;
    /**
     * 新客户可用 0 否，1是
     */
    private Integer newCustomer;


    private Long customInfoId;

    /**
     * 表单分类：1产品业务调研、2全院流程模拟、3满意度调查
     */
    private Integer formClass;

}
