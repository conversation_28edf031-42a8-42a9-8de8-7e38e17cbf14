package com.msun.csm.dao.entity.formlibnew;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 上线必备状态字典表
 *
 * @TableName dict_online_business_status
 */
@TableName(value = "dict_online_business_status", schema = "csm")
@Data
public class DictOnlineBusinessStatus extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * business_code
     */
    @TableField(value = "business_code")
    private String businessCode;

    /**
     * online_code
     */
    @TableField(value = "online_code")
    private Integer onlineCode;

    /**
     * status_class
     */
    @TableField(value = "status_class")
    private Long statusClass;

    /**
     * 字典分类名称
     */
    @TableField(value = "online_name")
    private String onlineName;

}