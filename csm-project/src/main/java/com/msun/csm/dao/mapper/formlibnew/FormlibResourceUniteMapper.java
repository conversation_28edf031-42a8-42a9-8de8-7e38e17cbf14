package com.msun.csm.dao.mapper.formlibnew;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDeliverToYjkDTO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductDataVO;
import com.msun.csm.dao.entity.formlibnew.FormlibResourceUnite;
import com.msun.csm.model.req.formlibnew.FormLibResourceUnitePageReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp;

/**
 * <AUTHOR>
 * @description 针对表【formlib_resource_unite(表单资源库)】的数据库操作Mapper
 * @createDate 2025-07-14 13:47:02
 * @Entity jiaofuceshi.autoceshi.FormlibResourceUnite
 */
public interface FormlibResourceUniteMapper extends BaseMapper<FormlibResourceUnite> {

    /**
     * 分页查询表单资源库数据
     * @param dto
     * @return
     */
    List<FormlibResourceUnitePageResp> findFormLibDataPage(FormLibResourceUnitePageReq dto);

    /**
     * 修改表单资源库数据
     * @param entity
     */
    void updateDataById(FormlibResourceUnite entity);

    /**
     * 查询表单资源库数据列表
     * @param dto
     * @return
     */
    List<FormlibResourceUnitePageResp> findFormLibDataList(FormlibResourceUniteSelectListReq dto);

    /**
     * 查询表单资源库数据列表
     * @param dto
     * @return
     */
    List<FormlibComeToProductDataVO> selectListByFormType(FormlibDeliverToYjkDTO dto);

    /**
     * 查询表单资源库数据列表
     * @param dto
     * @return
     */
    List<FormlibResourceUnite> selectBySurveyFormParam(FormlibResourceUniteSelectListReq dto);

    /**
     * 查询表单资源库数据列表(准备阶段设计列表用)
     * @param dto
     * @return
     */
    List<FormlibResourceUnitePageResp> findFormLibDataListToRecommend(FormlibResourceUniteSelectListReq dto);
}




