package com.msun.csm.dao.entity.basedata;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/7 10:26
 */
@ApiModel("字典类型")
@Data
@TableName(schema = "dict_data", value = "dict_type")
public class BdDictType {
    @TableId
    private Long id;
    private String dictName;
    private String dictCode;
    private String sourceSql;
    private Boolean opened;
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted = 0;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();
}
