package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


@Data
@TableName(schema = "csm", value = "dict_date_of_holidays")
public class DictDateOfHolidays implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 创建人id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    private Integer isDeleted;

    /**
     * 日期字符串
     */
    private String dateStr;

    /**
     * 日期
     */
    private Date dateInfo;

    /**
     * 星期中文描述，星期一，星期二
     */
    private String weekStr;

    /**
     * 星期，星期一为1，星期二为2，依此类推
     */
    private Integer weekInfo;

    /**
     * 是否为法定节假日，0-不是法定节假日；1-是法定节假日
     */
    private Integer holidaysFlag;

    /**
     * 是否为工作日(处理节假日调休)，0不是工作日；1-是工作日
     */
    private Integer workingDayFlag;

}
