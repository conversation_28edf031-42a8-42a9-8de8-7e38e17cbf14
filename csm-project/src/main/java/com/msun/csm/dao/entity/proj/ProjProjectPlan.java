package com.msun.csm.dao.entity.proj;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/13
 */

/**
 * 项目计划业务数据表
 */
@ApiModel(description = "项目计划业务数据表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjProjectPlan {
    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value = "主键")
    private Long projectPlanId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 计划阶段
     */
    @ApiModelProperty(value = "计划阶段")
    private Long projectPlanStageId;

    /**
     * 计划工作项
     */
    @ApiModelProperty(value = "计划工作项")
    private Long projectPlanItemId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date planTime;

    /**
     * 状态：0 未完成/1 已完成/2进行中
     */
    @ApiModelProperty(value = "状态：0 未完成/1 已完成/2进行中")
    private Integer status;

    /**
     * 完成数量 ，包含三种情况
     * 1产品维度来展示
     * 2任务总数展示-共xxx
     * 3接口等按照实际调研完成标准
     */
    @ApiModelProperty(value = "完成数量 ，包含三种情况,1产品维度来展示,2任务总数展示-共xxx,3接口等按照实际调研完成标准")
    private Integer completeCount;

    /**
     * 待办总数量
     */
    @ApiModelProperty(value = "待办总数量")
    private Integer totalCount;

    /**
     * 跳转类型：页面/通用-自定义详情或预置数据详情
     */
    @ApiModelProperty(value = "跳转类型：页面/通用-自定义详情或预置数据详情")
    private Integer jumpType;

    /**
     * 跳转地址
     */
    @ApiModelProperty(value = "跳转地址")
    private String jumpPath;

    /**
     * 工作项提出人
     */
    @ApiModelProperty(value = "工作项提出人")
    private Long submitterId;

    /**
     * 是否生成工作项自身对应的待办，本期默认都是1
     */
    @ApiModelProperty(value = "是否生成工作项自身对应的待办，本期默认都是1")
    private Integer generateTodoFlag;

    /**
     * 类型  1自定义的 2预置的
     */
    @ApiModelProperty(value = "类型  1自定义的 2预置的")
    private Integer planType;

    /**
     * 重点关注标识 0 未重点关注 1 重点关注
     */
    @ApiModelProperty(value = "重点关注标识 0 未重点关注 1 重点关注")
    private Integer attentionFlag;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 是否存在紧前工作项 pro_node_flag
     */
    @ApiModelProperty(value = "是否存在紧前工作项 pro_node_flag")
    private Integer priorProjectPlanItemFlag;

    /**
     * 紧前工作项id，多个pro_node_id
     */
    @ApiModelProperty(value = "紧前工作项id，多个pro_node_id")
    private String priorProjectPlanItemId;

    /**
     * 实施工程师-前端工程师
     */
    @ApiModelProperty(value = "实施工程师-前端工程师")
    private Long implementationEngineerId;

    /**
     * 后端工程师
     */
    @ApiModelProperty(value = "后端工程师")
    private Long backendEngineerId;

    /**
     * 是否需要配置前端负责人
     */
    @ApiModelProperty(value = "是否需要配置前端负责人")
    private Integer frontFlag;

    /**
     * 是否需要配置后端负责人
     */
    @ApiModelProperty(value = "是否需要配置后端负责人")
    private Integer backendFlag;

    /**
     * 项目计划工作项code
     */
    @TableField(exist = false)
    private String planItemCode;

    /**
     * 项目计划工作项的完成时间
     */
    private Date completionTime;

    /**
     * 是否为里程碑，0不是里程碑；1是里程碑
     */
    private Integer milestoneFlag;

    /**
     * 项目计划的完成人
     */
    private Long completionUserId;
}
