package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/9 10:05
 */
@ApiModel("预警单和罚款单预发送记录")
@Data
@TableName("csm.early_warning_and_penalty_messages")
public class EarlyWarningAndPenaltyMessages extends BasePO {
    /**
     * 预警单和罚款单预发送记录主键
     */
    private Long earlyWarningAndPenaltyMessagesId;

    /**
     * 如果是罚单，则该字段为0；如果是预警单，则该字段为罚款单的主键；该字段的目的是假如刚分配完责任人，就到了发送时间了，那么就要将该罚单对应的预警消息一并发出
     */
    private Long penaltyEarlyWarningAndPenaltyMessagesId = 0L;

    /**
     * 项目ID
     */
    private Long projectInfoId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 项目编号
     */
    private String projectNumber;

    /**
     * 项目审核模式配置表主键
     */
    private Long projectReviewId;

    /**
     * 处罚人的sys_user_id
     */
    private Long penaltyPersonId;
    /**
     * 处罚原因
     */
    private String penaltyReason;

    /**
     * penalty_amount
     */
    private BigDecimal penaltyAmount;

    /**
     * 处罚时间戳到毫秒
     */
    private Date penaltyTime;
    /**
     * 处罚类别【warning：预警单；penalty：罚款单】
     */
    private String penaltyCategory;
    /**
     * 处罚考核的标准开始时间
     */
    private Date penaltyStartTime;
    /**
     * 处罚考核的标准完成时间
     */
    private Date penaltyEndTime;
    /**
     * 是否排除节假日【true：排除；false：不排除】
     */
    private Boolean excludeHolidaysFlag;
    /**
     * 是否排除周末【true：排除；false：不排除】
     */
    private Boolean excludeWeekendsFlag;
    /**
     * 是否排除非工作时间【true：排除；false：不排除】
     */
    private Boolean excludeNonWorkingHoursFlag;
    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 发送时间到分钟：2025-06-03 23:30
     */
    private String sendTimeStr;
    /**
     * 是否已发送【true：已发送；false：未发送】
     */
    private Boolean sendFlag;
    /**
     * 处罚性质【1：一次性处罚；2：周期性处罚】
     */
    private Integer penaltyType;
    /**
     * 处罚周期单位：秒、分、时、天
     */
    private String penaltyPeriodUnit;

    /**
     * 处罚周期【0：一次性处罚（penalty_type值为1时）；大于0：处罚周期天数（penalty_type值为2时）】
     */
    private String penaltyPeriod;

    /**
     * 处罚前N（分钟/小时/天）发送预警，逗号分割数字
     */
    private String warningBeforePenalty;
    /**
     * 项目审核类型编码，取dict_project_review_type的dict_code字段值
     */
    private String reviewTypeCode;
    /**
     * 业务表主键，比如产品业务调研、报表调研等主键
     */
    private Long businessId;
    /**
     * 业务表名称，比如产品业务调研、报表调研等表名
     */
    private String businessTable;
    /**
     * 业务名称，比如产品业务调研、报表调研等名称，发送消息需要携带
     */
    private String businessName;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 调用运营平台出罚单/预警单的funValue参数，可以区分罚款金额及处罚场景，是监控点monitorPoint的下级分类
     */
    private String yyFunValue;
    /**
     * 调用运营平台出罚单/预警单的monitorPoint参数，可以区分罚款金额及处罚场景
     */
    private String yyMonitorPoint;
}
