package com.msun.csm.dao.mapper.config;


import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.config.ConfigOnlineStep;
import com.msun.csm.model.dto.ConfigOnlineStepSelectDTO;
import com.msun.csm.model.vo.ConfigOnlineStepVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/22
 */
@Mapper
public interface ConfigOnlineStepMapper extends BaseMapper<ConfigOnlineStep> {

    /**
     * 查询上线步骤配置信息【二级分类】
     * @param dto
     * @return
     */
    List<ConfigOnlineStepVO> selectOnlineStepList(ConfigOnlineStepSelectDTO dto);

    /**
     * 根据上线步骤编码获取配置的上线步骤列表
     *
     * @param onlineStepCode 上线步骤编码
     * @return 上线步骤列表
     */
    List<ConfigOnlineStep> getConfigOnlineStepListByStepCode(@Param("onlineStepCode") String onlineStepCode);


}
