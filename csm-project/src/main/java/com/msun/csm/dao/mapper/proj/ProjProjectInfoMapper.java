package com.msun.csm.dao.mapper.proj;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.DailyReportProjectInfoVO;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.sys.DeptUserComp;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.req.project.ProjectSelectNotOnlineReq;
import com.msun.csm.model.resp.project.ProjectFiscalYearResp;
import com.msun.csm.model.resp.project.ProjectNotOnlineResp;
import com.msun.csm.model.resp.project.ProjectThisWeekResp;
import com.msun.csm.model.vo.ProjProjectInfoVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */
@Mapper
public interface ProjProjectInfoMapper extends BaseMapper<ProjProjectInfo> {

    @Deprecated
    int deleteByPrimaryKey(Long projectInfoId);

    int insert(ProjProjectInfo record);

    @Deprecated
    int insertOrUpdate(ProjProjectInfo record);

    @Deprecated
    int insertOrUpdateSelective(ProjProjectInfo record);

    @Deprecated
    int insertSelective(ProjProjectInfo record);

    ProjProjectInfo selectByPrimaryKey(Long projectInfoId);

    int updateByPrimaryKeySelective(ProjProjectInfo record);

    int updateByPrimaryKey(ProjProjectInfo record);

    @Deprecated
    int updateBatch(List<ProjProjectInfo> list);

    @Deprecated
    int updateBatchSelective(List<ProjProjectInfo> list);

    @Deprecated
    int batchInsert(@Param("list") List<ProjProjectInfo> list);

    /**
     * 查询项目信息
     *
     * @param dto
     * @return
     */
    List<ProjProjectInfoVO> findProjectInfo(ProjProjectInfoDTO dto);

    List<ProjProjectInfoVO> findMemberProjectInfo(ProjProjectInfoDTO dto);

    List<ProjProjectInfoVO> findDataRangeProjectInfo(ProjProjectInfoDTO dto);

    /**
     * 根据项目团队id查询所在部门及人员相关信息
     *
     * @param projectInfoId
     * @return
     */
    List<DeptUserComp> selectDeptUserCompByProjInfoId(@Param("projectInfoId") long projectInfoId);

    /**
     * 根据项目主键查询项目负责人信息
     *
     * @param projectInfoId
     * @return
     */
    SysUser getSysUserByProjInfoId(@Param("projectInfoId") long projectInfoId);

    /**
     * 条件查询
     *
     * @param project
     * @return
     */
    List<ProjProjectInfo> selectByParam(ProjProjectInfo project);


    /**
     * 通过运营平台工单id查询项目id
     *
     * @param yyOrderId
     * @return
     */
    List<Long> getProjectInfoIdByOrderId(@Param("yyOrderId") Long yyOrderId);


    /**
     * 查询当前项目下的客户是否存在已上线项目
     */
    List<ProjProjectInfo> getOnlineFlag(@Param("projectInfoId") Long projectInfoId);


    /**
     * @param hospitalInfoId
     * @return
     */
    List<ProjProjectInfo> selectByHospital(@Param("hospitalInfoId") Long hospitalInfoId);

    /**
     * 查询新项目下对照的老项目信息
     *
     * @param projectInfoId
     * @return
     */
    ProjProjectInfoVO selectProjNewAndOldByNewProjId(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据项目类型和客户id查询该客户之前所有项目，不包含参数项目本身
     *
     * @param projectInfo
     * @return
     */
    List<ProjProjectInfo> findByCustomAndProjectType(ProjProjectInfo projectInfo);


    /**
     * 根据工单号查询项目信息
     *
     * @param orderNumber
     * @return
     */
    ProjProjectInfo findByOrderNumber(@Param("orderNumber") String orderNumber);

    /**
     * 根据项目id集合查询项目信息
     *
     * @param ids
     * @return
     */
    List<ProjProjectInfo> selectByIds(List<Long> ids);

    /**
     * 查询上线风险项目
     *
     * @return
     */
    List<ProjProjectInfo> queryOnlineRiskProjects();

    /**
     * 查询验收风险项目
     *
     * @return
     */
    List<ProjProjectInfo> queryAcceptRiskProjects();

    /**
     * 查询超时数据
     *
     * @param projectInfoId
     * @return
     */
    Integer queryAcceptanceExceedTimeLimit(@Param("projectInfoId") Long projectInfoId,
                                           @Param("hospitalInfoId") Long hospitalInfoId,
                                                   @Param("code") String code);

    void updateDataById(@Param("oldId") Long oldId, @Param("newOldId") Long newOldId, @Param("deptId") Long deptId);

    /**
     * 根据工单号删除项目信息
     *
     * @param orderInfoId
     * @return
     */
    Integer deleteByOrderInfoId(Long orderInfoId);

    /**
     * 说明: 根据客户id修改
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param orderInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId,
                             @Param("orderInfoIds") List<Long> orderInfoIds);

    /**
     * 说明: 根据工单id查询项目信息
     *
     * @param orderInfoId
     * @return
     */
    ProjProjectInfo selectByOrderId(Long orderInfoId);

    /**
     * 根据旧项目id查询项目信息
     *
     * @param oldId
     * @param newOldId
     * @return
     */
    List<ProjProjectMember> selectDataListByIds(@Param("oldId") Long oldId, @Param("newOldId") Long newOldId);

    /**
     * 说明: 根据工单id列表查询项目信息
     *
     * @param orderInfoIdList
     * @return
     */
    List<ProjProjectInfo> selectByOrderInfoIdList(@Param("orderInfoIdList") List<Long> orderInfoIdList);

    /**
     * 说明: 根据医院id查询项目信息  首期项目，用于项目开启报表、表单限制
     *
     * @param hospitalInfoId
     * @return
     */
    List<ProjProjectInfo> selectProjectListByHospitalId(@Param("hospitalInfoId") Long hospitalInfoId);

    /**
     * 说明: 根据项目id修改项目计划标识
     *
     * @param projectInfoId
     * @return
     */
    int updateProjectPlanFlag(Long projectInfoId);

    int initProjectPlanFlag(Long projectInfoId);

    /**
     * 查询未完成调研的项目
     * @param startTime
     * @return
     */
    List<ProjProjectInfo> findSurveyNotComplete(@Param("startTime") Date startTime);

    /**
     * 获取开启了产品业务调研审核的项目
     *
     * @return 开启了产品业务调研审核的项目
     */
    List<ProjProjectInfo> getOpenSurveyProductAudit();

    List<DailyReportProjectInfoVO> selectDailyReportProject(@Param("projectLeaderId") Long projectLeaderId, @Param("deliverStatusList") List<Integer> deliverStatusList);

    /**
     * 根据项目组长id查询未验收项目信息
     * @param projectLeaderId
     * @return
     */
    List<ProjProjectInfo> selectListByLeaderId(@Param("projectLeaderId") Long projectLeaderId);

    /**
     * 查询未上线项目
     * @param req
     * @return
     */
    List<ProjectNotOnlineResp> selectNotOnlineProjectList(ProjectSelectNotOnlineReq req);

    /**
     * 查询财年项目
     * @param req
     * @return
     */
    ProjectFiscalYearResp selectYearFiscalProjectList(ProjectSelectNotOnlineReq req);

    /**
     * 查询本周项目
     * @param req
     * @return
     */
    ProjectThisWeekResp selectThisWeekProjectList(ProjectSelectNotOnlineReq req);

    /**
     * 查询已上线项目
     * @param req
     * @return
     */
    List<ProjectNotOnlineResp> selectOnlineProjectList(ProjectSelectNotOnlineReq req);

    /**
     * 查询是否开启了三方接口限制
     * @param customInfoId
     * @return
     */
    Integer queryOpenCustomDataFlowLimit(@Param("customInfoId") Long customInfoId);

    /**
     * 根据医院id查询项目信息
     * @param hospitalId
     * @return
     */
    ProjProjectInfo selectByHospitalIdOne(@Param("hospitalId") Long hospitalId);

    /**
     * 查询首付款是否支付信息
     * @param info
     * @return
     */
    ProjProjectInfoVO selectPaymentInfo(ProjProjectInfoVO info);

    /**
     * 查询项目信息
     * @param projectInfoId
     * @return
     */
    List<ProjProjectInfo> selectByProjectLists(@Param("projectInfoId") Long projectInfoId);
}
