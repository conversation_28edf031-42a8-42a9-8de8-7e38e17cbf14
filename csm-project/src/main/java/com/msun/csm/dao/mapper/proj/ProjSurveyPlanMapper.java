package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.model.dto.GetSurveyPlanAuditListParam;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.dto.SurveyPlanAuditInfo;
import com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq;
import com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/19
 */
@Mapper
public interface ProjSurveyPlanMapper extends BaseMapper<ProjSurveyPlan> {

    int insert(ProjSurveyPlan record);

    ProjSurveyPlan selectByPrimaryKey(Long surveyPlanId);

    int updateByPrimaryKeySelective(ProjSurveyPlan record);

    int batchInsert(@Param("list") List<ProjSurveyPlan> list);

    /**
     * 查询项目下的所有计划
     *
     * @param dto@return
     */
    List<SurveyPlanTaskResp> findSurveyPlan(ProjSurveyPlanDTO dto);

    List<Long> findYyProductIdByProjectInfoId(Long projectInfoId);
    /**
     * 查询项目下的实施产品
     *
     * @param projectInfoId
     * @param completeStatus
     * @return
     */
    List<ProjSurveyPlan> findBranchHospitalSurveyPlan(@Param("projectInfoId") Long projectInfoId, @Param("completeStatus") Integer completeStatus);
    /**
     *
     *
     * @return
     */
    List<ProjSurveyPlan> findNotNeedSurveyPlan();
    /**
     * 说明: 获取项目调研计划任务-医院列表
     *
     * @param dto
     * @return:java.util.List<com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO>
     * @author: Yhongmin
     * @createAt: 2024/8/29 11:34
     * @remark: Copyright
     */
    List<SurveyPlanHospitalProductVO> findSurveyPlanHospitalProduct(ProjSurveyPlanDTO dto);

    /**
     * 更新计划状态
     *
     * @param updateSurveyPlanStatus
     * @return
     */
    int updateSurveyPlanStatus(UpdateSurveyPlanStatusReq updateSurveyPlanStatus);

    /**
     * 根据项目id更新计划状态
     *
     * @param oldProjectId
     * @param newProjectId
     * @param changeProductList
     */
    void updateByProjectId(@Param("oldProjectId") Long oldProjectId, @Param("newProjectId") Long newProjectId,
                           @Param("changeProductList") List<Long> changeProductList);

    /**
     * 查询已经进行调研的医院数量
     * @param projectInfoId
     * @return
     */
    Long selectHospitalPlanCount(@Param("projectInfoId") Long projectInfoId);

    /**
     * 查询某个项目下某个医院的调研计划
     * @param projectInfoId
     * @param hospitalInfoId
     * @return
     */
    List<ProjSurveyPlan> findSurveyPlanByProjectAndHospital(@Param("projectInfoId") Long projectInfoId,
                                                            @Param("hospitalInfoId") Long hospitalInfoId);

    /**
     * 查询分院模式下已分配调研产品的数据
     * @param projectInfoId
     * @return
     */
    List<Long> findIsBranchOnlineYyProductIdByProjectInfoId(Long projectInfoId);

    /**
     * 说明: 根据客户id修改调研计划
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param projectInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId,
                             @Param("projectInfoIds") List<Long> projectInfoIds);

    /**
     * 说明: 删除调研计划
     * @param projectInfoId
     * @param oldProductIds
     */
    int deleteByProjectAndProductIds(@Param("projectInfoId") Long projectInfoId,
                                      @Param("oldProductIds") List<Long> oldProductIds);

    List<SurveyPlanAuditInfo> getSurveyPlanAuditList(GetSurveyPlanAuditListParam param);
}
