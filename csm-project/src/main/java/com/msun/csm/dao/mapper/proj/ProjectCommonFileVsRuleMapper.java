package com.msun.csm.dao.mapper.proj;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule;
import com.msun.csm.model.param.ProjectCommonFileVsRuleParam;

public interface ProjectCommonFileVsRuleMapper {

    int addProjectCommonFile(ProjectCommonFileVsRule record);

    ProjectCommonFileVsRule selectByPrimaryKey(@Param("projectCommonFileId") Long projectCommonFileId);

    int updateByPrimaryKeySelective(ProjectCommonFileVsRule record);

    ProjectCommonFileVsRule selectByParam(ProjectCommonFileVsRuleParam record);

    int updateProjectFileIds(@Param("projectCommonFileId") Long projectCommonFileId, @Param("projectFileIds") String projectFileIds);

    int updateUseFlag(@Param("projectCommonFileId") Long projectCommonFileId, @Param("useFlag") Boolean useFlag);

    /**
     * 更新检测结果及检测结果说明
     *
     * @param projectCommonFileId 主键
     * @param checkResult         检测结果：1-未检测；2-检测通过；3-检测不通过
     * @param checkResultText     检测结果说明
     */
    int updateCheckInfoById(@Param("projectCommonFileId") Long projectCommonFileId, @Param("checkResult") Integer checkResult, @Param("checkResultText") String checkResultText);
}
