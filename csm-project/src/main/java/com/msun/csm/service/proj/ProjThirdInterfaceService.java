package com.msun.csm.service.proj;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictInterface;
import com.msun.csm.dao.entity.dict.DictInterfaceFirm;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjTaskProgress;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.entity.proj.ThirdInterfaceMemberDept;
import com.msun.csm.model.dto.CreateAppAuthorizationDTO;
import com.msun.csm.model.dto.InterfaceMemberParamDTO;
import com.msun.csm.model.dto.InterfaceReviewPageDTO;
import com.msun.csm.model.dto.ProjThirdInterfaceDTO;
import com.msun.csm.model.dto.ProjThirdInterfacePageDTO;
import com.msun.csm.model.dto.SaveDeployDeviceInfoDTO;
import com.msun.csm.model.imsp.ThirdInterfaceDTO;
import com.msun.csm.model.imsp.ThirdInterfaceVO;
import com.msun.csm.model.req.thirdinterface.CheckInterfaceReq;
import com.msun.csm.model.req.thirdinterface.GetInterfaceApplyDetailReq;
import com.msun.csm.model.req.thirdinterface.InterfaceBaseReq;
import com.msun.csm.model.req.thirdinterface.UpdateAuthFileReq;
import com.msun.csm.model.vo.CheckAppVO;
import com.msun.csm.model.vo.DeployDeviceInfoVO;
import com.msun.csm.model.vo.InterfaceReviewVO;
import com.msun.csm.model.vo.ProjInterfaceRecordLogVo;
import com.msun.csm.model.vo.ProjThirdInterfaceVO;
import com.msun.csm.model.vo.ThirdInterfaceEntryCheckVO;
import com.msun.csm.model.vo.user.SysUserVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */

public interface ProjThirdInterfaceService {

    int deleteByPrimaryKey(Long thirdInterfaceId);


    int insertOrUpdate(ProjThirdInterface record);

    int insertOrUpdateSelective(ProjThirdInterface record);

    int insertSelective(ProjThirdInterface record);

    ProjThirdInterface selectByPrimaryKey(Long thirdInterfaceId);

    int updateByPrimaryKeySelective(ProjThirdInterface record);

    int updateByPrimaryKey(ProjThirdInterface record);

    int updateBatch(List<ProjThirdInterface> list);

    int updateBatchSelective(List<ProjThirdInterface> list);

    int batchInsert(List<ProjThirdInterface> list);

    /**
     * 查询接口名称字典数据
     *
     * @return
     */
    Result<List<DictInterface>> selectDictInterface();

    /**
     * 查询接口厂商字典数据
     *
     * @return
     */
    Result<List<DictInterfaceFirm>> selectDictInterfaceFirm();

    /**
     * 查询接口产品字典数据
     *
     * @return
     */
    Result<List<DictProduct>> selectDictProductForInterface();

    /**
     * 分页查询三方接口信息
     *
     * @param dto
     * @return
     */
    Result<PageInfo<ProjThirdInterfaceVO>> selectByPage(ProjThirdInterfacePageDTO dto);

    /**
     * 保存接口的进度详情
     *
     * @param taskProgress
     * @return
     */
    Result saveInterfaceTaskProgress(ProjTaskProgress taskProgress);

    /**
     * 保存三方接口信息
     *
     * @param dto
     * @return
     */
    Result saveInterface(ProjThirdInterfaceDTO dto);

    /**
     * 修改/裁定三方接口信息【根据前端传入的标识区分 modifyFlag】
     *
     * @param dto
     * @return
     */
    Result updateInterface(ProjThirdInterfaceDTO dto);

    /**
     * 三方接口导入模板下载
     *
     * @param response
     * @param projectInfoId
     */
    void downloadThirdInterfaceTemplate(HttpServletResponse response, Long projectInfoId);


    /**
     * 下载模板
     *
     * @param req
     * @param response
     */
    void downloadAuthTemplate(InterfaceBaseReq req, HttpServletResponse response);

    /**
     * 导入三方接口信息
     *
     * @param file
     * @param customInfoId
     * @param projectType
     * @param projectInfoId
     * @return
     */
    Result excelImportThirdInterface(MultipartFile file, Long customInfoId, Integer projectType, Long projectInfoId);

    /**
     * 三方接口导出excel
     *
     * @param response
     * @param dto
     */
    void thirdInterfaceExportExcel(HttpServletResponse response, ProjThirdInterfaceDTO dto);

    /**
     * 更新授权文件
     *
     * @param req
     * @return
     */
    Result updateAuthLetterFiles(UpdateAuthFileReq req);

    /**
     * 查询三方接口明细数据
     *
     * @param thirdInterfaceId
     * @return
     */
    Result<ProjThirdInterfaceVO> selectThirdInterfaceDetail(Long thirdInterfaceId);

    /**
     * 删除接口
     *
     * @param thirdInterfaceIdList
     * @return
     */
    Result deleteInterfaceById(List<Long> thirdInterfaceIdList);

    /**
     * 指定责任人，当责任人属于接口分公司时。转到运营平台走接口实施流程
     *
     * @param dto
     * @return
     */
    Result updateDirPerson(ProjThirdInterfaceDTO dto);

    /**
     * 接口申请裁定
     *
     * @param thirdInterfaceIdList
     * @return
     */
    Result interfaceApplyRuled(List<Long> thirdInterfaceIdList);


    /**
     * 接口裁定列表分页查询
     *
     * @param dto
     * @return
     */
    Result<PageInfo<InterfaceReviewVO>> selectPageForReviewInterface(InterfaceReviewPageDTO dto);

    /**
     * 接口-裁定/驳回/转评审/转需求
     *
     * @param req
     * @return
     */
    Result checkInterface(@Valid CheckInterfaceReq req);

    /**
     * 接口-授权
     *
     * @param thirdInterfaceIds
     * @return
     */
    Result authInterFace(@Valid List<Long> thirdInterfaceIds);

    /**
     * 检测接口测试环境是否已完成
     *
     * @param item
     * @return
     */
    Result<CheckAppVO> checkApp(GetInterfaceApplyDetailReq item);

    /**
     * 三方接口调研阶段完成
     *
     * @param milestoneInfoId
     * @return
     */
    Result thirdInterfaceSurveyFinish(Long milestoneInfoId);

    /**
     * 三方接口入驻检查
     *
     * @param projectInfoId
     * @return
     */
    Result<ThirdInterfaceEntryCheckVO> thirdInterfaceEntryCheck(Long projectInfoId);

    /**
     * 三方接口准备阶段完成
     *
     * @param milestoneInfoId
     * @return
     */
    Result thirdInterfacePrepareFinish(Long milestoneInfoId);

    /**
     * 三方接口老系统数据处理
     *
     * @param projectInfoId
     * @param thirdInterfaceId
     * @return
     */
    Result imspInterfaceForNew(Long projectInfoId, Long thirdInterfaceId);

    /**
     * 老系统接口文件上传补偿接口
     *
     * @param thirdInterfaceId
     * @return
     */
    Result thirdInterfaceFileUpload(Long thirdInterfaceId);

    /**
     * 查询三方接口操作日志
     *
     * @param thirdInterfaceId
     * @return
     */
    Result<List<ProjInterfaceRecordLogVo>> selectInterfaceRecordLog(Long thirdInterfaceId);

    /**
     * 部署申请授权
     *
     * @param dto
     * @return
     */
    Result saveDeployDeviceInfo(SaveDeployDeviceInfoDTO dto);

    /**
     * 申请国密授权
     *
     * @param dto
     * @return
     */
    Result createAppAuthorization(CreateAppAuthorizationDTO dto);

    /**
     * 修改数据集
     *
     * @param dto
     * @return
     */
    Result updateDataSet(ProjThirdInterfaceDTO dto);

    /**
     * 查询部署申请信息
     *
     * @param thirdInterfaceId
     * @return
     */
    Result<SaveDeployDeviceInfoDTO> getDeployDeviceInfo(Long thirdInterfaceId);


    /**
     * 下载SDK
     *
     * @param fileCode
     * @return
     */
    Result<String> downloadSDK(String fileCode);

    /**
     * 运维平台查询交付平台三方接口列表
     * @param hospitalId
     * @return
     */
    Result<List<ThirdInterfaceVO>> findThirdInterfaceList(Long hospitalId);

    /**
     * 保存运维平台反馈单与交付平台三方接口对照关系
     * @param dto
     */
    Result saveInterfaceRelation(ThirdInterfaceDTO dto);

    /**
     * 运维平台反馈单完成状态回写
     * @param dto
     */
    Result completeFeeback(ThirdInterfaceDTO dto);


    /**
     * 操作手册
     *
     * @param fileCode
     * @return
     */
    Result<String> operateDoc(String fileCode);

    /**
     * 更新三方合同文件
     *
     * @param req
     * @return
     */
    Result updateContractFiles(UpdateAuthFileReq req);

    /**
     * 验证三方接口
     *
     * @param dto
     * @return
     */
    Result verifyInterface(ProjThirdInterfaceDTO dto);

    /**
     * 获取三方接口授权服务器设备信息
     * @param dto
     * @return
     */
    Result<List<DeployDeviceInfoVO>> findDeployDeviceInfo(ProjThirdInterfaceDTO dto);

    /**
     * 查询三方接口在运维平台是否是后端运维的客户
     * @param dto
     * @return
     */
    Result selectInterfaceInOtherSystemInfo(ProjThirdInterfaceDTO dto);


    ThirdInterfaceMemberDept thirdInterfaceMemberDept(ProjectInfoId param);

    /**
     * 查询三方接口分配时的负责人
     *
     * @param dto 参数
     * @return 分配三方接口时的负责人
     */
    Result<PageInfo<SysUserVO>> thirdInterfaceMember(InterfaceMemberParamDTO dto);

    /**
     * 后端运维查询页面
     * @param dto
     * @return
     */
    Result<PageInfo<ProjThirdInterfaceVO>> selectByPageToMake(ProjThirdInterfacePageDTO dto);

    /**
     * 查询接口分类
     * @return
     */
    Result<List<BaseIdCodeNameResp>> selectInterfaceCategory();
}
