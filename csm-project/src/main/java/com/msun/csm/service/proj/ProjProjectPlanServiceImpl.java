package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseDataExtra;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProjectPlanItem;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneManualUpdateLog;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjProjectPlanVO;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.dao.entity.proj.QueryProjectPlanParam;
import com.msun.csm.dao.entity.proj.UpdateProjectPlanParam;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity;
import com.msun.csm.dao.mapper.config.ConfigProjectPlanOperationMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjIssueInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneManualUpdateLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.dao.mapper.proj.ProjTodoTaskMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ProjStatisticalReportMainMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.project.UpdateProjectPlanReq;
import com.msun.csm.model.req.projectplan.QueryProjectPlanInfoReq;
import com.msun.csm.model.req.projectplan.QueryProjectPlanReq;
import com.msun.csm.model.req.projectplan.SaveProjectPlanReq;
import com.msun.csm.model.resp.issue.IssueDataResp;
import com.msun.csm.model.resp.projectplan.OperationResp;
import com.msun.csm.model.resp.projectplan.ProjectPlanResp;
import com.msun.csm.model.struct.StatisticalReportMethodModel;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.dict.DictBusinessStatusService;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.ValidateUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/6
 */

@Slf4j
@Service
public class ProjProjectPlanServiceImpl implements ProjProjectPlanService {

    @Resource
    private ProjProjectPlanMapper projectPlanMapper;
    @Resource
    private UserHelper userHelper;
    @Resource
    private ConfigProjectPlanOperationMapper configProjectPlanOperationMapper;
    @Resource
    private ProjIssueInfoMapper issueInfoMapper;
    @Resource
    private ProjTodoTaskMapper todoTaskMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;
    @Resource
    private DictProjectPlanStageMapper dictProjectPlanStageMapper;

    @Resource
    private ProjProjectPlanBaseService projProjectPlanBaseService;

    @Lazy
    @Resource
    private ProjMilestoneManualUpdateLogMapper projMilestoneManualUpdateLogMapper;

    @Resource
    private ProjCustomInfoService customInfoService;
    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;
    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;
    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;
    @Resource
    private DictBusinessStatusService dictBusinessStatusService;
    @Resource
    private ProjStatisticalReportMainMapper projStatisticalReportMainMapper;
    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private ProjProductDeliverRecordService projProductDeliverRecordService;

    @Resource
    private ProjSurveyPlanService projSurveyPlanService;

    @Resource
    private ProjTodoTaskService todoTaskService;

    @Resource
    private ProjProjectConfigService projectConfigService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjProductBacklogService productBacklogService;

    @Resource
    @Lazy
    private BaseQueryService baseQueryService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    @Lazy
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private DictProjectPlanItemMapper dictProjectPlanItemMapper;

    @Resource
    private ProjBusinessExamineLogService projBusinessExamineLogService;

    @Override
    public int deleteByPrimaryKey(Long projectPlanId) {
        return projectPlanMapper.deleteByPrimaryKey(projectPlanId);
    }


    @Override
    public ProjProjectPlan selectByPrimaryKey(Long projectPlanId) {
        return projectPlanMapper.selectByPrimaryKey(projectPlanId);
    }

    @Override
    public int batchInsert(List<ProjProjectPlan> list) {
        return projectPlanMapper.batchInsert(list);
    }

    /**
     * 分页查询项目计划列表
     *
     * @param req
     * @return
     */
    @Override
    public Result<List<ProjectPlanResp>> queryData(QueryProjectPlanReq req) {
        log.info("查询项目计划列表,参数-{}", JSON.toJSONString(req));
        List<ProjectPlanResp> planResps = projectPlanMapper.queryData(req);
//        log.warn("查询总数量：{},查询项目计划列表筛选filter前数量：{}", total, CollUtil.size(planResps));
//        MybatisUtils.printSql("com.msun.csm.dao.mapper.proj.ProjProjectPlanMapper.queryDataByProjectInfoId", new HashMap<String, Object>() {{
//            put("projectInfoId", req.getProjectInfoId());
//        }});
        if (!CollectionUtils.isEmpty(planResps) && req.getExecutorId() != null) {
            planResps = planResps.stream().filter(item -> {
                // 是前端计划
                if (Integer.valueOf(1).equals(item.getFrontFlag())) {
                    if (req.getExecutorId().equals(item.getImplementationEngineerId())) {
                        return true;
                    }
                }
                if (Integer.valueOf(1).equals(item.getBackendFlag())) {
                    return req.getExecutorId().equals(item.getBackendEngineerId());
                }
                return false;
            }).collect(Collectors.toList());
        }
        log.warn("查询项目计划列表筛选filter后数量：{}", CollUtil.size(planResps));
        planResps.forEach(resp -> {
            //查询右键操作
            List<OperationResp> operationList = configProjectPlanOperationMapper.getOperationListByPlan(resp);
            if (resp.getAttentionFlag() == 1) {
                operationList.stream().filter(a -> "重点关注".equals(a.getName())).findFirst().get().setName("取消关注");
            }
            resp.setOperationList(operationList);
            //问题数据查询
            QueryIssueReq issueReq = new QueryIssueReq();
            issueReq.setProjectInfoId(resp.getProjectInfoId());
            issueReq.setProjectPlanId(resp.getProjectPlanId());
            List<IssueDataResp> issueDataResps = issueInfoMapper.queryData(issueReq);
            String relatedIssue = "0/0";
            if (issueDataResps != null && !issueDataResps.isEmpty()) {
                Long completedIssue = issueDataResps.stream().filter(a -> a.getStatus() == 8 || a.getStatus() == 9).count();
                relatedIssue = String.format("%d/%d", completedIssue, issueDataResps.size());
            }
            resp.setRelatedIssue(relatedIssue);
            // 查询小前端大后端开启报表、表单审核设置比例
            getProgressDisplayMode(resp);
            // 0/0的不展示
            if (Integer.valueOf(0).equals(resp.getCompleteCount()) && Integer.valueOf(0).equals(resp.getTotalCount())) {
                resp.setProgressDisplayMode("none");
            }
        });
        log.warn("查询项目计划列表筛选forEach数量：{}", CollUtil.size(planResps));
        this.dealStageStatus(planResps);
        log.warn("查询项目计划列表筛选dealStageStatus数量：{}", CollUtil.size(planResps));
        // 只查询待分配
        if (Integer.valueOf(1).equals(req.getUndistributed())) {
            planResps = planResps.stream().filter(item -> (Integer.valueOf(1).equals(item.getFrontFlag()) && StringUtils.isBlank(item.getFrontExecutorName())) || (Integer.valueOf(1).equals(item.getBackendFlag()) && StringUtils.isBlank(item.getBackendExecutorName()))).collect(Collectors.toList());
            log.warn("查询项目计划列表筛选if-filter数量：{}", CollUtil.size(planResps));
        }
        return Result.success(planResps);
    }

    /**
     * 获取进度显示模式
     *
     * @param resp
     * @return
     */
    private void getProgressDisplayMode(ProjectPlanResp resp) {
        // 查询节点为调研打印报表、调研表单的数据
        ConfigCustomBackendDetailLimit limit = null;
        if (DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode().equals(resp.getProjectPlanItemCode())) {
            // 查询该项目是否开启小前端大后端报表表单审核
            limit = baseQueryService.isOpenAuditorFlag(resp.getProjectInfoId(), 11);
            if (limit != null && Integer.valueOf(1).equals(limit.getOpenFlag())) {
                resp.setProgressDisplayMode("ratio");
            }
        } else if (DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode().equals(resp.getProjectPlanItemCode())) {
            // 查询该项目是否开启小前端大后端报表表单审核
            limit = baseQueryService.isOpenAuditorFlag(resp.getProjectInfoId(), 12);
            if (limit != null && Integer.valueOf(1).equals(limit.getOpenFlag())) {
                resp.setProgressDisplayMode("ratio");
            }
        }
    }


    /**
     * 保存或更新数据
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveProjectPlan(SaveProjectPlanReq req) {
        log.info("保存或更新数据,参数-{}", JSON.toJSONString(req));
        SysUserVO currentUser = userHelper.getCurrentUser();
        Date now = new Date();
        if (CollectionUtils.isNotEmpty(req.getProjectPlanIdList())) {
            List<ProjProjectPlan> updateList = new ArrayList<>();
            //更新
            req.getProjectPlanIdList().stream().forEach(projectPlanId -> {
                ProjProjectPlan updateParam = new ProjProjectPlan();
                BeanUtil.copyProperties(req, updateParam);
                updateParam.setProjectPlanId(projectPlanId);
                updateParam.setUpdaterId(currentUser.getSysUserId());
                updateParam.setUpdateTime(now);
                updateParam.setCreateTime(null);
                updateParam.setCreaterId(null);
                updateList.add(updateParam);
            });
            projectPlanMapper.updateBatchSelective(updateList);
            //todo 同步更新对应的待办负责人
            if (req.getImplementationEngineerId() != null || req.getBackendEngineerId() != null) {
                List<ProjProjectPlan> projectPlanList = projectPlanMapper.selectBatchIds(req.getProjectPlanIdList());
                projectPlanList.stream().filter(plan -> plan.getGenerateTodoFlag() == 1).forEach(i -> {
                    List<ProjTodoTask> todoTaskList = todoTaskService.getTodoTaskByProjectAndPlan(i.getProjectInfoId(), i.getProjectPlanId());
                    if (!org.springframework.util.CollectionUtils.isEmpty(todoTaskList)) {
                        todoTaskList = todoTaskList.stream().filter(item2 -> item2.getYyProductId() == null).collect(Collectors.toList());
                        for (ProjTodoTask item : todoTaskList) {
                            ProjTodoTask todoTask = new ProjTodoTask();
                            todoTask.setTodoTaskId(item.getTodoTaskId());
                            todoTask.setProjectPlanId(i.getProjectPlanId());
                            todoTask.setImplementationEngineerId(req.getImplementationEngineerId());
                            todoTask.setBackendEngineerId(req.getBackendEngineerId());
                            todoTask.setFrontFlag(i.getFrontFlag());
                            todoTask.setBackendFlag(i.getBackendFlag());
                            todoTask.setUpdaterId(currentUser.getSysUserId());
                            todoTask.setUpdateTime(now);
                            todoTask.setPlanTime(i.getPlanTime());
                            todoTaskService.updateByPrimaryKeySelective(todoTask);
                        }
                    }
                });
            }
        } else {
            //新增
            //校验 title 不能为空
            if (StringUtils.isBlank(req.getTitle())) {
                return Result.fail("标题不能为空");
            }
            //校验 stage 不能为空
            if (req.getProjectPlanStageId() == null || req.getProjectPlanStageId() == 0) {
                return Result.fail("阶段不能为空");
            }
            //校验前后端标志
            if (req.getFrontFlag() == null || req.getBackendFlag() == null) {
                return Result.fail("前后端标志不能为空");
            }
            ProjProjectPlan saveParam = new ProjProjectPlan();
            BeanUtil.copyProperties(req, saveParam);
            saveParam.setProjectPlanId(SnowFlakeUtil.getId());
            //类型  1自定义的 2预置的 -手动创建的都是自定义的
            saveParam.setPlanType(1);
            saveParam.setStatus(0);
            saveParam.setCompleteCount(0);
            saveParam.setTotalCount(0);
            saveParam.setGenerateTodoFlag(0);
            if (req.getAttentionFlag() != null) {
                saveParam.setAttentionFlag(req.getAttentionFlag());
            } else {
                saveParam.setAttentionFlag(0);
            }
            //todo  是否上线必备还没确认逻辑
            saveParam.setPriorProjectPlanItemFlag(0);
            saveParam.setCreaterId(currentUser.getSysUserId());
            saveParam.setUpdaterId(currentUser.getSysUserId());
            saveParam.setCreateTime(now);
            saveParam.setUpdateTime(now);
            saveParam.setIsDeleted(0);
            //查询排序
            saveParam.setSort(projectPlanMapper.queryMaxSort(req.getProjectInfoId()) + 1);
            projectPlanMapper.insert(saveParam);
        }
        return Result.success();
    }

    /**
     * 查询项目计划阶段
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result queryStage(Long projectInfoId) {
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        List<BaseDataExtra> stageList = dictProjectPlanStageMapper.getList().stream().map(item -> {
            BaseDataExtra baseDataExtra = new BaseDataExtra();
            baseDataExtra.setId(item.getProjectPlanStageId());
            baseDataExtra.setName(item.getProjectPlanStageName());
            return baseDataExtra;
        }).collect(Collectors.toList());
        //处理当前阶段-对照-1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动
        //项目1已派工-1启动阶段
        //项目7已启动-2调研阶段
        //项目2已调研-3入驻阶段
        //项目3已入驻-4准备阶段
        //项目4准备完成-5上线阶段
        //项目5已上线-6验收阶段
        int projectDeliverStatus = projectInfo.getProjectDeliverStatus();
        switch (projectDeliverStatus) {
            case 1:
            default:
                stageList.stream().forEach(item -> {
                    if (item.getId() == 1) {
                        item.setChosen(true);
                    }
                });
                break;
            case 7:
                stageList.stream().forEach(item -> {
                    if (item.getId() == 2) {
                        item.setChosen(true);
                    }
                });
                break;
            case 2:
                stageList.stream().forEach(item -> {
                    if (item.getId() == 3) {
                        item.setChosen(true);
                    }
                });
                break;
            case 3:
                stageList.stream().forEach(item -> {
                    if (item.getId() == 4) {
                        item.setChosen(true);
                    }
                });
                break;
            case 4:
                stageList.stream().forEach(item -> {
                    if (item.getId() == 5) {
                        item.setChosen(true);
                    }
                });
                break;
            case 5:
            case 6:
                stageList.stream().forEach(item -> {
                    if (item.getId() == 6) {
                        item.setChosen(true);
                    }
                });
                break;

        }
        return Result.success(stageList);
    }

    /**
     * 处理项目计划的阶段完成状态，枚举值0、1、2，分别表示未开始、进行中、已完成。
     *
     * @param projectPlanRespList 项目计划
     */
    private void dealStageStatus(List<ProjectPlanResp> projectPlanRespList) {
        Map<String, Integer> planStageStatusMap = new HashMap<>();
        Map<String, List<ProjectPlanResp>> groupByPlanStageCode = projectPlanRespList.stream().collect(Collectors.groupingBy(ProjectPlanResp::getProjectPlanStageCode));
        for (Map.Entry<String, List<ProjectPlanResp>> entry : groupByPlanStageCode.entrySet()) {
            planStageStatusMap.put(entry.getKey(), this.getStageStatus(entry.getValue()));
        }
        projectPlanRespList.forEach(item -> {
            if (planStageStatusMap.containsKey(item.getProjectPlanStageCode())) {
                item.setStageStatus(planStageStatusMap.get(item.getProjectPlanStageCode()));
            }
        });
    }

    /**
     * 处理项目计划的阶段完成状态，枚举值0、1、2，分别表示未开始、进行中、已完成。
     *
     * @param projectPlanRespList 项目计划
     */
    private int getStageStatus(List<ProjectPlanResp> projectPlanRespList) {
        // 没有数据，认为是已完成
        if (org.springframework.util.CollectionUtils.isEmpty(projectPlanRespList)) {
            return 2;
        }
        // 全部都是已完成，整体状态为已完成
        if (projectPlanRespList.stream().allMatch(item -> Integer.valueOf(1).equals(item.getStatus()))) {
            return 2;
        }
        // 全部都是未完成，整体状态为未开始
        if (projectPlanRespList.stream().allMatch(item -> Integer.valueOf(0).equals(item.getStatus()))) {
            return 0;
        }
        // 既有已完成也有未完成，整体状态为进行中
        return 1;
    }


    @Override
    public List<BaseIdCodeNameResp> queryPlanItem(SimpleId simpleId) {
        QueryProjectPlanReq req = new QueryProjectPlanReq();
        req.setProjectInfoId(simpleId.getId());
        List<ProjectPlanResp> planResps = projectPlanMapper.queryData(req);
        if (org.springframework.util.CollectionUtils.isEmpty(planResps)) {
            return new ArrayList<>();
        }
        return planResps.stream().map(item -> new BaseIdCodeNameResp(String.valueOf(item.getProjectPlanId()), item.getProjectPlanItemCode(), item.getTitle())).collect(Collectors.toList());
    }
    //region ----------整理完成----------

    @Override
    public ProjProjectPlan getProjectPlanByProjectInfoIdAndItemCode(Long projectInfoId, DictProjectPlanItemEnum planItemEnum) {
        log.info("根据项目ID和项目计划工作项编码获取项目工作计划，projectInfoId={}，planItemEnum={}", projectInfoId, planItemEnum);
        return projProjectPlanBaseService.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, planItemEnum);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void followOrUnfollow(Long projectPlanId, Integer attentionFlag) {
        log.info("重点关注项目计划或者取消重点关注，projectPlanId={}，attentionFlag={}", projectPlanId, attentionFlag);
        boolean updateFlag = projProjectPlanBaseService.followOrUnfollow(projectPlanId, attentionFlag);
        if (!updateFlag) {
            throw new IllegalArgumentException("重点关注/取消关注项目计划失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByProjectPlanId(Long projectPlanId, ProjectPlanStatusEnum status) {
        log.info("根据项目计划ID更新项目计划状态，projectPlanId={}，status={}", projectPlanId, status);
        boolean updateFlag = projProjectPlanBaseService.updateStatusByProjectPlanId(projectPlanId, status);
        if (!updateFlag) {
            throw new IllegalArgumentException("更新项目计划状态失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanStatusByProjectInfoIdAndItemCode(Long projectInfoId, DictProjectPlanItemEnum planItemEnum, ProjectPlanStatusEnum status) {
        log.info("根据项目ID和项目计划工作项编码更新项目计划状态，projectInfoId={}，planItemEnum={}，status={}", projectInfoId, planItemEnum, status);
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return;
        }
        boolean result = projProjectPlanBaseService.updateStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, status);
        if (!result) {
            log.info("更新项目计划状态失败，projectInfoId={}，planItemEnum={}，status={}", projectInfoId, planItemEnum, status);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addTotalCountByProjectInfoIdAndItemCode(Long projectInfoId, DictProjectPlanItemEnum planItemEnum, int totalCount) {
        log.info("根据项目ID和项目计划工作项编码使待办总数（任务进度分母）在原来的基础上增加指定值，projectInfoId={}，planItemEnum={}，totalCount={}", projectInfoId, planItemEnum, totalCount);
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        return projProjectPlanBaseService.addTotalCountByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, totalCount);
    }

    @Override
    public List<ProjProjectPlanVO> getProjectPlanViewByProjectInfoId(QueryProjectPlanParam param) {
        return projProjectPlanBaseService.getProjectPlanViewByProjectInfoId(param);
    }
    //endregion

    /**
     * 同步项目计划总数量
     * 该方法用于与外部系统同步特定项目信息ID下的计划项代码的总数量
     *
     * @param projectInfoId 项目信息的唯一标识符
     * @param planItemCode  计划项的代码
     * @return 返回同步是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean projectPlanTotalCountSync(Long projectInfoId, String planItemCode) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        try {
            //获取项目计划总数量
            int totalCount = 0;
            int completeCount = 0;
            // 统计报表验证完成的数量
            int surveyResCount = 0;
            DictProjectPlanItemEnum planItemEnum = DictProjectPlanItemEnum.getPlanItemByCode(planItemCode);
            switch (planItemEnum) {
                // 产品业务调研
                case SURVEY_PRODUCT:
                    List<ProjProductDeliverRecord> surveyProductDeliverRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(projectInfoId, null);
                    totalCount = surveyProductDeliverRecord.size();
                    // 无需调研的产品
                    int notNeedSurveyProductCount = projSurveyPlanService.getNotNeedSurveyProductCount(projectInfoId);
                    // 完成调研的产品数量
                    int finishedProductCount = projSurveyPlanService.getFinishedProductCount(projectInfoId);
                    completeCount = notNeedSurveyProductCount + finishedProductCount;
                    break;
                case PREPARAT_PRODUCT:
                    //产品准备工作处理
                    ProjProductBacklogDTO productBacklogDTO = new ProjProductBacklogDTO();
                    productBacklogDTO.setProjectInfoId(projectInfoId);
                    List<ProjProductBacklogVO> backlogList = productBacklogService.findProductBacklogVOS(productBacklogDTO);
                    // 模块对应的产品准备工作
                    List<ProjProductBacklogVO> moduleBacklogVO = backlogList.stream().filter(item -> item.getYyProductModuleId() != -1).collect(Collectors.toList());
                    // 模块对应的产品编码
                    List<Long> moduleProductId = moduleBacklogVO.stream().map(ProjProductBacklogVO::getYyProductId).collect(Collectors.toList());
                    // 产品对应的产品准备工作
                    List<ProjProductBacklogVO> productBacklogVO = backlogList.stream().filter(item -> !moduleProductId.contains(item.getYyProductId())).collect(Collectors.toList());
                    totalCount = moduleBacklogVO.size() + productBacklogVO.size();
                    List<Long> completeModuleSet = moduleBacklogVO.stream().filter(item -> Integer.valueOf(1).equals(item.getCompleteStatus())).map(ProjProductBacklogVO::getYyProductModuleId).collect(Collectors.toList());
                    List<Long> completeProductSet = productBacklogVO.stream().filter(item -> Integer.valueOf(1).equals(item.getCompleteStatus())).map(ProjProductBacklogVO::getYyProductId).collect(Collectors.toList());
                    completeCount = completeModuleSet.size() + completeProductSet.size();
                    break;
                case SURVEY_DEVICE:
                case PREPARAT_DEVICE:
                    //设备对接工作处理
                    QueryWrapper<ProjEquipRecord> projEquipRecordQueryWrapper = new QueryWrapper<>();
                    projEquipRecordQueryWrapper.eq("project_info_id", projectInfoId);
                    projEquipRecordQueryWrapper.eq("is_deleted", 0);
                    if (DictProjectPlanItemEnum.PREPARAT_DEVICE.equals(planItemEnum)) {
                        projEquipRecordQueryWrapper.eq("required_flag", 1);
                    }
                    List<ProjEquipRecord> projEquipRecordList = projEquipRecordMapper.selectList(projEquipRecordQueryWrapper);
                    totalCount = projEquipRecordList.size();
                    if (DictProjectPlanItemEnum.PREPARAT_DEVICE.equals(planItemEnum)) {
                        completeCount = (int) projEquipRecordList.stream().filter(item -> Integer.valueOf(5).equals(item.getEquipStatus())).count();
                    }
                    break;
                case SURVEY_THIRD_PART:
                case SCHEDULE_THIRD_PART:
                    //三方接口工作处理
                    QueryWrapper<ProjThirdInterface> projThirdInterfaceQueryWrapper = new QueryWrapper<>();
                    projThirdInterfaceQueryWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.equals(planItemEnum)) {
                        projThirdInterfaceQueryWrapper.eq("online_flag", 1);
                    }
                    projThirdInterfaceQueryWrapper.eq("is_deleted", 0);
                    List<ProjThirdInterface> projThirdInterfaceList = projThirdInterfaceMapper.selectList(projThirdInterfaceQueryWrapper);
                    totalCount = projThirdInterfaceList.size();
                    if (DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.equals(planItemEnum)) {
                        completeCount = (int) projThirdInterfaceList.stream().filter(a -> Integer.valueOf(50).equals(a.getStatus()) || Integer.valueOf(32).equals(a.getStatus())).count() + (int) projThirdInterfaceList.stream().filter(a -> Integer.valueOf(13).equals(a.getStatus()) && Integer.valueOf(2).equals(a.getInterfaceType())).count();
                    }
                    break;
                case SURVEY_REPORT:
                case PREPARAT_REPORT:
                    //打印报表工作处理
                    QueryWrapper<ProjSurveyReport> projSurveyReportQueryWrapper = new QueryWrapper<>();
                    projSurveyReportQueryWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_REPORT.equals(planItemEnum)) {
                        projSurveyReportQueryWrapper.eq("online_essential", 1);
                    }
                    projSurveyReportQueryWrapper.eq("is_deleted", 0);
                    List<ProjSurveyReport> projSurveyReportList = projSurveyReportMapper.selectList(projSurveyReportQueryWrapper);
                    totalCount = projSurveyReportList.size();
                    if (DictProjectPlanItemEnum.PREPARAT_REPORT.equals(planItemEnum)) {
//                        completeCount = (int) projSurveyReportList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus())).count();
                        List<Integer> preparatReportFinishedStatus = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode(), 3);
                        completeCount = this.projSurveyReportMapper.getFinishedReportCount(projectInfoId, null, null, preparatReportFinishedStatus, 1);
                        log.info("打印报表制作，更新项目计划数量，已完成数={}", completeCount);
                    } else {
                        List<Integer> finishedStatus = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode(), 3);
                        completeCount = this.projSurveyReportMapper.getFinishedReportCount(projectInfoId, null, null, finishedStatus, null);
                        log.info("打印报表调研同步完成数量，已完成数={}", completeCount);
                    }
                    break;
                case SURVEY_STATISTICS_REPORT:
                case PREPARAT_STATISTICS_REPORT:
                    //统计报表工作处理
                    QueryWrapper<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityQueryWrapper = new QueryWrapper<>();
                    projStatisticalReportMainEntityQueryWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.equals(planItemEnum) || DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.equals(planItemEnum)) {
                        projStatisticalReportMainEntityQueryWrapper.eq("online_flag", 1);
                    }
                    projStatisticalReportMainEntityQueryWrapper.eq("is_deleted", 0);
                    List<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityList = projStatisticalReportMainMapper.selectList(projStatisticalReportMainEntityQueryWrapper);
                    totalCount = projStatisticalReportMainEntityList.size();
                    completeCount = (int) projStatisticalReportMainEntityList.stream().filter(item -> Integer.valueOf(22).compareTo(item.getReportStatus()) <= 0 || (Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0 && StatisticalReportMethodModel.getMethedList().contains(item.getProductionMethodId()))).count();
                    surveyResCount = projStatisticalReportMainEntityList.stream().filter(item -> Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0).collect(Collectors.toList()).size();
                    break;
                case PREPARAT_STATISTICS_RELEASE:
                    //统计报表工作处理
                    QueryWrapper<ProjStatisticalReportMainEntity> ssWrapper = new QueryWrapper<>();
                    ssWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.equals(planItemEnum) || DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.equals(planItemEnum)) {
                        ssWrapper.eq("online_flag", 1);
                    }
                    ssWrapper.eq("is_deleted", 0);
                    List<ProjStatisticalReportMainEntity> listReportss = projStatisticalReportMainMapper.selectList(ssWrapper);
                    totalCount = listReportss.size();
                    completeCount = (int) listReportss.stream().filter(item -> Integer.valueOf(31).compareTo(item.getReportStatus()) <= 0 || (Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0 && StatisticalReportMethodModel.getMethedList().contains(item.getProductionMethodId()))).count();
                    surveyResCount = listReportss.stream().filter(item -> Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0).collect(Collectors.toList()).size();
                    break;
                case SURVEY_FORM:
                    //表单工作处理
                    QueryWrapper<ProjSurveyForm> projSurveyaFormQueryWrapper = new QueryWrapper<>();
                    projSurveyaFormQueryWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_FORM.equals(planItemEnum)) {
                        projSurveyaFormQueryWrapper.eq("online_essential", 1);
                    }
                    projSurveyaFormQueryWrapper.eq("is_deleted", 0);
                    List<ProjSurveyForm> projSurveyFormLists = projSurveyFormMapper.selectList(projSurveyaFormQueryWrapper);
                    totalCount = projSurveyFormLists.size();
                    List<Long> businessIds = projSurveyFormLists.stream().map(ProjSurveyForm::getSurveyFormId).collect(Collectors.toList());
                    List<ProjBusinessExamineLog> list = projBusinessExamineLogService.selectListByIds(businessIds);
                    if (list != null && list.size() > 0) {
                        completeCount = list.size();
                    }
                    break;
                case PREPARAT_FORM:
                    //表单工作处理
                    QueryWrapper<ProjSurveyForm> projSurveyFormQueryWrapper = new QueryWrapper<>();
                    projSurveyFormQueryWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_FORM.equals(planItemEnum)) {
                        projSurveyFormQueryWrapper.eq("online_essential", 1);
                    }
                    projSurveyFormQueryWrapper.eq("is_deleted", 0);
                    List<ProjSurveyForm> projSurveyFormList = projSurveyFormMapper.selectList(projSurveyFormQueryWrapper);
                    totalCount = projSurveyFormList.size();
                    completeCount = (int) projSurveyFormList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus()) || Integer.valueOf(8).equals(item.getFinishStatus())).count();
                    break;
                default:
                    break;
            }
            if (totalCount > 0) {
                int status = 2;
                List<ProjTodoTask> todoTaskList = todoTaskService.selectTodoTaskByProjectInfoIdAndItemCode(projectInfoId, -1L, planItemCode, null);
                List<ProjTodoTask> tmpList = todoTaskList.stream().filter(a -> a.getYyProductId() == null).collect(Collectors.toList());
                ProjTodoTask projTodoTask = null;
                if (!tmpList.isEmpty()) {
                    projTodoTask = tmpList.get(0);
                }
                if (projTodoTask != null) {
                    status = projTodoTask.getStatus();
                    log.info("更新项目计划数量，planItemCode={}，status={},todoTaskId={}", planItemCode, status, projTodoTask.getTodoTaskId());
                    ProjTodoTask todoTask2 = new ProjTodoTask();
                    todoTask2.setTodoTaskId(projTodoTask.getTodoTaskId());
                    todoTask2.setTotalCount(totalCount);
                    todoTask2.setCompleteCount(completeCount);
                    todoTask2.setStatus(totalCount == completeCount || totalCount == surveyResCount ? 1 : 2);
                    projTodoTaskService.updateByPrimaryKeySelective(todoTask2);
                }
                //更新项目计划总数量
                log.info("更新项目计划数量，planItemCode={}，status={}，completeCount={}，totalCount={}，projectInfoId={}", planItemCode, status, completeCount, totalCount, projectInfoId);
                int c = projectPlanMapper.updateCompleteStatus(planItemCode, status, completeCount, totalCount, projectInfoId);
                // 更新里程碑状态为未完成， 根据项目id + 计划id + 更新标识
                if (status != 1) {
                    projMilestoneInfoMapper.updateMilestoneByProjectPlan(planItemCode, null, projectInfoId, ProjectPlanStatusEnum.UNFINISHED.getStatusCode());
                }
                if (c == 0) {
                    log.error("同步项目计划总数量失败，参数信息：projectInfoId={}，planItemCode={}; 更新项目计划总数量失败！", projectInfoId, planItemCode);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("同步项目计划总数量失败，参数信息：projectInfoId={}，planItemCode={}; 错误信息：{}", projectInfoId, planItemCode, e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlanTotalAndCompleteCountByProjectAndItemCode(Long projectInfoId, DictProjectPlanItemEnum planItemEnum) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        // 查询项目计划
        ProjProjectPlan projectPlan = this.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, planItemEnum);
        if (projectPlan == null || projectPlan.getProjectPlanId() == null) {
            return true;
        }
        switch (planItemEnum) {
            case SURVEY_PRODUCT:
                // 所有实施产品
                List<ProjProductDeliverRecord> surveyProductDeliverRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(projectInfoId, null);
                int totalCount = surveyProductDeliverRecord.size();
                // 已分配调研计划的无需调研的产品
                int notNeedSurveyProduct = projSurveyPlanService.getNotNeedSurveyProductCount(projectInfoId);
                // 完成调研的产品数量
                int completeSurveyProduct = projSurveyPlanService.getFinishedProductCount(projectInfoId);
                int completeCount = notNeedSurveyProduct + completeSurveyProduct;
                // 更新没有产品的总的我的待办
                ProjTodoTask projTodoTask = todoTaskMapper.selectOne(new QueryWrapper<ProjTodoTask>().eq("is_deleted", 0).eq("project_plan_id", projectPlan.getProjectPlanId()).eq(" project_info_id", projectInfoId).isNull("yy_product_id"));
                ProjTodoTask updateParam = new ProjTodoTask();
                updateParam.setTodoTaskId(projTodoTask.getTodoTaskId());
                updateParam.setTotalCount(totalCount);
                updateParam.setCompleteCount(completeCount);
                if (totalCount > completeCount && completeCount != 0) {
                    log.info("更新产品业务调研的项目计划和待办，总数大于完成数且完成数不是0，待办和项目计划更新为进行中状态，项目ID={}", projectInfoId);
                    updateParam.setStatus(ProjectPlanStatusEnum.UNDERWAY.getStatusCode());
                    this.updatePlanStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, ProjectPlanStatusEnum.UNDERWAY);
                }
                if (totalCount == completeCount && projectPlan.getCompletionTime() != null) {
                    log.info("更新产品业务调研的项目计划和待办，总数与完成数相等且存在完成时间，待办和项目计划更新为已完成状态，项目ID={}", projectInfoId);
                    updateParam.setStatus(ProjectPlanStatusEnum.FINISHED.getStatusCode());
                    this.updatePlanStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, ProjectPlanStatusEnum.FINISHED);
                }
                log.info("更新产品业务调研的项目计划和待办，更新参数={}", JSON.toJSONString(updateParam));
                todoTaskService.updateByPrimaryKeySelective(updateParam);
                // 更新项目计划总数和完成数
                projProjectPlanBaseService.updateTotalAndCompleteCountByPlanId(projectPlan.getProjectPlanId(), totalCount, completeCount);
                return true;
            case SURVEY_THIRD_PART:
                //三方接口工作处理
                QueryWrapper<ProjThirdInterface> projThirdInterfaceQueryWrapper = new QueryWrapper<>();
                projThirdInterfaceQueryWrapper.eq("is_deleted", 0);
                projThirdInterfaceQueryWrapper.eq("project_info_id", projectInfoId);
                List<ProjThirdInterface> projThirdInterfaceList = projThirdInterfaceMapper.selectList(projThirdInterfaceQueryWrapper);
                // 三方接口调研，不统计完成数
                projProjectPlanBaseService.updateTotalAndCompleteCountByPlanId(projectPlan.getProjectPlanId(), projThirdInterfaceList.size(), 0);
                // 更新没有产品的总的我的待办
                ProjTodoTask projTodoTask2 = todoTaskMapper.selectOne(new QueryWrapper<ProjTodoTask>().eq("is_deleted", 0).eq("project_plan_id", projectPlan.getProjectPlanId()).eq(" project_info_id", projectInfoId).isNull("yy_product_id"));
                ProjTodoTask surveyThirdPart = new ProjTodoTask();
                surveyThirdPart.setTodoTaskId(projTodoTask2.getTodoTaskId());
                surveyThirdPart.setTotalCount(projThirdInterfaceList.size());
                todoTaskService.updateByPrimaryKeySelective(surveyThirdPart);
                return true;
            default:
                return false;
        }

    }

    @Override
    public boolean updatePlanAndTodoTaskStatusByProjectAndItemCode(Long projectInfoId, DictProjectPlanItemEnum planItemEnum, ProjectPlanStatusEnum status) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        switch (planItemEnum) {
            case SURVEY_PRODUCT:
                // 更新项目计划总数和完成数
                projProjectPlanBaseService.updateStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, status);
                todoTaskService.updateTodoTaskStatus(projectInfoId, planItemEnum, status);
                return true;
            case SURVEY_THIRD_PART:
                projProjectPlanBaseService.updateStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, status);
                todoTaskService.updateTodoTaskStatus(projectInfoId, planItemEnum, status);
                return true;
            default:
                return false;
        }
    }

    @Override
    @Transactional
    public void updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(Long projectInfoId, DictProjectPlanItemEnum planItemEnum) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (planModel) {
            ProjProjectPlan projectPlan = this.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, planItemEnum);
            if (projectPlan == null) {
                log.info("更新项目计划状态为进行中时，未找到项目计划，projectInfoId={}，planItemEnum={}", projectInfoId, planItemEnum);
                return;
            }
            if (ProjectPlanStatusEnum.UNFINISHED.getStatusCode().equals(projectPlan.getStatus())) {
                this.updatePlanStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, ProjectPlanStatusEnum.UNDERWAY);
            }
            ProjTodoTask todoTask = todoTaskMapper.selectOne(new QueryWrapper<ProjTodoTask>().eq("is_deleted", 0).eq("project_plan_id", projectPlan.getProjectPlanId()).eq(" project_info_id", projectInfoId).eq("hospital_info_id", -1).isNull("yy_product_id"));
            if (ProjectPlanStatusEnum.UNFINISHED.getStatusCode().equals(todoTask.getStatus())) {
                ProjTodoTask updateParam = new ProjTodoTask();
                updateParam.setTodoTaskId(todoTask.getTodoTaskId());
                updateParam.setStatus(ProjectPlanStatusEnum.UNDERWAY.getStatusCode());
                todoTaskService.updateByPrimaryKeySelective(updateParam);
            }
        } else {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
        }
    }

    @Override
    @Transactional
    public void updatePlanAndTodoTaskStatus(Long projectInfoId, DictProjectPlanItemEnum planItemEnum, ProjectPlanStatusEnum statusEnum) {
        Long sysUserId = null;
        try {
            sysUserId = userHelper.getCurrentUser().getSysUserId();
        } catch (Throwable e) {
            log.error("更新项目待执行任务失败：未登录，获取当前用户信息失败, e.message: {}, e=", e.getMessage(), e);
//            throw new CustomException(StrUtil.format("更新项目待执行任务失败：未登录，获取当前用户信息失败"));
        }
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (planModel) {
            ProjProjectPlan projectPlan = this.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, planItemEnum);
            if (projectPlan == null) {
                log.info("更新项目计划状态时，未找到项目计划，projectInfoId={}，planItemEnum={}", projectInfoId, planItemEnum);
                throw new CustomException(StrUtil.format("更新项目计划状态时，未找到项目计划，projectInfoId={}，planItemEnum={}", projectInfoId, planItemEnum));
            }
            //更新了proj_project_plan的status
            this.updatePlanStatusByProjectInfoIdAndItemCode(projectInfoId, planItemEnum, statusEnum);
            Long finalSysUserId = sysUserId;
            new LambdaQueryChainWrapper<>(todoTaskMapper).eq(ProjTodoTask::getIsDeleted, 0).eq(ProjTodoTask::getProjectPlanId, projectPlan.getProjectPlanId()).eq(ProjTodoTask::getProjectInfoId, projectInfoId).eq(ProjTodoTask::getHospitalInfoId, -1).isNull(ProjTodoTask::getYyProductId).last("limit 1").oneOpt().ifPresent(todoTask -> {
                ProjTodoTask updateParam = new ProjTodoTask();
                updateParam.setTodoTaskId(todoTask.getTodoTaskId());
                updateParam.setStatus(statusEnum.getStatusCode());
                // 2025-03-28增加完成人标识
                updateParam.setCompletionUserId(finalSysUserId);
                todoTaskService.updateByPrimaryKeySelective(updateParam);
            });
        } else {
            log.info("更新项目计划状态时，当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
        }
    }

    @Override
    public Result<ProjectPlanResp> queryProjectPlanInfo(QueryProjectPlanInfoReq req) {
        QueryProjectPlanReq queryProjectPlanReq = new QueryProjectPlanReq();
        BeanUtils.copyProperties(req, queryProjectPlanReq);
        Result<List<ProjectPlanResp>> listResult = this.queryData(queryProjectPlanReq);
        List<ProjectPlanResp> data = listResult.getData();
        if (org.springframework.util.CollectionUtils.isEmpty(data)) {
            return Result.success(null, "没查到数据");
        }
        ProjectPlanResp projectPlanResp = data.stream().filter(item -> item.getProjectPlanId().equals(req.getProjectPlanId())).findFirst().orElse(null);
        return Result.success(projectPlanResp, projectPlanResp == null ? "没查到数据" : "成功");
    }

    @Override
    @Transactional
    public Result<?> todoTaskTotalCountSync(Long planId) {
        // 根据计划ID获取当前项目计划
        ProjProjectPlan proposalInfoVO = projProjectPlanBaseService.getProjectPlanByPlanId(planId);
        if (proposalInfoVO == null) {
            return Result.fail("节点信息不匹配");
        }
        try {
            DictProjectPlanItem dictProjectPlanItem = dictProjectPlanItemMapper.selectByPrimaryKey(proposalInfoVO.getProjectPlanItemId());
            DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(dictProjectPlanItem.getProjectPlanItemCode());
            projTodoTaskService.todoTaskTotalCountSync(proposalInfoVO.getProjectInfoId(), planItemByCode.getPlanItemCode());
        } catch (Exception e) {
            log.error("进入里程碑节点时，更新项目计划和待办的状态和数量，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.success();
    }

    @Override
    @Transactional
    public Result<String> verifyProjectPlanInfo(Long planId) {
        // 根据计划ID获取当前项目计划
        ProjProjectPlan proposalInfoVO = projProjectPlanBaseService.getProjectPlanByPlanId(planId);
        if (proposalInfoVO == null) {
            return Result.fail("节点信息不匹配");
        }
        //判断前直接点标识
        if (NumberEnum.NO_0.num().intValue() == proposalInfoVO.getPriorProjectPlanItemFlag()) {
            return Result.success(proposalInfoVO.getJumpPath());
        }
        ProjMilestoneInfo proposalInfo = new ProjMilestoneInfo();
        proposalInfo.setProjectInfoId(proposalInfoVO.getProjectInfoId());
        proposalInfo.setPreNodeId(proposalInfoVO.getPriorProjectPlanItemId());
        if (ObjectUtil.isNotEmpty(proposalInfoVO.getPriorProjectPlanItemId())) {
            List<String> userIds = Arrays.asList(proposalInfoVO.getPriorProjectPlanItemId().split(","));
            List<Long> proposalInfoList = userIds.stream().map(Long::parseLong).collect(Collectors.toList());
            List<ProjProjectPlan> milestoneInfoList = projProjectPlanBaseService.getProjectPlanByProjectAndItemIds(proposalInfoVO.getProjectInfoId(), proposalInfoList);
            if (ObjectUtil.isEmpty(milestoneInfoList)) {
                return Result.fail("前置节点信息配置有误");
            }
            StringBuffer sb = new StringBuffer();
            milestoneInfoList.forEach(item -> {
                if (!Integer.valueOf(1).equals(item.getStatus())) {
                    sb.append("【").append(item.getTitle()).append("】");
                }
            });
            if (ObjectUtil.isNotEmpty(sb)) {
                return Result.fail("请完成" + sb + "后再进行此操作！");
            }
        }
        return Result.success(proposalInfoVO.getJumpPath());
    }

    @Override
    @Transactional
    public Result<String> updateProjectPlanInfo(UpdateProjectPlanReq updateProjectContentReq) {
        try {
            ValidateUtil.validateWithException(updateProjectContentReq);
        } catch (Exception e) {
            log.error("后台管理更新项目计划，发生异常，params={}，errMsg={}，stackInfo=", JSON.toJSONString(updateProjectContentReq), e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        ProjProjectPlan projectPlan = projProjectPlanBaseService.getProjectPlanByPlanId(updateProjectContentReq.getProjectPlanId());
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectByPrimaryKey(updateProjectContentReq.getProjectInfoId());
        ProjCustomInfo projCustomInfo = customInfoService.selectByPrimaryKey(updateProjectContentReq.getCustomerInfoId());
        if (Integer.valueOf(6).equals(updateProjectContentReq.getUpdateType())) {
            if (StrUtil.isBlank(updateProjectContentReq.getOperatorAccount())) {
                return Result.fail("项目计划负责人账号不能为空");
            }
            if (ObjectUtil.isEmpty(updateProjectContentReq.getUpdateTime())) {
                return Result.fail("更新时间不能为空");
            }
            if (null == updateProjectContentReq.getProjectPlanId()) {
                return Result.fail("项目计划ID不可为空");
            }
            SysUser sysUser = sysUserMapper.selectByAccount(updateProjectContentReq.getOperatorAccount());
            UpdateProjectPlanParam updateProjectPlanParam = new UpdateProjectPlanParam();
            updateProjectPlanParam.setProjectPlanId(updateProjectContentReq.getProjectPlanId());
            updateProjectPlanParam.setCompletionTime(updateProjectContentReq.getUpdateTime());
            updateProjectPlanParam.setStatus(ProjectPlanStatusEnum.FINISHED.getStatusCode());
            if (projectPlan.getImplementationEngineerId() == null) {
                updateProjectPlanParam.setImplementationEngineerId(sysUser.getSysUserId());
            }
            boolean updateResult = projProjectPlanBaseService.updatePlanInfo(updateProjectPlanParam);
            // 操作日志信息
            ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(updateProjectContentReq.getProjectInfoId(), updateProjectContentReq.getCustomerInfoId(), updateProjectContentReq.getProjectPlanId());
            manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
            manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
            manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
            manualUpdateLog.setSyncYyStatusFlag(0);
            manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
            manualUpdateLog.setCustomerName(projCustomInfo.getCustomName());
            manualUpdateLog.setProjectName(projProjectInfo.getProjectName());
            manualUpdateLog.setMilestoneName(projectPlan.getTitle());
            manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
            manualUpdateLog.setOperateTitle("项目计划更新为已完成");
            projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
            if (updateResult) {
                return Result.success("项目计划更新为已完成成功");
            }
            return Result.fail("项目计划更新为已完成失败");
        } else if (Integer.valueOf(7).equals(updateProjectContentReq.getUpdateType())) {
            if (null == updateProjectContentReq.getProjectPlanId()) {
                return Result.fail("项目计划ID不可为空");
            }
            // 项目计划更新为未完成
            boolean updateResult = projProjectPlanBaseService.updateStatusByProjectPlanId(updateProjectContentReq.getProjectPlanId(), ProjectPlanStatusEnum.UNFINISHED);
            // 更新里程碑状态为未完成， 根据项目id + 计划id + 更新标识
            projMilestoneInfoMapper.updateMilestoneByProjectPlan(null, updateProjectContentReq.getProjectPlanId(), updateProjectContentReq.getProjectInfoId(), ProjectPlanStatusEnum.UNFINISHED.getStatusCode());
            // 操作日志信息
            ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(updateProjectContentReq.getProjectInfoId(), updateProjectContentReq.getCustomerInfoId(), updateProjectContentReq.getProjectPlanId());
            manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
            manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
            manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
            manualUpdateLog.setSyncYyStatusFlag(0);
            manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
            manualUpdateLog.setCustomerName(projCustomInfo.getCustomName());
            manualUpdateLog.setProjectName(projProjectInfo.getProjectName());
            manualUpdateLog.setMilestoneName(projectPlan.getTitle());
            manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
            manualUpdateLog.setOperateTitle("项目计划更新为未完成");
            projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
            if (updateResult) {
                return Result.success("项目计划更新为未完成成功");
            }
            return Result.fail("项目计划更新为未完成失败");
        } else if (Integer.valueOf(8).equals(updateProjectContentReq.getUpdateType())) {
            if (null == updateProjectContentReq.getProjectPlanId()) {
                return Result.fail("项目计划ID不可为空");
            }
            // 取消前置节点限制
            boolean cancelResult = projProjectPlanBaseService.cancelPriorProjectPlanItem(updateProjectContentReq.getProjectPlanId());
            // 操作日志信息
            ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(updateProjectContentReq.getProjectInfoId(), updateProjectContentReq.getCustomerInfoId(), updateProjectContentReq.getProjectPlanId());
            manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
            manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
            manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
            manualUpdateLog.setSyncYyStatusFlag(0);
            manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
            manualUpdateLog.setCustomerName(projCustomInfo.getCustomName());
            manualUpdateLog.setProjectName(projProjectInfo.getProjectName());
            manualUpdateLog.setMilestoneName(projectPlan.getTitle());
            manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
            manualUpdateLog.setOperateTitle("取消项目计划前置节点限制");
            projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
            if (cancelResult) {
                return Result.success("取消前置节点成功");
            }
            return Result.fail("取消前置节点失败");
        } else if (Integer.valueOf(9).equals(updateProjectContentReq.getUpdateType())) {
            // 我的待办更新为已完成
            if (CollectionUtils.isEmpty(updateProjectContentReq.getTodoTaskIdList())) {
                return Result.fail("我的待办ID不可为空");
            }
            SysUser sysUser = sysUserMapper.selectByAccount(updateProjectContentReq.getOperatorAccount());
            List<ProjTodoTask> todoTaskList = todoTaskService.selectTodoTaskByTaskIdList(updateProjectContentReq.getTodoTaskIdList());
            for (ProjTodoTask projTodoTask : todoTaskList) {
                ProjTodoTask updateTodoTaskParam = new ProjTodoTask();
                updateTodoTaskParam.setTodoTaskId(projTodoTask.getTodoTaskId());
                updateTodoTaskParam.setStatus(ProjectPlanStatusEnum.FINISHED.getStatusCode());
                updateTodoTaskParam.setUpdateTime(new Date());
                if (projTodoTask.getImplementationEngineerId() == null) {
                    updateTodoTaskParam.setImplementationEngineerId(sysUser.getSysUserId());
                }
                int i = todoTaskService.updateByPrimaryKeySelective(updateTodoTaskParam);
                // 操作日志信息
                ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(updateProjectContentReq.getProjectInfoId(), updateProjectContentReq.getCustomerInfoId(), updateProjectContentReq.getProjectPlanId());
                manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
                manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
                manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
                manualUpdateLog.setSyncYyStatusFlag(0);
                manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
                manualUpdateLog.setCustomerName(projCustomInfo.getCustomName());
                manualUpdateLog.setProjectName(projProjectInfo.getProjectName());
                manualUpdateLog.setMilestoneName(projTodoTask.getTitle());
                manualUpdateLog.setMilestoneInfoId(projTodoTask.getTodoTaskId());
                manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
                manualUpdateLog.setOperateTitle("我的待办更新为已完成");
                projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
            }
            return Result.success("我的待办更新为已完成成功");
        } else if (Integer.valueOf(10).equals(updateProjectContentReq.getUpdateType())) {
            if (CollectionUtils.isEmpty(updateProjectContentReq.getTodoTaskIdList())) {
                return Result.fail("我的待办ID不可为空");
            }
            // 我的待办更新为未完成
            List<ProjTodoTask> todoTaskList = todoTaskService.selectTodoTaskByTaskIdList(updateProjectContentReq.getTodoTaskIdList());
            for (ProjTodoTask projTodoTask : todoTaskList) {
                ProjTodoTask updateTodoTaskParam = new ProjTodoTask();
                updateTodoTaskParam.setTodoTaskId(projTodoTask.getTodoTaskId());
                updateTodoTaskParam.setStatus(ProjectPlanStatusEnum.UNFINISHED.getStatusCode());
                updateTodoTaskParam.setUpdateTime(new Date());
                int i = todoTaskService.updateByPrimaryKeySelective(updateTodoTaskParam);
                // 操作日志信息
                ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(updateProjectContentReq.getProjectInfoId(), updateProjectContentReq.getCustomerInfoId(), updateProjectContentReq.getProjectPlanId());
                manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
                manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
                manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
                manualUpdateLog.setSyncYyStatusFlag(0);
                manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
                manualUpdateLog.setCustomerName(projCustomInfo.getCustomName());
                manualUpdateLog.setProjectName(projProjectInfo.getProjectName());
                manualUpdateLog.setMilestoneName(projTodoTask.getTitle());
                manualUpdateLog.setMilestoneInfoId(projTodoTask.getTodoTaskId());
                manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
                manualUpdateLog.setOperateTitle("我的待办更新为未完成");
                projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
            }
            return Result.success("我的待办更新为未完成成功");
        }
        return Result.fail(String.format("没有当前操作类型。updateType=%s", updateProjectContentReq.getUpdateType()));
    }


    /**
     * 构建基于当前登录用户的里程碑手动操作日志实体
     *
     * @param projectInfoId
     * @param customInfoId
     * @param milestoneInfoId
     * @return com.msun.csm.dao.entity.proj.ProjMilestoneManualUpdateLog
     */
    private ProjMilestoneManualUpdateLog buildCurrentUserMilestoneManualOptLogEntity(Long projectInfoId, Long customInfoId, Long milestoneInfoId) {
        ProjMilestoneManualUpdateLog manualUpdateLog = new ProjMilestoneManualUpdateLog();
        manualUpdateLog.setProjMilestoneManualUpdateLogId(SnowFlakeUtil.getId());
        SysUserVO currentUser = userHelper.getCurrentUser();
        manualUpdateLog.setCreaterId(currentUser.getSysUserId());
        manualUpdateLog.setUpdaterId(currentUser.getSysUserId());
        manualUpdateLog.setCreateTime(new Date());
        manualUpdateLog.setUpdateTime(new Date());
        manualUpdateLog.setOperaterUserId(currentUser.getSysUserId());
        manualUpdateLog.setOperaterUserName(currentUser.getUserName());
        manualUpdateLog.setOperateTime(new Date());
        manualUpdateLog.setOperateTitle("");
        manualUpdateLog.setOperateContent("");
        manualUpdateLog.setProjectInfoId(projectInfoId);
        manualUpdateLog.setCustomerInfoId(customInfoId);
        manualUpdateLog.setMilestoneInfoId(milestoneInfoId);
        manualUpdateLog.setProductId(null);
        manualUpdateLog.setProjStatusOperatorAccount(null);
        manualUpdateLog.setSyncYyStatusFlag(null);
        manualUpdateLog.setYyApiRequest(null);
        manualUpdateLog.setYyApiResponse(null);
        return manualUpdateLog;
    }

    @Override
    @Transactional
    public Result<Void> initProjectPlan(Long projectInfoId) {
        int i = projectInfoMapper.initProjectPlanFlag(projectInfoId);
        if (i == 1) {
            return Result.success(null, "项目计划初始化成功");
        }
        return Result.fail("项目计划初始化失败");
    }
}
