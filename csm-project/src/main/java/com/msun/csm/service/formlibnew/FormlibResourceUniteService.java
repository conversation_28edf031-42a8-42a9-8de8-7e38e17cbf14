package com.msun.csm.service.formlibnew;


import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDataOneSaveDto;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDeliverToYjkDTO;
import com.msun.core.component.implementation.api.formlib.dto.FormlibProductDTO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductPageVO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibDataVO;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.formlibnew.FormlibResourceUnite;
import com.msun.csm.model.req.formlibnew.FormLibResourceUnitePageReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceReferenceReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectOneReq;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUniteSelectOneResp;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUniteUseResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;

/**
 * <AUTHOR>
 * @description 针对表【formlib_resource_unite(表单资源库)】的数据库操作Service
 * @createDate 2025-07-14 13:47:02
 */
public interface FormlibResourceUniteService extends IService<FormlibResourceUnite> {

    /**
     * 查询表单资源库分页数据
     * @param dto
     * @return
     */
    Result<PageInfo<FormlibResourceUnitePageResp>> findFormLibDataPage(FormLibResourceUnitePageReq dto);

    /**
     * 查询表单资源库单条数据
     * @param dto
     * @return
     */
    Result<FormlibResourceUniteSelectOneResp> findFormLibDataOne(FormLibResourceUnitePageReq dto);

    /**
     * 删除表单资源库单条数据
     * @param dto
     * @return
     */
    Result<String> deleteFormLibDataOne(FormLibResourceUnitePageReq dto);

    /**
     * 表单入库/维护
     * @param dto
     * @return
     */
    Result<String> formStoreUpdate(FormlibResourceUniteSelectOneReq dto);

    /**
     * 查询表单资源库列表数据
     * @param dto
     * @return
     */
    Result<List<FormlibResourceUnitePageResp>> findFormLibDataList(FormlibResourceUniteSelectListReq dto);

    /**
     * 获取表单数据
     * @param dto
     * @return
     */
    ResponseResult<FormlibDataVO> getFormDataByJfId(FormlibProductDTO dto);

    /**
     * 保存表单数据
     * @param dto
     * @return
     */
    ResponseResult<String> saveFormOneDataToDeliver(FormlibDataOneSaveDto dto);

    /**
     * 从交付根据类型获取资源库数据
     * @param dto
     * @return
     */
    ResponseResult<FormlibComeToProductPageVO> getFormDataByDeliverToYjk(FormlibDeliverToYjkDTO dto);

    /**
     *
     * @param dto
     * @return
     */
    Result<String> formlibReference(FormlibResourceReferenceReq dto);

    /**
     * 根据项目id查询表单资源库
     * @param dto
     * @return
     */
    Result<String> getFormlibByProductId(FormlibResourceUniteSelectListReq dto);

    /**
     * 导入表单资源库
     * @param dto
     * @return
     */
    Result<String> importFormlibToYjk(FormlibResourceUniteSelectListReq dto);

    /**
     * 根据标准名称及分类查询表单数据(准备设计推荐)
     * @param dto
     * @return
     */
    Result<List<FormlibResourceUnitePageResp>> findFormLibDataListToRecommend(FormlibResourceUniteSelectListReq dto);

    /**
     * 设计表单(根据部署跳转中心端或云健康)
     * @param dto
     * @return
     */
    Result<String> designFormByParam(FormlibResourceReferenceReq dto);

    /**
     * 资源库跳转中心端进行设计
     * @param dto
     * @return
     */
    Result<String> designFormResLibByParam(FormlibResourceReferenceReq dto);

    /**
     * 设计表单(根据部署跳转中心端或云健康)【只用于跳转
     * @param dto
     * @return
     */
    Result<String> jumpFormByParam(FormlibResourceReferenceReq dto);

    /**
     * 根据标准名称及分类查询表单数据(准备设计推荐)
     * @param dto
     * @return
     */
    Result<FormlibResourceUniteUseResp> findFormLibDataOneAndListToRecommend(FormlibResourceUniteSelectListReq dto);

    /**
     * 根据id查询表单数据
     * @param dto
     * @return
     */
    Result<ProjSurveyFormResp> findSurveyFormDataById(FormlibResourceUniteSelectListReq dto);
}
