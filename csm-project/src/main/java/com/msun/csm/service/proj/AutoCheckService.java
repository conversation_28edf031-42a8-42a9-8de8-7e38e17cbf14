package com.msun.csm.service.proj;

import cn.hutool.json.JSONObject;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.AutoCheckForTask;
import com.msun.csm.model.dto.SubmitAccountInfoDTO;
import com.msun.csm.model.dto.autocheck.AutoCheckHospitalInfoDTO;
import com.msun.csm.model.dto.autocheck.GenLoginUserDTO;
import com.msun.csm.model.dto.autocheck.GenLoginUserTransferDTO;
import com.msun.csm.model.resp.project.AutoCheckHospitalInfoResp;
import com.msun.csm.model.vo.ProjAutocheckInfoVO;

/**
 * @description:
 * @fileName:
 * @author:zhouz<PERSON>yu
 * @updateBy:
 * @Date:Created in 16:56 2024/7/8
 * @remark:
 */
public interface AutoCheckService {


    /**
     * 查询医院信息
     *
     * @param projectInfoId
     * @return
     */
    AutoCheckHospitalInfoResp getHospitalInfo(Long projectInfoId);

    /**
     * 调用自动化测试平台，获取返回的数据
     *
     * @param dto
     * @return
     */
    JSONObject applyToAutoCheck(AutoCheckHospitalInfoDTO dto);

    /**
     * 调用自动化测试平台  返回部门模版信息
     *
     * @param
     * @return
     */
    JSONObject getAutoCheckDeptList(Long hospitalInfoId);

    /**
     * @param hospitalInfoId 医院id
     * @param hospitalName   医院名称
     * @return JSONObject
     */
    JSONObject getAutoCheckDeptList(Long hospitalInfoId, String hospitalName);

    /**
     * 将选中的账号信息提交给自动化测试平台
     *
     * @param dto
     * @return
     */
    JSONObject submitAccountInfo(SubmitAccountInfoDTO dto);

    /**
     * 调用自动化测试平台，获取账号信息
     *
     * @param dto 请求参数
     * @return 结果
     */
    JSONObject getDeptUserInfo(GenLoginUserDTO dto);

    /**
     * 调用自动化测试平台，获取账号信息
     *
     * @param dto 请求参数
     * @return 结果
     */
    JSONObject getDeptUserInfo(GenLoginUserTransferDTO dto);

    /**
     * 承接参数进行接口调用
     *
     * @param dto 请求参数
     * @return jsonObject返回值
     */
    JSONObject getDeptUserInfoImpl(GenLoginUserDTO dto);

    /**
     * 调用自动化测试平台，运行场景任务
     *
     * @param taskType      【0：基础数据任务；1：业务数据】
     * @param projectInfoId
     * @return
     */
    Result autoCheckTaskRun(Integer taskType, Long projectInfoId);


    /**
     * 将场景任务置为不可运行状态并且在在线医院列表中新增一条
     *
     * @param dto
     * @return
     */
    JSONObject taskClose(AutoCheckForTask dto);


    /**
     * 查询自动化测试任务数据
     *
     * @param dto
     * @return
     */
    Result<ProjAutocheckInfoVO> selectAutoCheckInfoData(AutoCheckHospitalInfoDTO dto);

    /**
     * 获取自动化测试报告
     * @param reportId
     * @return
     */
    JSONObject getSummaryReport(String reportId);

    /**
     * 保存自动化测试报告
     * @param dto
     * @return
     */
    Result<JSONObject> saveSummaryReport(JSONObject dto);
}
