package com.msun.csm.service.formlibrary;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.exception.BusinessException;
import com.msun.core.component.implementation.api.formrepository.formstructure.api.ProductFormStructureApi;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.dto.PreviewQueryDTO;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.dto.ProductFormStructureDTO;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.dto.ProductFormStructureSaveDTO;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.dto.SheetImportDTO;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.vo.DocumentBase64VO;
import com.msun.core.component.implementation.api.formrepository.nurs.api.NursFormDictApi;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.dto.DictDataManagementDto;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.dto.DictManagementDto;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.dto.ImportDictDataValueDTO;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.dto.ImportEntryValueDTO;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.dto.NursDictDTO;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.vo.ImportDictDataValueVO;
import com.msun.core.component.implementation.api.formrepository.nurs.entity.vo.ImportEntryValueVO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.projform.LibFormTypeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.formlibrary.DictFormLibrary;
import com.msun.csm.dao.entity.formlibrary.DictFormLibraryDetail;
import com.msun.csm.dao.entity.formlibrary.LibProductForm;
import com.msun.csm.dao.entity.formlibrary.ProjFormLibraryLog;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.mapper.formlibrary.LibProductFormMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.req.formlibrary.LibFormParamerReq;
import com.msun.csm.model.req.formlibrary.LibFormReq;
import com.msun.csm.model.req.formlibrary.PreviewStyleReq;
import com.msun.csm.model.req.formlibrary.ToolLimitReq;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.resp.formlibrary.LibProductFormPageResp;
import com.msun.csm.model.resp.formlibrary.LibProductFormResp;
import com.msun.csm.model.resp.formlibrary.ProjProductFormLogResp;
import com.msun.csm.model.vo.SysConfigVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.config.SysConfigService;
import com.msun.csm.service.proj.ProjProjectFileService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjSurveyReprotServiceImpl;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;
import com.msun.csm.util.Base64MultipartFile;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.HttpUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 * @description 针对表【proj_survey_form(表单主表)】的数据库操作Service实现
 * @createDate 2024-09-14 15:15:49
 */
@Service
@Slf4j
public class LibProductFormServiceImpl extends ServiceImpl<LibProductFormMapper, LibProductForm> implements LibProductFormService {

    @Resource
    private ProductFormStructureApi productFormStructureApi;
    @Resource
    private NursFormDictApi nursFormDictApi;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Resource
    private DictFormLibraryDetailService dictFormLibraryDetailService;
    @Resource
    private DictFormLibraryService dictFormLibraryService;
    @Resource
    private LibProductFormMapper libProductFormMapper;
    @Resource
    private ProjFormLibraryLogService projFormLibraryLogService;
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Resource
    private ProjProductTaskService projProductTaskService;
    @Resource
    private UserHelper userHelper;

    @Value("${printreport.getTemplateBrief}")
    private String getTemplateBrief;

    @Value("${printreport.asyncbase64templates}")
    private String asyncbase64templates;

    @Value("${printreport.asynctaskstatus}")
    private String asynctaskstatus;

    @Value("${spring.profiles.active}")
    private String activeProfiles;


    @Lazy
    @Resource
    private ProjProjectInfoService projPorjectInfoService;

    @Lazy
    @Resource
    private BaseQueryService baseQueryService;

    @Resource
    private RedisUtil redisUtil;


    @Resource
    @Lazy
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    @Lazy
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Value("${thisProjectUrl}")
    private String tableUrlValue;

    @Lazy
    @Resource
    private SysConfigService sysConfigService;

    @Lazy
    @Resource
    private ProjProjectFileService projProjectFileService;

    /**
     * 提取方法
     *
     * @param tableUrlValuePrivate
     * @return
     */
    @Nullable
    private static String getString(String tableUrlValuePrivate) {
        if (tableUrlValuePrivate != null) {
            if (tableUrlValuePrivate.contains("-test") || tableUrlValuePrivate.contains("172")) {
                tableUrlValuePrivate = "https://************/";
            } else {
                tableUrlValuePrivate = "http://************:30355/";
            }
        }
        return tableUrlValuePrivate;
    }

    /**
     * 根据obs链接转换成文件格式
     *
     * @param obsUrl
     * @param fileName
     * @return
     * @throws IOException
     */
    public static String convertOBSUrlToMultipartFile(String obsUrl, String fileName) throws IOException {
        if (StringUtils.isNotEmpty(obsUrl) && !obsUrl.startsWith("http")) {
            obsUrl = OBSClientUtils.getTemporaryUrl(obsUrl, 3600);
        }
        // 从OBS链接获取文件内容
        URL url = new URL(obsUrl);
        URLConnection connection = url.openConnection();
        try (InputStream inputStream = connection.getInputStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[16384]; // 16Kb 的缓冲区
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            buffer.flush();
            String contentType = connection.getContentType();
            byte[] imageBytes = buffer.toByteArray();
            String base64Content = Base64.getEncoder().encodeToString(imageBytes);
            return "data:" + contentType + ";base64," + base64Content;
        }
    }

    /**
     * 分页查询表单库信息
     *
     * @param libFormReq
     * @return
     */
    @Override
    public Result<LibProductFormPageResp<LibProductFormResp>> selectLibFormByPage(LibFormReq libFormReq) {
        /**
         * 根据当前项目是否上线和是否有云护理实施产品控制拉取表单资源、批量导入云健康、跳转文书后台按钮显示权限：
         * 1.当前项目如果有云护理实施产品，展示拉取表单资源、批量导入云健康、跳转文书后台按钮，如无该产品隐藏这三个按钮
         * 2.如果当前项目含云护理实施产品，且当前项目已上线，不允许再次操作拉取表单资源、批量导入云健康、跳转文书后台
         */
        if (libFormReq.getProjectInfoId() == null) {
            return Result.fail("项目id不能为空");
        }
        // 根据有无表单id判断是否 是表单推荐跳转过来的
        if (libFormReq.getSurveyFormId() != null && !"".equals(libFormReq.getSurveyFormId())) {
            ProjSurveyForm form = projSurveyFormMapper.getSurveyFormById(libFormReq.getSurveyFormId());
            // 判断是否有工作流id并且已根据工作流id记录了推荐信息，没有则提醒到前端。
            if ((form.getReportTaskId() == null || "".equals(form.getReportTaskId())) || (form.getRecommendTemplateIds() == null || "".equals(form.getRecommendTemplateIds()))) {
                throw new CustomException("系统未检测到符合条件的推荐内容，请手动创建表单。（推荐规则说明：护理表单且使用众阳设计器）");
            }
        }
//        ProjProjectInfo projectInfo = projPorjectInfoService.selectByPrimaryKey(libFormReq.getProjectInfoId());
       /* Date date = null;
        if (ObjectUtil.isNotEmpty(projectInfo)) {
            date = projectInfo.getOnlineTime();
        }*/
        return PageHelperUtil.queryPage(libFormReq.getPageNum(), libFormReq.getPageSize(), page -> {
            List<LibProductFormResp> projHospitalInfos = libProductFormMapper.selectDataByPage(libFormReq);
            LibProductFormPageResp<LibProductFormResp> pageInfo = new LibProductFormPageResp<>(projHospitalInfos);
            pageInfo.getList().stream().forEach(item -> {
                item.setFormTypeName(LibFormTypeEnum.getMessageValueOf(item.getFormType()));
            });
            // 导入权限
            pageInfo.setImportFlag(true);
            // 拉取权限
            pageInfo.setPullFlag(true);
            // 跳转权限
            pageInfo.setJumpFlag(true);
     /*   // 从推荐跳转过来则开放权限
        if (libFormReq.getSurveyFormId() != null && !"".equals(libFormReq.getSurveyFormId())) {
            pageInfo.setImportFlag(true);
            pageInfo.setPullFlag(true);
            pageInfo.setJumpFlag(true);
        } else {
            if (date != null) {
                pageInfo.setImportFlag(false);
                pageInfo.setPullFlag(false);
                pageInfo.setJumpFlag(false);
            } else {
                // 查看项目下是否包含云护理
                List<DictProduct> list = libProductFormMapper.selectProductListData(libFormReq);
                if (list != null && list.size() > 0) {
                    pageInfo.setImportFlag(true);
                    pageInfo.setPullFlag(true);
                    pageInfo.setJumpFlag(true);
                }
            }
        }*/
            return Result.success(pageInfo);
        });

    }

    /**
     * 更新表单库信息
     *
     * @param libFormReq
     * @return
     */
    @Override
    public Result updateLibFormData(LibFormParamerReq libFormReq) {
        // 拉取资源库信息   保存到表单库
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(libFormReq.getHospitalInfoId());
        try {
            saveDeliveryPlatform(projHospitalInfo);
        } catch (Exception e) {
            log.error("拉取资源库表单数据异常:{}", e.getMessage());
            return Result.fail("拉取表单数据异常, 请拉取其他医院的进行尝试");
        }
        // 拉取字典库数据   保存到字典库
        try {
            saveDictFormData(projHospitalInfo);
        } catch (Exception e) {
            log.error("拉取资源库表单字典数据异常:{}", e.getMessage());
        }
        return Result.success();
    }

    /**
     * 发送资源库数据
     *
     * @param libFormReq
     * @return
     */
    @Override
    public Result sendLibFormData(LibFormParamerReq libFormReq) {
        // 发送到云健康
        List<Long> hospitalInfoIds = libFormReq.getHospitalInfoIds();
        if (hospitalInfoIds == null || hospitalInfoIds.size() == 0) {
            return Result.fail("请选择医院");
        }
        if (libFormReq.getIds() == null || libFormReq.getIds().size() == 0) {
            return Result.fail("请选择导入的表单");
        }
        for (Long hospitalInfoId : hospitalInfoIds) {
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(hospitalInfoId);
            try {
                // 发送资源库数据
                sendLibFormDetailData(hospitalInfo, libFormReq.getIds());
            } catch (Exception e) {
                log.error("发送资源库表单数据异常:{}", e.getMessage());
                if (hospitalInfo != null) {
                    ProjFormLibraryLog log = new ProjFormLibraryLog();
                    log.setProductFormId(0L);
                    log.setFormLibraryLogId(SnowFlakeUtil.getId());
                    log.setHospitalName(hospitalInfo.getHospitalName());
                    log.setFormType("-");
                    log.setFormName("-");
                    log.setStatus(0);
                    log.setCloudHospitalId(hospitalInfo.getCloudHospitalId());
                    projFormLibraryLogService.save(log);
                }
            }
        }
        return Result.success();
    }

    /**
     * 分页查询日志信息
     *
     * @param libFormReq
     * @return
     */
    @Override
    public Result<PageInfo<ProjProductFormLogResp>> selectLibFormLogByPage(LibFormReq libFormReq) {
        return PageHelperUtil.queryPage(libFormReq.getPageNum(), libFormReq.getPageSize(), page -> {
            List<ProjProductFormLogResp> projHospitalInfos = libProductFormMapper.selectDataLogByPage(libFormReq);
            projHospitalInfos.forEach(item -> {
                item.setFormTypeName(LibFormTypeEnum.getMessageValueOf(item.getFormType()));
            });
            PageInfo<ProjProductFormLogResp> pageInfo = new PageInfo<>(projHospitalInfos);
            return Result.success(pageInfo);
        });

    }

    /**
     * 获取表单跳转路径
     *
     * @param libFormReq
     * @return
     */
    @Override
    public Result getLibFormJumpPathData(LibFormParamerReq libFormReq) {
        ProjProductTaskParam dto = new ProjProductTaskParam();
        dto.setHospitalInfoId(libFormReq.getHospitalInfoId());
        // TODO 跳转路径CODE
        dto.setCloudProductCode("hulidanyuan-admin");
        // TODO 跳转URL
        dto.setTaskPageUrl("#/dashboard");
        try {
            return Result.success(projProductTaskService.hisLogin(dto));
        } catch (Exception e) {
            log.error("HIS登录链接获取失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("HIS登录链接获取失败 , " + e.getMessage());
        }
    }

    /**
     * @param libFormReq
     * @return
     */
    @Override
    public Result getIsToolLimit(ToolLimitReq libFormReq) {
        Integer isToolLimit = baseQueryService.queryAcceptanceExceedTimeLimit(libFormReq.getProjectInfoId(), libFormReq.getHospitalInfoId(), 2);
        if (isToolLimit != null && isToolLimit > 0) {
            return Result.success(1);
        }
        return Result.success(0);
    }

    /**
     * @param previewStyleReq
     * @return
     */
    @Override
    public Result<DocumentBase64VO> getPreviewStyle(PreviewStyleReq previewStyleReq) {
        List<LibProductForm> productFormStructures = this.libProductFormMapper.selectList(new QueryWrapper<LibProductForm>().eq("form_id", previewStyleReq.getFormId()).eq("source_hospital_id", previewStyleReq.getSourceHospitalId()));
        List<ProjHospitalInfo> hospitalInfos = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("is_deleted", 0).eq("cloud_hospital_id", previewStyleReq.getSourceHospitalId()));
        if (hospitalInfos == null || hospitalInfos.size() == 0) {
            return Result.fail("未查询到医院信息");
        }
        ProjHospitalInfo info = hospitalInfos.get(0);
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        PreviewQueryDTO queryDTO = new PreviewQueryDTO();
        queryDTO.setSheetId(previewStyleReq.getFormId());
        queryDTO.setHospitalId(info.getCloudHospitalId());
        queryDTO.setHisOrgId(info.getOrgId());
        try {
            if (CollectionUtil.isNotEmpty(productFormStructures)) {
                LibProductForm productForm = productFormStructures.get(0);
                queryDTO.setSheetType(Integer.valueOf(productForm.getFormType()));
            }
        } catch (Exception e) {
            log.error("获取表单类型异常:{}", e.getMessage());
        }
        DocumentBase64VO vo = new DocumentBase64VO();
        try {
            ResponseResult<DocumentBase64VO> result = this.productFormStructureApi.getDocumentBase64(queryDTO);
            log.info("获取表单数据:{}", result);
            if (result != null && result.getSuccess()) {
                vo = result.getData();
            } else {
                return Result.fail(result.getMessage());
            }
        } catch (Exception e) {
            log.error("获取表单数据异常:{}", e.getMessage());
            return Result.fail("获取表单预览接口数据异常");
        }
        return Result.success(vo);
    }

    /**
     * * 云护理表单资源库匹配定时任务
     * * 每50s运行一次
     * * 因推荐匹配时间较长，需提前运行匹配
     */
    @Override
    public void regularlyConvertData() {
        // 判断是否正在执行
        Object obj = redisUtil.get("regularlyConvertData");
        if ("regularlyConvertData".equals(obj)) {
            log.error("云护理表单资源库自动匹配定时转换数据正在执行中，请勿重复执行");
        } else {
            // 是否开启护理文书自动推荐功能 0关 / 1开 sys_config config_code=isOpenYunHuLiConvert
            Boolean isOpenConvert = isOpenInfo("isOpenYunHuLiConvert");
            if (!isOpenConvert) {
                log.error("云护理表单资源库自动匹配定时转换数据未开启");
                return;
            }
            // 设置执行标识
            redisUtil.set("regularlyConvertData", "regularlyConvertData", 60 * 20);
            try {
                // 1. 转换数据,将资源库众阳设计器grf转为text
                this.regularlyConvertDataStart();
                // 2. 表单推荐生成工作流
                this.surveyCompareData();
                // 3. 定时将打印报告转换数据
                getTaskStatusByTaskId();
            } finally {
                // 执行完成后删除标识
                redisUtil.del("regularlyConvertData");
            }

        }
    }

    /**
     * 是否开启定时
     *
     * @param code
     * @return
     */
    private Boolean isOpenInfo(String code) {
        Boolean isOpenConvert = false;
        SysConfigVO sysConfig = sysConfigService.selectConfigByName(code);
        if (sysConfig != null) {
            isOpenConvert = sysConfig.getConfigValue() != null && "1".equals(sysConfig.getConfigValue());
        }
        return isOpenConvert;
    }

    /**
     * 根据云护理表单工作流id查看推荐的模板
     */
    @Override
    public void getTaskStatusByTaskId() {
        log.info("根据云护理表单工作流id查看推荐的模板===start");
        // 判断是否正在执行
        Object obj = redisUtil.get("getTaskStatusByTaskId");
        if ("getTaskStatusByTaskId".equals(obj)) {
            log.error("云护理表单定时推荐数据正在执行中，请勿重复执行");
            return;
        }
        // 设置执行标识
        redisUtil.set("getTaskStatusByTaskId", "getTaskStatusByTaskId", 60 * 20);
        // 查询所有有工作流并切未同步数据的数据
        List<ProjSurveyForm> surveyReports = this.projSurveyReportMapper.selectListAllNotWorkProcssCompar();
        if (surveyReports != null && surveyReports.size() > 0) {
            for (ProjSurveyForm data : surveyReports) {
                Map<String, String> paramData = Maps.newHashMap();
                paramData.put("task_id", data.getReportTaskId());
                //调用接口
                try {
                    log.info("根据云护理表单工作流id查看推荐的模板:{}", JSONObject.toJSONString(paramData));
                    Map<String, Object> stringObjectMap = HttpUtil.doPost(asynctaskstatus, JSONObject.toJSONString(paramData), null);
                    log.error("根据云护理表单工作流id查看推荐的模板:{}", stringObjectMap);
                    // 解析返回结果： 将推荐内容存入表单表， 用于推荐。
                    String msg = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("success"));
                    Map jsonObject = (Map) stringObjectMap.get("data");
                    if ("true".equals(msg) && jsonObject != null) {
                        // 查询到结果进行更新表格内容
                        log.error("根据云护理表单工作流id查看推荐的模板:{}", jsonObject);
                        String ids = "";
                        Map result = (Map) jsonObject.get("result");
                        List<Map> resultlist = new ArrayList<>();
                        if (result != null) {
                            resultlist = (List<Map>) result.get("data");
                        }
                        if (resultlist != null && !resultlist.isEmpty()) {
                            for (Map map : resultlist) {
                                String id = com.msun.csm.util.StringUtils.nvl(map.get("id"));
                                if (StringUtils.isNotEmpty(id)) {
                                    ids += id + ",";
                                }
                            }
                            if (StringUtils.isNotEmpty(ids)) {
                                ids = ids.substring(0, ids.length() - 1);
                            }
                        }
                        if (StringUtils.isNotEmpty(ids)) {
                            ProjSurveyForm report = projSurveyFormMapper.getSurveyFormById(data.getSurveyFormId());
                            report.setRecommendTemplateIds(ids);
                            projSurveyFormMapper.updateById(report);
                        }
                    }
                } catch (Exception e) {
                    log.error("云护理表单推荐异常:", e);
                }
            }
        }
        // 执行完成后删除标识
        redisUtil.del("getTaskStatusByTaskId");
    }

    /**
     * 定时根据任务, 预处理打印报表资源库推荐
     */
    @Override
    public void regularlyPrintReportConvertData() {
        // 是否开启护预处理打印报表资源库
        Boolean isOpenConvert = isOpenInfo("isOpenPrintReportConvert");
        if (!isOpenConvert) {
            log.error("打印报表定时转换数据未开启");
            return;
        }
        // 判断是否正在执行
        Object obj = redisUtil.get("regularlyPrintReportConvertData");
        if ("regularlyPrintReportConvertData".equals(obj)) {
            log.error("打印报表定时转换数据正在执行中，请勿重复执行");
        } else {
            // 设置执行标识
            redisUtil.set("regularlyPrintReportConvertData", "regularlyPrintReportConvertData", 60 * 20);
            // 报表推荐生成工作流，报表中台将自动比对数据将结果存储
            this.surveyPrintReportCompareData();
            // 同步打印报表完成图片， 未上线且报表状态是审核通过或制作中的单据。
            this.asyncGetImgByNodeCode();
            // 执行完成后删除标识
            redisUtil.del("regularlyPrintReportConvertData");
        }
    }

    /**
     * 同步打印报表完成图片， 未上线且报表状态是审核通过或制作中的单据。
     */
    private void asyncGetImgByNodeCode() {
        // 1. 查询需同步图片的打印报表数据
        List<ProjSurveyReport> surveyReports = this.projSurveyReportMapper.selectListAllReprtNotImgByNodeCode();
        // 2. 调用接口获取打印报表默认工作站样式图片后 更新数据库数据
        if (surveyReports != null && surveyReports.size() > 0) {
            for (ProjSurveyReport report : surveyReports) {
                this.asynGetImgInfoSaveData(report);
            }
        }
    }

    /**
     * 调用接口获取打印报表默认工作站样式图片后 更新数据库数据
     *
     * @param report
     */
    private void asynGetImgInfoSaveData(ProjSurveyReport report) {
        // 获取打印平台公共路径
        String tableUrlValuePrivate = getString(tableUrlValue);
        String url = tableUrlValuePrivate + "msun-print-app-center/hospitalTemplate/getDefaultThumbnail";
        Map<String, Object> param = new HashMap<>(5);
        param.put("printNodeCode", report.getPrintDataCode());
        param.put("hospitalId", report.getCloudHospitalId());
        try {
            HttpHeaders headers = ProjSurveyReprotServiceImpl.getHeadersByUserId(null);
            log.error("调用报表平台获取默认图片参数=={}", JSONObject.toJSONString(param));
            Map<String, Object> reslutMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), headers);
            log.error("调用报表平台返回图片路径参数=={}", reslutMap);
            String msg = com.msun.csm.util.StringUtils.nvl(reslutMap.get("code"));
            String dataMsg = com.msun.csm.util.StringUtils.nvl(reslutMap.get("data"));
            // 调用转化后结果解析。
            if ("200".equals(msg) && !dataMsg.isEmpty() && !"null".equals(dataMsg)) {
                try {
                    // 将base64图片转为MultipartFile
                    // 上传OBS 并保存到pro_project_file表
                    // 保存至打印报表完成图片内容处
                    MultipartFile mul = Base64MultipartFile.base64ToMultipart(dataMsg, "default.png", "image/png", report.getSurveyReportId());
                    UploadFileReq req = new UploadFileReq(report.getProjectInfoId(), "", mul, "reportTaskId", false, null);
                    Result<ProjProjectFileExtend> result = projProjectFileService.uploadFile(req, null);
                    if (result != null && result.getData() != null && result.isSuccess()) {
                        String projectFileId = String.valueOf(result.getData().getProjectFileId());
                        ProjSurveyReport ss = projSurveyReportMapper.selectById(report.getSurveyReportId());
                        ss.setFinishImgs(projectFileId);
                        this.projSurveyReportMapper.updateById(ss);
                    }
                    log.error("调用报表平台返回图片路径参数=={}", dataMsg);
                } catch (Exception e) {
                    log.error("报表平台返回图片路径参数异常", e);
                }

            }
        } catch (Exception e) {
            log.error("请求报表平台错误", e);
        }
    }

    /**
     * 报表推荐生成工作流，报表中台将自动比对数据将结果存储
     */
    private void surveyPrintReportCompareData() {
        // 查询所有有工作流并切未同步数据的数据
        List<ProjSurveyReport> surveyReports = this.projSurveyReportMapper.selectListAllReprtNotCompar();
        if (surveyReports != null && surveyReports.size() > 0) {
            List<Long> surveyReportIds = new ArrayList<>();
            for (ProjSurveyReport report : surveyReports) {
                // 图片转换base64
                // 根据地址不同转化逻辑不同  将obs上图片和服务器的图片都转化为base64格式
                if (report.getSurveyImgs().contains("/tduck-api/u/")) {
                    try {
                        report.setSurveyImgs(convertOBSUrlToMultipartBase64(report.getSurveyImgs(), report.getSupplementImgs()));
                    } catch (Exception ss) {
                        log.error("ss", ss);
                    }
                } else if (report.getSurveyImgs().contains("https://chis-obs-lis.obs")) {
                    try {
                        report.setSurveyImgs(convertOBSUrlToMultipartFile(report.getSurveyImgs(), report.getSupplementImgs()));
                    } catch (Exception ss) {
                        log.error("ss");
                    }
                } else if (report.getSurveyImgs().contains("imsp/csm/")) {
                    try {
                        report.setSurveyImgs(convertOBSUrlToMultipartFile(report.getSurveyImgs(), report.getSupplementImgs()));
                    } catch (Exception ss) {
                        log.error("ss");
                    }
                }
                if (!surveyReportIds.contains(report.getSurveyReportId())) {
                    surveyReportIds.add(report.getSurveyReportId());
                }
            }
            for (Long surveyReportId : surveyReportIds) {
                List<String> imgList = new ArrayList<>();
                ProjSurveyReport reportss = new ProjSurveyReport();
                for (ProjSurveyReport report : surveyReports) {
                    // 批量处理 将报表相同id的作为一个值，用于调用打印报表平台传图片
                    if (report.getSurveyReportId().equals(surveyReportId)) {
                        imgList.add(report.getSurveyImgs());
                        reportss = report;
                    }
                }
                if (imgList != null && imgList.size() > 0) {
                    // 调用报表平台传图片
                    this.asyncbatchSendData(reportss, imgList);
                }
            }
        }
    }

    /**
     * 调用报表平台数据
     *
     * @param report
     * @param imgList
     */
    private void asyncbatchSendData(ProjSurveyReport report, List<String> imgList) {
        String tableUrlValuePrivate = getString(tableUrlValue);
        String url = tableUrlValuePrivate + "msun-print-app-center/resourceLibrary/thumbnailToText";
        Map<String, Object> param = new HashMap<>();
        param.put("base64", imgList);
        param.put("printNodeCode", report.getPrintDataCode());
        try {
            HttpHeaders headers = ProjSurveyReprotServiceImpl.getHeadersByUserId(null);
            Map<String, Object> reslutMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), headers);
            log.error("调用报表平台返回参数=={}", reslutMap);
            String msg = com.msun.csm.util.StringUtils.nvl(reslutMap.get("code"));
            String dataMsg = com.msun.csm.util.StringUtils.nvl(reslutMap.get("data"));
            // 调用转化后结果解析。 记录任务id。 用于查看推荐内容
            if ("200".equals(msg) && dataMsg != null && !"error".equals(dataMsg)) {
                ProjSurveyReport ss = projSurveyReportMapper.selectById(report.getSurveyReportId());
                ss.setReportTaskId(dataMsg);
                this.projSurveyReportMapper.updateById(ss);
            }
        } catch (Exception e) {
            log.error("请求报表平台错误", e);
        }
    }

    /**
     * 表单推荐
     */
    private void surveyCompareData() {
        log.info("云护理表单资源库推荐=====");
        // 多张图片的只查第一张的图片
        List<ProjSurveyForm> surveyReports = this.projSurveyReportMapper.selectListAllNotCompar();
        // 调用报表推荐接口。将文件转为base64 . 每10条调用一次
        // 查询所有已转换的数据。 没有转化的数据则跳过
        // 获取所有已转换表单文本的数据
        List<LibProductForm> productFormStructures = this.libProductFormMapper.selectListAllHaveParsedText();
        // 所有单据
        List<Map<String, Object>> list = new ArrayList<>();
        // 评估单
        List<Map<String, Object>> list1 = new ArrayList<>();
        // 记录单
        List<Map<String, Object>> list2 = new ArrayList<>();
        // 体温单
        List<Map<String, Object>> list3 = new ArrayList<>();
        // 知情文件
        List<Map<String, Object>> list4 = new ArrayList<>();
        // 拼接调用工作流参数。
        if (!productFormStructures.isEmpty()) {
            for (LibProductForm form : productFormStructures) {
                Map<String, Object> obj = new HashMap<>();
                obj.put("id", String.valueOf(form.getProductFormId()));
                obj.put("title", "");
                obj.put("content", form.getParsedText());
                obj.put("rawData", "");
                list.add(obj);
                switch (form.getFormType()) {
                    // 评估单
                    case "1":
                        list1.add(obj);
                        break;
                    // 记录单单
                    case "2":
                        list2.add(obj);
                        break;
                    // 体温单
                    case "30001":
                        list3.add(obj);
                        break;
                    // 体温单
                    case "30002":
                        list3.add(obj);
                        break;
                    // 知情文件
                    case "4":
                        list.add(obj);
                        break;
                    default:
                        break;
                }
            }
        }
        Map<String, Object> mapParamer = new HashMap<>();
        if (surveyReports.isEmpty() || productFormStructures.isEmpty()) {
            return;
        }
        // 判断图片是否合法，合法后进行调用转化
        Boolean bUse = true;
        for (ProjSurveyForm report : surveyReports) {
            boolean isNull = false;
            // 将表单类型根据类型进行分类， 减少比对内容
            switch (report.getTypeCode()) {
                // 评估单
                case "evaluat":
                    mapParamer.put("templates", list1);
                    isNull = list1 == null || list1.isEmpty();
                    break;
                // 记录单
                case "record":
                    mapParamer.put("templates", list2);
                    isNull = list2 == null || list2.isEmpty();
                    break;
                // 体温单
                case "temperature":
                    mapParamer.put("templates", list3);
                    isNull = list3 == null || list3.isEmpty();
                    break;
                default:
                    mapParamer.put("templates", list);
                    isNull = list == null || list.isEmpty();
                    break;
            }
            if (isNull) {
                continue;
            }
            // 图片转换base64
            // 根据地址不同转化逻辑不同  将obs上图片和服务器的图片都转化为base64格式
            if (report.getSurveyImgs().contains("/tduck-api/u/")) {
                try {
                    report.setSurveyImgs(convertOBSUrlToMultipartBase64(report.getSurveyImgs(), report.getSupplementImgs()));
                    bUse = true;
                } catch (Exception ss) {
                    log.error("ss", ss);
                    bUse = false;
                }
            } else if (report.getSurveyImgs().contains("https://chis-obs-lis.obs")) {
                try {
                    report.setSurveyImgs(convertOBSUrlToMultipartFile(report.getSurveyImgs(), report.getSupplementImgs()));
                    bUse = true;
                } catch (Exception ss) {
                    log.error("ss");
                    bUse = false;
                }
            } else if (report.getSurveyImgs().contains("imsp/csm/")) {
                try {
                    report.setSurveyImgs(convertOBSUrlToMultipartFile(report.getSurveyImgs(), report.getSupplementImgs()));
                    bUse = true;
                } catch (Exception ss) {
                    log.error("ss");
                    bUse = false;
                }
            }
            // 判断图片是否合法，合法后进行调用转化
            if (bUse) {
                mapParamer.put("base64", report.getSurveyImgs());
                this.asyncbatch(report, mapParamer);
            }
        }
    }

    /**
     * 服务器地址转base64文件
     *
     * @param surveyImgs
     * @param supplementImgs
     * @return
     */
    private String convertOBSUrlToMultipartBase64(String surveyImgs, String supplementImgs) throws IOException {
        String urlee = surveyImgs.substring(surveyImgs.indexOf("tduck-api"));
        if ("prod".equals(activeProfiles)) {
            urlee = "http://************/" + urlee;
        } else {
            urlee = "http://************/" + urlee;
        }
        // 从OBS链接获取文件内容
        URL url = new URL(urlee);
        URLConnection connection = url.openConnection();
        try (InputStream inputStream = connection.getInputStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[16384]; // 16Kb 的缓冲区
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            buffer.flush();
            String contentType = connection.getContentType();
            byte[] imageBytes = buffer.toByteArray();
            String base64Content = Base64.getEncoder().encodeToString(imageBytes);
            return "data:" + contentType + ";base64," + base64Content;
        } catch (Exception e) {
            log.error("转换异常", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 将云护理资源库推荐grf转为text
     */
    private void regularlyConvertDataStart() {
        log.info("云护理资源库推荐定时转换数据,将资源库grf转为text=====");
        List<LibProductForm> productFormStructures = this.libProductFormMapper.selectListAll();
        if (productFormStructures != null && !productFormStructures.isEmpty()) {
            for (LibProductForm data : productFormStructures) {
                Map<String, String> paramTime = Maps.newHashMap();
                paramTime.put("grf", data.getDesignerGrf());
                paramTime.put("data", "{}");
                //调用接口
                try {
                    log.info("将云护理资源库推荐grf转为text:{}", JSONObject.toJSONString(data.getProductFormId()));
                    Map<String, Object> stringObjectMap = HttpUtil.doPost(getTemplateBrief, JSONObject.toJSONString(paramTime), null);
                    log.error("将云护理资源库推荐grf转为textstringObjectMap:{}", stringObjectMap);
                    String msg = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("success"));
                    String dataMsg = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("data"));
                    // 调用转化后结果解析。 成功的数据且不是error才进行更新资源库众阳设计器模板解析后文字字段
                    if ("true".equals(msg) && dataMsg != null && !"error".equals(dataMsg)) {
                        LibProductForm form = this.libProductFormMapper.selectById(data.getProductFormId());
                        form.setParsedText(dataMsg);
                        this.libProductFormMapper.updateById(form);
                    }
                } catch (Exception e) {
                    log.error("将云护理资源库推荐grf转为text异常:", e);
                }
            }
        }
    }

    /**
     * 异步使用base64和模板列表进行匹配
     *
     * @param report
     * @param mapParamer
     */
    private void asyncbatch(ProjSurveyForm report, Map<String, Object> mapParamer) {
        try {
            Map<String, Object> stringObjectMap = HttpUtil.doPost(asyncbase64templates, JSONObject.toJSONString(mapParamer), null);
            log.error("异步使用base64和模板列表进行匹配stringObjectMap:{}", stringObjectMap);
            String msg = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("success"));
            Map jsonObject = (Map) stringObjectMap.get("data");
            String taskId = jsonObject != null ? String.valueOf(jsonObject.get("task_id")) : "";
            // 调用成功后插入表单表任务id
            if ("true".equals(msg) && !"".equals(taskId)) {
                ProjSurveyForm form = this.projSurveyFormMapper.getSurveyFormById(report.getSurveyFormId());
                form.setReportTaskId(taskId);
                this.projSurveyFormMapper.updateById(form);
            }
        } catch (Exception es) {
            log.error("定时转换云护理表单数据,将调研图片传入中台进行匹配错误==》" + es);
        }
    }

    /**
     * 发送表单信息
     *
     * @param info
     */
    private void sendLibFormDetailData(ProjHospitalInfo info, List<Long> ids) throws Exception {
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        List<LibProductForm> libProductFormList = libProductFormMapper.selectList(new QueryWrapper<LibProductForm>().eq("is_deleted", 0).eq("form_source", "nurs").in("product_form_id", ids));
        List<ProductFormStructureDTO> productFormStructureList = new ArrayList<>();
        for (LibProductForm data : libProductFormList) {
            String formStructure = data.getFormStructure();
            if (ObjectUtil.isNotEmpty(formStructure)) {
                JSONObject jsonObject = JSONObject.parseObject(formStructure);
                jsonObject.put("outHospitalFinishFlag", new ArrayList<>());
                data.setFormStructure(jsonObject.toJSONString());
            }
            ProductFormStructureDTO productFormStructureDTO = new ProductFormStructureDTO();
            BeanUtil.copyProperties(data, productFormStructureDTO);
            productFormStructureDTO.setId(data.getProductFormId());
            productFormStructureDTO.setHospitalId(info.getCloudHospitalId());
            productFormStructureDTO.setHisOrgId(info.getOrgId());
            productFormStructureList.add(productFormStructureDTO);
        }
        ProductFormStructureSaveDTO dto = new ProductFormStructureSaveDTO();
        dto.setList(productFormStructureList);
        dto.setHospitalId(info.getCloudHospitalId());
        dto.setHisOrgId(info.getOrgId());
        log.info("入参：", JSON.toJSONString(dto));
        ResponseResult<Boolean> result = this.productFormStructureApi.importSheet2NurseDoc(dto);
        if (!result.isSuccess()) {
            log.error("call interface failed:com.msun.core.component.implementation.api.formrepository.formstructure.api.ProductFormStructureApi.importSheet2NurseDoc;productFormStructureList={};response={}", productFormStructureList, result);
            throw BusinessException.build(result.convert());
        } else {
            // 记录日志
            log.info("调用接口返回：{}", result);
            List<ProjFormLibraryLog> logs = productFormStructureList.stream().map(data -> {
                ProjFormLibraryLog log = new ProjFormLibraryLog();
                log.setProductFormId(data.getId());
                log.setFormLibraryLogId(SnowFlakeUtil.getId());
                log.setCloudHospitalId(info.getCloudHospitalId());
                log.setHospitalName(info.getHospitalName());
                log.setFormType(data.getFormType());
                log.setFormName(data.getFormName());
                log.setStatus(1);
                return log;
            }).collect(Collectors.toList());
            projFormLibraryLogService.saveBatch(logs);
        }
        this.importDictValueNurseDoc(info);

    }

    /**
     * 发送字典数据
     *
     * @param info
     */
    private void importDictValueNurseDoc(ProjHospitalInfo info) {
        try {
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            LambdaQueryWrapper<DictFormLibrary> query = new LambdaQueryWrapper<DictFormLibrary>().eq(DictFormLibrary::getHisOrgId, 0).eq(DictFormLibrary::getVersion, 0);
            List<DictFormLibrary> nursDicts = this.dictFormLibraryService.list(query);
            List dictList = new ArrayList<DictManagementDto>();
            for (DictFormLibrary dict : nursDicts) {
                DictManagementDto managementDto = new DictManagementDto();
                BeanUtil.copyProperties(dict, managementDto);
                managementDto.setId(dict.getFormLibraryId());
                managementDto.setCode(dict.getFormLibraryCode());
                managementDto.setName(dict.getFormLibraryName());
                dictList.add(managementDto);
            }
            ImportEntryValueDTO importEntryValueDTO = new ImportEntryValueDTO();
            importEntryValueDTO.setRecords(dictList);
            importEntryValueDTO.setHospitalId(info.getCloudHospitalId());
            importEntryValueDTO.setHisOrgId(info.getOrgId());
            ResponseResult<Boolean> result = this.nursFormDictApi.importDictValue2NurseDoc(importEntryValueDTO);
            if (!result.isSuccess()) {
                log.error("call interface failed:com.msun.core.component.implementation.api.formrepository.nurs.api.NursFormDictApi.importDictValue2NurseDoc;importEntryValueDTO={};response={}", importEntryValueDTO, result);
                throw BusinessException.build(result.convert());
            } else {
                log.info("调用接口返回result1字典：{}", result);
            }
            LambdaQueryWrapper<DictFormLibraryDetail> query1 = new LambdaQueryWrapper<DictFormLibraryDetail>().eq(DictFormLibraryDetail::getHisOrgId, 0).eq(DictFormLibraryDetail::getVersion, 0);
            List<DictFormLibraryDetail> nursDictDataList = this.dictFormLibraryDetailService.list(query1);
            List<DictDataManagementDto> dataValueList = new ArrayList<>();
            for (DictFormLibraryDetail nursDictData : nursDictDataList) {
                DictDataManagementDto dictDataDto = new DictDataManagementDto();
                BeanUtil.copyProperties(nursDictData, dictDataDto);
                dictDataDto.setId(nursDictData.getFormLibraryDetailId());
                dictDataDto.setDictId(nursDictData.getFormLibraryId());
                dictDataDto.setCode(nursDictData.getFormLibraryDetailCode());
                dictDataDto.setName(nursDictData.getFormLibraryDetailName());
                dictDataDto.setSeq(nursDictData.getOrderNo());
                dataValueList.add(dictDataDto);
            }
            ImportDictDataValueDTO importDictDataValueDTO = new ImportDictDataValueDTO();
            importDictDataValueDTO.setHospitalId(info.getCloudHospitalId());
            importDictDataValueDTO.setHisOrgId(info.getOrgId());
            importDictDataValueDTO.setRecords(dataValueList);
            ResponseResult<Boolean> result1 = this.nursFormDictApi.importDictDataValue2NurseDoc(importDictDataValueDTO);
            if (!result1.isSuccess()) {
                log.error("call interface failed:com.msun.core.component.implementation.api.formrepository.nurs.api.NursFormDictApi.importDictDataValue2NurseDoc;importDictDataValueDTO={};response={}", importDictDataValueDTO, result);
                throw BusinessException.build(result.convert());
            } else {
                log.info("调用接口返回result1：{}", result1);
            }
        } catch (Exception e) {
            log.error("导入字典库数据异常:{}", e.getMessage());
        }
    }

    /**
     * 拉取资源库表单数据
     *
     * @param info
     */
    private void saveDeliveryPlatform(ProjHospitalInfo info) throws Exception {
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 删除该医院下的所有数据
        this.remove(new QueryWrapper<LibProductForm>().eq("source_hospital_id", info.getCloudHospitalId()));
        //使用CountDownLatch保证所有线程都执行完成
        for (int i = 1; i < 5; i++) {
            SheetImportDTO sheetImportDTO = new SheetImportDTO();
            sheetImportDTO.setHospitalId(info.getCloudHospitalId());
            sheetImportDTO.setNumber(100);
            sheetImportDTO.setHisOrgId(info.getOrgId());
            sheetImportDTO.setSheetTypeList(Collections.singletonList(i));
            LambdaQueryWrapper<LibProductForm> query = new LambdaQueryWrapper<LibProductForm>().eq(LibProductForm::getFormSource, "nurs").eq(LibProductForm::getSourceHospitalId, info.getCloudHospitalId()).eq(i != 3, LibProductForm::getFormType, String.valueOf(i)).in(i == 3, LibProductForm::getFormType, new String[] {"30001", "30002"});
            try {
                List<LibProductForm> productFormStructures = this.libProductFormMapper.selectList(query);
                List<Long> formIdList = productFormStructures.stream().map(LibProductForm::getFormId).collect(Collectors.toList());
                sheetImportDTO.setFormIdList(!formIdList.isEmpty() ? formIdList : Collections.singletonList(0L));
                ResponseResult<List<ProductFormStructureDTO>> result = this.productFormStructureApi.save2DeliveryPlatform(sheetImportDTO);
                if (!result.isSuccess()) {
                    log.error("call interface failed:com.msun.core.component.implementation.api.formrepository.formstructure.api.ProductFormStructureApi.save2DeliveryPlatform;sheetImportDTO={};response={}", sheetImportDTO, result);
                    throw BusinessException.build(result.convert());
                }
                List<ProductFormStructureDTO> data = result.getData();
                if (Objects.isNull(data) || data.isEmpty()) {
                    break;
                }
                List<LibProductForm> list = new ArrayList<>();
                for (ProductFormStructureDTO dto : data) {
                    // 交付平台已经存在的交单，不存
                    if (formIdList.contains(dto.getFormId())) {
                        continue;
                    }
                    LibProductForm productFormStructure = new LibProductForm();
                    BeanUtil.copyProperties(dto, productFormStructure);
                    productFormStructure.setProductFormId(SnowFlakeUtil.getId());
                    productFormStructure.setDesignerGrf(dto.getDesignerGrf());
                    // TODO 此处先默认云护理的数据
                    productFormStructure.setYyProductId(4664L);
                    productFormStructure.setGrf(dto.getGrf());
                    log.error("userhelper", userHelper);
                    if (userHelper != null) {
                        if (userHelper.getCurrentUser() != null) {
                            if (userHelper.getCurrentUser().getSysUserId() != null) {
                                productFormStructure.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                            }
                        }
                    }
                    list.add(productFormStructure);
                }
                if (!list.isEmpty()) {
                    this.saveBatch(list);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }


    public void saveDictFormData(ProjHospitalInfo info) throws Exception {
        //组装接口API.
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        NursDictDTO nursDictDTO = new NursDictDTO();
        nursDictDTO.setHospitalId(info.getCloudHospitalId());
        nursDictDTO.setHisOrgId(info.getOrgId());
        // 获取指定医院的护士站表单字典数据
        ResponseResult<ImportEntryValueVO> result1 = this.nursFormDictApi.exportDictValue2DeliveryPlatform(nursDictDTO);
        // 查询交付平台上关于护士站表单字典数据
        LambdaQueryWrapper<DictFormLibrary> query1 = new LambdaQueryWrapper<DictFormLibrary>().eq(DictFormLibrary::getHisOrgId, 0).eq(DictFormLibrary::getVersion, 0);
        Map<String, DictFormLibrary> map = dictFormLibraryService.list(query1).stream().collect(Collectors.toMap(DictFormLibrary::getFormLibraryCode, DictFormLibrary -> DictFormLibrary, (key1, key2) -> key2));
        if (!Objects.isNull(result1.getData())) {
            List<DictManagementDto> records = result1.getData().getRecords();
            List nursDictList = new ArrayList<DictFormLibrary>();
            for (DictManagementDto data : records) {
                // 字典根据code过滤,已存入交付平台的字典不在保存
                DictFormLibrary nursDict = map.get(data.getCode());
                if (Objects.isNull(nursDict)) {
                    DictFormLibrary newNursDict = new DictFormLibrary();
                    BeanUtil.copyProperties(data, newNursDict);
                    newNursDict.setFormLibraryId(data.getId());
                    newNursDict.setFormLibraryCode(data.getCode());
                    newNursDict.setFormLibraryName(data.getName());
                    newNursDict.setHisOrgId(0L);
                    nursDictList.add(newNursDict);
                }
            }
            if (!nursDictList.isEmpty()) {
                this.dictFormLibraryService.saveBatch(nursDictList);
            }
        }
        ResponseResult<ImportDictDataValueVO> result2 = this.nursFormDictApi.exportDictDataValue2DeliveryPlatform(nursDictDTO);
        LambdaQueryWrapper<DictFormLibraryDetail> query2 = new LambdaQueryWrapper<DictFormLibraryDetail>().eq(DictFormLibraryDetail::getHisOrgId, 0).eq(DictFormLibraryDetail::getVersion, 0);
        Map<String, DictFormLibraryDetail> nursDictMap = this.dictFormLibraryDetailService.list(query2).stream().collect(Collectors.toMap(DictFormLibraryDetail::getFormLibraryDetailCode, NursDictData -> NursDictData, (key1, key2) -> key2));
        if (!Objects.isNull(result2.getData())) {
            List<DictDataManagementDto> records1 = result2.getData().getRecords();
            List nursDictDataList = new ArrayList<DictFormLibraryDetail>();
            for (DictDataManagementDto dictManagementDto : records1) {
                DictFormLibraryDetail nursDictData = nursDictMap.get(dictManagementDto.getCode());
                if (Objects.isNull(nursDictData)) {
                    DictFormLibraryDetail dictData = new DictFormLibraryDetail();
                    BeanUtil.copyProperties(dictManagementDto, dictData);
                    dictData.setFormLibraryDetailCode(dictManagementDto.getCode());
                    dictData.setFormLibraryDetailName(dictManagementDto.getName());
                    dictData.setFormLibraryId(dictManagementDto.getDictId());
                    dictData.setOrderNo(dictManagementDto.getSeq());
                    dictData.setFormLibraryDetailId(dictManagementDto.getId());
                    dictData.setHisOrgId(0L);
                    nursDictDataList.add(dictData);
                }
            }
            if (!nursDictDataList.isEmpty()) {
                this.dictFormLibraryDetailService.saveBatch(nursDictDataList);
            }
        }
    }

}
