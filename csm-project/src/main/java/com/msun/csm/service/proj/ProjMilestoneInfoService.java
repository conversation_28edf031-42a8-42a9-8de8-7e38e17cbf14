package com.msun.csm.service.proj;


import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneManualUpdateLog;
import com.msun.csm.model.dto.CompSurveyProductMilestone;
import com.msun.csm.model.dto.MilestoneInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneManualUpdateLogQueryDto;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.param.UpdateProjMilestoneInfoAfterAuditPmoParam;
import com.msun.csm.model.req.project.UpdateProjectContentReq;
import com.msun.csm.model.resp.project.MileStoneInfoResp;
import com.msun.csm.model.vo.ProjMilestoneInfoVO;
import com.msun.csm.model.vo.user.SysUserVO;

/**
 * @Description:
 * @Author: zhouzhaoyu
 * @Date: 2024/5/06
 */
public interface ProjMilestoneInfoService {


    /**
     * 根据milestoneInfoDTO查询项目里程碑信息
     *
     * @param milestoneInfoDTO
     * @return
     */
    List<ProjMilestoneInfo> getMilestoneInfoByProjectInfoId(ProjMilestoneInfoDTO milestoneInfoDTO);


    /**
     * 根据项目id完善proj_milestone_info表信息
     *
     * @param dto
     * @return
     */
    Result<MileStoneInfoResp> generateMilestoneInfo(ProjMilestoneInfoDTO dto);


    /**
     * 更新里程碑节点信息
     *
     * @param dto
     * @return
     */
    Boolean updateMilestone(UpdateMilestoneDTO dto);

    /**
     * 更新里程碑节点信息, 可指定节点完成人员
     *
     * @param dto       里程碑
     * @param sysUserVO 更新人
     * @return 是否更新成功
     */
    Boolean updateMilestoneImpl(UpdateMilestoneDTO dto, SysUserVO sysUserVO);

    /**
     * @param dto 更新的里程碑
     * @return boolean
     */
    Boolean updateMilestoneById(UpdateMilestoneDTO dto);

    /**
     * 完成里程碑节点信息
     *
     * @param dto
     * @return
     */
    Result<Boolean> compMilestone(UpdateMilestoneDTO dto);

    /**
     * 完成里程碑节点信息
     *
     * @param dto
     * @return
     */
    Result<Boolean> compSurveyProductMilestone(CompSurveyProductMilestone dto);

    /**
     * 说明: 校验当前节点能否操作
     *
     * @param id
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/10 11:31
     * @remark: Copyright
     */
    Result verifyProjMilestoneInfoVO(Long id);

    /**
     * 说明: 是否完成入驻
     *
     * @param projectInfoId
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/7/2 17:26
     * @remark: Copyright
     */
    Result verifyStageEntry(Long projectInfoId);

    /**
     * 说明: 校验当前节点是否已经提交完成状态 false：未完成  true：已完成
     *
     * @param milestoneInfoId
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/8/28 9:36
     * @remark: Copyright
     */
    Boolean verifyMilestoneStatus(Long milestoneInfoId);

    /**
     * 通过项目Id查询准备阶段和测试阶段的里程碑阶段信息
     *
     * @param dto
     * @return
     */
    List<ProjMilestoneInfoVO> getMilestoneInfoByProjectId(SelectHospitalDTO dto);

    /**
     * 批量更新里程碑信息
     *
     * @param list
     * @return
     */
    Integer batchUpdateMilestoneInfo(List<MilestoneInfoDTO> list);


    /**
     * 通过项目id和code查询节点id
     *
     * @return
     */
    ProjMilestoneInfo getMilestoneInfo(Long projectInfoId, String code);

    Boolean revertProjMilestoneInfoStatus(UpdateProjMilestoneInfoAfterAuditPmoParam updateProjMilestoneInfoAfterAuditPmoParam);

    /**
     * 更新特殊节点里程碑状态
     * 当前只有部署申请节点会调用
     *
     * @param dto 请求参数
     * @return Result<Boolean>
     */
    Result<Boolean> compMilestoneSpetial(UpdateMilestoneDTO dto);

    /**
     * 根据id查询里程碑信息
     *
     * @param milestoneInfoId
     * @return
     */
    Result<ProjMilestoneInfo> selectById(Long milestoneInfoId);

    /**
     * 更新项目里程碑产品数据
     *
     * @param updateProjectContentReq
     * @return
     */
    Result updateProjectMilestoneProductData(UpdateProjectContentReq updateProjectContentReq);

    /**
     * 获取项目里程碑和项目产品参数
     *
     * @param updateProjectContentReq
     * @return
     */
    Result<List<BaseIdNameResp>> getMilstoneAndProductParamer(UpdateProjectContentReq updateProjectContentReq);

    /**
     * 获取项目里程碑手动更新操作日志的分页列表
     *
     * @param queryDto
     * @return
     */
    Result<PageInfo<ProjMilestoneManualUpdateLog>> queryMilestoneStatusOperateLogPageList(ProjMilestoneManualUpdateLogQueryDto queryDto);

    /**
     * 查询当前项目状态，校验当前节点可进行的操作
     * @param id
     * @return
     */
    Result<Integer> verifyProjMilestoneInfoIsCanUseType(Long id);

    /**
     * 完成里程碑节点信息
     *
     * @param dto
     * @return
     */
    Result<Boolean> preCompleteMilestoneValidate(UpdateMilestoneDTO dto);

    /**
     * 为null时符合校验条件，校验通过
     *
     * @param projectInfoId
     * @param milestoneNodeEnum
     * @return
     */
    Result<Boolean> checkFileRule(Long projectInfoId, MilestoneNodeEnum milestoneNodeEnum);
}
