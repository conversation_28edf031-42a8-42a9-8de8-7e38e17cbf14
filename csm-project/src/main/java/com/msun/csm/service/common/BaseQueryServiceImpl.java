package com.msun.csm.service.common;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.enums.projform.CustomTypeEnum;
import com.msun.csm.common.enums.projform.LibFormTypeEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectUpgradationTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseHospitalNameResp;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameExtendResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.BaseProjectInfoResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SelectOption;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictBusinessStatus;
import com.msun.csm.dao.entity.knowledge.BackendTeamInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectMemberRoleInfo;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.report.DictPaperSize;
import com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper;
import com.msun.csm.dao.mapper.dict.DictAgentScenarioConfigMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper;
import com.msun.csm.dao.mapper.formlibnew.DictOnlineBusinessStatusMapper;
import com.msun.csm.dao.mapper.proj.ProjContractCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewTypeUserMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.report.DictPaperSizeMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.dto.DirUserDTO;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.param.GetSearchPeopleTypeParam;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.vo.ProjProjectMemberVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.dict.DictBusinessStatusService;
import com.msun.csm.service.knowledge.YjkMaintenanceTeamService;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectMemberService;
import com.msun.csm.util.MsunChatMessagesUtil;
import com.msun.csm.util.StringUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */
@Slf4j
@Service
public class BaseQueryServiceImpl implements BaseQueryService {

    @Resource
    private ProjContractCustomInfoMapper contractCustomInfoMapper;
    @Resource
    private ProjCustomInfoMapper customInfoMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjProjectMemberService projProjectMemberService;

    @Resource
    private DictProductMapper dictProductMapper;
    @Resource
    private SysDeptMapper deptMapper;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Lazy
    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Lazy
    @Resource
    private ProjOnlineStepMapper projOnlineStepMapper;

    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private UserHelper userHelper;


    @Resource
    private ProjProjectMemberMapper projMemberMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Lazy
    @Resource
    private ConfigProductJobMenuDetailMapper configProductJobMenuDetailMapper;

    @Lazy
    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    @Resource
    private DictProjectPlanStageMapper dictProjectPlanStageMapper;

    @Resource
    private YjkMaintenanceTeamService yjkMaintenanceTeamService;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Resource
    private ConfigProjectReviewTypeUserMapper configProjectReviewTypeUserMapper;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Resource
    private DictAgentScenarioConfigMapper dictAgentScenarioConfigMapper;

    @Lazy
    @Resource
    private DictPaperSizeMapper dictPaperSizeMapper;
    @Resource
    private DictBusinessStatusService dictBusinessStatusService;
    @Resource
    private ProjProjectInfoService projectInfoService;
    @Lazy
    @Resource
    private DictOnlineBusinessStatusMapper dictOnlineBusinessStatusMapper;

    @Resource
    ProjProductDeliverRecordMapper projProductDeliverRecordMapper;

    /**
     * 查询合同客户
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryAllContractCustomer(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return contractCustomInfoMapper.selectByKeyword(keyword);
    }

    /**
     * 基础查询-查询所有客户
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryAllCustomer(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return customInfoMapper.selectByKeyword(keyword);
    }

    /**
     * 查询所有项目
     *
     * @param keyword
     * @param customInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProject(String keyword, Long customInfoId) {
        ProjProjectInfo queryParam = new ProjProjectInfo();
        queryParam.setCustomInfoId(customInfoId);
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        queryParam.setProjectName(keyword);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(queryParam);
        List<BaseIdNameResp> result = projectInfos.stream().map(item -> new BaseIdNameResp(item.getProjectInfoId(), item.getProjectName() + "-" + item.getProjectNumber() + "(" + (item.getHisFlag() == 1 ? "首期" : "非首期") + ")")).collect(Collectors.toList());
        return result;
    }

    /**
     * @param keyword
     * @param isOnlyDomain
     * @param customInfoId
     * @return
     */
    @Override
    public List<BaseHospitalNameResp> queryHospital(String keyword, Boolean isOnlyDomain, Long customInfoId, Long projectInfoId) {
        List<BaseHospitalNameResp> result = hospitalInfoMapper.selectHospitalInfoListByParamer(isOnlyDomain, customInfoId, projectInfoId);
        return result;
    }

    /**
     * 查询枚举表数据
     *
     * @return
     */
    @Override
    public List<BaseCodeNameResp> queryLibFormType() {
        List<BaseCodeNameResp> list = new ArrayList<>();
        for (LibFormTypeEnum e : LibFormTypeEnum.values()) {
            list.add(new BaseCodeNameResp(e.getCode(), e.getMessage()));
        }
        return list;
    }

    /**
     * 查询产品
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProduct(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return dictProductMapper.selectByKeyword(keyword);
    }

    /**
     * 查询部门
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryDept(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return deptMapper.selectByKeyword(keyword);
    }

    /**
     * 查询项目类型-单体/区域
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> getProjectType() {
        List<BaseIdNameResp> projectTypeList = ProjectTypeEnums.getBaseDataList();
        //医共体需要删除
        projectTypeList.removeIf(item -> item.getId().equals(3L));
        projectTypeList.removeIf(item -> item.getId().equals(4L));
        return projectTypeList;
    }

    /**
     * 查询项目升级方式-老换新/新客户
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> getProjectUpgradation() {
        List<BaseIdNameResp> upgradationTypeList = ProjectUpgradationTypeEnums.getBaseDataList();
        return upgradationTypeList;
    }

    /**
     * 查询项目验收超时天数
     *
     * @param projectInfoId
     * @param hospitalInfoId
     * @param limitType
     * @return
     */
    @Override
    public Integer queryAcceptanceExceedTimeLimit(Long projectInfoId, Long hospitalInfoId, Integer limitType) {
        // 默认不限制 0 / 1 满足限制
        Integer result = 0;
        // 默认查询三方接口限制
        if (limitType == null || limitType == 0) {
            result = projectInfoMapper.queryAcceptanceExceedTimeLimit(projectInfoId, hospitalInfoId, "interfacelimit");
        } else if (limitType == 2) {
            result = projectInfoMapper.queryAcceptanceExceedTimeLimit(projectInfoId, hospitalInfoId, "reportlimit");
        }
        return result;
    }

    /**
     * 查询里程碑数据
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameExtendResp> queryProjectMilestone(Long projectInfoId) {
        return milestoneInfoMapper.queryProjectMilestone(projectInfoId);
    }

    /**
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameExtendResp> queryProjectOnlineStep(Long projectInfoId) {
        return projOnlineStepMapper.queryProjectOnlineStep(projectInfoId);
    }

    /**
     * 根据项目id查询项目信息
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public ProjProjectInfo getProjectByProjectInfoId(Long projectInfoId) {
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
        projectInfo.setTelesalesFlag(customInfo.getTelesalesFlag());
        return projectInfo;
    }

    /**
     * 查询首期项目
     *
     * @param customInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryFirstProject(Long customInfoId) {
        ProjProjectInfo queryParam = new ProjProjectInfo();
        queryParam.setCustomInfoId(customInfoId);
        queryParam.setHisFlag(1);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(queryParam);
        List<BaseIdNameResp> result = new ArrayList<>();
        if (projectInfos != null && projectInfos.size() > 0) {
            for (ProjProjectInfo e : projectInfos) {
                if (e.getHisFlag() == 1) {
                    String projectNumber = e.getProjectType() == 2 ? "[区域]" : "[单体]";
                    String projectName = e.getProjectName() + "-" + projectNumber + "(" + (e.getHisFlag() == 1 ? "首期" : "非首期") + ")";
                    result.add(new BaseIdNameResp(e.getProjectInfoId(), projectName));
                }
            }
        }
        return result;
    }

    /**
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryCustomType() {
        List<BaseIdNameResp> result = new ArrayList<>();
        for (CustomTypeEnum e : CustomTypeEnum.values()) {
            result.add(new BaseIdNameResp(e.getCode(), e.getMessage()));
        }
        return result;
    }

    /**
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProjectProgress() {
        List<BaseIdNameResp> result = new ArrayList<>();
        for (ProjectDeliverStatusEnums e : ProjectDeliverStatusEnums.values()) {
            result.add(new BaseIdNameResp(Long.valueOf(e.getCode()), e.getName()));
        }
        return result;
    }

    /**
     * @param deptType
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryDeptByParamer(Integer deptType) {
        if (deptType == null || deptType == 0) {
            deptType = 1;
        }
        return deptMapper.queryDeptByParamer(deptType);
    }

    @Override
    public List<BaseProjectInfoResp> queryBackendProjectInfo(Long customInfoId) {
        ProjProjectInfo queryParam = new ProjProjectInfo();
        queryParam.setCustomInfoId(customInfoId);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(queryParam);
        return projectInfos.stream().map(item -> {
            List<String> productNames = orderProductService.getProductName(item.getProjectInfoId());
            return new BaseProjectInfoResp(String.valueOf(item.getProjectInfoId()), item.getProjectNumber() + "(" + (item.getHisFlag() == 1 ? "首期" : "非首期") + ")", StringUtils.join(productNames, ","));
        }).collect(Collectors.toList());
    }

    /**
     * 查询当前账号的角色信息--前后端人员
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Integer getUserRoleType(Long projectInfoId) {
        SysUserVO currentUser = userHelper.getCurrentUser();
        ProjProjectMemberDTO memberDTO = new ProjProjectMemberDTO();
        memberDTO.setProjectInfoId(projectInfoId);
        memberDTO.setProjectMemberId(currentUser.getSysUserId());
        List<ProjProjectMemberVO> members = projMemberMapper.selectMemberVO(memberDTO);
        //如果当前账号在项目组中，则返回在项目组中的角色
        if (ObjectUtil.isNotEmpty(members)) {
            return members.get(0).getRoleType();
        }
        // 不在项目组中，查询是否有系统管理员的角色
        List<SysRoleDTO> currentUserRoleList = sysRoleMapper.selectRoleByUserId(currentUser.getSysUserId());
        // 当前登录用户的角色编码
        List<String> currentUserRoleCodeSet = currentUserRoleList.stream().map(SysRoleDTO::getRoleCode).collect(Collectors.toList());
        //不在项目组中且不是系统管理员，则默认是前端人员
        if (!currentUserRoleCodeSet.contains("1")) {
            return 1;
        }
        return 0;
    }

    /**
     * 查询项目待办类型
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProjectTodoTypeData(Long projectInfoId) {
        List<BaseIdNameResp> list = new ArrayList<>();
        list.add(new BaseIdNameResp(0L, "产品总体状态"));
        list.addAll(configProductJobMenuDetailMapper.selectProductMenuDetail(projectInfoId));
        // 自定义顺序
        Map<String, Integer> sortOrder = new HashMap<>();
        sortOrder.put("产品总体状态", 0);
        sortOrder.put("基础数据", 1);
        sortOrder.put("配置", 2);
        sortOrder.put("待处理任务", 3);
        sortOrder.put("打印报表", 4);
        sortOrder.put("表单", 5);
        list.sort(Comparator.comparingInt(item -> sortOrder.getOrDefault(item.getName(), Integer.MAX_VALUE)));
        return list;
    }

    /**
     * * 查询项目是业务线否开启审核
     * 限制类型 1开启医护打印验证流程、2打印报表、3云护理表单、4手麻表单、5重症表单、6急诊表单、7三方接口、8医保接口、9统计报表/10 产品业务调研  11 打印报表 12 表单
     *
     * @param projectInfoId
     * @param openType
     * @return
     */
    @Override
    public ConfigCustomBackendDetailLimit isOpenAuditorFlag(Long projectInfoId, Integer openType) {
        ConfigCustomBackendDetailLimit configCustomBackendDetailLimit = new ConfigCustomBackendDetailLimit();
        List<ConfigCustomBackendDetailLimit> list = configCustomBackendDetailLimitMapper.selectList(new QueryWrapper<ConfigCustomBackendDetailLimit>().eq("is_deleted", 0).eq("project_info_id", projectInfoId).eq("open_type", openType));
        if (list != null && list.size() > 0) {
            configCustomBackendDetailLimit = list.get(0);
        } else {
            configCustomBackendDetailLimit.setOpenFlag(0);
        }
        return configCustomBackendDetailLimit;
    }

    @Override
    public List<BaseIdCodeNameResp> getBusinessStatusList(String businessCode, Long projectInfoId) {
        // 产品业务调研
        if ("surveyPlan".equals(businessCode)) {
            List<DictBusinessStatus> dictBusinessStatuses = dictBusinessStatusService.getBusinessStatusByBusinessCode("survey_product");
            boolean openSurveyAudit = projectInfoService.isOpenSurveyAudit(projectInfoId);
            if (openSurveyAudit) {
                // 开启后端审核，返回全部状态
                return dictBusinessStatuses.stream().map(item -> new BaseIdCodeNameResp(String.valueOf(item.getStatusId()), item.getStatusCode(), item.getStatusDescription())).collect(Collectors.toList());
            }
            List<Integer> common = Arrays.asList(0, 1, 2, 3);
            return dictBusinessStatuses.stream().filter(item -> common.contains(item.getStatusId())).map(item -> new BaseIdCodeNameResp(String.valueOf(item.getStatusId()), item.getStatusCode(), item.getStatusDescription())).collect(Collectors.toList());
        }
        List<DictBusinessStatus> dictBusinessStatuses = dictBusinessStatusService.getBusinessStatusByBusinessCode(businessCode);
        return dictBusinessStatuses.stream().map(item -> new BaseIdCodeNameResp(String.valueOf(item.getStatusId()), item.getStatusCode(), item.getStatusDescription())).collect(Collectors.toList());
    }


    /**
     * 查询项目计划阶段字典
     *
     * @return
     */
    @Override
    public List<BaseCodeNameResp> getAllPlanStage() {
        return dictProjectPlanStageMapper.getAllPlanStage();
    }


    @Override
    public List<BaseCodeNameResp> getBackendTeamByTypeCode(String teamTypeCode) {
        BackendTeamTypeEnum teamTypeEnum = BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(teamTypeCode);
        List<BackendTeamInfo> backendTeam = yjkMaintenanceTeamService.getTeamByTypeCode(teamTypeEnum);
        if (CollectionUtils.isEmpty(backendTeam)) {
            return new ArrayList<>();
        }
        return backendTeam.stream().map(item -> new BaseCodeNameResp(String.valueOf(item.getYyDeptId()), item.getName())).collect(Collectors.toList());
    }

    @Override
    public ProjectMemberRoleInfo getProjectMemberRoleInfo(Long projectInfoId) {
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projectInfoId);
        if (projProjectInfo == null) {
            throw new IllegalArgumentException("项目ID非法，没有根据项目ID获取到项目信息。项目ID=" + projectInfoId);
        }
        ProjectMemberRoleInfo projectMemberRoleInfo = new ProjectMemberRoleInfo();
        // 前端项目经理仍然根据项目信息表中的数据进行判断
        projectMemberRoleInfo.setFrontLeaderFlag(userHelper.getCurrentSysUserIdWithDefaultValue().equals(projProjectInfo.getProjectLeaderId()));
        // 当前人在项目成员中的信息
        Boolean backendManager = projProjectMemberService.isBackendLeader(projectInfoId);
        projectMemberRoleInfo.setBackendLeaderFlag(backendManager);
        return projectMemberRoleInfo;
    }

    /**
     * 查询众阳下下所有人
     *
     * @param dto
     * @return
     */
    @Override
    public List<BaseIdNameResp> getDirUserList(DirUserDTO dto) {
        return configProjectReviewTypeUserMapper.getDirUserList(dto);
    }

    @Override
    public SelectOption getAllBackendTeam() {
        // 所有后端服务团队
        List<BackendTeamInfo> allBackendTeam = new ArrayList<>();
        for (BackendTeamTypeEnum teamTypeEnum : BackendTeamTypeEnum.values()) {
            List<BackendTeamInfo> backendTeam = yjkMaintenanceTeamService.getTeamByTypeCode(teamTypeEnum);
            if (!CollectionUtils.isEmpty(backendTeam)) {
                allBackendTeam.addAll(backendTeam);
            }
        }
        // 获取当前登录人
        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        SysUser currentSysUser = sysUserMapper.selectBySysUserId(currentSysUserId);
        // 组装返回数据
        SelectOption selectOption = new SelectOption();
        List<Long> yyDeptIdList = allBackendTeam.stream().map(BackendTeamInfo::getYyDeptId).collect(Collectors.toList());
        if (yyDeptIdList.contains(currentSysUser.getDeptId())) {
            // 当前登录人是后端运维团队人员，默认选中对应团队
            selectOption.setDefaultOption(String.valueOf(currentSysUser.getDeptId()));
        } else {
            // 当前登录人不是后端运维团队人员，无默认选中
            selectOption.setDefaultOption(null);
        }
        // 后端运维团队选项
        List<BaseCodeNameResp> optionList = allBackendTeam.stream().map(item -> new BaseCodeNameResp(String.valueOf(item.getYyDeptId()), item.getName())).collect(Collectors.toList());
        selectOption.setOptionList(optionList);
        return selectOption;
    }

    /**
     * 获取智能助手进行问答
     *
     * @param dictAgentChatReq
     * @return
     */
    @Override
    public Result sendChartMessage(DictAgentChatReq dictAgentChatReq) {
        SysUserVO sysUserVO = userHelper.getCurrentUser();
        Long sysUserId = null;
        if (null == sysUserVO) {
            sysUserId = 9999999L;
        } else {
            sysUserId = sysUserVO.getSysUserId();
        }
        // 查询场景配置
        DictAgentChatScenarioConfigResp resp = dictAgentScenarioConfigMapper.selectByParam(dictAgentChatReq);
        if (dictAgentChatReq.getFileUrls() == null || dictAgentChatReq.getFileUrls().isEmpty()) {
            throw new CustomException("请上传图片");
        }
        String url = resp.getAgentAddress();
        if ("prod".equals(activeProfiles)) {
            url = resp.getAgentAddressProduce();
        }
        Map<String, Object> map = new HashMap<>();
        try {
            map = MsunChatMessagesUtil.sendChartMessage(dictAgentChatReq.getFileUrls(), sysUserId, resp.getScenarioPrompt(), resp.getAgentKey(), url);
            log.info("图片检测，结果={}", JSON.toJSONString(map));
            return Result.success(map);
        } catch (Exception e) {
            log.error("解析图片智能体反馈结果，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new CustomException("解析异常");
        }
    }

    @Override
    public SelectOption getSearchPeopleType(GetSearchPeopleTypeParam param) {
        // 调研人：survey
        BaseCodeNameResp survey = new BaseCodeNameResp();
        survey.setId("survey");
        survey.setName("调研人");
        // 审核人：auditor
        BaseCodeNameResp auditor = new BaseCodeNameResp();
        auditor.setId("auditor");
        auditor.setName("审核人");
        // 制作人：producer
        BaseCodeNameResp producer = new BaseCodeNameResp();
        producer.setId("producer");
        producer.setName("制作人");
        // 验证人：identifier
        BaseCodeNameResp identifier = new BaseCodeNameResp();
        identifier.setId("identifier");
        identifier.setName("验证人");
        // FIXME 完善逻辑
        List<BaseCodeNameResp> optionList = new ArrayList<>();
        optionList.add(survey);
        optionList.add(auditor);
        optionList.add(producer);
        optionList.add(identifier);
        SelectOption selectOption = new SelectOption();
        selectOption.setDefaultOption(survey.getId());
        selectOption.setOptionList(optionList);
        return selectOption;
    }

    @Override
    public Result<List<BaseCodeNameResp>> queryIdentifierList(ProjectInfoId projectInfoId) {
        List<BaseIdNameResp> memberList = projMemberMapper.queryProjectMemberByRoleType(projectInfoId.getProjectInfoId(), 1);
        if (CollectionUtils.isEmpty(memberList)) {
            return Result.success(new ArrayList<>());
        }
        List<BaseCodeNameResp> collect = memberList.stream().map(item -> new BaseCodeNameResp(String.valueOf(item.getId()), item.getName())).collect(Collectors.toList());
        return Result.success(collect);
    }

    /**
     * 查询纸张列表
     *
     * @param printCode
     * @return
     */
    @Override
    public List<BaseIdCodeNameResp> getPaperSizeList(String printCode) {
        List<DictPaperSize> dictList = dictPaperSizeMapper.selectList(new QueryWrapper<DictPaperSize>().eq("print_code", printCode).eq("is_deleted", 0));
        if (CollectionUtils.isNotEmpty(dictList)) {
            return dictList.stream().map(item -> new BaseIdCodeNameResp(String.valueOf(item.getPaperSizeId()), item.getPaperSizeName(), item.getPaperSizeName())).collect(Collectors.toList());
        } else {
            return dictPaperSizeMapper.getPaperSizeList();
        }
    }

    /**
     * 查询业务上线必备状态列表
     *
     * @param busCode
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> getOnlineStatusPublicData(String busCode) {
        return Result.success(dictOnlineBusinessStatusMapper.getOnlineStatusPublicData(busCode));
    }

    @Override
    public Result<List<BaseIdNameResp>> queryDeliverProductByProject(Long projectInfoId) {
        return Result.success(projProductDeliverRecordMapper.queryDeliverProductIdAndNameByProjectInfoId(projectInfoId));
    }

}
