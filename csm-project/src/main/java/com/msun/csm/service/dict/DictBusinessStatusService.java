package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictBusinessStatus;

public interface DictBusinessStatusService {

    List<DictBusinessStatus> getBusinessStatusByBusinessCode(String businessCode);

    String getSurveyPlanStatusDescriptionByStatusId(Integer statusId);


    /**
     * @param businessCode 业务编码
     *                     <p>survey_product：产品业务调研</p>
     *                     <p>survey_form：表单调研</p>
     *                     <p>survey_report：打印报表调研</p>
     *                     <p>survey_statistics_report：统计报表调研</p>
     *                     <p>survey_third_part：三方接口调研</p>
     *                     <p>preparat_report：打印报表制作</p>
     * @param statusClass  状态分类
     *                     <p>1：未开始/未完成</p>
     *                     <p>2：进行中</p>
     *                     <p>3：已完成</p>
     * @return 业务状态
     */
    List<DictBusinessStatus> getBusinessStatusByBusinessCodeAndStatusClass(String businessCode, Integer statusClass);

    /**
     * @param businessCode 业务编码
     *                     <p>survey_product：产品业务调研</p>
     *                     <p>survey_form：表单调研</p>
     *                     <p>survey_report：打印报表调研</p>
     *                     <p>survey_statistics_report：统计报表调研</p>
     *                     <p>survey_third_part：三方接口调研</p>
     *                     <p>preparat_report：打印报表制作</p>
     * @param statusClass  状态分类
     *                     <p>1：未开始/未完成</p>
     *                     <p>2：进行中</p>
     *                     <p>3：已完成</p>
     * @return 业务状态
     */
    List<Integer> getStatusIdByBusinessCodeAndStatusClass(String businessCode, Integer statusClass);
}
