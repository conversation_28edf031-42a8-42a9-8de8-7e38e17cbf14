package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjAutocheckInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.mapper.proj.ProjAutocheckInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.feign.client.aotocheck.AutoCheckClient;
import com.msun.csm.model.dto.AutoCheckDeptTDO;
import com.msun.csm.model.dto.AutoCheckForTask;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.SubmitAccountInfoDTO;
import com.msun.csm.model.dto.autocheck.AutoCheckHospitalInfoDTO;
import com.msun.csm.model.dto.autocheck.AutoDeptDTO;
import com.msun.csm.model.dto.autocheck.FieldTransDTO;
import com.msun.csm.model.dto.autocheck.GenLoginUserDTO;
import com.msun.csm.model.dto.autocheck.GenLoginUserTransferDTO;
import com.msun.csm.model.dto.autocheck.ReadyDeptDTO;
import com.msun.csm.model.dto.autocheck.YfDTO;
import com.msun.csm.model.dto.autocheck.testuse.DeptListObj;
import com.msun.csm.model.dto.autocheck.testuse.ReadyDeptObj;
import com.msun.csm.model.dto.autocheck.testuse.SelectYfListObj;
import com.msun.csm.model.dto.autocheck.testuse.YfItem;
import com.msun.csm.model.resp.project.AutoCheckHospitalInfo;
import com.msun.csm.model.resp.project.AutoCheckHospitalInfoResp;
import com.msun.csm.model.vo.ProjAutocheckInfoVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;

/**
 * @description:
 * @fileName:
 * @author:zhouzhaoyu
 * @updateBy:
 * @Date:Created in 16:57 2024/7/8
 * @remark:
 */
@Service
@Slf4j
public class AutoCheckServiceImpl implements AutoCheckService {

    @Resource
    private ProjOrderProductMapper projOrderProductMapper;
    @Resource
    private ProjHospitalInfoService projHospitalInfoService;

    @Resource
    private AutoCheckClient autoCheckClient;
    @Resource
    ProjAutocheckInfoService projAutocheckInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjAutocheckInfoMapper autoCheckInfoMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Override
    public AutoCheckHospitalInfoResp getHospitalInfo(Long projectInfoId) {
        if (ObjectUtil.isEmpty(projectInfoId)) {
            throw new RuntimeException("项目Id不能为空！！！");
        }
        AutoCheckHospitalInfoResp resp = new AutoCheckHospitalInfoResp();
        SelectHospitalDTO dto = new SelectHospitalDTO();
        dto.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoService.getHospitalInfoByProjectId(dto);
        List<AutoCheckHospitalInfo> hospitalInfos = new ArrayList<>();
        AtomicReference<String> cloudDomain = new AtomicReference<>();
        hospitalInfoList.forEach(v -> {
            AutoCheckHospitalInfo info = new AutoCheckHospitalInfo();
            info.setHospitalInfoId(v.getHospitalInfoId());
            info.setHospitalName(v.getHospitalName());
            info.setCloudHospitalId(v.getCloudHospitalId());
            info.setOrgId(v.getOrgId());
            if (StringUtils.isNotEmpty(v.getCloudDomain())) {
                cloudDomain.set(v.getCloudDomain());
            }
            hospitalInfos.add(info);
        });
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projectInfoId);
        //组装产品列表信息
        List<ProductInfo> allList = projOrderProductMapper.findAllList(productInfoDTO);
        List<ProductInfo> resList = allList.stream().filter(v -> !StringUtils.isEmpty(v.getProductName()))
                .collect(Collectors.toList());
        String name = resList.stream().map(e -> String.valueOf(e.getProductName()))
                .collect(Collectors.joining(","));
        resp.setHospitalInfoList(hospitalInfos);
        resp.setCloudDomain(null == cloudDomain.get() ? "" : cloudDomain.get());
        resp.setProductList(name);
        ProjAutocheckInfo projAutocheckInfo = projAutocheckInfoService.getAutoCheckInfoByProjectId(projectInfoId);
        if (ObjectUtil.isNotEmpty(projAutocheckInfo)) {
            resp.setStepNum(projAutocheckInfo.getStepNum());
            resp.setReportLink(projAutocheckInfo.getReportLink());
            resp.setReportLinkDetail(projAutocheckInfo.getReportLinkDetail());
            resp.setTaskId(projAutocheckInfo.getTaskId());
            resp.setCloudHospitalId(projAutocheckInfo.getCloudHospitalId());
            resp.setHospitalInfoId(projAutocheckInfo.getHospitalInfoId());
            resp.setOrgId(projAutocheckInfo.getOrgId());
            resp.setHospitalName(projAutocheckInfo.getHospitalName());
            resp.setHistoryReportLink(projAutocheckInfo.getHistoryReportLink());
            resp.setBusinessTaskFlag(projAutocheckInfo.getBusinessTaskFlag());
            resp.setBaseTaskId(projAutocheckInfo.getBaseTaskId());
            resp.setProjectInfoId(projAutocheckInfo.getProjectInfoId());
        } else {
            resp.setStepNum(1);
            resp.setReportLink("");
            resp.setReportLinkDetail("");
            resp.setTaskId(0L);
        }
        return resp;
    }

    /**
     * 自动化测试平台校验医院是否可测试
     *
     * @param dto
     * @return
     */
    @Override
    public JSONObject applyToAutoCheck(AutoCheckHospitalInfoDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("org_id", dto.getOrgId());
        map.put("hospital_id", dto.getCloudHospitalId());
        map.put("hospital_name", dto.getHospitalName());
        map.put("domain", dto.getCloudDomain());
        map.put("product_id_list", "");
        map.put("operator_user", userHelper.getCurrentUser().getUserName());
        JSONObject jsonObject = new JSONObject(map);
        return autoCheckClient.getHospitalInfo(jsonObject);
    }

    @Override
    public JSONObject getDeptUserInfo(GenLoginUserDTO dto) {
        List<DeptListObj> deptList = null; // jsonToJava(dto.getSelectModelJson());
        DeptListObj deptListObj = deptList.get(0);
        // 拼接参数
        FieldTransDTO fieldTransDTO = FieldTransDTO.builder()
                .deptListObj(deptListObj)
                .hospitalName(dto.getHospitalName())
                .clouddomain(dto.getCloudDomain())
                .hospitalId(dto.getCloudHospitalId())
                .orgId(dto.getOrgId())
                .operatorUser(dto.getOperatorUser())
                .projectInfoId(dto.getProjectInfoId())
                .hospitalInfoId(dto.getHospitalInfoId())
                .build();
        GenLoginUserTransferDTO transferDTO = AutoCheckServiceImpl.getGenLoginUserTransferDto(fieldTransDTO);
        // 获取创建的用户并返回
        return getDeptUserInfo(transferDTO);
    }

    /**
     * 测试使用
     *
     * @param dto 请求参数
     * @return
     */
    @Override
    public JSONObject getDeptUserInfo(GenLoginUserTransferDTO dto) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("hospital_id", dto.getCloudHospitalId());
        jsonObject.set("hospital_name", dto.getHospitalName());
        jsonObject.set("domain", dto.getCloudDomain());
        jsonObject.set("node_type", NumberEnum.NO_0.num());
        jsonObject.set("operator_user", ObjectUtil.isNotEmpty(userHelper.getCurrentUser())
                ? userHelper.getCurrentUser().getUserName() : (StrUtil.isNotBlank(dto.getOperatorUser())
                ? dto.getOperatorUser() : StrUtil.EMPTY)
        );
        jsonObject.set("dept_list", handleDeptList(dto.getDeptList()));
        sysOperLogService.apiOperLogInsert(jsonObject, "获取生成的测试用户信息", "入参", Log.LogOperType.SEARCH.getCode());
        JSONObject userInfo = autoCheckClient.generateLoginUser(jsonObject);
        sysOperLogService.apiOperLogInsert(userInfo, "获取生成的测试用户信息", "返回值", Log.LogOperType.SEARCH.getCode());
        if ("200".equals(String.valueOf(userInfo.get("code")))) {
            // 同一时间只能存在一个测试的数据。检测当前项目是否已经进行了测试。当进行了测试后 删除原测试信息
            projAutocheckInfoService.deleteByProjectInfoId(dto.getProjectInfoId());
            ProjAutocheckInfo autocheckInfo = new ProjAutocheckInfo();
            autocheckInfo.setProjAutocheckId(SnowFlakeUtil.getId());
            autocheckInfo.setStepNum(2);
            autocheckInfo.setHospitalInfoId(dto.getHospitalInfoId());
            autocheckInfo.setCloudHospitalId(dto.getCloudHospitalId());
            autocheckInfo.setProjectInfoId(dto.getProjectInfoId());
            autocheckInfo.setOrgId(dto.getOrgId());
            autocheckInfo.setHospitalName(dto.getHospitalName());
            projAutocheckInfoService.insertAutoCheck(autocheckInfo);
        }
        return userInfo;
    }

    public JSONObject getDeptUserInfoImpl(GenLoginUserDTO dto) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("hospital_id", dto.getCloudHospitalId());
        jsonObject.set("hospital_name", dto.getHospitalName());
        jsonObject.set("domain", dto.getCloudDomain());
        jsonObject.set("node_type", NumberEnum.NO_0.num());
        jsonObject.set("operator_user", ObjectUtil.isNotEmpty(userHelper.getCurrentUser())
                ? userHelper.getCurrentUser().getUserName() : (StrUtil.isNotBlank(dto.getOperatorUser())
                ? dto.getOperatorUser() : StrUtil.EMPTY)
        );
        jsonObject.set("dept_list", dto.getSelectModelJson());
        sysOperLogService.apiOperLogInsert(jsonObject, "获取生成的测试用户信息", "入参", Log.LogOperType.SEARCH.getCode());
        JSONObject userInfo = autoCheckClient.generateLoginUser(jsonObject);
        sysOperLogService.apiOperLogInsert(userInfo, "获取生成的测试用户信息", "返回值", Log.LogOperType.SEARCH.getCode());
        if ("200".equals(String.valueOf(userInfo.get("code")))) {
            // 同一时间只能存在一个测试的数据。检测当前项目是否已经进行了测试。当进行了测试后 删除原测试信息
            projAutocheckInfoService.deleteByProjectInfoId(dto.getProjectInfoId());
            ProjAutocheckInfo autocheckInfo = new ProjAutocheckInfo();
            autocheckInfo.setProjAutocheckId(SnowFlakeUtil.getId());
            autocheckInfo.setStepNum(2);
            autocheckInfo.setHospitalInfoId(dto.getHospitalInfoId());
            autocheckInfo.setCloudHospitalId(dto.getCloudHospitalId());
            autocheckInfo.setProjectInfoId(dto.getProjectInfoId());
            autocheckInfo.setOrgId(dto.getOrgId());
            autocheckInfo.setHospitalName(dto.getHospitalName());
            projAutocheckInfoService.insertAutoCheck(autocheckInfo);
        }
        return userInfo;
    }

    /**
     * 下划线格式转驼峰
     *
     * @param fieldTransDTO 请求参数
     * @return GenLoginUserDTO
     */
    public static GenLoginUserTransferDTO getGenLoginUserTransferDto(FieldTransDTO fieldTransDTO) {
        List<AutoDeptDTO> autoDeptDTOS = CollUtil.newArrayList();
        AutoDeptDTO autoDeptDTO = AutoDeptDTO.builder().build();
        List<ReadyDeptDTO> readyDeptList = CollUtil.newArrayList();
        fieldTransDTO.getDeptListObj().getReadyDeptList().forEach(e -> {
            ReadyDeptDTO readyDeptDTO = BeanUtil.copyProperties(e, ReadyDeptDTO.class);
            // 设置已经有的
            readyDeptList.add(readyDeptDTO);
            autoDeptDTO.setReadyDeptList(readyDeptList);
        });
        Map<String, Map<String, List<YfDTO>>> newMap = MapUtil.newHashMap();
        for (SelectYfListObj selectYfListObj : fieldTransDTO.getDeptListObj().getSelectYfList()) {
            Map<String, List<YfDTO>> yfDtoListMap = MapUtil.newHashMap();
            Map<String, List<YfItem>> itemMap = selectYfListObj.getSelectYf();
            newMap.put(selectYfListObj.getDeptName(), yfDtoListMap);
            if (MapUtil.isNotEmpty(itemMap)) {
                itemMap.keySet().forEach(e -> {
                    List<YfItem> itemList = itemMap.get(e);
                    if (CollUtil.isNotEmpty(itemList)) {
                        List<YfDTO> yfDTOS =
                                itemList.stream().map(t -> BeanUtil.copyProperties(t, YfDTO.class)).collect(Collectors.toList());
                        yfDtoListMap.put(e, yfDTOS);
                    }
                });
            }
        }
        autoDeptDTO.setSureYf(newMap);
        autoDeptDTOS.add(autoDeptDTO);
        // 拼接主体参数
        return GenLoginUserTransferDTO.builder()
                .cloudDomain(fieldTransDTO.getClouddomain())
                .orgId(fieldTransDTO.getOrgId())
                .cloudHospitalId(fieldTransDTO.getHospitalId())
                .operatorUser(fieldTransDTO.getOperatorUser())
                .projectInfoId(fieldTransDTO.getProjectInfoId())
                .hospitalName(fieldTransDTO.getHospitalName())
                .hospitalInfoId(fieldTransDTO.getHospitalInfoId())
                .deptList(autoDeptDTOS)
                .build();
    }

    /**
     * json对象转java
     *
     * @param jsonObject json对象, 字段为下划线模式
     * @return java对象
     */
    public static List<DeptListObj> jsonToJava(JSONObject jsonObject) {
        List<DeptListObj> deptListObjs = CollUtil.newArrayList();
        JSONArray array = JSONUtil.parseArray(jsonObject.get("dept_list"));
        for (Object object : array) {
            // 添加对象
            DeptListObj deptListObj = DeptListObj.builder().build();
            deptListObjs.add(deptListObj);
            JSONObject deptJson = JSONUtil.parseObj(object);
            // ready_dept_list
            JSONArray readyJsonAry = JSONUtil.parseArray(deptJson.get("ready_dept_list"));
            List<ReadyDeptObj> readyDeptObjs = CollUtil.newArrayList();
            deptListObj.setReadyDeptList(readyDeptObjs);
            for (Object readyObj : readyJsonAry) {
                JSONObject readyJson = JSONUtil.parseObj(readyObj);
                ReadyDeptObj readyDeptObj = ReadyDeptObj.builder().build();
                readyDeptObjs.add(readyDeptObj);
                readyDeptObj.setAccountName(StrUtil.toStringOrNull(readyJson.get("account_name")));
                readyDeptObj.setDeptName(StrUtil.toStringOrNull(readyJson.get("dept_name")));
                readyDeptObj.setDescription(StrUtil.toStringOrNull(readyJson.get("description")));
                readyDeptObj.setLoginUser(StrUtil.toStringOrNull(readyJson.get("login_user")));
                readyDeptObj.setModelDeptId(Long.parseLong(readyJson.get("model_dept_id").toString()));
                readyDeptObj.setRoleName(StrUtil.toStringOrNull(readyJson.get("role_name")));
                readyDeptObj.setSystemName(StrUtil.toStringOrNull(readyJson.get("system_name")));
                readyDeptObj.setRuleName(StrUtil.toStringOrNull(readyJson.get("rule_name")));
                JSONArray usernameary = JSONUtil.parseArray(readyJson.get("user_name"));
                List<String> usernames = CollUtil.newArrayList();
                if (CollUtil.isNotEmpty(usernameary)) {
                    for (Object o : usernameary) {
                        usernames.add(o.toString());
                    }
                }
                readyDeptObj.setUserName(usernames);
            }
            JSONArray selfJsonAry = JSONUtil.parseArray(deptJson.get("select_yf_list"));
            List<SelectYfListObj> selfYfObjs = CollUtil.newArrayList();
            deptListObj.setSelectYfList(selfYfObjs);
            for (Object selfObj : selfJsonAry) {
                JSONObject selfJson = JSONUtil.parseObj(selfObj);
                SelectYfListObj selfYfListObj = SelectYfListObj.builder().build();
                selfYfObjs.add(selfYfListObj);
                selfYfListObj.setDeptId(Long.parseLong(selfJson.get("dept_id").toString()));
                selfYfListObj.setDeptName(StrUtil.toStringOrNull(selfJson.get("dept_name")));
                JSONObject selfyfJson = JSONUtil.parseObj(selfJson.get("select_yf"));
                Map<String, List<YfItem>> map = MapUtil.newHashMap();
                selfYfListObj.setSelectYf(map);
                // 创建选择的模板
                createMap(map, selfyfJson);
            }
        }
        return deptListObjs;
    }

    public static void createMap(Map<String, List<YfItem>> map, JSONObject selfyfJson) {
        for (Map.Entry<String, Object> stringObjectEntry : selfyfJson) {
            List<YfItem> items = CollUtil.newArrayList();
            map.put(stringObjectEntry.getKey(), items);
            JSONArray array1 = JSONUtil.parseArray(stringObjectEntry.getValue());
            if (CollUtil.isNotEmpty(array1)) {
                for (Object o : array1) {
                    JSONObject itemJson = JSONUtil.parseObj(o);
                    YfItem item = YfItem.builder().build();
                    item.setCategoryId(StrUtil.toStringOrNull(itemJson.get("category_id")));
                    item.setBfId(Long.parseLong(StrUtil.toStringOrNull(itemJson.get("bf_id"))));
                    item.setYfId(Long.parseLong(StrUtil.toStringOrNull(itemJson.get("yf_id"))));
                    item.setYfName(StrUtil.toStringOrNull(itemJson.get("yf_name")));
                    item.setYkId(StrUtil.toStringOrNull(itemJson.get("yk_id")));
                    item.setYkName(StrUtil.toStringOrNull(itemJson.get("yk_name")));
                    item.setYkType(StrUtil.toStringOrNull(itemJson.get("yk_type")));
                    items.add(item);
                }
            }
        }
    }

    /**
     * @param autoDeptDTOS 前端请求参数
     * @return 解析后返回jsonArray对象
     */
    private JSONArray handleDeptList(List<AutoDeptDTO> autoDeptDTOS) {
        if (CollUtil.isEmpty(autoDeptDTOS)) {
            return new JSONArray();
        }
        JSONArray jsonArray = new JSONArray();
        for (AutoDeptDTO autoDeptDTO : autoDeptDTOS) {
            // 添加数组元素
            JSONObject jsonObject = new JSONObject();
            jsonArray.put(jsonObject);
            // 添加已准备的部门集合
            JSONArray readyDeptArray = new JSONArray();
            jsonObject.putOpt("ready_dept_list", readyDeptArray);
            List<ReadyDeptDTO> readyDeptDTOS = autoDeptDTO.getReadyDeptList();
            // - 解析已准备的部门集合
            if (CollUtil.isNotEmpty(readyDeptDTOS)) {
                for (ReadyDeptDTO readyDeptDTO : readyDeptDTOS) {
                    JSONObject readyJson = new JSONObject();
                    readyJson.set("model_dept_id", readyDeptDTO.getModelDeptId());
                    readyJson.set("rule_name", readyDeptDTO.getRuleName());
                    readyJson.set("dept_name", readyDeptDTO.getDeptName());
                    readyJson.set("role_name", readyDeptDTO.getRoleName());
                    readyJson.set("system_name", readyDeptDTO.getSystemName());
                    readyJson.set("description", readyDeptDTO.getDescription());
                    readyJson.set("account_name", readyDeptDTO.getAccountName());
                    readyJson.set("user_name", readyDeptDTO.getUserName());
                    readyJson.set("login_user", readyDeptDTO.getLoginUser());
                    readyDeptArray.put(readyJson);
                }
            }
            // 添加选择的科室信息
            JSONObject sureYfjson = new JSONObject();
            jsonObject.putOpt("sure_yf_list", sureYfjson);
            Map<String, Map<String, List<YfDTO>>> sureYfMap = autoDeptDTO.getSureYf();
            if (MapUtil.isNotEmpty(sureYfMap)) {
                for (String deptName : sureYfMap.keySet()) {
                    JSONObject zhjson = new JSONObject();
                    sureYfjson.set(deptName, zhjson);
                    // 处理zh_yf
                    Map<String, List<YfDTO>> yfDtoMap = sureYfMap.get(deptName);
                    if (MapUtil.isNotEmpty(yfDtoMap)) {
                        yfDtoMap.keySet().forEach(e -> {
                            JSONArray yfary = new JSONArray();
                            zhjson.set(e, yfary);
                            JSONObject zhyfjson = new JSONObject();
                            List<YfDTO> yfDTOS = yfDtoMap.get(e);
                            if (CollUtil.isNotEmpty(yfDTOS)) {
                                for (YfDTO yfDTO : yfDTOS) {
                                    zhyfjson.set("dept_id", yfDTO.getDeptId());
                                    zhyfjson.set("bf_id", yfDTO.getBfId());
                                    zhyfjson.set("category_id", yfDTO.getCategoryId());
                                    zhyfjson.set("yf_id", yfDTO.getYfId());
                                    zhyfjson.set("yf_name", yfDTO.getYfName());
                                    zhyfjson.set("yk_id", yfDTO.getYkId());
                                    zhyfjson.set("yk_name", yfDTO.getYkName());
                                    zhyfjson.set("yk_type", yfDTO.getYkType());
                                    yfary.put(zhyfjson);
                                }
                            }
                        });
                    }
                }
            }
        }
        return jsonArray;
    }

    /**
     * 运行任务
     *
     * @param taskType      【0：基础数据任务；1：业务数据】
     * @param projectInfoId
     * @return
     */
    @Override
    public Result autoCheckTaskRun(Integer taskType, Long projectInfoId) {
        if (redisUtil.get("auto_check") != null) {
            return Result.fail("当前有任务正在执行，请稍后再试！");
        }
        // 根据任务id查询数据
        ProjAutocheckInfo projAutocheckInfo = autoCheckInfoMapper.selectOne(new QueryWrapper<ProjAutocheckInfo>()
                .eq("project_info_id", projectInfoId)
        );
        // 判断具体任务
        Long taskId = taskType == 0 ? projAutocheckInfo.getBaseTaskId() : projAutocheckInfo.getTaskId();
        if (ObjectUtil.isEmpty(taskId)) {
            return Result.fail("当前任务不存在，请先在第二步提交用户信息进行生成任务！");
        }
        // 组装数据 调用自动化测试平台 运行场景任务
        Map<String, Object> map2 = new HashMap<>();
        map2.put("hospital_id", projAutocheckInfo.getCloudHospitalId());
        map2.put("org_id", projAutocheckInfo.getOrgId());
        map2.put("task_id", taskId);
        map2.put("operator_user", userHelper.getCurrentUser().getUserName());
        JSONObject js2 = new JSONObject(map2);
        log.info("交付平台运行场景任务参数信息：{}", js2);
        JSONObject jsonObject = autoCheckClient.taskRun(js2);
        log.info("交付平台运行场景任务返回信息：{}", jsonObject);
        if (jsonObject.get("code").equals(200)) {
            redisUtil.set("auto_check", taskId, 20, TimeUnit.MINUTES);
            // 任务开始运行后，修改表数据中任务id以及步骤信息
            ProjAutocheckInfo projAutocheckInfo2 = new ProjAutocheckInfo();
            projAutocheckInfo2.setStepNum(4);
            projAutocheckInfo2.setCloudHospitalId(projAutocheckInfo.getCloudHospitalId());
            projAutocheckInfo2.setOrgId(projAutocheckInfo.getOrgId());
            projAutocheckInfo2.setReportLinkDetail("");
            projAutocheckInfo2.setReportLink("");
            // 上次报告赋值成 历史报告
            projAutocheckInfo2.setHistoryReportLink(projAutocheckInfo.getReportLink());
            projAutocheckInfoService.updateAutoCheckByParam(projAutocheckInfo2);
        }
        return Result.success(jsonObject);
    }

    /**
     * 向自动化测试系统获取模块信息
     *
     * @return
     */
    @Override
    public JSONObject getAutoCheckDeptList(Long hospitalInfoId) {
        // 获取云健康机构id
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq(
                "hospital_info_id",
                hospitalInfoId));
        if (ObjectUtil.isEmpty(hospitalInfo)) {
            log.error("未查询到医院信息. hospitalInfoId: {}", hospitalInfoId);
            throw new CustomException("未查询到医院信息.");
        }
        Long cloudHospitalId = hospitalInfo.getCloudHospitalId();
        if (ObjectUtil.isEmpty(cloudHospitalId)) {
            log.error("医院机构信息异常: 云健康机构id为空.");
            throw new CustomException("医院机构信息异常: 云健康机构id为空.");
        }
        return getAutoCheckDeptList(hospitalInfo.getCloudHospitalId(), hospitalInfo.getHospitalName());
    }

    /**
     * @param cloudHospitalId 医院id
     * @param hospitalName    医院名称
     * @return JSONObject
     */
    @Override
    public JSONObject getAutoCheckDeptList(Long cloudHospitalId, String hospitalName) {
        JSONObject param = new JSONObject();
        param.set("hospital_id", cloudHospitalId);
        param.set("hospital_name", hospitalName);
        sysOperLogService.apiOperLogInsert(param, "向自动化测试系统获取模块信息", "入参", Log.LogOperType.SEARCH.getCode());
        JSONObject resultJson = autoCheckClient.generateDeptModel(param);
        log.info("获取模块信息接口返回值: {}", resultJson);
        sysOperLogService.apiOperLogInsert(resultJson, "向自动化测试系统获取模块信息", "返回值", Log.LogOperType.SEARCH.getCode());
        return resultJson;
    }


    /**
     * 将选中的账号信息提交给自动化测试平台
     *
     * @param dto
     * @return
     */
    @Override
    public JSONObject submitAccountInfo(SubmitAccountInfoDTO dto) {
        JSONObject jsonObject2 = new JSONObject();
        Map<String, Object> map = new HashMap<>();
        map.put("hospital_id", dto.getCloudHospitalId());
        map.put("hospital_name", dto.getHospitalName());
        map.put("domain", dto.getDomain());
        map.put("node_type", 0);
        map.put("operator_user", userHelper.getCurrentUser().getUserName());
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (AutoCheckDeptTDO autoCheckDept : dto.getDtos()) {
            Map<String, Object> maps = new HashMap<>();
            maps.put("model_dept_id", autoCheckDept.getModelDeptId());
            maps.put("rule_name", autoCheckDept.getRuleName());
            maps.put("dept_name", autoCheckDept.getDeptName());
            maps.put("role_name", autoCheckDept.getRoleName());
            maps.put("system_name", autoCheckDept.getSystemName());
            maps.put("description", ObjectUtil.isEmpty(autoCheckDept.getDescription()) ? ""
                    : autoCheckDept.getDescription());
            maps.put("account_name", autoCheckDept.getAccountName());
            mapList.add(maps);
        }
        map.put("dept_list", mapList);
        JSONObject js = new JSONObject(map);
        log.info("给自动化测试平台,推送测试人员账号信息：{}", js);
        Map<String, Object> map1 = new HashMap<>();
        map1.put("hospital_id", dto.getCloudHospitalId());
        map1.put("hospital_name", dto.getHospitalName());
        map1.put("operator_user", userHelper.getCurrentUser().getUserName());
        JSONObject js1 = new JSONObject(map1);
        log.info("自动化测试平台组建场景任务：{}", js1);
        JSONObject jsonObject1 = autoCheckClient.assembleTask(js1);
        jsonObject1.set("businessTaskFlag", 1);
        log.info("自动化测试平台组建场景任务返回结果：{}", jsonObject1);
        if ("200".equals(String.valueOf(jsonObject1.get("code")))) {
            // 成功创建任务后，把两个任务id存入表中。下一步执行
            // 任务开始运行后，修改表数据中任务id以及步骤信息
            ProjAutocheckInfo projAutocheckInfo = new ProjAutocheckInfo();
            projAutocheckInfo.setStepNum(3);
            projAutocheckInfo.setTaskId(Convert.toLong(jsonObject1.get("task_id")));
            projAutocheckInfo.setBaseTaskId(Convert.toLong(jsonObject1.get("base_task_id")));
            projAutocheckInfo.setCloudHospitalId(dto.getCloudHospitalId());
            projAutocheckInfo.setOrgId(dto.getOrgId());
            projAutocheckInfoService.updateAutoCheckByParam(projAutocheckInfo);
            return jsonObject1;
        } else {
            return jsonObject1;
        }
    }

    /**
     * 自动化测试平台关闭场景任务
     *
     * @param dto
     * @return
     */
    @Override
    public JSONObject taskClose(AutoCheckForTask dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("hospital_id", dto.getCloudHospitalId());
        map.put("org_id", dto.getOrgId());
       /* if (ObjectUtil.isNotEmpty(dto.getTaskId())) {
            List<Long> arrayList = new ArrayList<>();
            arrayList.add(dto.getTaskId());
            map.put("task_id", arrayList);
            map.put("task_type", 0);
        } else if (CollectionUtil.isNotEmpty(dto.getTaskIds())) {
            map.put("task_id", dto.getTaskIds());
            map.put("task_type", 1);
        }*/
        // 因为不会产生taskId.  上线后直接冻结所有任务即可。
        map.put("task_type", 1);
        map.put("operator_user", userHelper.getCurrentUser().getUserName());
        JSONObject jsonObject = new JSONObject(map);
        log.info("自动化测试平台关闭场景任务：{}", jsonObject);
        JSONObject jsonObject1 = autoCheckClient.taskClose(jsonObject);
        log.info("自动化测试平台关闭场景任务返回结果：{}", jsonObject1);
        if ("200".equals(String.valueOf(jsonObject1.get("code"))) && ObjectUtil.isNotEmpty(dto.getTaskId())) {
            // 当只关闭全量任务时 需要更新表数据的 业务任务Flag
            ProjAutocheckInfo projAutocheckInfo = new ProjAutocheckInfo();
            projAutocheckInfo.setCloudHospitalId(dto.getCloudHospitalId());
            projAutocheckInfo.setBusinessTaskFlag(0);
            projAutocheckInfoService.updateAutoCheckByParam(projAutocheckInfo);
        }
        return jsonObject1;
    }

    /**
     * 查询自动化测试任务数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjAutocheckInfoVO> selectAutoCheckInfoData(AutoCheckHospitalInfoDTO dto) {
        ProjAutocheckInfo autoCheckInfoByProjectId =
                projAutocheckInfoService.getAutoCheckInfoByProjectId(dto.getProjectInfoId());
        ProjAutocheckInfoVO projAutocheckInfoVO = new ProjAutocheckInfoVO();
        projAutocheckInfoVO.setBusinessTaskFlag(autoCheckInfoByProjectId.getBusinessTaskFlag());
        projAutocheckInfoVO.setHistoryReportLink(autoCheckInfoByProjectId.getHistoryReportLink());
        projAutocheckInfoVO.setReportLink(autoCheckInfoByProjectId.getReportLink());
        projAutocheckInfoVO.setReportLinkDetail(autoCheckInfoByProjectId.getReportLinkDetail());
        return Result.success(projAutocheckInfoVO);
    }

    /**
     * 获取自动化测试报告
     *
     * @param reportId
     * @return
     */
    @Override
    public JSONObject getSummaryReport(String reportId) {
        JSONObject jsonObject1 = autoCheckClient.getSummaryReport(reportId);
        return jsonObject1;
    }

    /**
     * 保存自动化测试报告
     *
     * @param dto
     * @return
     */
    @Override
    public Result<JSONObject> saveSummaryReport(JSONObject dto) {
        log.error("保存自动化测试报告：{}", dto);
        JSONObject jsonObject1 = autoCheckClient.saveSummaryReport(dto);
        return Result.success(jsonObject1);
    }
}
