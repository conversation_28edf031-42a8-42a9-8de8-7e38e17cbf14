package com.msun.csm.service.proj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.MilestoneNodeEnum.SURVEY_HARDWARE;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.cloudservice.CloudServiceKit;
import com.msun.csm.common.constants.MessageUrlConsts;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projectfile.ProjectFileTypeEnums;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.common.enums.projsettlement.CloudServiceTypeEnum;
import com.msun.csm.common.enums.projsettlement.NextCheckNodeUnNormalEnum;
import com.msun.csm.common.enums.projsettlement.SettlementMidOrderStatusEnum;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.enums.projsettlement.SettlementStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictCloudEnvironments;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementLog;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementRule;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheck;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementMiddlewareOrderInfo;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;
import com.msun.csm.dao.entity.proj.projsettlement.SurveyHardwareNewResultdataEntity;
import com.msun.csm.dao.mapper.dict.DictCloudEnvironmentsMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementCheckMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMidOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementRuleMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementUnCheckMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.entity.ProjProjectSettlementMidOrder;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.model.convert.DictCloudEnvironmentsSaleConvert;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderMainDTO;
import com.msun.csm.model.dto.projsetttlement.CheckNodeParam;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleCloudNodeDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleMiddlewareDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleSaveDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleUploadDTO;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleParam;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.vo.projsettlement.DictCloudEnvironmentsSaleVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckSaleMainVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckSaleUploadResultVO;
import com.msun.csm.model.vo.projsettlement.ShowFile;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderServiceImpl;
import com.msun.csm.service.sysuser.SysUserService;
import com.msun.csm.service.yunying.YunYingService;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */

@Slf4j
@Service
public class ProjProjectSettlementCheckSaleServiceImpl implements ProjProjectSettlementCheckSaleService {

    @Value("${project.feign.yunwei.url}")
    private String devUrl;

    @Value("${project.feign.yunwei.checkPreExcel-method}")
    private String checkPreExcelUrl;

    @Value("${project.current.url}")
    private String currentUrl;

    /**
     * 免中间件部署服务工单
     */
    private static final ProjSettlementMiddlewareOrderInfo MIDDLEWARE_ORDER_INFO =
            new ProjSettlementMiddlewareOrderInfo(-1L, StrUtil.EMPTY, "免除中间件部署服务", StrUtil.EMPTY);

    @Resource
    private ProjMessageInfoService messageInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private DictCloudEnvironmentsSaleConvert cloudEnvironmentsSaleConvert;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private ProjOrderInfoService projOrderInfoService;

    @Resource
    private ProjContractInfoService projContractInfoService;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private ProjProjectSettlementRuleMapper projectSettlementRuleMapper;

    @Resource
    private ProjProjectFileService projProjectFileService;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private DictCloudEnvironmentsMapper dictCloudEnvironmentsMapper;

    @Resource
    private ProjProjectSettlementLogMapper settlementLogMapper;

    @Resource
    private ProjProjectSettlementMapper settlementMapper;

    @Resource
    private ProjProjectSettlementService projProjectSettlementService;

    @Resource
    private ProjProjectSettlementLogService settlementLogService;

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjProjectSettlementUnCheckMapper settlementUnCheckMapper;

    @Resource
    @Lazy
    private YunYingService yunYingService;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private ProjProjectSettlementCheckMapper settlementCheckMapper;

    @Resource
    private ProjProjectOrderRelationService orderRelationService;

    @Resource
    private ProjCustomCloudServiceService cloudServiceService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjProjectSettlementRuleService settlementRuleService;


    @Resource
    private CommonService commonService;

    @Override
    public Result<List<ProjSettlementOrderInfo>> findCloudServiceOrder(ProjProjectSettlementCheckSaleDTO settlementCheckSaleDTO) {
        List<ProjSettlementOrderInfo> cloudServices =
                mainService.findCloudServiceForm(Long.parseLong(settlementCheckSaleDTO.getProjectInfoId()));
        if (CollUtil.isEmpty(cloudServices)) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(cloudServices);
    }

    @Override
    public Result<List<DictCloudEnvironmentsSaleVO>> findCloudNode(ProjProjectSettlementCheckSaleCloudNodeDTO saleCloudNodeDTO) {
        QueryWrapper<DictCloudEnvironments> queryWrapper = null;
        if (ObjectUtil.isEmpty(saleCloudNodeDTO.getMsunCloudFlag())) {
            saleCloudNodeDTO.setMsunCloudFlag(2);
        }
        if (ObjectUtil.isNotEmpty(saleCloudNodeDTO.getMsunCloudFlag())) {
            queryWrapper = new QueryWrapper<DictCloudEnvironments>().eq("msun_cloud_flag",
                    saleCloudNodeDTO.getMsunCloudFlag());
        }
        List<DictCloudEnvironments> environments = dictCloudEnvironmentsMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(environments)) {
            return Result.success(CollUtil.newArrayList());
        }
        return Result.success(cloudEnvironmentsSaleConvert.po2Vo(environments));
    }

    @Override
    public Result<List<ProjSettlementMiddlewareOrderInfo>> findMiddlewareOrderInfo(ProjProjectSettlementCheckSaleMiddlewareDTO dto) {
        List<ProjSettlementOrderInfo> orderInfos =
                mainService.findMiddlewareSettlementOrderInfo(dto.getProjectInfoId());
        if (CollUtil.isEmpty(orderInfos)) {
            return Result.success(CollUtil.newArrayList(MIDDLEWARE_ORDER_INFO));
        }
        return Result.success(orderInfos.stream().map(e -> new ProjSettlementMiddlewareOrderInfo(e.getOrderInfoId(),
                e.getContractNo(), e.getContractName(), e.getDeliveryOrderNo())).collect(Collectors.toList()));
    }

    public void validateExcel(Long projectInfoId, String filePath) {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        MultiValueMap<String, Object> envApplyMap = new LinkedMultiValueMap<>();
        envApplyMap.add("deployType", StrUtil.EMPTY);
        FileSystemResource fileSystemResource =
                applyOrderService.getFileSystemResource(projectInfo.getCustomInfoId().toString(),
                        filePath);
        envApplyMap.add("file", fileSystemResource);
        // 发送运维平台文件校验是否满足条件
        ResponseEntity<String> envApplyRes = null;
        try {
            HttpHeaders headers = applyOrderService.getHeaders();
            HttpEntity<MultiValueMap<String, Object>> httpRequest =
                    new HttpEntity<>(envApplyMap, headers);
            envApplyRes = new RestTemplate().postForEntity(devUrl + checkPreExcelUrl,
                    httpRequest, String.class);
            log.info("校验预资源规划文件返回值================:{}", envApplyRes);
        } catch (Exception e) {
            log.info("校验预资源规划文件返回值error================:{}", envApplyRes);
            log.error(e.getMessage());
            if (e.getMessage().contains("400")) {
                String replace = e.getMessage().replace("400 :", "").replace("[", "")
                        .replace("]", "");
                try {
                    JSONObject jsonObject = JSONObject.parseObject(replace);
                    throw new CustomException("校验预资源规划文件失败, 不符合技术中台要求, 错误：" + jsonObject.get("message"));
                } catch (Exception ex) {
                    throw new CustomException("校验预资源规划文件失败, 不符合技术中台要求, 错误：" + replace);
                }
            } else {
                throw new CustomException("校验预资源规划文件失败");
            }
        }
    }

    /**
     * 获取销售Id
     *
     * @param projectInfoId 项目id
     * @return String
     */
    public String getContractSignPersonId(Long projectInfoId) {
        ProjOrderInfo orderInfo = projOrderInfoService.selectByPrimaryKey(projectInfoId);
        ProjContractInfo contractInfo = projContractInfoService.selectByPrimaryKey(orderInfo.getContractInfoId());
        return StrUtil.toString(contractInfo.getContractSignPersonId());
    }

    @Override
    public Result<ProjProjectSettlementCheckSaleMainVO> getSaleApplyFormMainInfo(ProjProjectSettlementCheckSaleDTO projectSettlementCheckSaleDTO) {
        projectSettlementCheckSaleDTO.setSysUserId(StrUtil.toString(userHelper.getCurrentUser().getSysUserId()));
        long projectInfoId = Long.parseLong(projectSettlementCheckSaleDTO.getProjectInfoId());
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        // 根据项目id获取标题、项目名称等
        if (ObjectUtil.isEmpty(projectInfo)) {
            throw new RuntimeException("未获取到项目信息.");
        }
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(projectInfo.getCustomInfoId());
        if (ObjectUtil.isEmpty(customInfo)) {
            throw new RuntimeException("未获取到实施地客户信息.");
        }
        ProjProjectSettlementCheckSaleMainVO saleMainVO = new ProjProjectSettlementCheckSaleMainVO();
        saleMainVO.setTitle(customInfo.getCustomName() + "入驻申请");
        // - 设置合同编号
        ProjContractInfo contractInfo = mainService.getContract(projectInfo.getOrderInfoId());
        saleMainVO.setContractNo(contractInfo.getContractNo());
        saleMainVO.setContractName(contractInfo.getContractName());
        saleMainVO.setProjectName(projectInfo.getProjectName());
        // - 设置实施产品
        saleMainVO.setProductNames(mainService.getProductNames(projectInfoId));
        // - 根据申请条件表。若没有小硬件清单等
        getRulesInfo(projectInfo, saleMainVO);
        ProjProjectSettlement projProjectSettlement =
                projProjectSettlementService.getSettlementByProjectInfoId(projectInfoId);
        saleMainVO.setSettlementStatus(projProjectSettlement.getSettlementStatus());
        // 获取存储云资源工单与项目关联数据返给前端展示
        ProjProjectOrderRelation relation =
                mainService.getCloudResourceRelationBothType(projectInfoId);
        if (ObjectUtil.isNotEmpty(relation)) {
            List<ProjSettlementOrderInfo> settlementOrderInfos =
                    settlementMapper.findSettlementOrderInfoByCustomCloudServiceId(relation.getBussinessInfoId());
            if (CollUtil.isNotEmpty(settlementOrderInfos)) {
                // 获取客户云服务信息
                ProjSettlementOrderInfo settlementOrderInfo = settlementOrderInfos.get(0);
                // 获取项目工单
                List<ProjOrderInfo> orderInfos = orderInfoMapper.selectList(new QueryWrapper<ProjOrderInfo>().eq(
                        "yy_order_id", relation.getYyOrderId()));
                if (CollUtil.isNotEmpty(orderInfos)) {
                    boolean hasMiddlewareOrderInfo = mainService.hasMiddlewareOrderInfo(projectInfoId);
                    // 设置中间件工单信息
                    if (CollUtil.isNotEmpty(settlementOrderInfos) && hasMiddlewareOrderInfo) {
                        saleMainVO.setMiddlewareOrderInfo(new ProjSettlementMiddlewareOrderInfo(settlementOrderInfo.getOrderInfoId(), settlementOrderInfo.getContractNo(), settlementOrderInfo.getContractName(), settlementOrderInfo.getDeliveryOrderNo()));
                    }
                }
                // 若存在免中间件部署服务工单, 设置回显
                setFreeMidOrderInfo(projectInfoId, saleMainVO);
                // 设置众阳云类型
                saleMainVO.setCloudServiceType(ObjectUtil.isNotEmpty(settlementOrderInfo.getCloudServiceType())
                        ? settlementOrderInfo.getCloudServiceType() : null);
                // 设置部署节点
                saleMainVO.setEnvirId(settlementOrderInfo.getEnvirId());
                saleMainVO.setEnvirName(settlementOrderInfo.getEnvirName());
                saleMainVO.setSubscribeStartTime(DateUtil.formatDateTime(settlementOrderInfo.getSubscribeStartTime()));
                saleMainVO.setMsunCloudFlag(StrUtil.isNotBlank(settlementOrderInfo.getMsunCloudFlag())
                        ? Integer.parseInt(settlementOrderInfo.getMsunCloudFlag()) : null);
                // 设置众阳云工单
                if (CollUtil.isNotEmpty(settlementOrderInfos) && settlementOrderInfo.getCloudServiceType() == CloudServiceTypeEnum.MSUN_CLOUDE.getCode()) {
                    saleMainVO.setCloudServiceSale(settlementOrderInfo);
                }
            }
        }
        // 获取终审部分节点(用于PMO审核节点)
        saleMainVO.setCheckedProgressVOList(settlementCheckService.findCheckedNode(projectInfoId));
        // 操作人是否可编辑
        // - 查询当前节点人id是否与访问人id相同, 不同则readonly = true;
        setReadOnly(projectInfoId, projectSettlementCheckSaleDTO, saleMainVO);
        // 关键节点设置标识
        saleMainVO.setKeyFlagVO(mainService.getSettlementKeyFlagBean(projectInfoId));
        return Result.success(saleMainVO);
    }

    /**
     * 若存在免中间件部署服务工单, 设置回显
     *
     * @param projectInfoId 项目id
     * @param saleMainVO    返回值
     */
    private void setFreeMidOrderInfo(Long projectInfoId, ProjProjectSettlementCheckSaleMainVO saleMainVO) {
        ProjProjectSettlementMidOrder midOrder = commonService.getSettlementMidOrder(projectInfoId);
        if (ObjectUtil.isNotEmpty(midOrder)) {
            saleMainVO.setMiddlewareOrderInfo(MIDDLEWARE_ORDER_INFO);
            // 只有状态为驳回时会设置驳回原因
            if (midOrder.getStatus() == SettlementMidOrderStatusEnum.REJECTED.getCode()) {
                saleMainVO.setFreeMidOrderRejectReason(midOrder.getRejectReason());
            } else {
                saleMainVO.setFreeMidOrderRejectReason(StrUtil.EMPTY);
            }
            // 设置状态
            saleMainVO.setFreeMidOrderStatus(midOrder.getStatus());
            SettlementMidOrderStatusEnum statusEnum =
                    SettlementMidOrderStatusEnum.getSettlementMidOrderStatus(midOrder.getStatus());
            saleMainVO.setFreeMidOrderStatusDesc(statusEnum != null ? statusEnum.getDesc() : StrUtil.EMPTY);
            // 设置原因
            saleMainVO.setFreeMidOrderRemark(midOrder.getRemark());
            // 设置证明文件
            if (ObjectUtil.isNotEmpty(midOrder.getProjectFileId())) {
                ProjProjectFile projProjectFile = getProjectFile(midOrder.getProjectFileId());
                if (ObjectUtil.isNotEmpty(projProjectFile)) {
                    saleMainVO.setFreeMidOrderFileUrl(getProjectFileUrl(projProjectFile));
                    saleMainVO.setFreeMidOrderFileOriginalName(projProjectFile.getFileName());
                }
            }
        }
    }

    /**
     * 获取规则相关信息。
     * 如根据规则上传的文件信息等
     *
     * @param projectInfo 项目
     * @param saleMainVO  接口返回值
     */
    private void getRulesInfo(ProjProjectInfo projectInfo, ProjProjectSettlementCheckSaleMainVO saleMainVO) {
        List<ProjProjectSettlementRule> rules =
                projectSettlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>().eq(
                        "project_info_id", projectInfo.getProjectInfoId()));
        if (CollUtil.isNotEmpty(rules)) {
            rules.forEach(e -> {
                if (e.getProjectRuleCode().equals(SettlementRuleCodeEnum.SETTLEMENT_HARDWARE_LIST_FILE.getCode())) {
                    // 根据查询的projectFileId查询文件内容
                    saleMainVO.setSmallHardwareUrl(getSaleFileTempUrl(projectInfo.getProjectInfoId()));
                    // 获取项目文件obs临时路径
                    saleMainVO.setSmallHardwareBackUrl(getProjectFileUrl(e.getProjectFileId()));
                }
                if (e.getProjectRuleCode().equals(SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM.getCode())) {
                    // sys_file表获取模板文件信息, templatefileCode对应sys_file表中file_code字段. business_code不起作用
                    List<SysFile> sysFiles = sysFileMapper.selectList(new QueryWrapper<SysFile>().eq("file_code",
                            e.getTemplateFileCode()).orderByDesc("create_time"));
                    if (CollUtil.isNotEmpty(sysFiles)) {
                        SysFile sysFile = sysFiles.get(0);
                        saleMainVO.setCloudFirmFormModelUrl(OBSClientUtils.getTemporaryUrl(sysFile.getFilePath(),
                                ObsExpireTimeConsts.SEVEN_DAY));
                        saleMainVO.setCloudFirmFormModelBackUrl(getProjectFileUrl(e.getProjectFileId()));
                        saleMainVO.setCloudFirmFormName(sysFile.getFileName());
                    }
                }
                if (e.getProjectRuleCode().equals(SettlementRuleCodeEnum.SETTLEMENT_PLAN_CLOUD_FILE.getCode())) {
                    ProjApplyOrderMainDTO applyOrderMainDTO = new ProjApplyOrderMainDTO();
                    applyOrderMainDTO.setProjectInfoId(StrUtil.toString(projectInfo.getProjectInfoId()));
                    applyOrderMainDTO.setCustomInfoId(StrUtil.toString(projectInfo.getCustomInfoId()));
                    String preResouceUrl =
                            applyOrderService.downloadPreResourceFromOutSystem(applyOrderMainDTO).getFilePath();
                    saleMainVO.setPreResourceModelUrl(preResouceUrl);
                    // 尝试获取上传的与资源规划文件
                    saleMainVO.setPreResourceModelBackUrl(getProjectFileUrl(e.getProjectFileId()));
                }
            });
        }
    }

    /**
     * 设置页面是否可已进行审核通过或驳回
     *
     * @param projectInfoId                 项目id
     * @param projectSettlementCheckSaleDTO 请求参数
     * @param saleMainVO                    接口返回值
     */
    private void setReadOnly(Long projectInfoId, ProjProjectSettlementCheckSaleDTO projectSettlementCheckSaleDTO,
                             ProjProjectSettlementCheckSaleMainVO saleMainVO) {
        List<ProjProjectSettlementLog> settlementLogs =
                settlementLogMapper.selectList(new QueryWrapper<ProjProjectSettlementLog>().eq("project_info_Id",
                        projectInfoId).orderByDesc("create_time"));
        if (CollUtil.isEmpty(settlementLogs)) {
            saleMainVO.setReadOnly(false);
            return;
        }
        ProjProjectSettlementLog settlementLog = settlementLogs.get(0);
        if (settlementLog.getOperateUserId().toString().equals(projectSettlementCheckSaleDTO.getSysUserId())) {
            List<ProjProjectSettlement> settlements =
                    settlementMapper.selectList(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id",
                            projectInfoId));
            ProjProjectSettlement settlement = settlements.get(0);
            CheckNodeEnum checkNodeEnum =
                    CheckNodeEnum.getCheckNodeEnumByCode(Integer.parseInt(projectSettlementCheckSaleDTO.getCurrentCheckNode()));
            if (ObjectUtil.isEmpty(checkNodeEnum)) {
                saleMainVO.setReadOnly(true);
            } else {
                if (CollUtil.isEmpty(checkNodeEnum.getSettlementStatusEnums())) {
                    saleMainVO.setReadOnly(true);
                } else {
                    saleMainVO.setReadOnly(checkNodeEnum.getSettlementStatusEnums().stream().anyMatch(e -> e.getCode() == settlement.getSettlementStatus()));
                }
            }
        } else {
            saleMainVO.setReadOnly(false);
        }
    }


    public List<ShowFile> getSaleFileTempUrl(Long projectInfoId) {
        // 获取小硬件清单项目文件信息
        List<ProjProjectFile> projectFiles = getSaleFileHardware(projectInfoId);
        List<ShowFile> urlList = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(projectFiles)) {
            projectFiles.forEach(e -> {
                ShowFile showFile = ShowFile.builder()
                        .fileName(e.getFileName())
                        .fileUrl(OBSClientUtils.getTemporaryUrl(e.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY))
                        .build();
                urlList.add(showFile);
            });
        }
        return urlList;
    }

    public List<ProjProjectFile> getSaleFileHardware(Long projectInfoId) {
        List<ProjProjectFile> projProjectFiles = CollUtil.newArrayList();
        // 老平台获取小硬件清单
        List<SurveyHardwareNewResultdataEntity> hardwareNewResultdataEntities =
                projProjectSettlementService.selectHardwareFile(projectInfoId);
        if (CollUtil.isNotEmpty(hardwareNewResultdataEntities)) {
            for (SurveyHardwareNewResultdataEntity hardwareNewResultdataEntity : hardwareNewResultdataEntities) {
                ProjProjectFile projProjectFile = new ProjProjectFile();
                String fileName = hardwareNewResultdataEntity.getUrl();
                String filePath = "imsp/" + fileName;
                projProjectFile.setFileName(fileName);
                projProjectFile.setFilePath(filePath);
                projProjectFiles.add(projProjectFile);
            }
        }
        // 新平台获取小硬件清单
        List<ProjProjectFile> fileList = projectFileMapper.selectList(new QueryWrapper<ProjProjectFile>()
                .eq("project_info_id", projectInfoId).eq("milestone_node_code", SURVEY_HARDWARE.getCode()));
        if (CollUtil.isNotEmpty(fileList)) {
            projProjectFiles.addAll(fileList);
        }
        return projProjectFiles;
    }

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<Boolean> saveSettlementSale(ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO) {
        settlementCheckSaleSaveDTO.setSysUserId(StrUtil.toString(userHelper.getCurrentUser().getSysUserId()));
        // 生成审核节点
        Long projectInfoId = Long.parseLong(settlementCheckSaleSaveDTO.getProjectInfoId());
        ProjProjectSettlement settlement = settlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq(
                "project_info_id",
                projectInfoId));
        SettlementRuleParam settlementRuleParam = SettlementRuleParam.builder()
                .sysUserId(Long.parseLong(settlementCheckSaleSaveDTO.getSysUserId()))
                .projectInfoId(projectInfoId)
                .projectSettlementId(settlement.getProjectSettlementId())
                .createRule(true)
                .createCheck(true)
                .cloudServiceType(settlementCheckSaleSaveDTO.getCloudServiceType())
                .settlementCheckSaleSaveDTO(settlementCheckSaleSaveDTO).build();
        settlementRuleService.setRulesAndCheckBySence(settlementRuleParam);
        // 处理销售提交申请
        saveSettlementSaleImpl(settlementCheckSaleSaveDTO);
        return Result.success();
    }

    public void saveSettlementSaleImpl(ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO) {
        long projectInfoId = Long.parseLong(settlementCheckSaleSaveDTO.getProjectInfoId());
        int currentCheckNode = CheckNodeEnum.SALE_APPLY_ENTRY.getCode();
        int checkResult = CheckResultEnum.AUDIT_PASS.getCode();
        ProjProjectSettlement settlement = projProjectSettlementService.getProjectSettlement(projectInfoId);
        // 更新运营部审核状态
        settlementCheckService.updateRiskaudit(projectInfoId, currentCheckNode, checkResult);
        // 保存申请单状态
        // 获取最后一个成功节点
        SettlementStatusEnum settlementStatusEnum = getLastedSuccessSettlementStatusEnum(projectInfoId,
                currentCheckNode, checkResult);
        if (ObjectUtil.isEmpty(settlementStatusEnum)) {
            log.warn("未查询到审核节点状态. projectInfoId: {}", projectInfoId);
            throw new CustomException("未查询到审核节点状态. ");
        }
        projProjectSettlementService.updateSettlementStatus(settlement, settlementStatusEnum);
        SysUserVO sysUserVO = sysUserService.getUserById(Long.parseLong(settlementCheckSaleSaveDTO.getSysUserId()));
        // 插入日志
        settlementLogService.insert(settlement, CheckNodeEnum.SALE_APPLY_ENTRY.getDesc(),
                CheckNodeEnum.SALE_APPLY_ENTRY, sysUserVO,
                CheckNodeEnum.SALE_APPLY_ENTRY.getSettlementStatusEnums().get(0).getCode());
        // 更新终审表
        settlementCheckService.updateResult(settlement.getProjectSettlementId(), sysUserVO.getSysUserId(),
                CheckNodeEnum.SALE_APPLY_ENTRY.getDesc(), CheckNodeEnum.SALE_APPLY_ENTRY, CheckResultEnum.AUDIT_PASS);
        boolean isFirstProject = mainService.isFirstProject(projectInfoId);
        // 上传到货清单
        if (StrUtil.isNotBlank(settlementCheckSaleSaveDTO.getHardwareArrivalUrl())) {
            handlUploadFile(projectInfoId, "采购到货清单.png", settlementCheckSaleSaveDTO.getHardwareArrivalUrl(),
                    SettlementRuleCodeEnum.SETTLEMENT_HARDWARE_LIST_FILE.getVoCode());
        }
        // 若是首期
        if (isFirstProject) {
            cloudHandle(settlementCheckSaleSaveDTO, projectInfoId);
        }
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        // 查询销售申请的下一个节点
        int nextCheckNode = getNextCheckNode(projectInfoId, currentCheckNode, checkResult);
        if (nextCheckNode == NextCheckNodeUnNormalEnum.HAS_SETTLEMENT_NEXT_NODE.getCode()) {
            settlementCheckService.updateProjectEntryMilestone(projectInfoId, settlementCheckSaleSaveDTO,
                    Long.parseLong(settlementCheckSaleSaveDTO.getSysUserId()));
            String msg = ", " + CheckNodeEnum.SALE_APPLY_ENTRY.getDesc() + ", 请您知晓!";
            // 向项目经理推送消息
            sendMessageToProjMgr(projectInfoId, msg);
        } else {
            // 发送到下一个节点
            sendMessage(currentCheckNode, nextCheckNode, projectInfoId, projectInfo);
        }
    }

    /**
     * 获取最后一个不间断的成功节点
     *
     * @param projectInfoId    项目id
     * @param currentCheckNode 当前审查节点
     * @return SettlementStatusEnum
     */
    public SettlementStatusEnum getLastedSuccessSettlementStatusEnum(Long projectInfoId, int currentCheckNode,
                                                                     int checkResultCode) {
        CheckNodeParam checkNodeParam = getNextCheckNodeImpl(projectInfoId, currentCheckNode, checkResultCode);
        CheckNodeEnum lastSucessCheckNodeEnum =
                CheckNodeEnum.getCheckNodeEnumByCode(checkNodeParam.getCurrentCheckNode());
        return CheckNodeEnum.getSettlementStatusEnum(lastSucessCheckNodeEnum, checkResultCode);
    }

    private void updateProjectFile(String cloudFileUrl,
                                   ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO,
                                   Long projectInfoId) {
        if (StrUtil.isNotBlank(cloudFileUrl)) {
            handleCloudFile(projectInfoId, settlementCheckSaleSaveDTO,
                    SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM);
        }
    }

    /**
     * 处理云资源
     *
     * @param settlementCheckSaleSaveDTO 请求参数
     * @param projectInfoId              项目id
     */
    private void cloudHandle(ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO, Long projectInfoId) {
        boolean hasMsunCloudForm = false;
        if (StrUtil.isNotEmpty(settlementCheckSaleSaveDTO.getCustomCloudServiceId())) {
            ProjOrderInfo orderInfo =
                    orderInfoMapper.selectById(Long.parseLong(settlementCheckSaleSaveDTO.getCustomCloudServiceId()));
            OrderTypeEnums orderTypeEnums = OrderTypeEnums.getEnum(orderInfo.getDeliveryOrderType());
            hasMsunCloudForm = OrderTypeEnums.CLOUD_RESOURCE == orderTypeEnums;
        }
        // 查出工单关联
        ProjProjectOrderRelation relation = mainService.getCloudResourceRelationBothType(projectInfoId);
        ProjProjectOrderRelation relationDto = ObjectUtil.isEmpty(relation) ? null : relation;
        if (hasMsunCloudForm) {
            // 有云资源工单（众阳云）
            // - 获取云资源工单详情
            Long customCloudServiceId = Long.parseLong(settlementCheckSaleSaveDTO.getCustomCloudServiceId());
            List<ProjSettlementOrderInfo> settlementOrderInfos =
                    settlementMapper.findSettlementOrderInfoByOrderInfoId(customCloudServiceId);
            ProjSettlementOrderInfo settlementOrderInfo = settlementOrderInfos.get(0);
            // - 保存客服云信息
            ProjCustomCloudService cloudService = getCloudService(settlementCheckSaleSaveDTO, settlementOrderInfo);
            cloudService = cloudServiceService.insertOrUpdate(cloudService, projectInfoId, relationDto,
                    settlementOrderInfo.getYyOrderId());
            // - 绑定关系
            inserOrUpdateRelation(projectInfoId, cloudService.getCustomCloudServiceId(), settlementOrderInfo,
                    relationDto);
            // 保存项目文件
            updateProjectFile(settlementCheckSaleSaveDTO.getCloudFileUrl(), settlementCheckSaleSaveDTO, projectInfoId);
        } else {
            ProjSettlementOrderInfo settlementOrderInfo;
            String cloudFileUrl = settlementCheckSaleSaveDTO.getCloudFileUrl();
            boolean isMusnServiceType = isMsunServiceType(settlementCheckSaleSaveDTO.getCloudServiceType());
            if (StrUtil.isNotBlank(cloudFileUrl)) {
                handleCloudFile(projectInfoId, settlementCheckSaleSaveDTO, isMusnServiceType
                        ? SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM
                        : SettlementRuleCodeEnum.SETTLEMENT_PLAN_CLOUD_FILE);
            }
            // 无云资源工单（众阳云/共享众阳云/私有云）
            long businessInfoId;
            if (settlementCheckSaleSaveDTO.getCloudServiceType() == CloudServiceTypeEnum.PRIVATE_CLOUDE.getCode()) {
                ProjOrderInfo orderInfo =
                        orderInfoMapper.selectById(Long.parseLong(settlementCheckSaleSaveDTO.getMiddlewareOrderInfoId()));
                ProjCustomCloudService cloudService = getCloudServiceNotForm(settlementCheckSaleSaveDTO,
                        isMusnServiceType);
                // 若未查询到中间件工单, 则工单id置为-1, 此种情况用于免中间件服务
                cloudService = cloudServiceService.insertOrUpdate(cloudService, projectInfoId, relationDto,
                        ObjectUtil.isNotEmpty(orderInfo) ? orderInfo.getYyOrderId() : -1L);
                businessInfoId = cloudService.getCustomCloudServiceId();
                // 若含免中间件部署, 创建申请单
                if (StrUtil.equals(settlementCheckSaleSaveDTO.getMiddlewareOrderInfoId(), "-1")) {
                    handleMidOrder(projectInfoId, settlementCheckSaleSaveDTO);
                } else {
                    // 作废免中间件申请单, 详情看方法注释
                    deleteMidOrder(projectInfoId);
                }
            } else {
                ProjCustomCloudService cloudService = getCloudServiceNotForm(settlementCheckSaleSaveDTO,
                        isMusnServiceType);
                cloudService = cloudServiceService.insertOrUpdate(cloudService, projectInfoId, relationDto, -1L);
                businessInfoId = cloudService.getCustomCloudServiceId();
                // 作废免中间件申请单, 详情看方法注释
                deleteMidOrder(projectInfoId);
            }
            List<ProjSettlementOrderInfo> orderInfos =
                    settlementMapper.findSettlementOrderInfoByCustomCloudServiceId(businessInfoId);
            settlementOrderInfo = orderInfos.get(0);
            // 保存手动填写的新增节点
            inserOrUpdateRelation(projectInfoId, businessInfoId, settlementOrderInfo, relationDto);
        }
    }

    /**
     * 若存在免中间申请, 作废, 销售切换云类型时发挥作用, 如已申请过私有云, 后又变更至共享云时。
     *
     * @param projectInfoId 项目id
     */
    private void deleteMidOrder(Long projectInfoId) {
        // 若存在免中间申请, 作废, 销售切换云类型时发挥作用, 如已申请过私有云, 后又变更至共享云时。
        int count = settlementMidOrderMapper.delete(new QueryWrapper<ProjProjectSettlementMidOrder>().eq(
                "project_info_id", projectInfoId));
        log.info("作废免中间件申请单. count: {}, projectInfoId: {}", count, projectInfoId);
    }

    /**
     * 处理免中间件申请. 会更新或新增申请单
     *
     * @param projectInfoId              项目id
     * @param settlementCheckSaleSaveDTO 销售申请载荷
     */
    private void handleMidOrder(Long projectInfoId, ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO) {
        // 获取申请单
        ProjProjectSettlementMidOrder midOrder = commonService.getSettlementMidOrder(projectInfoId);
        // 若驳回则重置状态为准申请
        if (ObjectUtil.isNotEmpty(midOrder)) {
            if (!canEdit(midOrder)) {
                return;
            }
            ProjProjectSettlementMidOrder copy = new ProjProjectSettlementMidOrder();
            copy.setProjectSettlementMidOrderId(midOrder.getProjectSettlementMidOrderId());
            copy.setStatus(SettlementMidOrderStatusEnum.PRE_APPLY.getCode());
            copy.setApplyUserId(userHelper.getCurrentUser().getSysUserId());
            // 更新原因说明, 若与原始内容不一致则更新
            if (!StrUtil.equals(midOrder.getRemark(), settlementCheckSaleSaveDTO.getFreeMidOrderRemark())) {
                copy.setRemark(settlementCheckSaleSaveDTO.getFreeMidOrderRemark());
            }
            // 更新证明文件, 若上传新内容, 则更新
            if (StrUtil.isNotBlank(settlementCheckSaleSaveDTO.getFreeMidOrderFileUrl())) {
                // 作废原始文件
                if (ObjectUtil.isNotEmpty(midOrder.getProjectFileId())) {
                    int delcount = settlementMidOrderMapper.deleteById(midOrder.getProjectFileId());
                    log.info("销售提交申请, 作废免中间件项目文件. count: {}, getProjectFileId: {}", delcount,
                            midOrder.getProjectFileId());
                }
                // 设置文件id
                ProjProjectFileExtend projectFileExtend = createProjectFileRelation(projectInfoId,
                        settlementCheckSaleSaveDTO.getFreeMidOrderFileOriginalName(),
                        settlementCheckSaleSaveDTO.getFreeMidOrderFileUrl());
                copy.setProjectFileId(projectFileExtend.getProjectFileId());
            }
            int count = settlementMidOrderMapper.updateById(copy);
            log.info("销售提交申请, 变更免中间件申请单状态. count: {}, origion: {}, target: {}", count, JSONUtil.toJsonStr(midOrder),
                    JSONUtil.toJsonStr(copy));
        } else {
            ProjProjectSettlementMidOrder copy = new ProjProjectSettlementMidOrder();
            copy.setStatus(SettlementMidOrderStatusEnum.PRE_APPLY.getCode());
            copy.setProjectSettlementMidOrderId(SnowFlakeUtil.getId());
            copy.setApplyUserId(userHelper.getCurrentUser().getSysUserId());
            copy.setRemark(settlementCheckSaleSaveDTO.getFreeMidOrderRemark());
            // 设置文件id
            ProjProjectFileExtend projectFileExtend = createProjectFileRelation(projectInfoId,
                    settlementCheckSaleSaveDTO.getFreeMidOrderFileOriginalName(),
                    settlementCheckSaleSaveDTO.getFreeMidOrderFileUrl());
            copy.setProjectFileId(projectFileExtend.getProjectFileId());
            copy.setProjectInfoId(projectInfoId);
            int count = settlementMidOrderMapper.insert(copy);
            log.info("销售提交申请, 新增免中间件申请单. count: {}, detail: {}", count, JSONUtil.toJsonStr(copy));
        }
    }

    /**
     * 是否需要更新
     *
     * @param midOrder 申请单
     * @return true: 需要更新, false: 无需更新
     */
    private boolean canEdit(ProjProjectSettlementMidOrder midOrder) {
        if (midOrder.getStatus() == SettlementMidOrderStatusEnum.APPROVED.getCode()) {
            log.info("销售提交申请, 变更免中间件申请单状态为审核通过, 不更新.");
            return false;
        }
        if (midOrder.getStatus() == SettlementMidOrderStatusEnum.APPLYED.getCode()) {
            log.info("销售提交申请, 变更免中间件申请单状态为已申请, 不更新.");
            return false;
        }
        return true;
    }

    @Resource
    private ProjProjectSettlementMidOrderMapper settlementMidOrderMapper;

    /**
     * 生成云资源(未选择云资源工单时使用)服务单
     *
     * @param settlementCheckSaleSaveDTO 申请参数
     * @param isMusnServiceType          是否是众阳云资源
     * @return ProjCustomCloudService
     */
    private static ProjCustomCloudService getCloudServiceNotForm(ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO, boolean isMusnServiceType) {
        ProjCustomCloudService cloudService = new ProjCustomCloudService();
        cloudService.setDeployNodeName(settlementCheckSaleSaveDTO.getEnvirName());
        cloudService.setDeployNodeId(StrUtil.isNotEmpty(settlementCheckSaleSaveDTO.getDeployNodeId())
                ? Long.valueOf(settlementCheckSaleSaveDTO.getDeployNodeId()) : null);
        cloudService.setCloudServiceType(settlementCheckSaleSaveDTO.getCloudServiceType());
        if (isMusnServiceType) {
            cloudService.setSubscribeStartTime(settlementCheckSaleSaveDTO.getSubscribeStartTime());
        }
        return cloudService;
    }

    /**
     * 生成云资源服务单(选择云资源工单使用)
     *
     * @param settlementCheckSaleSaveDTO 申请参数
     * @return ProjCustomCloudService
     */
    private ProjCustomCloudService getCloudService(ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO,
                                                   ProjSettlementOrderInfo settlementOrderInfo) {
        ProjCustomCloudService cloudService = new ProjCustomCloudService();
        cloudService.setDeployNodeId(Long.parseLong(settlementCheckSaleSaveDTO.getDeployNodeId()));
        // 查询节点名称
        DictCloudEnvironments cloudEnvironments =
                dictCloudEnvironmentsMapper.selectById(Long.parseLong(settlementCheckSaleSaveDTO.getDeployNodeId()));
        if (ObjectUtil.isEmpty(cloudEnvironments)) {
            throw new RuntimeException("未查询操云资源信息.");
        }
        cloudService.setDeployNodeName(cloudEnvironments.getEnvirName());
        cloudService.setSubscribeStartTime(settlementCheckSaleSaveDTO.getSubscribeStartTime());
        // 设置云资源开通和结束时间
        List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq(
                "order_info_id", settlementOrderInfo.getOrderInfoId()).orderByDesc("create_time"));
        if (CollUtil.isEmpty(orderProducts)) {
            throw new RuntimeException("未查询到云资源工单产品.");
        }
        ProjOrderProduct orderProduct = orderProducts.get(0);
        setSubscribeEndTime(orderProduct, cloudService, settlementCheckSaleSaveDTO.getSubscribeStartTime(),
                orderProduct.getProductSubscribeTerm());
        cloudService.setServiceSubscribeTerm(orderProduct.getProductSubscribeTerm().intValue());
        cloudService.setCloudServiceType(CloudServiceTypeEnum.MSUN_CLOUDE.getCode());
        return cloudService;
    }

    /**
     * 设置结束时间
     * <p>使用合同签订时间计算到期时间</p>
     *
     * @param orderProduct  产品
     * @param cloudService  客户云信息
     * @param startTime     开始时间
     * @param subscribeTerm 订阅时间单位
     */
    public void setSubscribeEndTime(ProjOrderProduct orderProduct, ProjCustomCloudService cloudService,
                                    Date startTime, Long subscribeTerm) {
        Date endDateTime = getEndTime(startTime, orderProduct.getProductBuyMode(), subscribeTerm);
        cloudService.setSubscribeEndTime(endDateTime);
    }

    /**
     * 设置开始结束时间
     *
     * @param startDateTime  开始时间
     * @param productBuyMode 模式
     * @param subscribeTerm  期限
     * @return Date
     */
    public static Date getEndTime(Date startDateTime, int productBuyMode, Long subscribeTerm) {
        return CloudServiceKit.getCloudServiceSubscribeEndTime(startDateTime, productBuyMode, subscribeTerm);
    }


    private static boolean isMsunServiceType(int cloudServiceType) {
        return cloudServiceType == CloudServiceTypeEnum.MSUN_CLOUDE.getCode();
    }

    public int getNextCheckNode(Long projectInfoId, int currentCheckNode, int checkResult) {
        return getNextCheckNodeImpl(projectInfoId, currentCheckNode, checkResult).getNextCheckNode();
    }

    /**
     * 查询下一个节点和当前实际节点
     *
     * @param projectInfoId    项目id
     * @param currentCheckNode 当前节点
     * @param checkResult      审核结果
     * @return CheckNodeParam
     */
    public CheckNodeParam getNextCheckNodeImpl(Long projectInfoId, int currentCheckNode, int checkResult) {
        // 不是销售申请。查询终审内容进行获取下一步节点
        if (CheckResultEnum.AUDIT_PASS.getCode() == checkResult) {
            return getNextCheckNodeNormal(projectInfoId, currentCheckNode);
        }
        // 驳回一定会从销售重新发起
        return CheckNodeParam.builder().nextCheckNode(CheckNodeEnum.SALE_APPLY_ENTRY.getCode()).currentCheckNode(currentCheckNode).build();
    }

    public CheckNodeParam getNextCheckNodeNormal(Long projectInfoId, int currentCheckNode) {
        List<ProjProjectSettlementCheck> settlementChecks =
                settlementRuleService.findSettlementCheck(projectInfoId).stream().sorted(Comparator.comparing(ProjProjectSettlementCheck::getCheckNode)).collect(Collectors.toList());
        int nextCheckNode = NextCheckNodeUnNormalEnum.NOT_EXIST_NEXT_NODE.getCode();
        if (currentCheckNode < settlementChecks.get(0).getCheckNode()) {
            return CheckNodeParam.builder()
                    .nextCheckNode(settlementChecks.get(0).getCheckNode())
                    .currentCheckNode(currentCheckNode)
                    .build();
        }
        for (int i = 0; i < settlementChecks.size(); i++) {
            ProjProjectSettlementCheck settlementCheck = settlementChecks.get(i);
            if (settlementCheck.getCheckNode() == currentCheckNode) {
                if (i == settlementChecks.size() - 1) {
                    return CheckNodeParam.builder()
                            .nextCheckNode(NextCheckNodeUnNormalEnum.HAS_SETTLEMENT_NEXT_NODE.getCode())
                            .currentCheckNode(currentCheckNode)
                            .build();
                }
                ProjProjectSettlementCheck nextCheck = settlementChecks.get(i + 1);
                if (ObjectUtil.isNotEmpty(nextCheck.getCheckResult()) && nextCheck.getCheckResult() == CheckResultEnum.AUDIT_PASS.getCode()) {
                    currentCheckNode = nextCheck.getCheckNode();
                    continue;
                }
                nextCheckNode = nextCheck.getCheckNode();
                break;
            }
        }
        return CheckNodeParam.builder()
                .nextCheckNode(nextCheckNode)
                .currentCheckNode(currentCheckNode)
                .build();
    }

    public void sendMessage(int currentCheckNode, int nextCheckNode, Long projectInfoId, ProjProjectInfo projectInfo) {
        sendMessage(currentCheckNode, nextCheckNode, projectInfoId, projectInfo, false);
    }

    public void sendMessage(int currentCheckNode, int nextCheckNode, Long projectInfoId, ProjProjectInfo projectInfo,
                            boolean onlyMessagePush) {
        // 判断下一个节点要发送的人会id
        CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(nextCheckNode);
        CheckNodeEnum currentCheckNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(currentCheckNode);
        if (ObjectUtil.isEmpty(checkNodeEnum)) {
            log.warn("未查询到下一节点. nextCheckNode: {}, currentCheckNode: {}, projectInfoId: {}, onlyMessagePush: {}",
                    nextCheckNode, currentCheckNode, projectInfoId, onlyMessagePush);
            return;
        }
        // PMO审核
        if (checkNodeEnum.getCode() == CheckNodeEnum.PMO_AUDIT.getCode()) {
            // 查询pmo的部门成员
            String businessUrl =
                    currentUrl + MessageUrlConsts.getSendSettledAuditUrl() + "projectInfoId=" + projectInfoId
                            + "&currentCheckNode=" + nextCheckNode;
            sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_PMO_AUDIT.getId(),
                    commonService.getMessageUrl(businessUrl), projectInfoId,
                    projectInfo.getProjectName(), null, MsgToCategory.TO_ROLE.getCode());
            // 向项目经理推送消息
            sendMessageToProjMgr(projectInfoId, ", " + currentCheckNodeEnum.getDesc() + "已通过审核, 请您知晓!");
        }
        // 分公司经理
        if (checkNodeEnum.getCode() == CheckNodeEnum.BRANCH_MANAGER_AUDIT.getCode()) {
            // 查询分公司经理id
            String userId =
                    mainService.getBranchMgrLeaderYunyingId(mainService.getProjectInfo(projectInfoId).getOrderInfoId());
            if (!onlyMessagePush) {
                // 更新下一节点信息
                updateOrInsertUnCheckData(Long.parseLong(userId), currentCheckNode, nextCheckNode, projectInfoId);
            }
            // 发送消息
            SysUser sysUser = sysUserMapper.selectUserIdByYungyingId(userId);
            String url = getBranchUrl(currentUrl, projectInfoId, nextCheckNode);
            sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_BRANCH_MGR_AUDIT.getId(), url, projectInfoId,
                    projectInfo.getProjectName(), sysUser.getSysUserId(),
                    MsgToCategory.SYS.getCode());
            // 向项目经理推送消息
            sendMessageToProjMgr(projectInfoId, ", " + currentCheckNodeEnum.getDesc() + "已通过审核, 请您知晓!");
        }
        // RISK 向风控发送消息并通知运营平台
        if (checkNodeEnum.getCode() == CheckNodeEnum.RISK_AUDIT.getCode()) {
            assert currentCheckNodeEnum != null;
            sendRiskMessage(projectInfo, onlyMessagePush);
            // 向项目经理上一节点审核通过消息
            sendMessageToProjMgr(projectInfoId, ", " + currentCheckNodeEnum.getDesc() + "已通过审核, 请您知晓!");
        }
        // 向项目经理发送消息、向销售发送消息
        if (checkNodeEnum.getCode() == CheckNodeEnum.ENSURE_SETTLE_IN.getCode()) {
            SysUser sysUser = commonService.getContractCustomSalePerson(projectInfoId);
            // 向销售发消息
            commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_ENSURE.getId(), null, projectInfoId,
                    sysUser.getSysUserId(), MsgToCategory.SYS.getCode(),
                    ", " + projectInfo.getProjectName() + "入驻确认单" + currentCheckNodeEnum + "已通过审核, 请您知晓!");
            // 向项目经理推送消息
            sendMessageToProjMgr(projectInfoId, ", " + currentCheckNodeEnum.getDesc() + "已通过审核, 请您知晓!");
        }
        // 向销售发消息。此处只有在驳回状态下执行. 除此之外（给销售发送消息，还有入场条件确认申请时，申请方法在settlementRuleController.saveSettlement方法中调用）
        if (checkNodeEnum.getCode() == CheckNodeEnum.SALE_APPLY_ENTRY.getCode()) {
            String msg = ", " + currentCheckNodeEnum.getDesc() + "驳回, 请您知晓!";
            // 向项目经理推送消息
            sendMessageToProjMgr(projectInfoId, msg);
            // 向销售发消息
            sendMessageToSalePerson(projectInfoId, msg);
            // 只有在被驳回时才发送消息通知pmo
            if (!onlyMessagePush) {
                String nodeName = StrUtil.EMPTY;
                if (ObjectUtil.isNotEmpty(currentCheckNodeEnum)) {
                    nodeName = currentCheckNodeEnum.getDesc();
                }
                // 获取驳回原因
                String checkContent = StrUtil.EMPTY;
                ProjProjectSettlementCheck check =
                        settlementCheckMapper.selectOne(new QueryWrapper<ProjProjectSettlementCheck>().eq(
                                "project_info_id",
                                projectInfoId).eq("check_node", currentCheckNodeEnum.getCode()));
                if (ObjectUtil.isNotEmpty(check)) {
                    checkContent = check.getCheckContent();
                }
                // 向pmo发送消息通知, 需pmo知晓
                commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_ENTRY_REJECT_PMO_KOWN.getId(), null,
                        projectInfoId,
                        null, MsgToCategory.TO_ROLE.getCode(),
                        projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber()
                                + ", 入驻申请被驳回, 请知晓, " + nodeName + "驳回原因:" + checkContent);
            }
        }
    }

    /**
     * 向风控和项目经理发送消息
     *
     * @param projectInfo 项目
     */
    public void sendRiskMessage(ProjProjectInfo projectInfo, boolean onlyMessagePush) {
        sendRiskMessage(projectInfo, onlyMessagePush, false);
    }

    /**
     * 向风控和项目经理发送消息
     *
     * @param projectInfo     项目
     * @param onlyMessagePush 是否只发消息
     * @param jumpPaysignage  是否跳过首付款
     */
    public void sendRiskMessage(ProjProjectInfo projectInfo, boolean onlyMessagePush, boolean jumpPaysignage) {
        Long projectInfoId = projectInfo.getProjectInfoId();
        ProjOrderInfo projOrderInfo = projOrderInfoService.selectByPrimaryKey(projectInfo.getOrderInfoId());
        ProjContractInfo contractInfo = getContract(projOrderInfo);
        if (commonService.needAuditMidOrder(projectInfoId)) {
            // 向运营平台发送消息, 审核免中间件工单
            commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_RISK_AUDIT.getId(), null, projectInfoId,
                    null, MsgToCategory.SINGLE_PERSON.getCode(), projectInfo.getProjectName()
                            + "入驻确认单-免派工中间件部署申请, "
                            + "运营平台合同号为:"
                            + " " + contractInfo.getContractNo());
            if (!onlyMessagePush) {
                yunYingService.sendMidOrder(projectInfo);
            }
        } else if (!jumpPaysignage && settlementRuleService.needPaySignage(projectInfoId)) {
            // 给运营平台也要发送消息知晓
            commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_RISK_AUDIT.getId(), null,
                    projectInfoId, null, MsgToCategory.SINGLE_PERSON.getCode(),
                    projectInfo.getProjectName() + "入驻确认单-审核首付款, 运营平台合同号为: " + contractInfo.getContractNo());
            if (!onlyMessagePush) {
                SysUser sysUser = mainService.getSysUserByProjectInfoId(projectInfoId);
                String messageContent =
                        projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + ", "
                                + "通知运营部预付款审核接口异常.";
                mainService.prePaymentFeeBack(sysUser, projOrderInfo.getYyOrderId(), messageContent,
                        projectInfo.getProjectInfoId());
            }
        } else if (mainService.hasCloudFormConfirm(projectInfoId)) {
            // 向运营同步云资源开通时间(电销可以不用审核开通确认单)
            commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_RISK_AUDIT.getId(), null, projectInfoId,
                    null, MsgToCategory.SINGLE_PERSON.getCode(), projectInfo.getProjectName()
                            + "入驻确认单-审核云资源开通时间, "
                            + "运营平台合同号为:"
                            + " " + contractInfo.getContractNo());
            if (!onlyMessagePush) {
                yunYingService.sendYunyingCloudTimeSettlement(projectInfoId);
            }
        }
    }

    /**
     * 获取分公司经理url
     *
     * @param currentUrl    根路径
     * @param projectInfoId 项目id
     * @param nextCheckNode 下一个结点
     * @return String
     */
    private String getBranchUrl(String currentUrl, Long projectInfoId, int nextCheckNode) {
        String businessUrl = currentUrl + MessageUrlConsts.getSenddeployNodesUrl() + "projectInfoId=" + projectInfoId
                + "&currentCheckNode=" + nextCheckNode + "&operation=1";
        return commonService.getMessageUrl(businessUrl);
    }

    /**
     * 向项目经理发送消息
     *
     * @param projectInfoId 项目id
     * @param content       消息描述内容
     */
    public void sendMessageToSalePerson(Long projectInfoId, String content) {
        SysUser sysUser = commonService.getContractCustomSalePerson(projectInfoId);
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        String url = getUrl(currentUrl, projectInfoId, sysUser.getSysUserId());
        commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_ENSURE.getId(), url, projectInfoId,
                sysUser.getSysUserId(), MsgToCategory.SYS.getCode(), projectInfo.getProjectName() + "入驻确认单" + content);
    }

    /**
     * 拼接url
     *
     * @param currentUrl    路径
     * @param projectInfoId 项目id
     * @return String
     */
    private String getUrl(String currentUrl, Long projectInfoId, Long sysUserId) {
        String businessUrl = currentUrl + MessageUrlConsts.getSendSaleUrl() + "projectInfoId="
                + projectInfoId + "&currentCheckNode=" + CheckNodeEnum.SALE_APPLY_ENTRY.getCode();
        return commonService.getMessageUrl(businessUrl, sysUserId);
    }

    /**
     * 向项目经理发送消息
     *
     * @param projectInfoId 项目id
     * @param content       消息描述内容
     */
    public void sendMessageToProjMgr(Long projectInfoId, String content) {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        Long projLeaderId = mainService.getProjectMgrPersonId(projectInfoId);
        // 向项目经理发消息
        commonService.sendMessageToSysPerson(DictMessageTypeEnum.SETTLEMENT_PROJ_MGR_AUDIT.getId(), null, projectInfoId,
                projLeaderId, MsgToCategory.TO_PRO_MGR.getCode(), projectInfo.getProjectName() + "入驻确认单" + content);
    }

    /**
     * 更新待审核数据
     *
     * @param userId           操作用户id
     * @param currentCheckNode 当前操作节点
     * @param nextCheckNode    下一个操作节点
     * @param projectInfoId    项目id
     */
    private void updateOrInsertUnCheckData(long userId, int currentCheckNode, int nextCheckNode, Long projectInfoId) {
        List<ProjProjectSettlementUnCheck> unChecks =
                settlementUnCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementUnCheck>()
                        .eq("project_info_id", projectInfoId)
                        .eq("un_check_node", currentCheckNode)
                        .eq("un_check_user_id", userId).orderByDesc("create_time"));
        if (CollUtil.isEmpty(unChecks)) {
            insertUnCheckData(userId, currentCheckNode, nextCheckNode, projectInfoId);
            return;
        }
        ProjProjectSettlementUnCheck unCheck = unChecks.get(0);
        ProjProjectSettlementUnCheck copy = new ProjProjectSettlementUnCheck();
        copy.setProjectSettlementLogId(null);
        copy.setProjectSettlementUnCheckId(unCheck.getProjectSettlementUnCheckId());
        int count = settlementUnCheckMapper.updateById(copy);
        log.info("更新待审核数据. count: {}", count);

    }

    /**
     * 新增待审核节点
     *
     * @param userId           用户id
     * @param currentCheckNode 当前节点
     * @param nextCheckNode    下一节点
     * @param projectInfoId    项目id
     */
    public void insertUnCheckData(Long userId, int currentCheckNode, int nextCheckNode, Long projectInfoId) {
        ProjProjectSettlementUnCheck unCheck = new ProjProjectSettlementUnCheck();
        unCheck.setUnCheckNode(nextCheckNode);
        unCheck.setUnCheckUserId(userId);
        List<ProjProjectSettlementCheck> checks =
                settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_info_id",
                        projectInfoId).eq("check_node", currentCheckNode));
        if (CollUtil.isEmpty(checks)) {
            throw new RuntimeException("未查询到终审表数据. projectInfoId: " + projectInfoId);
        }
        unCheck.setProjectSettlementCheckId(checks.get(0).getProjectSettlementCheckId());
        List<ProjProjectSettlement> settlements =
                settlementMapper.selectList(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id",
                        projectInfoId));
        if (CollUtil.isEmpty(checks)) {
            throw new RuntimeException("未查询到记录当前节点表数据. projectInfoId: " + projectInfoId);
        }
        unCheck.setProjectSettlementId(settlements.get(0).getProjectSettlementId());
        if (CollUtil.isEmpty(checks)) {
            throw new RuntimeException("未查询到记录节点日志表数据. projectInfoId: " + projectInfoId);
        }
        unCheck.setProjectInfoId(projectInfoId);
        int count = settlementUnCheckMapper.insert(unCheck);
        log.info("待审核表新增数据. count: {}", count);
    }

    /**
     * 发送给系统指定人员
     *
     * @param projectInfoId 项目id
     * @param projectName   项目名称
     * @param userId        用户id
     */
    public void sendMessageToSysPerson(long messageTypeId, String url, Long projectInfoId, String projectName,
                                       Long userId, int messageToCategory) {
        commonService.sendMessageToSysPerson(messageTypeId, url, projectInfoId, userId, messageToCategory,
                projectName + "入驻确认单" + StrUtil.EMPTY);
    }

    public void handleCloudFile(Long projectInfoId, ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO,
                                SettlementRuleCodeEnum settlementRuleCodeEnum) {
        handlUploadFile(projectInfoId, settlementCheckSaleSaveDTO.getCloudFileOriginalName(),
                settlementCheckSaleSaveDTO.getCloudFileUrl(), settlementRuleCodeEnum.getVoCode());
    }

    /**
     * 处理上传逻辑-具体实现
     *
     * @param projectInfoId 项目id
     * @param fileName      文件名
     * @param fileUrl       文件路径
     * @param busiType      业务类型
     */
    private void handlUploadFile(Long projectInfoId, String fileName, String fileUrl, int busiType) {
        // 创建项目文件关联
        ProjProjectFileExtend projectFile = createProjectFileRelation(projectInfoId, fileName, fileUrl);
        // 更新项目规则表项目文件id字段
        updateProjectRuleProjectFileId(projectFile, busiType);
    }

    /**
     * 创建项目文件关联
     *
     * @param projectInfoId 项目id
     * @param fileName      文件名
     * @param fileUrl       文件路径->obs相对路径
     */
    private ProjProjectFileExtend createProjectFileRelation(Long projectInfoId, String fileName, String fileUrl) {
        UploadFileReq uploadFileReq = new UploadFileReq();
        uploadFileReq.setProjectInfoId(projectInfoId);
        uploadFileReq.setMilestoneCode(ProjectFileTypeEnums.PROJECT_ENTRY.getMilestone());
        ProjProjectFileExtend projectFile = new ProjProjectFileExtend(uploadFileReq, fileName, SnowFlakeUtil.getId(),
                fileUrl, "");
        projProjectFileService.insert(projectFile);
        return projectFile;
    }

    /**
     * 新增关联关系
     *
     * @param projectInfoId       项目id
     * @param businessInfoId      工单id
     * @param settlementOrderInfo 工单
     */
    private void inserOrUpdateRelation(Long projectInfoId, Long businessInfoId,
                                       ProjSettlementOrderInfo settlementOrderInfo,
                                       ProjProjectOrderRelation relationDto) {
        // - 查询是否存在关联关系，存在则更新，否则新增
        ProjProjectOrderRelation orderRelation = new ProjProjectOrderRelation();
        // 查询customInfoId
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        orderRelation.setBussinessInfoId(businessInfoId);
        // - 查询需要保存的数据, 根据云资源工单id查询
        orderRelation.setDeliveryOrderId(ObjectUtil.isEmpty(settlementOrderInfo.getDeliveryOrderType()) ? -1
                : settlementOrderInfo.getDeliveryOrderType());
        orderRelation.setContractCustomInfoId(ObjectUtil.isEmpty(settlementOrderInfo.getContractCustomInfoId()) ? -1
                : settlementOrderInfo.getContractCustomInfoId());
        orderRelation.setProjectInfoId(projectInfoId);
        orderRelation.setCustomInfoId(ObjectUtil.isEmpty(projectInfo.getCustomInfoId()) ? -1
                : projectInfo.getCustomInfoId());
        orderRelation.setYyOrderId(ObjectUtil.isEmpty(settlementOrderInfo.getYyOrderId()) ? -1
                : settlementOrderInfo.getYyOrderId());
        if (ObjectUtil.isNotEmpty(relationDto)) {
            orderRelation.setProjectVsOrderId(relationDto.getProjectVsOrderId());
        }
        orderRelationService.insertOrUpdate(orderRelation);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<ProjProjectSettlementCheckSaleUploadResultVO> mobileUploadFile(@Valid @RequestBody ProjProjectSettlementCheckSaleUploadDTO checkSaleUploadDTO) {
        Result<ProjProjectSettlementCheckSaleUploadResultVO> result = real(checkSaleUploadDTO);
        // 验证文件
        if (checkSaleUploadDTO.getBusiType() == NumberEnum.NO_2.num().intValue()) {
            // 验证上传文件
            validateExcel(checkSaleUploadDTO.getProjectInfoId(),
                    result.getData().getObsRelativeUrl());
        }
        return result;
    }

    private Result<ProjProjectSettlementCheckSaleUploadResultVO> real(ProjProjectSettlementCheckSaleUploadDTO checkSaleUploadDTO) {
        // 获取文件, 没有文件名称
        String savePath =
                ProjApplyOrderServiceImpl.DEFAULT_TMP_PATH + StrUtil.SLASH + UUID.randomUUID().toString()
                        .replace("-", StrUtil.EMPTY) + StrUtil.SLASH;
        base64ToFile(checkSaleUploadDTO.getSettlementFileDTO().getContent(), checkSaleUploadDTO.getFileName(),
                savePath);
        // 保存文件。获取projectfileid
        ProjProjectSettlementCheckSaleUploadResultVO uploadResultVO = uploadTmpFileToObs(savePath,
                checkSaleUploadDTO.getProjectInfoId(), checkSaleUploadDTO.getFileName());
        return Result.success(uploadResultVO);
    }

    /**
     * 上传文件到obs
     *
     * @param savePath      存储文件夹路径
     * @param projectInfoId 项目id
     * @param fileName      文件名
     */
    public ProjProjectSettlementCheckSaleUploadResultVO uploadTmpFileToObs(String savePath, Long projectInfoId,
                                                                           String fileName) {
        String absFile = savePath + fileName;
        UploadFileReq uploadFileReq = new UploadFileReq(projectInfoId,
                ProjectFileTypeEnums.PROJECT_ENTRY.getMilestone(), null, null, false, null);
        String filePath = projProjectFileService.uploadFileOnly(uploadFileReq, new File(absFile), fileName);
        if (StrUtil.isEmpty(filePath)) {
            throw new RuntimeException("上传文件失败.");
        }
        ProjProjectSettlementCheckSaleUploadResultVO uploadResultVO =
                new ProjProjectSettlementCheckSaleUploadResultVO();
        uploadResultVO.setObsRelativeUrl(filePath);
        uploadResultVO.setObsTempUrl(OBSClientUtils.getTemporaryUrl(filePath, ObsExpireTimeConsts.SEVEN_DAY));
        uploadResultVO.setOriginalFileName(fileName);
        return uploadResultVO;
    }

    public static void base64ToFile(String base64, String fileName, String savePath) {
        if (base64.contains("data:image/jpeg;base64")) {
            base64 = base64.replace("data:image/jpeg;base64,", "");
        } else if (base64.contains("data:image/png;base64")) {
            base64 = base64.replace("data:image/png;base64,", "");
        } else if (base64.contains("base64,")) {
            base64 = base64.split(",")[1];
        }
        base64 = base64.replaceAll("\r|\n", "");
        File file = null;
        //创建文件目录
        File dir = new File(savePath);
        if (!dir.exists() && !dir.isDirectory()) {
            dir.mkdirs();
        }
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            byte[] bytes = Base64.getDecoder().decode(base64);
            file = new File(savePath + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            log.error("base64ToFile，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    log.error("BufferedOutputStream关闭，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.error("FileOutputStream关闭，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 跟新项目规则文件id
     *
     * @param fileExtend 上传的文件信息
     * @param busiType   文件所属业务类型
     */
    private void updateProjectRuleProjectFileId(ProjProjectFileExtend fileExtend, int busiType) {
        // 更新规则表中projectFileId
        ProjProjectSettlementRule settlementRule = new ProjProjectSettlementRule();
        settlementRule.setProjectFileId(fileExtend.getProjectFileId());
        QueryWrapper<ProjProjectSettlementRule> queryWrapper = new QueryWrapper<ProjProjectSettlementRule>().eq(
                "project_info_id", fileExtend.getProjectInfoId()).eq("project_rule_code",
                SettlementRuleCodeEnum.getCodeByVoCode(busiType));
        int count = projectSettlementRuleMapper.update(settlementRule, queryWrapper);
        log.info("更新规则表. count: {}", count);
        if (count <= 0) {
            throw new RuntimeException("上传文件失败, 更新规则表异常. projectInfoId: " + fileExtend.getProjectInfoId());
        }
    }

    /**
     * 获取项目文件
     *
     * @param projectFileId 主键id
     * @return 项目文件信息
     */
    public ProjProjectFile getProjectFile(Long projectFileId) {
        return projProjectFileService.selectByPrimaryKey(projectFileId);
    }

    /**
     * 获取文件临时路径
     *
     * @param projectFile 项目文件
     * @return 临时文件路径
     */
    public String getProjectFileUrl(ProjProjectFile projectFile) {
        if (ObjectUtil.isNotEmpty(projectFile)) {
            return OBSClientUtils.getTemporaryUrl(projectFile.getFilePath(), 3600);
        }
        return null;
    }

    /**
     * 根据项目文件id查询项目文件信息
     *
     * @param projectFileId 项目文件id
     * @return String
     */
    public String getProjectFileUrl(Long projectFileId) {
        ProjProjectFile projProjectFile = getProjectFile(projectFileId);
        return getProjectFileUrl(projProjectFile);
    }

    /**
     * 获取合同号
     *
     * @param projOrderInfo 工单对象
     * @return String
     */
    private ProjContractInfo getContract(ProjOrderInfo projOrderInfo) {
        if (ObjectUtil.isEmpty(projOrderInfo)) {
            throw new RuntimeException("未获取到工单信息.");
        }
        ProjContractInfo contractInfo = projContractInfoService.selectByPrimaryKey(projOrderInfo.getContractInfoId());
        if (ObjectUtil.isEmpty(contractInfo)) {
            throw new RuntimeException("未获取到合同信息.");
        }
        return contractInfo;
    }
}
