package com.msun.csm.service.proj;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjMilestoneInfoFileInitializeDTO;
import com.msun.csm.model.vo.SysFileInitializeVO;

/**
 * @version : V1.52.0
 * @ClassName: ProjMilestoneInfoFileInitializeService
 * @Description:
 * @Author: Yhongmin
 * @Date: 15:20 2024/5/16
 */
public interface ProjMilestoneInfoFileInitializeService {
    /**
     * 说明: 里程碑-准备阶段-数据导入工作
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    Result dataImportPreparationWork(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 里程碑-入驻阶段-数据导入准备工作
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    Result dataPrepare(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 准备阶段节点-表单制作节点
     *
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    Result preparatForm(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 准备阶段节点-报表制作节点
     *
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    Result preparatReport(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 全员流程模拟-全员流程模拟引导
     *
     * @param
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/17 10:04
     * @remark: Copyright
     */
    Result productTestCaseResult(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 里程碑-调研阶段-制定网络改造方案
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/29 14:01
     * @remark: Copyright
     */
    Result schemeNewwork(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 里程碑-调研阶段-制定切换方案
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/29 14:01
     * @remark: Copyright
     */
    Result schemeProject(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 说明: 里程碑-调研阶段-制定切换方案
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/29 14:01
     * @remark: Copyright
     */
    Result<SysFileInitializeVO> productOperationManual(ProjMilestoneInfoFileInitializeDTO dto);

    /**
     * 报表制作节点-区域初始化--替换医院地址信息
     *
     * @param hospitalInfoId
     * @param projectInfoId
     * @return
     */
    Result reportReplaceUrl(Long hospitalInfoId, Long projectInfoId);

    /**
     * 说明: 项目工具--报表制作--授权地址
     *
     * @param url
     * @return
     */
    Result authReportUrl(String url);

    /**
     * 根据项目id获取基础老平台基础数据地址
     * @param projectInfoId
     * @return
     */
    Result getBaseDataUrlByProjectInfoId(Long projectInfoId);
}
