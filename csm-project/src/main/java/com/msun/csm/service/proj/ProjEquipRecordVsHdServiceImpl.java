package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.constants.DictEquipTypeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.projproduct.ProjProductEnum;
import com.msun.csm.common.enums.projreview.ProjectStageCodeEnum;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictEquipAttributes;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjEquipFiles;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsHd;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.dao.entity.tmp.TmpOldEquipVsNew;
import com.msun.csm.dao.mapper.dict.DictEquipAttributesMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.oldimsp.OldCustomerInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipFilesMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsHdMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.TmpOldEquipVsNewMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.client.analymanager.LisAnalyManagerClient;
import com.msun.csm.model.dto.DeleteEquipForLisDTO;
import com.msun.csm.model.dto.EquipRecordVsHdExcelDTO;
import com.msun.csm.model.dto.EquipSendFilesToLisDTO;
import com.msun.csm.model.dto.EquipSendToLisDTO;
import com.msun.csm.model.dto.HdEquipSelectDTO;
import com.msun.csm.model.dto.MultipartFileImpl;
import com.msun.csm.model.dto.ProjEquipFilesDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsHdDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.SendCsmEquipToOldHdDTO;
import com.msun.csm.model.vo.ProjEquipFilesVO;
import com.msun.csm.model.vo.ProjEquipRecordHdResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsHdVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.dict.DictEquipCommService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.EasyExcelData;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

@Service
@Slf4j
public class ProjEquipRecordVsHdServiceImpl extends ServiceImpl<ProjEquipRecordVsHdMapper, ProjEquipRecordVsHd> implements ProjEquipRecordVsHdService {

    private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();

    private static Validator validator = validatorFactory.getValidator();

    @Resource
    SystemSettingApi systemSettingApi;

    @Resource
    private RuleProjectRuleConfigMapper ruleProjectRuleConfigMapper;

    @Resource
    private ProjEquipRecordVsHdMapper projEquipRecordVsHdMapper;
    @Resource
    private ProjEquipRecordService projEquipRecordService;
    @Resource
    private ProjEquipFilesService projEquipFilesService;
    @Resource
    private DictEquipCommService dictEquipCommService;
    @Resource
    private UserHelper userHelper;
    @Resource
    private LisAnalyManagerClient lisAnalyManagerClient;
    @Resource
    private DictEquipAttributesMapper equipAttributesMapper;
    @Resource
    private ProjCustomInfoMapper customInfoMapper;
    @Resource
    private DictProductMapper productMapper;
    @Resource
    private ProjEquipRecordMapper equipRecordMapper;
    @Resource
    private ProjEquipFilesMapper projEquipFilesMapper;
    @Resource
    private ProjProjectFileMapper projProjectFileMapper;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;

    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private OldCustomerInfoMapper oldCustomerInfoMapper;

    @Resource
    private TmpOldEquipVsNewMapper tmpOldEquipVsNewMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ImspSysUserMapper imspSysUserMapper;

    @Resource
    @Lazy
    private ProjEquipRecordVsLisService equipRecordVsLisService;

    @Resource
    private ProjEquipRecordLogService projEquipRecordLogService;

    @Resource
    private CommonService commonService;

    @Resource
    private ProjEquipRecordCommonService equipRecordCommonService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    /**
     * 根据obs链接转换成文件格式
     *
     * @param obsUrl
     * @param fileName
     * @return
     * @throws IOException
     */
    public static MultipartFile convertOBSUrlToMultipartFile(String obsUrl, String fileName) throws IOException {
        if (StringUtils.isNotEmpty(obsUrl)) {
            obsUrl = OBSClientUtils.getTemporaryUrl(obsUrl, 3600);
        }
        // 从OBS链接获取文件内容
        URL url = new URL(obsUrl);
        URLConnection connection = url.openConnection();
        try (InputStream inputStream = connection.getInputStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[16384]; // 16Kb 的缓冲区
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] fileContent = buffer.toByteArray();
            // 创建并返回自定义的MultipartFile实现
            return new MultipartFileImpl(fileName, fileName, "application/octet-stream", fileContent);
        }
    }

    /**
     * 保存血透设备信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateEquipToHd(ProjEquipRecordVsHdDTO dto) {
        // 保存设备公共表数据
        ProjEquipRecord projEquipRecord = new ProjEquipRecord();
        BeanUtil.copyProperties(dto, projEquipRecord);
        // 处理厂商、类型、型号前端传入为 名称问题。查询在字典中是否存在，当存在时说明是下拉选择的数据。 数据表中存入id 。否则说明是手输入，不存入id
        //设备类型
        projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(
                DictEquipTypeConsts.DICT_EQUIP_TYPE, dto.getEquipTypeName()));
        //设备厂商
        projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(
                DictEquipTypeConsts.DICT_EQUIP_FACTORY, dto.getEquipFactoryName()));
        //设备型号
        projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(
                DictEquipTypeConsts.DICT_EQUIP_INFO, dto.getEquipModelName()));
        //通讯方式
        projEquipRecord.setCommModeKey(dto.getCommModeObj().getId());
        projEquipRecord.setCommMode(dto.getCommModeObj().getName());
        //查询血透产品id
        if (dto.getYyProductId() == null) {
            DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                    ProjProductEnum.HD.getProductName()).last(" limit 1"));
            projEquipRecord.setYyProductId(product.getYyProductId());
        }
        //判断是否对接，如果更新为是，则将不对接原因置空
        if (dto.getRequiredFlag() == 1) {
            projEquipRecord.setStopReason("");
        }
        if (ObjectUtil.isEmpty(projEquipRecord.getEquipRecordId())) {
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            equipRecordMapper.insert(projEquipRecord);
        } else {
            equipRecordMapper.updateById(projEquipRecord);
        }
        // 保存血透设备明细数据
        ProjEquipRecordVsHd projEquipRecordVsHd = new ProjEquipRecordVsHd();
        BeanUtil.copyProperties(dto, projEquipRecordVsHd);
        projEquipRecordVsHd.setEquipRecordId(projEquipRecord.getEquipRecordId());
        if (ObjectUtil.isEmpty(projEquipRecordVsHd.getEquipRecordVsHdId())) {
            projEquipRecordVsHd.setEquipRecordVsHdId(SnowFlakeUtil.getId());
            projEquipRecordVsHdMapper.insert(projEquipRecordVsHd);
        } else {
            projEquipRecordVsHdMapper.updateById(projEquipRecordVsHd);
        }
        // 保存血透设备附件数据
        List<ProjEquipFilesDTO> equipFileList = dto.getEquipFileList();
        if (CollectionUtil.isNotEmpty(equipFileList)) {
            // 校验哪些附件信息没进行保存。 存储模板信息到设备附件表中
            List<String> fileCodeList =
                    equipFileList.stream().map(ProjEquipFilesDTO::getFileTypeCode).collect(Collectors.toList());
            List<RuleProjectRuleConfig> ruleProjectRuleConfigs =
                    ruleProjectRuleConfigMapper.selectList(new QueryWrapper<RuleProjectRuleConfig>()
                            .eq("scene_code", dto.getEquipFileList().get(0).getFileItemCode())
                            .notIn("project_rule_code", fileCodeList)
                    );
            if (CollectionUtil.isNotEmpty(ruleProjectRuleConfigs)) {
                for (RuleProjectRuleConfig ruleProjectRuleConfig : ruleProjectRuleConfigs) {
                    // 判断当前附件是否已在业务表 存在
                    List<ProjEquipFiles> projEquipFiles =
                            projEquipFilesMapper.selectList(new QueryWrapper<ProjEquipFiles>()
                                    .eq("file_type_code", ruleProjectRuleConfig.getProjectRuleCode())
                                    .eq("product_equip_record_id", projEquipRecordVsHd.getEquipRecordVsHdId())
                            );
                    if (CollectionUtil.isEmpty(projEquipFiles)) {
                        ProjEquipFilesDTO projEquipFilesDTO = new ProjEquipFilesDTO();
                        projEquipFilesDTO.setFileTypeCode(ruleProjectRuleConfig.getProjectRuleCode());
                        projEquipFilesDTO.setFileTypeName(ruleProjectRuleConfig.getProjectRuleContent());
                        projEquipFilesDTO.setFileItemCode(ruleProjectRuleConfig.getSceneCode());
                        projEquipFilesDTO.setOrderNo(ruleProjectRuleConfig.getOrderNo());
                        projEquipFilesDTO.setRequiredFlag(ruleProjectRuleConfig.getRequiredFlag());
                        equipFileList.add(projEquipFilesDTO);
                    }
                }
            }
            for (ProjEquipFilesDTO fileDTO : equipFileList) {
                ProjEquipFiles projEquipFiles = new ProjEquipFiles();
                BeanUtil.copyProperties(fileDTO, projEquipFiles);
                if (ObjectUtil.isEmpty(fileDTO.getEquipFilesId())) {
                    projEquipFiles.setProductEquipRecordId(projEquipRecordVsHd.getEquipRecordVsHdId());
                    projEquipFiles.setHospitalInfoId(dto.getHospitalInfoId());
                    projEquipFiles.setEquipFilesId(SnowFlakeUtil.getId());
                    projEquipFilesMapper.insert(projEquipFiles);
                } else {
                    projEquipFilesMapper.updateById(projEquipFiles);
                }
            }
        }
        if (ObjectUtil.isEmpty(dto.getEquipRecordVsHdId())) {
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(projEquipRecordVsHd.getEquipRecordVsHdId());
            projEquipRecordLog.setOperateName("添加设备");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
        }
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
        return Result.success();
    }

    /**
     * 查询单个血透设备数据
     *
     * @param equipRecordVsHdId
     * @return
     */
    @Override
    public Result<ProjEquipRecordVsHdVO> getEquipRecordVsHd(Long equipRecordVsHdId, Integer isMobile) {
        ProjEquipRecordVsHdDTO projEquipRecordVsHdDTO = new ProjEquipRecordVsHdDTO();
        projEquipRecordVsHdDTO.setEquipRecordVsHdId(equipRecordVsHdId);
        ProjEquipRecordVsHdVO equipRecordVsHdDetail =
                projEquipRecordVsHdMapper.getEquipRecordVsHdDetail(projEquipRecordVsHdDTO);
        BaseCodeNameResp baseCodeNameResp = new BaseCodeNameResp();
        baseCodeNameResp.setId(equipRecordVsHdDetail.getCommModeKey());
        baseCodeNameResp.setName(equipRecordVsHdDetail.getCommMode());
        equipRecordVsHdDetail.setCommModeObj(baseCodeNameResp);
        //查询附件信息
        List<ProjEquipFilesVO> fileList = projEquipFilesMapper.findEquipFiles(equipRecordVsHdId, isMobile);
        //获取附件临时访问路径
        fileList.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getFilePath())) {
                String temporaryUrlFile = OBSClientUtils.getTemporaryUrl(v.getFilePath(), 3600);
                v.setFilePath(temporaryUrlFile);
            }
        });
        equipRecordVsHdDetail.setEquipFileList(fileList);
        // 医院id为 -1 时 赋值为null
        if (equipRecordVsHdDetail.getHospitalInfoId() != null && equipRecordVsHdDetail.getHospitalInfoId() == -1) {
            equipRecordVsHdDetail.setHospitalInfoId(null);
        }
        return Result.success(equipRecordVsHdDetail);
    }

    /**
     * 删除血透设备数据
     *
     * @param equipRecordVsHdId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteEquipRecordVsHd(Long equipRecordVsHdId) {
        // 查询PACS设备数据信息（撤销申请时LIS开放平台会同步删除数据，因此这里无需再单独调用删除接口）
        ProjEquipRecordVsHd projEquipRecordVsHd = this.getById(equipRecordVsHdId);
        // 删除PACS设备明细数据
        this.removeById(equipRecordVsHdId);
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectById(projEquipRecordVsHd.getEquipRecordId());
        // 删除PACS设备公共表数据
        projEquipRecordService.removeById(projEquipRecordVsHd.getEquipRecordId());
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
        // 删除PACS设备附件数据
        projEquipFilesService.remove(new QueryWrapper<ProjEquipFiles>()
                .eq("product_equip_record_id", equipRecordVsHdId)
        );
        return Result.success();
    }

    /**
     * 查询血透设备列表数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjEquipRecordHdResultVO> selectHdEquipData(HdEquipSelectDTO dto) {
        ProjEquipRecordHdResultVO projEquipRecordHdResultVO = new ProjEquipRecordHdResultVO();
        //查询产品id
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                ProjProductEnum.HD.getProductName()).last(" limit 1"));
        projEquipRecordHdResultVO.setProductId(product.getYyProductId());
        //查询设备记录
        List<ProjEquipRecordVsHdVO> recordVsHdList = projEquipRecordVsHdMapper.selectHdEquipData(dto);
        for (ProjEquipRecordVsHdVO vo : recordVsHdList) {
            // 组装前端展示的设备名称
            vo.setEquipModelNameStr(vo.getEquipFactoryName() + "/" + vo.getEquipTypeName() + "/" + vo.getEquipModelName());
            // 设置是否必须对接
            vo.setRequiredFlagBool(vo.getRequiredFlag() == 1);
            //回显通讯模式对象
            BaseCodeNameResp baseIdNameResp = new BaseCodeNameResp();
            baseIdNameResp.setId(vo.getCommModeKey());
            baseIdNameResp.setName(vo.getCommMode());
            vo.setCommModeObj(baseIdNameResp);
        }
        projEquipRecordHdResultVO.setRecordList(recordVsHdList);
        return Result.success(projEquipRecordHdResultVO);
    }

    /**
     * 设备信息发送到LIS解析平台
     *
     * @param equipRecordVsHdIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result equipSendToLis(List<Long> equipRecordVsHdIds) {
        try {
            for (Long id : equipRecordVsHdIds) {
                HdEquipSelectDTO dto = new HdEquipSelectDTO();
                dto.setEquipRecordVsHdId(id);
                // 查询LIS设备信息
                ProjEquipRecordVsHdVO hdEquipVo = projEquipRecordVsHdMapper.selectHdEquipData(dto).get(0);
                if (ObjectUtil.isEmpty(hdEquipVo.getHospitalInfoId())) {
                    return Result.fail("未查询到医院信息，请先修改来源医院！");
                }
                // 附件信息查询
                List<ProjEquipFiles> list = projEquipFilesService.list(new QueryWrapper<ProjEquipFiles>()
                        .eq("product_equip_record_id", id)
                );
                // 把血透设备信息发送到LIS解析平台
                EquipSendToLisDTO equipSendToLis = new EquipSendToLisDTO();
                // 组装设备信息
                infoDataForLis(hdEquipVo, equipSendToLis);
                JSONObject entries = lisAnalyManagerClient.insertEquip(equipSendToLis.getId(), equipSendToLis);
                log.info("设备发送结果: {}", entries);
                if (!(Boolean) entries.get("success")) {
                    throw new RuntimeException("设备发送失败 , " + entries.get("message"));
                }
                // 设置附件参数。。只能进行单个发送
                for (ProjEquipFiles file : list) {
                    //跳过没有选择附件的对象
                    if (file.getProjectFileId() == 0) {
                        continue;
                    }
                    EquipSendFilesToLisDTO d = new EquipSendFilesToLisDTO();
                    d.setId(file.getProductEquipRecordId());
                    // 获取obs上的文件，转换成文件流
                    ProjProjectFile projProjectFile = projProjectFileMapper.selectById(file.getProjectFileId());
                    MultipartFile multipartFile = convertOBSUrlToMultipartFile(projProjectFile.getFilePath(),
                            projProjectFile.getFileName());
                    switch (file.getFileTypeCode()) {
                        case "tongxun_text":
                            //通讯协议文档
                            d.setFile1(multipartFile);
                            break;
                        case "yuanshichuan":
                            // 原始串
                            d.setFile2(multipartFile);
                            break;
                        case "report_pic":
                            //报告图片
                            d.setFile3(multipartFile);
                            break;
                        case "other_pic":
                            // 其他图片
                            d.setFile4(multipartFile);
                            break;
                        case "instrument_appear":
                            // 设备全貌图片
                            d.setFile5(multipartFile);
                            break;
                        case "instrument_config":
                            // 仪器配置文档
                            d.setFile6(multipartFile);
                            break;
                        case "yangben_result":
                            // 样本结果
                            d.setFile7(multipartFile);
                            break;
                        case "putong_yuanshichuan":
                            // 普通结果原始串
                            d.setFile21(multipartFile);
                            break;
                        case "jizhen_result":
                            // .急诊结果原始串
                            d.setFile22(multipartFile);
                            break;
                        case "zhikong_result":
                            // .质控结果原始串
                            d.setFile23(multipartFile);
                            break;
                        case "duplex_tongxun":
                            // .双工通信日志
                            d.setFile24(multipartFile);
                            break;
                        case "duplex":
                            // .双工请求
                            d.setFile25(multipartFile);
                            break;
                        default:
                            // 普通结果
                            break;
                    }
                    log.info("LIS开放平台上传附件信息 参数列表 =====, {}", JSONUtil.toJsonStr(d));
                    lisAnalyManagerClient.uploadFiles(d.getId(),
                            null,
                            d.getFile1(),
                            d.getFile2(),
                            d.getFile21(),
                            d.getFile22(),
                            d.getFile23(),
                            d.getFile24(),
                            d.getFile25(),
                            d.getFile3(),
                            d.getFile4(),
                            d.getFile5(),
                            d.getFile6(),
                            d.getFile7(),
                            d.getFile13()
                    );
                    log.info("LIS开放平台上传附件信息 结束");
                }
                ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                projEquipRecord.setEquipRecordId(hdEquipVo.getEquipRecordId());
                projEquipRecord.setEquipStatus(1);
                projEquipRecord.setApplyTime(new Date());
                equipRecordMapper.updateById(projEquipRecord);

                equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
                //保存操作日志
                ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
                projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
                projEquipRecordLog.setEquipRecordBusinessId(id);
                projEquipRecordLog.setOperateName("申请设备");
                projEquipRecordLogService.saveLog(projEquipRecordLog);
            }
            return Result.success("设备发送成功");
        } catch (Exception e) {
            log.info("设备发送失败, {}", e);
            return Result.fail("设备发送失败 , " + e.getMessage());
        }
    }

    /**
     * 组装LIS开发平台数据
     *
     * @param hdEquipVo
     * @param equipSendToLis
     */
    private void infoDataForLis(ProjEquipRecordVsHdVO hdEquipVo, EquipSendToLisDTO equipSendToLis) {
        equipSendToLis.setId(hdEquipVo.getEquipRecordVsHdId());
        equipSendToLis.setEquipClassId(hdEquipVo.getEquipTypeId());
        equipSendToLis.setEquipFactoryId(hdEquipVo.getEquipFactoryId());
        equipSendToLis.setEquipNameId(hdEquipVo.getEquipInfoId());
        equipSendToLis.setHisCreaterId(Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()));
        equipSendToLis.setHisUpdaterId(Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()));
        equipSendToLis.setLinkman(null);
        equipSendToLis.setTelephone(hdEquipVo.getEquipFactoryPhone());
        equipSendToLis.setCustomerId(hdEquipVo.getCustomInfoId());
        equipSendToLis.setStatus(1);
        equipSendToLis.setProductId(hdEquipVo.getYyProductId());
        equipSendToLis.setUnreviewedEquipFactoryName(ObjectUtil.isEmpty(hdEquipVo.getEquipFactoryId())
                ? hdEquipVo.getEquipFactoryName() : null);
        equipSendToLis.setUnreviewedEquipClassName(ObjectUtil.isEmpty(hdEquipVo.getEquipTypeId())
                ? hdEquipVo.getEquipTypeName() : null);
        equipSendToLis.setUnreviewedEquipName(ObjectUtil.isEmpty(hdEquipVo.getEquipInfoId())
                ? hdEquipVo.getEquipModelName() : null);
        // 查询通讯方式
        DictEquipAttributes dictEquipAttributes =
                equipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>().eq("equip_attributes_value",
                        hdEquipVo.getCommMode()));
        equipSendToLis.setDataPipe(dictEquipAttributes.getEquipAttributesKey());
        equipSendToLis.setCommModelName(dictEquipAttributes.getEquipAttributesValue());
        //查询客户名称
        ProjCustomInfo customInfo = customInfoMapper.selectById(hdEquipVo.getCustomInfoId());
        equipSendToLis.setCustomerName(customInfo.getCustomName());
        equipSendToLis.setDataSource(3);
        equipSendToLis.setLocation(hdEquipVo.getEquipPosition());
        equipSendToLis.setContent(hdEquipVo.getMemo());
        // 查询产品名称
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("yy_product_id",
                hdEquipVo.getYyProductId()));
        equipSendToLis.setProductName(product.getProductName());
    }

    /**
     * 撤销血透设备接口申请
     *
     * @param equipRecordVsHdId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result equipRevokeToHd(Long equipRecordVsHdId) {
        try {
            // 查询该接口数据
            ProjEquipRecordVsHd projEquipRecordVsHd = projEquipRecordVsHdMapper.selectById(equipRecordVsHdId);

            // 撤销LIS解析平台的接口申请
            DeleteEquipForLisDTO deleteEquipForLisDTO = new DeleteEquipForLisDTO();
            deleteEquipForLisDTO.setId(equipRecordVsHdId);
            deleteEquipForLisDTO.setOperatorId(Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()));
            JSONObject entries = lisAnalyManagerClient.cancelEquipApply(deleteEquipForLisDTO.getId(),
                    deleteEquipForLisDTO.getOperatorId());
            log.info("Lis开发平台撤销设备结果: {}", entries);
            if (!(Boolean) entries.get("success")) {
                throw new RuntimeException("Lis开发平台撤销设备失败 , " + entries.get("message"));
            }
            // 更新公共表该设备的状态为 0
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipRecordId(projEquipRecordVsHd.getEquipRecordId());
            projEquipRecord.setEquipStatus(0);
            equipRecordMapper.updateById(projEquipRecord);
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(equipRecordVsHdId);
            projEquipRecordLog.setOperateName("撤销申请");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
            return Result.success();
        } catch (Exception e) {
            log.info("撤销失败 , {}", e);
            return Result.fail("撤销失败 , " + e.getMessage());
        }
    }

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    @Override
    public Result commitFinish(ProjEquipVsProductFinishDTO dto) {
        //校验是否可以提交完成
        // 判断节点类型【调研阶段、准备阶段】
        Result<ProjMilestoneInfo> milestoneInfoResult = milestoneInfoService.selectById(dto.getMilestoneInfoId());
        String stageCode = milestoneInfoResult.getData().getProjectStageCode();
        Long projectInfoId1 = milestoneInfoResult.getData().getProjectInfoId();
        String nodeCode = milestoneInfoResult.getData().getMilestoneNodeCode();
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(stageCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(0);
            statusList.add(2);
            Integer count = projEquipRecordVsHdMapper.getNotApplyRecordCount(statusList, projectInfoId1);
            if (count > 0) {
                return Result.fail("设置对接的设备全部提交申请后，才可提交完成！");
            }
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(stageCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(0);
            statusList.add(1);
            statusList.add(2);
            statusList.add(3);
            statusList.add(4);
            statusList.add(6);
            Integer count = projEquipRecordVsHdMapper.getNotApplyRecordCount(statusList, projectInfoId1);
            if (count > 0) {
                return Result.fail("存在需要对接的设备未测试通过,请全部测试通过后再进此操作");
            }
        }
        //更新任务计划明细状态
        List<ProjMilestoneTask> projMilestoneTasks =
                milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                        .eq("project_info_id", milestoneInfoResult.getData().getProjectInfoId())
                        .eq("milestone_node_code", nodeCode)
                );
        if (CollectionUtil.isEmpty(projMilestoneTasks)) {
            return Result.fail("请先分配产品业务调研后再进行提交完成");
        }
        //查询血透产品id
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                ProjProductEnum.HD.getProductName()).last(" limit 1"));
        List<Long> collect =
                projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
        List<ProjMilestoneTaskDetail> projMilestoneTaskDetails =
                milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("product_deliver_id", product.getYyProductId())
                        .in("milestone_task_id", collect)
                );
        if (CollectionUtil.isEmpty(projMilestoneTaskDetails)) {
            return Result.fail("血透产品未查询到对应任务");
        }
        for (ProjMilestoneTaskDetail taskDetail : projMilestoneTaskDetails) {
            taskDetail.setCompleteStatus(1);
            milestoneTaskDetailMapper.updateById(taskDetail);
        }
        projTodoTaskService.updateTodoTaskStatusOne(projectInfoId1, DictProjectPlanItemEnum.SURVEY_DEVICE, product.getYyProductId(), ProjectPlanStatusEnum.FINISHED);
        return Result.success();
    }

    /**
     * 发送到云健康
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result sendToMsunVsHd(ProjEquipSendToCloudDTO dto) {
        // 查询当前客户下的主院数据
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>()
                .eq("custom_info_id", dto.getCustomInfoId())
                .eq("health_bureau_flag", 1)
        );
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            return Result.fail("请检查客户下的医院信息");
        }
        if (ObjectUtil.isEmpty(hospitalInfoList.get(0).getCloudHospitalId())) {
            return Result.fail("该项目未部署,禁止对照");
        }
        ProjHospitalInfo hospitalInfo = hospitalInfoList.get(0);
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        List<Long> equipRecordVsHdIds = dto.getEquipRecordVsProductIds();
        //查询发送到云健康设备列列表
        dto.setSendCloudFlag(0);
        List<ProductEquipmentDto> equipments = projEquipRecordVsHdMapper.findToMsunEquipRecord(dto);
        for (ProductEquipmentDto d : equipments) {
            d.setHospitalId(hospitalInfo.getCloudHospitalId());
            d.setHisOrgId(hospitalInfo.getOrgId());
        }
        SystemConfigDto sysDto = new SystemConfigDto();
        //设置hospitalId
        sysDto.setHospitalId(hospitalInfo.getCloudHospitalId());
        sysDto.setHisOrgId(hospitalInfo.getOrgId());
        sysDto.setData(equipments);
        //数据组装   API数据发送
        ResponseResult responseResult = systemSettingApi.syncEquipment(sysDto);
        if (responseResult.isSuccess()) {
            //更新设备的发送云健康状态
            projEquipRecordVsHdMapper.updateSetdToMusnStatus(equipRecordVsHdIds);
        } else {
            return Result.fail("发送到云健康失败，失败原因：" + responseResult.getMessage());
        }
        for (Long id : equipRecordVsHdIds) {
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(id);
            projEquipRecordLog.setOperateName("发送到云健康");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
        }
        return Result.success();
    }

    /**
     * 血透设备一键检测
     *
     * @param dto
     * @return
     */
    @Override
    public Result checkSendToMsunVsHd(ProjEquipSendToCloudDTO dto) {
        ProjProjectInfo projectInfo = commonService.getProjectInfo(dto.getProjectInfoId());
        dto.setProjectType(projectInfo.getProjectType());
        //查询需要对接但未对照云健康设备的数量
        Integer noMappingCount = projEquipRecordVsHdMapper.getNoMappingCount(dto);
        if (noMappingCount > 0) {
            return Result.fail("存在需要对接但未发送到云健康设备的记录，请先发送到云健康！");
        }
        //查询设备记录
        dto.setSendCloudFlag(1);

        //查询血透设备记录，不包含已检测通过的记录
        List<ProductEquipmentDto> equipmentDataDto = projEquipRecordVsHdMapper.findToMsunEquipRecord(dto);
        if (CollectionUtil.isEmpty(equipmentDataDto)) {
            return Result.success();
        }

        // 有云健康设备id的设备（发送对照或从云健康手动对照）的正常走流程，没有云健康设备id的进行提示进行对照
        List<ProductEquipmentDto> equipmentData = equipmentDataDto.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOldEquipId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(equipmentData)) {
            // 查询项目类型, 对照环境
            ProjHospitalInfo hospitalInfo = equipRecordCommonService.getHospitalInfo(dto.getCustomInfoId(),
                    projectInfo.getProjectType());
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                return Result.fail("请检查客户下的医院信息");
            }
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            String productCode = "HD";
            try {
                // 调用云健康接口一键检测
                Map<Long, EquipmentStatusWrapperResult> mapData =
                        equipRecordCommonService.getEquipmentStatusBatch(productCode, hospitalInfo,
                                equipmentData);
                if (MapUtil.isEmpty(mapData)) {
                    return Result.fail("未查询到设备检测状态.");
                }
                List<ProjHospitalInfo> hospitalInfoList = equipRecordCommonService.findHospitalInfos(dto.getCustomInfoId(),
                        projectInfo.getProjectType());
                // 更新设备状态
                equipRecordCommonService.updateEquipStatus(mapData, hospitalInfoList, dto.getProjectInfoId(),
                        productCode, equipmentData);
            } catch (Exception e) {
                log.error("更新血透设备检测状态, 异常. message: {}, e=", e.getMessage(), e);
                return Result.fail(e.getMessage());
            }
            equipRecordCommonService.updateEquipProjectTodoTaskStatus(projectInfo.getProjectInfoId(), null, ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
        }

        // 未对照过云健康设备的进行提示
        List<ProductEquipmentDto> unSendCloudEquipData = equipmentDataDto.stream().filter(e -> ObjectUtil.isEmpty(e.getOldEquipId())).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (ProductEquipmentDto item : unSendCloudEquipData) {
            if (ObjectUtil.isEmpty(item.getOldEquipId())) {
                sb.append(item.getModel() + "未对照云健康设备 \n");
            }
        }
        if (sb.length() > 0) {
//            return Result.fail(sb.toString());
            return Result.fail(1002, "存在未对照的设备，请进行云健康设备对照后进行检测");
        }

        return Result.success();
    }

    @Override
    public void downloadTemplateVsHD(HttpServletResponse response, Long projectInfoId) {
        //查询客户名称
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("血透设备导入模版");
        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        // 创建带星号(*)的标题行字体颜色为红色
        CellStyle redFontStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setBold(true);
        redFontStyle.setFont(redFont);
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        String[] headers = {"*设备型号", "*设备类型", "*设备厂商", "设备区域", "*设备编号", "厂商电话", "*通讯方式", "*来源医院", "*是否对接", "备注说明"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (headers[i].startsWith("*")) {
                cell.setCellStyle(redFontStyle);
            } else {
                cell.setCellStyle(headerStyle);
            }
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 创建是否的下拉框区域，从第2-499行，第6列【通讯方式】
        List<BaseCodeNameResp> attributes =
                projEquipRecordMapper.selectSurveyEquipAttributes(DictEquipTypeConsts.DICT_EQUIP_COMM_MODE);
        if (CollectionUtil.isNotEmpty(attributes)) {
            String[] choices2 = new String[attributes.size()];
            for (int i = 0; i < attributes.size(); i++) {
                choices2[i] = attributes.get(i).getName();
            }
            CellRangeAddressList addressList2 = new CellRangeAddressList(1, 500, 6, 6);
            DataValidation dataValidation2 = helper.createValidation(helper.createExplicitListConstraint(choices2),
                    addressList2);
            dataValidation2.setSuppressDropDownArrow(true);
            dataValidation2.createErrorBox("错误", "请从列表中选择一个选项。");
            dataValidation2.setShowErrorBox(true);
            sheet.addValidationData(dataValidation2);
        }
        // 创建是否的下拉框区域，从第2-499行，第6列【来源医院】
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtil.isNotEmpty(hospitalInfoList)) {
            String[] hospitalInfoListChoices = new String[hospitalInfoList.size()];
            for (int i = 0; i < hospitalInfoList.size(); i++) {
                hospitalInfoListChoices[i] = hospitalInfoList.get(i).getHospitalName();
            }
            CellRangeAddressList hospitalInfoListAddress = new CellRangeAddressList(1, 500, 7, 7);
            DataValidation hospitalInfoDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(hospitalInfoListChoices),
                            hospitalInfoListAddress);
            hospitalInfoDataValidation.setSuppressDropDownArrow(true);
            hospitalInfoDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            hospitalInfoDataValidation.setShowErrorBox(true);
            sheet.addValidationData(hospitalInfoDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第8列【是否对接】
        String[] choices1 = new String[]{"是", "否"};
        CellRangeAddressList addressList1 = new CellRangeAddressList(1, 500, 8, 8);
        DataValidation dataValidation1 = helper.createValidation(helper.createExplicitListConstraint(choices1),
                addressList1);
        dataValidation1.setSuppressDropDownArrow(true);
        dataValidation1.createErrorBox("错误", "请从列表中选择一个选项。");
        dataValidation1.setShowErrorBox(true);
        sheet.addValidationData(dataValidation1);
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(customInfo.getCustomName() + "-血透设备导入模版.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 血透导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Override
    public Result importExcelDataVsHD(MultipartFile multipartFile, Long customInfoId, Long projectInfoId) {
        // 确保上传的是Excel文件
        if (!multipartFile.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("导入失败,请选择.xlsx格式的Excel文件");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(multipartFile.getInputStream(), EquipRecordVsHdExcelDTO.class);
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("导入失败,请检查导入文件是否存在数据");
                }
            }
            //导入Excel数据
            Result result = saveDatas(easyExcelData.getDatas(), customInfoId, projectInfoId);
            return result;
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败，失败信息：" + e.getMessage());
        }
    }

    /**
     * 血透设备导出数据
     *
     * @param dto
     * @param response
     */
    @Override
    public void exportExcelDataVsHD(HdEquipSelectDTO dto, HttpServletResponse response) {
        // 查询项目信息
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
        List<ProjEquipRecordVsHdVO> hdVOList = projEquipRecordVsHdMapper.selectHdEquipData(dto);
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("血透设备记录");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        headerRow.createCell(0).setCellValue("设备型号");
        headerRow.createCell(1).setCellValue("设备类型");
        headerRow.createCell(2).setCellValue("设备厂商");
        headerRow.createCell(3).setCellValue("设备区域");
        headerRow.createCell(4).setCellValue("设备编号");
        headerRow.createCell(5).setCellValue("厂商电话");
        headerRow.createCell(6).setCellValue("通讯方式");
        headerRow.createCell(7).setCellValue("来源医院");
        headerRow.createCell(8).setCellValue("是否对接");
        headerRow.createCell(9).setCellValue("备注说明");
        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        for (Cell headerCell : headerRow) {
            headerCell.setCellStyle(headerStyle);
        }
        //填充数据
        for (int i = 0; i < hdVOList.size(); i++) {
            ProjEquipRecordVsHdVO hdVO = hdVOList.get(i);
            Row rowData = sheet.createRow(i + 1);
            rowData.createCell(0).setCellValue(hdVO.getEquipModelName());
            rowData.createCell(1).setCellValue(hdVO.getEquipTypeName());
            rowData.createCell(2).setCellValue(hdVO.getEquipFactoryName());
            rowData.createCell(3).setCellValue(hdVO.getEquipPosition());
            rowData.createCell(4).setCellValue(hdVO.getEquipNum());
            rowData.createCell(5).setCellValue(hdVO.getEquipFactoryPhone());
            rowData.createCell(6).setCellValue(hdVO.getCommMode());
            rowData.createCell(7).setCellValue(hdVO.getHospitalInfoName());
            rowData.createCell(8).setCellValue(hdVO.getRequiredFlag() == 1 ? "是" : "否");
            rowData.createCell(9).setCellValue(hdVO.getMemo());
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(projProjectInfo.getProjectName() + "_血透设备记录.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 老系统血透设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    @Override
    public Result sendEquipToOldHD(Long projectInfoId, Long oldEquipId) {
        // 查询老系统Lis设备数据
        List<SendCsmEquipToOldHdDTO> sendCsmEquipToOldHdDTOList =
                projEquipRecordVsHdMapper.selectOldEquipDataToHd(projectInfoId,
                        oldEquipId);
        if (CollectionUtil.isNotEmpty(sendCsmEquipToOldHdDTOList)) {
            for (SendCsmEquipToOldHdDTO dto : sendCsmEquipToOldHdDTOList) {
                TmpOldEquipVsNew tmpOldEquipVsNew = new TmpOldEquipVsNew();
                tmpOldEquipVsNew.setId(SnowFlakeUtil.getId());
                tmpOldEquipVsNew.setOldEquipId(dto.getId());
                tmpOldEquipVsNew.setNewEquipId(dto.getId());
                tmpOldEquipVsNew.setProductName("Hd");
                // 获取当前数据的创建人信息 csm系统人员
                ImspSysUser imspSysUser = imspSysUserMapper.selectById(dto.getCreateId());
                Long userId = null;
                if (ObjectUtil.isNotEmpty(imspSysUser)) {
                    SysUser user = sysUserMapper.selectUserIdByYungyingId(imspSysUser.getUserYunyingId().toString());
                    userId = ObjectUtil.isNotEmpty(user) && ObjectUtil.isNotEmpty(user.getSysUserId())
                            ? user.getSysUserId() : null;
                }
                try {
                    // 封装csm的Lis设备数据
                    ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                    projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
                    projEquipRecord.setCustomInfoId(dto.getCustomInfoId());
                    if (ObjectUtil.isEmpty(dto.getNewProjectInfoId())) {
                        // 查询对应客户下 存在lis产品的项目id 【默认放在存在lis产品下的项目中】
                        List<ProjProjectInfo> projectInfoList =
                                projProjectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>()
                                        .eq("his_flag", 1)
                                        .eq("custom_info_id", dto.getCustomInfoId())
                                );
                        projEquipRecord.setProjectInfoId(projectInfoList.get(0).getProjectInfoId());
                        dto.setNewProjectInfoId(projectInfoList.get(0).getProjectInfoId());
                    } else {
                        projEquipRecord.setProjectInfoId(dto.getNewProjectInfoId());
                    }
                    tmpOldEquipVsNew.setNewProjectInfoId(projEquipRecord.getProjectInfoId());
                    // 转换新系统医院id
                    OldCustomerInfo oldCustomerInfo = oldCustomerInfoMapper.selectById(dto.getHospitalInfoId());
                    projEquipRecord.setHospitalInfoId(ObjectUtil.isNotEmpty(oldCustomerInfo)
                            && ObjectUtil.isNotEmpty(oldCustomerInfo.getCsmHospitalInfoId())
                            ? oldCustomerInfo.getCsmHospitalInfoId() : -1);
                    projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
                    projEquipRecord.setEquipFactoryId(ObjectUtil.isNotEmpty(dto.getDictEquipFactoryId())
                            ? dto.getDictEquipFactoryId() : null);
                    projEquipRecord.setEquipFactoryName(ObjectUtil.isNotEmpty(dto.getDictEquipFactoryName())
                            ? dto.getDictEquipFactoryName() : dto.getEquipFactoryName());
                    projEquipRecord.setEquipTypeId(ObjectUtil.isNotEmpty(dto.getDictEquipTypeId())
                            ? dto.getDictEquipTypeId() : null);
                    projEquipRecord.setEquipTypeName(ObjectUtil.isNotEmpty(dto.getDictEquipTypeName())
                            ? dto.getDictEquipTypeName() : dto.getEquipTypeName());
                    projEquipRecord.setEquipInfoId(ObjectUtil.isNotEmpty(dto.getDictEquipModelId())
                            ? dto.getDictEquipModelId() : null);
                    projEquipRecord.setEquipModelName(ObjectUtil.isNotEmpty(dto.getDictEquipModelName())
                            ? dto.getDictEquipModelName() : dto.getEquipModelName());
                    projEquipRecord.setRequiredFlag(dto.getRequiredFlag() == 1 ? 1 : 0);
                    projEquipRecord.setStopReason(dto.getStopReason());
                    // 设备状态 【新老系统设备状态统一， 不需要额外处理】
                    projEquipRecord.setEquipStatus(dto.getEquipStatus());
                    projEquipRecord.setEquipPosition(dto.getEquipPosition());
                    projEquipRecord.setEquipFactoryPhone(dto.getEquipFactoryPhone());
                    projEquipRecord.setMemo(dto.getMemo());
                    projEquipRecord.setApplyTime(dto.getApplyTime());
                    projEquipRecord.setCloudEquipId(dto.getCloudEquipId());
                    projEquipRecord.setCloudEquipName(dto.getCloudEquipName());
                    // 查询通讯方式的key
                    DictEquipAttributes dictEquipAttributes =
                            equipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                                    .eq("equip_attributes_code", DictEquipTypeConsts.DICT_EQUIP_COMM_MODE)
                                    .eq("equip_attributes_key", dto.getCommMode())
                                    .last(" limit 1"));
                    projEquipRecord.setCommModeKey(ObjectUtil.isNotEmpty(dictEquipAttributes)
                            ? dictEquipAttributes.getEquipAttributesKey() : "-1");
                    projEquipRecord.setCommMode(ObjectUtil.isNotEmpty(dictEquipAttributes)
                            ? dictEquipAttributes.getEquipAttributesValue() : "-1");
                    projEquipRecord.setCheckResult(dto.getCheckResult());
                    projEquipRecord.setCreaterId(userId);
                    projEquipRecord.setUpdaterId(userId);
                    // Lis设备纪录信息保存
                    ProjEquipRecordVsHd projEquipRecordVsHd = new ProjEquipRecordVsHd();
                    projEquipRecordVsHd.setEquipRecordVsHdId(dto.getId());
                    projEquipRecordVsHd.setEquipRecordId(projEquipRecord.getEquipRecordId());
                    projEquipRecordVsHd.setEquipNum(ObjectUtil.isNotEmpty(dto.getEquipNum()) ? dto.getEquipNum()
                            : "暂无");
                    projEquipRecordVsHd.setSendCloudFlag(ObjectUtil.isNotEmpty(dto.getIsSend()) && Convert.toInt(dto.getIsSend()) == 1 ? 1 : 0);
                    projEquipRecordVsHd.setCreaterId(userId);
                    projEquipRecordVsHd.setUpdaterId(userId);
                    // 判断是否已经存在当前数据
                    ProjEquipRecordVsHd projEquipRecordVsHd1 =
                            projEquipRecordVsHdMapper.selectById(projEquipRecordVsHd.getEquipRecordVsHdId());
                    if (ObjectUtil.isEmpty(projEquipRecordVsHd1)) {
                        projEquipRecordMapper.insert(projEquipRecord);
                        projEquipRecordVsHdMapper.insert(projEquipRecordVsHd);
                        // tmp记录表 记录数据
                        tmpOldEquipVsNew.setFlag(1);
                        tmpOldEquipVsNewMapper.insert(tmpOldEquipVsNew);
                    }
                    ProjSurveyPlan projSurveyPlan = new ProjSurveyPlan();
                    projSurveyPlan.setCustomInfoId(dto.getCustomInfoId());
                    projSurveyPlan.setProjectInfoId(dto.getNewProjectInfoId());
                    // 赋值当前项目的主院id
                    SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
                    selectHospitalDTO.setCustomInfoId(dto.getCustomInfoId());
                    selectHospitalDTO.setProjectInfoId(dto.getNewProjectInfoId());
                    selectHospitalDTO.setHealthBureauFlag(1);
                    List<ProjHospitalInfo> hospitalInfoList =
                            hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
                    if (CollectionUtil.isNotEmpty(hospitalInfoList) && hospitalInfoList.size() > 0) {
                        projSurveyPlan.setHospitalInfoId(hospitalInfoList.get(0).getHospitalInfoId());
                        projSurveyPlan.setYyProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
                        // 生成调研阶段里程碑任务
                        equipRecordVsLisService.addEquipSurveyTask(userId, projSurveyPlan,
                                MilestoneNodeEnum.SURVEY_DEVICE.getCode());
                        // 生成准备阶段里程碑任务
                        equipRecordVsLisService.addEquipSurveyTask(userId, projSurveyPlan,
                                MilestoneNodeEnum.PREPARAT_DEVICE.getCode());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("设备迁移失败 , 迁移失败数据 ， {}", JSONUtil.toJsonStr(dto));
                    tmpOldEquipVsNew.setFlag(0);
                    tmpOldEquipVsNew.setMessage(e.toString());
                    tmpOldEquipVsNewMapper.insert(tmpOldEquipVsNew);
                }
            }
        }
        return Result.success();
    }

    /**
     * 保存血透模版导入数据
     *
     * @param dtoList
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result saveDatas(List<Object> dtoList, Long customInfoId, Long projectInfoId) {
        for (Object dto : dtoList) {
            EquipRecordVsHdExcelDTO excelDTO = (EquipRecordVsHdExcelDTO) dto;
            //检验数据中必填选项
            Set<ConstraintViolation<EquipRecordVsHdExcelDTO>> violations = validator.validate(excelDTO);
            if (!violations.isEmpty()) {
                return Result.fail("导入失败，Excel中存在必填的选项为空，请检查标题前带*的内容是否填写完整！");
            }
            //封装设备记录实体
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            BeanUtil.copyProperties(excelDTO, projEquipRecord);
            //查询医院id
            SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
            selectHospitalDTO.setProjectInfoId(projectInfoId);
            selectHospitalDTO.setHospitalName(excelDTO.getHospitalInfoName());
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
            if (CollectionUtil.isEmpty(hospitalInfoList)) {
                return Result.fail("导入失败,《" + excelDTO.getHospitalInfoName() + "》在系统中未匹配到对应医院，请核对数据！");
            }
            projEquipRecord.setHospitalInfoId(hospitalInfoList.get(0).getHospitalInfoId());
            //检查模态
            DictEquipAttributes dictEquipAttributes =
                    equipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                            .eq("equip_attributes_code", DictEquipTypeConsts.DICT_EQUIP_COMM_MODE)
                            .eq("equip_attributes_value", excelDTO.getCommMode())
                            .last(" limit 1"));
            if (ObjectUtil.isEmpty(dictEquipAttributes)) {
                return Result.fail("导入失败,《" + excelDTO.getCommMode() + "》在系统中未匹配到对应通讯方式，请核对数据！");
            }
            projEquipRecord.setCommMode(excelDTO.getCommMode());
            projEquipRecord.setCommModeKey(dictEquipAttributes.getEquipAttributesKey());
            //设备厂商
            projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY,
                    projEquipRecord.getEquipFactoryName()));
            //设备型号
            projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO,
                    projEquipRecord.getEquipModelName()));
            // 设备类型
            projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_TYPE,
                    projEquipRecord.getEquipTypeName()));
            //产品ID
            DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                    ProjProductEnum.HD.getProductName()).last(" limit 1"));
            projEquipRecord.setYyProductId(product.getYyProductId());
            //是否对接
            projEquipRecord.setRequiredFlag("是".equals(excelDTO.getRequiredBoolean()) ? 1 : 0);
            projEquipRecord.setEquipPosition(excelDTO.getEquipPosition());
            //保存设备记录信息
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecord.setCustomInfoId(customInfoId);
            projEquipRecord.setProjectInfoId(projectInfoId);
            projEquipRecordMapper.insert(projEquipRecord);
            //保存Pacs设备记录
            ProjEquipRecordVsHd projEquipRecordVsPacs = new ProjEquipRecordVsHd();
            BeanUtil.copyProperties(excelDTO, projEquipRecordVsPacs);
            projEquipRecordVsPacs.setEquipNum(ObjectUtil.isNotEmpty(excelDTO.getEquipNum()) ? excelDTO.getEquipNum()
                    : "暂无");
            projEquipRecordVsPacs.setEquipRecordVsHdId(SnowFlakeUtil.getId());
            projEquipRecordVsPacs.setEquipRecordId(projEquipRecord.getEquipRecordId());
            projEquipRecordVsHdMapper.insert(projEquipRecordVsPacs);
        }
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        return Result.success();
    }
}
