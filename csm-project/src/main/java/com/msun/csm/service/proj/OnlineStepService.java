package com.msun.csm.service.proj;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.model.dto.HospitalOnlinePublishedSystemDTO;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.ProjOnlineStepDetailDTO;
import com.msun.csm.model.req.project.ProjectOnlineBeforeReq;
import com.msun.csm.model.vo.ProjOnlineStepData;
import com.msun.csm.model.vo.ProjOnlineStepSuccessVO;
import com.msun.csm.model.vo.ProjectOnlineToHospitalVO;
import com.msun.csm.model.vo.user.SysUserVO;

/**
 * 确认上线Service
 *
 * @Author: duxu
 * @Date: 2024/05/22/10:23
 */

public interface OnlineStepService {

    /**
     * 进入页面时，从配置表中获取上线步骤，存入项目步骤表中
     *
     * @param dto
     * @return
     */
    Result saveOnlineStep(ProjOnlineStepDTO dto);

    /**
     * 查询项目上线步骤信息
     *
     * @param dto
     * @return
     */
    Result<ProjOnlineStepData> selectOnlineStepList(ProjOnlineStepDTO dto);

    /**
     * 修改项目上线信息表
     *
     * @param dto
     * @return
     */
    Result updateOnlineStep(ProjOnlineStepDTO dto);

    /**
     * 判断步骤是否执行
     *
     * @param dto
     * @return
     */
    Result judgeStepExecute(ProjOnlineStepDTO dto);

    /**
     * 测试交付平台连接到云健康接口
     *
     * @param dto
     * @return
     */
    Result testCsmToAPI(ProjOnlineStepDTO dto);

    /**
     * 项目上线-- 查询医院上线信息
     *
     * @param dto
     * @return
     */
    Result<PageInfo<ProjectOnlineToHospitalVO>> projectOnlineForShowHospital(ProjOnlineStepDetailDTO dto);

    /**
     * 根据主键列表 批量更新明细数据
     *
     * @param dtoList
     * @return
     */
    Result<ProjOnlineStepSuccessVO> updateProjOnlineStepDetail(List<ProjOnlineStepDetailDTO> dtoList);

    /**
     * 解除医院限制
     * @param dtoList
     * @param projProjectInfo
     */
    void sendSysManageBatchPublic(List<ProjOnlineStepDetailDTO> dtoList, ProjProjectInfo projProjectInfo);

    /**
     * 说明: 统一更新his密码
     *
     * @param stepDTO
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/24 9:37
     * @remark: Copyright
     */
    Result changePassword(ProjOnlineStepDTO stepDTO);

    /**
     * 说明: 同步宽表数据
     *
     * @param stepDTO
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/24 14:56
     * @remark: Copyright
     */
    Result syncWidthTableData(ProjOnlineStepDTO stepDTO);

    /**
     * 说明: 查询主日志信息
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.github.pagehelper.PageInfo>
     * @author: Yhongmin
     * @createAt: 2024/5/24 15:35
     * @remark: Copyright
     */
    Result<PageInfo> selectLogAllByPage(ProjOnlineStepDetailDTO dto);

    /**
     * 单体医院项目上线
     *
     * @param dto
     * @return
     */
    Result projectOnlineForNotArea(ProjOnlineStepDTO dto) throws Exception;

    /**
     * 根据项目id更新产品状态
     *
     * @param projectInfoId 项目id
     */
    void updateProductExcutionStatus(Long projectInfoId);

    /**
     * 更新运营平台 工单状态与时间, 交付平台的项目状态与时间
     *
     * @param projectInfoId
     * @param stepEnum
     */
    void updateNodeStatus(Long projectInfoId, OrderStepEnum stepEnum);

    /**
     * 更新运营平台 工单状态与时间, 交付平台的项目状态与时间
     *
     * @param orderInfo   工单信息
     * @param stepEnum    同步节点
     * @param currentUser 当前用户
     */
    void updateNodeStatus(ProjOrderInfo orderInfo, OrderStepEnum stepEnum, SysUserVO currentUser);

    /**
     * 更新运营平台 工单状态与时间, 交付平台的项目状态与时间
     *
     * @param projectInfoId 项目id
     * @param stepEnum      枚举状态
     */
    void updateNodeStatus(Long projectInfoId, OrderStepEnum stepEnum, SysUserVO currentUser);

    /**
     * 项目上线 -- 停用患者智能服务老站点
     *
     * @param dto
     * @return
     */
    Result projectOnlineForStopOldSite(ProjOnlineStepDTO dto);

    /**
     * 患者智能服务发布
     *
     * @param dto
     * @return
     */
    Result projectOnlineForPublishPublicNo(ProjOnlineStepDTO dto);

    /**
     * 说明: 合并患者
     *
     * @param hospitalId
     * @return:java.lang.Object
     * @author: zd
     * @createAt: 2024/5/24 16:02
     * @remark: Copyright
     */
    Object mergePatientComm(Long hospitalId);

    /**
     * 添加外部接口调用日志
     *
     * @param message      消息
     * @param hospitalName 医院名称
     */
    void addSyncApiLog(String message, String hospitalName);


    /**
     * 说明: 云健康数据库权限下放/收回
     *
     * @param projectInfoId    项目id
     * @param cloudHospitalIds 医院列表
     * @param type             0-下发权限 1-回收权限
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/8/1 17:09
     * @remark: Copyright
     */
    Result updateSchemaGrant(Long projectInfoId, List<Long> cloudHospitalIds, List<Long> productInfos, String type);

    /**
     * 说明: 云健康浏览器上线步骤完成
     *
     * @param dto
     * @return
     */
    Result saveInitializationData(ProjOnlineStepDTO dto);

    /**
     * 合理用药抽数据
     *
     * @param dto
     * @return
     */
    Result fetchReasonableMedicationData(ProjOnlineStepDTO dto);

    /**
     * 项目上线-处理合理用药抽数据-查询主日志信息
     *
     * @param dto
     * @return
     */
    Result<PageInfo> selectReasonableMedicationLogAllByPage(ProjOnlineStepDetailDTO dto);

    /**
     * 清除测试数据消息
     * @param hospitalInfoIdList
     */
    void clearMessageData(List<Long> hospitalInfoIdList);

    /**
     * 项目上线--查询上线阶段---上线前准备文件
     * @param stepDTO
     * @return
     */
    Object selectOnlineBeforFile(ProjOnlineStepDTO stepDTO);

    /**
     * 查询数据
     * @param stepDTO
     * @return
     */
    Object selectOnlineBeforeIpHost(ProjOnlineStepDTO stepDTO);

    /**
     * 上线解除30分钟限制
     * @param dtoList
     * @param projProjectInfo
     */
    void sendSysManageBatch(List<ProjOnlineStepDetailDTO> dtoList, ProjProjectInfo projProjectInfo);

    /**
     * 产品授权后调用系统管理进行解除上线30分钟限制
     * @param hospitalInfo
     * @param arrayList
     * @param publishedSystems
     */
    void extracted(ProjHospitalInfo hospitalInfo, List<Long> arrayList, List<HospitalOnlinePublishedSystemDTO> publishedSystems);

    /**
     * 同步云健康用户到运维平台
     * @param stepDTO
     * @return
     */
    Result syncSysUserToUnwed(ProjectOnlineBeforeReq stepDTO,  SysUserVO currentUser);

    /**
     * 获取云健康用户部门
     * @param dto
     * @return
     */
    List<BaseIdNameResp> getUnwedDeptHospitalityInfoId(ProjectOnlineBeforeReq dto);

    /**
     * 根据运营科室id查询团队下实施人员
     * @param dto
     * @return
     */
    List<BaseIdNameResp> getUserListByDeptId(ProjectOnlineBeforeReq dto);

    /**
     * 获取运维平台角色
     * @return
     */
    List<BaseIdNameResp> getUnwedRoles();

    /**
     * 设置转众阳角色
     * @param dto
     * @return
     */
    Result updateUnwedRolesByParam(ProjectOnlineBeforeReq dto);

    /**
     * 开启运维:设置客户服务团队及专员/设置转众阳角色
     * @param dto
     * @return
     */
    Result<String> updateUnwedDataByParam(ProjectOnlineBeforeReq dto);

    /**
     * 单体医院项目上线
     *
     * @param dto
     * @return
     */
    void sendMessageInfo(ProjProjectInfo dto);

}
