package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 调用增量环境用：https://zengliang.msunhis.com 医院用10085001
 *
 * <AUTHOR>
 * @date 2024/11/7
 */
@Service
@Slf4j
public class MsunCloudYywgServiceImpl implements MsunCloudYywgService {

    /**
     * 客服运维平台的appid和secret
     */
    public static final String APP_ID = "kfywpt";
    public static final String APP_SECRET = "XgYH1xg5ov+8tx38bT+IQhGNnUBh9M";

    @Value("${cloud.forwardGateway}")
    private String forwardGateway;


    private static final BASE64Encoder encoder = new BASE64Encoder();


    @Override
    public JSONObject sendMsg(String hospitalId, String sendFrom, List<Long> userSysIdList, String title, String content, String url) {
        log.info("调用云健康接口推送云健康系统消息或者首页小铃铛消息，hospitalId={}，sendFrom={}，userSysIdList={}，title={}，content={}，url={}", hospitalId, sendFrom, userSysIdList, title, content, url);

        JSONObject joResult = null;
        try {
            String businessCode = "knowledgeServiceCheck";

            if (StringUtils.isNotEmpty(url)) {
                content = "<a href=\"" + url + "\">" + content + "</a>";
            }

            JSONArray jsonArray = (JSONArray) JSON.toJSON(userSysIdList);

            JSONObject paramJson = new JSONObject();
            paramJson.put("businessCode", businessCode);
            paramJson.put("title", title);
            paramJson.put("content", content);
            paramJson.put("sendTargetType", "USER");
            paramJson.put("sendUserType", "USER");
            paramJson.put("sendUserIdList", jsonArray);
            String paramStr = paramJson.toJSONString();

            String authorization = generateAuthorization("", paramStr, null);

            String requestUrl = forwardGateway + "/msun-core-component-message/v1/message/send";

            HttpHeaders newHeaders = new HttpHeaders();
            newHeaders.add("Authorization", authorization);
            newHeaders.add("Content-Type", "application/json");
            newHeaders.add("gateway", "app");
            newHeaders.add("hospitalId", hospitalId);
            newHeaders.add("orgId", hospitalId.substring(0, 5));
            newHeaders.add("preService", URLEncoder.encode(sendFrom, StandardCharsets.UTF_8.toString()));

            log.info("调用云健康接口推送云健康系统消息或者首页小铃铛消息，请求地址={}，参数={}，Authorization={}，headers={}", requestUrl, paramStr, authorization, JSON.toJSON(newHeaders));
            HttpEntity<?> requestEntity = new HttpEntity<>(paramStr, newHeaders);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(requestUrl, HttpMethod.POST, requestEntity, String.class);

            log.info("调用云健康接口推送云健康系统消息或者首页小铃铛消息，请求响应={}，接口返回值={}", JSON.toJSONString(response), JSON.toJSON(response.getBody()));

            joResult = JSONObject.parseObject(response.getBody());
        } catch (Exception e) {
            log.error("调用云健康接口推送云健康系统消息或者首页小铃铛消息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return joResult;

    }

    /**
     * 生成鉴权信息
     *
     * @param queryString 请求字符串
     * @param body        请求体信息
     * @return 鉴权信息
     */
    private static String generateAuthorization(String queryString, String body, String orgId) {
        String signType = "md5";
        String timestamp = String.valueOf(System.currentTimeMillis());
        //这里的lis换成自己的systemId，其他信息换成实际的信息
        String clientInfo = JSON.toJSONString(new HashMap<String, Object>(2) {{
            put("systemId", APP_ID);
            if (null != orgId) {
                put("orgId", orgId);
            }
        }});

        String param = StringUtils.isEmpty(body) ? queryString : body;

        StringBuilder plainText = new StringBuilder();
        plainText.append(signType).append(".");
        plainText.append(timestamp).append(".");
        plainText.append(clientInfo).append(".");
        plainText.append(StringUtils.isEmpty(param) ? "" : param).append(".");
        //这里的5hAwXtYdsjsp91Ic是secret，换成自己的secret
        plainText.append(APP_SECRET);

        String sign = DigestUtils.md5DigestAsHex(plainText.toString().getBytes()).toUpperCase();
        //log.info("FeignInterceptor.createAuthorization.sign,plainText={},md5={}", plainText, sign);
        StringBuilder authorization = new StringBuilder();
        authorization.append(encoder.encode(signType.getBytes())).append(".");
        authorization.append(encoder.encode(timestamp.getBytes())).append(".");
        authorization.append(encoder.encode(clientInfo.getBytes())).append(".");
        authorization.append(encoder.encode(sign.getBytes()));
        //log.info("FeignInterceptor.createAuthorization.plain,plainText={}", authorization);
        return authorization.toString().replace("\r\n", "");
    }

}
