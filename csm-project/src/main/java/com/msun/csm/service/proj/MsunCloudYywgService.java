package com.msun.csm.service.proj;


import java.util.List;

import com.alibaba.fastjson.JSONObject;

/**
 * 调用云健康接口 - 使用应用网关出口方式调用
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/11/7
 */
public interface MsunCloudYywgService {


    /**
     * 给云健康医生推送小铃铛消息
     *
     * @param hospitalId    云健康医院id
     * @param sendFrom      发送人/发送方（谁发送的）:【客户服务平台】
     * @param userSysIdList 医生云健康系统id集合（ 医生在某个云健康系统里的userId），如果是comm.identity表的identity_id，是进入云健康系统的具体产品后提醒的消息
     *                      如果是comm.identity表的user_id，是进入云健康后首页的小铃铛消息
     * @param title         消息标题【产品满意度调查】
     * @param content       消息内容
     * @param url           跳转地址（非必传）
     * @return
     */
    JSONObject sendMsg(String hospitalId, String sendFrom, List<Long> userSysIdList, String title, String content, String url);

}
