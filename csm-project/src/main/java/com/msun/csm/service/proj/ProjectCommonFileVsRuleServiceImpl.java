package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule;
import com.msun.csm.dao.mapper.proj.ProjectCommonFileVsRuleMapper;
import com.msun.csm.model.param.ProjectCommonFileVsRuleParam;

@Slf4j
@Service
public class ProjectCommonFileVsRuleServiceImpl implements ProjectCommonFileVsRuleService {

    @Resource
    private ProjectCommonFileVsRuleMapper projectCommonFileVsRuleMapper;


    @Override
    @Transactional
    public int addProjectCommonFile(ProjectCommonFileVsRule record) {
        return projectCommonFileVsRuleMapper.addProjectCommonFile(record);
    }

    @Override
    public ProjectCommonFileVsRule selectByPrimaryKey(Long projectCommonFileId) {
        return projectCommonFileVsRuleMapper.selectByPrimaryKey(projectCommonFileId);
    }

    @Override
    @Transactional
    public int updateByPrimaryKeySelective(ProjectCommonFileVsRule record) {
        return projectCommonFileVsRuleMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public ProjectCommonFileVsRule selectByParam(ProjectCommonFileVsRuleParam record) {
        return projectCommonFileVsRuleMapper.selectByParam(record);
    }

    @Override
    @Transactional
    public int updateProjectFileIds(Long projectCommonFileId, String projectFileIds) {
        return projectCommonFileVsRuleMapper.updateProjectFileIds(projectCommonFileId, projectFileIds);
    }

    @Override
    @Transactional
    public int updateUseFlag(Long projectCommonFileId, Boolean useFlag) {
        return projectCommonFileVsRuleMapper.updateUseFlag(projectCommonFileId, useFlag);
    }

    @Override
    @Transactional
    public int updateCheckInfoById(Long projectCommonFileId, Integer checkResult, String checkResultText) {
        return projectCommonFileVsRuleMapper.updateCheckInfoById(projectCommonFileId, checkResult, checkResultText);
    }
}
