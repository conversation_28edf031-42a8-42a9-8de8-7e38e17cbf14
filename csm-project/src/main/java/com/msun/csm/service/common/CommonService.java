package com.msun.csm.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.MilestoneStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.YunweiPaysignePreFlagTypeEnum;
import com.msun.csm.common.enums.api.yunying.ProductSolutionEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projsettlement.ContractTypeEnum;
import com.msun.csm.common.enums.projsettlement.SettlementMidOrderStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.yunying.YunyingResult;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProjContractCustomInfo;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjContractCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMidOrderMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.entity.ProjProjectSettlementMidOrder;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunwei.resp.YunweiLoginResp;
import com.msun.csm.feign.entity.yunying.req.SyncIsMsunCloudDTO;
import com.msun.csm.feign.entity.yunying.req.YunyingDisasterCloudPaysignageDTO;
import com.msun.csm.feign.entity.yunying.req.YunyingPaysignageDataDTO;
import com.msun.csm.model.dto.yunweiplatform.YunweiDisasterCloudSyncTimeDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.api.ApiYunyingServiceImpl;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalYunweiService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.service.yunying.YunYingService;
import com.msun.csm.service.yunying.YunYingServiceImpl;
import com.msun.csm.util.DomainMapUtil;

/**
 * 查询通用业务数据
 * <p>如</p>
 */
@Slf4j
@Service
public class CommonService {

    @Value("${project.feign.yunwei.recoverUpdateInfo-method}")
    private String recoverUpdateInfoUrl;

    @Value("${project.feign.yunwei.url}")
    private String devUrl;

    @Value("${project.current.qyWeChat-Auth-url}")
    private String weChatAuthUrl;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private ProjContractInfoMapper contractInfoMapper;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Autowired
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjContractCustomInfoMapper contractCustomInfoMapper;

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;

    @Resource
    private ProjMessageInfoService messageInfoService;

    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Resource
    private ProjProjectSettlementMidOrderMapper settlementMidOrderMapper;

    @Resource
    @Lazy
    private YunYingService yunYingService;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Lazy
    @Resource
    private ProjProjectSettlementCheckMainService onlyCommonInPrivateUseSettlementCheckMainService;

    /**
     * 获取开通的医院
     * 条件：域名不为空, 已开通
     *
     * @param hospitalInfos 请求参数
     * @return List<ProjHospitalInfoRelative>
     */
    public static List<ProjHospitalInfo> getOpenHospitalInfo(List<ProjHospitalInfo> hospitalInfos) {
        return hospitalInfos.stream()
                .filter(ProjApplyOrderHospitalYunweiService::hospitalOpen)
                .collect(Collectors.toList());
    }

    /**
     * 根据解决方案获取匹配的项目类型
     * <p>
     * 只会匹配非云健康升级的解决方案
     * </p>
     *
     * @param solutionType 解决方案
     * @return 项目类型
     */
    public static Integer getProjectType(Integer solutionType) {
        Integer projectType = null;
        if (ProductSolutionEnum.DAN_TI.getCode().intValue() == solutionType
                || ProductSolutionEnum.JI_CENG_DAN_TI.getCode().intValue() == solutionType) {
            projectType = ProjectTypeEnums.SINGLE.getCode();
        } else if (ProductSolutionEnum.QU_YU.getCode().intValue() == solutionType
                || ProductSolutionEnum.YI_GONG_TI.getCode().intValue() == solutionType
                || ProductSolutionEnum.QUAN_XIAN_YU.getCode().intValue() == solutionType) {
            projectType = ProjectTypeEnums.REGION.getCode();
        }
        if (ObjectUtil.isEmpty(projectType)) {
            log.error("根据解决方案未能匹配对应的项目类型. solutionType: {}", solutionType);
            throw new CustomException("未匹配到解决方案.");
        }
        return projectType;
    }

    /**
     * @param customInfoId 实施客户id
     * @return 客户信息
     */
    public ProjCustomInfo getCustomInfo(Long customInfoId) {
        return customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq("custom_info_id", customInfoId));
    }

    /**
     * @param sysUserId 系统用户id
     * @return 系统用户
     */
    public SysUser getSysUserInfo(Long sysUserId) {
        return sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", sysUserId));
    }

    /**
     * @param projectInfoId 项目
     * @return ProjProjectInfo
     */
    public ProjProjectInfo getProjectInfo(Long projectInfoId) {
        return projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId));
    }

    /**
     * @param yyCustomerId 运营实施客户id
     * @return ProjCustomInfo
     */
    public ProjCustomInfo getCustomInfoByYyCustomerId(Long yyCustomerId) {
        return customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq("yy_customer_id", yyCustomerId));
    }

    /**
     * 根据工单id查询项目
     *
     * @param orderInfoId 工单id
     * @return 项目信息
     */
    public ProjProjectInfo getProjectInfoByOrderInfoId(Long orderInfoId) {
        return projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq("order_info_id", orderInfoId));
    }

    /**
     * 根据工单id查询项目
     *
     * @param yyOrderId 运营工单id
     * @return 项目信息
     */
    public ProjProjectInfo getProjectInfoByYyOrderId(Long yyOrderId) {
        ProjOrderInfo orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("yy_order_id",
                yyOrderId));
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new CustomException("未查询到工单信息.");
        }
        return getProjectInfoByOrderInfoId(orderInfo.getOrderInfoId());
    }

    /**
     * @param contractInfoId 合同id
     * @return ProjContractInfo
     */
    public ProjContractInfo getContractInfo(Long contractInfoId) {
        return contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq("contract_info_id",
                contractInfoId));
    }

    /**
     * @param yyOrderId 运营工单id
     * @return ProjContractInfo
     */
    public ProjContractInfo getContractInfoByYyOrderId(Long yyOrderId) {
        ProjOrderInfo orderInfo = getOrderInfoByYyOrderId(yyOrderId);
        return contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq("contract_info_id",
                orderInfo.getContractInfoId()));
    }

    /**
     * 更具工单id查询工单
     *
     * @param orderInfoId 工单id
     * @return 更具工单id查询工单
     */
    public ProjOrderInfo getOrderInfo(Long orderInfoId) {
        return orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("order_info_id", orderInfoId));
    }

    /**
     * 更具工单id查询工单
     *
     * @param yyOrderId 运营工单id
     * @return 更具工单id查询工单
     */
    public ProjOrderInfo getOrderInfoByYyOrderId(Long yyOrderId) {
        return orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("yy_order_id", yyOrderId));
    }

    /**
     * 发送给pmo消息
     *
     * @param projectInfoId  项目id
     * @param messageContent 消息内容
     */
    public void sendToPmo(Long projectInfoId, String messageContent) {
        try {
            ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
            String keyword = projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber();
            sendMessageToSysPerson(DictMessageTypeEnum.DEPLOY_REJECT_KOWN.getId(), null,
                    projectInfoId,
                    null, MsgToCategory.TO_ROLE.getCode(),
                    keyword + StrUtil.COMMA + StrUtil.SPACE + messageContent);
        } catch (Throwable e) {
            log.error("发送给pmo消息异常. message: {}, e=", e.getMessage(), e);
        }
    }

    /**
     * 发送给提交人消息
     *
     * @param projectInfoId  项目id
     * @param messageContent 消息内容
     * @param messageTypeId  消息类型id, 如 9001, 部署节点驳回, 9002, 部署交付通知（云资源、新医院、新产品）
     * @param sysUserId      指定的个人用户id
     */
    public void sendToSinglePerson(Long projectInfoId, String messageContent, Long messageTypeId, Long sysUserId) {
        try {
            ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
            String keyword = projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber();
            sendMessageToSysPerson(messageTypeId, null,
                    projectInfoId,
                    sysUserId, MsgToCategory.SYS.getCode(),
                    keyword + StrUtil.COMMA + StrUtil.SPACE + messageContent);
        } catch (Throwable e) {
            log.error("发送给个人消息异常. message: {}, e=", e.getMessage(), e);
        }
    }

    public void sendMessageToSysPerson(long messageTypeId, String url, Long projectInfoId,
                                       Long userId, int messageToCategory, String content) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        List<Long> sysUserIds = CollUtil.newArrayList();
        if (userId != null) {
            sysUserIds.add(userId);
            if (messageTypeId == DictMessageTypeEnum.DEPLOY_REJECT_KOWN.getId() || messageTypeId == DictMessageTypeEnum.DEPLOY_REJECT_KOWN.getId()) {
                if (!sysUserIds.contains(projectInfo.getProjectLeaderId())) {
                    sysUserIds.add(projectInfo.getProjectLeaderId());
                }
            }
        }
        MessageParam messageParam = new MessageParam();
        messageParam.setUrl(url);
        messageParam.setMessageTypeId(messageTypeId); // 关键节点消息推送
        messageParam.setMessageToCategory(messageToCategory);
        messageParam.setProjectInfoId(projectInfoId);
        messageParam.setContent(content);
        messageParam.setSysUserIds(ObjectUtil.isNotEmpty(userId) ? sysUserIds : null);
        log.info("发送消息 : {}", JSONObject.toJSONString(messageParam));
        try {
            sendMessageService.sendMessage(messageParam, true);
        } catch (Exception e) {
            log.error("发送消息异常. message: {}, e=", e.getMessage(), e);
        }
    }

    /**
     * 获取企业微信url
     *
     * @return string
     */
    public String getMessageUrl(String businessUrl) {
        return getMessageUrl(businessUrl, userHelper.getCurrentUser().getSysUserId());
    }

    /**
     * 获取企业微信url
     *
     * @return string
     */
    public String getMessageUrl(String businessUrl, Long sysUserId) {
        Long msgInfoId = messageInfoService.insert(businessUrl, sysUserId);
        return weChatAuthUrl + "?state=" + msgInfoId;
    }

    /**
     * 获取合同销售用户信息
     *
     * @param yyOrderId 工单信息
     * @return sysuser
     */
    public SysUser getSalePersonByYyOrderId(Long yyOrderId) {
        return getContractCustomSalePersonByYyOrderId(yyOrderId);
    }

    /**
     * 获运维平台取请求头
     *
     * @return HttpHeaders
     */
    public HttpHeaders getYunweiHeaders() {
        HttpHeaders headers = new HttpHeaders();
        Result<YunweiLoginResp> operationPlatformToken = yunWeiPlatFormService.getOperationPlatformToken();
        headers.add("Authorization", operationPlatformToken.getData().getBody().getToken());
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    /**
     * 同步运维平台到期时间
     *
     * @param dto                  请求参数
     * @param disasterRecoveryInfo 云容灾信息
     * @param customInfo           客户信息
     */
    public void callYunweiSyncEndTime(YunweiDisasterCloudSyncTimeDTO dto,
                                      ProjDisasterRecoveryInfo disasterRecoveryInfo, ProjCustomInfo customInfo) {
        HttpHeaders headers = getYunweiHeaders();
        HttpEntity<YunweiDisasterCloudSyncTimeDTO> httpRequest =
                new HttpEntity<>(dto, headers);
        sysOperLogService.apiOperLogInsertObjAry("云容灾向运维同步到期时间入参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(),
                dto);
        ResponseEntity<Result> response =
                new RestTemplate().postForEntity(devUrl + recoverUpdateInfoUrl,
                        httpRequest, Result.class);
        sysOperLogService.apiOperLogInsertObjAry("云容灾向运维同步到期时间返回结果", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(),
                response);
        if (ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getBody()) || Objects.requireNonNull(response.getBody()).getCode() != 200) {
            String messageKeyword = StrUtil.EMPTY;
            try {
                ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(disasterRecoveryInfo.getSolutionType());
                String solutionTypeName = StrUtil.EMPTY;
                if (ObjectUtil.isNotEmpty(projectTypeEnums)) {
                    assert projectTypeEnums != null;
                    solutionTypeName = projectTypeEnums.getName();
                }
                messageKeyword = customInfo.getCustomName() + StrUtil.DASHED + solutionTypeName;
            } catch (Throwable e) {
                log.error("拼接参数异常. message: {}, e=", e.getMessage(), e);
            }
            exceptionMessageService.sendToSystemManager(-1L, messageKeyword + StrUtil.COMMA + "云容灾向运维同步到期时间接口调用异常, "
                    + "请及时处理. 客户: " + customInfo.getCustomName() + "(" + customInfo.getCustomInfoId() + ")");
        }
    }

    /**
     * 获取合同是否缴纳首付款
     * paysignage -> getSalePersonByYyOrderId -> getContractCustomSalePersonByYyOrderId -> getContractCustomSalePersonByOrderInfoId
     *
     * @param projectInfoId 项目id
     * @param yyContractIds 运营合同id集合
     * @return true: 已缴纳, false: 未缴纳
     */
    public boolean paysignage(Long projectInfoId, List<Long> yyContractIds) {
        // 查询项目工单
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        String keyMsg = projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber();
        ProjOrderInfo softOrderInfo = getOrderInfo(projectInfo.getOrderInfoId());
        // 调用运营平台接口, 判断是否缴纳首付款
        YunyingDisasterCloudPaysignageDTO paysignageDTO = YunyingDisasterCloudPaysignageDTO.builder()
                .token(YunYingServiceImpl.TOKEN)
                .projId(StrUtil.toString(softOrderInfo.getYyOrderId()))
                .conOgIdList(yyContractIds.stream().map(StrUtil::toString).collect(Collectors.toList()))
                .build();

        // 2025-07-25 将当前项目下的所有合同传入运营平台(如发现问题需要回退)
        List<ProjContractInfo> tmpContractInfos = onlyCommonInPrivateUseSettlementCheckMainService.findStandardContractInfo(projectInfo.getProjectInfoId());
        if (tmpContractInfos != null && !tmpContractInfos.isEmpty()) {
            List<Long> yyContractAllIds = tmpContractInfos.stream().map(ProjContractInfo::getYyContractId).collect(Collectors.toList());
            paysignageDTO.setConOgIdList(yyContractAllIds.stream().map(StrUtil::toString).collect(Collectors.toList()));
        }

        // 查询合同所有工单是否包含当前项目工单
        List<ProjOrderInfo> orderList = orderInfoMapper.selectOrderListByContractIds(paysignageDTO);
        if (ObjectUtil.isEmpty(orderList)) {
            return true;
        }
        List<Long> orderIds = orderList.stream().map(ProjOrderInfo::getYyOrderId).collect(Collectors.toList());
        if (!orderIds.contains(Long.valueOf(paysignageDTO.getProjId()))) {
            paysignageDTO.setProjId(String.valueOf(orderIds.get(0)));
        }
        // 获取合同客户销售负责人(根据的项目工单)
        SysUser sysUser = getSalePersonByYyOrderId(softOrderInfo.getYyOrderId());
        String userLoginName = sysUser.getAccount();
        if (ObjectUtil.isEmpty(sysUser)) {
            throw new CustomException("获取首付款缴纳状态时未查询到销售人员账号信息.");
        }
        // 记录日志
        sysOperLogService.apiOperLogInsertObjAry("获取首付款缴纳信息入参", StrUtil.EMPTY,
                Log.LogOperType.SEARCH.getCode(),
                userLoginName, paysignageDTO);
        // 存标识结果
        List<Map<Long, Boolean>> resultFlagList = CollUtil.newArrayList();
        // 结果标识
        boolean resultFlag = true;
        YunyingResult<List<YunyingPaysignageDataDTO>> payResult;
        try {
            // 调接口
            payResult = yunyingFeignClient.getDisasterCloudPaysignage(userLoginName, paysignageDTO);
            sysOperLogService.apiOperLogInsertObjAry("获取首付款缴纳信息返回结果", StrUtil.EMPTY,
                    Log.LogOperType.SEARCH.getCode(),
                    payResult);
            // 处理返回值
            if (ObjectUtil.isEmpty(payResult) || !payResult.isSuccess()) {
                log.error("接口调用异常. result: {}", payResult);
                // 判断项目若已经入驻, 不再抛出异常.
                if (hasFinishProjEntry(projectInfoId)) {
                    log.warn("项目已入驻, 不再异常处理. projectInfo: {}", JSONUtil.toJsonStr(projectInfo));
                    return true;
                }
                throw new CustomException("获取首付款缴纳状态时, 接口调用返回值异常. result:" + payResult);
            } else {
                for (YunyingPaysignageDataDTO paysignageDataDTO : payResult.getObj()) {
                    YunweiPaysignePreFlagTypeEnum preFlagTypeEnum =
                            YunweiPaysignePreFlagTypeEnum.getEnum(paysignageDataDTO.getConPreFlag());
                    if (ObjectUtil.isEmpty(preFlagTypeEnum)) {
                        log.warn("查询运营平台首付款信息. 返回值: {}", payResult);
                        throw new CustomException("获取首付款缴纳状态时, 接口调用结果标识异常. result:" + payResult);
                    }
                    assert preFlagTypeEnum != null;
                    // 标识合同是否缴纳map
                    Map<Long, Boolean> flagResult = MapUtil.newHashMap();
                    Long yyContractId = Long.parseLong(paysignageDataDTO.getConOgId());
                    if (preFlagTypeEnum.getCode() == YunweiPaysignePreFlagTypeEnum.PASSED.getCode()) {
                        // 已缴纳
                        ProjContractInfo contractInfo =
                                contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq(
                                        "yy_contract_id",
                                        yyContractId));
                        if (ObjectUtil.isEmpty(contractInfo)) {
                            log.error("根据首付款缴纳接口返回值, 未查询到合同信息. yyContractId: {}", yyContractId);
                            throw new CustomException("未查询到合同信息.");
                        }
                        // 若已缴纳更新合同首付款标识状态
                        ProjContractInfo update = new ProjContractInfo();
                        update.setPaySignage(preFlagTypeEnum.getPaySignageEnum().getCode());
                        update.setConPreDesc(paysignageDataDTO.getConPreDesc());
                        update.setContractInfoId(contractInfo.getContractInfoId());
                        update.setUpdateTime(new Date());
                        int count = contractInfoMapper.updateById(update);
                        log.info("同步运营平台合同首付款已缴纳状态. {}", count);
                        flagResult.put(yyContractId, Boolean.TRUE);
                    } else {
                        // 未缴纳
                        ProjContractInfo info = new ProjContractInfo();
                        info.setYyContractId(yyContractId);
                        flagResult.put(yyContractId, Boolean.FALSE);
                        resultFlag = false;
                    }
                    resultFlagList.add(flagResult);
                }
            }
        } catch (Throwable e) {
            // 处理异常
            log.error("接口调用异常. message: {}, e=", e.getMessage(), e);
            exceptionMessageService.sendToSystemManager(projectInfoId,
                    keyMsg + StrUtil.COLON + "调用接口获取运营平台首付款标识报错." + e.getMessage());
            throw e;
        }
        log.info("返回首付款标识结果: {}", resultFlagList);
        if (CollUtil.isEmpty(resultFlagList)) {
            log.error("获取首付款是否缴纳结果异常, result: {}", payResult);
            throw new CustomException("获取首付款是否缴纳结果异常.");
        }
        if (resultFlagList.size() != yyContractIds.size()) {
            log.error("获取首付款是否缴纳结果异常, 查询合同数量与返回结果不一致, result: {}, request: {}", payResult, paysignageDTO);
            throw new CustomException("获取首付款是否缴纳结果异常.");
        }
        return resultFlag;
    }

    /**
     * 是否完成了入驻
     *
     * @param projectInfoId 项目id
     * @return true:已完成入驻, false:未完成入驻或未查询到入驻节点
     */
    public boolean hasFinishProjEntry(Long projectInfoId) {
        ProjMilestoneInfo milestoneInfo =
                milestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id",
                                projectInfoId).eq("invalid_flag", NumberEnum.NO_0.num())
                        .in("milestone_node_code",
                                MilestoneNodeEnum.SETTLED_PMO_AUDIT.getCode()));
        return ObjectUtil.isNotEmpty(milestoneInfo) && milestoneInfo.getMilestoneStatus() == MilestoneStatusEnum.COMPLETED.getCode().intValue();
    }

    /**
     * 查询已开通的医院. 根据客户id和项目类型查询
     *
     * @param customInfoId 客户id
     * @param projectType  项目类型
     * @return 医院合集
     */
    public List<ProjHospitalInfo> findOpendHospitalInfoSimple(Long customInfoId, Integer projectType) {
        return hospitalInfoService.findOpenHospitalInfoSimple(customInfoId, projectType);
    }

    /**
     * 刷新域名缓存, 用于获取对应的信息
     *
     * @param hospitalInfo 医院
     */
    public void refreshDomain(ProjHospitalInfo hospitalInfo) {
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        // - 刷新域名控制器
        domainHolder.refresh(domainMap);
    }

    /**
     * 根据项目id查询该项目所含运营合同id集合
     *
     * @param projectInfoId 项目id
     * @return 运营合同id集合
     */
    public List<Long> findYyContractId(Long projectInfoId) {
        List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq(
                "project_info_id",
                projectInfoId));
        if (CollUtil.isEmpty(orderProducts)) {
            return CollUtil.newArrayList();
        }
        return orderProducts.stream().map(ProjOrderProduct::getYyContractId).distinct().collect(Collectors.toList());
    }

    /**
     * 同步运营平台云资源附加信息
     *
     * @param customInfoId      实施客户id
     * @param projectInfoId     项目id
     * @param deployCloudVendor 云资源厂商
     * @param msunPayFlag       是否众阳付费：0 否, 1 是
     */
    public void syncIsMsunCloud(Long customInfoId, Long projectInfoId, String deployCloudVendor, String msunPayFlag) {
        Long tmpProjectInfoId = -1L;
        ProjProjectInfo projectInfo = null;
        try {
            // 查询运营客户id
            ProjCustomInfo customInfo = getCustomInfo(customInfoId);
            projectInfo = getProjectInfo(projectInfoId);
            tmpProjectInfoId = projectInfo.getProjectInfoId();
            SysUser sysUser = getSysUserInfo(projectInfo.getProjectLeaderId());
            String userLoginName = sysUser.getAccount();
            // 查询产品信息, 获取解决方案
            ProjOrderProduct product = getOrderProduct(projectInfo.getOrderInfoId());
            syncIsMsunCloud(StrUtil.toString(customInfo.getYyCustomerId()), deployCloudVendor,
                    StrUtil.toString(product.getProductResolveTypeId()),
                    msunPayFlag,
                    userLoginName);
        } catch (Throwable e) {
            log.error("同步运营平台云资源附加信息异常. message: {}, e=", e.getMessage(), e);
            exceptionMessageService.sendToSystemManager(tmpProjectInfoId, (ObjectUtil.isNotEmpty(projectInfo)
                    ? Objects.requireNonNull(projectInfo).getProjectName() + StrUtil.DASHED
                    + projectInfo.getProjectNumber()
                    : StrUtil.EMPTY) + StrUtil.COMMA
                    + "同步运营平台云资源附加信息异常, 请及时处理.");
        }
    }

    /**
     * 同步运营平台云容灾附加信息
     *
     * @param recoveryInfo      云容灾详情
     * @param deployCloudVendor 云资源厂商
     * @param msunPayFlag       是否众阳付费：0 否, 1 是
     */
    public void syncIsMsunCloudDisRecover(ProjDisasterRecoveryInfo recoveryInfo, String deployCloudVendor,
                                          String msunPayFlag) {
        ProjCustomInfo customInfo = null;
        try {
            customInfo = getCustomInfo(recoveryInfo.getCustomInfoId());
            // 查询运营客户id
            // 查询产品信息, 获取解决方案
            ProjOrderProduct product = getOrderProduct(recoveryInfo.getOrderInfoId());
            SysUser sysUser =
                    sysUserMapper.selectUserIdByYungyingId(StrUtil.toString(recoveryInfo.getResponsiblePersonId()));
            syncIsMsunCloud(StrUtil.toString(customInfo.getYyCustomerId()), deployCloudVendor,
                    StrUtil.toString(product.getProductResolveTypeId()),
                    msunPayFlag,
                    sysUser.getAccount());
        } catch (Throwable e) {
            log.error("同步运营平台云容灾附加信息异常. message: {}, e=", e.getMessage(), e);
            exceptionMessageService.sendToSystemManager(-1L, (ObjectUtil.isNotEmpty(customInfo)
                    ? Objects.requireNonNull(customInfo).getCustomName() + StrUtil.DASHED
                    : StrUtil.EMPTY) + StrUtil.COMMA
                    + "同步运营平台云容灾附加信息异常, 请及时处理.");
        }
    }

    public void syncIsMsunCloud(String yyCustomerId, String deployCloudVendor, String pemCusSolType,
                                String msunPayFlag, String userLoginName) throws Exception {
        SyncIsMsunCloudDTO syncIsMsunCloudDTO = SyncIsMsunCloudDTO.builder()
                .customerId(yyCustomerId)
                .token(ApiYunyingServiceImpl.TOKEN)
                .cloudManufacturer(deployCloudVendor)
                .pemCusSolType(pemCusSolType)
                .zyPay(msunPayFlag)
                .build();
        // 查询项目经理id
        yunYingService.syncIsMsunCloud(userLoginName, syncIsMsunCloudDTO);
    }

    /**
     * 查询一条工单产品.
     * 获取第一条数据, 这种情况用于首期项目中, 一般是为了查询客户的解决方案
     *
     * @param orderInfoId 工单id
     * @return 产品信息
     */
    public ProjOrderProduct getOrderProduct(Long orderInfoId) {
        return orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq(
                "order_info_id", orderInfoId)).get(0);
    }

    /**
     * 根据项目id查询该项目所含运营合同id集合
     *
     * @param projectInfoId 项目id
     * @return 运营合同id集合
     */
    public List<ProjContractInfo> findContractInfo(Long projectInfoId) {
        List<Long> yyContractIds = findYyContractId(projectInfoId);
        if (CollUtil.isEmpty(yyContractIds)) {
            return CollUtil.newArrayList();
        }
        return contractInfoMapper.selectList(new QueryWrapper<ProjContractInfo>().in(
                "yy_contract_id",
                yyContractIds));
    }

    /**
     * 根据 运营平台合同id查询项目信息
     *
     * @param yyContractIds 运营合同id集合
     * @return 项目集合
     */
    public List<ProjProjectInfo> findProjectInfoByYyContractIds(List<Long> yyContractIds) {
        // 查询合同下所有产品
        List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().in(
                "yy_contract_id",
                yyContractIds));
        if (CollUtil.isEmpty(orderProducts)) {
            return CollUtil.newArrayList();
        }
        // 去重查询所有软件工单
        List<ProjOrderInfo> orderInfos = orderInfoMapper.selectList(new QueryWrapper<ProjOrderInfo>().in(
                "order_info_id",
                orderProducts.stream().map(ProjOrderProduct::getOrderInfoId).collect(Collectors.toList())));
        if (CollUtil.isEmpty(orderProducts)) {
            return CollUtil.newArrayList();
        }
        return projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().in(
                "order_info_id", orderInfos.stream().map(ProjOrderInfo::getOrderInfoId).collect(Collectors.toList())));
    }

    /**
     * 是否标准合同
     *
     * @param contractInfo 合同
     * @return boolean
     */
    public static boolean isStandardContract(ProjContractInfo contractInfo) {
        return ContractTypeEnum.STANDARD_CONTRACT.getCode().intValue() == contractInfo.getContractType();
    }

    /**
     * 获取合同客户信息下销售人员信息
     *
     * @param projectInfoId 项目id
     * @return 账号信息
     */
    public SysUser getContractCustomSalePerson(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        return getContractCustomSalePersonByOrderInfoId(projectInfo.getOrderInfoId());
    }

    /**
     * 获取合同客户信息下销售人员信息
     *
     * @param yyOrderId 项目id
     * @return 账号信息
     */
    public SysUser getContractCustomSalePersonByYyOrderId(Long yyOrderId) {
        ProjOrderInfo orderInfo = getOrderInfoByYyOrderId(yyOrderId);
        if (ObjectUtil.isEmpty(orderInfo)) {
            log.info("未查询到工单信息. yyOrderId: {}", yyOrderId);
            throw new CustomException("未查询到工单信息.");
        }
        return getContractCustomSalePersonByOrderInfoId(orderInfo.getOrderInfoId());
    }

    /**
     * 获取合同客户信息下销售人员信息
     *
     * @param orderInfoId 项目id
     * @return 账号信息
     */
    public SysUser getContractCustomSalePersonByOrderInfoId(Long orderInfoId) {
        ProjOrderInfo orderInfo = new LambdaQueryChainWrapper<>(orderInfoMapper)
                .eq(ProjOrderInfo::getOrderInfoId, orderInfoId)
                .eq(ProjOrderInfo::getIsDeleted, 0)
                .one();
        ProjContractInfo contractInfo = new LambdaQueryChainWrapper<>(contractInfoMapper)
                .eq(ProjContractInfo::getContractInfoId, orderInfo.getContractInfoId())
                .eq(ProjContractInfo::getIsDeleted, 0)
                .one();
        ProjContractCustomInfo contractCustomInfo = new LambdaQueryChainWrapper<>(contractCustomInfoMapper)
                .eq(ProjContractCustomInfo::getYyPartaId, contractInfo.getYyContractCustomId())
                .eq(ProjContractCustomInfo::getIsDeleted, 0)
                .one();
        if (ObjectUtil.isEmpty(contractCustomInfo)) {
            throw new CustomException(StrUtil.format("未查询到合同客户信息.orderInfoId: {}, contractInfoId: {}, yy_parta_id: {}",
                    orderInfoId, orderInfo.getContractInfoId(), contractInfo.getYyContractCustomId()));
        }
        SysUser sysUser = new LambdaQueryChainWrapper<>(sysUserMapper)
                .eq(SysUser::getUserYunyingId, String.valueOf(contractCustomInfo.getContractCustomSalepersonId()))
                .eq(SysUser::getIsDeleted, 0)
                .one();
        if (sysUser == null) {
            throw new CustomException(StrUtil.format("未查询到销售信息.orderInfoId: {}, contractInfoId: {}, yy_parta_id: {}, user_yunying_id:{}",
                    orderInfoId, orderInfo.getContractInfoId(), contractInfo.getYyContractCustomId(), contractCustomInfo.getContractCustomSalepersonId()));
        }
        return sysUser;
    }

    /**
     * 查询是否存在免中间件工单申请
     *
     * @param projectInfoId 项目id
     * @return true: 有, false: 没有
     */
    public boolean needAuditMidOrder(Long projectInfoId) {
        ProjProjectSettlementMidOrder midOrder = getSettlementMidOrder(projectInfoId);
        if (ObjectUtil.isEmpty(midOrder)) {
            return false;
        }
        return midOrder.getStatus() == SettlementMidOrderStatusEnum.PRE_APPLY.getCode();
    }

    /**
     * 根据项目id获取免中间件申请单
     *
     * @param projectInfoId 项目id
     * @return 申请单内容
     */
    public ProjProjectSettlementMidOrder getSettlementMidOrder(Long projectInfoId) {
        return settlementMidOrderMapper.selectOne(new QueryWrapper<ProjProjectSettlementMidOrder>().eq(
                "project_info_id",
                projectInfoId));
    }

    public Map<Long, String> getUserNameMap(List<Long> uids) {
        uids.removeAll(Arrays.asList(null, -1L, 0L));
        if (CollUtil.isEmpty(uids)) {
            return new HashMap<>();
        }
        return new LambdaQueryChainWrapper<>(sysUserMapper)
                .select(SysUser::getSysUserId, SysUser::getUserName)
                .in(SysUser::getSysUserId, new HashSet<>(uids))
                .eq(SysUser::getIsDeleted, 0).list()
                .stream().collect(Collectors.toMap(SysUser::getSysUserId, SysUser::getUserName));
    }
}
