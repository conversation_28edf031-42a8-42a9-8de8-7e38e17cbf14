package com.msun.csm.service.proj;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.utils.AesUtil;
import com.msun.core.component.implementation.api.port.ApplicationCheckApi;
import com.msun.core.component.implementation.api.port.dto.SqlCheckApiDTO;
import com.msun.core.component.implementation.api.port.dto.SqlCheckDTO;
import com.msun.core.component.implementation.api.port.dto.SqlSelectLoginDTO;
import com.msun.core.component.implementation.api.port.vo.SqlCheckVO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.HospitalLevelEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.dto.ExcelHeaderRowDTO;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.common.model.dto.Pagination;
import com.msun.csm.dao.entity.dict.DictProductFunction;
import com.msun.csm.dao.entity.dict.DictProjectAcceptRule;
import com.msun.csm.dao.entity.dict.DictProjectCheckAcceptMenu;
import com.msun.csm.dao.entity.dict.DictSatisfactionSurveyLevel;
import com.msun.csm.dao.entity.proj.AttachmentInfoVO;
import com.msun.csm.dao.entity.proj.ClassificationScoreVO;
import com.msun.csm.dao.entity.proj.CommonScoreRecordPO;
import com.msun.csm.dao.entity.proj.CommonScoreRecordVO;
import com.msun.csm.dao.entity.proj.DocumentScoreRecordPO;
import com.msun.csm.dao.entity.proj.InitProductFunctionResultVO;
import com.msun.csm.dao.entity.proj.ProcessScoreRecordPO;
import com.msun.csm.dao.entity.proj.ProductFunctionScoreRecordPO;
import com.msun.csm.dao.entity.proj.ProductFunctionScoreRecordVO;
import com.msun.csm.dao.entity.proj.ProductFunctionUseInfoPO;
import com.msun.csm.dao.entity.proj.ProductFunctionUseInfoVO;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProductFunctionUseInfo;
import com.msun.csm.dao.entity.proj.ProjProjectClassificationScore;
import com.msun.csm.dao.entity.proj.ProjProjectClassificationScorePO;
import com.msun.csm.dao.entity.proj.ProjProjectDeductionDetail;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectAcceptanceMenuResultVO;
import com.msun.csm.dao.entity.proj.ProjectAcceptanceMenuVO;
import com.msun.csm.dao.entity.proj.ProjectAcceptanceRecordVO;
import com.msun.csm.dao.entity.proj.SatisfactionSurveyScoreRecordVO;
import com.msun.csm.dao.entity.proj.UpdateDeductionParam;
import com.msun.csm.dao.entity.proj.UpdateFunctionUseInfoParam;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.mapper.dict.DictProductFunctionMapper;
import com.msun.csm.dao.mapper.dict.DictProjectAcceptRuleMapper;
import com.msun.csm.dao.mapper.dict.DictProjectCheckAcceptMenuMapper;
import com.msun.csm.dao.mapper.dict.DictSatisfactionSurveyLevelMapper;
import com.msun.csm.dao.mapper.proj.ProjDeductionDetailCommonMapper;
import com.msun.csm.dao.mapper.proj.ProjDeductionDetailDocumentMapper;
import com.msun.csm.dao.mapper.proj.ProjDeductionDetailProcessMapper;
import com.msun.csm.dao.mapper.proj.ProjDeductionDetailSatisfactionMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductFunctionUseInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectClassificationScoreMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectDeductionDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.model.ProjectAcceptanceRecordParam;
import com.msun.csm.model.SimulationStatisticalDataVO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.param.CustomAndProjectInfoIdParam;
import com.msun.csm.model.param.DeleteProcessDeductionParam;
import com.msun.csm.model.param.InitProductFunctionParam;
import com.msun.csm.model.param.QueryClassificationScoreParam;
import com.msun.csm.model.param.QueryProductFunctionScoreParam;
import com.msun.csm.model.param.QueryProductFunctionScoreParam2;
import com.msun.csm.model.param.QueryProductFunctionScoreParam3;
import com.msun.csm.model.param.QueryProductFunctionScoreRecordParam;
import com.msun.csm.model.param.QueryProjectAcceptanceMenuParam;
import com.msun.csm.model.param.QueryProjectAcceptanceRecordParam;
import com.msun.csm.model.param.QueryScoreSummaryParam;
import com.msun.csm.model.param.SaveClassificationScoreParam;
import com.msun.csm.model.param.SaveDeductionParam;
import com.msun.csm.model.param.StartCheckUseCountParam2;
import com.msun.csm.model.vo.ProjectAcceptanceRecord;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.FilterUtil;
import com.msun.csm.util.PageUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

@Slf4j
@Service
public class ProjectCheckAndAcceptServiceImpl implements ProjectCheckAndAcceptService {

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectAcceptanceMapper projProjectAcceptanceMapper;

    @Resource
    private DictProjectCheckAcceptMenuMapper dictProjectCheckAcceptMenuMapper;

    @Resource
    private DictProjectAcceptRuleMapper dictProjectAcceptRuleMapper;

    @Resource
    private ProjProductDeliverRecordMapper projProductDeliverRecordMapper;

    @Resource
    private ProjProjectDeductionDetailMapper projProjectDeductionDetailMapper;

    @Resource
    private ProjProductFunctionUseInfoMapper projProductFunctionUseInfoMapper;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjProjectClassificationScoreMapper projProjectClassificationScoreMapper;

    @Resource
    private DictProductFunctionMapper dictProductFunctionMapper;

    @Resource
    private ApplicationCheckApi applicationCheckApi;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private DictSatisfactionSurveyLevelMapper dictSatisfactionSurveyLevelMapper;

    @Resource
    private ProjDeductionDetailDocumentMapper projDeductionDetailDocumentMapper;

    @Resource
    private ProjDeductionDetailProcessMapper projDeductionDetailProcessMapper;

    @Resource
    private ProjDeductionDetailCommonMapper projDeductionDetailCommonMapper;

    @Resource
    private ProjDeductionDetailSatisfactionMapper projDeductionDetailSatisfactionMapper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Override
    public PageList<ProjectAcceptanceRecordVO> queryProjectAcceptanceRecord(QueryProjectAcceptanceRecordParam param) {
        ProjectAcceptanceRecordParam projectAcceptanceRecordDTO = new ProjectAcceptanceRecordParam();
        projectAcceptanceRecordDTO.setCustomInfoId(param.getCustomInfoId());
        projectAcceptanceRecordDTO.setProjectNumber(param.getProjectNumber());
        projectAcceptanceRecordDTO.setStartTime(DateUtil.formatDateTime(param.getStartTime()));
        projectAcceptanceRecordDTO.setEndTime(DateUtil.formatDateTime(param.getEndTime()));

        // 所有验收记录
        List<ProjectAcceptanceRecord> projectAcceptanceRecords = projProjectAcceptanceMapper.queryProjectAcceptanceRecord(projectAcceptanceRecordDTO);
        // 仅首验的验收记录
        List<ProjectAcceptanceRecord> projectAcceptanceRecordOnlyFirst = projProjectAcceptanceMapper.queryProjectAcceptanceRecordOnlyFirst(projectAcceptanceRecordDTO);

        if (CollectionUtils.isEmpty(projectAcceptanceRecords)) {
            PageList<ProjectAcceptanceRecordVO> pageList = new PageList<>();
            pageList.setPagination(new Pagination(param.getPageNum(), param.getPageSize(), 0, 0));
            pageList.setPageResultList(new ArrayList<>());
            return pageList;
        }
        List<ProjectAcceptanceRecordVO> collect = projectAcceptanceRecords.stream().map(item -> {
            List<BaseIdNameResp> baseIdNameResps = projProductDeliverRecordMapper.queryDeliverProductIdAndNameByProjectInfoId(item.getProjectInfoId());
            String productName;
            if (CollectionUtils.isEmpty(baseIdNameResps)) {
                productName = null;
            } else {
                productName = baseIdNameResps.stream().map(BaseIdNameResp::getName).collect(Collectors.joining(", "));
            }

            // 验收得分
            String acceptanceScore;
            // 首验得分
            String firstScore;
            // 终验得分
            String finalScore;
            // 仅需一次验收
            if (Short.valueOf("1").equals(item.getRequiredAcceptanceTimes())) {
                acceptanceScore = item.getAcceptanceScore();
                firstScore = null;
                finalScore = item.getAcceptanceScore();
            } else if (Short.valueOf("1").equals(item.getCurrentTimes())) {
                acceptanceScore = null;
                firstScore = item.getAcceptanceScore();
                finalScore = null;
            } else {
                ProjectAcceptanceRecord projectAcceptanceRecord = projectAcceptanceRecordOnlyFirst.stream().filter(item2 -> item.getCustomInfoId().equals(item2.getCustomInfoId()) && item.getProjectInfoId().equals(item2.getProjectInfoId())).findFirst().orElse(null);
                acceptanceScore = item.getAcceptanceScore();
                // 计算第一次得分
                firstScore = projectAcceptanceRecord == null ? null : projectAcceptanceRecord.getAcceptanceScore();
                // 计算第二次得分
                finalScore = firstScore == null ? null : String.valueOf(Integer.parseInt(item.getAcceptanceScore()) - Integer.parseInt(projectAcceptanceRecord.getAcceptanceScore()));
            }

            String standardDuration;
            if (item.getSettleInTime() == null) {
                standardDuration = null;
            } else if (item.getApplyAcceptanceTime() == null) {
                standardDuration = String.valueOf(DateUtil.betweenDay(item.getSettleInTime(), new Date(), true) + 1);
            } else {
                standardDuration = String.valueOf(DateUtil.betweenDay(item.getSettleInTime(), item.getApplyAcceptanceTime(), true) + 1);
            }

            return ProjectAcceptanceRecordVO
                    .builder()
                    .projectNumber(item.getProjectNumber())
                    .projectInfoId(String.valueOf(item.getProjectInfoId()))
                    .customName(item.getCustomName())
                    .customInfoId(String.valueOf(item.getCustomInfoId()))
                    .productName(productName)
                    .status(this.convertStatus(item))
                    .acceptanceScore(acceptanceScore)
                    .firstScore(firstScore)
                    .finalScore(finalScore)
                    .standardDuration(standardDuration)
                    .settleInTime(DateUtil.formatDate(item.getSettleInTime()))
                    .acceptanceTestTime(DateUtil.formatDate(item.getApplyAcceptanceTime()))
                    .acceptanceTestTimeSort(item.getApplyAcceptanceTime())
                    .durationReduction(item.getDurationReduction())
                    .projectTeamName(item.getDeptName())
                    .projectTeamId(String.valueOf(item.getProjectTeamId()))
                    .projectLeaderName(item.getUserName())
                    .projectLeaderId(String.valueOf(item.getProjectLeaderId()))
                    .onlyOneCheckFlag(Short.valueOf("1").equals(item.getRequiredAcceptanceTimes()))
                    .build();
        }).collect(Collectors.toList());

        // 根据验收状态过滤数据
        if (StringUtils.isNotBlank(param.getStatus())) {
            collect = collect.stream().filter(item -> param.getStatus().equals(String.valueOf(item.getStatus()))).collect(Collectors.toList());
        }

        // 排序
        List<ProjectAcceptanceRecordVO> sorted = collect.stream().filter(item -> item.getAcceptanceTestTimeSort() != null).sorted((o1, o2) -> (this.compareTime(o1.getAcceptanceTestTimeSort(), o2.getAcceptanceTestTimeSort()))).collect(Collectors.toList());
        Collections.reverse(sorted);
        List<ProjectAcceptanceRecordVO> timeEmpty = collect.stream().filter(item -> item.getAcceptanceTestTimeSort() == null).sorted(Comparator.comparing(ProjectAcceptanceRecordVO::getCustomName)).collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(timeEmpty)) {
            sorted.addAll(timeEmpty);
        }

        // 分页
        PageList<ProjectAcceptanceRecordVO> pageList = PageUtil.getPageList(sorted, param.getPageNum(), param.getPageSize());
        // 此处total暂时当作总数据条数使用
        pageList.getPagination().setTotal(sorted.size());
        return pageList;
    }

    private int compareTime(Date time1, Date time2) {
        try {
            return time1.compareTo(time2);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 转换验收状态
     *
     * @param item 参数
     * @return 验收状态：1-首验申请；2-首验接受；3-首验驳回；4-首验完成；5-终验申请；6-终验接收；7-终验驳回；8-终验通过
     */
    private Integer convertStatus(ProjectAcceptanceRecord item) {
        // 仅需一次验收
        if (Short.valueOf("1").equals(item.getRequiredAcceptanceTimes())) {
            if (Short.valueOf("1").equals(item.getAcceptanceStatus())) {
                return 5;
            }
            if (Short.valueOf("5").equals(item.getAcceptanceStatus())) {
                return 7;
            }
            if (Short.valueOf("11").equals(item.getAcceptanceStatus())) {
                return 6;
            }
            if (Short.valueOf("21").equals(item.getAcceptanceStatus())) {
                return 7;
            }
            return 8;
        } else if (Short.valueOf("1").equals(item.getCurrentTimes())) {
            if (Short.valueOf("1").equals(item.getAcceptanceStatus())) {
                return 1;
            }
            if (Short.valueOf("5").equals(item.getAcceptanceStatus())) {
                return 3;
            }
            if (Short.valueOf("11").equals(item.getAcceptanceStatus())) {
                return 2;
            }
            if (Short.valueOf("12").equals(item.getAcceptanceStatus())) {
                return 3;
            }
            if (Short.valueOf("13").equals(item.getAcceptanceStatus())) {
                return 4;
            }
            if (Short.valueOf("21").equals(item.getAcceptanceStatus())) {
                return 3;
            }
            return 4;
        } else {
            if (Short.valueOf("1").equals(item.getAcceptanceStatus())) {
                return 1;
            }
            if (Short.valueOf("5").equals(item.getAcceptanceStatus())) {
                return 3;
            }
            if (Short.valueOf("11").equals(item.getAcceptanceStatus())) {
                return 6;
            }
            if (Short.valueOf("12").equals(item.getAcceptanceStatus())) {
                return 3;
            }
            if (Short.valueOf("13").equals(item.getAcceptanceStatus())) {
                return 4;
            }
            if (Short.valueOf("20").equals(item.getAcceptanceStatus())) {
                return 5;
            }
            if (Short.valueOf("21").equals(item.getAcceptanceStatus())) {
                return 7;
            }
            return 8;
        }
    }

    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    /**
     * 是否有后端服务，三个服务类型有一个即返回true，否则返回false
     *
     * @param projectInfoId
     * @return
     */
    private boolean isOpenBackendServer(Long projectInfoId) {
        ConfigCustomBackendDetailLimit businessSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(projectInfoId, 13);
        if (businessSwitch != null && Integer.valueOf(1).equals(businessSwitch.getOpenFlag())) {
            return true;
        }
        ConfigCustomBackendDetailLimit dataSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(projectInfoId, 14);
        if (dataSwitch != null && Integer.valueOf(1).equals(dataSwitch.getOpenFlag())) {
            return true;
        }
        ConfigCustomBackendDetailLimit interfaceSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(projectInfoId, 15);
        return dataSwitch != null && Integer.valueOf(1).equals(dataSwitch.getOpenFlag());
    }


    @Override
    public ProjectAcceptanceMenuResultVO queryMenu(QueryProjectAcceptanceMenuParam param) {
        QueryWrapper<DictProjectCheckAcceptMenu> queryWrapper = new QueryWrapper<DictProjectCheckAcceptMenu>()
                .eq("is_delete", 0);
        // 只需要一次验收
        if (param.getOnlyOneCheckFlag()) {
            queryWrapper.eq("only_one_check", 1);
        } else if (Integer.valueOf("1").equals(param.getCurrentAcceptanceTimes())) {
            // 两次验收的第一次验收
            queryWrapper.eq("first_check", 1);
        } else {
            // 两次验收的第二次验收
            queryWrapper.eq("final_check", 1);
        }
        queryWrapper.orderByAsc("sort_no");
        // 所有菜单
        List<DictProjectCheckAcceptMenu> allMenuList = dictProjectCheckAcceptMenuMapper.selectList(queryWrapper);
        // 没有后端服务时
        if (!this.isOpenBackendServer(param.getProjectInfoId())) {
            allMenuList = allMenuList.stream().filter(item -> !"BackEndServiceTeamConfirm".equals(item.getMenuCode())).collect(Collectors.toList());
        }

        // 一级菜单
        List<DictProjectCheckAcceptMenu> oneLevelMenu = allMenuList.stream().filter(item -> StringUtils.isBlank(item.getParentCode())).collect(Collectors.toList());

        // 二级菜单
        List<DictProjectCheckAcceptMenu> twoLevelMenu = allMenuList.stream().filter(item -> StringUtils.isNotBlank(item.getParentCode())).collect(Collectors.toList());

        // 二级菜单按照上级菜单分组
        Map<String, List<DictProjectCheckAcceptMenu>> groupByParentCode = twoLevelMenu.stream().collect(Collectors.groupingBy(DictProjectCheckAcceptMenu::getParentCode));

        // 展示用的一级菜单
        List<ProjectAcceptanceMenuVO> oneLeveMenuShow = oneLevelMenu.stream().map(item -> {
            ProjectAcceptanceMenuVO projectAcceptanceMenuVO = new ProjectAcceptanceMenuVO();
            projectAcceptanceMenuVO.setMenuCode(item.getMenuCode());
            projectAcceptanceMenuVO.setMenuName(item.getMenuName());
            // 当前一级菜单对应的二级菜单
            List<DictProjectCheckAcceptMenu> childMenuList = groupByParentCode.get(item.getMenuCode());
            if (CollectionUtils.isEmpty(childMenuList)) {
                projectAcceptanceMenuVO.setChildMenuList(new ArrayList<>());
            } else {
                List<ProjectAcceptanceMenuVO> twoLevelMenuShow = childMenuList.stream().map(childMenuItem -> {
                    ProjectAcceptanceMenuVO childMenu = new ProjectAcceptanceMenuVO();
                    childMenu.setMenuCode(childMenuItem.getMenuCode());
                    childMenu.setMenuName(childMenuItem.getMenuName());
                    childMenu.setChildMenuList(new ArrayList<>());
                    return childMenu;
                }).collect(Collectors.toList());
                projectAcceptanceMenuVO.setChildMenuList(twoLevelMenuShow);
            }
            return projectAcceptanceMenuVO;

        }).collect(Collectors.toList());

        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(param.getProjectInfoId());

        oneLeveMenuShow = oneLeveMenuShow.stream().filter(item -> {
            if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag()) && "SatisfactionSurveySubsequent".equals(item.getMenuCode())) {
                return false;
            }
            if (!Integer.valueOf(1).equals(projProjectInfo.getHisFlag()) && "SatisfactionSurvey".equals(item.getMenuCode())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        ProjectAcceptanceMenuResultVO projectAcceptanceMenuResultVO = new ProjectAcceptanceMenuResultVO();
        projectAcceptanceMenuResultVO.setMenuList(oneLeveMenuShow);
        if (StringUtils.isBlank(param.getMenuCode())) {
            ProjectAcceptanceMenuVO projectAcceptanceMenuVO = oneLeveMenuShow.get(0);
            if (CollectionUtils.isEmpty(projectAcceptanceMenuVO.getChildMenuList())) {
                // 没有子菜单，默认选中第一个菜单的菜单编码
                projectAcceptanceMenuResultVO.setMenuCode(projectAcceptanceMenuVO.getMenuCode());
            } else {
                // 有子菜单，默认选中子菜单的第一个菜单的菜单编码
                projectAcceptanceMenuResultVO.setMenuCode(projectAcceptanceMenuVO.getChildMenuList().get(0).getMenuCode());
            }
        } else {
            // 默认选中前端声明的菜单编码
            projectAcceptanceMenuResultVO.setMenuCode(param.getMenuCode());
        }
        return projectAcceptanceMenuResultVO;
    }

    @Override
    public InitProductFunctionResultVO initProductFunctionDetail(InitProductFunctionParam param) {
        InitProductFunctionResultVO result = new InitProductFunctionResultVO();
        // 医院等级下拉
        List<BaseIdNameResp> hospitalLevelList = Arrays.stream(HospitalLevelEnum.values()).map(item -> new BaseIdNameResp(item.getIdForLong(), item.getName())).collect(Collectors.toList());
        result.setHospitalLevelList(hospitalLevelList);
        result.setSelectedHospitalLevel(HospitalLevelEnum.SCEND_LEVEL.getIdForLong());

        // 实施产品下拉
        List<BaseIdNameResp> productList = projProductDeliverRecordMapper.queryDeliverProductIdAndNameByProjectInfoId(param.getProjectInfoId());
        result.setProductInfoList(productList);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProductFunctionUseInfoVO> queryProductFunctionUseInfo(QueryProductFunctionScoreParam3 param) {
        // 用于判断扣分明细表是否存在数据
        List<ProductFunctionUseInfoPO> projProjectDeductionDetails = projProductFunctionUseInfoMapper.queryProductFunctionUseInfoByProject(param.getProjectInfoId(), null, param.getUseStatus());
        if (!CollectionUtils.isEmpty(projProjectDeductionDetails)) {
            return projProjectDeductionDetails.stream().map(item -> {
                ProductFunctionUseInfoVO result = new ProductFunctionUseInfoVO();
                BeanUtils.copyProperties(item, result);
                result.setRequiredFlag(item.isRequiredFunction(param.getHospitalType()));

                return result;
            }).collect(Collectors.toList());
        }
        this.startCheckUseCount2(param.getProjectInfoId(), param.getCustomInfoId());
        return new ArrayList<>();
    }

    @Override
    public List<CommonScoreRecordVO> queryProductFunctionDeduction(QueryProductFunctionScoreParam2 param2) {
        QueryProductFunctionScoreParam queryProductFunctionScoreParam = new QueryProductFunctionScoreParam();
        queryProductFunctionScoreParam.setCustomInfoId(param2.getCustomInfoId());
        queryProductFunctionScoreParam.setProjectInfoId(param2.getProjectInfoId());
        queryProductFunctionScoreParam.setMenuCode(param2.getMenuCode());
        queryProductFunctionScoreParam.setOnlyDeduction(true);
        queryProductFunctionScoreParam.setOnlyOneCheckFlag(param2.getOnlyOneCheckFlag());
        queryProductFunctionScoreParam.setCurrentAcceptanceTimes(param2.getCurrentAcceptanceTimes());

        // 项目验收菜单字典
        DictProjectCheckAcceptMenu dictProjectCheckAcceptMenu = dictProjectCheckAcceptMenuMapper.getMenuByCode(queryProductFunctionScoreParam.getMenuCode());

        // 数据来源，判断查询首验还是终验的数据
        String source;
        if (queryProductFunctionScoreParam.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(queryProductFunctionScoreParam.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }

        QueryProductFunctionScoreRecordParam queryProductFunctionScoreRecordParam = new QueryProductFunctionScoreRecordParam();
        queryProductFunctionScoreRecordParam.setCustomInfoId(queryProductFunctionScoreParam.getCustomInfoId());
        queryProductFunctionScoreRecordParam.setProjectInfoId(queryProductFunctionScoreParam.getProjectInfoId());
        queryProductFunctionScoreRecordParam.setClassificationCode(dictProjectCheckAcceptMenu.getClassificationCode());
        queryProductFunctionScoreRecordParam.setSource(source);
        queryProductFunctionScoreRecordParam.setYyProductId(queryProductFunctionScoreParam.getYyProductId());
        List<ProductFunctionScoreRecordPO> productFunctionScoreRecordPOS = projProjectDeductionDetailMapper.queryProductFunctionScoreRecord(queryProductFunctionScoreRecordParam);
        List<ProductFunctionScoreRecordVO> functionScoreRecordVOS = productFunctionScoreRecordPOS.stream().map(item -> {
            ProductFunctionScoreRecordVO scoreRecordVO = new ProductFunctionScoreRecordVO();
            scoreRecordVO.setId(item.getId());
            scoreRecordVO.setCustomInfoId(item.getCustomInfoId());
            scoreRecordVO.setProjectInfoId(item.getProjectInfoId());
            scoreRecordVO.setMenuCode(queryProductFunctionScoreParam.getMenuCode());
            scoreRecordVO.setFunctionCode(item.getFunctionCode());
            scoreRecordVO.setProductName(item.getProductName());
            scoreRecordVO.setYyProductId(item.getYyProductId());
            scoreRecordVO.setFunctionName(item.getFunctionName());
            scoreRecordVO.setFunctionDesc(item.getFunctionDesc());
            scoreRecordVO.setUseCount(item.getUseCount() == null ? null : String.valueOf(item.getUseCount()));
            scoreRecordVO.setRequiredFlag(item.isRequiredFunction(queryProductFunctionScoreParam.getHospitalLevel()) ? 1 : 0);
            scoreRecordVO.setEstimatedDeduction(item.getEstimatedDeduction());
            scoreRecordVO.setPracticalDeduction(item.getPracticalDeduction());
            scoreRecordVO.setRemark(item.getRemark());
            scoreRecordVO.setOnlyOneCheckFlag(queryProductFunctionScoreParam.getOnlyOneCheckFlag());
            scoreRecordVO.setCurrentAcceptanceTimes(queryProductFunctionScoreParam.getCurrentAcceptanceTimes());
            scoreRecordVO.setDeductionType(item.getDeductionType());
            scoreRecordVO.setDeductionTypeDesc(item.getDeductionTypeDesc());
            scoreRecordVO.setAttachmentId(item.getAttachmentId());
            List<AttachmentInfoVO> collect1 = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getAttachmentId())) {
                List<String> collect = Arrays.stream(item.getAttachmentId().split(",")).collect(Collectors.toList());
                List<Long> collect3 = collect.stream().filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(collect3)) {
                    collect1 = new ArrayList<>();
                } else {
                    List<ProjProjectFile> projProjectFiles = projProjectFileMapper.selectBatchIds(collect3);
                    collect1 = projProjectFiles.stream().map(e -> new AttachmentInfoVO(e.getProjectFileId(), e.getFileName(), OBSClientUtils.getTemporaryUrl(e.getFilePath(), 3600))).collect(Collectors.toList());
                }
            }
            scoreRecordVO.setAttachmentInfoList(collect1);
            return scoreRecordVO;
        }).collect(Collectors.toList());

        List<ProductFunctionScoreRecordVO> scoreRecord = functionScoreRecordVOS.stream().filter(item -> item.getPracticalDeduction().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        return scoreRecord.stream().map(item -> {
            CommonScoreRecordVO commonScoreRecordVO = new CommonScoreRecordVO();
            commonScoreRecordVO.setId(item.getId());
            commonScoreRecordVO.setDeductionType(item.getDeductionType());
            commonScoreRecordVO.setDeductionTypeDesc(item.getDeductionTypeDesc());
            commonScoreRecordVO.setAttachmentInfoList(item.getAttachmentInfoList());
            commonScoreRecordVO.setPracticalDeduction(item.getPracticalDeduction() == null ? "" : item.getPracticalDeduction().toPlainString());
            commonScoreRecordVO.setRemark("根据预估扣分自动执行扣分".equals(item.getRemark()) ? item.getProductName() + item.getFunctionName() + item.getFunctionDesc() : item.getRemark());
            commonScoreRecordVO.setCustomInfoId(item.getCustomInfoId());
            commonScoreRecordVO.setProjectInfoId(item.getProjectInfoId());
            commonScoreRecordVO.setMenuCode(item.getMenuCode());
            commonScoreRecordVO.setOnlyOneCheckFlag(item.getOnlyOneCheckFlag());
            commonScoreRecordVO.setCurrentAcceptanceTimes(item.getCurrentAcceptanceTimes());
            return commonScoreRecordVO;
        }).collect(Collectors.toList());
    }

    private void startCheck(int currentAllowedExecuteSqlSize, ProjHospitalInfo projHospitalInfo, CustomAndProjectInfoIdParam param, List<DictProductFunction> needQuerySQL, List<SqlCheckVO> queryResult) throws Exception {
        if (projHospitalInfo == null || projHospitalInfo.getCloudHospitalId() == null) {
            log.info("项目验收查询功能点应用次数，云健康医院ID为null，终止查询");
            return;
        }
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("项目验收查询功能点应用次数，设定医院信息:{}", domainMap);

        domainHolder.refresh(domainMap);

        // 全部的需要检测的SQL
        List<SqlCheckDTO> totalCheckSqlList = new ArrayList<>();
        for (DictProductFunction dictProductFunction : needQuerySQL) {
            // 修改SQL脚本，讲脚本中的###替换为实际的云健康医院ID
            String sqlStr = dictProductFunction.getCheckSql().replace("###", projHospitalInfo.getCloudHospitalId().toString());

            SqlCheckDTO dto = new SqlCheckDTO();
            dto.setProjectVsApplicationCheckId(dictProductFunction.getId());
            dto.setProductId(dictProductFunction.getYyProductId());
            dto.setProductName("");
            dto.setFunctionName(dictProductFunction.getFunctionName());
            dto.setOrgId(projHospitalInfo.getOrgId());
            dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
            // 云健康WAF防火墙会过滤危险的SQL，为避免请求被WAF防火墙拦截，将SQL进行加密
            dto.setSqlStr(AesUtil.encrypt(sqlStr));
            dto.setCustomInfoId(param.getCustomInfoId());
            dto.setProjectInfoId(param.getProjectInfoId());
            dto.setFunctionCode(dictProductFunction.getFunctionCode());
            totalCheckSqlList.add(dto);
        }
        ResponseResult<List<SqlCheckVO>> result = new ResponseResult<>();
        try {
            // 本次检测的SQL，执行分批次检测，防止SQL超时，方法参考自杜旭在老系统写的方法（ProjectVsApplicationCheckServiceImpl的startCheck方法）
            List<SqlCheckDTO> currentCheckSqlList = new ArrayList<>();
            for (int i = 0; i < totalCheckSqlList.size(); i++) {
                currentCheckSqlList.add(totalCheckSqlList.get(i));
                // 本次检测的SQL长度等于允许执行的SQL长度或者本次检测的SQL长度等于全部的需要检测的SQL长度
                if (currentCheckSqlList.size() == currentAllowedExecuteSqlSize || currentCheckSqlList.size() == totalCheckSqlList.size()) {

                    SqlSelectLoginDTO sqlSelectLoginDTO = new SqlSelectLoginDTO();
                    sqlSelectLoginDTO.setHospitalIdList(new ArrayList<>());
                    sqlSelectLoginDTO.setSheetCodeList(new ArrayList<>());

                    SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
                    sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
                    sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
                    sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
                    sqlCheckApiDTO.setDtoList(currentCheckSqlList);
                    sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
                    sqlCheckApiDTO.setTaskDtoList(new ArrayList<>());
                    log.info("项目验收查询功能点应用次数，调用快速实施平台的参数={}", JSONUtil.toJsonStr(sqlCheckApiDTO));
                    result = applicationCheckApi.startCheck(sqlCheckApiDTO);
                    log.info("项目验收查询功能点应用次数，快速实施平台返回结果={}", result);
                    if ("0000".equals(result.getCode())) {
                        List<SqlCheckVO> voList = result.getData();
                        if (!CollectionUtils.isEmpty(voList)) {
                            queryResult.addAll(voList);
                        }
                    }
                    currentCheckSqlList = new ArrayList<>();
                }
            }
        } catch (Exception e) {
            if (currentAllowedExecuteSqlSize > 8) {
                startCheck(currentAllowedExecuteSqlSize / 2, projHospitalInfo, param, needQuerySQL, queryResult);
            } else {
                log.error("项目验收查询功能点应用次数，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                throw new Exception(result.getMessage() + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDeduction(SaveDeductionParam param) {
        if (StringUtils.isBlank(param.getPracticalDeduction()) && param.getRemark() == null) {
            return true;
        }

        // 菜单字典
        DictProjectCheckAcceptMenu dictProjectCheckAcceptMenu = dictProjectCheckAcceptMenuMapper.getMenuByCode(param.getMenuCode());

        // 来源：首验/终验
        String source;
        if (param.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }

        // 主键非空，修改原功能点扣分记录
        UpdateDeductionParam projProjectDeductionDetail = new UpdateDeductionParam();
        projProjectDeductionDetail.setId(param.getId());
        if (StringUtils.isNotBlank(param.getPracticalDeduction())) {
            try {
                projProjectDeductionDetail.setPracticalDeduction(new BigDecimal(param.getPracticalDeduction()));
            } catch (Exception e) {
                throw new RuntimeException("扣分仅允许输入数字");
            }
        }

        if (param.getRemark() != null) {
            projProjectDeductionDetail.setRemark(param.getRemark());
        }

        int update = projProjectDeductionDetailMapper.updateDeductionById(projProjectDeductionDetail);
        if (1 != update) {
            throw new IllegalArgumentException("修改应用功能点扣分记录，修改数据库数据失败");
        }
        // 2.更新得分主表数据

        // 2.1 查询当前分类有无评分记录
        List<ProjProjectClassificationScore> projProjectClassificationScores = projProjectClassificationScoreMapper.getProjectClassificationScore(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);
        if (!CollectionUtils.isEmpty(projProjectClassificationScores)) {
            // 扣分记录明细
            List<ProjProjectDeductionDetail> projProjectDeductionDetailList = projProjectDeductionDetailMapper.getFunctionDeductionDetail(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);

            // 根据扣分记录明细表的实际扣分计算当前分类下的实际扣分
            BigDecimal practicalDeduction = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(projProjectDeductionDetailList)) {
                practicalDeduction = projProjectDeductionDetailList.stream().map(ProjProjectDeductionDetail::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            BigDecimal max = (new BigDecimal(String.valueOf(projProjectClassificationScores.get(0).getTotalScore())).subtract(practicalDeduction)).max(BigDecimal.ZERO);
            ProjProjectClassificationScore updateInfo = new ProjProjectClassificationScore();
            updateInfo.setPracticalDeduction(practicalDeduction);
            updateInfo.setFinalScore(max);
            int update1 = projProjectClassificationScoreMapper.update(updateInfo, new QueryWrapper<ProjProjectClassificationScore>().eq("id", projProjectClassificationScores.get(0).getId()));
            if (1 != update1) {
                throw new IllegalArgumentException("修改应用功能点扣分记录，更新得分主表数据异常");
            }
            return true;
        }
        return true;
    }


    @Override
    public ClassificationScoreVO queryClassificationScore(QueryClassificationScoreParam param) {
        // 当前菜单
        DictProjectCheckAcceptMenu dictProjectCheckAcceptMenu = dictProjectCheckAcceptMenuMapper.getMenuByCode(param.getMenuCode());

        // 判断首验还是终验
        String source;
        if (param.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }
        // 查询当前分类有无评分记录
        List<ProjProjectClassificationScore> projProjectClassificationScores = projProjectClassificationScoreMapper.getProjectClassificationScore(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);

        // 评分验收规则字典表
        DictProjectAcceptRule dictProjectAcceptRule = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode(dictProjectCheckAcceptMenu.getClassificationCode());

        // 判断应该获取哪个总分
        Integer totalScore;
        if (param.getOnlyOneCheckFlag()) {
            totalScore = dictProjectAcceptRule.getScoreWeight();
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            totalScore = dictProjectAcceptRule.getFinalScore();
        } else {
            totalScore = dictProjectAcceptRule.getFirstScore();
        }

        // 根据扣分记录明细表的实际扣分计算当前分类下的实际扣分
        BigDecimal practicalDeduction = BigDecimal.ZERO;

        BigDecimal finalScore = BigDecimal.ZERO;

        ClassificationScoreVO classificationScoreVO = new ClassificationScoreVO();
        // 快速实施应用
        if ("RapidImplementationApplication".equals(dictProjectCheckAcceptMenu.getClassificationCode())) {
            classificationScoreVO.setShowImplementApplication(true);
            List<DocumentScoreRecordPO> documentScoreRecord = projDeductionDetailDocumentMapper.getDocumentScoreRecord(param.getProjectInfoId());
            if (!CollectionUtils.isEmpty(documentScoreRecord)) {
                BigDecimal documentDeduction = documentScoreRecord.stream().map(DocumentScoreRecordPO::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                practicalDeduction = practicalDeduction.add(documentDeduction);
                classificationScoreVO.setDocumentDeduction(documentDeduction.stripTrailingZeros().toPlainString());
            }

            List<ProcessScoreRecordPO> processScoreRecord = projDeductionDetailProcessMapper.getProcessScoreRecord(param.getProjectInfoId());

            if (!CollectionUtils.isEmpty(processScoreRecord)) {
                BigDecimal processDeduction = processScoreRecord.stream().filter(item -> item.getPracticalDeduction() != null).filter(FilterUtil.distinctByKey(ProcessScoreRecordPO::getYyProductId)).map(ProcessScoreRecordPO::getPracticalDeduction).reduce(BigDecimal.ZERO, BigDecimal::add);
                practicalDeduction = practicalDeduction.add(processDeduction);
                classificationScoreVO.setProcessDeduction(processDeduction.stripTrailingZeros().toPlainString());

            }
            List<CommonScoreRecordPO> commonScoreRecord = projDeductionDetailCommonMapper.getCommonScoreRecord(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode());
            if (!CollectionUtils.isEmpty(commonScoreRecord)) {
                BigDecimal commonDeduction = commonScoreRecord.stream().map(CommonScoreRecordPO::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                practicalDeduction = practicalDeduction.add(commonDeduction);
                classificationScoreVO.setOfflineDeduction(commonDeduction.stripTrailingZeros().toPlainString());
            }

            finalScore = (new BigDecimal(String.valueOf(totalScore)).subtract(practicalDeduction)).max(BigDecimal.ZERO);

        }
        // 产品功能应用
        if ("ProductFunctionApplication".equals(dictProjectCheckAcceptMenu.getClassificationCode())) {
            // 扣分记录明细
            List<ProjProjectDeductionDetail> projProjectDeductionDetailList = projProjectDeductionDetailMapper.getFunctionDeductionDetail(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);

            if (!CollectionUtils.isEmpty(projProjectDeductionDetailList)) {
                practicalDeduction = projProjectDeductionDetailList.stream().map(ProjProjectDeductionDetail::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            finalScore = (new BigDecimal(String.valueOf(totalScore)).subtract(practicalDeduction)).max(BigDecimal.ZERO);
        }

        // 设备接口对接
        if ("EquipmentInterface".equals(dictProjectCheckAcceptMenu.getClassificationCode())) {
            List<CommonScoreRecordPO> commonScoreRecord = projDeductionDetailCommonMapper.getCommonScoreRecord(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode());
            if (!CollectionUtils.isEmpty(commonScoreRecord)) {
                BigDecimal commonDeduction = commonScoreRecord.stream().map(CommonScoreRecordPO::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                practicalDeduction = practicalDeduction.add(commonDeduction);
            }
            finalScore = (new BigDecimal(String.valueOf(totalScore)).subtract(practicalDeduction)).max(BigDecimal.ZERO);
        }

        // 数据统计
        if ("DataStatistics".equals(dictProjectCheckAcceptMenu.getClassificationCode())) {
            List<CommonScoreRecordPO> commonScoreRecord = projDeductionDetailCommonMapper.getCommonScoreRecord(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode());
            if (!CollectionUtils.isEmpty(commonScoreRecord)) {
                BigDecimal commonDeduction = commonScoreRecord.stream().map(CommonScoreRecordPO::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                practicalDeduction = practicalDeduction.add(commonDeduction);
            }
            finalScore = (new BigDecimal(String.valueOf(totalScore)).subtract(practicalDeduction)).max(BigDecimal.ZERO);
        }
        // 满意度调查
        if ("SatisfactionSurvey".equals(dictProjectCheckAcceptMenu.getClassificationCode())) {
            classificationScoreVO.setSatisfactionSurveyRule("计算公式=Σ每个调查满意得分×15/(5×回访人数)");
            DictSatisfactionSurveyLevel verySatisfied = dictSatisfactionSurveyLevelMapper.getSatisfactionSurveyLevelByCode("verySatisfied");
            List<SatisfactionSurveyScoreRecordVO> satisfactionScoreRecord = projDeductionDetailSatisfactionMapper.getRevisitSatisfactionScoreRecord(param.getProjectInfoId());
            if (CollectionUtils.isEmpty(satisfactionScoreRecord)) {
                practicalDeduction = BigDecimal.ZERO;
                finalScore = BigDecimal.ZERO;
            } else {
                // 院长打分
                SatisfactionSurveyScoreRecordVO director = satisfactionScoreRecord.stream()
                        .filter(item -> Boolean.TRUE.equals(item.getRevisitFlag()) && "director".equals(item.getRoleCode())).findFirst().orElse(null);

                // 分管院长打分
                SatisfactionSurveyScoreRecordVO divisionDirector = satisfactionScoreRecord.stream()
                        .filter(item -> Boolean.TRUE.equals(item.getRevisitFlag()) && "division_director".equals(item.getRoleCode())).findFirst().orElse(null);

                // 院长打分为不满意或很不满意
                if (director != null && this.isDissatisfied(director)) {
                    finalScore = new BigDecimal(director.getLevelScore()).multiply(new BigDecimal(String.valueOf(totalScore))).divide(new BigDecimal(String.valueOf(verySatisfied.getLevelScore())), 0, RoundingMode.HALF_UP);
                    practicalDeduction = new BigDecimal(String.valueOf(totalScore)).subtract(finalScore);
                } else if (director == null && divisionDirector != null && this.isDissatisfied(divisionDirector)) {
                    finalScore = new BigDecimal(divisionDirector.getLevelScore()).multiply(new BigDecimal(String.valueOf(totalScore))).divide(new BigDecimal(String.valueOf(verySatisfied.getLevelScore())), 0, RoundingMode.HALF_UP);
                    practicalDeduction = new BigDecimal(String.valueOf(totalScore)).subtract(finalScore);
                } else {
                    // 评价总分满分
                    BigDecimal multiply = new BigDecimal(String.valueOf(verySatisfied.getLevelScore())).multiply(new BigDecimal(String.valueOf(satisfactionScoreRecord.size())));

                    // 评价总得分
                    int sum = satisfactionScoreRecord.stream().mapToInt(item -> Convert.toInt(item.getLevelScore(), 0)).sum();

                    finalScore = new BigDecimal(String.valueOf(sum)).multiply(new BigDecimal(String.valueOf(totalScore))).divide(multiply, 0, RoundingMode.HALF_UP);
                    practicalDeduction = new BigDecimal(String.valueOf(totalScore)).subtract(finalScore);
                }
            }
        }


        classificationScoreVO.setTotalScore(totalScore);
        classificationScoreVO.setPracticalDeduction(practicalDeduction.stripTrailingZeros().toPlainString());
        classificationScoreVO.setFinalScore(finalScore.stripTrailingZeros().toPlainString());
        // 评分记录非空，展示评分记录表中的数据
        if (!CollectionUtils.isEmpty(projProjectClassificationScores)) {
            ProjProjectClassificationScore projProjectClassificationScore = projProjectClassificationScores.get(0);
            classificationScoreVO.setId(projProjectClassificationScore.getId());
            classificationScoreVO.setTotalScore(projProjectClassificationScore.getTotalScore());
            classificationScoreVO.setPracticalDeduction(projProjectClassificationScore.getPracticalDeduction().stripTrailingZeros().toPlainString());
            classificationScoreVO.setFinalScore(projProjectClassificationScore.getFinalScore().stripTrailingZeros().toPlainString());
            classificationScoreVO.setRemark(projProjectClassificationScore.getRemark());
        }
        return classificationScoreVO;
    }

    private boolean isDissatisfied(SatisfactionSurveyScoreRecordVO surveyScoreRecordVO) {
        return "dissatisfied".equals(surveyScoreRecordVO.getLevelCode()) || "veryDissatisfied".equals(surveyScoreRecordVO.getLevelCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveClassificationScore(SaveClassificationScoreParam param) {
        // 修改
        if (param.getId() != null) {
            QueryWrapper<ProjProjectClassificationScore> queryWrapper = new QueryWrapper<ProjProjectClassificationScore>().eq("id", param.getId());
            ProjProjectClassificationScore projProjectClassificationScore = projProjectClassificationScoreMapper.selectOne(queryWrapper);
            ProjProjectClassificationScore projProjectDeductionDetail = new ProjProjectClassificationScore();
            projProjectDeductionDetail.setPracticalDeduction(param.getPracticalDeduction());
            BigDecimal max = (new BigDecimal(String.valueOf(projProjectClassificationScore.getTotalScore())).subtract(param.getPracticalDeduction())).max(BigDecimal.ZERO);
            projProjectDeductionDetail.setFinalScore(max);
            projProjectDeductionDetail.setRemark(param.getRemark());
            int update = projProjectClassificationScoreMapper.update(projProjectDeductionDetail, queryWrapper);
            return 1 == update;
        }

        Date now = new Date();
        ProjProjectClassificationScore projProjectDeductionDetail = new ProjProjectClassificationScore();
        projProjectDeductionDetail.setId(SnowFlakeUtil.getId());
        projProjectDeductionDetail.setIsDelete(0);
        projProjectDeductionDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setCreateTime(now);
        projProjectDeductionDetail.setUpdateTime(now);
        projProjectDeductionDetail.setCustomInfoId(param.getCustomInfoId());
        projProjectDeductionDetail.setProjectInfoId(param.getProjectInfoId());
        DictProjectCheckAcceptMenu dictProjectCheckAcceptMenu = dictProjectCheckAcceptMenuMapper.getMenuByCode(param.getMenuCode());
        projProjectDeductionDetail.setClassificationCode(dictProjectCheckAcceptMenu.getClassificationCode());

        DictProjectAcceptRule dictProjectAcceptRule = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode(dictProjectCheckAcceptMenu.getClassificationCode());

        Integer totalScore;
        if (param.getOnlyOneCheckFlag()) {
            totalScore = dictProjectAcceptRule.getScoreWeight();
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            totalScore = dictProjectAcceptRule.getFinalScore();
        } else {
            totalScore = dictProjectAcceptRule.getFirstScore();
        }

        projProjectDeductionDetail.setTotalScore(totalScore);
        projProjectDeductionDetail.setPracticalDeduction(param.getPracticalDeduction());
        BigDecimal max = (new BigDecimal(String.valueOf(totalScore)).subtract(param.getPracticalDeduction())).max(BigDecimal.ZERO);
        projProjectDeductionDetail.setFinalScore(max);
        projProjectDeductionDetail.setRemark(param.getRemark());

        String source;
        if (param.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }
        projProjectDeductionDetail.setSource(source);
        int insert = projProjectClassificationScoreMapper.insert(projProjectDeductionDetail);
        return 1 == insert;
    }

    @Override
    public SimulationStatisticalDataVO queryScoreSummary(QueryScoreSummaryParam param) {
        // 两次验收的第一次验收的评分记录
        List<ProjProjectClassificationScorePO> firstRecord = projProjectClassificationScoreMapper.getProjectClassificationScoreInfo(param.getProjectInfoId(), "first");

        // 只有一次验收或者两次验收的第二次验收的评分记录
        List<ProjProjectClassificationScorePO> finalRecord = projProjectClassificationScoreMapper.getProjectClassificationScoreInfo(param.getProjectInfoId(), "final");

        DictProjectAcceptRule rapidImplementationApplication = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode("RapidImplementationApplication");
        DictProjectAcceptRule cloudApplication = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode("CloudApplication");
        DictProjectAcceptRule productFunctionApplication = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode("ProductFunctionApplication");
        DictProjectAcceptRule equipmentInterface = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode("EquipmentInterface");
        DictProjectAcceptRule dataStatistics = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode("DataStatistics");
        DictProjectAcceptRule satisfactionSurvey = dictProjectAcceptRuleMapper.getAcceptRuleByClassificationCode("SatisfactionSurvey");

        // 快速实施应用
        ProjProjectClassificationScorePO firstItem1 = firstRecord.stream()
                .filter(score1 -> "RapidImplementationApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("RapidImplementationApplication"));

        // 云健康应用-产品应用功能点
        ProjProjectClassificationScorePO firstItem2 = firstRecord.stream()
                .filter(score1 -> "ProductFunctionApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("ProductFunctionApplication"));

        // 云健康应用-设备接口
        ProjProjectClassificationScorePO firstItem3 = firstRecord.stream()
                .filter(score1 -> "EquipmentInterface".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("EquipmentInterface"));

        // 云健康应用-数据统计
        ProjProjectClassificationScorePO firstItem4 = firstRecord.stream()
                .filter(score1 -> "DataStatistics".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("DataStatistics"));

        // 满意度调查
        ProjProjectClassificationScorePO firstItem5 = firstRecord.stream()
                .filter(score1 -> "SatisfactionSurvey".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("SatisfactionSurvey"));

        // 快速实施应用
        ProjProjectClassificationScorePO finalItem1 = finalRecord.stream()
                .filter(score1 -> "RapidImplementationApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("RapidImplementationApplication"));

        // 云健康应用-产品应用功能点
        ProjProjectClassificationScorePO finalItem2 = finalRecord.stream()
                .filter(score1 -> "ProductFunctionApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("ProductFunctionApplication"));

        // 云健康应用-设备接口
        ProjProjectClassificationScorePO finalItem3 = finalRecord.stream()
                .filter(score1 -> "EquipmentInterface".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("EquipmentInterface"));

        // 云健康应用-数据统计
        ProjProjectClassificationScorePO finalItem4 = finalRecord.stream()
                .filter(score1 -> "DataStatistics".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("DataStatistics"));

        // 满意度调查
        ProjProjectClassificationScorePO finalItem5 = finalRecord.stream()
                .filter(score1 -> "SatisfactionSurvey".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("SatisfactionSurvey"));

        // 只需要一次验收
        if (param.getOnlyOneCheckFlag()) {
            List<ExcelHeaderRowDTO> headerInfo = new ArrayList<>();
//            headerInfo.add(new ExcelHeaderRowDTO(1, "评分标准", "scoreStandard"));
            headerInfo.add(new ExcelHeaderRowDTO(2, "验收扣分", "deduction"));
            headerInfo.add(new ExcelHeaderRowDTO(3, "验收得分", "score"));
            headerInfo.add(new ExcelHeaderRowDTO(4, "验收备注", "remark"));

            SimulationStatisticalDataVO simulationStatisticalDataVO = new SimulationStatisticalDataVO();
            simulationStatisticalDataVO.setHeaderInfo(headerInfo);

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> item1 = new HashMap<>();
            item1.put("classificationName", finalItem1.getClassificationName());
            item1.put("scoreWeight", finalItem1.getScoreWeight());
            item1.put("subitemWeight", finalItem1.getScoreWeight());
            item1.put("scoreStandard", finalItem1.getScoreStandard());
            item1.put("deduction", finalItem1.getPracticalDeduction());
            item1.put("score", finalItem1.getScore());
            item1.put("remark", finalItem1.getRemark());
            list.add(item1);

            Map<String, Object> item2 = new HashMap<>();

            item2.put("classificationName", cloudApplication.getClassificationName());
            item2.put("scoreWeight", cloudApplication.getScoreWeight());
            item2.put("subitemWeight", finalItem2.getScoreWeight());
            item2.put("scoreStandard", finalItem2.getScoreStandard());
            item2.put("deduction", finalItem2.getPracticalDeduction());
            item2.put("score", finalItem2.getScore());
            item2.put("remark", finalItem2.getRemark());
            list.add(item2);

            Map<String, Object> item3 = new HashMap<>();

            item3.put("classificationName", cloudApplication.getClassificationName());
            item3.put("scoreWeight", cloudApplication.getScoreWeight());
            item3.put("subitemWeight", finalItem3.getScoreWeight());
            item3.put("scoreStandard", finalItem3.getScoreStandard());
            item3.put("deduction", finalItem3.getPracticalDeduction());
            item3.put("score", finalItem3.getScore());
            item3.put("remark", finalItem3.getRemark());
            list.add(item3);

            Map<String, Object> item4 = new HashMap<>();

            item4.put("classificationName", cloudApplication.getClassificationName());
            item4.put("scoreWeight", cloudApplication.getScoreWeight());
            item4.put("subitemWeight", finalItem4.getScoreWeight());
            item4.put("scoreStandard", finalItem4.getScoreStandard());
            item4.put("deduction", finalItem4.getPracticalDeduction());
            item4.put("score", finalItem4.getScore());
            item4.put("remark", finalItem4.getRemark());
            list.add(item4);

            Map<String, Object> item5 = new HashMap<>();

            item5.put("classificationName", finalItem5.getClassificationName());
            item5.put("scoreWeight", finalItem5.getScoreWeight());
            item5.put("subitemWeight", finalItem5.getScoreWeight());
            item5.put("scoreStandard", finalItem5.getScoreStandard());
            item5.put("deduction", finalItem5.getPracticalDeduction());
            item5.put("score", finalItem5.getScore());
            item5.put("remark", finalItem5.getRemark());
            list.add(item5);

            Map<String, Object> item6 = new HashMap<>();
            item6.put("classificationName", "合计");
            item6.put("scoreWeight", finalItem1.getScoreWeight() + cloudApplication.getScoreWeight() + finalItem5.getScoreWeight());
            item6.put("subitemWeight", finalItem1.getScoreWeight() + finalItem2.getScoreWeight() + finalItem3.getScoreWeight() + finalItem4.getScoreWeight() + finalItem5.getScoreWeight());
            item6.put("scoreStandard", null);
            item6.put("deduction", finalItem1.getPracticalDeduction().add(finalItem2.getPracticalDeduction()).add(finalItem3.getPracticalDeduction()).add(finalItem4.getPracticalDeduction()).add(finalItem5.getPracticalDeduction()));
            item6.put("score", finalItem1.getScore().add(finalItem2.getScore()).add(finalItem3.getScore()).add(finalItem4.getScore()).add(finalItem5.getScore()));
            item6.put("remark", null);
            list.add(item6);

            simulationStatisticalDataVO.setData(list);
            return simulationStatisticalDataVO;
        }
        List<ExcelHeaderRowDTO> headerInfo = new ArrayList<>();
        headerInfo.add(new ExcelHeaderRowDTO(1, "第一次", "firstWeight"));
        headerInfo.add(new ExcelHeaderRowDTO(2, "第二次", "secondWeight"));
//        headerInfo.add(new ExcelHeaderRowDTO(3, "评分标准", "scoreStandard"));
        headerInfo.add(new ExcelHeaderRowDTO(4, "首验扣分", "firstDeduction"));
        headerInfo.add(new ExcelHeaderRowDTO(5, "首验得分", "firstScore"));
        headerInfo.add(new ExcelHeaderRowDTO(6, "首验备注", "firstRemark"));
        // 需要两次验收的第一次验收
        if (Integer.valueOf(1).equals(param.getCurrentAcceptanceTimes())) {
            SimulationStatisticalDataVO simulationStatisticalDataVO = new SimulationStatisticalDataVO();
            simulationStatisticalDataVO.setHeaderInfo(headerInfo);

            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> item1 = new HashMap<>();
            item1.put("classificationName", rapidImplementationApplication.getClassificationName());
            item1.put("scoreWeight", rapidImplementationApplication.getScoreWeight());
            item1.put("subitemWeight", rapidImplementationApplication.getScoreWeight());
            item1.put("scoreStandard", rapidImplementationApplication.getScoreStandard());
            item1.put("firstWeight", rapidImplementationApplication.getFirstScore());
            item1.put("secondWeight", rapidImplementationApplication.getFinalScore());
            item1.put("firstDeduction", firstItem1.getPracticalDeduction());
            item1.put("firstScore", firstItem1.getScore());
            item1.put("firstRemark", firstItem1.getRemark());
            list.add(item1);

            Map<String, Object> item2 = new HashMap<>();
            item2.put("classificationName", cloudApplication.getClassificationName());
            item2.put("scoreWeight", cloudApplication.getScoreWeight());
            item2.put("subitemWeight", firstItem2.getScoreWeight());
            item2.put("scoreStandard", firstItem2.getScoreStandard());
            item2.put("firstWeight", firstItem2.getFirstScore());
            item2.put("secondWeight", firstItem2.getFinalScore());
            item2.put("firstDeduction", firstItem2.getPracticalDeduction());
            item2.put("firstScore", firstItem2.getScore());
            item2.put("firstRemark", firstItem2.getRemark());
            list.add(item2);

            Map<String, Object> item3 = new HashMap<>();
            item3.put("classificationName", cloudApplication.getClassificationName());
            item3.put("scoreWeight", cloudApplication.getScoreWeight());
            item3.put("subitemWeight", firstItem3.getScoreWeight());
            item3.put("scoreStandard", firstItem3.getScoreStandard());
            item3.put("firstWeight", firstItem3.getFirstScore());
            item3.put("secondWeight", firstItem3.getFinalScore());
            item3.put("firstDeduction", firstItem3.getPracticalDeduction());
            item3.put("firstScore", firstItem3.getScore());
            item3.put("firstRemark", firstItem3.getRemark());
            list.add(item3);

            Map<String, Object> item4 = new HashMap<>();
            item4.put("classificationName", cloudApplication.getClassificationName());
            item4.put("scoreWeight", cloudApplication.getScoreWeight());
            item4.put("subitemWeight", firstItem4.getScoreWeight());
            item4.put("scoreStandard", firstItem4.getScoreStandard());
            item4.put("firstWeight", firstItem4.getFirstScore());
            item4.put("secondWeight", firstItem4.getFinalScore());
            item4.put("firstDeduction", firstItem4.getPracticalDeduction());
            item4.put("firstScore", firstItem4.getScore());
            item4.put("firstRemark", firstItem4.getRemark());
            list.add(item4);

            Map<String, Object> item5 = new HashMap<>();
            item5.put("classificationName", satisfactionSurvey.getClassificationName());
            item5.put("scoreWeight", satisfactionSurvey.getScoreWeight());
            item5.put("subitemWeight", firstItem5.getScoreWeight());
            item5.put("scoreStandard", firstItem5.getScoreStandard());
            item5.put("firstWeight", firstItem5.getFirstScore());
            item5.put("secondWeight", firstItem5.getFinalScore());
            item5.put("firstDeduction", firstItem5.getPracticalDeduction());
            item5.put("firstScore", firstItem5.getScore());
            item5.put("firstRemark", firstItem5.getRemark());
            list.add(item5);

            Map<String, Object> item6 = new HashMap<>();
            item6.put("classificationName", "合计");
            item6.put("scoreWeight", rapidImplementationApplication.getScoreWeight() + cloudApplication.getScoreWeight() + satisfactionSurvey.getScoreWeight());
            item6.put("subitemWeight", firstItem1.getScoreWeight() + firstItem2.getScoreWeight() + firstItem3.getScoreWeight() + firstItem4.getScoreWeight() + firstItem5.getScoreWeight());
            item6.put("scoreStandard", null);
            item6.put("firstWeight", firstItem1.getFirstScore() + firstItem2.getFirstScore() + firstItem3.getFirstScore() + firstItem4.getFirstScore() + firstItem5.getFirstScore());
            item6.put("secondWeight", firstItem1.getFinalScore() + firstItem2.getFinalScore() + firstItem3.getFinalScore() + firstItem4.getFinalScore() + firstItem5.getFinalScore());
            item6.put("firstDeduction", firstItem1.getPracticalDeduction().add(firstItem2.getPracticalDeduction()).add(firstItem3.getPracticalDeduction()).add(firstItem4.getPracticalDeduction()).add(firstItem5.getPracticalDeduction()));
            item6.put("firstScore", firstItem1.getScore().add(firstItem2.getScore()).add(firstItem3.getScore()).add(firstItem4.getScore()).add(firstItem5.getScore()));
            list.add(item6);
            simulationStatisticalDataVO.setData(list);
            return simulationStatisticalDataVO;
        }

        headerInfo.add(new ExcelHeaderRowDTO(7, "终验扣分", "finalDeduction"));
        headerInfo.add(new ExcelHeaderRowDTO(8, "终验得分", "finalScore"));
        headerInfo.add(new ExcelHeaderRowDTO(9, "终验备注", "finalRemark"));
        headerInfo.add(new ExcelHeaderRowDTO(9, "验收总分", "score"));

        SimulationStatisticalDataVO simulationStatisticalDataVO = new SimulationStatisticalDataVO();
        simulationStatisticalDataVO.setHeaderInfo(headerInfo);

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> item1 = new HashMap<>();
        item1.put("classificationName", rapidImplementationApplication.getClassificationName());
        item1.put("scoreWeight", rapidImplementationApplication.getScoreWeight());
        item1.put("subitemWeight", rapidImplementationApplication.getScoreWeight());
        item1.put("scoreStandard", rapidImplementationApplication.getScoreStandard());
        item1.put("firstWeight", rapidImplementationApplication.getFirstScore());
        item1.put("secondWeight", rapidImplementationApplication.getFinalScore());
        item1.put("firstDeduction", firstItem1.getPracticalDeduction());
        item1.put("firstScore", firstItem1.getScore());
        item1.put("firstRemark", firstItem1.getRemark());
        item1.put("finalDeduction", finalItem1.getPracticalDeduction());
        item1.put("finalScore", finalItem1.getScore());
        item1.put("finalRemark", finalItem1.getRemark());
        item1.put("score", firstItem1.getScore().add(finalItem1.getScore()).stripTrailingZeros().stripTrailingZeros());
        list.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("classificationName", cloudApplication.getClassificationName());
        item2.put("scoreWeight", cloudApplication.getScoreWeight());
        item2.put("subitemWeight", firstItem2.getScoreWeight());
        item2.put("scoreStandard", firstItem2.getScoreStandard());
        item2.put("firstWeight", firstItem2.getFirstScore());
        item2.put("secondWeight", firstItem2.getFinalScore());
        item2.put("firstDeduction", firstItem2.getPracticalDeduction());
        item2.put("firstScore", firstItem2.getScore());
        item2.put("firstRemark", firstItem2.getRemark());
        item2.put("finalDeduction", finalItem2.getPracticalDeduction());
        item2.put("finalScore", finalItem2.getScore());
        item2.put("finalRemark", finalItem2.getRemark());
        item2.put("score", firstItem2.getScore().add(finalItem2.getScore()).stripTrailingZeros().stripTrailingZeros());
        list.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("classificationName", cloudApplication.getClassificationName());
        item3.put("scoreWeight", cloudApplication.getScoreWeight());
        item3.put("subitemWeight", firstItem3.getScoreWeight());
        item3.put("scoreStandard", firstItem3.getScoreStandard());
        item3.put("firstWeight", firstItem3.getFirstScore());
        item3.put("secondWeight", firstItem3.getFinalScore());
        item3.put("firstDeduction", firstItem3.getPracticalDeduction());
        item3.put("firstScore", firstItem3.getScore());
        item3.put("firstRemark", firstItem3.getRemark());
        item3.put("finalDeduction", finalItem3.getPracticalDeduction());
        item3.put("finalScore", finalItem3.getScore());
        item3.put("finalRemark", finalItem3.getRemark());
        item3.put("score", firstItem3.getScore().add(finalItem3.getScore()).stripTrailingZeros().toPlainString());
        list.add(item3);

        Map<String, Object> item4 = new HashMap<>();
        item4.put("classificationName", cloudApplication.getClassificationName());
        item4.put("scoreWeight", cloudApplication.getScoreWeight());
        item4.put("subitemWeight", firstItem4.getScoreWeight());
        item4.put("scoreStandard", firstItem4.getScoreStandard());
        item4.put("firstWeight", firstItem4.getFirstScore());
        item4.put("secondWeight", firstItem4.getFinalScore());
        item4.put("firstDeduction", firstItem4.getPracticalDeduction());
        item4.put("firstScore", firstItem4.getScore());
        item4.put("firstRemark", firstItem4.getRemark());
        item4.put("finalDeduction", finalItem4.getPracticalDeduction());
        item4.put("finalScore", finalItem4.getScore());
        item4.put("finalRemark", finalItem4.getRemark());
        item4.put("score", firstItem4.getScore().add(finalItem4.getScore()).stripTrailingZeros().stripTrailingZeros());
        list.add(item4);

        Map<String, Object> item5 = new HashMap<>();
        item5.put("classificationName", satisfactionSurvey.getClassificationName());
        item5.put("scoreWeight", satisfactionSurvey.getScoreWeight());
        item5.put("subitemWeight", firstItem5.getScoreWeight());
        item5.put("scoreStandard", firstItem5.getScoreStandard());
        item5.put("firstWeight", firstItem5.getFirstScore());
        item5.put("secondWeight", firstItem5.getFinalScore());
        item5.put("firstDeduction", firstItem5.getPracticalDeduction());
        item5.put("firstScore", firstItem5.getScore());
        item5.put("firstRemark", firstItem5.getRemark());
        item5.put("finalDeduction", finalItem5.getPracticalDeduction());
        item5.put("finalScore", finalItem5.getScore());
        item5.put("finalRemark", finalItem5.getRemark());
        item5.put("score", firstItem5.getScore().add(finalItem5.getScore()).stripTrailingZeros().stripTrailingZeros());
        list.add(item5);

        Map<String, Object> item6 = new HashMap<>();
        item6.put("classificationName", "合计");
        item6.put("scoreWeight", rapidImplementationApplication.getScoreWeight() + cloudApplication.getScoreWeight() + satisfactionSurvey.getScoreWeight());
        item6.put("subitemWeight", firstItem1.getScoreWeight() + firstItem2.getScoreWeight() + firstItem3.getScoreWeight() + firstItem4.getScoreWeight() + firstItem5.getScoreWeight());
        item6.put("scoreStandard", null);
        item6.put("firstWeight", firstItem1.getFirstScore() + firstItem2.getFirstScore() + firstItem3.getFirstScore() + firstItem4.getFirstScore() + firstItem5.getFirstScore());
        item6.put("secondWeight", firstItem1.getFinalScore() + firstItem2.getFinalScore() + firstItem3.getFinalScore() + firstItem4.getFinalScore() + firstItem5.getFinalScore());
        item6.put("firstDeduction", firstItem1.getPracticalDeduction().add(firstItem2.getPracticalDeduction()).add(firstItem3.getPracticalDeduction()).add(firstItem4.getPracticalDeduction()).add(firstItem5.getPracticalDeduction()));
        item6.put("firstScore", firstItem1.getScore().add(firstItem2.getScore()).add(firstItem3.getScore()).add(firstItem4.getScore()).add(firstItem5.getScore()));
        item6.put("finalDeduction", finalItem1.getPracticalDeduction().add(finalItem2.getPracticalDeduction()).add(finalItem3.getPracticalDeduction()).add(finalItem4.getPracticalDeduction()).add(finalItem5.getPracticalDeduction()));
        item6.put("finalScore", finalItem1.getScore().add(finalItem2.getScore()).add(finalItem3.getScore()).add(finalItem4.getScore()).add(finalItem5.getScore()));
        item6.put("finalRemark", null);
        item6.put("score", firstItem1.getScore().add(firstItem2.getScore()).add(firstItem3.getScore()).add(firstItem4.getScore()).add(firstItem5.getScore()).add(finalItem1.getScore()).add(finalItem2.getScore()).add(finalItem3.getScore()).add(finalItem4.getScore()).add(finalItem5.getScore()));
        list.add(item6);

        simulationStatisticalDataVO.setData(list);
        return simulationStatisticalDataVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startCheckUseCount2(Long projectInfoId, Long customInfoId) {
        // 根据项目ID获取医院信息
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        // 获取主院
        ProjHospitalInfo projHospitalInfo = hospitalInfoList.stream().filter(item -> Integer.valueOf(1).equals(item.getHealthBureauFlag())).findFirst().orElse(null);

        // 根据项目ID获取实施产品
        List<BaseIdNameResp> productList = projProductDeliverRecordMapper.queryDeliverProductIdAndNameByProjectInfoId(projectInfoId);
        // 查询全部实施产品的产品ID
        List<Long> productIdList = productList.stream().map(BaseIdNameResp::getId).collect(Collectors.toList());

        // 产品功能字典表全部数据
        List<DictProductFunction> allDictProductFunctionList = dictProductFunctionMapper.selectList(
                new QueryWrapper<DictProductFunction>()
                        .eq("is_delete", 0)
                        .in("yy_product_id", productIdList)
        );

        // 检测脚本非空，需要进行检测的功能字典信息
        List<DictProductFunction> needQuerySQL = allDictProductFunctionList.stream().filter(item -> StringUtils.isNotBlank(item.getCheckSql())).collect(Collectors.toList());

        // 使用次数检测结果
        List<SqlCheckVO> queryResult = new ArrayList<>();
        try {
            CustomAndProjectInfoIdParam customAndProjectInfoIdParam = new CustomAndProjectInfoIdParam();
            customAndProjectInfoIdParam.setCustomInfoId(customInfoId);
            customAndProjectInfoIdParam.setProjectInfoId(projectInfoId);
            this.startCheck(needQuerySQL.size(), projHospitalInfo, customAndProjectInfoIdParam, needQuerySQL, queryResult);
        } catch (Exception e) {
            log.error("检测发生次数，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }

        // 遍历所有产品应用功能点
        for (DictProductFunction dictProductFunction : allDictProductFunctionList) {
            // 查询当前功能点的扣分明细
            ProjProductFunctionUseInfo existedDeductionDetail = projProductFunctionUseInfoMapper.selectUseInfoById(projectInfoId, dictProductFunction.getYyProductId(), dictProductFunction.getFunctionCode());

            // 云健康功能点使用次数检测结果
            SqlCheckVO checkResult = queryResult.stream().filter(item -> dictProductFunction.getFunctionCode().equals(item.getFunctionCode())).findFirst().orElse(null);

            Integer userCount;
            // 检测结果为null，认为使用次数为null
            if (checkResult == null) {
                userCount = null;
            } else {
                if (checkResult.getUseCount() == null) {
                    userCount = null;
                } else if (checkResult.getUseCount() != null && checkResult.getUseCount() > 0) {
                    // 检测结果的使用次数大于0
                    userCount = checkResult.getUseCount();
                } else if (checkResult.getUseCount() != null && checkResult.getUseCount() == 0 && StringUtils.isBlank(checkResult.getErrorMessage())) {
                    // 检测结果的使用次数为0但是错误信息为空，说明是正常返回了0
                    userCount = 0;
                } else {
                    userCount = null;
                }
            }

            // 当应用功能点的扣分明细不存在
            if (existedDeductionDetail == null) {
                // 新增扣分明细
                Date now = new Date();
                ProjProductFunctionUseInfo projProjectDeductionDetail = new ProjProductFunctionUseInfo();
                projProjectDeductionDetail.setProjProductFunctionUseInfoId(SnowFlakeUtil.getId());
                projProjectDeductionDetail.setIsDeleted(0);
                projProjectDeductionDetail.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                projProjectDeductionDetail.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                projProjectDeductionDetail.setCreateTime(now);
                projProjectDeductionDetail.setUpdateTime(now);
                projProjectDeductionDetail.setProjectInfoId(projectInfoId);
                projProjectDeductionDetail.setYyProductId(dictProductFunction.getYyProductId());
                projProjectDeductionDetail.setFunctionCode(dictProductFunction.getFunctionCode());
                projProjectDeductionDetail.setUseCount(userCount);
                int insert = projProductFunctionUseInfoMapper.insert(projProjectDeductionDetail);
            } else {
                // 修改扣分明细
                UpdateFunctionUseInfoParam projProjectDeductionDetail = new UpdateFunctionUseInfoParam();
                projProjectDeductionDetail.setProjProductFunctionUseInfoId(existedDeductionDetail.getProjProductFunctionUseInfoId());
                projProjectDeductionDetail.setUseCount(userCount);
                int insert = projProductFunctionUseInfoMapper.updateUseInfoById(projProjectDeductionDetail);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addProductFunctionDeduction(StartCheckUseCountParam2 param) {
        // 项目验收菜单字典
        DictProjectCheckAcceptMenu dictProjectCheckAcceptMenu = dictProjectCheckAcceptMenuMapper.getMenuByCode(param.getMenuCode());

        // 数据来源，判断查询首验还是终验的数据。只需要一次验收或者需要两次验收的第二次验收认为是终验的数据。
        String source;
        if (param.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }

        // 新增扣分明细
        Date now = new Date();
        ProjProjectDeductionDetail projProjectDeductionDetail = new ProjProjectDeductionDetail();
        projProjectDeductionDetail.setProjDeductionDetailInfoId(SnowFlakeUtil.getId());
        projProjectDeductionDetail.setIsDeleted(0);
        projProjectDeductionDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setCreateTime(now);
        projProjectDeductionDetail.setUpdateTime(now);
        projProjectDeductionDetail.setProjectInfoId(param.getProjectInfoId());
        projProjectDeductionDetail.setYyProductId(param.getYyProductId());
        projProjectDeductionDetail.setFunctionCode(param.getFunctionCode());
        // 实际扣分
        projProjectDeductionDetail.setPracticalDeduction(param.getPracticalDeduction());
        projProjectDeductionDetail.setRemark(param.getRemark());
        projProjectDeductionDetail.setClassificationCode(dictProjectCheckAcceptMenu.getClassificationCode());
        projProjectDeductionDetail.setSource(source);
        projProjectDeductionDetail.setOperationType("function");
        projProjectDeductionDetail.setDeductionType(param.getDeductionType());
        int insert = projProjectDeductionDetailMapper.insert(projProjectDeductionDetail);
        // 2.更新得分主表数据

        // 2.1 查询当前分类有无评分记录
        List<ProjProjectClassificationScore> projProjectClassificationScores = projProjectClassificationScoreMapper.getProjectClassificationScore(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);
        if (!CollectionUtils.isEmpty(projProjectClassificationScores)) {
            // 扣分记录明细
            List<ProjProjectDeductionDetail> projProjectDeductionDetailList = projProjectDeductionDetailMapper.getFunctionDeductionDetail(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);

            // 根据扣分记录明细表的实际扣分计算当前分类下的实际扣分
            BigDecimal practicalDeduction = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(projProjectDeductionDetailList)) {
                practicalDeduction = projProjectDeductionDetailList.stream().map(ProjProjectDeductionDetail::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            BigDecimal max = (new BigDecimal(String.valueOf(projProjectClassificationScores.get(0).getTotalScore())).subtract(practicalDeduction)).max(BigDecimal.ZERO);
            ProjProjectClassificationScore updateInfo = new ProjProjectClassificationScore();
            updateInfo.setPracticalDeduction(practicalDeduction);
            updateInfo.setFinalScore(max);
            int update1 = projProjectClassificationScoreMapper.update(updateInfo, new QueryWrapper<ProjProjectClassificationScore>().eq("id", projProjectClassificationScores.get(0).getId()));
            if (1 != update1) {
                throw new IllegalArgumentException("修改应用功能点扣分记录，更新得分主表数据异常");
            }
            return true;
        }
        return true;
    }

    @Override
    public Boolean deleteProductFunctionDeduction(DeleteProcessDeductionParam param) {
        int i = projDeductionDetailCommonMapper.updateStatusToDeletedById(param.getId());
        if (1 != i) {
            throw new IllegalArgumentException("修改应用功能点扣分记录，更新得分主表数据异常");
        }

        // 项目验收菜单字典
        DictProjectCheckAcceptMenu dictProjectCheckAcceptMenu = dictProjectCheckAcceptMenuMapper.getMenuByCode(param.getMenuCode());

        // 数据来源，判断查询首验还是终验的数据。只需要一次验收或者需要两次验收的第二次验收认为是终验的数据。
        String source;
        if (param.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }

        // 2.更新得分主表数据

        // 2.1 查询当前分类有无评分记录
        List<ProjProjectClassificationScore> projProjectClassificationScores = projProjectClassificationScoreMapper.getProjectClassificationScore(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);
        if (!CollectionUtils.isEmpty(projProjectClassificationScores)) {
            // 扣分记录明细
            List<ProjProjectDeductionDetail> projProjectDeductionDetailList = projProjectDeductionDetailMapper.getFunctionDeductionDetail(param.getProjectInfoId(), dictProjectCheckAcceptMenu.getClassificationCode(), source);

            // 根据扣分记录明细表的实际扣分计算当前分类下的实际扣分
            BigDecimal practicalDeduction = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(projProjectDeductionDetailList)) {
                practicalDeduction = projProjectDeductionDetailList.stream().map(ProjProjectDeductionDetail::getPracticalDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            BigDecimal max = (new BigDecimal(String.valueOf(projProjectClassificationScores.get(0).getTotalScore())).subtract(practicalDeduction)).max(BigDecimal.ZERO);
            ProjProjectClassificationScore updateInfo = new ProjProjectClassificationScore();
            updateInfo.setPracticalDeduction(practicalDeduction);
            updateInfo.setFinalScore(max);
            int update1 = projProjectClassificationScoreMapper.update(updateInfo, new QueryWrapper<ProjProjectClassificationScore>().eq("id", projProjectClassificationScores.get(0).getId()));
            if (1 != update1) {
                throw new IllegalArgumentException("修改应用功能点扣分记录，更新得分主表数据异常");
            }
            return true;
        }
        return true;
    }
}

