package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.config.ConfigProductJobMenuDetail;
import com.msun.csm.dao.entity.dict.DictProductExtend;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.dict.DictProjectPlanItem;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjPrereleaseMsg;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity;
import com.msun.csm.dao.entity.sys.DictMessageType;
import com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper;
import com.msun.csm.dao.mapper.config.ConfigProjectPlanOperationMapper;
import com.msun.csm.dao.mapper.dict.DictMessageTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProductExtendMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjIssueInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjPrereleaseMsgMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.dao.mapper.proj.ProjTodoTaskMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ProjStatisticalReportMainMapper;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.todotask.GetJumpDetailReq;
import com.msun.csm.model.req.todotask.QueryTodoTaskInfoReq;
import com.msun.csm.model.req.todotask.QueryTodoTaskReq;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.req.todotask.UpdateTodoTaskReq;
import com.msun.csm.model.resp.issue.IssueDataResp;
import com.msun.csm.model.resp.projectplan.OperationResp;
import com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp;
import com.msun.csm.model.resp.todotask.JumpPageDetailResult;
import com.msun.csm.model.resp.todotask.TodoTaskResp;
import com.msun.csm.model.struct.StatisticalReportMethodModel;
import com.msun.csm.model.vo.EquipSummaryVo;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.ProjProductBacklogDataVO;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.dict.DictBusinessStatusService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/6
 */

@Service
@Slf4j
public class ProjTodoTaskServiceImpl implements ProjTodoTaskService {

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjTodoTaskMapper todoTaskMapper;

    @Resource
    private DictProjectPlanItemMapper dictProjectPlanItemMapper;

    @Resource
    private DictMessageTypeMapper dictMessageTypeMapper;

    @Resource
    private ProjPrereleaseMsgMapper projPrereleaseMsgMapper;

    @Resource
    private ProjTodoTaskService todoTaskService;
    @Resource
    private UserHelper userHelper;
    @Resource
    private ProjIssueInfoMapper issueInfoMapper;
    @Resource
    private ConfigProjectPlanOperationMapper configProjectPlanOperationMapper;
    @Resource
    private ProjProductBacklogService productBacklogService;
    @Resource
    private ProjPrereleaseMsgService projPrereleaseMsgService;
    @Resource
    private ConfigProductJobMenuDetailMapper configProductJobMenuDetailMapper;
    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;
    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private ProjSurveyPlanService surveyPlanService;

    @Resource
    private ProjEquipSummaryService projEquipSummaryService;

    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private DictProductVsModulesMapper dictProductVsModulesMapper;

    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;

    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private DictBusinessStatusService dictBusinessStatusService;

    @Resource
    private ProjStatisticalReportMainMapper projStatisticalReportMainMapper;

    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private ProjProductDeliverRecordService projProductDeliverRecordService;

    @Resource
    private DictProductExtendMapper dictProductExtendMapper;

    @Resource
    private SendBusinessMessageService sendBusinessMessageService;

    @Resource
    private ProjProjectConfigService projectConfigService;

    @Lazy
    @Resource
    private ProjBusinessExamineLogService projBusinessExamineLogService;

    @Resource
    @Lazy
    private BaseQueryService baseQueryService;

    @Override
    public int deleteByPrimaryKey(Long todoTaskId) {
        return todoTaskMapper.deleteByPrimaryKey(todoTaskId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjTodoTask record) {
        // 记录更新之前的待办信息
        ProjTodoTask projTodoTask = todoTaskMapper.selectByPrimaryKey(record.getTodoTaskId());

        // 执行更新
        if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(record.getStatus())) {
            Long sysUserId = -1L;
            try {
                sysUserId = userHelper.getCurrentUser().getSysUserId();
            } catch (Throwable e) {
                log.error("未登录, e.message: {}, e=", e.getMessage(), e);
            }
            record.setCompletionUserId(sysUserId);
        }
        int i = todoTaskMapper.updateByPrimaryKeySelective(record);

        // 修改前后端责任人时发消息
        if (record.getImplementationEngineerId() != null || record.getBackendEngineerId() != null) {
            ProjTodoTask newTodoTask = todoTaskMapper.selectByPrimaryKey(record.getTodoTaskId());
            try {
                sendBusinessMessageService.sendAllocatingOrCancelTaskMessage(projTodoTask.getProjectInfoId(), projTodoTask, newTodoTask);
            } catch (Exception e) {
                log.error("责任人信息变更时发送分配/取消待办任务提醒消息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }

        try {
            // 如果是更新成已完成，需要查询一下待办消息是否发送了，如果没发送需要发送
            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(record.getStatus())) {
                projPrereleaseMsgService.sendPrereleaseMsg(projTodoTask.getProjectInfoId(), projTodoTask.getYyProductId(), projTodoTask.getProjectPlanId());
            }
        } catch (Exception e) {
            log.error("待办更新为完成时发送任务待办提醒，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return i;
    }

    /**
     * 查询数据
     *
     * @param req
     * @return
     */
    @Override
    public Result<List<TodoTaskResp>> queryData(QueryTodoTaskReq req) {
        log.info("查询我的待办,参数-{}", JSON.toJSONString(req));
        List<TodoTaskResp> respList = todoTaskMapper.queryData(req);
        if (!CollectionUtils.isEmpty(respList) && req.getExecutorId() != null) {
            respList = respList.stream().filter(item -> {
                // 是前端计划
                if (Integer.valueOf(1).equals(item.getFrontFlag())) {
                    if (req.getExecutorId().equals(item.getImplementationEngineerId())) {
                        return true;
                    }
                }
                if (Integer.valueOf(1).equals(item.getBackendFlag())) {
                    if (req.getExecutorId().equals(item.getBackendEngineerId())) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }

        respList.forEach(resp -> {
            //查询右键操作
            List<OperationResp> operationList = configProjectPlanOperationMapper.getOperationListByTodo(resp);
            resp.setOperationList(operationList);
            //问题数据查询
            QueryIssueReq issueReq = new QueryIssueReq();
            issueReq.setProjectInfoId(resp.getProjectInfoId());
            issueReq.setProjectPlanId(resp.getProjectPlanId());
            if (resp.getYyProductId() != null) {
                issueReq.setProductIdList(Collections.singletonList(resp.getYyProductId()));
                // 产品ID非空时，产品业务调研、设备调研、产品准备工作、设备对接需要跳转对应产品的具体明细页面
                if ("survey_product".equals(resp.getProjectPlanItemCode()) || "survey_device".equals(resp.getProjectPlanItemCode()) || "preparat_product".equals(resp.getProjectPlanItemCode()) || "preparat_device".equals(resp.getProjectPlanItemCode())) {
                    resp.setJumpType(2);
                }
            }
            List<IssueDataResp> issueDataResps = issueInfoMapper.queryData(issueReq);
            String relatedIssue = "0/0";
            if (issueDataResps != null && !issueDataResps.isEmpty()) {
                Long completedIssue = issueDataResps.stream().filter(a -> a.getStatus() == 8 || a.getStatus() == 9).count();
                relatedIssue = String.format("%d/%d", completedIssue, issueDataResps.size());
            }
            resp.setRelatedIssue(relatedIssue);

            getProgressDisplayMode(resp);
            // 0/0的不展示
            if (Integer.valueOf(0).equals(resp.getCompleteCount()) && Integer.valueOf(0).equals(resp.getTotalCount())) {
                resp.setProgressDisplayMode("none");
            }
        });
        this.dealStageStatus(respList);
        return Result.success(respList);
    }

    /**
     * 获取进度显示模式
     *
     * @param resp
     * @return
     */
    private void getProgressDisplayMode(TodoTaskResp resp) {
        // 查询节点为调研打印报表、调研表单的数据
        ConfigCustomBackendDetailLimit limit = null;
        if (DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode().equals(resp.getProjectPlanItemCode())) {
            // 查询该项目是否开启小前端大后端报表表单审核
            limit = baseQueryService.isOpenAuditorFlag(resp.getProjectInfoId(), 11);
            if (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() == 1) {
                resp.setProgressDisplayMode("ratio");
            } else {
                resp.setProgressDisplayMode("total");
            }
        } else if (DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode().equals(resp.getProjectPlanItemCode())) {
            // 查询该项目是否开启小前端大后端报表表单审核
            limit = baseQueryService.isOpenAuditorFlag(resp.getProjectInfoId(), 12);
            if (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() == 1) {
                resp.setProgressDisplayMode("ratio");
            } else {
                resp.setProgressDisplayMode("total");
            }
        }

    }

    /**
     * 处理项目计划的阶段完成状态，枚举值0、1、2，分别表示未开始、进行中、已完成。
     *
     * @param projectPlanRespList 项目计划
     */
    private void dealStageStatus(List<TodoTaskResp> projectPlanRespList) {
        Map<String, Integer> planStageStatusMap = new HashMap<>();
        Map<String, List<TodoTaskResp>> groupByPlanStageCode = projectPlanRespList.stream().collect(Collectors.groupingBy(TodoTaskResp::getProjectPlanStageCode));
        for (Map.Entry<String, List<TodoTaskResp>> entry : groupByPlanStageCode.entrySet()) {
            planStageStatusMap.put(entry.getKey(), this.getStageStatus(entry.getValue()));
        }
        projectPlanRespList.forEach(item -> {
            if (planStageStatusMap.containsKey(item.getProjectPlanStageCode())) {
                item.setStageStatus(planStageStatusMap.get(item.getProjectPlanStageCode()));
            }
        });
    }

    /**
     * 处理项目计划的阶段完成状态，枚举值0、1、2，分别表示未开始、进行中、已完成。
     *
     * @param projectPlanRespList 项目计划
     */
    private int getStageStatus(List<TodoTaskResp> projectPlanRespList) {
        // 没有数据，认为是已完成
        if (CollectionUtils.isEmpty(projectPlanRespList)) {
            return 2;
        }
        // 全部都是已完成，整体状态为已完成
        if (projectPlanRespList.stream().allMatch(item -> Integer.valueOf(1).equals(item.getStatus()))) {
            return 2;
        }
        // 全部都是未完成，整体状态为未开始
        if (projectPlanRespList.stream().allMatch(item -> Integer.valueOf(0).equals(item.getStatus()))) {
            return 0;
        }
        // 既有已完成也有未完成，整体状态为进行中
        return 1;
    }

    /**
     * 更新待办数据
     *
     * @param req
     * @return
     */
    @Override
    public Result updateData(UpdateTodoTaskReq req) {
        ProjTodoTask oldTodoTask = todoTaskMapper.selectByPrimaryKey(req.getTodoTaskId());
        log.info("更新待办数据,参数-{}", JSON.toJSONString(req));
        ProjTodoTask todoTask = new ProjTodoTask();
        BeanUtil.copyProperties(req, todoTask);
        todoTask.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        todoTask.setUpdateTime(new Date());
        this.updateByPrimaryKeySelective(todoTask);
        todoTask.setTitle(oldTodoTask.getTitle());
        todoTask.setYyProductId(oldTodoTask.getYyProductId());
        todoTask.setPlanTime(req.getPlanTime() == null ? oldTodoTask.getPlanTime() : req.getPlanTime());
        return Result.success();
    }

    /**
     * 根据项目信息ID和项目计划项代码查询待办任务
     *
     * @param projectInfoId  项目信息ID
     * @param hospitalInfoId 医院信息ID
     * @param itemCode       项目计划项代码
     * @param yyProductId    医疗产品ID
     * @return 返回查询到的待办任务，如果项目计划不存在或没有找到对应的待办任务，则返回null
     * @throws CustomException 如果项目计划不存在，则抛出自定义异常
     */
    @Override
    public List<ProjTodoTask> selectTodoTaskByProjectInfoIdAndItemCode(Long projectInfoId, Long hospitalInfoId, String itemCode, Long yyProductId) {
        // 记录查询日志
        log.info("查询项目计划下的待办数据,参数-项目id={}, 项目计划项code={}", projectInfoId, itemCode);

        // 根据项目信息ID和项目计划项代码获取项目计划
        ProjProjectPlan projProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, DictProjectPlanItemEnum.getPlanItemByCode(itemCode));

        // 如果项目计划不存在，则抛出异常
        if (projProjectPlan == null) {
            throw new CustomException("项目计划不存在");
        }

        // 创建查询条件
        QueryWrapper<ProjTodoTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_plan_id", projProjectPlan.getProjectPlanId());
        queryWrapper.eq("project_info_id", projectInfoId);
        queryWrapper.eq("hospital_info_id", hospitalInfoId);
        if (yyProductId != null) {
            queryWrapper.eq("yy_product_id", yyProductId);
        }
        queryWrapper.eq("is_deleted", 0);

        // 执行查询并返回结果
        return todoTaskMapper.selectList(queryWrapper);
    }

    /**
     * 保存或更新待办数据-里程碑分配任务时调用
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateProjTodoTask(SaveOrUpdateTodoTaskParam param) {
        boolean planModel = projectConfigService.isPlanModel(param.getProjectInfoId());
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", param.getProjectInfoId());
            return true;
        }
        log.info("保存或更新待办数据,参数-{}", JSON.toJSONString(param));
        //校验参数
        if (!ObjectUtil.isAllNotEmpty(param.getProjectInfoId(), param.getUserId(), param.getCode(), param.getHospitalInfoId())) {
            throw new CustomException("必备参数不完整，请检查项目id、医院id，人员id或计划编码是否为空！");
        }
        //查询对应的项目计划projectPlan数据
        ProjProjectPlan projProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(param.getCode()));
        if (projProjectPlan == null) {
            throw new CustomException("项目计划不存在");
        }

        //要处理待办对象
        ProjTodoTask projTodoTask = null;
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), null);
        //专项工作是否由多人完成标识，如三方接口、报表、表单
        boolean flagMorePerson = false;
        DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(param.getCode());
        switch (planItemByCode) {
            case SURVEY_PRODUCT:
            case SCHEDULE_THIRD_PART:
            case PREPARAT_REPORT:
            case PREPARAT_STATISTICS_REPORT:
            case PREPARAT_FORM:
                flagMorePerson = true;
                break;
            default:
                break;
        }
        List<ProjTodoTask> tmpTodoTaskList;
        if (!org.springframework.util.CollectionUtils.isEmpty(todoTaskList)) {
            if (flagMorePerson) {
                tmpTodoTaskList = todoTaskList.stream().filter(v -> {
                    if (v.getImplementationEngineerId() == null) {
                        return true;
                    }
                    return v.getImplementationEngineerId().equals(param.getUserId());
                }).collect(Collectors.toList());
            } else {
                tmpTodoTaskList = todoTaskList;
            }
            if (!tmpTodoTaskList.isEmpty()) {
                projTodoTask = tmpTodoTaskList.get(0);
            }
        }

        //有更新
        if (projTodoTask != null) {
            //更新待办数据
            projTodoTask.setPlanTime(param.getPlanTime());
            projTodoTask.setImplementationEngineerId(param.getUserId());
            if (param.getBackendSysUserId() != null) {
                projTodoTask.setBackendEngineerId(param.getBackendSysUserId());
            }
            if (ObjectUtil.isNotEmpty(param.getTotalCount()) && param.getTotalCount() > 0) {
                projTodoTask.setTotalCount(param.getTotalCount());
            }
            projTodoTask.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            projTodoTask.setUpdateTime(new Date());
            projTodoTask.setCompleteCount(param.getCompleteCount());
            projTodoTask.setStatus(param.getStatus());
            this.updateByPrimaryKeySelective(projTodoTask);
        } else {
            //无新增
            ProjTodoTask newRecord = new ProjTodoTask();
            newRecord.setTodoTaskId(SnowFlakeUtil.getId());
            newRecord.setProjectPlanId(projProjectPlan.getProjectPlanId());
            newRecord.setProjectInfoId(projProjectPlan.getProjectInfoId());
            newRecord.setYyProductId(param.getYyProductId());
            newRecord.setTitle(projProjectPlan.getTitle());
            newRecord.setDescription(projProjectPlan.getDescription());
            newRecord.setSort(todoTaskMapper.queryMaxSort(projProjectPlan.getProjectInfoId()) + 1);
            newRecord.setPlanTime(param.getPlanTime());
            newRecord.setStatus(param.getStatus() == null ? 0 : param.getStatus());
            newRecord.setImplementationEngineerId(param.getUserId());
            newRecord.setBackendEngineerId(param.getBackendSysUserId() == null ? -1L : param.getBackendSysUserId());
            newRecord.setCompleteCount(param.getCompleteCount() == null ? 0 : param.getCompleteCount());
            newRecord.setTotalCount((ObjectUtil.isNotEmpty(param.getTotalCount()) && param.getTotalCount() > 0) ? param.getTotalCount() : 0);
            newRecord.setJumpType(1);
            newRecord.setJumpPath("");
            newRecord.setAttentionFlag(0);
            newRecord.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            newRecord.setCreateTime(new Date());
            newRecord.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            newRecord.setUpdateTime(new Date());
            newRecord.setIsDeleted(0);
            newRecord.setFrontFlag(projProjectPlan.getFrontFlag());
            newRecord.setBackendFlag(projProjectPlan.getBackendFlag());
            newRecord.setHospitalInfoId(param.getHospitalInfoId());
            todoTaskMapper.addTodoTask(newRecord);

            // 向预发表写入数据
            try {
                // 存在前置工作
                if (Integer.valueOf(1).equals(projProjectPlan.getPriorProjectPlanItemFlag()) && StringUtils.isNotBlank(projProjectPlan.getPriorProjectPlanItemId())) {
                    DictProjectPlanItem dictProjectPlanItem = dictProjectPlanItemMapper.selectByPrimaryKey(projProjectPlan.getProjectPlanItemId());

                    // 当前待办对应的所有前置工作的工作项ID
                    DictMessageType dictMessageType = dictMessageTypeMapper.getDictMessageTypeById(100001L);

                    List<ProjPrereleaseMsg> projPrereleaseMsgs = projPrereleaseMsgMapper.selectList(new QueryWrapper<ProjPrereleaseMsg>()
                            .eq("is_deleted", 0)
                            .eq("msg_class_id", 100001L)
                            .eq("project_info_id", newRecord.getProjectInfoId())
                            .eq("plan_item_code", dictProjectPlanItem.getProjectPlanItemCode())
                            .eq("pre_plan_item_code", dictProjectPlanItem.getProjectPlanItemCode())
                            .eq(newRecord.getYyProductId() != null, "yy_product_id", newRecord.getYyProductId())

                    );
                    if (org.springframework.util.CollectionUtils.isEmpty(projPrereleaseMsgs) && dictMessageType != null) {
                        ProjPrereleaseMsg prereleaseMsg = new ProjPrereleaseMsg();
                        prereleaseMsg.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                        prereleaseMsg.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                        prereleaseMsg.setPlanItemCode(dictProjectPlanItem.getProjectPlanItemCode());
                        prereleaseMsg.setPrePlanItemCode(dictProjectPlanItem.getProjectPlanItemCode());
                        prereleaseMsg.setHospitalInfoId(null);
                        prereleaseMsg.setProjectInfoId(newRecord.getProjectInfoId());
                        // TODO 固定消息类型号段
                        prereleaseMsg.setMsgClassId(100001L);
                        prereleaseMsg.setPrereleaseMsgId(SnowFlakeUtil.getId());
                        prereleaseMsg.setYyProductId(newRecord.getYyProductId());
                        projPrereleaseMsgMapper.insertData(prereleaseMsg);
                    }
                }

                sendBusinessMessageService.sendAllocatingOrCancelTaskMessage(projProjectPlan.getProjectInfoId(), null, newRecord);
            } catch (Exception e) {
                log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
        return true;
    }

    //#region 初始化待办任务

    /**
     * 初始化待办任务
     *
     * @param param 待办任务参数实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void projectTodoTaskInit(SaveOrUpdateTodoTaskParam param) {
        boolean planModel = projectConfigService.isPlanModel(param.getProjectInfoId());
        if (!planModel) {
            log.warn("当前项目未开启小前端大后端模式：projectInfoId: {}", param.getProjectInfoId());
            return;
        }
        //查询对应的项目计划projectPlan数据
        ProjProjectPlan projProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(param.getCode()));
        DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(param.getCode());
        if (projProjectPlan == null) {
            log.warn("项目计划不存在,不生成代办任务：{}", JSON.toJSONString(param));
            return;
        }
        switch (planItemByCode) {
            case PREPARAT_PRODUCT: // 产品准备待办任务
                productPreparatTodoTaskInit(param);
                break;
            case PREPARAT_DEVICE: // 设备对接待办任务
                equipPreparatTodoTaskInit(param);
                break;
            case SURVEY_DEVICE:// 设备调研待办任务
                equipSurveyTodoTaskInit(param);
                break;
            case SCHEDULE_THIRD_PART: // 三方接口待办任务
                thirdInterfaceTodoTaskInit(param, projProjectPlan);
                break;
            case PREPARAT_STATISTICS_REPORT: // 统计报表待办任务
                statisticsReportTodoTaskInit(param, projProjectPlan);
                break;
            case PREPARAT_REPORT: // 打印报表待办任务
                printReportTodoTaskInit(param, projProjectPlan);
                break;
            case PREPARAT_FORM: // 表单待办任务
                projectFormTodoTaskInit(param, projProjectPlan);
                break;
            case SURVEY_PRODUCT:
                surveyProductTodoTaskInit(param, projProjectPlan);
                break;
            default:
                break;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void surveyProductTodoTaskInit(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
        projSurveyPlanDTO.setProjectInfoId(param.getProjectInfoId());
        projSurveyPlanDTO.setHospitalInfoId(param.getHospitalInfoId());
        projSurveyPlanDTO.setYyProductId(param.getYyProductId());
        List<SurveyPlanTaskResp> surveyPlan = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);

        List<Long> personList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(surveyPlan)) {
            personList = surveyPlan.stream().map(SurveyPlanTaskResp::getSurveyUserId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(personList)) {
                for (Long dirPersonId : personList) {
                    // 总数
                    int totalCount = Integer.parseInt(String.valueOf(surveyPlan.stream().filter(x -> dirPersonId.equals(x.getSurveyUserId())).count()));
                    param.setTotalCount(totalCount);

                    // 完成数
                    int completeCount = Integer.parseInt(String.valueOf(surveyPlan.stream().filter(item -> dirPersonId.equals(item.getSurveyUserId()) && !Integer.valueOf(0).equals(item.getCompleteStatus())).count()));
                    param.setCompleteCount(completeCount);

                    DictProductExtend dictProductExtend = dictProductExtendMapper.selectOne(new QueryWrapper<DictProductExtend>()
                            .eq("yy_product_id", param.getYyProductId())
                            .eq("survey_flag", 0)
                            .eq("is_deleted", 0));

                    // 状态
                    Set<Integer> collect = surveyPlan.stream().filter(x -> dirPersonId.equals(x.getSurveyUserId())).map(SurveyPlanTaskResp::getCompleteStatus).collect(Collectors.toSet());
                    log.info("产品业务调研生成todotask，产品ID={}，当前产品={}", param.getYyProductId(), collect);
                    if (!collect.contains(0) || dictProductExtend != null) {
                        // 没有未开始的或者当前产品无需调研
                        param.setStatus(1);
                    } else if (collect.size() == 1) {
                        // 仅有未开始的
                        param.setStatus(0);
                    } else {
                        param.setStatus(2);
                    }
                    saveOrUpdateProjTodoTask(param);
                }
            }
        }
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), null);
        refreshTodoTask(todoTaskList, personList);
    }

    /**
     * 初始化产品准备待办任务
     *
     * @param param 待办任务参数实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void productPreparatTodoTaskInit(SaveOrUpdateTodoTaskParam param) {
        ProjProductBacklogDTO productBacklogDTO = new ProjProductBacklogDTO();
        productBacklogDTO.setProjectInfoId(param.getProjectInfoId());
        productBacklogDTO.setHospitalInfoId(param.getHospitalInfoId());
        productBacklogDTO.setYyProductId(param.getYyProductId());
        ProjProductBacklog productBacklog = productBacklogService.selectByParam(productBacklogDTO);
        if (ObjectUtil.isNotEmpty(productBacklog)) {
            int totalCount = 0;
            if (productBacklog.getBaseDataStatus() != 0) {
                totalCount++;
            }
            if (productBacklog.getConfigDataStatus() != 0) {
                totalCount++;
            }
            if (productBacklog.getTodoTaskStatus() != 0) {
                totalCount++;
            }
            if (productBacklog.getReportDataStatus() != 0) {
                totalCount++;
            }
            if (productBacklog.getFormDataStatus() != 0) {
                totalCount++;
            }
            if (totalCount > 0) {
                param.setTotalCount(totalCount);
            }
        }
        // 调用服务保存或更新待办任务
        saveOrUpdateProjTodoTask(param);
        // 初始化设备对接待办任务
        param.setCode(DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        equipPreparatTodoTaskInit(param);
    }

    /**
     * 初始化设备对接待办任务
     *
     * @param param 待办任务参数实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void equipPreparatTodoTaskInit(SaveOrUpdateTodoTaskParam param) {
        // 查询当前产品是否包含设备。当不包含时 跳过
        ConfigProductJobMenuDetail configProductJobMenuDetail = configProductJobMenuDetailMapper.selectOne(new QueryWrapper<ConfigProductJobMenuDetail>().eq("product_job_menu_id", 5).eq("yy_product_id", param.getYyProductId()));
        if (ObjectUtil.isNotEmpty(configProductJobMenuDetail)) {
            param.setTotalCount(0);
            QueryWrapper<ProjEquipRecord> equipRecordQueryWrapper = new QueryWrapper<ProjEquipRecord>().eq("project_info_id", param.getProjectInfoId()).eq("hospital_info_id", param.getHospitalInfoId()).eq("yy_product_id", param.getYyProductId()).eq("required_flag", 1).eq("is_deleted", 0);
            List<ProjEquipRecord> equipRecordList = projEquipRecordMapper.selectList(equipRecordQueryWrapper);
            if (ObjectUtil.isEmpty(equipRecordList)) {
                param.setTotalCount(equipRecordList.size());
            }
            saveOrUpdateProjTodoTask(param);
        }
    }

    /**
     * 初始化设备调研待办任务
     *
     * @param param 待办任务参数实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void equipSurveyTodoTaskInit(SaveOrUpdateTodoTaskParam param) {
        // 查询当前产品是否包含设备。当不包含时 跳过
        ConfigProductJobMenuDetail configProductJobMenuDetail = configProductJobMenuDetailMapper.selectOne(new QueryWrapper<ConfigProductJobMenuDetail>().eq("product_job_menu_id", 5).eq("yy_product_id", param.getYyProductId()));
        if (ObjectUtil.isNotEmpty(configProductJobMenuDetail)) {
            param.setTotalCount(0);
            QueryWrapper<ProjEquipRecord> equipRecordQueryWrapper = new QueryWrapper<ProjEquipRecord>().eq("project_info_id", param.getProjectInfoId()).eq("hospital_info_id", param.getHospitalInfoId()).eq("yy_product_id", param.getYyProductId()).eq("is_deleted", 0);
            List<ProjEquipRecord> equipRecordList = projEquipRecordMapper.selectList(equipRecordQueryWrapper);
            if (!ObjectUtil.isEmpty(equipRecordList)) {
                param.setTotalCount(equipRecordList.size());
            }
            param.setUserId(userHelper.getCurrentUser().getSysUserId());
            saveOrUpdateProjTodoTask(param);
        }
    }

    /**
     * 初始化三方接口待办任务
     *
     * @param param           待办任务参数实体
     * @param projProjectPlan 项目计划实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void thirdInterfaceTodoTaskInit(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        QueryWrapper<ProjThirdInterface> projThirdInterfaceQueryWrapper = new QueryWrapper<>();
        projThirdInterfaceQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projThirdInterfaceQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        projThirdInterfaceQueryWrapper.eq("online_flag", 1);
        projThirdInterfaceQueryWrapper.isNotNull("dir_person_id");
        List<ProjThirdInterface> projThirdInterfaceList = projThirdInterfaceMapper.selectList(projThirdInterfaceQueryWrapper);
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), null);
        List<Long> personList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(projThirdInterfaceList)) {
            personList = projThirdInterfaceList.stream().map(ProjThirdInterface::getDirPersonId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(personList)) {
                for (Long dirPersonId : personList) {
                    int totalCount = (int) projThirdInterfaceList.stream().filter(x -> dirPersonId.equals(x.getDirPersonId())).count();
                    param.setTotalCount(totalCount);

                    saveOrUpdateProjTodoTask(param);
                }
            }
        }
        //批量删除无实际工作内容的待办任务
        if (CollectionUtil.isNotEmpty(todoTaskList)) {
            List<Long> finalPersonList = personList;
            List<ProjTodoTask> todoTaskList1 = todoTaskList.stream().filter(x -> !finalPersonList.contains(x.getImplementationEngineerId())).collect(Collectors.toList());
            if (!todoTaskList1.isEmpty()) {
                log.info("批量删除无实际工作内容的三方接口待办任务,删除的任务明细：{}", todoTaskList1);
                List<ProjTodoTask> updateTodoList = todoTaskList1.stream().map(x -> {
                    x.setIsDeleted(1);
                    return x;
                }).collect(Collectors.toList());
                todoTaskMapper.updateBatch(updateTodoList);
            }
        }

    }

    /**
     * 初始化统计报表待办任务
     *
     * @param param           待办任务参数实体
     * @param projProjectPlan 项目计划实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void statisticsReportTodoTaskInit(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        QueryWrapper<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityQueryWrapper = new QueryWrapper<>();
        projStatisticalReportMainEntityQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projStatisticalReportMainEntityQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        projStatisticalReportMainEntityQueryWrapper.eq("online_flag", 1);
        projStatisticalReportMainEntityQueryWrapper.eq("is_deleted", 0);
        List<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityList = projStatisticalReportMainMapper.selectList(projStatisticalReportMainEntityQueryWrapper);

        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), null);
        List<Long> personList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(projStatisticalReportMainEntityList)) {
            personList = projStatisticalReportMainEntityList.stream().map(ProjStatisticalReportMainEntity::getAllocateUserId).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(personList) && personList.size() > 0) {
                for (Long dirPersonId : personList) {
                    if (dirPersonId == null) {
                        continue;
                    }
                    int totalCount = (int) projStatisticalReportMainEntityList.stream().filter(x -> dirPersonId.equals(x.getAllocateUserId())).count();
                    param.setTotalCount(totalCount);

                    saveOrUpdateProjTodoTask(param);
                }
            }
        }
        refreshTodoTask(todoTaskList, personList);
    }

    /**
     * 初始化打印报表待办任务
     *
     * @param param           待办任务参数实体
     * @param projProjectPlan 项目计划实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void printReportTodoTaskInit(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        QueryWrapper<ProjSurveyReport> projSurveyReportQueryWrapper = new QueryWrapper<>();
        projSurveyReportQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projSurveyReportQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        projSurveyReportQueryWrapper.eq("online_essential", 1);
        projSurveyReportQueryWrapper.eq("is_deleted", 0);
        List<ProjSurveyReport> projSurveyReportList = projSurveyReportMapper.selectList(projSurveyReportQueryWrapper);

        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), null);
        List<Long> personList = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(projSurveyReportList)) {
            personList = projSurveyReportList.stream().map(ProjSurveyReport::getMakeUserId).filter(Objects::nonNull).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(personList)) {
                for (Long dirPersonId : personList) {
                    int totalCount = (int) projSurveyReportList.stream().filter(x -> dirPersonId.equals(x.getMakeUserId())).count();
                    param.setTotalCount(totalCount);

                    saveOrUpdateProjTodoTask(param);
                }
            }
        }
        refreshTodoTask(todoTaskList, personList);
    }

    /**
     * 初始化表单待办任务
     *
     * @param param           待办任务参数实体
     * @param projProjectPlan 项目计划实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void projectFormTodoTaskInit(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        QueryWrapper<ProjSurveyForm> projSurveyFormQueryWrapper = new QueryWrapper<>();
        projSurveyFormQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projSurveyFormQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        projSurveyFormQueryWrapper.eq("online_essential", 1);
        projSurveyFormQueryWrapper.eq("is_deleted", 0);
        List<ProjSurveyForm> projSurveyFormList = projSurveyFormMapper.selectList(projSurveyFormQueryWrapper);

        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), null);
        List<Long> personList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(projSurveyFormList)) {
            personList = projSurveyFormList.stream().map(ProjSurveyForm::getMakeUserId).filter(Objects::nonNull).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(personList)) {
                for (Long dirPersonId : personList) {
                    int totalCount = (int) projSurveyFormList.stream().filter(x -> dirPersonId.equals(x.getMakeUserId())).count();
                    param.setTotalCount(totalCount);

                    saveOrUpdateProjTodoTask(param);
                }
            }
        }
        refreshTodoTask(todoTaskList, personList);
    }
    //#endregion

    //#region 更新待办进度

    /**
     * 更新项目待办任务进度
     *
     * @param param 待办任务参数实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectTodoTaskStatus(SaveOrUpdateTodoTaskParam param) {
        boolean planModel = projectConfigService.isPlanModel(param.getProjectInfoId());
        if (planModel) {
            // 查询对应的项目计划projectPlan数据
            ProjProjectPlan projProjectPlan = null;
            try {
                projProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(param.getCode()));
                if (projProjectPlan == null) {
                    throw new CustomException("项目计划不存在");
                }
            } catch (Exception e) {
                log.error("更新项目待办任务进度，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                return;
            }
            DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(param.getCode());
            switch (planItemByCode) {
                case PREPARAT_PRODUCT: // 产品准备待办任务
                    updateProductPreparatTodoTaskStatus(param);
                    break;
                case SURVEY_DEVICE:
                case PREPARAT_DEVICE: // 设备对接待办任务
                    updateEquipProjectTodoTaskStatus(param);
                    break;
                case SURVEY_THIRD_PART:
                case SCHEDULE_THIRD_PART: // 三方接口待办任务
                    updateThirdInterfaceTodoTaskStatus(param, projProjectPlan);
                    break;
                case SURVEY_STATISTICS_REPORT:
                case PREPARAT_STATISTICS_REPORT: // 统计报表待办任务
                    updateStatiscsReportTodoTaskStatus(param, projProjectPlan);
                    break;
                case SURVEY_REPORT:
                case PREPARAT_REPORT: // 打印报表待办任务
                    updateSurveyReportTodoTaskStatus(param, projProjectPlan);
                    break;
                case SURVEY_FORM:
                case PREPARAT_FORM: // 表单待办任务
                    updateProjectFormTodoTaskStatus(param, projProjectPlan);
                    break;
                default:
                    break;
            }
        } else {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", param.getProjectInfoId());
        }
    }

    /**
     * 更新产品准备待办任务进度
     *
     * @param param 待办任务参数实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProductPreparatTodoTaskStatus(SaveOrUpdateTodoTaskParam param) {
        // 创建一个产品待办事项DTO并设置相关参数
        ProjProductBacklogDTO productBacklogDTO = new ProjProductBacklogDTO();
        productBacklogDTO.setProjectInfoId(param.getProjectInfoId());
        productBacklogDTO.setHospitalInfoId(param.getHospitalInfoId());
        productBacklogDTO.setYyProductId(param.getYyProductId());

        // 根据参数查询产品待办事项
        ProjProductBacklog productBacklog = productBacklogService.selectByParam(productBacklogDTO);

        // 如果查询到的产品待办事项不为空，则进一步处理
        if (ObjectUtil.isNotEmpty(productBacklog)) {
            int completeCount = 0;

            // 检查各个状态，如果状态为2，则增加完成计数
            if (productBacklog.getBaseDataStatus() == 2) {
                completeCount++;
            }
            if (productBacklog.getConfigDataStatus() == 2) {
                completeCount++;
            }
            if (productBacklog.getTodoTaskStatus() == 2) {
                completeCount++;
            }
            if (productBacklog.getReportDataStatus() == 2) {
                completeCount++;
            }
            if (productBacklog.getFormDataStatus() == 2) {
                completeCount++;
            }

            // 根据项目信息ID、医院信息ID和产品ID查询待办任务
            ProjTodoTask todoTask = null;
            List<ProjTodoTask> productPreparatTodoTaskList = selectTodoTaskByProjectInfoIdAndItemCode(param.getProjectInfoId(), param.getHospitalInfoId(), "preparat_product", null);
            if (!productPreparatTodoTaskList.isEmpty()) {
                List<ProjTodoTask> tmpProductPreparatTodoTaskList = productPreparatTodoTaskList.stream().filter(a -> a.getYyProductId().equals(param.getYyProductId())).collect(Collectors.toList());
                if (!tmpProductPreparatTodoTaskList.isEmpty()) {
                    todoTask = tmpProductPreparatTodoTaskList.get(0);
                }
            }
            // 如果待办任务不为空，则更新其完成计数和状态
            if (ObjectUtil.isNotEmpty(todoTask)) {
                todoTask.setCompleteCount(completeCount);
                // 如果完成计数等于总计数，更新任务状态为完成（状态码1.已完成2.进行中）
                if (todoTask.getTotalCount().equals(completeCount)) {
                    todoTask.setStatus(1);
                } else {
                    todoTask.setStatus(2);
                }
                // 选择性更新待办任务
                updateByPrimaryKeySelective(todoTask);

                //更新产品准备计划进度
                List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(todoTask.getProjectPlanId(), null, param.getYyProductId(), null);
                int nocompleteCount = (int) todoTaskList.stream().filter(a -> !Integer.valueOf(1).equals(a.getStatus())).count();
                if (nocompleteCount == 0) {
                    // 记录更新日志
                    log.info("各院区某一产品待办工作全部完成，更新产品准备项目计划进度，projectInfoId={},projectPlanItemCode={}", param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
                    List<ProjTodoTask> tmpList = productPreparatTodoTaskList.stream().filter(a -> a.getYyProductId() != null).collect(Collectors.toList());
                    // 总的待办
                    List<ProjTodoTask> totalTodoTask = selectTodoTaskByProjectInfoIdAndItemCode(param.getProjectInfoId(), -1L, "preparat_product", null);
                    List<ProjTodoTask> tmpList2 = totalTodoTask.stream().filter(a -> a.getYyProductId() == null).collect(Collectors.toList());
                    if (!tmpList2.isEmpty()) {
                        //产品准备工作处理
                        ProjProductBacklogDTO productBacklogDTO2 = new ProjProductBacklogDTO();
                        productBacklogDTO2.setProjectInfoId(param.getProjectInfoId());
                        List<ProjProductBacklogVO> backlogList = productBacklogService.findProductBacklogVOS(productBacklogDTO2);
                        // 模块对应的产品准备工作
                        List<ProjProductBacklogVO> moduleBacklogVO = backlogList.stream().filter(item -> item.getYyProductModuleId() != -1).collect(Collectors.toList());
                        // 模块对应的产品编码
                        List<Long> moduleProductId = moduleBacklogVO.stream().map(ProjProductBacklogVO::getYyProductId).collect(Collectors.toList());
                        // 产品对应的产品准备工作
                        List<ProjProductBacklogVO> productBacklogVO = backlogList.stream().filter(item -> !moduleProductId.contains(item.getYyProductId())).collect(Collectors.toList());
                        int totalCount2 = moduleBacklogVO.size() + productBacklogVO.size();

                        List<Long> completeModuleSet = moduleBacklogVO.stream().filter(item -> Integer.valueOf(1).equals(item.getCompleteStatus())).map(ProjProductBacklogVO::getYyProductModuleId).collect(Collectors.toList());
                        List<Long> completeProductSet = productBacklogVO.stream().filter(item -> Integer.valueOf(1).equals(item.getCompleteStatus())).map(ProjProductBacklogVO::getYyProductId).collect(Collectors.toList());
                        int completeCount2 = completeModuleSet.size() + completeProductSet.size();
                        ProjTodoTask todoTask2 = new ProjTodoTask();
                        todoTask2.setTodoTaskId(tmpList2.get(0).getTodoTaskId());
                        todoTask2.setTotalCount(totalCount2);
                        todoTask2.setCompleteCount(completeCount2);
                        todoTask2.setStatus(totalCount2 == completeCount2 ? 1 : 2);
                        updateByPrimaryKeySelective(todoTask2);
                    }
                    projProjectPlanService.projectPlanTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
                }
            }
        }
    }

    /**
     * 更新设备对接待办状态及进度
     *
     * @param param 待办任务参数实体
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEquipProjectTodoTaskStatus(SaveOrUpdateTodoTaskParam param) {
        updateEquipProjectTodoTask(param, DictProjectPlanItemEnum.SURVEY_DEVICE);
        updateEquipProjectTodoTask(param, DictProjectPlanItemEnum.PREPARAT_DEVICE);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEquipProjectTodoTask(SaveOrUpdateTodoTaskParam param, DictProjectPlanItemEnum planItemByCode) {
        QueryWrapper<ProjEquipRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("project_info_id", param.getProjectInfoId());
        wrapper.eq("is_deleted", 0);
        if (DictProjectPlanItemEnum.PREPARAT_DEVICE.equals(planItemByCode)) {
            wrapper.eq("required_flag", 1);
        }

        List<ProjEquipRecord> projEquipRecordList = projEquipRecordMapper.selectList(wrapper);

        //查询对应的项目计划projectPlan数据
        ProjProjectPlan projProjectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(param.getProjectInfoId(), planItemByCode);
        if (projProjectPlan != null) {
            List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), null, param.getYyProductId(), null);
            if (param.getHospitalInfoId() != null) {
                //更新某一医院设备对接待办状态及进度
                List<ProjTodoTask> todoTaskListOneHospital = todoTaskList.stream().filter(a -> a.getHospitalInfoId().equals(param.getHospitalInfoId())).collect(Collectors.toList());
                if (!CollUtil.isEmpty(todoTaskListOneHospital)) {
                    List<ProjEquipRecord> productEquipRecordList = projEquipRecordList.stream().filter(a -> a.getHospitalInfoId().equals(param.getHospitalInfoId()) && a.getYyProductId().equals(param.getYyProductId())).collect(Collectors.toList());
                    ProjTodoTask projTodoTask = todoTaskListOneHospital.get(0);
                    if (!CollUtil.isEmpty(productEquipRecordList)) {
                        updateEquipPreparaTodoTaskOneHospital(productEquipRecordList, projTodoTask, planItemByCode);
                    }
                }
            } else {
                //更新所有医院设备对接待办状态及进度
                ProjProjectInfo projectInfo = projectInfoMapper.selectById(param.getProjectInfoId());
                Result<List<ProjHospitalInfoVO>> listResult = hospitalInfoService.findHospitalInfoByProjIdAndCustomerId(projectInfo.getCustomInfoId().toString(), param.getProjectInfoId().toString());
                List<ProjHospitalInfoVO> hospitalInfoList = listResult.getData();
                for (ProjHospitalInfoVO hospitalInfo : hospitalInfoList) {
                    List<ProjTodoTask> todoTaskListOneHospital = todoTaskList.stream().filter(a -> a.getHospitalInfoId().equals(Long.parseLong(hospitalInfo.getHospitalInfoId()))).collect(Collectors.toList());
                    if (!CollUtil.isEmpty(todoTaskListOneHospital)) {
                        List<ProjEquipRecord> productEquipRecordList = projEquipRecordList.stream().filter(a -> a.getHospitalInfoId().equals(Long.parseLong(hospitalInfo.getHospitalInfoId())) && a.getYyProductId().equals(param.getYyProductId())).collect(Collectors.toList());
                        ProjTodoTask projTodoTask = todoTaskListOneHospital.get(0);
                        if (!CollUtil.isEmpty(productEquipRecordList)) {
                            updateEquipPreparaTodoTaskOneHospital(productEquipRecordList, projTodoTask, planItemByCode);
                        }
                    }
                }
            }
            todoTaskTotalCountSync(param.getProjectInfoId(), planItemByCode.getPlanItemCode());
        } else {
            log.info("设备对接更新小前端大后端任务状态及进度失败：{}, projectInfoId={},projectPlanId={}", "未查询到对应的项目计划", param.getProjectInfoId(), projProjectPlan.getProjectPlanId());
        }
    }

    /**
     * 更新特定医院设备准备的待办任务状态
     * 此方法根据设备记录列表更新待办任务的完成状态、完成数量和总数量
     *
     * @param productEquipRecordList 设备记录列表，包含每个设备的状态
     * @param projTodoTask           待更新的待办任务对象
     * @param planItemByCode         项目计划项
     */

    private void updateEquipPreparaTodoTaskOneHospital(List<ProjEquipRecord> productEquipRecordList, ProjTodoTask projTodoTask, DictProjectPlanItemEnum planItemByCode) {
        // 初始化待办状态为未完成，完成数量和总数量分别为0和1
        int todoStatus = 2, todoCompleteCount = 0, todoTotalCount = 1;

        // 如果设备记录列表不为空，则进行后续处理
        if (!CollUtil.isEmpty(productEquipRecordList)) {
            // 计算设备记录总数
            todoTotalCount = productEquipRecordList.size();
            // 计算已完成的设备数量，即设备状态为5的数量
            todoCompleteCount = (int) (int) productEquipRecordList.stream().filter(a -> Integer.valueOf(5).equals(a.getEquipStatus())).count();

            if (DictProjectPlanItemEnum.PREPARAT_DEVICE.equals(planItemByCode)) {
                // 如果所有设备都已完成，则更新待办状态为已完成
                if (todoTotalCount == todoCompleteCount) {
                    todoStatus = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                } else if (ProjectPlanStatusEnum.UNFINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                    todoStatus = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                }
            } else {
                if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                    todoStatus = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                }
            }

            // 更新待办任务的状态、完成数量和总数量
            projTodoTask.setStatus(todoStatus);
            projTodoTask.setCompleteCount(todoCompleteCount);
            projTodoTask.setTotalCount(todoTotalCount);
            // 选择性更新待办任务，只更新有变化的字段
            todoTaskService.updateByPrimaryKeySelective(projTodoTask);
        }
    }

    /**
     * 更新第三方接口待办任务状态
     * 根据项目信息ID、医院信息ID和用户ID查询第三方接口状态，并根据查询结果更新待办任务的状态
     *
     * @param param           保存或更新待办任务的参数对象，包含项目信息ID、医院信息ID、用户ID等必要信息
     * @param projProjectPlan 项目计划对象，用于更新项目计划进度
     * @return 返回一个布尔值表示操作是否成功，当前方法始终返回true
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateThirdInterfaceTodoTaskStatus(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        //查询第三方接口数据
        QueryWrapper<ProjThirdInterface> projThirdInterfaceQueryWrapper = new QueryWrapper<>();
        projThirdInterfaceQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projThirdInterfaceQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        projThirdInterfaceQueryWrapper.eq("dir_person_id", param.getUserId());
        //projThirdInterfaceQueryWrapper.in("status", Arrays.asList(32, 50));
        projThirdInterfaceQueryWrapper.eq("online_flag", 1);
        projThirdInterfaceQueryWrapper.eq("is_deleted", 0);
        List<ProjThirdInterface> projThirdInterfaceList = projThirdInterfaceMapper.selectList(projThirdInterfaceQueryWrapper);

        // 初始化要处理的待办对象
        ProjTodoTask projTodoTask = null;
        // 根据项目计划ID、医院信息ID、产品ID和用户ID查询待办任务列表
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), param.getUserId());
        // 初始化完成计数和完成状态
        int completeCount = 0, completeStatus = 2, totalCount = projThirdInterfaceList.size();
        // 如果第三方接口列表不为空，计算完成的数量和状态
        if (!CollUtil.isEmpty(projThirdInterfaceList)) {
            completeCount = (int) projThirdInterfaceList.stream().filter(a -> Integer.valueOf(50).equals(a.getStatus()) || Integer.valueOf(32).equals(a.getStatus())).count();
            if (totalCount == completeCount) {
                completeStatus = 1;
            }
        }
        // 如果待办任务列表不为空，更新待办任务的状态和完成计数
        if (todoTaskList != null && !todoTaskList.isEmpty()) {
            projTodoTask = todoTaskList.get(0);
            projTodoTask.setStatus(completeStatus);
            projTodoTask.setCompleteCount(completeCount);
            projTodoTask.setTotalCount(totalCount);
            updateByPrimaryKeySelective(projTodoTask);
        }

        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_THIRD_PART.getPlanItemCode());
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
    }

    /**
     * 更新打印报表待办任务状态
     * 根据项目信息ID、医院信息ID和用户ID查询打印报表状态，并根据查询结果更新待办任务的状态
     *
     * @param param           保存或更新待办任务的参数对象，包含项目信息ID、医院信息ID、用户ID等必要信息
     * @param projProjectPlan 项目计划对象，用于更新项目计划进度
     * @return 返回一个布尔值表示操作是否成功，当前方法始终返回true
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSurveyReportTodoTaskStatus(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        log.info("同步打印报表任务状态,参数：param={},projProjectPlan", param, projProjectPlan);
        // 创建查询条件，用于获取调查报告列表
        QueryWrapper<ProjSurveyReport> projSurveyReportQueryWrapper = new QueryWrapper<>();
        projSurveyReportQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projSurveyReportQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        projSurveyReportQueryWrapper.eq("make_user_id", param.getUserId());
        projSurveyReportQueryWrapper.eq("online_essential", 1);
        projSurveyReportQueryWrapper.eq("is_deleted", 0);
        // 查询符合条件的调查报告列表
        List<ProjSurveyReport> projSurveyReportList = projSurveyReportMapper.selectList(projSurveyReportQueryWrapper);

        // 初始化要处理的待办对象
        ProjTodoTask projTodoTask = null;
        // 根据项目计划ID、医院信息ID、产品ID和用户ID查询待办任务列表
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), param.getHospitalInfoId(), param.getYyProductId(), param.getUserId());
        // 初始化完成计数和完成状态
        int completeCount = 0, completeStatus = 2, totalCount = projSurveyReportList.size();
        // 如果打印报表列表不为空，计算完成的数量和状态
        if (!CollUtil.isEmpty(projSurveyReportList)) {
            completeCount = (int) projSurveyReportList.stream().filter(a -> Integer.valueOf(3).equals(a.getFinishStatus())).count();
            if (totalCount == completeCount) {
                completeStatus = 1;
            }
        }
        // 如果待办任务列表不为空，更新待办任务的状态和完成计数
        if (todoTaskList != null && !todoTaskList.isEmpty()) {
            projTodoTask = todoTaskList.get(0);
            projTodoTask.setStatus(completeStatus);
            projTodoTask.setCompleteCount(completeCount);
            projTodoTask.setTotalCount(totalCount);
            // 选择性更新，只更新非空字段
            updateByPrimaryKeySelective(projTodoTask);
        }
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode());
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
    }

    /**
     * 更新统计报表待办任务状态
     * 根据项目信息ID、医院信息ID和用户ID查询统计报表状态，并根据查询结果更新待办任务的状态
     *
     * @param param           保存或更新待办任务的参数对象，包含项目信息ID、医院信息ID、用户ID等必要信息
     * @param projProjectPlan 项目计划对象，用于更新项目计划进度
     * @return 返回一个布尔值表示操作是否成功，当前方法始终返回true
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatiscsReportTodoTaskStatus(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        QueryWrapper<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityQueryWrapper = new QueryWrapper<>();
        projStatisticalReportMainEntityQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projStatisticalReportMainEntityQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(param.getCode());
        if (planItemByCode.equals(DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT)) {
            projStatisticalReportMainEntityQueryWrapper.eq("survey_user_id", param.getUserId());
        } else {
            projStatisticalReportMainEntityQueryWrapper.eq("allocate_user_id", param.getUserId());
        }
        projStatisticalReportMainEntityQueryWrapper.eq("online_flag", 1);
        projStatisticalReportMainEntityQueryWrapper.eq("is_deleted", 0);
        List<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityList = projStatisticalReportMainMapper.selectList(projStatisticalReportMainEntityQueryWrapper);

        // 初始化要处理的待办对象
        ProjTodoTask projTodoTask = null;
        // 根据项目计划ID、医院信息ID、产品ID和用户ID查询待办任务列表
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), -1L, param.getYyProductId(), param.getUserId());
        // 初始化完成计数和完成状态
        int completeCount = 0, completeStatus = 2, totalCount = projStatisticalReportMainEntityList.size();
        // 如果打印报表列表不为空，计算完成的数量和状态
        if (!CollUtil.isEmpty(projStatisticalReportMainEntityList)) {
            completeCount = (int) projStatisticalReportMainEntityList.stream().filter(a -> Integer.valueOf(31).equals(a.getReportStatus())).count();
            if (totalCount == completeCount) {
                completeStatus = 1;
            }
        }
        // 如果待办任务列表不为空，更新待办任务的状态和完成计数
        if (todoTaskList != null && !todoTaskList.isEmpty()) {
            projTodoTask = todoTaskList.get(0);
            projTodoTask.setStatus(completeStatus);
            projTodoTask.setCompleteCount(completeCount);
            projTodoTask.setTotalCount(totalCount);
            // 选择性更新，只更新非空字段
            updateByPrimaryKeySelective(projTodoTask);
        }
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT.getPlanItemCode());
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
    }

    /**
     * 更新表单待办任务状态
     * 根据项目信息ID、医院信息ID和用户ID查询表单状态，并根据查询结果更新待办任务的状态
     *
     * @param param           保存或更新待办任务的参数对象，包含项目信息ID、医院信息ID、用户ID等必要信息
     * @param projProjectPlan 项目计划对象，用于更新项目计划进度
     * @return 返回一个布尔值表示操作是否成功，当前方法始终返回true
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectFormTodoTaskStatus(SaveOrUpdateTodoTaskParam param, ProjProjectPlan projProjectPlan) {
        QueryWrapper<ProjSurveyForm> projSurveyFormQueryWrapper = new QueryWrapper<>();
        projSurveyFormQueryWrapper.eq("project_info_id", param.getProjectInfoId());
        projSurveyFormQueryWrapper.eq("hospital_info_id", param.getHospitalInfoId());
        DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(param.getCode());
        if (planItemByCode.equals(DictProjectPlanItemEnum.SURVEY_FORM)) {
            projSurveyFormQueryWrapper.eq("survey_user_id", param.getUserId());
        } else {
            projSurveyFormQueryWrapper.eq("make_user_id", param.getUserId());
        }
        projSurveyFormQueryWrapper.eq("online_essential", 1);
        projSurveyFormQueryWrapper.eq("is_deleted", 0);
        List<ProjSurveyForm> projSurveyFormList = projSurveyFormMapper.selectList(projSurveyFormQueryWrapper);

        // 初始化要处理的待办对象
        ProjTodoTask projTodoTask = null;
        // 根据项目计划ID、医院信息ID、产品ID和用户ID查询待办任务列表
        List<ProjTodoTask> todoTaskList = todoTaskMapper.selectByPlanAndProduct(projProjectPlan.getProjectPlanId(), -1L, param.getYyProductId(), param.getUserId());
        // 初始化完成计数和完成状态
        int completeCount = 0, completeStatus = 2, totalCount = projSurveyFormList.size();
        // 如果打印报表列表不为空，计算完成的数量和状态
        if (!CollUtil.isEmpty(projSurveyFormList)) {
            completeCount = (int) projSurveyFormList.stream().filter(a -> Integer.valueOf(3).equals(a.getFinishStatus())).count();
            if (totalCount == completeCount) {
                completeStatus = 1;
            }
        }
        // 如果待办任务列表不为空，更新待办任务的状态和完成计数
        if (todoTaskList != null && !todoTaskList.isEmpty()) {
            projTodoTask = todoTaskList.get(0);
            projTodoTask.setStatus(completeStatus);
            projTodoTask.setCompleteCount(completeCount);
            projTodoTask.setTotalCount(totalCount);
            // 选择性更新，只更新非空字段
            updateByPrimaryKeySelective(projTodoTask);
        }
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode());
        todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
    }

    //#endregion

    @Override
    public Result<JumpPageDetailResult> getJumpPageDetailParam(GetJumpDetailReq req) {
        Long sysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();

        ProjTodoTask projTodoTask = this.todoTaskMapper.selectByPrimaryKey(req.getTodoTaskId());

        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.getHospitalInfoById(projTodoTask.getHospitalInfoId());


        JumpPageDetailResult jumpPageDetailResult = new JumpPageDetailResult();
        jumpPageDetailResult.setYyProductId(projTodoTask.getYyProductId());
        jumpPageDetailResult.setSysUserId(sysUserId);
        jumpPageDetailResult.setHospitalName(hospitalInfo.getHospitalName());


        BaseIdNameResp product = dictProductMapper.getProductByYyProductId(req.getYyProductId());
        if (product != null) {
            jumpPageDetailResult.setProductName(product.getName());
            jumpPageDetailResult.setYyProductModuleId(-1L);
        } else {
            DictProductVsModules modules = dictProductVsModulesMapper.selectOne(new QueryWrapper<DictProductVsModules>()
                    .eq("is_deleted", 0)
                    .eq("yy_module_id", req.getYyProductId()));

            if (modules != null) {
                jumpPageDetailResult.setProductName(modules.getYyModuleName());
                jumpPageDetailResult.setYyProductModuleId(req.getYyProductId());
            }
        }

        // 产品业务调研
        if (DictProjectPlanItemEnum.SURVEY_PRODUCT.getPlanItemCode().equals(req.getProjectPlanItemCode())) {
            ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
            projSurveyPlanDTO.setProjectInfoId(projTodoTask.getProjectInfoId());
            projSurveyPlanDTO.setYyProductId(projTodoTask.getYyProductId());
            projSurveyPlanDTO.setSurveyUserId(sysUserId);
            projSurveyPlanDTO.setHospitalInfoId(projTodoTask.getHospitalInfoId());
            List<SurveyPlanTaskResp> surveyPlanList = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
            if (!CollectionUtils.isEmpty(surveyPlanList) && surveyPlanList.size() == 1) {
                ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
                projProductBacklogDTO.setCustomInfoId(req.getCustomInfoId());
                projProductBacklogDTO.setProjectInfoId(projTodoTask.getProjectInfoId());
                projProductBacklogDTO.setHospitalInfoId(projTodoTask.getHospitalInfoId());
                projProductBacklogDTO.setYyProductId(projTodoTask.getYyProductId());
                projProductBacklogDTO.setOperationSource("survey");
                projProductBacklogDTO.setSource("survey");
                projProductBacklogDTO.setSysUserId(sysUserId);
                projProductBacklogDTO.setDeptName(surveyPlanList.get(0).getDeptName());
                Result<List<ProductBacklogUrlVO>> listResult = surveyPlanService.selectSurveyProductJobMenuDetail(projProductBacklogDTO);
                jumpPageDetailResult.setNeedJumpSurveyProduct(false);
                jumpPageDetailResult.setList(listResult.getData());
                return Result.success(jumpPageDetailResult);
            }

            // 存在多个调研计划无法确认调研科室
            jumpPageDetailResult.setNeedJumpSurveyProduct(true);
            jumpPageDetailResult.setList(new ArrayList<>());
            return Result.success(jumpPageDetailResult);
        }

        // 设备调研或设备对接
        if (DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode().equals(req.getProjectPlanItemCode()) || DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode().equals(req.getProjectPlanItemCode())) {
            Result<List<EquipSummaryVo>> listResult = projEquipSummaryService.selectSurveyEquipMenu(projTodoTask.getProjectInfoId());
            ProductEquipSurveyMenuEnum equipSurveyMenuEnum = ProductEquipSurveyMenuEnum.getByYyProductId(projTodoTask.getYyProductId());
            if (null == equipSurveyMenuEnum) {
                throw new IllegalArgumentException("当前产品不需要进行设备调研/设备对接，产品ID=" + projTodoTask.getYyProductId());
            }

            String code = equipSurveyMenuEnum.getCode();
            EquipSummaryVo equipSummaryVo = listResult.getData().stream().filter(item -> code.equals(item.getName())).findFirst().orElse(null);
            if (equipSummaryVo == null) {
                jumpPageDetailResult.setList(new ArrayList<>());
                return Result.success(jumpPageDetailResult);
            }

            List<ProductBacklogUrlVO> list = new ArrayList<>();
            ProductBacklogUrlVO productBacklogUrlVO = new ProductBacklogUrlVO();
            productBacklogUrlVO.setMenuCode(equipSummaryVo.getName());
            productBacklogUrlVO.setMenuName(equipSummaryVo.getTitle());
            list.add(productBacklogUrlVO);

            jumpPageDetailResult.setList(list);
            return Result.success(jumpPageDetailResult);
        }

        // 产品准备工作
        if (DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode().equals(req.getProjectPlanItemCode())) {
            ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectByProjectAndCode(projTodoTask.getProjectInfoId(), req.getProjectPlanItemCode());
            ProjProductBacklogDTO dto = new ProjProductBacklogDTO();
            dto.setProjectInfoId(projTodoTask.getProjectInfoId());
            dto.setHospitalInfoId(projTodoTask.getHospitalInfoId());
            dto.setMilestoneTaskDetailId(projMilestoneInfo.getMilestoneInfoId());
            ProjProductBacklogDataVO projProductBacklogDataVO = productBacklogService.selectProductBacklog(dto);
            ProjProductBacklogVO projProductBacklogVO = projProductBacklogDataVO.getBacklogList().stream().filter(item -> req.getYyProductId().equals(item.getYyProductId()) || req.getYyProductId().equals(item.getYyProductModuleId())).findFirst().orElse(null);
            jumpPageDetailResult.setBaseDataStatus(projProductBacklogVO.getBaseDataStatus());
            jumpPageDetailResult.setConfigDataStatus(projProductBacklogVO.getConfigDataStatus());
            jumpPageDetailResult.setTodoTaskStatus(projProductBacklogVO.getTodoTaskStatus());
            jumpPageDetailResult.setReportDataStatus(projProductBacklogVO.getReportDataStatus());
            jumpPageDetailResult.setFormDataStatus(projProductBacklogVO.getFormDataStatus());
            jumpPageDetailResult.setCompleteStatus(projProductBacklogVO.getCompleteStatus());

            ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
            projProductBacklogDTO.setCustomInfoId(req.getCustomInfoId());
            projProductBacklogDTO.setProjectInfoId(projTodoTask.getProjectInfoId());
            projProductBacklogDTO.setHospitalInfoId(projTodoTask.getHospitalInfoId());
            projProductBacklogDTO.setYyProductId(projTodoTask.getYyProductId());
            projProductBacklogDTO.setUserId(sysUserId);
            Result<List<ProductBacklogUrlVO>> listResult1 = productBacklogService.selectBacklogUrlList(projProductBacklogDTO);
            jumpPageDetailResult.setList(listResult1.getData());
            return Result.success(jumpPageDetailResult);
        }
        throw new IllegalArgumentException("当前场景暂不支持");
    }

    @Resource
    private ProjSurveyPlanService projSurveyPlanService;

    /**
     * 同步待办任务的总数量
     * 此方法用于同步更新各段对应专项的待办总任务数量
     * 它的目标是确保系统中的待办任务计数与实际的未完成任务数量保持一致
     *
     * @param projectInfoId 项目信息的ID，用于指定待办任务所属的项目
     * @param itemCode      待办事项的代码，用于标识特定的待办任务类型
     * @return 返回一个布尔值，通常用于指示同步操作是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean todoTaskTotalCountSync(Long projectInfoId, String itemCode) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        try {
            ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
            DictProjectPlanItemEnum planItemByCode = DictProjectPlanItemEnum.getPlanItemByCode(itemCode);
            int totalCount = 0;
            int completeCount = 0;
            int status = 2;
            List<ProjTodoTask> todoTaskList = selectTodoTaskByProjectInfoIdAndItemCode(projectInfoId, -1L, itemCode, null);
            List<ProjTodoTask> tmpList = todoTaskList.stream().filter(a -> a.getYyProductId() == null).collect(Collectors.toList());
            ProjTodoTask projTodoTask = null;
            if (!tmpList.isEmpty()) {
                projTodoTask = tmpList.get(0);
            }
            switch (planItemByCode) {
                case SURVEY_PRODUCT:
                    List<ProjProductDeliverRecord> projProductDeliverRecordList = projProductDeliverRecordService.getSurveyProductDeliverRecord(projectInfoId, null);
                    totalCount = projProductDeliverRecordList.size();
                    // 无需调研的产品
                    int notNeedSurveyProductCount = projSurveyPlanService.getNotNeedSurveyProductCount(projectInfoId);
                    // 完成调研的产品数量
                    int finishedProductCount = surveyPlanService.getFinishedProductCount(projectInfoId);
                    completeCount = notNeedSurveyProductCount + finishedProductCount;
                    if (totalCount == completeCount && totalCount != 0) {
                        status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                    }
                    break;
                case PREPARAT_PRODUCT:
                    //产品准备工作处理
                    ProjProductBacklogDTO productBacklogDTO = new ProjProductBacklogDTO();
                    productBacklogDTO.setProjectInfoId(projectInfoId);
                    List<ProjProductBacklogVO> backlogList = productBacklogService.findProductBacklogVOS(productBacklogDTO);

                    totalCount = surveyPlanService.getFinishedProductCount(projectInfoId);
                    List<Long> productIdList = backlogList.stream().filter(item -> Integer.valueOf(1).equals(item.getCompleteStatus())).map(ProjProductBacklogVO::getYyProductId).distinct().collect(Collectors.toList());
                    completeCount = productIdList.size();
                    if (totalCount == completeCount && totalCount != 0) {
                        status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        log.info("更新个人项目待办，总数与完成数相等，状态={}", status);
                    } else if (projTodoTask != null) {
                        if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                            log.info("更新个人项目待办，保留已完成状态，状态={}", status);
                        } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                            status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            log.info("更新个人项目待办，项目为已入驻，更新为进行中，状态={}", status);
                        } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                            status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            log.info("更新个人项目待办，项目为已派工已调研，更新为未开始，状态={}", status);
                        } else {
                            status = projTodoTask.getStatus();
                            log.info("更新个人项目待办，保留待办原有状态，状态={}", status);
                        }
                    }
                    break;
                case SURVEY_DEVICE:
                case PREPARAT_DEVICE:
                    //设备对接工作处理
                    QueryWrapper<ProjEquipRecord> projEquipRecordQueryWrapper = new QueryWrapper<>();
                    projEquipRecordQueryWrapper.eq("project_info_id", projectInfoId);
                    projEquipRecordQueryWrapper.eq("is_deleted", 0);
                    if (DictProjectPlanItemEnum.PREPARAT_DEVICE.equals(planItemByCode)) {
                        projEquipRecordQueryWrapper.eq("required_flag", 1);
                    }
                    List<ProjEquipRecord> projEquipRecordList = projEquipRecordMapper.selectList(projEquipRecordQueryWrapper);
                    totalCount = projEquipRecordList.size();
                    if (DictProjectPlanItemEnum.PREPARAT_DEVICE.equals(planItemByCode)) {
                        completeCount = (int) projEquipRecordList.stream().filter(item -> Integer.valueOf(5).equals(item.getEquipStatus())).count();
                    }

                    if (DictProjectPlanItemEnum.SURVEY_DEVICE.equals(planItemByCode)) {
                        if (projTodoTask != null && ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        }
                    } else {
                        if (totalCount == completeCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        } else if (projTodoTask != null) {
                            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            } else {
                                status = projTodoTask.getStatus();
                            }
                        }
                    }
                    break;
                case SURVEY_THIRD_PART:
                case SCHEDULE_THIRD_PART:
                    //三方接口工作处理
                    QueryWrapper<ProjThirdInterface> projThirdInterfaceQueryWrapper = new QueryWrapper<>();
                    projThirdInterfaceQueryWrapper.eq("project_info_id", projectInfoId);
                    //projThirdInterfaceQueryWrapper.eq("hospital_info_id", Long.valueOf(hospitalInfo.getHospitalInfoId()));
                    if (DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.equals(planItemByCode)) {
                        projThirdInterfaceQueryWrapper.eq("online_flag", 1);
                    }
                    projThirdInterfaceQueryWrapper.eq("is_deleted", 0);
                    List<ProjThirdInterface> projThirdInterfaceList = projThirdInterfaceMapper.selectList(projThirdInterfaceQueryWrapper);
                    totalCount = projThirdInterfaceList.size();
                    if (DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.equals(planItemByCode)) {
                        completeCount = (int) projThirdInterfaceList.stream().filter(a -> Integer.valueOf(50).equals(a.getStatus()) || Integer.valueOf(32).equals(a.getStatus())).count() + (int) projThirdInterfaceList.stream().filter(a -> Integer.valueOf(13).equals(a.getStatus()) && Integer.valueOf(2).equals(a.getInterfaceType())).count();
                    }

                    if (DictProjectPlanItemEnum.SURVEY_THIRD_PART.equals(planItemByCode)) {
                        if (projTodoTask != null && ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        }
                    } else {
                        if (totalCount == completeCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        } else if (projTodoTask != null) {
                            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            } else {
                                status = projTodoTask.getStatus();
                            }
                        }
                    }
                    break;
                case SURVEY_REPORT:
                case PREPARAT_REPORT:
                    //打印报表工作处理
                    QueryWrapper<ProjSurveyReport> projSurveyReportQueryWrapper = new QueryWrapper<>();
                    projSurveyReportQueryWrapper.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_REPORT.equals(planItemByCode)) {
                        projSurveyReportQueryWrapper.eq("online_essential", 1);
                    }
                    projSurveyReportQueryWrapper.eq("is_deleted", 0);
                    List<ProjSurveyReport> projSurveyReportList = projSurveyReportMapper.selectList(projSurveyReportQueryWrapper);
                    totalCount = projSurveyReportList.size();
//                    completeCount = (int) projSurveyReportList.stream().filter(item -> Integer.valueOf(8).equals(item.getFinishStatus()) || Integer.valueOf(1).equals(item.getFinishStatus())).count();
                    List<Integer> preparatReportFinishedStatus = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode(), 3);
                    completeCount = this.projSurveyReportMapper.getFinishedReportCount(projectInfoId, null, null, preparatReportFinishedStatus, 1);
                    log.info("打印报表调研同步完成数量，已完成数={}", completeCount);
                    if (DictProjectPlanItemEnum.SURVEY_REPORT.equals(planItemByCode)) {
                        if (projTodoTask != null && ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        }
                        List<Integer> finishedStatus = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode(), 3);
                        completeCount = this.projSurveyReportMapper.getFinishedReportCount(projectInfoId, null, null, finishedStatus, null);
                        log.info("打印报表调研同步完成数量，已完成数={}", completeCount);
                    } else {
                        if (totalCount == completeCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        } else if (projTodoTask != null) {
                            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            } else {
                                status = projTodoTask.getStatus();
                            }
                        }
                    }
                    break;
                case SURVEY_STATISTICS_REPORT:
                case PREPARAT_STATISTICS_REPORT:
                    //统计报表工作处理
                    QueryWrapper<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityQueryWrapper = new QueryWrapper<>();
                    projStatisticalReportMainEntityQueryWrapper.eq("project_info_id", projectInfoId);
                    //projStatisticalReportMainEntityQueryWrapper.eq("hospital_info_id", Long.valueOf(hospitalInfo.getHospitalInfoId()));
                    if (DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.equals(planItemByCode)) {
                        projStatisticalReportMainEntityQueryWrapper.eq("online_flag", 1);
                    }
                    projStatisticalReportMainEntityQueryWrapper.eq("is_deleted", 0);
                    List<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityList = projStatisticalReportMainMapper.selectList(projStatisticalReportMainEntityQueryWrapper);
                    totalCount = projStatisticalReportMainEntityList.size();
                    completeCount = (int) projStatisticalReportMainEntityList.stream().filter(item -> Integer.valueOf(22).compareTo(item.getReportStatus()) <= 0 || (Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0 && StatisticalReportMethodModel.getMethedList().contains(item.getProductionMethodId()))).count();

                    int surveyCount = projStatisticalReportMainEntityList.stream().filter(item -> Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0).collect(Collectors.toList()).size();

                    if (DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT.equals(planItemByCode)) {
                        if (totalCount == surveyCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        }
                    } else {
                        if (totalCount == completeCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        } else if (projTodoTask != null) {
                            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            } else {
                                status = projTodoTask.getStatus();
                            }
                        }
                    }
                    break;
                case PREPARAT_STATISTICS_RELEASE:
                    //统计报表工作处理
                    QueryWrapper<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityQueryWrapperrrz = new QueryWrapper<>();
                    projStatisticalReportMainEntityQueryWrapperrrz.eq("project_info_id", projectInfoId);
                    if (DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.equals(planItemByCode) || DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.equals(planItemByCode)) {
                        projStatisticalReportMainEntityQueryWrapperrrz.eq("online_flag", 1);
                    }
                    projStatisticalReportMainEntityQueryWrapperrrz.eq("is_deleted", 0);
                    List<ProjStatisticalReportMainEntity> projStatisticalReportMainEntityListee = projStatisticalReportMainMapper.selectList(projStatisticalReportMainEntityQueryWrapperrrz);
                    totalCount = projStatisticalReportMainEntityListee.size();
                    completeCount = (int) projStatisticalReportMainEntityListee.stream().filter(item -> Integer.valueOf(31).compareTo(item.getReportStatus()) <= 0 || (Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0 && StatisticalReportMethodModel.getMethedList().contains(item.getProductionMethodId()))).count();
                    int surveyResCount = projStatisticalReportMainEntityListee.stream().filter(item -> Integer.valueOf(13).compareTo(item.getReportStatus()) <= 0).collect(Collectors.toList()).size();

                    if (DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT.equals(planItemByCode)) {
                        if (totalCount == surveyResCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        }
                    } else {
                        if (totalCount == completeCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        } else if (projTodoTask != null) {
                            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            } else {
                                status = projTodoTask.getStatus();
                            }
                        }
                    }
                    break;
                case SURVEY_FORM:
                case PREPARAT_FORM:
                    //表单工作处理
                    QueryWrapper<ProjSurveyForm> projSurveyFormQueryWrapper = new QueryWrapper<>();
                    projSurveyFormQueryWrapper.eq("project_info_id", projectInfoId);
                    //projSurveyFormQueryWrapper.eq("hospital_info_id", Long.valueOf(hospitalInfo.getHospitalInfoId()));
                    if (DictProjectPlanItemEnum.PREPARAT_FORM.equals(planItemByCode)) {
                        projSurveyFormQueryWrapper.eq("online_essential", 1);
                    }
                    projSurveyFormQueryWrapper.eq("is_deleted", 0);
                    List<ProjSurveyForm> projSurveyFormList = projSurveyFormMapper.selectList(projSurveyFormQueryWrapper);
                    totalCount = projSurveyFormList.size();
                    completeCount = (int) projSurveyFormList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus()) || Integer.valueOf(8).equals(item.getFinishStatus())).count();

                    if (DictProjectPlanItemEnum.SURVEY_FORM.equals(planItemByCode)) {
                        if (projTodoTask != null && ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        }
                        List<Long> businessIds = projSurveyFormList.stream().map(ProjSurveyForm::getSurveyFormId).collect(Collectors.toList());
                        List<ProjBusinessExamineLog> list = projBusinessExamineLogService.selectListByIds(businessIds);
                        if (list != null && list.size() > 0) {
                            completeCount = list.size();
                        }
                    } else {
                        if (totalCount == completeCount && totalCount != 0) {
                            status = ProjectPlanStatusEnum.FINISHED.getStatusCode();
                        } else if (projTodoTask != null) {
                            if (ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projTodoTask.getStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.SETTLED.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNDERWAY.getStatusCode();
                            } else if (ProjectDeliverStatusEnums.DELIVERED.getCode().equals(projectInfo.getProjectDeliverStatus()) || ProjectDeliverStatusEnums.RESEARCHING.getCode().equals(projectInfo.getProjectDeliverStatus())) {
                                status = ProjectPlanStatusEnum.UNFINISHED.getStatusCode();
                            } else {
                                status = projTodoTask.getStatus();
                            }
                        }
                    }
                    break;
                default:
                    break;
            }

            if (projTodoTask != null) {
                projTodoTask.setTotalCount(totalCount);
                projTodoTask.setCompleteCount(completeCount);
                projTodoTask.setStatus(status);
                this.updateByPrimaryKeySelective(projTodoTask);
            }

            //更新计划总数量
            projProjectPlanService.projectPlanTotalCountSync(projectInfoId, itemCode);
        } catch (Exception e) {
            log.error("同步待办任务的总数量失败,参数：itemCode={},projectInfoId={};错误信息：{}", itemCode, projectInfoId, e.getMessage());
            return false;
        }
        return true;
    }


    /**
     * 刷新待办任务列表
     * 此方法用于更新或重新加载待办任务列表，以确保数据的最新和准确性
     *
     * @param todoTaskList 待办任务列表，包含多个ProjTodoTask对象
     * @param personList   包含多个Long类型的人员ID
     */
    private void refreshTodoTask(List<ProjTodoTask> todoTaskList, List<Long> personList) {
        log.info("刷新待办任务列表--->");
        //批量删除无实际工作内容的待办任务
        if (ObjectUtil.isNotEmpty(todoTaskList)) {
            List<Long> finalPersonList = personList;
            //List<ProjTodoTask> todoTaskList1 = todoTaskList.stream().filter(x -> !finalPersonList.contains(x.getImplementationEngineerId())).collect(Collectors.toList());
            //List<ProjTodoTask> todoTaskList1 = new ArrayList<>();
            for (ProjTodoTask projTodoTask : todoTaskList) {
                if (!finalPersonList.contains(projTodoTask.getImplementationEngineerId())) {
                    log.info("删除无实际工作内容的待办任务,删除的任务对象：{}", projTodoTask);
                    projTodoTask.setIsDeleted(1);
                    todoTaskMapper.updateById(projTodoTask);
                }
            }
/*            if (!todoTaskList1.isEmpty()) {
                log.info("批量删除无实际工作内容的待办任务,删除的任务明细：{}", todoTaskList1);
                List<ProjTodoTask> updateTodoList = todoTaskList1.stream().map(x -> {
                    x.setIsDeleted(1);
                    return x;
                }).collect(Collectors.toList());
                todoTaskMapper.updateBatch(updateTodoList);
            }*/
        }
    }

    @Override
    public Result<TodoTaskResp> queryToDoTaskInfo(QueryTodoTaskInfoReq req) {
        QueryTodoTaskReq queryTodoTaskReq = new QueryTodoTaskReq();
        BeanUtils.copyProperties(req, queryTodoTaskReq);
        Result<List<TodoTaskResp>> listResult = this.queryData(queryTodoTaskReq);
        List<TodoTaskResp> data = listResult.getData();
        if (org.springframework.util.CollectionUtils.isEmpty(data)) {
            return Result.success(null, "没查到数据");
        }
        TodoTaskResp todoTaskResp = data.stream().filter(item -> item.getTodoTaskId().equals(req.getTodoTaskId())).findFirst().orElse(null);
        return Result.success(todoTaskResp, todoTaskResp == null ? "没查到数据" : "成功");
    }

    @Override
    public boolean updateTodoTaskStatus(Long projectInfoId, DictProjectPlanItemEnum planItemEnum, ProjectPlanStatusEnum statusEnum) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        ProjProjectPlan projectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, planItemEnum);
        if (projectPlan == null) {
            return true;
        }
        switch (planItemEnum) {
            case SURVEY_PRODUCT:
                // 更新我的待办
                ProjTodoTask projTodoTask = todoTaskMapper.selectOne(new QueryWrapper<ProjTodoTask>()
                        .eq("is_deleted", 0)
                        .eq("project_plan_id", projectPlan.getProjectPlanId())
                        .eq(" project_info_id", projectInfoId)
                        .isNull("yy_product_id")
                );
                ProjTodoTask updateParam = new ProjTodoTask();
                updateParam.setTodoTaskId(projTodoTask.getTodoTaskId());
                updateParam.setStatus(statusEnum.getStatusCode());
                todoTaskService.updateByPrimaryKeySelective(updateParam);
                return true;
            case SURVEY_THIRD_PART:
                ProjTodoTask projTodoTask2 = todoTaskMapper.selectOne(new QueryWrapper<ProjTodoTask>()
                        .eq("is_deleted", 0)
                        .eq("project_plan_id", projectPlan.getProjectPlanId())
                        .eq(" project_info_id", projectInfoId)
                        .eq("hospital_info_id", -1)
                        .isNull("yy_product_id")
                );

                ProjTodoTask updateParam2 = new ProjTodoTask();
                updateParam2.setTodoTaskId(projTodoTask2.getTodoTaskId());
                updateParam2.setStatus(statusEnum.getStatusCode());
                todoTaskService.updateByPrimaryKeySelective(updateParam2);
                return true;
            case SURVEY_DEVICE:
                ProjTodoTask projTodoTask3 = todoTaskMapper.selectOne(new QueryWrapper<ProjTodoTask>()
                        .eq("is_deleted", 0)
                        .eq("project_plan_id", projectPlan.getProjectPlanId())
                        .eq(" project_info_id", projectInfoId)
                        .eq("hospital_info_id", -1)
                        .isNull("yy_product_id")
                );

                ProjTodoTask updateParam3 = new ProjTodoTask();
                updateParam3.setTodoTaskId(projTodoTask3.getTodoTaskId());
                updateParam3.setStatus(statusEnum.getStatusCode());
                todoTaskService.updateByPrimaryKeySelective(updateParam3);
                return true;
            default:
                return false;
        }
    }

    @Override
    public boolean updateTodoTaskStatusOne(Long projectInfoId, DictProjectPlanItemEnum planItemEnum, Long yyProductId, ProjectPlanStatusEnum statusEnum) {
        boolean planModel = projectConfigService.isPlanModel(projectInfoId);
        if (!planModel) {
            log.info("当前项目未开启小前端大后端模式，projectInfoId={}", projectInfoId);
            return true;
        }
        ProjProjectPlan projectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId, planItemEnum);
        if (projectPlan == null) {
            return true;
        }
        switch (planItemEnum) {
            case SURVEY_DEVICE:
                List<ProjTodoTask> projTodoTaskList = todoTaskMapper.selectList(new QueryWrapper<ProjTodoTask>()
                        .eq("is_deleted", 0)
                        .eq("project_plan_id", projectPlan.getProjectPlanId())
                        .eq(" project_info_id", projectInfoId)
                        .eq("yy_product_id", yyProductId)
                );
                log.info("设备对接，projTodoTaskList={}", JSON.toJSONString(projTodoTaskList));

                List<ProjTodoTask> collect = projTodoTaskList.stream().map(x -> {
                    x.setStatus(statusEnum.getStatusCode());
                    return x;
                }).collect(Collectors.toList());
                log.info("设备对接，projTodoTaskList={}", JSON.toJSONString(collect));

                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(collect)) {
                    todoTaskMapper.updateBatch(collect);
                }

                return true;
            default:
                return false;
        }
    }

    @Override
    public List<ProjTodoTask> getTodoTaskByProjectAndPlan(Long projectInfoId, Long planId) {
        return todoTaskMapper.getTodoTaskByProjectAndPlan(projectInfoId, planId);
    }

    @Override
    public Result<List<BaseIdCodeNameResp>> queryTodoTask(SimpleId simpleId) {
        QueryTodoTaskReq queryTodoTaskReq = new QueryTodoTaskReq();
        queryTodoTaskReq.setProjectInfoId(simpleId.getId());

        Result<List<TodoTaskResp>> respList = this.queryData(queryTodoTaskReq);
        if (respList == null || org.springframework.util.CollectionUtils.isEmpty(respList.getData())) {
            return Result.success(new ArrayList<>());
        }
        List<BaseIdCodeNameResp> collect = respList.getData().stream().map(item -> {
            String productName = org.apache.commons.lang3.StringUtils.isBlank(item.getProductName()) ? "" : "（" + item.getProductName() + "）";
            return new BaseIdCodeNameResp(String.valueOf(item.getTodoTaskId()), null, item.getTitle() + productName);
        }).collect(Collectors.toList());
        return Result.success(collect);
    }

    @Override
    public List<ProjTodoTask> selectTodoTaskByTaskIdList(List<Long> todoTaskIds) {
        return todoTaskMapper.selectTodoTaskByTaskIdList(todoTaskIds);
    }

    @Override
    public Result<Void> updateTodoTaskByBatch(List<UpdateTodoTaskReq> req) {
        if (org.springframework.util.CollectionUtils.isEmpty(req)) {
            return Result.success(null, "没有需要更新的数据");
        }
        for (UpdateTodoTaskReq todoTaskReq : req) {
            this.updateData(todoTaskReq);
        }
        return Result.success(null, "更新成功");
    }
}
