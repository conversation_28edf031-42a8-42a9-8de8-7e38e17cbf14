package com.msun.csm.service.config;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/1 9:58
 */
@Service
public class DictProjectSchemaServiceImpl implements DictProjectSchemaService{
    @Override
    public Object generateProjectPlan(GenerateProjectPlanBaseArgs args) {
        return null;
    }

    @Override
    public Object generateProjectPlanByBizType(GenerateProjectPlanBaseArgs args) {
        return null;
    }

    @Override
    public Object generateProjectPlanByProductType(GenerateProjectPlanBaseArgs args) {
        return null;
    }

    @Override
    public Object generateProjectPlanByDeliveryOrderType(GenerateProjectPlanBaseArgs args) {
        return null;
    }
}
