package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.SystemSettingWorkflowApi;
import com.msun.core.component.implementation.api.imsp.dto.ThridWorkflowResourceQueryDTO;
import com.msun.core.component.implementation.api.imsp.vo.ThridWorkflowResourceQueryVO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjInterfaceVsAuthorMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.feign.client.dataapplication.DataApplicationFeignClient;
import com.msun.csm.feign.client.openapi.OpenApiPlatformFeignClient;
import com.msun.csm.feign.entity.dataapplication.ResDataResp;
import com.msun.csm.feign.entity.openapi.resp.ApiGroupResp;
import com.msun.csm.feign.entity.openapi.resp.ApiInterfaceResp;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.resp.thirdinterface.ThridWorkflowResourceQueryResp;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.Md5Util;
import com.msun.csm.util.PinyinUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */
@Slf4j
@Service
public class ProjInterfaceVsAuthorServiceImpl implements ProjInterfaceVsAuthorService {

    @Value("${project.feign.openApi.sign-key}")
    private String generateKey;
    @Value("${project.feign.openApi.header-user_value}")
    private String platformUser;

    @Resource
    private ProjInterfaceVsAuthorMapper projInterfaceVsAuthorMapper;
    @Resource
    private OpenApiPlatformFeignClient openApiPlatformFeignClient;
    @Resource
    private DataApplicationFeignClient dataApplicationFeignClient;

    @Resource
    private SystemSettingWorkflowApi systemSettingWorkflowApi;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Lazy
    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;

    @Override
    public int deleteByPrimaryKey(Long interfaceVsAuthorId) {
        return projInterfaceVsAuthorMapper.deleteByPrimaryKey(interfaceVsAuthorId);
    }

    @Override
    public int insert(ProjInterfaceVsAuthor record) {
        return projInterfaceVsAuthorMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjInterfaceVsAuthor record) {
        return projInterfaceVsAuthorMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjInterfaceVsAuthor record) {
        return projInterfaceVsAuthorMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjInterfaceVsAuthor record) {
        return projInterfaceVsAuthorMapper.insertSelective(record);
    }

    @Override
    public ProjInterfaceVsAuthor selectByPrimaryKey(Long interfaceVsAuthorId) {
        return projInterfaceVsAuthorMapper.selectByPrimaryKey(interfaceVsAuthorId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjInterfaceVsAuthor record) {
        return projInterfaceVsAuthorMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjInterfaceVsAuthor record) {
        return projInterfaceVsAuthorMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjInterfaceVsAuthor> list) {
        return projInterfaceVsAuthorMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjInterfaceVsAuthor> list) {
        return projInterfaceVsAuthorMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjInterfaceVsAuthor> list) {
        return projInterfaceVsAuthorMapper.batchInsert(list);
    }

    /**
     * 获取接口分组列表
     *
     * @return
     */
    @Override
    public List<ApiGroupResp> getApiGroupList() {
        String accessToken = getAccessToken();
        String result = openApiPlatformFeignClient.getApiGroupList(accessToken, platformUser);
        log.info("调用能力开放平台-获取接口分组列表-result:{}", result);
        ResponseResult responseResult = JSONObject.parseObject(result, ResponseResult.class);
        if (!responseResult.isSuccess()) {
            log.error(responseResult.toString());
            throw new CustomException(responseResult.getTraceId() + StrUtil.DASHED + responseResult.getMessage());
        }
        try {
            Object data = responseResult.getData();
            if (data == null) {
                log.warn("分组数据为空");
                return Collections.emptyList();
            }
            // 显式声明完整泛型结构（假设 ApiGroupResp 包含 List<ApiGroupResp> 子节点）
            TypeReference<List<ApiGroupResp>> typeReference = new TypeReference<List<ApiGroupResp>>() {
            };
            return JSONObject.parseObject(data.toString(), typeReference.getType(), new Feature[] {Feature.SupportArrayToBean});
        } catch (Exception e) {
            log.error("分组列表反序列化失败: {}", e.getMessage(), e);
            throw new CustomException("分组数据格式异常");
        }
    }

    /**
     * 获取接口列表
     *
     * @return
     */
    @Override
    public List<ApiInterfaceResp> getApiList(Long groupId) {
        String accessToken = getAccessToken();
        String result = openApiPlatformFeignClient.getApiList(accessToken, platformUser, groupId);
        ResponseResult responseResult = JSONObject.parseObject(result, ResponseResult.class);
        if (!responseResult.isSuccess()) {
            log.error(responseResult.toString());
            throw new CustomException(responseResult.getTraceId() + StrUtil.DASHED + responseResult.getMessage());
        }
        List<ApiInterfaceResp> respList = JSONObject.parseArray(JSONObject.toJSONString(responseResult.getData()), ApiInterfaceResp.class);
        return respList;
    }

    /**
     * 数据中台-获取数据集
     *
     * @return
     */
    @Override
    public List<ResDataResp> getResDataList() {
        final String gateway = "app";
        String resp = dataApplicationFeignClient.getResDataClassList(gateway);
        log.info("调用接口返回结果{}", resp);
        ResponseResult response = JSONObject.parseObject(resp, ResponseResult.class);
        Map<String, Object> result = (Map<String, Object>) response.getData();
        List<ResDataResp> resDataRespList = JSONArray.parseArray(JSONObject.toJSONString(result.get("list")), ResDataResp.class);
        return resDataRespList;
    }

    /**
     * MD5加密
     *
     * @return
     */
    @Override
    public String getAccessToken() {
        String timeStamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String toSignStr = timeStamp + generateKey; //待签名字符串
        String sign; //签名值
        try {
            sign = Md5Util.md5Hex(MessageDigest.getInstance("SHA-512").digest(toSignStr.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new CustomException("签名失败");
        }
        String accessToken = timeStamp + "." + sign; //鉴权令牌值
        log.info("accessToken:{}", accessToken);
        return accessToken;
    }

    /**
     * 获取埋点下拉数据--系统管理接口
     * @param projectInfoId
     * @return
     */
    @Override
    public Result getWorkflowDataList(Long projectInfoId) {
        ProjHospitalInfo projHospitalInfo = new ProjHospitalInfo();
        if (projectInfoId != null &&  projectInfoId != -1) {
            SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
            selectHospitalDTO.setProjectInfoId(projectInfoId);
            List<ProjHospitalInfoRelative> hoslist = projHospitalInfoMapper.getHospitalInfoRelativeByHospitalDTO(selectHospitalDTO);
            if (hoslist != null && hoslist.size() > 0) {
                ProjHospitalInfoRelative projHospitalInfoRelative = hoslist.get(0);
                if (projHospitalInfoRelative.getCloudHospitalId() != null && projHospitalInfoRelative.getHospitalOpenStatus() >= NumberEnum.NO_21.num()) {
                    projHospitalInfo = projHospitalInfoMapper.selectById(projHospitalInfoRelative.getHospitalInfoId());
                } else {
                    throw new CustomException("云资源未部署,请在部署后进行埋点接口的申请。");
                }
            }
        } else {
          List<ProjThirdInterface> list = projThirdInterfaceMapper.selectListToNewTime();
          if (list != null && !list.isEmpty()) {
              projHospitalInfo = projHospitalInfoMapper.selectById(list.get(0).getHospitalInfoId());
          }
        }
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);

        ThridWorkflowResourceQueryDTO dto = new ThridWorkflowResourceQueryDTO();
        dto.setPageNum(1);
        dto.setPageSize(200);
        dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
        dto.setHisOrgId(projHospitalInfo.getOrgId());
        dto.setOrgId(projHospitalInfo.getOrgId());
        dto.setGroupCode("XTGL_MDPZ_SH");

        ResponseResult<List<ThridWorkflowResourceQueryVO>> result = systemSettingWorkflowApi.query(dto);
        if (result.isSuccess()) {
            List<ThridWorkflowResourceQueryResp> listReturn = new ArrayList<>();
            List<ThridWorkflowResourceQueryVO> voList = result.getData();
            if (voList != null) {
                for (ThridWorkflowResourceQueryVO vo : voList) {
                    ThridWorkflowResourceQueryResp resp = new ThridWorkflowResourceQueryResp();
                    BeanUtil.copyProperties(vo, resp);
                    resp.setId(String.valueOf(vo.getId()));
                    resp.setResourceId(String.valueOf(vo.getResourceId()));
                    resp.setGroupId(String.valueOf(vo.getGroupId()));
                    listReturn.add(resp);
                }
            }
            return Result.success(listReturn);
        }
        return null;
    }

    /**
     * 更新接口分类
     *
     * @return
     */
    @Override
    public Result updateInterfaceCategory() {
        try {
            List<ResDataResp> list = this.getResDataList();
            if (list != null && list.size() > 0) {
                for (ResDataResp resDataResp : list) {
                    List<ProjThirdInterface> projThirdInterfaces = projThirdInterfaceMapper.selectList(new QueryWrapper<ProjThirdInterface>().eq("interface_type", 0).eq("dict_interface_name", resDataResp.getDataClassName()));
                    if (projThirdInterfaces != null && projThirdInterfaces.size() > 0) {
                        for (ProjThirdInterface projThirdInterface : projThirdInterfaces) {
                            projThirdInterface.setInterfaceCategory(resDataResp.getDataClassSecondName());
                            projThirdInterface.setInterfaceCategoryCode(resDataResp.getDataClassSecondCode());
                            projThirdInterface.setDictInterfaceCode(resDataResp.getDataClassName());
                            projThirdInterfaceMapper.updateById(projThirdInterface);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        List<ProjThirdInterface> projThirdInterfaces = projThirdInterfaceMapper.selectList(new QueryWrapper<ProjThirdInterface>().in("interface_type", Arrays.asList(1, 2)));
        if (projThirdInterfaces != null && projThirdInterfaces.size() > 0) {
            for (ProjThirdInterface projThirdInterface : projThirdInterfaces) {
                if (projThirdInterface.getInterfaceType() == 1) {
                    if (projThirdInterface.getInterfaceCategory() != null && !projThirdInterface.getInterfaceCategory().isEmpty()) {
                        projThirdInterface.setInterfaceCategoryCode("jhl" + PinyinUtils.getFirstSpell(projThirdInterface.getInterfaceCategory()));
                    }
                    projThirdInterface.setDictInterfaceCode("jhl" + PinyinUtils.getFirstSpell(projThirdInterface.getDictInterfaceName()));
                } else if (projThirdInterface.getInterfaceType() == 2) {
                    projThirdInterface.setInterfaceCategory("埋点类");
                    projThirdInterface.setInterfaceCategoryCode(PinyinUtils.getFirstSpell("埋点类接口新增"));
                    projThirdInterface.setDictInterfaceCode(PinyinUtils.getFirstSpell(projThirdInterface.getDictInterfaceName()));
                }

                projThirdInterfaceMapper.updateById(projThirdInterface);
            }
        }
        return Result.success();
    }

}
