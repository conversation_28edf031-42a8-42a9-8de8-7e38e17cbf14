package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.utils.AesUtil;
import com.msun.core.component.implementation.api.importdata.HospitalDataApi;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertGeneralDTO;
import com.msun.core.component.implementation.api.imsp.dto.SystemAuthorizeInfoSaveDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.api.imsp.vo.ProductBatchEncryptResultVo;
import com.msun.core.component.implementation.api.imsp.vo.ProductEncryptDto;
import com.msun.core.component.implementation.api.imsp.vo.SystemSettingResultDto;
import com.msun.core.component.implementation.api.port.DataPreparationApi;
import com.msun.core.component.implementation.api.port.dto.SqlCheckApiDTO;
import com.msun.core.component.implementation.api.port.dto.SqlSelectLoginDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.OpenStatusEnum;
import com.msun.csm.common.enums.WorkStatusEnum;
import com.msun.csm.common.enums.WorkTypeEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.config.SysFileCodeEnum;
import com.msun.csm.common.enums.productempower.DbAuthFlagEnum;
import com.msun.csm.common.enums.productempower.ProductEmpowerScopeEnum;
import com.msun.csm.common.enums.projapplyorder.DeployModEnum;
import com.msun.csm.common.enums.projapplyorder.DeployProductTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyOrderResultTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyTypeEnum;
import com.msun.csm.common.enums.projprojectinfo.EspecialProductEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProduct;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSpecialProductRecord;
import com.msun.csm.dao.entity.rule.RuleProductRuleConfig;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderProductRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSpecialProductRecordMapper;
import com.msun.csm.dao.mapper.projectreview.DictProjectReviewTypeMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.exception.OperationProductMisMatchingException;
import com.msun.csm.feign.client.productauth.ICDProductAuthFeignClient;
import com.msun.csm.feign.client.productauth.ProductAuthFeignClient;
import com.msun.csm.feign.entity.productauth.req.ICDAuthReq;
import com.msun.csm.feign.entity.productauth.req.MrsQAAuthReq;
import com.msun.csm.model.dto.ApplyOrderNodeRecordParamDTO;
import com.msun.csm.model.dto.DeployProductApplyDTO;
import com.msun.csm.model.dto.DeployResourceApplyProductDTO;
import com.msun.csm.model.dto.HospitalOnlinePublishedSystemDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneInfoFileInitializeDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderDTO;
import com.msun.csm.model.dto.empower.EmpowerRecord;
import com.msun.csm.model.dto.empower.EmpowerRecordExtend;
import com.msun.csm.model.dto.productauth.ApplyOpenProductBatchDTO;
import com.msun.csm.model.dto.productauth.ApplyOpenProductDTO;
import com.msun.csm.model.dto.productempower.ModuleEmpowerDTO;
import com.msun.csm.model.dto.productempower.ModuleEmpowerResultDTO;
import com.msun.csm.model.dto.productempower.NormalProductAuthDTO;
import com.msun.csm.model.dto.productempower.ProductEmpowerDTO;
import com.msun.csm.model.dto.productempower.ProductEmpowerImplDTO;
import com.msun.csm.model.dto.productempower.SingleProductAuthDTO;
import com.msun.csm.model.vo.SysFileInitializeVO;
import com.msun.csm.model.vo.product.NotOpenedProductHospitalCountVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalYunweiService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderProductService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;
import com.msun.csm.service.rule.RuleProductRuleConfigService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.StringUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */
@Slf4j
@Service
public class ProjOrderProductServiceImpl implements ProjOrderProductService {

    /**
     * 特殊产品集合
     */
    private static List<Long> especialProductIds = CollUtil.newArrayList(EspecialProductEnums.QUALITY.getCode(),
            EspecialProductEnums.AUTO_CODE.getCode(), EspecialProductEnums.ICD.getCode());
    @Resource
    UserHelper userHelper;
    @Resource
    ProjProductEmpowerRecordService projProductEmpowerRecordService;
    @Resource
    SystemSettingApi systemSettingApi;
    @Resource
    private OnlineStepService onlineStepService;
    @Resource
    private ProjApplyOrderProductService applyOrderProductService;
    @Resource
    private ProjOrderProductMapper projOrderProductMapper;
    @Value("${project.feign.yunwei.url}")
    private String operationPlatformUrl;
    @Value("${project.feign.yunwei.saveProductInfo-method}")
    private String saveProductInfoUrl;
    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Resource
    private HospitalDataApi hospitalDataApi;
    @Resource
    private ProductAuthFeignClient productAuthFeignClient;
    @Resource
    private ICDProductAuthFeignClient icdProductAuthFeignClient;
    @Resource
    private ProjHospitalInfoService hospitalInfoService;
    @Resource
    private ProjMilestoneInfoFileInitializeService projMilestoneInfoFileInitializeService;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;
    @Resource
    private ProjOrderProductService projOrderProductService;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private ProjSpecialProductRecordMapper specialProductRecordMapper;
    @Resource
    private ProjApplyOrderService applyOrderService;
    @Resource
    private ProjOrderProductService orderProductService;
    @Resource
    private ProjProjectSettlementCheckMainService mainService;
    @Resource
    private ProjApplyOrderMapper applyOrderMapper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RuleProductRuleConfigService ruleProductRuleConfigService;
    @Resource
    private ProjProductEmpowerRecordMapper productEmpowerRecordMapper;
    @Resource
    private ExceptionMessageService exceptionMessageService;
    @Resource
    private ProjApplyOrderProductRecordMapper applyOrderProductRecordMapper;
    @Resource
    private ProjCustomInfoMapper customInfoMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private ProjApplyOrderHospitalYunweiService applyOrderHospitalYunweiService;
    @Autowired
    private ProjProjectInfoService projProjectInfoService;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private DataPreparationApi dataPreparationApi;
    @Autowired
    private DictProjectReviewTypeMapper dictProjectReviewTypeMapper;
    @Autowired
    private SendBusinessMessageService sendBusinessMessageService;
    @Autowired
    private SysUserMapper sysUserMapper;

    public static String getUncheckMessageContent(ProjProjectInfo projectInfo, StringBuilder noneProductStrbuilder) {
        return projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + ":运维平台无法接收. 产品开通失败. " + noneProductStrbuilder + ". 联系运维平台匹配后重新开通.";
    }

    /**
     * 拼接开通失败的产品
     *
     * @param productInfos 产品集合
     * @return StringBuilder
     */
    public static StringBuilder splitProductName(List<ProductInfo> productInfos) {
        StringBuilder noneProductStrb = new StringBuilder();
        if (CollUtil.isNotEmpty(productInfos)) {
            for (ProductInfo noneProduct : productInfos) {
                noneProductStrb.append(noneProduct.getProductName()).append(StrUtil.DASHED).append(noneProduct.getYyOrderProductId()).append(StrUtil.COMMA);
            }
        }
        return noneProductStrb;
    }

    /**
     * 设置异常信息
     *
     * @param projectInfo  项目
     * @param productInfos 产品
     * @return string, 拼接的异常信息
     */
    public static OperationProductMisMatchingException setExceptionMessage(ProjProjectInfo projectInfo,
                                                                           List<ProductInfo> productInfos) {
        StringBuilder productNamesDetail = new StringBuilder();
        StringBuilder productNames = new StringBuilder();
        for (ProductInfo productInfo : productInfos) {
            productNamesDetail.append(productInfo.getProductName()).append(StrUtil.DASHED).append(productInfo.getYyOrderProductId()).append(StrUtil.COMMA);
            productNames.append(productInfo.getReProductName()).append(StrUtil.COMMA);
        }
        String message = projectInfo.getProjectName() + "项目-" + projectInfo.getProjectNumber() + ":"
                + "申请部署的所有产品匹配失败, 需联系交付平台核实.";
        String returnMessage = "产品: " + productNames + " 开通失败. 请联系交付平台处理.";
        return new OperationProductMisMatchingException(message, message + "工单产品包括: " + productNamesDetail,
                returnMessage);
    }

    /**
     * 查询无法部署产品id
     *
     * @param jsonObject 接口返回值
     * @return List<Long> 运营产品id
     */
    public static List<Long> findUnCheckProductIds(JSONObject jsonObject) {
        JSONObject envApplyRes = jsonObject.getJSONObject("envApplyRes");
        List<JSONObject> unCheckApplyProductVOList = envApplyRes.getJSONArray("unCheckApplyProductVOList")
                .toJavaList(JSONObject.class);
        List<Long> unCheckApplyYyOrderProductIds = CollUtil.newArrayList();
        if (unCheckApplyProductVOList.isEmpty()) {
            return CollUtil.newArrayList();
        }
        for (JSONObject object : unCheckApplyProductVOList) {
            // 申请是产品id
            Object customerProductId = object.get("customerProductId");
            unCheckApplyYyOrderProductIds.add(Long.parseLong(customerProductId.toString()));
        }
        return unCheckApplyYyOrderProductIds;
    }

    /**
     * 查询未部署产品id
     *
     * @param jsonObject 接口返回值
     * @return List<Long> 运营产品id
     */
    public static List<Long> findDeployedProductIds(JSONObject jsonObject) {
        List<Long> deployedYyOrderProductId = CollUtil.newArrayList();
        JSONObject deployProductApplyJson = jsonObject.getJSONObject("envApplyRes");
        List<JSONObject> deployApplyProductVOList = deployProductApplyJson.getJSONArray("deployApplyProductVOList")
                .toJavaList(JSONObject.class);
        if (deployApplyProductVOList.isEmpty()) {
            return CollUtil.newArrayList();
        }
        // 返回值有已经部署的产品进行授权, 若返回已经部署的产品与需要开通的产品数量相同则更新节点完成状态, 并记录日志为自动交付.
        for (JSONObject object : deployApplyProductVOList) {
            // 申请是产品id
            Object customerProductId = object.get("customerProductId");
            deployedYyOrderProductId.add(Long.parseLong(customerProductId.toString()));
        }
        log.info("产品开通. 查询到已部署产品: {}", deployedYyOrderProductId);
        return deployedYyOrderProductId;

    }

    /**
     * 过滤去重
     *
     * @param empowerRecordListParam 传参
     * @return List<ProjProductEmpowerRecord>
     */
    public static List<ProductEmpowerDTO> distinctEmpowerProductByCode(List<ProductEmpowerDTO> empowerRecordListParam) {
        if (CollUtil.isEmpty(empowerRecordListParam)) {
            log.info("实际查询到的申请部署的产品. count: 0");
            return new ArrayList<>();
        }
        log.info("实际查询到的申请部署的产品. count: {}", empowerRecordListParam);
        // 去重
        Map<String, ProductEmpowerDTO> distinctEmpowerRecordMap = new HashMap<>();
        if (CollUtil.isNotEmpty(empowerRecordListParam)) {
            for (ProductEmpowerDTO record : empowerRecordListParam) {
                String moduleCode = StrUtil.isNotEmpty(record.getMsunHealthModuleCode())
                        ? record.getMsunHealthModuleCode().trim() : StrUtil.EMPTY;
                if (StrUtil.isEmpty(moduleCode)) {
                    continue;
                }
                if (!distinctEmpowerRecordMap.containsKey(moduleCode)) {
                    distinctEmpowerRecordMap.put(moduleCode, record);
                }
            }
        }
        List<ProductEmpowerDTO> empowerRecordList = new ArrayList<>();
        for (String code : distinctEmpowerRecordMap.keySet()) {
            empowerRecordList.add(distinctEmpowerRecordMap.get(code));
        }
        return empowerRecordList;
    }

    /**
     * 是否包含分院模式产品,
     * 用于判断工单产品中是否含分院模式产品
     *
     * @param yyProductIds           运营产品id
     * @param ruleProductRuleConfigs 通用产品配置
     * @return boolean
     */
    public static boolean containBranchModeProduct(List<Long> yyProductIds,
                                                   List<RuleProductRuleConfig> ruleProductRuleConfigs) {
        return yyProductIds.stream().anyMatch(e -> ruleProductRuleConfigs.stream().anyMatch(f -> f.getYyProductId().longValue() == e));
    }

    /**
     * 截取异常信息提供前端展示
     *
     * @param message 异常信息
     * @return string
     */
    public static String getExMsgSubscribe(String message) {
        if (StrUtil.isNotBlank(message)) {
            if (message.length() > 200) {
                message = message.substring(0, 200);
            }
            message += "...";
            return message;
        }
        return StrUtil.EMPTY;
    }

    /**
     * 获取授权描述
     *
     * @param dto 请求授权参数
     * @return 授权描述
     */
    public static String getAuthDesc(ProductEmpowerImplDTO dto) {
        ProductEmpowerScopeEnum scopeEnum = ProductEmpowerScopeEnum.getEnumByCode(dto.getProductEmpowerScope());
        String scopeDesc;
        if (ObjectUtil.isNotEmpty(scopeEnum)) {
            assert scopeEnum != null;
            scopeDesc = scopeEnum.getDesc();
        } else {
            scopeDesc = StrUtil.EMPTY;
        }
        return (dto.getOpenAuth() ? "添加" : "取消") + scopeDesc + "授权";
    }

    /**
     * 记录授权异常记录
     *
     * @param projectInfoId       项目id
     * @param collectEmpowerError 异常记录收集
     */
    public static void setEmpowerErrorRecord(Long projectInfoId, CollectEmpowerError collectEmpowerError, Map<Long,
            List<String>> errorRecordMap) {
        if (CollUtil.isEmpty(collectEmpowerError.getEmpowerRecordList())) {
            return;
        }
        if (!errorRecordMap.containsKey(collectEmpowerError.getHospitalInfo().getHospitalInfoId())) {
            errorRecordMap.put(collectEmpowerError.getHospitalInfo().getHospitalInfoId(), new ArrayList<>());
        }
        List<String> empowerRecordError = CollUtil.newArrayList();
        String title = collectEmpowerError.isProductEncrypt ? "产品加密异常:" + collectEmpowerError.getUuid()
                : "授权菜单异常:" + collectEmpowerError.getUuid();
        title += ":projectInfoId:" + projectInfoId;
        empowerRecordError.add(title);
        empowerRecordError.addAll(collectEmpowerError.getEmpowerRecordList().stream().map(m -> m.getMsunHealthModuleCode() + ":" + m.getYyOrderProductId()).collect(Collectors.toList()));
        errorRecordMap.get(collectEmpowerError.getHospitalInfo().getHospitalInfoId()).addAll(empowerRecordError);
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replace(StrUtil.DASHED, StrUtil.EMPTY);
    }

    @Override
    public int deleteByPrimaryKey(Long orderProductId) {
        return projOrderProductMapper.deleteByPrimaryKey(orderProductId);
    }

    @Override
    public int insert(ProjOrderProduct record) {
        return projOrderProductMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjOrderProduct record) {
        return projOrderProductMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjOrderProduct record) {
        return projOrderProductMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjOrderProduct record) {
        return projOrderProductMapper.insertSelective(record);
    }

    @Override
    public ProjOrderProduct selectByPrimaryKey(Long orderProductId) {
        return projOrderProductMapper.selectByPrimaryKey(orderProductId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjOrderProduct record) {
        return projOrderProductMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjOrderProduct record) {
        return projOrderProductMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjOrderProduct> list) {
        return projOrderProductMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjOrderProduct> list) {
        return projOrderProductMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjOrderProduct> list) {
        return projOrderProductMapper.batchInsert(list);
    }

    @Override
    public Result<PageInfo<ProductInfo>> getProductList(ProductInfoDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            List<ProductInfo> list = getProductListImpl(dto);
            PageInfo<ProductInfo> pageInfo = new PageInfo<>(list);
            return Result.success(pageInfo);
        });
    }

    /**
     * 根据项目id查询产品
     *
     * @param projectInfoId 项目id
     * @return List<ProductInfo>
     */
    public List<ProductInfo> findProductList(Long projectInfoId) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projectInfoId);
        return getProductListImpl(productInfoDTO);
    }

    /**
     * 查询产品信息更新客户id
     *
     * @param customInfoId 客户id
     * @return List<ProductInfo>
     */
    public List<ProductInfo> findProductListByCustomInfoId(Long customInfoId) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setCustomInfoId(customInfoId);
        return getProductListImpl(productInfoDTO);
    }

    @Override
    public List<ProductInfo> getProductListImpl(ProductInfoDTO dto) {
        List<ProductInfo> list;
        //默认查询全部 0 工单派工 1特批派工
        if (WorkTypeEnum.ORDER_WORK.getCode().equals(dto.getProductWorkType())) {
            list = projOrderProductMapper.findProductList(dto);
        } else if ((WorkTypeEnum.SPECIAL_WORK.getCode()).equals(dto.getProductWorkType())) {
            list = projOrderProductMapper.findSpecialProductList(dto);
        } else {
            list = projOrderProductMapper.findAllList(dto);
        }
        //暂时未知链接
        ProjMilestoneInfoFileInitializeDTO projMilestoneInfoFileInitializeDTO =
                new ProjMilestoneInfoFileInitializeDTO();
        projMilestoneInfoFileInitializeDTO.setProjectInfoId(dto.getProjectInfoId());
        SysFileInitializeVO sysFile =
                projMilestoneInfoFileInitializeService.productOperationManual(projMilestoneInfoFileInitializeDTO).getData();
        list.forEach(v -> {
            v.setOpenStatus(OpenStatusEnum.getDesc(v.getProductOpenStatus()));
            v.setExcutionStatus(WorkStatusEnum.getDesc(v.getProductExcutionStatus()));
            if (sysFile != null && sysFile.getSysFiles() != null) {
                v.setFilePath(sysFile.getSysFiles().get(SysFileCodeEnum.PRODUCT_OPERATION_MANUAL.getFileCode()));
            }
        });
        return list;
    }

    /**
     * 批量开通产品
     *
     * @param dto           请求
     * @param applyOrderNum 工单编号
     * @return Result<String>
     */
    public Result<String> applyOpenProductBatchImpl(ApplyOpenProductDTO dto, String applyOrderNum) {
        // 获取未开通产品
        List<ProductInfo> productInfos = getUnOpenProducts(dto.getProjectId());
        if (CollUtil.isEmpty(productInfos)) {
            log.warn("未查询到要开通的产品. projectInfoId: {}", dto.getProjectId());
            return Result.fail("未查询到要开通的产品.");
        }
        return orderProductService.applyProductHandlerSwap(productInfos, dto, applyOrderNum);
    }

    /**
     * 查询未开通的产品
     *
     * @param projectInfoId 项目id
     * @return List<ProductInfo>
     */
    public List<ProductInfo> getUnOpenProducts(Long projectInfoId) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projectInfoId);
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        if (CollUtil.isEmpty(productInfos)) {
            log.warn("未查询待开通产品. projectInfoId: {}", projectInfoId);
            return CollUtil.newArrayList();
        }
        productInfos = applyOrderHospitalYunweiService.orderProductNotOpen(productInfos);
        if (CollUtil.isEmpty(productInfos)) {
            log.warn("未查询待开通的产品. projectInfoId: {}", projectInfoId);
            return CollUtil.newArrayList();
        }
        return productInfos;
    }

    public Result<String> applyOpenProductImpl(ApplyOpenProductDTO dto, ProjApplyOrder applyOrder,
                                               String applyOrderNum) {
        List<ProductInfo> productInfos = getUnOpenProducts(dto.getProjectId());
        if (CollUtil.isEmpty(productInfos)) {
            log.warn("未查询到要开通的产品. projectInfoId: {}", dto.getProjectId());
            return Result.fail("未查询到要开通的产品.");
        }
        return orderProductService.applyProductHandlerSwap(productInfos, dto, applyOrderNum);
    }

    /**
     * 申请产品开通
     * 会判断异常向客服反馈
     *
     * @param productInfos  产品
     * @param dto           请求参数
     * @param applyOrderNum 申请单号
     * @return Result<String>
     */
    public Result<String> applyProductHandlerSwap(List<ProductInfo> productInfos, ApplyOpenProductDTO dto,
                                                  String applyOrderNum) {
        String key = "apply:product:" + dto.getProjectId();
        if (ObjectUtil.isNotEmpty(redisUtil.get(key))) {
            return Result.fail("请勿重复提交产品开通申请. 稍后再试.");
        }
        redisUtil.set(key, dto.getProjectId(), 3L, TimeUnit.MINUTES);
        Long projectInfoId = dto.getProjectId();
        List<ProductInfo> allProductInfos = CollUtil.newArrayList(productInfos);
        List<ProductInfo> unCheckProducts;
        // 未部署对照的产品
        List<ProductInfo> accurateProducts;
        ProjProjectInfo projectInfo = mainService.getProjectInfo(dto.getProjectId());
        String operName = "产品开通:工单编号:" + projectInfo.getProjectNumber() + ",项目id:" + projectInfoId;
        try {
            try {
                // 筛选符合开通条件的产品
                accurateProducts = findAccurateProduct(allProductInfos, projectInfoId);
                findNoneMatchProduct(projectInfo, operName, accurateProducts, allProductInfos);
                unCheckProducts = orderProductService.applyProductHandler(accurateProducts, dto, applyOrderNum);
            } catch (OperationProductMisMatchingException e) {
                log.error("交付产品失败. 项目id: {}, 异常: {}, e=", dto.getProjectId(), e.getMessage(), e);
                // 发送消息给系统管理员交付失败的消息
                String operContent =
                        projectInfo.getProjectName() + "项目-" + projectInfo.getProjectNumber() + ":" + e.getDetail();
                sysOperLogService.apiOperLogInsert(operContent, operName, StrUtil.EMPTY,
                        Log.LogOperType.OTHER.getCode());
                exceptionMessageService.sendToSystemManager(dto.getProjectId(), operContent);
                return Result.fail(e.getReturnMessage());
            } catch (CustomException e) {
                throw e;
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        } finally {
            redisUtil.del(key);
        }
        // 对比筛查后的产品, 不符合要求的向客服做出提醒反馈
        if (CollUtil.isNotEmpty(unCheckProducts)) {
            log.error("对比筛查后的产品, 不符合要求的向客服做出提醒反馈: {}, projectInfoId: {}",
                    splitProductName(unCheckProducts), projectInfoId);
            StringBuilder noneProductStrbuilder = splitProductName(unCheckProducts);
            String messageUnCheckContent = getUncheckMessageContent(projectInfo, noneProductStrbuilder);
            exceptionMessageService.sendToSystemManager(projectInfoId, messageUnCheckContent);
            return Result.fail("产品: " + noneProductStrbuilder + " 开通失败. 请联系交付平台处理.");
        }
        return Result.success();
    }

    /**
     * 查找不能开通的产品
     *
     * @param projectInfo     项目
     * @param operName        操作名称
     * @param productInfos    已经申请开通的产品信息
     * @param allProductInfos 请求开通的所有产品信息
     */
    public void findNoneMatchProduct(ProjProjectInfo projectInfo, String operName, List<ProductInfo> productInfos,
                                     List<ProductInfo> allProductInfos) {
        List<ProductInfo> unDeployProducts =
                allProductInfos.stream().filter(e -> productInfos.stream().noneMatch(f -> f.getYyOrderProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(unDeployProducts)) {
            String operContent = projectInfo.getProjectName() + "项目-" + projectInfo.getProjectNumber() + ", "
                    + "有部署产品未做对照：" + splitProductName(unDeployProducts);
            String messageContent = projectInfo.getProjectName() + "项目-" + projectInfo.getProjectNumber() + ", "
                    + "有部署产品未做对照. 请尽快处理.";
            log.warn(operContent);
            sysOperLogService.apiOperLogInsert(operContent, operName, StrUtil.EMPTY, Log.LogOperType.OTHER.getCode());
            exceptionMessageService.sendToSystemManager(projectInfo.getProjectInfoId(), messageContent);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public List<ProductInfo> applyProductHandlerImpl(List<ProductInfo> productInfos, ApplyOpenProductDTO dto2,
                                                     String applyOrderNum,
                                                     List<ProjHospitalInfoRelative> openedHospitals) {
        if (CollUtil.isEmpty(openedHospitals)) {
            throw new CustomException("开通的医院至少有一个");
        }
        Long projectInfoId = dto2.getProjectId();
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        // 创建工单
        ProjApplyOrderDTO applyOrderDTO = new ProjApplyOrderDTO();
        applyOrderDTO.setApplyType(ProjApplyTypeEnum.PRODUCT_APPLY.getCode());
        applyOrderDTO.setProjectInfoId(projectInfo.getProjectInfoId());
        applyOrderDTO.setCustomInfoId(projectInfo.getCustomInfoId());
        ProjApplyOrder applyOrder = applyOrderService.insertApplyOrder(applyOrderDTO, applyOrderNum);
        // 创建医院记录
        ProjApplyOrderDTO applyOrderDTO1 = new ProjApplyOrderDTO();
        applyOrderDTO1.setProjectInfoId(projectInfo.getProjectInfoId());
        applyOrderService.addHospitalRecords(applyOrderDTO1, applyOrder, true);
        // 创建产品记录
        applyOrderService.addProductRecordsImpl(applyOrder, productInfos);
        //TODO 添加部署审批日志
        SendBusinessMessageService.ReviewUserStrs yzyys = sendBusinessMessageService.getReviewUserStrByCode("yzyys");
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(StrUtil.EMPTY)
                .telephone(StrUtil.EMPTY)
                .operateContent(StrUtil.format("{}{} - 下一步审批人：主审批人（{}）；副审批人（{}）",
                        applyOrder.getApplicant(), ProjApplyOrderResultTypeEnum.APPLYED.getDesc(),
                        CollUtil.isNotEmpty(yzyys.getMain()) ? String.join("；", yzyys.getMain()) : "无",
                        CollUtil.isNotEmpty(yzyys.getSub()) ? String.join("；", yzyys.getSub()) : "无"
                ))
                .operator(applyOrder.getApplicant())
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        // 调用接口进行申请
        ProjHospitalInfo hospitalInfo = BeanUtil.copyProperties(openedHospitals.get(0), ProjHospitalInfo.class);
        DeployProductApplyDTO applyDTO = getProductApplyOrderMainParam(applyOrder, hospitalInfo,
                projectInfo.getProjectType());
        // 获取产品列参数
        applyDTO.setDeployResourceApplyProductDTOList(findProductsForApplyProduct(productInfos,
                projectInfo.getProjectInfoId()));
        // 设置派工产品类型
        ProjOrderInfo orderInfo = commonService.getOrderInfo(projectInfo.getOrderInfoId());
        if (ObjectUtil.isEmpty(orderInfo)) {
            log.error("产品部署开通. 未查询到软件工单. projectInfo: {}", JSONUtil.toJsonStr(projectInfo));
            throw new CustomException("未查询到软件工单.");
        }
        // 设置派工产品类型
        if (orderInfo.getDeliveryOrderType() == OrderTypeEnums.PURCHASE_SOFTWARE.getCode()) {
            applyDTO.setProductType(DeployProductTypeEnum.PURCHASE.getCode());
        } else {
            applyDTO.setProductType(DeployProductTypeEnum.NOT_PURCHASE.getCode());
        }
        List<ProductInfo> unCheckProducts = CollUtil.newArrayList();
        // 先更新状态
        List<Long> yyProductIds =
                productInfos.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList());
        applyOrderProductService.updateProductOpenStatus(ProductOpenStatusEnum.OPENING, yyProductIds, projectInfoId);
        try {
            callDepOpenProduct(applyDTO);
        } catch (Throwable e) {
            log.error("开通产品调接口异常/正常, message: {}, e=", e.getMessage(), e);
            if (e.getMessage().contains("401")) {
                throw setExceptionMessage(projectInfo, productInfos);
            } else if (e.getMessage().contains("402")) {
                try {
                    productEmpower(productInfos, projectInfo, openedHospitals);
                    // 更新状态为已交付状态
                    updateApplyOrderResult(applyOrder.getId());
                } catch (Throwable throwable) {
                    log.error("产品申请开通时授权或更新工单状态异常. e:{}, e=", throwable.getMessage(), throwable);
                    throw throwable;
                }
            } else {
                log.error("产品开通异常. message: {}, e=", e.getMessage(), e);
                throw setExceptionMessage(projectInfo, productInfos);
            }
        }
        return unCheckProducts;
    }

    /**
     * 申请产品开通
     *
     * @param productInfos  产品
     * @param dto           请求参数
     * @param applyOrderNum 申请单号
     */

    public List<ProductInfo> applyProductHandler(List<ProductInfo> productInfos, ApplyOpenProductDTO dto,
                                                 String applyOrderNum) {
        Long projectInfoId = dto.getProjectId();
        // 查询需要开通的医院
        List<ProjHospitalInfoRelative> hospitalInfoList =
                applyOrderHospitalService.getApplyOrderHospitals(projectInfoId);
        List<ProjHospitalInfoRelative> openedHospitals =
                ProjApplyOrderHospitalYunweiService.getOpenHospital(hospitalInfoList);
        if (CollUtil.isEmpty(openedHospitals)) {
            log.error("未查询到开通的医院信息. projectInfoId: {}", projectInfoId);
            throw new RuntimeException("未查询到开通的医院信息.");
        }
        // 过滤分院类型工单产品、正常工单产品
        List<RuleProductRuleConfig> ruleProductRuleConfigs =
                ruleProductRuleConfigService.findRuleProductRuleConfigForBranch();
        List<ProductInfo> branchProducts =
                productInfos.stream().filter(e -> ruleProductRuleConfigs.stream().anyMatch(f -> f.getYyProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(branchProducts)) {
            ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
            productEmpower(branchProducts, projectInfo, openedHospitals);
        }
        List<ProductInfo> normalProducts =
                productInfos.stream().filter(e -> ruleProductRuleConfigs.stream().noneMatch(f -> f.getYyProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(normalProducts)) {
            return orderProductService.applyProductHandlerImpl(normalProducts, dto, applyOrderNum, openedHospitals);
        }
        return CollUtil.newArrayList();
    }

    /**
     * 查询精确可以开通的产品, 仅进行申请前的检测
     * 若申请的产品全部不符合条件会抛出异常
     *
     * @param productInfos  申请产品
     * @param projectInfoId 项目id
     * @return List<ProductInfo> 筛出的产品集合
     */
    public List<ProductInfo> findAccurateProduct(List<ProductInfo> productInfos, Long projectInfoId) {
        List<ProjApplyOrderProduct> arrangeProducts = applyOrderService.findArrangeProduct(productInfos, projectInfoId);
        if (CollUtil.isEmpty(arrangeProducts)) {
            log.warn("未查询到部署产品. projectInfoId: {}, product: {}", projectInfoId, JSONObject.toJSONString(productInfos));
            throw new CustomException("未检测到部署产品. 请联系交付平台处理.");
        }
        // 过滤可用的工单产品
        return productInfos.stream().filter(e -> arrangeProducts.stream().anyMatch(f -> f.getYyOrderProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
    }

    /**
     * 处理申请产品开通返回值
     *
     * @param projectInfo  项目
     * @param productInfos 产品
     * @param jsonObject   返回值
     * @return List<ProductInfo> 运维未匹配产品
     */
    public List<ProductInfo> handleCallResult(ProjProjectInfo projectInfo, List<ProductInfo> productInfos,
                                              JSONObject jsonObject) {
        // 处理不能开通的产品
        List<ProductInfo> unCheckProducts = handleUnCheckApplyProductVOList(projectInfo, jsonObject);
        // 处理待开通产品
        handleProductOpenStatus(productInfos, projectInfo, jsonObject);
        return unCheckProducts;

    }

    /**
     * 处理产品开通状态
     *
     * @param productInfoAll 申请的所有产品
     * @param projectInfo    项目
     * @param jsonObject     接口返回值
     */
    private void handleProductOpenStatus(List<ProductInfo> productInfoAll, ProjProjectInfo projectInfo,
                                         JSONObject jsonObject) {
        List<Long> notApplyYyProductIds = findUnCheckProductIds(jsonObject);
        notApplyYyProductIds.addAll(findDeployedProductIds(jsonObject));
        List<Long> applySuccessProductIds;
        if (CollUtil.isNotEmpty(notApplyYyProductIds)) {
            List<ProductInfo> applySuccessProducts =
                    productInfoAll.stream().filter(e -> notApplyYyProductIds.stream().noneMatch(f -> f.longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
            applySuccessProductIds =
                    applySuccessProducts.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList());
        } else {
            applySuccessProductIds =
                    productInfoAll.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(applySuccessProductIds)) {
            log.warn("未检测到需要更新开通状态的产品. projectInfoId: {}", projectInfo.getProjectInfoId());
            return;
        }
        applyOrderProductService.updateProductOpenStatus(ProductOpenStatusEnum.OPENING, applySuccessProductIds,
                projectInfo.getProjectInfoId());
    }

    /**
     * 处理运维匹配失败的产品
     *
     * @param projectInfo 项目
     * @param jsonObject  接口返回值
     * @return List<ProductInfo>
     */
    private List<ProductInfo> handleUnCheckApplyProductVOList(ProjProjectInfo projectInfo, JSONObject jsonObject) {
        Long projectInfoId = projectInfo.getProjectInfoId();
        List<Long> unCheckApplyProductIds = findUnCheckProductIds(jsonObject);
        if (CollUtil.isEmpty(unCheckApplyProductIds)) {
            return CollUtil.newArrayList();
        }
        // 更新产品作废标识
        return updateApplyOrderProductRecord(unCheckApplyProductIds, projectInfoId);
    }

    /**
     * 更新产品作废标识, 为了在交付授权时不授权未匹配的产品
     *
     * @param unCheckApplyProductIds 未匹配的产品id
     * @param projectInfoId          项目id
     */
    public List<ProductInfo> updateApplyOrderProductRecord(List<Long> unCheckApplyProductIds, Long projectInfoId) {
        List<ProductInfo> productInfosTmp = orderProductService.findProductList(projectInfoId);
        productInfosTmp =
                productInfosTmp.stream().filter(e -> unCheckApplyProductIds.stream().anyMatch(f -> e.getYyOrderProductId().longValue() == f.longValue())).collect(Collectors.toList());
        return productInfosTmp;
    }

    /**
     * 根据表id更新结果状态
     *
     * @param applyOrderId 申请单id
     */
    public void updateApplyOrderResult(Long applyOrderId) {
        ProjApplyOrder updateApplyOrder = new ProjApplyOrder();
        updateApplyOrder.setId(applyOrderId);
        updateApplyOrder.setResultType(ProjApplyOrderResultTypeEnum.DELIVERED.getCode());
        applyOrderMapper.updateById(updateApplyOrder);
        // 添加交付日志
        ApplyOrderNodeRecordParamDTO paramDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(StrUtil.EMPTY)
                .telephone(StrUtil.EMPTY)
                .operator("交付系统")
                .operateContent("系统自动授权")
                .build();
        applyOrderService.addApplyOrderNodeRecord(updateApplyOrder, paramDTO);
    }

    public void productEmpower(List<ProductInfo> productInfos, ProjProjectInfo projectInfo,
                               List<ProjHospitalInfoRelative> hospitalInfoList) {
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().in(
                "hospital_info_id",
                hospitalInfoList.stream().map(ProjHospitalInfoRelative::getHospitalInfoId).collect(Collectors.toList())));
        List<Long> yyOrderProductIds =
                productInfos.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList());
        applyEmpowers(projectInfo.getProjectInfoId(), hospitalInfos, yyOrderProductIds);
    }

    /**
     * 处理反馈已经授权的产品
     *
     * @param projectInfo 项目
     * @param jsonObject  反馈的json体
     */
    public void handleDepolyedProduct(ProjProjectInfo projectInfo, JSONObject jsonObject) {
        Long projectInfoId = projectInfo.getProjectInfoId();
        List<Long> deployedOrderProductId = findDeployedProductIds(jsonObject);
        if (CollUtil.isEmpty(deployedOrderProductId)) {
            return;
        }
        List<ProjHospitalInfo> openedHospitalInfos =
                applyOrderHospitalService.findOpenedHospitalInfos(projectInfo.getProjectInfoId());
        List<ProductInfo> productInfosTmp = orderProductService.findProductList(projectInfoId);
        productInfosTmp =
                productInfosTmp.stream().filter(e -> deployedOrderProductId.stream().anyMatch(f -> f.longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        List<Long> yyOrderProductIds =
                productInfosTmp.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(yyOrderProductIds)) {
            log.info("对已部署产品开通授权. deployedOrderProductId: {}, yyOrderProductIds: {}", deployedOrderProductId,
                    yyOrderProductIds);
            applyEmpowers(projectInfo.getProjectInfoId(), openedHospitalInfos, yyOrderProductIds);
        }
    }

    @Override
    public Result<String> applyOpenProductImpl(ApplyOpenProductDTO dto) {
        ProductInfo productInfo = isArranged(dto);
        List<ProjHospitalInfoRelative> relatives = applyOrderHospitalService.getApplyOrderHospitals(dto.getProjectId());
        List<ProjHospitalInfo> hospitalInfos = new ArrayList<>();
        for (ProjHospitalInfoRelative hospitalInfoRelative : relatives) {
            if (!(hospitalInfoRelative.getHospitalOpenStatus() == HospitalOpenStatusEnum.OPENED.getCode() || hospitalInfoRelative.getHospitalOpenStatus() == HospitalOpenStatusEnum.ONLINE.getCode())) {
                continue;
            }
            if (ObjectUtil.isNotEmpty(dto.getHospitalInfoId())) {
                if (!(hospitalInfoRelative.getHospitalInfoId().intValue() == dto.getHospitalInfoId().intValue())) {
                    continue;
                }
            }
            ProjHospitalInfo hospitalInfo = BeanUtil.copyProperties(hospitalInfoRelative, ProjHospitalInfo.class);
            hospitalInfos.add(hospitalInfo);
        }
        if (ObjectUtil.isEmpty(hospitalInfos)) {
            throw new RuntimeException("未查询到处于开通状态的医院.");
        }
        projOrderProductService.applyEmpowers(dto.getProjectId(), hospitalInfos,
                CollUtil.newArrayList(productInfo.getYyOrderProductId()));
        return Result.success();
    }

    @Override
    public Result<String> applyOpenProductImplManu(ApplyOpenProductBatchDTO dto) {
        List<ProductInfo> productInfos;
        if (ObjectUtil.isNotEmpty(dto.getProductOpened())) {
            // 未开通的
            if (dto.getProductOpened() == NumberEnum.NO_0.num().intValue()) {
                productInfos = getProductList(dto);
            } else if (dto.getProductOpened() == NumberEnum.NO_1.num().intValue()) {
                productInfos = getProductListOpened(dto);
            } else if (dto.getProductOpened() == NumberEnum.NO_2.num().intValue()) {
                productInfos = getProductList(dto);
                productInfos.addAll(getProductListOpened(dto));
            } else {
                throw new RuntimeException("参数(productOpened)不正确: productOpened: " + dto.getProductOpened());
            }
        } else {
            // 若值为空则默认查询未开通产品
            productInfos = getProductList(dto);
        }
        if (CollUtil.isEmpty(productInfos)) {
            throw new CustomException("未查询到符合条件的产品. dto: " + dto);
        }
        // 过滤指定的产品，根据yyProductIds
        if (CollUtil.isNotEmpty(dto.getOrderProductIdList())) {
            productInfos =
                    productInfos.stream().filter(e -> dto.getOrderProductIdList().stream().anyMatch(f -> f.longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        }
        List<ProjHospitalInfoRelative> relatives =
                applyOrderHospitalService.getApplyOrderHospitals(dto.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = new ArrayList<>();
        for (ProjHospitalInfoRelative hospitalInfoRelative : relatives) {
            if (ObjectUtil.isNotEmpty(dto.getHospitalInfoId())) {
                if (!(hospitalInfoRelative.getHospitalInfoId().intValue() == dto.getHospitalInfoId().intValue())) {
                    continue;
                }
            }
            ProjHospitalInfo hospitalInfo = BeanUtil.copyProperties(hospitalInfoRelative, ProjHospitalInfo.class);
            hospitalInfos.add(hospitalInfo);
        }
        hospitalInfos =
                hospitalInfos.stream().filter(hospitalInfo -> hospitalInfo.getHospitalOpenStatus() == HospitalOpenStatusEnum.OPENED.getCode() || hospitalInfo.getHospitalOpenStatus() == HospitalOpenStatusEnum.ONLINE.getCode()).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(hospitalInfos)) {
            throw new RuntimeException("未查询到处于开通状态的医院.");
        }
        projOrderProductService.applyEmpowers(dto.getProjectInfoId(), hospitalInfos,
                productInfos.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList()));
        return Result.success();
    }

    /**
     * 调用产品部署申请接口
     *
     * @param applyDTO
     * @return
     */
    private JSONObject callDepOpenProduct(DeployProductApplyDTO applyDTO) {
        JSONObject resultJson = new JSONObject();
        MultiValueMap<String, Object> envApplyMap = new LinkedMultiValueMap<>();
        envApplyMap.add("dto", applyDTO);
        log.info("产品申请入参:{}", JSON.toJSONString(applyDTO));
        HttpEntity<MultiValueMap<String, Object>> httpRequest =
                new HttpEntity<>(envApplyMap, applyOrderService.getHeaders());
        sysOperLogService.apiOperLogInsert(httpRequest, "部署产品-入参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
        ResponseEntity<JSONObject> envApplyRes;
        try {
            envApplyRes = new RestTemplate().postForEntity(operationPlatformUrl + saveProductInfoUrl,
                    httpRequest, JSONObject.class);
            sysOperLogService.apiOperLogInsert(envApplyRes, "部署产品-出参`", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
            assert envApplyRes != null;
            resultJson.put("envApplyRes", envApplyRes.getBody());
        } catch (Throwable e) {
            log.error("申请开通产品异常: message: {}, e=", e.getMessage(), e);
            throw e;
        }
        return resultJson;
    }

    /**
     * 获取主参数
     *
     * @param applyOrder
     * @param hospitalInfo
     * @return
     */
    private DeployProductApplyDTO getProductApplyOrderMainParam(ProjApplyOrder applyOrder,
                                                                ProjHospitalInfo hospitalInfo, int projectType) {
        DeployProductApplyDTO deployApplyDTO = new DeployProductApplyDTO();
        deployApplyDTO.setDeliverPlatformApplyId(applyOrder.getApplyNum());
        deployApplyDTO.setCountryCount(1);
        deployApplyDTO.setCustomerName(hospitalInfo.getHospitalName());
        deployApplyDTO.setCustomerId(StrUtil.toString(hospitalInfo.getHospitalInfoId()));
        deployApplyDTO.setDeployMod(DeployModEnum.getEnumByCode(projectType).getDevCode());
        deployApplyDTO.setDeployType(ProjApplyTypeEnum.PRODUCT_APPLY.getDevCode());
        deployApplyDTO.setEnvId(ObjectUtil.isEmpty(hospitalInfo.getEnvId()) ? 0L : hospitalInfo.getEnvId());
        deployApplyDTO.setEnvName(StrUtil.isEmpty(hospitalInfo.getEnvName()) ? StrUtil.EMPTY
                : hospitalInfo.getEnvName());
        deployApplyDTO.setSubmitName(userHelper.getCurrentUser().getUserName());
        return deployApplyDTO;
    }

    /**
     * 查询要申请开通的部署产品
     *
     * @param products      产品集合
     * @param projectInfoId 项目id
     * @return List<DeployResourceApplyProductDTO>
     */
    private List<DeployResourceApplyProductDTO> findProductsForApplyProduct(List<ProductInfo> products,
                                                                            Long projectInfoId) {
        List<ProjApplyOrderProduct> arrangeProduct = applyOrderService.findArrangeProduct(products, projectInfoId);
        return getDeployResourceApplyProductDTOListImpl(arrangeProduct);
    }

    /**
     * 用于产品申请
     *
     * @param products
     * @return
     */
    private List<DeployResourceApplyProductDTO> getDeployResourceApplyProductDTOListImpl(List<ProjApplyOrderProduct> products) {
        List<DeployResourceApplyProductDTO> resourceApplyProductDTOS = new ArrayList<>();
        products.forEach(e -> {
            DeployResourceApplyProductDTO resourceApplyProductDTO = new DeployResourceApplyProductDTO();
            resourceApplyProductDTO.setCustomerProductName(e.getProductName());
            resourceApplyProductDTO.setCustomerProductCode(StrUtil.isBlank(e.getYyProductCode()) ? StrUtil.EMPTY
                    : e.getYyProductCode());
            resourceApplyProductDTO.setCustomerProductId(e.getProductArrangeId());
            resourceApplyProductDTOS.add(resourceApplyProductDTO);
        });
        return resourceApplyProductDTOS;
    }

    /**
     * 判断是否已经申请部署
     *
     * @param dto
     * @return
     */
    private ProductInfo isArranged(ApplyOpenProductDTO dto) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(dto.getProjectId());
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        log.info("产品信息:{}", JSON.toJSONString(productInfos));
//        productInfos = productInfos.stream()
//                .filter(e -> e.getOrderProductId().equals(dto.getOrderProductId().toString()))
//                .collect(Collectors.toList());
        log.info("过滤后产品信息:{}", JSON.toJSONString(productInfos));
        if (CollectionUtils.isEmpty(productInfos)) {
            throw new RuntimeException("产品已申请开通，无需重复申请!");
        }
        return productInfos.get(0);
    }

    /**
     * 获取产品集合
     *
     * @param dto 参数
     * @return List<ProductInfo>
     */
    private List<ProductInfo> getProductList(ApplyOpenProductBatchDTO dto) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        log.info("产品信息:{}", JSON.toJSONString(productInfos));
        productInfos =
                productInfos.stream().filter(e -> OpenStatusEnum.NOT_OPENED.getCode().equals(e.getProductOpenStatus())).collect(Collectors.toList());
        return productInfos;
    }

    private List<ProductInfo> getProductListOpened(ApplyOpenProductBatchDTO dto) {
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        log.info("产品信息:{}", JSON.toJSONString(productInfos));
        productInfos =
                productInfos.stream().filter(e -> OpenStatusEnum.OPENED.getCode().equals(e.getProductOpenStatus())).collect(Collectors.toList());
        return productInfos;
    }

    /**
     * 回更工单产品表产品的状态
     *
     * @param projectInfoId
     * @param orderProductId
     * @param deployFlag
     * @return
     */
    public Boolean updateOrderProductStatus(Long projectInfoId, Long orderProductId, Integer deployFlag,
                                            Integer openStatus) {
        ProjOrderProduct product = new ProjOrderProduct();
        product.setArrangeStatus(deployFlag);
        product.setProductOpenStatus(openStatus);
        product.setOrderProductId(orderProductId);
        return projOrderProductMapper.updateById(product) > 0;
    }

    public void updateEspectialOrderProductStatus(Long projectInfoId, List<Long> specialProductRecordIds,
                                                  Integer deployFlag, Integer openStatus) {
        ProjSpecialProductRecord specialProductRecord = new ProjSpecialProductRecord();
        specialProductRecord.setProductOpenStatus(openStatus);
        specialProductRecord.setArrangeStatus(deployFlag);
        int count = specialProductRecordMapper.update(specialProductRecord,
                new UpdateWrapper<ProjSpecialProductRecord>().in("special_product_id", specialProductRecordIds).eq(
                        "project_info_id", projectInfoId));
        log.info("更新特殊产品表产品状态. count: {}, spetailRecord: {}", count, specialProductRecord);
    }

    /**
     * 获取授权产品集合
     * 若skipBranchProduct=true, 则会清空分院产品id
     *
     * @param projectInfoId 项目id
     * @param yyProductIds  产品id
     * @return List<ProductEmpowerDTO> 实际授权的产品内容
     */
    public List<ProductEmpowerDTO> findProjEmpowerProductRecord(Long projectInfoId, List<Long> yyProductIds) {
        log.info("查询授权模块. projectInfoId: {}, yyProductIds: {}", projectInfoId, yyProductIds);
        // 查询要授权的菜单
        List<ProjProductEmpowerRecord> projEmpowerRecords =
                projProductEmpowerRecordService.findEmpowerRecord(projectInfoId, yyProductIds);
        log.info("授权菜单, projectInfoId: {}, empower: {}", projectInfoId, projEmpowerRecords);
        if (CollUtil.isEmpty(projEmpowerRecords)) {
            return CollUtil.newArrayList();
        }
        List<ProductEmpowerDTO> productEmpowerDTOS = CollUtil.newArrayList();
        for (ProjProductEmpowerRecord projEmpowerRecord : projEmpowerRecords) {
            ProductEmpowerDTO productEmpowerDTO = new ProductEmpowerDTO();
            BeanUtils.copyProperties(projEmpowerRecord, productEmpowerDTO);
            productEmpowerDTOS.add(productEmpowerDTO);
        }
        return distinctEmpowerProductByCode(productEmpowerDTOS);
    }

    public Result<List<EmpowerRecord>> applyEmpowersImpl(ProductEmpowerImplDTO productEmpowerImplDTOS) {
        Long projectInfoId = productEmpowerImplDTOS.getProjectInfoId();
        List<Long> yyProductIds = productEmpowerImplDTOS.getYyProductIds();
        String authDesc = getAuthDesc(productEmpowerImplDTOS);
        if (CollUtil.isEmpty(productEmpowerImplDTOS.getProductEmpowerDTOS())) {
            log.warn("未查询到" + authDesc + "产品, 直接更新产品开通状态. projectInfoId: "
                    + "{}, yyProductIds: {}", projectInfoId, yyProductIds);
            return Result.success(CollUtil.newArrayList());
        }
        Map<Long, List<String>> errorRecordMap = new HashMap<>();
        Map<Long, String> singleErrorRecordMap = new HashMap<>();
        Map<String, EmpowerRecord> hospitalEmpRecordMap = MapUtil.newHashMap();
        // 调用API进行产品加密
        List<Long> empowerYyProductIds =
                productEmpowerImplDTOS.getProductEmpowerDTOS().stream().map(ProductEmpowerDTO::getYyOrderProductId).distinct().collect(Collectors.toList());
        productEmpowerImplDTOS.getHospitalInfos().forEach(e -> {
            try {
                // 刷新域名缓存
                commonService.refreshDomain(e);
                // 单独处理授权
                SingleProductAuthDTO singleProductAuthDTO = SingleProductAuthDTO.builder()
                        .singleErrorRecordMap(singleErrorRecordMap)
                        .hospitalInfo(e)
                        .yyProductIds(empowerYyProductIds)
                        .openAuth(productEmpowerImplDTOS.getOpenAuth())
                        .build();
                singleProductAuth(singleProductAuthDTO);
                // 正常产品授权
                NormalProductAuthDTO normalProductAuthDTO = NormalProductAuthDTO.builder()
                        .projectInfoId(productEmpowerImplDTOS.getProjectInfoId())
                        .hospitalInfo(e)
                        .productEmpowerDTOS(productEmpowerImplDTOS.getProductEmpowerDTOS())
                        .yyProductIds(yyProductIds)
                        .errorRecordMap(errorRecordMap)
                        .openAuth(productEmpowerImplDTOS.getOpenAuth())
                        .productEmpowerImplDTO(productEmpowerImplDTOS)
                        .messageContentKeyWord(productEmpowerImplDTOS.getMessageContentKeyWord())
                        .build();
                nomalProductEmpower(normalProductAuthDTO);
                // 添加授权成功的医院
                hospitalEmpRecordMap.put(e.getHospitalName(),
                        EmpowerRecord.builder()
                                .status(NumberEnum.NO_1.num())
                                .build());
            } catch (Throwable throwable) {
                log.error("医院授权异常. message: {}, e=", throwable.getMessage(), throwable);
                // 截取异常信息.
                String message = getExMsgSubscribe(throwable.getMessage());
                // 添加异常医院信息
                hospitalEmpRecordMap.put(e.getHospitalName(),
                        EmpowerRecord.builder()
                                .status(NumberEnum.NO_0.num())
                                .message(message)
                                .build());
            }
        });
        if (CollUtil.isNotEmpty(errorRecordMap)) {
            String operName = "产品授权记录" + productEmpowerImplDTOS.getMessageContentKeyWord()
                    + ":projectInfoId:" + projectInfoId;
            String messageContent = ", " + authDesc + "异常. 菜单编码对照异常等, "
                    + "详情请查询数据库日志.";
            sendMessageForApplyEmpower(projectInfoId, productEmpowerImplDTOS.getMessageContentKeyWord(), operName,
                    errorRecordMap, messageContent);
        }
        if (CollUtil.isNotEmpty(singleErrorRecordMap)) {
            String operName =
                    authDesc + "特殊产品记录:" + productEmpowerImplDTOS.getMessageContentKeyWord()
                            + ":projectInfoId:" + projectInfoId;
            String messageContent = ", 特殊产品" + authDesc + "异常, 详情请查询数据库日志.";
            sendMessageForApplyEmpower(projectInfoId, productEmpowerImplDTOS.getMessageContentKeyWord(), operName,
                    singleErrorRecordMap, messageContent);
        }
        // 添加页面提醒（此部分只在手动添加授权时会用到）
        List<EmpowerRecord> tipMsgList = CollUtil.newArrayList();
        if (MapUtil.isNotEmpty(hospitalEmpRecordMap)) {
            // 拼写返回值
            hospitalEmpRecordMap.keySet().forEach(hos -> {
                // 判断异常医院和正常医院进行添加
                EmpowerRecord record = hospitalEmpRecordMap.get(hos);
                String contentStr = hos + productEmpowerImplDTOS.getAuthDesc();
                if (record.getStatus() == NumberEnum.NO_0.num()) {
                    contentStr += "异常: " + StrUtil.COLON + record.getMessage();
                } else {
                    contentStr += "正常." + (StrUtil.isNotBlank(record.getMessage()) ? record.getMessage()
                            : StrUtil.EMPTY);
                }
                EmpowerRecord copy =
                        EmpowerRecord.builder().message(contentStr).status(record.getStatus()).build();
                tipMsgList.add(copy);
            });
            // 异常医院
            List<EmpowerRecordExtend> extendList = CollUtil.newArrayList();
            hospitalEmpRecordMap.keySet().forEach(e -> {
                EmpowerRecord record = hospitalEmpRecordMap.get(e);
                if (record.getStatus() == NumberEnum.NO_0.num()) {
                    extendList.add(EmpowerRecordExtend.builder()
                            .empowerRecord(record)
                            .hospitalName(e)
                            .build());
                }
            });
            // 处理异常医院进行提醒
            if (CollUtil.isNotEmpty(extendList)) {
                StringBuilder hosMesContent = new StringBuilder();
                // 拼接异常
                for (EmpowerRecordExtend empowerRecordExtend : extendList) {
                    hosMesContent.append(empowerRecordExtend.getHospitalName()).append(StrUtil.COMMA)
                            .append(empowerRecordExtend.getEmpowerRecord().getMessage())
                            .append(StrUtil.COMMA);
                }
                String operName =
                        authDesc + "医院异常记录:" + productEmpowerImplDTOS.getMessageContentKeyWord()
                                + ":projectInfoId:" + projectInfoId;
                String messageContent =
                        ", " + authDesc + "异常, 异常概要：" + hosMesContent + StrUtil.EMPTY
                                + " " + "详情请查询数据库日志.";
                log.error(operName);
                sendMessageForApplyEmpower(projectInfoId, productEmpowerImplDTOS.getMessageContentKeyWord(), operName,
                        extendList, messageContent);
            }
        }
        // 授权后调用系统上线30分钟解除限制
        // 2025-06-11 将上线产品
//        this.extractedUseLimit(productEmpowerImplDTOS);
        return Result.success(tipMsgList);
    }

    /**
     * 授权后调用系统上线30分钟解除限制
     * 主要应用与手动授权时使用
     *
     * @param productEmpowerImplDTOS
     */
    private void extractedUseLimit(ProductEmpowerImplDTO productEmpowerImplDTOS) {
        try {
            // 非正式环境授权时不调用上线30分钟限制
            if (!"prod".equals(activeProfiles)) {
                return;
            }
            Long projectInfoId = productEmpowerImplDTOS.getProjectInfoId();
            ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
            // 开放授权的在进行调用30分钟解除限制。
            if (productEmpowerImplDTOS.getOpenAuth() && (projectInfo == null || (projectInfo != null && projectInfo.getProjectDeliverStatus() >= NumberEnum.NO_5.num()))) {
                List<ProjHospitalInfo> cloudHospitalIds = productEmpowerImplDTOS.getHospitalInfos();
                List<Long> arrayList = new ArrayList<>();
                List<HospitalOnlinePublishedSystemDTO> publishedSystems = new ArrayList<>();
                if (cloudHospitalIds != null && cloudHospitalIds.size() > 0) {
                    cloudHospitalIds.forEach(hospitalInfo -> {
                        for (ProductEmpowerDTO productEmpowerDTO : productEmpowerImplDTOS.getProductEmpowerDTOS()) {
                            HospitalOnlinePublishedSystemDTO hospitalOnlinePublishedSystemDTO = new HospitalOnlinePublishedSystemDTO();
                            hospitalOnlinePublishedSystemDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
                            hospitalOnlinePublishedSystemDTO.setPublishedSystemCode(productEmpowerDTO.getMsunHealthModuleCode());
                            hospitalOnlinePublishedSystemDTO.setPublishedSystemCommonName(productEmpowerDTO.getMsunHealthModule());
                            hospitalOnlinePublishedSystemDTO.setPublishedSystemMenuCode(productEmpowerDTO.getMsunHealthModuleCode());
                            hospitalOnlinePublishedSystemDTO.setPublishedSystemStandardName(productEmpowerDTO.getMsunHealthModule());
                            publishedSystems.add(hospitalOnlinePublishedSystemDTO);
                        }
                    });
                }
                if (CollectionUtil.isNotEmpty(cloudHospitalIds)) {
                    cloudHospitalIds.stream().forEach(hospitalInfoMap -> {
                        if (!arrayList.contains(hospitalInfoMap.getCloudHospitalId())) {
                            arrayList.add(hospitalInfoMap.getCloudHospitalId());
                        }
                    });
                }
                onlineStepService.extracted(cloudHospitalIds.get(0), arrayList, publishedSystems);
            }
        } catch (Exception e) {
            log.error("授权后调用系统上线30分钟解除限制异常", e);
        }
    }

    /**
     * 更新数据库开放权限
     *
     * @param productEmpowerImplDTOS 请求参数
     */
    public void updateSchemaGrantWrapper(ProductEmpowerImplDTO productEmpowerImplDTOS) {
        // 11-20 修改内容：不在此处判定是否首期项目进行判断处理。 将判断首期内容放入内层逻辑。
        // 若不做处理则跳过
        // 上线后不允许授权数据库权限
        ProjProjectInfo projectInfo = mainService.getProjectInfo(productEmpowerImplDTOS.getProjectInfoId());
        if (DbAuthFlagEnum.DONT_HANDLE.getCode()
                == productEmpowerImplDTOS.getDbAuthFlagEnum().getCode() || projectInfo.getProjectDeliverStatus() >= NumberEnum.NO_5.num()) {
            log.info("不做处理时进行跳过 updateSchemaGrantWrapper{}", productEmpowerImplDTOS.getDbAuthFlagEnum().getCode());
            return;
        }

        onlineStepService.updateSchemaGrant(productEmpowerImplDTOS.getProjectInfoId(),
                productEmpowerImplDTOS.getHospitalInfos().stream().map(ProjHospitalInfo::getCloudHospitalId).collect(Collectors.toList()),
                productEmpowerImplDTOS.getYyProductIds(),
                StrUtil.toString(productEmpowerImplDTOS.getDbAuthFlagEnum().getCode()));
    }

    /**
     * 授权异常记录发送消息
     *
     * @param projectInfoId         项目id
     * @param messageContentKeyWord 关键词
     * @param operName              日志名
     * @param operDetailObj         日志详情
     * @param messageContent        消息内容
     */
    private void sendMessageForApplyEmpower(Long projectInfoId, String messageContentKeyWord, String operName,
                                            Object operDetailObj, String messageContent) {
        // 记录系统日志
        sysOperLogService.apiOperLogInsert(operDetailObj, operName, StrUtil.EMPTY, Log.LogOperType.OTHER.getCode());
        // 拼接消息参数
        StringBuilder errorRecordContent = new StringBuilder();
        errorRecordContent.append(messageContentKeyWord).append(messageContent);
        try {
            log.info("向管理员发送交付异常通知. projectInfoId: {}, message: {}", projectInfoId, errorRecordContent);
            exceptionMessageService.sendToSystemManager(projectInfoId, errorRecordContent.toString());
        } catch (Throwable e) {
            log.error("消息发送异常: message: {}, e=", e.getMessage(), e);
        }
    }

    public Result<List<EmpowerRecord>> applyEmpowers(List<ProductEmpowerDTO> productEmpowerDTOS,
                                                     Long projectInfoId,
                                                     List<ProjHospitalInfo> hospitalInfos,
                                                     List<Long> yyProductIds) {
        // 获取项目
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        // 拼接授权参数
        ProductEmpowerImplDTO implDTO = ProductEmpowerImplDTO.builder()
                .projectInfoId(projectInfoId)
                .yyProductIds(yyProductIds)
                .productEmpowerDTOS(productEmpowerDTOS)
                .hospitalInfos(hospitalInfos)
                .dbAuthFlagEnum(DbAuthFlagEnum.OPEN)
                .openAuth(true)
                .productEmpowerScope(ProductEmpowerScopeEnum.PRODUCT.getCode())
                .customInfoId(projectInfo.getCustomInfoId())
                .implementationType(projectInfo.getProjectType())
                .build();
        return applyEmpowers(implDTO);
    }

    @Override
    public Result<List<EmpowerRecord>> applyEmpowers(Long projectInfoId, List<ProjHospitalInfo> hospitalInfos,
                                                     List<Long> yyProductIds) {
        List<ProductEmpowerDTO> productEmpowerDTOS =
                findProjEmpowerProductRecord(projectInfoId, yyProductIds);
        return applyEmpowers(productEmpowerDTOS, projectInfoId, hospitalInfos, yyProductIds);
    }

    /**
     * 产品加密授权
     * 需要手动传递需要对照授权的产品记录
     *
     * @param productEmpowerImplDTOS 项目授权产品记录等参数
     * @return ResponseResult<SystemSettingResultDto>
     */
    public Result<List<EmpowerRecord>> applyEmpowers(ProductEmpowerImplDTO productEmpowerImplDTOS) {
        log.info(getAuthDesc(productEmpowerImplDTOS) + "接收到的参数: {},{}",
                productEmpowerImplDTOS.getHospitalInfos(),
                productEmpowerImplDTOS.getYyProductIds());
        if (CollectionUtils.isEmpty(productEmpowerImplDTOS.getHospitalInfos()) || CollectionUtils.isEmpty(productEmpowerImplDTOS.getYyProductIds())) {
            log.warn("未查询到产品或医院信息. projectInfoId: {}", productEmpowerImplDTOS.getProjectInfoId());
            EmpowerRecord empowerRecord =
                    EmpowerRecord.builder().message(productEmpowerImplDTOS.getMessageContentKeyWord() + ", "
                                    + "未查询到产品或医院信息" + ".").status(NumberEnum.NO_0.num())
                            .build().subscribeMessage();
            return Result.fail(CollUtil.newArrayList(empowerRecord));
        }
        // 添加产品开通状态枚举
        productEmpowerImplDTOS.setProductOpenStatusEnum(productEmpowerImplDTOS.getOpenAuth()
                ? ProductOpenStatusEnum.OPENED : ProductOpenStatusEnum.NOT_OPEN);
        // 数据库开放权限设置(若取消授权,则默认关闭数据库开放权限,若授权,根据传参设置)
        productEmpowerImplDTOS.setDbAuthFlagEnum(!productEmpowerImplDTOS.getOpenAuth()
                ? DbAuthFlagEnum.CLOSE : productEmpowerImplDTOS.getDbAuthFlagEnum());
        // 设置授权描述
        productEmpowerImplDTOS.setAuthDesc(getAuthDesc(productEmpowerImplDTOS));
        // 设置是否授权菜单
        productEmpowerImplDTOS.setAuthModule(productEmpowerImplDTOS.getProductEmpowerScope() == ProductEmpowerScopeEnum.MODULE.getCode());
        // 设置异常消息关键测
        String messageContentKeyWord;
        if (!productEmpowerImplDTOS.getAuthModule()) {
            ProjProjectInfo projectInfo = mainService.getProjectInfo(productEmpowerImplDTOS.getProjectInfoId());
            messageContentKeyWord = projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber();
        } else {
            // 获取客户名称、实施类型
            ProjCustomInfo customInfo = customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq(
                    "custom_info_id",
                    productEmpowerImplDTOS.getCustomInfoId()));
            String customName = ObjectUtil.isNotEmpty(customInfo) ? customInfo.getCustomName() : StrUtil.EMPTY;
            ProjectTypeEnums projectTypeEnums =
                    ProjectTypeEnums.getEnum(productEmpowerImplDTOS.getImplementationType());
            String implementationTypeName;
            if (ObjectUtil.isNotEmpty(projectTypeEnums)) {
                assert projectTypeEnums != null;
                implementationTypeName = projectTypeEnums.getName();
            } else {
                implementationTypeName = StrUtil.EMPTY;
            }
            // 设置消息关键词
            messageContentKeyWord =
                    customName + "-" + implementationTypeName + StrUtil.COMMA + productEmpowerImplDTOS.getAuthDesc();
        }
        productEmpowerImplDTOS.setMessageContentKeyWord(messageContentKeyWord);
        try {
            // 授权深处
            Result<List<EmpowerRecord>> result = applyEmpowersImpl(productEmpowerImplDTOS);
            if (result.isSuccess()) {
                // 若只授权菜单不对产品状态进行更新
                if (productEmpowerImplDTOS.getProductEmpowerScope() == ProductEmpowerScopeEnum.PRODUCT.getCode()) {
                    applyOrderProductService.updateProductOpenStatus(productEmpowerImplDTOS.getProductOpenStatusEnum(),
                            productEmpowerImplDTOS.getYyProductIds(), productEmpowerImplDTOS.getProjectInfoId());
                    // 数据库授权, 因授权需要项目id, 故只有在对产品授权时参能进行操作
                    updateSchemaGrantWrapper(productEmpowerImplDTOS);
                }
            }
            return result;
        } catch (Throwable e) {
            log.error(getAuthDesc(productEmpowerImplDTOS) + "异常, 向管理员发送交付异常通知, projectInfoId: {}. e"
                            + ".message: {}, e=",
                    productEmpowerImplDTOS.getProjectInfoId(), e.getMessage(), e);
            String message;
            if (e instanceof CustomException) {
                message = e.getMessage();
            } else {
                ProjProjectInfo projectInfo = mainService.getProjectInfo(productEmpowerImplDTOS.getProjectInfoId());
                message =
                        projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + ", 产品" + getAuthDesc(productEmpowerImplDTOS) + "异常. 请及时处理.";
            }
            exceptionMessageService.sendToSystemManager(productEmpowerImplDTOS.getProjectInfoId(), message);
            // 拼接错误信息
            EmpowerRecord empowerRecord =
                    EmpowerRecord.builder().message(message).status(NumberEnum.NO_0.num())
                            .build().subscribeMessage();
            return Result.fail(CollUtil.newArrayList(empowerRecord));
        }
    }

    /**
     * 正常产品授权
     *
     * @param normalProductAuthDTO 产品授权参数
     * @return List<ProjProductEmpowerRecord> 授权成功的产品记录
     */
    public List<ProductEmpowerDTO> nomalProductEmpower(NormalProductAuthDTO normalProductAuthDTO) {
        ProjHospitalInfo hospitalInfo = normalProductAuthDTO.getHospitalInfo();
        Map<Long, List<String>> errorRecordMap = normalProductAuthDTO.getErrorRecordMap();
        // 刷新域名缓存
        commonService.refreshDomain(hospitalInfo);
        // 过滤含对照的授权产品, 记录未对照的产品进行提示
        String uuid = getUUID();
        // 拼接授权需要参数
        ModuleEmpowerDTO moduleEmpowerDTO = ModuleEmpowerDTO.builder()
                .uuid(uuid)
                .authDesc(normalProductAuthDTO.getProductEmpowerImplDTO().getAuthDesc())
                .productEmpowerDTOS(normalProductAuthDTO.getProductEmpowerDTOS())
                .errorRecordMap(errorRecordMap)
                .hospitalInfo(hospitalInfo)
                .openAuth(normalProductAuthDTO.isOpenAuth())
                .authModule(normalProductAuthDTO.getProductEmpowerImplDTO().getAuthModule())
                .projectInfoId(normalProductAuthDTO.getProjectInfoId())
                .messageContentKeyWord(normalProductAuthDTO.getMessageContentKeyWord())
                .build();
        // 授权往里走
        ModuleEmpowerResultDTO moduleEmpowerResult = empowerModule(moduleEmpowerDTO);
        return handleResult(moduleEmpowerResult, moduleEmpowerDTO);
    }

    public List<ProductEmpowerDTO> handleResult(ModuleEmpowerResultDTO moduleEmpowerResult,
                                                ModuleEmpowerDTO moduleEmpowerDTO) {
        List<ProductEmpowerDTO> productEmpowerDTOS = moduleEmpowerDTO.getProductEmpowerDTOS();
        Map<Long, List<String>> errorRecordMap = moduleEmpowerDTO.getErrorRecordMap();
        String uuid = moduleEmpowerDTO.getUuid();
        Long projectInfoId = moduleEmpowerDTO.getProjectInfoId();
        List<ProductEmpowerDTO> sucEmpowerRecords = null;
        List<ProductEmpowerDTO> failEmpowerRecords;
        ResponseResult<SystemSettingResultDto> result = moduleEmpowerResult.getResult();
        CollectEmpowerError collectEmpowerError = CollectEmpowerError.builder()
                .isProductEncrypt(false)
                .hospitalInfo(moduleEmpowerDTO.getHospitalInfo())
                .build();
        if (!result.isSuccess()) {
            collectEmpowerError.setEmpowerRecordList(productEmpowerDTOS);
            collectEmpowerError.setUuid(uuid);
            setEmpowerErrorRecord(projectInfoId, collectEmpowerError, errorRecordMap);
        } else {
            sucEmpowerRecords =
                    productEmpowerDTOS.stream().filter(e -> moduleEmpowerResult.getCodeAndEncryption().stream().anyMatch(f -> f.getPublishedSystemCode().equals(e.getMsunHealthModuleCode()))).collect(Collectors.toList());
            List<ProductEmpowerDTO> finalEmpowerRecords = sucEmpowerRecords;
            failEmpowerRecords =
                    productEmpowerDTOS.stream().filter(e -> finalEmpowerRecords.stream().noneMatch(f -> f.getYyOrderProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(failEmpowerRecords)) {
                log.warn("未授权成功的产品模块. {}", failEmpowerRecords);
                collectEmpowerError.setEmpowerRecordList(failEmpowerRecords);
                collectEmpowerError.setUuid(uuid);
                setEmpowerErrorRecord(projectInfoId, collectEmpowerError, errorRecordMap);
            }
        }
        return sucEmpowerRecords;
    }

    /**
     * 模块授权
     *
     * @param moduleEmpowerDTO 请求参数
     * @return ResponseResult<SystemSettingResultDto> 处理结果
     */
    private ModuleEmpowerResultDTO empowerModule(ModuleEmpowerDTO moduleEmpowerDTO) {
        String uuid = moduleEmpowerDTO.getUuid();
        ProjHospitalInfo hospitalInfo = moduleEmpowerDTO.getHospitalInfo();
        List<ProductEmpowerDTO> productEmpowerDTOS = moduleEmpowerDTO.getProductEmpowerDTOS();
        Long projectInfoId = moduleEmpowerDTO.getProjectInfoId();
        Map<Long, List<String>> errorRecordMap = moduleEmpowerDTO.getErrorRecordMap();
        // 拼接批量加密参数, 调用
        SystemConfigDto<ProductEncryptDto> dto = new SystemConfigDto<>();
        List<ProductEncryptDto> productEncryptDtos = CollUtil.newArrayList();
        dto.setData(productEncryptDtos);
        dto.setHisOrgId(hospitalInfo.getOrgId());
        dto.setOrgId(hospitalInfo.getOrgId());
        dto.setHospitalId(hospitalInfo.getCloudHospitalId());
        for (ProductEmpowerDTO record : productEmpowerDTOS) {
            ProductEncryptDto encryptDto = new ProductEncryptDto();
            encryptDto.setHisOrgId(hospitalInfo.getOrgId());
            encryptDto.setHospitalId(hospitalInfo.getCloudHospitalId());
            encryptDto.setProductCode(record.getMsunHealthModuleCode());
            productEncryptDtos.add(encryptDto);
        }
        ResponseResult<List<ProductBatchEncryptResultVo>> responseResult;
        try {
            sysOperLogService.apiOperLogInsert(dto, "产品批量加密", "入参", Log.LogOperType.OTHER.getCode());
            responseResult = systemSettingApi.productBatchEncrypt(dto);
            sysOperLogService.apiOperLogInsert(responseResult, "产品批量加密", "出参", Log.LogOperType.OTHER.getCode());
            if (ObjectUtil.isEmpty(responseResult) || ObjectUtil.isEmpty(responseResult.getData())) {
                log.error("产品批量加密接口异常, 返回值为空, responseResult: {}", responseResult);
                throw new CustomException(moduleEmpowerDTO.getMessageContentKeyWord() + ", "
                        + "产品批量加密接口异常, 请及时处理.");
            }
        } catch (Throwable calle) {
            log.error("接口调用异常. message: {}, e=", calle.getMessage(), calle);
            sysOperLogService.apiOperLogInsert(calle.getMessage(), "产品批量加密", "异常", Log.LogOperType.OTHER.getCode());
            throw calle;
        }
        // 处理返回值
        List<ProductBatchEncryptResultVo> batchEncryptResultVos = responseResult.getData();
        // 排查加密产品, 加密前与加密后是否数量一致
        List<ProductEncryptDto> notBackProduct =
                productEncryptDtos.stream().filter(e -> batchEncryptResultVos.stream().noneMatch(f -> e.getProductCode().equals(f.getProductCode()))).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notBackProduct)) {
            // 记录未正常加密的产品（批量加密后未返回的产品）
            if (CollUtil.isNotEmpty(productEmpowerDTOS)) {
                List<ProductEmpowerDTO> notBackProductEmpowerList =
                        productEmpowerDTOS.stream().filter(e -> notBackProduct.stream().anyMatch(f -> f.getProductCode().equals(e.getMsunHealthModuleCode()))).collect(Collectors.toList());
                CollectEmpowerError collectEmpowerError =
                        CollectEmpowerError.builder().isProductEncrypt(true).empowerRecordList(notBackProductEmpowerList).uuid(uuid).build();
                setEmpowerErrorRecord(projectInfoId, collectEmpowerError, errorRecordMap);
            }
        }
        List<SystemAuthorizeInfoSaveDto> codeAndEncryption = new ArrayList<>();
        List<ProductBatchEncryptResultVo> empEncryptCode = CollUtil.newArrayList();
        for (ProductBatchEncryptResultVo resultVo : batchEncryptResultVos) {
            if (StrUtil.isBlank(resultVo.getEncryptCode())) {
                empEncryptCode.add(resultVo);
                continue;
            }
            SystemAuthorizeInfoSaveDto authorizeInfoSaveDto = new SystemAuthorizeInfoSaveDto();
            authorizeInfoSaveDto.setPublishedSystemCode(resultVo.getProductCode());
            authorizeInfoSaveDto.setSecretKey(resultVo.getEncryptCode());
            codeAndEncryption.add(authorizeInfoSaveDto);
        }
        if (CollUtil.isEmpty(codeAndEncryption)) {
            String warnContent = moduleEmpowerDTO.getMessageContentKeyWord() + ", 产品批量加密接口异常,"
                    + " 未获取到加密串, 请及时处理.";
            log.warn(warnContent);
            throw new CustomException(warnContent);
        }
        // 判断返回的加密串是否存在, 不存在则记录到错误中
        if (CollUtil.isNotEmpty(empEncryptCode)) {
            log.warn("产品加密未获取到加密串. {}", empEncryptCode);
            List<ProductEmpowerDTO> emptyEncrptRecords =
                    productEmpowerDTOS.stream().filter(e -> empEncryptCode.stream().anyMatch(f -> e.getMsunHealthModuleCode().equals(f.getProductCode()))).collect(Collectors.toList());
            CollectEmpowerError collectEmpowerError =
                    CollectEmpowerError.builder().isProductEncrypt(true).empowerRecordList(emptyEncrptRecords).uuid(uuid).build();
            setEmpowerErrorRecord(projectInfoId, collectEmpowerError, errorRecordMap);
        }
        //调用API, API调用基础中台接口, 发送数据给基础中台
        SystemConfigDto<SystemAuthorizeInfoSaveDto> saveDto = new SystemConfigDto<>();
        saveDto.setData(codeAndEncryption);
        saveDto.setHospitalId(hospitalInfo.getCloudHospitalId());
        saveDto.setHisOrgId(hospitalInfo.getOrgId());
        saveDto.setOrgId(hospitalInfo.getOrgId());
        //调用基础中台  发送加密串
        applyOrderService.applyParamOperLogInsert(saveDto,
                uuid + "-调用技术中台请求参数(" + moduleEmpowerDTO.getAuthDesc() + ")",
                "applyEmpowers");
        ResponseResult<SystemSettingResultDto> result;
        if (moduleEmpowerDTO.getOpenAuth()) {
            // 授权
            result = systemSettingApi.sentProjectEncryption(saveDto);
            // 解析result, 若success为false则授权异常.
            if (ObjectUtil.isEmpty(result)) {
                String tip = moduleEmpowerDTO.getMessageContentKeyWord() + "调用技术中台接口返回值异常, 返回值为空.";
                throw new CustomException(tip);
            }
            if (!result.isSuccess()) {
                String tip = moduleEmpowerDTO.getMessageContentKeyWord() + "授权失败：" + result.getMessage();
                log.error("{}授权失败\n参数：{}\n\n返回值：{}", moduleEmpowerDTO.getMessageContentKeyWord(), JSON.toJSONString(saveDto), JSON.toJSONString(result));
                throw new CustomException(tip);
            }
        } else {
            // 取消授权
            result = systemSettingApi.batchCancelSystemAuthorized(saveDto);
            if (!result.isSuccess()) {
                String tip = moduleEmpowerDTO.getMessageContentKeyWord() + "取消授权失败：" + result.getMessage();
                log.error("{}取消授权失败\n参数：{}\n\n返回值：{}", moduleEmpowerDTO.getMessageContentKeyWord(), JSON.toJSONString(saveDto), JSON.toJSONString(result));
                throw new CustomException(tip);
            }
        }
        applyOrderService.applyParamOperLogInsert(result,
                uuid + "-调用技术中台结果(" + moduleEmpowerDTO.getAuthDesc() + ")",
                "applyEmpowers");
        ModuleEmpowerResultDTO moduleEmpowerResult = ModuleEmpowerResultDTO.builder()
                .codeAndEncryption(codeAndEncryption)
                .result(result)
                .build();
        return moduleEmpowerResult;
    }

    /**
     * 特殊产品授权
     *
     * @param singleProductAuthDTO 特殊产品授权请求参数
     */
    public void singleProductAuth(SingleProductAuthDTO singleProductAuthDTO) {
        log.info("特殊产品单独授权. 请求参数-{}", JSON.toJSONString(singleProductAuthDTO));
        List<Long> needOpenProductIds =
                especialProductIds.stream().filter(e -> singleProductAuthDTO.getYyProductIds().stream().anyMatch(f -> e.longValue() == f)).distinct().collect(Collectors.toList());
        if (singleProductAuthDTO.isOpenAuth()) {
            // 特殊产品取消授权跳过
            if (CollUtil.isNotEmpty(needOpenProductIds)) {
                ProjHospitalInfo hospitalInfo = singleProductAuthDTO.getHospitalInfo();
                BatchInsertGeneralDTO<Long> param = new BatchInsertGeneralDTO<>();
                param.setHisOrgId(hospitalInfo.getOrgId());
                param.setHospitalId(hospitalInfo.getCloudHospitalId());
                log.info("特殊产品单独授权医院信息参数-{}", JSON.toJSONString(param));
                String uuid = UUID.randomUUID().toString().replace(StrUtil.DASHED, StrUtil.EMPTY);
                ResponseResult<Object> resp = getRemoteHospitalInfo(param, uuid);
                log.info("特殊产品单独授权医院信息返回值-{}", JSON.toJSONString(resp));
                JSONObject hospitalJson = JSON.parseObject(JSON.toJSONString(resp.getData()));
                if (ObjectUtil.isEmpty(hospitalJson)) {
                    singleProductAuthDTO.getSingleErrorRecordMap().put(hospitalInfo.getHospitalInfoId(),
                            "特殊产品授权时无法获取云健康医院信息. orgId: " + hospitalInfo.getOrgId() + ", cloudHospitalId: " + hospitalInfo.getCloudHospitalId() + ". 待授权产品: " + needOpenProductIds);
                    return;
                }
                singleProductAuthImpl(uuid, needOpenProductIds, hospitalInfo, hospitalJson.getString(
                                "organizationCode"),
                        singleProductAuthDTO.getSingleErrorRecordMap());
            }
        }
    }

    public ResponseResult<Object> getRemoteHospitalInfo(BatchInsertGeneralDTO<Long> param, String uuid) {
        sysOperLogService.apiOperLogInsert(param, uuid + "特殊产品单独授权医院信息入参", StrUtil.EMPTY,
                Log.LogOperType.OTHER.getCode());
        ResponseResult<Object> resp = hospitalDataApi.getHospitalInfoById(param);
        sysOperLogService.apiOperLogInsert(resp, uuid + "特殊产品单独授权医院信息出参", StrUtil.EMPTY,
                Log.LogOperType.OTHER.getCode());
        return resp;
    }

    public void singleProductAuthImpl(String uuid, List<Long> needOpenProductIds, ProjHospitalInfo hospitalInfo,
                                      String organizationCode, Map<Long, String> singleErrorRecordMap) {
        StringBuilder errorDesc = new StringBuilder();
        //病案首页质控单独授权
        if (needOpenProductIds.contains(EspecialProductEnums.QUALITY.getCode())) {
            //参数需要机构编码
            MrsQAAuthReq mrsQAAuthReq = new MrsQAAuthReq(hospitalInfo);
            mrsQAAuthReq.setOrganizationCode(organizationCode);
            mrsQAAuthReq.setOnlyCheck(false);
            log.info("病案首页质控授权参数-{}", JSON.toJSONString(mrsQAAuthReq));
            String respStr = null;
            try {
                sysOperLogService.apiOperLogInsert(mrsQAAuthReq, uuid + "-病案首页质控授权入参", "singleProductAuth",
                        Log.LogOperType.OTHER.getCode());
                respStr = productAuthFeignClient.mrsAuthorization(mrsQAAuthReq);
                sysOperLogService.apiOperLogInsert(respStr, uuid + "-病案首页质控授权返回值", "singleProductAuth",
                        Log.LogOperType.OTHER.getCode());
            } catch (Throwable e) {
                log.error("病案首页质控授权.errmessage: {}, e=", e.getMessage(), e);
                errorDesc.append("病案首页质控授权接口调用异常, 请及时处理. uuid: ").append(uuid).append(StrUtil.COMMA);
            }
            if (StrUtil.isBlank(respStr)) {
                errorDesc.append("病案首页质控授权异常, 请及时处理. uuid: ").append(uuid).append(StrUtil.COMMA);
            } else {
                log.info("病案首页质控授权返回值-{}", respStr);
            }
        }
        //智能诊断编码-1039   智能手术编码-1040
        if (needOpenProductIds.contains(EspecialProductEnums.AUTO_CODE.getCode())) {
            //参数需要机构编码
            ICDAuthReq icdAuthReq = new ICDAuthReq();
            icdAuthReq.setOrganizationCode(organizationCode);
            icdAuthReq.setHospitalName(hospitalInfo.getHospitalName());
            icdAuthReq.setSysCode("icd_smart_diag");
            icdAuthReq.setOnlyCheck(false);
            log.info("智能诊断编码授权参数-{}", JSON.toJSONString(icdAuthReq));
            String respStr = null;
            try {
                sysOperLogService.apiOperLogInsert(icdAuthReq, uuid + "-智能诊断编码授权入参", "singleProductAuth",
                        Log.LogOperType.OTHER.getCode());
                respStr = icdProductAuthFeignClient.icdAuthorization(icdAuthReq);
                sysOperLogService.apiOperLogInsert(respStr, uuid + "-智能诊断编码授权返回值", "singleProductAuth",
                        Log.LogOperType.OTHER.getCode());
            } catch (Throwable e) {
                log.error("智能诊断编码授权.errmessage: {}, e=", e.getMessage(), e);
                errorDesc.append("智能诊断编码授权接口调用异常, 请及时处理. uuid: ").append(uuid).append(StrUtil.COMMA);
            }
            if (StrUtil.isBlank(respStr)) {
                errorDesc.append("智能诊断编码授权异常, 请及时处理. uuid: ").append(uuid).append(StrUtil.COMMA);
            } else {
                log.info("智能诊断编码授权返回值-{}", respStr);
            }
        }
        if (needOpenProductIds.contains(EspecialProductEnums.ICD.getCode())) {
            //参数需要机构编码
            ICDAuthReq icdAuthReq = new ICDAuthReq();
            icdAuthReq.setOrganizationCode(organizationCode);
            icdAuthReq.setHospitalName(hospitalInfo.getHospitalName());
            icdAuthReq.setSysCode("icd_smart_oper");
            icdAuthReq.setOnlyCheck(false);
            log.info("智能手术编码授权参数-{}", JSON.toJSONString(icdAuthReq));
            String respStr = null;
            try {
                sysOperLogService.apiOperLogInsert(icdAuthReq, uuid + "-智能手术编码授权入参", "singleProductAuth",
                        Log.LogOperType.OTHER.getCode());
                respStr = icdProductAuthFeignClient.icdAuthorization(icdAuthReq);
                sysOperLogService.apiOperLogInsert(respStr, uuid + "-智能手术编码授权返回值", "singleProductAuth",
                        Log.LogOperType.OTHER.getCode());
            } catch (Throwable e) {
                log.error("智能手术编码授权.errmessage: {}, e=", e.getMessage(), e);
                errorDesc.append("智能手术编码授权接口调用异常, 请及时处理. uuid: ").append(uuid).append(StrUtil.COMMA);
            }
            if (StrUtil.isBlank(respStr)) {
                errorDesc.append("智能手术编码授权异常, 请及时处理. uuid: ").append(uuid).append(StrUtil.COMMA);
            } else {
                log.info("智能手术编码授权返回值-{}", respStr);
            }
        }
        if (StrUtil.isNotBlank(errorDesc)) {
            singleErrorRecordMap.put(hospitalInfo.getHospitalInfoId(), errorDesc.toString());
        }
    }

    @Override
    public List<String> getProductName(Long projectInfoId) {
        return projOrderProductMapper.getProductName(projectInfoId);
    }

    @Override
    public int updateExcutionStatusByOrderId(Long orderInfoId, List<Integer> list, Integer unPass, Integer pass) {
        return projOrderProductMapper.updateExcutionStatusByOrderId(orderInfoId, list, unPass, pass);
    }

    /**
     * 说明: 根据项目id获取未开通医院和产品
     *
     * @param projectInfoId
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.product.NotOpenedProductHospitalCountVO>
     * @author: Yhongmin
     * @createAt: 2024/7/12 15:38
     * @remark: Copyright
     */
    @Override
    public Result<NotOpenedProductHospitalCountVO> getNotOpenedProductProjectCountByProjectInfoId(Long projectInfoId) {
        NotOpenedProductHospitalCountVO notOpenedProductHospitalCountVO = new NotOpenedProductHospitalCountVO();
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projectInfoId);
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        if (productInfos.size() == 0) {
            notOpenedProductHospitalCountVO.setNotOpenedProductCount(0);
        }
        notOpenedProductHospitalCountVO.setNotOpenedProductCount(productInfos.stream().filter(productInfo -> OpenStatusEnum.NOT_OPENED.getCode().equals(productInfo.getProductOpenStatus())).collect(Collectors.toList()).size());
        SelectHospitalDTO dto = new SelectHospitalDTO();
        dto.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> infoList = hospitalInfoService.getHospitalInfoByProjectId(dto);
        if (infoList.size() == 0) {
            notOpenedProductHospitalCountVO.setNotOpenedHospitalInfoCount(0);
        }
        notOpenedProductHospitalCountVO.setNotOpenedHospitalInfoCount(infoList.stream().filter(info -> OpenStatusEnum.NOT_OPENED.getCode().equals(info.getHospitalOpenStatus())).collect(Collectors.toList()).size());
        return Result.success(notOpenedProductHospitalCountVO);
    }

    /**
     * 单个产品进行上线
     *
     * @param productInfo
     * @return
     */
    @Override
    public Result<String> applyOpenProductOnline(ProductInfo productInfo) {
        // 获取云健康医院是否上线
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(productInfo.getProjectInfoId());
        if (projectInfo == null) {
            return Result.fail("项目不存在");
        }
        // 交付医院信息
        List<ProjHospitalInfo> listHospitalInfo = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("is_deleted", 0).eq("custom_info_id", projectInfo.getCustomInfoId()).eq("hos_project_type", projectInfo.getProjectType()));
        List<BaseIdNameResp> hospitalList = this.getHospitalList(listHospitalInfo);
        if (hospitalList == null || hospitalList.isEmpty()) {
            return Result.fail("云健康医院未上线,不能进行此操作");
        }
        // 已上线的医院调用系统管理进行上线
        this.extractedUseLimitOne(projectInfo, listHospitalInfo, hospitalList, productInfo.getYyOrderProductId());
        try {
            // 交付工单号
            String deliveryOrderNo = productInfo.getDeliveryOrderNo();
            if (!StringUtils.isBlank(deliveryOrderNo) && deliveryOrderNo.contains("特批")) {
                ProjSpecialProductRecord record = specialProductRecordMapper.selectByPrimaryKey(Long.valueOf(productInfo.getOrderProductId()));
                if (record != null) {
                    // 设置已上线
                    record.setProductExcutionStatus(1);
                    specialProductRecordMapper.updateById(record);
                }
            } else {
                // 将项目下的产品进行上线
                ProjOrderProduct projOrderProduct = projOrderProductMapper.selectByPrimaryKey(Long.valueOf(productInfo.getOrderProductId()));
                if (projOrderProduct != null) {
                    projOrderProduct.setProductExcutionStatus(1);
                    projOrderProductMapper.updateById(projOrderProduct);
                }

            }
        } catch (Exception e) {
            log.error("上线异常: {}", e.getMessage());
        }
        return Result.success();
    }

    /**
     * 云健康医院上线
     *
     * @param projectInfo
     * @param listHospitalInfo
     * @param hospitalList
     */
    private void extractedUseLimitOne(ProjProjectInfo projectInfo, List<ProjHospitalInfo> listHospitalInfo, List<BaseIdNameResp> hospitalList, Long yyProductId) {
        ProductEmpowerImplDTO prod = new ProductEmpowerImplDTO();
        prod.setProjectInfoId(projectInfo.getProjectInfoId());
        this.extractedUseLimit(prod);
        List<ProductEmpowerDTO> productEmpowerDTOS = projOrderProductMapper.selectProductData(yyProductId);
        List<Long> arrayList = new ArrayList<>();
        List<HospitalOnlinePublishedSystemDTO> publishedSystems = new ArrayList<>();
        if (hospitalList != null && hospitalList.size() > 0) {
            hospitalList.forEach(hospitalInfo -> {
                for (ProductEmpowerDTO productEmpowerDTO : productEmpowerDTOS) {
                    HospitalOnlinePublishedSystemDTO hospitalOnlinePublishedSystemDTO = new HospitalOnlinePublishedSystemDTO();
                    hospitalOnlinePublishedSystemDTO.setHospitalId(hospitalInfo.getId());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemCode(productEmpowerDTO.getMsunHealthModuleCode());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemCommonName(productEmpowerDTO.getMsunHealthModule());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemMenuCode(productEmpowerDTO.getMsunHealthModuleCode());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemStandardName(productEmpowerDTO.getMsunHealthModule());
                    publishedSystems.add(hospitalOnlinePublishedSystemDTO);
                }
                if (!arrayList.contains(hospitalInfo.getId())) {
                    arrayList.add(hospitalInfo.getId());
                }
            });
        }
        try {

            listHospitalInfo.stream().forEach(hospitalInfo -> {
                if (arrayList.contains(hospitalInfo.getCloudHospitalId())) {
                    log.error("onlineStepService.extracted开始执行");
                    onlineStepService.extracted(listHospitalInfo.get(0), arrayList, publishedSystems);
                    log.error("onlineStepService.extracted执行成功");
                }
            });
        } catch (Exception e) {
            log.error("项目上线-同步权限异常", e);
        }

    }

    /**
     * 获取医院信息
     *
     * @param listHospitalInfo
     * @return
     */
    private List<BaseIdNameResp> getHospitalList(List<ProjHospitalInfo> listHospitalInfo) {
        if (listHospitalInfo == null || listHospitalInfo.isEmpty()) {
            return new ArrayList<>();
        }
        ProjHospitalInfo projHospitalInfo = listHospitalInfo.get(0);
        // 调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // SQL文本
        String sqlText = "select hospital_id as id,hospital_name as name from comm.hospital where invalid_flag = '0' and online = '1';";
        // 组装调用API的参数
        SqlSelectLoginDTO sqlSelectLoginDTO = new SqlSelectLoginDTO();
        sqlSelectLoginDTO.setHospitalInfoId(projHospitalInfo.getHospitalInfoId());
        sqlSelectLoginDTO.setSqlStr(AesUtil.encrypt(sqlText));
        // 组装调用API的参数
        SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
        sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
        try {
            ResponseResult<List<JSONObject>> responseResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
            if (responseResult == null || CollectionUtils.isEmpty(responseResult.getData())) {
                return new ArrayList<>();
            }
            String jsonString = JSON.toJSONString(responseResult.getData());
            TypeReference<List<BaseIdNameResp>> typeReference = new TypeReference<List<BaseIdNameResp>>(BaseIdNameResp.class) {
            };
            return JSON.parseObject(jsonString, typeReference);
        } catch (Exception e) {
            log.error("查询数据库异常", e);
            return new ArrayList<>();
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CollectEmpowerError {
        private String uuid;
        //        private Map<Long, List<String>> errorRecordMap;
        private ProjHospitalInfo hospitalInfo;
        private List<ProductEmpowerDTO> empowerRecordList;
        private boolean isProductEncrypt;
    }
}
