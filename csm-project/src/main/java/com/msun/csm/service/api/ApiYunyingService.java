package com.msun.csm.service.api;

import java.util.Date;
import java.util.List;

import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.feign.entity.yunying.req.YunOpenDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamEachDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamResultDTO;
import com.msun.csm.model.imsp.CustomerParamerDTO;
import com.msun.csm.model.imsp.ProjCustomInfoResp;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.imsp.SyncYunRenewalApplicationRenewDTO;
import com.msun.csm.model.imsp.SyncYunyingApplicationDTO;
import com.msun.csm.model.yunying.*;
import com.msun.csm.model.yunying.resp.HospitalInfo;


/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/26
 */
public interface ApiYunyingService {
    /**
     * 同步合同信息
     *
     * @param syncContractDTO
     * @return
     */
    Result syncWorkOrder(SyncContractDTO syncContractDTO);

    /**
     * 调用运维平台, 向系统管理发送指令, 设置是否要对医院进行到期提醒。
     *
     * @param dto 运营请求参数
     * @return 返回值
     */
    Result<List<HospitalReminderResult>> setHospitalReminder(HospitalReminderSwapDTO dto);

    /**
     * 实施团队变更
     *
     * @param dto 变更请求内容
     * @return 变更结果
     */
    Result<ChangeCustomTeamResultDTO> changeCustomTeam(ChangeCustomTeamDTO dto);

    /**
     * 处理客户实施团队变更
     *
     * @param changeCustomTeamEachDTO 请求变更的参数
     */
    void changeCustomTeamEach(ChangeCustomTeamEachDTO changeCustomTeamEachDTO, ChangeCustomTeamDTO changeCustomTeamDTO);

    /**
     * 变更项目实施团队
     *
     * @param changeCustomTeamEachDTO 请求参数
     */
    void changeProjectTeam(ChangeCustomTeamEachDTO changeCustomTeamEachDTO);

    /**
     * 根据项目类型和客户id查询主院
     *
     * @param projectType  项目类型
     * @param customInfoId 客户id
     * @return ProjHospitalInfo
     */
    ProjHospitalInfo getMainHospitalInfo(int projectType, Long customInfoId);


    /**
     * 手动处理项目产品对照
     *
     * @param projectInfoId
     * @return
     */
    Result dealProducts(Long projectInfoId);

    /**
     * @param dto 请求参数
     * @return ResponseData<String>
     */
    ResponseData<String> syncYunRenew(SyncYunRenewDTO dto);

    /**
     * 说明: 派单校验云资源工单续期-计算时间同步运营，运维平台
     *
     * @param dto
     * @return:com.msun.csm.common.model.ResponseData<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/7/17 16:26
     * @remark: Copyright
     */
    ResponseData<String> syncYunRenewalApplicationRenew(SyncYunRenewalApplicationRenewDTO dto, Integer projectType);

    /**
     * 同步运维云时间
     *
     * @param dto           请求参数
     * @param cloudService  客户云信息
     * @param hospitalInfos 客户相关医院
     */
    void syncYunweiCloudTime(SyncYunRenewDTO dto, ProjCustomCloudService cloudService,
                             List<ProjHospitalInfo> hospitalInfos);

    /**
     * 说明:
     *
     * @param openDate 开通时间
     * @param signDate 订阅时间
     * @param dto      请求参数
     */
    Result<YunOpenDTO> sendTimeToYunYingCloudTime(Date openDate, Date signDate, SyncYunyingApplicationDTO dto);

    /**
     * 说明: 同步运维云,运营时间
     *
     * @param dto
     * @param cloudService
     * @param hospitalInfos
     * @param projectType
     * @return:com.msun.csm.common.model.ResponseData
     * @author: Yhongmin
     * @createAt: 2024/9/23 16:51
     * @remark: Copyright
     */
    ResponseData syncYunRenewalApplicationRenewCloudTime(SyncYunRenewalApplicationRenewDTO dto,
                                                         ProjCustomCloudService cloudService,
                                                         List<ProjHospitalInfo> hospitalInfos,
                                                         Integer projectType);

    /**
     * 查询所有客户数据
     *
     * @param dto
     * @return
     */
    List<ProjCustomInfoResp> getCustomerList(CustomerParamerDTO dto);

    /**
     * 删除工单-需要处理关联数据-项目-工单-工单产品
     *
     * @param simpleId
     * @return
     */
    Result deleteOrder(SimpleId simpleId);

    /**
     * 修改客户信息
     *
     * @param updateCustomInfoDTO
     * @return
     */
    Result updateCustomInfo(UpdateCustomInfoDTO updateCustomInfoDTO);

    /**
     * 说明: 替换工单产品
     *
     * @param replaceOrderProductDTO
     * @return
     */
    Result replaceOrderProduct(ReplaceOrderProductDTO replaceOrderProductDTO);

    /**
     * 转换合同
     *
     * @param dto
     * @return
     */
    Result convertFormalContract(ConvertContractDTO dto);

    /**
     * 查询云健康医院信息
     *
     * @param dto 请求参数
     * @return 返回值, 含医院信息
     */
    Result<List<HospitalInfo>> findHospitalInfo(HospitalInfoDTO dto);

    /**
     * 首付款是否满足回调
     * @param dto
     * @return
     */
    Result<String> paySignageIsEnoughFuction(YunyingPaysignageReq dto);


    /**
     * 运维特殊事项审批日志回调
     * @param args
     * @return
     */
    Result<?> kfSpecialApproveLogCallback(ApproveLogCallbackArgs args);
}
