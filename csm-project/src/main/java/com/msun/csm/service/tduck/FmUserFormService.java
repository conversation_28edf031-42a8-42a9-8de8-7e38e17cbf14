package com.msun.csm.service.tduck;

import com.msun.csm.common.enums.tduck.FormStatusEnum;
import com.msun.csm.dao.entity.tduck.FmUserForm;
import com.msun.csm.exception.NoProjectUserFormException;

public interface FmUserFormService {


    /**
     * 根据项目ID和实施产品的运营平台ID获取项目的调研问卷
     *
     * @param projectInfoId 项目ID
     * @param yyProductId   实施产品的运营平台ID
     * @return 对应项目ID和实施产品的项目调研问卷
     */
    FmUserForm getProjectUserFormByProjectInfoIdAndYyProductId(Long projectInfoId, String yyProductId, Integer formClass);


    /**
     * 根据项目ID和实施产品的运营平台ID获取项目的调研问卷的表单key
     *
     * @param projectInfoId 项目ID
     * @param yyProductId   实施产品的运营平台ID
     * @return 对应项目ID和实施产品的项目调研问卷的表单key
     */
    String getProjectFormKeyByProjectInfoIdAndYyProductId(Long projectInfoId, String yyProductId,Integer formClass);

    String getProjectFormKeyByProjectInfoIdAndYyProductIdNoException(Long projectInfoId, String yyProductId,Integer formClass);


    /**
     * 根据项目类型和实施类型获取对应产品的调研问卷模板
     *
     * @param productId       实施产品的运营平台ID
     * @param projectType     项目类型：1-单体、2-区域
     * @param upgradationType 项目实施类型：1-老换新升级、2-新客户上线
     * @param formClass       项目实施类型：1-产品业务调研、2-全院流程模拟、3-满意度调研
     * @return 调研问卷模板
     */
    FmUserForm getTemplateUserFormByProjectInfo(String productId, Integer projectType, Integer upgradationType, Integer formClass);


    /**
     * @param projectInfoId
     * @param yyProductId
     * @return
     */
    FmUserForm getProjectUserFormByProjectInfoId(Long projectInfoId, String yyProductId, Integer formClass) throws NoProjectUserFormException;

    boolean updateFormStatusByFormKey(FormStatusEnum formStatusEnum, String formKey);

}


