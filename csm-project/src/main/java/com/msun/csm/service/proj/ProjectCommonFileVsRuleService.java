package com.msun.csm.service.proj;

import com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule;
import com.msun.csm.model.param.ProjectCommonFileVsRuleParam;

public interface ProjectCommonFileVsRuleService {

    int addProjectCommonFile(ProjectCommonFileVsRule record);

    ProjectCommonFileVsRule selectByPrimaryKey(Long projectCommonFileId);

    int updateByPrimaryKeySelective(ProjectCommonFileVsRule record);

    ProjectCommonFileVsRule selectByParam(ProjectCommonFileVsRuleParam projectCommonFileId);

    int updateProjectFileIds(Long projectCommonFileId, String projectFileIds);

    int updateUseFlag(Long projectCommonFileId, Boolean useFlag);

    /**
     * 更新检测结果及检测结果说明
     *
     * @param projectCommonFileId 主键
     * @param checkResult         检测结果：1-未检测；2-检测通过；3-检测不通过
     * @param checkResultText     检测结果说明
     */
    int updateCheckInfoById(Long projectCommonFileId, Integer checkResult, String checkResultText);

}
