package com.msun.csm.service.projtool;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.port.HospitalApi;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlDTO;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlSwitchDTO;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlSysCode;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.rule.CustomerLimitProductEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigCustomFormLimit;
import com.msun.csm.dao.entity.config.ConfigCustomLimit;
import com.msun.csm.dao.entity.config.ConfigOnlineStep;
import com.msun.csm.dao.entity.oldimsp.OldUserReport;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOnlineStep;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjUpdateDataLog;
import com.msun.csm.dao.entity.report.ReportCustomInfo;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.config.ConfigCustomFormLimitMapper;
import com.msun.csm.dao.mapper.config.ConfigCustomLimitMapper;
import com.msun.csm.dao.mapper.config.ConfigOnlineStepMapper;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.oldimsp.OldUserReportMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.dao.mapper.proj.ProjUpdateDataLogMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ReportCustomInfoMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.req.projtool.ProjProjectInfoUpdateReq;
import com.msun.csm.model.req.projtool.ProjToolConfigDataReq;
import com.msun.csm.model.req.projtool.ProjToolCustomLimitDataReq;
import com.msun.csm.model.req.projtool.ProjToolCustomLimitUpdateDataReq;
import com.msun.csm.model.req.projtool.ProjToolUpdateReq;
import com.msun.csm.model.req.projtool.SysConfigReq;
import com.msun.csm.model.resp.projtool.OldSysUserResp;
import com.msun.csm.model.resp.projtool.ProjToolCustomLimitDataResp;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【proj_survey_form(表单主表)】的数据库操作Service实现
 * @createDate 2024-09-14 15:15:49
 */
@Service
@Slf4j
public class ProjectToolUpdateServiceImpl implements ProjectToolUpdateService {

    @Qualifier("com.msun.core.component.implementation.api.port.HospitalApi")
    @Resource
    private HospitalApi hospitalApi;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Lazy
    @Resource
    private SysUserMapper userMapper;

    @Lazy
    @Resource
    private OldUserReportMapper oldUserReportMapper;

    @Lazy
    @Resource
    private ProjUpdateDataLogMapper projUpdateDataLogMapper;

    @Lazy
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Lazy
    @Resource
    private ReportCustomInfoMapper reportCustomInfoMapper;

    @Lazy
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Lazy
    @Resource
    private ProjOnlineStepMapper projOnlineStepMapper;

    @Lazy
    @Resource
    private ConfigOnlineStepMapper configOnlineStepMapper;

    @Lazy
    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Lazy
    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;

    @Lazy
    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;

    @Lazy
    @Resource
    private SysDeptMapper sysDeptMapper;

    @Lazy
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Lazy
    @Resource
    private SysConfigMapper sysConfigMapper;

    @Lazy
    @Resource
    private ConfigCustomFormLimitMapper configCustomFormLimitMapper;

    @Lazy
    @Resource
    private ConfigCustomLimitMapper configCustomLimitMapper;

    @Lazy
    @Resource
    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;

    @Lazy
    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Lazy
    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;



    /**
     * 修改账户信息
     *
     * @param libFormReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result updateAccountData(ProjToolUpdateReq libFormReq) {
        if (libFormReq.getOldUserAccount() == null) {
            return Result.fail("请输入老账号");
        }
        if (libFormReq.getNewUserAccount() == null) {
            return Result.fail("请输入新账号");
        }
        // 0 查看是否已操作过
        List<ProjUpdateDataLog> updateDataLogList = projUpdateDataLogMapper.selectList(new QueryWrapper<ProjUpdateDataLog>().eq("old_account", libFormReq.getOldUserAccount()).eq("new_account", libFormReq.getNewUserAccount()));
        if (updateDataLogList != null && updateDataLogList.size() > 0) {
            return Result.fail("该账号已操作过，请勿重复操作");
        }
        // 1.新平台新老账号交换sysuserid
        SysUserDTO dto = new SysUserDTO();
        dto.setAccount(libFormReq.getOldUserAccount());
        List<SysUser> oldUserList = userMapper.selectAllUser(dto);
        dto.setAccount(libFormReq.getNewUserAccount());
        List<SysUser> newUserList = userMapper.selectAllUser(dto);
        if (oldUserList == null || (oldUserList != null && oldUserList.size() != 1)) {
            return Result.fail("老账号不存在或存在多条数据");
        }
        if (newUserList == null || (newUserList != null && newUserList.size() != 1)) {
            return Result.fail("新账号不存在或存在多条数据");
        }
        SysUser oldUserUpdate = oldUserList.get(0);
        // 老账号
        Long oldId = oldUserUpdate.getSysUserId();
        Long newId = -oldId;
        // 将老账号变为 -老账号
        userMapper.updateDataById(oldId, newId);
        SysUser newUserUpdate = newUserList.get(0);
        Long newOldId = newUserUpdate.getSysUserId();
        newId = oldId;
        // 将新账号变为老账号
        userMapper.updateDataById(newOldId, newId);
        // 将老账号变为新账号
        userMapper.updateDataById(-oldId, newOldId);
        try {
            // 更新项目表的项目经理账号
            projectInfoMapper.updateDataById(oldId, newOldId, newUserUpdate.getDeptId());
            // 查询人员表数据
            List<ProjProjectMember> listMemberData = projectInfoMapper.selectDataListByIds(oldId, newOldId);
            if (listMemberData != null && listMemberData.size() > 0) {
//                Map<Long, Long> map = listMemberData.stream().collect(Collectors.toMap(ProjProjectMember::getProjectInfoId, ProjProjectMember::getProjectMemberRoleId));
                Map<Long, Long> map = new HashMap<>();
                List<Long> listProjectId = new ArrayList<>();
                for (ProjProjectMember projProjectMember : listMemberData) {
                   if (!listProjectId.contains(projProjectMember.getProjectInfoId())) {
                       listProjectId.add(projProjectMember.getProjectInfoId());
                       map.put(projProjectMember.getProjectInfoId(), projProjectMember.getProjectMemberRoleId());
                   } else {
                       if (map.get(projProjectMember.getProjectInfoId()) < projProjectMember.getProjectMemberRoleId()) {
                           map.put(projProjectMember.getProjectInfoId(), projProjectMember.getProjectMemberRoleId());
                       }
                   }

                }

                projProjectMemberMapper.deleteByList(listMemberData);
                SysDept sysDept = sysDeptMapper.selectYunYingId(newUserUpdate.getDeptId());
                if (listProjectId != null && listProjectId.size() > 0) {
                    for (Long projectId : listProjectId) {
                        ProjProjectMember projProjectMember = new ProjProjectMember();
                        projProjectMember.setProjectMemberInfoId(SnowFlakeUtil.getId());
                        projProjectMember.setProjectInfoId(projectId);
                        projProjectMember.setProjectMemberName(newUserUpdate.getUserName());
                        projProjectMember.setProjectMemberId(newId);
                        projProjectMember.setPhone(newUserUpdate.getPhone());
                        projProjectMember.setProjectTeamId(newUserUpdate.getDeptId());
                        if (sysDept != null) {
                            projProjectMember.setProjectTeamName(sysDept.getDeptName());
                        } else {
                            projProjectMember.setProjectTeamName(" ");
                        }
                        projProjectMember.setProjectMemberRoleId(map.get(projectId));
                        projProjectMember.setIsDeleted(0);
                        projProjectMember.setCreaterId(-1L);
                        projProjectMember.setCreateTime(new Date());
                        projProjectMember.setUpdaterId(-1L);
                        projProjectMember.setUpdateTime(new Date());
                        projProjectMemberMapper.insert(projProjectMember);
                    }
                }
            }

        } catch (Exception e) {
            log.error("修改项目表数据失败", e);
        }
        // 2.老平台新老账号交换userid
        // 逻辑： 找出老账号在项目表中的数据， 将新账号的userid赋值给老账号， 将老账号的userid赋值给新账号
        try {
            OldSysUserResp oldSysUserResp = oldUserReportMapper.getUserByAccount(libFormReq.getOldUserAccount());
            OldSysUserResp newSysUserResp = oldUserReportMapper.getUserByAccount(libFormReq.getNewUserAccount());
            if (oldSysUserResp != null) {
                Integer updateId = oldSysUserResp.getUserId();
                oldSysUserResp.setNewId(-updateId);
                oldUserReportMapper.updatePlatformUserByIdData(oldSysUserResp);
                if (newSysUserResp != null) {
                    newSysUserResp.setNewId(updateId);
                    Integer oldIdOld = newSysUserResp.getUserId();
                    oldUserReportMapper.updatePlatformUserByIdData(newSysUserResp);
                    if (oldIdOld != null) {
                        OldSysUserResp oldSysUserZi = oldSysUserResp;
                        oldSysUserZi.setNewId(oldIdOld);
                        oldSysUserZi.setUserId(-updateId);
                        oldUserReportMapper.updatePlatformUserByIdData(oldSysUserZi);
                    }
                }
            }
        } catch (Exception e) {
            log.error("修改userid失败", e);
        }

        // 3. 老平台账号增加报表权限。
        // 将老账号的权限赋值给新账号
        try {
            OldSysUserResp newSysUserResp = oldUserReportMapper.getUserByAccount(libFormReq.getNewUserAccount());
            List<OldUserReport> oldUserReportList = oldUserReportMapper.getReportLimitByAccount(libFormReq);
            String level = "";
            if (oldUserReportList != null && oldUserReportList.size() > 0) {
                oldUserReportMapper.deleteByList(oldUserReportList);
                for (OldUserReport oldUserReport : oldUserReportList) {
                    if (oldUserReport.getLevel() != null && !"".equals(oldUserReport.getLevel())) {
                        level = oldUserReport.getLevel();
                    }
                }
            }
            OldUserReport oldUserReport = new OldUserReport();
            oldUserReport.setUserId(Long.valueOf(newSysUserResp.getUserId()));
            oldUserReport.setAccount(libFormReq.getNewUserAccount());
            oldUserReport.setLevel(level);
            oldUserReportMapper.insert(oldUserReport);
        } catch (Exception e) {
            log.error("修改报表权限失败", e);
        }

        // 4. 其他表数据处理逻辑
        try {
            updateOtherTableData(newOldId, oldId);
        } catch (Exception e) {
            log.error("其他表数据处理失败", e);
        }
        // 5.存储日志
        insertLogData(libFormReq);
        return Result.success();
    }

    /**
     * 其他表数据处理
     * 将新账号产生的业务数据改为老账号
     *
     * @param newOldId
     * @param oldId
     */
    private void updateOtherTableData(Long newOldId, Long oldId) {
        // 修改报表的负责人
        projSurveyReportMapper.udpateReprotByOldId(newOldId, oldId);
        projSurveyReportMapper.udpateReprotMakeByOldId(newOldId, oldId);
        // 修改报表调研负责人与制作负责人
        projSurveyFormMapper.udpateReprotByOldId(newOldId, oldId);
        projSurveyFormMapper.udpateReprotMakeByOldId(newOldId, oldId);
        // 准备阶段负责人
        projSurveyReportMapper.updateProductBacklogByOldId(newOldId, oldId);
        projSurveyReportMapper.updateProductBacklogDetailByOldId(newOldId, oldId);
        // fm_user_form_data 字段sys_user_id
        projSurveyReportMapper.updateFmUserFormDataByOldId(newOldId, oldId);
        // 调研计划	proj_survey_plan 字段survey_user_id
        projSurveyReportMapper.updateSurveyPlanByOldId(newOldId, oldId);
        // 三方接口：创建人、责任人	proj_third_interface 字段：creater_id、dir_person_id
        projSurveyReportMapper.updateThirdInterfaceByOldId(newOldId, oldId);
        projSurveyReportMapper.updateThirdInterfaceDirPersonByOldId(newOldId, oldId);

    }

    /**
     * 分页查询日志
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ProjUpdateDataLog>> selectUpdateLogByPage(ProjToolUpdateReq dto) {
        if (dto.getOperateType() == null || dto.getOperateType() == 0) {
            dto.setOperateType(1);
        }
        List<ProjUpdateDataLog> relatives = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> projUpdateDataLogMapper.selectByCondition(dto));
        PageInfo<ProjUpdateDataLog> pageInfo = new PageInfo<>(relatives);
        return Result.success(pageInfo);
    }

    /**
     * @param libFormReq
     * @return
     */
    @Override
    public Result updateProjectConfigInfo(ProjProjectInfoUpdateReq libFormReq) {
        Result result = null;
        if (libFormReq.getOperateType() != null) {
            if (ObjectUtil.isEmpty(libFormReq.getCustomerInfoId())) {
                result = Result.fail("客户id不能为空");
            } else if (ObjectUtil.isEmpty(libFormReq.getProjectInfoId())) {
                result = Result.fail("项目id不能为空");
            } else {
                // 1 更新项目信息 2 里程碑新增 3里程碑作废  4上线步骤新增 5上线步骤作废 6上线步骤完成 7 项目首期/非首期修改 8项目调研管控时间修改
                switch (libFormReq.getOperateType()) {
                    case 1:
                        // 更新项目信息：已完成 （项目类型，项目初始化，上线步骤初始化 不能同事为空）
                        if (ObjectUtil.isEmpty(libFormReq.getProjectType()) && ObjectUtil.isEmpty(libFormReq.getMemberInitFlag()) && ObjectUtil.isEmpty(libFormReq.getOnlineStepFlag())) {
                            result = Result.fail("项目类型，项目初始化，上线步骤初始化 不能同时为空");
                        }
                        break;
                    case 2:
                        if (ObjectUtil.isEmpty(libFormReq.getMilestoneNodeId())) {
                            result = Result.fail("里程碑节点不能为空");
                        }
                        break;
                    case 3:
                        if (ObjectUtil.isEmpty(libFormReq.getMilestoneNodeId())) {
                            result = Result.fail("里程碑节点不能为空");
                        }
                        break;
                    case 4:
                        if (ObjectUtil.isEmpty(libFormReq.getConfigOnlineStepId())) {
                            result = Result.fail("上线节点不能为空");
                        }
                        break;
                    case 5:
                        if (ObjectUtil.isEmpty(libFormReq.getConfigOnlineStepId())) {
                            result = Result.fail("上线节点不能为空");
                        }
                        break;
                    case 6:
                        if (ObjectUtil.isEmpty(libFormReq.getConfigOnlineStepId())) {
                            result = Result.fail("上线节点不能为空");
                        }
                        break;
                    default:
                        break;
                }
                if (ObjectUtil.isNull(result)) {
                    switch (libFormReq.getOperateType()) {
                        case 1:
                            // 更新项目信息
                            result = updateProjectInfo(libFormReq);
                            break;
                        case 2:
                            // 里程碑节点新增
                            result = updateMilestoneNode(libFormReq, true);
                            break;
                        case 3:
                            // 里程碑节点作废
                            result = updateMilestoneNode(libFormReq, false);
                            break;
                        case 4:
                            // 增加上线部署
                            result = updateOnlineStep(libFormReq, true);
                            break;
                        case 5:
                            // 作废上线步骤
                            result = updateOnlineStep(libFormReq, false);
                            break;
                        case 6:
                            // 完成、未完成上线步骤
                            result = updateOnlineStepFinish(libFormReq);
                            break;
                        case 7:
                            // 更新项目首期/非首期标识
                            result = updateProjectTimeOrHisFlag(libFormReq, 7);
                            break;
                        case 8:
                            // 8项目调研管控时间修改
                            result = updateProjectTimeOrHisFlag(libFormReq, 8);
                            break;
                        default:
                            result = Result.fail("更新类型不存在");
                            break;
                    }
                }
            }
        } else {
            result = Result.fail("更新类型不能为空");
        }
        return result;
    }

    /**
     * 完成/取消完成上线步骤
     *
     * @param libFormReq
     * @return
     */
    private Result updateOnlineStepFinish(ProjProjectInfoUpdateReq libFormReq) {
        List<ProjOnlineStep> projOnlineStepList = projOnlineStepMapper.selectList(new QueryWrapper<ProjOnlineStep>().eq("project_info_id", libFormReq.getProjectInfoId()).in("config_online_step_id", libFormReq.getConfigOnlineStepId()).eq("is_deleted", 0));
        String operateName = null;
        if (projOnlineStepList != null && projOnlineStepList.size() > 0) {
            for (ProjOnlineStep projOnlineStep : projOnlineStepList) {
                ConfigOnlineStep configOnlineSteps = configOnlineStepMapper.selectById(projOnlineStep.getConfigOnlineStepId());
                projOnlineStep.setStatus(projOnlineStep.getStatus() == 1 ? 0 : 1);
                projOnlineStepMapper.updateById(projOnlineStep);
                operateName += "【" + configOnlineSteps.getOnlineStepName() + "】";
            }
        }
        // 记录日志
        insertProjectLogData(libFormReq, operateName);
        return Result.success();
    }

    /**
     * 更新项目管控时间或首期非首期项目
     *
     * @param libFormReq
     * @param i
     * @return
     */
    private Result updateProjectTimeOrHisFlag(ProjProjectInfoUpdateReq libFormReq, int i) {
        ProjProjectInfo info = projectInfoMapper.selectById(libFormReq.getProjectInfoId());
        if (ObjectUtil.isNotEmpty(info)) {
            if (i == 8) {
                info.setControlTime(libFormReq.getBeginTime());
            } else {
                info.setHisFlag(info.getHisFlag() == 1 ? 0 : 1);
            }
            projectInfoMapper.updateById(info);
        }
        // 记录日志
        insertProjectLogData(libFormReq, null);
        return Result.success();
    }

    /**
     * 查询数据
     *
     * @param libFormReq
     * @return
     */
    @Override
    public Result<List<SysConfig>> selectConfigList(ProjToolConfigDataReq libFormReq) {
        List<String> configCodes = new ArrayList<>();
        configCodes.add("customLimit");
        configCodes.add("customLimitDay");
        List<SysConfig> limitCustomerSysConfig = sysConfigMapper.selectConfigByCodes(configCodes);
        return Result.success(limitCustomerSysConfig);
    }

    /**
     * 配置信息修改-报表限制
     *
     * @param sysConfigReq
     * @return
     */
    @Override
    public Result updateSysConfigInfo(SysConfigReq sysConfigReq) {
        if (ObjectUtil.isEmpty(sysConfigReq.getId())) {
            return Result.fail("id不能为空");
        }
        if (ObjectUtil.isEmpty(sysConfigReq.getConfigValue())) {
            return Result.fail("配置值不能为空");
        }
        SysConfig sysConfig = sysConfigMapper.selectByPrimaryKey(sysConfigReq.getId());
        sysConfig.setConfigValue(sysConfigReq.getConfigValue());
        sysConfigMapper.updateById(sysConfig);
        return Result.success();
    }

    /**
     * 分页查询客户限制配置信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ProjToolCustomLimitDataResp>> selectCustomerLimitListByPage(ProjToolCustomLimitDataReq dto) {
        List<ProjToolCustomLimitDataResp> relatives = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> configCustomFormLimitMapper.selectByCondition(dto));
        PageInfo<ProjToolCustomLimitDataResp> pageInfo = new PageInfo<>(relatives);
        return Result.success(pageInfo);
    }

    @Override
    public void apiSyncCustomFormCtrlTask(Long customInfoId, Long hospitalInfoId) {
        List<ConfigCustomFormLimit> list = configCustomFormLimitMapper.selectNoDealListByInfoId(customInfoId, hospitalInfoId);
        syncCustomFormCtrlTask(list);
    }

    @Override
    public void syncCustomFormCtrlTask(List<ConfigCustomFormLimit> formLimitList) {
        if (CollUtil.isEmpty(formLimitList)) {
            throw new CustomException("没有需要同步的表单限制");
        }
        Set<Long> errIds = new HashSet<>();
        //分组医院
        Map<Long, List<ConfigCustomFormLimit>> hospitalMap = formLimitList.stream()
                .collect(Collectors.groupingBy(ConfigCustomFormLimit::getHospitalInfoId));
        hospitalMap.forEach((hospitalId, formLimits) -> {
            //查询医院
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(hospitalId);
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            //分组：是否开通
            Map<Integer, List<ConfigCustomFormLimit>> openOrCloseMap = formLimits.stream()
                    .collect(Collectors.groupingBy(ConfigCustomFormLimit::getSwitchFlag));
            openOrCloseMap.forEach((flag, list) -> {
                log.info("医院id:{}, 开关状态:{}", hospitalId, flag);
                CustFormCtrlSwitchDTO switchDTO = getSwitchDTO(flag, list, hospitalInfo);
                log.info("调用接口开启 or 关闭报表操作限制 参数:{}", JSONObject.toJSONString(switchDTO));
                ResponseResult responseResult = hospitalApi.switchCustForm(switchDTO);
                log.info("调用接口开启 or 关闭报表操作限制 返回结果:{}", JSONObject.toJSONString(responseResult));
                //分组：验收收时间
                Map<Date, List<ConfigCustomFormLimit>> accepTimeMap = list.stream()
                        .collect(Collectors.groupingBy(ConfigCustomFormLimit::getAcceptTime));
                accepTimeMap.forEach((acceptTime, accetTimeList) -> {
                    //分组：延期时间
                    Map<Integer, List<ConfigCustomFormLimit>> delayDaysMap = accetTimeList.stream()
                            .collect(Collectors.groupingBy(ConfigCustomFormLimit::getDaysAfterCheck));
                    //TODO 报错跳过，更新状态的时候需要剔除报错的
                    delayDaysMap.forEach((days, delayList) -> {
                        try {
                            log.info("医院id:{}, 验收时间:{}, 延期天数:{}", hospitalId, acceptTime, days);
                            CustFormCtrlSwitchDTO ctrlSwitchDTO = getSwitchDTO(flag, delayList, hospitalInfo);
                            CustFormCtrlDTO ctrlDTO = new CustFormCtrlDTO();
                            BeanUtil.copyProperties(ctrlSwitchDTO, ctrlDTO);
                            ctrlDTO.setCheckAcceptTime(DateUtil.formatDateTime(acceptTime));
                            ctrlDTO.setDaysAfterCheckClose(days);
                            log.info("调用接口设置报表操作限制 参数:{}", JSONObject.toJSONString(ctrlDTO));
                            ResponseResult response = hospitalApi.saveCustFormCtrl(ctrlDTO);
                            log.info("调用接口设置报表操作限制 返回结果:{}", JSONObject.toJSONString(response));
                            if (Boolean.FALSE.equals(response.isSuccess())) {
                                throw new RuntimeException(response.getMessage());
                            }
                        } catch (Throwable e) {
                            log.error("调用接口设置报表操作限制错误：", e);
                            errIds.addAll(delayList.stream().map(ConfigCustomFormLimit::getCustomFormLimitId).collect(Collectors.toSet()));
                        }

                    });
                });
            });
            domainMap.clear();
        });
        //更新处理标识
        List<Long> ids = formLimitList.stream().map(ConfigCustomFormLimit::getCustomFormLimitId).collect(Collectors.toList());
        ids.removeAll(errIds);
        if (CollUtil.isNotEmpty(ids)) {
            configCustomFormLimitMapper.updateDealFlag(ids, 1);
        }
        if (CollUtil.isNotEmpty(errIds)) {
            configCustomFormLimitMapper.updateDealFlag(errIds, 2);
        }
    }

    private static @NotNull
    CustFormCtrlSwitchDTO getSwitchDTO(Integer flag,
                                       List<ConfigCustomFormLimit> list,
                                       ProjHospitalInfo hospitalInfo) {
        CustFormCtrlSwitchDTO switchDTO = new CustFormCtrlSwitchDTO();
        List<CustFormCtrlSysCode> sysList = new ArrayList<>();
        Set<String> codeSet = list.stream().map(a -> a.getMsunHealthModuleCode())
                .collect(Collectors.toSet());
        CustFormCtrlSysCode sysCode = new CustFormCtrlSysCode();
        sysCode.setHospitalId(hospitalInfo.getCloudHospitalId().intValue());
        sysCode.setSystemCodeList(codeSet.toArray(new String[codeSet.size()]));
        sysList.add(sysCode);
        switchDTO.setSwitchFlag(flag);
        switchDTO.setSysList(sysList);
        switchDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        switchDTO.setHisOrgId(hospitalInfo.getOrgId());
        return switchDTO;
    }

    /**
     * 客服工作限制配置: 修改
     *
     * @param req
     * @return
     */
    @Override
    public Result updateCustomWorkLimitConfig(ProjToolCustomLimitUpdateDataReq req) {
        if (ObjectUtil.isEmpty(req.getCustomInfoId())) {
            return Result.fail("客户id不能为空");
        }
        // 更新 客户开启关闭状态
        if (ObjectUtil.isNotEmpty(req.getCustomSwitchFlag())) {
            return updateCustomLimit(req);
        }
        // 三方接口限制
        if (ObjectUtil.isNotEmpty(req.getInterfacelimitFlag())) {
            return updateDetailLimit(req, "interfacelimit", req.getInterfacelimitFlag());
        }
        // 统计报表限制
        if (ObjectUtil.isNotEmpty(req.getReportlimitFlag())) {
            return updateDetailLimit(req, "reportlimit", req.getReportlimitFlag());
        }
        //  打印报表查询
        if (ObjectUtil.isNotEmpty(req.getBaseprintFlag())) {
            return updateDetailLimit(req, "baseprint", req.getBaseprintFlag());
        }
        // 统计查询
        if (ObjectUtil.isNotEmpty(req.getReportwebFlag())) {
            return updateDetailLimit(req, "reportweb", req.getReportwebFlag());
        }
        // 急诊表单
        if (ObjectUtil.isNotEmpty(req.getEmisFormFlag())) {
            return updateDetailLimit(req, "emis", req.getEmisFormFlag());
        }
        // 重症
        if (ObjectUtil.isNotEmpty(req.getIcuFormFlag())) {
            return updateDetailLimit(req, "icuisnew", req.getIcuFormFlag());
        }
        // 手麻
        if (ObjectUtil.isNotEmpty(req.getAimsFormFlag())) {
            return updateDetailLimit(req, "aims", req.getAimsFormFlag());
        }
        // 云护理
        if (ObjectUtil.isNotEmpty(req.getHulidanyuanFlag())) {
            return updateDetailLimit(req, "hulidanyuan", req.getHulidanyuanFlag());
        }
        return Result.success();
    }

    /**
     * 客服工作限制配置: 查询
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result changeCustomTypeModel(Long projectInfoId) {
        projCustomInfoMapper.updateCustomTypeModel(projectInfoId);
        return Result.success();
    }

    /**
     * 更新明细开启关闭状态
     *
     * @param req
     * @param code
     * @param flag
     * @return
     */
    private Result updateDetailLimit(ProjToolCustomLimitUpdateDataReq req, String code, Integer flag) {
        if (ObjectUtil.isEmpty(req.getHospitalInfoId())) {
            return Result.fail("医院id不能为空");
        }
        Integer daysAfterCheck = 30;
        SysConfig daysAfterCheckSysConfig = sysConfigMapper.selectConfigByName("customLimitDay");
        if (daysAfterCheckSysConfig != null) {
            try {
                daysAfterCheck = Integer.parseInt(daysAfterCheckSysConfig.getConfigValue());
            } catch (Exception e) {
                log.error("获取配置表数据异常，配置编码：customLimitDay，配置值：{}", daysAfterCheckSysConfig.getConfigValue());
            }
        }
        List<ConfigCustomFormLimit> list = configCustomFormLimitMapper.selectDataByHospitalInfoIdAndCode(req.getHospitalInfoId(), code);
        if (list != null && list.size() > 0) {
            for (ConfigCustomFormLimit configCustomFormLimit : list) {
                configCustomFormLimit.setSwitchFlag(flag);
                configCustomFormLimit.setUpdateTime(new Date());
                configCustomFormLimit.setHasDeal(0);
                configCustomFormLimit.setDaysAfterCheck(daysAfterCheck);
                configCustomFormLimit.setAcceptTime(new Date(System.currentTimeMillis() - 86400000L * 50));
                configCustomFormLimitMapper.updateByPrimaryKey(configCustomFormLimit);
            }
        } else {
            ProjProjectInfo projectInfo = null;
            List<ProjProjectInfo> projectInfoList = projectInfoMapper.selectProjectListByHospitalId(req.getHospitalInfoId());
            if (projectInfoList != null && projectInfoList.size() > 0) {
                for (ProjProjectInfo projProjectInfo : projectInfoList) {
                    if (projProjectInfo.getHisFlag() == 1) {
                        projectInfo = projProjectInfo;
                        break;
                    }
                }
                if (projectInfo == null) {
                    projectInfo = projectInfoList.get(0);
                }
            }
            CustomerLimitProductEnum valueList = CustomerLimitProductEnum.getEnumByCode(code);
            ConfigCustomFormLimit limit = new ConfigCustomFormLimit();
            Date currentTime = new Date();
            limit.setCustomFormLimitId(SnowFlakeUtil.getId());
            limit.setCustomInfoId(req.getCustomInfoId());
            limit.setHospitalInfoId(req.getHospitalInfoId());
            limit.setOrderProductId(valueList.getYyProductId());
            limit.setMsunHealthModuleCode(code);
            limit.setSwitchFlag(flag);
            limit.setDaysAfterCheck(daysAfterCheck);
            limit.setIsDeleted(0);
            if (projectInfo != null) {
                limit.setCreaterId(projectInfo.getProjectLeaderId());
                limit.setUpdaterId(projectInfo.getProjectLeaderId());
                //验收时间
                limit.setAcceptTime(projectInfo.getAcceptTime());
            } else {
                limit.setCreaterId(-1L);
                limit.setUpdaterId(-1L);
                //验收时间
                limit.setAcceptTime(new Date());
            }
            if (limit.getAcceptTime() == null) {
                limit.setAcceptTime(new Date(System.currentTimeMillis() - 86400000L * 50));
            }
            limit.setCreateTime(currentTime);
            limit.setUpdateTime(currentTime);
            //定时任务是否处理标识
            limit.setHasDeal(0);
            configCustomFormLimitMapper.insert(limit);
        }
        return Result.success();
    }

    private Result updateCustomLimit(ProjToolCustomLimitUpdateDataReq req) {
        List<ConfigCustomLimit> list = configCustomLimitMapper.selectList(new QueryWrapper<ConfigCustomLimit>().eq("custom_info_id", req.getCustomInfoId()));
        if (list != null && list.size() > 0) {
            for (ConfigCustomLimit configCustomLimit : list) {
                configCustomLimit.setCreateTime(new Date(System.currentTimeMillis() - 86400000L * 50));
                configCustomLimit.setSwitchFlag(req.getCustomSwitchFlag());
                configCustomLimitMapper.updateById(configCustomLimit);
            }
        } else {
            ConfigCustomLimit configCustomLimit = new ConfigCustomLimit();
            configCustomLimit.setCustomLimitId(SnowFlakeUtil.getId());
            configCustomLimit.setCustomInfoId(req.getCustomInfoId());
            configCustomLimit.setSwitchFlag(req.getCustomSwitchFlag());
            configCustomLimit.setCreateTime(new Date(System.currentTimeMillis() - 86400000L * 50));
            configCustomLimitMapper.insert(configCustomLimit);
        }
        List<ConfigCustomFormLimit> listDetail = configCustomFormLimitMapper.selectByCustomerId(req.getCustomInfoId());
        if (listDetail != null && listDetail.size() > 0) {
            for (ConfigCustomFormLimit configCustomFormLimit : listDetail) {
                configCustomFormLimit.setUpdateTime(new Date());
                configCustomFormLimit.setHasDeal(0);
                configCustomFormLimit.setAcceptTime(new Date(System.currentTimeMillis() - 86400000L * 50));
                configCustomFormLimitMapper.updateByPrimaryKey(configCustomFormLimit);
            }
        }
        return Result.success();
    }

    /**
     * 更新上线步骤
     *
     * @param libFormReq
     * @param b
     * @return
     */
    private Result updateOnlineStep(ProjProjectInfoUpdateReq libFormReq, boolean b) {
        String operateName = "";
        if (b) {
            List<Long> configOnlineStepIdList = new ArrayList<>();
            List<ProjOnlineStep> projOnlineStepList = projOnlineStepMapper.selectList(new QueryWrapper<ProjOnlineStep>().eq("project_info_id", libFormReq.getProjectInfoId()).in("config_online_step_id", libFormReq.getConfigOnlineStepId()).eq("is_deleted", 0));
            List<Long> configOnlineStepId = libFormReq.getConfigOnlineStepId();
            if (projOnlineStepList != null && projOnlineStepList.size() > 0) {
                List<Long> pidList = projOnlineStepList.stream().map(ProjOnlineStep::getConfigOnlineStepId).collect(Collectors.toList());
                for (Long id : configOnlineStepId) {
                    if (!pidList.contains(id) && !configOnlineStepIdList.contains(id)) {
                        configOnlineStepIdList.add(id);
                    }
                }
            } else {
                for (Long id : configOnlineStepId) {
                    if (!configOnlineStepIdList.contains(id)) {
                        configOnlineStepIdList.add(id);
                    }
                }
            }
            if (configOnlineStepIdList != null && configOnlineStepIdList.size() > 0) {
                for (Long configOnlineStepId1 : configOnlineStepIdList) {
                    ConfigOnlineStep configOnlineStep = configOnlineStepMapper.selectById(configOnlineStepId1);
                    ProjOnlineStep projOnlineStep = new ProjOnlineStep();
                    projOnlineStep.setProjOnlineStepId(SnowFlakeUtil.getId());
                    projOnlineStep.setProjectInfoId(libFormReq.getProjectInfoId());
                    projOnlineStep.setConfigOnlineStepId(configOnlineStepId1);
                    projOnlineStep.setStatus(0);
                    projOnlineStep.setCustomInfoId(libFormReq.getCustomerInfoId());
                    projOnlineStep.setPid(configOnlineStep.getPid());
                    operateName += "【" + configOnlineStep.getOnlineStepName() + "】";
                    projOnlineStepMapper.insert(projOnlineStep);
                }
            } else {
                return Result.fail("上线步骤已存在,无需新增");
            }
        } else {
            List<ProjOnlineStep> projOnlineStepList = projOnlineStepMapper.selectList(new QueryWrapper<ProjOnlineStep>().eq("project_info_id", libFormReq.getProjectInfoId()).in("config_online_step_id", libFormReq.getConfigOnlineStepId()).eq("is_deleted", 0));
            if (projOnlineStepList != null && projOnlineStepList.size() > 0) {
                for (ProjOnlineStep projOnlineStep : projOnlineStepList) {
                    ConfigOnlineStep configOnlineSteps = configOnlineStepMapper.selectById(projOnlineStep.getConfigOnlineStepId());
                    projOnlineStep.setIsDeleted(1);
                    operateName += "【" + configOnlineSteps.getOnlineStepName() + "】";
                    projOnlineStepMapper.deleteById(projOnlineStep);
                }
            } else {
                return Result.fail("上线步骤不存在,无需作废");
            }
        }
        // 记录日志
        insertProjectLogData(libFormReq, operateName);
        return Result.success();
    }

    /**
     * 里程碑节点新增/作废
     *
     * @param libFormReq
     * @param b
     * @return
     */
    private Result updateMilestoneNode(ProjProjectInfoUpdateReq libFormReq, boolean b) {
        String operateName = "";
        List<ProjMilestoneInfo> projMilestoneInfo = projMilestoneInfoMapper.findByProjectInfoId(libFormReq.getProjectInfoId());
        List<Long> milestoneNodeIdParamer = libFormReq.getMilestoneNodeId();
        List<Long> milestoneNodeIdInsert = new ArrayList<>();
        List<Long> milestoneNodeIdUpdate = new ArrayList<>();
        if (projMilestoneInfo != null && projMilestoneInfo.size() > 0) {
            List<Long> projMilestoneNodeIds = projMilestoneInfo.stream().map(ProjMilestoneInfo::getMilestoneNodeId).collect(Collectors.toList());
            for (Long milestoneNodeId : milestoneNodeIdParamer) {
                if (!projMilestoneNodeIds.contains(milestoneNodeId) && !milestoneNodeIdInsert.contains(milestoneNodeId)) {
                    milestoneNodeIdInsert.add(milestoneNodeId);
                }
            }
            for (ProjMilestoneInfo milestoneInfo : projMilestoneInfo) {
                // 作废数据
                Long id = milestoneInfo.getMilestoneNodeId();
                if (milestoneNodeIdParamer.contains(id) && !milestoneNodeIdUpdate.contains(id)) {
                    milestoneNodeIdUpdate.add(id);
                }
            }
        }
        if (b) {
            if (milestoneNodeIdInsert.size() > 0) {
                for (Long milestoneNodeId : milestoneNodeIdInsert) {
                    libFormReq.setProjmilestoneNodeId(milestoneNodeId);
                    ProjMilestoneInfo milestoneInfo = projMilestoneInfoMapper.getProjMilestoneInfoByProjectId(libFormReq);
                    operateName += "【" + milestoneInfo.getMilestoneNodeName() + "】";
                    milestoneInfo.setMilestoneInfoId(SnowFlakeUtil.getId());
                    milestoneInfo.setProjectInfoId(libFormReq.getProjectInfoId());
                    milestoneInfo.setMilestoneNodeId(milestoneNodeId);
                    projMilestoneInfoMapper.insert(milestoneInfo);
                }
            } else {
                return Result.fail("没有可新增的里程碑节点");
            }
        } else {
            if (projMilestoneInfo != null && projMilestoneInfo.size() > 0) {
                for (ProjMilestoneInfo milestoneInfo : projMilestoneInfo) {
                    if (milestoneNodeIdParamer.contains(milestoneInfo.getMilestoneNodeId())) {
                        operateName += "【" + milestoneInfo.getMilestoneNodeName() + "】";
                        milestoneInfo.setInvalidFlag(1);
                        projMilestoneInfoMapper.updateById(milestoneInfo);
                    }
                }
                if (milestoneNodeIdUpdate.size() == 0) {
                    return Result.fail("没有可作废的里程碑节点");
                }
            } else {
                return Result.fail("没有可作废的里程碑节点");
            }
        }
        // 记录日志
        insertProjectLogData(libFormReq, operateName);
        return Result.success();
    }

    private Result updateProjectInfo(ProjProjectInfoUpdateReq libFormReq) {
        boolean flag = false;
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(libFormReq.getProjectInfoId());
        if (libFormReq.getProjectType() != null && projectInfo.getProjectType() != libFormReq.getProjectType()) {
            Integer projectType = projectInfo.getProjectType();
            flag = true;
            projectInfo.setProjectType(libFormReq.getProjectType());
            List<Long> projectIds = Collections.singletonList(projectInfo.getProjectInfoId());
            List<TmpProjectNewVsOld> list = tmpProjectNewVsOldMapper.selectByNewProjectIds(projectIds);
            if (list != null && list.size() > 0) {
                for (TmpProjectNewVsOld tmpProjectNewVsOld : list) {
                    tmpProjectNewVsOld.setNewProjectSource(1);
                    tmpProjectNewVsOldMapper.updateById(tmpProjectNewVsOld);
                }
            }
            // 是否更新医院对照表类型标识
            if (libFormReq.getIsUpdateHospitalTypeFlag() != null && libFormReq.getIsUpdateHospitalTypeFlag() == 1) {
                List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectList(new QueryWrapper<ProjHospitalVsProjectType>().eq("custom_info_id", projectInfo.getCustomInfoId()).eq("project_type", projectType));
                if (hospitalVsProjectTypes != null && hospitalVsProjectTypes.size() > 0) {
                    for (ProjHospitalVsProjectType hospitalVsProjectType : hospitalVsProjectTypes) {
                        hospitalVsProjectType.setProjectType(libFormReq.getProjectType());
                        hospitalVsProjectTypeMapper.updateById(hospitalVsProjectType);
                    }
                }
            }
            // 如果是首期 更新客户统计表数据
            if (projectInfo.getHisFlag() == 1) {
                List<ReportCustomInfo> reportCustomInfos = reportCustomInfoMapper.selectList(new QueryWrapper<ReportCustomInfo>().eq("custom_info_id", projectInfo.getCustomInfoId()).eq("project_info_id", projectInfo.getProjectInfoId()).eq("is_deleted", 0));
                if (reportCustomInfos != null && reportCustomInfos.size() > 0) {
                    for (ReportCustomInfo reportCustomInfo : reportCustomInfos) {
                        reportCustomInfo.setCustomType(libFormReq.getProjectType());
                        reportCustomInfoMapper.updateById(reportCustomInfo);
                    }
                }
            }
            // 更新三方接口信息
            projThirdInterfaceMapper.updateByProjectInfoIdAndProjectType(libFormReq);

        }
        // 里程碑初始化
        if (libFormReq.getMemberInitFlag() != null && libFormReq.getMemberInitFlag() == 1) {
            flag = true;
            List<Long> projectIds = Collections.singletonList(projectInfo.getProjectInfoId());
            List<TmpProjectNewVsOld> list = tmpProjectNewVsOldMapper.selectByNewProjectIds(projectIds);
            if (list != null && list.size() > 0) {
                for (TmpProjectNewVsOld tmpProjectNewVsOld : list) {
                    tmpProjectNewVsOld.setNewProjectSource(1);
                    tmpProjectNewVsOldMapper.updateById(tmpProjectNewVsOld);
                }
            }
            projectInfo.setMemberInitFlag(0);
        }
        // 上线步骤初始化
        if (libFormReq.getOnlineStepFlag() != null && libFormReq.getOnlineStepFlag() == 1) {
            flag = true;
            projectInfo.setOnlineStepFlag(0);
        }
        if (flag) {
            projectInfoMapper.updateById(projectInfo);
            // 记录日志
            insertProjectLogData(libFormReq, null);
        } else {
            return Result.fail("无改动，无需处理");
        }
        return Result.success();
    }

    /**
     * 存储日志
     *
     * @param libFormReq
     * @param operateName
     */
    private void insertProjectLogData(ProjProjectInfoUpdateReq libFormReq, String operateName) {
        try {
            ProjUpdateDataLog projUpdateDataLog = new ProjUpdateDataLog();
            projUpdateDataLog.setOperateType(2);
            projUpdateDataLog.setUpdateDataLogId(SnowFlakeUtil.getId());
            projUpdateDataLog.setCustomInfoId(libFormReq.getCustomerInfoId());
            projUpdateDataLog.setProjectId(libFormReq.getProjectInfoId());
            ProjProjectInfo projectInfo = projectInfoMapper.selectById(libFormReq.getProjectInfoId());
            ProjCustomInfo customInfo = projCustomInfoMapper.selectById(libFormReq.getCustomerInfoId());
            projUpdateDataLog.setProjectName(projectInfo.getProjectName());
            projUpdateDataLog.setCustomName(customInfo.getCustomName());
            switch (libFormReq.getOperateType()) {
                case 1:
                    String title = "";
                    if (projectInfo.getProjectType() == libFormReq.getProjectType()) {
                        // 更新项目信息
                        projUpdateDataLog.setProjectType(libFormReq.getProjectType());
                        String projectTypeName = ProjectTypeEnums.getEnum(libFormReq.getProjectType()).getName();
                        projUpdateDataLog.setProjectTypeName(projectTypeName);
                        title += "修改项目类型为" + projectTypeName + ";";
                    }
                    if (libFormReq.getMemberInitFlag() != null && libFormReq.getMemberInitFlag() == 1) {
                        projUpdateDataLog.setMilestoneType(3);
                        projUpdateDataLog.setMilestoneTypeName("初始化");
                        title += "初始化里程碑;";
                    }
                    if (libFormReq.getOnlineStepFlag() != null && libFormReq.getOnlineStepFlag() == 1) {
                        projUpdateDataLog.setOnlineStepType(3);
                        projUpdateDataLog.setOnlineStepTypeName("初始化");
                        title += "初始化上线步骤;";
                    }
                    projUpdateDataLog.setOperateTitle(title);
                    break;
                case 2:
                    // 里程碑节点新增
                    projUpdateDataLog.setMilestoneType(1);
                    projUpdateDataLog.setMilestoneTypeName("新增");
                    projUpdateDataLog.setOperateTitle("新增里程碑节点【" + operateName + "】");
                    break;
                case 3:
                    // 里程碑节点作废
                    projUpdateDataLog.setMilestoneType(2);
                    projUpdateDataLog.setMilestoneTypeName("作废");
                    projUpdateDataLog.setOperateTitle("作废里程碑节点【" + operateName + "】");
                    break;
                case 4:
                    // 增加上线部署
                    projUpdateDataLog.setOnlineStepType(1);
                    projUpdateDataLog.setOnlineStepTypeName("新增");
                    projUpdateDataLog.setOperateTitle("新增上线步骤节点【" + operateName + "】");
                    break;
                case 5:
                    // 作废上线步骤
                    projUpdateDataLog.setOnlineStepType(2);
                    projUpdateDataLog.setOnlineStepTypeName("作废");
                    projUpdateDataLog.setOperateTitle("作废上线步骤节点【" + operateName + "】");
                    break;
                case 6:
                    // 更新上线步骤完成标志 完成标识
                    projUpdateDataLog.setOnlineStepType(4);
                    projUpdateDataLog.setOnlineStepTypeName("更新完成标识");
                    projUpdateDataLog.setOperateTitle("更新完成标识上线步骤节点【" + operateName + "】");
                    break;
                case 7:
                    // 7 项目首期/非首期修改
                    projUpdateDataLog.setOnlineStepTypeName("更新项目首期非首期");
                    String hisFlag = projectInfo.getHisFlag() == 1 ? "首期" : "非首期";
                    projUpdateDataLog.setOperateTitle("更新【" + projectInfo.getProjectName() + "】为【" + hisFlag + "】项目");
                    break;
                case 8:
                    // 8项目调研管控时间修改
                    projUpdateDataLog.setOnlineStepTypeName("项目调研管控时间修改");
                    projUpdateDataLog.setOperateTitle("更新【" + projectInfo.getProjectName() + "】管控时间为【" + libFormReq.getBeginTime() + "】");
                    break;
                default:
                    break;
            }
            projUpdateDataLogMapper.insert(projUpdateDataLog);
        } catch (Exception e) {
            log.error("存储日志失败", e);
        }
    }

    /**
     * 存储日志
     *
     * @param libFormReq
     */
    private void insertLogData(ProjToolUpdateReq libFormReq) {
        try {
            ProjUpdateDataLog projUpdateDataLog = new ProjUpdateDataLog();
            // 修改账号
            projUpdateDataLog.setOperateType(1);
            projUpdateDataLog.setUpdateDataLogId(SnowFlakeUtil.getId());
            projUpdateDataLog.setOldAccount(libFormReq.getOldUserAccount());
            projUpdateDataLog.setNewAccount(libFormReq.getNewUserAccount());
            projUpdateDataLog.setOperateTitle("账号替换操作");
            projUpdateDataLogMapper.insert(projUpdateDataLog);
        } catch (Exception e) {
            log.error("存储日志失败", e);
        }
    }
}
