package com.msun.csm.service.common;

import java.util.List;

import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseHospitalNameResp;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameExtendResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.BaseProjectInfoResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SelectOption;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectMemberRoleInfo;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.model.dto.DirUserDTO;
import com.msun.csm.model.param.GetSearchPeopleTypeParam;
import com.msun.csm.model.req.DictAgentChatReq;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */
public interface BaseQueryService {

    /**
     * 查询所有客户
     *
     * @param keyword
     * @return
     */
    List<BaseIdNameResp> queryAllCustomer(String keyword);

    /**
     * 查询所有项目
     *
     * @param keyword
     * @param customInfoId
     * @return
     */
    List<BaseIdNameResp> queryProject(String keyword, Long customInfoId);

    /**
     * 根据条件查询医院
     *
     * @param keyword
     * @param isOnlyDomain
     * @param customInfoId
     * @return
     */
    List<BaseHospitalNameResp> queryHospital(String keyword, Boolean isOnlyDomain, Long customInfoId, Long projectInfoId);

    /**
     * 查询资源库表单类型
     *
     * @return
     */
    List<BaseCodeNameResp> queryLibFormType();

    /**
     * 查询合同客户
     *
     * @param keyword
     * @return
     */
    List<BaseIdNameResp> queryAllContractCustomer(String keyword);

    /**
     * 查询产品
     *
     * @param keyword
     * @return
     */
    List<BaseIdNameResp> queryProduct(String keyword);

    /**
     * 查询部门
     *
     * @param keyword
     * @return
     */
    List<BaseIdNameResp> queryDept(String keyword);

    /**
     * 查询项目类型-单体/区域
     *
     * @return
     */
    List<BaseIdNameResp> getProjectType();

    /**
     * 查询项目升级方式-老换新/新客户
     *
     * @return
     */
    List<BaseIdNameResp> getProjectUpgradation();

    /**
     * 查询项目验收超时天数
     *
     * @param projectInfoId
     * @param hospitalInfoId
     * @param limitType
     * @return
     */
    Integer queryAcceptanceExceedTimeLimit(Long projectInfoId, Long hospitalInfoId, Integer limitType);

    /**
     * 查询里程碑数据
     *
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameExtendResp> queryProjectMilestone(Long projectInfoId);

    /**
     * 查询项目上线步骤
     *
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameExtendResp> queryProjectOnlineStep(Long projectInfoId);

    /**
     * 查询项目信息
     *
     * @param projectInfoId
     * @return
     */
    ProjProjectInfo getProjectByProjectInfoId(Long projectInfoId);

    List<BaseIdNameResp> queryFirstProject(Long customInfoId);


    /**
     * 查询客户类型
     *
     * @return
     */
    List<BaseIdNameResp> queryCustomType();

    /**
     * 查询项目进度
     *
     * @return
     */
    List<BaseIdNameResp> queryProjectProgress();

    /**
     * 查询部门
     *
     * @param deptType
     * @return
     */
    List<BaseIdNameResp> queryDeptByParamer(Integer deptType);


    List<BaseProjectInfoResp> queryBackendProjectInfo(Long customInfoId);

    /**
     * 查询当前账号的角色信息--前后端人员
     *
     * @param projectInfoId
     * @return
     */
    Integer getUserRoleType(Long projectInfoId);

    /**
     * 查询项目待办类型
     *
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameResp> queryProjectTodoTypeData(Long projectInfoId);

    /**
     * 查询项目是业务线否开启审核
     * 限制类型 1开启医护打印验证流程、2打印报表、3云护理表单、4手麻表单、5重症表单、6急诊表单、7三方接口、8医保接口、9统计报表/10 产品业务调研  11 打印报表 12 表单
     *
     * @param projectInfoId
     * @param openType
     * @return
     */
    ConfigCustomBackendDetailLimit isOpenAuditorFlag(Long projectInfoId, Integer openType);

    List<BaseIdCodeNameResp> getBusinessStatusList(String businessCode, Long projectInfoId);

    /**
     * 查询项目计划阶段字典
     * @return
     */
    List<BaseCodeNameResp> getAllPlanStage();


    /**
     * 根据团队类型编码获取团队信息
     *
     * @param teamTypeCode 团队类型编码：bustype-业务服务团队；datatype-数据服务团队；interfacetype-接口服务团队
     * @return 运维平台的后端团队信息
     */
    List<BaseCodeNameResp> getBackendTeamByTypeCode(String teamTypeCode);

    /**
     * 根据项目ID查询当前登陆人是否为对应的的前端项目经理或者后端项目经理
     *
     * @param projectInfoId 项目ID
     * @return 是否为项目经理的标记
     */
    ProjectMemberRoleInfo getProjectMemberRoleInfo(Long projectInfoId);

    /**
     * 查询众阳下下所有人
     * @param dto
     * @return
     */
    List<BaseIdNameResp> getDirUserList(DirUserDTO dto);
    SelectOption  getAllBackendTeam();

    /**
     * 获取智能助手进行问答
     * @param dictAgentChatReq
     * @return
     */
    Result sendChartMessage(DictAgentChatReq dictAgentChatReq);


    SelectOption getSearchPeopleType(GetSearchPeopleTypeParam param);

    Result<List<BaseCodeNameResp>> queryIdentifierList(ProjectInfoId projectInfoId);

    /**
     * 查询纸张列表
     * @param printCode
     * @return
     */
    List<BaseIdCodeNameResp> getPaperSizeList(String printCode);

    /**
     * 查询业务上线必备状态列表
     * @param busCode
     * @return
     */
    Result<List<BaseIdNameResp>> getOnlineStatusPublicData(String busCode);

    Result<List<BaseIdNameResp>> queryDeliverProductByProject(Long projectInfoId);

}
