package com.msun.csm.service.formlibnew.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.formlib.FormlibFeignControllerApi;
import com.msun.core.component.implementation.api.formlib.dto.FormlibComeToProductDTO;
import com.msun.core.component.implementation.api.formlib.dto.FormlibComeToProductImportDto;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDataOneSaveDto;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDeliverToYjkDTO;
import com.msun.core.component.implementation.api.formlib.dto.FormlibProductDTO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductDataVO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductImportResultVO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductPageVO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibDataVO;
import com.msun.core.component.implementation.api.formrepository.formstructure.api.ProductFormStructureApi;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.dto.PreviewQueryDTO;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.vo.DocumentBase64VO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.projform.FormSourceType;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.formlibnew.FormlibResourceType;
import com.msun.csm.dao.entity.formlibnew.FormlibResourceUnite;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.proj.projform.DictFormType;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.formlibnew.DictFormlibStandardMapper;
import com.msun.csm.dao.mapper.formlibnew.FormlibResourceTypeMapper;
import com.msun.csm.dao.mapper.formlibnew.FormlibResourceUniteMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.projform.DictFormTypeMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.req.formlibnew.FormLibResourceUnitePageReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceReferenceReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectOneReq;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projreport.statis.ProjFileReq;
import com.msun.csm.model.resp.formlibnew.FormlibResourceSelectType;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUniteSelectOneResp;
import com.msun.csm.model.resp.formlibnew.FormlibResourceUniteUseResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.vo.SysConfigVO;
import com.msun.csm.service.config.SysConfigService;
import com.msun.csm.service.formlibnew.FormlibResourceUniteService;
import com.msun.csm.service.proj.ProjBusinessExamineLogService;
import com.msun.csm.service.proj.ProjProjectFileService;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;
import com.msun.csm.util.Base64MultipartFile;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 * @description 针对表【formlib_resource_unite(表单资源库)】的数据库操作Service实现
 * @createDate 2025-07-14 13:47:02
 */
@Slf4j
@Service
public class FormlibResourceUniteServiceImpl extends ServiceImpl<FormlibResourceUniteMapper, FormlibResourceUnite> implements FormlibResourceUniteService {

    @Resource
    private FormlibResourceUniteMapper formlibResourceUniteMapper;

    @Resource
    private DictFormlibStandardMapper dictFormlibStandardMapper;
    @Autowired
    private FormlibResourceTypeMapper formlibResourceTypeMapper;
    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjProjectFileService projProjectFileService;
    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private FormlibFeignControllerApi formlibFeignControllerApi;

    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Resource
    private DictFormTypeMapper dictFormTypeMapper;
    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Lazy
    @Resource
    private ProjProductTaskService projProductTaskService;

    @Lazy
    @Resource
    private SysConfigService sysConfigService;

    @Lazy
    @Resource
    private ProjBusinessExamineLogService projBusinessExamineLogService;

    @Lazy
    @Resource
    private ProductFormStructureApi productFormStructureApi;

    /**
     * 查询表单资源库分页数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<FormlibResourceUnitePageResp>> findFormLibDataPage(FormLibResourceUnitePageReq dto) {
        if (dto.getFormlibStandardIds() != null) {
            List<Long> ids = new ArrayList<>();
            for (Long id : dto.getFormlibStandardIds()) {
                List<Long> idsC = dictFormlibStandardMapper.getAllChildrenIdByPid(id, 0);
                ids.addAll(idsC);
            }
            dto.setIds(ids);
        }
        List<FormlibResourceUnitePageResp> list = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> formlibResourceUniteMapper.findFormLibDataPage(dto));
        if (list != null && !list.isEmpty()) {
            list.forEach(e -> {
                // 图片处理
                List<Long> listImgs = new ArrayList<>();
                if (e.getFormPicturePaths() != null && !"".equals(e.getFormPicturePaths())) {
                    String[] styles = e.getFormPicturePaths().split(",");
                    if (styles != null) {
                        for (String style : styles) {
                            try {
                                listImgs.add(Long.parseLong(style));
                            } catch (Exception ee) {
                                log.error("表单样式转换异常", ee);
                            }
                        }
                    }
                }
                List<ProjFileReq> p = new ArrayList<>();
                if (listImgs != null && listImgs.size() > 0) {
                    List<ProjProjectFile> reportStyleList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", listImgs));
                    if (reportStyleList != null && reportStyleList.size() > 0) {
                        reportStyleList.forEach(e1 -> {
                            ProjFileReq req = new ProjFileReq();
                            req.setProjectFileId(e1.getProjectFileId());
                            req.setName(e1.getFileName());
                            req.setUrl(OBSClientUtils.getTemporaryUrlLimit(e1.getFilePath(), 3600));
                            // 因为之前只允许传图片，获取不到文件类型时默认为图片
                            if (StringUtils.isBlank(e1.getFileType())) {
                                req.setFileType("image");
                            } else {
                                req.setFileType(e1.getFileType());
                            }
                            p.add(req);
                        });
                    }
                }
                e.setFormStyleList(p);

            });
        }
        PageInfo<FormlibResourceUnitePageResp> pageInfo = new PageInfo<>(list);
        return Result.success(pageInfo);
    }

    /**
     * 查询表单资源库单条数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<FormlibResourceUniteSelectOneResp> findFormLibDataOne(FormLibResourceUnitePageReq dto) {
        FormlibResourceUniteSelectOneResp entity = new FormlibResourceUniteSelectOneResp();
        List<FormlibResourceUnitePageResp> list = formlibResourceUniteMapper.findFormLibDataPage(dto);
        if (list != null && !list.isEmpty()) {
            BeanUtil.copyProperties(list.get(0), entity);
        }
        List<FormlibResourceSelectType> list2 = formlibResourceTypeMapper.selectListByPid(dto.getFormlibResourceUniteId());
        entity.setFormlibResourceTypes(list2);
        return Result.success(entity);
    }

    /**
     * 删除表单资源库单条数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> deleteFormLibDataOne(FormLibResourceUnitePageReq dto) {
        FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(dto.getFormlibResourceUniteId());
        if (entity != null) {
            entity.setIsDeleted(1);
            if (dto.getIsDeleted() != null) {
                entity.setIsDeleted(dto.getIsDeleted());
            }
            formlibResourceUniteMapper.updateDataById(entity);
            return Result.success("删除成功");
        } else {
            return Result.fail("当前查询或操作的数据不存在，请刷新页面后重试。");
        }
    }

    /**
     * 表单入库/维护
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> formStoreUpdate(FormlibResourceUniteSelectOneReq dto) {
        // 查询表单数据
        FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(dto.getFormlibResourceUniteId());
        if (entity == null) {
            return Result.fail("未查询到数据，不能进行入库及维护操作");
        }
        try {
            if (entity.getFormPicturePaths() != null && !dto.getFormPicturePaths().isEmpty()) {
                // 拉取时获取图片
                getPreviewStyle(entity, null);
            }
        } catch (Exception e) {
            log.error("表单入库拉取时获取图片异常", e);
        }

        /*
         *  判断是通用库还是项目库，
         * 1. 通用库只维护表单名称，备注。 将已有标签进行删除，重新添加
         * 2. 项目库进行表单入库，重新向通用库增加一条主数据，明细数据进行新增
         */
        Long uniteId = entity.getFormlibResourceUniteId();
        if (entity.getFormSource() == 0) {
            entity.setFormName(dto.getFormName());
            entity.setRemark(dto.getRemark());
            formlibResourceUniteMapper.updateDataById(entity);
            uniteId = entity.getFormlibResourceUniteId();

        } else {
            uniteId = SnowFlakeUtil.getId();
            entity.setFormlibResourceUniteId(uniteId);
            entity.setFormSaveId(uniteId);
            entity.setFormSource(0);
            entity.setFormName(dto.getFormName());
            entity.setRemark(dto.getRemark());
            entity.setProjectInfoId(-1L);
            formlibResourceUniteMapper.insert(entity);
            // 将旧图片复制一份，生成到新表单中
            if (entity.getFormPicturePaths() != null && !entity.getFormPicturePaths().isEmpty()) {
                String[] strings = entity.getFormPicturePaths().split(",");
                List<Long> longIds = new ArrayList<>();
                for (String string : strings) {
                    try {
                        Long id = Long.valueOf(string);
                        longIds.add(id);
                    } catch (Exception e) {
                        continue;
                    }
                }
                List<Long> newImgLongIds = new ArrayList<>();
                if (longIds != null && !longIds.isEmpty()) {
                    List<ProjProjectFile> oldFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", longIds));
                    if (oldFileList != null && !oldFileList.isEmpty()) {
                        for (ProjProjectFile item : oldFileList) {
                            Long projectFileId = SnowFlakeUtil.getId();
                            item.setProjectFileId(projectFileId);
                            newImgLongIds.add(projectFileId);
                            projProjectFileMapper.insert(item);
                        }
                        entity.setFormPicturePaths(newImgLongIds.stream().map(item -> item.toString()).collect(Collectors.joining(",")));
                        formlibResourceUniteMapper.updateDataById(entity);
                    }
                }

            }
        }
        List<FormlibResourceType> oldDetaillist = formlibResourceTypeMapper.selectList(new QueryWrapper<FormlibResourceType>().eq("formlib_resource_unite_id", dto.getFormlibResourceUniteId()));
        // 插入表单类型
        List<FormlibResourceSelectType> newDetaillist = dto.getFormlibResourceTypes();
        if (newDetaillist == null || newDetaillist.isEmpty()) {
            throw new CustomException("未选择分类,请选择表单类型");
        }
        /**
         * 1.通用库则删除原有标签数据，将标签新数据插入
         * 2. 项目库则进行完全标签新增， 旧标签数据不进行删除
         */
        if (entity.getFormSource() == 0) {
            if (oldDetaillist != null && !oldDetaillist.isEmpty()) {
                for (FormlibResourceType item : oldDetaillist) {
                    formlibResourceTypeMapper.deleteById(item);
                }
            }
        }
        if (!newDetaillist.isEmpty()) {
            for (FormlibResourceSelectType item : newDetaillist) {
                FormlibResourceType detailModel = new FormlibResourceType();
                BeanUtil.copyProperties(item, detailModel);
                detailModel.setFormlibResourceUniteId(uniteId);
                detailModel.setFormlibResourceTypeId(SnowFlakeUtil.getId());
                formlibResourceTypeMapper.insert(detailModel);
            }
        }
        return Result.success("操作成功");
    }

    /**
     * 查询表单资源库列表数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<FormlibResourceUnitePageResp>> findFormLibDataList(FormlibResourceUniteSelectListReq dto) {
        List<FormlibResourceUnitePageResp> list = formlibResourceUniteMapper.findFormLibDataList(dto);
        if (list != null && !list.isEmpty()) {
            for (FormlibResourceUnitePageResp resp : list) {
                // 引用资源库图片
                List<ProjProjectFile> formlibImgsList = new ArrayList<>();
                String[] formlibImgs = null;
                if (StringUtils.isNotEmpty(resp.getFormPicturePaths())) {
                    formlibImgs = resp.getFormPicturePaths().split(",");
                }
                if (formlibImgs != null && formlibImgs.length > 0) {
                    List<Long> formlibImgsListLong = new ArrayList<>();
                    for (String finishImg : formlibImgs) {
                        try {
                            formlibImgsListLong.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (formlibImgsListLong != null && formlibImgsListLong.size() > 0) {
                        formlibImgsList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", formlibImgsListLong).eq("is_deleted", 0));
                        if (CollectionUtil.isNotEmpty(formlibImgsList)) {
                            formlibImgsList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        formlibImgsList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(formlibImgsList);
                    }
                }
                List<ProjFileReq> styleList = new ArrayList<>();
                if (formlibImgsList != null && !formlibImgsList.isEmpty()) {
                    for (ProjProjectFile item : formlibImgsList) {
                        ProjFileReq req = new ProjFileReq();
                        req.setProjectFileId(item.getProjectFileId());
                        req.setFileType(item.getFileType());
                        req.setName(item.getFileName());
                        req.setUrl(item.getFilePath());
                        styleList.add(req);
                    }
                }
                resp.setFormStyleList(styleList);
            }
            // 按照useCount从大到小排序
            list = list.stream().sorted((a, b) -> Integer.compare(b.getUseCount(), a.getUseCount())).collect(Collectors.toList());
        }
        return Result.success(list);
    }

    /**
     * 获取表单数据
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult<FormlibDataVO> getFormDataByJfId(FormlibProductDTO dto) {
        log.error("获取表单数据dto:{}");
        FormlibDataVO vo = new FormlibDataVO();
        if (dto.getJfid() != null) {
            FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(dto.getJfid());
            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                vo.setFormId(entity.getFormSaveId());
                vo.setSourceHospitalId(dto.getHospitalId());
                vo.setHisOrgId(dto.getHisOrgId());
                return ResponseResult.success(vo);
            } else {
                ResponseResult result = new ResponseResult();
                result.setSuccess(false);
                result.setMessage("未查询到表单数据");
                return result;
            }
        } else {
            ResponseResult result = new ResponseResult();
            result.setSuccess(false);
            result.setMessage("jfid不能为空");
            return result;
        }
    }

    /**
     * 保存表单数据
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult<String> saveFormOneDataToDeliver(FormlibDataOneSaveDto dto) {
        log.error("保存表单数据dto:{}", dto);
        if (dto.getJfid() != null) {
            FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(dto.getJfid());
            Long hlId = 4664L;
            if (entity != null && entity.getYyProductId() != null && hlId.equals(entity.getYyProductId()) && (dto.getFormStructure() == null || dto.getFormStructure().isEmpty())) {
                ResponseResult result = new ResponseResult();
                result.setMessage("表单结构不允许为空");
                result.setSuccess(false);
                result.setCode("500");
                return result;
            }
            if (entity == null) {
                ResponseResult result = new ResponseResult();
                result.setMessage("未找到存储对象内容");
                result.setSuccess(false);
                result.setCode("500");
                return result;
            } else {
                entity.setFormSaveId(dto.getFormId());
                entity.setFormStructure(dto.getFormStructure());
                entity.setFormName(dto.getFormName());
                entity.setRemark(dto.getRemark());
                entity.setSourceHospitalId(dto.getSourceHospitalId());
                entity.setSourceHospitalName(dto.getSourceHospitalName());
                entity.setHisOrgId(dto.getHisOrgId());
                entity.setDesignerGrf(dto.getDesignerGrf());
                entity.setDesignerGrfApp(dto.getDesignerGrfApp());
                entity.setDesignerGrfPad(dto.getDesignerGrfPad());
                entity.setFormConfigurationPc(dto.getFormConfigurationPc());
                entity.setFormType(dto.getFormType());
                entity.setFormSaveId(dto.getFormId());
                // base64转图片流存储到OBS
                try {
                    String imgId = "";
                    if (dto.getFormPicture() != null && !dto.getFormPicture().isEmpty()) {
                        for (String item : dto.getFormPicture()) {
                            MultipartFile mul = Base64MultipartFile.base64ToMultipart(item, "default.png", "image/png", dto.getFormId());
                            UploadFileReq req = new UploadFileReq(-1L, "", mul, "reportTaskId", false, null);
                            Result<ProjProjectFileExtend> result = projProjectFileService.uploadFile(req, null);
                            if (result != null && result.getData() != null && result.isSuccess()) {
                                String projectFileId = String.valueOf(result.getData().getProjectFileId());
                                imgId = projectFileId + ",";
                            }
                        }
                        if (imgId != null && !imgId.isEmpty()) {
                            imgId = imgId.substring(0, imgId.length() - 1);
                            entity.setFormPicturePaths(imgId);
                        }
                        // 根据数据处理调研图片
                        List<ProjSurveyForm> surveyForm = projSurveyFormMapper.selectList(new QueryWrapper<ProjSurveyForm>().eq("is_deleted",0).eq("survey_form_id", entity.getFormlibResourceUniteId()));
                        if (surveyForm != null && !surveyForm.isEmpty()) {
                            for (ProjSurveyForm item : surveyForm) {
                                item.setFinishImgs(imgId);
                                projSurveyFormMapper.updateById(item);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("保存表单数据异常:{}", e);
                }
                formlibResourceUniteMapper.updateDataById(entity);
                // 拉取时获取图片
                Result<DocumentBase64VO> vos = getPreviewStyle(entity, null);
                log.error("拉取表单数据vos:{}", vos);
            }
        }
        return ResponseResult.success();
    }

    /**
     * 从交付根据类型获取资源库数据
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult<FormlibComeToProductPageVO> getFormDataByDeliverToYjk(FormlibDeliverToYjkDTO dto) {
        log.error("从交付根据类型获取资源库数据dto:{}", dto);
        if (dto.getFormType() != null) {
            FormlibComeToProductPageVO vo = new FormlibComeToProductPageVO();
            List<FormlibComeToProductDataVO> entity = formlibResourceUniteMapper.selectListByFormType(dto);
            if (entity != null && !entity.isEmpty()) {
                vo.setTotal(entity.size());
                vo.setData(entity);
                return ResponseResult.success(vo);
            } else {
                vo.setTotal(0);
                vo.setData(entity);
                return ResponseResult.success(vo);
            }
        } else {
            ResponseResult result = new ResponseResult();
            result.setMessage("入参存在问题");
            result.setSuccess(false);
            result.setCode("500");
            return result;
        }
    }

    /**
     * 资源库引用或 准备阶段选择制作
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> formlibReference(FormlibResourceReferenceReq dto) {
        // 查询调研表单
        ProjSurveyForm surveyForm = projSurveyFormMapper.selectById(dto.getSurveyFormId());
        if (surveyForm == null) {
            return Result.fail("未找到调研表单");
        }
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(surveyForm.getHospitalInfoId());
        List<DictFormType> list = new ArrayList<>();
        if (surveyForm.getFormlibStandardTypeId() != null) {
            list = formlibResourceTypeMapper.getFormlibStandardTypeByParam(surveyForm.getFormlibStandardTypeId());
        }
        // 新表单数据（项目引用）
        FormlibResourceUnite newSaveData = new FormlibResourceUnite();
        if (dto.getFormlibResourceUniteId() != null) {
            // 资源库通用表单数据
            FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(dto.getFormlibResourceUniteId());
            if (entity != null && entity.getFormSource() == 0) {
                BeanUtil.copyProperties(entity, newSaveData);
                if (hospitalInfo != null) {
                    newSaveData.setHisOrgId(hospitalInfo.getOrgId());
                    newSaveData.setSourceHospitalId(hospitalInfo.getCloudHospitalId());
                    newSaveData.setSourceHospitalName(hospitalInfo.getHospitalName());
                }
                Long uniteId = SnowFlakeUtil.getId();
                newSaveData.setFormlibResourceUniteId(uniteId);
                newSaveData.setFormSource(1);
                newSaveData.setProjectInfoId(surveyForm.getProjectInfoId());
                newSaveData.setFormSaveId(uniteId);
                formlibResourceUniteMapper.insert(newSaveData);
                // 将原有资源库数据引用次数+1
                entity.setUseCount(entity.getUseCount() + 1);
                formlibResourceUniteMapper.updateDataById(entity);
                // 将旧图片复制一份，生成到新表单中
                if (newSaveData.getFormPicturePaths() != null && !newSaveData.getFormPicturePaths().isEmpty()) {
                    String[] strings = newSaveData.getFormPicturePaths().split(",");
                    List<Long> longIds = new ArrayList<>();
                    for (String string : strings) {
                        try {
                            Long id = Long.valueOf(string);
                            longIds.add(id);
                        } catch (Exception e) {
                            continue;
                        }
                    }
                    List<Long> newImgLongIds = new ArrayList<>();
                    if (longIds != null && !longIds.isEmpty()) {
                        List<ProjProjectFile> oldFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", longIds));
                        if (oldFileList != null && !oldFileList.isEmpty()) {
                            for (ProjProjectFile item : oldFileList) {
                                Long projectFileId = SnowFlakeUtil.getId();
                                item.setProjectFileId(projectFileId);
                                newImgLongIds.add(projectFileId);
                                projProjectFileMapper.insert(item);
                            }
                            newSaveData.setFormPicturePaths(newImgLongIds.stream().map(item -> item.toString()).collect(Collectors.joining(",")));
                            formlibResourceUniteMapper.updateDataById(newSaveData);
                        }
                    }

                }
                // 保存资源库标签
                saveFormlibResourceTypeData(entity.getFormlibResourceUniteId(), newSaveData.getFormlibResourceUniteId());
                // 将表单引用与资源库关联
                surveyForm.setFormlibResourceUniteId(uniteId);
                projSurveyFormMapper.updateById(surveyForm);
                // 引用日志   15
                projBusinessExamineLogService.saveOperationLog("form", 15, "引用表单", surveyForm.getSurveyFormId());
                // 判断是否有跳转路径
                if (dto.getIsIncludePath() != null && dto.getIsIncludePath() == 1) {
                    try {
                        return Result.success(getUrl(list, surveyForm, newSaveData, 1));
                    } catch (Exception e) {
                        log.error("获取表单跳转路径失败", e);
                        return Result.fail(e.getMessage());
                    }
                }
            } else if (entity != null && entity.getFormSource() == 1) {
                // 将表单引用与资源库关联
                surveyForm.setFormlibResourceUniteId(entity.getFormlibResourceUniteId());
                projSurveyFormMapper.updateById(surveyForm);
                // 引用日志   15
                projBusinessExamineLogService.saveOperationLog("form", 15, "引用表单", surveyForm.getSurveyFormId());
                // 判断是否有跳转路径
                if (dto.getIsIncludePath() != null && dto.getIsIncludePath() == 1) {
                    try {
                        return Result.success(getUrl(list, surveyForm, entity, 0));
                    } catch (Exception e) {
                        log.error("获取表单跳转路径失败", e);
                        return Result.fail(e.getMessage());
                    }
                }
            }
        } else {
            // 新增一条数据 根据表单类型 + 标准名称等信息
            Long uniteId = SnowFlakeUtil.getId();
            newSaveData.setFormlibResourceUniteId(uniteId);
            newSaveData.setFormSource(1);
            newSaveData.setProjectInfoId(surveyForm.getProjectInfoId());
            newSaveData.setFormSaveId(uniteId);
            newSaveData.setFormName(surveyForm.getFormName());
            newSaveData.setUseCount(0);
            newSaveData.setYyProductId(surveyForm.getYyProductId());
            if (list != null && !list.isEmpty()) {
                DictFormType item = list.get(0);
                String typeCode = item.getTypeCode();
                // 1-评估单 2-记录单 4 知情文件
                if ("informedDocument".equals(item.getTypeCode())) {
                    typeCode = "4";
                } else if ("record".equals(item.getTypeCode())) {
                    // 护理记录单
                    typeCode = "2";
                } else if ("evaluat".equals(item.getTypeCode())) {
                    typeCode = "1";
                }
                newSaveData.setFormType(typeCode);
            }
            formlibResourceUniteMapper.insert(newSaveData);
            // 将表单引用与资源库关联
            surveyForm.setFormlibResourceUniteId(uniteId);
            projSurveyFormMapper.updateById(surveyForm);
            FormlibResourceType dictFormType = new FormlibResourceType();
            dictFormType.setFormlibResourceTypeId(SnowFlakeUtil.getId());
            dictFormType.setFormlibResourceUniteId(uniteId);
            dictFormType.setFormlibStandardId(surveyForm.getFormlibStandardId());
            dictFormType.setFormlibStandardTypeId(surveyForm.getFormlibStandardTypeId());
            formlibResourceTypeMapper.insert(dictFormType);
            // 引用日志   15
            projBusinessExamineLogService.saveOperationLog("form", 15, "引用表单", surveyForm.getSurveyFormId());
            if (dto.getIsIncludePath() != null && dto.getIsIncludePath() == 1) {
                try {
                    return Result.success(getUrl(list, surveyForm, newSaveData, 1));
                } catch (Exception e) {
                    log.error("获取表单跳转路径失败", e);
                    return Result.fail(e.getMessage());
                }
            }
        }
        return Result.success();
    }

    /**
     * 保存资源库标签数据s
     *
     * @param oldFormlibUniteId
     * @param newFormlibUniteId
     */
    private void saveFormlibResourceTypeData(Long oldFormlibUniteId, Long newFormlibUniteId) {
        // 将资源库标签也处理到项目库中
        List<FormlibResourceType> resourceTypeList = formlibResourceTypeMapper.selectList(new QueryWrapper<FormlibResourceType>().eq("formlib_resource_unite_id", oldFormlibUniteId).eq("is_deleted", 0));
        if (resourceTypeList != null && !resourceTypeList.isEmpty()) {
            for (FormlibResourceType item : resourceTypeList) {
                FormlibResourceType dictFormType = new FormlibResourceType();
                BeanUtil.copyProperties(item, dictFormType);
                dictFormType.setFormlibResourceTypeId(SnowFlakeUtil.getId());
                dictFormType.setFormlibResourceUniteId(newFormlibUniteId);
                formlibResourceTypeMapper.insert(dictFormType);
            }
        }
    }


    /**
     * 获取跳转路径
     *
     * @param list
     * @param form
     * @param newSaveData
     * @param isAdd       1 新增 0 修改
     * @return
     */
    private String getUrl(List<DictFormType> list, ProjSurveyForm form, FormlibResourceUnite newSaveData, Integer isAdd) {
        String formPageUrl = "";
        Long hospitalInfoId = form.getHospitalInfoId();
        DictFormType item = new DictFormType();
        if (list != null && !list.isEmpty()) {
            item = list.get(0);
        }
        // 部署后跳转云健康制作
        Boolean isPrintStatus = false;
        try {
            isPrintStatus = projSurveyFormMapper.getPrintStatusByProjectInfoId(form.getProjectInfoId());
        } catch (Exception e) {
            log.error("获取项目是否已上线，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (isPrintStatus || (form.getImportCloudStatus() != null && form.getImportCloudStatus() != -1)) {
            try {
                formPageUrl = item.getFormPageUrl();
            } catch (Exception e) {
                log.error("获取项目是否已上线，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                throw new CustomException("未能找到跳转路径，请前往云健康系统进行制作");
            }
        } else {
            SysConfigVO sysConfig = sysConfigService.selectConfigByName("upCenterUrl");
            if (sysConfig != null) {
                hospitalInfoId = Long.valueOf(sysConfig.getConfigValue());
            }
            formPageUrl = item.getFormPageCenterUrl();
        }
        if (!(formPageUrl != null && !"".equals(formPageUrl))) {
            throw new CustomException("未能找到跳转路径，请前往云健康系统进行制作");
        }
        try {
            ProjProductTaskParam dto = new ProjProductTaskParam();
            dto.setHospitalInfoId(hospitalInfoId);
            dto.setCloudProductCode(item.getCloudProductCode());
            dto.setTaskPageUrl(formPageUrl);
            Map<String, Object> map = new HashMap<>();
            map.put("isAddSheet", isAdd);
            map.put("formType", item.getTypeCode());
            map.put("jfid", String.valueOf(newSaveData.getFormlibResourceUniteId()));
            map.put("formId", String.valueOf(newSaveData.getFormSaveId()));
            dto.setData(JSON.toJSONString(map));
            Object str = projProductTaskService.hisLogin(dto);
            if (str != null && !"".equals(str.toString())) {
                // 设计日志   16
                projBusinessExamineLogService.saveOperationLog("form", 16, "设计表单(查看表单)", form.getSurveyFormId());
                return String.valueOf(str);
            } else {
                log.error("获取表单跳转路径失败1");
                throw new CustomException("未能找到跳转路径或云健康环境未部署，请部署后前往云健康系统进行制作");
            }
        } catch (Exception e) {
            log.error("获取表单跳转路径失败2");
            throw new CustomException("未能找到跳转路径或云健康环境未部署，请部署后前往云健康系统进行制作");
        }

    }


    /**
     * 根据项目id查询表单资源库
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> getFormlibByProductId(FormlibResourceUniteSelectListReq dto) {
        // 默认每页10条数据
        String configCode = "formlibPageSize";
        Long pageSize = 10L;
        SysConfig sysConfig = sysConfigMapper.selectConfigByName(configCode);
        if (sysConfig != null) {
            try {
                pageSize = Long.valueOf(sysConfig.getConfigValue());
            } catch (Exception e) {
                log.error("获取配置表数据异常");
                pageSize = 10L;
            }
        }
        /**
         * 根据产品 + 医院 + 项目id查询表单资源库
         * 1. 查询医院
         * 2. 根据医院id查询项目id
         * 3. 根据产品查询表单类型
         * 4. 根据 医院 + 产品 + 表单类型 查询表单资源库
         *    4.1  查询完成后立即保存
         */
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
        if (hospitalInfo == null || hospitalInfo.getCloudHospitalId() == null) {
            return Result.fail("无医院信息");
        }
        List<ProjProjectInfo> projectInfo = projProjectInfoMapper.selectProjectListByHospitalId(hospitalInfo.getHospitalInfoId());
        if (projectInfo != null && !projectInfo.isEmpty()) {
            dto.setProjectInfoId(projectInfo.get(0).getProjectInfoId());
        }
        List<DictFormType> formTypeList = new ArrayList<>();
        Long hlId = 4664L;
        if (dto.getYyProductId() != null && hlId.equals(dto.getYyProductId())) {
            formTypeList = dictFormTypeMapper.selectList(new QueryWrapper<DictFormType>().eq("is_deleted", NumberEnum.NO_0.num()).eq("yy_product_id", dto.getYyProductId()));
        }
        if (formTypeList != null && !formTypeList.isEmpty()) {
            for (DictFormType item : formTypeList) {
                String typeCode = "-1";
                if (item.getTypeCode() != null) {
                    typeCode = item.getTypeCode();
                }
                // 1-评估单 2-记录单 4 知情文件
                if ("informedDocument".equals(item.getTypeCode())) {
                    typeCode = "4";
                } else if ("record".equals(item.getTypeCode())) {
                    // 护理记录单
                    typeCode = "2";
                } else if ("evaluat".equals(item.getTypeCode())) {
                    typeCode = "1";
                }
                if ("-1".equals(typeCode)) {
                    continue;
                }
                getFormlibByProductByParamer(dto, pageSize, item.getTypeCode(), hospitalInfo);
            }
        } else {
            getFormlibByProductByParamer(dto, pageSize, null, hospitalInfo);
        }
        return Result.success();
    }

    /**
     * 导入表单资源库
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> importFormlibToYjk(FormlibResourceUniteSelectListReq dto) {
        log.error("导入表单资源库入参{}", dto);
        // 默认每页10条数据
        String configCode = "formlibPageSize";
        Integer pageSize = 10;
        SysConfig sysConfig = sysConfigMapper.selectConfigByName(configCode);
        if (sysConfig != null) {
            try {
                pageSize = Integer.valueOf(sysConfig.getConfigValue());
            } catch (Exception e) {
                log.error("获取配置表数据异常");
                pageSize = 10;
            }
        }
        /**
         *
         * 1. 根据项目id  查询所有已对照资源库并制作完成的数据 按照医院id + 产品排序
         * 2. 按照分页导入云健康
         * 3. 存储日志
         *
         */
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
        List<Long> hospitalInfoIds = new ArrayList<>();
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.selectListByFormInfo(projectInfo);
        if (hospitalInfoList == null || hospitalInfoList.isEmpty()) {
            return Result.fail("无医院信息");
        }
        // 需要将所有没有对照的表单资源库的表单统一更新为一个固定值 2 ，记录导入时是没有对照的
        projSurveyFormMapper.updateFormByProjectId(dto.getProjectInfoId());
        hospitalInfoIds = hospitalInfoList.stream().map(item -> item.getHospitalInfoId()).collect(Collectors.toList());
        List<Long> productIds = Arrays.asList(4664L, 4050L);
        // 按照分页导入云健康数据
        List<FormlibResourceUnite> formlibResourceUnites = formlibResourceUniteMapper.selectBySurveyFormParam(dto);
        for (Long hospitalInfoId : hospitalInfoIds) {
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(hospitalInfoId);
            for (Long productId : productIds) {
                importFormlibToYjkByJf(formlibResourceUnites, hospitalInfo, productId, pageSize);
            }
        }
        return Result.success();
    }

    /**
     * 根据标准名称及分类查询表单数据(准备设计推荐)
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<FormlibResourceUnitePageResp>> findFormLibDataListToRecommend(FormlibResourceUniteSelectListReq dto) {
        List<FormlibResourceUnitePageResp> list = formlibResourceUniteMapper.findFormLibDataListToRecommend(dto);
        if (list != null && !list.isEmpty()) {
            for (FormlibResourceUnitePageResp resp : list) {
                // 引用资源库图片
                List<ProjProjectFile> formlibImgsList = new ArrayList<>();
                String[] formlibImgs = null;
                if (StringUtils.isNotEmpty(resp.getFormPicturePaths())) {
                    formlibImgs = resp.getFormPicturePaths().split(",");
                }
                if (formlibImgs != null && formlibImgs.length > 0) {
                    List<Long> formlibImgsListLong = new ArrayList<>();
                    for (String finishImg : formlibImgs) {
                        try {
                            formlibImgsListLong.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (formlibImgsListLong != null && formlibImgsListLong.size() > 0) {
                        formlibImgsList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", formlibImgsListLong).eq("is_deleted", 0));
                        if (CollectionUtil.isNotEmpty(formlibImgsList)) {
                            formlibImgsList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        formlibImgsList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(formlibImgsList);
                    }
                }
                List<ProjFileReq> styleList = new ArrayList<>();
                if (formlibImgsList != null && !formlibImgsList.isEmpty()) {
                    for (ProjProjectFile item : formlibImgsList) {
                        ProjFileReq req = new ProjFileReq();
                        req.setProjectFileId(item.getProjectFileId());
                        req.setFileType(item.getFileType());
                        req.setName(item.getFileName());
                        req.setUrl(item.getFilePath());
                        styleList.add(req);
                    }
                }
                resp.setFormStyleList(styleList);
            }
            // 按照useCount从大到小排序
            list = list.stream().sorted((a, b) -> Integer.compare(b.getUseCount(), a.getUseCount())).collect(Collectors.toList());
        }
        return Result.success(list);
    }

    /**
     * 设计表单(根据部署跳转中心端或云健康)
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> designFormByParam(FormlibResourceReferenceReq dto) {
        dto.setIsIncludePath(1);
        return this.formlibReference(dto);
    }

    /**
     * 资源库跳转中心端进行设计
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> designFormResLibByParam(FormlibResourceReferenceReq dto) {
        // 资源库通用表单数据
        FormlibResourceUnite newSaveData = formlibResourceUniteMapper.selectById(dto.getFormlibResourceUniteId());
        List<DictFormType> list = dictFormTypeMapper.selectList(new QueryWrapper<DictFormType>().eq("is_deleted", 0).eq("yy_product_id", newSaveData.getYyProductId()).eq("type_code", newSaveData.getFormType()));
        if (list == null || list.isEmpty()) {
            return Result.fail("表单没有表单类型，不能进行设计");
        }
        DictFormType item = list.get(0);
        String formPageUrl = "";
        Long hospitalInfoId = null;
        SysConfigVO sysConfig = sysConfigService.selectConfigByName("upCenterUrl");
        if (sysConfig != null) {
            hospitalInfoId = Long.valueOf(sysConfig.getConfigValue());
        }
        formPageUrl = item.getFormPageCenterUrl();
        if (!(formPageUrl != null && !"".equals(formPageUrl))) {
            throw new CustomException("未能找到与表单对应的节点，请前往相应系统进行制作");
        }
        try {
            ProjProductTaskParam dtoParam = new ProjProductTaskParam();
            dtoParam.setHospitalInfoId(hospitalInfoId);
            dtoParam.setCloudProductCode(item.getCloudProductCode());
            dtoParam.setTaskPageUrl(formPageUrl);
            Map<String, Object> map = new HashMap<>();
            map.put("formType", item.getTypeCode());
            map.put("jfid", String.valueOf(newSaveData.getFormlibResourceUniteId()));
            map.put("formId", String.valueOf(newSaveData.getFormSaveId()));
            dtoParam.setData(JSON.toJSONString(map));
            Object str = projProductTaskService.hisLogin(dtoParam);
            if (str != null && !"".equals(str.toString())) {
                return Result.success(String.valueOf(str));
            } else {
                throw new CustomException("未检测到医院信息，请联系管理员");
            }
        } catch (Exception e) {
            throw new CustomException("未检测到医院信息，请联系管理员");
        }
    }

    /**
     * 设计表单(根据部署跳转中心端或云健康)【只用于跳转】
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> jumpFormByParam(FormlibResourceReferenceReq dto) {
        // 查询调研表单
        ProjSurveyForm surveyForm = projSurveyFormMapper.selectById(dto.getSurveyFormId());
        if (surveyForm == null) {
            return Result.fail("未找到调研表单");
        }
        List<DictFormType> list = new ArrayList<>();
        if (surveyForm.getFormlibStandardTypeId() != null) {
            list = formlibResourceTypeMapper.getFormlibStandardTypeByParam(surveyForm.getFormlibStandardTypeId());
        }
        // 如果已关联资源库数据
        if (surveyForm.getFormlibResourceUniteId() != null) {
            // 资源库通用表单数据
            FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(surveyForm.getFormlibResourceUniteId());
            if (entity != null) {
                try {
                    return Result.success(getUrl(list, surveyForm, entity, 0));
                } catch (Exception e) {
                    log.error("获取表单跳转路径失败", e);
                    return Result.fail(e.getMessage());
                }
            }
        } else {
            FormlibResourceUnite newSaveData = new FormlibResourceUnite();
            Long uniteId = SnowFlakeUtil.getId();
            newSaveData.setFormlibResourceUniteId(uniteId);
            newSaveData.setFormSaveId(uniteId);
            newSaveData.setYyProductId(surveyForm.getYyProductId());
            try {
                return Result.success(getUrl(list, surveyForm, newSaveData, 1));
            } catch (Exception e) {
                log.error("获取表单跳转路径失败", e);
                return Result.fail("获取表单跳转路径失败");
            }
        }
        return Result.fail("获取表单跳转路径失败");
    }

    /**
     * 根据标准名称及分类查询表单数据(准备设计推荐)
     *
     * @param dto
     * @return
     */
    @Override
    public Result<FormlibResourceUniteUseResp> findFormLibDataOneAndListToRecommend(FormlibResourceUniteSelectListReq dto) {

        Long id = null;
        if (dto.getSurveyFormId() != null) {
            ProjSurveyForm form = projSurveyFormMapper.selectById(dto.getSurveyFormId());
            id = form.getFormlibResourceUniteId();
            dto.setFormlibResourceUniteId(id);
        }

        // 初始化返回对象
        FormlibResourceUniteUseResp formlibResourceUniteUseResp = new FormlibResourceUniteUseResp();
        formlibResourceUniteUseResp.setFormUse(new FormlibResourceUnitePageResp());
        formlibResourceUniteUseResp.setFormList(new ArrayList<>());

        List<FormlibResourceUnitePageResp> respList = this.findFormLibDataListToRecommend(dto).getData();

        if (id != null) {
            if (respList != null && !respList.isEmpty()) {
                List<FormlibResourceUnitePageResp> respListz = new ArrayList<>();
                for (FormlibResourceUnitePageResp item : respList) {
                    if (item.getFormlibResourceUniteId().equals(id)) {
                        formlibResourceUniteUseResp.setFormUse(item);
                    } else {
                        respListz.add(item);
                    }
                }
                formlibResourceUniteUseResp.setFormList(respListz);
            }
        } else {
            formlibResourceUniteUseResp.setFormList(respList);
        }
        return Result.success(formlibResourceUniteUseResp);
    }

    /**
     * 根据id查询表单数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjSurveyFormResp> findSurveyFormDataById(FormlibResourceUniteSelectListReq dto) {

        FormlibResourceUnite entity = formlibResourceUniteMapper.selectById(dto.getFormlibResourceUniteId());
        getPreviewStyle(entity, null);

        ProjSurveyForm entityResp = projSurveyFormMapper.selectById(dto.getSurveyFormId());
        if (entityResp != null && entityResp.getFinishImgs() != null && !entityResp.getFinishImgs().isEmpty()) {
            // 制作完成
            entityResp.setFinishStatus(1);
            projSurveyFormMapper.updateById(entityResp);
        }

        // 列表查询
        ProjSurveyFormReq projSurveyFormReq = new ProjSurveyFormReq();
        projSurveyFormReq.setSurveyFormId(dto.getSurveyFormId());
        List<ProjSurveyFormResp> projSurveyReportResps = projSurveyFormMapper.selectSurveyFormByPage(projSurveyFormReq);
        if (projSurveyReportResps != null && projSurveyReportResps.size() > 0) {
            for (ProjSurveyFormResp projSurveyReportResp : projSurveyReportResps) {
                projSurveyReportResp.setFormSourceStr(FormSourceType.getDescByCode(projSurveyReportResp.getFormSource()));
                // 获取文件信息
                String[] surveyImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getSurveyImgs())) {
                    surveyImgs = projSurveyReportResp.getSurveyImgs().split(",");
                }
                String[] finishImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getFinishImgs())) {
                    finishImgs = projSurveyReportResp.getFinishImgs().split(",");
                }
                List<ProjProjectFile> surveyImgsFileList = new ArrayList<>();
                if (surveyImgs != null && surveyImgs.length > 0) {
                    List<Long> surveyImgsList = new ArrayList<>();
                    for (String surveyImg : surveyImgs) {
                        try {
                            surveyImgsList.add(Long.valueOf(surveyImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (surveyImgsList != null && surveyImgsList.size() > 0) {
                        surveyImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", surveyImgsList));
                        if (CollectionUtil.isNotEmpty(surveyImgsFileList)) {
                            surveyImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        surveyImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(surveyImgsFileList);
                    }
                }
                List<ProjProjectFile> finishImgsFileList = new ArrayList<>();
                if (finishImgs != null && finishImgs.length > 0) {
                    List<Long> finishImgsList = new ArrayList<>();
                    for (String finishImg : finishImgs) {
                        try {
                            finishImgsList.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (finishImgsList != null && finishImgsList.size() > 0) {
                        finishImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", finishImgsList));
                        if (CollectionUtil.isNotEmpty(finishImgsFileList)) {
                            finishImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        finishImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(surveyImgsFileList);
                    }
                }
                // 引用资源库图片
                List<ProjProjectFile> formlibImgsList = new ArrayList<>();
                String[] formlibImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getFormPicturePaths())) {
                    formlibImgs = projSurveyReportResp.getFormPicturePaths().split(",");
                }
                if (formlibImgs != null && formlibImgs.length > 0) {
                    List<Long> formlibImgsListLong = new ArrayList<>();
                    for (String finishImg : formlibImgs) {
                        try {
                            formlibImgsListLong.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (formlibImgsListLong != null && formlibImgsListLong.size() > 0) {
                        formlibImgsList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", formlibImgsListLong).eq("is_deleted", 0));
                        if (CollectionUtil.isNotEmpty(formlibImgsList)) {
                            formlibImgsList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        formlibImgsList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(formlibImgsList);
                    }
                }
                projSurveyReportResp.setSurveyImgsList(surveyImgsFileList);
                projSurveyReportResp.setFinishImgsList(finishImgsFileList);
                projSurveyReportResp.setFormlibImgsList(formlibImgsList);
            }
        }

        if (projSurveyReportResps != null && !projSurveyReportResps.isEmpty()) {
            return Result.success(projSurveyReportResps.get(0));
        }
        return Result.success(new ProjSurveyFormResp());
    }

    /**
     * 导入表单资源库
     *
     * @param formlibResourceUnites
     * @param hospitalInfo
     * @param productId
     * @param pageSize
     */
    private Integer importFormlibToYjkByJf(List<FormlibResourceUnite> formlibResourceUnites, ProjHospitalInfo hospitalInfo, Long productId, Integer pageSize) {
        // 将formlibResourceUnites 按照hospitalInfo.cloudhospitalid + 产品id进行筛选
        List<FormlibResourceUnite> flList = formlibResourceUnites.stream().filter(e -> e.getSourceHospitalId().equals(hospitalInfo.getCloudHospitalId()) && e.getYyProductId().equals(productId)).collect(Collectors.toList());
        if (flList == null || flList.isEmpty()) {
            return null;
        }
        Integer total = flList.size();
        if (total != null && total > 0) {
            if (total > pageSize) {
                int discount = (int) Math.ceil((double) total / pageSize);
                for (int i = 1; i <= discount; i++) {
                    List<FormlibComeToProductDataVO> formList = new ArrayList<>();
                    int jcount = i * pageSize > total ? total : i * pageSize;
                    for (int j = i * pageSize - pageSize; j < jcount; j++) {
                        FormlibComeToProductDataVO vo = new FormlibComeToProductDataVO();
                        BeanUtil.copyProperties(flList.get(j), vo);
                        vo.setFormId(flList.get(j).getFormlibResourceUniteId());
                        vo.setJfid(flList.get(j).getFormlibResourceUniteId());
                        formList.add(vo);
                    }
                    importYjkData(hospitalInfo, productId, formList);
                }
            } else {
                List<FormlibComeToProductDataVO> formList = new ArrayList<>();
                for (int j = 0; j < flList.size(); j++) {
                    FormlibComeToProductDataVO vo = new FormlibComeToProductDataVO();
                    BeanUtil.copyProperties(flList.get(j), vo);
                    vo.setFormId(flList.get(j).getFormlibResourceUniteId());
                    vo.setJfid(flList.get(j).getFormlibResourceUniteId());
                    formList.add(vo);
                }
                importYjkData(hospitalInfo, productId, formList);
            }
        }
        return null;
    }

    private void importYjkData(ProjHospitalInfo hospitalInfo, Long productId, List<FormlibComeToProductDataVO> formList) {
        try {
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            FormlibComeToProductImportDto importDto = new FormlibComeToProductImportDto();
            importDto.setHospitalId(hospitalInfo.getCloudHospitalId());
            importDto.setHisOrgId(hospitalInfo.getOrgId());
            importDto.setYyProductId(productId);
            importDto.setFormList(formList);
            ResponseResult<List<FormlibComeToProductImportResultVO>> result = formlibFeignControllerApi.importFormDataByProductToCloudHealth(importDto);
            log.error("导入表单资源库结果:{}", result);
            // 根据导入结果进行更新交付导入状态  TODO 记录导入日志
            if (result.isSuccess()) {
                List<FormlibComeToProductImportResultVO> list = result.getData();
                ProjSurveyForm formOne = new ProjSurveyForm();
                if (list != null && !list.isEmpty()) {
                    for (FormlibComeToProductImportResultVO voResult : list) {
                        formOne.setFormlibResourceUniteId(voResult.getFormId());
                        formOne.setImportCloudStatus(voResult.getImportResult());
                        formOne.setImportMsg(voResult.getErrorMsg());
                        projSurveyFormMapper.updateDataByParamer(formOne);
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            log.error("导入表单资源库异常:{}", e.getMessage(), e);
        }
    }

    /**
     * 根据产品查询表单资源库并进行保存
     *
     * @param dto
     * @param pageSize
     * @param formType
     * @param hospitalInfo
     */
    private void getFormlibByProductByParamer(FormlibResourceUniteSelectListReq dto, Long pageSize, String formType, ProjHospitalInfo hospitalInfo) {
        try {
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            FormlibComeToProductDTO formDto = new FormlibComeToProductDTO();
            formDto.setHisOrgId(hospitalInfo.getOrgId());
            formDto.setHospitalId(hospitalInfo.getCloudHospitalId());
            formDto.setYyProductId(dto.getYyProductId());
            formDto.setFormType(formType);
            formDto.setPageNum(1);
            formDto.setPageSize(pageSize);
            ResponseResult<PageInfo<FormlibComeToProductDataVO>> result = formlibFeignControllerApi.getFormDataByProductToDeliver(formDto);
            if (result.getSuccess()) {
                List<FormlibComeToProductDataVO> firstData = result.getData().getList();
                Long total = result.getData().getTotal();
                if (total != null && total > 0) {
                    saveFormlibResourceUnite(dto, firstData, new ProjHospitalInfo());
                    if (total > pageSize) {
                        int discount = (int) Math.ceil((double) total / pageSize);
                        for (int i = 2; i <= discount; i++) {
                            formDto.setPageNum(i);
                            ResponseResult<PageInfo<FormlibComeToProductDataVO>> resultnew = formlibFeignControllerApi.getFormDataByProductToDeliver(formDto);
                            if (resultnew.getSuccess()) {
                                List<FormlibComeToProductDataVO> twodata = result.getData().getList();
                                saveFormlibResourceUnite(dto, twodata, new ProjHospitalInfo());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理查询表单资源库异常:{}", e);
        }
    }

    /**
     * 保存表单资源库
     *
     * @param dto
     * @param firstData
     * @param info
     */
    private void saveFormlibResourceUnite(FormlibResourceUniteSelectListReq dto, List<FormlibComeToProductDataVO> firstData, ProjHospitalInfo info) {
        // TODO 记录日志信息
        if (firstData != null && !firstData.isEmpty()) {
            for (FormlibComeToProductDataVO item : firstData) {
                try {
                    FormlibResourceUnite entity = new FormlibResourceUnite();
                    BeanUtil.copyProperties(item, entity);
                    Long id = SnowFlakeUtil.getId();
                    entity.setFormlibResourceUniteId(id);
                    entity.setFormSource(1);
                    entity.setProjectInfoId(dto.getProjectInfoId());
                    entity.setYyProductId(dto.getYyProductId());
                    entity.setSourceHospitalId(info.getCloudHospitalId());
                    entity.setSourceHospitalName(info.getHospitalName());
                    entity.setHisOrgId(info.getOrgId());
                    entity.setFormSaveId(item.getFormId());
                    Long hlId = 4664L;
                    if (entity.getYyProductId() != null && entity.getYyProductId().equals(hlId) && (entity.getFormStructure() == null || entity.getFormStructure().isEmpty())) {
                        continue;
                    }
                    formlibResourceUniteMapper.insert(entity);
                    if (entity.getYyProductId() != null && entity.getYyProductId().equals(hlId)) {
                        // 拉取时获取图片
                        getPreviewStyle(entity, info);
                    }
                } catch (Exception e) {
                    log.error("保存表单资源库异常:{}", e);
                }
            }
        }
    }

    /**
     * @param entity
     * @return
     */
    public Result<DocumentBase64VO> getPreviewStyle(FormlibResourceUnite entity, ProjHospitalInfo info) {
        Long hospitalInfoId = null;
        SysConfigVO sysConfig = sysConfigService.selectConfigByName("upCenterUrl");
        if (sysConfig != null) {
            hospitalInfoId = Long.valueOf(sysConfig.getConfigValue());
        } else {
            return Result.fail("未查询到医院信息");
        }
        Long productId = 4664L;
        if (entity.getYyProductId() == null || !productId.equals(entity.getYyProductId())) {
            return Result.fail("产品无需拉取图片");
        }
        if (!"1".equals(entity.getFormType()) && !"2".equals(entity.getFormType())) {
            return Result.fail("无需拉取图片");
        }
        if (info == null) {
            info = projHospitalInfoMapper.selectById(hospitalInfoId);
        }
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        PreviewQueryDTO queryDTO = new PreviewQueryDTO();
        queryDTO.setSheetId(entity.getFormSaveId());
        queryDTO.setHospitalId(info.getCloudHospitalId());
        queryDTO.setHisOrgId(info.getOrgId());
        try {
            queryDTO.setSheetType(Integer.valueOf(entity.getFormType()));
        } catch (Exception e) {
            log.error("获取表单类型异常:{}", e.getMessage());
        }
        DocumentBase64VO vo = new DocumentBase64VO();
        try {
            ResponseResult<DocumentBase64VO> results = this.productFormStructureApi.getDocumentPngBase64(queryDTO);
            log.info("获取表单数据:{}", results);
            if (results != null && results.getSuccess()) {
                // base64转图片流存储到OBS
                try {
                    String imgId = "";
                    String pngData = results.getData().getDocument();
                    MultipartFile mul = Base64MultipartFile.base64ToMultipart(pngData, "default.png", "image/png", entity.getFormlibResourceUniteId());
                    UploadFileReq req = new UploadFileReq(-1L, "", mul, "reportTaskId", false, null);
                    Result<ProjProjectFileExtend> resultPng = projProjectFileService.uploadFile(req, null);
                    if (resultPng != null && resultPng.getData() != null && resultPng.isSuccess()) {
                        String projectFileId = String.valueOf(resultPng.getData().getProjectFileId());
                        imgId = projectFileId + ",";
                    }
                    if (imgId != null && !imgId.isEmpty()) {
                        imgId = imgId.substring(0, imgId.length() - 1);
                        entity.setFormPicturePaths(imgId);
                    }
                    formlibResourceUniteMapper.updateDataById(entity);
                } catch (Exception e) {
                    log.error("获取表单预览图片接口数据异常:{}", e);
                }

            } else {
                log.error("获取表单预览图片接口数据异常:{}", results);
            }
        } catch (Exception e) {
            log.error("获取表单预览图片接口数据异常:{}", e.getMessage());
        }
        try {
            // 根据数据处理调研图片
            List<ProjSurveyForm> surveyForm = projSurveyFormMapper.selectList(new QueryWrapper<ProjSurveyForm>().eq("is_deleted",0).eq("formlib_resource_unite_id", entity.getFormlibResourceUniteId()));
            if (surveyForm != null && !surveyForm.isEmpty()) {
                for (ProjSurveyForm item : surveyForm) {
                    // 将旧图片复制一份，生成到新表单中
                    if (entity.getFormPicturePaths() != null && !entity.getFormPicturePaths().isEmpty()) {
                        String[] strings = entity.getFormPicturePaths().split(",");
                        List<Long> longIds = new ArrayList<>();
                        for (String string : strings) {
                            try {
                                Long id = Long.valueOf(string);
                                longIds.add(id);
                            } catch (Exception e) {
                                continue;
                            }
                        }
                        List<Long> newImgLongIds = new ArrayList<>();
                        if (longIds != null && !longIds.isEmpty()) {
                            List<ProjProjectFile> oldFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", longIds));
                            if (oldFileList != null && !oldFileList.isEmpty()) {
                                for (ProjProjectFile itemes : oldFileList) {
                                    Long projectFileId = SnowFlakeUtil.getId();
                                    itemes.setProjectFileId(projectFileId);
                                    itemes.setProjectInfoId(item.getProjectInfoId());
                                    newImgLongIds.add(projectFileId);
                                    projProjectFileMapper.insert(itemes);
                                }
                                item.setFinishImgs(newImgLongIds.stream().map(ls -> ls.toString()).collect(Collectors.joining(",")));
                            }
                        }
                    }
                    projSurveyFormMapper.updateById(item);
                }
            }
        } catch (Exception e) {
            log.error("获取表单预览图片接口数据异常保存表单图片数据:{}", e.getMessage());
            return Result.fail("获取表单预览图片接口数据异常");
        }
        return Result.success(vo);
    }
}




