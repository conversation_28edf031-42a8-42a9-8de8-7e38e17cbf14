package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.msun.csm.common.enums.ExamineStatusEnum;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.dao.mapper.comm.ProjBusinessExamineLogMapper;
import com.msun.csm.model.resp.RejectReasonAndCount;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;

@Slf4j
@Service
public class ProjBusinessExamineLogServiceImpl implements ProjBusinessExamineLogService {
    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjBusinessExamineLogMapper projBusinessExamineLogMapper;

    @Override
    public boolean saveOperationLog(String businessType, Integer examineStatus, String examineOpinion, Long businessId) {
        ProjBusinessExamineLog projBusinessExamineLog = new ProjBusinessExamineLog();
        projBusinessExamineLog.setBusinessExamineLogId(SnowFlakeUtil.getId());
        projBusinessExamineLog.setBusinessId(businessId);
        projBusinessExamineLog.setBusinessType(businessType);
        projBusinessExamineLog.setExamineStatus(examineStatus);
        projBusinessExamineLog.setExamineOpinion(examineOpinion);

        Date now = new Date();
        Long currentUser = userHelper.getCurrentSysUserIdWithDefaultValue();
        projBusinessExamineLog.setIsDeleted(0);
        projBusinessExamineLog.setCreaterId(currentUser);
        projBusinessExamineLog.setCreateTime(now);
        projBusinessExamineLog.setUpdaterId(currentUser);
        projBusinessExamineLog.setUpdateTime(now);
        int insert = projBusinessExamineLogMapper.saveBusinessLog(projBusinessExamineLog);
        return 1 == insert;
    }

    @Override
    public List<ProjBusinessExamineLogResp> selectLogById(Long businessId) {
        return projBusinessExamineLogMapper.selectLogById(businessId, null);
    }

    @Override
    public List<ProjBusinessExamineLog> selectListByIds(List<Long> businessIds) {
        return projBusinessExamineLogMapper.selectListByIds(businessIds);
    }

    @Override
    public RejectReasonAndCount selectReasonAndCountById(Long businessId, ExamineStatusEnum examineStatusEnum) {
        List<ProjBusinessExamineLogResp> projBusinessExamineLogResps = projBusinessExamineLogMapper.selectLogById(businessId, examineStatusEnum.getExamineStatusCode());
        if (CollectionUtils.isEmpty(projBusinessExamineLogResps)) {
            return new RejectReasonAndCount(0, "");
        }
        StringBuilder rejectReason = new StringBuilder();
        for (int i = projBusinessExamineLogResps.size(); i >= 1; i--) {
            rejectReason.append("【第").append(i).append("次】").append(projBusinessExamineLogResps.get(i - 1).getOperateContent()).append("；");
        }
        return new RejectReasonAndCount(projBusinessExamineLogResps.size(), rejectReason.toString());
    }

    @Override
    public List<ProjBusinessExamineLog> getLogByBusinessAndStatus(String businessType, Integer examineStatus, Long businessId) {
        return projBusinessExamineLogMapper.getLogByBusinessAndStatus(businessType,examineStatus,businessId);
    }
}
