package com.msun.csm.service.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.api.yunying.OrderTypeEnums.CONSUMABLE;
import static com.msun.csm.common.enums.api.yunying.OrderTypeEnums.HARDWARE;
import static com.msun.csm.util.FilterUtil.distinctByKey;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.id.IdGenerator;
import com.msun.core.component.implementation.api.port.HospitalApi;
import com.msun.core.component.implementation.api.port.dto.SysManagerHospitalReminderDTO;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.cloudservice.CloudServiceKit;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.OpenStatusEnum;
import com.msun.csm.common.enums.ProjectViewModelEnum;
import com.msun.csm.common.enums.api.yunwei.CloudTypeEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.api.yunying.ProductSolutionEnum;
import com.msun.csm.common.enums.could.CloudPhaseTypeEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectSourceEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectUpgradationTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.common.model.dto.CloudServiceRenewalTimeDTO;
import com.msun.csm.common.model.dto.CloudServiceTimeResult;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.oldimsp.Customer;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.oldimsp.UserProjectRelation;
import com.msun.csm.dao.entity.proj.AddProjProjectConfigParamDTO;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjContractCustomInfo;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjCustomDetailInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProductArrangeRecord;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;
import com.msun.csm.dao.entity.proj.ProjProjectConfig;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjSpecialProductRecord;
import com.msun.csm.dao.entity.proj.ProjSurveyFineConfig;
import com.msun.csm.dao.entity.proj.ProjSyncApiLogs;
import com.msun.csm.dao.entity.proj.extend.ProjContractCustomInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjContractInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjCustomDetailInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjCustomInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjHospitalInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjHospitalVsProjectTypeExtend;
import com.msun.csm.dao.entity.proj.extend.ProjOrderInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjOrderProductExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProductArrangeRecordExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProductDeliverRecordExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProductEmpowerRecordExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProjectInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProjectMemberExtend;
import com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveLog;
import com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveRecord;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReviewTypeUser;
import com.msun.csm.dao.entity.projectreview.DictProjectReviewType;
import com.msun.csm.dao.entity.report.ReportCustomInfo;
import com.msun.csm.dao.entity.rule.RuleProductRuleConfig;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.dict.DictProductVsArrangeMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsDeliverMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsEmpowerMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspProjectMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.oldimsp.UserProjectRelationMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderHospitalRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderProductRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjCloudAccountPasswordMapper;
import com.msun.csm.dao.mapper.proj.ProjContractCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomDetailInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHardwareRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalOnlineDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductArrangeRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerAddRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductSupplementaryRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectClassificationScoreMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectOrderRelationMapper;
import com.msun.csm.dao.mapper.proj.ProjSpecialProductRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyFineConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjSyncApiLogsMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.dao.mapper.proj.flowable.KfSpecialApproveLogMapper;
import com.msun.csm.dao.mapper.proj.flowable.KfSpecialApproveRecordMapper;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewTypeUserMapper;
import com.msun.csm.dao.mapper.projectreview.DictProjectReviewTypeMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ReportCustomInfoMapper;
import com.msun.csm.dao.mapper.rule.RuleProductRuleConfigMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeHospitalReq;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeReq;
import com.msun.csm.feign.entity.yunying.req.YunOpenDTO;
import com.msun.csm.model.dto.ProductIdContrastDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.WorkOrderDisasterCloudDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamEachDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamEachResultDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamResultDTO;
import com.msun.csm.model.imsp.CustomerParamerDTO;
import com.msun.csm.model.imsp.ProjCustomInfoResp;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.imsp.SyncYunRenewalApplicationRenewDTO;
import com.msun.csm.model.imsp.SyncYunyingApplicationDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.resp.project.OrderCheckResp;
import com.msun.csm.model.yunying.ApproveLogCallbackArgs;
import com.msun.csm.model.yunying.ConvertContractDTO;
import com.msun.csm.model.yunying.CustomerDTO;
import com.msun.csm.model.yunying.HospitalInfoDTO;
import com.msun.csm.model.yunying.HospitalReminderDTO;
import com.msun.csm.model.yunying.HospitalReminderResult;
import com.msun.csm.model.yunying.HospitalReminderSwapDTO;
import com.msun.csm.model.yunying.OrderDTO;
import com.msun.csm.model.yunying.ProductDTO;
import com.msun.csm.model.yunying.ReplaceOrderProductDTO;
import com.msun.csm.model.yunying.ReplaceProductDTO;
import com.msun.csm.model.yunying.SyncContractDTO;
import com.msun.csm.model.yunying.UpdateCustomInfoDTO;
import com.msun.csm.model.yunying.YunyingPaysignageReq;
import com.msun.csm.model.yunying.resp.HospitalInfo;
import com.msun.csm.model.yunying.resp.SyncWorkOrderResp;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.oldimsp.OldCustomerInfoService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.OnlineStepService;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.ProjProjectConfigService;
import com.msun.csm.service.proj.applyorder.AsyncService;
import com.msun.csm.service.proj.disastercloud.ProjDisasterRecoveryInfoService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.util.FilterUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/26
 */
@Service
@Api("运营平台调用接口统一处理")
@Slf4j
public class ApiYunyingServiceImpl implements ApiYunyingService {
    public static final String TOKEN = "imsp";
    @Resource
    ProjOrderProductMapper projOrderProductMapper;
    @Resource
    private AsyncService asyncService;
    //合同客户
    @Resource
    private ProjContractCustomInfoMapper projContractCustomInfoMapper;
    //实施地客户
    @Resource
    private ProjCustomInfoMapper customInfoMapper;
    //实施地客户
    @Resource
    private ProjCustomDetailInfoMapper customDetailInfoMapper;
    //合同信息
    @Resource
    private ProjContractInfoMapper contractInfoMapper;
    //工单信息
    @Resource
    private ProjOrderInfoMapper orderInfoMapper;
    //工单产品信息
    @Resource
    private ProjOrderProductMapper orderProductMapper;
    //项目信息
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;
    //新旧项目对照信息
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    //项目成员
    @Resource
    private ProjProjectMemberMapper projectMemberMapper;
    //实施产品
    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;
    //部署产品
    @Resource
    private ProjProductArrangeRecordMapper productArrangeRecordMapper;
    //授权产品
    @Resource
    private ProjProductEmpowerRecordMapper productEmpowerRecordMapper;
    //实施产品字典
    @Resource
    private DictProductVsDeliverMapper dictDeliverMapper;
    //字典
    @Resource
    private DictProductVsArrangeMapper dictArrangeMapper;
    //授权产品字典
    @Resource
    private DictProductVsEmpowerMapper dictEmpowerMapper;
    //医院
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    //医院
    @Resource
    private ProjHospitalOnlineDetailMapper hospitalOnlineDetailMapper;
    //医院项目类型
    @Resource
    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;
    //人员
    @Resource
    private SysUserMapper sysUserMapper;
    //部门
    @Resource
    private SysDeptMapper sysDeptMapper;
    //老系统user表
    @Resource
    private ImspSysUserMapper imspSysUserMapper;
    //老系统项目成员表
    @Resource
    private UserProjectRelationMapper userProjectRelationMapper;
    @Resource
    private ProjHospitalInfoService projHospitalInfoService;
    //产品规则表
    @Resource
    private RuleProductRuleConfigMapper productRuleConfigMapper;
    //老系统客户信息表
    @Resource
    private OldCustomerInfoService oldCustomerInfoService;
    @Resource
    private ExceptionMessageService exceptionMessageService;
    //老系统项目信息表
    @Resource
    private ProjSyncApiLogsMapper syncApiLogsMapper;
    @Resource
    private YunyingFeignClient yunyingFeignClient;
    @Resource
    private SendMessageService sendMessageService;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private ProjSpecialProductRecordMapper specialProductRecordMapper;
    @Resource
    private ProjApplyOrderMapper applyOrderMapper;
    @Resource
    private ProjCustomCloudServiceMapper customCloudServiceMapper;
    @Resource
    private ProjProjectOrderRelationMapper projectOrderRelationMapper;
    @Resource
    private ApiYunyingService apiYunyingService;
    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;
    @Resource
    private ReportCustomInfoMapper reportCustomInfoMapper;

    @Resource
    private ProjDisasterRecoveryInfoService disasterRecoveryInfoService;
    @Resource
    private ProjSurveyFormMapper surveyFormMapper;
    @Resource
    private ProjSurveyPlanMapper surveyPlanMapper;
    @Resource
    private ProjSurveyReportMapper surveyReportMapper;
    @Resource
    private ProjEquipRecordMapper equipRecordMapper;
    @Resource
    private ProjHardwareRecordMapper hardwareRecordMapper;
    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;
    @Resource
    private ProjOnlineStepMapper onlineStepMapper;
    @Resource
    private ProjProductEmpowerAddRecordMapper productEmpowerAddRecordMapper;
    @Resource
    private ProjProductSupplementaryRecordMapper productSupplementaryRecordMapper;
    @Resource
    private ProjProjectClassificationScoreMapper projectClassificationScoreMapper;
    @Resource
    private ProjThirdInterfaceMapper thirdInterfaceMapper;
    @Resource
    private ProjApplyOrderHospitalRecordMapper applyOrderHospitalRecordMapper;
    @Resource
    private ProjApplyOrderProductRecordMapper applyOrderProductRecordMapper;
    @Resource
    private ProjCloudAccountPasswordMapper cloudAccountPasswordMapper;
    @Resource
    private OnlineStepService onlineStepService;
    @Resource
    private ProjProductBacklogMapper productBacklogMapper;
    @Resource
    private ProjProductConfigMapper productConfigMapper;
    @Resource
    private ProjProductTaskMapper productTaskMapper;
    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;
    @Resource
    private ImspProjectMapper imspProjectMapper;

    @Resource
    private ProjProjectConfigService projectConfigService;

    @Resource
    private HospitalApi hospitalApi;

    @Resource
    private ProjSurveyFineConfigMapper projSurveyFineConfigMapper;
    @Resource
    private CommonService commonService;

    @Autowired
    private KfSpecialApproveLogMapper kfSpecialApproveLogMapper;

    @Autowired
    private KfSpecialApproveRecordMapper kfSpecialApproveRecordMapper;

    @Autowired
    private ConfigProjectReviewTypeUserMapper configProjectReviewTypeUserMapper;
    @Autowired
    private DictProjectReviewTypeMapper dictProjectReviewTypeMapper;

    /**
     * 获取项目类型
     *
     * @param order           工单
     * @param syncContractDTO 合同
     * @return 项目类型编码
     */
    private static Integer getProjectType(OrderDTO order, SyncContractDTO syncContractDTO) {
        //自动创建的项目，以页面选择参数为准
        if (syncContractDTO.getProjectType() != null) {
            return syncContractDTO.getProjectType();
        }
        // 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
        //运营平台已经按照解决方案分类
        int solutionType = order.getProduct().get(0).getPemCusSolType();
        //5481-云健康升级单体0个点   5482-云健康升级单体1个点  5483-云健康升级单体3个点
        //5484-云健康升级区域
        boolean singleProduct = order.getProduct().stream().filter(p -> p.getProductId() == 5481 || p.getProductId() == 5482 || p.getProductId() == 5483).findFirst().isPresent();
        boolean regionProduct = order.getProduct().stream().filter(p -> p.getProductId() == 5484).findFirst().isPresent();
        if (!singleProduct && !regionProduct && ProductSolutionEnum.YUN_JIAN_KANG_SHENG_JI.getCode() == solutionType) {
            return syncContractDTO.getProjectType();
        } else if (ProductSolutionEnum.DAN_TI.getCode() == solutionType || ProductSolutionEnum.JI_CENG_DAN_TI.getCode() == solutionType || (ProductSolutionEnum.YUN_JIAN_KANG_SHENG_JI.getCode() == solutionType && singleProduct)) {
            return ProjectTypeEnums.SINGLE.getCode();
        } else if (ProductSolutionEnum.QU_YU.getCode() == solutionType || ProductSolutionEnum.YI_GONG_TI.getCode() == solutionType || ProductSolutionEnum.QUAN_XIAN_YU.getCode() == solutionType || (ProductSolutionEnum.YUN_JIAN_KANG_SHENG_JI.getCode() == solutionType && regionProduct)) {
            return ProjectTypeEnums.REGION.getCode();
        } else if (ProductSolutionEnum.SHI_PING_TAI.getCode() == solutionType) {
            return ProjectTypeEnums.REGION.getCode();
        }
        throw new CustomException("未查询到对应的项目类型.");
    }

    /**
     * 同步合同信息
     *
     * @param syncContractDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result syncWorkOrder(SyncContractDTO syncContractDTO) {
        sysOperLogService.apiOperLogInsert(syncContractDTO, "syncWorkOrder-参数", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
        //参数校验
        OrderCheckResp checkResp = checkContractOrder(syncContractDTO);
        if (StringUtils.isNotEmpty(checkResp.getErrMsg())) {
            log.error("syncWorkOrder 参数校验失败:{}", checkResp.getErrMsg());
            return Result.fail(checkResp.getErrMsg());
        }
        SysUser projectManager = checkResp.getProjectManager();
        //项目经理id对照  (小硬件、耗材 无需校验项目经理、实施团队，给定默认值)
        Long managerId;
        if (ObjectUtil.isNotEmpty(projectManager)) {
            managerId = projectManager.getSysUserId();
        } else {
            managerId = -1L;
        }
        Date now = new Date();
        //保存合同信息、工单信息、产品信息
        //合同客户(甲方客户)信息
        ProjContractCustomInfo contractCustomInfo = projContractCustomInfoMapper.selectByYyPartaId(ObjectUtils.isNotEmpty(syncContractDTO.getPrincipalCustomer().getCustomerId()) ? syncContractDTO.getPrincipalCustomer().getCustomerId().longValue() : syncContractDTO.getCustomerInfo().getCustomerId().longValue());
        ProjContractCustomInfoExtend contractCustomInfoExt = new ProjContractCustomInfoExtend(managerId, now, syncContractDTO);
        if (ObjectUtil.isEmpty(contractCustomInfo)) {
            Long contractCustomInfoId = SnowFlakeUtil.getId();
            contractCustomInfoExt.setContractCustomInfoId(contractCustomInfoId);
            projContractCustomInfoMapper.insert(contractCustomInfoExt);
        } else {
            contractCustomInfoExt.setCreateTime(null);
            contractCustomInfoExt.setContractCustomInfoId(contractCustomInfo.getContractCustomInfoId());
            projContractCustomInfoMapper.updateByPrimaryKeySelective(contractCustomInfoExt);
        }
        //实施地客户信息
        CustomerDTO customerDTO = syncContractDTO.getCustomerInfo();
        ProjCustomInfo customInfo = customInfoMapper.selectByYyCustomerId(syncContractDTO.getCustomerInfo().getCustomerId().longValue());
        ProjCustomDetailInfo customDetailInfo;
        ProjCustomInfoExtend customInfoExtend = new ProjCustomInfoExtend(syncContractDTO, customerDTO, managerId, now);
        //-1 自动续期 0 无有效数据
        if (customInfoExtend.getCustomTeamId() != -1 && customInfoExtend.getCustomTeamId() != 0) {
            SysDept dept = sysDeptMapper.selectYunYingId(customInfoExtend.getCustomTeamId());
            if (dept != null) {
                customInfoExtend.setCustomHeadId(Long.valueOf(dept.getDeptLeaderYunyingId()));
                customInfoExtend.setCustomDeptId(dept.getPid());
            }
        }
        //实施地客户详情数据
        ProjCustomDetailInfoExtend customDetailInfoExtend = new ProjCustomDetailInfoExtend(customerDTO, managerId, now);
        if (ObjectUtil.isEmpty(customInfo)) {
            Long customInfoId = SnowFlakeUtil.getId();
            customInfoExtend.setCustomInfoId(customInfoId);
            //判断是否是电子销售
            SysDept sysDept = sysDeptMapper.selectYunYingId(syncContractDTO.getSaleOrgId().longValue());
            if (sysDept != null && "electronicSales".equals(sysDept.getDeptCategory())) {
                customInfoExtend.setTelesalesFlag(1);
            } else {
                customInfoExtend.setTelesalesFlag(0);
            }
            customInfoMapper.insert(customInfoExtend);
            customInfo = customInfoExtend;
            customDetailInfoExtend.setCustomDetailInfoId(customInfoId);
            customDetailInfoExtend.setCustomInfoId(customInfoId);
            customDetailInfoMapper.insert(customDetailInfoExtend);
            customDetailInfo = customDetailInfoExtend;
        } else {
            //无效数据无需更新
            if (customInfoExtend.getCustomTeamId() == -1 || customInfoExtend.getCustomTeamId() == 0) {
                customInfoExtend.setCustomTeamId(null);
                customInfoExtend.setCustomDeptId(null);
                customInfoExtend.setCustomHeadId(null);
            }
            customInfoExtend.setCustomInfoId(customInfo.getCustomInfoId());
            customInfoExtend.setCreateTime(null);
            customInfoMapper.updateByPrimaryKeySelective(customInfoExtend);
            customDetailInfo = customDetailInfoMapper.selectById(customInfo.getCustomInfoId());
            customDetailInfoExtend.setCustomDetailInfoId(customDetailInfo.getCustomDetailInfoId());
            customDetailInfoExtend.setCreateTime(null);
            customDetailInfoMapper.updateByPrimaryKeySelective(customDetailInfoExtend);
        }
        //保存合同信息
        ProjContractInfo contractInfo = contractInfoMapper.selectByYyContractId(syncContractDTO.getContractNum());
        Long contractInfoId;
        if (ObjectUtil.isEmpty(contractInfo)) {
            contractInfoId = SnowFlakeUtil.getId();
            ProjContractInfoExtend contractInfoExtend = new ProjContractInfoExtend(syncContractDTO, contractInfoId, managerId, now);
            // 默认非正式合同
            contractInfoExtend.setPaySignage(-1);
            contractInfoMapper.insert(contractInfoExtend);
        } else {
            contractInfoId = contractInfo.getContractInfoId();
        }
        ProjCustomInfo finalCustomInfo = customInfo;
        ProjCustomDetailInfo finalCustomDetailInfo = customDetailInfo;
        Long finalContractInfoId = contractInfoId;
        //工单-虽然是循环但是List内只有一条数据
        OrderDTO order = syncContractDTO.getDatas().get(0);
        // 设置项目类型
        Integer projectType = getProjectType(order, syncContractDTO);
        Long customInfoId = finalCustomInfo.getCustomInfoId();
        //提前创建项目ID-projectInfoId
        Long projectInfoId = SnowFlakeUtil.getId();
        Boolean createProject = true;
        if (order.getOrderType() != OrderTypeEnums.SOFTWARE.getCode().intValue() && order.getOrderType() != OrderTypeEnums.PURCHASE_SOFTWARE.getCode().intValue()) {
            projectInfoId = Long.valueOf(order.getWorkNum());
            createProject = false;
        }
        //工单信息
        Long orderInfoId = SnowFlakeUtil.getId();
        ProjOrderInfoExtend projOrderInfoExtend = new ProjOrderInfoExtend(order, orderInfoId, managerId, now, syncContractDTO.getCustomerInfo().getCustomerId().longValue());
        projOrderInfoExtend.setContractInfoId(finalContractInfoId);
        orderInfoMapper.insert(projOrderInfoExtend);
        //工单产品
        List<ProjOrderProduct> orderProducts = new ArrayList<>();
        List<Long> productIds = new ArrayList<>();
        for (ProductDTO product : order.getProduct()) {
            Long orderProductId = SnowFlakeUtil.getId();
            productIds.add(product.getProductId().longValue());
            ProjOrderProductExtend orderProductExtend = new ProjOrderProductExtend(product, orderProductId, orderInfoId, projectInfoId, managerId, now);
            orderProductExtend.setProductSubscribeTime(now);
            //工单产品-合同信息
            orderProductExtend.setYyContractId(syncContractDTO.getContractNum());
            orderProducts.add(orderProductExtend);
        }
        orderProductMapper.batchInsert(orderProducts);
        // 设置项目类型
//        Integer projectType = getProjectType(order, syncContractDTO);
        // 云容灾工单处理
        if (order.getOrderType() == OrderTypeEnums.DISASTER_RECOVERY.getCode().intValue()) {
            WorkOrderDisasterCloudDTO workOrderDisasterCloudDTO = WorkOrderDisasterCloudDTO.builder().orderProducts(orderProducts).contractCustomInfoExt(contractCustomInfoExt).customInfo(customInfo).contractInfoId(contractInfoId).projectType(projectType).order(order).disaststerRenewal(syncContractDTO.getDisaststerRenewal()).orderInfoId(orderInfoId).syncContractDTO(syncContractDTO).build();
            disasterRecoveryInfoService.syncWorkOrderHandleDisasterCloud(workOrderDisasterCloudDTO);
        }
        //如果不是软件工单或者外采工单 不需要创建项目数据
        if (!createProject) {
            return Result.success();
        }
        handleProducts(productIds, customInfoId, projectInfoId, managerId, now);
        //创建项目
        ProjProjectInfoExtend projectInfoExtend = new ProjProjectInfoExtend(order, projectInfoId, customInfoId, orderInfoId, managerId, now);
        //类型老换新/新客户、项目首期his/非his
        List<RuleProductRuleConfig> hisProducts = productRuleConfigMapper.findHisFlag();
        boolean hisFlag = false;
        for (RuleProductRuleConfig hisProduct : hisProducts) {
            if (productIds.contains(hisProduct.getYyProductId())) {
                hisFlag = true;
                break;
            }
        }
        projectInfoExtend.setHisFlag(hisFlag ? 1 : 0);
        List<RuleProductRuleConfig> cloudProducts = productRuleConfigMapper.findCloudFlag();
        boolean cloudFlag = false;
        for (RuleProductRuleConfig cloudProduct : cloudProducts) {
            if (productIds.contains(cloudProduct.getYyProductId())) {
                cloudFlag = true;
                break;
            }
        }
        //是否包含分院模式产品
        List<RuleProductRuleConfig> branchHospitalProducts = productRuleConfigMapper.findBranchHospitalFlag();
        boolean branchHospitalFlag = false;
        for (RuleProductRuleConfig branchHospitalProduct : branchHospitalProducts) {
            if (productIds.contains(branchHospitalProduct.getYyProductId())) {
                branchHospitalFlag = true;
                break;
            }
        }
        if (syncContractDTO.getUpgradationType() != null) {
            //自动创建的项目，以页面选择参数为准
            projectInfoExtend.setUpgradationType(syncContractDTO.getUpgradationType());
        } else {
            //即是云健康升级并且包含his产品 才是老换新，其他都是新上线
            projectInfoExtend.setUpgradationType(cloudFlag && hisFlag ? ProjectUpgradationTypeEnums.LHX.getCode() : ProjectUpgradationTypeEnums.XKH.getCode());
        }
        //项目类型  解决方案
        projectInfoExtend.setProjectType(projectType);
        projectInfoMapper.insert(projectInfoExtend);
        try {
            log.info("项目派工时新增项目配置信息，项目ID={}，运营平台调用参数={}", projectInfoExtend.getProjectInfoId(), JSON.toJSONString(syncContractDTO));
            // 根据项目ID获取项目配置
            ProjProjectConfig projectConfig = projectConfigService.getConfigByProjectInfoId(projectInfoExtend.getProjectInfoId());
            // 项目配置不存在时进行新增操作
            if (projectConfig == null) {
                AddProjProjectConfigParamDTO addParamDTO = new AddProjProjectConfigParamDTO();
                addParamDTO.setViewModel(ProjectViewModelEnum.PROJECT_PLAN);
                addParamDTO.setProjectInfoId(projectInfoExtend.getProjectInfoId());
                projectConfigService.addProjectConfig(addParamDTO);
            }
        } catch (Exception e) {
            log.error("新增项目配置，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        //如果不是首期项目，并且包含分院模式，需要查询原来客户下对应项目的部署产品，实施产品，授权产品全部加到这个项目下
        SyncWorkOrderResp syncWorkOrderResp = new SyncWorkOrderResp();
        syncWorkOrderResp.setBranchHospitalFlag(0);
        if (branchHospitalFlag && !hisFlag) {
            this.copyProducts(projectInfoExtend, managerId, now);
            syncWorkOrderResp.setBranchHospitalFlag(1);
        }
        //老项目新项目关系
        TmpProjectNewVsOld tmpProjectNewVsOld = getTmpProjectNewVsOld(syncContractDTO, projectInfoId, customInfoId);
        tmpProjectNewVsOldMapper.insert(tmpProjectNewVsOld);
        //项目成员表-默认添加项目经理以及团队成员
        List<SysUser> teamMembers = sysUserMapper.selectUserByYYDeptId(order.getTeamId());
        List<ProjProjectMember> projectMembers = new ArrayList<>();
        for (SysUser user : teamMembers) {
            Long pmInfoId = SnowFlakeUtil.getId();
            ProjProjectMemberExtend pm = new ProjProjectMemberExtend(order, pmInfoId, projectInfoId, user, managerId, now);
            projectMembers.add(pm);
        }
        projectMemberMapper.batchInsert(projectMembers);
        //老项目同步添加成员
        List<Long> userYYIdList = teamMembers.stream().map(sysUser -> Long.parseLong(sysUser.getUserYunyingId())).collect(Collectors.toList());
        QueryWrapper imspUserWrapper = new QueryWrapper();
        imspUserWrapper.in("user_yunying_id", userYYIdList);
        imspUserWrapper.ne("status", "DELETED");
        List<ImspSysUser> imspSysUserList = imspSysUserMapper.selectList(imspUserWrapper);
        List<UserProjectRelation> uprList = imspSysUserList.stream().map(imspSysUser -> {
            UserProjectRelation upr = new UserProjectRelation();
            upr.setProjectId(syncContractDTO.getOldProjectId());
            upr.setUserId(imspSysUser.getUserId());
            upr.setCreateTime(now);
            upr.setUpdateTime(now);
            return upr;
        }).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(uprList)) {
            throw new CustomException("项目成员为空，无法创建项目");
        }
        userProjectRelationMapper.insertBatch(uprList);
        //添加医院信息-主键id保证跟老项目一致。根据项目类型和客户id查询主院
        ProjHospitalInfo hospitalInfo = getMainHospitalInfo(projectInfoExtend.getProjectType(), customInfoId);
        Long hospitalInfoId;
        if (ObjectUtil.isEmpty(hospitalInfo)) {
            //新增医院
            hospitalInfoId = syncContractDTO.getOldCustomerInfoId();
            ProjHospitalInfoExtend hospitalInfoExtend = new ProjHospitalInfoExtend(hospitalInfoId, customInfoId, finalCustomInfo, finalCustomDetailInfo, managerId, now);
            //增加字段
            hospitalInfoExtend.setDictHospitalLevelId(ObjectUtil.isEmpty(customerDTO.getHospitalLevel()) ? -1L : Long.valueOf(customerDTO.getHospitalLevel()));
            hospitalInfoMapper.insert(hospitalInfoExtend);
            //添加项目类型医院对照信息
            Long hospitalVsProjectTypeId = SnowFlakeUtil.getId();
            ProjHospitalVsProjectType hospitalVsProjectType = new ProjHospitalVsProjectTypeExtend(hospitalVsProjectTypeId, projectInfoExtend.getProjectType(), hospitalInfoId, managerId, now);
            hospitalVsProjectType.setCustomInfoId(customInfoId);
            hospitalVsProjectTypeMapper.insert(hospitalVsProjectType);
        } else {
            hospitalInfo.setUpdateTime(now);
            hospitalInfoMapper.updateById(hospitalInfo);
            hospitalInfoId = hospitalInfo.getHospitalInfoId();
        }
        //同步老数据库customerInfo表数据
        OldCustomerInfo oldCustomerInfo = new OldCustomerInfo();
        oldCustomerInfo.setId(syncContractDTO.getOldCustomerInfoId());
        oldCustomerInfo.setCsmHospitalInfoId(hospitalInfoId);
        oldCustomerInfoService.updateById(oldCustomerInfo);
        //小硬件、耗材单独处理-自动发货给项目经理发消息-因为老项已发新项目中暂时不用处理
        // 新系统中的实施地客户下的所有医院 除主院外，需要复制一份数据到老系统中
        copyHospitalForImsp(customInfoId, projectInfoExtend.getProjectType(), syncContractDTO.getOldProjectId(), syncContractDTO.getOldCustomerId());
        //首期项目-客户统计表处理数据
        if (hisFlag) {
            ReportCustomInfo reportCustomInfo = reportCustomInfoMapper.selectByCustomInfoAndType(customInfoId, finalCustomInfo.getCustomName(), projectInfoExtend.getProjectType());
            if (ObjectUtil.isEmpty(reportCustomInfo)) {
                // 1. 交付平台创建临时测试项目，无需写入report表，例如：木兰人民、木兰中医
                if (!projOrderInfoExtend.getDeliveryOrderNo().contains("TMP")) {
                    //新增
                    ReportCustomInfo saveData = new ReportCustomInfo();
                    saveData.setReportCustomInfoId(SnowFlakeUtil.getId());
                    saveData.setCustomInfoId(customInfoId);
                    saveData.setCustomName(finalCustomInfo.getCustomName());
                    saveData.setCustomType(projectInfoExtend.getProjectType());
                    saveData.setProjectInfoId(projectInfoId);
                    saveData.setTelesalesFlag(finalCustomInfo.getTelesalesFlag());
                    //新增数据都是新客户 老换新的设置为老客户
                    if (projectInfoExtend.getUpgradationType() != 2) {
                        saveData.setUpgradationType(3);
                    } else {
                        saveData.setUpgradationType(2);
                    }
                    saveData.setYyCustomId(finalCustomInfo.getYyCustomerId());
                    saveData.setProvinceId(customerDTO.getProvinceId().longValue());
                    saveData.setCtiyId(customerDTO.getCityId().longValue());
                    saveData.setTownId(customerDTO.getAreaId().longValue());
                    saveData.setSaleCenterId(customerDTO.getSaleCenterId());
                    saveData.setDeptId(ObjectUtil.isNotNull(customInfoExtend.getCustomDeptId()) ? customInfoExtend.getCustomDeptId().longValue() : -1L);
                    saveData.setServiceOrgId(ObjectUtil.isNotNull(customInfoExtend.getCustomTeamId()) ? customInfoExtend.getCustomTeamId().longValue() : -1L);
                    reportCustomInfoMapper.insert(saveData);
                }
            } else {
                //更新
                // 1. 更新客户规则：更新范围，交付客户ID、客户名称、单体/区域属性、项目ID（若当前客户+客户类型，已存在关联首期项目时，
                // 不更新首期项目ID，写入备注：需核实首期项目关联ID）
                reportCustomInfo.setCustomInfoId(customInfoId);
                reportCustomInfo.setCustomName(finalCustomInfo.getCustomName());
                reportCustomInfo.setCustomType(projectInfoExtend.getProjectType());
                if (reportCustomInfo.getProjectInfoId() != null && !"".equals(reportCustomInfo.getProjectInfoId())) {
                    reportCustomInfo.setRemark("需核实首期项目关联ID");
                } else {
                    reportCustomInfo.setProjectInfoId(projectInfoId);
                }
                //更新无需处理属性
//                reportCustomInfo.setUpgradationType(3);
                reportCustomInfo.setTelesalesFlag(finalCustomInfo.getTelesalesFlag());
                reportCustomInfoMapper.updateById(reportCustomInfo);
            }
        }
        //医院id赋值
        syncWorkOrderResp.setHospitalInfoId(hospitalInfoId);
        //项目派工发送消息
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId));
        Integer type = projProjectInfo.getUpgradationType();
        String typeName;
        if (Integer.valueOf("1").equals(type)) {
            typeName = "老换新";
        } else {
            typeName = "新客户";
        }
        SysDept sysDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>().eq("dept_yunying_id", projProjectInfo.getProjectTeamId()));
        SysUser sysUser2 = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectInfo.getProjectLeaderId()));
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projProjectInfo.getProjectInfoId());
        //组装产品列表信息
        List<ProductInfo> allList = projOrderProductMapper.findAllList(productInfoDTO);
        List<ProductInfo> resList = allList.stream().filter(v -> !com.msun.csm.util.StringUtils.isEmpty(v.getProductName())).collect(Collectors.toList());
        try {
            // 首期项目发送消息
            if (projProjectInfo.getHisFlag() == 1) {
                // xxx项目（老换新）已派工，实施团队xxx，项目经理xxx，本次上线xx个产品，请您知晓！
                String content = projProjectInfo.getProjectName() + "(" + typeName + ")" + "已派工，实施团队" + sysDept.getDeptName() + "，项目经理" + sysUser2.getUserName() + "，本次上线" + resList.size() + "个产品，请您知晓！";
                MessageParam messageParam = new MessageParam();
                messageParam.setProjectInfoId(projectInfoId);
                messageParam.setContent(content);
                messageParam.setMessageTypeId(4002L);
                messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
                sendMessageService.sendMessage(messageParam);
            }
        } catch (Exception e) {
            log.error("派工时发企业微信消息异常.errMsg={}", e);
        }
        try {
            //运营平台推送派工提示，涉及调研超期会产生罚单问题
            sendMessage(projProjectInfo, Long.valueOf(sysDept.getDeptLeaderYunyingId()), typeName, sysUser2.getUserName(), now);
        } catch (Exception e) {
            log.error("发送消息失败：" + e.getMessage());
        }
        return Result.success(syncWorkOrderResp);
    }

    /**
     * 运营平台推送派工提示，涉及调研超期会产生罚单问题
     *
     * @param projProjectInfo
     * @param deptLeaderId
     * @param typeName
     * @param projectLeaderName
     * @param currentTime
     */
    private void sendMessage(ProjProjectInfo projProjectInfo, Long deptLeaderId, String typeName, String projectLeaderName, Date currentTime) {
        ProjSurveyFineConfig projSurveyFineConfig = projSurveyFineConfigMapper.selectOne(new QueryWrapper<ProjSurveyFineConfig>());
        //开启了项目调研监控才发送消息提醒
        if (ObjectUtil.isNotEmpty(projSurveyFineConfig) && projSurveyFineConfig.getOpenFlag() == 1) {
            //给分公司经理发送消息提醒
            MessageParam deptleaderMessageParam = new MessageParam();
            deptleaderMessageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            List<Long> deptLeaderUserIds = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(deptLeaderId)) {
                SysUser deptLeader = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("user_yunying_id", String.valueOf(deptLeaderId)));
                deptLeaderUserIds.add(deptLeader.getSysUserId());
            }
            deptleaderMessageParam.setSysUserIds(deptLeaderUserIds);
            deptleaderMessageParam.setMessageToCategory(MsgToCategory.SYS.getCode());
            deptleaderMessageParam.setMessageTypeId(3001L);
            deptleaderMessageParam.setTitle("项目派工");
            deptleaderMessageParam.setContent(projProjectInfo.getProjectName() + "(" + typeName + ")已派工，项目经理：" + projectLeaderName + "，项目需在" + projSurveyFineConfig.getCompleteDay().toString() + "天内完成调研，超期会产生罚单，请知悉！");
            log.info("项目派工，发送企业微信通知分公司经理 , {}", JSONUtil.toJsonStr(deptleaderMessageParam));
            try {
                sendMessageService.sendMessage(deptleaderMessageParam, true);
            } catch (Exception e) {
                e.printStackTrace();
            }
            //给项目经理发送消息提醒
            MessageParam projLeaderMessageParam = new MessageParam();
            projLeaderMessageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            projLeaderMessageParam.setMessageToCategory(MsgToCategory.TO_PRO_MGR.getCode());
            projLeaderMessageParam.setMessageTypeId(2001L);
            projLeaderMessageParam.setTitle("项目派工");
            StringBuilder content = new StringBuilder();
            if (projProjectInfo.getHisFlag() == 1) {
                content.append(projProjectInfo.getProjectName()).append("(").append(typeName).append(")已派工，请于").append(projSurveyFineConfig.getWarningDay().toString()).append("天内（").append(DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:MM:ss")).append(" 至 ").append(DateFormatUtils.format(DateUtil.offsetDay(currentTime, projSurveyFineConfig.getWarningDay()), "yyyy-MM-dd HH:MM:ss")).append("）完成调研，并于").append(projSurveyFineConfig.getCompleteDay().toString()).append("天内（").append(DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:MM:ss")).append(" 至 ").append(DateFormatUtils.format(DateUtil.offsetDay(currentTime, projSurveyFineConfig.getCompleteDay()), "yyyy-MM-dd HH:MM:ss")).append("）完成通过调研审核！");
            } else {
                content.append(projProjectInfo.getProjectName()).append("(").append(typeName).append(")已派工，请于").append(projSurveyFineConfig.getCompleteDay().toString()).append("天内（").append(DateFormatUtils.format(currentTime, "yyyy-MM-dd HH:MM:ss")).append(" 至 ").append(DateFormatUtils.format(DateUtil.offsetDay(currentTime, projSurveyFineConfig.getCompleteDay()), "yyyy-MM-dd HH:MM:ss")).append("）完成调研！");
            }
            projLeaderMessageParam.setContent(content.toString());
            log.info("项目派工，发送企业微信通知项目经理 , {}", JSONUtil.toJsonStr(projLeaderMessageParam));
            sendMessageService.sendMessage(projLeaderMessageParam, true);
        }
    }

    public Result<List<HospitalReminderResult>> setHospitalReminder(HospitalReminderSwapDTO dto) {
        // 记录结果
        List<HospitalReminderResult> resultData = CollUtil.newArrayList();
        for (HospitalReminderDTO hospitalReminderDTO : dto.getHospitalReminderDTOS()) {
            Result<String> result;
            // 返回结果
            try {
                result = setHospitalReminder(hospitalReminderDTO);
            } catch (Throwable e) {
                log.error("云资源设置白名单异常. message: {}, e=", e.getMessage(), e);
                result = Result.fail(e.getMessage());
            }
            // 设置返回值
            HospitalReminderResult reminderResult = HospitalReminderResult.builder().message(result.getMsg()).yyCustomerId(hospitalReminderDTO.getYyCustomerId()).solutionType(hospitalReminderDTO.getSolutionType()).whiteListFlag(hospitalReminderDTO.getWhiteListFlag()).build();
            resultData.add(reminderResult);
            if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
                log.warn("云资源白名单设置异常. param: {}, result: {}", hospitalReminderDTO, result);
                reminderResult.setSuccess(false);
            } else {
                reminderResult.setSuccess(true);
            }
        }
        log.info("云资源白名单设置返回值. result: {}", resultData);
        return Result.success(resultData);
    }

    public Result<String> setHospitalReminder(HospitalReminderDTO dto) {
        // 判断项目类型
        Integer projectType = CommonService.getProjectType(dto.getSolutionType());
        log.info("通过解决方案: {}, 判断项目类型为: {}", dto.getSolutionType(), projectType);
        // 查询实施地客户
        ProjCustomInfo customInfo = customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq("yy_customer_id", dto.getYyCustomerId()));
        if (ObjectUtil.isEmpty(customInfo)) {
            log.error("未查询到客户信息");
            throw new CustomException("未查询到客户信息.");
        }
        log.info("获取到客户信息. {}", JSONUtil.toJsonStr(customInfo));
        // 查询已开通的医院
        List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectList(new QueryWrapper<ProjHospitalVsProjectType>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("project_type", projectType));
        if (CollUtil.isEmpty(hospitalVsProjectTypes)) {
            log.error("客户下未查询到医院信息");
            throw new CustomException("未查询到医院信息.");
        }
        log.info("医院与项目类型对照关系. {}", JSONUtil.toJsonStr(hospitalVsProjectTypes));
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().in("hospital_info_id", hospitalVsProjectTypes.stream().map(ProjHospitalVsProjectType::getHospitalInfoId).collect(Collectors.toList())));
        if (CollUtil.isEmpty(hospitalInfoList)) {
            log.error("未查询到医院信息");
            throw new CustomException("未查询到医院信息.");
        }
        log.info("查询到医院信息. {}", JSONUtil.toJsonStr(hospitalInfoList));
        hospitalInfoList = CommonService.getOpenHospitalInfo(hospitalInfoList);
        log.info("查询到开通医院. count: {}, 请求参数: {}", hospitalInfoList.size(), dto);
        if (CollUtil.isEmpty(hospitalInfoList)) {
            log.error("未查询到开通的医院");
            throw new CustomException("未查询到医院信息.");
        }
        // 区分域名, 分别进行调用
        Map<String, List<ProjHospitalInfo>> hospitalMap = MapUtil.newHashMap();
        Map<String, ProjHospitalInfo> domainHolderHospitalMap = MapUtil.newHashMap();
        for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
            if (!hospitalMap.containsKey(hospitalInfo.getCloudDomain())) {
                hospitalMap.put(hospitalInfo.getCloudDomain(), CollUtil.newArrayList(hospitalInfo));
            } else {
                hospitalMap.get(hospitalInfo.getCloudDomain()).add(hospitalInfo);
            }
        }
        hospitalMap.keySet().forEach(e -> domainHolderHospitalMap.put(e, hospitalMap.get(e).get(0)));
        // 调用结果记录
        StringBuilder resultRecord = new StringBuilder();
        // 记录异常结果
        StringBuilder errorRecord = new StringBuilder();
        domainHolderHospitalMap.keySet().forEach(f -> {
            List<ProjHospitalInfo> hospitalInfos = hospitalMap.get(f);
            // 刷新缓存
            ProjHospitalInfo hospitalInfo = domainHolderHospitalMap.get(f);
            commonService.refreshDomain(hospitalInfo);
            SysManagerHospitalReminderDTO reminderDTO = new SysManagerHospitalReminderDTO();
            // 拼接参数
            reminderDTO.setHospitalIdList(hospitalInfos.stream().map(ProjHospitalInfo::getCloudHospitalId).collect(Collectors.toList()));
            reminderDTO.setWhiteFlag(StrUtil.toString(dto.getWhiteListFlag()));
            reminderDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
            reminderDTO.setHisOrgId(hospitalInfo.getOrgId());
            // 调用api
            ResponseResult<String> responseResult = null;
            StringBuilder tmpRecord = new StringBuilder();
            // 接口异常标识
            boolean thrErrFlag = false;
            String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.SPACE);
            try {
                sysOperLogService.apiOperLogInsert(reminderDTO, "云资源白名单设置-入参", uuid, Log.LogOperType.ADD.getCode());
                responseResult = hospitalApi.updateServiceWhite(reminderDTO);
                sysOperLogService.apiOperLogInsert(responseResult, "云资源白名单设置-出参", uuid, Log.LogOperType.ADD.getCode());
            } catch (Throwable e) {
                sysOperLogService.apiOperLogInsert(e, "云资源白名单设置-异常", uuid, Log.LogOperType.ADD.getCode());
                thrErrFlag = true;
                tmpRecord.append("调用交付接口服务异常, 接口报错: ").append(e);
                errorRecord.append(tmpRecord);
                log.error("调用交付接口服务异常, 接口报错. message: {}, e=", e.getMessage(), e);
            }
            if (!thrErrFlag) {
                // 处理结果
                if (ObjectUtil.isEmpty(responseResult) || !responseResult.getSuccess()) {
                    tmpRecord.append("调用交付接口服务异常. 请求参数: ").append(reminderDTO).append(". 请求结果: ").append(responseResult);
                    errorRecord.append(tmpRecord);
                    log.info("调用交付接口服务异常. {}", tmpRecord);
                } else {
                    tmpRecord.append("请求参数: ").append(reminderDTO).append(". 请求结果: ").append(responseResult);
                    log.info("调用交付接口服务成功. 请求参数: {}", reminderDTO);
                }
            }
            resultRecord.append(tmpRecord).append(StrUtil.COMMA);
        });
        // 若有失败结果发送消息
        if (StrUtil.isNotBlank(errorRecord)) {
            ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(projectType);
            String projectTypeName;
            try {
                assert projectTypeEnums != null;
                projectTypeName = projectTypeEnums.getName();
            } catch (Throwable e) {
                projectTypeName = StrUtil.toString(projectType);
                log.error("未获取到对应的项目类型. message: {}, e=", e.getMessage(), e);
            }
            String keyword = customInfo.getCustomName() + StrUtil.DASHED + projectTypeName + StrUtil.COMMA;
            String errMessage = keyword + " 设置医院云资源到期提醒白名单异常, 详情请查询数据.";
            log.warn("白名单设置: {}", errorRecord);
            exceptionMessageService.sendToSystemManager(-1L, errMessage);
            return Result.fail("白名单设置失败.");
        }
        log.info("云资源设置白名单: {}", resultRecord);
        return Result.success();
    }

    /**
     * 实施团队变更
     *
     * @param dto 变更请求内容
     * @return 变更结果
     */
    public Result<ChangeCustomTeamResultDTO> changeCustomTeam(ChangeCustomTeamDTO dto) {
        if (dto.getChangeType() != NumberEnum.NO_0.num().intValue() && dto.getChangeType() != NumberEnum.NO_1.num().intValue()) {
            log.warn("团队变更, 未找到匹配的变更类型. param: {}", dto);
            return Result.fail("未匹配的变更类型");
        }
        ChangeCustomTeamResultDTO resultData = ChangeCustomTeamResultDTO.builder().build();
        List<ChangeCustomTeamEachResultDTO> eachResultDTOS = CollUtil.newArrayList();
        // 复制结果必要值
        resultData.setChangeType(dto.getChangeType());
        resultData.setYyCustomerId(dto.getYyCustomerId());
        resultData.setYyCustomTeamList(eachResultDTOS);
        // 团队变更
        if (dto.getChangeType() == NumberEnum.NO_0.num().intValue()) {
            ChangeCustomTeamEachDTO eachDTO = dto.getYyOrderIdList().get(0);
            // 处理客户团队
            try {
                apiYunyingService.changeCustomTeamEach(eachDTO, dto);
                resultData.setSuccess(true);
                resultData.setMessage(Result.success().getMsg());
            } catch (Throwable e) {
                log.error("客户团队变更, 变更客户实施团队异常, yyCustomerId: {}, team: {}. message: {}, e=", dto.getYyCustomerId(), eachDTO, e.getMessage(), e);
                resultData.setSuccess(false);
                resultData.setMessage(e.getMessage());
            }
            // 处理每个实施团队
            for (ChangeCustomTeamEachDTO changeCustomTeamEachDTO : dto.getYyOrderIdList()) {
                ChangeCustomTeamEachResultDTO eachResultDTO = BeanUtil.copyProperties(changeCustomTeamEachDTO, ChangeCustomTeamEachResultDTO.class);
                eachResultDTOS.add(eachResultDTO);
                try {
                    apiYunyingService.changeProjectTeam(changeCustomTeamEachDTO);
                    eachResultDTO.setMessage(Result.success().getMsg());
                    eachResultDTO.setSuccess(true);
                } catch (Throwable e) {
                    log.error("客户团队变更, 变更项目实施团队异常, param: {}. message: {}, e=", changeCustomTeamEachDTO, e.getMessage(), e);
                    eachResultDTO.setMessage(e.getMessage());
                    eachResultDTO.setSuccess(false);
                }
            }
        } else {
            // 处理变更项目实施团队
            for (ChangeCustomTeamEachDTO changeCustomTeamEachDTO : dto.getYyOrderIdList()) {
                // 处理每个实施团队
                ChangeCustomTeamEachResultDTO eachDTO = BeanUtil.copyProperties(changeCustomTeamEachDTO, ChangeCustomTeamEachResultDTO.class);
                try {
                    apiYunyingService.changeProjectTeam(changeCustomTeamEachDTO);
                    eachDTO.setSuccess(true);
                    eachDTO.setMessage(Result.success().getMsg());
                } catch (Throwable e) {
                    log.error("项目团队变更, 变更实施团队异常, param: {}. message: {}, e=", changeCustomTeamEachDTO, e.getMessage(), e);
                    eachDTO.setSuccess(false);
                    eachDTO.setMessage(e.getMessage());
                }
                eachResultDTOS.add(eachDTO);
            }
        }
        return Result.success(resultData);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void changeCustomTeamEach(ChangeCustomTeamEachDTO changeCustomTeamEachDTO, ChangeCustomTeamDTO changeCustomTeamDTO) {
        // 查询客户信息
        ProjCustomInfo customInfo;
        try {
            customInfo = commonService.getCustomInfoByYyCustomerId(changeCustomTeamDTO.getYyCustomerId());
        } catch (Throwable e) {
            log.error("实施团队变更, 查询客户信息异常. param:{}, message: {}, e=", changeCustomTeamEachDTO, e.getMessage(), e);
            throw new CustomException("查询客户信息异常.");
        }
        if (ObjectUtil.isEmpty(customInfo)) {
            log.error("实施团队变更, 未查询到客户信息. param: {}", changeCustomTeamEachDTO);
            throw new CustomException("未查询到客户信息.");
        }
        // 记录变更前的实施团队.
        String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
        sysOperLogService.apiOperLogInsert(customInfo, "实施团队变更(前)", uuid, Log.LogOperType.SEARCH.getCode());
        // 更新实施团队id和团队负责人id
        ProjCustomInfo updateObj = new ProjCustomInfo();
        updateObj.setCustomInfoId(customInfo.getCustomInfoId());
        updateObj.setCustomTeamId(changeCustomTeamEachDTO.getTeamId());
        updateObj.setCustomHeadId(changeCustomTeamEachDTO.getTeamLeaderId());
        int count = customInfoMapper.updateById(updateObj);
        log.info("实施团队变更, 更新客户实施团队. count: {}", count);
        customInfo = commonService.getCustomInfoByYyCustomerId(changeCustomTeamDTO.getYyCustomerId());
        sysOperLogService.apiOperLogInsert(customInfo, "实施团队变更(后)", uuid, Log.LogOperType.SEARCH.getCode());
    }

    @Transactional(rollbackFor = Throwable.class)
    public void changeProjectTeam(ChangeCustomTeamEachDTO changeCustomTeamEachDTO) {
        // 若不是软件工单.默认按照成功不处理
        if (changeCustomTeamEachDTO.getDeliveryOrderType() != OrderTypeEnums.SOFTWARE.getCode().intValue()) {
            log.info("项目实施团队变更. 非软件工单不做处理. param: {}", changeCustomTeamEachDTO);
            return;
        }
        // 下面只处理软件工单
        ProjOrderInfo orderInfo;
        try {
            // 更新项目实施团队
            orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("yy_order_id", changeCustomTeamEachDTO.getYyOrderId()));
        } catch (Throwable e) {
            log.error("实施团队变更, 查询工单信息异常. param: {}, message: {}, e=", changeCustomTeamEachDTO, e.getMessage(), e);
            throw new CustomException("查询工单信息异常.");
        }
        if (ObjectUtil.isEmpty(orderInfo)) {
            log.error("实施团队变更, 未查询到工单信息. param: {}", changeCustomTeamEachDTO);
            throw new CustomException("未查询到工单信息.");
        }
        // 查询项目信息
        ProjProjectInfo projectInfo;
        try {
            projectInfo = commonService.getProjectInfoByOrderInfoId(orderInfo.getOrderInfoId());
        } catch (Throwable e) {
            log.error("实施团队变更, 查询项目信息异常, param: {}. message: {}, e=", changeCustomTeamEachDTO, e.getMessage(), e);
            throw new CustomException("未查询到工单信息.");
        }
        if (ObjectUtil.isEmpty(projectInfo)) {
            log.error("实施团队变更, 未查询到项目信息. param: {}", changeCustomTeamEachDTO);
            throw new CustomException("未查询到工单信息.");
        }
        String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
        sysOperLogService.apiOperLogInsert(projectInfo, "项目实施团队变更(前)", uuid, Log.LogOperType.SEARCH.getCode());
        ProjProjectInfo updateProj = new ProjProjectInfo();
        updateProj.setProjectInfoId(projectInfo.getProjectInfoId());
        updateProj.setProjectTeamId(changeCustomTeamEachDTO.getTeamId());
        int count = projectInfoMapper.updateById(updateProj);
        log.info("实施团队变更, 更新项目实施团队. count: {}", count);
        projectInfo = commonService.getProjectInfoByOrderInfoId(orderInfo.getOrderInfoId());
        sysOperLogService.apiOperLogInsert(projectInfo, "项目实施团队变更(后)", uuid, Log.LogOperType.SEARCH.getCode());
    }

    //分院模式复制之前项目部署、实施、授权产品
    private void copyProducts(ProjProjectInfo projectInfoExtend, Long managerId, Date now) {
        //根据客户、项目类型查询系统已有的所有项目
        List<ProjProjectInfo> projectInfoList = projectInfoMapper.findByCustomAndProjectType(projectInfoExtend);
        if (CollectionUtils.isEmpty(projectInfoList)) {
            return;
        }
        List<Long> projectIds = projectInfoList.stream().map(a -> a.getProjectInfoId()).collect(Collectors.toList());
        int deliverCount = 0;
        int arrangeCount = 0;
        int empowerCount = 0;
        //处理实施产品
        List<ProjProductDeliverRecord> deliverRecords = productDeliverRecordMapper.findByProjectInfoIdList(projectIds);
        deliverRecords = deliverRecords.stream().filter(distinctByKey(ProjProductDeliverRecord::getProductDeliverId)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deliverRecords)) {
            log.info("分院模式复制之前项目实施产品开始,数量为：{}", deliverRecords.size());
            List<ProjProductDeliverRecord> saveDeliverRecords = deliverRecords.stream().map(a -> {
                ProjProductDeliverRecord save = new ProjProductDeliverRecord();
                BeanUtil.copyProperties(a, save);
                save.setProductDeliverRecordId(SnowFlakeUtil.getId());
                save.setCreaterId(managerId);
                save.setUpdaterId(managerId);
                save.setCreateTime(now);
                save.setUpdateTime(now);
                save.setProjectInfoId(projectInfoExtend.getProjectInfoId());
                return save;
            }).collect(Collectors.toList());
            deliverCount = productDeliverRecordMapper.batchInsert(saveDeliverRecords);
        }
        log.info("分院模式复制之前项目实施产品成功，数量为：{}", deliverCount);
        //处理部署产品
//        List<ProjProductArrangeRecord> arrangeRecords = productArrangeRecordMapper.findByProjectInfoIdList
//        (projectIds);
//        arrangeRecords = arrangeRecords.stream().filter(distinctByKey(ProjProductArrangeRecord::getProductArrangeId))
//                .collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(arrangeRecords)) {
//            log.info("分院模式复制之前项目部署产品开始,数量为：{}", arrangeRecords.size());
//            List<ProjProductArrangeRecord> saveArrangeRecords = arrangeRecords.stream().map(a -> {
//                ProjProductArrangeRecord save = new ProjProductArrangeRecord();
//                BeanUtil.copyProperties(a, save);
//                save.setProductArrangeRecordId(SnowFlakeUtil.getId());
//                save.setCreaterId(managerId);
//                save.setUpdaterId(managerId);
//                save.setCreateTime(now);
//                save.setUpdateTime(now);
//                save.setProjectInfoId(projectInfoExtend.getProjectInfoId());
//                return save;
//            }).collect(Collectors.toList());
//            arrangeCount = productArrangeRecordMapper.batchInsert(saveArrangeRecords);
//        }
//        log.info("分院模式复制之前项目部署产品成功，数量为：{}", arrangeCount);
        //处理授权产品
        List<ProjProductEmpowerRecord> empowerRecords = productEmpowerRecordMapper.findEmpowerRecordByProjectIds(projectIds);
        empowerRecords = empowerRecords.stream().filter(distinctByKey(ProjProductEmpowerRecord::getMsunHealthModuleCode)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(empowerRecords)) {
            log.info("分院模式复制之前项目授权产品开始,数量为：{}", empowerRecords.size());
            List<ProjProductEmpowerRecord> saveEmpowerRecords = empowerRecords.stream().map(a -> {
                ProjProductEmpowerRecord save = new ProjProductEmpowerRecord();
                BeanUtil.copyProperties(a, save);
                save.setProductEmpowerRecordId(SnowFlakeUtil.getId());
                save.setCreaterId(managerId);
                save.setUpdaterId(managerId);
                save.setCreateTime(now);
                save.setUpdateTime(now);
                save.setProjectInfoId(projectInfoExtend.getProjectInfoId());
                return save;
            }).collect(Collectors.toList());
            empowerCount = productEmpowerRecordMapper.batchInsert(saveEmpowerRecords);
        }
        log.info("分院模式复制之前项目授权产品成功，数量为：{}", empowerCount);
        log.info("分院模式复制之前项目所有产品成功");
    }



    /**
     * 根据项目类型和客户id查询主院
     *
     * @param projectType  项目类型
     * @param customInfoId 客户id
     * @return ProjHospitalInfo
     */
    public ProjHospitalInfo getMainHospitalInfo(int projectType, Long customInfoId) {
        List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectList(new QueryWrapper<ProjHospitalVsProjectType>().eq("project_type", projectType).eq("custom_info_id", customInfoId));
        ProjHospitalInfo hospitalInfo = null;
        if (CollUtil.isNotEmpty(hospitalVsProjectTypes)) {
            List<Long> hospitalInfoIds = hospitalVsProjectTypes.stream().map(ProjHospitalVsProjectType::getHospitalInfoId).collect(Collectors.toList());
            List<ProjHospitalInfo> hospitalInfos = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().in("hospital_info_id", hospitalInfoIds).eq("health_bureau_flag", NumberEnum.NO_1.num()));
            if (CollUtil.isNotEmpty(hospitalInfos)) {
                hospitalInfo = hospitalInfos.get(0);
            }
        }
        return hospitalInfo;
    }

    //处理实施、部署、授权产品数据
    private void handleProducts(List<Long> productIds, Long customInfoId, Long projectInfoId, Long managerId, Date now) {
        //云健康升级产品需要排除
        List<Long> needDeleteList = new ArrayList<>();
        needDeleteList.add(5481L);
        needDeleteList.add(5482L);
        needDeleteList.add(5483L);
        needDeleteList.add(5484L);
        //实施产品
        List<ProjProductDeliverRecord> deliverRecords = new ArrayList<>();
        List<ProductIdContrastDTO> deliverProductContrasts = dictDeliverMapper.selectByProductIds(productIds);
        List<Long> deliverProductIds = new ArrayList<>();
        deliverProductIds.addAll(productIds);
        deliverProductIds.removeAll(needDeleteList);
        dealProductIdContrast(deliverProductIds, deliverProductContrasts);
        deliverProductContrasts.stream().forEach(productIdContrast -> {
            Long deliverRecordId = SnowFlakeUtil.getId();
            ProjProductDeliverRecordExtend deliverRecordExtend = new ProjProductDeliverRecordExtend(productIdContrast, deliverRecordId, customInfoId, projectInfoId, managerId, now);
            deliverRecords.add(deliverRecordExtend);
        });
        //先删除
        productDeliverRecordMapper.deleteByProjectInfoId(projectInfoId);
        if (!CollectionUtils.isEmpty(deliverRecords)) {
            productDeliverRecordMapper.batchInsert(deliverRecords);
        }
        //部署产品
        List<ProjProductArrangeRecord> arrangeRecords = new ArrayList<>();
        List<ProductIdContrastDTO> arrangeProductContrasts = dictArrangeMapper.selectByProductIds(productIds);
        //去除对照的产品-筛选对照是自身的产品
        List<Long> arrangeProductIds = new ArrayList<>();
        arrangeProductIds.addAll(productIds);
        arrangeProductIds.removeAll(needDeleteList);
        dealProductIdContrast(arrangeProductIds, arrangeProductContrasts);
        arrangeProductContrasts.stream().forEach(productIdContrast -> {
            Long arrangeRecordId = SnowFlakeUtil.getId();
            ProjProductArrangeRecordExtend arrangeRecordExtend = new ProjProductArrangeRecordExtend(productIdContrast, arrangeRecordId, customInfoId, projectInfoId, managerId, now);
            //默认状态为0  部署状态 0未申请  1处理中  2  已部署
            arrangeRecordExtend.setArrangeStatus(0);
            arrangeRecords.add(arrangeRecordExtend);
        });
        //先删除
        productArrangeRecordMapper.deleteByProjectInfoId(projectInfoId);
        if (!CollectionUtils.isEmpty(arrangeRecords)) {
            productArrangeRecordMapper.batchInsert(arrangeRecords);
        }
        //授权产品
        List<ProjProductEmpowerRecord> empowerRecords = new ArrayList<>();
        List<ProductIdContrastDTO> empowerProductContrasts = dictEmpowerMapper.selectByProductIds(productIds);
        empowerProductContrasts.stream().forEach(productIdContrast -> {
            Long empowerRecordId = SnowFlakeUtil.getId();
            ProjProductEmpowerRecordExtend empowerRecordExtend = new ProjProductEmpowerRecordExtend(productIdContrast, empowerRecordId, customInfoId, projectInfoId, managerId, now);
            empowerRecords.add(empowerRecordExtend);
        });
        //先删除
        productEmpowerRecordMapper.deleteByProjectInfoId(projectInfoId);
        if (!CollectionUtils.isEmpty(empowerRecords)) {
            productEmpowerRecordMapper.batchInsert(empowerRecords);
        }
    }
//    @Resource
//    private ProjHospitalInfoMapper hospitalInfoMapper;
//    @Resource
//    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;

    //获取老项目新项目关系
    private @NotNull TmpProjectNewVsOld getTmpProjectNewVsOld(SyncContractDTO syncContractDTO, Long projectInfoId, Long customInfoId) {
        Long projectNewVsOldId = SnowFlakeUtil.getId();
        TmpProjectNewVsOld tmpProjectNewVsOld = new TmpProjectNewVsOld();
        tmpProjectNewVsOld.setProjectNewVsOldId(projectNewVsOldId);
        tmpProjectNewVsOld.setNewProjectInfoId(projectInfoId);
        tmpProjectNewVsOld.setNewCustomInfoId(customInfoId);
        tmpProjectNewVsOld.setOldProjectInfoId(syncContractDTO.getOldProjectId());
        tmpProjectNewVsOld.setOldCustomInfoId(syncContractDTO.getOldCustomerInfoId());
        tmpProjectNewVsOld.setOldCustomId(syncContractDTO.getOldCustomerId());
        tmpProjectNewVsOld.setNewProjectSource(ProjectSourceEnums.ORDER_GENERATE.getCode());
        return tmpProjectNewVsOld;
    }

    /**
     * 复制医院数据到老系统中
     *
     * @param customInfoId
     * @param projectType
     * @param projectId
     * @param oldCustomerId
     */
    private void copyHospitalForImsp(Long customInfoId, Integer projectType, Long projectId, Long oldCustomerId) {
        try {
            // 查询实施地客户下的全部医院数据
            List<ProjHospitalInfo> projHospitalInfos = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", customInfoId).eq("health_bureau_flag", 0));
            log.info("需要进行复制的医院数据 , ==== {}", JSONUtil.toJsonStr(projHospitalInfos));
            // 复制医院数据到老系统中
            List<OldCustomerInfo> oldCustomerInfoList = new ArrayList<>();
            for (ProjHospitalInfo hospitalInfo : projHospitalInfos) {
                OldCustomerInfo oldCustomerInfo = new OldCustomerInfo();
                oldCustomerInfo.setId(SnowFlakeUtil.getId());
                oldCustomerInfo.setCsmHospitalInfoId(hospitalInfo.getHospitalInfoId());
                oldCustomerInfo.setCustomerName(hospitalInfo.getHospitalName());
                oldCustomerInfo.setHospitalType(Convert.toStr(projectType));
                oldCustomerInfo.setHospitalStatus(0);
                oldCustomerInfo.setProjectId(Convert.toInt(projectId));
                oldCustomerInfo.setCustomerId(oldCustomerId);
                oldCustomerInfo.setHospitalId(hospitalInfo.getCloudHospitalId());
                oldCustomerInfo.setOrgId(hospitalInfo.getOrgId());
                oldCustomerInfo.setProductNetwork(hospitalInfo.getCloudDomain());
                oldCustomerInfo.setPreProductNetwork(hospitalInfo.getCloudDomain());
                oldCustomerInfo.setHost(ObjectUtil.isNotEmpty(hospitalInfo.getCloudDomain()) ? hospitalInfo.getCloudDomain().replace("https://", "") : null);
                oldCustomerInfo.setPreHost(ObjectUtil.isNotEmpty(hospitalInfo.getCloudDomain()) ? hospitalInfo.getCloudDomain().replace("https://", "") : null);
                oldCustomerInfo.setEnvId(hospitalInfo.getEnvId());
                oldCustomerInfo.setEnvName(hospitalInfo.getEnvName());
                oldCustomerInfoList.add(oldCustomerInfo);
            }
            log.info("复制医院数据 === , {}", JSONUtil.toJsonStr(oldCustomerInfoList));
            oldCustomerInfoService.saveBatch(oldCustomerInfoList);
        } catch (Exception e) {
            log.error("copyHospitalForImsp，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    //获取部署产品记录
    private void dealProductIdContrast(List<Long> productIds, List<ProductIdContrastDTO> contrasts) {
        //去除对照的产品-筛选对照是自身的产品
        productIds.removeAll(contrasts.stream().map(ProductIdContrastDTO::getOriginalProductId).collect(Collectors.toList()));
        //自身对照产品记录
        contrasts.addAll(productIds.stream().map(originalProductId -> new ProductIdContrastDTO(originalProductId)).collect(Collectors.toList()));
    }

    /**
     * 校验工单数据
     *
     * @param syncContractDTO
     * @return
     */
    private OrderCheckResp checkContractOrder(SyncContractDTO syncContractDTO) {
        OrderCheckResp checkResp = new OrderCheckResp();
        StringBuilder sb = new StringBuilder();
        //销售人员信息确认
        QueryWrapper<SysUser> userWrapper = new QueryWrapper<>();
        userWrapper.eq("user_yunying_id", syncContractDTO.getSaleUserId().toString());
        SysUser saleUser = sysUserMapper.selectOne(userWrapper);
        if (ObjectUtil.isNull(saleUser)) {
            sb.append(String.format("销售人员不存在(id:%s)", syncContractDTO.getSaleUserName()));
            checkResp.setErrMsg(sb.toString());
            return checkResp;
        }
        checkResp.setSaleUser(saleUser);
        //------------------------------------派工单信息校验--------------------------------------
        List<OrderDTO> orders = syncContractDTO.getDatas();
        for (OrderDTO order : orders) {
            ProjOrderInfo projOrderInfo = orderInfoMapper.selectByYyOrderId(order.getWorkOrderId());
            if (ObjectUtil.isNotEmpty(projOrderInfo)) {
                sb.append(String.format("工单已经存在(id:%s)! 请勿重复同步", order.getWorkOrderId()));
                checkResp.setErrMsg(sb.toString());
                return checkResp;
            }
            //解决方案校验
            order.getProduct().stream().filter(product -> ObjectUtil.isEmpty(product.getPemCusSolType())).forEach(product -> sb.append(product.getProductName() + "产品解决方案为空!"));
            //硬件单独处理
            //默认是小硬件，产品有一个大硬件，则不是小硬件工单
            Boolean bigHardware = false;
            if (order.getOrderType() == HARDWARE.getCode()) {
                if (order.getProduct().stream().filter(pru -> pru.getProdHardwareType() == 1).count() > 0) {
                    bigHardware = true;
                }
            }
            //校验是否续期checkWhetherItIsRenewed(syncContractDTO, order.getProduct().get(0).getPemCusSolType());
            boolean result = syncContractDTO.getWhetherRenewal() || syncContractDTO.getDisaststerRenewal();
            //1、项目经理id对照  (小硬件、耗材 无需校验项目经理、实施团队，给定默认值)
            if (order.getOrderType() == CONSUMABLE.getCode() || (order.getOrderType() == HARDWARE.getCode() && !bigHardware) || result) {
                order.setAutoFlag(true);
            } else {
                //项目经理校验
                userWrapper.clear();
                userWrapper.eq("user_yunying_id", order.getProjectManagerId().toString());
                SysUser projectManager = sysUserMapper.selectOne(userWrapper);
                if (ObjectUtil.isNull(projectManager)) {
                    sb.append(String.format("项目经理不存在(id:%s)", order.getProjectManagerId()));
                    checkResp.setErrMsg(sb.toString());
                    return checkResp;
                }
                checkResp.setProjectManager(projectManager);
                //实施团队校验
                QueryWrapper<SysDept> deptWrapper = new QueryWrapper<>();
                deptWrapper.eq("dept_yunying_id", order.getTeamId());
                SysDept dept = sysDeptMapper.selectOne(deptWrapper);
                if (ObjectUtil.isNull(dept)) {
                    sb.append(String.format("实施团队不存在(id:%s)", order.getTeamId()));
                    checkResp.setErrMsg(sb.toString());
                    return checkResp;
                }
            }
        }
        return checkResp;
    }

    /**
     * 保存日志
     *
     * @param datas
     */
    private void saveLogs(String datas, String actionName) {
        ProjSyncApiLogs syncApiLogsEntity = new ProjSyncApiLogs();
        syncApiLogsEntity.setId(SnowFlakeUtil.getId());
        syncApiLogsEntity.setActionname(actionName);
        syncApiLogsEntity.setDatas(datas);
        syncApiLogsMapper.insert(syncApiLogsEntity);
    }

    /**
     * 手动处理项目产品对照
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result dealProducts(Long projectInfoId) {
        //查询项目
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        if (ObjectUtil.isEmpty(projectInfo)) {
            return Result.success();
        }
        Long customInfoId = projectInfo.getCustomInfoId();
        Long managerId = projectInfo.getProjectLeaderId();
        //查询项目工单产品
        List<ProjOrderProduct> orderProductList = orderProductMapper.findByProjectInfoId(projectInfoId);
        // 查询特殊产品表
        List<ProjSpecialProductRecord> specialProductRecords = specialProductRecordMapper.selectList(new QueryWrapper<ProjSpecialProductRecord>().eq("project_info_id", projectInfoId));
        if (CollectionUtils.isEmpty(orderProductList) && CollUtil.isEmpty(specialProductRecords)) {
            return Result.success();
        }
        List<ProjOrderProduct> spetialToOrders = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(specialProductRecords)) {
            spetialToOrders = specialProductRecords.stream().map(e -> {
                ProjOrderProduct orderProduct = new ProjOrderProduct();
                orderProduct.setYyOrderProductId(e.getSpecialProductId());
                return orderProduct;
            }).collect(Collectors.toList());
        }
        orderProductList.addAll(spetialToOrders);
        List<Long> productIds = orderProductList.stream().map(ProjOrderProduct::getYyOrderProductId).collect(Collectors.toList());
        Date now = new Date();
        handleProducts(productIds, customInfoId, projectInfoId, managerId, now);
        //是否包含分院模式产品
        List<RuleProductRuleConfig> branchHospitalProducts = productRuleConfigMapper.findBranchHospitalFlag();
        boolean branchHospitalFlag = false;
        for (RuleProductRuleConfig branchHospitalProduct : branchHospitalProducts) {
            if (productIds.contains(branchHospitalProduct.getYyProductId())) {
                branchHospitalFlag = true;
                break;
            }
        }
        //包含分院模式产品-且只有分院模式产品
        if (branchHospitalFlag && !(projectInfo.getHisFlag() == 1) && orderProductList.size() == 1) {
            this.copyProducts(projectInfo, managerId, now);
        }
        return Result.success();
    }

    @Override
    public ResponseData<String> syncYunRenew(SyncYunRenewDTO dto) {
        if (!(CloudTypeEnum.CLOUD_RESOURCE.getOrderType().intValue() == dto.getType())) {
            log.error("不是云资源续期: {}", dto);
            throw new RuntimeException("不是云资源续期.");
        }
        // 获取实施地客户的云资源工单
        List<ProjCustomInfo> customInfos = customInfoMapper.selectList(new QueryWrapper<ProjCustomInfo>().eq("yy_customer_id", dto.getCustomerId()));
        if (CollUtil.isEmpty(customInfos)) {
            log.error("未查询到客户信息: {}", dto);
            throw new RuntimeException("未查询到客户信息.");
        }
        ProjCustomInfo customInfo = customInfos.get(0);
        List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectList(new QueryWrapper<ProjHospitalVsProjectType>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("project_type", dto.getPemcusssolType()));
        if (CollUtil.isEmpty(hospitalVsProjectTypes)) {
            log.error("未查询到医院信息: {}", dto);
            throw new RuntimeException("未查询到医院信息.");
        }
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().in("hospital_info_id", hospitalVsProjectTypes.stream().map(ProjHospitalVsProjectType::getHospitalInfoId).collect(Collectors.toList())));
        // 查询客户云信息
        List<ProjProjectOrderRelation> projectOrderRelations = projectOrderRelationMapper.selectList(new QueryWrapper<ProjProjectOrderRelation>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("delivery_order_id", CloudTypeEnum.CLOUD_RESOURCE.getOrderType()).orderByDesc("create_time"));
        if (CollUtil.isEmpty(projectOrderRelations) || projectOrderRelations.size() >= NumberEnum.NO_2.num()) {
            throw new RuntimeException("未查询到云资源绑定关系.");
        }
        //检索上一次是不是借货合同，如果是需要减去借货合同服务时间
        ProjOrderInfo orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("yy_order_id", projectOrderRelations.get(1).getYyOrderId()));
        if (orderInfo == null) {
            throw new RuntimeException("未查询到上一次云资源工单");
        }
        ProjContractInfo contractInfo = contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq("contract_info_id", orderInfo.getContractInfoId()));
        if (contractInfo == null) {
            throw new RuntimeException("未查询到上一次云资源合同");
        }
        ProjProjectOrderRelation projProjectOrderRelation = projectOrderRelations.get(0);
        ProjCustomCloudService cloudService = customCloudServiceMapper.selectById(projProjectOrderRelation.getBussinessInfoId());
        apiYunyingService.syncYunweiCloudTime(dto, cloudService, hospitalInfos);
        return ResponseData.success("成功", null);
    }

    @Override
    public ResponseData<String> syncYunRenewalApplicationRenew(SyncYunRenewalApplicationRenewDTO dto, Integer projectType) {
        if (!(CloudTypeEnum.CLOUD_RESOURCE.getOrderType().intValue() == dto.getType())) {
            log.info("不是云资源续期: {}", dto);
            return ResponseData.success("不是云资源续期", null);
        }
        // 获取实施地客户的云资源工单
        List<ProjCustomInfo> customInfos = customInfoMapper.selectList(new QueryWrapper<ProjCustomInfo>().eq("yy_customer_id", dto.getCustomerId()));
        if (CollUtil.isEmpty(customInfos)) {
            log.info("未查询到客户信息: {}", dto);
            return ResponseData.success("未查询到客户信息", null);
        }
        ProjCustomInfo customInfo = customInfos.get(0);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("is_deleted", 0).eq("project_type", projectType));
        if (CollUtil.isEmpty(projectInfos)) {
            return ResponseData.success("未查询到项目信息", null);
        }
        ProjCustomCloudService cloudService = customCloudServiceMapper.selectById(dto.getCustomCloudServiceId());
        SelectHospitalDTO hospitalDTO = new SelectHospitalDTO();
        hospitalDTO.setProjectInfoId(projectInfos.get(0).getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = projHospitalInfoService.getHospitalInfoByProjectId(hospitalDTO);
        hospitalInfos = hospitalInfos.stream().filter(hosp -> (ObjectUtil.isNotEmpty(hosp.getOrgId()) && ObjectUtil.isNotEmpty(hosp.getCloudHospitalId()) && ObjectUtil.isNotEmpty(hosp.getCloudDomain())) || OpenStatusEnum.OPENED.getCode().equals(hosp.getHospitalOpenStatus())).collect(Collectors.toList());
        if (hospitalInfos.size() == 0) {
            log.info("开通状态或者没有医院: {}", dto);
            return ResponseData.success("开通状态或者没有医院", null);
        }
        if (cloudService != null) {
            return apiYunyingService.syncYunRenewalApplicationRenewCloudTime(dto, cloudService, hospitalInfos, projectType);
        }
        return ResponseData.success("未查询到云资源服务", null);

    }

    /**
     * 说明: 校验是否续期
     *
     * @param syncContractDTO
     * @param pemcusssolType
     * @return:boolean
     * @author: Yhongmin
     * @createAt: 2024/7/19 19:39
     * @remark: Copyright
     */
    public boolean checkWhetherItIsRenewed(SyncContractDTO syncContractDTO, Integer pemcusssolType, Integer orderType) {
        //非云资源不能是续期
        if (!NumberEnum.NO_9.num().equals(orderType)) {
            return false;
        }
        ProjCustomInfo customInfo = customInfoMapper.selectByYyCustomerId(syncContractDTO.getCustomerInfo().getCustomerId().longValue());
        if (customInfo == null) {
            return false;
        }
        List<ProjCustomCloudService> cloudServices = customCloudServiceMapper.selectList(new QueryWrapper<ProjCustomCloudService>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("solution_type", pemcusssolType).orderByDesc("create_time"));
        return cloudServices.size() > 0;
    }

    /**
     * 说明: 获取实际应该服务期限，主要是判断有没有借贷合同
     *
     * @param cloudService
     * @param date
     * @param dto
     * @return:com.msun.csm.dao.entity.proj.ProjCustomCloudService
     * @author: Yhongmin
     * @createAt: 2024/7/16 14:24
     * @remark: Copyright
     */
    public ProjCustomCloudService getProjCustomCloudService(ProjCustomCloudService cloudService, Date date, SyncYunRenewalApplicationRenewDTO dto) {
        ProjCustomCloudService customCloudService = new ProjCustomCloudService();
        BeanUtils.copyProperties(cloudService, customCloudService);
        customCloudService.setCustomCloudServiceId(SnowFlakeUtil.getId());
        customCloudService.setCreateTime(date);
        customCloudService.setUpdateTime(date);
        customCloudService.setServiceSubscribeStatus(1);
        customCloudService.setDeployNodeId(-1L);
        customCloudService.setDeployNodeName(StrUtil.EMPTY);
        customCloudService.setIsDeleted(0);
        customCloudService.setCreaterId(-1L);
        customCloudService.setUpdaterId(-1L);
        customCloudService.setYyOrderId(dto.getYyWoId());
        customCloudService.setServiceSubscribeTerm(dto.getLen());
        customCloudService.setContractType(dto.getContractType());
        customCloudService.setSolutionType(dto.getPemcusssolType());
        // 拼接参数
        CloudServiceRenewalTimeDTO cloudServceTimeDTO = CloudServiceRenewalTimeDTO.builder().serviceSubscribeTerm(dto.getLen()).lastCloudServiceData(CloudServiceRenewalTimeDTO.LastCloudServiceData.builder().planStartTime(cloudService.getPlanStartTime()).subscribeStartTime(cloudService.getSubscribeStartTime()).serviceSubscribeTerm(cloudService.getServiceSubscribeTerm()).contractType(cloudService.getContractType()).build()).build();
        // 计算结果
        CloudServiceTimeResult timeResult = CloudServiceKit.getCloudServiceRenewalTime(cloudServceTimeDTO);
        // 复制最终返回值
        customCloudService.setPlanStartTime(timeResult.getPlanStartTime());
        customCloudService.setSubscribeStartTime(timeResult.getSubscribeStartTime());
        customCloudService.setSubscribeEndTime(timeResult.getSubscribeEndTime());
        return customCloudService;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ResponseData<String> syncYunRenewalApplicationRenewCloudTime(SyncYunRenewalApplicationRenewDTO dto, ProjCustomCloudService cloudService, List<ProjHospitalInfo> hospitalInfos, Integer projectType) {
        try {
            boolean success = cloudService == null || cloudService.getYyOrderId() == null || cloudService.getServiceSubscribeTerm() == null || (cloudService.getSubscribeStartTime() == null && cloudService.getPlanStartTime() == null);
            if (success) {
                log.error("云资源信息为空,cloudService={}", cloudService);
                return ResponseData.success("云资源信息为空", null);
            }
            SyncCloudTimeReq syncCloudTimeReq = new SyncCloudTimeReq(CloudTypeEnum.CLOUD_RESOURCE.getCode(), projectType);
            Date date = new Date();
            ProjCustomCloudService customCloudService = getProjCustomCloudService(cloudService, date, dto);
            ProjCustomCloudService isProjCustomCloudService = customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>().eq("yy_order_id", dto.getYyWoId()));
            int count = 0;
            if (isProjCustomCloudService != null) {
                customCloudService.setCustomCloudServiceId(isProjCustomCloudService.getCustomCloudServiceId());
                count = customCloudServiceMapper.updateById(customCloudService);
            } else {
                count = customCloudServiceMapper.insert(customCloudService);
            }
            log.info("新增客户云信息. count: {}", count);
            if (count == 0) {
                return ResponseData.success("项目与工单绑定关系表", null);
            }
            List<SyncCloudTimeHospitalReq> hospitalReqs = new ArrayList<>();
            log.info("项目与工单绑定关系表. count: {}", count);
            for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
                SyncCloudTimeHospitalReq hospitalReq = new SyncCloudTimeHospitalReq();
                hospitalReq.setHospitalId(hospitalInfo.getCloudHospitalId());
                Date planEndTime = DateUtils.addDays(DateUtils.addMonths(customCloudService.getPlanStartTime(), dto.getLen()), -1);
                hospitalReq.setEndTime(DateUtil.formatDateTime(planEndTime));
                hospitalReq.setMainRemindFlag(NumberEnum.NO_1.num().toString());
                hospitalReqs.add(hospitalReq);
            }
            syncCloudTimeReq.setHospitals(hospitalReqs);
            yunWeiPlatFormService.sendTimeToYunWei(syncCloudTimeReq);
            asyncService.getDataAndProcessLater(customCloudService.getPlanStartTime(), customCloudService.getSubscribeStartTime(), dto);
        } catch (Exception e) {
            log.error("同步云资源时间失败, message: {}, e=", e.getMessage(), e);
            String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
            sysOperLogService.apiOperLogInsertObjAry("派工续期云资源失败异常", uuid, Log.LogOperType.ADD.getCode(), dto, cloudService, hospitalInfos, projectType);
            String errMsg = "派工续期云资源失败. 请及时处理, 详情根据此uuid查询系统日志. uuid:" + uuid + StrUtil.DOT;
            String keyword = StrUtil.EMPTY;
            try {
                ProjCustomInfo customInfo = commonService.getCustomInfo(cloudService.getCustomInfoId());
                if (ObjectUtil.isNotEmpty(customInfo)) {
                    ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(projectType);
                    String projectTypeName = StrUtil.EMPTY;
                    if (ObjectUtil.isNotEmpty(projectTypeEnums)) {
                        assert projectTypeEnums != null;
                        projectTypeName = projectTypeEnums.getName();
                    }
                    keyword = customInfo.getCustomName() + StrUtil.DASHED + projectTypeName + StrUtil.COMMA;
                }
                errMsg += keyword + errMsg + dto;
                exceptionMessageService.sendToSystemManager(-1L, errMsg);
            } catch (Throwable ex) {
                log.error("派工续期云资源处理异常时报错. message: {}, e=", e.getMessage(), ex);
            }

        }
        return ResponseData.success("成功", null);
    }

    /**
     * 查询所有客户数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<ProjCustomInfoResp> getCustomerList(CustomerParamerDTO dto) {
        List<ProjCustomInfoResp> list = customInfoMapper.getCustomerList(dto);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void syncYunweiCloudTime(SyncYunRenewDTO dto, ProjCustomCloudService cloudService, List<ProjHospitalInfo> hospitalInfos) {
        SyncCloudTimeReq syncCloudTimeReq = new SyncCloudTimeReq(CloudTypeEnum.CLOUD_RESOURCE.getCode(), dto.getPemcusssolType());
        List<SyncCloudTimeHospitalReq> hospitalReqs = new ArrayList<>();
        // - 计算新的开通和结束时间
        Date startTime = DateUtils.addDays(cloudService.getSubscribeEndTime(), 1);
        Date endTime = cloudService.getSubscribeEndTime();
        if (dto.getLen() > 0) {
            endTime = DateUtils.addDays(cloudService.getSubscribeEndTime(), dto.getLen());
        }
        // 更新时间
        ProjCustomCloudService updateObj = new ProjCustomCloudService();
        updateObj.setCustomCloudServiceId(cloudService.getCustomCloudServiceId());
        updateObj.setSubscribeStartTime(startTime);
        updateObj.setSubscribeEndTime(endTime);
        int count = customCloudServiceMapper.updateById(updateObj);
        log.info("更新客户云信息. count: {}", count);
        for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
            SyncCloudTimeHospitalReq hospitalReq = new SyncCloudTimeHospitalReq();
            hospitalReq.setHospitalId(hospitalInfo.getCloudHospitalId());
            hospitalReq.setStartTime(DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN));
            hospitalReq.setEndTime(DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN));
            hospitalReqs.add(hospitalReq);
        }
        syncCloudTimeReq.setHospitals(hospitalReqs);
        syncCloudTimeReq.setCloudFeedback(dto.getCloudFeedback());
        yunWeiPlatFormService.sendTimeToYunWei(syncCloudTimeReq);
    }

    /**
     * 说明: 同步运维平台开通、结束时间
     */
    public Result<YunOpenDTO> sendTimeToYunYingCloudTime(Date openDate, Date signDate, SyncYunyingApplicationDTO dto) {
        YunOpenDTO yunOpenDTO = new YunOpenDTO();
        yunOpenDTO.setCloudProjId(dto.getYyWoId());
        yunOpenDTO.setPemCusSolType(dto.getPemcusssolType());
        yunOpenDTO.setToken(TOKEN);
        yunOpenDTO.setProductType(dto.getProductType());
        yunOpenDTO.setType(com.msun.csm.util.StringUtils.nvl(NumberEnum.NO_3.num()));
        List<Object> param = new ArrayList<>();
        yunOpenDTO.setOpenDate(DateUtil.formatDate(openDate));
        yunOpenDTO.setSignDate(DateUtil.formatDate(signDate) + " 00:00:00");
        yunOpenDTO.setSoftProjId(dto.getYyWoId());
        String account = commonService.getSalePersonByYyOrderId(dto.getYyWoId()).getAccount();
        param.add(yunOpenDTO);
        param.add(account);
        sysOperLogService.apiOperLogInsert(param, "同步运营平台云服务-时间", "入参", Log.LogOperType.ADD.getCode());
        // 当前阶段
        CloudPhaseTypeEnum cloudPhaseType = CloudPhaseTypeEnum.getEnum(dto.getPhaseType());
        String phaseDesc = ObjectUtil.isNotEmpty(cloudPhaseType) ? cloudPhaseType.getDesc() : StrUtil.EMPTY;
        Result<String> result = null;
        try {
            log.info("同步运营平台云服务-时间. yunOpenDTO: {}, account: {}, phase: {}", yunOpenDTO, account, phaseDesc);
            result = yunyingFeignClient.syncYzyOpen(account, yunOpenDTO);
            sysOperLogService.apiOperLogInsertObjAry("同步运营平台云服务-时间", "返回值", Log.LogOperType.ADD.getCode(), result, phaseDesc);
            if (!result.isSuccess()) {
                log.info("同步运营平台云服务-时间. yunOpenDTO: {}, account: {}, phase: {}", yunOpenDTO, account, phaseDesc);
                ProjCustomInfo customInfo = commonService.getCustomInfoByYyCustomerId(dto.getCustomerId());
                exceptionMessageService.sendToSystemManager(-1L, "云容灾向运维同步到期时间接口调用异常, " + "请及时处理. 客户: " + (ObjectUtil.isNotEmpty(customInfo) ? customInfo.getCustomName() : StrUtil.EMPTY));
                return Result.fail(result.getMsg());
            }
            return Result.success(yunOpenDTO);
        } catch (Throwable e) {
            log.error("同步运营平台云服务-时间异常. errMessage: {}, e=", e.getMessage(), e);
            sysOperLogService.apiOperLogInsertObjAry("同步运营平台云服务-时间", "返回值", Log.LogOperType.ADD.getCode(), result, phaseDesc);
            return Result.fail("同步运营平台云服务-时间异常" + e.getMessage());
        }
    }

    /**
     * 删除工单-需要处理关联数据-项目-工单-工单产品
     *
     * @param simpleId-工单id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteOrder(SimpleId simpleId) {
        log.info("删除工单-需要处理关联数据-项目-工单-工单产品. simpleId: {}", JSON.toJSONString(simpleId));
        sysOperLogService.apiOperLogInsert(simpleId, "yy-deleteOrder", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
        Long yyOrderId = simpleId.getId();
        ProjOrderInfo orderInfo = orderInfoMapper.selectByYyOrderId(yyOrderId);
        if (ObjectUtil.isNull(orderInfo)) {
            return Result.success("工单不存在");
        }
        //删除对应项目
        projectInfoMapper.deleteByOrderInfoId(orderInfo.getOrderInfoId());
        //删除对应工单
        orderInfoMapper.deleteByPrimaryKey(orderInfo.getOrderInfoId());
        //删除工单产品
        orderProductMapper.deleteByOrderInfoId(orderInfo.getOrderInfoId());
        //project_order_relation
        projectOrderRelationMapper.deleteByYYOrderId(orderInfo.getOrderInfoId());
        return Result.success("处理完成");
    }

    /**
     * 修改客户信息
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateCustomInfo(UpdateCustomInfoDTO req) {
        try {
            log.info("修改客户信息. req: {}", JSON.toJSONString(req));
            sysOperLogService.apiOperLogInsert(req, "yy-updateCustomInfo", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
            ProjCustomInfo oldCustomInfo = customInfoMapper.selectByYyCustomerId(req.getOldYYCustomId());
            ProjCustomInfo newCustomInfo = customInfoMapper.selectByYyCustomerId(req.getNewYYCustomId());
            if (ObjectUtil.isEmpty(oldCustomInfo) || ObjectUtil.isEmpty(newCustomInfo)) {
                return Result.success("客户信息不存在");
            }
            Long oldCustomInfoId = oldCustomInfo.getCustomInfoId();
            Long newCustomInfoId = newCustomInfo.getCustomInfoId();
            //判断是否是项目级别的变更数据
            List<Long> orderInfoIdList = new ArrayList<>();
            List<Long> projectInfoIds = new ArrayList<>();
            if (req.getChangeType() == 2) {
                //项目级别的变更
                List<Long> yyOrderIdList = req.getOrderIdList();
                if (CollectionUtils.isEmpty(yyOrderIdList)) {
                    return Result.fail("工单id列表为空");
                }
                List<ProjOrderInfo> orderInfoList = new ArrayList<>();
                for (Long yyOrderId : yyOrderIdList) {
                    List<ProjOrderInfo> orderList = new LambdaQueryChainWrapper<>(orderInfoMapper).eq(ProjOrderInfo::getYyOrderId, yyOrderId).list();
                    if (CollUtil.isNotEmpty(orderList)) {
                        continue;
                    }
                    orderInfoList.addAll(orderList);
                }
                orderInfoIdList = orderInfoList.stream().map(ProjOrderInfo::getOrderInfoId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orderInfoIdList)) {
                    return Result.success("工单项目全不存在");
                }
                List<ProjProjectInfo> projectInfoList = projectInfoMapper.selectByOrderInfoIdList(orderInfoIdList);
                projectInfoIds = projectInfoList.stream().map(ProjProjectInfo::getProjectInfoId).collect(Collectors.toList());
                //proj_project_info-变更
                projectInfoMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, orderInfoIdList);
            } else {
                //客户合并
                //proj_custom_cloud_service-变更
                customCloudServiceMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId);
                //proj_hospital_info-变更
                hospitalInfoMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId);
                //proj_hospital_vs_project_type-变更
                hospitalVsProjectTypeMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId);
                //proj_product_empower_add_record-变更
                productEmpowerAddRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId);
                //proj_cloud_account_password-变更
                cloudAccountPasswordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId);
                //proj_custom_info-删除
                customInfoMapper.deleteByPrimaryKey(oldCustomInfo.getCustomInfoId());
            }
            //proj_hospital_online_detail-变更
            hospitalOnlineDetailMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_survey_form-变更
            surveyFormMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_survey_plan-变更
            surveyPlanMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_survey_report-变更
            surveyReportMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_equip_record-变更
            equipRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_hardware_record-变更
            hardwareRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_milestone_task-变更
            milestoneTaskMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_online_step-变更
            onlineStepMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_order_info-变更
            orderInfoMapper.updateByCustomInfoId(req.getOldYYCustomId(), req.getNewYYCustomId(), orderInfoIdList);
            //proj_product_arrange_record-变更
            productArrangeRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_product_deliver_record-变更
            productDeliverRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_product_empower_record-变更
            productEmpowerRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_product_supplementary_record-变更
            productSupplementaryRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, orderInfoIdList);
            //proj_project_classification_score-变更
            projectClassificationScoreMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_project_deduction_detail-变更
//            projectDeductionDetailMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_project_order_relation-变更
            projectOrderRelationMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_third_interface-变更
            thirdInterfaceMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_apply_order-变更
            applyOrderMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_apply_order_hospital_record-变更
            applyOrderHospitalRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_apply_order_product_record-变更
            applyOrderProductRecordMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, projectInfoIds);
            //proj_project_info-变更
            projectInfoMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, orderInfoIdList);
            //tmp_project_new_vs_old-变更
            //查询老平台客户id
            Customer oldJfCustomer = imspProjectMapper.selectCustomerByYYId(req.getNewYYCustomId());
            Long oldJfNewCustomerId = null;
            if (ObjectUtil.isNotNull(oldJfCustomer)) {
                oldJfNewCustomerId = oldJfCustomer.getId();
            }
            tmpProjectNewVsOldMapper.updateByCustomInfoId(oldCustomInfoId, newCustomInfoId, oldJfNewCustomerId, projectInfoIds);
            return Result.success();
        } catch (Throwable e) {
            log.error("更新客户信息失败了：{}", e.getMessage(), e);
            throw new CustomException(StrUtil.format("更新客户信息失败了：{}", e.getMessage()), e);
        }
    }

    /**
     * 说明: 替换工单产品
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result replaceOrderProduct(ReplaceOrderProductDTO req) {
        log.info("置换工单产品, param: {}", JSON.toJSONString(req));
        sysOperLogService.apiOperLogInsert(req, "yy-replaceOrderProduct", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
        //判断是否存在工单
        Long yyOrderId = req.getOrderId();
        ProjOrderInfo orderInfo = orderInfoMapper.selectByYyOrderId(yyOrderId);
        if (ObjectUtil.isNull(orderInfo)) {
            return Result.success("工单不存在");
        }
        Long orderInfoId = orderInfo.getOrderInfoId();
        ProjProjectInfo projectInfo = projectInfoMapper.selectByOrderId(orderInfoId);
        if (ObjectUtil.isNull(projectInfo)) {
            return Result.success("项目不存在");
        }
        Long projectInfoId = projectInfo.getProjectInfoId();
        //判断是删除还是新增还是置换
        Date now = new Date();
        if (req.getType() == 1) {
            //删除工单产品
            List<Long> productIds = req.getProductList().stream().map(ReplaceProductDTO::getProductId).collect(Collectors.toList());
            orderProductMapper.deleteByOrderInfoIdAndProductIds(orderInfoId, productIds);
            //查询deliverProductId-实施产品id
            //云健康升级产品需要排除
            List<Long> needDeleteList = new ArrayList<>();
            needDeleteList.add(5481L);
            needDeleteList.add(5482L);
            needDeleteList.add(5483L);
            needDeleteList.add(5484L);
            List<ProductIdContrastDTO> deliverProductContrasts = dictDeliverMapper.selectByProductIds(productIds);
            List<Long> deliverProductIds = new ArrayList<>();
            deliverProductIds.addAll(productIds);
            deliverProductIds.removeAll(needDeleteList);
            this.dealProductIdContrast(deliverProductIds, deliverProductContrasts);
            if (CollectionUtil.isNotEmpty(deliverProductContrasts)) {
                List<Long> delIds = deliverProductContrasts.stream().map(ProductIdContrastDTO::getContrastProductId).collect(Collectors.toList());
                //处理调研数据-删除需要删除的工单产品数据
                surveyPlanMapper.deleteByProjectAndProductIds(projectInfoId, delIds);
                //处理准备工作数据-删除需要删除的工单产品数据
                //待办主表-csm.proj_product_backlog
                productBacklogMapper.deleteByProjectAndProductIds(projectInfoId, delIds);
                //配置-csm.proj_product_config
                productConfigMapper.deleteByProjectAndProductIds(projectInfoId, delIds);
                //报表-csm.proj_survey_report
                surveyReportMapper.deleteByProjectAndProductIds(projectInfoId, delIds);
                //表单-csm.proj_survey_form
                surveyFormMapper.deleteByProjectAndProductIds(projectInfoId, delIds);
                //待处理任务-csm.proj_product_task
                productTaskMapper.deleteByProjectAndProductIds(projectInfoId, delIds);
                //里程碑任务-csm.proj_milestone_task_detail
                String code = "preparat_product";
                ProjMilestoneTask milestoneTask = milestoneTaskMapper.selectByProjectAndCode(projectInfoId, code);
                if (ObjectUtil.isNotNull(milestoneTask)) {
                    milestoneTaskDetailMapper.deleteByMTaskAndProducts(milestoneTask.getMilestoneTaskId(), delIds);
                }
            }
        } else if (req.getType() == 2) {
            for (ReplaceProductDTO replaceProduct : req.getProductList()) {
                //新增工单产品
                ProjOrderProductExtend orderProduct = new ProjOrderProductExtend(replaceProduct, projectInfoId, orderInfoId, now);
                orderProduct.setOrderProductId(SnowFlakeUtil.getId());
                orderProduct.setIsSpecial(req.getIsSpecial());
                if (Boolean.TRUE.equals(req.getIsSpecial())) {
                    orderProduct.setSpecialId(req.getSpecialId());
                    orderProduct.setProductBuyMode(0);
                    orderProduct.setProductSubscribeType(1);
                }
                orderProductMapper.insert(orderProduct);
            }
        }
        //处理产品对照
        this.dealProducts(projectInfoId);
        //处理上线步骤
        ProjOnlineStepDTO projOnlineStepDTO = new ProjOnlineStepDTO();
        projOnlineStepDTO.setProjectInfoId(projectInfoId);
        projOnlineStepDTO.setCustomInfoId(projectInfo.getCustomInfoId());
        onlineStepService.saveOnlineStep(projOnlineStepDTO);
        return Result.success();
    }

    /**
     * 转换合同
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result convertFormalContract(ConvertContractDTO dto) {
        log.info("转换合同, param: {}", JSON.toJSONString(dto));
        //合同处理数据处理-新增合同信息
        Date now = new Date();
        ProjContractInfo contractInfo = contractInfoMapper.selectByYyContractId(dto.getContractNum());
        if (ObjectUtil.isEmpty(contractInfo)) {
            Long contractInfoId = SnowFlakeUtil.getId();
            ProjContractInfoExtend contractInfoExtend = new ProjContractInfoExtend(dto, contractInfoId, -2L, now);
            contractInfoMapper.insert(contractInfoExtend);
        }
        //产品数据处理
        int count = orderProductMapper.updateContractId(dto.getContractNum(), dto.getProjProductIdList());
        /*if (count != dto.getProjProductIdList().size()) {
            throw new CustomException("转换合同, 产品数据处理失败, 产品数据数量不一致!");
        }*/
        log.info("转换合同, 产品数据处理完成, 影响行数: {}", count);
        return Result.success();
    }

    @Override
    public Result<List<HospitalInfo>> findHospitalInfo(HospitalInfoDTO dto) {
        // 判断客户是否存在
        ProjCustomInfo customInfo;
        try {
            customInfo = commonService.getCustomInfoByYyCustomerId(dto.getYyCustomerId());
        } catch (Throwable e) {
            log.error("运营获取医院信息. 查询客户信息异常. dto: {}, message: {}, e=", JSONUtil.toJsonStr(dto), e.getMessage(), e);
            throw new CustomException("查询客户信息异常, 程序报错: " + e.getMessage());
        }
        if (ObjectUtil.isEmpty(customInfo)) {
            log.error("运营获取医院信息. 未查询到客户信息. dto: {}", JSONUtil.toJsonStr(dto));
            throw new CustomException("不存在此客户.");
        }
        // 判断解决方案是否可用
        int projectType;
        try {
            projectType = getProjectType(dto.getSolutionType());
        } catch (Throwable e) {
            log.error("运营获取医院信息. 项目类型查询失败. message: {}, e=", e.getMessage(), e);
            throw new CustomException("解决方案不正确.");
        }
        log.info("运营获取医院信息. dto:{}, 获取到项目类型: {}, 获取到客户: {}", JSONUtil.toJsonStr(dto), projectType, JSONUtil.toJsonStr(customInfo));
        List<ProjProjectInfo> projectInfoList = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("project_type", projectType));
        if (CollUtil.isEmpty(projectInfoList)) {
            log.error("未查询到医院. customInfo: {}, projectType: {}", JSONUtil.toJsonStr(customInfo), projectType);
            throw new CustomException("该客户下不存在项目");
        }
        // 查询已开通的医院
        List<ProjHospitalInfoRelative> hospitalInfoRelatives = projHospitalInfoService.findOpenHospitalInfo(customInfo.getCustomInfoId(), projectType);
        if (CollUtil.isEmpty(hospitalInfoRelatives)) {
            log.error("运营获取医院信息, 未查询到医院信息. 获取到项目类型: {}, 获取到客户: {}", projectType, JSONUtil.toJsonStr(customInfo));
            throw new CustomException("用户未开通云资源.");
        }
        // 处理返回值
        List<HospitalInfo> hospitalInfos = hospitalInfoRelatives.stream().map(e -> HospitalInfo.builder().cloudHospitalId(e.getCloudHospitalId()).hospitalName(e.getHospitalName()).orgId(e.getOrgId()).build()).distinct().collect(Collectors.toList());
        log.info("运营获取医院信息. 提供的医院信息: {}", JSONUtil.toJsonStr(hospitalInfos));
        if (!CollUtil.isEmpty(hospitalInfos)) {
            log.info("运营获取医院信息，去重前数据={}", JSON.toJSONString(hospitalInfos));
            hospitalInfos = hospitalInfos.stream().filter(FilterUtil.distinctByKeys(item -> Arrays.asList(item.getCloudHospitalId(), item.getOrgId()))).collect(Collectors.toList());
        }
        return Result.success(hospitalInfos);
    }

    /**
     * 首付款是否满足回调
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> paySignageIsEnoughFuction(YunyingPaysignageReq dto) {

        // 记录日志
        sysOperLogService.apiOperLogInsert(dto, "首付款是否满足回调-参数", StrUtil.EMPTY, Log.LogOperType.FIX.getCode());

        // 已缴纳
        ProjContractInfo contractInfo = contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq("yy_contract_id", dto.getConOgId()));
        if (ObjectUtil.isEmpty(contractInfo)) {
            log.error("根据首付款缴纳接口返回值, 未查询到合同信息. yyContractId: {}", dto.getConOgId());
            return Result.fail("未查询到合同信息.");
        }
        SysUser sysUser = sysUserMapper.selectByAccount(dto.getAccount());
        // 若已缴纳更新合同首付款标识状态
        ProjContractInfo update = new ProjContractInfo();
        update.setPaySignage(dto.getConPreFlag());
        update.setConPreDesc(dto.getConPreDesc());
        update.setContractInfoId(contractInfo.getContractInfoId());
        update.setUpdateTime(new Date());
        if (sysUser != null && sysUser.getSysUserId() != null) {
            update.setUpdaterId(sysUser.getSysUserId());
        }
        int count = contractInfoMapper.updateById(update);
        log.info("同步运营平台合同首付款已缴纳状态. {}", count);
        return Result.success("操作成功");
    }

    /**
     * 更具解决方案查询项目类型
     *
     * @param solutionType 解决方案
     * @return 项目类型. 1:单体, 2:区域
     */
    public int getProjectType(Integer solutionType) {
        if (ProductSolutionEnum.DAN_TI.getCode().intValue() == solutionType || ProductSolutionEnum.JI_CENG_DAN_TI.getCode().intValue() == solutionType) {
            return ProjectTypeEnums.SINGLE.getCode();
        } else if (ProductSolutionEnum.QU_YU.getCode().intValue() == solutionType || ProductSolutionEnum.YI_GONG_TI.getCode().intValue() == solutionType || ProductSolutionEnum.QUAN_XIAN_YU.getCode().intValue() == solutionType) {
            return ProjectTypeEnums.REGION.getCode();
        }
        throw new CustomException("未查询到项目类型.");
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> kfSpecialApproveLogCallback(ApproveLogCallbackArgs args) {
        KfSpecialApproveLog logs = new KfSpecialApproveLog();
        logs.setId(IdGenerator.ins().generator());
        logs.setApproveId(args.getId());
        logs.setIsFinish(args.getIsFinish());
        logs.setStatus(args.getStatus());
        logs.setIsDeleted(0);
        KfSpecialApproveRecord record = kfSpecialApproveRecordMapper.selectById(args.getId());
        SysUser user = new LambdaQueryChainWrapper<>(sysUserMapper)
                .eq(SysUser::getAccount, args.getAccount())
                .eq(SysUser::getIsDeleted, 0)
                .one();
        if (user != null) {
            logs.setCreaterId(user.getSysUserId());
            logs.setCreateTime(new Date());
        }
        if (Boolean.TRUE.equals(args.getIsFinish()) && args.getStatus() == 2) {
            DictProjectReviewType dict = null;
            if (StrUtil.equals(record.getApproveCode(), "product")) {
                dict = new LambdaQueryChainWrapper<>(dictProjectReviewTypeMapper)
                        .eq(DictProjectReviewType::getDictCode, "cptssxsp")
                        .last("limit 1")
                        .one();
            }
            //后面还可以继续拓展不同业务查询不同的人员
            //审批结束设置下一步处理人
            if (dict != null) {
                List<ConfigProjectReviewTypeUser> approveMembers = new LambdaQueryChainWrapper<>(configProjectReviewTypeUserMapper)
                        .eq(ConfigProjectReviewTypeUser::getProjectReviewTypeId, dict.getProjectReviewTypeId())
                        .eq(ConfigProjectReviewTypeUser::getIsDeleted, 0)
                        .list();
                if (CollUtil.isNotEmpty(approveMembers)) {
                    Set<String> uidStr = approveMembers.stream().map(ConfigProjectReviewTypeUser::getReviewUserId).collect(Collectors.toSet());
                    Set<Long> uids = new HashSet<>();
                    for (String str : uidStr) {
                        List<String> split = StrUtil.split(str, ",");
                        for (String s : split) {
                            if (StrUtil.isBlank(s)) {
                                continue;
                            }
                            uids.add(Long.parseLong(s));
                        }
                    }
                    List<SysUser> mems = new LambdaQueryChainWrapper<>(sysUserMapper)
                            .in(SysUser::getSysUserId, uids)
                            .eq(SysUser::getIsDeleted, 0)
                            .list();
                    if (CollUtil.isNotEmpty(mems)) {
                        List<String> us = new ArrayList<>();
                        for (SysUser mem : mems) {
                            us.add(StrUtil.format("{}，电话号：{}", mem.getUserName(), mem.getPhone()));
                        }
                        args.setResult(StrUtil.format("{} - 后续处理人：{}", args.getResult(), StrUtil.join("，", us)));
                    }
                }
            }
        }
        logs.setResult(args.getResult());
        kfSpecialApproveLogMapper.insert(logs);
        if (Boolean.TRUE.equals(args.getIsFinish())) {
            record.setStatus(args.getStatus());
            record.setResult(args.getResult());
            if (user != null) {
                record.setUpdaterId(user.getSysUserId());
                record.setUpdateTime(new Date());
            }
            kfSpecialApproveRecordMapper.updateById(record);

            //发送消息
            try {
                if (StrUtil.equals(record.getApproveCode(), "product")) {
                    //特批产品发消息
                    if (args.getStatus() == 4) {
                        Set<Long> userIds = new HashSet<>();
                        userIds.add(464993509700489218L);
                        userIds.add(record.getCreaterId());
                        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                                .eq(ProjProjectInfo::getProjectNumber, record.getProjectNumber())
                                .one();
                        userIds.add(proj.getProjectLeaderId());
                        userIds.remove(-1L);
                        userIds.remove(null);
                        userIds.remove(0L);
                        // 消息内容替换的参数
                        Map<String, String> messageContentParam = new HashMap<>();
                        messageContentParam.put("projectName", proj.getProjectName());
                        messageContentParam.put("projectNumber", proj.getProjectNumber());
                        String productNames = args.getResult();
                        List<String> split = StrUtil.split(productNames, ",");
                        split.removeIf(s -> StrUtil.isBlank(s.trim()));
                        messageContentParam.put("number", String.valueOf(split.size()));
                        if (Integer.valueOf(1).equals(proj.getHisFlag())) {
                            messageContentParam.put("hisFlag", "（首期）");
                            messageContentParam.put("projectNode", "云资源划分及部署");
                        } else {
                            messageContentParam.put("hisFlag", "");
                            messageContentParam.put("projectNode", "产品申请开通");
                        }
                        // 产品名称
                        messageContentParam.put("productNames", productNames);

                        SendMessageParam messageParam = new SendMessageParam();
                        // 消息类型
                        messageParam.setMessageTypeId(DictMessageTypeEnum.SPECIAL_PRODUCT_MESSAGING.getId());
                        messageParam.setProjectInfoId(proj.getProjectInfoId());
                        messageParam.setMessageContentParam(messageContentParam);
                        messageParam.setSysUserIds(new ArrayList<>(userIds));
                        sendMessageService.sendMessage2(messageParam);
                    }
                }
            } catch (Throwable e) {
                log.error("特批产品添加完成，发送消息失败：", e);
            }
        }
        return Result.success();
    }

}
