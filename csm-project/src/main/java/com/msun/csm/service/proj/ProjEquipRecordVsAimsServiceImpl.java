package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.constants.DictEquipTypeConsts.DICT_EQUIP_COMM_MODE;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.constants.DictEquipTypeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.device.CommuntMode;
import com.msun.csm.common.enums.device.EquipStatusEnum;
import com.msun.csm.common.enums.projproduct.ProjProductEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictEquipAttributes;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjEquipFiles;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsAims;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.dao.entity.tmp.TmpEquipCheckDetail;
import com.msun.csm.dao.mapper.dict.DictEquipAttributesMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipFilesMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsAimsMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.dao.mapper.tmp.TmpEquipCheckDetailMapper;
import com.msun.csm.feign.client.analymanager.LisAnalyManagerClient;
import com.msun.csm.model.convert.ProjEquipFilesConvert;
import com.msun.csm.model.dto.AimsCloudEquipSelectDTO;
import com.msun.csm.model.dto.AimsComparedEquipSelectDTO;
import com.msun.csm.model.dto.AimsEquipSelectDTO;
import com.msun.csm.model.dto.DeleteEquipForLisDTO;
import com.msun.csm.model.dto.EquipRecordVsAimsExcelDTO;
import com.msun.csm.model.dto.EquipSendFilesToLisDTO;
import com.msun.csm.model.dto.EquipSendToLisDTO;
import com.msun.csm.model.dto.MultipartFileImpl;
import com.msun.csm.model.dto.OldEquipImportDTO;
import com.msun.csm.model.dto.ProjEquipFilesDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsAimsDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateAimsCloudEquipDTO;
import com.msun.csm.model.vo.AimsEquipSendCloudVO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.DictEquipInfoVO;
import com.msun.csm.model.vo.ProjEquipFilesVO;
import com.msun.csm.model.vo.ProjEquipRecordAimsResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsAimsVO;
import com.msun.csm.model.vo.ProjProjectFileRuleVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.dict.DictEquipCommService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.EasyExcelData;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.ObsObject;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/02/16:00
 */
@Slf4j
@Service
public class ProjEquipRecordVsAimsServiceImpl implements ProjEquipRecordVsAimsService {

    private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();

    private static Validator validator = validatorFactory.getValidator();

    @Resource
    private ProjEquipRecordVsAimsMapper projEquipRecordVsAimsMapper;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;

    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;

    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;

    @Resource
    private ProjEquipFilesMapper projEquipFilesMapper;

    @Resource
    private DictEquipAttributesMapper dictEquipAttributesMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private DictEquipCommService dictEquipCommService;

    @Resource
    private RuleProjectRuleConfigMapper ruleProjectRuleConfigMapper;

    @Resource
    private ProjEquipFilesConvert projEquipFilesConvert;

    @Resource
    private ProjEquipSummaryService equipSummaryService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private LisAnalyManagerClient lisAnalyManagerClient;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Value("${project.obs.bucketName}")
    private String bucketName;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private ProjEquipRecordCommonService equipRecordCommonService;

    @Resource
    private CommonService commonService;

    @Resource
    private ProjEquipRecordMapper equipRecordMapper;

    @Resource
    private TmpEquipCheckDetailMapper tmpEquipCheckDetailMapper;

    @Resource
    private ProjEquipRecordLogService projEquipRecordLogService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    /**
     * 查询手麻设备数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjEquipRecordAimsResultVO> selectEquipRecordVsAimsList(AimsEquipSelectDTO dto) {
        ProjEquipRecordAimsResultVO projEquipRecordAimsResultVO = new ProjEquipRecordAimsResultVO();
        List<ProjEquipRecordVsAimsVO> projEquipRecordVsAimsVOS =
                projEquipRecordVsAimsMapper.selectEquipRecordVsAimsList(dto);
        for (ProjEquipRecordVsAimsVO vo : projEquipRecordVsAimsVOS) {
            // 组装前端展示的设备名称
            vo.setEquipModelNameStr(vo.getEquipFactoryName() + "/" + vo.getEquipTypeName() + "/" + vo.getEquipModelName());
            // 设置是否必须对接
            vo.setRequiredFlagBool(vo.getRequiredFlag() == 1);
            //回显通讯模式对象
            BaseCodeNameResp baseIdNameResp = new BaseCodeNameResp();
            baseIdNameResp.setId(vo.getCommModeKey());
            baseIdNameResp.setName(vo.getCommMode());
            vo.setCommModeObj(baseIdNameResp);
        }
        projEquipRecordAimsResultVO.setProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
        projEquipRecordAimsResultVO.setEquipRecordVsAimsVOList(projEquipRecordVsAimsVOS);
        return Result.success(projEquipRecordAimsResultVO);
    }

    /**
     * 根据手麻设备记录id查询手麻设备数据
     *
     * @param equipRecordVsAimsId
     * @return
     */
    @Override
    public Result<ProjEquipRecordVsAimsVO> getEquipRecordVsAimsById(Long equipRecordVsAimsId, Integer isMobile) {
        ProjEquipRecordVsAimsVO projEquipRecordVsAimsVO = new ProjEquipRecordVsAimsVO();
        //查询LIS设备信息
        ProjEquipRecordVsAims projEquipRecordVsAims = projEquipRecordVsAimsMapper.selectById(equipRecordVsAimsId);
        if (ObjectUtil.isEmpty(projEquipRecordVsAims)) {
            return Result.fail("未查询到设备记录信息！");
        }
        BeanUtil.copyProperties(projEquipRecordVsAims, projEquipRecordVsAimsVO);
        //查询设备记录信息
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectById(projEquipRecordVsAims.getEquipRecordId());
        BeanUtil.copyProperties(projEquipRecord, projEquipRecordVsAimsVO);
        // 查询对应的医院信息
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(projEquipRecord.getHospitalInfoId());
        if (ObjectUtil.isNotEmpty(hospitalInfo)) {
            projEquipRecordVsAimsVO.setHospitalInfoName(hospitalInfo.getHospitalName());
        }
        //查询附件信息 【需要判断是否为 手机端进行查看。当手机端查看时  只查询图片】
        List<ProjEquipFilesVO> fileList = Lists.newArrayList();
        fileList = projEquipFilesMapper.findEquipFiles(equipRecordVsAimsId, isMobile);
        if (CollectionUtil.isNotEmpty(fileList)) {
            //获取附件临时访问路径
            fileList.forEach(v -> {
                if (StringUtils.isNotEmpty(v.getFilePath())) {
                    String temporaryUrlFile = OBSClientUtils.getTemporaryUrl(v.getFilePath(), 3600);
                    v.setFilePath(temporaryUrlFile);
                }
                //返回上传附件限制类型
                if (ObjectUtil.isNotEmpty(v.getLimitType())) {
                    v.setLimitTypeArray(v.getLimitType().split(","));
                }
                v.setProjectFileId(v.getProjectFileId() == 0
                        ? null : v.getProjectFileId());
            });
        } else {
            //老系统导入的设备记录没有附件列表，需要查询附件模版(不含文件, 只含要上传文件的规则内容)返回给前端
            List<ProjProjectFileRuleVO> ruleList = ruleProjectRuleConfigMapper.getTemplateByProductCode(
                    ProjProductEnum.AIMS.getFileSceneCode(), isMobile);
            for (ProjProjectFileRuleVO projProjectFileRuleVO : ruleList) {
                ProjEquipFilesVO projEquipFilesVO = new ProjEquipFilesVO();
                BeanUtil.copyProperties(projProjectFileRuleVO, projEquipFilesVO);
                //返回上传附件限制类型
                if (ObjectUtil.isNotEmpty(projProjectFileRuleVO.getLimitType())) {
                    projEquipFilesVO.setLimitTypeArray(projEquipFilesVO.getLimitType().split(","));
                }
                fileList.add(projEquipFilesVO);
            }
        }
        projEquipRecordVsAimsVO.setEquipFileList(fileList);
        // 医院id为 -1 时 赋值为null
        if (projEquipRecordVsAimsVO.getHospitalInfoId() != null && projEquipRecordVsAimsVO.getHospitalInfoId() == -1) {
            projEquipRecordVsAimsVO.setHospitalInfoId(null);
        }
        //通讯方式单独处理
        BaseCodeNameResp baseCodeNameResp = new BaseCodeNameResp();
        baseCodeNameResp.setId(projEquipRecord.getCommModeKey());
        baseCodeNameResp.setName(projEquipRecord.getCommMode());
        projEquipRecordVsAimsVO.setCommModeObj(baseCodeNameResp);
        return Result.success(projEquipRecordVsAimsVO);
    }

    /**
     * ip正则
     */
    private static final String IP_REGEX = "^((25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(25[0-5]|2[0-4]\\d|("
            + "(1\\d{2})|"
            + "([1-9]?\\d)"
            + "))$";
    /**
     * 端口正则
     */
    private static final String PORT_REGEX = "^([0-9]|[1-9]\\d{1,3}|[1-5]\\d{4}|6[0-4]\\d{4}|65[0-4]\\d{2}|655[0-2]\\d"
            + "|6553[0-5])$";
    /**
     * 协议正则
     */
    private static final String PROTOTYPE_REGEX = "^[0-9]{0,2}$";

    /**
     * 新增或更新保存手麻设备信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateAimsEquip(ProjEquipRecordVsAimsDTO dto) {
        // 判断必填项
        if (ObjectUtil.isNotEmpty(dto.getCenterMonDeviceFlag()) && dto.getCenterMonDeviceFlag() == NumberEnum.NO_1.num().intValue()) {
            if (ObjectUtil.isEmpty(dto.getCenterMonDeviceId())) {
                throw new CustomException("请填写中央监护仪id.");
            }
        }
        if (StrUtil.isNotBlank(dto.getEquipIp())) {
            // ip校验规则
            if (!ReUtil.isMatch(IP_REGEX, dto.getEquipIp())) {
                throw new CustomException("请输入正确的监听仪ip地址");
            }
        }
        if (StrUtil.isNotBlank(dto.getListenIp())) {
            // ip校验规则
            if (!ReUtil.isMatch(IP_REGEX, dto.getListenIp())) {
                throw new CustomException("请输入正确的监听ip地址");
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getListenPort())) {
            // 监听端口号
            if (!ReUtil.isMatch(PORT_REGEX, dto.getListenPort().toString())) {
                throw new CustomException("请输入正确的监听端口号");
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getMonDeviceConfigPort())) {
            // 监听端口号
            if (!ReUtil.isMatch(PORT_REGEX, dto.getMonDeviceConfigPort().toString())) {
                throw new CustomException("请输入正确的监护仪配置端口");
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getProtocolType())) {
            // 监听端口号
            if (!ReUtil.isMatch(PROTOTYPE_REGEX, dto.getProtocolType().toString())) {
                throw new CustomException("请输入正确的传输协议(数字类型)");
            }
        }
        //保存设备记录信息
        ProjEquipRecord projEquipRecord = new ProjEquipRecord();
        BeanUtil.copyProperties(dto, projEquipRecord);
        //设备类型
        projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_TYPE,
                dto.getEquipTypeName()));
        //设备厂商
        projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY, dto.getEquipFactoryName()));
        //设备型号
        projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO,
                dto.getEquipModelName()));
        // todo 若需要获取系列id, 则需补全功能. 手麻对接待使用
        //通讯方式
        projEquipRecord.setCommModeKey(dto.getCommModeObj().getId());
        projEquipRecord.setCommMode(dto.getCommModeObj().getName());
        projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
        // 保存设备公共表数据
        if (ObjectUtil.isNotEmpty(dto.getEquipRecordId())) {
            projEquipRecordMapper.updateById(projEquipRecord);
        } else {
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecordMapper.insert(projEquipRecord);
        }
        // 保存手麻设备记录表数据
        ProjEquipRecordVsAims projEquipRecordVsAims = new ProjEquipRecordVsAims();
        BeanUtil.copyProperties(dto, projEquipRecordVsAims);
        projEquipRecordVsAims.setEquipRecordId(projEquipRecord.getEquipRecordId());
        if (ObjectUtil.isNotEmpty(dto.getEquipRecordVsAimsId())) {
            int count = projEquipRecordVsAimsMapper.updateAimsById(projEquipRecordVsAims);
            log.info("更新aims设备. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipRecordVsAims));
        } else {
            projEquipRecordVsAims.setEquipRecordVsAimsId(SnowFlakeUtil.getId());
            projEquipRecordVsAimsMapper.insert(projEquipRecordVsAims);
        }
        //保存设备附件
        List<ProjEquipFilesDTO> equipFileList = dto.getEquipFileList();
        if (CollectionUtil.isNotEmpty(equipFileList)) {
            // 保存手麻附件数据  删除原附件信息，重新保存。
            List<String> fileCodeList =
                    equipFileList.stream().map(ProjEquipFilesDTO::getFileTypeCode).collect(Collectors.toList());
            // 查询附件模板数据
            List<RuleProjectRuleConfig> ruleProjectRuleConfigs =
                    ruleProjectRuleConfigMapper.selectList(new QueryWrapper<RuleProjectRuleConfig>()
                            .eq("scene_code", dto.getEquipFileList().get(0).getFileItemCode())
                            .notIn("project_rule_code", fileCodeList)
                    );
            if (CollectionUtil.isNotEmpty(ruleProjectRuleConfigs)) {
                for (RuleProjectRuleConfig ruleProjectRuleConfig : ruleProjectRuleConfigs) {
                    // 判断当前附件是否已在业务表 存在
                    List<ProjEquipFiles> projEquipFiles =
                            projEquipFilesMapper.selectList(new QueryWrapper<ProjEquipFiles>()
                                    .eq("file_type_code", ruleProjectRuleConfig.getProjectRuleCode())
                                    .eq("product_equip_record_id", projEquipRecordVsAims.getEquipRecordVsAimsId())
                            );
                    if (CollectionUtil.isEmpty(projEquipFiles)) {
                        ProjEquipFilesDTO projEquipFilesDTO = new ProjEquipFilesDTO();
                        projEquipFilesDTO.setFileTypeCode(ruleProjectRuleConfig.getProjectRuleCode());
                        projEquipFilesDTO.setFileTypeName(ruleProjectRuleConfig.getProjectRuleContent());
                        projEquipFilesDTO.setFileItemCode(ruleProjectRuleConfig.getSceneCode());
                        projEquipFilesDTO.setOrderNo(ruleProjectRuleConfig.getOrderNo());
                        projEquipFilesDTO.setRequiredFlag(ruleProjectRuleConfig.getRequiredFlag());
                        equipFileList.add(projEquipFilesDTO);
                    }
                }
            }
            for (ProjEquipFilesDTO projEquipFilesDTO : equipFileList) {
                ProjEquipFiles projEquipFiles = projEquipFilesConvert.dto2Po(projEquipFilesDTO);
                if (ObjectUtil.isEmpty(projEquipFiles.getEquipFilesId())) {
                    projEquipFiles.setEquipFilesId(SnowFlakeUtil.getId());
                    projEquipFiles.setProductEquipRecordId(projEquipRecordVsAims.getEquipRecordVsAimsId());
                    projEquipFiles.setHospitalInfoId(dto.getHospitalInfoId());
                    projEquipFiles.setProjectInfoId(projEquipRecord.getProjectInfoId());
                    projEquipFilesMapper.insert(projEquipFiles);
                } else {
                    projEquipFilesMapper.updateById(projEquipFiles);
                }
            }
        }
        if (ObjectUtil.isEmpty(dto.getEquipRecordVsAimsId())) {
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(projEquipRecordVsAims.getEquipRecordVsAimsId());
            projEquipRecordLog.setOperateName("申请设备");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
        }
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());

        return Result.success();
    }

    /**
     * 根据主键id删除手麻设备
     *
     * @param equipRecordVsAimsId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteAimsEquip(Long equipRecordVsAimsId) {
        ProjEquipRecordVsAims projEquipRecordVsAims = projEquipRecordVsAimsMapper.selectById(equipRecordVsAimsId);
        // 删除手麻设备记录表数据
        projEquipRecordVsAimsMapper.deleteById(equipRecordVsAimsId);
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectById(projEquipRecordVsAims.getEquipRecordId());
        // 删除公共表数据
        projEquipRecordMapper.deleteById(projEquipRecordVsAims.getEquipRecordId());
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
        // 删除手麻设备的 附件数据
        projEquipFilesMapper.delete(new QueryWrapper<ProjEquipFiles>().eq("product_equip_record_id",
                equipRecordVsAimsId));

        //projProjectPlanService.addTotalCountByProjectInfoIdAndItemCode(projEquipRecord.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_DEVICE, -1);
        return Result.success();
    }

    /**
     * 申请 、 一键提交申请
     *
     * @param dto
     * @return
     */
    @Override
    public Result equipSendToAimsAnaly(AimsEquipSelectDTO dto) {
        try {
            for (Long id : dto.getEquipRecordVsAimsIdList()) {
                dto.setEquipRecordVsAimsId(id);
                // 查询手麻设备信息
                ProjEquipRecordVsAimsVO aimsEquipVO =
                        projEquipRecordVsAimsMapper.selectEquipRecordVsAimsList(dto).get(0);
                if (ObjectUtil.isEmpty(aimsEquipVO.getHospitalInfoId())) {
                    return Result.fail("未查询到医院信息，请先修改来源医院！");
                }
                // 附件信息查询
                List<ProjEquipFiles> list = projEquipFilesMapper.selectList(new QueryWrapper<ProjEquipFiles>()
                        .eq("product_equip_record_id", id));
                // 根据手麻数据校验数据完整性 查看附件是否都已经上传
                Result result = checkFileToAims(list, aimsEquipVO);
                if (!result.isSuccess()) {
                    return Result.fail(result.getMsg());
                }
                // 把手麻设备信息发送到LIS解析平台
                EquipSendToLisDTO equipSendToLis = new EquipSendToLisDTO();
                // 组装设备信息
                infoDataForAims(aimsEquipVO, equipSendToLis);
                JSONObject entries = lisAnalyManagerClient.insertEquip(equipSendToLis.getId(), equipSendToLis);
                log.info("设备发送结果: {}", entries);
                if (!(Boolean) entries.get("success")) {
                    throw new RuntimeException("设备发送失败 , " + entries.get("message"));
                }
                // 设置附件参数。。只能进行单个发送
                for (ProjEquipFiles file : list) {
                    //跳过没有选择附件的对象
                    if (file.getProjectFileId() == 0) {
                        continue;
                    }
                    EquipSendFilesToLisDTO d = new EquipSendFilesToLisDTO();
                    d.setId(file.getProductEquipRecordId());
                    // 获取obs上的文件，转换成文件流
                    ProjProjectFile projProjectFile = projProjectFileMapper.selectById(file.getProjectFileId());
                    MultipartFile multipartFile = convertOBSUrlToMultipartFile(projProjectFile.getFilePath(),
                            projProjectFile.getFileName());
                    switch (file.getFileTypeCode()) {
                        case "agreement_text":
                            //通讯协议文档
                            d.setFile1(multipartFile);
                            break;
                        case "equip_nameplate":
                            // 仪器铭牌图片
                            d.setFile5(multipartFile);
                            break;
                        case "yuanshichuan":
                            // 原始串
                            d.setFile21(multipartFile);
                            break;
                        default:
                            // 普通结果
                            break;
                    }
                    try {
                        JSONObject entrie = lisAnalyManagerClient.uploadFiles(d.getId(), null, d.getFile1(),
                                d.getFile2(), d.getFile21(),
                                d.getFile22(), d.getFile23(), d.getFile24(), d.getFile25(), d.getFile3(), d.getFile4(),
                                d.getFile5(), d.getFile6(), d.getFile7(), d.getFile13());
                        log.info("LIS开放平台上传附件信息 结束 , " + entrie);
                    } catch (Exception e) {
                        log.info("LIS开放平台上传附件信息失败 ， " + e);
                    }
                }
                ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                projEquipRecord.setEquipRecordId(aimsEquipVO.getEquipRecordId());
                projEquipRecord.setEquipStatus(1);
                projEquipRecord.setApplyTime(new Date());
                projEquipRecordMapper.updateById(projEquipRecord);

                equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
                //保存操作日志
                ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
                projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
                projEquipRecordLog.setEquipRecordBusinessId(id);
                projEquipRecordLog.setOperateName("申请设备");
                projEquipRecordLogService.saveLog(projEquipRecordLog);
            }
            return Result.success("设备发送成功");
        } catch (Exception e) {
            log.info("设备发送失败, {}", e);
            return Result.fail("设备发送失败 , " + e.getMessage());
        }
    }

    /**
     * 撤销手麻接口申请
     *
     * @param equipRecordVsAmisId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result equipRevokeToAims(Long equipRecordVsAmisId) {
        try {
            // 查询该接口数据
            ProjEquipRecordVsAims projEquipRecordVsAims = projEquipRecordVsAimsMapper.selectById(equipRecordVsAmisId);
            // 撤销LIS解析平台的接口申请
            DeleteEquipForLisDTO deleteEquipForLisDTO = new DeleteEquipForLisDTO();
            deleteEquipForLisDTO.setId(equipRecordVsAmisId);
            deleteEquipForLisDTO.setOperatorId(Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()));
            JSONObject entries = lisAnalyManagerClient.cancelEquipApply(deleteEquipForLisDTO.getId(),
                    deleteEquipForLisDTO.getOperatorId());
            log.info("Lis开发平台撤销设备结果: {}", entries);
            if (!(Boolean) entries.get("success")) {
                throw new RuntimeException("Lis开发平台撤销设备失败 , " + entries.get("message"));
            }
            // 更新公共表该设备的状态为 0
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipRecordId(projEquipRecordVsAims.getEquipRecordId());
            projEquipRecord.setEquipStatus(0);
            projEquipRecordMapper.updateById(projEquipRecord);
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(equipRecordVsAmisId);
            projEquipRecordLog.setOperateName("撤销申请");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
            return Result.success();
        } catch (Exception e) {
            log.info("撤销失败 , {}", e);
            return Result.fail("撤销失败 , " + e.getMessage());
        }
    }

    /**
     * 根据obs链接转换成文件格式
     *
     * @param obsUrl
     * @param fileName
     * @return
     * @throws IOException
     */
    public MultipartFile convertOBSUrlToMultipartFile(String obsUrl, String fileName) throws IOException {
        List<ObsObject> obsObjectList = OBSClientUtils.getAllObjects(Collections.singletonList(obsUrl));
        if (CollUtil.isEmpty(obsObjectList)) {
            log.warn("obs中未查询到要下载的文件. filePath: {}", obsUrl);
            throw new RuntimeException("obs中未查询到要下载的文件. filePath: " + obsUrl);
        }
        ObsObject obsObject = obsObjectList.get(0);
        String objectKey = obsObject.getObjectKey();
        obsObject = OBSClientUtils.getObsClient().getObject(bucketName, objectKey);
        // 下载到本地, 修改文件名再读取
        InputStream in = obsObject.getObjectContent();
        try (InputStream inputStream = in) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[16384]; // 16Kb 的缓冲区
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] fileContent = buffer.toByteArray();
            // 创建并返回自定义的MultipartFile实现
            return new MultipartFileImpl(fileName, fileName, "application/octet-stream", fileContent);
        }
    }

    /**
     * LIS设备附件信息检测
     *
     * @param list
     * @param projEquipRecordVsAimsVO
     * @return
     */
    private Result checkFileToAims(List<ProjEquipFiles> list, ProjEquipRecordVsAimsVO projEquipRecordVsAimsVO) {
        StringBuilder sb = new StringBuilder();
        sb.append("设备：" + projEquipRecordVsAimsVO.getEquipModelName());
        // 检查设备是否缺少图片上传
        if (list.stream().noneMatch(x -> "agreement_text".equals(x.getFileTypeCode()))) {
            sb.append("缺少协议文档;");
        }
        for (ProjEquipFiles file : list) {
            //  必传数据校验
            if ("agreement_text".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("协议文档不能为空;");
                }
            }
        }
        if (sb.indexOf("为空") != -1 || sb.indexOf("缺少") != -1) {
            return Result.fail(sb + "请修改后重新提交。");
        } else {
            return Result.success();
        }
    }

    /**
     * 组装LIS开发平台数据
     *
     * @param aimsEquipVO
     * @param equipSendToLis
     */
    private void infoDataForAims(ProjEquipRecordVsAimsVO aimsEquipVO, EquipSendToLisDTO equipSendToLis) {
        equipSendToLis.setId(aimsEquipVO.getEquipRecordVsAimsId());
        equipSendToLis.setEquipClassId(aimsEquipVO.getEquipTypeId());
        equipSendToLis.setEquipFactoryId(aimsEquipVO.getEquipFactoryId());
        equipSendToLis.setEquipNameId(aimsEquipVO.getEquipInfoId());
        // 手填厂商
        Result<List<BaseIdNameResp>> lisEquipFactory = equipSummaryService.selectSurveyEquipFactory("Lis",
                aimsEquipVO.getEquipFactoryId());
        equipSendToLis.setUnreviewedEquipFactoryName(CollectionUtil.isEmpty(lisEquipFactory.getData())
                ? aimsEquipVO.getEquipFactoryName() : null);
        // 手填类型
        Result<List<BaseIdNameResp>> lisEquipType = equipSummaryService.selectSurveyEquipType(
                "AIMS", aimsEquipVO.getEquipTypeId()
        );
        equipSendToLis.setUnreviewedEquipClassName(CollectionUtil.isEmpty(lisEquipType.getData())
                ? aimsEquipVO.getEquipTypeName() : null);
        // 手填型号
        Result<List<DictEquipInfoVO>> lisEquipInfo = equipSummaryService.selectSurveyEquipInfo(
                "AIMS", aimsEquipVO.getEquipInfoId()
        );
        equipSendToLis.setUnreviewedEquipName(CollectionUtil.isEmpty(lisEquipInfo.getData())
                ? aimsEquipVO.getEquipModelName() : null);
        equipSendToLis.setHisCreaterId(Convert.toLong(userHelper.getCurrentUser().getSysUserId()));
        equipSendToLis.setHisUpdaterId(Convert.toLong(userHelper.getCurrentUser().getSysUserId()));
        equipSendToLis.setLinkman(null);
        equipSendToLis.setTelephone(aimsEquipVO.getEquipFactoryPhone());
        equipSendToLis.setCustomerId(aimsEquipVO.getCustomInfoId());
        equipSendToLis.setStatus(1);
        equipSendToLis.setProductId(aimsEquipVO.getYyProductId());
        // 查询通讯方式
        equipSendToLis.setDataPipe(aimsEquipVO.getCommModeKey());
        equipSendToLis.setCommModelName(aimsEquipVO.getCommMode());
        //查询客户名称
        ProjCustomInfo customInfo = customInfoMapper.selectById(aimsEquipVO.getCustomInfoId());
        equipSendToLis.setCustomerName(customInfo.getCustomName());
        equipSendToLis.setDataSource(3);
        equipSendToLis.setLocation(aimsEquipVO.getEquipPosition());
        equipSendToLis.setContent(aimsEquipVO.getMemo());
        // 查询产品名称
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("yy_product_id",
                aimsEquipVO.getYyProductId()));
        equipSendToLis.setProductName(product.getProductName());

    }


    /**
     * 老换新设备字典获取
     *
     * @param dto
     * @return
     */
    @Override
    public Result executeImportForAims(OldEquipImportDTO dto) {
        if (!dto.getProductCode().equals(ProjProductEnum.AIMS.getProductCode())) {
            return Result.fail("请检查老换新导入的设备是否为手麻设备");
        }
        if (StringUtils.isBlank(dto.getJsonData())) {
            return Result.fail("未查询到需要导入的设备数据");
        }
        JSONArray jsonArray = JSON.parseArray(dto.getJsonData());
        for (Object jsonObj : jsonArray) {
            com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonObj;
            StringBuilder memoBuilder = new StringBuilder("老系统导入设备");
            //封装设备记录实体
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipModelName(jsonObject.getString("具体型号"));
            //通讯方式
            DictEquipAttributes dictEquipAttributes =
                    dictEquipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                            .eq("equip_attributes_code", DICT_EQUIP_COMM_MODE)
                            .eq("equip_attributes_value", jsonObject.getString("通讯方式"))
                            .last(" limit 1"));
            if (ObjectUtil.isNotEmpty(dictEquipAttributes)) {
                projEquipRecord.setCommModeKey(dictEquipAttributes.getEquipAttributesKey());
                projEquipRecord.setCommMode(dictEquipAttributes.getEquipAttributesValue());
            }
            //医院
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_name", jsonObject.getString("医院名称")).last("limit 1"));
            if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                projEquipRecord.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
            } else {
                // 未查询到医院取-1, 手动再进行修改
                projEquipRecord.setHospitalInfoId(-1L);
                memoBuilder.append(",医院名称：").append(jsonObject.getString("医院名称"));
            }
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
            projEquipRecord.setCustomInfoId(dto.getCustomInfoId());
            projEquipRecord.setProjectInfoId(dto.getProjectInfoId());
            projEquipRecord.setMemo(memoBuilder.toString());
            projEquipRecord.setEquipStatus(0);
            // 查询通讯方式
            List<BaseCodeNameResp> resps = equipRecordMapper.selectSurveyEquipAttributes(DICT_EQUIP_COMM_MODE);
            if (CollUtil.isNotEmpty(resps)) {
                resps = resps.stream().filter(e -> StrUtil.equals(e.getId(),
                        CommuntMode.SOCKET.getKey())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(resps)) {
                    BaseCodeNameResp resp = resps.get(0);
                    projEquipRecord.setCommMode(resp.getName());
                    projEquipRecord.setCommModeKey(resp.getId());
                }
            }
            projEquipRecord.setCloudEquipId(Convert.toLong(jsonObject.getString("云健康仪器id")));
            projEquipRecord.setCloudEquipName(jsonObject.getString("具体型号"));
            int count = projEquipRecordMapper.insert(projEquipRecord);
            log.info("新增手麻设备数据. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipRecord));
            //封装LIS设备记录实体
            ProjEquipRecordVsAims projEquipRecordVsAims = new ProjEquipRecordVsAims();
            projEquipRecordVsAims.setEquipRecordVsAimsId(SnowFlakeUtil.getId());
            projEquipRecordVsAims.setEquipRecordId(projEquipRecord.getEquipRecordId());
            projEquipRecordVsAims.setOldAimsImportFlag(1);
            projEquipRecordVsAims.setEquipIp(jsonObject.getString("设备IP"));
            String port = jsonObject.getString("端口");
            projEquipRecordVsAims.setMonDeviceConfigPort(StrUtil.isNotBlank(port) ? Integer.parseInt(port) : null);
            projEquipRecordVsAims.setListenIp(jsonObject.getString("监听IP"));
            String lisPort = jsonObject.getString("监听端口");
            projEquipRecordVsAims.setListenPort(StrUtil.isNotBlank(lisPort) ? Integer.parseInt(lisPort) : null);
            projEquipRecordVsAims.setMonitorCode(jsonObject.getString("编号"));
            String protocolType = jsonObject.getString("传输协议");
            projEquipRecordVsAims.setProtocolType(StrUtil.isNotEmpty(protocolType) ? Integer.parseInt(protocolType)
                    : null);
            // 老平台数据的类型做系列使用
            projEquipRecordVsAims.setSeriesName(jsonObject.getString("类型"));
            count = projEquipRecordVsAimsMapper.insert(projEquipRecordVsAims);
            log.info("新增手麻设备拓展数据. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipRecordVsAims));
        }
        return Result.success();
    }

    /**
     * 手麻设备一键检测
     *
     * @param dto
     * @return
     */
    @Override
    public Result aimsEquipCheckForLisAnaly(AimsEquipSelectDTO dto) {
        ProjProjectInfo projectInfo = commonService.getProjectInfo(dto.getProjectInfoId());
        dto.setProjectType(projectInfo.getProjectType());

        //查询设备记录(不包含已检测通过的记录)
        List<ProductEquipmentDto> equipmentDataDto = projEquipRecordVsAimsMapper.findToMsunEquipRecord(dto);
        if (CollectionUtil.isEmpty(equipmentDataDto)) {
            return Result.success();
        }

        // 有云健康设备id的设备（发送对照或从云健康手动对照）的正常走流程，没有云健康设备id的进行提示进行对照
        List<ProductEquipmentDto> equipmentData = equipmentDataDto.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOldEquipId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(equipmentData)) {
            // 查询项目类型, 对照环境
            ProjHospitalInfo hospitalInfo = equipRecordCommonService.getHospitalInfo(dto.getCustomInfoId(), projectInfo.getProjectType());
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                return Result.fail("请检查客户下的医院信息");
            }
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            String productCode = "AIMS";
            try {
                // 调用云健康接口一键检测
                Map<Long, EquipmentStatusWrapperResult> mapData = equipRecordCommonService.getEquipmentStatusBatch(productCode, hospitalInfo, equipmentData);
                if (MapUtil.isEmpty(mapData)) {
                    return Result.fail("未查询到设备检测状态.");
                }
                List<ProjHospitalInfo> hospitalInfoList = equipRecordCommonService.findHospitalInfos(dto.getCustomInfoId(), projectInfo.getProjectType());
                // 更新设备状态
                equipRecordCommonService.updateEquipStatus(mapData, hospitalInfoList, dto.getProjectInfoId(), productCode, equipmentData);
                equipRecordCommonService.updateEquipProjectTodoTaskStatus(projectInfo.getProjectInfoId(), null, ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
            } catch (Exception e) {
                log.error("更新手麻设备检测状态, 异常. message: {}, e=", e.getMessage(), e);
                return Result.fail(e.getMessage());
            }
        }

        // 未对照过云健康设备的进行提示
        List<ProductEquipmentDto> unSendCloudEquipData = equipmentDataDto.stream().filter(e -> ObjectUtil.isEmpty(e.getOldEquipId())).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (ProductEquipmentDto item : unSendCloudEquipData) {
            if (ObjectUtil.isEmpty(item.getOldEquipId())) {
                sb.append(item.getModel() + "未对照云健康设备 \n");
            }
        }
        if (sb.length() > 0) {
//            return Result.fail(sb.toString());
            return Result.fail(102, "存在未对照的设备，请进行云健康设备对照后进行检测");
        }

        return Result.success();
    }

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    @Override
    public Result commitFinish(ProjEquipVsProductFinishDTO dto) {
        //校验是否可以提交完成
        // 判断节点类型【调研阶段、准备阶段】
        Result<ProjMilestoneInfo> milestoneInfoResult = milestoneInfoService.selectById(dto.getMilestoneInfoId());
        String milestoneNodeCode = milestoneInfoResult.getData().getMilestoneNodeCode();
        Long projectInfoId1 = milestoneInfoResult.getData().getProjectInfoId();
        String nodeCode = "";
        if (MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(0);
            statusList.add(2);
            nodeCode = MilestoneNodeEnum.SURVEY_DEVICE.getCode();
        } else if (MilestoneNodeEnum.PREPARAT_PRODUCT.getCode().equals(milestoneNodeCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(0);
            statusList.add(1);
            statusList.add(2);
            statusList.add(3);
            statusList.add(4);
            statusList.add(6);
            Integer count = projEquipRecordVsAimsMapper.getNotApplyRecordCount(statusList, projectInfoId1);
            if (count > 0) {
                return Result.fail("存在需要对接的设备未测试通过,请全部测试通过后再进此操作");
            }
            nodeCode = MilestoneNodeEnum.PREPARAT_DEVICE.getCode();
        }
        //更新任务计划明细状态
        List<ProjMilestoneTask> projMilestoneTasks =
                milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                        .eq("project_info_id", milestoneInfoResult.getData().getProjectInfoId())
                        .eq("milestone_node_code", nodeCode)
                );
        if (CollectionUtil.isEmpty(projMilestoneTasks)) {
            return Result.fail("请先分配产品业务调研后再进行提交完成");
        }
        //查询手麻产品id
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                ProjProductEnum.AIMS.getProductName()).last(" limit 1"));
        List<Long> collect =
                projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
        List<ProjMilestoneTaskDetail> projMilestoneTaskDetails =
                milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("product_deliver_id", product.getYyProductId())
                        .in("milestone_task_id", collect)
                );
        if (CollectionUtil.isEmpty(projMilestoneTaskDetails)) {
            return Result.fail(MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)
                    ? "手麻产品未查询到对应任务,请在产品调研重新指定责任人" : "手麻产品未查询到对应任务");
        }
        for (ProjMilestoneTaskDetail taskDetail : projMilestoneTaskDetails) {
            taskDetail.setCompleteStatus(1);
            milestoneTaskDetailMapper.updateById(taskDetail);
        }
        projTodoTaskService.updateTodoTaskStatusOne(projectInfoId1, DictProjectPlanItemEnum.SURVEY_DEVICE, product.getYyProductId(), ProjectPlanStatusEnum.FINISHED);
        return Result.success();
    }

    /**
     * 发送到云健康
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> aimsEquipSendCloudEquip(AimsEquipSelectDTO dto) {
        // 获取项目类型
        List<ProjHospitalInfo> hospitalInfoList = equipRecordCommonService.findHospitalInfos(dto.getCustomInfoId(),
                dto.getProjectInfoId());
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            return Result.fail("请检查客户下的医院信息");
        }
        if (ObjectUtil.isEmpty(hospitalInfoList.get(0).getCloudHospitalId())) {
            return Result.fail("该项目未部署,禁止对照");
        }
        ProjHospitalInfo hospitalInfo = hospitalInfoList.get(0);
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        //查询发送到云健康设备列列表
        List<ProductEquipmentDto> equipments = new ArrayList<>();
        List<AimsEquipSendCloudVO> aimsEquipSendCloudVOList =
                projEquipRecordVsAimsMapper.selectAimsEquipSendCloudData(dto);
        // 处理需要发送云健康的数据
        for (AimsEquipSendCloudVO d : aimsEquipSendCloudVOList) {
            String str = d.getEquipPositionFlag() == 1 ? "sss" : "fsj";
            ProductEquipmentDto productEquipmentDto = new ProductEquipmentDto();
            productEquipmentDto.setId(d.getEquipRecordVsAimsId());
            productEquipmentDto.setHospitalId(d.getCloudHospitalId());
            productEquipmentDto.setHisOrgId(d.getOrgId());
            // 传设备类型id。 手麻系统自己判断
            productEquipmentDto.setMonitorClass(Convert.toInt(d.getEquipTypeId()));
            // deviceName : 设备位置名称-设备类型. equipmentNumber : 设备位置flag-设备位置名称 [sss-手术间1]
            productEquipmentDto.setDeviceName(d.getEquipPositionName() + "-" + d.getEquipTypeName());
            productEquipmentDto.setEquipmentNumber(str + "-" + d.getEquipPositionName());
            productEquipmentDto.setProductId(Integer.parseInt(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId().toString()));
            productEquipmentDto.setProductCode(ProjProductEnum.AIMS.getProductCode());
            // 系列
            productEquipmentDto.setMonitorType(d.getSeriesName());
            productEquipmentDto.setIp(d.getEquipIp());
            productEquipmentDto.setPort(d.getMonDeviceConfigPort());
            productEquipmentDto.setListenerIp(d.getListenIp());
            productEquipmentDto.setListenerPort(d.getListenPort());
            productEquipmentDto.setProtocol(d.getProtocolType());
            productEquipmentDto.setMonitorCode(d.getMonitorCode());
            productEquipmentDto.setEquipModelName(d.getEquipModelName());
            productEquipmentDto.setParentId(d.getCenterMonDeviceId());
            productEquipmentDto.setModelType(d.getEquipTypeName());
            productEquipmentDto.setCenterMonDeviceFlag(d.getCenterMonDeviceFlag());
            equipments.add(productEquipmentDto);
        }
        ResponseResult<String> responseResult = equipRecordCommonService.syncEquipment("AIMS", hospitalInfo,
                equipments);
        if (responseResult.isSuccess()) {
            //更新设备的发送云健康状态, 以及自动更新云健康设备id
            for (Long equipRecordVsAimsId : dto.getEquipRecordVsAimsIdList()) {
                // 查询设备明细数据
                ProjEquipRecordVsAims projEquipRecordVsAims =
                        projEquipRecordVsAimsMapper.selectById(equipRecordVsAimsId);
                // 修改设备的发送状态
                ProjEquipRecordVsAims projEquipRecordVsAims1 = new ProjEquipRecordVsAims();
                projEquipRecordVsAims1.setEquipRecordVsAimsId(equipRecordVsAimsId);
                projEquipRecordVsAims1.setSendCloudFlag(1);
                projEquipRecordVsAimsMapper.updateById(projEquipRecordVsAims1);
                // 以及云健康设备id、名称
                ProjEquipRecord projEquipRecord1 =
                        projEquipRecordMapper.selectById(projEquipRecordVsAims.getEquipRecordId());
                ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                projEquipRecord.setCloudEquipId(projEquipRecordVsAims.getEquipRecordVsAimsId());
                projEquipRecord.setCloudEquipName(projEquipRecord1.getEquipModelName());
                projEquipRecord.setEquipRecordId(projEquipRecord1.getEquipRecordId());
                projEquipRecordMapper.updateById(projEquipRecord);
            }
            UpdateWrapper<ProjEquipRecordVsAims> uw = new UpdateWrapper<>();
            uw.in("equip_record_vs_aims_id", dto.getEquipRecordVsAimsIdList());
            ProjEquipRecordVsAims projEquipRecordVsAims = new ProjEquipRecordVsAims();
            projEquipRecordVsAims.setSendCloudFlag(1);
            int count = projEquipRecordVsAimsMapper.update(projEquipRecordVsAims, uw);
            log.info("发送设备到云健康更新发送标识. count: {}", count);
        } else {
            return Result.fail("发送到云健康失败，失败原因：" + responseResult.getMessage());
        }
        return Result.success();
    }

    /**
     * 手麻设备下载模版
     *
     * @param response
     * @param projectInfoId
     */
    @Override
    public void downloadTemplateForAims(HttpServletResponse response, Long projectInfoId) {
        //查询客户名称
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("手麻设备导入模版");
        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        // 创建带星号(*)的标题行字体颜色为红色
        CellStyle redFontStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setBold(true);
        redFontStyle.setFont(redFont);
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        String[] headers = {"*设备型号", "*设备类型", "*设备厂商", "*通讯方式", "*来源医院", "厂商电话", "*设备位置", "*设备位置名称", "*是否对接", "备注说明"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (headers[i].startsWith("*")) {
                cell.setCellStyle(redFontStyle);
            } else {
                cell.setCellStyle(headerStyle);
            }
        }
        // 创建一个下拉框的区域，从第2-500行，第8列【设备位置】
        CellRangeAddressList equipPositionAddressList = new CellRangeAddressList(1, 500, 6, 6);
        // 设置下拉框的数据有效性
        DataValidationHelper equipPositionDataValid = sheet.getDataValidationHelper();
        DataValidationConstraint equipPositionConstraint =
                equipPositionDataValid.createExplicitListConstraint(new String[]{"手术间", "复苏间"});
        DataValidation equipPositionDataValidation =
                equipPositionDataValid.createValidation(equipPositionConstraint, equipPositionAddressList);
        // 设置下拉框
        equipPositionDataValidation.setSuppressDropDownArrow(true);
        equipPositionDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
        equipPositionDataValidation.setShowErrorBox(true);
        sheet.addValidationData(equipPositionDataValidation);
        // 创建一个下拉框的区域，从第2-500行，第8列【是否对接】
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 500, 8, 8);
        // 设置下拉框的数据有效性
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[]{"是", "否"});
        DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
        // 设置下拉框
        dataValidation.setSuppressDropDownArrow(true);
        dataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
        dataValidation.setShowErrorBox(true);
        sheet.addValidationData(dataValidation);
        // 创建是否的下拉框区域，从第2-499行，第4列【通讯方式】
        List<BaseCodeNameResp> attributes =
                projEquipRecordMapper.selectSurveyEquipAttributes(DICT_EQUIP_COMM_MODE);
        if (CollectionUtil.isNotEmpty(attributes)) {
            String[] choices2 = new String[attributes.size()];
            for (int i = 0; i < attributes.size(); i++) {
                choices2[i] = attributes.get(i).getName();
            }
            CellRangeAddressList addressList2 = new CellRangeAddressList(1, 500, 3, 3);
            DataValidation dataValidation2 = helper.createValidation(helper.createExplicitListConstraint(choices2),
                    addressList2);
            dataValidation2.setSuppressDropDownArrow(true);
            dataValidation2.createErrorBox("错误", "请从列表中选择一个选项。");
            dataValidation2.setShowErrorBox(true);
            sheet.addValidationData(dataValidation2);
        }
        // 创建是否的下拉框区域，从第2-499行，第5列【来源医院】
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtil.isNotEmpty(hospitalInfoList)) {
            String[] hospitalInfoListChoices = new String[hospitalInfoList.size()];
            for (int i = 0; i < hospitalInfoList.size(); i++) {
                hospitalInfoListChoices[i] = hospitalInfoList.get(i).getHospitalName();
            }
            CellRangeAddressList hospitalInfoListAddress = new CellRangeAddressList(1, 500, 4, 4);
            DataValidation hospitalInfoDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(hospitalInfoListChoices),
                            hospitalInfoListAddress);
            hospitalInfoDataValidation.setSuppressDropDownArrow(true);
            hospitalInfoDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            hospitalInfoDataValidation.setShowErrorBox(true);
            sheet.addValidationData(hospitalInfoDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第1列【设备型号】
        Result<List<DictEquipInfoVO>> equipInfoResult = equipSummaryService.selectSurveyEquipInfo("AIMS", null);
        List<DictEquipInfoVO> equipInfoData = equipInfoResult.getData();
        if (CollectionUtil.isNotEmpty(equipInfoData)) {
            //创建隐藏sheet页，用来绑定超过50个的下拉项
            Sheet hidden1 = workbook.createSheet("hidden1");
            Cell cell = null;
            for (int i = 0; i < equipInfoData.size(); i++) {
                Row row = hidden1.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(equipInfoData.get(i).getEquipModelName());
            }
            Name name1 = workbook.createName();
            name1.setNameName("hidden1");
            name1.setRefersToFormula("hidden1!$A$1:$A$" + equipInfoData.size());
            DataValidationConstraint equipInfoConstraint = helper.createFormulaListConstraint(hidden1.getSheetName());
            workbook.setSheetHidden(1, true);
            CellRangeAddressList apiGroup = new CellRangeAddressList(1, 500, 0, 0);
            DataValidation apiGroupDataValidation = helper.createValidation(equipInfoConstraint, apiGroup);
            apiGroupDataValidation.setSuppressDropDownArrow(true);
            apiGroupDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            apiGroupDataValidation.setShowErrorBox(true);
            sheet.addValidationData(apiGroupDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第2列【设备类型】
        Result<List<BaseIdNameResp>> equipTypeResult = equipSummaryService.selectSurveyEquipType("AIMS", null);
        List<BaseIdNameResp> equipTypeData = equipTypeResult.getData();
        if (CollectionUtil.isNotEmpty(equipTypeData)) {
            String[] equipTypeListChoices = new String[equipTypeData.size()];
            for (int i = 0; i < equipTypeData.size(); i++) {
                equipTypeListChoices[i] = equipTypeData.get(i).getName();
            }
            CellRangeAddressList equipTypeListAddress = new CellRangeAddressList(1, 500, 1, 1);
            DataValidation equipTypeDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(equipTypeListChoices),
                            equipTypeListAddress);
            equipTypeDataValidation.setSuppressDropDownArrow(true);
            equipTypeDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            equipTypeDataValidation.setShowErrorBox(true);
            sheet.addValidationData(equipTypeDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第3列【设备厂商】
        Result<List<BaseIdNameResp>> equipFactoryResult = equipSummaryService.selectSurveyEquipFactory("AIMS", null);
        List<BaseIdNameResp> equipFactoryData = equipFactoryResult.getData();
        if (CollectionUtil.isNotEmpty(equipFactoryData)) {
            String[] equipFactoryListChoices = new String[equipFactoryData.size()];
            for (int i = 0; i < equipFactoryData.size(); i++) {
                equipFactoryListChoices[i] = equipFactoryData.get(i).getName();
            }
            CellRangeAddressList equipFactoryListAddress = new CellRangeAddressList(1, 500, 2, 2);
            DataValidation equipFactoryDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(equipFactoryListChoices),
                            equipFactoryListAddress);
            equipFactoryDataValidation.setSuppressDropDownArrow(true);
            equipFactoryDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            equipFactoryDataValidation.setShowErrorBox(true);
            sheet.addValidationData(equipFactoryDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第4列【通讯方式】
        Result<List<BaseCodeNameResp>> communtModeResult = equipSummaryService.selectSurveyAttributesInfo(
                "communt_mode");
        List<BaseCodeNameResp> communtModeData = communtModeResult.getData();
        if (CollectionUtil.isNotEmpty(communtModeData)) {
            String[] communtModeListChoices = new String[communtModeData.size()];
            for (int i = 0; i < communtModeData.size(); i++) {
                communtModeListChoices[i] = communtModeData.get(i).getName();
            }
            CellRangeAddressList communtModeListAddress = new CellRangeAddressList(1, 500, 3, 3);
            DataValidation communtModeDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(communtModeListChoices),
                            communtModeListAddress);
            communtModeDataValidation.setSuppressDropDownArrow(true);
            communtModeDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            communtModeDataValidation.setShowErrorBox(true);
            sheet.addValidationData(communtModeDataValidation);
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(customInfo.getCustomName() + "-手麻设备导入模版.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Override
    public Result importExcelDatas(MultipartFile multipartFile, Long customInfoId, Long projectInfoId) {
        // 确保上传的是Excel文件
        if (!multipartFile.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("导入失败,请选择.xlsx格式的Excel文件");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(multipartFile.getInputStream(), EquipRecordVsAimsExcelDTO.class);
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("导入失败,请检查导入文件是否存在数据");
                }
            }
            //导入Excel数据
            return saveDatas(easyExcelData.getDatas(), customInfoId, projectInfoId);
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败，失败信息：" + e.getMessage());
        }
    }


    /**
     * 保存模版导入数据
     *
     * @param dtoList
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result saveDatas(List<Object> dtoList, Long customInfoId, Long projectInfoId) {
        for (Object dto : dtoList) {
            EquipRecordVsAimsExcelDTO excelDTO = (EquipRecordVsAimsExcelDTO) dto;
            //检验数据中必填选项
            Set<ConstraintViolation<EquipRecordVsAimsExcelDTO>> violations = validator.validate(excelDTO);
            if (!violations.isEmpty()) {
                return Result.fail("导入失败，Excel中存在必填的选项为空，请检查标题前带*的内容是否填写完整！");
            }
            //封装设备记录实体
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            BeanUtil.copyProperties(excelDTO, projEquipRecord);
            //查询医院id
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_name", excelDTO.getHospitalName()).last("limit 1"));
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                return Result.fail("导入失败,《" + excelDTO.getHospitalName() + "》在系统中未匹配到对应医院，请核对数据！");
            }
            if (!customInfoId.equals(hospitalInfo.getCustomInfoId())) {
                return Result.fail("导入失败,项目下不存在《" + excelDTO.getHospitalName() + "》，请核对数据！");
            }
            projEquipRecord.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
            //通讯方式
            DictEquipAttributes dictEquipAttributes =
                    dictEquipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                            .eq("equip_attributes_code", DICT_EQUIP_COMM_MODE)
                            .eq("equip_attributes_value", excelDTO.getCommModeValue())
                            .last(" limit 1"));
            if (ObjectUtil.isEmpty(dictEquipAttributes)) {
                return Result.fail("导入失败,《" + excelDTO.getCommModeValue() + "》在系统中未匹配到对应通讯方式，请核对数据！");
            }
            projEquipRecord.setCommMode(excelDTO.getCommModeValue());
            projEquipRecord.setCommModeKey(dictEquipAttributes.getEquipAttributesKey());
            //处理设备类型
            projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_TYPE, projEquipRecord.getEquipTypeName()));
            //设备厂商
            projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY, projEquipRecord.getEquipFactoryName()));
            //设备型号
            projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO, projEquipRecord.getEquipModelName()));
            //产品ID
            projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
            //是否对接
            projEquipRecord.setRequiredFlag("是".equals(excelDTO.getRequiredFlagExcel()) ? 1 : 0);
            //保存设备记录信息
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecord.setCustomInfoId(customInfoId);
            projEquipRecord.setProjectInfoId(projectInfoId);
            projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
            projEquipRecordMapper.insert(projEquipRecord);
            //保存手麻设备记录
            ProjEquipRecordVsAims projEquipRecordVsAims = new ProjEquipRecordVsAims();
            BeanUtil.copyProperties(excelDTO, projEquipRecordVsAims);
            projEquipRecordVsAims.setEquipRecordVsAimsId(SnowFlakeUtil.getId());
            projEquipRecordVsAims.setEquipRecordId(projEquipRecord.getEquipRecordId());
            projEquipRecordVsAims.setOldAimsImportFlag(0);
            projEquipRecordVsAims.setEquipPositionFlag("手术间".equals(excelDTO.getEquipPositionName()) ? 1 : 2);
            projEquipRecordVsAims.setEquipPositionName(excelDTO.getEquipPositionName());
            projEquipRecordVsAimsMapper.insert(projEquipRecordVsAims);
        }
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        return Result.success();
    }

    /**
     * 导出数据
     *
     * @param dto
     * @param response
     */
    @Override
    public void exportExcelDatas(AimsEquipSelectDTO dto, HttpServletResponse response) {
        List<ProjEquipRecordVsAimsVO> aimsVOList = projEquipRecordVsAimsMapper.selectEquipRecordVsAimsList(dto);
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("手麻设备记录");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        headerRow.createCell(0).setCellValue("设备型号");
        headerRow.createCell(1).setCellValue("设备类型");
        headerRow.createCell(2).setCellValue("设备厂商");
        headerRow.createCell(3).setCellValue("通讯方式");
        headerRow.createCell(4).setCellValue("医院名称");
        headerRow.createCell(5).setCellValue("厂商电话");
        headerRow.createCell(6).setCellValue("设备位置");
        headerRow.createCell(7).setCellValue("设备位置名称");
        headerRow.createCell(8).setCellValue("是否对接");
        headerRow.createCell(9).setCellValue("不对接原因");
        headerRow.createCell(10).setCellValue("备注说明");
        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        for (Cell headerCell : headerRow) {
            headerCell.setCellStyle(headerStyle);
        }
        //填充数据
        for (int i = 0; i < aimsVOList.size(); i++) {
            ProjEquipRecordVsAimsVO aimsEquipVo = aimsVOList.get(i);
            Row rowData = sheet.createRow(i + 1);
            rowData.createCell(0).setCellValue(aimsEquipVo.getEquipModelName());
            rowData.createCell(1).setCellValue(aimsEquipVo.getEquipTypeName());
            rowData.createCell(2).setCellValue(aimsEquipVo.getEquipFactoryName());
            rowData.createCell(3).setCellValue(aimsEquipVo.getCommMode());
            rowData.createCell(4).setCellValue(aimsEquipVo.getHospitalInfoName());
            rowData.createCell(5).setCellValue(aimsEquipVo.getEquipFactoryPhone());
            rowData.createCell(6).setCellValue(aimsEquipVo.getEquipPositionFlag() == 1 ? "手术间" : "复苏间");
            rowData.createCell(7).setCellValue(aimsEquipVo.getEquipPositionName());
            rowData.createCell(8).setCellValue(aimsEquipVo.getRequiredFlag() == 1 ? "是" : "否");
            rowData.createCell(9).setCellValue(aimsEquipVo.getStopReason());
            rowData.createCell(10).setCellValue(aimsEquipVo.getMemo());
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("手麻设备记录.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 返回前端. 获取云健康设备信息
     *
     * @param selectDTO 客户及项目类型
     * @return Map集合, Key为string类型,,云健康医院id
     */
    public Result<Map<String, List<CloudEquipVO>>> selectCloudEquipDataTransfer(AimsCloudEquipSelectDTO selectDTO) {
        Map<Long, List<CloudEquipVO>> listMap = selectCloudEquipData(selectDTO);
        Map<String, List<CloudEquipVO>> stringListMap = MapUtil.newHashMap();
        // 转换hospitalId为str
        if (MapUtil.isNotEmpty(listMap)) {
            for (Long hosId : listMap.keySet()) {
                stringListMap.put(StrUtil.toString(hosId), listMap.get(hosId));
            }
        }
        if (MapUtil.isEmpty(stringListMap)) {
            return Result.fail("未获取到云健康设备信息.");
        }
        return Result.success(stringListMap);
    }

    public Map<Long, List<CloudEquipVO>> selectCloudEquipData(AimsCloudEquipSelectDTO selectDTO) {
        return equipRecordCommonService.findEquipmentDataBatch(selectDTO.getCustomInfoId(),
                selectDTO.getProjectType(), ProjProductEnum.AIMS.getProductCode(),
                dto -> {
                    CloudEquipVO cloudEquipVO = new CloudEquipVO();
                    cloudEquipVO.setCloudEquipId(dto.getId());
                    StringBuilder desp = new StringBuilder();
                    // 医院名称
                    if (ObjectUtil.isNotEmpty(dto.getHospitalName())) {
                        desp.append(dto.getHospitalName()).append("-");
                    }
                    // 类别
                    if (StrUtil.isNotBlank(dto.getCategoryName())) {
                        desp.append(dto.getCategoryName()).append(StrUtil.DASHED);
                    }
                    // 类型
                    if (ObjectUtil.isNotEmpty(dto.getTypeName())) {
                        desp.append(dto.getTypeName()).append("/");
                    }
                    // 名称
                    if (ObjectUtil.isNotEmpty(dto.getModelName())) {
                        desp.append(dto.getModelName()).append("/");
                    }
                    // 型号
                    if (ObjectUtil.isNotEmpty(dto.getStyleName())) {
                        desp.append(dto.getStyleName());
                    }
                    cloudEquipVO.setCloudEquipNameAndHospital(desp.toString());
                    cloudEquipVO.setCloudEquipName(dto.getModelName());
                    return cloudEquipVO;
                });
    }

    @Override
    public Result<List<ProjEquipRecordVsAimsVO>> compareEquipmentToAims(AimsComparedEquipSelectDTO dto) {
        // 返回值
        AimsEquipSelectDTO selectDTO = new AimsEquipSelectDTO();
        selectDTO.setRequiredFlag(NumberEnum.NO_1.num());
        selectDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProjEquipRecordVsAimsVO> equipRecordVsAimsVOS =
                projEquipRecordVsAimsMapper.selectEquipRecordVsAimsList(selectDTO);
        if (CollUtil.isEmpty(equipRecordVsAimsVOS)) {
            log.warn("未查询到手麻设备对照信息. dto: {}", dto);
            return Result.success(CollUtil.newArrayList());
        }
        equipRecordVsAimsVOS.forEach(e -> e.setEquipModelNameStr(e.getEquipTypeName() + StrUtil.SLASH + e.getEquipModelName()));
        // 对照更新
        AimsCloudEquipSelectDTO cloudEquipSelectDTO = new AimsCloudEquipSelectDTO();
        cloudEquipSelectDTO.setCustomInfoId(dto.getCustomInfoId());
        ProjProjectInfo projectInfo = commonService.getProjectInfo(dto.getProjectInfoId());
        if (ObjectUtil.isEmpty(projectInfo)) {
            log.error("获取手麻对照数据时, 未获取到项目信息. dto: {}", dto);
            throw new CustomException("获取手麻对照数据时, 未获取到项目信息.");
        }
        cloudEquipSelectDTO.setProjectType(projectInfo.getProjectType());
        // 获取云健康设备
        Map<Long, List<CloudEquipVO>> result = selectCloudEquipData(cloudEquipSelectDTO);
        equipRecordCommonService.compareEquip(result, equipRecordVsAimsVOS,
                ProductEquipSurveyMenuEnum.AIMSEQUIP.getName());
        return Result.success(equipRecordVsAimsVOS);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<String> updateCloudEquipToAims(List<UpdateAimsCloudEquipDTO> dtoList) {
        List<Long> cloudEquipIds = dtoList.stream()
                .map(vo -> {
                    if (ObjectUtil.isNotEmpty(vo.getCloudEquipVO())) {
                        return vo.getCloudEquipVO().getCloudEquipId();
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (UpdateAimsCloudEquipDTO dto : dtoList) {
            // 设备未进行对照, 不做处理
            if (ObjectUtil.isEmpty(dto.getCloudEquipVO())) {
                continue;
            }
            int count = 0;
            // 若交付设备对照多次进行记录
            for (Long cloudEquipId : cloudEquipIds) {
                if (dto.getCloudEquipVO().getCloudEquipId().equals(cloudEquipId)) {
                    count++;
                }
            }
            // 获取设备记录lis拓展表数据
            ProjEquipRecordVsAims equipRecordVsAims =
                    projEquipRecordVsAimsMapper.selectById(dto.getEquipRecordVsAimsId());
            // 获取历史对照记录
            ProjEquipRecord projEquipRecordHistory =
                    equipRecordMapper.selectById(equipRecordVsAims.getEquipRecordId());
            // 定义新的对照记录
            Long equipRecordId = equipRecordVsAims.getEquipRecordId();
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipRecordId(equipRecordId);
            if (ObjectUtil.isNotEmpty(dto.getCloudEquipVO())) {
                projEquipRecord.setCloudEquipId(dto.getCloudEquipVO().getCloudEquipId());
                projEquipRecord.setCloudEquipName(dto.getCloudEquipVO().getCloudEquipName());
            }
            // 一个云健康设备禁止重复对照多个交付设备 【赵飞】
            if (count > 1) {
                sb.append(dto.getCloudEquipVO().getCloudEquipName())
                        .append("已对照交付设备【")
                        .append(projEquipRecordHistory.getEquipFactoryName())
                        .append("/")
                        .append(projEquipRecordHistory.getEquipTypeName())
                        .append("/")
                        .append(projEquipRecordHistory.getEquipModelName())
                        .append("】，请勿重复对照;");
                sb.append("\n");
                log.warn(sb.toString());
                continue;
            }
            // 如果云健康设备已经对照, 不做处理
            if (ObjectUtil.isNotEmpty(projEquipRecordHistory.getCloudEquipId())
                    && dto.getCloudEquipVO().getCloudEquipId().longValue() == projEquipRecordHistory.getCloudEquipId()) {
                log.warn("此手麻设备已对照,不做处理. cloudEquipId: {}",
                        projEquipRecordHistory.getCloudEquipName() + "-" + projEquipRecordHistory.getCloudEquipId());
                continue;
            }
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(projEquipRecordHistory.getHospitalInfoId());
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                log.warn("未查询到医院信息. hospitalInfoId: {}", projEquipRecordHistory.getHospitalInfoId());
                continue;
            }
            // 若历史对照数据中云健康设备id不等于空, 则作废
            handlePreviousRecord(projEquipRecordHistory, projEquipRecord);
            // 修改对照后，检查临时检测表中是否存在数据，当存在时候 更新检测进度以及检测明细数据
            handleAimsCheckDetails(projEquipRecordHistory, projEquipRecord, hospitalInfo);
            count = equipRecordMapper.updateById(projEquipRecord);
            log.info("更新项目检测记录. count: {} equipRecord: {}", count, projEquipRecord);
        }
        if (ObjectUtil.isNotEmpty(sb)) {
            return Result.fail(sb.toString());
        } else {
            return Result.success();
        }
    }

    /**
     * 处理原云健康设备的对照数据
     *
     * @param projEquipRecordHistory 历史对照记录
     * @param projEquipRecord        新对照记录
     */
    private void handlePreviousRecord(ProjEquipRecord projEquipRecordHistory, ProjEquipRecord projEquipRecord) {
        if (ObjectUtil.isEmpty(projEquipRecordHistory.getCloudEquipId())) {
            return;
        }
        log.info("处理重新对照的手麻设备. 对照前(交付设备id-设备名称(云健康设备id)): {}, 对照后(交付设备id-设备名称(云健康设备id)): {}",
                projEquipRecordHistory.getEquipRecordId() + "-" + projEquipRecordHistory.getCloudEquipName()
                        + "(" + projEquipRecordHistory.getCloudEquipId() + ")",
                projEquipRecord.getEquipRecordId() + "-" + projEquipRecord.getCloudEquipName()
                        + "(" + projEquipRecord.getCloudEquipId() + ")"
        );
        // 更新标识为未检测
        ProjEquipRecord update = new ProjEquipRecord();
        update.setEquipRecordId(projEquipRecord.getEquipRecordId());
        update.setEquipStatus(EquipStatusEnum.DEVELOPED.getCode());
        update.setTestProgress("0%");
        int count = equipRecordMapper.updateById(update);
        log.info("重置手麻设备记录. count: {}, record: {}", count, update);
    }


    /**
     * 自动检测临时表处理, 若存在则先获取，再删除, 将获取的设备检测结果取出进行更新, 取出检测明心进行新增
     *
     * @param projEquipRecordHistory 主表历史记录
     * @param projEquipRecord        项目设备检测主表
     * @param hospitalInfo           医院信息
     */
    private void handleAimsCheckDetails(ProjEquipRecord projEquipRecordHistory,
                                        ProjEquipRecord projEquipRecord,
                                        ProjHospitalInfo hospitalInfo) {

        List<TmpEquipCheckDetail> tmpEquipCheckDetails =
                tmpEquipCheckDetailMapper.selectList(new QueryWrapper<TmpEquipCheckDetail>()
                        .in("cloud_equip_id", projEquipRecord.getCloudEquipId(),
                                projEquipRecordHistory.getCloudEquipId())
                        .eq("cloud_hospital_id", hospitalInfo.getCloudHospitalId())
                );
        // 若暂存表有数据, 先更新, 再作废
        if (CollectionUtil.isNotEmpty(tmpEquipCheckDetails)) {
            // 删除数据
            for (TmpEquipCheckDetail tmpEquipCheckDetail : tmpEquipCheckDetails) {
                projEquipRecord.setEquipStatus(tmpEquipCheckDetail.getStatus());
                // 当已经匹配上后 ，直接删除临时表中的数据
                int count = tmpEquipCheckDetailMapper.deleteById(tmpEquipCheckDetail.getTmpEquipCheckVsLisId());
                log.info("删除暂存表lis设备检测结果明细. count: {}, detail: {}", count, tmpEquipCheckDetail);
            }
        }
    }
}
