package com.msun.csm.service.proj.applyorder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.CLOUD_APPLY;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.CLOUD_CHECK;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.config.AsyncManager;
import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.MilestoneStatusEnum;
import com.msun.csm.common.enums.ProductType;
import com.msun.csm.common.enums.active.ActiveEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.projapplyorder.CloudEnvTypeEnum;
import com.msun.csm.common.enums.projapplyorder.HospitalTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyOrderResultTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyTypeEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectHisFlagEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projsettlement.CloudServiceTypeEnum;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysOperLog;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecord;
import com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecordRelative;
import com.msun.csm.dao.entity.proj.ProjApplyOrderNodeRecord;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProduct;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProductRecord;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProductRecordRelative;
import com.msun.csm.dao.entity.proj.ProjApplyOrderRelative;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjCustomVsProjectTypeEntity;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProductArrangeRecord;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.rule.RuleProductRuleConfig;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderHospitalRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjProductArrangeRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.entity.yunwei.resp.YunweiLoginResp;
import com.msun.csm.intercepter.LogAspect;
import com.msun.csm.model.convert.ProjApplyOrderConvert;
import com.msun.csm.model.convert.ProjApplyOrderHospitalRecordRelativeConvert;
import com.msun.csm.model.convert.ProjApplyOrderNodeRecordConvert;
import com.msun.csm.model.convert.ProjApplyOrderProductRecordRelativeConvert;
import com.msun.csm.model.convert.ProjApplyOrderRelativeConvert;
import com.msun.csm.model.dto.ApplyOrderNodeRecordParamDTO;
import com.msun.csm.model.dto.DeployEnvApplyDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderCheckDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderHospitalRecordDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderListDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderMainDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderResFileDTO;
import com.msun.csm.model.dto.productauth.ApplyOpenProductDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleSaveDTO;
import com.msun.csm.model.dto.yunweiplatform.SaveEnvDTO;
import com.msun.csm.model.dto.yunweiplatform.SaveEnvSourceDTO;
import com.msun.csm.model.dto.yunweiplatform.predownload.PreDownloadCountryDTO;
import com.msun.csm.model.dto.yunweiplatform.predownload.PreDownloadDTO;
import com.msun.csm.model.dto.yunweiplatform.predownload.PreDownloadHospital;
import com.msun.csm.model.dto.yunweiplatform.predownload.PreDownloadProduct;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.resp.applyorder.DepApplyOrderResult;
import com.msun.csm.model.resp.applyorder.DepApplyProductResult;
import com.msun.csm.model.resp.applyorder.DepResourceApplyCountryResult;
import com.msun.csm.model.resp.applyorder.DepResourceApplyHospitalResult;
import com.msun.csm.model.vo.ProjApplyOrderDetailsVO;
import com.msun.csm.model.vo.ProjApplyOrderHospitalRecordRelativeVO;
import com.msun.csm.model.vo.ProjApplyOrderMainInfoVO;
import com.msun.csm.model.vo.ProjApplyOrderNodeRecordVO;
import com.msun.csm.model.vo.ProjApplyOrderProductRecordRelativeVO;
import com.msun.csm.model.vo.ProjApplyOrderRelativeVO;
import com.msun.csm.model.vo.ProjApplyOrderVO;
import com.msun.csm.model.vo.SysFileVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.OnlineStepService;
import com.msun.csm.service.proj.ProjCustomInfoService;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.ProjMilestoneInfoService;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.service.proj.ProjOrderProductServiceImpl;
import com.msun.csm.service.proj.ProjProductArrangeRecordService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckSaleService;
import com.msun.csm.service.proj.projform.PrintReportLimitService;
import com.msun.csm.service.rule.RuleProductRuleConfigService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.util.DomainUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.ObsObject;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/26
 */

@Slf4j
@Service
public class ProjApplyOrderServiceImpl implements ProjApplyOrderService {

    public static final String DEFAULT_TMP_PATH = System.getProperty("user.dir") + StrUtil.SLASH + "tmpFile" + StrUtil.SLASH + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT);
    /**
     * 预下载文件夹名称
     */
    public static final String PRE_DOWNLOAD = "predownload";
    public static final String LOG_PRE_YUNWEI_SAVE_LOG = "运维平台回调";
    private static final String LOGPRE = "部署订单.";
    @Value("${project.obs.prePath}")
    private String prePath;
    @Value("${project.feign.yunwei.url}")
    private String yunweiUrl;
    @Value("${project.feign.yunwei.saveHospitalInfo-method}")
    private String saveHospitalInfoMethod;
    @Value("${project.obs.bucketName}")
    private String bucketName;
    @Value("${project.feign.yunwei.saveEnv-method}")
    private String saveEnvPath;
    @Value("${project.feign.yunwei.preDownload-method}")
    private String preDownloadUrl;
    @Value("${project.feign.yunwei.editStatusByDeliverPlatformApplyId-method}")
    private String editStatusByDeliverPlatformApplyIdPath;
    @Value("${project.feign.yunwei.url}")
    private String devUrl;
    @Resource
    private SysOperLogService sysOperLogService;
    @Resource
    private OnlineStepService onlineStepService;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private ProjApplyOrderMapper applyOrderMapper;


    @Resource
    private ProjApplyOrderHospitalRecordRelativeConvert applyOrderHospitalRecordRelativeConvert;

    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private ProjProductArrangeRecordService productArrangeRecordService;

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;
    @Resource
    private ProjProjectInfoService projectInfoService;

    @Autowired
    private ProjApplyOrderMapper projApplyOrderMapper;

    @Resource
    private ProjApplyOrderConvert applyOrderConvert;

    @Resource
    private ProjApplyOrderNodeRecordConvert applyOrderNodeRecordConvert;

    @Resource
    private ProjApplyOrderProductRecordRelativeConvert applyOrderProductRecordRelativeConvert;

    @Resource
    private ProjApplyOrderRelativeConvert applyOrderRelativeConvert;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private ProjApplyOrderNodeRecordService applyOrderNodeRecordService;

    @Resource
    private ProjApplyOrderProductRecordService applyOrderProductRecordService;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjApplyOrderHospitalRecordService applyOrderHospitalRecordService;

    @Resource
    private ProjApplyOrderHospitalRecordMapper applyOrderHospitalRecordMapper;

    @Resource
    private ProjApplyOrderEnvService applyOrderEnvService;

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private ProjMilestoneInfoService projMilestoneInfoService;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjApplyOrderHospitalYunweiService hospitalYunweiService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjProjectSettlementCheckSaleService settlementCheckSaleService;

    @Resource
    private PrintReportLimitService printReportLimitService;

    @Lazy
    @Resource
    private ProjCustomVsProjectTypeMapper projCustomVsProjectTypeMapper;

    @Resource
    @Lazy
    private SysConfigMapper sysConfigMapper;

    @Resource
    @Lazy
    private SysUserMapper sysUserMapper;

    @Resource
    private UserUtil userUtil;
    @Resource
    private ProjCustomCloudServiceMapper customCloudServiceMapper;
    @Resource
    private ProjProjectSettlementCheckMainService mainService;
    @Resource
    private ProjApplyOrderHospitalYunweiService applyOrderHospitalYunweiService;
    @Resource
    private RuleProductRuleConfigService ruleProductRuleConfigService;
    @Resource
    private ProjProductArrangeRecordMapper productArrangeRecordMapper;
    @Resource
    private ProjApplyOrderDescService applyOrderDescService;

    @Lazy
    @Resource
    private SendMessageService sendMessageService;

    @Lazy
    @Resource
    private RedisUtil redisUtil;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    private static String mergeDomainName(String domainName) {
        return "https://" + domainName + ".msunhis.com";
    }

    public static OldCustomerInfo getOldCustomerInfo(HospitalOpenStatusEnum statusEnum, String cloudDomain, ProjHospitalInfo opendHospitalInfo) {
        String host = null;
        if (StrUtil.isNotEmpty(cloudDomain)) {
            host = DomainUtil.getDomainInfo(cloudDomain).getHost();
        }
        OldCustomerInfo oldCustomerInfoCopy = new OldCustomerInfo();
        oldCustomerInfoCopy.setPreHost(host);
        oldCustomerInfoCopy.setHost(host);
        oldCustomerInfoCopy.setProductNetwork(cloudDomain);
        oldCustomerInfoCopy.setPreProductNetwork(cloudDomain);
        oldCustomerInfoCopy.setHospitalStatus(ObjectUtil.isEmpty(statusEnum) ? null : statusEnum.getCode());
        oldCustomerInfoCopy.setEnvId(opendHospitalInfo.getEnvId());
        oldCustomerInfoCopy.setEnvName(opendHospitalInfo.getEnvName());
        return oldCustomerInfoCopy;
    }

    /**
     * 首次调用, 创建文件夹, 返回要创建文件的绝对路径
     *
     * @param businessId 业务id
     * @return string
     */
    public static String getDirFilePath(String businessId) {
        String dirPath = DEFAULT_TMP_PATH + StrUtil.SLASH + businessId + StrUtil.SLASH + UUID.randomUUID().toString().replace(StrUtil.DASHED, StrUtil.EMPTY);
        File dir = new File(dirPath);
        if (dir.mkdirs()) {
            log.info("完成临时文件夹创建. path: {}", dir.getAbsolutePath());
        }
        return dirPath;
    }

    public static FileSystemResource getFileSystemResource(String bucketName, String businessId, String obsFilePath) {
        String saveFilePath = getDirFilePath(businessId);
        FileOutputStream fileOutputStream = null;
        List<ObsObject> obsObjectList = OBSClientUtils.getAllObjects(Collections.singletonList(obsFilePath));
        if (CollUtil.isEmpty(obsObjectList)) {
            log.warn("obs中未查询到要下载的文件. filePath: {}", obsFilePath);
            throw new RuntimeException("obs中未查询到要下载的文件. filePath: " + obsFilePath);
        }
        ObsObject obsObject = obsObjectList.get(0);
        String objectKey = obsObject.getObjectKey();
        obsObject = OBSClientUtils.getObsClient().getObject(bucketName, objectKey);
        // 下载到本地, 修改文件名再读取
        InputStream in = obsObject.getObjectContent();
        String tmpFilePath = saveFilePath + StrUtil.SLASH + obsFilePath.substring(obsFilePath.lastIndexOf(StrUtil.SLASH));
        try {
            fileOutputStream = new FileOutputStream(tmpFilePath);
            int copyCount = IOUtils.copy(in, fileOutputStream);
            if (copyCount == -1) {
                log.warn("文件复制异常.");
                throw new RuntimeException("文件复制异常.");
            }
            return new FileSystemResource(tmpFilePath);
        } catch (Throwable e) {
        } finally {
            try {
                fileOutputStream.close();
                in.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private static void isDone(File file) {
        long lastModified1 = file.lastModified();
        long lastModified2 = file.lastModified();
        if (lastModified1 != lastModified2) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            isDone(file);
        }
    }

    /**
     * 医院信息-申请开通老系统回调修改状态与产品部署
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    @SneakyThrows
    @Override
    public Result<String> saveApplyHistoryLog(HospitalUpdateStatusAndProductDeployDTO dto) {
        // 查询申请单信息
        ProjApplyOrder applyOrder = getProjApplyOrder(dto.getDeliverPlatformApplyId());
        // 判断异常
        if (ObjectUtil.isEmpty(applyOrder)) {
            return Result.fail(MessageFormat.format("{0} 申请单不存在, param: {1}", LOG_PRE_YUNWEI_SAVE_LOG, dto));
        }
        if (!Arrays.stream(ProjApplyOrderResultTypeEnum.values()).anyMatch(e -> e.getCode() == dto.getOperateType())) {
            return Result.fail(MessageFormat.format("{0} 传入的申请单状态编码不正确, param: {1}", LOG_PRE_YUNWEI_SAVE_LOG, dto));
        }
        printReportLimitService.printReportLimitDataUpdate(applyOrder);
        // 更新交付文档
        if (StrUtil.isNotBlank(dto.getRemark())) {
            if (JSONUtil.isTypeJSON(dto.getRemark())) {
                JSONObject jsonObject = JSONObject.parseObject(dto.getRemark());
                Object filePath = jsonObject.get("deliverTxtPath");
                if (ObjectUtil.isNotEmpty(filePath)) {
                    applyOrder.setResourceFileDeliveryPath(filePath.toString());
                }
            }
        }
        // 处理交付内容
        if (applyOrder.getApplyType() == ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode()) {
            return applyOrderEnvService.envHandle(applyOrder, dto);
        }
        if (applyOrder.getApplyType() == ProjApplyTypeEnum.HOSPITAL_APPLY.getCode()) {
            applyOrderHospitalService.hospitalHandler(applyOrder, dto);
        }
        if (applyOrder.getApplyType() == ProjApplyTypeEnum.PRODUCT_APPLY.getCode()) {
            applyOrderProductService.productHandler(applyOrder, dto);
        }
        return Result.success();
    }

    @Override
    public ProjApplyOrder getProjApplyOrder(Long applyNum) {
        return projApplyOrderMapper.selectOne(new QueryWrapper<ProjApplyOrder>().eq("apply_num", applyNum));
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return projApplyOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 查询申请单是否有申请记录
     *
     * @param dto
     * @return
     */
    public boolean cloudEnvHasDeployed(ProjHospitalInfoDTO dto) {
        List<ProjHospitalInfo> hospitalInfoList = projApplyOrderMapper.findDeployEvnByCustomInfoId(dto.getCustomInfoId());
        return CollUtil.isNotEmpty(hospitalInfoList);
    }

    /**
     * 获取运维平台接口调用结果
     *
     * @param deployEnvApplyDTO
     * @return
     */
    public ResponseEntity<com.alibaba.fastjson.JSONObject> getDepPlatformResult(DeployEnvApplyDTO deployEnvApplyDTO) {
        //2、接口调用
        //获取token
        HttpHeaders headers = getHeaders();
        MultiValueMap<String, Object> envApplyMap = new LinkedMultiValueMap<>();
        envApplyMap.add("dto", deployEnvApplyDTO);
        log.info("申请参数:, {}", JSONObject.toJSONString(deployEnvApplyDTO));
        HttpEntity<MultiValueMap<String, Object>> httpRequest = new HttpEntity<>(envApplyMap, headers);
        sysOperLogService.apiOperLogInsert(httpRequest, "部署医院-入参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
        ResponseEntity<com.alibaba.fastjson.JSONObject> envApplyRes = new RestTemplate().postForEntity(yunweiUrl + saveHospitalInfoMethod, httpRequest, com.alibaba.fastjson.JSONObject.class);
        sysOperLogService.apiOperLogInsert(envApplyRes, "部署医院-出参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
        if (envApplyRes.getStatusCodeValue() != 200) {
            throw new RuntimeException("申请环境部署发生错误：调用自助运维平台接口发生错误");
        }
        return envApplyRes;
    }

    /**
     * 获取请求头
     *
     * @return
     */
    public HttpHeaders getHeaders() {
        HttpHeaders headers = new HttpHeaders();
        Result<YunweiLoginResp> operationPlatformToken = yunWeiPlatFormService.getOperationPlatformToken();
        headers.add("Authorization", operationPlatformToken.getData().getBody().getToken());
        headers.add("Content-Type", "multipart/form-data");
        return headers;
    }

    @Override
    public int insert(ProjApplyOrder record) {
        return projApplyOrderMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjApplyOrder record) {
        return projApplyOrderMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjApplyOrder record) {
        return projApplyOrderMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjApplyOrder record) {
        return projApplyOrderMapper.insertSelective(record);
    }

    @Override
    public ProjApplyOrder selectByPrimaryKey(Long id) {
        return projApplyOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjApplyOrder record) {
        return projApplyOrderMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjApplyOrder record) {
        return projApplyOrderMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjApplyOrder> list) {
        return projApplyOrderMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjApplyOrder> list) {
        return projApplyOrderMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjApplyOrder> list) {
        return projApplyOrderMapper.batchInsert(list);
    }

    @Override
    public Result<PageInfo<ProjApplyOrderRelativeVO>> findProjApplyOrderInfoList(ProjApplyOrderListDTO dto) {
        List<ProjApplyOrderRelative> applyOrders = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> findProjApplyOrderListImpl(dto));
        // 判断如果是申请医院,显示医院
        if (CollUtil.isNotEmpty(applyOrders)) {
            for (ProjApplyOrderRelative applyOrder : applyOrders) {
                if (applyOrder.getApplyType() == ProjApplyTypeEnum.HOSPITAL_APPLY.getCode()) {
                    List<ProjApplyOrderHospitalRecord> applyOrderHospitalRecords = applyOrderHospitalRecordMapper.selectList(new QueryWrapper<ProjApplyOrderHospitalRecord>().eq("project_info_id", Long.parseLong(dto.getProjectInfoId())).eq("apply_order_id", applyOrder.getId()));
                    if (CollUtil.isNotEmpty(applyOrderHospitalRecords)) {
                        StringBuilder showContent = new StringBuilder();
                        applyOrderHospitalRecords.forEach(e -> showContent.append(e.getHospitalName()).append(StrUtil.COMMA));
                        applyOrder.setProductNames(showContent.toString());
                    }
                }
            }
        }
        PageInfo<ProjApplyOrderRelative> pageInfo = new PageInfo<>(applyOrders);
        List<ProjApplyOrderRelativeVO> vos = applyOrderRelativeConvert.po2Vo(applyOrders);
        PageInfo<ProjApplyOrderRelativeVO> pageInfoVO = PageInfo.of(vos);
        pageInfoVO.setTotal(pageInfo.getTotal());
        return Result.success(pageInfoVO);
    }

    @Override
    public ProjHospitalInfo getHospitalInfoById(Long hospitalInfoId) {
        return null;
    }

    public void updateMilestoneStatus(Long projectInfoId) {
        updateMilestoneStatus(projectInfoId, MilestoneNodeEnum.CLOUD_RESOURCE);
    }

    public void updateMilestoneStatus(Long projectInfoId, MilestoneNodeEnum milestoneNodeEnum) {
        ProjMilestoneInfo projMilestoneInfo = milestoneInfoService.getMilestoneInfo(projectInfoId, milestoneNodeEnum.getCode());
        // 若未查到里程碑则不进行更新
        if (ObjectUtil.isEmpty(projMilestoneInfo)) {
            return;
        }
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneStatus(MilestoneStatusEnum.COMPLETED.getCode().intValue());
        updateMilestoneDTO.setMilestoneInfoId(projMilestoneInfo.getMilestoneInfoId());
        projMilestoneInfoService.updateMilestone(updateMilestoneDTO);
    }

    @Override
    public Result<String> applicationCheck(ProjApplyOrderCheckDTO checkDTO) {
        ProjApplyOrder applyOrder = BeanUtil.copyProperties(checkDTO, ProjApplyOrder.class);
        ProjApplyOrder savedApplyOrder = projApplyOrderMapper.selectById(applyOrder.getId());
        ProjApplyTypeEnum applyTypeEnum = ProjApplyTypeEnum.getEnumByCode(savedApplyOrder.getApplyType());
        MilestoneNodeEnum milestoneNodeEnum = null;
        if (applyTypeEnum == ProjApplyTypeEnum.PRODUCT_APPLY) {
            milestoneNodeEnum = MilestoneNodeEnum.PRODUCT_IMPOWER;
        } else if (applyTypeEnum == ProjApplyTypeEnum.FIRST_EVN_APPLY) {
            milestoneNodeEnum = MilestoneNodeEnum.CLOUD_RESOURCE;
        }
        //交付验收时间
        if (ObjectUtil.isNotEmpty(checkDTO.getResultType()) && ProjApplyOrderResultTypeEnum.ACCEPTED.getCode() == checkDTO.getResultType()) {
            Result<String> result = applyOrderService.checkAcceptance(applyOrder, milestoneNodeEnum, savedApplyOrder.getProjectInfoId());
            if (ObjectUtil.isNotEmpty(result) && result.isSuccess()) {
                // 同步运营平台工单信息
                if (milestoneNodeEnum == MilestoneNodeEnum.CLOUD_RESOURCE) {
                    ProjApplyOrder tmpApplyOrder = applyOrderMapper.selectById(applyOrder.getId());
                    applyOrderEnvService.updateNodeStatusApplyOrder(tmpApplyOrder, CLOUD_CHECK);
                }
            }
            return Result.success();
        }
        // 拒收
        if (ObjectUtil.isNotEmpty(checkDTO.getResultType()) && ProjApplyOrderResultTypeEnum.REFUSED.getCode() == checkDTO.getResultType()) {
            return applyOrderService.checkReject(applyOrder);
        }
        return Result.fail();
    }

    @Transactional(rollbackFor = Throwable.class)
    public Result<String> checkAcceptance(ProjApplyOrder applyOrder, MilestoneNodeEnum milestoneNodeEnum, Long projectInfoId) {
        applyOrder.setAcceptanceTime(new Date());
        ProjApplyOrderResultTypeEnum typeEnum = ProjApplyOrderResultTypeEnum.getEnumDes(applyOrder.getResultType());
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder().refusedReason(StrUtil.EMPTY).rejectReason(StrUtil.EMPTY).telephone(StrUtil.EMPTY).operateContent(userUtil.getCurrentUserName() + typeEnum.getDesc()).operator(userUtil.getCurrentUserName()).build();
        applyOrder.setAcceptancePerson(userUtil.getCurrentUserName());
        updateApplyOrder(applyOrder, typeEnum.getCode());
        addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        if (ObjectUtil.isNotEmpty(milestoneNodeEnum)) {
            assert milestoneNodeEnum != null;
            updateMilestoneStatus(projectInfoId, milestoneNodeEnum);
        }
        return Result.success();
    }

    @Transactional(rollbackFor = Throwable.class)
    public Result<String> checkReject(ProjApplyOrder applyOrder) {
        ProjApplyOrderResultTypeEnum typeEnum = ProjApplyOrderResultTypeEnum.getEnumDes(applyOrder.getResultType());
        updateApplyOrder(applyOrder, typeEnum.getCode());
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder().refusedReason(StrUtil.EMPTY).rejectReason(StrUtil.EMPTY).telephone(StrUtil.EMPTY).operateContent(userUtil.getCurrentUserName() + typeEnum.getDesc()).operator(userUtil.getCurrentUserName()).build();
        addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        return Result.success();
    }

    /**
     * 更新部署申请单状态
     *
     * @param applyOrder
     * @param status
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updateApplyOrder(ProjApplyOrder applyOrder, int status) {
        ProjApplyOrder copy = new ProjApplyOrder();
        applyOrder.setResultType(status);
        copy.setId(applyOrder.getId());
        copy.setAcceptanceTime(ObjectUtil.isEmpty(applyOrder.getAcceptanceTime()) ? null : applyOrder.getAcceptanceTime());
        copy.setRefusedReason(ObjectUtil.isEmpty(applyOrder.getRefusedReason()) ? null : applyOrder.getRefusedReason());
        copy.setRejectReason(ObjectUtil.isEmpty(applyOrder.getRejectReason()) ? null : applyOrder.getRejectReason());
        copy.setResultType(status);
        copy.setOperationPerson(ObjectUtil.isEmpty(applyOrder.getOperationPerson()) ? null : applyOrder.getOperationPerson());
        copy.setAcceptancePerson(ObjectUtil.isEmpty(applyOrder.getAcceptancePerson()) ? null : applyOrder.getAcceptancePerson());
        copy.setResourceFileDeliveryPath(applyOrder.getResourceFileDeliveryPath());
        if (ObjectUtil.isNotEmpty(applyOrder.getFirstDeployNode())) {
            copy.setFirstDeployNode(applyOrder.getFirstDeployNode());
        }
        if (ObjectUtil.isNotEmpty(applyOrder.getIsLocalRoom())) {
            copy.setIsLocalRoom(applyOrder.getIsLocalRoom());
        }
        if (ObjectUtil.isNotEmpty(applyOrder.getDeployPhone())) {
            copy.setDeployPhone(applyOrder.getDeployPhone());
        }
        if (ObjectUtil.isNotEmpty(applyOrder.getDeployUserName())) {
            copy.setDeployUserName(applyOrder.getDeployUserName());
        }
        int count = projApplyOrderMapper.updateById(copy);
        log.info("{} 更新部署申请单状态. {}", LOG_PRE_YUNWEI_SAVE_LOG, count);
    }

    /**
     * 具体查询使用
     *
     * @param applyOrderMainDTO
     */

    public SysFileVO downloadPreResourceFromOutSystem(ProjApplyOrderMainDTO applyOrderMainDTO) {
        // 获取hospitals和products
        long projectInfoId = Long.valueOf(applyOrderMainDTO.getProjectInfoId());
        PreDownloadDTO preDownloadDTO = getPreDownloadParamDto(Long.valueOf(applyOrderMainDTO.getCustomInfoId()), Long.valueOf(projectInfoId));
        try {
            return downloadPreResourceFromOutSystemImpl(projectInfoId, preDownloadDTO);
        } catch (Throwable e) {
            log.error("下载预资源附件, e: {}, e=", e.getMessage(), e);
            return new SysFileVO();
        }
    }

    @Override
    public void deleteRecordByApplyOrderId(Long applyOrderId) {
        int count = applyOrderHospitalRecordService.deleteByApplyOrderId(applyOrderId);
        log.info("删除部署申请单记录-医院. count:{}", count);
        count = applyOrderProductRecordService.deleteByApplyOrderId(applyOrderId);
        log.info("删除部署申请单记录-产品. count:{}", count);
    }

    private PreDownloadDTO getPreDownloadParamDto(Long customInfoId, Long projectInfoId) {
        // 查询用户信息
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(customInfoId);
        if (ObjectUtil.isEmpty(customInfo)) {
            throw new RuntimeException(MessageFormat.format("{0} 未获取用户信息.", LOGPRE));
        }
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        if (ObjectUtil.isEmpty(projectInfo)) {
            throw new RuntimeException(MessageFormat.format("{0} 未获取项目信息.", LOGPRE));
        }
        // 设置医院
        PreDownloadDTO downloadDTO = getPreDownloadMainInfo(customInfo, projectInfo);
        List<ProjHospitalInfoRelative> hospitalInfoRelatives = applyOrderHospitalService.getApplyOrderHospitals(projectInfoId);
        // 检查医院信息
        if (CollUtil.isEmpty(hospitalInfoRelatives)) {
            throw new CustomException("未查询到机构信息, 若机构已存在, 请检查机构区划编码是否异常, 并通知管理员处理.");
        }
        List<PreDownloadCountryDTO> preDownloadCountryDTOS = getPreDownloadHospitalList(hospitalInfoRelatives, projectInfo);
        downloadDTO.setDeployResourceApplyCountryDTOList(preDownloadCountryDTOS);
        // 设置产品
        List<ProductInfo> productInfos = getProductInfoList(projectInfoId);
        List<ProjApplyOrderProduct> applyOrderProducts = findByYyOrderProductIdList(productInfos, projectInfoId);
        List<PreDownloadProduct> preDownloadProducts = getPreDownloadCustomDTOList(applyOrderProducts);
        downloadDTO.setDeployResourceApplyProductDTOList(preDownloadProducts);
        // 添加客户id
        downloadDTO.setCustomerId(customInfoId);
        return downloadDTO;
    }

    private PreDownloadDTO getPreDownloadMainInfo(ProjCustomInfo customInfo, ProjProjectInfo projectInfo) {
        PreDownloadDTO preDownloadDTO = new PreDownloadDTO();
        preDownloadDTO.setSubmitName(userUtil.getCurrentUserName());
        preDownloadDTO.setCustomerName(customInfo.getCustomName());
        preDownloadDTO.setCountryCount(1);
        preDownloadDTO.setDeployType(ProjApplyTypeEnum.FIRST_EVN_APPLY.getDevCode());
        preDownloadDTO.setDeployMod(ProjectTypeEnums.getEnum(projectInfo.getProjectType()).getDevCode());
        return preDownloadDTO;
    }

    private SysFileVO downloadPreResourceFromOutSystemImpl(long projectInfoId, PreDownloadDTO downloadDTO) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", yunWeiPlatFormService.getOperationPlatformToken().getData().getBody().getToken());
        headers.add("Content-Type", "application/json;charset=utf-8");
        HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(downloadDTO), headers);
        String url = devUrl + preDownloadUrl;
        log.info("{} 预资源附件下载url: {}", LOGPRE, url);
        org.springframework.core.io.Resource resource = new RestTemplate().postForObject(url, httpEntity, org.springframework.core.io.Resource.class);
        String fileName = resource.getFilename();
        String uploadTempFile = OBSClientUtils.getObsProjectPath(prePath, PRE_DOWNLOAD, String.valueOf(projectInfoId)) + fileName;
        // 上传至obs
        try (InputStream in = resource.getInputStream()) {
            File logTmpFile = new File(uploadTempFile);
            FileUtils.copyInputStreamToFile(in, logTmpFile);
            if (!logTmpFile.exists()) {
                throw new RuntimeException("上传日志文件异常");
            }
            OBSClientUtils.uploadFile(logTmpFile, uploadTempFile);
            FileUtils.forceDelete(logTmpFile);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        SysFileVO sysFileVO = new SysFileVO();
        sysFileVO.setFilePath(OBSClientUtils.getTemporaryUrl(uploadTempFile, 3600));
        sysFileVO.setFileName(fileName);
        return sysFileVO;
    }

    @Override
    public Result<ProjApplyOrderDetailsVO> applyOrderDetailsView(String applyOrderId) {
        // 查询主要信息
        // -- 根据当前节点查询日志信息
        ProjApplyOrder applyOrder = projApplyOrderMapper.selectById(Long.valueOf(applyOrderId));
        if (ObjectUtil.isEmpty(applyOrder)) {
            log.error("未查询到部署申请工单. applyOrderId: {}", applyOrderId);
            return Result.fail("未查询到部署申请工单");
        }
        // -- 查询客户信息, 根据customInfoId查询
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(applyOrder.getCustomInfoId());
        // -- 转换字段
        ProjApplyOrderMainInfoVO mainInfoVO = BeanUtil.copyProperties(applyOrder, ProjApplyOrderMainInfoVO.class);
        if (!ObjectUtil.isEmpty(customInfo)) {
            mainInfoVO.setCustomName(customInfo.getCustomName());
        } else {
            mainInfoVO.setCustomName(StrUtil.EMPTY);
            log.error("未查询到部署申请工单. customInfoId: {}", applyOrder.getCustomInfoId());
        }
        // 设置部署节点及服务器类型
        ProjProjectOrderRelation relation = mainService.getCloudResourceRelationBothType(applyOrder.getProjectInfoId());
        if (ObjectUtil.isNotEmpty(relation)) {
            ProjCustomCloudService cloudService = customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>().eq("custom_cloud_service_id", relation.getBussinessInfoId()));
            if (ObjectUtil.isNotEmpty(cloudService)) {
                mainInfoVO.setDeployNodeName(StrUtil.isBlank(cloudService.getDeployNodeName()) ? StrUtil.EMPTY : cloudService.getDeployNodeName());
                String cloudServiceTypeName = StrUtil.EMPTY;
                if (ObjectUtil.isNotEmpty(cloudService.getCloudServiceType())) {
                    CloudServiceTypeEnum typeEnum = CloudServiceTypeEnum.getCheckResultEnumByCode(cloudService.getCloudServiceType());
                    cloudServiceTypeName = ObjectUtil.isEmpty(typeEnum) ? StrUtil.EMPTY : typeEnum.getDesc();
                }
                mainInfoVO.setCloudServiceTypeName(cloudServiceTypeName);
            }
        }
        // 查询节点数组（各状态信息, 如申请、...、验收）
        List<ProjApplyOrderNodeRecord> applyOrderNodeRecord = applyOrderNodeRecordService.findByApplyOrderId(applyOrder.getId());
        List<ProjApplyOrderNodeRecordVO> applyOrderNodeRecordVOS = applyOrderNodeRecordConvert.po2Vo(applyOrderNodeRecord);
        // 查询产品信息
        List<ProjApplyOrderProductRecordRelative> applyOrderProductRecords = applyOrderProductRecordService.findByApplyOrderId(applyOrder.getId());
        List<ProjApplyOrderProductRecordRelativeVO> applyOrderProductRecordVOS = applyOrderProductRecordRelativeConvert.po2Vo(applyOrderProductRecords);
        // 查询所属环境
        String envname = StrUtil.EMPTY;
        String domain = StrUtil.EMPTY;
        List<ProjHospitalInfo> hospitalInfoList = applyOrderHospitalYunweiService.findOpenedHospitalInfo(applyOrder.getProjectInfoId());
        if (CollUtil.isNotEmpty(hospitalInfoList)) {
            envname = hospitalInfoList.get(0).getEnvName();
            domain = hospitalInfoList.get(0).getCloudDomain();
        } else {
            log.info("未查询到客户医院, customerInfoId: {}", applyOrder.getCustomInfoId());
        }
        ProjApplyOrderDetailsVO applyOrderDetailsVO = new ProjApplyOrderDetailsVO(mainInfoVO, applyOrderNodeRecordVOS, applyOrderProductRecordVOS, envname, domain);
        log.info("查询到详情: {}", applyOrderDetailsVO);
        return Result.success(applyOrderDetailsVO);
    }

    @Override
    public Result domainDetection(String applyDomainName) {
        boolean checkResult = yunWeiPlatFormService.checkDomain(mergeDomainName(applyDomainName));
        return Result.success(checkResult);
    }

    @Override
    public Result<String> uploadApplyOrderPrePlanResFile(ProjApplyOrderResFileDTO dto, MultipartFile file) {
        String uploadTempFile = OBSClientUtils.getObsProjectPath(prePath, PRE_DOWNLOAD, dto.getProjectInfoId()) + file.getOriginalFilename();
        // 上传至obs
        try {
            OBSClientUtils.uploadFileStream(file.getInputStream(), uploadTempFile, false);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.success(uploadTempFile);
    }

    @Override
    public Result<String> applyOrderSubmit(ProjApplyOrderDTO dto, String applyOrderNum) {
        // 判断订单是否存在
        // - 已申请
        if (dto.getApplyType() == ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode()) {
            if (StrUtil.isEmpty(dto.getId())) {
                Result<String> result = applyOrderService.newlyApplyOrderSubmit(dto, applyOrderNum);
                if (ObjectUtil.isNotEmpty(result) && result.isSuccess()) {
                    try {
                        // 同步运营平台工单信息
                        List<ProjApplyOrder> applyOrders = applyOrderMapper.selectList(new QueryWrapper<ProjApplyOrder>().eq("apply_num", Long.parseLong(applyOrderNum)));
                        if (CollUtil.isNotEmpty(applyOrders)) {
                            ProjApplyOrder applyOrder = applyOrders.get(0);
                            applyOrderEnvService.updateNodeStatusApplyOrder(applyOrder, CLOUD_APPLY);
                        }
                    } catch (Exception e) {
                        log.error("同步运营平台云资源申请单状态: {}, e=", e.getMessage(), e);
                    }
                }
            } else {
                if (dto.getResultType() == ProjApplyOrderResultTypeEnum.APPLYED.getCode()) {
                    throw new RuntimeException(MessageFormat.format("{0} 该客户新环境已申请, 请联系管理员.", LOGPRE));
                }
                return applyOrderService.retryApplyOrderSubmit(dto, applyOrderNum);
            }
        } else if (dto.getApplyType() == ProjApplyTypeEnum.PRODUCT_APPLY.getCode()) {
            // 从此进入一定是重新申请
            applyOrderService.retryProductApplyOrderSubmit(dto, applyOrderNum);
        } else if (dto.getApplyType() == ProjApplyTypeEnum.HOSPITAL_APPLY.getCode()) {
            // 从此进入一定是重新申请
            applyOrderService.retryHospitalApplyOrderSubmit(dto, applyOrderNum);
        }
        return Result.success();
    }

    /**
     * 产品开通重新发起申请
     *
     * @param dto           请求参数
     * @param applyOrderNum 工单号
     * @return Result<String>
     */
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> retryProductApplyOrderSubmit(ProjApplyOrderDTO dto, String applyOrderNum) {
        ProjApplyOrder applyOrder = projApplyOrderMapper.selectById(Long.valueOf(dto.getId()));
        ApplyOpenProductDTO applyOpenProductDTO = new ApplyOpenProductDTO();
        applyOpenProductDTO.setProjectId(applyOrder.getProjectInfoId());
        return orderProductService.applyOpenProductImpl(applyOpenProductDTO, applyOrder, applyOrderNum);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> retryHospitalApplyOrderSubmit(ProjApplyOrderDTO dto, String applyOrderNum) {
        ProjApplyOrder applyOrder = projApplyOrderMapper.selectById(Long.valueOf(dto.getId()));
        // 查询申请医院
        ProjApplyOrderHospitalRecord record = applyOrderHospitalRecordMapper.selectOne(new QueryWrapper<ProjApplyOrderHospitalRecord>().eq("apply_order_id", applyOrder.getId()));
        // 删除医院信息
        deleteRecordByApplyOrderId(applyOrder.getId());
        ProjHospitalInfoDTO hospitalInfoDTO = new ProjHospitalInfoDTO();
        hospitalInfoDTO.setHospitalInfoId(record.getHospitalInfoId());
        hospitalInfoDTO.setCustomInfoId(applyOrder.getCustomInfoId());
        hospitalInfoDTO.setProjectInfoId(applyOrder.getProjectInfoId());
        return applyOrderHospitalService.applyOrderHospital(hospitalInfoDTO, applyOrderNum);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> retryApplyOrderSubmit(ProjApplyOrderDTO dto, String applyOrderNum) {
        if (StrUtil.isEmpty(dto.getId())) {
            log.error("再次提交部署申请异常. id为空, param: {}", dto);
            throw new RuntimeException("再次提交部署申请异常.");
        }
        // 查询申请单
        ProjApplyOrder applyOrder = projApplyOrderMapper.selectById(Long.valueOf(dto.getId()));
        BeanUtil.copyProperties(applyOrder, dto);
        // 重新上传需要变更工单的单号才可以
        applyOrder.setApplyNum(Long.valueOf(applyOrderNum));
        applyOrder.setResultType(ProjApplyOrderResultTypeEnum.APPLYED.getCode());
        // 清楚记录
        deleteRecordByApplyOrderId(applyOrder.getId());
        applyOrder.setUpdateTime(null);
        applyOrder.setUpdaterId(null);
        applyOrderMapper.updateById(applyOrder);
        try {
            if (dto.getEnvirId() != null) {
                // 20250407 修改共享云处理逻辑。 提交后进行保存共享云节点处理。
                ProjCustomCloudService cloudService = customCloudServiceMapper.selectProjCustomCloudServiceByParamer(dto);
                if (cloudService.getCustomCloudServiceId() == null) {
                    customCloudServiceMapper.insert(cloudService);
                } else {
                    customCloudServiceMapper.updateById(cloudService);
                }
            }
        } catch (Exception e) {
            log.error("保存云资源派工单异常{} ", e);
        }
        return applyOrderSubmit(dto, applyOrder);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> newlyApplyOrderSubmit(ProjApplyOrderDTO dto, String applyOrderNum) {
        dto.setApplyType(ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode());
        ProjApplyOrder applyOrder = insertApplyOrder(dto, applyOrderNum);
        return applyOrderSubmit(dto, applyOrder);
    }

    /**
     * 新增环境
     *
     * @param dto2       请求参数
     * @param applyOrder 工单
     */
    public Result<String> applyOrderSubmit(ProjApplyOrderDTO dto2, ProjApplyOrder applyOrder) {
        Long projectInfoId = dto2.getProjectInfoId();
        StringBuilder tipStrBuilder = new StringBuilder();
        StringBuilder sysOperDetailBuilder = new StringBuilder();
        ProjProjectInfo projectInfo = mainService.getProjectInfo(dto2.getProjectInfoId());
        List<ProjHospitalInfo> notOpenHospitalInfos = applyOrderHospitalYunweiService.findNotOpenHospitalInfo(projectInfoId);
        Result<String> validateHospitalForEnv = applyOrderHospitalService.validateHospitalForEnv(projectInfo, notOpenHospitalInfos, tipStrBuilder, sysOperDetailBuilder);
        if (ObjectUtil.isEmpty(validateHospitalForEnv) || !validateHospitalForEnv.isSuccess()) {
            throw new CustomException(validateHospitalForEnv.getMsg());
        }
        // 订单保存
        if (ObjectUtil.isEmpty(applyOrder)) {
            throw new RuntimeException(MessageFormat.format("{0} 创建订单异常", LOGPRE));
        }
        // 拉取交付工单对应的医院与产品信息
        // - 存医院
        List<ProjHospitalInfoRelative> hospitals = addHospitalRecords(dto2, applyOrder);
        // - 更新医院域名
        String fullDomain = mergeDomainName(applyOrder.getDomainUrl());
        // - 存产品
        ProductTmp productTmp = addProductRecords(dto2, applyOrder);
        List<ProjApplyOrderProduct> applyOrderProducts = findArrangeProductForEnv(productTmp.getProductInfos(), applyOrder.getProjectInfoId());
        // - 保存节点日志 - 提示下一步预计谁负责审核，电话是多少
        SysConfig config = sysConfigMapper.selectConfigByName("env_deployment_reviewer");
        SysUser sysUser = null;
        if (config != null) {
            try {
                List<SysUser> listUser = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_yunying_id", config.getConfigValue()).eq("is_deleted", 0));
                sysUser = listUser.get(0);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder().refusedReason(StrUtil.EMPTY).rejectReason(StrUtil.EMPTY).telephone(StrUtil.EMPTY).operateContent(userUtil.getCurrentUserName() + ProjApplyOrderResultTypeEnum.APPLYED.getDesc()).operator(userUtil.getCurrentUserName()).operatorId(userUtil.getCurrentUserId()).build();
        StringBuffer sb = new StringBuffer(";");
        if (ObjectUtil.isNotEmpty(sysUser.getUserName())) {
            sb.append("下一步审核人:").append(sysUser.getUserName()).append(";");
        }
        if (ObjectUtil.isNotEmpty(sysUser.getPhone())) {
            sb.append("联系电话:").append(sysUser.getPhone()).append(";");
        }
        applyOrderNodeRecordParamDTO.setOperateContent(applyOrderNodeRecordParamDTO.getOperateContent() + sb);
        addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        // 调用运维平台接口创建订单
        SaveEnvDTO saveEnvDTO = applyOrderEnvService.getDevParamDto(dto2, fullDomain, applyOrder, hospitals, applyOrderProducts);
        SaveEnvSourceDTO saveEnvSourceDTO = new SaveEnvSourceDTO(hospitals, applyOrderProducts);
        try {
            // 调用部署申请接口
            Result result = applyOrderSubmitHandle(saveEnvDTO, saveEnvSourceDTO, applyOrder, hospitals);
            ResponseEntity<DepApplyOrderResult> envApplyRes = (ResponseEntity<DepApplyOrderResult>) result.getData();
            // 成功后更新医院的orgId和cloudHospitalId
            if (!CollUtil.isNotEmpty(envApplyRes.getBody().getDeployResourceApplyCountryVOList())) {
                throw new CustomException(MessageFormat.format("{0} 未得到反馈的医院信息, {1}", LOGPRE));
            }
            for (DepResourceApplyCountryResult depResourceApplyCountryResult : envApplyRes.getBody().getDeployResourceApplyCountryVOList()) {
                if (CollUtil.isEmpty(depResourceApplyCountryResult.getDeployResourceApplyHospitalVOList())) {
                    continue;
                }
                // 批量更新老库
                updateHospital(depResourceApplyCountryResult);
            }
            List<Long> needDeleteRecordIds;
            List<DepApplyProductResult> unCheckApplyProductVOList = envApplyRes.getBody().getUnCheckApplyProductVOList();
            // 作废无法不能部署的产品
            if (CollUtil.isNotEmpty(unCheckApplyProductVOList)) {
                needDeleteRecordIds = productTmp.getProductRecords().stream().filter(e -> unCheckApplyProductVOList.stream().anyMatch(f -> f.getCustomerProductId().equals(e.getYyOrderProductId().toString()))).map(m -> m.getProjApplyOrderProductRecordId()).collect(Collectors.toList());
                // 更新作废的产品
                int count = projApplyOrderMapper.updateDeleteByProductRecordIdList(needDeleteRecordIds);
                log.info("{} 对运维平台返回无法部署的产品记录标记作废. count: {}", LOGPRE, count);
            }
            //获取要回更产品数据
            List<Long> productIds = applyOrderProductService.getProductIdListByApplyOrderId(applyOrder.getId());
            //回更新老项目状态
            applyOrderProductService.updateProductOpenStatus(ProductOpenStatusEnum.OPENING, productIds, applyOrder.getProjectInfoId());
            try {
                // 成功后更新proj_custom_vs_project_type 表部署状态为 21已部署
                List<ProjCustomVsProjectTypeEntity> vsProjectTypeEntities = projCustomVsProjectTypeMapper.selectList(new QueryWrapper<ProjCustomVsProjectTypeEntity>().eq("custom_info_id", projectInfo.getCustomInfoId()).eq("project_type", projectInfo.getProjectType()));
                if (CollUtil.isNotEmpty(vsProjectTypeEntities)) {
                    vsProjectTypeEntities.forEach(e -> {
                        e.setDeploymentStatus(21);
                        projCustomVsProjectTypeMapper.updateById(e);
                    });
                }
            } catch (Exception e) {
                log.error("更新proj_custom_vs_project_type 表部署状态为 21已部署异常. err: {}", e.getMessage());
            }
            // 更新新老产品状态
            return Result.success();
        } catch (Throwable e) {
            throw new CustomException(MessageFormat.format("{0} 申请订单异常. err: {1}", LOGPRE, e.getMessage()));
        }
    }

    public void updateHospital(DepResourceApplyCountryResult depResourceApplyCountryResult) {
        for (DepResourceApplyHospitalResult hospitalResult : depResourceApplyCountryResult.getDeployResourceApplyHospitalVOList()) {
            if (ObjectUtil.isEmpty(hospitalResult.getCustomerInfoId())) {
                continue;
            }
            OldCustomerInfo oldCustomerInfo = new OldCustomerInfo();
            oldCustomerInfo.setOrgId(hospitalResult.getOrgId());
            oldCustomerInfo.setHospitalId(hospitalResult.getHospitalId());
            ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
            hospitalInfo.setOrgId(hospitalResult.getOrgId());
            hospitalInfo.setCloudHospitalId(hospitalResult.getHospitalId());
            hospitalInfo.setHospitalOpenStatus(HospitalOpenStatusEnum.OPENING.getCode());
            applyOrderHospitalService.updateHospitalRelativeInfo(oldCustomerInfo, hospitalInfo, CollUtil.newArrayList(hospitalResult.getCustomerInfoId()));
        }
    }

    private Result applyOrderSubmitHandle(SaveEnvDTO saveEnvDTO, SaveEnvSourceDTO saveEnvSourceDTO, ProjApplyOrder applyOrder, List<ProjHospitalInfoRelative> hospitals) {
        ResponseEntity<DepApplyOrderResult> envApplyRes = getDevPlatformResult(saveEnvDTO, applyOrder);
        if (envApplyRes.getStatusCode() == HttpStatus.OK) {
            DepApplyOrderResult depApplyOrderResult = envApplyRes.getBody();
            if (ObjectUtil.isEmpty(depApplyOrderResult)) {
                log.info("{} 返回值================:{}", LOGPRE, envApplyRes);
                throw new RuntimeException(MessageFormat.format("{0} 接口调用异常.", LOGPRE));
            }
            if (ProjApplyOrderResultTypeEnum.APPLYED.getCodeStr().equals(depApplyOrderResult.getStatus())) {
                log.info("{} 正常申请完成.", LOGPRE);
                return Result.success(envApplyRes);
            } else if (ProjApplyOrderResultTypeEnum.DELIVERED.getCodeStr().equals(depApplyOrderResult.getStatus()) || ProjApplyOrderResultTypeEnum.ACCEPTED.getCodeStr().equals(depApplyOrderResult.getStatus())) {
                // 获取医院和产品id
                List<Long> productIds = saveEnvSourceDTO.getApplyOrderProducts().stream().map(ProjApplyOrderProduct::getYyOrderProductId).collect(Collectors.toList());
                List<ProjHospitalInfo> hospitalInfos = hospitals.stream().map(e -> BeanUtil.copyProperties(e, ProjHospitalInfo.class)).collect(Collectors.toList());
                orderProductService.applyEmpowers(applyOrder.getProjectInfoId(), hospitalInfos, productIds);
                return Result.success();
            } else {
                revokeApplyOrder(applyOrder.getApplyNum());
                log.info("{} 运维平台返回部署申请状态: {}", LOGPRE, ProjApplyOrderResultTypeEnum.getEnumDes(Integer.parseInt(depApplyOrderResult.getStatus())).getDesc());
                throw new RuntimeException(MessageFormat.format("{0} 接口调用返回状态异常.", LOGPRE));
            }
        } else {
            log.info("{} 返回值================:{}", LOGPRE, envApplyRes);
            return Result.fail(envApplyRes.getStatusCode());
        }
    }

    /**
     * 撤销申请
     */
    public void revokeApplyOrder(Long orderId) {
        String url = devUrl + editStatusByDeliverPlatformApplyIdPath + "?deliverPlatformApplyId={orderId}";
        applyParamOperLogInsert(url + ",orderId=" + orderId, "撤销申请请求参数", "revokeApplyOrder");
        ResponseEntity<String> jsonObject = new RestTemplate().exchange(url, HttpMethod.GET, null, String.class, orderId);
        applyParamOperLogInsert(jsonObject, "撤销申请返回值", "revokeApplyOrder");
        log.info("撤销申请结果: {}", jsonObject);
    }

    /**
     * 查询部署产品集合
     *
     * @param products 产品
     * @return List<ProjApplyOrderProduct>
     */
    private List<ProjApplyOrderProduct> findByYyOrderProductIdList(List<ProductInfo> products, Long projectInfoId) {
        log.info("{} 查询部署产品集合. products: {}", LOGPRE, products);
        if (CollUtil.isEmpty(products)) {
            return CollUtil.newArrayList();
        }
        List<ProjApplyOrderProduct> applyOrderProducts = productArrangeRecordService.findByYyOrderProductIdList(products, projectInfoId);
        if (CollUtil.isEmpty(applyOrderProducts)) {
            return CollUtil.newArrayList();
        }
        return applyOrderProducts;
    }

    /**
     * 新环境部署查询部署产品
     *
     * @param products      产品id
     * @param projectInfoId 项目di
     * @return List<ProjApplyOrderProduct> 部署产品
     */
    private List<ProjApplyOrderProduct> findArrangeProductForEnv(List<ProductInfo> products, Long projectInfoId) {
        return findByYyOrderProductIdList(products, projectInfoId);
    }

    public void findAndSetBranchArrangeProduct(List<ProductInfo> products, Long projectInfoId, List<ProjApplyOrderProduct> arrangeProducts2) {
        List<ProjApplyOrderProduct> tmpArrangeProduct = CollUtil.newArrayList(arrangeProducts2);
        List<RuleProductRuleConfig> ruleProductRuleConfigs = ruleProductRuleConfigService.findRuleProductRuleConfigForBranch();
        if (CollUtil.isNotEmpty(ruleProductRuleConfigs)) {
            if (ProjOrderProductServiceImpl.containBranchModeProduct(products.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList()), ruleProductRuleConfigs)) {
                arrangeProducts2.clear();
                tmpArrangeProduct = tmpArrangeProduct.stream().filter(e -> ruleProductRuleConfigs.stream().noneMatch(f -> f.getYyProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
                arrangeProducts2.addAll(tmpArrangeProduct);
                // 查询分院模式产品
                List<ProjApplyOrderProduct> branchModeArrangeProduct = findBranchModeArrangeProduct(projectInfoId);
                arrangeProducts2.addAll(branchModeArrangeProduct);
            }
        }
    }

    public List<ProjApplyOrderProduct> findBranchModeArrangeProduct(Long projectInfoId) {
        List<ProductInfo> productInfos = orderProductService.findProductList(projectInfoId);
        // 分院产品
        List<ProjProductArrangeRecord> productArrangeRecords = productArrangeRecordMapper.selectList(new QueryWrapper<ProjProductArrangeRecord>().eq("project_info_id", projectInfoId));
        if (CollUtil.isEmpty(productArrangeRecords)) {
            return CollUtil.newArrayList();
        }
        // 排除已有工单产品的部署产品
        productArrangeRecords = productArrangeRecords.stream().filter(e -> productInfos.stream().noneMatch(f -> f.getYyOrderProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(productArrangeRecords)) {
            return CollUtil.newArrayList();
        }
        // 转换为条件准备查询
        List<ProductInfo> tmpProductInfos = productArrangeRecords.stream().map(e -> {
            ProductInfo productInfo = new ProductInfo();
            productInfo.setYyOrderProductId(e.getYyOrderProductId());
            return productInfo;
        }).collect(Collectors.toList());
        return productArrangeRecordMapper.findByYyOrderProductIdList(tmpProductInfos, projectInfoId);
    }

    @Override
    public List<ProjApplyOrderProduct> findArrangeProduct(List<ProductInfo> products, Long projectInfoId) {
        return findByYyOrderProductIdList(products, projectInfoId);
    }

    public ProjApplyOrderNodeRecord addApplyOrderNodeRecord(ProjApplyOrder applyOrder, ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO) {
        ProjApplyOrderNodeRecord nodeRecord = new ProjApplyOrderNodeRecord();
        nodeRecord.setProjApplyOrderNodeRecordId(SnowFlakeUtil.getId());
        nodeRecord.setApplyOrderId(applyOrder.getId());
        nodeRecord.setOperator(applyOrderNodeRecordParamDTO.getOperator());
        nodeRecord.setOperatorId(applyOrderNodeRecordParamDTO.getOperatorId());
        nodeRecord.setNodeTypeCode(applyOrder.getResultType());
        nodeRecord.setTelephone(applyOrderNodeRecordParamDTO.getTelephone());
        nodeRecord.setOperateContent(applyOrderNodeRecordParamDTO.getOperateContent());
        nodeRecord.setRejectReason(applyOrderNodeRecordParamDTO.getRejectReason());
        nodeRecord.setRefusedReason(applyOrderNodeRecordParamDTO.getRefusedReason());
        int count = applyOrderNodeRecordService.insertOrderNodeRecord(nodeRecord);
        if (count <= 0) {
            throw new RuntimeException(MessageFormat.format("{0} 创建订单异常", LOGPRE));
        }
        log.info("{} 批量保存交付产品记录. count: {}", LOGPRE, count);
        return nodeRecord;
    }

    public ProductTmp addProductRecords(ProjApplyOrderDTO dto2, ProjApplyOrder applyOrder) {
        ProductTmp productTmp = new ProductTmp();
        List<ProductInfo> productInfos = getProductInfoList(applyOrder.getProjectInfoId());
        List<ProjApplyOrderProductRecord> productRecords = addProductRecordsImpl(applyOrder, productInfos);
        productTmp.setProductRecords(productRecords);
        productTmp.setProductInfos(productInfos);
        return productTmp;
    }

    public void addProductRecordsForHospitalApply(ProjApplyOrder applyOrder) {
        // 查询项目
        List<ProductInfo> productInfos = findOpendProductForCustomInfo(applyOrder.getProjectInfoId());
        if (CollUtil.isEmpty(productInfos)) {
            log.warn("申请医院开通, 未查询到相关产品. apply_num: {}, projectInfoId: {}", applyOrder.getApplyNum(), applyOrder.getProjectInfoId());
            return;
        }
        addProductRecordsImpl(applyOrder, productInfos);
    }

    public List<ProductInfo> findOpendProductForCustomInfo(Long projectInfoId) {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        int projectType = projectInfo.getProjectType();
        // 查询客户下所有项目
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq("custom_info_id", projectInfo.getCustomInfoId()).eq("project_type", projectType));
        List<ProductInfo> productInfos = CollUtil.newArrayList();
        for (ProjProjectInfo info : projectInfos) {
            productInfos.addAll(hospitalYunweiService.productOpened(info.getProjectInfoId()));
        }
        return productInfos;
    }

    public List<ProjApplyOrderProductRecord> addProductRecordsImpl(ProjApplyOrder applyOrder, List<ProductInfo> productInfos) {
        List<ProjApplyOrderProductRecord> productRecords = productInfos.stream().map(e -> {
            ProjApplyOrderProductRecord record = new ProjApplyOrderProductRecord();
            record.setProjApplyOrderProductRecordId(SnowFlakeUtil.getId());
            record.setApplyOrderId(applyOrder.getId());
            record.setProjectInfoId(applyOrder.getProjectInfoId());
            record.setCustomInfoId(applyOrder.getCustomInfoId());
            record.setProductDictId(e.getYyOrderProductId());
            record.setProductType(ProductType.ORDER.getCode());
            record.setYyOrderProductId(e.getYyOrderProductId());
            return record;
        }).collect(Collectors.toList());
        int count = applyOrderProductRecordService.insertBatch(productRecords);
        log.info("{} 批量保存交付产品记录. count: {}", LOGPRE, count);
        if (count <= 0) {
            throw new RuntimeException(MessageFormat.format("{0} 创建订单异常", LOGPRE));
        }
        return productRecords;
    }

    /**
     * 根据项目id获取交付工单产品集合
     *
     * @param projectInfoId
     * @return
     */
    private List<ProductInfo> getProductInfoList(Long projectInfoId) {
        ProductInfoDTO infoDTO = new ProductInfoDTO();
        infoDTO.setProjectInfoId(projectInfoId);
        return orderProductService.getProductListImpl(infoDTO);
    }

    public List<ProjHospitalInfoRelative> addHospitalRecords(ProjApplyOrderDTO dto, ProjApplyOrder applyOrder) {
        return addHospitalRecords(dto, applyOrder, false);
    }

    @Override
    public List<ProjHospitalInfoRelative> addHospitalRecords(ProjApplyOrderDTO dto, ProjApplyOrder applyOrder, boolean onlyOpen) {
        List<ProjHospitalInfoRelative> hospitalInfoList = findApplyHospitalInfoRelative(applyOrder.getProjectInfoId(), onlyOpen);
        insertApplyOrderHospitalRecord(hospitalInfoList, applyOrder);
        return hospitalInfoList;
    }

    /**
     * 查询医院
     *
     * @param projectInfoId 项目Id
     * @param onlyOpen      true:只查询开通的
     * @return list
     */
    public List<ProjHospitalInfoRelative> findApplyHospitalInfoRelative(Long projectInfoId, boolean onlyOpen) {
        List<ProjHospitalInfoRelative> hospitalInfoList = applyOrderHospitalService.getApplyOrderHospitals(projectInfoId);
        if (CollUtil.isEmpty(hospitalInfoList)) {
            throw new RuntimeException("未查询到医院. projectInfoId:" + projectInfoId);
        }
        if (onlyOpen) {
            hospitalInfoList = hospitalInfoList.stream().filter(e -> e.getHospitalOpenStatus() == HospitalOpenStatusEnum.OPENED.getCode() || e.getHospitalOpenStatus() == HospitalOpenStatusEnum.ONLINE.getCode()).collect(Collectors.toList());
        }
        return hospitalInfoList;
    }

    public void insertApplyOrderHospitalRecord(List<ProjHospitalInfoRelative> hospitalInfoList, ProjApplyOrder applyOrder) {
        List<ProjApplyOrderHospitalRecord> hospitalRecords = hospitalInfoList.stream().map(e -> {
            ProjApplyOrderHospitalRecord record = new ProjApplyOrderHospitalRecord();
            record.renderHospital(e);
            record.setApplyOrderId(applyOrder.getId());
            record.setProjectInfoId(applyOrder.getProjectInfoId());
            record.setCustomInfoId(applyOrder.getCustomInfoId());
            record.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            record.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            record.setDictHospitalOrgId(e.getDictHospitalOrgId());
            return record;
        }).collect(Collectors.toList());
        int batchHosRecordCount = applyOrderHospitalRecordService.inserBatch(hospitalRecords);
        log.info("{} 批量保存交付医院记录. count: {}", LOGPRE, batchHosRecordCount);
        if (batchHosRecordCount <= 0) {
            throw new RuntimeException(MessageFormat.format("{0} 创建订单异常", LOGPRE));
        }
    }

    /**
     * 创建部署工单
     *
     * @param dto 请求参数
     * @return ProjApplyOrder
     */
    @Override
    public ProjApplyOrder insertApplyOrder(ProjApplyOrderDTO dto, String applyOrderNum) {
        ProjApplyOrder applyOrder = BeanUtil.copyProperties(dto, ProjApplyOrder.class);
        applyOrder.setId(SnowFlakeUtil.getId());
        // 保存工单, 生成工单号
        applyOrder.setApplyNum(Long.valueOf(applyOrderNum));
        applyOrder.setApplyTime(new Date());
        applyOrder.setApplyType(dto.getApplyType());
        applyOrder.setApplicant(userUtil.getCurrentUserName());
        applyOrder.setResultType(ProjApplyOrderResultTypeEnum.APPLYED.getCode());
        // 处理私有云上传的预规划附件
        handlePrivateEnvCloudResourceFile(dto, applyOrder);
        applyOrder.setCloudEnv(dto.getCloudEnv());
        applyOrder.setDomainUrl(dto.getApplyDomain());
        int count = projApplyOrderMapper.insert(applyOrder);
        log.info("{} 保存影响条数: {}, 保存后: {}", LOGPRE, count, applyOrder);
        if (count <= 0) {
            throw new RuntimeException(MessageFormat.format("{0} 创建订单异常", LOGPRE));
        }
        try {
            if (dto.getEnvirId() != null) {
                // 20250407 修改共享云处理逻辑。 提交后进行保存共享云节点处理。
                ProjCustomCloudService cloudService = customCloudServiceMapper.selectProjCustomCloudServiceByParamer(dto);
                if (cloudService.getCustomCloudServiceId() == null) {
                    customCloudServiceMapper.insert(cloudService);
                } else {
                    customCloudServiceMapper.updateById(cloudService);
                }
            }
        } catch (Exception e) {
            log.error("保存云资源派工单异常{} ", e);
        }
        return applyOrder;
    }

    /**
     * 处理私有云申请时上传的文件
     *
     * @param dto        请求参数
     * @param applyOrder 要保存的申请单内容
     */
    public void handlePrivateEnvCloudResourceFile(ProjApplyOrderDTO dto, ProjApplyOrder applyOrder) {
        try {
            // 产品和医院开通时跳过
            if (ObjectUtil.isEmpty(dto.getCloudEnv())) {
                return;
            }
            if (dto.getCloudEnv() != CloudEnvTypeEnum.PRIVATE.getCode()) {
                dto.setResourceFilePath(StrUtil.EMPTY);
            } else {
                // 私有云. 若dto.getResourceFilePath文件路径为空, 则采用销售上传的文件
                if (StrUtil.isBlank(dto.getResourceFilePath())) {
                    ProjProjectFile projectFile = applyOrderDescService.getPreResourceProjectFile(dto.getProjectInfoId());
                    if (ObjectUtil.isNotEmpty(projectFile)) {
                        applyOrder.setResourceFilePath(projectFile.getFilePath());
                    }
                } else {
                    // 私有云. 若dto.getResourceFilePath不为空, 则采用重新上传的路径, 并更新规则表上传路径
                    applyOrder.setResourceFilePath(dto.getResourceFilePath());
                    ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO = new ProjProjectSettlementCheckSaleSaveDTO();
                    settlementCheckSaleSaveDTO.setCloudFileUrl(dto.getResourceFilePath());
                    // 设置文件名称
                    String[] names = dto.getResourceFilePath().split(StrUtil.SLASH);
                    String fileName = names[names.length - 1];
                    settlementCheckSaleSaveDTO.setCloudFileOriginalName(fileName);
                    settlementCheckSaleService.handleCloudFile(dto.getProjectInfoId(), settlementCheckSaleSaveDTO, SettlementRuleCodeEnum.SETTLEMENT_PLAN_CLOUD_FILE);
                }
            }
        } catch (Exception e) {
            log.error("保存私有云资源文件异常{} ", e);
            throw new CustomException("请上传预资源规划文件", e);
        }
    }

    /**
     * 获取请求头
     *
     * @return HttpHeaders
     */
    public HttpHeaders getRequestHeader() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", yunWeiPlatFormService.getAuthorization());
        headers.add("Content-Type", "multipart/form-data");
        return headers;
    }

    public ResponseEntity<DepApplyOrderResult> getDevPlatformResult(SaveEnvDTO dto, ProjApplyOrder applyOrder) {
        HttpHeaders headers = getRequestHeader();
        MultiValueMap<String, Object> envApplyMap = new LinkedMultiValueMap<>();
        envApplyMap.add("dto", dto);
        log.info("{} dto参数：{}", LOGPRE, JSON.toJSONString(dto));
        String uploadFilePath = StrUtil.EMPTY;
        if (applyOrder.getCloudEnv() == CloudEnvTypeEnum.PRIVATE.getCode()) {
            FileSystemResource fileSystemResource = getFileSystemResource(applyOrder.getCustomInfoId().toString(), applyOrder.getResourceFilePath());
            if (ObjectUtil.isEmpty(fileSystemResource)) {
                throw new RuntimeException(MessageFormat.format("{0} 未查询到私有云需要上传的预资源规划附件", LOGPRE));
            }
            envApplyMap.add("file", fileSystemResource);
            uploadFilePath = fileSystemResource.getPath();
        }
        log.info("{} 入参================:{}", LOGPRE, JSON.toJSONString(dto));
        HttpEntity<MultiValueMap<String, Object>> httpRequest = new HttpEntity<>(envApplyMap, headers);
        String postUrl = MessageFormat.format("{0}{1}", devUrl, saveEnvPath);
        try {
            // 记录, 请求
            sysOperLogService.apiOperLogInsertObjAry("部署申请-请求参数", StrUtil.EMPTY, Log.LogOperType.APPLY.getCode(), dto, uploadFilePath, headers);
            ResponseEntity<DepApplyOrderResult> envApplyRes = new RestTemplate().postForEntity(postUrl, httpRequest, DepApplyOrderResult.class);
            applyParamOperLogInsert(envApplyRes, "部署申请-返回值", "getDevPlatformResult");
            if (ObjectUtil.isEmpty(envApplyRes)) {
                log.info("{} 返回值================:{}", LOGPRE, envApplyRes);
                throw new RuntimeException(MessageFormat.format("{0} 接口调用异常.", LOGPRE));
            }
            return envApplyRes;
        } catch (Exception e) {
            log.error("新环境申请异常. message: {}, e=", e.getMessage(), e);
            throw new RuntimeException(MessageFormat.format("{0} 异常. 接口异常, url: {1}, error: {1}", LOGPRE, postUrl, e.getMessage()));
        }
    }

    /**
     * 加入操作日志
     *
     * @param obj
     * @param operName
     * @param operKeyWord
     */
    public void applyParamOperLogInsert(Object obj, String operName, String operKeyWord) {
        try {
            SysOperLog sysOperLog = new SysOperLog();
            sysOperLog.setSysOperLogId(SnowFlakeUtil.getId());
            sysOperLog.setOperType(Log.LogOperType.APPLY.getCode());
            sysOperLog.setOperDetail(ObjectUtil.isEmpty(obj) ? StrUtil.EMPTY : JSONObject.toJSONString(obj));
            sysOperLog.setOperModule("调用第三方接口");
            sysOperLog.setOperName(operName);
            sysOperLog.setOperKeyWord(operKeyWord);
            AsyncManager.me().execute(LogAspect.AsyncFactory.operLogSave(sysOperLog));
        } catch (Throwable e) {
        }
    }

    /**
     * 用于资源预下载
     *
     * @param products
     * @return
     */
    private List<PreDownloadProduct> getPreDownloadCustomDTOList(List<ProjApplyOrderProduct> products) {
        List<PreDownloadProduct> preDownloadProducts = new ArrayList<>();
        products.forEach(e -> {
            PreDownloadProduct preDownloadProduct = new PreDownloadProduct();
            preDownloadProduct.setCustomerProductName(e.getProductName());
            preDownloadProduct.setCustomerProductCode(e.getYyProductCode());
            preDownloadProduct.setCustomerProductId(e.getYyOrderProductId());
            preDownloadProducts.add(preDownloadProduct);
        });
        return preDownloadProducts;
    }

    private List<PreDownloadCountryDTO> getPreDownloadHospitalList(List<ProjHospitalInfoRelative> hospitals, ProjProjectInfo projectInfo) {
        List<PreDownloadCountryDTO> preDownloadCountryDTOS = new ArrayList<>();
        PreDownloadCountryDTO preDownloadCountryDTO = new PreDownloadCountryDTO();
        preDownloadCountryDTOS.add(preDownloadCountryDTO);
        List<PreDownloadHospital> preDownloadHospitals = new ArrayList<>();
        preDownloadCountryDTO.setDeployResourceApplyHospitalDTOList(preDownloadHospitals);
        // 获取日门诊量(客户下所有医院数量) 并 添加所属属医院
        AtomicInteger dayClinicCount = new AtomicInteger();
        AtomicInteger bedCount = new AtomicInteger();
        AtomicInteger termnalCount = new AtomicInteger();
        AtomicInteger peopleCount = new AtomicInteger();
        AtomicInteger clinicCount = new AtomicInteger();
        hospitals.forEach(e -> {
            dayClinicCount.addAndGet(ObjectUtil.isEmpty(e.getHospitalOutPatientCount()) ? 0 : e.getHospitalOutPatientCount().intValue());
            bedCount.addAndGet(ObjectUtil.isEmpty(e.getHospitalBedCount()) ? 0 : e.getHospitalBedCount().intValue());
            termnalCount.addAndGet(e.getTerminalCount());
            peopleCount.addAndGet(e.getPopulation());
            clinicCount.addAndGet(e.getClinicCount());
            PreDownloadHospital preDownloadHospital = new PreDownloadHospital();
            preDownloadHospital.setHospitalName(e.getHospitalName());
            preDownloadHospital.setAdministrativeDivisions(e.getAdministrativeDivisions());
            preDownloadHospital.setAdministrativeCode(e.getAdministrativeCode());
            preDownloadHospital.setHospitalType(HospitalTypeEnum.getEnumByCode(projectInfo.getProjectType()).getDevCode());
            preDownloadHospitals.add(preDownloadHospital);
        });
        preDownloadCountryDTO.setDayClinicCount(dayClinicCount.intValue());
        preDownloadCountryDTO.setBedCount(bedCount.intValue());
        preDownloadCountryDTO.setAdministrativeDivisions(hospitals.get(0).getAdministrativeDivisions());
        preDownloadCountryDTO.setTerminalCount(termnalCount.intValue());
        preDownloadCountryDTO.setPeopleCount(peopleCount.intValue());
        preDownloadCountryDTO.setClinicCount(clinicCount.intValue());
        if (projectInfo.getProjectType() == ProjectTypeEnums.SINGLE.getCode().intValue()) {
            preDownloadCountryDTO.setRemarks(hospitals.get(0).getHospitalName());
        } else {
            preDownloadCountryDTO.setRemarks(hospitals.get(0).getTownName());
        }
        return preDownloadCountryDTOS;
    }

    /**
     * 获取可以上传的资源文件
     *
     * @param customInfoId 客户id
     * @param filePath     文件路径
     * @return
     */
    public FileSystemResource getFileSystemResource(String customInfoId, String filePath) {
        return getFileSystemResource(bucketName, customInfoId, filePath);
    }

    @Override
    public Result applyOrderSubmitAgain(ProjApplyOrderDTO dto) {
        return Result.success();
    }

    /**
     * 获取申请单信息
     *
     * @param dto
     * @return
     */
    @Override
    public ProjApplyOrderVO getApplyOrderInfo(ProjApplyOrderDTO dto) {
        ProjApplyOrder applyOrder = projApplyOrderMapper.selectOne(new QueryWrapper<ProjApplyOrder>().eq(ObjectUtil.isNotEmpty(dto.getProjectInfoId()), "project_info_id", dto.getProjectInfoId()).eq(ObjectUtil.isNotEmpty(dto.getCustomInfoId()), "custom_info_id", dto.getCustomInfoId()).eq(ObjectUtil.isNotEmpty(dto.getHospitalInfoId()), "hospital_info_id", dto.getHospitalInfoId()).eq(ObjectUtil.isNotEmpty(dto.getApplyType()), "apply_type", dto.getApplyType()).eq(ObjectUtil.isNotEmpty(dto.getResultType()), "result_type", dto.getResultType()));
        ProjApplyOrderVO projApplyOrderVO = applyOrderConvert.po2Vo(applyOrder);
        return projApplyOrderVO;
    }

    /**
     * 获取申请单信息
     *
     * @param dto
     * @return
     */
    @Override
    public List<ProjApplyOrderVO> getApplyOrderInfoList(ProjApplyOrderDTO dto) {
        List<ProjApplyOrder> applyOrderList = projApplyOrderMapper.selectList(new QueryWrapper<ProjApplyOrder>().eq(ObjectUtil.isNotEmpty(dto.getProjectInfoId()), "project_info_id", dto.getProjectInfoId()).eq(ObjectUtil.isNotEmpty(dto.getCustomInfoId()), "custom_info_id", dto.getCustomInfoId()).eq(ObjectUtil.isNotEmpty(dto.getHospitalInfoId()), "hospital_info_id", dto.getHospitalInfoId()).eq(ObjectUtil.isNotEmpty(dto.getApplyType()), "apply_type", dto.getApplyType()).eq(ObjectUtil.isNotEmpty(dto.getResultType()), "result_type", dto.getResultType()).orderByDesc("create_time"));
        List<ProjApplyOrderVO> voList = applyOrderConvert.po2Vo(applyOrderList);
        return voList;
    }

    @Override
    public Result<PageInfo<ProjApplyOrderHospitalRecordRelativeVO>> applyOrderDetailsViewHospital(ProjApplyOrderHospitalRecordDTO dto) {;
        List<ProjApplyOrderHospitalRecordRelative> applyOrderHospitalRecordRelatives = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> applyOrderHospitalRecordService.findByApplyOrderId(dto.getApplyOrderId()));
        PageInfo<ProjApplyOrderHospitalRecordRelative> pageInfo = new PageInfo<>(applyOrderHospitalRecordRelatives);
        List<ProjApplyOrderHospitalRecordRelativeVO> vos = applyOrderHospitalRecordRelativeConvert.po2Vo(applyOrderHospitalRecordRelatives);
        PageInfo<ProjApplyOrderHospitalRecordRelativeVO> pageInfoVO = PageInfo.of(vos);
        pageInfoVO.setTotal(pageInfo.getTotal());
        return Result.success(pageInfoVO);
    }

    @Override
    public boolean isEnvOpen(Long projectInfoId) {
        ProjApplyOrderDTO applyOrderDTO = new ProjApplyOrderDTO();
        applyOrderDTO.setProjectInfoId(projectInfoId);
        applyOrderDTO.setApplyType(ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode());
        List<ProjApplyOrderVO> applyOrderVOList = getApplyOrderInfoList(applyOrderDTO);
        if (CollUtil.isEmpty(applyOrderVOList)) {
            return false;
        }
        ProjApplyOrderVO applyOrderVO = applyOrderVOList.get(0);
        if (ObjectUtil.isEmpty(applyOrderVO)) {
            log.info("是否部署云环境. projectInfoId: {}", projectInfoId);
            return false;
        }
        return isEnvOpenByResultType(applyOrderVO.getResultType());
    }

    /**
     * 根据部署结果类型判断是否部署交付完成.
     *
     * @param resultType 结果类型. 对应枚举中ProjApplyOrderResultTypeEnum的状态code
     * @return boolean
     */
    private boolean isEnvOpenByResultType(Integer resultType) {
        if (ObjectUtil.isEmpty(resultType)) {
            return false;
        }
        return resultType == ProjApplyOrderResultTypeEnum.DELIVERED.getCode() || resultType == ProjApplyOrderResultTypeEnum.ACCEPTED.getCode();
    }

    @Override
    public boolean isFirstProject(ProjProjectInfo projectInfo) {
        if (ObjectUtil.isEmpty(projectInfo)) {
            throw new RuntimeException(MessageFormat.format("是否是首期项目查询. 未查询到此项目. projectInfo: {0}", projectInfo));
        }
        if (ObjectUtil.isEmpty(projectInfo.getHisFlag())) {
            throw new RuntimeException(MessageFormat.format("是否是首期项目查询. 未查询到项目是否是首期标识. projectInfo: {0}", projectInfo));
        }
        // 判断云资源是否已部署
        return ProjectHisFlagEnum.FRESH.getCode() == projectInfo.getHisFlag();
    }

    /**
     * 发送审核消息
     */
    @Override
    public void voidTimeSendEarlywarMsg() {
        // 查询超固定时间未审核的单据进行预警提醒。 发送消息后不在发送预警消息。
        // 先发送审核消息。
        // 给提交人发送驳回消息
        // - 保存节点日志 - 提示下一步预计谁负责审核，电话是多少
        SysConfig config = sysConfigMapper.selectConfigByName("env_deployment_reviewer");
        SysUser sysUser = null;
        if (config != null) {
            try {
                List<SysUser> listUser = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_yunying_id", config.getConfigValue()).eq("is_deleted", 0));
                sysUser = listUser.get(0);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        List<Long> sysUserIds = new ArrayList<>();
        sysUserIds.add(464993509700489218L);
        if (ActiveEnum.PROD.getActive().equals(activeProfiles)) {
            sysUserIds.add(sysUser.getSysUserId());
        }
        // 查询申请单超10分钟的数据
        List<ProjApplyOrder> list = projApplyOrderMapper.selectByReminder();
        if (list != null && list.size() > 0) {
            for (ProjApplyOrder order : list) {
                String key = "yjxx" + order.getId();
                Object value = redisUtil.get(key);
                if (ObjectUtils.isEmpty(value)) {
                    SendMessageParam messageParam = new SendMessageParam();
                    messageParam.setMessageTypeId(DictMessageTypeEnum.DEPLOYMENT_AUDIT_REMINDER.getId());
                    messageParam.setProjectInfoId(order.getProjectInfoId());
                    messageParam.setMessageContentParam(this.getIssueMessageContentParam(order.getProjectInfoId(), order.getApplyNum()));
                    messageParam.setSysUserIds(sysUserIds);
                    sendMessageService.sendMessage2(messageParam);
                    int time = order.getCron() > 0 ? order.getCron() : 10;
                    redisUtil.set(key, order.getId(), time, TimeUnit.MINUTES);
                }
            }
        }
    }

    /**
     * 发送预警及超期部署消息
     */
    @Override
    public void voidTimeSendEarlywarToYunweiMsg() {
        SysConfig config = sysConfigMapper.selectConfigByName("env_deployment_reviewer");
        SysUser sysUser = null;
        if (config != null) {
            try {
                List<SysUser> listUser = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_yunying_id", config.getConfigValue()).eq("is_deleted", 0));
                sysUser = listUser.get(0);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        List<Long> sysUserIds = null;
        // 查询申请单超部署时长的数据
        List<ProjApplyOrder> list = projApplyOrderMapper.selectByReminderTimeOut();
        if (list != null && list.size() > 0) {
            for (ProjApplyOrder order : list) {
                try {
                    sysUserIds = new ArrayList<>();
                    sysUserIds.add(464993509700489218L);
                    if (ActiveEnum.PROD.getActive().equals(activeProfiles)) {
                        if (order.getDeployUserId() != null) {
                            sysUserIds.add(order.getDeployUserId());
                        }
                        sysUserIds.add(sysUser.getSysUserId());
                    }
                    String key = "bushucaoqi" + order.getId();
                    Object value = redisUtil.get(key);
                    if (ObjectUtils.isEmpty(value)) {
                        SendMessageParam messageParam = new SendMessageParam();
                        messageParam.setMessageTypeId(DictMessageTypeEnum.DEPLOYMENT_TIMEOUT_REMINDER.getId());
                        messageParam.setProjectInfoId(order.getProjectInfoId());
                        messageParam.setMessageContentParam(this.getIssueMessageContentParam(order.getProjectInfoId(), order.getApplyNum()));
                        messageParam.setSysUserIds(sysUserIds);
                        sendMessageService.sendMessage2(messageParam);
                        int time = order.getCron() > 0 ? order.getCron() : 30;
                        redisUtil.set(key, order.getId(), time, TimeUnit.MINUTES);
                    }
                } catch (Exception e) {
                    log.error("超期后异常，" + e);
                }

            }
        }


        // 查询申请单预警的数据
        List<ProjApplyOrder> listerr = projApplyOrderMapper.selectByReminderTimeWarning();
        if (listerr != null && listerr.size() > 0) {
            for (ProjApplyOrder order : listerr) {
                try {
                    sysUserIds = new ArrayList<>();
                    sysUserIds.add(464993509700489218L);
                    if (ActiveEnum.PROD.getActive().equals(activeProfiles)) {
                        if (order.getDeployUserId() != null) {
                            sysUserIds.add(order.getDeployUserId());
                        }
                        sysUserIds.add(sysUser.getSysUserId());
                    }
                    String key = "bushuyujing" + order.getId();
                    Object value = redisUtil.get(key);
                    if (ObjectUtils.isEmpty(value)) {
                        SendMessageParam messageParam = new SendMessageParam();
                        messageParam.setMessageTypeId(DictMessageTypeEnum.DEPLOYMENT_EARLY_WARNING_REMINDER.getId());
                        messageParam.setProjectInfoId(order.getProjectInfoId());
                        messageParam.setMessageContentParam(this.getIssueMessageContentParam(order.getProjectInfoId(), order.getApplyNum()));
                        messageParam.setSysUserIds(sysUserIds);
                        sendMessageService.sendMessage2(messageParam);
                        int time = order.getCron() > 0 ? order.getCron() : 30;
                        redisUtil.set(key, order.getId(), time, TimeUnit.MINUTES);
                    }
                } catch (Exception e) {
                    log.error("超期后异常，" + e);
                }
            }
        }
    }

    private Map<String, String> getIssueMessageContentParam(Long projectInfoId, Long applyNum) {
        ProjProjectInfo projProjectInfo = this.projectInfoMapper.selectById(projectInfoId);
        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("projectName", projProjectInfo.getProjectName());
        messageContentParam.put("orderNumber", String.valueOf(applyNum));
        return messageContentParam;
    }

    public List<ProjApplyOrderRelative> findProjApplyOrderListImpl(ProjApplyOrderListDTO dto) {
        ProjApplyOrder projApplyOrder = BeanUtil.copyProperties(dto, ProjApplyOrder.class);
        return projApplyOrderMapper.findProjApplyOrderInfoList(projApplyOrder);
    }

    /**
     * 暂存产品信息
     */
    @Data
    public static class ProductTmp {
        List<ProductInfo> productInfos;

        List<ProjApplyOrderProductRecord> productRecords;

    }
}
