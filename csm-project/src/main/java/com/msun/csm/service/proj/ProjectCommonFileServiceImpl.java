package com.msun.csm.service.proj;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.mapper.dict.DictAgentScenarioConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.model.param.GetProjectFileRuleParam;
import com.msun.csm.model.req.CheckResult;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.req.GetProjectFileRuleReq;
import com.msun.csm.model.req.UpdateUseFlagReq;
import com.msun.csm.model.req.projectfile.DeleteFileReq;
import com.msun.csm.model.req.projectfile.UploadFileReq2;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.vo.FileUploadRuleVO;
import com.msun.csm.model.vo.ProjProjectFileVO;
import com.msun.csm.model.vo.ProjectFileUploadRuleVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.rule.RuleProjectRuleConfigService;
import com.msun.csm.util.ListUtils;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;

@Slf4j
@Service
public class ProjectCommonFileServiceImpl implements ProjectCommonFileService {

    @Value("${project.obs.prePath}")
    private String prePath;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private RuleProjectRuleConfigService ruleProjectRuleConfigService;

    @Resource
    private DictAgentScenarioConfigMapper dictAgentScenarioConfigMapper;

    @Resource
    private BaseQueryService baseQueryService;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjectCommonFileVsRuleService projectCommonFileVsRuleService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public FileUploadRuleVO getProjectFileRule(GetProjectFileRuleReq req) {
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(req.getProjectInfoId());

        ProjCustomInfo projCustomInfo = customInfoService.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        GetProjectFileRuleParam getProjectFileRuleParam = GetProjectFileRuleParam.builder()
                .hisFlag(projProjectInfo.getHisFlag())
                .upgradationType(projProjectInfo.getUpgradationType())
                .telesalesFlag(projCustomInfo.getTelesalesFlag())
                .milestoneNodeFlag(1)
                .milestoneNodeCode(req.getNodeCode())
                .projectInfoId(req.getProjectInfoId())
                .build();
        if (Integer.valueOf(1).equals(projProjectInfo.getProjectType())) {
            getProjectFileRuleParam.setMonomerFlag(1);
        }
        if (Integer.valueOf(2).equals(projProjectInfo.getProjectType())) {
            getProjectFileRuleParam.setRegionFlag(1);
        }
        FileUploadRuleVO fileUploadRuleVO = new FileUploadRuleVO("", new ArrayList<>());
        List<ProjectFileUploadRuleVO> projectFileRule = ruleProjectRuleConfigService.getProjectFileRule(getProjectFileRuleParam);
        if (CollectionUtils.isEmpty(projectFileRule)) {
            return new FileUploadRuleVO("", new ArrayList<>());
        }
        AtomicReference<String> text = new AtomicReference<>("");
        projectFileRule.forEach(item -> {
            if (Integer.valueOf(1).equals(item.getRequiredFlag()) && CollectionUtils.isEmpty(item.getFileList())) {
                text.set(text + "需上传" + item.getProjectRuleContent() + "，当前尚未上传。");
            }
        });

        fileUploadRuleVO.setText(text.get());
        fileUploadRuleVO.setRuleList(projectFileRule);
        return fileUploadRuleVO;
    }

    @Override
    @Transactional
    public Result<ProjProjectFileExtend> uploadFileWithRule(UploadFileReq2 req, HttpServletRequest request) {
        String redisKey;
        if (StringUtils.isBlank(req.getProjectCommonFileId()) || "null".equals(req.getProjectCommonFileId())) {
            redisKey = req.getSceneCode() + "_" + req.getProjectRuleCode() + "_" + req.getNodeCode() + "_" + req.getClassCode() + "_" + req.getItemCode();
        } else {
            redisKey = req.getProjectCommonFileId();
        }
        Object lock = redisUtil.get(redisKey);
        if (ObjectUtil.isNotEmpty(lock)) {
            return Result.fail("请等待上次文件上传结束之后再进行新的上传任务");
        }
        redisUtil.set(redisKey, true, 3L, TimeUnit.MINUTES);
        try {
            if (StringUtils.isBlank(req.getNodeCode())) {
                log.error("上传文件的项目计划节点编码不可为空");
                return Result.fail("上传文件的项目计划节点编码不可为空");
            }

            String mimeType = null;
            String fileType = null;
            try {
                if (request != null) {
                    // 获取文件Part
                    Part filePart = request.getPart("file");
                    // 直接获取MIME类型
                    mimeType = filePart.getContentType();
                    if (StringUtils.isNotBlank(mimeType)) {
                        if (mimeType.startsWith("image")) {
                            fileType = "image";
                        } else if ("application/pdf".equals(mimeType)) {
                            fileType = "pdf";
                        } else if ("application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(mimeType)) {
                            fileType = "word";
                        } else if ("application/msword".equals(mimeType)) {
                            fileType = "word";
                        }
                    }
                }
            } catch (Exception e) {
                log.error("上传文件获取mimeType，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }

            String middlePath = req.getNodeCode();
            if (StringUtils.isNotEmpty(req.getSceneCode())) {
                middlePath = middlePath + StrUtil.SLASH + req.getSceneCode();
            }
            if (StringUtils.isNotEmpty(req.getProjectRuleCode())) {
                middlePath = middlePath + StrUtil.SLASH + req.getProjectRuleCode();
            }
            // 构建路径
            String path = prePath + req.getProjectInfoId() + StrUtil.SLASH + middlePath + StrUtil.SLASH + req.getFile().getOriginalFilename();
            // 获取文件名（不包括路径）
            String fileName = path.substring(path.lastIndexOf(StrUtil.SLASH) + 1);
            // 获取文件名（不包括扩展名）
            String fileNameWithoutExtension = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
            // 拼接时间戳和文件扩展名
            String newFileName = fileNameWithoutExtension + "_" + System.currentTimeMillis() + "." + (fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "");
            // 构建新的完整路径
            String objectKey = path.substring(0, path.lastIndexOf(StrUtil.SLASH) + 1) + newFileName;

            // 上传文件
            PutObjectResult putObjectResult;
            try {
                putObjectResult = OBSClientUtils.uploadMultipartFile(req.getFile(), objectKey, req.getIsPublic());
            } catch (Exception e) {
                log.error("基于文件上传规则的文件上传，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                throw new CustomException("基于文件上传规则的文件上传，obs发生错误：" + e.getMessage());
            }
            ProjProjectFileExtend projectFile = new ProjProjectFileExtend(req, SnowFlakeUtil.getId(), objectKey, "");
            if (req.getIsPublic()) {
                projectFile.setFileUrl(URLDecoder.decode(putObjectResult.getObjectUrl()));
            } else {
                projectFile.setFileUrl(OBSClientUtils.getTemporaryUrl(objectKey, 3600));
            }
            projectFile.setMimeType(mimeType);
            projectFile.setFileType(fileType);
//
//            // 校验图片
//            if ("image".equals(fileType)) {
//                log.info("基于文件上传规则的文件上传，当前文件类型为图片，即将使用AI工具检测图片内容");
//
//                DictAgentChatReq dictAgentChatReq = new DictAgentChatReq();
//                dictAgentChatReq.setScenarioCode(req.getSceneCode());
//
//                // AI工具检测配置
//                DictAgentChatScenarioConfigResp resp = dictAgentScenarioConfigMapper.selectByParam(dictAgentChatReq);
//                // AI工具检测配置非空时，进行AI检测
//                if (resp != null) {
//                    dictAgentChatReq.setFileUrls(Collections.singletonList(projectFile.getFileUrl()));
//                    Result result = baseQueryService.sendChartMessage(dictAgentChatReq);
//                    if (result != null && !result.isSuccess()) {
//                        log.error("基于文件上传规则的文件上传，使用AI工具检测图片失败，返回结果={}", JSON.toJSONString(result));
//                        return Result.fail("使用AI工具检测图片失败");
//                    } else {
//                        if (result != null && result.getData() != null) {
//                            Map map = (Map) result.getData();
//                            String companyCode = (String) map.get("compliance_status");
//                            if (!"是".equals(companyCode)) {
//                                String resultText = (String) map.get("result_text");
//                                log.error("基于文件上传规则的文件上传，AI工具检测图片不通过，检测结果={}", resultText);
//                            }
//                        }
//                    }
//                }
//            }
            projProjectFileMapper.insert(projectFile);

            Date now = new Date();

            if (StringUtils.isBlank(req.getProjectCommonFileId()) || "null".equals(req.getProjectCommonFileId())) {
                ProjectCommonFileVsRule addParam = new ProjectCommonFileVsRule();
                addParam.setProjectCommonFileId(SnowFlakeUtil.getId());
                addParam.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                addParam.setCreateTime(now);
                addParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                addParam.setUpdateTime(now);

                addParam.setSceneCode(req.getSceneCode());
                addParam.setProjectRuleCode(req.getProjectRuleCode());
                addParam.setNodeCode(req.getNodeCode());
                addParam.setClassCode(req.getClassCode());
                addParam.setItemCode(req.getItemCode());
                addParam.setProjectFileIds(String.valueOf(projectFile.getProjectFileId()));
                addParam.setProjectInfoId(req.getProjectInfoId());
                projectCommonFileVsRuleService.addProjectCommonFile(addParam);
            } else {
                ProjectCommonFileVsRule existData = projectCommonFileVsRuleService.selectByPrimaryKey(Long.valueOf(req.getProjectCommonFileId()));
                String projectFileIds = existData.getProjectFileIds();
                log.info("基于文件上传规则的文件上传，上传前文件ID={}", projectFileIds);
                if (StringUtils.isBlank(projectFileIds)) {
                    projectFileIds = String.valueOf(projectFile.getProjectFileId());
                } else {
                    projectFileIds = projectFileIds + "," + projectFile.getProjectFileId();
                }
                log.info("基于文件上传规则的文件上传，上传后文件ID={}", projectFileIds);
                projectCommonFileVsRuleService.updateProjectFileIds(existData.getProjectCommonFileId(), projectFileIds);
            }
            return Result.success(projectFile);
        } catch (Exception e) {
            log.error("基于文件上传规则的文件上传，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("发生错误：" + e.getMessage());
        } finally {
            redisUtil.del(redisKey);
        }
    }

    @Override
    public Result<Void> deleteFile(DeleteFileReq req) {
        ProjectCommonFileVsRule existData = projectCommonFileVsRuleService.selectByPrimaryKey(req.getProjectCommonFileId());
        if (existData == null || StringUtils.isBlank(existData.getProjectFileIds())) {
            return Result.fail("没有需要删除的文件");
        }

        String projectFileIds = existData.getProjectFileIds();
        log.info("删除前文件ID集合={}", projectFileIds);
        List<Long> fileIdList = Arrays.stream(projectFileIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<Long> deletedFileIdList = fileIdList.stream().filter(item -> !Objects.equals(req.getProjectFileId(), item)).collect(Collectors.toList());
        String updateProjectFileIds = deletedFileIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        log.info("删除后文件ID集合={}", updateProjectFileIds);
        projectCommonFileVsRuleService.updateProjectFileIds(existData.getProjectCommonFileId(), updateProjectFileIds);
        return Result.success();
    }

    @Override
    public Result<Void> updateUseFlag(UpdateUseFlagReq req) {
        String redisKey;
        if (StringUtils.isBlank(req.getProjectCommonFileId()) || "null".equals(req.getProjectCommonFileId())) {
            redisKey = req.getSceneCode() + "_" + req.getProjectRuleCode() + "_" + req.getNodeCode() + "_" + req.getClassCode() + "_" + req.getItemCode();
        } else {
            redisKey = req.getProjectCommonFileId();
        }
        Object lock = redisUtil.get(redisKey);
        if (ObjectUtil.isNotEmpty(lock)) {
            return Result.fail("请等待上次操作结束之后再进行新的操作");
        }
        redisUtil.set(redisKey, true, 3L, TimeUnit.MINUTES);

        try {
            if (StringUtils.isBlank(req.getNodeCode())) {
                log.error("更新是否使用标记的项目计划节点编码不可为空");
                return Result.fail("更新是否使用标记的项目计划节点编码不可为空");
            }

            Date now = new Date();
            if (StringUtils.isBlank(req.getProjectCommonFileId()) || "null".equals(req.getProjectCommonFileId())) {
                ProjectCommonFileVsRule addParam = new ProjectCommonFileVsRule();
                addParam.setProjectCommonFileId(SnowFlakeUtil.getId());
                addParam.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                addParam.setCreateTime(now);
                addParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                addParam.setUpdateTime(now);
                addParam.setSceneCode(req.getSceneCode());
                addParam.setProjectRuleCode(req.getProjectRuleCode());
                addParam.setNodeCode(req.getNodeCode());
                addParam.setClassCode(req.getClassCode());
                addParam.setItemCode(req.getItemCode());
                addParam.setUseFlag(req.getUseFlag());
                addParam.setProjectInfoId(req.getProjectInfoId());
                projectCommonFileVsRuleService.addProjectCommonFile(addParam);
            } else {
                projectCommonFileVsRuleService.updateUseFlag(Long.valueOf(req.getProjectCommonFileId()), req.getUseFlag());
            }
            return Result.success();
        } catch (NumberFormatException e) {
            log.error("更新使用状态，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("发生错误：" + e.getMessage());
        } finally {
            redisUtil.del(redisKey);
        }
    }

    @Override
    public Result<Void> saveProjectCommonFile(GetProjectFileRuleReq req) {
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(req.getProjectInfoId());

        ProjCustomInfo projCustomInfo = customInfoService.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        GetProjectFileRuleParam getProjectFileRuleParam = GetProjectFileRuleParam.builder()
                .hisFlag(projProjectInfo.getHisFlag())
                .upgradationType(projProjectInfo.getUpgradationType())
                .telesalesFlag(projCustomInfo.getTelesalesFlag())
                .milestoneNodeFlag(1)
                .milestoneNodeCode(req.getNodeCode())
                .projectInfoId(req.getProjectInfoId())
                .build();
        if (Integer.valueOf(1).equals(projProjectInfo.getProjectType())) {
            getProjectFileRuleParam.setMonomerFlag(1);
        }
        if (Integer.valueOf(2).equals(projProjectInfo.getProjectType())) {
            getProjectFileRuleParam.setRegionFlag(1);
        }
        List<ProjectFileUploadRuleVO> projectFileRule = ruleProjectRuleConfigService.getProjectFileRule(getProjectFileRuleParam);
        if (CollectionUtils.isEmpty(projectFileRule)) {
            return Result.success();
        }

        for (ProjectFileUploadRuleVO item : projectFileRule) {
            // 必传文件未上传时，检测失败
            if (Integer.valueOf(1).equals(item.getRequiredType()) && CollectionUtils.isEmpty(item.getFileList())) {
                projectCommonFileVsRuleService.updateCheckInfoById(item.getProjectCommonFileId(), 3, "需上传" + item.getProjectRuleContent() + "。");
                continue;
            }

            // 必传文件未上传且未勾选不使用时，检测失败
            if (Integer.valueOf(2).equals(item.getRequiredType()) && CollectionUtils.isEmpty(item.getFileList()) && !Boolean.FALSE.equals(item.getUseFlag())) {
                projectCommonFileVsRuleService.updateCheckInfoById(item.getProjectCommonFileId(), 3, "需上传" + item.getProjectRuleContent() + "或勾选不使用。");
                continue;
            }

            DictAgentChatReq dictAgentChatReq = new DictAgentChatReq();
            dictAgentChatReq.setScenarioCode(item.getSceneCode());

            // AI工具检测配置
            DictAgentChatScenarioConfigResp resp = dictAgentScenarioConfigMapper.selectByParam(dictAgentChatReq);
            // 没有AI工具检测配置时，默认检测通过
            if (resp == null) {
                projectCommonFileVsRuleService.updateCheckInfoById(item.getProjectCommonFileId(), 2, "检测通过");
                continue;
            }
            // 没有需要检测的图片。默认检测通过
            if (CollectionUtils.isEmpty(item.getFileList())) {
                projectCommonFileVsRuleService.updateCheckInfoById(item.getProjectCommonFileId(), 2, "没有需要检测的图片，检测通过");
                continue;
            }

            // 需要检测的图片
            List<String> imageList = item.getFileList().stream().map(ProjProjectFileVO::getFilePath).filter(StringUtils::isNotBlank).collect(Collectors.toList());

            // 因为AI检测工具限制，需要检测的图片6个一组
            List<List<String>> groupedImageList = ListUtils.splitImageList(imageList, 6);

            // 每组图片检测结果
            List<CheckResult> checkResultList = new ArrayList<>();

            // 检测每组图片
            for (List<String> image : groupedImageList) {
                DictAgentChatReq agentChatReq = new DictAgentChatReq();
                agentChatReq.setScenarioCode(item.getSceneCode());
                agentChatReq.setFileUrls(image);
                Result result = null;
                try {
                    result = baseQueryService.sendChartMessage(agentChatReq);
                } catch (Exception e) {
                    log.error("调用图片智能体进行图片检测，发生异常，参数={}，errMsg={}，stackInfo=", JSON.toJSONString(agentChatReq), e.getMessage(), e);
                    CheckResult checkResult = new CheckResult();
                    checkResult.setCheckCode(3);
                    checkResult.setCheckResult("错误：" + e.getMessage());
                    checkResultList.add(checkResult);
                }
                if (result != null) {
                    if (!result.isSuccess()) {
                        CheckResult checkResult = new CheckResult();
                        checkResult.setCheckCode(3);
                        checkResult.setCheckResult("错误：图片检测失败");
                        checkResultList.add(checkResult);
                    } else {
                        if (result.getData() == null) {
                            CheckResult checkResult = new CheckResult();
                            checkResult.setCheckCode(3);
                            checkResult.setCheckResult("错误：图片检测结果为null");
                            checkResultList.add(checkResult);
                        } else {
                            TypeReference<Map<String, Object>> type = new TypeReference<Map<String, Object>>() {
                            };
                            Map<String, Object> map = JSON.parseObject(JSON.toJSONString(result.getData()), type);
                            // 检测状态：是-符合条件；否-不符合条件
                            String companyCode = (String) map.get("compliance_status");
                            // 检测内容文本
                            String resultText = (String) map.get("result_text");
                            if ("是".equals(companyCode)) {
                                CheckResult checkResult = new CheckResult();
                                checkResult.setCheckCode(1);
                                checkResult.setCheckResult(resultText);
                                checkResultList.add(checkResult);
                            }
                            if (!"是".equals(companyCode)) {
                                CheckResult checkResult = new CheckResult();
                                checkResult.setCheckCode(2);
                                checkResult.setCheckResult(resultText);
                                checkResultList.add(checkResult);
                            }
                        }
                    }
                }
            }
            if (checkResultList.stream().anyMatch(checkResult -> checkResult.getCheckCode() == 1)) {
                projectCommonFileVsRuleService.updateCheckInfoById(item.getProjectCommonFileId(), 2, "检测通过");
            } else {
                String checkResultText = checkResultList.stream().filter(checkResult -> checkResult.getCheckCode() != 1).map(CheckResult::getCheckResult).collect(Collectors.joining(","));
                projectCommonFileVsRuleService.updateCheckInfoById(item.getProjectCommonFileId(), 3, "图片检测不通过。检测结果：" + checkResultText);
            }
        }
        return Result.success();
    }

//
//    /**
//     * 如果需要检测的图片过多，按照6个一组进行分组检测
//     *
//     * @param originalImageList 需要分组的图片
//     * @param groupSize         每组的图片数量
//     * @return 分组结果
//     */
//    public static List<List<String>> splitImageList(List<String> originalImageList, int groupSize) {
//        List<List<String>> groupedList = new ArrayList<>();
//
//        // 遍历原始列表，将其拆分成子列表
//        for (int i = 0; i < originalImageList.size(); i += groupSize) {
//            int end = Math.min(i + groupSize, originalImageList.size()); // 确保不会超出范围
//            groupedList.add(originalImageList.subList(i, end));
//        }
//        return groupedList;
//    }
}
