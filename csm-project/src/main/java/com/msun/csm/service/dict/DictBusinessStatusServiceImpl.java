package com.msun.csm.service.dict;

import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.dict.DictBusinessStatus;
import com.msun.csm.dao.mapper.dict.DictBusinessStatusMapper;

@Slf4j
@Service
public class DictBusinessStatusServiceImpl implements DictBusinessStatusService {

    @Resource
    private DictBusinessStatusMapper dictBusinessStatusMapper;

    @Override
    public List<DictBusinessStatus> getBusinessStatusByBusinessCode(String businessCode) {
        return dictBusinessStatusMapper.getBusinessStatusByBusinessCode(businessCode);
    }

    @Override
    public String getSurveyPlanStatusDescriptionByStatusId(Integer statusId) {
        DictBusinessStatus dictBusinessStatus = dictBusinessStatusMapper.getBusinessStatusByBusinessCodeAndStatusId("survey_product", statusId);
        if (dictBusinessStatus == null) {
            throw new IllegalArgumentException("业务状态字典缺少对应的状态，business_code=survey_product，status_id=" + statusId);
        }
        return dictBusinessStatus.getStatusDescription();
    }

    @Override
    public List<DictBusinessStatus> getBusinessStatusByBusinessCodeAndStatusClass(String businessCode, Integer statusClass) {
        return dictBusinessStatusMapper.getBusinessStatusByBusinessCodeAndStatusClass(businessCode, statusClass);
    }

    @Override
    public List<Integer> getStatusIdByBusinessCodeAndStatusClass(String businessCode, Integer statusClass) {
        List<DictBusinessStatus> businessStatuses = this.getBusinessStatusByBusinessCodeAndStatusClass(businessCode, statusClass);
        if (CollectionUtils.isEmpty(businessStatuses)) {
            return Collections.emptyList();
        }
        return businessStatuses.stream().map(DictBusinessStatus::getStatusId).collect(Collectors.toList());
    }
}
