package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.error.BaseErrorCode;
import com.msun.core.commons.utils.AesUtil;
import com.msun.core.component.implementation.api.imsp.ImspApi;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertDTO;
import com.msun.core.component.implementation.api.imsp.dto.PasswordDTO;
import com.msun.core.component.implementation.api.port.DataPreparationApi;
import com.msun.core.component.implementation.api.port.HospitalApi;
import com.msun.core.component.implementation.api.port.dto.PortTestDTO;
import com.msun.core.component.implementation.api.port.dto.SelectAndInsertDTO;
import com.msun.core.component.implementation.api.port.dto.SqlCheckApiDTO;
import com.msun.core.component.implementation.api.port.dto.SqlSelectLoginDTO;
import com.msun.core.component.implementation.api.port.dto.SqlUpdateDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.common.enums.config.SerialNumType;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.dict.DictExamDept;
import com.msun.csm.dao.entity.dict.DictExamImportStep;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjExamHospitalStepDetail;
import com.msun.csm.dao.entity.proj.ProjExamVsDept;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.SimulationHospital;
import com.msun.csm.dao.entity.proj.UrlParam;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.dict.DictExamDeptMapper;
import com.msun.csm.dao.mapper.dict.DictExamImportStepMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjExamHospitalStepDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjExamMapper;
import com.msun.csm.dao.mapper.proj.ProjExamVsDeptMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.SimulationHospitalMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.feign.client.oldimsp.OldImspFeignClient;
import com.msun.csm.model.dto.AddProjectMemberDTO;
import com.msun.csm.model.dto.ExamImportStepDTO;
import com.msun.csm.model.dto.FindExamImportStepDTO;
import com.msun.csm.model.dto.ProjExamVsDeptDTO;
import com.msun.csm.model.dto.ProjExamVsDeptsDTO;
import com.msun.csm.model.dto.ProjHospitalInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoExamDTO;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.applyorder.ApplyOrderHospitalDTO;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.vo.ExamApplyHospitalVO;
import com.msun.csm.model.vo.ExamHospitalVO;
import com.msun.csm.model.vo.ExamOnlineDocVO;
import com.msun.csm.model.vo.ProjExamHosVsDeptVO;
import com.msun.csm.model.vo.ProjExamHospitalStepVO;
import com.msun.csm.model.vo.ProjExamVsDeptVO;
import com.msun.csm.model.vo.ProjProjectMemberVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.schedule.TaskSchedule;
import com.msun.csm.service.config.ConfigSerialNumberService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalYunweiService;
import com.msun.csm.util.CsmSignUtil;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.DomainUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;

@Service
@Slf4j
public class ProjExamServiceImpl implements ProjExamService {

    @Resource
    private ProjExamMapper projExamMapper;

    @Resource
    private ProjHospitalInfoService projHospitalInfoService;

    @Resource
    private SimulationHospitalMapper simulationHospitalMapper;

    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;

    @Resource
    private ProjProjectMemberService projProjectMemberService;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ConfigSerialNumberService configSerialNumberService;

    @Resource
    private ProjApplyOrderHospitalYunweiService applyOrderHospitalYunweiService;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private OldImspFeignClient oldImspFeignClient;

    @Value("${project.exam_custom_name}")
    private String examCustomName;

    @Value("${project.exam_domain}")
    private String examDomain;

    @Value("#{'${project.feign.oldImsp.copyDataBusiness}'.split(',')}")
    private List<String> copyDataBusiness;

    @Resource(name = "defaultThreadPool")
    private ThreadPoolTaskExecutor defaultThreadPool;

    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private DictExamImportStepMapper dictExamImportStepMapper;

    @Resource
    private ProjExamHospitalStepDetailMapper projExamHospitalStepDetailMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private DataPreparationApi dataPreparationApi;

    @Resource
    private DictExamDeptMapper dictExamDeptMapper;

    @Resource
    private ProjExamVsDeptMapper projExamVsDeptMapper;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    @Lazy
    private ImspApi imspApi;

    @Resource
    private HospitalApi hospitalApi;

    @Resource
    private TaskSchedule taskScheduler;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询客户下是否有未开通培训环境的医院
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ExamHospitalVO> findApplyExamEnvironment(ProjHospitalInfoExamDTO dto) {
        ExamHospitalVO examHospitalVO = new ExamHospitalVO();
        List<ProjHospitalInfo> notApplyHos = projExamMapper.findNotApplyExamEnvironment(dto);
        List<ProjHospitalInfo> applyHos = projExamMapper.findApplyExamEnvironment(dto);
        if (CollectionUtil.isNotEmpty(applyHos)) {
            //查询培训环境的customInfoId和projectInfoId--培训环境客户名称为固定值：众阳培训平台
            getExamCustomAndProjectInfo(dto);
            List<ExamApplyHospitalVO> applyHospitalInfoList = Lists.newArrayList();
            for (ProjHospitalInfo hos : applyHos) {
                ExamApplyHospitalVO examApplyHospitalVO = new ExamApplyHospitalVO();
                BeanUtil.copyProperties(hos, examApplyHospitalVO);
                //封装导基础数据地址
                UrlParam params = projMilestoneInfoMapper.getUrlParams(dto.getExamProjectInfoId());
                ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>()
                        .eq("project_info_id", dto.getProjectInfoId())
                        .eq("milestone_node_code", "custom_exam")
                        .last(" limit 1"));
                StringBuilder flowWorkUrl = new StringBuilder("/projectManagementCenter/fastOnlineMenu?projectId=");
                flowWorkUrl.append(params.getProjectId()).append("&customerId=").append(params.getCustomerId())
                        .append("&detailId=").append(projMilestoneInfo.getMilestoneInfoId());
                examApplyHospitalVO.setFlowWorkUrl(flowWorkUrl.toString());
                //赋值导数据状态
                FindExamImportStepDTO findExamImportStepDTO = new FindExamImportStepDTO();
                findExamImportStepDTO.setCustomInfoId(dto.getCustomInfoId());
                findExamImportStepDTO.setExamHospitalInfoId(hos.getHospitalInfoId());
                List<ProjExamHospitalStepVO> notExcuteSteps = dictExamImportStepMapper.findNotExcuteSteps(findExamImportStepDTO);
                if (CollectionUtil.isNotEmpty(notExcuteSteps)) {
                    examApplyHospitalVO.setDataImportStatus(0);
                } else {
                    examApplyHospitalVO.setDataImportStatus(1);
                }
                applyHospitalInfoList.add(examApplyHospitalVO);
            }
            examHospitalVO.setApplyHospitalInfoList(applyHospitalInfoList);
        } else {
            examHospitalVO.setApplyHospitalInfoList(new ArrayList<>());
        }
        if (CollectionUtil.isNotEmpty(notApplyHos)) {
            examHospitalVO.setNotApplyFlag(true);
        } else {
            examHospitalVO.setNotApplyFlag(false);
        }
        return Result.success(examHospitalVO);
    }

    /**
     * 一键申请培训环境
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result applyExamEnvironment(ProjHospitalInfoExamDTO dto) {
        //查询原项目下未申请培训环境的医院
        List<ProjHospitalInfo> notApplyHospitals = projExamMapper.findNotApplyExamEnvironment(dto);
        if (CollectionUtil.isEmpty(notApplyHospitals)) {
            return Result.fail("当前项目下所有医院都已申请学习环境！");
        }

        //查询培训环境的customInfoId和projectInfoId--培训环境客户名称为固定值：众阳培训平台
        getExamCustomAndProjectInfo(dto);

        //培训环境下保存原项目医院信息
        saveHospitalInfo(notApplyHospitals, dto);

        //原项目下的团队成员添加到培训环境
        saveProjectMemebers(dto);

        //发送开通医院申请
        batchOpenHospital(dto);

        return Result.success();
    }

    /**
     * 查询培训环境的customInfoId和projectInfoId
     *
     * @param dto
     */
    private void getExamCustomAndProjectInfo(ProjHospitalInfoExamDTO dto) {
        try {
            ProjCustomInfo customInfo = projCustomInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>()
                    .eq("custom_name", examCustomName)
                    .last(" limit 1"));
            Long examCustomInfoId = customInfo.getCustomInfoId();
            dto.setExamCustomInfoId(examCustomInfoId);
            dto.setExamCustomInfoName(customInfo.getCustomName());
            ProjProjectInfo projectInfo = projProjectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>()
                    .eq("custom_info_id", examCustomInfoId)
                    .last(" limit 1"));
            dto.setExamProjectInfoId(projectInfo.getProjectInfoId());
            dto.setExamProjectInfoName(projectInfo.getProjectName());
            dto.setExamOrderInfoId(projectInfo.getOrderInfoId());
            dto.setExamProjectType(projectInfo.getProjectType());
        } catch (Exception e) {
            throw new CustomException("查询学习环境项目信息发生错误：" + e.getMessage());
        }
    }

    /**
     * 培训环境下保存医院信息
     *
     * @param notApplyHospitals
     * @param dto
     */
    private void saveHospitalInfo(List<ProjHospitalInfo> notApplyHospitals, ProjHospitalInfoExamDTO dto) {
        try {
            List<ProjHospitalInfoDTO> insertHospitals = Lists.newArrayList();
            for (ProjHospitalInfo hospitalInfo : notApplyHospitals) {
                ProjHospitalInfoDTO hospitalInfoDTO = new ProjHospitalInfoDTO();
                BeanUtil.copyProperties(hospitalInfo, hospitalInfoDTO);
                hospitalInfoDTO.setCustomInfoId(dto.getExamCustomInfoId());
                hospitalInfoDTO.setProvinceId(Long.valueOf("370000"));
                hospitalInfoDTO.setCityId(Long.valueOf("371700"));
                hospitalInfoDTO.setTownId(Long.valueOf("371721"));
                hospitalInfoDTO.setDictHospitalLevelId(Long.valueOf("1"));
                hospitalInfoDTO.setDictHospitalOrgId(Long.valueOf("1"));
                hospitalInfoDTO.setProjectType(dto.getExamProjectType());
                hospitalInfoDTO.setOrganizationCode("test" + hospitalInfo.getHospitalInfoId());
                hospitalInfoDTO.setHospitalName(hospitalInfo.getHospitalName() + "学习环境");
                hospitalInfoDTO.setHospitalInfoId(null);
                insertHospitals.add(hospitalInfoDTO);
            }
            Result result = projHospitalInfoService.batchInsert(insertHospitals);
            if (!result.isSuccess()) {
                throw new CustomException("学习环境下保存医院信息发生错误：" + result.getMsg());
            }
            //保存原项目医院与培训环境医院对照
            for (ProjHospitalInfoDTO hospitalInfoDTO : insertHospitals) {
                /*ProjHospitalVsExam projHospitalVsExam = new ProjHospitalVsExam();
                projHospitalVsExam.setProjHospitalVsExamId(SnowFlakeUtil.getId());
                projHospitalVsExam.setHospitalInfoId(Long.valueOf(hospitalInfoDTO.getOrganizationCode().replace("test", "")));
                projHospitalVsExam.setExamHospitalInfoId(hospitalInfoDTO.getHospitalInfoId());
                projHospitalVsExam.setCustomInfoId(dto.getCustomInfoId());
                projHospitalVsExamMapper.insert(projHospitalVsExam);*/
                SimulationHospital simulationHospital = new SimulationHospital();
                simulationHospital.setId(SnowFlakeUtil.getId());
                simulationHospital.setCustomInfoId(dto.getCustomInfoId());
                simulationHospital.setProjectInfoId(dto.getProjectInfoId());
                simulationHospital.setTestDomain(examDomain);
                simulationHospital.setHospitalInfoId(Long.valueOf(hospitalInfoDTO.getOrganizationCode().replace("test", "")));
                simulationHospital.setExamHospitalInfoId(hospitalInfoDTO.getHospitalInfoId());
                simulationHospital.setHospitalName(hospitalInfoDTO.getHospitalName());
                simulationHospital.setCloudHospitalId("-1");
                simulationHospital.setIsDelete(Short.valueOf("0"));
                simulationHospital.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                simulationHospital.setCreateTime(new Date());
                simulationHospital.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                simulationHospital.setUpdateTime(new Date());
                simulationHospitalMapper.insert(simulationHospital);
            }
        } catch (Exception e) {
            throw new CustomException("学习环境下保存医院信息发生错误：" + e.getMessage());
        }
    }

    /**
     * 原项目下的团队成员添加到培训环境
     *
     * @param dto
     */
    private void saveProjectMemebers(ProjHospitalInfoExamDTO dto) {
        try {
            //查询原项目的团队成员
            ProjProjectMemberDTO memberDTO = new ProjProjectMemberDTO();
            memberDTO.setProjectInfoId(dto.getProjectInfoId());
            List<ProjProjectMemberVO> projProjectMemberS = projProjectMemberMapper.selectMemberVO(memberDTO);
            //保存项目成员到培训环境下
            if (CollectionUtil.isNotEmpty(projProjectMemberS)) {
                List<SysUserDTO> userList = Lists.newArrayList();
                for (ProjProjectMemberVO projProjectMemberVO : projProjectMemberS) {
                    if (ObjectUtil.isEmpty(projProjectMemberVO.getAccount())) {
                        continue;
                    }
                    Long aLong = projProjectMemberMapper.selectCount(new QueryWrapper<ProjProjectMember>()
                            .eq("project_info_id", dto.getExamProjectInfoId())
                            .eq("project_member_id", projProjectMemberVO.getProjectMemberId())
                    );
                    if (aLong > 0) {
                        continue;
                    }
                    SysUserDTO sysUserDTO = new SysUserDTO();
                    sysUserDTO.setSysUserId(projProjectMemberVO.getProjectMemberId());
                    sysUserDTO.setDeptId(projProjectMemberVO.getProjectTeamId());
                    sysUserDTO.setDeptName(projProjectMemberVO.getProjectTeamName());
                    sysUserDTO.setUserName(projProjectMemberVO.getProjectMemberName());
                    sysUserDTO.setPhone(projProjectMemberVO.getPhone());
                    sysUserDTO.setUserYunyingId(projProjectMemberVO.getUserYunyingId());
                    userList.add(sysUserDTO);
                }
                if (CollectionUtil.isNotEmpty(userList)) {
                    AddProjectMemberDTO addProjectMemberDTO = new AddProjectMemberDTO();
                    addProjectMemberDTO.setProjectInfoId(dto.getExamProjectInfoId());
                    addProjectMemberDTO.setDtoList(userList);
                    projProjectMemberService.insertMember(addProjectMemberDTO);
                }

            }
        } catch (Exception e) {
            throw new CustomException("项目下的团队成员添加到培训环境发生错误：" + e.getMessage());
        }
    }

    /**
     * 申请开通医院信息
     *
     * @param dto
     */
    private void batchOpenHospital(ProjHospitalInfoExamDTO dto) {
        try {
            //生成申请工单编号
            String applyOrderNum = configSerialNumberService.createNum(SerialNumType.APPLY_ORDER);
            List<ProjHospitalInfoRelative> hospitalInfoList = projExamMapper.findApplyExamHospitals(dto);
            ApplyOrderHospitalDTO applyOrderHospitalDTO = applyOrderHospitalYunweiService.applyExamHospital(dto, applyOrderNum, hospitalInfoList);
            // 更新医院信息（接收返回值）
            List<ProjHospitalInfo> hospitalInfos = getHospitalForUpdate(applyOrderHospitalDTO.getDepPlatformResult(),
                    applyOrderHospitalDTO.getHospitalInfoOpen());
            if (CollUtil.isEmpty(hospitalInfos)) {
                throw new CustomException("未查询到需要更新的医院.");
            }
            String host = DomainUtil.getDomainInfo(hospitalInfos.get(0).getCloudDomain()).getHost();
            for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
                OldCustomerInfo oldCustomerInfo = new OldCustomerInfo();
                oldCustomerInfo.setOrgId(hospitalInfo.getOrgId());
                oldCustomerInfo.setHospitalId(hospitalInfo.getCloudHospitalId());
                oldCustomerInfo.setProductNetwork(hospitalInfo.getCloudDomain());
                oldCustomerInfo.setPreProductNetwork(hospitalInfo.getCloudDomain());
                oldCustomerInfo.setHost(host);
                oldCustomerInfo.setPreHost(host);
                oldCustomerInfo.setEnvId(hospitalInfo.getEnvId());
                oldCustomerInfo.setEnvName(hospitalInfo.getEnvName());
                applyOrderHospitalService.updateHospitalRelativeInfo(oldCustomerInfo, hospitalInfo,
                        CollUtil.newArrayList(hospitalInfo.getHospitalInfoId()));
                //更新医院对照表的云健康医院ID
                SimulationHospital simulationHospital = simulationHospitalMapper.selectOne(new UpdateWrapper<SimulationHospital>()
                        .eq("exam_hospital_info_id", hospitalInfo.getHospitalInfoId())
                        .eq("is_delete", 0)
                        .last(" limit 1"));
                if (ObjectUtil.isNotEmpty(simulationHospital)) {
                    simulationHospital.setCloudHospitalId(hospitalInfo.getCloudHospitalId().toString());
                    simulationHospitalMapper.updateById(simulationHospital);
                }
            }
        } catch (Exception e) {
            throw new CustomException("申请开通医院发生错误：" + e.getMessage());
        }
    }

    /**
     * 更新医院信息
     *
     * @param platformResult
     * @param hasOpenHospitalInfo
     * @return
     */
    private List<ProjHospitalInfo> getHospitalForUpdate(ResponseEntity<JSONObject> platformResult,
                                                        ProjHospitalInfoRelative hasOpenHospitalInfo) {
        Map<String, Object> res = Convert.toMap(String.class, Object.class, platformResult.getBody());
        List<LinkedHashMap<String, Object>> resCustomerInfoList = new ArrayList<>();
        List<LinkedHashMap<String, Object>> deployResourceApplyCountryVOList =
                (List<LinkedHashMap<String, Object>>) res.get("deployResourceApplyCountryVOList");
        deployResourceApplyCountryVOList.forEach(item -> {
            List<LinkedHashMap<String, Object>> resHospitalInfoList = (List<LinkedHashMap<String, Object>>) item.get(
                    "deployResourceApplyHospitalVOList");
            resHospitalInfoList.forEach(newItem -> resCustomerInfoList.add(newItem));
        });
        List<ProjHospitalInfo> hospitalInfos = new ArrayList<>();
        // 查询申请单
        if (CollectionUtil.isNotEmpty(resCustomerInfoList)) {
            for (LinkedHashMap<String, Object> stringObjectLinkedHashMap : resCustomerInfoList) {
                ProjHospitalInfo projHospitalInfo = new ProjHospitalInfo();
                projHospitalInfo.setHospitalInfoId(Long.parseLong(stringObjectLinkedHashMap.get("customerInfoId").toString()));
                projHospitalInfo.setHospitalOpenStatus(HospitalOpenStatusEnum.OPENING.getCode());
                projHospitalInfo.setEnvId(hasOpenHospitalInfo.getEnvId());
                projHospitalInfo.setEnvName(hasOpenHospitalInfo.getEnvName());
                projHospitalInfo.setCloudDomain(hasOpenHospitalInfo.getCloudDomain());
                projHospitalInfo.setCloudHospitalId(Long.parseLong(StrUtil.toString(stringObjectLinkedHashMap.get(
                        "hospitalId"))));
                projHospitalInfo.setOrgId(Long.parseLong(StrUtil.toString(stringObjectLinkedHashMap.get("orgId"))));
                hospitalInfos.add(projHospitalInfo);
            }
        }
        return hospitalInfos;
    }

    /**
     * 医院开通时执行复制数据操作
     *
     * @param customInfoId
     * @param hospitals
     */
    @Override
    public void handleExamEnviorment(Long customInfoId, List<ProjHospitalInfo> hospitals) {
        ProjCustomInfo customInfo = projCustomInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>()
                .eq("custom_name", examCustomName)
                .last(" limit 1"));
        if (ObjectUtil.isNotEmpty(customInfo) && customInfo.getCustomInfoId().equals(customInfoId)) {
            //查询需要复制的业务信息
            List<String> rowIds = projExamMapper.getProductBusiness(copyDataBusiness);
            //查询培训环境主院信息
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                    .eq("custom_info_id", customInfoId)
                    .eq("health_bureau_flag", 1)
                    .last(" limit 1"));
            Long resId = projExamMapper.getOldMainHospital(hospitalInfo.getHospitalInfoId());
            List<Long> hospitalIds = hospitals.stream().map(ProjHospitalInfo::getHospitalInfoId).collect(Collectors.toList());
            List<Long> tarHosIds = projExamMapper.findOldHospitals(hospitalIds);
            CompletableFuture[] completableFutures = tarHosIds.stream().map(tarId ->
                    CompletableFuture.runAsync(() -> {
                        Map<String, Object> parameter = Maps.newHashMap();
                        parameter.put("resId", resId);
                        parameter.put("tarIds", tarId);
                        parameter.put("isArea", 1);
                        parameter.put("rowIds", rowIds.stream().collect(Collectors.joining(",")));
                        String auth = CsmSignUtil.getHeader();
                        Object object = oldImspFeignClient.copyData(parameter, auth);
                        log.info(JSONObject.toJSONString(object));
                    }, defaultThreadPool)
            ).toArray(CompletableFuture[]::new);
            try {
                CompletableFuture.allOf(completableFutures).get(10, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("多线程复制数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
    }

    /**
     * 获取在线文档路径
     *
     * @return
     */
    @Override
    public Result<ExamOnlineDocVO> getOnlineDoc() {
        ExamOnlineDocVO examOnlineDocVO = new ExamOnlineDocVO();
        String examGuideUrl = sysFileMapper.selectOne(new QueryWrapper<SysFile>()
                .eq("file_code", "exam_guide_url")).getFilePath();
        examOnlineDocVO.setExamGuideUrl(examGuideUrl);
        String wholeSchemeUrl = sysFileMapper.selectOne(new QueryWrapper<SysFile>()
                .eq("file_code", "whole_scheme_url")).getFilePath();
        examOnlineDocVO.setWholeSchemeUrl(wholeSchemeUrl);
        return Result.success(examOnlineDocVO);
    }

    /**
     * 查询医院导数据状态
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ProjExamHospitalStepVO>> findExamHospitalStepList(FindExamImportStepDTO dto) {
        return Result.success(dictExamImportStepMapper.findExamHospitalStepList(dto));
    }

    /**
     * 查询科室对照
     *
     * @param examHospitalId
     * @return
     */
    @Override
    public Result<ProjExamVsDeptVO> findExamVsDept(Long examHospitalId) {
        ProjExamVsDeptVO projExamVsDeptVO = new ProjExamVsDeptVO();
        List<DictExamDept> examDepts = dictExamDeptMapper.selectList(new QueryWrapper<DictExamDept>());
        if (CollectionUtil.isEmpty(examDepts)) {
            return Result.fail("未查询到科室信息！");
        }
        //查询需要对照科室的医院信息
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(examHospitalId);
        if (ObjectUtil.isEmpty(projHospitalInfo) || projHospitalInfo.getHospitalOpenStatus() < 21) {
            return Result.fail("当前医院在学习环境暂未开通！");
        }
        List<ProjExamHosVsDeptVO> projExamVsDeptVOS = Lists.newArrayList();
        for (DictExamDept dictExamDept : examDepts) {
            ProjExamHosVsDeptVO projExamHosVsDeptVO = new ProjExamHosVsDeptVO();
            projExamHosVsDeptVO.setOldDeptId(dictExamDept.getExamDeptId());
            projExamHosVsDeptVO.setOldDeptName(dictExamDept.getExamDeptName());
            projExamVsDeptVOS.add(projExamHosVsDeptVO);
        }
        List<ProjExamVsDept> projExamVsDepts = projExamVsDeptMapper.selectList(new QueryWrapper<ProjExamVsDept>()
                .eq("hospital_id", examHospitalId));
        if (CollectionUtil.isNotEmpty(projExamVsDepts)) {
            for (ProjExamVsDept projExamVsDept : projExamVsDepts) {
                for (ProjExamHosVsDeptVO examHosVsDeptVO : projExamVsDeptVOS) {
                    if (projExamVsDept.getOldDeptId().equals(examHosVsDeptVO.getOldDeptId())) {
                        examHosVsDeptVO.setNewDeptId(projExamVsDept.getNewDeptId());
                        examHosVsDeptVO.setNewDeptName(projExamVsDept.getNewDeptName());
                        examHosVsDeptVO.setProjExamVsDeptId(projExamVsDept.getProjExamVsDeptId());
                    }
                }
            }
        }
        projExamVsDeptVO.setProjExamHosVsDeptVOList(projExamVsDeptVOS);
        // 调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 组装调用API的参数
        SqlSelectLoginDTO sqlSelectLoginDTO = new SqlSelectLoginDTO();
        sqlSelectLoginDTO.setHospitalInfoId(projHospitalInfo.getCloudHospitalId());
        //查询院内科室列表
        String deptSql = sysConfigMapper.selectConfigByName("EXAM_DEPTSQL").getConfigValue();
        deptSql = deptSql.replace("#{examHospitalId}", projHospitalInfo.getCloudHospitalId().toString());
        sqlSelectLoginDTO.setSqlStr(AesUtil.encrypt(deptSql));
        // 组装调用API的参数
        SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
        sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
        ResponseResult<List<JSONObject>> deptResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
        if (deptResult == null || CollectionUtils.isEmpty(deptResult.getData())) {
            return Result.success(projExamVsDeptVO);
        }
        List<BaseIdNameResp> projExamHosDeptVOList = JSONObject.parseArray(JSONObject.toJSONString(deptResult.getData()), BaseIdNameResp.class);
        projExamVsDeptVO.setProjExamHosDeptVOList(projExamHosDeptVOList);
        return Result.success(projExamVsDeptVO);
    }

    /**
     * 保存科室对照
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveExamVsDept(ProjExamVsDeptDTO dto) {
        List<ProjExamVsDeptsDTO> projExamVsDeptsDTOS = dto.getProjExamHosVsDeptVOList();
        for (ProjExamVsDeptsDTO projExamVsDeptDTO : projExamVsDeptsDTOS) {
            if (ObjectUtil.isNotEmpty(projExamVsDeptDTO.getProjExamVsDeptId())) {
                ProjExamVsDept projExamVsDept = projExamVsDeptMapper.selectById(projExamVsDeptDTO.getProjExamVsDeptId());
                projExamVsDept.setNewDeptId(projExamVsDeptDTO.getNewDeptId());
                projExamVsDept.setNewDeptName(projExamVsDeptDTO.getNewDeptName());
                projExamVsDeptMapper.updateById(projExamVsDept);
            } else {
                List<ProjExamVsDept> projExamVsDepts = projExamVsDeptMapper.selectList(new QueryWrapper<ProjExamVsDept>()
                        .eq("hospital_id", dto.getExamHospitalInfoId())
                        .eq("old_dept_id", projExamVsDeptDTO.getOldDeptId())
                        .eq("new_dept_id", projExamVsDeptDTO.getNewDeptId()));
                if (CollectionUtil.isNotEmpty(projExamVsDepts)) {
                    throw new CustomException("《" + projExamVsDeptDTO.getOldDeptName() + "》的对照科室已存在，请勿重复添加！");
                }
                ProjExamVsDept projExamVsDept = new ProjExamVsDept();
                projExamVsDept.setProjExamVsDeptId(SnowFlakeUtil.getId());
                projExamVsDept.setOldDeptId(projExamVsDeptDTO.getOldDeptId());
                projExamVsDept.setOldDeptName(projExamVsDeptDTO.getOldDeptName());
                projExamVsDept.setNewDeptId(projExamVsDeptDTO.getNewDeptId());
                projExamVsDept.setNewDeptName(projExamVsDeptDTO.getNewDeptName());
                projExamVsDept.setHospitalId(dto.getExamHospitalInfoId());
                projExamVsDeptMapper.insert(projExamVsDept);
            }
        }
        return Result.success();
    }

    /**
     * 执行SQL脚本
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result doSqltext(ExamImportStepDTO dto) {
        ProjCustomInfo customInfo = new LambdaQueryChainWrapper<>(projCustomInfoMapper)
                .eq(ProjCustomInfo::getCustomName, examCustomName)
                .last("limit 1")
                .one();
        if (ObjectUtil.isEmpty(customInfo)) {
            throw new CustomException("无效客户");
        }
        //查询培训环境主院信息
        ProjHospitalInfo hospitalInfo = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                .eq(ProjHospitalInfo::getCustomInfoId, customInfo.getCustomInfoId())
                .eq(ProjHospitalInfo::getHealthBureauFlag, 1)
                .last("limit 1")
                .one();
        if (ObjectUtil.isEmpty(customInfo)) {
            throw new CustomException("无效医院信息");
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(dto.getExamHospitalInfoId());
        if (ObjectUtil.isEmpty(projHospitalInfo) || projHospitalInfo.getHospitalOpenStatus() < 21) {
            throw new CustomException("当前医院在学习环境暂未开通！");
        }
        List<DictExamImportStep> dictExamImportSteps = dictExamImportStepMapper.selectList(new QueryWrapper<DictExamImportStep>()
                .eq("step_code", dto.getStepCode())
                .orderByAsc("order_no"));
        if (CollectionUtil.isEmpty(dictExamImportSteps)) {
            throw new CustomException("未获取到执行步骤业务脚本信息！");
        }
        //--
        ProjExamHospitalStepDetail hospitalStepDetail = new LambdaQueryChainWrapper<>(projExamHospitalStepDetailMapper)
                .eq(ProjExamHospitalStepDetail::getExamHospitalInfoId, dto.getExamHospitalInfoId())
                .eq(ProjExamHospitalStepDetail::getStepCode, dto.getStepCode())
                .eq(ProjExamHospitalStepDetail::getStepState, 2)
                .last("limit 1")
                .one();
        if (ObjectUtil.isNotEmpty(hospitalStepDetail)) {
            throw new CustomException("当前业务步骤已成功执行，请勿重复执行！");
        }
        Integer stepOrder = dictExamImportSteps.get(0).getStepOrder();
        if (stepOrder == 1) {
            //执行第一步脚本之前需要校验人员、科室等基础数据是否已导入
            Boolean checkResult = checkMsunData(projHospitalInfo);
            if (!checkResult) {
                throw new CustomException("人员、科室和病区数据还未导入，请先完成《打开数据收集页面》工作后再导入基础数据！");
            }
        }
        if (stepOrder > 1) {
            //从第二步开始需要检验之前一步是否执行完成
            DictExamImportStep dictExamImportStep = new LambdaQueryChainWrapper<>(dictExamImportStepMapper)
                    .eq(DictExamImportStep::getStepOrder, stepOrder - 1)
                    .last("limit 1")
                    .one();
            if (ObjectUtil.isEmpty(dictExamImportStep)) {
                throw new CustomException("执行业务步骤有误，未查询到前序业务操作！");
            }
            ProjExamHospitalStepDetail projExamHospitalStepDetail = new LambdaQueryChainWrapper<>(projExamHospitalStepDetailMapper)
                    .eq(ProjExamHospitalStepDetail::getExamHospitalInfoId, dto.getExamHospitalInfoId())
                    .eq(ProjExamHospitalStepDetail::getStepCode, dictExamImportStep.getStepCode())
                    .last("limit 1")
                    .one();
            if (ObjectUtil.isEmpty(projExamHospitalStepDetail)) {
                throw new CustomException("执行业务步骤有误，未查询到前序业务执行状态，请按步骤顺序执行！");
            }
        }

        ResponseResult responseResult = null;
        if (dictExamImportSteps.size() > 1) {
            // 调用API时设置domain信息
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            //当前业务需要执行多步操作
            SelectAndInsertDTO selectAndInsertDTO = new SelectAndInsertDTO();
            selectAndInsertDTO.setHisOrgId(projHospitalInfo.getOrgId());
            selectAndInsertDTO.setOrgId(projHospitalInfo.getOrgId());
            selectAndInsertDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
            for (DictExamImportStep dictExamImportStep : dictExamImportSteps) {
                if (dictExamImportStep.getSqlType() == 2) {
                    selectAndInsertDTO.setSelectBaseName(dictExamImportStep.getStepDatabaseName());
                    String sqlText = dictExamImportStep.getSqlText();
                    sqlText = sqlText.replace("\r\n", " ")
                            .replace("#{hospitalId}", hospitalInfo.getCloudHospitalId().toString())
                            .replace("#{examHospitalId}", projHospitalInfo.getCloudHospitalId().toString());
                    selectAndInsertDTO.setSelectSqlText(AesUtil.encrypt(sqlText));
                } else {
                    selectAndInsertDTO.setInsertBaseName(dictExamImportStep.getStepDatabaseName());
                    selectAndInsertDTO.setInsertSchema(dictExamImportStep.getStepSchema());
                    selectAndInsertDTO.setInsertTableName(dictExamImportStep.getStepTableName());
                }
            }
            try {
                responseResult = dataPreparationApi.executeSelectAndInsert(selectAndInsertDTO);
                log.warn("API接口执行SQL返回值1：{}，参数：{}", responseResult, selectAndInsertDTO);
            } catch (Throwable e) {
                log.error("API接口执行SQL报错了1：{}", selectAndInsertDTO, e);
                throw e;
            }
        } else {
            DictExamImportStep dictExamImportStep = dictExamImportSteps.get(0);
            if (dictExamImportStep.getSqlType() == 4) {
                //批量更新云健康账号的密码
                responseResult = updatePassword(projHospitalInfo);
            } else {
                if (ObjectUtil.isEmpty(dictExamImportStep.getSqlText())) {
                    ProjExamHospitalStepDetail examHospitalStepDetail = new ProjExamHospitalStepDetail();
                    examHospitalStepDetail.setStepVsHospitalId(SnowFlakeUtil.getId());
                    examHospitalStepDetail.setCustomInfoId(dto.getCustomInfoId());
                    examHospitalStepDetail.setExamHospitalInfoId(dto.getExamHospitalInfoId());
                    examHospitalStepDetail.setStepCode(dto.getStepCode());
                    examHospitalStepDetail.setStepState(2);
                    projExamHospitalStepDetailMapper.insert(examHospitalStepDetail);
                    return Result.success();
                }
                // 调用API时设置domain信息
                Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
                log.info("设定医院信息:{}", domainMap);
                domainHolder.refresh(domainMap);
                domainMap.clear();
                SqlUpdateDTO sqlUpdateDTO = new SqlUpdateDTO();
                sqlUpdateDTO.setHisOrgId(projHospitalInfo.getOrgId());
                sqlUpdateDTO.setOrgId(projHospitalInfo.getOrgId());
                sqlUpdateDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
                sqlUpdateDTO.setDataSource(dictExamImportStep.getStepDatabaseName());
                String sqlText = dictExamImportStep.getSqlText();
                if (dictExamImportStep.getSqlType() == 3) {
                    sqlText = getBatchUpdateSql(projHospitalInfo.getHospitalInfoId(), sqlText);

                }
                sqlText = sqlText.replace("\r\n", " ")
                        .replace("#{hospitalId}", hospitalInfo.getCloudHospitalId().toString())
                        .replace("#{examHospitalId}", projHospitalInfo.getCloudHospitalId().toString());
                sqlUpdateDTO.setSqlText(AesUtil.encrypt(sqlText));
                try {
                    responseResult = dataPreparationApi.executeInsertOrUpdate(sqlUpdateDTO);
                    log.warn("API接口执行SQL返回值2：{}，参数：{}", responseResult, sqlUpdateDTO);
                } catch (Throwable e) {
                    log.error("API接口执行SQL报错了2：{}", sqlUpdateDTO, e);
                    throw e;
                }
            }
        }
        //responseResult == null是接口的脚本执行超时，不影响数据的正常插入，因为不作为异常处理
        if (responseResult == null || responseResult.getSuccess()) {
            ProjExamHospitalStepDetail examHospitalStepDetail = new ProjExamHospitalStepDetail();
            examHospitalStepDetail.setStepVsHospitalId(SnowFlakeUtil.getId());
            examHospitalStepDetail.setCustomInfoId(dto.getCustomInfoId());
            examHospitalStepDetail.setExamHospitalInfoId(dto.getExamHospitalInfoId());
            examHospitalStepDetail.setStepCode(dto.getStepCode());
            examHospitalStepDetail.setStepState(2);
            projExamHospitalStepDetailMapper.insert(examHospitalStepDetail);
            return Result.success();
        } else {
            return Result.fail(responseResult.getMessage());
        }
    }

    /**
     * 生成批量更新SQL
     *
     * @param examHospitalId
     * @param sqlText
     * @return
     */
    private String getBatchUpdateSql(Long examHospitalId, String sqlText) {
        List<ProjExamVsDept> projExamVsDepts = projExamVsDeptMapper.selectList(new QueryWrapper<ProjExamVsDept>()
                .eq("hospital_id", examHospitalId));
        if (CollectionUtil.isEmpty(projExamVsDepts)) {
            throw new CustomException("请先进行科室对照！");
        }
        String[] sqls = sqlText.split(";");
        StringBuilder sqlBuilder = new StringBuilder("");
        for (int i = 0; i < sqls.length; i++) {
            String sql = sqls[i];
            for (ProjExamVsDept projExamVsDept : projExamVsDepts) {
                String deptSql = sql.replace("#{newDeptId}", projExamVsDept.getNewDeptId().toString())
                        .replace("#{newDeptName}", "'" + projExamVsDept.getNewDeptName() + "'")
                        .replace("#{oldDeptId}", projExamVsDept.getOldDeptId().toString());
                sqlBuilder.append(deptSql).append(";");
            }
        }
        return sqlBuilder.toString();
    }

    /**
     * 更新云健康用户密码
     *
     * @param projHospitalInfo
     * @return
     */
    private ResponseResult updatePassword(ProjHospitalInfo projHospitalInfo) {
        //查询要修改的云健康密码
        String password = sysConfigMapper.selectConfigByName("NEW_PASSWORD").getConfigValue();
        // 调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        //拼装提交参数；hospital_id和password
        BatchInsertDTO<PasswordDTO> batchInsertDTO = new BatchInsertDTO<>();
        PasswordDTO passwordDTO = new PasswordDTO();
        passwordDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        passwordDTO.setNewPassword(password);
        List<PasswordDTO> list = new ArrayList<>();
        list.add(passwordDTO);
        batchInsertDTO.setData(list);
        batchInsertDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        batchInsertDTO.setHisOrgId(projHospitalInfo.getOrgId());
        //修改密码
        ResponseResult result = imspApi.changeUserPassword(batchInsertDTO);
        if (result.isSuccess()) {
            removeOnlineUserSessionByHospitalId(projHospitalInfo);
        }
        return result;
    }

    /**
     * 说明: 根据医院id清空医院所有用户会话
     *
     * @param projHospitalInfo
     * @return:com.msun.core.commons.api.ResponseResult
     * @author: Yhongmin
     * @createAt: 2024/5/24 9:10
     * @remark: Copyright
     */
    public ResponseResult removeOnlineUserSessionByHospitalId(ProjHospitalInfo projHospitalInfo) {
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        try {
            PortTestDTO dto = new PortTestDTO();
            dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
            dto.setHisOrgId(projHospitalInfo.getOrgId());
            List<String> str = new ArrayList<>();
            dto.setStrs(str);
            return hospitalApi.removeOnlineUserSessionByHospitalId(dto);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return ResponseResult.error(BaseErrorCode.FALLBACK, e.getMessage());
        }
    }

    /**
     * 校验院内人员、科室、病区数据是否已导入
     *
     * @param projHospitalInfo
     * @return
     */
    private Boolean checkMsunData(ProjHospitalInfo projHospitalInfo) {
        try {
            // 调用API时设置domain信息
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            String checkSql = sysConfigMapper.selectConfigByName("CHECK_MSUN_DATA").getConfigValue();
            checkSql = checkSql.replace("#{examHospitalId}", projHospitalInfo.getCloudHospitalId().toString());
            // 组装调用API的参数
            SqlSelectLoginDTO sqlSelectLoginDTO = new SqlSelectLoginDTO();
            sqlSelectLoginDTO.setHospitalInfoId(projHospitalInfo.getCloudHospitalId());
            sqlSelectLoginDTO.setSqlStr(AesUtil.encrypt(checkSql));
            // 组装调用API的参数
            SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
            sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
            sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
            sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
            sqlCheckApiDTO.setDto(sqlSelectLoginDTO);
            ResponseResult<List<JSONObject>> deptResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
            if (deptResult == null || CollectionUtils.isEmpty(deptResult.getData())) {
                return Boolean.FALSE;
            }
            log.info("校验院内人员、科室、病区数据结果：｛｝", JSONObject.toJSONString(deptResult.getData()));
            List<JSONObject> counts = JSONObject.parseArray(JSONObject.toJSONString(deptResult.getData()), JSONObject.class);
            for (JSONObject count : counts) {
                if (count.getInteger("cut") == 0) {
                    return Boolean.FALSE;
                }
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.info("校验院内人员、科室、病区数据是否已导入错误：｛｝", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 执行SQL脚本
     *
     * @param dto
     * @return
     */
    @Override
    public Result doTestSqltext(ExamImportStepDTO dto) {
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(dto.getExamHospitalInfoId());
        // 调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        SqlUpdateDTO sqlUpdateDTO = new SqlUpdateDTO();
        sqlUpdateDTO.setHisOrgId(projHospitalInfo.getOrgId());
        sqlUpdateDTO.setOrgId(projHospitalInfo.getOrgId());
        sqlUpdateDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        sqlUpdateDTO.setDataSource("chis");
        String sqlText = "delete from comm.dict_charge_price where order_category_id =2020";
        sqlUpdateDTO.setSqlText(AesUtil.encrypt(sqlText));
        ResponseResult responseResult = dataPreparationApi.executeInsertOrUpdate(sqlUpdateDTO);
        return Result.success();
    }

    /**
     * 删除数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result recycleExamData(ExamImportStepDTO dto) {
        // 检测 redis 是否存在 key
        List<SimulationHospital> hospitals = new LambdaQueryChainWrapper<>(simulationHospitalMapper)
                .eq(SimulationHospital::getIsDelete, 0)
                .eq(dto.getCustomInfoId() != null && dto.getCustomInfoId() > 0, SimulationHospital::getCustomInfoId, dto.getCustomInfoId())
                .eq(dto.getProjectInfoId() != null && dto.getProjectInfoId() > 0, SimulationHospital::getProjectInfoId, dto.getProjectInfoId())
                .eq(dto.getExamHospitalInfoId() != null && dto.getExamHospitalInfoId() > 0, SimulationHospital::getId, dto.getExamHospitalInfoId())
                .list();
        if (CollUtil.isNotEmpty(hospitals)) {
            for (SimulationHospital hospital : hospitals) {
                try {
                    taskScheduler.doRecycleExamHospital(hospital, Boolean.TRUE.equals(dto.getDestroy()));
                } catch (Throwable e) {
                    log.error("手动清除学习环境错误：{}", JSON.toJSONString(hospital), e);
                }
            }
        }
//
//        Object taskCode = redisUtil.get("recycle_exam_hospital");
//        if (ObjectUtil.isNotEmpty(taskCode)) {
//            return Result.fail("任务正在执行中");
//        }
//        taskScheduler.recycleExamHospital();
        return Result.success();
    }

    /**
     * 处理学习环境医院状态
     *
     * @param projectInfoId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExamHospital(Long projectInfoId) {
        //查询项目下的医院列表
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.findHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtils.isEmpty(hospitalInfoList)) {
            return;
        }
        //查询学习环境在使用中的医院列表
        List<Long> hospitalInfoIdList = hospitalInfoList.stream().map(ProjHospitalInfo::getHospitalInfoId).distinct().sorted().collect(Collectors.toList());
        LambdaQueryWrapper<SimulationHospital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SimulationHospital::getStatus, 1);
        queryWrapper.eq(SimulationHospital::getIsDelete, 0);
        queryWrapper.in(SimulationHospital::getHospitalInfoId, hospitalInfoIdList);
        List<SimulationHospital> simulationHospitalList = simulationHospitalMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(simulationHospitalList)) {
            //更新学习环境医院状态为待回收
            for (SimulationHospital simulationHospital : simulationHospitalList) {
                simulationHospital.setStatus(2);
                simulationHospitalMapper.updateById(simulationHospital);
            }
        }
    }
}
