package com.msun.csm.service.proj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.config.FileCodeEnum;
import com.msun.csm.common.enums.config.SysFileCodeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.Global;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendLimit;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendLimitMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.model.dto.ProjHospitalInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneInfoFileInitializeDTO;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.SysFileInitializeVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.sysfile.SysFileService;
import com.msun.csm.util.Md5Util;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.StringUtils;

/**
 * @version : V1.52.0
 * @ClassName: ProjMilestoneInfoFileInitializeServiceImpl
 * @Description:
 * @Author: Yhongmin
 * @Date: 15:20 2024/5/16
 */
@Service
public class ProjMilestoneInfoFileInitializeServiceImpl implements ProjMilestoneInfoFileInitializeService {
    private static final String REGEX = "(https?:\\/\\/[^:\\/]+):\\d+(.*)";
    private static final String IP_ADDRESS_PATTERN = "^(http|https):\\/\\/" + "((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))" + "(?::\\d+)?$";
    @Resource
    private SysFileService sysFileService;
    @Resource
    private ProjHospitalInfoService hospitalInfoService;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    private UserHelper userHelper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ProjOnlineStepDetailMapper projOnlineStepDetailMapper;
    @Resource
    private ConfigCustomBackendLimitMapper configCustomBackendLimitMapper;
    @Resource
    private BaseQueryService baseQueryService;
    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    /**
     * 说明: 文件或者链接处理
     *
     * @param projectInfoId
     * @param businessCode
     * @return:java.util.Map<java.lang.String,java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/29 13:40
     * @remark: Copyright
     */
    public Map<String, String> getTmpProjectNewVsOld(Long projectInfoId, String businessCode) {
        List<SysFile> sysFileList = sysFileService.findBySysFileCodeBusinessCode(businessCode);
        if (sysFileList == null || sysFileList.size() == 0) {
            return null;
        }
        //判断是否需要替换项目id或者客户id
        boolean isTmpProjectNewVsOld = sysFileList.stream().anyMatch(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{projectId}") || fe.getFilePath().contains("#{customerId}")));
        if (isTmpProjectNewVsOld) {
            // 查询当前项目对应的老项目id
            TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
            if (tmpProjectNewVsOld != null) {
                //处理链接
                sysFileList.stream().filter(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{projectId}") || fe.getFilePath().contains("#{customerId}"))).forEach(fe -> {
                    fe.setFilePath(fe.getFilePath().replace("#{projectId}", StringUtils.nvl(tmpProjectNewVsOld.getOldProjectInfoId())).replace("#{customerId}", StringUtils.nvl(tmpProjectNewVsOld.getOldCustomId())));
                });
            }
        }
        //判断项目名称
        boolean isprojProjectInfo = sysFileList.stream().anyMatch(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{projectName}")));
        if (isprojProjectInfo) {
            ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId));
            if (projProjectInfo != null) {
                sysFileList.stream().filter(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && fe.getFilePath().contains("#{projectName}")).forEach(fe -> {
                    fe.setFilePath(fe.getFilePath().replace("#{projectName}", StringUtils.nvl(projProjectInfo.getProjectName())));
                });
            }
        }
        //判断医院相关信息
        boolean isHospitalInfo = sysFileList.stream().anyMatch(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{hospitalName}") || fe.getFilePath().contains("#{hisOrgId}") || fe.getFilePath().contains("#{hospitalId}") || fe.getFilePath().contains("#{domain}")));
        if (isHospitalInfo) {
            SelectHospitalDTO dto = new SelectHospitalDTO();
            dto.setProjectInfoId(projectInfoId);
            dto.setHealthBureauFlag(NumberEnum.NO_1.num());
            List<ProjHospitalInfo> infoList = hospitalInfoService.getHospitalInfoByProjectId(dto);
            if (infoList.size() > 0) {
                ProjHospitalInfo projHospitalInfo = infoList.get(0);
                sysFileList.stream().filter(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{hospitalName}") || fe.getFilePath().contains("#{hisOrgId}") || fe.getFilePath().contains("#{hospitalId}"))).forEach(fe -> {
                    fe.setFilePath(fe.getFilePath().replace("#{hospitalName}", StringUtils.nvl(projHospitalInfo.getHospitalName())).replace("#{hisOrgId}", StringUtils.nvl(projHospitalInfo.getOrgId())).replace("#{domain}", StringUtils.nvl(projHospitalInfo.getCloudDomain())).replace("#{hospitalId}", StringUtils.nvl(projHospitalInfo.getCloudHospitalId())));
                });
            }
        }
        //判断用户相关信息
        boolean isUser = sysFileList.stream().anyMatch(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{userId}") || fe.getFilePath().contains("#{userName}")));
        if (isUser) {
            // 报表制作地址。人员id改为 运营平台人员id
            for (SysFile file : sysFileList) {
                if (file.getFileCode().equals("reportCreationAreaReportInitialization")) {
                    file.setFilePath(file.getFilePath().replace("#{userId}", StringUtils.nvl(Global.getSysUserVO().getUserYunyingId())));
                }
            }
            sysFileList.stream().filter(fe -> NumberEnum.NO_1.num().equals(fe.getFileUsedMode()) && (fe.getFilePath().contains("#{userId}") || fe.getFilePath().contains("#{userName}"))).forEach(fe -> {
                fe.setFilePath(fe.getFilePath().replace("#{userId}", StringUtils.nvl(Global.getSysUserVO().getSysUserId())).replace("#{userName}", StringUtils.nvl(Global.getSysUserVO().getUserName())));
            });
        }
        Map<String, String> map = sysFileList.stream().collect(Collectors.toMap(SysFile::getFileCode, SysFile::getFilePath));
        // 查询数据
        List<SysFile> findList = projOnlineStepDetailMapper.selectOnlineBeforFile(new ProjOnlineStepDTO());
        if (findList != null && findList.size() > 0) {
            for (SysFile sysFile : findList) {
                if ("set_browser".equals(sysFile.getFileCode())) {
                    map.put(sysFile.getFileCode(), sysFile.getFilePath());
                }
            }

        }
        return map;
    }

    /**
     * 说明: 里程碑-准备阶段-数据导入工作
     *
     * @param infoFileInitializeDTO
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    @Override
    public Result dataImportPreparationWork(ProjMilestoneInfoFileInitializeDTO infoFileInitializeDTO) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.DATA_IMPORT.getFileCode();
        SelectHospitalDTO dto = new SelectHospitalDTO();
        dto.setProjectInfoId(infoFileInitializeDTO.getProjectInfoId());
        List<ProjHospitalInfo> infoList = hospitalInfoService.getHospitalInfoByProjectId(dto);
        sysFileInitialize.setProjHospitalInfoList(infoList);
//        Map<String, String> maps = sysFileService.findMapBySysFileCodeBusinessCode(businessCode);
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(infoFileInitializeDTO.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 说明: 里程碑-入驻阶段-数据导入准备工作
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    @Override
    public Result dataPrepare(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.DATA_PREPARE.getFileCode();
        // 查询数据准备
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        // 查询数据导入工作中kettle下载链接和到数据脚本下载链接
        sysFileInitialize.getSysFiles().putAll(getSysFileMap(dto.getProjectInfoId(), SysFileCodeEnum.DATA_IMPORT, FileCodeEnum.KETTLE_TOOL, FileCodeEnum.GUIDE_DATA_SCRIPT));
        // 设置浏览器下载链接
        sysFileInitialize.getSysFiles().putAll(getSysFileMap(dto.getProjectInfoId(), SysFileCodeEnum.DATA_PREPARE, FileCodeEnum.BROWSER_SET_DESCRIPTION));
        return Result.success(sysFileInitialize);
    }

    /**
     * 获取指定文件编码的文件map
     *
     * @param projectInfoId   项目id
     * @param sysFileCodeEnum 文件类型枚举=businessCodeEnum
     * @param fileCodeEnums   文件编码数组
     * @return Map<String, String>
     */
    public Map<String, String> getSysFileMap(Long projectInfoId, SysFileCodeEnum sysFileCodeEnum, FileCodeEnum... fileCodeEnums) {
        Map<String, String> stringMap = getTmpProjectNewVsOld(projectInfoId, sysFileCodeEnum.getFileCode());
        Map<String, String> resultMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(stringMap)) {
            for (FileCodeEnum fileCodeEnum : fileCodeEnums) {
                Object url = stringMap.get(fileCodeEnum.getFileCode());
                if (ObjectUtil.isNotEmpty(url)) {
                    resultMap.put(fileCodeEnum.getFileCode(), url.toString());
                }
            }
        }
        return resultMap;
    }

    /**
     * 说明: 准备阶段节点-表单制作节点
     *
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    @Override
    public Result preparatForm(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.PREPARAT_FORM.getFileCode();
        // Map<String, String> maps = sysFileService.findMapBySysFileCodeBusinessCode(businessCode);
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 说明: 准备阶段节点-报表制作节点
     *
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    @Override
    public Result preparatReport(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.PREPARAT_REPORT.getFileCode();
        //Map<String, String> maps = sysFileService.findMapBySysFileCodeBusinessCode(businessCode);
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 说明: 里程碑-全员流程模拟-全员流程模拟引导
     *
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/16 16:10
     * @remark: Copyright
     */
    @Override
    public Result productTestCaseResult(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.TEST_LAUNCH.getFileCode();
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 说明: 里程碑-调研阶段-制定网络改造方案
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/29 14:01
     * @remark: Copyright
     */
    @Override
    public Result schemeNewwork(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.SCHEME_NEWWORK.getFileCode();
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 说明: 里程碑-调研阶段-制定切换方案
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/29 14:01
     * @remark: Copyright
     */
    @Override
    public Result schemeProject(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.SCHEME_PROJECT.getFileCode();
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 说明: 里程碑-调研阶段-制定切换方案
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/29 14:01
     * @remark: Copyright
     */
    @Override
    public Result productOperationManual(ProjMilestoneInfoFileInitializeDTO dto) {
        SysFileInitializeVO sysFileInitialize = new SysFileInitializeVO();
        String businessCode = SysFileCodeEnum.PRODUCT_OPERATION_MANUAL.getFileCode();
        sysFileInitialize.setSysFiles(getTmpProjectNewVsOld(dto.getProjectInfoId(), businessCode));
        return Result.success(sysFileInitialize);
    }

    /**
     * 报表制作节点-区域初始化--替换医院地址信息
     *
     * @param hospitalInfoId
     * @param projectInfoId
     * @return
     */
    @Override
    public Result reportReplaceUrl(Long hospitalInfoId, Long projectInfoId) {
        // 查询接口地址
        SysFile file = new SysFile();
        file.setFileCode("reportCreationAreaReportInitialization");
        Result<SysFile> sysFileResult = sysFileService.selectFile(file);
        // 查询医院信息
        ProjHospitalInfoDTO dto = new ProjHospitalInfoDTO();
        dto.setHospitalInfoId(hospitalInfoId);
        ProjHospitalInfoVO projHospitalInfoVO = hospitalInfoService.selectHospitalInfo(dto).getData();
        // 查询项目信息
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
        String filePath = sysFileResult.getData().getFilePath();
        // 老系统对应项目id
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
        // 地址信息替换
        //http://172.16.2.104/reportweb/#/reportUserList
        // ?projectId=#{projectId}&projectName=#{projectName}
        // &preProductNetwork=null&hisOrgId=#{hisOrgId}
        // &hospitalId=#{hospitalId}&hospitalName=#{hospitalName}
        // &roleCode=employee&roleName=员工&level=lv2&isArea=-1
        // &userId=#{userId}&userName=#{userName}
        filePath = filePath.replace("#{projectId}", tmpProjectNewVsOld.getOldProjectInfoId().toString());
        filePath = filePath.replace("#{projectName}", projProjectInfo.getProjectName());
        filePath = filePath.replace("#{hisOrgId}", projHospitalInfoVO.getOrgId().toString());
        filePath = filePath.replace("#{hospitalId}", projHospitalInfoVO.getCloudHospitalId().toString());
        filePath = filePath.replace("#{hospitalName}", projHospitalInfoVO.getHospitalName());
        filePath = filePath.replace("#{userId}", Global.getSysUserVO().getSysUserId().toString());
        filePath = filePath.replace("#{userName}", Global.getSysUserVO().getUserName());
        Pattern validator = Pattern.compile(IP_ADDRESS_PATTERN);
        String newPreProductNetwork = projHospitalInfoVO.getCloudDomain();
        Boolean b = validator.matcher(newPreProductNetwork).matches();
        if (!b) {
            Pattern pattern1 = Pattern.compile(REGEX);
            Matcher matcher1 = pattern1.matcher(newPreProductNetwork);
            if (matcher1.matches()) {
                newPreProductNetwork = matcher1.group(1) + matcher1.group(2);
            }
        }
        filePath = filePath.replace("#{preProductNetwork}", newPreProductNetwork);
        filePath = filePath.replace("#{host}", newPreProductNetwork);
        String token = Md5Util.md5(filePath);
        redisUtil.set(token, filePath, 60);
        filePath = filePath + "&token=" + token;
        return Result.success(filePath);
    }

    /**
     * 说明: 项目工具--报表制作--授权地址
     *
     * @param url
     * @return
     */
    @Override
    public Result authReportUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return Result.fail("url不能为空");
        }
        //从url中获取项目id
        String projectInfoId = "";
        Pattern validator = Pattern.compile(IP_ADDRESS_PATTERN);
        String[] queryParams = url.split("\\?");
        if (queryParams.length > 1) {
            String[] params = queryParams[1].split("&");
            for (String param : params) {
                if (param.startsWith("projectId=")) {
                    projectInfoId = param.substring("projectId=".length());
                }
                if (param.startsWith("preProductNetwork=")) {
                    String oldPreProductNetwork = param;
                    String newPreProductNetwork = param.substring("preProductNetwork=".length());
                    Boolean b = validator.matcher(newPreProductNetwork).matches();
                    if (!b) {
                        Pattern pattern1 = Pattern.compile(REGEX);
                        Matcher matcher1 = pattern1.matcher(newPreProductNetwork);
                        if (matcher1.matches()) {
                            newPreProductNetwork = matcher1.group(1) + matcher1.group(2);
                        }
                    }
                    url = url.replace(oldPreProductNetwork, "preProductNetwork=" + newPreProductNetwork);

                }
            }
        }
        //查询当前项目是否开启了小前端大后端模式
        List<ConfigCustomBackendLimit> projectLimits = configCustomBackendLimitMapper.selectList(new LambdaQueryWrapper<ConfigCustomBackendLimit>().eq(ConfigCustomBackendLimit::getProjectInfoId, Long.valueOf(projectInfoId)).eq(ConfigCustomBackendLimit::getOpenFlag, 1));
        //当前项目开启了小前端大后端模式，则只有后端人员有操作权限
        if (CollectionUtils.isNotEmpty(projectLimits)) {
            //判断是否开启了统计报表限制
            List<ConfigCustomBackendDetailLimit> limitDetails = configCustomBackendDetailLimitMapper.selectList(new LambdaQueryWrapper<ConfigCustomBackendDetailLimit>().eq(ConfigCustomBackendDetailLimit::getProjectInfoId, Long.valueOf(projectInfoId)).eq(ConfigCustomBackendDetailLimit::getOpenType, 9).eq(ConfigCustomBackendDetailLimit::getOpenFlag, 1));
            if (CollectionUtils.isNotEmpty(limitDetails)) {
                //查询当前账号的角色信息--前后端人员
                Integer roleType = baseQueryService.getUserRoleType(Long.valueOf(projectInfoId));
                //不是后端人员返回无权限
                if (roleType != 0 && roleType != 2) {
                    return Result.fail("暂无报表制作权限！");
                }
            }
        }
        String token = Md5Util.md5(url);
        redisUtil.set(token, url, 60);
        url = url + "&token=" + token;
        return Result.success(url);
    }

    /**
     * 根据项目id获取基础老平台基础数据地址
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result getBaseDataUrlByProjectInfoId(Long projectInfoId) {
        String url = "/projectManagementCenter/fastOnlineMenu";
        // 查询当前项目对应的老项目id
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
        if (tmpProjectNewVsOld != null) {
            url = url + "?projectId=" + tmpProjectNewVsOld.getOldProjectInfoId() + "&customerId=" + tmpProjectNewVsOld.getOldCustomId() + "&detailId=" + projectInfoId;
        } else {
            return Result.fail("获取基础老平台基础数据地址出错，请联系交付平台处理！");
        }
        return Result.success(url);
    }
}
