package com.msun.csm.service.message.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.dict.DictDateOfHolidays;
import com.msun.csm.dao.entity.dict.DictDateOfWorkTime;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.dao.entity.projectreview.DictProjectReviewType;
import com.msun.csm.dao.mapper.dict.DictDateOfHolidaysMapper;
import com.msun.csm.dao.mapper.dict.DictDateOfWorkTimeMapper;
import com.msun.csm.dao.mapper.proj.*;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewMapper;
import com.msun.csm.dao.mapper.projectreview.DictProjectReviewTypeMapper;
import com.msun.csm.model.req.project.GenerateEarlyWarningAndPenaltyMessageArgs;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectPlanService;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SendBusinessMessageServiceImpl implements SendBusinessMessageService {

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private DictProductService dictProductService;

    @Resource
    private ProjProjectPlanService planService;

    @Resource
    private ProjProductDeliverRecordMapper deliverRecordMapper;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Autowired
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Autowired
    private EarlyWarningAndPenaltyMessagesMapper earlyWarningAndPenaltyMessagesMapper;
    @Autowired
    private DictProjectReviewTypeMapper dictProjectReviewTypeMapper;
    @Autowired
    private ConfigProjectReviewMapper configProjectReviewMapper;
    @Autowired
    private DictDateOfWorkTimeMapper dictDateOfWorkTimeMapper;
    @Autowired
    private DictDateOfHolidaysMapper dictDateOfHolidaysMapper;


    @Override
    public boolean sendAllocatingOrCancelTaskMessage(Long projectInfoId, ProjTodoTask oldTodoTask, ProjTodoTask newTodoTask) {
        try {
            if (projectInfoId == null || newTodoTask == null) {
                log.info("发送分配/取消待办任务提醒消息，参数 projectInfoId、newTodoTask 不可为null。");
                return false;
            }
            // 项目信息
            ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
            // 客户信息
            ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

            // 新增代办，只给前后端责任人发送待办任务分配提醒消息
            if (oldTodoTask == null) {
                List<Long> sysUserIds = new ArrayList<>();

                if (!this.notExistUser(newTodoTask.getImplementationEngineerId())) {
                    sysUserIds.add(newTodoTask.getImplementationEngineerId());
                }
                if (!this.notExistUser(newTodoTask.getBackendEngineerId())) {
                    sysUserIds.add(newTodoTask.getBackendEngineerId());
                }
                if (CollectionUtils.isEmpty(sysUserIds)) {
                    log.info("当前待办没有分配责任人，无需发送发送待办任务分配提醒消息。todoTaskId={}", newTodoTask.getTodoTaskId());
                    return false;
                }

                Map<String, String> messageContentParam = this.getTodoTaskMessageContentParam(projCustomInfo, projProjectInfo, newTodoTask);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.ALLOCATING_TASK_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(sysUserIds);
                sendMessageService.sendMessage2(messageParam);
                return true;
            }

            List<Long> cancelMessageSysUserIds = new ArrayList<>();

            List<Long> allocatingMessageSysUserIds = new ArrayList<>();

            // 原来没有责任人
            if (this.notExistUser(oldTodoTask.getImplementationEngineerId())) {
                // 现在有责任人，发送待办任务分配提醒消息
                if (!this.notExistUser(newTodoTask.getImplementationEngineerId())) {
                    allocatingMessageSysUserIds.add(newTodoTask.getImplementationEngineerId());
                }
            } else {
                // 原来有责任人

                // 现在没有责任人，发送待办任务取消提醒消息
                if (this.notExistUser(newTodoTask.getImplementationEngineerId())) {
                    cancelMessageSysUserIds.add(oldTodoTask.getImplementationEngineerId());
                } else {
                    // 现在有责任人但是不是原来的责任人
                    if (!newTodoTask.getImplementationEngineerId().equals(oldTodoTask.getImplementationEngineerId())) {
                        // 给原来的人发送待办任务取消提醒消息，给现在的人发送待办任务分配提醒消息
                        cancelMessageSysUserIds.add(oldTodoTask.getImplementationEngineerId());
                        allocatingMessageSysUserIds.add(newTodoTask.getImplementationEngineerId());
                    }
                }
            }

            // 原来没有责任人
            if (this.notExistUser(oldTodoTask.getBackendEngineerId())) {
                // 现在有责任人，发送待办任务分配提醒消息
                if (!this.notExistUser(newTodoTask.getBackendEngineerId())) {
                    allocatingMessageSysUserIds.add(newTodoTask.getBackendEngineerId());
                }
            } else {
                // 原来有责任人

                // 现在没有责任人，发送待办任务取消提醒消息
                if (this.notExistUser(newTodoTask.getBackendEngineerId())) {
                    cancelMessageSysUserIds.add(oldTodoTask.getBackendEngineerId());
                } else {
                    // 现在有责任人但是不是原来的责任人
                    if (!newTodoTask.getBackendEngineerId().equals(oldTodoTask.getBackendEngineerId())) {
                        // 给原来的人发送待办任务取消提醒消息，给现在的人发送待办任务分配提醒消息
                        cancelMessageSysUserIds.add(oldTodoTask.getBackendEngineerId());
                        allocatingMessageSysUserIds.add(newTodoTask.getBackendEngineerId());
                    }
                }
            }

            // 发送待办任务取消提醒消息
            if (!CollectionUtils.isEmpty(cancelMessageSysUserIds)) {
                Map<String, String> messageContentParam = this.getTodoTaskMessageContentParam(projCustomInfo, projProjectInfo, newTodoTask);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.CANCEL_TASK_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(cancelMessageSysUserIds);
                sendMessageService.sendMessage2(messageParam);
            }

            if (!CollectionUtils.isEmpty(allocatingMessageSysUserIds)) {
                Map<String, String> messageContentParam = this.getTodoTaskMessageContentParam(projCustomInfo, projProjectInfo, newTodoTask);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.ALLOCATING_TASK_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(allocatingMessageSysUserIds);
                sendMessageService.sendMessage2(messageParam);
            }
            return true;
        } catch (Exception e) {
            log.error("发送分配/取消待办任务提醒消息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return false;
        }
    }

    private Map<String, String> getTodoTaskMessageContentParam(ProjCustomInfo projCustomInfo, ProjProjectInfo projProjectInfo, ProjTodoTask todoTask) {
        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }
        messageContentParam.put("title", todoTask.getTitle());
        if (todoTask.getYyProductId() == null) {
            messageContentParam.put("productName", "");
        } else {
            String productName = dictProductService.getProductNameByYyProductId(todoTask.getYyProductId());
            if (StringUtils.isBlank(productName)) {
                messageContentParam.put("productName", "");
            } else {
                messageContentParam.put("productName", "（" + productName + "）");
            }
        }
        messageContentParam.put("planTime", todoTask.getPlanTime() == null ? "计划日期" : DateUtil.formatDate(todoTask.getPlanTime()));

        return messageContentParam;
    }

    @Override
    public boolean sendAllocatingOrCancelIssueMessage(Long projectInfoId, ProjIssueInfo oldIssue, ProjIssueInfo newIssue) {
        try {
            if (projectInfoId == null || newIssue == null) {
                log.info("发送分配/取消待办任务提醒消息，参数 projectInfoId、newTodoTask 不可为null。");
                return false;
            }
            // 项目信息
            ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
            // 客户信息
            ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

            // 新增代办，只给前后端责任人发送待办任务分配提醒消息
            if (oldIssue == null) {
                List<Long> sysUserIds = new ArrayList<>();

                if (!this.notExistUser(newIssue.getChargePerson())) {
                    sysUserIds.add(newIssue.getChargePerson());
                }

                if (CollectionUtils.isEmpty(sysUserIds)) {
                    log.info("当前待办没有分配责任人，无需发送发送待办任务分配提醒消息。todoTaskId={}", newIssue.getTodoTaskId());
                    return false;
                }

                Map<String, String> messageContentParam = this.getIssueMessageContentParam(projCustomInfo, projProjectInfo, newIssue);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.ALLOCATING_ISSUE_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(sysUserIds);
                sendMessageService.sendMessage2(messageParam);
                return true;
            }

            List<Long> cancelMessageSysUserIds = new ArrayList<>();

            List<Long> allocatingMessageSysUserIds = new ArrayList<>();

            // 原来没有责任人
            if (this.notExistUser(oldIssue.getChargePerson())) {
                // 现在有责任人，发送待办任务分配提醒消息
                if (!this.notExistUser(newIssue.getChargePerson())) {
                    allocatingMessageSysUserIds.add(newIssue.getChargePerson());
                }
            } else {
                // 原来有责任人

                // 现在没有责任人，发送待办任务取消提醒消息
                if (this.notExistUser(newIssue.getChargePerson())) {
                    cancelMessageSysUserIds.add(oldIssue.getChargePerson());
                } else {
                    // 现在有责任人但是不是原来的责任人
                    if (!newIssue.getChargePerson().equals(oldIssue.getChargePerson())) {
                        // 给原来的人发送待办任务取消提醒消息，给现在的人发送待办任务分配提醒消息
                        cancelMessageSysUserIds.add(oldIssue.getChargePerson());
                        allocatingMessageSysUserIds.add(newIssue.getChargePerson());
                    }
                }
            }

            // 发送待办任务取消提醒消息
            if (!CollectionUtils.isEmpty(cancelMessageSysUserIds)) {
                Map<String, String> messageContentParam = this.getIssueMessageContentParam(projCustomInfo, projProjectInfo, newIssue);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.CANCEL_ISSUE_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(cancelMessageSysUserIds);
                sendMessageService.sendMessage2(messageParam);
            }

            if (!CollectionUtils.isEmpty(allocatingMessageSysUserIds)) {
                Map<String, String> messageContentParam = this.getIssueMessageContentParam(projCustomInfo, projProjectInfo, newIssue);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.ALLOCATING_ISSUE_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(allocatingMessageSysUserIds);
                sendMessageService.sendMessage2(messageParam);
            }
            return true;
        } catch (Exception e) {
            log.error("发送分配/取消待办任务提醒消息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return false;
        }
    }

    private Map<String, String> getIssueMessageContentParam(ProjCustomInfo projCustomInfo, ProjProjectInfo projProjectInfo, ProjIssueInfo issueInfo) {
        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }
        if (issueInfo.getProjectPlanId() == null || issueInfo.getProjectPlanId() == 0) {
            messageContentParam.put("planName", "");
        } else {
            ProjProjectPlan projProjectPlan = planService.selectByPrimaryKey(issueInfo.getProjectPlanId());
            if (projProjectPlan == null) {
                messageContentParam.put("planName", "");
            } else {
                messageContentParam.put("planName", projProjectPlan.getTitle());
            }

        }

        if (issueInfo.getProductId() == null) {
            messageContentParam.put("productName", "");
        } else {
            if (issueInfo.getProductId() == -1) {
                messageContentParam.put("productName", "其他");
            } else {
                String productName = dictProductService.getProductNameByYyProductId(Long.valueOf(String.valueOf(issueInfo.getProductId())));
                if (StringUtils.isBlank(productName)) {
                    messageContentParam.put("productName", "");
                } else {
                    messageContentParam.put("productName", "（" + productName + "）");
                }
            }
        }
        messageContentParam.put("classification", StringUtils.isBlank(issueInfo.getClassification()) ? "" : issueInfo.getClassification());
        if (issueInfo.getPriority() == null) {
            messageContentParam.put("level", "暂未指定优先级");
        } else if (issueInfo.getPriority() == 1) {
            messageContentParam.put("level", "紧急");
        } else if (issueInfo.getPriority() == 2) {
            messageContentParam.put("level", "普通");
        } else {
            messageContentParam.put("level", "较低");
        }

        return messageContentParam;
    }

    /**
     * 判断是不是不存在责任人
     *
     * @param userId 用户ID
     * @return true：不存在责任人
     */
    private boolean notExistUser(Long userId) {
        return userId == null || userId == -1;
    }

    @Override
    public void sendSurveyProductProgressMessage() {
        List<ProjProjectInfo> openSurveyProductAuditProject = projectInfoService.getOpenSurveyProductAudit();
        if (CollectionUtils.isEmpty(openSurveyProductAuditProject)) {
            return;
        }
        // 调研没有完成的
        List<ProjProjectInfo> unfinishedSurvey = openSurveyProductAuditProject.stream().filter(item -> Integer.valueOf(1).equals(item.getProjectDeliverStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unfinishedSurvey)) {
            return;
        }
        for (ProjProjectInfo projectInfo : unfinishedSurvey) {
            Map<String, String> messageContentParam = this.getSurveyProductProgressMessageContentParam(projectInfo);
            SendMessageParam messageParam = new SendMessageParam();
            messageParam.setMessageTypeId(100014L);
            messageParam.setProjectInfoId(projectInfo.getProjectInfoId());
            messageParam.setMessageContentParam(messageContentParam);
            messageParam.setSysUserIds(this.getSurveyProductProgressMessageReceiver(projectInfo));
            sendMessageService.sendMessage2(messageParam);
        }
    }

    private List<Long> getSurveyProductProgressMessageReceiver(ProjProjectInfo projectInfo) {
        List<Long> receiver = new ArrayList<>();
        // 前端项目经理
        receiver.add(projectInfo.getProjectLeaderId());
        ProjProjectMember backLeader = projectInfoService.getBackLeader(projectInfo.getProjectInfoId());
        // 后端项目经理
        if (backLeader != null && backLeader.getProjectMemberId() != null) {
            receiver.add(backLeader.getProjectMemberId());
        }
        ProjProjectPlan projectPlan = planService.getProjectPlanByProjectInfoIdAndItemCode(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
        if (projectPlan != null) {
            // 前端专项负责人
            if (projectPlan.getImplementationEngineerId() != null) {
                receiver.add(projectPlan.getImplementationEngineerId());
            }
            // 后端专项负责人不是null且需要后端负责人的情况下
            if (projectPlan.getBackendEngineerId() != null && Integer.valueOf(1).equals(projectPlan.getBackendFlag())) {
                receiver.add(projectPlan.getBackendEngineerId());
            }
        }
        receiver = receiver.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        return receiver;
    }

    private Map<String, String> getSurveyProductProgressMessageContentParam(ProjProjectInfo projProjectInfo) {
        // 客户信息
        ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projProjectInfo.getProjectInfoId());
        List<ProjHospitalInfo> hospitalList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);

        List<ProjProductDeliverRecord> deliverRecordList = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(projProjectInfo.getProjectInfoId());
        Set<Long> productIds = deliverRecordList.stream().map(ProjProductDeliverRecord::getProductDeliverId).collect(Collectors.toSet());
        List<BaseIdNameResp> productInfoList = productMapper.findByProductIds(productIds);

        ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
        projSurveyPlanDTO.setProjectInfoId(projProjectInfo.getProjectInfoId());
        List<SurveyPlanTaskResp> surveyPlan = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
        // 待审核
        long waitingAuditCount = surveyPlan.stream().filter(item -> Integer.valueOf(4).equals(item.getCompleteStatus())).count();
        // 已驳回
        long rejectedCount = surveyPlan.stream().filter(item -> Integer.valueOf(5).equals(item.getCompleteStatus())).count();
        // 审核通过
        long auditPassedCount = surveyPlan.stream().filter(item -> Integer.valueOf(3).equals(item.getCompleteStatus()) || Integer.valueOf(1).equals(item.getCompleteStatus())).count();
        // 待提交审核
        long noSubmitAuditCount = Long.parseLong(String.valueOf(surveyPlan.size())) - waitingAuditCount - rejectedCount - auditPassedCount;
        // 需调研医院总数
        messageContentParam.put("hospitalCount", String.valueOf(hospitalList.size()));
        // 需调研产品总数
        messageContentParam.put("productCount", String.valueOf(productInfoList.size()));
        // 总计需调研
        messageContentParam.put("totalCount", String.valueOf(surveyPlan.size()));

        // 待提交审核
        messageContentParam.put("noSubmitAuditCount", String.valueOf(noSubmitAuditCount));

        // 待审核
        messageContentParam.put("waitingAuditCount", String.valueOf(waitingAuditCount));

        // 已驳回
        messageContentParam.put("rejectedCount", String.valueOf(rejectedCount));

        // 审核通过
        messageContentParam.put("auditPassedCount", String.valueOf(auditPassedCount));

        return messageContentParam;
    }

    @Override
    public void sendAllocatingOrCancelSurveyAuditMessage(Long projectInfoId, ProjSurveyPlan oldSurveyPlan, ProjSurveyPlan newSurveyPlan) {
        try {
            if (projectInfoId == null || newSurveyPlan == null) {
                log.info("发送分配/取消调研结果审核提醒，参数 projectInfoId、newSurveyPlan 不可为null。");
                return;
            }
            // 项目信息
            ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
            // 客户信息
            ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

            // 新增代办，只给后端责任人发送
            if (oldSurveyPlan == null) {
                List<Long> sysUserIds = new ArrayList<>();

                if (!this.notExistUser(newSurveyPlan.getAuditSysUserId())) {
                    sysUserIds.add(newSurveyPlan.getAuditSysUserId());
                }
                if (CollectionUtils.isEmpty(sysUserIds)) {
                    log.info("当前调研结果没有分配审核人，无需发送消息。surveyPlanId={}", newSurveyPlan.getSurveyPlanId());
                    return;
                }

                Map<String, String> messageContentParam = this.getSurveyPlanAuditMessageContentParam(projCustomInfo, projProjectInfo, newSurveyPlan);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.ALLOCATING_SURVEY_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(sysUserIds);
                sendMessageService.sendMessage2(messageParam);
                return;
            }

            List<Long> cancelMessageSysUserIds = new ArrayList<>();

            List<Long> allocatingMessageSysUserIds = new ArrayList<>();

            // 原来没有责任人
            if (this.notExistUser(oldSurveyPlan.getAuditSysUserId())) {
                // 现在有责任人，发送待办任务分配提醒消息
                if (!this.notExistUser(newSurveyPlan.getAuditSysUserId())) {
                    allocatingMessageSysUserIds.add(newSurveyPlan.getAuditSysUserId());
                }
            } else {
                // 原来有责任人

                // 现在没有责任人，发送待办任务取消提醒消息
                if (this.notExistUser(newSurveyPlan.getAuditSysUserId())) {
                    cancelMessageSysUserIds.add(oldSurveyPlan.getAuditSysUserId());
                } else {
                    // 现在有责任人但是不是原来的责任人
                    if (!newSurveyPlan.getAuditSysUserId().equals(oldSurveyPlan.getAuditSysUserId())) {
                        // 给原来的人发送待办任务取消提醒消息，给现在的人发送待办任务分配提醒消息
                        cancelMessageSysUserIds.add(oldSurveyPlan.getAuditSysUserId());
                        allocatingMessageSysUserIds.add(newSurveyPlan.getAuditSysUserId());
                    }
                }
            }

            // 发送待办任务取消提醒消息
            if (!CollectionUtils.isEmpty(cancelMessageSysUserIds)) {
                Map<String, String> messageContentParam = this.getSurveyPlanAuditMessageContentParam(projCustomInfo, projProjectInfo, newSurveyPlan);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.CANCEL_SURVEY_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(cancelMessageSysUserIds);
                sendMessageService.sendMessage2(messageParam);
            }

            if (!CollectionUtils.isEmpty(allocatingMessageSysUserIds)) {
                Map<String, String> messageContentParam = this.getSurveyPlanAuditMessageContentParam(projCustomInfo, projProjectInfo, newSurveyPlan);
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(DictMessageTypeEnum.ALLOCATING_SURVEY_MESSAGE.getId());
                messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(allocatingMessageSysUserIds);
                sendMessageService.sendMessage2(messageParam);
            }
        } catch (Exception e) {
            log.error("发送分配/取消待办任务提醒消息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    private Map<String, String> getSurveyPlanAuditMessageContentParam(ProjCustomInfo customInfo, ProjProjectInfo projectInfo, ProjSurveyPlan surveyPlan) {
        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", customInfo.getCustomName());
        messageContentParam.put("projectNumber", projectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }

        if (surveyPlan.getYyProductId() == null) {
            messageContentParam.put("productName", "");
        } else {
            String productName = dictProductService.getProductNameByYyProductId(surveyPlan.getYyProductId());
            if (StringUtils.isBlank(productName)) {
                messageContentParam.put("productName", "");
            } else {
                messageContentParam.put("productName", "（" + productName + "）");
            }
        }
        messageContentParam.put("planTime", surveyPlan.getPlanAuditTime() == null ? "计划日期" : DateUtil.formatDate(surveyPlan.getPlanAuditTime()));

        return messageContentParam;
    }

    @Override
    public List<EarlyWarningAndPenaltyMessages> generateEarlyWarningAndPenaltyMessage(GenerateEarlyWarningAndPenaltyMessageArgs args) {
        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projProjectInfoMapper)
                .eq(ProjProjectInfo::getProjectInfoId, args.getProjectInfoId())
                .eq(ProjProjectInfo::getIsDeleted, 0)
                .one();
        if (proj == null) {
            throw new CustomException("无效项目ID：" + args.getProjectInfoId());
        }
        DictProjectReviewType reviewType = new LambdaQueryChainWrapper<>(dictProjectReviewTypeMapper)
                .eq(DictProjectReviewType::getDictCode, args.getReviewTypeCode())
                .eq(DictProjectReviewType::getIsDeleted, 0)
                .last("limit 1")
                .one();
        if (reviewType == null) {
            throw new CustomException("无效审核类型编码：" + args.getReviewTypeCode());
        }
        boolean isSmallFrontBigBack = projectInfoService.isSmallFrontBigBack(args.getProjectInfoId());
        int projectType = proj.getProjectType();
        int telesalesFlag = proj.getTelesalesFlag();
        int hisFlag = proj.getHisFlag() == 0 ? 2 : 1;
        int frontBackFlag = isSmallFrontBigBack ? 1 : 0;
        ConfigProjectReview cfg = new LambdaQueryChainWrapper<>(configProjectReviewMapper)
                .eq(ConfigProjectReview::getIsDeleted, 0)
                .eq(ConfigProjectReview::getReviewTypeId, reviewType.getProjectReviewTypeId()) // 审核类型字典ID
                .in(ConfigProjectReview::getCustomType, Arrays.asList(projectType, -1)) //单体区域
                .in(ConfigProjectReview::getTelesalesFlag, Arrays.asList(telesalesFlag, -1)) //是否电销
                .in(ConfigProjectReview::getDeliveryModel, Arrays.asList(frontBackFlag, -1)) //是否前后端
                .in(ConfigProjectReview::getProjectType, Arrays.asList(hisFlag, -1)) //是否首期
                .last("limit 1")
                .one();
        if (cfg == null) {
            log.warn("未配置预警单和罚款单消息，args: {}", args);
            return Collections.emptyList();
        }
        if (StrUtil.isBlank(cfg.getPenaltyPeriod()) || Long.parseLong(cfg.getPenaltyPeriod()) <= 0) {
            log.warn("配置预警单和罚款单消息时，未配置罚单产生周期，args: {}，cfg: {}", args, cfg);
            return Collections.emptyList();
        }
        if (StrUtil.isBlank(cfg.getWarningBeforePenalty())) {
            log.warn("配置预警单和罚款单消息时，预警时间为空，args: {}，cfg: {}", args, cfg);
            return Collections.emptyList();
        }
        List<EarlyWarningAndPenaltyMessages> arr = new ArrayList<>();
        Date penaltyEndTime = generatePenaltyEndTime(cfg, args.getPenaltyStartTime());
        long earlyWarningAndPenaltyMessagesId = 0;
        if (cfg.getIsFineFlag() == 1) {
            earlyWarningAndPenaltyMessagesId = IdGenerator.ins().generator();
            //生成罚单
            EarlyWarningAndPenaltyMessages penaltyMessage = new EarlyWarningAndPenaltyMessages();
            penaltyMessage.setEarlyWarningAndPenaltyMessagesId(earlyWarningAndPenaltyMessagesId);
            penaltyMessage.setProjectInfoId(args.getProjectInfoId());
            penaltyMessage.setProjectName(proj.getProjectName());
            penaltyMessage.setProjectNumber(proj.getProjectNumber());
            penaltyMessage.setProjectReviewId(cfg.getProjectReviewId());
            penaltyMessage.setPenaltyPersonId(args.getPenaltyPersonId());
            penaltyMessage.setPenaltyReason(args.getPenaltyReason());
            penaltyMessage.setPenaltyAmount(BigDecimal.valueOf(cfg.getFineMoney()));
            penaltyMessage.setPenaltyTime(penaltyEndTime);
            penaltyMessage.setPenaltyCategory("penalty");
            penaltyMessage.setPenaltyStartTime(args.getPenaltyStartTime());
            penaltyMessage.setPenaltyEndTime(penaltyEndTime);
            penaltyMessage.setExcludeHolidaysFlag(cfg.getExcludeHolidaysFlag());
            penaltyMessage.setExcludeWeekendsFlag(cfg.getExcludeWeekendsFlag());
            penaltyMessage.setExcludeNonWorkingHoursFlag(cfg.getExcludeNonWorkingHoursFlag());
            penaltyMessage.setSendTime(penaltyEndTime);
            penaltyMessage.setSendTimeStr(DateUtil.format(penaltyEndTime, "yyyy-MM-dd HH:mm"));
            penaltyMessage.setSendFlag(false);
            penaltyMessage.setPenaltyType(cfg.getPenaltyType());
            penaltyMessage.setPenaltyPeriod(cfg.getPenaltyPeriod());
            penaltyMessage.setPenaltyPeriodUnit(cfg.getPenaltyPeriodUnit());
            penaltyMessage.setWarningBeforePenalty(cfg.getWarningBeforePenalty());
            penaltyMessage.setReviewTypeCode(args.getReviewTypeCode());
            penaltyMessage.setBusinessId(args.getBusinessId());
            penaltyMessage.setBusinessTable(args.getBusinessTable());
            penaltyMessage.setBusinessName(StrUtil.isBlank(args.getBusinessName()) ? reviewType.getDictName() : args.getBusinessName());
            penaltyMessage.setRemark(reviewType.getRemark());
            penaltyMessage.setYyFunValue(cfg.getYyFunValue());
            penaltyMessage.setYyMonitorPoint(cfg.getYyMonitorPoint());
            penaltyMessage.setIsDeleted(0);
            arr.add(penaltyMessage);
        }

        //生成预警单
        List<Date> dates = generateWarningTime(cfg, penaltyEndTime);
        for (Date date : dates) {
            EarlyWarningAndPenaltyMessages message = new EarlyWarningAndPenaltyMessages();
            message.setEarlyWarningAndPenaltyMessagesId(IdGenerator.ins().generator());
            message.setPenaltyEarlyWarningAndPenaltyMessagesId(earlyWarningAndPenaltyMessagesId);
            message.setProjectInfoId(args.getProjectInfoId());
            message.setProjectName(proj.getProjectName());
            message.setProjectNumber(proj.getProjectNumber());
            message.setProjectReviewId(cfg.getProjectReviewId());
            message.setPenaltyPersonId(args.getPenaltyPersonId());
            message.setPenaltyReason(args.getPenaltyReason());
            message.setPenaltyAmount(BigDecimal.valueOf(cfg.getFineMoney()));
            message.setPenaltyTime(penaltyEndTime);
            message.setPenaltyCategory("warning");
            message.setPenaltyStartTime(args.getPenaltyStartTime());
            message.setPenaltyEndTime(penaltyEndTime);
            message.setExcludeHolidaysFlag(cfg.getExcludeHolidaysFlag());
            message.setExcludeWeekendsFlag(cfg.getExcludeWeekendsFlag());
            message.setExcludeNonWorkingHoursFlag(cfg.getExcludeNonWorkingHoursFlag());
            message.setSendTime(penaltyEndTime);
            message.setSendTimeStr(DateUtil.format(date, "yyyy-MM-dd HH:mm"));
            message.setSendFlag(false);
            message.setPenaltyType(cfg.getPenaltyType());
            message.setPenaltyPeriod(cfg.getPenaltyPeriod());
            message.setPenaltyPeriodUnit(cfg.getPenaltyPeriodUnit());
            message.setWarningBeforePenalty(cfg.getWarningBeforePenalty());
            message.setReviewTypeCode(args.getReviewTypeCode());
            message.setBusinessId(args.getBusinessId());
            message.setBusinessTable(args.getBusinessTable());
            message.setBusinessName(StrUtil.isBlank(args.getBusinessName()) ? reviewType.getDictName() : args.getBusinessName());
            message.setRemark(reviewType.getRemark());
            message.setYyFunValue(cfg.getYyFunValue());
            message.setYyMonitorPoint(cfg.getYyMonitorPoint());
            message.setIsDeleted(0);
            arr.add(message);
        }
        if (CollUtil.isNotEmpty(arr)) {
            earlyWarningAndPenaltyMessagesMapper.insertBatch(arr);
        }
        return arr;
    }

    private long parseUnit(String unit, Boolean excludeNonWorkingHoursFlag, DictDateOfWorkTime workTime) {
        if (unit.contains("秒")) {
            return 1000;
        } else if (unit.contains("分")) {
            return 1000 * 60;
        } else if (unit.contains("时")) {
            return 1000 * 60 * 60;
        } else if (unit.contains("天") || unit.contains("日")) {
            if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
                long amBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getAmToTime(), "HH:mm"), DateUtil.parse(workTime.getAmFromTime(), "HH:mm"));
                long pmBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getPmToTime(), "HH:mm"), DateUtil.parse(workTime.getPmFromTime(), "HH:mm"));
                return amBetweenMills + pmBetweenMills;
            } else {
                return 1000 * 60 * 60 * 24;
            }
        } else {
            throw new CustomException("无效的时间单位：" + unit);
        }
    }

    @Override
    public Date generatePenaltyEndTime(ConfigProjectReview cfg, Date penaltyStartTime) {

        Boolean excludeHolidaysFlag = cfg.getExcludeHolidaysFlag(); //是否排除节假日
        Boolean excludeWeekendsFlag = cfg.getExcludeWeekendsFlag(); //是否排除周末
        Boolean excludeNonWorkingHoursFlag = cfg.getExcludeNonWorkingHoursFlag(); //是否排除非工作时间
        DictDateOfWorkTime workTime = new LambdaQueryChainWrapper<>(dictDateOfWorkTimeMapper)
                .gt(DictDateOfWorkTime::getId, 0)
                .last("limit 1")
                .one();
        long amBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getAmToTime(), "HH:mm"), DateUtil.parse(workTime.getAmFromTime(), "HH:mm"));
        long pmBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getPmToTime(), "HH:mm"), DateUtil.parse(workTime.getPmFromTime(), "HH:mm"));
        long amRestBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getPmFromTime(), "HH:mm"), DateUtil.parse(workTime.getAmToTime(), "HH:mm"));
        long dayBetweenMills = amBetweenMills + pmBetweenMills;
        String penaltyStartDay = DateUtil.format(penaltyStartTime, "yyyy-MM-dd");
        List<DictDateOfHolidays> days = new LambdaQueryChainWrapper<>(dictDateOfHolidaysMapper)
                .ge(DictDateOfHolidays::getDateInfo, DateUtil.parse(penaltyStartDay, "yyyy-MM-dd")) //排除时分秒
                .eq(Boolean.TRUE.equals(excludeHolidaysFlag), DictDateOfHolidays::getHolidaysFlag, 0)
                .eq(Boolean.TRUE.equals(excludeWeekendsFlag), DictDateOfHolidays::getWorkingDayFlag, 1)
                .orderByAsc(DictDateOfHolidays::getDateInfo)
                .list();
        if (CollectionUtils.isEmpty(days)) {
            throw new CustomException(StrUtil.format("未配置日期信息，无法计算罚款结束时间，penaltyStartDay: {}", penaltyStartDay));
        }
        //修正时间，确保开始时间在工作时间内
        if (StrUtil.equals(penaltyStartDay, CollUtil.getFirst(days).getDateStr())) {
            if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
                if (penaltyStartTime.getTime() < DateUtil.parse(penaltyStartDay + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm").getTime()) {
                    //如果开始时间在工作时间之前，则将开始时间修正为工作时间的开始时间
                    penaltyStartTime = DateUtil.parse(penaltyStartDay + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm");
                } else if (penaltyStartTime.getTime() >= DateUtil.parse(penaltyStartDay + " " + workTime.getAmToTime(), "yyyy-MM-dd HH:mm").getTime() &&
                        penaltyStartTime.getTime() < DateUtil.parse(penaltyStartDay + " " + workTime.getPmFromTime(), "yyyy-MM-dd HH:mm").getTime()) {
                    //如果开始时间在中午休息时间段，那么就把开始时间修正为下午的开始时间
                    penaltyStartTime = DateUtil.parse(penaltyStartDay + " " + workTime.getPmFromTime(), "yyyy-MM-dd HH:mm");
                } else if (penaltyStartTime.getTime() >= DateUtil.parse(penaltyStartDay + " " + workTime.getPmToTime(), "yyyy-MM-dd HH:mm").getTime()) {
                    //如果开始时间是当天的工作时间结束时间之后，则需要将开始时间修正为下一天的工作时间开始时间，删除当天时间
                    days.remove(0);
                    penaltyStartDay = CollUtil.getFirst(days).getDateStr();
                    penaltyStartTime = DateUtil.parse(penaltyStartDay + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm");
                }
            }
        } else {
            penaltyStartDay = CollUtil.getFirst(days).getDateStr();
            if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
                //调整为下一个工作日的工作时间
                penaltyStartTime = DateUtil.parse(penaltyStartDay + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm");
            } else {
                //调整为下一个工作日的凌晨开始时间
                penaltyStartTime = DateUtil.parse(penaltyStartDay + " " + "00:00:00", "yyyy-MM-dd HH:mm:ss");
            }
        }
        String penaltyStartHm = DateUtil.format(penaltyStartTime, "HH:mm");
        //获取一天的时长
        Long dayTotalMillis = 1000 * 60 * 60 * 24L;
        if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
            //如果是排除非工作时间的情况
            dayTotalMillis = dayBetweenMills;
        }
        String penaltyUnit = cfg.getPenaltyPeriodUnit();
        long penaltyUnitVal = parseUnit(penaltyUnit, cfg.getExcludeNonWorkingHoursFlag(), workTime);
        double penaltyPeriod = Double.parseDouble(cfg.getPenaltyPeriod());
        Long totalPeriodMillis = Double.valueOf(penaltyPeriod * penaltyUnitVal).longValue();
        //计算周期跨天数量
        long outDays = totalPeriodMillis / dayTotalMillis;
        //计算周期跨天后剩余的毫秒数
        long outMillis = totalPeriodMillis % dayTotalMillis;
        //获取跨天后的目标天，
        if (outDays + 1 >= days.size()) {
            //有些情况还需要在超出范围内再延后一天
            throw new CustomException(StrUtil.format("罚款周期超出了维护的时间，无法计算罚单时间，outDays: {}, days.size: {}", outDays + 1, days.size()));
        }
        DictDateOfHolidays day = days.get((int) outDays);
        //跨天后的时间开始时间
        Date mdayTime = DateUtil.parse(day.getDateStr() + " " + penaltyStartHm, "yyyy-MM-dd HH:mm");
        if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
            if (mdayTime.getTime() < DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                //mdayTime落在了上午
                if (mdayTime.getTime() + outMillis <= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                    //mdayTime 推移 outMillis后落在了上午的时间
                    return new Date(mdayTime.getTime() + outMillis);
                } else if (mdayTime.getTime() + outMillis + amRestBetweenMills <= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                    //mdayTime 推移 outMillis后落在了下午的时间
                    return new Date(mdayTime.getTime() + outMillis + amRestBetweenMills);
                } else {
                    //需要延后一天 将剩余时间减去当天早上的跟下午完整的工作时间
                    outMillis -= ((DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmToTime()), "yyyy-MM-dd HH:mm").getTime() - mdayTime.getTime()) + pmBetweenMills);
                    outDays += 1;
                    day = days.get((int) outDays);
                    //并且跨天后的开始时间调整为下一天的工作时间开始时间
                    mdayTime = DateUtil.parse(day.getDateStr() + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm");
                    //因为outMillis不可能再次跨天，所以只需要判断是否上午跟下午即可
                    if (mdayTime.getTime() + outMillis <= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                        //上午的时间
                        return new Date(mdayTime.getTime() + outMillis);
                    } else {
                        //下午的时间
                        return new Date(mdayTime.getTime() + outMillis + amRestBetweenMills);
                    }
                }
            } else {
                //mdayTime落在了下午
                if (mdayTime.getTime() + outMillis <= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                    //mdayTime 推移 outMillis后落在了下午的时间
                    return new Date(mdayTime.getTime() + outMillis);
                } else {
                    //需要延后一天 将剩余时间减去当天下午的工作时间
                    outMillis -= (DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmToTime()), "yyyy-MM-dd HH:mm").getTime() - mdayTime.getTime());
                    outDays += 1;
                    day = days.get((int) outDays);
                    //并且跨天后的开始时间调整为下一天的工作时间开始时间
                    mdayTime = DateUtil.parse(day.getDateStr() + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm");
                    //因为outMillis不可能再次跨天，所以只需要判断是否上午跟下午即可
                    if (mdayTime.getTime() + outMillis <= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                        //上午的时间
                        return new Date(mdayTime.getTime() + outMillis);
                    } else {
                        //下午的时间
                        return new Date(mdayTime.getTime() + outMillis + amRestBetweenMills);
                    }
                }
            }
        } else {
            if (mdayTime.getTime() + outMillis > DateUtil.parse(StrUtil.format("{} 23:59:59.999", day.getDateStr()), "yyyy-MM-dd HH:mm:ss.SSS").getTime()) {
                //超出时间截掉当前的
                outMillis -= (DateUtil.parse(StrUtil.format("{} 23:59:59.999", day.getDateStr()), "yyyy-MM-dd HH:mm:ss.SSS").getTime() - mdayTime.getTime());
                //需要延后一天
                outDays += 1;
                day = days.get((int) outDays);
                //开始时间就是下一个工作日的开始时间
                mdayTime = DateUtil.parse(StrUtil.format("{} 00:00:00", day.getDateStr()), "yyyy-MM-dd HH:mm:ss");
            }
            return new Date(mdayTime.getTime() + outMillis);
        }
    }

    @Override
    public List<Date> generateWarningTime(ConfigProjectReview cfg, Date penaltyEndTime) {

        Boolean excludeHolidaysFlag = cfg.getExcludeHolidaysFlag(); //是否排除节假日
        Boolean excludeWeekendsFlag = cfg.getExcludeWeekendsFlag(); //是否排除周末
        Boolean excludeNonWorkingHoursFlag = cfg.getExcludeNonWorkingHoursFlag(); //是否排除非工作时间
        String warningBeforePenalty = cfg.getWarningBeforePenalty();
        List<Double> warningBeforePenaltys = StrUtil.split(warningBeforePenalty, ",").stream().map(str -> Double.parseDouble(str.trim())).collect(Collectors.toList());
        DictDateOfWorkTime workTime = new LambdaQueryChainWrapper<>(dictDateOfWorkTimeMapper)
                .gt(DictDateOfWorkTime::getId, 0)
                .last("limit 1")
                .one();
        long amBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getAmToTime(), "HH:mm"), DateUtil.parse(workTime.getAmFromTime(), "HH:mm"));
        long pmBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getPmToTime(), "HH:mm"), DateUtil.parse(workTime.getPmFromTime(), "HH:mm"));
        long amRestBetweenMills = DateUtil.betweenMs(DateUtil.parse(workTime.getPmFromTime(), "HH:mm"), DateUtil.parse(workTime.getAmToTime(), "HH:mm"));
        long dayBetweenMills = amBetweenMills + pmBetweenMills;
        String penaltyEndDay = DateUtil.format(penaltyEndTime, "yyyy-MM-dd");
        List<DictDateOfHolidays> days = new LambdaQueryChainWrapper<>(dictDateOfHolidaysMapper)
                .le(DictDateOfHolidays::getDateInfo, DateUtil.parse(penaltyEndDay, "yyyy-MM-dd")) //排除时分秒
                .eq(Boolean.TRUE.equals(excludeHolidaysFlag), DictDateOfHolidays::getHolidaysFlag, 0)
                .eq(Boolean.TRUE.equals(excludeWeekendsFlag), DictDateOfHolidays::getWorkingDayFlag, 1)
                .orderByDesc(DictDateOfHolidays::getDateInfo) //往前推移的话用倒序排列
                .list();
        if (CollectionUtils.isEmpty(days)) {
            throw new CustomException(StrUtil.format("未配置日期信息，无法计算预警单时间，penaltyEndDay: {}", penaltyEndDay));
        }
        String penaltyEndHm = DateUtil.format(penaltyEndTime, "HH:mm");
        //获取一天的时长
        Long dayTotalMillis = 1000 * 60 * 60 * 24L;
        if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
            //如果是排除非工作时间的情况
            dayTotalMillis = dayBetweenMills;
        }
        List<Date> warningTimes = new ArrayList<>();
        String penaltyUnit = cfg.getPenaltyPeriodUnit();
        long penaltyUnitVal = parseUnit(penaltyUnit, cfg.getExcludeNonWorkingHoursFlag(), workTime);
        for (Double beforePenalty : warningBeforePenaltys) {
            Long totalBeforeMillis = Double.valueOf(beforePenalty * penaltyUnitVal).longValue();
            long outDays = totalBeforeMillis / dayTotalMillis;
            long outMillis = totalBeforeMillis % dayTotalMillis;
            if (outDays + 1 >= days.size()) {
                //有些情况还需要在超出范围内再延后一天
                throw new CustomException(StrUtil.format("预警单预警周期超出了维护的时间，无法计算预警单时间，outDays: {}, days.size: {}", outDays + 1, days.size()));
            }
            DictDateOfHolidays day = days.get((int) outDays);
            //跨天后的时间开始时间
            Date mdayTime = DateUtil.parse(day.getDateStr() + " " + penaltyEndHm, "yyyy-MM-dd HH:mm");
            if (Boolean.TRUE.equals(excludeNonWorkingHoursFlag)) {
                if (mdayTime.getTime() < DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmToTime()), "yyyy-MM-dd HH:mm").getTime()) {
                    //mdayTime落在了上午
                    if (mdayTime.getTime() - outMillis >= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmFromTime()), "yyyy-MM-dd HH:mm").getTime()) {
                        //mdayTime 推移 outMillis后落在了上午的时间
                        warningTimes.add(new Date(mdayTime.getTime() - outMillis));
                    } else {
                        //需要往前延一天 将剩余时间减去当天早上的工作时间
                        outMillis -= (mdayTime.getTime() - DateUtil.parse(day.getDateStr() + " " + workTime.getAmFromTime(), "yyyy-MM-dd HH:mm").getTime());
                        outDays += 1;
                        day = days.get((int) outDays);
                        //并且跨天后的开始时间调整为下一天的工作时间的结束时间
                        mdayTime = DateUtil.parse(day.getDateStr() + " " + workTime.getPmToTime(), "yyyy-MM-dd HH:mm");
                        //因为outMillis不可能再次跨天，所以只需要判断是否上午跟下午即可
                        if (mdayTime.getTime() - outMillis >= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmFromTime()), "yyyy-MM-dd HH:mm").getTime()) {
                            //下午的时间
                            warningTimes.add(new Date(mdayTime.getTime() - outMillis));
                        } else {
                            //上午的时间
                            warningTimes.add(new Date(mdayTime.getTime() - outMillis - amRestBetweenMills));
                        }
                    }
                } else {
                    //mdayTime落在了下午
                    if (mdayTime.getTime() - outMillis >= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmFromTime()), "yyyy-MM-dd HH:mm").getTime()) {
                        //mdayTime - outMillis 还是在下午
                        warningTimes.add(new Date(mdayTime.getTime() - outMillis));
                    } else if (mdayTime.getTime() - outMillis - amRestBetweenMills >= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getAmFromTime()), "yyyy-MM-dd HH:mm").getTime()) {
                        //mdayTime - outMillis 落在了上午
                        warningTimes.add(new Date(mdayTime.getTime() - outMillis - amRestBetweenMills));
                    } else {
                        //需要往前再延一天 将剩余时间减去当天下午的跟早上完整的工作时间
                        outMillis -= ((mdayTime.getTime() - DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmFromTime()), "yyyy-MM-dd HH:mm").getTime()) + amBetweenMills);
                        outDays += 1;
                        day = days.get((int) outDays);
                        //并且跨天后的开始时间调整为下一天的工作时间结束时间
                        mdayTime = DateUtil.parse(day.getDateStr() + " " + workTime.getPmToTime(), "yyyy-MM-dd HH:mm");
                        //因为outMillis不可能再次跨天，所以只需要判断是否上午跟下午即可
                        if (mdayTime.getTime() - outMillis >= DateUtil.parse(StrUtil.format("{} {}", day.getDateStr(), workTime.getPmFromTime()), "yyyy-MM-dd HH:mm").getTime()) {
                            //下午的时间
                            warningTimes.add(new Date(mdayTime.getTime() - outMillis));
                        } else {
                            //上午的时间
                            warningTimes.add(new Date(mdayTime.getTime() - outMillis - amRestBetweenMills));
                        }
                    }
                }
            } else {
                if (mdayTime.getTime() - outMillis < DateUtil.parse(StrUtil.format("{} 00:00:00", day.getDateStr()), "yyyy-MM-dd HH:mm:ss").getTime()) {
                    //超出时间截掉当前的
                    outMillis -= (mdayTime.getTime() - DateUtil.parse(StrUtil.format("{} 00:00:00", day.getDateStr()), "yyyy-MM-dd HH:mm:ss").getTime());
                    //需要往前延一天
                    outDays += 1;
                    day = days.get((int) outDays);
                    //开始时间就是下一个工作日的结束时间
                    mdayTime = DateUtil.parse(StrUtil.format("{} 23:59:59.999", day.getDateStr()), "yyyy-MM-dd HH:mm:ss.SSS");
                }
                warningTimes.add(new Date(mdayTime.getTime() - outMillis));
            }
        }
        return warningTimes;
    }

    public void destroyEarlyWarningAndPenaltyMessage(Long businessId, String businessTable) {
        if (businessId == null || StrUtil.isBlank(businessTable)) {
            throw new CustomException(StrUtil.format("销毁预警单和罚款单消息时，businessId或businessTable为空，无法执行销毁操作。businessId: {}, businessTable: {}", businessId, businessTable));
        }
        new LambdaUpdateChainWrapper<>(earlyWarningAndPenaltyMessagesMapper)
                .eq(EarlyWarningAndPenaltyMessages::getBusinessId, businessId)
                .eq(EarlyWarningAndPenaltyMessages::getBusinessTable, businessTable)
                .eq(EarlyWarningAndPenaltyMessages::getSendFlag, false)
                .set(EarlyWarningAndPenaltyMessages::getIsDeleted, 1)
                .set(EarlyWarningAndPenaltyMessages::getUpdateTime, new Date())
                .update();
    }

    @Override
    public void updateEarlyWarningAndPenaltyPerson(Long businessId, String businessTable, Long newPerson) {
        if (businessId == null || StrUtil.isBlank(businessTable) || newPerson == null) {
            throw new CustomException(StrUtil.format("修改预警单和罚款单消息人员，businessId或businessTable或newPerson为空，无法执行修改操作。businessId: {}, businessTable: {}, newPerson: {}", businessId, businessTable, newPerson));
        }
        new LambdaUpdateChainWrapper<>(earlyWarningAndPenaltyMessagesMapper)
                .eq(EarlyWarningAndPenaltyMessages::getBusinessId, businessId)
                .eq(EarlyWarningAndPenaltyMessages::getBusinessTable, businessTable)
                .eq(EarlyWarningAndPenaltyMessages::getSendFlag, false)
                .set(EarlyWarningAndPenaltyMessages::getPenaltyPersonId, newPerson)
                .set(EarlyWarningAndPenaltyMessages::getUpdateTime, new Date())
                .update();
    }

    public List<EarlyWarningAndPenaltyMessages> cronFindNotSendEarlyWarningOrPenaltyMessages(String penaltyCategory, Date now) {
        return new LambdaQueryChainWrapper<>(earlyWarningAndPenaltyMessagesMapper)
                .le(EarlyWarningAndPenaltyMessages::getSendTime, now)
                .eq(EarlyWarningAndPenaltyMessages::getIsDeleted, 0)
                .eq(EarlyWarningAndPenaltyMessages::getSendFlag, false)
                .eq(StrUtil.isNotBlank(penaltyCategory), EarlyWarningAndPenaltyMessages::getPenaltyCategory, penaltyCategory)
                .orderByAsc(EarlyWarningAndPenaltyMessages::getSendTime)
                .list();
    }

    public List<EarlyWarningAndPenaltyMessages> cronFindNotSendEarlyWarningMessagesByPenaltyId(Long penaltyId) {
        return new LambdaQueryChainWrapper<>(earlyWarningAndPenaltyMessagesMapper)
                .le(EarlyWarningAndPenaltyMessages::getPenaltyEarlyWarningAndPenaltyMessagesId, penaltyId)
                .eq(EarlyWarningAndPenaltyMessages::getIsDeleted, 0)
                .eq(EarlyWarningAndPenaltyMessages::getSendFlag, false)
                .orderByAsc(EarlyWarningAndPenaltyMessages::getSendTime)
                .list();
    }

    public void changeEarlyWarningAndPenaltyMessagesSendFlag(List<Long> earlyWarningAndPenaltyMessagesIds, Boolean flag) {
        if (CollUtil.isEmpty(earlyWarningAndPenaltyMessagesIds)) {
            return;
        }
        new LambdaUpdateChainWrapper<>(earlyWarningAndPenaltyMessagesMapper)
                .in(EarlyWarningAndPenaltyMessages::getEarlyWarningAndPenaltyMessagesId, earlyWarningAndPenaltyMessagesIds)
                .set(EarlyWarningAndPenaltyMessages::getSendFlag, flag)
                .set(EarlyWarningAndPenaltyMessages::getUpdateTime, new Date())
                .update();
    }
}
