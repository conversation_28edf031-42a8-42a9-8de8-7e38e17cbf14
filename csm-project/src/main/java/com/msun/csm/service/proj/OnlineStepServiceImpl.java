package com.msun.csm.service.proj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums.ONLINE;
import static com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums.PREPARING;
import static com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums.RESEARCHING;
import static com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums.SETTLED;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_ONLINE;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_ON_SITE;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_SURVEY;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_TEST;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_ONLINE;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_ON_SITE;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_SURVEY;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_TEST;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.error.BaseErrorCode;
import com.msun.core.component.implementation.api.androidout.MsunMiddleAggregatePatientApi;
import com.msun.core.component.implementation.api.androidout.dto.PatMergeConfigSaveDTO;
import com.msun.core.component.implementation.api.imsp.ImspApi;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertDTO;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertGeneralDTO;
import com.msun.core.component.implementation.api.imsp.dto.DataTransDto;
import com.msun.core.component.implementation.api.imsp.dto.PasswordDTO;
import com.msun.core.component.implementation.api.imsp.dto.SendMessageValueDto;
import com.msun.core.component.implementation.api.port.HospitalApi;
import com.msun.core.component.implementation.api.port.dto.PortTestDTO;
import com.msun.core.component.implementation.api.test.ImplementationTestApi;
import com.msun.core.component.implementation.api.test.dto.TestDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.config.ProxyNetworkConfig;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.OpenStatusEnum;
import com.msun.csm.common.enums.WorkStatusEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectUpgradationTypeEnums;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.comm.HzzDeliverHospital;
import com.msun.csm.dao.entity.comm.HzzDeliverPublic;
import com.msun.csm.dao.entity.comm.HzznDeliver;
import com.msun.csm.dao.entity.comm.HzznSyncResult;
import com.msun.csm.dao.entity.config.ConfigOnlineStep;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductVsEmpower;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalOnlineDetail;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOnlineStep;
import com.msun.csm.dao.entity.proj.ProjOnlineStepDetail;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProductArrangeRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSyncApiLogs;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.config.ConfigOnlineStepMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsEmpowerMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspProjectMapper;
import com.msun.csm.dao.mapper.proj.ProjAutocheckInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalOnlineDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductArrangeRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSyncApiLogsMapper;
import com.msun.csm.dao.mapper.proj.SysCscVsCustomerMapper;
import com.msun.csm.dao.mapper.report.ReportCustomInfoMapper;
import com.msun.csm.dao.mapper.rule.RuleProductRuleConfigMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.exception.DbOperationException;
import com.msun.csm.feign.client.knowledge.KnowledgeFeignClient;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunwei.req.SchemaGrantReq;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.feign.entity.yunying.req.NodeBaseData;
import com.msun.csm.feign.entity.yunying.req.ProductReq;
import com.msun.csm.feign.entity.yunying.req.UpdateNodeStatusDTO;
import com.msun.csm.model.convert.ProjOnlineStepConvert;
import com.msun.csm.model.convert.ProjOnlineStepDetailConvert;
import com.msun.csm.model.dto.AutoCheckForTask;
import com.msun.csm.model.dto.HospitalOnlineDTO;
import com.msun.csm.model.dto.HospitalOnlinePublishedSystemDTO;
import com.msun.csm.model.dto.HospitalOnlineToDetailDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.ProjOnlineStepDetailDTO;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.imsp.SysLoginUser;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.req.project.ProjectOnlineBeforeReq;
import com.msun.csm.model.req.project.SysCscVsCustomerEntity;
import com.msun.csm.model.vo.ProjOnlineStepData;
import com.msun.csm.model.vo.ProjOnlineStepSuccessVO;
import com.msun.csm.model.vo.ProjOnlineStepVO;
import com.msun.csm.model.vo.ProjProjectInfoVO;
import com.msun.csm.model.vo.ProjectOnlineToHospitalVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonSearchCloudDbService;
import com.msun.csm.service.common.HzzDeliverHospitalService;
import com.msun.csm.service.common.HzzDeliverPublicService;
import com.msun.csm.service.common.HzznDeliverService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderEnvService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalYunweiService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.util.CustomYunweiSignUtil;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.HttpClientUtils;
import com.msun.csm.util.HttpUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/22/10:29
 */
@Service
@Slf4j
public class OnlineStepServiceImpl implements OnlineStepService {
    @Resource
    ProjMilestoneInfoService projMilestoneInfoService;
    @Resource
    private ProjOnlineStepMapper projOnlineStepMapper;
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;
    @Resource
    private ProjOnlineStepDetailMapper onlineStepDetailMapper;
    @Resource
    private ConfigOnlineStepMapper configOnlineStepMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;
    @Resource
    private ProjOnlineStepConvert projOnlineStepConvert;
    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Resource
    private ImplementationTestApi testApi;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private ProjOnlineStepDetailConvert stepDetailConvert;
    @Resource
    private UserHelper userHelper;
    @Resource
    private ProjHospitalOnlineDetailMapper projHospitalOnlineDetailMapper;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Resource
    @Lazy
    private ImspApi imspApi;
    @Resource
    private ProjHospitalInfoService hospitalInfoService;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Resource
    private DictProductMapper dictProductMapper;
    @Resource
    private HospitalApi hospitalApi;
    @Value("${widthTable.url}")
    private String widthTableUrl;
    @Value("${chis.systemId}")
    private String chisSystemId;
    @Value("${chis.secret}")
    private String chisSecret;
    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Resource
    @Lazy
    private ProjSyncApiLogsMapper syncApiLogsMapper;
    @Resource
    @Lazy
    private YunyingFeignClient yunyingFeignClient;
    @Resource
    @Lazy
    private ProjOrderInfoMapper orderInfoMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private ProjOrderProductMapper projOrderProductMapper;
    @Resource
    private ProjContractInfoMapper contractInfoMapper;
    @Resource
    private ProjHospitalOnlineDetailService procedureOrderProductService;
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    @Resource
    private HzznDeliverService hzznDeliverService;
    @Resource
    private HzzDeliverHospitalService hzzDeliverHospitalService;
    @Resource
    private HzzDeliverPublicService hzzDeliverPublicService;
    @Resource
    private ProjProductArrangeRecordMapper projProductArrangeRecordMapper;
    @Resource
    private AutoCheckService autoCheckService;
    @Resource
    private ProjAutocheckInfoMapper autocheckInfoMapper;
    @Resource
    private MsunMiddleAggregatePatientApi msunMiddleAggregatePatientApi;
    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;
    @Resource
    private RuleProductRuleConfigMapper ruleProductRuleConfigMapper;
    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjApplyOrderEnvService applyOrderEnvService;

    @Resource
    private SystemSettingApi systemSettingApi;
    @Resource
    private ProjProjectSettlementCheckMainService settlementCheckMainService;

    @Resource
    private SysOperLogService sysOperLogService;

    @Lazy
    @Resource
    private ProjApplyOrderHospitalService projApplyOrderHospitalService;

    @Lazy
    @Resource
    private CommonSearchCloudDbService commonSearchCloudDbService;

    @Lazy
    @Resource
    private ReportCustomInfoMapper reportCustomInfoMapper;
    @Autowired
    private OBSClientUtils oBSClientUtils;

    @Resource
    @Lazy
    private ProjApplyOrderHospitalYunweiService hospitalYunweiService;

    @Resource
    @Lazy
    private DictProductVsEmpowerMapper dictProductVsEmpowerMapper;

    @Resource
    @Lazy
    private KnowledgeFeignClient knowledgeFeignClient;

    @Lazy
    @Resource
    private ImspProjectMapper imspProjectMapper;

    @Lazy
    @Resource
    private SysCscVsCustomerMapper sysCscVsCustomerMapper;

    @Resource
    private SendMessageService sendMessageService;

    /**
     * 进入页面时，从配置表中获取上线步骤，存入项目步骤表中
     *
     * @param dto
     * @return
     */
    @Override
    public Result saveOnlineStep(ProjOnlineStepDTO dto) {
        // 1、前置条件 ：
        // 区域是否可用 region_flag
        int regionFlag = 0;
        // 单体是否可用 monomer_flag
        int monomerFlag = 0;
        // 新客户是否可用 new_project_flag
        int newProjectFlag = 0;
        // 首期是否可用 his_flag
        List<Integer> hisFlag = new ArrayList<>();
        hisFlag.add(-1);
        // 老换新是否可用 old_for_new_flag
        int oldForNewFlag = 0;
        // 电销是否可用 telesales_flag
        int telesalesFlag = 0;
        // 是否包含患者智能服务
        int hasPatient = 0;
        // 是否包含合理用药产品
        int hasReasonableMedication = 0;
        // 是否包含健康档案浏览器
        int hasHealthArchive = 0;
        // 2、判断项目属性
        // fixme 查询项目优化
        ProjProjectInfoDTO projProjectInfoDTO = new ProjProjectInfoDTO();
        projProjectInfoDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProjProjectInfoVO> projectInfo = projectInfoMapper.findProjectInfo(projProjectInfoDTO);
//        if (CollUtil.isNotEmpty(projProjectInfoDTO.getDataRange()) && (projProjectInfoDTO.getProjectMemberId() != null && projProjectInfoDTO.getProjectMemberId() > 0)) {
//            projectInfo.addAll(projectInfoMapper.findMemberProjectInfo(projProjectInfoDTO));
//            projectInfo.addAll(projectInfoMapper.findDataRangeProjectInfo(projProjectInfoDTO));
//        } else if (CollUtil.isNotEmpty(projProjectInfoDTO.getDataRange())) {
//            projectInfo.addAll(projectInfoMapper.findDataRangeProjectInfo(projProjectInfoDTO));
//        } else if (projProjectInfoDTO.getProjectMemberId() != null && projProjectInfoDTO.getProjectMemberId() > 0) {
//            projectInfo.addAll(projectInfoMapper.findMemberProjectInfo(projProjectInfoDTO));
//        } else {
//            projectInfo.addAll(projectInfoMapper.findMemberProjectInfo(projProjectInfoDTO));
//        }
//        //去重并从大到小排序
//        projectInfo = projectInfo.stream()
//                .collect(Collectors.toMap(
//                        ProjProjectInfoVO::getProjectInfoId, // 使用 projectInfoId 作为键
//                        vo -> vo,                           // 保留当前对象作为值
//                        (existing, replacement) -> existing // 如果有重复，保留第一个
//                )).values().stream()
//                .sorted(Comparator.comparing(ProjProjectInfoVO::getProjectNumber).reversed())
//                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(projectInfo)) {
            newProjectFlag = projectInfo.get(0).getUpgradationType().equals(ProjectUpgradationTypeEnums.XKH.getCode())
                    ? 1 : 0;
            oldForNewFlag = projectInfo.get(0).getUpgradationType().equals(ProjectUpgradationTypeEnums.LHX.getCode())
                    ? 1 : 0;
            monomerFlag = projectInfo.get(0).getProjectType().equals(ProjectTypeEnums.SINGLE.getCode()) ? 1 : 0;
            regionFlag = projectInfo.get(0).getProjectType().equals(ProjectTypeEnums.REGION.getCode()) ? 1 : 0;
            if (projectInfo.get(0).getHisFlag() == 1) {
                hisFlag.add(1);
            } else {
                hisFlag.add(0);
            }
            // 患者智能
            Integer count = projOnlineStepMapper.hasPatient(projectInfo.get(0).getProjectInfoId(),
                    projectInfo.get(0).getOrderInfoId(), 1);
            hasPatient = count == 0 ? 0 : 1;
            // 是否包含合理用药产品
            Integer countHasReasonableMedication =
                    projOnlineStepMapper.hasPatient(projectInfo.get(0).getProjectInfoId(),
                            projectInfo.get(0).getOrderInfoId(), 2);
            hasReasonableMedication = countHasReasonableMedication == 0 ? 0 : 1;
            // 是否包含健康档案浏览器
            Integer countHasHealthArchive = projOnlineStepMapper.hasPatient(projectInfo.get(0).getProjectInfoId(),
                    projectInfo.get(0).getOrderInfoId(), 3);
            hasHealthArchive = countHasHealthArchive == 0 ? 0 : 1;
        }
        List<Integer> upgradationType = new ArrayList<>();
        upgradationType.add(-1);
        if (newProjectFlag == 1) {
            upgradationType.add(ProjectUpgradationTypeEnums.XKH.getCode());
        } else if (oldForNewFlag == 1) {
            upgradationType.add(ProjectUpgradationTypeEnums.LHX.getCode());
        }
        // 【8.26】 新增需求：支持分院模式。分院模式下 确认上线步骤走 区域的
        if (projectInfoService.isBranchHospital(dto.getProjectInfoId())) {
            regionFlag = 1;
            monomerFlag = 0;
            hisFlag.add(1);
        }
        // 3、 根据项目信息查询 确认上线步骤配置表
        List<ConfigOnlineStep> configOnlineSteps = configOnlineStepMapper.selectList(
                new QueryWrapper<ConfigOnlineStep>()
                        .eq(regionFlag == 1, "region_flag", regionFlag)
                        .eq(monomerFlag == 1, "monomer_flag", monomerFlag)
                        .in(newProjectFlag == 1, "upgradation_type", upgradationType)
                        .in(oldForNewFlag == 1, "upgradation_type", upgradationType)
                        .in("his_flag", hisFlag)
                        .eq(telesalesFlag == 1, "telesales_flag", telesalesFlag)
                        .ne(hasPatient == 0, "online_step_code", "old_hzzn_stop")
                        .ne(hasPatient == 0, "online_step_code", "new_hzzn_start")
                        .ne(hasReasonableMedication == 0, "online_step_code", "reasonable_medication_records")
                        .ne(hasHealthArchive == 0, "online_step_code", "browse_health_profile")
        );
        // 3.1 当包含患者智能服务时，单独查询并加入到第一个查询的结果集中
        if (hasPatient == 1) {
            List<ConfigOnlineStep> configOnlineSteps2 = configOnlineStepMapper.selectList(
                    new QueryWrapper<ConfigOnlineStep>()
                            .eq(hasPatient == 1, "online_step_code", "old_hzzn_stop")
                            .eq(hasPatient == 1, "online_step_code", "new_hzzn_start")
            );
            configOnlineSteps.addAll(configOnlineSteps2);
        }
        // 3.2 当包含合理用药时，单独查询并加入到第一个查询的结果集中
        if (hasReasonableMedication == 1) {
            List<ConfigOnlineStep> configOnlineSteps3 = configOnlineStepMapper.selectList(
                    new QueryWrapper<ConfigOnlineStep>()
                            .eq(hasReasonableMedication == 1, "online_step_code", "reasonable_medication_records")
            );
            configOnlineSteps.addAll(configOnlineSteps3);
        }
        // 3.3 当包含健康档案浏览器时，单独查询并加入到第一个查询的结果集中
        if (hasHealthArchive == 1) {
            List<ConfigOnlineStep> configOnlineSteps4 = configOnlineStepMapper.selectList(
                    new QueryWrapper<ConfigOnlineStep>()
                            .eq(hasHealthArchive == 1, "online_step_code", "browse_health_profile")
            );
            configOnlineSteps.addAll(configOnlineSteps4);
        }
        // 4、查询出的步骤信息存入项目上线步骤中
        List<ProjOnlineStep> list = intoProjOnlineStep(configOnlineSteps, dto);
        // 5、查询当前步骤是否存在，已存在则跳过，不存在则新增
        for (ProjOnlineStep po : list) {
            ProjOnlineStep projOnlineStep = projOnlineStepMapper.selectOne(new QueryWrapper<ProjOnlineStep>()
                    .eq("custom_info_id", po.getCustomInfoId())
                    .eq("project_info_id", po.getProjectInfoId())
                    .eq("config_online_step_id", po.getConfigOnlineStepId())
            );
            if (ObjectUtil.isNotEmpty(projOnlineStep)) {
                projOnlineStepMapper.update(po, new QueryWrapper<ProjOnlineStep>().eq("proj_online_step_id",
                        projOnlineStep.getProjOnlineStepId()));
                continue;
            } else {
                projOnlineStepMapper.insert(po);
            }
        }
        return Result.success();
    }

    /**
     * 项目上线步骤参数拼装
     *
     * @param configOnlineSteps
     * @param dto
     * @return
     */
    private List<ProjOnlineStep> intoProjOnlineStep(List<ConfigOnlineStep> configOnlineSteps, ProjOnlineStepDTO dto) {
        List<ProjOnlineStep> list = new ArrayList<>();
        for (ConfigOnlineStep configOnlineStep : configOnlineSteps) {
            ProjOnlineStep po = new ProjOnlineStep();
            po.setProjOnlineStepId(SnowFlakeUtil.getId());
            po.setCustomInfoId(dto.getCustomInfoId());
            po.setProjectInfoId(dto.getProjectInfoId());
            po.setConfigOnlineStepId(configOnlineStep.getConfigOnlineStepId());
            po.setPid(configOnlineStep.getPid());
            po.setStatus(0);
            list.add(po);
        }
        log.info("项目上线步骤信息添加：{}", JSONUtil.toJsonStr(list));
        return list;
    }

    /**
     * 查询项目上线步骤信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjOnlineStepData> selectOnlineStepList(ProjOnlineStepDTO dto) {
        ProjOnlineStepData projOnlineStepData = new ProjOnlineStepData();
        // 查询是否上线
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().
                eq("project_info_id", dto.getProjectInfoId()).
                eq("milestone_node_code", MilestoneNodeEnum.PROJECT_LAUNCH.getCode()).eq("invalid_flag",
                        NumberEnum.NO_0.num()));
        Boolean isOnline = false;
        if (projMilestoneInfo.getMilestoneStatus() != null && projMilestoneInfo.getMilestoneStatus() == 1) {
            isOnline = true;
        }
        projOnlineStepData.setIsOnline(isOnline);
        List<ProjOnlineStepVO> projOnlineStepVOS = projOnlineStepMapper.selectOnlineStepList(dto);
        // 查询项目类型、查询 实施地老系统的ip和端口
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(dto.getProjectInfoId());
        projOnlineStepData.setUpgradationType(projProjectInfo.getUpgradationType());
        if (projProjectInfo.getUpgradationType().equals(ProjectUpgradationTypeEnums.LHX.getCode())) {
            // 查询实施地老系统的ip和端口
            ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectById(dto.getCustomInfoId());
            projOnlineStepData.setIp(projCustomInfo.getIp());
            projOnlineStepData.setPort(projCustomInfo.getPort());
        }
        // 区分父子级
        List<ProjOnlineStepVO> projOnlineParentStepVOS = new ArrayList<>();
        // 处理步骤执行时间问题 在新增时同步创建了  创建时间、创建人、更新时间、更新人。但前端展示时 执行时间和执行人用的更新时间。需要处理
        // 当更新时间和创建时间一致时，更新时间和更新人设置为空
        if (CollectionUtil.isNotEmpty(projOnlineStepVOS)) {
            List<Long> listAll = projOnlineStepVOS.stream().map(ProjOnlineStepVO::getConfigOnlineStepId).collect(Collectors.toList());
            for (ProjOnlineStepVO projOnlineStepVO : projOnlineStepVOS) {
                // 改为按照状态处理。 已执行的展示执行人及执行时间
                if (projOnlineStepVO.getStatus() != null && projOnlineStepVO.getStatus() != 1) {
                    projOnlineStepVO.setUpdateTime(null);
                    projOnlineStepVO.setUserName(null);
                    projOnlineStepVO.setUpdaterId(null);
                }
                // 当老系统通停止长期医嘱时进行连接替换
                String startLongOrder = "startLongOrder";
                if (startLongOrder.equals(projOnlineStepVO.getOnlineStepCode())) {
                    String url = this.getUrl(projProjectInfo, projOnlineStepVO.getLinkAddress(), projOnlineStepVOS);
                    projOnlineStepVO.setLinkAddress(url);
                }
                if ("0".equals(projOnlineStepVO.getPid()) || !listAll.contains(projOnlineStepVO.getPid())) {
                    projOnlineParentStepVOS.add(projOnlineStepVO);
                }
            }
            for (ProjOnlineStepVO parentVo : projOnlineParentStepVOS) {
                List<ProjOnlineStepVO> childrenList = projOnlineStepVOS.stream().filter(x -> x.getPid() != null && x.getPid()
                        .equals(parentVo.getConfigOnlineStepId())).collect(Collectors.toList());
                parentVo.setChildrenList(childrenList);

            }
        }
        projOnlineStepData.setVoList(projOnlineParentStepVOS);
        return Result.success(projOnlineStepData);
    }

    /**
     * 获取连接地址
     *
     * @param projProjectInfo
     * @param linkAddress
     * @param projOnlineStepVOS
     * @return
     */
    private String getUrl(ProjProjectInfo projProjectInfo, String linkAddress,
                          List<ProjOnlineStepVO> projOnlineStepVOS) {
        String beginTime = "";
        List<ProjHospitalInfo> projHospitalInfos =
                projApplyOrderHospitalService.findHospitalInfo(projProjectInfo.getProjectInfoId());
        // 调用API时设置domain信息
        try {
            String sqlText = "SELECT TO_CHAR(min(insert_time), 'YYYY-MM-DD HH24:MI:SS') insert_time FROM zy"
                    + ".in_doc_order";
            String databaseName = "chis";
            ExecutorService executor = Executors.newSingleThreadExecutor();
            CompletableFuture<List<Map>> future = CompletableFuture.supplyAsync(
                    () -> commonSearchCloudDbService
                            .commonSearchCloudDb(projHospitalInfos.get(0), Map.class, sqlText, databaseName),
                    executor
            );
            List<Map> departmentCountData;
            try {
                // 设置超时时间为2秒
                departmentCountData = future.get(2, TimeUnit.SECONDS);
            } catch (Exception e) {
                // 超时或异常处理
                departmentCountData = null; // 或者返回默认值
            }
            executor.shutdown();
            if (CollectionUtil.isNotEmpty(departmentCountData) && departmentCountData.size() > 0) {
                beginTime = departmentCountData.get(0).get("insert_time").toString();
            }
        } catch (Exception e) {
            log.error("调用API时设置domain信息失败", e);
        }
        if (StringUtils.isNotEmpty(beginTime)) {
            return linkAddress.replace("#{beginTime}", beginTime);
        } else {
            // 查询IIS配置信息
            for (ProjOnlineStepVO projOnlineStepVO : projOnlineStepVOS) {
                // 关闭老IIS
                String startLongOrder = "old_his_stop_iis";
                if (projOnlineStepVO.getOnlineStepCode().equals(startLongOrder)) {
                    beginTime = DateUtil.format(projOnlineStepVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
                    return linkAddress.replace("#{beginTime}", beginTime);
                }
            }
        }
        return linkAddress;
    }


    /**
     * 修改项目上线信息表
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateOnlineStep(ProjOnlineStepDTO dto) {
        ProjOnlineStep projOnlineStep = projOnlineStepConvert.dto2Po(dto);
        projOnlineStep.setStatus(1);
        log.info("修改项目上线信息表：{}", JSONUtil.toJsonStr(projOnlineStep));
        projOnlineStepMapper.updateById(projOnlineStep);
        return Result.success();
    }

    /**
     * 判断步骤是否执行
     *
     * @param dto
     * @return
     */
    @Override
    public Result judgeStepExecute(ProjOnlineStepDTO dto) {
        // 根据传入的步骤id ，查询配置表的上级步骤  判断是否已经执行，当执行后，可继续执行当前步骤，未执行时 返回语句 “请先执行。。。”
        //1、 查询 传入的步骤id对应的配置信息表id
        ProjOnlineStep projOnlineStep = projOnlineStepMapper.selectById(dto.getProjOnlineStepId());
        // 2、查询对应配置表中 当前步骤的前置步骤信息数据
        ConfigOnlineStep configOnlineStep1 = configOnlineStepMapper.selectById(projOnlineStep.getConfigOnlineStepId());
        // 3、 转换需完成的前置步骤
        String[] split = configOnlineStep1.getPreviousOnlinePlanIdArray().split(",");
        List<Long> configStepIds = Arrays.stream(split).map(Long::valueOf).collect(Collectors.toList());
        // 根据配置步骤id 查询当前项目下的上线步骤数据是否都已经完成。当未完成时 记录下来提示到前端。当全部完成时 返回成功
        List<ProjOnlineStep> projOnlineSteps = projOnlineStepMapper.selectList(new QueryWrapper<ProjOnlineStep>()
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("custom_info_id", dto.getCustomInfoId())
                .in("config_online_step_id", configStepIds)
                .eq("status", 0)
        );
        if (CollectionUtil.isEmpty(projOnlineSteps)) {
            return Result.success();
        } else {
            List<Long> notFinishConfigStepIdList =
                    projOnlineSteps.stream().map(ProjOnlineStep::getConfigOnlineStepId).collect(Collectors.toList());
            List<ConfigOnlineStep> list = configOnlineStepMapper.selectList(new QueryWrapper<ConfigOnlineStep>()
                    .in("config_online_step_id", notFinishConfigStepIdList)
            );
            String str = "请完成前置步骤：";
            if (CollectionUtil.isNotEmpty(list)) {
                str += list.stream().map(ConfigOnlineStep::getOnlineStepName).collect(Collectors.toList()).stream().collect(Collectors.joining(","));
            }
            return Result.fail(str);
        }
    }

    /**
     * 测试交付平台连接到云健康接口
     *
     * @param dto
     * @return
     */
    @Override
    public Result testCsmToAPI(ProjOnlineStepDTO dto) {
        // 查询医院的信息
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProjHospitalInfo> projHospitalInfos = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        // 调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfos.get(0));
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 2.拼装提交参数
        TestDTO testDTO = new TestDTO();
        testDTO.setHisOrgId(projHospitalInfos.get(0).getOrgId());
        testDTO.setHospitalId(projHospitalInfos.get(0).getCloudHospitalId());
        List<TestDTO> datas = new ArrayList<>();
        datas.add(testDTO);
        BatchInsertDTO<TestDTO> batchInsertDTO = new BatchInsertDTO<>();
        batchInsertDTO.setData(datas);
        batchInsertDTO.setHisOrgId(projHospitalInfos.get(0).getOrgId());
        batchInsertDTO.setHospitalId(projHospitalInfos.get(0).getCloudHospitalId());
        try {
            return Result.success(testApi.testImspToAPI(batchInsertDTO));
        } catch (Exception e) {
            return Result.fail("检测失败");
        }

    }

    /**
     * 项目上线-- 查询医院上线信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ProjectOnlineToHospitalVO>> projectOnlineForShowHospital(ProjOnlineStepDetailDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            //默认查询已开通状态
            dto.setHospitalOpenStatus(NumberEnum.NO_21.num());
            List<ProjectOnlineToHospitalVO> voList = onlineStepDetailMapper.selectHospitalOnlineDetail(dto);
            return Result.success(new PageInfo<>(voList));
        });
    }

    /**
     * 根据主键列表 批量更新明细数据
     *
     * @param dtoList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public Result<ProjOnlineStepSuccessVO> updateProjOnlineStepDetail(List<ProjOnlineStepDetailDTO> dtoList) {
        //todo 过滤掉已经上线的医院数据
        List<ProjOnlineStepDetailDTO> notOnlineDtoList = dtoList;
        dtoList.stream().filter(vo -> ObjectUtil.isNotEmpty(vo.getOnlineStatus())
                && vo.getOnlineStatus() != 1).collect(Collectors.toList());
        // 可能存在已经执行过的医院数据，要进行数据区分，哪些是要新增的 ， 哪些是继续更新
        List<ProjOnlineStepDetail> addList = new ArrayList<>();
        List<ProjOnlineStepDetail> updateList = new ArrayList<>();
        for (ProjOnlineStepDetailDTO dto : notOnlineDtoList) {
            if (ObjectUtil.isNotEmpty(dto.getOnlineStepDetailId())) {
                ProjOnlineStepDetail stepDetail = stepDetailConvert.dto2Po(dto);
                stepDetail.setStepDetailStatus(1);
                stepDetail.setIsDeleted(0);
                stepDetail.setUpdaterId(-1L);
                stepDetail.setUpdateTime(new Date());
                updateList.add(stepDetail);
            } else {
                ProjOnlineStepDetail stepDetail = stepDetailConvert.dto2Po(dto);
                stepDetail.setOnlineStepDetailId(SnowFlakeUtil.getId());
                stepDetail.setStepDetailStatus(1);
                stepDetail.setIsDeleted(0);
                stepDetail.setCreaterId(-1L);
                stepDetail.setCreateTime(new Date());
                stepDetail.setUpdaterId(-1L);
                stepDetail.setUpdateTime(new Date());
                addList.add(stepDetail);
            }
        }
        if (CollectionUtil.isNotEmpty(addList)) {
            onlineStepDetailMapper.batchInsert(addList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            onlineStepDetailMapper.batchUpdate(updateList);
        }
        // 修改医院上线状态与其他涉及到上线时的数据
        updateHospitalOnlineStatus(notOnlineDtoList);
        // 修改自动测试平台 当前客户的状态（只有首期才会进行修改，需判断首期项目）
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(notOnlineDtoList.get(0).getProjectInfoId());
        // 医院上线30分钟限制取消。 首期项目需走此方法
        sendSysManageBatch(notOnlineDtoList, projProjectInfo);
        if (projProjectInfo.getHisFlag() == 1) {
            // 1. 客户首期项目上线时，判断为老体系的客户，状态更新状态为老换新
            //  实现思路：首期项目上线，直接项目ID、客户类型（固定为3老体系）执行
            //  调用原则：不影响主线上线流程
            try {
                reportCustomInfoMapper.updateByProjectInfoData(projProjectInfo);
            } catch (Exception e) {
                log.error("修改数据失败", e);
            }
            try {
                /*  2025-04-23 与测试平台商定无需传递task_id 只需传医院id+orgid即可 */
                for (ProjOnlineStepDetailDTO dto : dtoList) {
                    ProjHospitalInfo info = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
                    AutoCheckForTask autoCheckForTask = new AutoCheckForTask();
                    List<Long> taskIds = new ArrayList<>();
                    autoCheckForTask.setTaskIds(taskIds);
                    autoCheckForTask.setCloudHospitalId(info.getCloudHospitalId());
                    autoCheckForTask.setOrgId(info.getOrgId());
                    autoCheckService.taskClose(autoCheckForTask);
                }
            } catch (Exception e) {
                log.error("自动化测试任务终止失败");
            }
            // 首期上线后发消息到中层（当前部门经理） + 高层 + 交付系统管理员 + PMO + 项目经理
            this.sendMessageInfo(projProjectInfo);
        }
        // 拼装返回数据
        ProjOnlineStepSuccessVO projOnlineStepSuccessVO = resultDataInfo(notOnlineDtoList.get(0).getProjectInfoId());
        return Result.success(projOnlineStepSuccessVO);
    }

    /**
     * 解除医院限制
     *
     * @param dtoList
     * @param projProjectInfo
     */
    @Override
    public void sendSysManageBatchPublic(List<ProjOnlineStepDetailDTO> dtoList, ProjProjectInfo projProjectInfo) {
        sendSysManageBatch(dtoList, projProjectInfo);
    }

    /**
     * 医院上线30分钟限制解除
     *
     * @param dtoList
     */
    @Override
    public void sendSysManageBatch(List<ProjOnlineStepDetailDTO> dtoList, ProjProjectInfo projProjectInfo) {
        // TODDO : 根据项目查询项目下的所有菜单
        List<Long> arrayList = new ArrayList<>();
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectListByParamer(dtoList);
        if (CollectionUtil.isNotEmpty(hospitalInfoList)) {
            hospitalInfoList.stream().forEach(hospitalInfoMap -> {
                if (!arrayList.contains(hospitalInfoMap.getCloudHospitalId())) {
                    arrayList.add(hospitalInfoMap.getCloudHospitalId());
                }
            });
        }
        List<ProductInfo> productList = hospitalYunweiService.productOpened(projProjectInfo.getProjectInfoId());
        if (!(productList != null && productList.size() > 0)) {
            return;
        }
        List<DictProductVsEmpower> codeList = dictProductVsEmpowerMapper.selectList(new QueryWrapper<DictProductVsEmpower>().in(productList != null && productList.size() > 0, "order_product_id", productList.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList())));
        List<HospitalOnlinePublishedSystemDTO> publishedSystems = new ArrayList<>();

        if (codeList != null && codeList.size() > 0) {
            arrayList.forEach(hospitalInfo -> {
                for (DictProductVsEmpower dictProductVsEmpower : codeList) {
                    HospitalOnlinePublishedSystemDTO hospitalOnlinePublishedSystemDTO = new HospitalOnlinePublishedSystemDTO();
                    hospitalOnlinePublishedSystemDTO.setHospitalId(hospitalInfo);
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemCode(dictProductVsEmpower.getMsunHealthModuleCode());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemCommonName(dictProductVsEmpower.getMsunHealthModule());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemMenuCode(dictProductVsEmpower.getMsunHealthModuleCode());
                    hospitalOnlinePublishedSystemDTO.setPublishedSystemStandardName(dictProductVsEmpower.getMsunHealthModule());
                    publishedSystems.add(hospitalOnlinePublishedSystemDTO);
                }
            });
        }
        // 首期项目进行患者档案合并
        if (projProjectInfo.getHisFlag() == 1) {
            // 批量合并患者
            mergePatientCommPrivate(hospitalInfoList);
        }

        ProjHospitalInfo hospitalInfo = hospitalInfoList.get(NumberEnum.NO_0.num());
        extracted(hospitalInfo, arrayList, publishedSystems);
    }

    /**
     * 上线后解除30分钟
     * @param hospitalInfo
     * @param arrayList
     * @param publishedSystems
     */
    @Override
    public void extracted(ProjHospitalInfo hospitalInfo, List<Long> arrayList, List<HospitalOnlinePublishedSystemDTO> publishedSystems) {
        //调用系统管理接口
        HospitalOnlineDTO hospitalOnlineDTO = new HospitalOnlineDTO();
        hospitalOnlineDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        hospitalOnlineDTO.setOrgId(hospitalInfo.getOrgId());
        hospitalOnlineDTO.setHospitalIdList(arrayList);
        hospitalOnlineDTO.setPublishedSystems(publishedSystems);
        //设置调通参数
        HttpClientUtils clientUtils = new HttpClientUtils();
        Map<String, String> paramUser = new HashMap<>();
        paramUser.put("systemId", chisSystemId);
        paramUser.put("secret", chisSecret);
        paramUser.put("address", hospitalInfo.getCloudDomain());
        paramUser.put("methodUrl", "/msun-middle-base-common/api/hospital/hospitalOnline");
        paramUser.put("body", JSONObject.toJSONString(hospitalOnlineDTO));
        paramUser.put("hisOrgId", hospitalInfo.getOrgId().toString());
        paramUser.put("hospitalId", hospitalInfo.getCloudHospitalId().toString());
        try {
            //发送数据
            String cloudDomain = ProxyNetworkConfig.getProxyNetworkUrl();
            log.info("调用基础中台解除登录限制参数 :paramUser==========>" + paramUser);
            if ("prod".equals(activeProfiles)) {
                cloudDomain = ProxyNetworkConfig.getProxyNetworkUrl();
            } else if ("test".equals(activeProfiles)) {
                // 临时使用表里的。
                cloudDomain = hospitalInfo.getCloudDomain();
            } else {
                cloudDomain = hospitalInfo.getCloudDomain();
            }
            String result = clientUtils.post(paramUser, cloudDomain);
            if (ObjectUtil.isNotEmpty(result)) {
                log.info("调用基础中台解除登录限制返回的数据信息: {}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                // 返回结果
                String code = "0000";
                if (jsonObject.get("code").equals(code)) {
                    //调用成功
                    log.info("调用系统管理接口进行医院上线授权成功");
                } else {
                    //调用失败
                    log.error("调用系统管理接口发生异常,接口未返回数据");
                    //添加日志记录
                    addSyncApiLog("调用系统管理进行医院上线授权:" + result, hospitalInfo.getHospitalName());
                    throw new RuntimeException("调用系统管理接口发生异常,错误信息:" + jsonObject.get("msg"));
                }
            } else {
                //添加日志记录
                addSyncApiLog("调用系统管理进行医院上线授权:" + result, hospitalInfo.getHospitalName());
                //调用失败
                log.error("未返回结果");
                throw new RuntimeException("调用系统管理接口发生异常,接口未返回数据");
            }
            //打印基础中台返回的数据
            log.info("调研基础中台解除登录限制返回的数据信息: {}", result);
            //添加日志记录
            addSyncApiLog("调用系统管理进行医院上线授权:" + result, hospitalInfo.getHospitalName());
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new RuntimeException("调用系统管理接口进行医院上线授权失败");
        }
    }

    /**
     * 同步云健康用户到运维平台
     *
     * @param stepDTO
     * @return
     */
    @Override
    public Result syncSysUserToUnwed(ProjectOnlineBeforeReq stepDTO, SysUserVO currentUser) {

        ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectById(stepDTO.getCustomInfoId());
        if (projCustomInfo == null) {
            throw new RuntimeException("未找到客户信息");
        }

        if (currentUser == null) {
            currentUser = userHelper.getCurrentUser();
        }
        SysLoginUser user = new SysLoginUser();
        if (currentUser != null && currentUser.getSysUserId() != null) {
            user = imspProjectMapper.selectUserByNewUserId(currentUser.getSysUserId());
        }
        JSONObject json = new JSONObject();
        if (user != null && user.getUserId() != null) {
            json.put("userId", user.getUserId());
            json.put("name", user.getName());
        } else {
            json.put("userId", -2);
            json.put("name", "交付自动更新");
        }

        json.put("customerYunyingId", projCustomInfo.getYyCustomerId());
        log.error("======调用客户同步用户信息======{}", user);
        ResponseResult result = knowledgeFeignClient.downloadJfHosDeptUserInfo(json, CustomYunweiSignUtil.getAuthorization());
        log.info("======调用客户同步用户信息======{}", result);
        if (result.isSuccess()) {
            return Result.success();
        } else {
            return Result.fail(result.getMessage());
        }
    }

    /**
     * 获取云健康用户部门
     *
     * @param dto
     * @return
     */
    @Override
    public List<BaseIdNameResp> getUnwedDeptHospitalityInfoId(ProjectOnlineBeforeReq dto) {
        return projOnlineStepMapper.getUnwedDeptHospitalityInfoId(dto);
    }

    /**
     * 根据运营科室id查询团队下实施人员
     *
     * @param dto
     * @return
     */
    @Override
    public List<BaseIdNameResp> getUserListByDeptId(ProjectOnlineBeforeReq dto) {
        SysUserVO vo = userHelper.getCurrentUser();
        // 另外开启一个线程进行数据同步
        // 异步执行数据同步
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                syncData(dto, vo);
            } catch (Exception e) {
                log.error("数据同步过程中发生异常", e);
            }
        });
        return projOnlineStepMapper.getUserListByDeptId(dto);
    }

    /**
     * 同步运维平台云健康用户数据
     * @param dto
     * @param vo
     */
    private void syncData(ProjectOnlineBeforeReq dto,SysUserVO vo) {
        this.syncSysUserToUnwed(dto, vo);
    }

    /**
     * 获取运维平台角色
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> getUnwedRoles() {
        return projOnlineStepMapper.getUnwedRoles("");
    }

    /**
     * 设置转众阳角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateUnwedRolesByParam(ProjectOnlineBeforeReq dto) {
        // 科室id不能为空
        if (dto.getDeptId() == null) {
            return Result.fail("科室id不能为空");
        }
        // 没有责任角色时默认转众阳角色
        if (dto.getRoleId() == null) {
            List<BaseIdNameResp> roleList = projOnlineStepMapper.getUnwedRoles("SERVICE_APPROVE");
            if (!roleList.isEmpty()) {
                dto.setRoleId(roleList.get(0).getId());
            } else {
                return Result.fail("未查询到转众阳角色");
            }
        }
        // 没有人员时直接默认科室所有人
        if (dto.getUserId() == null) {
            // 没有人员时默认科室下所有人
            List<BaseIdNameResp> userList = projOnlineStepMapper.getUnwedUserByDeptId(dto);
            if (!userList.isEmpty()) {
                for (BaseIdNameResp user : userList) {
                    dto.setUserId(user.getId());
                    projOnlineStepMapper.updateRoles(dto);
                }
            }
        } else {
            projOnlineStepMapper.updateRoles(dto);
        }

        return Result.success();
    }

    /**
     * 开启运维:设置客户服务团队及专员/设置转众阳角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> updateUnwedDataByParam(ProjectOnlineBeforeReq dto) {

        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
        if (projProjectInfo == null) {
            throw new RuntimeException("未找到项目信息");
        }
        if (projProjectInfo.getHisFlag() != null && projProjectInfo.getHisFlag() != 1) {
            throw new RuntimeException("非首期项目无需操作此步骤");
        }
        if (projProjectInfo.getOnlineTime() != null && projProjectInfo.getProjectDeliverStatus() >= 5) {
            return Result.fail("项目已上线，请从运维平台操作");
        }

        // 设置转众阳角色
        this.updateUnwedRolesByParam(dto);

        // 设置客户服务团队及专员
        if (dto.getCustomerUserId() == null) {
            return Result.fail("请选择客服专员");
        }
        if (dto.getCustomerDeptId() == null) {
            return Result.fail("请选择客服团队");
        }
        SysDept dept = sysDeptMapper.selectYunYingId(dto.getCustomerDeptId());
        ProjCustomInfo customInfo = projCustomInfoMapper.selectById(dto.getCustomInfoId());
        // 查询老平台用户信息
        SysUser user = new SysUser();
        List<SysUser> userList = new ArrayList<>();
        List<SysUser> sysUserList = projOnlineStepMapper.selectListOldUserByParams(dto);
        if (sysUserList != null && !sysUserList.isEmpty()) {
            for (SysUser sysUser : sysUserList) {
                if (sysUser.getSysUserId().equals(dto.getCustomerUserId())) {
                    user = sysUser;
                } else {
                    userList.add(sysUser);
                }
            }
        }

        String cseHelpIds = "";
        String cseHelpNames = "";
        if (userList != null && !userList.isEmpty()) {
            List<String> cseHelpIdsList = userList.stream().map(SysUser::getUserYunyingId).collect(Collectors.toList());
            cseHelpIds = StringUtils.join(cseHelpIdsList, ",");
            List<String> cseHelpNameList = userList.stream().map(SysUser::getUserName).collect(Collectors.toList());
            cseHelpNames = StringUtils.join(cseHelpNameList, ",");
        }
        SysCscVsCustomerEntity entity = sysCscVsCustomerMapper.selectById(customInfo.getYyCustomerId());
        if (entity != null) {
            entity.setCscYunyingId(dept.getDeptYunyingId());
            entity.setCscName(dept.getDeptName());
            entity.setHisUpdateTime(new Timestamp(System.currentTimeMillis()));
            entity.setCseIds(user.getUserYunyingId());
            entity.setCseNames(user.getUserName());

            entity.setCseHelpIds(cseHelpIds);
            entity.setCseHelpNames(cseHelpNames);

            sysCscVsCustomerMapper.updateById(entity);
        } else {
            entity = new SysCscVsCustomerEntity();
            entity.setCustomerName(customInfo.getCustomName());
            entity.setCustomerYunyingId(customInfo.getYyCustomerId());
            entity.setCscYunyingId(dept.getDeptYunyingId());
            entity.setCscName(dept.getDeptName());
            entity.setHisCreateTime(new Timestamp(System.currentTimeMillis()));
            entity.setHisUpdateTime(new Timestamp(System.currentTimeMillis()));
            entity.setCseIds(user.getUserYunyingId());
            entity.setCseNames(user.getUserName());

            entity.setCseHelpIds(cseHelpIds);
            entity.setCseHelpNames(cseHelpNames);

            sysCscVsCustomerMapper.insert(entity);
        }

        return Result.success();
    }

    /**
     * 患者档案合并。
     *
     * @param hospitalInfoList
     */
    private void mergePatientCommPrivate(List<ProjHospitalInfo> hospitalInfoList) {
        List<String> hospitalIdList = new ArrayList<>();
        if (hospitalInfoList != null && hospitalInfoList.size() > 0) {
            for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                if (hospitalInfo.getCloudDomain() != null && hospitalIdList.contains(hospitalInfo.getCloudDomain())) {
                    hospitalIdList.add(hospitalInfo.getCloudDomain());
                    // 批量合并患者
                    mergePatientComm(hospitalInfo.getHospitalInfoId());
                }
            }
        }
    }

    /**
     * 修改医院上线状态与其他涉及到上线时的数据(运营平台、云健康系统管理)
     *
     * @param dtoList
     */
    private void updateHospitalOnlineStatus(List<ProjOnlineStepDetailDTO> dtoList) throws Exception {
        // 同步更新 医院明细表的上线状态
        for (ProjOnlineStepDetailDTO dto : dtoList) {
            HospitalOnlineToDetailDTO onlineToDetailDTO = HospitalOnlineToDetailDTO.builder()
                    .hospitalInfoId(dto.getHospitalInfoId())
                    .projectInfoId(dto.getProjectInfoId())
                    .customInfoId(dto.getCustomInfoId())
                    .onlineStatus(WorkStatusEnum.ONLINE.getCode())
                    .onlineTime(new Date())
                    .build();
            procedureOrderProductService.saveOrUpdateProjHospitalOnlineDetail(onlineToDetailDTO);
            try {
                ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(dto.getHospitalInfoId());
                if (hospitalInfo != null) {
                    hospitalInfo.setHospitalOpenStatus(31);
                    hospitalInfo.setOnlineTime(new Timestamp(System.currentTimeMillis()));
                    hospitalInfoMapper.updateById(hospitalInfo);
                }
            } catch (Exception e) {
                log.error("修改医院上线状态失败，errMsg={}", e.getMessage());
            }
        }
        // 修改上线步骤主表信息
        ProjOnlineStep projOnlineStep = new ProjOnlineStep();
        projOnlineStep.setProjOnlineStepId(dtoList.get(0).getProjOnlineStepId());
        projOnlineStep.setStatus(1);
        projOnlineStepMapper.updateById(projOnlineStep);
        // 判断是否全部医院上线，当全部医院上线时 更新项目表中上线状态和 运营平台工单状态为“上线”
        Long onlineStatus = projHospitalOnlineDetailMapper.selectCount(new QueryWrapper<ProjHospitalOnlineDetail>()
                .eq("online_status", 0)
                .eq("project_info_id", dtoList.get(0).getProjectInfoId())
        );
        if (onlineStatus == 0) {
            // 更新里程碑
            updateMilestoneByProjectInfoId(dtoList.get(0).getProjectInfoId());
            //云健康数据库权限收回
            updateSchemaGrant(dtoList.get(0).getProjectInfoId(), null, null, "1");
            // TODO 发送上线消息
            ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(
                    new QueryWrapper<ProjProjectInfo>().eq("project_info_id", dtoList.get(0).getProjectInfoId()));
            //获取工单信息
            ProjOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(projProjectInfo.getOrderInfoId());
            if (orderInfo == null) {
                throw new RuntimeException("修改项目自身状态-入驻阶段-同步运营平台节点数据后更新, 未查询到运营平台工单信息");
            }
            Integer type = projProjectInfo.getUpgradationType();
            String typeName;
            if (Integer.valueOf("1").equals(type)) {
                typeName = "老换新";
            } else {
                typeName = "新客户";
            }
            SysDept sysDept = sysDeptMapper.selectOne(
                    new QueryWrapper<SysDept>().eq("dept_yunying_id", projProjectInfo.getProjectTeamId()));
            SysUser sysUser = sysUserMapper.selectOne(
                    new QueryWrapper<SysUser>().eq("sys_user_id", projProjectInfo.getProjectLeaderId()));
            ProductInfoDTO productInfoDTO = new ProductInfoDTO();
            productInfoDTO.setProjectInfoId(projProjectInfo.getProjectInfoId());
            // 更新上线项目下产品状态为已上线
            projOrderProductMapper.updateBatchByProjectInfoId(productInfoDTO);
            // 已经全部上线，调用运营平台接口 更新工单状态; 交付平台的项目状态与时间
            // 判断是否全部医院上线，当全部医院上线时 更新项目表中上线状态和 运营平台工单状态为“上线”
            switch (orderInfo.getDeliveryOrderType()) {
                case 1:
                    // 软件
                    updateNodeStatus(dtoList.get(0).getProjectInfoId(), SOFTWARE_ONLINE);
                    break;
                case 8:
                    // 外采软件
                    updateNodeStatus(dtoList.get(0).getProjectInfoId(), PURCHASE_SOFTWARE_ONLINE);
                    break;
                default:
                    break;
            }
            //组装产品列表信息
            List<ProductInfo> allList = projOrderProductMapper.findAllList(productInfoDTO);
            List<ProductInfo> resList = allList.stream()
                    .filter(v -> !com.msun.csm.util.StringUtils.isEmpty(v.getProductName()))
                    .collect(Collectors.toList());
            // xxx项目（老换新）已派工，实施团队xxx，项目经理xxx，本次上线xx个产品，请您知晓！
            String content =
                    projProjectInfo.getProjectName() + "(" + typeName + ")" + "已上线，上线时间" + DateUtil.formatDateTime(new Date()) + "，实施团队"
                            + sysDept.getDeptName() + "，项目经理" + sysUser.getUserName() + "，本次上线" + resList.size()
                            + "个产品，请您知晓！";
            MessageParam messageParam = new MessageParam();
            messageParam.setProjectInfoId(dtoList.get(0).getProjectInfoId());
            messageParam.setContent(content);
            messageParam.setMessageTypeId(4002L);
            messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
//            sendMessageService.sendMessage(messageParam);
        }
    }

    /**
     * 拼装更新医院列表时 返回数据信息
     *
     * @param projectInfoId
     * @return
     */
    ProjOnlineStepSuccessVO resultDataInfo(Long projectInfoId) {
        Date date = new Date();
        ProjOnlineStepSuccessVO projOnlineStepSuccessVO = new ProjOnlineStepSuccessVO();
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projectInfoId);
        // 查询项目经理
        SysUser user = userMapper.selectById(projProjectInfo.getProjectLeaderId());
        projOnlineStepSuccessVO.setProjectInfoName(projProjectInfo.getProjectName());
        projOnlineStepSuccessVO.setProjectLeaderName(user.getUserName());
        projOnlineStepSuccessVO.setOnlineTime(date);
        projOnlineStepSuccessVO.setSettleInTime(projProjectInfo.getSettleInTime());
        long day = DateUtil.between(projProjectInfo.getSettleInTime(), date, DateUnit.DAY) + 1;
        projOnlineStepSuccessVO.setOnlineUsedDate(day);
        return projOnlineStepSuccessVO;
    }

    /**
     * 说明: 统一更新his密码
     *
     * @return:java.lang.Object
     * @author: Yhongmin
     * @createAt: 2024/5/24 8:44
     * @remark: Copyright
     */
    @Override
    public Result changePassword(ProjOnlineStepDTO stepDTO) {
        if (stepDTO == null || stepDTO.getHospitalInfoIdList() == null || stepDTO.getHospitalInfoIdList().size() == 0
                || StringUtils.isEmpty(stepDTO.getNewPassword())) {
            log.error("参数校验失败");
            return Result.fail("参数校验失败");
        }
        List<ProjOnlineStepDetail> addList = new ArrayList<>();
        List<ProjOnlineStepDetail> updateList = new ArrayList<>();
        //获取步骤信息
        ProjOnlineStep projOnlineStep = projOnlineStepMapper.selectById(stepDTO.getProjOnlineStepId());
        List<Long> hospitalInfoIdList = stepDTO.getHospitalInfoIdList();
        //统一操作时间
        Date date = new Date();
        AtomicBoolean success = new AtomicBoolean(true);
        hospitalInfoIdList.stream().forEach(dto -> {
            ProjOnlineStepDetail projOnlineStepDetail = new ProjOnlineStepDetail();
            projOnlineStepDetail.setProjOnlineStepId(projOnlineStep.getProjOnlineStepId());
            projOnlineStepDetail.setHospitalInfoId(dto);
            projOnlineStepDetail.setConfigOnlineStepId(projOnlineStep.getConfigOnlineStepId());
            projOnlineStepDetail.setStepDetailStatus(1);
            projOnlineStepDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            projOnlineStepDetail.setIsDeleted(0);
            projOnlineStepDetail.setCreateTime(date);
            projOnlineStepDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            projOnlineStepDetail.setUpdateTime(date);
            ProjOnlineStepDetail detail = onlineStepDetailMapper.selectOne(
                    new QueryWrapper<ProjOnlineStepDetail>().eq("hospital_info_id", dto)
                            .eq("proj_online_step_id", stepDTO.getProjOnlineStepId()));
            if (detail != null) {
                projOnlineStepDetail.setOnlineStepDetailId(detail.getOnlineStepDetailId());
                updateList.add(projOnlineStepDetail);
            } else {
                addList.add(projOnlineStepDetail);
            }
            //获取医院信息
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectOne(
                    new QueryWrapper<ProjHospitalInfo>().eq("hospital_info_id", dto));
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            //拼装提交参数；hospital_id和password
            BatchInsertDTO<PasswordDTO> batchInsertDTO = new BatchInsertDTO<>();
            PasswordDTO password = new PasswordDTO();
            password.setHospitalId(projHospitalInfo.getCloudHospitalId());
            password.setNewPassword(stepDTO.getNewPassword());
            List<PasswordDTO> list = new ArrayList<>();
            list.add(password);
            batchInsertDTO.setData(list);
            batchInsertDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
            batchInsertDTO.setHisOrgId(projHospitalInfo.getOrgId());
            //修改密码
            ResponseResult result = imspApi.changeUserPassword(batchInsertDTO);
            if (result.isSuccess()) {
                log.error("修改密码失败");
                removeOnlineUserSessionByHospitalId(projHospitalInfo);
            }
        });
        if (!success.get()) {
            log.error("接口调用失败");
            return Result.fail("接口调用失败");
        }
        log.info("统一更新his密码成功");
        if (addList.size() > 0) {
            onlineStepDetailMapper.batchInsert(addList);
        }
        if (updateList.size() > 0) {
            onlineStepDetailMapper.batchUpdate(updateList);
        }
        // 创建一个单线程的线程池
        ExecutorService executor = Executors.newSingleThreadExecutor();
        // 提交任务到线程池
        executor.submit(() -> {
            try {
                this.clearMessageData(hospitalInfoIdList);
            } catch (Exception e) {
                // 清除测试消息
                log.error("清除测试消息{}: ", e.getMessage());
                // 可以记录日志或者进行其他错误处理操作
                sysOperLogService.apiOperLogInsert(e.getMessage(), "调用数据中台错误信息", "调用数据中台错误信息",
                        Log.LogOperType.OTHER.getCode());
            }

        });
        // 关闭线程池
        executor.shutdown();
        UpdateWrapper<ProjOnlineStep> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("proj_online_step_id", projOnlineStep.getProjOnlineStepId());
        ProjOnlineStep updateInfo = new ProjOnlineStep();
        updateInfo.setStatus(1);
        updateInfo.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        updateInfo.setUpdateTime(date);
        int update = projOnlineStepMapper.update(updateInfo, updateWrapper);
        return Result.success();
    }

    /**
     * 说明: 清除测试消息
     *
     * @param hospitalInfoIdList
     */
    @Override
    public void clearMessageData(List<Long> hospitalInfoIdList) {
        log.error("清除云健康测试消息：hospitalInfoIdList{}", hospitalInfoIdList);
        List<ProjHospitalInfo> projHospitalInfos = projHospitalInfoMapper.selectBatchIds(hospitalInfoIdList);
        List<Long> cloudHospitalIds = projHospitalInfos.stream().map(ProjHospitalInfo::getCloudHospitalId).collect(Collectors.toList());
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectOne(
                new QueryWrapper<ProjHospitalInfo>().eq("hospital_info_id", hospitalInfoIdList.get(0)));
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        BatchInsertGeneralDTO<SendMessageValueDto> dto = new BatchInsertGeneralDTO<>();
        SendMessageValueDto sendMessageValueDto = new SendMessageValueDto();
        sendMessageValueDto.setHospitalIdList(cloudHospitalIds);
        dto.setData(sendMessageValueDto);
        dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
        dto.setHisOrgId(projHospitalInfo.getOrgId());
        log.info("清除云健康测试消息,发送前数据:{}", dto);
        ResponseResult result = systemSettingApi.clearMessageData(dto);
        log.info("清除云健康测试消息,发送后数据:{}", result);
    }

    /**
     * @param stepDTO
     * @return
     */
    @Override
    public Object selectOnlineBeforFile(ProjOnlineStepDTO stepDTO) {
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(stepDTO.getProjectInfoId());
        List<SysFile> onlineStepDetail = onlineStepDetailMapper.selectOnlineBeforFile(stepDTO);
        Map<String, Object> map = new HashMap<>();
        if (onlineStepDetail != null && onlineStepDetail.size() > 0) {
            for (SysFile sysFile : onlineStepDetail) {
                String filePath = sysFile.getFilePath();
                if (!filePath.startsWith("http")) {
                    filePath = OBSClientUtils.getTemporaryUrl(filePath, 300);
                }
                String fileCode = sysFile.getFileCode();
                if (fileCode.equals("his_cut_tool")) {
                    fileCode = "download_iis_tool";
                }
                map.put(fileCode, filePath);
            }
        }
        map.put("ip", "");
        map.put("prot", "");
        if (projProjectInfo.getUpgradationType().equals(ProjectUpgradationTypeEnums.LHX.getCode())) {
            // 查询实施地老系统的ip和端口
            ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectById(projProjectInfo.getCustomInfoId());
            map.put("ip", projCustomInfo.getIp());
            map.put("prot", projCustomInfo.getPort());
        }
        return map;
    }

    /**
     * 查询数据
     *
     * @param stepDTO
     * @return
     */
    @Override
    public Object selectOnlineBeforeIpHost(ProjOnlineStepDTO stepDTO) {
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(stepDTO.getProjectInfoId());
        Map<String, Object> map = new HashMap<>();
        map.put("ip", "");
        map.put("prot", "");
        if (projProjectInfo.getUpgradationType().equals(ProjectUpgradationTypeEnums.LHX.getCode())) {
            // 查询实施地老系统的ip和端口
            ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectById(projProjectInfo.getCustomInfoId());
            map.put("ip", projCustomInfo.getIp());
            map.put("prot", projCustomInfo.getPort());
        }
        return map;
    }

    /**
     * 说明: 根据医院id清空医院所有用户会话
     *
     * @param projHospitalInfo
     * @return:com.msun.core.commons.api.ResponseResult
     * @author: Yhongmin
     * @createAt: 2024/5/24 9:10
     * @remark: Copyright
     */
    public ResponseResult removeOnlineUserSessionByHospitalId(ProjHospitalInfo projHospitalInfo) {
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        try {
            PortTestDTO dto = new PortTestDTO();
            dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
            dto.setHisOrgId(projHospitalInfo.getOrgId());
            List<String> str = new ArrayList<>();
            dto.setStrs(str);
            return hospitalApi.removeOnlineUserSessionByHospitalId(dto);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return ResponseResult.error(BaseErrorCode.FALLBACK, e.getMessage());
        }
    }

    /**
     * 说明: 同步宽表数据
     *
     * @param stepDTO
     * @return:com.msun.core.commons.api.ResponseResult
     * @author: Yhongmin
     * @createAt: 2024/5/24 13:58
     * @remark: Copyright
     */
    @Override
    public Result syncWidthTableData(ProjOnlineStepDTO stepDTO) {
        //根据项目获取医院
        SelectHospitalDTO dto = new SelectHospitalDTO();
        dto.setProjectInfoId(stepDTO.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(dto);
        if (hospitalInfos == null || hospitalInfos.size() == 0) {
            return Result.fail("根据项目未查询到匹配医院信息");
        }
        String url = widthTableUrl + "/etl/scheduleJob/newCustomerRepair";
        //筛选主院
        ProjHospitalInfo projHospitalInfo = hospitalInfos.stream()
                .filter(ho -> NumberEnum.NO_1.num().equals(ho.getHealthBureauFlag())).findFirst().orElse(null);
        if (projHospitalInfo == null) {
            projHospitalInfo = hospitalInfos.stream().findFirst().get();
        }
        //接口入参
        Map<String, Object> wrapper = new HashMap<>();
        wrapper.put("hospiatl_id", projHospitalInfo.getCloudHospitalId());
        String strStart = DateUtil.formatDate(stepDTO.getStartTime());
        String strEnd = DateUtil.formatDate(stepDTO.getEndTime());
        Map<String, String> paramTime = Maps.newHashMap();
        paramTime.put("$START_DATE$", strStart + " 00:00:00");
        paramTime.put("$END_DATE$", strEnd + " 23:59:59");
        ProjHospitalInfo finalProjHospitalInfo = projHospitalInfo;
        Map<String, Object> param = new HashMap<>();
        param.put("hospitalId", finalProjHospitalInfo.getCloudHospitalId());
        param.put("orgId", finalProjHospitalInfo.getOrgId());
        param.put("dispatchSource", "1");
        param.put("proArgs", paramTime);
        //调用接口
        try {
            log.info("调用宽表接口入参:{}", JSONObject.toJSONString(param));
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            String msg = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("msg"));
            String code = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("code"));
            if (!"200".equals(code)) {
                return Result.fail(msg + "联系数据中台人员进行手动抽取！！");
            }
            //如果确认上线步骤id为空的话，是确认上线步骤id查询是否存在明细，其他页面调用，不需要回更，直接返回结果
            if (stepDTO.getProjOnlineStepId() == null) {
                return Result.success(msg);
            }
            //确认上线步骤id查询是否存在明细
            List<ProjOnlineStepDetail> addList = new ArrayList<>();
            List<ProjOnlineStepDetail> updateList = new ArrayList<>();
            ProjOnlineStep projOnlineStep = projOnlineStepMapper.selectById(stepDTO.getProjOnlineStepId());
            //统一操作时间
            Date date = new Date();
            hospitalInfos.stream().forEach(hospitalInfo -> {
                //明细表
                ProjOnlineStepDetail projOnlineStepDetail = new ProjOnlineStepDetail();
                projOnlineStepDetail.setProjOnlineStepId(stepDTO.getProjOnlineStepId());
                projOnlineStepDetail.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                projOnlineStepDetail.setConfigOnlineStepId(projOnlineStep.getConfigOnlineStepId());
                projOnlineStepDetail.setStepDetailStatus(1);
                projOnlineStepDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                projOnlineStepDetail.setIsDeleted(0);
                projOnlineStepDetail.setCreateTime(date);
                projOnlineStepDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                projOnlineStepDetail.setUpdateTime(date);
                ProjOnlineStepDetail detail = onlineStepDetailMapper.selectOne(
                        new QueryWrapper<ProjOnlineStepDetail>().eq("hospital_info_id",
                                        hospitalInfo.getHospitalInfoId())
                                .eq("proj_online_step_id", stepDTO.getProjOnlineStepId()));
                if (detail != null) {
                    projOnlineStepDetail.setOnlineStepDetailId(detail.getOnlineStepDetailId());
                    updateList.add(projOnlineStepDetail);
                } else {
                    addList.add(projOnlineStepDetail);
                }

            });
            if (addList.size() > 0) {
                onlineStepDetailMapper.batchInsert(addList);
            }
            if (updateList.size() > 0) {
                onlineStepDetailMapper.batchUpdate(updateList);
            }
            UpdateWrapper<ProjOnlineStep> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("proj_online_step_id", projOnlineStep.getProjOnlineStepId());
            ProjOnlineStep updateInfo = new ProjOnlineStep();
            updateInfo.setStatus(1);
            updateInfo.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            updateInfo.setUpdateTime(date);
            int update = projOnlineStepMapper.update(updateInfo, updateWrapper);
            return Result.success(msg);
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
        }
        return Result.fail("抽取失败，可能原因：1. 客户端网络异常 2. 调用数据中台错误 ");
    }

    /**
     * 说明: 查询主日志信息
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.github.pagehelper.PageInfo>
     * @author: Yhongmin
     * @createAt: 2024/5/24 15:35
     * @remark: Copyright
     */
    @Override
    public Result<PageInfo> selectLogAllByPage(ProjOnlineStepDetailDTO dto) {
        //根据项目获取医院
        SelectHospitalDTO hospitalDTO = new SelectHospitalDTO();
        hospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(hospitalDTO);
        if (hospitalInfos == null || hospitalInfos.size() == 0) {
            return Result.fail("根据项目未查询到匹配医院信息");
        }
        //筛选主院
        ProjHospitalInfo projHospitalInfo = hospitalInfos.stream()
                .filter(ho -> NumberEnum.NO_1.num().equals(ho.getHealthBureauFlag())).findFirst().orElse(null);
        if (projHospitalInfo == null) {
            projHospitalInfo = hospitalInfos.stream().findFirst().get();
        }
        //拼接接口参数
        Map<String, Object> wrapper = Maps.newHashMap();
        wrapper.put("hospiatl_id", projHospitalInfo.getCloudHospitalId());
        String url = widthTableUrl + "/etl/log/getOtherVendorExeMainLog";
        ProjHospitalInfo finalProjHospitalInfo = projHospitalInfo;
        Map<String, Object> param = new HashMap<>();
        param.put("hospitalId", finalProjHospitalInfo.getCloudHospitalId());
        param.put("orgId", finalProjHospitalInfo.getOrgId());
        param.put("dispatchSource", "1");
        param.put("pageNo", dto.getPageNum());
        param.put("pageSize", dto.getPageSize());
        try {
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            String code = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("code"));
            if ("200".equals(code)) {
                Map<String, Object> mapDate = (Map<String, Object>) stringObjectMap.get("data");
                List listDate = (List) mapDate.get("list");
                int total = (int) mapDate.get("total");
                PageInfo page = new PageInfo(listDate);
                page.setPageNum(dto.getPageNum());
                page.setPageSize(dto.getPageSize());
                page.setTotal(total);
                return Result.success(page);
            }
            return Result.success(new PageInfo<>());

        } catch (Exception e) {
            log.error("====>" + e.getMessage());
            return Result.fail();
        }
    }

    /**
     * 调用系统管理接口进行医院上线授权
     * TODO : 此方法暂时不用，因为调用系统管理接口重复 2025-03-06  看到此处的千万别用， 此方法1不符合 csm---->API --->云健康 流转，存在隐患
     *
     * @param hospitalId
     */
    public void sendSysManage(Long hospitalId) throws Exception {
        // 获取需要上线的医院信息
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(hospitalId);
        //调用系统管理接口
        HospitalOnlineDTO hospitalOnlineDTO = new HospitalOnlineDTO();
        hospitalOnlineDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        hospitalOnlineDTO.setOrgId(hospitalInfo.getOrgId());
        List<Long> arrayList = new ArrayList<>();
        arrayList.add(hospitalInfo.getCloudHospitalId());
        hospitalOnlineDTO.setHospitalIdList(arrayList);
        List<HospitalOnlinePublishedSystemDTO> publishedSystems = new ArrayList<>();
        // 调用系统管理入参
        extracted(hospitalInfo, arrayList, publishedSystems);
    }

    /**
     * 说明: 更新里程碑节点
     *
     * @param projectInfoId
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/7/1 19:48
     * @remark: Copyright
     */
    public void updateMilestoneByProjectInfoId(Long projectInfoId) {
        try {
            SelectHospitalDTO dto = new SelectHospitalDTO();
            dto.setProjectInfoId(projectInfoId);
            List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(dto);
            if (hospitalInfos == null || hospitalInfos.size() == 0) {
                return;
            }
            // 增加注释： 筛选出未开通或者未部署的医院
            List<ProjHospitalInfo> hospitalInfoList =
                    hospitalInfos.stream().filter(hop -> OpenStatusEnum.ARRANGE.getCode().equals(hop.getHospitalOpenStatus()) || OpenStatusEnum.NOT_OPENED.getCode().equals(hop.getHospitalOpenStatus())).collect(Collectors.toList());
            if (hospitalInfoList.size() == 0) {
                Date date = new Date();
                ProjMilestoneInfo projMilestoneInfo =
                        projMilestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().
                                eq("project_info_id", projectInfoId).
                                eq("milestone_node_code", MilestoneNodeEnum.PROJECT_LAUNCH.getCode()).eq("invalid_flag",
                                        NumberEnum.NO_0.num()));
                UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
                updateMilestoneDTO.setMilestoneInfoId(projMilestoneInfo.getMilestoneInfoId());
                updateMilestoneDTO.setMilestoneStatus(NumberEnum.NO_1.num());
                updateMilestoneDTO.setActualCompTime(date);
                projMilestoneInfoService.compMilestone(updateMilestoneDTO);
                ProjProjectInfo projProjectInfo = new ProjProjectInfo();
                projProjectInfo.setProjectInfoId(projMilestoneInfo.getProjectInfoId());
                projProjectInfo.setOnlineTime(date);
                projProjectInfo.setProjectDeliverStatus(ONLINE.getCode());
                projProjectInfoMapper.updateById(projProjectInfo);
            }
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 增加转换。 由工单产品转换为项目部署产品
     *
     * @param projectInfoId    项目id
     * @param cloudHospitalIds 医院列表
     * @param productInfos
     * @param type             0-下发权限 1-回收权限
     * @return
     */
    @Override
    public Result updateSchemaGrant(Long projectInfoId, List<Long> cloudHospitalIds, List<Long> productInfos,
                                    String type) {
        // 由工单产品转换为项目部署产品
        if (productInfos != null && productInfos.size() > 0) {
            List<ProjProductArrangeRecord> productArrangeRecords = new ArrayList<>();
            List<ProjProductArrangeRecord> records1 =
                    projProductArrangeRecordMapper.selectList(new QueryWrapper<ProjProductArrangeRecord>().in(
                            "product_arrange_id", productInfos).eq("project_info_id", projectInfoId).eq("is_deleted",
                            0));
            List<ProjProductArrangeRecord> records2 =
                    projProductArrangeRecordMapper.selectList(new QueryWrapper<ProjProductArrangeRecord>().in(
                            "yy_order_product_id", productInfos).eq("project_info_id", projectInfoId).eq("is_deleted",
                            0));
            // 合并两个查询结果
            productArrangeRecords.addAll(records1);
            productArrangeRecords.addAll(records2);
            if (CollUtil.isNotEmpty(productArrangeRecords)) {
                productInfos =
                        productArrangeRecords.stream().map(ProjProductArrangeRecord::getProductArrangeId).collect(Collectors.toList());
            }
        }
        return updateSchemaGrantArrangeProduct(projectInfoId, cloudHospitalIds, productInfos, type);
    }

    private Result updateSchemaGrantArrangeProduct(Long projectInfoId, List<Long> cloudHospitalIds,
                                                   List<Long> arrangeProductInfos,
                                                   String type) {
        try {
            return updateSchemaGrantImpl(projectInfoId, cloudHospitalIds, arrangeProductInfos, type);
        } catch (Throwable e) {
            String message;
            String returnMsg = StrUtil.EMPTY;
            if (e instanceof DbOperationException) {
                message = e.getMessage();
                returnMsg = ((DbOperationException) e).getReturnMessage();
            } else {
                ProjProjectInfo projectInfo = settlementCheckMainService.getProjectInfo(projectInfoId);
                message = projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + ":云健康数据库授权接口调用异常. "
                        + "请及时处理.";
            }
            //applyOrderEnvService.sendToSystemManager(projectInfoId, message);
            return Result.fail(returnMsg);
        }
    }

    /**
     * 云健康浏览器端调用
     *
     * @param stepDTO
     * @return
     */
    @Override
    public Result saveInitializationData(ProjOnlineStepDTO stepDTO) {
        //根据项目获取医院
        SelectHospitalDTO dto = new SelectHospitalDTO();
        dto.setProjectInfoId(stepDTO.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(dto);
        if (hospitalInfos == null || hospitalInfos.size() == 0) {
            return Result.fail("根据项目未查询到匹配医院信息");
        }
        //筛选主院
        ProjHospitalInfo projHospitalInfo = hospitalInfos.stream()
                .filter(ho -> NumberEnum.NO_1.num().equals(ho.getHealthBureauFlag())).findFirst().orElse(null);
        if (projHospitalInfo == null) {
            projHospitalInfo = hospitalInfos.stream().findFirst().get();
        }
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        DataTransDto dtoCloudParamer = new DataTransDto();
        dtoCloudParamer.setHospitalId(projHospitalInfo.getCloudHospitalId());
        dtoCloudParamer.setHisOrgId(projHospitalInfo.getOrgId());
        dtoCloudParamer.setHisHosId(projHospitalInfo.getCloudHospitalId());
        dtoCloudParamer.setCustomJumpUrl(projHospitalInfo.getCloudDomain().replace("http://", StrUtil.EMPTY).replace(
                "https://", StrUtil.EMPTY));
        // 上线初始化的话是当前时间，不是上线补之前的数据的话，这个时间就是要补的时间
        dtoCloudParamer.setTransTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        //调用接口
        try {
            ResponseResult resultOne = systemSettingApi.saveInitializationData(dtoCloudParamer);
            ResponseResult resultTwo = systemSettingApi.saveDataTransLog(dtoCloudParamer);
            Boolean flag = false;
            if (resultOne.getSuccess() && resultTwo.getSuccess()) {
                Map<String, Object> resultOneMap = (Map<String, Object>) resultOne.getData();
                Map<String, Object> resultTwoMap = (Map<String, Object>) resultTwo.getData();
                if (Boolean.valueOf((Boolean) resultOneMap.get("success")) && Boolean.valueOf((Boolean) resultTwoMap.get("success"))) {
                    flag = true;
                }
            }
            if (!flag) {
                return Result.fail("处理失败，联系交付平台管理员！！");
            }
            //如果确认上线步骤id为空的话，是确认上线步骤id查询是否存在明细，其他页面调用，不需要回更，直接返回结果
            if (stepDTO.getProjOnlineStepId() == null) {
                return Result.success("处理成功");
            }
            //确认上线步骤id查询是否存在明细
            List<ProjOnlineStepDetail> addList = new ArrayList<>();
            List<ProjOnlineStepDetail> updateList = new ArrayList<>();
            ProjOnlineStep projOnlineStep = projOnlineStepMapper.selectById(stepDTO.getProjOnlineStepId());
            //统一操作时间
            Date date = new Date();
            hospitalInfos.stream().forEach(hospitalInfo -> {
                //明细表
                ProjOnlineStepDetail projOnlineStepDetail = new ProjOnlineStepDetail();
                projOnlineStepDetail.setProjOnlineStepId(stepDTO.getProjOnlineStepId());
                projOnlineStepDetail.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                projOnlineStepDetail.setConfigOnlineStepId(projOnlineStep.getConfigOnlineStepId());
                projOnlineStepDetail.setStepDetailStatus(1);
                projOnlineStepDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                projOnlineStepDetail.setIsDeleted(0);
                projOnlineStepDetail.setCreateTime(date);
                projOnlineStepDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                projOnlineStepDetail.setUpdateTime(date);
                ProjOnlineStepDetail detail = onlineStepDetailMapper.selectOne(
                        new QueryWrapper<ProjOnlineStepDetail>().eq("hospital_info_id",
                                        hospitalInfo.getHospitalInfoId())
                                .eq("proj_online_step_id", stepDTO.getProjOnlineStepId()));
                if (detail != null) {
                    projOnlineStepDetail.setOnlineStepDetailId(detail.getOnlineStepDetailId());
                    updateList.add(projOnlineStepDetail);
                } else {
                    addList.add(projOnlineStepDetail);
                }

            });
            if (addList.size() > 0) {
                onlineStepDetailMapper.batchInsert(addList);
            }
            if (updateList.size() > 0) {
                onlineStepDetailMapper.batchUpdate(updateList);
            }
            UpdateWrapper<ProjOnlineStep> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("proj_online_step_id", projOnlineStep.getProjOnlineStepId());
            ProjOnlineStep updateInfo = new ProjOnlineStep();
            updateInfo.setStatus(1);
            updateInfo.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            updateInfo.setUpdateTime(date);
            int update = projOnlineStepMapper.update(updateInfo, updateWrapper);
            return Result.success("处理成功");
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
        }
        return Result.fail("云健康档案浏览器抽数据失败，可能原因：1. 客户端网络异常 2. 调用云健康接口错误 ");
    }

    /**
     * 处理合理用药抽数据
     *
     * @param stepDTO
     * @return
     */
    @Override
    public Result fetchReasonableMedicationData(ProjOnlineStepDTO stepDTO) {
        //根据项目获取医院
        SelectHospitalDTO dto = new SelectHospitalDTO();
        dto.setProjectInfoId(stepDTO.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(dto);
        if (hospitalInfos == null || hospitalInfos.size() == 0) {
            return Result.fail("根据项目未查询到匹配医院信息");
        }
        //筛选主院
        ProjHospitalInfo projHospitalInfo = hospitalInfos.stream()
                .filter(ho -> NumberEnum.NO_1.num().equals(ho.getHealthBureauFlag())).findFirst().orElse(null);
        if (projHospitalInfo == null) {
            projHospitalInfo = hospitalInfos.stream().findFirst().get();
        }
        try {
            Boolean flag = false;
            Date startTime = DateUtil.parse(DateUtil.format(stepDTO.getStartTime(), DatePattern.NORM_DATE_PATTERN)
                    + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
            Date endTime = DateUtil.parse(DateUtil.format(stepDTO.getEndTime(), DatePattern.NORM_DATE_PATTERN) + " "
                    + "23:59:59", DatePattern.NORM_DATETIME_PATTERN);
            Map<String, Object> resultMap = syncEtlHlyyData(projHospitalInfo, startTime, endTime);
            String code = com.msun.csm.util.StringUtils.nvl(resultMap.get("code"));
            if ("200".equals(code)) {
                flag = true;
            }
            if (!flag) {
                return Result.fail("抽取失败，联系数据中台人员进行手动抽取！！");
            }
            //如果确认上线步骤id为空的话，是确认上线步骤id查询是否存在明细，其他页面调用，不需要回更，直接返回结果
            if (stepDTO.getProjOnlineStepId() == null) {
                return Result.success("处理成功");
            }
            //确认上线步骤id查询是否存在明细
            List<ProjOnlineStepDetail> addList = new ArrayList<>();
            List<ProjOnlineStepDetail> updateList = new ArrayList<>();
            ProjOnlineStep projOnlineStep = projOnlineStepMapper.selectById(stepDTO.getProjOnlineStepId());
            //统一操作时间
            Date date = new Date();
            hospitalInfos.stream().forEach(hospitalInfo -> {
                //明细表
                ProjOnlineStepDetail projOnlineStepDetail = new ProjOnlineStepDetail();
                projOnlineStepDetail.setProjOnlineStepId(stepDTO.getProjOnlineStepId());
                projOnlineStepDetail.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                projOnlineStepDetail.setConfigOnlineStepId(projOnlineStep.getConfigOnlineStepId());
                projOnlineStepDetail.setStepDetailStatus(1);
                projOnlineStepDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                projOnlineStepDetail.setIsDeleted(0);
                projOnlineStepDetail.setCreateTime(date);
                projOnlineStepDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                projOnlineStepDetail.setUpdateTime(date);
                ProjOnlineStepDetail detail = onlineStepDetailMapper.selectOne(
                        new QueryWrapper<ProjOnlineStepDetail>().eq("hospital_info_id",
                                        hospitalInfo.getHospitalInfoId())
                                .eq("proj_online_step_id", stepDTO.getProjOnlineStepId()));
                if (detail != null) {
                    projOnlineStepDetail.setOnlineStepDetailId(detail.getOnlineStepDetailId());
                    updateList.add(projOnlineStepDetail);
                } else {
                    addList.add(projOnlineStepDetail);
                }

            });
            if (addList.size() > 0) {
                onlineStepDetailMapper.batchInsert(addList);
            }
            if (updateList.size() > 0) {
                onlineStepDetailMapper.batchUpdate(updateList);
            }
            UpdateWrapper<ProjOnlineStep> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("proj_online_step_id", projOnlineStep.getProjOnlineStepId());
            ProjOnlineStep updateInfo = new ProjOnlineStep();
            updateInfo.setStatus(1);
            updateInfo.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            updateInfo.setUpdateTime(date);
            int update = projOnlineStepMapper.update(updateInfo, updateWrapper);
            return Result.success("处理成功");
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
        }
        return Result.fail("合理用药抽数据失败，可能原因：1. 客户端网络异常 2. 调用数据中台接口错误 ");
    }

    /**
     * 查询合理用药抽数据日志
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo> selectReasonableMedicationLogAllByPage(ProjOnlineStepDetailDTO dto) {
        //根据项目获取医院
        SelectHospitalDTO hospitalDTO = new SelectHospitalDTO();
        hospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(hospitalDTO);
        if (hospitalInfos == null || hospitalInfos.size() == 0) {
            return Result.fail("根据项目未查询到匹配医院信息");
        }
        //筛选主院
        ProjHospitalInfo projHospitalInfo = hospitalInfos.stream()
                .filter(ho -> NumberEnum.NO_1.num().equals(ho.getHealthBureauFlag())).findFirst().orElse(null);
        if (projHospitalInfo == null) {
            projHospitalInfo = hospitalInfos.stream().findFirst().get();
        }
        //拼接接口参数
        Map<String, Object> wrapper = Maps.newHashMap();
        wrapper.put("hospiatl_id", projHospitalInfo.getCloudHospitalId());
        String url = widthTableUrl + "/etl/log/getOtherVendorExeMainLog";
        ProjHospitalInfo finalProjHospitalInfo = projHospitalInfo;
        Map<String, Object> param = new HashMap<>();
        param.put("hospitalId", finalProjHospitalInfo.getCloudHospitalId());
        param.put("orgId", finalProjHospitalInfo.getOrgId());
        param.put("dispatchSource", "1");
        param.put("workflowName", "合理用药抽取任务");
        param.put("pageNo", dto.getPageNum());
        param.put("pageSize", dto.getPageSize());
        try {
            log.info("调用合理用药抽取调度接口getOtherVendorExeMainLog,参数:{}", JSONObject.toJSONString(param));
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            String code = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("code"));
            if ("200".equals(code)) {
                Map<String, Object> mapDate = (Map<String, Object>) stringObjectMap.get("data");
                List listDate = (List) mapDate.get("list");
                int total = (int) mapDate.get("total");
                PageInfo page = new PageInfo(listDate);
                page.setPageNum(dto.getPageNum());
                page.setPageSize(dto.getPageSize());
                page.setTotal(total);
                return Result.success(page);
            }
            return Result.success(new PageInfo<>());

        } catch (Exception e) {
            log.error("====>" + e.getMessage());
            return Result.fail();
        }
    }


    public Result updateSchemaGrantImpl(Long projectInfoId, List<Long> cloudHospitalIds, List<Long> productInfos,
                                        String type) {
        log.info("调用云健康数据库权限接口,projectInfoId={},cloudHospitalIds={},productInfos={},type={}", projectInfoId,
                cloudHospitalIds, productInfos, type);
        try {
            if (projectInfoId == null) {
                return Result.fail("未查询到医院信息-项目id为空");
            }
            ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
            //判断是否是首期项目
            if (projectInfo != null && NumberEnum.NO_1.num().equals(projectInfo.getHisFlag())) {
                //首期要给所有产品进行下发或者收回
                List<DictProduct> dictProductInfos = dictProductMapper.selectList(new QueryWrapper<DictProduct>().eq(
                        "is_deleted", 0));
                if (CollUtil.isNotEmpty(dictProductInfos)) {
                    productInfos =
                            dictProductInfos.stream().map(DictProduct::getYyProductId).collect(Collectors.toList());
                }
            }
            if (CollUtil.isEmpty(cloudHospitalIds)) {
                SelectHospitalDTO dto = new SelectHospitalDTO();
                dto.setProjectInfoId(projectInfoId);
                List<ProjHospitalInfo> hospitalInfoList = hospitalInfoService.getHospitalInfoByProjectId(dto);
                if (CollUtil.isNotEmpty(hospitalInfoList)) {
                    cloudHospitalIds = hospitalInfoList.stream().filter(
                                    hot -> hot.getCloudHospitalId() != null && hot.getOrgId() != null && ObjectUtil.isNotEmpty(hot.getCloudDomain()))
                            .map(ProjHospitalInfo::getCloudHospitalId).collect(Collectors.toList());
                }
            }
            if (CollUtil.isEmpty(cloudHospitalIds)) {
                log.info("未查询到医院信息");
                return Result.fail("未查询到医院信息");
            }
            if (CollUtil.isEmpty(productInfos)) {
                List<ProjProductArrangeRecord> productArrangeRecords =
                        projProductArrangeRecordMapper.findByProjectInfoId(projectInfoId);
                if (CollUtil.isNotEmpty(productArrangeRecords)) {
                    productInfos =
                            productArrangeRecords.stream().map(ProjProductArrangeRecord::getProductArrangeId).collect(Collectors.toList());
                }
            }
            if (CollUtil.isEmpty(productInfos)) {
                log.info("未查询到部署产品信息");
                return Result.fail("未查询到产品信息");
            }
            SchemaGrantReq req = new SchemaGrantReq();
            req.setCustomerProductIdList(productInfos);
            req.setHospitalIdList(cloudHospitalIds);
            req.setType(type);
            // 阶段 1-第一期 2-第二期
            if (projectInfo != null && NumberEnum.NO_1.num().equals(projectInfo.getHisFlag())) {
                req.setStage("1");
            } else {
                req.setStage("2");
            }
            yunWeiPlatFormService.schemaGrant(req);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            ProjProjectInfo projectInfo = settlementCheckMainService.getProjectInfo(projectInfoId);
            String returnMessage = "云健康数据库权限-" + ("0".equals(type) ? "下放" : "收回") + "异常";
            String message = projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + ":" + returnMessage;
            throw new DbOperationException(message, message + e.getMessage(), returnMessage);
        }
        return Result.success();
    }

    @Override
    public void updateNodeStatus(Long projectInfoId, OrderStepEnum stepEnum) {
        SysUserVO currentUser = null;
        try {
            currentUser = userHelper.getCurrentUser();
        } catch (Throwable e) {
        }
        updateNodeStatus(projectInfoId, stepEnum, currentUser);
    }

    /**
     * 更新运营平台 工单状态与时间, 交付平台的项目状态与时间
     * <p>若项目id为空, 则不更新项目</p>
     *
     * @param orderInfo   工单信息
     * @param stepEnum    状态
     * @param currentUser 当前用户
     */
    private void updateNodeStatusImpl(ProjOrderInfo orderInfo, OrderStepEnum stepEnum,
                                      SysUserVO currentUser, Date endDate) {
        try {
            //查询合同信息
            ProjContractInfo contractInfo = contractInfoMapper.selectByPrimaryKey(orderInfo.getContractInfoId());
            UpdateNodeStatusDTO updateNodeStatusDTO = new UpdateNodeStatusDTO();
            updateNodeStatusDTO.setStartDate(DateUtil.formatLocalDateTime(LocalDateTime.now()));
            updateNodeStatusDTO.setEndDate(DateUtil.formatDate(endDate));
            updateNodeStatusDTO.setStepId(stepEnum.getStepId().toString());
            updateNodeStatusDTO.setProjectNum(orderInfo.getYyProjectNumber());
            updateNodeStatusDTO.setContractNum(contractInfo.getYyContractId());
            updateNodeStatusDTO.setWorkOrderId(orderInfo.getYyOrderId());
            updateNodeStatusDTO.setPushFlag("1");
            NodeBaseData nodeBaseData = new NodeBaseData(currentUser, stepEnum.getOperDesc(),
                    DateUtil.formatDateTime(new Date()));
            //产品信息
            List<ProductReq> productReqList = projOrderProductMapper.getYyProductByOrderInfoId(
                    orderInfo.getOrderInfoId());
            //软件项目数量默认1
            productReqList.stream().forEach(a -> a.setNumber(1));
            nodeBaseData.setProduct(productReqList);
            //文件
            updateNodeStatusDTO.setData(Collections.singletonList(nodeBaseData));
            log.info("更新运营平台工单状态接口信息: {}", JSON.toJSONString(updateNodeStatusDTO));
            String result = yunyingFeignClient.updateNodeStatus(currentUser.getAccount(),
                    updateNodeStatusDTO);
            log.info("更新运营平台工单状态返回值信息: {}", result);
            //添加日志
            addSyncApiLog("更新运营平台工单状态: 参数信息 ==" + JSONUtil.toJsonStr(updateNodeStatusDTO)
                    + "【返回信息：】 ==, {}" + result, StrUtil.EMPTY, currentUser);
            if (JSONObject.parseObject(result).getInteger("code") != 200 && !JSONObject.parseObject(result).getString("msg").contains("当前节点已完成")) {
                log.error("更新运营平台工单状态失败，errMsg={}", result);
                throw new Exception("调用运营平台节点信息同步接口失败=-=-=" + result);
            }
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new RuntimeException("更新运营平台工单状态失败 ");
        }
    }

    /**
     * 更新运营平台 工单状态与时间, 交付平台的项目状态与时间
     *
     * @param orderInfo   工单信息
     * @param stepEnum    同步节点
     * @param currentUser 当前用户
     */
    @Override
    public void updateNodeStatus(ProjOrderInfo orderInfo, OrderStepEnum stepEnum, SysUserVO currentUser) {
        Date endDate = new Date();
        // 更新运营平台工单状态
        updateNodeStatusImpl(orderInfo, stepEnum, currentUser, endDate);
    }

    /**
     * 更新运营平台 工单状态与时间, 交付平台的项目状态与时间
     *
     * @param projectInfoId 项目id
     * @param stepEnum      同步节点
     * @param currentUser   当前用户
     */
    @Override
    public void updateNodeStatus(Long projectInfoId, OrderStepEnum stepEnum, SysUserVO currentUser) {
        // 查询项目信息
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        // 查询工单信息
        ProjOrderInfo projOrderInfo;
        // 获取云资源工单
        if (ObjectUtil.isNotEmpty(projectInfoId) && stepEnum.getOrderType() == 9) {
            List<ProjSettlementOrderInfo> settlementOrderInfos =
                    settlementCheckMainService.findCloudServiceForm(projectInfoId);
            if (CollectionUtil.isEmpty(settlementOrderInfos)) {
                // 未查询到云资源工单
                log.warn("未查询到云资源工单");
                return;
            }
            ProjSettlementOrderInfo settlementOrderInfo = settlementOrderInfos.get(0);
            projOrderInfo = orderInfoMapper.selectById(settlementOrderInfo.getOrderInfoId());
        } else {
            projOrderInfo = orderInfoMapper.selectById(projectInfo.getOrderInfoId());
        }
        Date endDate = new Date();
        // 更新运营平台工单状态
        updateNodeStatusImpl(projOrderInfo, stepEnum, currentUser, endDate);
        // 更新项目里程碑状态
        if (ObjectUtil.isNotEmpty(projectInfoId)) {
            // 更新项目上线状态和时间
            ProjProjectInfo projProjectInfo = new ProjProjectInfo();
            projProjectInfo.setProjectInfoId(projectInfoId);
            if (stepEnum == SOFTWARE_SURVEY || stepEnum == PURCHASE_SOFTWARE_SURVEY) {
                projProjectInfo.setSurveyCompleteTime(endDate);
                projProjectInfo.setProjectDeliverStatus(RESEARCHING.getCode());
            } else if (stepEnum == SOFTWARE_ON_SITE || stepEnum == PURCHASE_SOFTWARE_ON_SITE) {
                projProjectInfo.setSettleInTime(endDate);
                projProjectInfo.setProjectDeliverStatus(SETTLED.getCode());
            } else if (stepEnum == SOFTWARE_TEST || stepEnum == PURCHASE_SOFTWARE_TEST) {
                projProjectInfo.setPreCompleteTime(endDate);
                projProjectInfo.setProjectDeliverStatus(PREPARING.getCode());
            } else if (stepEnum == SOFTWARE_ONLINE || stepEnum == PURCHASE_SOFTWARE_ONLINE) {
                projProjectInfo.setOnlineTime(endDate);
                projProjectInfo.setProjectDeliverStatus(ONLINE.getCode());
            }
            projProjectInfo.setUpdateTime(endDate);
            projectInfoMapper.updateById(projProjectInfo);
        }
    }

    /**
     * 项目上线 -- 停用患者智能服务老站点
     *
     * @param dto
     * @return
     */
    @Override
    public Result projectOnlineForStopOldSite(ProjOnlineStepDTO dto) {
        // 替换传入的 实施地客户id 和项目id 。 该方法只适用于患者智能服务
        TmpProjectNewVsOld tmpProjectNewVsOld =
                tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>()
                        .eq("new_project_info_id", dto.getProjectInfoId())
                        .eq("new_custom_info_id", dto.getCustomInfoId())
                );
        String deliverId = "";
        Integer operateType = 0;
        try {
            List<HzznDeliver> hzznDeliverList =
                    hzznDeliverService.getHzznDeliver(tmpProjectNewVsOld.getOldCustomId().toString(),
                            tmpProjectNewVsOld.getOldProjectInfoId().toString());
            if (CollectionUtils.isNotEmpty(hzznDeliverList)) {
                operateType = hzznDeliverList.get(0).getOperateType();
                deliverId = hzznDeliverList.get(0).getId();
            }
            List<HzzDeliverHospital> hzzDeliverHospitalList =
                    hzzDeliverHospitalService.getHznDeliverHospitalByDeliver(deliverId);
            if (CollectionUtils.isEmpty(hzzDeliverHospitalList)) {
                return Result.fail("同步数据失败,未查询到医院信息.");
            }
            // 停止站点
            String result = null;
            if (0 == operateType) {
                return Result.success();
            } else {
                result = hzznDeliverService.stopOldSite(hzzDeliverHospitalList.get(0));
            }
            if ("true".equals(result)) {
                // 更新上线步骤表数据
                ProjOnlineStep projOnlineStep = new ProjOnlineStep();
                projOnlineStep.setProjOnlineStepId(dto.getProjOnlineStepId());
                projOnlineStep.setStatus(1);
                projOnlineStepMapper.updateById(projOnlineStep);
                return Result.success();
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                return Result.fail(result);
            }
        } catch (Exception e) {
            log.error("停止站点失败,发生异常！deliverId={} errmsg={}", deliverId, e.getMessage(), e);
        }
        return Result.fail("停止站点失败");
    }

    /**
     * 患者智能服务发布
     *
     * @param dto
     * @return
     */
    @Override
    public Result projectOnlineForPublishPublicNo(ProjOnlineStepDTO dto) {
        // 替换传入的 实施地客户id 和项目id 。 该方法只适用于患者智能服务
        TmpProjectNewVsOld tmpProjectNewVsOld =
                tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>()
                        .eq("new_project_info_id", dto.getProjectInfoId())
                        .eq("new_custom_info_id", dto.getCustomInfoId())
                );
        String deliverId = "";
        Integer operateType = 0;
        try {
            List<HzznDeliver> hzznDeliverList =
                    hzznDeliverService.getHzznDeliver(tmpProjectNewVsOld.getOldCustomId().toString(),
                            tmpProjectNewVsOld.getOldProjectInfoId().toString());
            if (CollectionUtils.isNotEmpty(hzznDeliverList)) {
                operateType = hzznDeliverList.get(0).getOperateType();
                deliverId = hzznDeliverList.get(0).getId();
            }
            List<HzzDeliverHospital> hzzDeliverHospitalList =
                    hzzDeliverHospitalService.getHznDeliverHospitalByDeliver(deliverId);
            if (CollectionUtils.isEmpty(hzzDeliverHospitalList)) {
                return Result.fail("同步数据失败,未查询到医院信息.");
            }
            List<HzzDeliverPublic> hzzDeliverPublicList =
                    hzzDeliverPublicService.getHzzDeliverPublicByDeliver(deliverId);
            if (CollectionUtils.isEmpty(hzzDeliverPublicList)) {
                return Result.fail("同步数据失败,未查询到公众号信息");
            }
            // 同步数据
            String result = null;
            if (0 == operateType) {
                List<HzznSyncResult> hzznSyncResultList = hzznDeliverService.pulishOfAdd(hzzDeliverHospitalList,
                        hzzDeliverPublicList.get(0));
                if (CollectionUtils.isEmpty(hzznSyncResultList)) {
                    result = "提交测试失败";
                } else {
                    result = this.analysisHzznSyncResult(hzznSyncResultList);
                }
            } else {
                result = hzznDeliverService.pulishOfChangeNew(hzzDeliverHospitalList, hzzDeliverPublicList.get(0));
            }
            if ("true".equals(result)) {
                hzznDeliverService.updateHzznDeliver(deliverId, 5);
                // 更新上线步骤表数据
                ProjOnlineStep projOnlineStep = new ProjOnlineStep();
                projOnlineStep.setProjOnlineStepId(dto.getProjOnlineStepId());
                projOnlineStep.setStatus(1);
                projOnlineStepMapper.updateById(projOnlineStep);
                return Result.success();
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(result)) {
                return Result.fail(result);
            }
        } catch (Exception e) {
            log.error("发布失败,发生异常！deliverId={} errmsg={}", deliverId, e.getMessage(), e);
        }
        return Result.fail("发布失败");
    }

    /**
     * 患者档案合并
     *
     * @param hospitalId
     * @return
     */
    @Override
    public Object mergePatientComm(Long hospitalId) {
        // 获取需要上线的医院信息
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(hospitalId);
        //API 调用，执行刷新 domain
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 档案合并
        PatMergeConfigSaveDTO patMergeConfigSaveDTO = new PatMergeConfigSaveDTO();
        patMergeConfigSaveDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        patMergeConfigSaveDTO.setOrgId(hospitalInfo.getOrgId());
        patMergeConfigSaveDTO.setHisOrgId(hospitalInfo.getOrgId());
        Object result = msunMiddleAggregatePatientApi.mergePatientComm(patMergeConfigSaveDTO);
        log.info("患者档案合并--基础中台返回的数据:" + result);
        return result;
    }

    /**
     * 添加外部接口调用日志
     *
     * @param message
     * @param hospitalName
     */
    @Override
    public void addSyncApiLog(String message, String hospitalName) {
        SysUserVO sysUserVO = null;
        try {
            sysUserVO = userHelper.getCurrentUser();
        } catch (Throwable e) {
            log.error("未查询到用户. message:{}, e=", e.getMessage(), e);
        }
        addSyncApiLog(message, hospitalName, sysUserVO);
    }

    /**
     * 添加日志
     *
     * @param message
     * @param hospitalName
     * @param sysUserVO
     */
    private void addSyncApiLog(String message, String hospitalName, SysUserVO sysUserVO) {
        ProjSyncApiLogs projSyncApiLogs = new ProjSyncApiLogs();
        projSyncApiLogs.setId(SnowFlakeUtil.getId());
        projSyncApiLogs.setActionname(ObjectUtil.isEmpty(sysUserVO) ? StrUtil.EMPTY : sysUserVO.getUserName());
        projSyncApiLogs.setCustomername(hospitalName);
        projSyncApiLogs.setDatas(message);
        syncApiLogsMapper.insert(projSyncApiLogs);
    }

    /**
     * 单体医院项目上线
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result projectOnlineForNotArea(ProjOnlineStepDTO dto) throws Exception {
        // 查询当前项目下的医院信息
        List<ProjOnlineStepDetailDTO> detailDTOList = new ArrayList<>();
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        selectHospitalDTO.setCustomInfoId(dto.getCustomInfoId());
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(dto.getProjectInfoId());
        List<ProjOnlineStepDetailDTO> dtoList = new ArrayList<>();
        try {
            if (hospitalInfoList != null && hospitalInfoList.size() > 0) {
                for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                    ProjOnlineStepDetailDTO detailDTO = new ProjOnlineStepDetailDTO();
                    detailDTO.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                    dtoList.add(detailDTO);
                }
                sendSysManageBatch(dtoList, projProjectInfo);
            }
        } catch (Exception ese) {
            SysUserVO currentUser = userHelper.getCurrentUser();
            //添加日志
//            addSyncApiLog("上线开启三十分钟限制报错 ==" + JSONUtil.toJsonStr(projProjectInfo)
//                    + "【返回信息：】 ==, {}" + ese.getMessage(), StrUtil.EMPTY, currentUser);
            log.error("上线开启三十分钟限制，发生异常，projProjectInfo={}，currentUser={}，errMsg={}，stackInfo=", JSONUtil.toJsonStr(projProjectInfo), JSON.toJSONString(currentUser), ese.getMessage(), ese);
        }
//        // 只有首期项目时 才会解除云健康30分钟登录限制
        if (projProjectInfo.getHisFlag() == 1) {
            // 1. 客户首期项目上线时，判断为老体系的客户，状态更新状态为老换新
            //  实现思路：首期项目上线，直接项目ID、客户类型（固定为3老体系）执行
            //  调用原则：不影响主线上线流程
            try {
                reportCustomInfoMapper.updateByProjectInfoData(projProjectInfo);
                reportCustomInfoMapper.updateByProjectInfoDetailData(projProjectInfo);
            } catch (Exception e) {
                log.error("修改数据失败", e);
            }
            for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                try {
                    // 更新医院标状态为已上线
                    hospitalInfo.setHospitalOpenStatus(31);
                    hospitalInfo.setOnlineTime(new Timestamp(System.currentTimeMillis()));
                    hospitalInfoMapper.updateById(hospitalInfo);
                } catch (Exception e) {
                    log.error("更新医院标状态为已上线失败", e);
                }
                ProjOnlineStepDetailDTO dto1 = new ProjOnlineStepDetailDTO();
                dto1.setCustomInfoId(dto.getCustomInfoId());
                dto1.setProjectInfoId(dto.getProjectInfoId());
                dto1.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                detailDTOList.add(dto1);
                //每上线一家医院需要更新云健康中 “系统管理”的医院权限
//                sendSysManage(hospitalInfo.getHospitalInfoId());
                // 患者档案合并
                mergePatientComm(hospitalInfo.getHospitalInfoId());

                // 测试平台同步上线状态
                try {
                    List<Long> taskIds = new ArrayList<>();
                    AutoCheckForTask autoCheckForTask = new AutoCheckForTask();
                    autoCheckForTask.setTaskIds(taskIds);
                    autoCheckForTask.setCloudHospitalId(hospitalInfo.getCloudHospitalId());
                    autoCheckForTask.setOrgId(hospitalInfo.getOrgId());
                    autoCheckService.taskClose(autoCheckForTask);
                } catch (Exception e) {
                    log.error("测试平台同步上线状态失败", e);
                }
            }
            // 上线后发消息到中层（当前部门经理） + 高层 + 交付系统管理员 + PMO + 项目经理
            this.sendMessageInfo(projProjectInfo);
        }
        // 2024.12.05 新增：单体医院上线时要更新医院上线状态
        try {
            for (ProjOnlineStepDetailDTO dtoDto : detailDTOList) {
                HospitalOnlineToDetailDTO onlineToDetailDTO = HospitalOnlineToDetailDTO.builder()
                        .hospitalInfoId(dtoDto.getHospitalInfoId())
                        .projectInfoId(dtoDto.getProjectInfoId())
                        .customInfoId(dtoDto.getCustomInfoId())
                        .onlineStatus(WorkStatusEnum.ONLINE.getCode())
                        .onlineTime(new Date())
                        .build();
                procedureOrderProductService.saveOrUpdateProjHospitalOnlineDetail(onlineToDetailDTO);
            }
        } catch (Exception e) {
            log.error("单体项目上线-保存医院上线明细数据失败", e);
        }

        // 修改上线步骤主表信息
        ProjOnlineStep projOnlineStep = new ProjOnlineStep();
        projOnlineStep.setProjOnlineStepId(dto.getProjOnlineStepId());
        projOnlineStep.setStatus(1);
        projOnlineStepMapper.updateById(projOnlineStep);
        //获取工单信息
        ProjOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(projProjectInfo.getOrderInfoId());
        if (orderInfo == null) {
            throw new RuntimeException("修改项目自身状态-上线状态-同步运营平台节点数据后更新, 未查询到运营平台工单信息");
        }
        // 判断是否全部医院上线，当全部医院上线时 更新项目表中上线状态和 运营平台工单状态为“上线”
        updateMilestoneByProjectInfoId(dto.getProjectInfoId());
        // 更新产品为上线状态
        updateProductExcutionStatus(dto.getProjectInfoId());
        switch (orderInfo.getDeliveryOrderType()) {
            case 1:// 软件
                updateNodeStatus(dto.getProjectInfoId(), SOFTWARE_ONLINE);
                break;
            case 8:// 外采软件
                updateNodeStatus(dto.getProjectInfoId(), PURCHASE_SOFTWARE_ONLINE);
                break;
            default:
                break;
        }
        //云健康数据库权限收回
        updateSchemaGrant(dto.getProjectInfoId(), null, null, "1");
        ProjOnlineStepSuccessVO projOnlineStepSuccessVO = resultDataInfo(dto.getProjectInfoId());
        return Result.success(projOnlineStepSuccessVO);
    }

    /**
     * 首期上线后发消息到中层（当前部门经理） + 高层 + 交付系统管理员 + PMO + 项目经理
     *
     * @param projProjectInfos
     */
    @Override
    public void sendMessageInfo(ProjProjectInfo projProjectInfos) {
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projProjectInfos.getProjectInfoId());
        SysUser sysUser = sysUserMapper.selectById(projProjectInfo.getProjectLeaderId());
        SysDept sysDept = sysDeptMapper.selectYunYingId(sysUser.getDeptId());
        StringBuffer sb = new StringBuffer("【" + projProjectInfo.getProjectName() + "】");
        String hisFlagStr = projProjectInfo.getHisFlag() == 1 ? "首期" : "非首期";
        sb.append("【").append(hisFlagStr).append("】");
        sb.append("工单号【").append(projProjectInfo.getProjectNumber()).append("】已正式上线，请知晓！");
        sb.append("项目实施团队:").append(sysDept.getDeptName());
        sb.append(",项目经理:").append(sysUser.getUserName());
        sb.append(",上线时间:").append(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date()));
        if (projProjectInfo.getSettleInTime() != null) {
            try {
                sb.append(",上线用时:").append(com.msun.csm.util.DateUtil.getDifferentDays(projProjectInfo.getSettleInTime(), new Date())).append("天");
            } catch (Exception e) {
                log.error("发送上线消息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setContent(sb.toString());
        messageParam.setTitle("【" + projProjectInfo.getProjectName() + "】上线通知");
        // 上线后发消息到中层（当前部门经理） + 高层 + 交付系统管理员 + PMO + 项目经理
        List<Long> sysUserIds = sysUserMapper.selectSendSysUserIds(projProjectInfo.getProjectInfoId());
        messageParam.setMessageTypeId(7003L);
        messageParam.setMessageToCategory(MsgToCategory.SYS.getCode());
        messageParam.setSysUserIds(sysUserIds);
        sendMessageService.sendMessage(messageParam);
    }

    /**
     * 根据项目id更新产品状态
     *
     * @param projectInfoId 项目id
     */
    @Override
    public void updateProductExcutionStatus(Long projectInfoId) {
        List<ProjOrderProduct> orderProducts =
                projOrderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("project_info_id",
                        projectInfoId));
        if (CollUtil.isNotEmpty(orderProducts)) {
            orderProducts.forEach(e -> {
                e.setProductExcutionStatus(NumberEnum.NO_1.num());
            });
            projOrderProductMapper.updateExcutionStatusByIds(orderProducts);
        } else {
            log.warn("未查询到上线产品. projectInfoId: {}", projectInfoId);
        }
    }

    /**
     * 解析同步患者智能返回数据
     *
     * @param hzznSyncResultList
     * @return
     */
    private String analysisHzznSyncResult(List<HzznSyncResult> hzznSyncResultList) {
        List<String> successHospitallist = new ArrayList<>();
        String failMsg = "";
        for (HzznSyncResult hzznSyncResult : hzznSyncResultList) {
            // 0标识成功， 成功后如果商户id不为空则保存数据库
            if (0 == hzznSyncResult.getState()) {
                successHospitallist.add(hzznSyncResult.getHospitalId());
                if (org.apache.commons.lang3.StringUtils.isNotBlank(hzznSyncResult.getPartnerId())) {
                    HzzDeliverHospital hospital = new HzzDeliverHospital();
                    hospital.setDeliverId(hzznSyncResult.getDeliverId());
                    hospital.setHospitalId(hzznSyncResult.getHospitalId());
                    hospital.setPartnerId(hzznSyncResult.getPartnerId());
                    hzzDeliverHospitalService.updateHzznDeliverHospital(hospital);
                }
            } else {
                failMsg = hzznSyncResult.getMessage();
            }
        }
        if (successHospitallist.size() > 0) {
            return "true";
        } else {
            return failMsg;
        }
    }


    /**
     * ETL对接产品系统接口
     *
     * @param projHospitalInfo
     * @param startDate
     * @param endDate
     * @return
     */
    private Map<String, Object> syncEtlHlyyData(ProjHospitalInfo projHospitalInfo, Date startDate, Date endDate) {
        // 先开启定时任务
        List<Map<String, Object>> stringObjectList = getProductScheduleInfo(projHospitalInfo);
        if (stringObjectList != null && stringObjectList.size() > 0) {
            enableScheduleInfo(stringObjectList);
        }
        // 调度日志
        Map<String, Object> param = performProductTask(projHospitalInfo, startDate, endDate);
        // 调度任务
        return param;
    }

    /**
     * 查询定时调度运行状态
     *
     * @param projHospitalInfo
     * @return
     */
    private List<Map<String, Object>> getProductScheduleInfo(ProjHospitalInfo projHospitalInfo) {
        List<Map<String, Object>> list = new ArrayList<>();
        String url = widthTableUrl + "/etl/productOperation/getProductScheduleInfo";
        Map<String, Object> param = new HashMap<>();
        param.put("hospitalId", projHospitalInfo.getCloudHospitalId());
        param.put("orgId", projHospitalInfo.getOrgId());
        param.put("productId", "1");
        param.put("scheduleName", "合理用药抽取调度");
        try {
            log.info("调用合理用药抽取调度接口getProductScheduleInfo,参数:{}", JSONObject.toJSONString(param));
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            String code = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("code"));
            if ("200".equals(code)) {
                list = (List) stringObjectMap.get("data");
            }
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
        }
        return list;
    }

    /**
     * 开启关闭的定时调度
     *
     * @param stringObjectList
     */
    private void enableScheduleInfo(List<Map<String, Object>> stringObjectList) {
        List<Map<String, Object>> list = new ArrayList<>();
        String url = widthTableUrl + "/etl/productOperation/enableScheduleInfo";
        Map<String, Object> param = new HashMap<>();
        param.put("scheduleJobs", stringObjectList);
        param.put("productId", "1");
        param.put("operatorName", "交付平台");
        try {
            log.info("调用合理用药抽取调度接口enableScheduleInfo,参数:{}", JSONObject.toJSONString(param));
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("开启关闭的定时调度返回参数=====> {}", stringObjectMap);
            String code = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("code"));
            if ("200".equals(code)) {
                log.error("开启关闭的定时调度成功=====> {}", code);
            }
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
        }
    }

    /**
     * 执行合理用药任务
     *
     * @param projHospitalInfo
     * @param startDate
     * @param endDate
     * @return
     */
    private Map<String, Object> performProductTask(ProjHospitalInfo projHospitalInfo, Date startDate, Date endDate) {
        Map<String, Object> result = new HashMap<>(10);
        String url = widthTableUrl + "/etl/productOperation/performProductTask";
        Map<String, Object> param = new HashMap<>();
        param.put("hospitalId", projHospitalInfo.getCloudHospitalId());
        param.put("orgId", projHospitalInfo.getOrgId());
        param.put("dispatchSource", "1");
        param.put("operatorName", "交付平台");
        Map<String, String> paramTime = Maps.newHashMap();
        paramTime.put("$PRO_START_DATE$", DateUtil.format(startDate, "yyyy-MM-dd"));
        paramTime.put("$PRO_END_DATE$", DateUtil.format(endDate, "yyyy-MM-dd"));
        paramTime.put("$START_DATE$", DateUtil.format(startDate, "yyyy-MM-dd HH:mm:ss"));
        paramTime.put("$END_DATE$", DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss"));
        param.put("proArgs", paramTime);
        param.put("taskAlias", "合理用药抽取任务");
        try {
            log.info("调用合理用药抽取调度接口performProductTask,参数:{}", JSONObject.toJSONString(param));
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("合理用药抽取任务返回参数=====> {}", stringObjectMap);
            String code = com.msun.csm.util.StringUtils.nvl(stringObjectMap.get("code"));
            if ("200".equals(code)) {
                return stringObjectMap;
            } else {
                result.put("code", "500");
                result.put("msg", "执行合理用药任务失败");
            }

        } catch (Exception e) {
            result.put("code", "500");
            result.put("msg", "执行合理用药任务失败");
            log.error("====>" + e.getMessage());
        }
        return result;
    }

}
