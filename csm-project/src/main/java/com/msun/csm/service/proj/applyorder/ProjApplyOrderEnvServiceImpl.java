package com.msun.csm.service.proj.applyorder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.projapplyorder.ProjApplyOrderResultTypeEnum.APPLYED;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.CLOUD_DEPLOY;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.CLOUD_OPEN;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.OpenStatusEnum;
import com.msun.csm.common.enums.active.ActiveEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.projapplyorder.CloudEnvTypeEnum;
import com.msun.csm.common.enums.projapplyorder.HospitalTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyOrderResultTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyTypeEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CloudServiceTypeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.entity.oldimsp.ProjectDevelopPortApply;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecord;
import com.msun.csm.dao.entity.proj.ProjApplyOrderNodeRecord;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProduct;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjCustomVsProjectTypeEntity;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.oldimsp.ProjectDevelopPortApplyMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderHospitalRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderNodeRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementCheckMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.model.csm.CsmParamerDTO;
import com.msun.csm.model.dto.ApplyOrderNodeRecordParamDTO;
import com.msun.csm.model.dto.HospitalDto;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.ThirdApplyDto;
import com.msun.csm.model.dto.applyorder.ApplyEmpowerDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderDTO;
import com.msun.csm.model.dto.yunweiplatform.SaveEnvCustomDTO;
import com.msun.csm.model.dto.yunweiplatform.SaveEnvDTO;
import com.msun.csm.model.dto.yunweiplatform.SaveEnvHospital;
import com.msun.csm.model.dto.yunweiplatform.SaveEnvProduct;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.formlibnew.FormlibResourceUniteService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.OnlineStepService;
import com.msun.csm.service.proj.ProjCustomInfoService;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckSaleService;
import com.msun.csm.service.proj.ProjSurveyReportService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.service.yunying.YunYingService;

/**
 * 区县医院实现
 */
@Slf4j
@Service
public class ProjApplyOrderEnvServiceImpl implements ProjApplyOrderEnvService {

    @Value("${project.feign.interface-demand.url}")
    private String interfaceDemandUrl;

    @Value("${project.feign.interface-demand.updateDemand-method}")
    private String interfaceDemandMethod;

    @Value("${requestUrl}")
    private String requestUrl;

    private static final String LOG_PRE = "部署订单.";

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private UserUtil userUtil;

    @Resource
    private ProjApplyOrderMapper applyOrderMapper;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;


    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjApplyOrderEnvService applyOrderEnvService;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjApplyOrderNodeRecordMapper applyOrderNodeRecordMapper;

    @Resource
    @Lazy
    private YunYingService yunYingService;

    @Resource
    private OnlineStepService onlineStepService;

    @Resource
    private ProjCustomCloudServiceMapper cloudServiceMapper;

    @Resource
    private ProjProjectSettlementCheckMapper settlementCheckMapper;

    @Resource
    private ProjProjectSettlementCheckSaleService settlementCheckSaleService;

    @Resource
    private ProjectDevelopPortApplyMapper developPortApplyMapper;

    @Resource
    private ImspSysUserMapper imspSysUserMapper;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private ExceptionMessageService messageService;

    @Resource
    private CommonService commonService;

    @Lazy
    @Resource
    private ProjCustomVsProjectTypeMapper projCustomVsProjectTypeMapper;

    @Lazy
    @Resource
    private ProjSurveyReportService surveyReportService;

    @Resource
    @Lazy
    private ProjApplyOrderHospitalRecordMapper projApplyOrderHospitalRecordMapper;

    @Lazy
    @Resource
    private SendMessageService sendMessageService;
    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Lazy
    @Resource
    private FormlibResourceUniteService formlibResourceUniteService;

    /**
     * 获取上传使用的dto参数
     *
     * @param domainName 域名
     * @param applyOrder 工单
     * @param hospitals  医院集合
     * @param products   工单产品集合
     * @return
     */
    @Override
    public SaveEnvDTO getDevParamDto(ProjApplyOrderDTO dto2, String domainName, ProjApplyOrder applyOrder,
                                     List<ProjHospitalInfoRelative> hospitals, List<ProjApplyOrderProduct> products) {
        // 查询用户信息
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(applyOrder.getCustomInfoId());
        if (ObjectUtil.isEmpty(customInfo)) {
            throw new RuntimeException(MessageFormat.format("{0} 未获取用户信息.", LOG_PRE));
        }
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(applyOrder.getProjectInfoId());
        if (ObjectUtil.isEmpty(projectInfo)) {
            throw new RuntimeException(MessageFormat.format("{0} 未获取项目信息.", LOG_PRE));
        }
        // 获取主院信息
        ProjHospitalInfo finalMainHospital = getMainHospitalInfo(applyOrder.getHospitalInfoId());
        // 设置医院
        ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(projectInfo.getProjectType());
        if (ObjectUtil.isEmpty(projectTypeEnums)) {
            throw new RuntimeException("未查询到项目类型.");
        }
        boolean isRegion = ProjectTypeEnums.REGION.getCode() == projectTypeEnums.getCode().intValue();
        SaveEnvDTO saveEnvDTO = getDtoMainInfo(applyOrder.getApplyNum(), customInfo, projectInfo);
        // 设置是否为电销客户
        saveEnvDTO.setTelesalesFlag(customInfo.getTelesalesFlag());
        List<SaveEnvCustomDTO> saveEnvCustomDTOList = getEnvApplyCountryList(isRegion, domainName, hospitals,
                applyOrder, finalMainHospital);
        saveEnvDTO.setDeployResourceApplyCountryDTOList(saveEnvCustomDTOList);
        // 设置产品
        List<SaveEnvProduct> saveEnvProductList = getSaveEnvCustomDTOList(products);
        saveEnvDTO.setDeployResourceApplyProductDTOList(saveEnvProductList);
        // 添加运营平台实施客户id
        saveEnvDTO.setCustomerId(customInfo.getYyCustomerId());
        // 设置部署类型
        CloudEnvTypeEnum envTypeEnum = CloudEnvTypeEnum.getEnumByCode(applyOrder.getCloudEnv());
        // 若是共有则envTypeEnum.code = 0, 私有则为 1, 可以区分isPrivate 0:非私有, 1:私有;
        assert envTypeEnum != null;
        if (envTypeEnum != null && envTypeEnum.getCode() == CloudEnvTypeEnum.PRIVATE.getCode()) {
            saveEnvDTO.setIsPrivate(NumberEnum.NO_1.description());
        } else {
            saveEnvDTO.setIsPrivate(NumberEnum.NO_0.description());
        }
        // 设置方案分公司经理复核意见
        List<ProjProjectSettlementCheck> settlementChecks =
                settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_info_id",
                        projectInfo.getProjectInfoId()).eq("check_node", CheckNodeEnum.BRANCH_MANAGER_AUDIT.getCode()));
        if (CollUtil.isNotEmpty(settlementChecks)) {
            saveEnvDTO.setSchemeAuditInfo(settlementChecks.get(0).getCheckContent());
        }
        // 查询云资源部署节点名称, 查不到就传空
        try {
            ProjProjectOrderRelation orderRelation =
                    mainService.getCloudResourceRelationBothType(applyOrder.getProjectInfoId());
            if (ObjectUtil.isNotEmpty(orderRelation)) {
                Long busiId = orderRelation.getBussinessInfoId();
                ProjCustomCloudService cloudService = cloudServiceMapper.selectById(busiId);
                if (ObjectUtil.isNotEmpty(cloudService)) {
                    saveEnvDTO.setCloudName(cloudService.getDeployNodeName());
                    // 若是众阳云
                    if (CloudServiceTypeEnum.MSUN_CLOUDE.getCode() == cloudService.getCloudServiceType()) {
                        saveEnvDTO.setCloudType(NumberEnum.NO_0.num());
                    } else {
                        saveEnvDTO.setCloudType(NumberEnum.NO_1.num());
                    }
                }
            }

            List<ProjCustomCloudService> cloudService = cloudServiceMapper.selectList(new QueryWrapper<ProjCustomCloudService>().eq("custom_info_id", applyOrder.getCustomInfoId()).eq("cloud_service_type", dto2.getCloudEnv() == 0 ? 1 : dto2.getCloudEnv() == 1 ? 3 : 2));
            if (ObjectUtil.isNotEmpty(cloudService) && cloudService.size() > 0) {
                saveEnvDTO.setCloudName(cloudService.get(0).getDeployNodeName());
            }
        } catch (Throwable e) {
            log.error("查询客户云信息异常. projectInfoId: {}, e=", applyOrder.getProjectInfoId(), e);
        }
        try {
            // 查询提交申请占用域名preSubmitId
            // 查询该项目是否已提前申请过域名
            List<ProjCustomVsProjectTypeEntity> listType = projCustomVsProjectTypeMapper.selectList(new QueryWrapper<ProjCustomVsProjectTypeEntity>().eq("custom_info_id", projectInfo.getCustomInfoId()).eq("project_type", projectInfo.getProjectType()));
            if (CollUtil.isNotEmpty(listType) && listType.size() > 0) {
                saveEnvDTO.setPreSubmitId(listType.get(0).getPreSubmitId());
            }
        } catch (Exception e) {
            log.error("提前获取域名===" + e.getMessage());
        }

        return saveEnvDTO;
    }

    /**
     * 用于部署申请
     *
     * @param products
     * @return
     */
    private List<SaveEnvProduct> getSaveEnvCustomDTOList(List<ProjApplyOrderProduct> products) {
        List<SaveEnvProduct> saveEnvProductList = new ArrayList<>();
        products.forEach(e -> {
            SaveEnvProduct saveEnvProduct = new SaveEnvProduct();
            saveEnvProduct.setCustomerProductName(e.getProductName());
            saveEnvProduct.setCustomerProductCode(StrUtil.isBlank(e.getYyProductCode()) ? StrUtil.EMPTY
                    : e.getYyProductCode());
            saveEnvProduct.setCustomerProductId(e.getProductArrangeId());
            saveEnvProductList.add(saveEnvProduct);
        });
        return saveEnvProductList;
    }

    /**
     * 设置医院信息
     *
     * @param isRegion          是否是区域
     * @param domainName        域名
     * @param hospitals         医院
     * @param applyOrder        申请单
     * @param finalMainHospital 主院信息
     * @return List<SaveEnvCustomDTO>
     */
    private List<SaveEnvCustomDTO> getEnvApplyCountryList(boolean isRegion, String domainName,
                                                          List<ProjHospitalInfoRelative> hospitals,
                                                          ProjApplyOrder applyOrder,
                                                          ProjHospitalInfo finalMainHospital) {
        List<SaveEnvCustomDTO> saveEnvCustomDTOList = new ArrayList<>();
        SaveEnvCustomDTO saveEnvCustomDTO = new SaveEnvCustomDTO();
        saveEnvCustomDTOList.add(saveEnvCustomDTO);
        // 获取日门诊量(客户下所有医院数量) 并 添加所属属医院
        AtomicInteger dayClinicCount = new AtomicInteger();
        AtomicInteger bedCount = new AtomicInteger();
        AtomicInteger termnalCount = new AtomicInteger();
        AtomicInteger peopleCount = new AtomicInteger();
        AtomicInteger clinicCount = new AtomicInteger();
        AtomicLong annualIncomeCount = new AtomicLong();
        List<SaveEnvHospital> saveEnvHospitalList = new ArrayList<>();
        // 查询客户是否是电销
        hospitals.forEach(e -> {
            dayClinicCount.addAndGet(ObjectUtil.isEmpty(e.getHospitalOutPatientCount()) ? 0
                    : e.getHospitalOutPatientCount().intValue());
            bedCount.addAndGet(ObjectUtil.isEmpty(e.getHospitalBedCount()) ? 0 : e.getHospitalBedCount().intValue());
            termnalCount.addAndGet(e.getTerminalCount());
            peopleCount.addAndGet(e.getPopulation());
            clinicCount.addAndGet(e.getClinicCount());
            if (ObjectUtil.isNotEmpty(e.getHospitalAnnualIncome())) {
                annualIncomeCount.addAndGet(e.getHospitalAnnualIncome());
            }
            SaveEnvHospital saveEnvHospital = new SaveEnvHospital();
            saveEnvHospital.setCustomerInfoId(String.valueOf(e.getHospitalInfoId()));
            saveEnvHospital.setHospitalName(e.getHospitalName());
            saveEnvHospital.setAdministrativeDivisions(e.getAdministrativeDivisions());
            saveEnvHospital.setAdministrativeCode(e.getAdministrativeCode());
            saveEnvHospital.setIsMainHospital(getIsMainHospital(applyOrder, e));
            String hospitalType;
            // 对单体和区域做区分
            if (!isRegion) {
                if (NumberEnum.NO_1.num().toString().equals(saveEnvHospital.getIsMainHospital())) {
                    hospitalType = HospitalTypeEnum.INDIVIDUAL.getDevCode();
                } else {
                    hospitalType = HospitalTypeEnum.HospitalTypeBranchEnum.SINGLE_BRANCH.getDevCode();
                }
                // 单体医院设置主院信息。这里只有分院设置主院信息. 主院为空
                saveEnvHospital.setMainHospitalName(e.getHospitalInfoId().intValue() != finalMainHospital.getHospitalInfoId() ? e.getHospitalName() : null);
            } else {
                hospitalType = HospitalTypeEnum.HOSPITAL.getDevCode();
            }
            saveEnvHospital.setHospitalType(hospitalType);
            // 这是新增的参数; 提前申请域名时应用
            saveEnvHospital.setHospitalId(e.getCloudHospitalId());
            saveEnvHospitalList.add(saveEnvHospital);
        });
        saveEnvCustomDTO.setDeployResourceApplyHospitalDTOList(saveEnvHospitalList);
        saveEnvCustomDTO.setDayClinicCount(dayClinicCount.intValue());
        saveEnvCustomDTO.setDomainName(domainName);
        saveEnvCustomDTO.setBedCount(bedCount.intValue());
        saveEnvCustomDTO.setAdministrativeDivisions(hospitals.get(0).getAdministrativeDivisions());
        saveEnvCustomDTO.setAdministrativeCode(hospitals.get(0).getAdministrativeCode());
        saveEnvCustomDTO.setTerminalCount(termnalCount.intValue());
        saveEnvCustomDTO.setPeopleCount(peopleCount.intValue());
        saveEnvCustomDTO.setClinicCount(clinicCount.intValue());
        saveEnvCustomDTO.setCdssType(StrUtil.EMPTY);
        saveEnvCustomDTO.setCustomerManager(StrUtil.EMPTY);
        saveEnvCustomDTO.setCustomerPersonnel(StrUtil.EMPTY);
        saveEnvCustomDTO.setHospitalLevel(StrUtil.EMPTY);
        saveEnvCustomDTO.setRemarks(finalMainHospital.getHospitalName());
        saveEnvCustomDTO.setAnnualIncome(Double.parseDouble(ProjApplyOrderDescServiceImpl.convertToHundredMillion(annualIncomeCount.get())));
        return saveEnvCustomDTOList;
    }


    /**
     * 获取是否是主院的判断结果
     * "1" 主院, "0" 非主院
     *
     * @param applyOrder           申请单
     * @param hospitalInfoRelative 医院
     * @return String
     */
    private static String getIsMainHospital(ProjApplyOrder applyOrder, ProjHospitalInfoRelative hospitalInfoRelative) {
        return applyOrder.getHospitalInfoId() == hospitalInfoRelative.getHospitalInfoId().longValue()
                ? NumberEnum.NO_1.description() : NumberEnum.NO_0.description();
    }

    @Override
    public ProjHospitalInfo getMainHospitalInfo(Long mainHospitalInfoId) {
        if (ObjectUtil.isNotEmpty(mainHospitalInfoId)) {
            List<ProjHospitalInfo> hospitalInfos =
                    hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("hospital_info_id",
                            mainHospitalInfoId));
            if (CollUtil.isNotEmpty(hospitalInfos)) {
                return hospitalInfos.get(0);
            }
        }
        return null;
    }

    /**
     * 获取dto参数中主要信息
     *
     * @param applyNum    申请单号
     * @param customInfo  客户id
     * @param projectInfo 项目id
     * @return SaveEnvDTO
     */
    private SaveEnvDTO getDtoMainInfo(Long applyNum, ProjCustomInfo customInfo, ProjProjectInfo projectInfo) {
        SaveEnvDTO saveEnvDTO = new SaveEnvDTO();
        saveEnvDTO.setSubmitName(userUtil.getCurrentUserName());
        saveEnvDTO.setCustomerName(customInfo.getCustomName());
        saveEnvDTO.setCountryCount(1);
        saveEnvDTO.setDeployType(ProjApplyTypeEnum.FIRST_EVN_APPLY.getDevCode());
        saveEnvDTO.setDeployMod(ProjectTypeEnums.getEnum(projectInfo.getProjectType()).getDevCode());
        saveEnvDTO.setDeliverPlatformApplyId(applyNum);
        // 查询该项目是否已提前申请过域名
        List<ProjCustomVsProjectTypeEntity> listType =
                projCustomVsProjectTypeMapper.selectList(new QueryWrapper<ProjCustomVsProjectTypeEntity>()
                        .eq("custom_info_id", projectInfo.getCustomInfoId())
                        .eq("project_type", projectInfo.getProjectType()));
        if (CollUtil.isNotEmpty(listType) && listType.size() > 0) {
            saveEnvDTO.setPreSubmitId(listType.get(0).getPreSubmitId());
        }
        return saveEnvDTO;
    }

    /**
     * 新环境
     *
     * @param applyOrder 工单
     * @param dto        申请参数
     * @return Result<String>
     */
    @Override
    public Result<String> envHandle(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        // 已审核处理
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.AUDITED.getCode()) {
            applyOrderEnvService.auditHandler(applyOrder, dto);
        }
        // 驳回
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.REJECTED.getCode()) {
            return applyOrderEnvService.rejectedHandler(applyOrder, dto);
        }
        // 已部署完成处理
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.ENV_DEPLOYED.getCode()) {
            Result<String> result = applyOrderEnvService.deployedHandler(applyOrder, dto);
            log.info("部署完成处理, applyNum: {}. result: {}", applyOrder.getApplyNum(), result);
            // 同步运营平台工单信息
            try {
                updateNodeStatusApplyOrder(applyOrder, CLOUD_DEPLOY);
            } catch (Exception e) {
                log.error("更新运营平台云资源工单.部署状态失败. e: {}, e=", e.getMessage(), e);
                throw e;
            }
            return Result.success();
        }
        // 已交付处理
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.DELIVERED.getCode()) {
            try {
                Result<String> result = applyOrderEnvService.deliverHandler(applyOrder, dto);
                if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
                    // 发送消息给系统管理员交付失败的消息
                    sendToSystemManager(applyOrder);
                } else {
                    updateDemandByDemandId(applyOrder.getProjectInfoId());
                    // 发送给提交人交付成功通知
                    commonService.sendToSinglePerson(applyOrder.getProjectInfoId(), "新环境已完成交付, 请知晓!确认无误后在交付平台进行环境验收！",
                            DictMessageTypeEnum.DEPLOY_DEPLOYED_KOWN.getId(),
                            applyOrder.getCreaterId());
                    // 调用打印平台进行数据处理
                    surveyReportService.preLaunchHospitalPushOnSiteSend(applyOrder.getProjectInfoId());

                    // 调用表单资源库进行数据导入
                    FormlibResourceUniteSelectListReq reqform = new FormlibResourceUniteSelectListReq();
                    reqform.setProjectInfoId(applyOrder.getProjectInfoId());
                    formlibResourceUniteService.importFormlibToYjk(reqform);

                    return Result.success();
                }

            } catch (Throwable e) {
                log.error("新环境部署异常. message:{}, e=", e.getMessage(), e);
                // 发送消息给系统管理员交付失败的消息
                sendToSystemManager(applyOrder);
            }
        }
        return Result.fail();
    }

    /**
     * 同步医院信息到接口开发平台
     *
     * @param projectInfoId
     */
    public void updateDemandByDemandId(Long projectInfoId) {
        // 转换老系统项目id
        TmpProjectNewVsOld tmpProjectNewVsOld =
                tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>()
                        .eq("new_project_info_id", projectInfoId)
                );
        try {
            List<ProjectDevelopPortApply> projectDevelopPortApplyList =
                    developPortApplyMapper.selectList(new QueryWrapper<ProjectDevelopPortApply>().
                            eq("project_id", tmpProjectNewVsOld.getOldProjectInfoId())
                    );
            if (CollectionUtil.isNotEmpty(projectDevelopPortApplyList)) {
                projectDevelopPortApplyList.forEach(apply -> {
                    //组装传参
                    String paraJsonString = insertDemandIntoData(apply);
                    try {
                        HttpHeaders newHeaders = new HttpHeaders();
                        RestTemplate restTemplate = new RestTemplate();
                        newHeaders.setContentType(MediaType.APPLICATION_JSON);
                        // 组装 接口开发平台参数
                        String url = interfaceDemandUrl + interfaceDemandMethod;
                        newHeaders.add("Content-Type", "application/json");
                        newHeaders.add("gateway", "app");
                        HttpEntity<?> requestEntity = new HttpEntity<>(paraJsonString, newHeaders);
                        ResponseEntity<ResponseResult> response =
                                restTemplate.exchange(url, HttpMethod.POST, requestEntity, ResponseResult.class);
                        log.info("同步医院信息到接口开发平台 === ,{}", response.getBody());
                    } catch (Exception e) {
                        log.info("同步医院信息到接口开发平台失败!!!!  , " + e.getMessage());
                    }
                });
            }
        } catch (Exception e) {
            log.error("更新需求信息失败. e: {}, e=", e.getMessage(), e);
        }
    }

    /**
     * 接口开发平台参数信息组装
     *
     * @param projectDevelopPortApply
     * @return
     */
    @SneakyThrows
    String insertDemandIntoData(ProjectDevelopPortApply projectDevelopPortApply) {
        try {
            ThirdApplyDto thirdApplyDto = new ThirdApplyDto();
            thirdApplyDto.setDemandId(projectDevelopPortApply.getId().toString());
            thirdApplyDto.setDemandName(projectDevelopPortApply.getPortName());
            StringBuilder description = new StringBuilder();
            description.append("【三方接口】\n")
                    .append("使用场景：\n").append(projectDevelopPortApply.getServiceDesc()).append("\n")
                    .append("备注：\n")
                    .append(projectDevelopPortApply.getRemark() == null ? "" : projectDevelopPortApply.getRemark())
                    .append("\n");
            thirdApplyDto.setDemandDescription(description.toString());
            ImspSysUser sysUser = imspSysUserMapper.selectById(projectDevelopPortApply.getCreateUserId());
            if (ObjectUtil.isEmpty(sysUser)) {
                throw new Exception("用户不存在");
            }
            thirdApplyDto.setAuthor(sysUser.getName());
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            thirdApplyDto.setSubmitTime(format.format(new Date()));
            thirdApplyDto.setDemandState(0);
            thirdApplyDto.setDataType(projectDevelopPortApply.getDevPortId());
            thirdApplyDto.setDataTypeName(projectDevelopPortApply.getDevPortName());
            //获取客服上传的文件
            StringBuilder picUrls = new StringBuilder();
            ProjectDevelopPortApply projectDevelopPortApply1 =
                    developPortApplyMapper.getUserIdAndFileUrl(projectDevelopPortApply.getId());
            //判断是否为空
            if (ObjectUtil.isNotEmpty(projectDevelopPortApply1) && ObjectUtil.isNotEmpty(projectDevelopPortApply1.getFileData())) {
                //路径替换
                String filePath = requestUrl + projectDevelopPortApply1.getFileData().replace("/msun/data/upload/", (
                        "/fileView/"));
                picUrls.append(filePath).append(",");
            }
            String releasePicUrls = "";
            if (ObjectUtil.isNotEmpty(projectDevelopPortApply1) && ObjectUtil.isNotEmpty(projectDevelopPortApply1.getFileData())) {
                releasePicUrls = picUrls.substring(0, picUrls.toString().lastIndexOf(","));
            }
            thirdApplyDto.setAttachedFileUrl(releasePicUrls);
            // 查询当前项目下的医院信息
            SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
            selectHospitalDTO.setProjectInfoId(
                    tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq(
                            "old_project_info_id", projectDevelopPortApply.getProjectId())
                    ).getNewProjectInfoId()
            );
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
            List<Long> orgIds = hospitalInfoList.stream().map(vo -> vo.getOrgId()).collect(Collectors.toList());
            if (orgIds != null && orgIds.size() > 0) {
                for (Long id : orgIds) {
                    if (ObjectUtil.isNotEmpty(id)) {
                        thirdApplyDto.setOrgId(Convert.toInt(id));
                    }
                }
            }
            //根据客户获取其所有的医院信息
            List<HospitalDto> hospitalDtos = new ArrayList<>();
            for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                HospitalDto hospitalDto = new HospitalDto();
                hospitalDto.setHospitalId(hospitalInfo.getCloudHospitalId().toString());
                hospitalDto.setHospitalName(hospitalInfo.getHospitalName());
                hospitalDto.setHospitalHost(hospitalInfo.getCloudDomain().replace("https://", ""));
                hospitalDto.setHospitalNetwork(hospitalInfo.getCloudDomain());
                hospitalDto.setEnvStatus("1");
                hospitalDtos.add(hospitalDto);
            }
            String customerIdListString = JSON.toJSONString(hospitalDtos, false);
            thirdApplyDto.setHospitalList(customerIdListString);
            String paraJsonString = JSON.toJSONString(thirdApplyDto, false);
            log.info("接口组装传参：{}", paraJsonString);
            return paraJsonString;
        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new Exception("接口数据异常");
        }
    }

    @Override
    public void sendToSystemManager(ProjApplyOrder applyOrder) {
        messageService.sendToSystemManager(applyOrder.getProjectInfoId(), setMessageContent(applyOrder, "交付"));
    }


    /**
     * 设置消息内容
     *
     * @param applyOrder 申请工单
     * @return String
     */
    @Override
    public String setMessageContent(ProjApplyOrder applyOrder, String nodeName) {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(applyOrder.getProjectInfoId());
        ProjOrderInfo orderInfo = mainService.getSoftOrderInfo(projectInfo.getProjectInfoId());
        return projectInfo.getProjectName() + nodeName + ", 失败通知：" + projectInfo.getProjectName()
                + "在" + DateUtil.now() + nodeName + "，失败！部署申请工单号：: " + applyOrder.getApplyNum() + ", 项目工单编号:" + orderInfo.getDeliveryOrderNo();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> auditHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        applyOrder.setIsLocalRoom(dto.getIsLocalRoom());
        applyOrder.setFirstDeployNode(dto.getFirstDeployNode());
        applyOrder.setDeployUserName(dto.getAssignerName());
        applyOrder.setDeployPhone(dto.getAssignerPhone());
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        // - 更新新老医院状态
        applyOrderHospitalService.updateHospitalOpenStatus(HospitalOpenStatusEnum.OPENING,
                applyOrderHospitalService.getHospitalIdsByApplyOrderId(applyOrder.getId()));
        // - 查询申请记录添加日志
        applyOrder = applyOrderService.getProjApplyOrder(dto.getDeliverPlatformApplyId());
        SysUser sysUser = extractedSysUser(dto);
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(StrUtil.EMPTY)
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        StringBuffer sb = new StringBuffer(";");
        if (ObjectUtil.isNotEmpty(dto.getAssignerName())) {
            sb.append("下一步部署人:").append(dto.getAssignerName()).append(";");
        }
        if (ObjectUtil.isNotEmpty(dto.getAssignerPhone())) {
            sb.append("联系电话:").append(dto.getAssignerPhone()).append(";");
        }
        Double dtime = projApplyOrderHospitalRecordMapper.selectRecordTimeLong(dto);
        if (dtime != null && dtime > 0) {
            sb.append("预计部署时长:").append(dtime).append("小时;");
        }
        applyOrderNodeRecordParamDTO.setOperateContent(applyOrderNodeRecordParamDTO.getOperateContent() + sb.toString());
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);

        try {
            ProjProjectInfo info = projectInfoService.selectByPrimaryKey(applyOrder.getProjectInfoId());
            List<Long> sysUserIds = new ArrayList<>();
            sysUserIds.add(464993509700489218L);
            if (ActiveEnum.PROD.getActive().equals(activeProfiles)) {
                sysUserIds.add(info.getProjectLeaderId());
                sysUserIds.add(applyOrder.getCreaterId());
            }
            SendMessageParam messageParam = new SendMessageParam();
            messageParam.setMessageTypeId(DictMessageTypeEnum.DEPLOYMENT_AUDIT_FINISH_REMINDER.getId());
            messageParam.setProjectInfoId(applyOrder.getProjectInfoId());
            Map<String, String> messageContentParam = new HashMap<>();
            messageContentParam.put("projectName", info.getProjectName());
            messageContentParam.put("orderNumber", String.valueOf(applyOrder.getApplyNum()));
            messageContentParam.put("userName", dto.getAssignerName());
            messageContentParam.put("hourTime", String.valueOf(dtime));

            messageParam.setMessageContentParam(messageContentParam);
            messageParam.setSysUserIds(sysUserIds);
            sendMessageService.sendMessage2(messageParam);
        } catch (Exception ope) {
            log.error("发送消息失败" + ope);
        }

        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> rejectedHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        SysUser sysUser = extractedSysUser(dto);
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        // - 查询申请记录添加日志
        applyOrder = applyOrderService.getProjApplyOrder(dto.getDeliverPlatformApplyId());
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(dto.getRemark())
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        // 更新医院产品-未开通状态
        List<ProjHospitalInfo> hospitals = applyOrderHospitalService.getHospitalInfoByApplyOrderId(applyOrder.getId());
        List<Long> productIds = applyOrderProductService.getProductIdListByApplyOrderId(applyOrder.getId());
        applyOrderHospitalService.updateHospitalOpenStatus(HospitalOpenStatusEnum.NOT_OPEN,
                hospitals.stream().map(ProjHospitalInfo::getHospitalInfoId).collect(Collectors.toList()));
        applyOrderProductService.updateProductOpenStatus(ProductOpenStatusEnum.NOT_OPEN, productIds,
                applyOrder.getProjectInfoId());
        String rejectContent = "新环境申请被驳回, 请知晓, 驳回原因: " + dto.getRemark();
        // 给pmo发送驳回消息
        commonService.sendToPmo(applyOrder.getProjectInfoId(), rejectContent);
        // 给提交人发送驳回消息
        commonService.sendToSinglePerson(applyOrder.getProjectInfoId(), rejectContent,
                DictMessageTypeEnum.DEPLOY_REJECT_KOWN.getId(), applyOrder.getCreaterId());
        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> deployedHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        SysUser sysUser = extractedSysUser(dto);
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(dto.getRemark())
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        StringBuffer sb = new StringBuffer(";");
        sb.append("下一步交付人:").append(dto.getOperateName()).append(";");
        if (ObjectUtil.isNotEmpty(sysUser.getPhone())) {
            sb.append("联系电话:").append(sysUser.getPhone()).append(";");
        }
        applyOrderNodeRecordParamDTO.setOperateContent(applyOrderNodeRecordParamDTO.getOperateContent() + sb.toString());
        applyOrder.setOperationPerson(applyOrderNodeRecordParamDTO.getOperator());
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        return Result.success();
    }

    /**
     * 更新运营节点状态
     *
     * @param applyOrder    工单
     * @param orderStepEnum 工单枚举
     */
    @Override
    public void updateNodeStatusApplyOrder(ProjApplyOrder applyOrder, OrderStepEnum orderStepEnum) {
        if (notCloudForm(applyOrder)) {
            return;
        }
        List<ProjApplyOrderNodeRecord> nodeRecords =
                applyOrderNodeRecordMapper.selectList(new QueryWrapper<ProjApplyOrderNodeRecord>().eq(
                        "apply_order_id", applyOrder.getId()).eq("node_type_code", APPLYED.getCode()));
        if (CollUtil.isNotEmpty(nodeRecords)) {
            ProjApplyOrderNodeRecord nodeRecord = nodeRecords.get(0);
            // 查询用户
            SysUser sysUser = sysUserMapper.getUserById(nodeRecord.getOperatorId());
            SysUserVO sysUserVO = BeanUtil.copyProperties(sysUser, SysUserVO.class);
            onlineStepService.updateNodeStatus(applyOrder.getProjectInfoId(), orderStepEnum, sysUserVO);
        }
    }

    /**
     * 是否有云资源工单（不是私有云和共享云）
     *
     * @param applyOrder 工单
     * @return boolean
     */
    private boolean notCloudForm(ProjApplyOrder applyOrder) {
        return applyOrder.getApplyType() != ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode()
                || applyOrder.getCloudEnv() == CloudEnvTypeEnum.PRIVATE.getCode()
                || (!mainService.hasCloudFormConfirm(applyOrder.getProjectInfoId()));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> deliverHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        JSONObject jsonObject = JSONObject.parseObject(dto.getRemark());
        applyOrderHospitalService.updateHospitalRelativeInfo(Long.valueOf(jsonObject.get("envId").toString()),
                ObjectUtil.isEmpty(jsonObject.get("envName")) ? StrUtil.EMPTY : jsonObject.get("envName").toString(),
                OpenStatusEnum.OPENED.getCode(), applyOrder.getCustomInfoId(), applyOrder.getId());
        SysUser sysUser = extractedSysUser(dto);
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(dto.getRemark())
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc() + ";下一步:环境确认无误后进行验收")
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        List<Long> productIds = applyOrderProductService.getProductIdListByApplyOrderId(applyOrder.getId());
        // 更新医院orgId和hospitalId
        Map<String, Object> remarkMap = new HashMap<>();
        if (StrUtil.isNotEmpty(dto.getRemark())) {
            remarkMap = JSONObject.parseObject(dto.getRemark());
        }
        if (ObjectUtil.isNotEmpty(remarkMap.get("domainUrl"))) {
            Map<String, Object> domainUrlMap;
            domainUrlMap = (Map<String, Object>) remarkMap.get("domainUrl");
            domainUrlMap.forEach((key, value) -> {
                ProjHospitalInfo projHospitalInfo = new ProjHospitalInfo();
                projHospitalInfo.setCloudDomain(value.toString());
                long cloudHospitalId = Long.parseLong(key);
                List<ProjHospitalInfoRelative> infoList =
                        hospitalInfoMapper.findHospitalByCloudHospitalId(cloudHospitalId);
                if (CollUtil.isNotEmpty(infoList)) {
                    ProjHospitalInfoRelative hospitalInfoRelative = infoList.get(0);
                    projHospitalInfo.setEnvId(hospitalInfoRelative.getEnvId());
                    projHospitalInfo.setEnvName(hospitalInfoRelative.getEnvName());
                    applyOrderHospitalService.updateHospitalRelativeInfoByCloudHospitalId(ProjApplyOrderServiceImpl.getOldCustomerInfo(HospitalOpenStatusEnum.OPENED, value.toString(), projHospitalInfo), projHospitalInfo, Collections.singletonList(Long.valueOf(key)));
                    applyOrderHospitalService.saveOrUpdateHospitalNewFunData(hospitalInfoRelative);
                }
            });
        }
        List<ProjHospitalInfo> hospitals = applyOrderHospitalService.getHospitalInfoByApplyOrderId(applyOrder.getId());
        // 更新医院产品开通状态
        applyOrderHospitalService.updateHospitalOpenStatus(HospitalOpenStatusEnum.OPENED,
                hospitals.stream().map(ProjHospitalInfo::getHospitalInfoId).collect(Collectors.toList()));
        applyOrderProductService.updateProductOpenStatus(ProductOpenStatusEnum.OPENED, productIds,
                applyOrder.getProjectInfoId());
        // 更新运营平台厂商等数据
        syncIsMsunCloud(applyOrder, dto);
        // 授权
        orderProductService.applyEmpowers(applyOrder.getProjectInfoId(), hospitals, productIds);
        // 调用运营平台同步部署时间
        String nowDateTime = DateUtil.now();
        // 更新云资源实际交付时间
        updateCloudSubTime(applyOrder.getProjectInfoId(), nowDateTime);
        // 同步运营平台工单信息
        ProjProjectInfo projectInfo = mainService.getProjectInfo(applyOrder.getProjectInfoId());
        try {
            updateNodeStatusApplyOrder(applyOrder, CLOUD_OPEN);
        } catch (Throwable e) {
            log.error("更新运营平台云资源开通节点异常, e: {}, e=", e.getMessage(), e);
            String message = projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + "更新运营平台云资源开通节点异常. "
                    + "请及时处理.";
            exceptionMessageService.sendToSystemManager(projectInfo.getProjectInfoId(), message);
        }
        try {
            yunYingService.sendYunyingCloudTimeDeployed(applyOrder.getProjectInfoId(), nowDateTime);
        } catch (Throwable e) {
            log.error("向运营平台同步云资源部署时间异常. message: {}, e=", e.getMessage(), e);
            String message = projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + "向运营平台同步云资源部署时间异常."
                    + " 请及时处理.";
            exceptionMessageService.sendToSystemManager(projectInfo.getProjectInfoId(), message);
        }
        try {
            // 调用运维平台同步时间
            yunWeiPlatFormService.sendTimeToYunWei(applyOrder.getProjectInfoId());
        } catch (Throwable e) {
            log.error("向运维平台同步开通、结束时间异常. message: {}, e=", e.getMessage(), e);
            String message = projectInfo.getProjectName() + "-" + projectInfo.getProjectNumber() + "向运维平台同步开通、结束时间异常."
                    + " 请及时处理.";
            exceptionMessageService.sendToSystemManager(projectInfo.getProjectInfoId(), message);
        }
        return Result.success();
    }

    /**
     * 获取操作人信息
     * @param dto
     * @return
     */
    private SysUser extractedSysUser(HospitalUpdateStatusAndProductDeployDTO dto) {
        List<SysUser> voList = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("is_deleted", 0).eq("user_name", dto.getOperateName()));
        SysUser sysUser = new SysUser();
        if (voList != null && voList.size() > 0) {
            sysUser = voList.get(0);
        }
        return sysUser;
    }

    /**
     * 同步运营平台云资源附加信息
     *
     * @param applyOrder 申请单
     * @param dto        请求参数
     */
    @Override
    public void syncIsMsunCloud(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        commonService.syncIsMsunCloud(applyOrder.getCustomInfoId(), applyOrder.getProjectInfoId(),
                dto.getDeployCloudVendor(),
                StrUtil.toString(dto.getIsMsunPay()));
    }

    /**
     * 更新云资源实际交付时间
     *
     * @param projectInfoId 项目id
     * @param nowDateTime   交付开通时间
     */
    @Override
    public void updateCloudSubTime(Long projectInfoId, String nowDateTime) {
        List<ProjSettlementOrderInfo> settlementOrderInfos = mainService.findCloudServiceForm(projectInfoId);
        if (CollUtil.isNotEmpty(settlementOrderInfos)) {
            ProjSettlementOrderInfo settlementOrderInfo = settlementOrderInfos.get(0);
            List<ProjOrderProduct> orderProducts =
                    orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("order_info_id",
                            settlementOrderInfo.getOrderInfoId()));
            if (CollUtil.isNotEmpty(orderProducts)) {
                ProjOrderProduct orderProduct = orderProducts.get(0);
                List<ProjCustomCloudService> cloudServices =
                        cloudServiceMapper.selectList(new QueryWrapper<ProjCustomCloudService>().eq("yy_order_id",
                                settlementOrderInfo.getYyOrderId()));
                if (CollUtil.isNotEmpty(cloudServices)) {
                    ProjCustomCloudService cloudService = cloudServices.get(0);
                    ProjCustomCloudService copy = new ProjCustomCloudService();
                    copy.setCustomCloudServiceId(cloudService.getCustomCloudServiceId());
                    copy.setSubscribeStartTime(DateUtil.parse(nowDateTime, DatePattern.NORM_DATETIME_PATTERN));
                    // 根据合同计算的结束时间
                    settlementCheckSaleService.setSubscribeEndTime(orderProduct, copy,
                            cloudService.getPlanStartTime(), cloudService.getServiceSubscribeTerm().longValue());
                    int count = cloudServiceMapper.updateById(copy);
                    log.info("更新云资源订阅实际交付时间: {}", copy);
                    log.info("更新云资源订阅实际交付时间: count: {}", count);
                }
            }
        }
    }

    /**
     * 获取开始时间
     *
     * @return String
     */
    public static Date getStartTime() {
        return new Date();
    }

    /**
     * 指定开通传入的医院
     *
     * @param applyEmpowerDTO dto
     */
    @Override
    public void addHospitalToCloudHealth(ApplyEmpowerDTO applyEmpowerDTO) {
        List<ProjHospitalInfoRelative> relatives =
                applyOrderHospitalService.getApplyOrderHospitals(applyEmpowerDTO.getProjectInfoId());
        if (CollUtil.isEmpty(relatives)) {
            throw new RuntimeException("未查询到医院.");
        }
        relatives =
                relatives.stream().filter(e -> ObjectUtil.isNotEmpty(e.getCloudHospitalId()) && applyEmpowerDTO.getCloudHospitalIds().stream().anyMatch(f -> f.intValue() == e.getCloudHospitalId())).collect(Collectors.toList());
        log.info("relative: {}", CollUtil.isNotEmpty(relatives) ? relatives.toString() : StrUtil.EMPTY);
        for (ProjHospitalInfoRelative relative : relatives) {
            log.info("hospitalInfo: {}", relative.toString());
            applyOrderHospitalService.saveOrUpdateHospitalNewFunData(relative);
        }
    }

    /**
     * 查询申请单号根据医院id
     * @param applyEmpowerDTO
     * @return
     */
    @Override
    public Result<Long> selectApplyNoByHosId(CsmParamerDTO applyEmpowerDTO) {
        List<ProjApplyOrderHospitalRecord> list = projApplyOrderHospitalRecordMapper.selectApplyNoByHosId(applyEmpowerDTO);
        if (list != null && list.size() > 0) {
            return Result.success(list.get(0).getApplyOrderId());
        }
        return Result.fail("未查询到申请单信息");
    }


}
