package com.msun.csm.service.yunying;

import java.util.Map;

import com.msun.csm.common.enums.api.yunying.CloudTimeNodeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.feign.entity.yunying.req.SyncIsMsunCloudDTO;
import com.msun.csm.model.imsp.SyncYunInfoDTO;
import com.msun.csm.model.param.GetProjBackendScoreParam;
import com.msun.csm.model.param.SendYunYingFineParam;
import com.msun.csm.model.param.SendYunYingMessageParam;

public interface YunYingService {
    /**
     * 同步云云资源信息
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    Result<String> syncCloudInfo(SyncYunInfoDTO dto);

    /**
     * 云资源信息实际修改逻辑
     * <p>
     * 1.若修改台账失败, 回滚并返回
     * </p>
     * <p>
     * 2. 若系统管理调用失败. 回滚并返回
     * </p>
     * <p>
     * 3. 运营平台操作失败, 只记录日志不回滚, 因系统管理值已修改. 需手动修复运营平台数据
     * </p>
     *
     * @param dto       请求参数
     * @param recordMap 日志记录
     * @return 返回值
     */
    Result<String> syncCloudInfoImpl(SyncYunInfoDTO dto, Map<YunYingServiceImpl.FixCloudPhaseEnum, String> recordMap);

    /**
     * 多线程异步发送运营平台消息
     *
     * @param param 参数
     */
    void sendYunYingMessageByAsync(SendYunYingMessageParam param);

    /**
     * 发送企业微信消息并返回发送结果
     * <p>1. 发送成功返回true</p>
     * <p>2. 发送失败返回false</p>
     * <p>3. 不支持批量发送      </p>
     *
     * @param param 参数
     */
    boolean sendEnterpriseWeChatMessageForOnePeople(SendYunYingMessageParam param);

    /**
     * 向运营平台同步云资源开通时间,
     *
     * @param projectInfoId     项目id
     * @param cloudTimeNodeEnum 节点枚举
     * @param signDate          部署时间（只有在部署节点调用时传参）
     */
    void sendYunyingCloudTime(Long projectInfoId, CloudTimeNodeEnum cloudTimeNodeEnum, String signDate);

    /**
     * 部署时运营同步
     *
     * @param projectInfoId 项目id
     * @param signDate      部署时间
     */
    void sendYunyingCloudTimeDeployed(Long projectInfoId, String signDate);

    /**
     * 同步运营平台云厂商等
     *
     * @param userLoginname      登录
     * @param syncIsMsunCloudDTO 同步内容
     */
    void syncIsMsunCloud(String userLoginname, SyncIsMsunCloudDTO syncIsMsunCloudDTO) throws Exception;

    /**
     * 确认入驻时同步开通时间
     *
     * @param projectInfoId 项目id
     */
    void sendYunyingCloudTimeSettlement(Long projectInfoId);

    /**
     * 发送运营平台消息
     *
     * @param param 参数
     */
    Boolean sendYunYingMessageWithResult(SendYunYingMessageParam param);

    /**
     * 发送运营处罚消息
     *
     * @param param 参数
     */
    Boolean sendYunYingFineWithResult(SendYunYingFineParam param);

    /**
     * 免中间件部署申请
     *
     * @param projectInfo 项目信息
     */
    void sendMidOrder(ProjProjectInfo projectInfo);

    /**
     * 同步后端得分信息给运营平台
     *
     * @param param 参数
     */
    Boolean getProjBackendScore(GetProjBackendScoreParam param);
}
