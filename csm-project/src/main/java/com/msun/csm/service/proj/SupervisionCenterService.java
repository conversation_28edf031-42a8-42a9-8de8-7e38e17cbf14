package com.msun.csm.service.proj;


import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailVO;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionRecordVO;
import com.msun.csm.dao.entity.proj.DeductionClassificationVO;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecordVO;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUserVO;
import com.msun.csm.dao.entity.proj.QueryServerTeamDeductionRecordForBackendTeamParam;
import com.msun.csm.dao.entity.proj.ServerTeamDeductionRecordForBackendTeamVO;
import com.msun.csm.model.DictDeductionTypeVO;
import com.msun.csm.model.param.BackendOperationParam;
import com.msun.csm.model.param.BackendOperationParam2;
import com.msun.csm.model.param.ChangeDeductionParam;
import com.msun.csm.model.param.DeductionDetailIdParam;
import com.msun.csm.model.param.GetDeductionClassificationParam;
import com.msun.csm.model.param.GetIssueClassificationParam;
import com.msun.csm.model.param.QueryBackendDeductionRecordParam;
import com.msun.csm.model.param.QueryDeductionClassificationDictParam;
import com.msun.csm.model.param.QueryServerTeamDeductionRecordParam;
import com.msun.csm.model.param.RevertConfirmationParam;
import com.msun.csm.model.param.SendConfirmationParam;
import com.msun.csm.model.param.TestParam1;
import com.msun.csm.model.param.TestParam10;
import com.msun.csm.model.param.TestParam9;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.issue.SaveDeductionClassificationDictType;
import com.msun.csm.model.req.issue.SaveIssueReq;
import com.msun.csm.model.resp.issue.IssueDataResp2;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq2;

public interface SupervisionCenterService {

    List<BaseCodeNameResp> getAcceptanceClassification();

    List<BaseCodeNameResp> getIssueClassification(GetIssueClassificationParam param);

    List<DeductionClassificationVO> getDeductionClassification(GetDeductionClassificationParam param);

    boolean saveIssue(SaveIssueReq req);

    Result<List<IssueDataResp2>> queryIssue(QueryIssueReq queryIssueReq);

    boolean saveDeductionClassificationDict(SaveDeductionClassificationDictType req);

    boolean deleteDeductionClassificationDict(Long id);

    List<DictDeductionTypeVO> queryDeductionClassificationDict(QueryDeductionClassificationDictParam req);

    List<BaseCodeNameResp> queryServerTypeDict();

    List<BackendTeamDeductionRecordVO> queryServerTeamDeductionRecordForBackendTeam(QueryServerTeamDeductionRecordParam param);

    boolean sendConfirmation(SendConfirmationParam param);

    boolean revertConfirmation(RevertConfirmationParam param);

    BackendTeamDeductionDetailVO queryBackendTeamDeductionDetail(QueryBackendDeductionRecordParam param);

    List<ServerTeamDeductionRecordForBackendTeamVO> queryServerTeamDeductionRecordForBackendTeam(QueryServerTeamDeductionRecordForBackendTeamParam param);

    boolean invalidDeduction(DeductionDetailIdParam param);

    boolean submitConfirm(DeductionDetailIdParam param);

    boolean changeDeduction(ChangeDeductionParam param);

    boolean backendOperation(BackendOperationParam param);

    boolean backendConfirmOrReject(@RequestBody BackendOperationParam2 param);

    Result<Void> checkAcceptStatus(Long projectInfoId);

    List<ProductSatisfactionSurveyRecordVO> getProductSatisfactionRecord(ProjectInfoId param);

    boolean saveProductScore(TestParam9 param);

    boolean saveProductSatisfactionUser(TestParam1 param);

    List<ProductSatisfactionSurveyUserVO> getProductSatisfactionUser(ProjectInfoId param);

    boolean deleteProductSatisfactionUser(SimpleId param);

    boolean send(TestParam10 param10);

    boolean stop(TestParam10 param10);

    boolean urge(Long productSatisfactionSurveyRecordId);

    Result<Void> updateSatisfactionSurveyStatus(UpdateSurveyPlanStatusReq2 updateSurveyPlanStatus);
}
