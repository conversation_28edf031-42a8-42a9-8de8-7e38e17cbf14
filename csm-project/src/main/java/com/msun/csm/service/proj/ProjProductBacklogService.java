package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProductConfigLog;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickDetectDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickImportDTO;
import com.msun.csm.model.req.surveyplan.QuerySurveyPlanProductReq;
import com.msun.csm.model.resp.surveyplan.SurveyPlanProductResp;
import com.msun.csm.model.vo.ConfigImportCloudVO;
import com.msun.csm.model.vo.ConfigProductJobMenuDetailVO;
import com.msun.csm.model.vo.OneClickDetectionVO;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProjProductBacklogDataVO;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.model.vo.ProjProductConfigLogVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/25/14:58
 */
public interface ProjProductBacklogService {

    /**
     * 批量插入产品待办任务数据
     *
     * @param list
     * @return
     */
    Result batchInsert(List<ProjProductBacklog> list);

    /**
     * 查询产品待办任务列表数据
     *
     * @param dto
     * @return
     */
    ProjProductBacklogDataVO selectProductBacklog(ProjProductBacklogDTO dto);

    /**
     * 查询代办产品
     *
     * @param dto 请求参数
     * @return List<ProjProductBacklogVO>
     */
    List<ProjProductBacklogVO> findProductBacklogVOS(ProjProductBacklogDTO dto);

    /**
     * 查询运维平台产品 **下拉使用**
     *
     * @param projectInfoId
     * @return
     */
    Result<List<BaseIdNameResp>> selectYyProductAndModule(Long projectInfoId);


    /**
     *分配调研计划查询产品
     *
     * @param
     * @return
     */
    Result<List<SurveyPlanProductResp>> selectSurveyPlanProducts(QuerySurveyPlanProductReq req);



    /**
     * 更新产品待办任务责任人
     *
     * @param dto
     * @return
     */
    Result updateProductBacklogLeader(ProjProductBacklogDTO dto);

    /**
     * 说明: 批量分配责任人
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/9/25 16:23
     * @remark: Copyright
      */
    Result batchUpdateProductBacklogLeaderList(ProjProductBacklogDTO dto);

    /**
     * 产品待办任务明细节点地址数据查询
     *
     * @param dto
     * @return
     */
    Result<List<ProductBacklogUrlVO>> selectBacklogUrlList(ProjProductBacklogDTO dto);

    /**
     * 配置数据导入云健康
     *
     * @param dto
     * @return
     */
    Result importHealthConfig(ProjProductBacklogDTO dto);

    /**
     * 测试csm是否通云健康环境
     *
     * @param customInfoId
     * @return
     */
    Result testCsmToApi(Long customInfoId);

    /**
     * 检测交付平台访问云健康数据库
     *
     * @param customInfoId
     * @return
     */
    Result checkNetwork(Long customInfoId);

    /**
     * 查询云健康表操作权限
     *
     * @param customInfoId
     * @return
     */
    Result chisDictTableOperationPermissions(Long customInfoId);

    /**
     * 查询配置导入日志信息
     *
     * @param log
     * @return
     */
    Result<List<ProjProductConfigLogVO>> selectConfigLogList(ProjProductConfigLog log);

    /**
     * 产品待办任务完成
     *
     * @param dto
     * @return
     */
    Result taskBacklogFinish(ProjProductBacklogDTO dto, String message);

    /**
     * 配置是否可导入云健康
     *
     * @param dto
     * @return
     */
    Result<ConfigImportCloudVO> configIsImportHealth(ProjProductBacklogDTO dto);

    /**
     * 修改产品配置状态
     *
     * @param dto
     * @return
     */
    Result<Integer> updateProductConfigStatus(ProjProductBacklogDTO dto);

    /**
     * 修改产品准备节点里程碑完成状态
     *
     * @param dto
     * @return
     */
    Result updateProductBacklogMilestoneInfo(ProjProductBacklogDTO dto);

    /**
     * 产品准备节点左侧医院列表信息
     *
     * @param dto
     * @return
     */
    Result<SurveyPlanInitVO> selectProductPrepareHospital(ProjProductBacklogDTO dto);

    /**
     * @param oneClickDTO 请求参数
     * @return Result<String>
     */
    Result<OneClickDetectionVO> oneClickDetection(ProjProductBacklogOneClickDetectDTO oneClickDTO);

    /**
     * @param importDTO 请求参数
     * @return Result<List < String>>
     */
    Result<List<String>> oneClickImportCloudHealth(ProjProductBacklogOneClickImportDTO importDTO);

    /**
     * 报表、表单、基础数据链接处理
     *
     * @param vo
     * @param dto
     */
    void infoDataBaseUrl(ConfigProductJobMenuDetailVO vo, ProjProductBacklogDTO dto);

    /**
     * 根据参数查询产品待办任务数据
     * @param dto
     * @return
     */
    ProjProductBacklog selectByParam(ProjProductBacklogDTO dto);

    /**
     * 确认开始，设置当前时间为开始时间
     *
     * @param dto
     * @return
     */
    Result confirmStart(ProjProductBacklogDTO dto);

    /**
     * 根据项目id查询当前客户下该项目类型上线的产品 **下拉使用**
     * @param projectInfoId
     * @return
     */
    Result selectYyProductAndModuleByCustomInfoId(Long projectInfoId);
}
