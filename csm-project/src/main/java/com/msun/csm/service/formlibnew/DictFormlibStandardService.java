package com.msun.csm.service.formlibnew;


import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.formlibnew.DictFormlibStandard;
import com.msun.csm.model.req.formlibnew.DictFormlibStandardSaveReq;
import com.msun.csm.model.req.formlibnew.DictFormlibStandardTypeSaveReq;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardTypePageReq;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardResp;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTypeSelectResp;

/**
 * <AUTHOR>
 * @description 针对表【dict_formlib_standard(表单资源库标准字典)】的数据库操作Service
 * @createDate 2025-07-14 13:47:02
 */
public interface DictFormlibStandardService extends IService<DictFormlibStandard> {

    /**
     * 查询标准树数据
     * @return
     */
    Result findStandardTreeData(DictFormLibStandardPageReq dto);

    /**
     * 查询标准数据(表格)
     * @param dto
     * @return
     */
    Result<List<DictFormlibStandardResp>> findDictDataPage(DictFormLibStandardPageReq dto);

    /**
     * 保存数据
     * @param dto
     * @return
     */
    Result<String> saveData(DictFormlibStandardSaveReq dto);

    /**
     * 修改启用禁用
     * @param dto
     * @return
     */
    Result<String> updateIsStandard(DictFormlibStandardSaveReq dto);

    /**
     * 分页查询分类字典数据
     * @param dto
     * @return
     */
    Result<PageInfo<DictFormlibStandardTypeSelectResp>> selectStandardTypeData(DictFormLibStandardTypePageReq dto);

    /**
     * 修改字典启用禁用
     * @param dto
     * @return
     */
    Result<String> updateIsStandardType(DictFormLibStandardTypePageReq dto);

    /**
     * 保存字典数据
     * @param dto
     * @return
     */
    Result<String> saveDictData(DictFormlibStandardTypeSaveReq dto);

    /**
     * 查询字典数据，用于资源库下拉选项
     * @param dto
     * @return
     */
    Result<List<BaseCodeNameResp>> findStandardTypeDictData(DictFormLibStandardPageReq dto);

    /**
     * 查询表单类型
     * @param dto
     * @return
     */
    Result<List<BaseCodeNameResp>> selectDictFormTypeList(DictFormLibStandardPageReq dto);
}
