package com.msun.csm.service.config;

import java.util.Arrays;
import java.util.HashSet;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.config.ConfigProjectSchema;
import com.msun.csm.dao.mapper.config.ConfigProjectSchemaMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper;
import com.msun.csm.dao.mapper.dict.DictProjectSchemaMapper;
import com.msun.csm.dao.mapper.dict.DictProjectSchemaVsPlanItemMapper;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/1 9:58
 */
@Service
public class ConfigProjectSchemaServiceImpl implements ConfigProjectSchemaService {

    @Autowired
    private ConfigProjectSchemaMapper configProjectSchemaMapper;
    @Autowired
    private DictProjectSchemaMapper dictProjectSchemaMapper;
    @Autowired
    private DictProjectPlanStageMapper dictProjectPlanStageMapper;
    @Autowired
    private DictProjectPlanItemMapper dictProjectPlanItemMapper;
    @Autowired
    private DictProjectSchemaVsPlanItemMapper dictProjectSchemaVsPlanItemMapper;



    @Override
    public Object generateProjectPlanByBizType(GenerateProjectPlanBaseArgs args) {
        ConfigProjectSchema cfg = new LambdaQueryChainWrapper<>(configProjectSchemaMapper)
                .eq(ConfigProjectSchema::getConfigType, "业务场景")
                .eq(ConfigProjectSchema::getConfigTypeCode, args.getConfigTypeCode())
                .eq(ConfigProjectSchema::getIsDeleted, 0)
                .in(ConfigProjectSchema::getUpgradationType, new HashSet<>(Arrays.asList(args.getUpgradationType(), -1)))
                .in(ConfigProjectSchema::getHisFlag, new HashSet<>(Arrays.asList(args.getHisFlag(), -1)))
                .in(ConfigProjectSchema::getTelesalesFlag, new HashSet<>(Arrays.asList(args.getTelesalesFlag(), -1)))
                .in(ConfigProjectSchema::getMonomerFlag, new HashSet<>(Arrays.asList(args.getMonomerFlag(), -1)))
                .in(ConfigProjectSchema::getRegionFlag, new HashSet<>(Arrays.asList(args.getRegionFlag(), -1)))
                .one();
        if (cfg == null) {
            throw new CustomException("项目模式配置不存在");
        }

        return null;
    }

    @Override
    public Object generateProjectPlanByProductType(GenerateProjectPlanBaseArgs args) {
        ConfigProjectSchema cfg = new LambdaQueryChainWrapper<>(configProjectSchemaMapper)
                .eq(ConfigProjectSchema::getConfigType, "产品分类")
                .eq(ConfigProjectSchema::getConfigTypeCode, args.getConfigTypeCode())
                .eq(ConfigProjectSchema::getIsDeleted, 0)
                .in(ConfigProjectSchema::getUpgradationType, new HashSet<>(Arrays.asList(args.getUpgradationType(), -1)))
                .in(ConfigProjectSchema::getHisFlag, new HashSet<>(Arrays.asList(args.getHisFlag(), -1)))
                .in(ConfigProjectSchema::getTelesalesFlag, new HashSet<>(Arrays.asList(args.getTelesalesFlag(), -1)))
                .in(ConfigProjectSchema::getMonomerFlag, new HashSet<>(Arrays.asList(args.getMonomerFlag(), -1)))
                .in(ConfigProjectSchema::getRegionFlag, new HashSet<>(Arrays.asList(args.getRegionFlag(), -1)))
                .one();
        if (cfg == null) {
            throw new CustomException("项目模式配置不存在");
        }
        return null;
    }

    @Override
    public Object generateProjectPlanByDeliveryOrderType(GenerateProjectPlanBaseArgs args) {
        ConfigProjectSchema cfg = new LambdaQueryChainWrapper<>(configProjectSchemaMapper)
                .eq(ConfigProjectSchema::getConfigType, "工单类型")
                .eq(ConfigProjectSchema::getConfigTypeCode, args.getConfigTypeCode())
                .eq(ConfigProjectSchema::getIsDeleted, 0)
                .in(ConfigProjectSchema::getUpgradationType, new HashSet<>(Arrays.asList(args.getUpgradationType(), -1)))
                .in(ConfigProjectSchema::getHisFlag, new HashSet<>(Arrays.asList(args.getHisFlag(), -1)))
                .in(ConfigProjectSchema::getTelesalesFlag, new HashSet<>(Arrays.asList(args.getTelesalesFlag(), -1)))
                .in(ConfigProjectSchema::getMonomerFlag, new HashSet<>(Arrays.asList(args.getMonomerFlag(), -1)))
                .in(ConfigProjectSchema::getRegionFlag, new HashSet<>(Arrays.asList(args.getRegionFlag(), -1)))
                .one();
        if (cfg == null) {
            throw new CustomException("项目模式配置不存在");
        }
        return null;
    }
}
