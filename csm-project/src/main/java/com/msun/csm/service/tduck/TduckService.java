package com.msun.csm.service.tduck;

import java.util.List;

import com.msun.csm.common.enums.FormClassEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.tduck.FmUserForm;
import com.msun.csm.model.dto.DifferenceInfoDTO;
import com.msun.csm.model.dto.FormDataDTO;
import com.msun.csm.model.dto.TduckConfigInfoDTO;
import com.msun.csm.model.param.ConfirmDataForProductParam;
import com.msun.csm.model.param.ConfirmDataParam;
import com.msun.csm.model.param.ConfirmFinalDataParam;
import com.msun.csm.model.param.CreateTduckFormParam;
import com.msun.csm.model.param.ShowConfigParam;
import com.msun.csm.model.param.ShowDifferenceParam;
import com.msun.csm.model.param.TduckCopySurveyResultParam;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;

public interface TduckService {


    String createTduckForm(CreateTduckFormParam param);

    DifferenceInfoDTO showDifference(ShowDifferenceParam param);

    Result confirmData(ConfirmDataParam param);

    List<FormDataDTO> getDataByFormKey(String formKey, Long hospitalInfoId);

    List<TduckConfigInfoDTO> showConfigQuestionAndAnswer(ShowConfigParam showConfigParam);

    Result<Boolean> copySurveyResult(TduckCopySurveyResultParam param);

    Result confirmDataForProduct(ConfirmDataForProductParam param);

    Result<List<SaveBackLogAndDetailReq>> getCreateTaskParam(ConfirmFinalDataParam param);


    FmUserForm createSatisfactionSurveyForm(FormClassEnum formClass, String yyProductId, ProjProjectInfo projectInfo);

    /**
     * 基于问卷类型、产品、项目类型通过模板复制产生对应的项目调研问卷
     *
     * @param formClass   问卷类型
     * @param yyProductId 产品ID
     * @param projectInfo 项目ID
     * @return 通过模板复制产生的项目调研问卷
     */
    FmUserForm copyFormByTemplate(FormClassEnum formClass, String yyProductId, ProjProjectInfo projectInfo);

    String createFormUrl(String formKey, Long sysUserId, String source, Long hospitalInfoId, String deptName);


}


