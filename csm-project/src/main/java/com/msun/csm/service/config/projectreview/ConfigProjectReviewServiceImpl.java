package com.msun.csm.service.config.projectreview;


import cn.hutool.core.bean.BeanUtil;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewMapper;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewResp;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【config_project_review(项目审核模式配置表)】的数据库操作Service实现
 * @createDate 2025-06-18 08:30:31
 */
@Service
public class ConfigProjectReviewServiceImpl extends ServiceImpl<ConfigProjectReviewMapper, ConfigProjectReview> implements ConfigProjectReviewService {

    @Resource
    private ConfigProjectReviewMapper configProjectReviewMapper;

    /**
     * 查询项目审核模式配置列表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ConfigProjectReviewResp>> findDataPage(ConfigProjectReviewReq dto) {
        // 查询 医院信息
        List<ConfigProjectReviewResp> pagelist = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> configProjectReviewMapper.findDataPage(dto));
        PageInfo<ConfigProjectReviewResp> pageInfo = new PageInfo<>(pagelist);
        return Result.success(pageInfo);
    }

    /**
     * 项目审核模式配置启用作废
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> updateDelData(ConfigProjectReviewReq dto) {
        if (dto.getProjectReviewId() == null) {
            return Result.fail("项目审核模式配置id不能为空");
        }
        ConfigProjectReview configProjectReview = configProjectReviewMapper.selectByIdHand(dto.getProjectReviewId());
        if (configProjectReview == null) {
            return Result.fail("项目审核模式配置不存在");
        }
        // 优先使用前端传入的数据， 如果传入没有则根据数据库数据取反
        Integer isDeleted = dto.getIsDeleted() != null ? dto.getIsDeleted() : configProjectReview.getIsDeleted() == 0 ? 1 : 0;
        configProjectReview.setIsDeleted(isDeleted);
        configProjectReviewMapper.updateById(configProjectReview);
        return Result.success();
    }

    /**
     * 项目审核配置字典保存修改
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> saveData(ConfigProjectReviewReq dto) {
        /*
         * 新增 校验同一审核类型下，以下字段组合是否已存在：
         * - 客户类型（通用 + 传入）
         * - 电销属性（通用 + 传入）
         * - 交付模式（通用 + 传入）
         * - 项目类型（通用 + 传入）
         *
         * 若为编辑操作，校验时需排除当前记录ID，避免误判重复。
         */
        List<ConfigProjectReview> listModel = configProjectReviewMapper.selectListByParamer(dto);
        if (listModel != null && !listModel.isEmpty()) {
            return Result.fail("同一审核类型下，客户类型、电销属性、交付模式、项目类型组合已存在,请勿重复操作！");
        }
        if (dto.getProjectReviewId() == null) {
            ConfigProjectReview configProjectReview = new ConfigProjectReview();
            BeanUtil.copyProperties(dto, configProjectReview);
            configProjectReview.setProjectReviewId(SnowFlakeUtil.getId());
            configProjectReviewMapper.insert(configProjectReview);
        } else {
            ConfigProjectReview configProjectReview = configProjectReviewMapper.selectById(dto.getProjectReviewId());
            if (configProjectReview == null) {
                return Result.fail("项目审核模式配置不存在");
            }
            BeanUtil.copyProperties(dto, configProjectReview);
            configProjectReviewMapper.updateById(configProjectReview);
        }
        return Result.success();
    }
}




