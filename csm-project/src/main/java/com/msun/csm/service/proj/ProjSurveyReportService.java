package com.msun.csm.service.proj;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.vo.PrinterDTO;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.projreport.ProjPrinterConfigInfo;
import com.msun.csm.model.csm.PrintInfoMainCsmDTO;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projreport.PrintReportVerificationPassedParam;
import com.msun.csm.model.req.projreport.ProjSurveyReportDeleteReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportDetailReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportExamineReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintParmerReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReviewExamineReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportTagReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateListReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainImportReq;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormPageResp;
import com.msun.csm.model.resp.projreport.ProjProjectFormTerminalResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportMenuResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReprotDetailPageResp;
import com.msun.csm.model.statis.ProjCustomReq;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */

public interface ProjSurveyReportService {

    /**
     * 分页查询报表信息
     *
     * @param projSurveyReportReq
     * @return
     */
    Result<ProjSurveyReprotFormPageResp<ProjSurveyReportResp>> selectSurveyReportByPage(ProjSurveyReportReq projSurveyReportReq);

    /**
     * 驳回
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result updateExamineReportStatus(ProjSurveyReportExamineReq projSurveyReportExamineReq);

    /**
     * 更新报表
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result updateReport(ProjSurveyReportUpdateReq projSurveyReportExamineReq);

    /**
     * 修改报表的完成状态
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result updateReportFinishStatus(ProjSurveyReportUpdateReq projSurveyReportExamineReq);

    /**
     * 报表制作
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result reprotMake(ProjSurveyReportExamineReq projSurveyReportExamineReq);

    /**
     * 批量保存
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result batchReprotSave(List<ProjSurveyReportUpdateReq> projSurveyReportExamineReq);

    /**
     * 根据打印节点查询报表标识
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result getReportFileTagByPrintCode(ProjSurveyReportTagReq projSurveyReportExamineReq);

    /**
     * 根据项目id查询报表菜单
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result<List<ProjSurveyReportMenuResp>> getReportMenuByProjectId(ProjSurveyReportTagReq projSurveyReportExamineReq);

    /**
     * 获取初始化路径
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result<String> getPacsStartPath(ProjSurveyReportTagReq projSurveyReportExamineReq);

    /**
     * 批量分配责任人
     *
     * @param projSurveyFormReq
     * @return
     */
    Result updateReportResponsibilities(ProjSurveyFormResponsibilitiesReq projSurveyFormReq);

    /**
     * 刷新报表终端明细
     *
     * @param projSurveyReportReq
     * @return
     */
    Result syncReportTerminalData(ProjSurveyReportReq projSurveyReportReq);

    /**
     * 分页查询报表明细信息
     *
     * @param projSurveyReportReq
     * @return
     */
    Result<ProjSurveyReprotDetailPageResp<ProjProjectFormTerminalResp>> selectSurveyReportDetailByPage(ProjSurveyReportDetailReq projSurveyReportReq);

    /**
     * 查询报表明细状态
     *
     * @param projSurveyReportReq
     * @return
     */
    Result<List<BaseCodeNameResp>> selectSatusDictList(ProjSurveyReportDetailReq projSurveyReportReq);

    /**
     * 导出模板
     *
     * @param response
     * @param projectInfoId
     */
    void download(HttpServletResponse response, Long projectInfoId) throws IOException;

    /**
     * 导入
     *
     * @param file
     * @param request
     * @param param
     * @return
     * @throws IOException
     */
    Result<String> importExcel(MultipartFile file, HttpServletRequest request, ProjStatisticalReportMainImportReq param) throws IOException;

    /**
     * 批量修改审核人
     *
     * @param projSurveyFormReq
     * @return
     */
    Result updateReportReviewer(ProjSurveyFormResponsibilitiesReq projSurveyFormReq);

    /**
     * 批量审核
     *
     * @param projSurveyFormReq
     * @return
     */
    Result updateReportExamine(ProjSurveyReportReviewExamineReq projSurveyFormReq);

    /**
     * 提交运维审核
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    Result updateReportExamineStatus(ProjSurveyReportReviewExamineReq projSurveyReportExamineReq);

    /**
     * 发送消息给审核人或者负责人
     */
    void getSendMsg();

    /**
     * 单个或者批量删除打印报表
     */
    Result<Void> deleteReport(ProjSurveyReportDeleteReq projSurveyFormReq);

    /**
     * 修改打印报表状态
     *
     * @param dto
     * @return
     */
    Result<String> updatePrintReportStatusData(ProjCustomReq dto);

    /**
     * 创建预上线医院打印报表模板信息
     *
     * @param customerId
     * @return
     */
    String sendStartPrintReportData(Long customerId);

    /**
     * 环境交付后进行调用中心端接口制作河模板的报表节点下发到云健康
     *
     * @param customerId
     * @param hisHospitalId
     * @param hisOrgId
     * @return
     */
    String preLaunchHospitalPushOnSite(Long customerId, Long hisHospitalId, Long hisOrgId);

    Result<String> preLaunchHospitalPushOnSiteSend(Long projectInfoId);

    /**
     * 查询打印报表信息
     *
     * @param dto
     * @return
     */
    PageInfo<PrinterDTO> queryPrintInfo(ProjSurveyReportPrintReq dto);

    /**
     * 查询所有节点
     *
     * @param model
     * @return
     */
    Result getAllNode(ProjSurveyReportPrintParmerReq model);

    /**
     * 保存批量保存
     *
     * @param saveModel
     * @return
     */
    Result saveReprotSaveList(ProjSurveyReportUpdateListReq saveModel);

    /**
     * 根据id查询报表对象信息
     *
     * @param projSurveyReportReq
     * @return
     */
    ProjSurveyReportResp selectSurveyReportById(ProjSurveyReportReq projSurveyReportReq);

    /**
     * 查询打印报表信息（从交付表查询）
     *
     * @param dto
     * @return
     */
    PageInfo<ProjPrinterConfigInfo> queryPrintInfoByJf(ProjSurveyReportPrintReq dto);

    /**
     * 保存打印报表信息
     *
     * @param dto
     * @return
     */
    ResponseResult<String> savePrintInfo(PrintInfoMainCsmDTO dto);

    /**
     * 交付挂载统计报表资源库页面
     * @param projSurveyReportExamineReq
     * @return
     */
    Result getReportLibraryPageLink(ProjSurveyReportExamineReq projSurveyReportExamineReq);

    /**
     * 批量分配前端验证人
     *
     * @param param 参数
     */
    Result<Void> updateReportIdentifier(ProjSurveyFormResponsibilitiesReq param);

    /**
     * 批量分配前端验证人
     *
     * @param param 参数
     */
    boolean verificationPassed(PrintReportVerificationPassedParam param);

    /**
     * 报表打印导出Excel
     * @param response
     * @param dto
     */
    void reportPrintExportExcel(HttpServletResponse response, ProjSurveyReportReq dto);

    /**
     *
     * @param projectInfoId 项目ID
     * @param hospitalInfoId 医院ID
     * @param yyProductId 产品ID
     * @param nodeEnum 里程碑节点编码
     * @return 上线必备的报表中未完成的打印报表数量
     */
    Integer getReportCount(Long projectInfoId, Long hospitalInfoId, Long yyProductId, MilestoneNodeEnum nodeEnum);

}
