package com.msun.csm.service.tduck;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.datasource.DataSourceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.FormClassEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.tduck.FormStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.comm.HzznDeliver;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProductConfig;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.tduck.FmUserForm;
import com.msun.csm.dao.entity.tduck.FmUserFormData;
import com.msun.csm.dao.entity.tduck.FmUserFormItem;
import com.msun.csm.dao.entity.tduck.FmUserFormLogic;
import com.msun.csm.dao.entity.tduck.FmUserFormSetting;
import com.msun.csm.dao.entity.tduck.FmUserFormTheme;
import com.msun.csm.dao.entity.tduck.TaskCondition;
import com.msun.csm.dao.entity.tduck.TaskTrigger;
import com.msun.csm.dao.entity.tduck.UserFormItemOptional;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormDataMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormItemMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormLogicMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormSettingMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormThemeMapper;
import com.msun.csm.dao.mapper.tduck.TaskConditionMapper;
import com.msun.csm.dao.mapper.tduck.TaskTriggerMapper;
import com.msun.csm.dao.mapper.tduck.UserFormItemOptionalMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.exception.NoProjectUserFormException;
import com.msun.csm.manage.TduckManage;
import com.msun.csm.model.dto.AnswerInfoDTO;
import com.msun.csm.model.dto.DifferenceInfoDTO;
import com.msun.csm.model.dto.FormDataDTO;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.dto.QuestionHeaderDTO;
import com.msun.csm.model.dto.QuestionInfoDTO;
import com.msun.csm.model.dto.TduckConfigInfoDTO;
import com.msun.csm.model.param.ConfirmDataForProductParam;
import com.msun.csm.model.param.ConfirmDataParam;
import com.msun.csm.model.param.ConfirmFinalDataParam;
import com.msun.csm.model.param.CreateTduckFormParam;
import com.msun.csm.model.param.ShowConfigParam;
import com.msun.csm.model.param.ShowDifferenceParam;
import com.msun.csm.model.param.TduckCopySurveyResultParam;
import com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.HzznDeliverService;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.proj.ProjCustomInfoService;
import com.msun.csm.service.proj.ProjProductDeliverRecordService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectPlanService;
import com.msun.csm.service.proj.ProjSurveyPlanService;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SignUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/28
 */
@Slf4j
@Service
public class TduckServiceImpl implements TduckService {

    @Resource
    private FmUserFormMapper fmUserFormMapper;

    @Resource
    private FmUserFormItemMapper fmUserFormItemMapper;

    @Resource
    private FmUserFormLogicMapper fmUserFormLogicMapper;

    @Resource
    private FmUserFormSettingMapper fmUserFormSettingMapper;

    @Resource
    private FmUserFormThemeMapper fmUserFormThemeMapper;

    @Resource
    private UserFormItemOptionalMapper userFormItemOptionalMapper;

    @Resource
    private TaskTriggerMapper taskTriggerMapper;

    @Resource
    private TaskConditionMapper taskConditionMapper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Value("${tduck.domain}")
    private String tduckDomain;

    @Resource
    private TduckManage tduckManage;

    @Resource
    private FmUserFormDataMapper fmUserFormDataMapper;

    @Resource
    private FmUserFormService fmUserFormService;

    @Resource
    private ProjProductConfigMapper projProductConfigMapper;

    @Resource
    private ProjSurveyPlanService surveyPlanService;

    @Resource
    private HzznDeliverService hzznDeliverService;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private DictProductService dictProductService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 创建项目问卷的表单key
     *
     * @param templateFormKey 模板问卷的formKey
     * @param projectInfoId   项目ID
     * @param productId       产品ID
     * @return 项目问卷的表单key
     */
    private String createProjectFormKey(String templateFormKey, String projectInfoId, String productId) {
        if (StringUtils.isAnyBlank(templateFormKey, projectInfoId, productId)) {
            throw new IllegalArgumentException(String.format("参数 templateFormKey、projectInfoId、productId 都不可为空，templateFormKey=%s，projectInfoId=%s，productId=%s", templateFormKey, projectInfoId, productId));
        }
        return templateFormKey + "_" + projectInfoId + "_" + productId;
    }

    /**
     * 创建调研问卷跳转地址
     *
     * @param formKey        表单key
     * @param sysUserId      用户ID
     * @param source         来源
     * @param hospitalInfoId 医院ID
     * @param deptName       科室
     * @return 调研问卷跳转地址
     */
    public String createFormUrl(String formKey, Long sysUserId, String source, Long hospitalInfoId, String deptName) {
        String sign = SignUtil.encrypt(formKey + sysUserId + source, CharsetUtil.UTF_8);
        String url = tduckDomain + "/" + formKey + "?sysUserId=" + sysUserId + "&sign=" + sign + "&source=" + source;
        if (hospitalInfoId != null) {
            url = url + "&hospitalInfoId=" + hospitalInfoId;
        }
        if (StringUtils.isNotBlank(deptName)) {
            url = url + "&deptName=" + deptName;
        }
        return url;
    }

    /**
     * 获取填鸭问卷填写地址
     *
     * @param formKey        表单KEY
     * @param sysUserId      要查询的问卷的填写人
     * @param source         survey-原始调研结果
     * @param hospitalInfoId
     * @param deptName       要查询的问卷的科室
     * @param surveyDept     调研的来源科室
     * @param onlyOneSurvey  是否只有一份调研问卷
     * @return
     */
    private String getTduckFormUrl(String formKey, Long sysUserId, String source, Long hospitalInfoId, String deptName, String surveyDept, Boolean onlyOneSurvey) {
        String sign = SignUtil.encrypt(formKey + sysUserId + source, CharsetUtil.UTF_8);
        String url = tduckDomain + "/" + formKey + "?sysUserId=" + sysUserId + "&sign=" + sign + "&source=" + source;
        if (hospitalInfoId != null) {
            url = url + "&hospitalInfoId=" + hospitalInfoId;
        }
        if (StringUtils.isNotBlank(deptName)) {
            url = url + "&deptName=" + deptName;
        }
        url = url + "&surveyDept=" + surveyDept + "&onlyOneSurvey=" + onlyOneSurvey;
        return url;
    }

    /**
     * 向患者智能的hzz_deliver表中写入数据
     *
     * @param projectInfoId 项目ID
     * @param yyProductId   产品ID
     */
    private void saveHzznDeliver(Long projectInfoId, Long yyProductId) {
        // 查询患者智能产品
        List<DictProduct> dictProducts = null;
        try {
            dictProducts = dictProductMapper.selectList(
                    new QueryWrapper<DictProduct>()
                            .eq("is_deleted", 0)
                            .like("product_name", "患者智能")
            );
        } catch (Exception e) {
            log.error("向hzz_deliver表中写入数据，查询患者智能产品ID，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (CollectionUtils.isEmpty(dictProducts)) {
            return;
        }
        List<Long> hzznProductIdList = dictProducts.stream().map(DictProduct::getYyProductId).collect(Collectors.toList());
        if (hzznProductIdList.contains(yyProductId)) {
            try {
                // 替换传入的 实施地客户id 和项目id 。 该方法只适用于患者智能服务
                TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
                ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
                String deliverId = "";
                if (tmpProjectNewVsOld != null) {
                    List<HzznDeliver> hzznDeliverList = hzznDeliverService.getHzznDeliver(tmpProjectNewVsOld.getOldCustomId().toString(), tmpProjectNewVsOld.getOldProjectInfoId().toString());
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hzznDeliverList)) {
                        deliverId = hzznDeliverList.get(0).getId();
                    }
                }
                if (StringUtils.isEmpty(deliverId)) {
                    deliverId = String.valueOf(SnowFlakeUtil.getId());
                    HzznDeliver hzznDeliver = new HzznDeliver();
                    hzznDeliver.setId(deliverId);
                    hzznDeliver.setProjectId(String.valueOf(tmpProjectNewVsOld.getOldProjectInfoId()));
                    hzznDeliver.setCustomerId(String.valueOf(tmpProjectNewVsOld.getOldCustomId()));
                    hzznDeliver.setState(0);
                    hzznDeliver.setOperateType(projectInfo.getUpgradationType() == 1 ? 1 : 0);
                    hzznDeliver.setCreateTime(new Date());
                    hzznDeliver.setUpdateTime(new Date());
                    hzznDeliverService.save(hzznDeliver);
                }
            } catch (Exception e) {
                log.error("向hzz_deliver表中写入数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTduckForm(CreateTduckFormParam param) {
        // 先处理参数
        log.info("创建项目使用的填鸭问卷，原始入参={}", JSON.toJSONString(param));
        Long projectInfoId = param.getProjectInfoId();
        String productId = param.getProductId();
        String productName = dictProductService.getProductNameByYyProductId(Long.valueOf(productId));
        this.saveHzznDeliver(projectInfoId, Long.valueOf(productId));

        // 校验项目信息是否合法
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        if (ObjectUtils.isEmpty(projectInfo)) {
            throw new IllegalArgumentException("参数projectInfoId的值非法");
        }

        // 先根据项目ID和产品ID获取调研问卷，如果结果不为null，表示已经存在调研问卷了，直接返回问卷地址即可
        FmUserForm projectUserForm = null;
        try {
            projectUserForm = fmUserFormService.getProjectUserFormByProjectInfoId(projectInfoId, productId, 1);
        } catch (NoProjectUserFormException e) {
            log.error("创建项目使用的填鸭问卷，目前不存在项目问卷需要正常进行流程，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        // 1. 项目调研问卷存在
        if (null != projectUserForm) {
            // 后端审核
            if ("audit".equals(param.getOperationSource())) {
                List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                        new QueryWrapper<FmUserFormData>()
                                .eq("form_key", projectUserForm.getFormKey())
                                .eq("hospital_info_id", param.getHospitalInfoId())
                                .eq("source", "config")
                                .eq("save_type", "permanent")
                );
                if (CollectionUtils.isEmpty(fmUserFormDataList)) {
                    log.info("后端审核没有查询到最终调研结果，form_key={}，hospital_info_id={}，source=config，save_type=permanent", projectUserForm.getFormKey(), param.getHospitalInfoId());
                    return this.getTduckFormUrl(projectUserForm.getFormKey(), param.getSysUserId(), "config", param.getHospitalInfoId(), "最终结果", param.getDeptName(), true);
                }
                return this.getTduckFormUrl(projectUserForm.getFormKey(), fmUserFormDataList.get(0).getSysUserId(), "config", param.getHospitalInfoId(), fmUserFormDataList.get(0).getDeptName(), param.getDeptName(), true);
            }
            if ("survey".equals(param.getOperationSource())) {
//                // 未开始调研
//                if ("unfinished".equals(param.getSurveyStatus())) {
//                    return this.createFormUrl(projectUserForm.getFormKey(), param.getSysUserId(), "survey", param.getHospitalInfoId(), param.getDeptName());
//                }
                List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                        new QueryWrapper<FmUserFormData>()
                                .eq("form_key", projectUserForm.getFormKey())
                                .eq("hospital_info_id", param.getHospitalInfoId())
                                .eq("source", "config")
                                .eq("save_type", "permanent")
                );
                // 没有最终调研结果
                if (CollectionUtils.isEmpty(fmUserFormDataList)) {
                    return this.createFormUrl(projectUserForm.getFormKey(), param.getSysUserId(), "survey", param.getHospitalInfoId(), param.getDeptName());
                }
                if (fmUserFormDataList.size() > 1) {
                    throw new IllegalArgumentException(String.format("当前产品当前医院的最终调研结果存在多份，form_key=%s，hospital_info_id=%s", projectUserForm.getFormKey(), param.getHospitalInfoId()));
                }
                FmUserFormData fmUserFormData = fmUserFormDataList.get(0);
                return this.createFormUrl(projectUserForm.getFormKey(), fmUserFormData.getSysUserId(), "config", param.getHospitalInfoId(), fmUserFormData.getDeptName());
            }

            // 准备阶段
            List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                    new QueryWrapper<FmUserFormData>()
                            .eq("form_key", projectUserForm.getFormKey())
                            .eq("hospital_info_id", param.getHospitalInfoId())
                            .eq("source", "config")
                            .eq("save_type", "permanent")
            );
            if (!CollectionUtils.isEmpty(fmUserFormDataList)) {
                if (fmUserFormDataList.size() > 1) {
                    throw new IllegalArgumentException(String.format("当前产品当前医院的最终调研结果存在多份，form_key=%s，hospital_info_id=%s", projectUserForm.getFormKey(), param.getHospitalInfoId()));
                }
                FmUserFormData fmUserFormData = fmUserFormDataList.get(0);
                return this.createFormUrl(projectUserForm.getFormKey(), fmUserFormData.getSysUserId(), "config", param.getHospitalInfoId(), fmUserFormData.getDeptName());
            }

            throw new IllegalArgumentException(String.format("请确认当前产品【%s】已经完成调研。form_key=%s，hospital_info_id=%s", productName, projectUserForm.getFormKey(), param.getHospitalInfoId()));
        }

        //--------------------以下为项目调研问卷不存在逻辑

        // 2. 项目调研问卷不存在
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(projectInfo.getCustomInfoId());

        FmUserForm templateForm = fmUserFormService.getTemplateUserFormByProjectInfo(productId, projectInfo.getProjectType(), projectInfo.getUpgradationType(), 1);
        String templateFormKey = templateForm.getFormKey();

        // 2.1 进入调研，查看原始调研结果

        // 2.1.1 未分配调研负责人，只允许查看调研模板，并且不创建项目调研问卷
        if ("survey".equals(param.getSource()) && param.getSysUserId() == null) {
            String sign = SignUtil.encrypt(templateFormKey + "preview", CharsetUtil.UTF_8);
            String url = tduckDomain + "/" + templateFormKey + "?sign=" + sign + "&source=preview&submit=disabled";
            if (param.getHospitalInfoId() != null) {
                url = url + "&hospitalInfoId=" + param.getHospitalInfoId();
            }
            if (StringUtils.isNotBlank(param.getDeptName())) {
                url = url + "&deptName=" + param.getDeptName();
            }
            return url;
        }

        String formKey = this.createProjectFormKey(templateFormKey, String.valueOf(projectInfoId), productId);
        // 表单信息不存在，先创建表单，再返回表单地址
        // 先查询模板表单
        FmUserForm templateFmUserForm = fmUserFormMapper.selectOne(new QueryWrapper<FmUserForm>().eq("form_key", templateFormKey).eq("form_class", 1));
        // 添加复制的表单信息
        FmUserForm copyFmUserForm = new FmUserForm();
        BeanUtils.copyProperties(templateFmUserForm, copyFmUserForm);
        copyFmUserForm.setId(SnowFlakeUtil.getId());
        copyFmUserForm.setFormKey(formKey);
        String textName = templateFmUserForm.getTextName();
        String copyTextName = projectInfo.getProjectName() + "_" + templateFmUserForm.getTextName();
        String copyName = templateFmUserForm.getName().replace(textName, copyTextName);
        copyFmUserForm.setTextName(copyTextName);
        copyFmUserForm.setName(copyName);
        copyFmUserForm.setStatus(FormStatusEnum.RELEASE);
        copyFmUserForm.setCreateTime(new Date());
        copyFmUserForm.setUpdateTime(new Date());
        copyFmUserForm.setYunYingProductId(productId);
        copyFmUserForm.setProjectInfoId(projectInfoId);
        copyFmUserForm.setCustomInfoId(customInfo.getCustomInfoId());
        copyFmUserForm.setIsTemplate(1);
        // 项目固定放到id为10001的文件夹下
        copyFmUserForm.setFolderId(10001L);
        int insert = fmUserFormMapper.insert(copyFmUserForm);
        if (1 != insert) {
            throw new DataSourceException("fm_user_form 添加数据失败");
        }
        // 复制题目
        List<FmUserFormItem> fmUserFormItemList = fmUserFormItemMapper.selectList(new QueryWrapper<FmUserFormItem>().eq("form_key", templateFormKey));
        if (CollectionUtils.isEmpty(fmUserFormItemList)) {
            throw new IllegalArgumentException("需要复制的表单模板没有调研项目");
        }
        int formKey1 = fmUserFormItemMapper.delete(new QueryWrapper<FmUserFormItem>().eq("form_key", formKey));
        int a = 0;
        for (FmUserFormItem fmUserFormItem : fmUserFormItemList) {
            FmUserFormItem copyFmUserFormItem = new FmUserFormItem();
            BeanUtils.copyProperties(fmUserFormItem, copyFmUserFormItem);
            copyFmUserFormItem.setId(SnowFlakeUtil.getId());
            copyFmUserFormItem.setFormKey(formKey);
            copyFmUserFormItem.setCreateTime(new Date());
            copyFmUserFormItem.setUpdateTime(new Date());
            int insert1 = fmUserFormItemMapper.insert(copyFmUserFormItem);
            a = a + insert1;
        }
        if (fmUserFormItemList.size() != a) {
            throw new DataSourceException("复制表单调研项目异常");
        }
        // 复制逻辑判断
        List<FmUserFormLogic> fmUserFormLogicList = fmUserFormLogicMapper.selectList(new QueryWrapper<FmUserFormLogic>().eq("form_key", templateFormKey));
        if (!CollectionUtils.isEmpty(fmUserFormLogicList)) {
            int de = fmUserFormLogicMapper.delete(new QueryWrapper<FmUserFormLogic>().eq("form_key", formKey));
            int b = 0;
            for (FmUserFormLogic fmUserFormLogic : fmUserFormLogicList) {
                FmUserFormLogic copyUserFormLogic = new FmUserFormLogic();
                BeanUtils.copyProperties(fmUserFormLogic, copyUserFormLogic);
                copyUserFormLogic.setId(SnowFlakeUtil.getId());
                copyUserFormLogic.setFormKey(formKey);
                copyUserFormLogic.setCreateTime(new Date());
                copyUserFormLogic.setUpdateTime(new Date());
                int insert1 = fmUserFormLogicMapper.insert(copyUserFormLogic);
                b = b + insert1;
            }
            if (fmUserFormLogicList.size() != b) {
                throw new DataSourceException("复制表单逻辑判断异常");
            }
        }
        // 复制配置
        List<FmUserFormSetting> fmUserFormSettings = fmUserFormSettingMapper.selectList(new QueryWrapper<FmUserFormSetting>().eq("form_key", templateFormKey));
        if (!CollectionUtils.isEmpty(fmUserFormSettings)) {
            int delete1 = fmUserFormSettingMapper.delete(new QueryWrapper<FmUserFormSetting>().eq("form_key", formKey));
            int c = 0;
            for (FmUserFormSetting fmUserFormSetting : fmUserFormSettings) {
                FmUserFormSetting copyFmUserFormSetting = new FmUserFormSetting();
                BeanUtils.copyProperties(fmUserFormSetting, copyFmUserFormSetting);
                copyFmUserFormSetting.setId(SnowFlakeUtil.getId());
                copyFmUserFormSetting.setFormKey(formKey);
                int insert1 = fmUserFormSettingMapper.insert(copyFmUserFormSetting);
                c = c + insert1;
            }
            if (fmUserFormSettings.size() != c) {
                throw new DataSourceException("复制表单配置信息异常");
            }
        }
        // 复制外观
        List<FmUserFormTheme> fmUserFormThemeList = fmUserFormThemeMapper.selectList(new QueryWrapper<FmUserFormTheme>().eq("form_key", templateFormKey));
        if (!CollectionUtils.isEmpty(fmUserFormThemeList)) {
            int delete2 = fmUserFormThemeMapper.delete(new QueryWrapper<FmUserFormTheme>().eq("form_key", formKey));
            int d = 0;
            for (FmUserFormTheme fmUserFormTheme : fmUserFormThemeList) {
                FmUserFormTheme copyFmUserFormTheme = new FmUserFormTheme();
                BeanUtils.copyProperties(fmUserFormTheme, copyFmUserFormTheme);
                copyFmUserFormTheme.setId(SnowFlakeUtil.getId());
                copyFmUserFormTheme.setFormKey(formKey);
                copyFmUserFormTheme.setCreateTime(new Date());
                copyFmUserFormTheme.setUpdateTime(new Date());
                int insert1 = fmUserFormThemeMapper.insert(copyFmUserFormTheme);
                d = d + insert1;
            }
            if (fmUserFormThemeList.size() != d) {
                throw new DataSourceException("复制表单调研项目异常");
            }
        }
        // 复制题目选项
        List<UserFormItemOptional> userFormItemOptionalList = userFormItemOptionalMapper.selectList(new QueryWrapper<UserFormItemOptional>().eq("form_key", templateFormKey));
        if (!CollectionUtils.isEmpty(userFormItemOptionalList)) {
            int delete3 = userFormItemOptionalMapper.delete(new QueryWrapper<UserFormItemOptional>().eq("form_key", formKey));
            // 添加复制的表单的表单项目
            int addCount = 0;
            for (UserFormItemOptional userFormItemOptional : userFormItemOptionalList) {
                UserFormItemOptional copyUserFormItemOptional = new UserFormItemOptional();
                BeanUtils.copyProperties(userFormItemOptional, copyUserFormItemOptional);
                copyUserFormItemOptional.setId(SnowFlakeUtil.getId());
                copyUserFormItemOptional.setFormKey(formKey);
                copyUserFormItemOptional.setCreateTime(new Date());
                copyUserFormItemOptional.setUpdateTime(new Date());
                int addResult = userFormItemOptionalMapper.insert(copyUserFormItemOptional);
                addCount = addCount + addResult;
            }
            if (userFormItemOptionalList.size() != addCount) {
                throw new DataSourceException("复制表单调研项明细异常");
            }
        }
        // 复制待办内容
        List<TaskTrigger> taskTriggerList = taskTriggerMapper.selectList(new QueryWrapper<TaskTrigger>().eq("form_key", templateFormKey));
        if (!CollectionUtils.isEmpty(taskTriggerList)) {
            int delete4 = taskTriggerMapper.delete(new QueryWrapper<TaskTrigger>().eq("form_key", formKey));
            // 添加复制的表单的表单项目
            int addCount = 0;
            for (TaskTrigger taskTrigger : taskTriggerList) {
                TaskTrigger copyTaskTrigger = new TaskTrigger();
                BeanUtils.copyProperties(taskTrigger, copyTaskTrigger);
                copyTaskTrigger.setId(SnowFlakeUtil.getId());
                copyTaskTrigger.setFormKey(formKey);
                copyTaskTrigger.setCreateTime(new Date());
                copyTaskTrigger.setUpdateTime(new Date());
                copyTaskTrigger.setProjectInfoId(projectInfoId);
                int addResult = taskTriggerMapper.insert(copyTaskTrigger);
                addCount = addCount + addResult;
            }
            if (taskTriggerList.size() != addCount) {
                throw new DataSourceException("复制待办内容异常");
            }
        }
        // 复制待办生成条件
        List<TaskCondition> taskConditionList = taskConditionMapper.selectList(new QueryWrapper<TaskCondition>().eq("form_key", templateFormKey));
        if (!CollectionUtils.isEmpty(taskConditionList)) {
            int delete5 = taskConditionMapper.delete(new QueryWrapper<TaskCondition>().eq("form_key", formKey));
            // 添加复制的表单的表单项目
            int addCount = 0;
            for (TaskCondition taskCondition : taskConditionList) {
                TaskCondition copyTaskCondition = new TaskCondition();
                BeanUtils.copyProperties(taskCondition, copyTaskCondition);
                copyTaskCondition.setId(SnowFlakeUtil.getId());
                copyTaskCondition.setFormKey(formKey);
                copyTaskCondition.setCreateTime(new Date());
                copyTaskCondition.setUpdateTime(new Date());
                copyTaskCondition.setProjectInfoId(projectInfoId);
                int addResult = taskConditionMapper.insert(copyTaskCondition);
                addCount = addCount + addResult;
            }
            if (taskConditionList.size() != addCount) {
                throw new DataSourceException("复制待办生成条件异常");
            }
        }

        // 后端审核
        if ("audit".equals(param.getOperationSource())) {
            return this.createFormUrl(formKey, param.getSysUserId(), "config", param.getHospitalInfoId(), param.getDeptName());
        }
        if ("survey".equals(param.getOperationSource())) {
            List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                    new QueryWrapper<FmUserFormData>()
                            .eq("form_key", formKey)
                            .eq("hospital_info_id", param.getHospitalInfoId())
                            .eq("source", "config")
                            .eq("save_type", "permanent")
            );
            // 没有最终调研结果
            if (CollectionUtils.isEmpty(fmUserFormDataList)) {
                return this.createFormUrl(formKey, param.getSysUserId(), "survey", param.getHospitalInfoId(), param.getDeptName());
            }
            if (fmUserFormDataList.size() > 1) {
                throw new IllegalArgumentException(String.format("当前产品当前医院的最终调研结果存在多份，form_key=%s，hospital_info_id=%s", formKey, param.getHospitalInfoId()));
            }
            FmUserFormData fmUserFormData = fmUserFormDataList.get(0);
            return this.createFormUrl(formKey, fmUserFormData.getSysUserId(), "config", param.getHospitalInfoId(), fmUserFormData.getDeptName());
        }

        // 准备阶段
        List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                new QueryWrapper<FmUserFormData>()
                        .eq("form_key", formKey)
                        .eq("hospital_info_id", param.getHospitalInfoId())
                        .eq("source", "config")
                        .eq("save_type", "permanent")
        );
        if (!CollectionUtils.isEmpty(fmUserFormDataList)) {
            if (fmUserFormDataList.size() > 1) {
                throw new IllegalArgumentException(String.format("当前产品当前医院的最终调研结果存在多份，form_key=%s，hospital_info_id=%s", formKey, param.getHospitalInfoId()));
            }
            FmUserFormData fmUserFormData = fmUserFormDataList.get(0);
            return this.createFormUrl(formKey, fmUserFormData.getSysUserId(), "config", param.getHospitalInfoId(), fmUserFormData.getDeptName());
        }
        throw new IllegalArgumentException(String.format("请确认当前产品【%s】已经完成调研。form_key=%s，hospital_info_id=%s", productName, formKey, param.getHospitalInfoId()));
    }


    @Override
    public DifferenceInfoDTO showDifference(ShowDifferenceParam param) {
        String differenceInfo = tduckManage.getDifferenceInfo(param.getFormKey(), param.getHospitalInfoId());
        JSONObject jsonObject = JSON.parseObject(differenceInfo);
        JSONObject data = JSON.parseObject(jsonObject.getString("data"));
        List<String> deptList = JSON.parseArray(data.getString("deptList"), String.class);
        List<QuestionInfoDTO> questionList = JSON.parseArray(data.getString("questionList"), QuestionInfoDTO.class);
        List<FormDataDTO> formData = JSON.parseArray(data.getString("formData"), FormDataDTO.class).stream().sorted(Comparator.comparing(FormDataDTO::getDeptName)).collect(Collectors.toList());
        // 组装表头
        List<QuestionHeaderDTO> headerInfoList = this.createHeader(questionList);
        // 开始拼装行数据
        List<Map<String, Object>> list = new ArrayList<>(formData.size());
        for (int row = 1; row <= formData.size(); row++) {
            FormDataDTO deptName = formData.get(row - 1);
            Map<String, Object> map = new HashMap<>();
            for (int column = 1; column <= headerInfoList.size(); column++) {
                QuestionHeaderDTO questionHeaderDTO = headerInfoList.get(column - 1);
                if ("deptName".equals(questionHeaderDTO.getId())) {
                    map.put("deptName", deptName.getDeptName());
                    map.put("dataId", deptName.getId());
                    map.put("confirmed", "config".equals(deptName.getSource()));
                    map.put("formKey", deptName.getFormKey());
                    map.put("detailUrl", this.createUrl(deptName));
                } else {
                    map.put(questionHeaderDTO.getId(), this.convertValue(questionList, questionHeaderDTO.getId(), deptName.getDeptName()));
                }
            }
            list.add(map);
        }
        // 固定header
        List<String> headerNameList = new ArrayList<>();
        headerNameList.add("deptName");
        // 最终返回的表头信息只包含活动列
        List<QuestionHeaderDTO> collect = headerInfoList.stream().filter(item -> !headerNameList.contains(item.getId())).collect(Collectors.toList());
        return DifferenceInfoDTO.builder().headerInfo(collect).tableData(list).build();
    }

    private String convertValue(List<QuestionInfoDTO> questionList, String questionId, String deptName) {
        QuestionInfoDTO questionInfoDTO = questionList.stream().filter(item -> questionId.equals(item.getQuestionId())).findFirst().orElse(null);
        if (questionInfoDTO == null) {
            return null;
        } else {
            AnswerInfoDTO answerInfoDTO = questionInfoDTO.getAnswersList().stream().filter(item -> deptName.equals(item.getDeptName())).findFirst().orElse(null);
            if (answerInfoDTO == null) {
                return null;
            }
            if (answerInfoDTO.getAnswersDesc() == null && answerInfoDTO.getAnswersValue() != null) {
                answerInfoDTO.setAnswersDesc(answerInfoDTO.getAnswersValue());
            }
            if (answerInfoDTO.getAnswersDesc() instanceof List) {
                return String.join(", ", (List) answerInfoDTO.getAnswersDesc());
            } else {
                if (answerInfoDTO.getAnswersDesc() != null) {
                    return answerInfoDTO.getAnswersDesc().toString();
                }
                return null;
            }

        }

    }


    private List<QuestionHeaderDTO> createHeader(List<QuestionInfoDTO> questionList) {
        List<QuestionHeaderDTO> headerInfoList = new ArrayList<>();
        QuestionHeaderDTO infoDTO1 = QuestionHeaderDTO.builder().sort(1).name("科室").id("deptName").build();
        headerInfoList.add(infoDTO1);
        List<QuestionHeaderDTO> collect = questionList.stream().map(item -> QuestionHeaderDTO.builder().sort(item.getSort()).name(item.getQuestionDesc()).id(item.getQuestionId()).build()).collect(Collectors.toList());
        headerInfoList.addAll(collect);
        headerInfoList = headerInfoList.stream().sorted(Comparator.comparing(QuestionHeaderDTO::getSort).reversed()).collect(Collectors.toList());
        return headerInfoList;
    }


    @Resource
    private ProjProductDeliverRecordService deliverRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result confirmData(ConfirmDataParam param) {
        String s = tduckManage.confirmData(param.getId(), param.getSysUserId(), param.getProjectInfoId(),
                param.getHospitalInfoId());
        // 判断返回值是否为200. 当返回值不是200时 代表异常。返回前端错误信息
        JSONObject jsonObject = JSON.parseObject(s);
        if ("200".equals(jsonObject.get("code").toString())) {
            projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);

            List<ProjProductDeliverRecord> surveyProductDeliverRecord = deliverRecordService.getSurveyProductDeliverRecord(param.getProjectInfoId(), true);
            int finishedProductCount = surveyPlanService.getFinishedProductCount(param.getProjectInfoId());
            // 需要调研的产品总数不等于已完成的调研总数
            if (surveyProductDeliverRecord.size() != finishedProductCount) {
                projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);
            }
            if (surveyProductDeliverRecord.size() == finishedProductCount) {
                projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.FINISHED);
            }
            return Result.success("成功");
        } else {
            throw new CustomException(StrUtil.format("调研问卷生成代办任务失败：{}", jsonObject.getString("msg")));
        }
    }

    @Override
    public List<FormDataDTO> getDataByFormKey(String formKey, Long hospitalInfoId) {
        String s = tduckManage.getDataByFormKey(formKey, hospitalInfoId);
        JSONObject jsonObject = JSON.parseObject(s);
        if (!jsonObject.containsKey("data")) {
            return new ArrayList<>();
        }
        return JSON.parseArray(jsonObject.getString("data"), FormDataDTO.class);
    }


    private String createUrl(FormDataDTO formDataDTO) {
        String sign = SignUtil.encrypt(formDataDTO.getFormKey() + formDataDTO.getSysUserId() + formDataDTO.getSource(), CharsetUtil.UTF_8);
        String url = tduckDomain + "/" + formDataDTO.getFormKey() + "?sysUserId=" + formDataDTO.getSysUserId() + "&sign=" + sign + "&source=" + formDataDTO.getSource();
        if (formDataDTO.getHospitalInfoId() != null) {
            url = url + "&hospitalInfoId=" + formDataDTO.getHospitalInfoId();
        }
        if (StringUtils.isNotBlank(formDataDTO.getDeptName())) {
            url = url + "&deptName=" + formDataDTO.getDeptName();
        }
        return url;
    }

    @Override
    public List<TduckConfigInfoDTO> showConfigQuestionAndAnswer(ShowConfigParam showConfigParam) {
        List<ProjProductConfig> projProductConfigs = projProductConfigMapper.selectList(
                new QueryWrapper<ProjProductConfig>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", showConfigParam.getProjectInfoId())
                        .eq("hospital_info_id", showConfigParam.getHospitalInfoId())
                        .eq("yy_product_id", showConfigParam.getYyProductId())
        );

        if (!CollectionUtils.isEmpty(projProductConfigs)) {
            return projProductConfigs.stream().map(item -> TduckConfigInfoDTO.builder()
                    .configName(item.getConfigName())
                    .configCode(item.getConfigCode())
                    .configValue(item.getConfigValue())
                    .question(item.getSurveyQuestion())
                    .answer(item.getSurveyAnswer())
                    .configStatus(item.getConfigStatus())
                    .build()).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public Result<Boolean> copySurveyResult(TduckCopySurveyResultParam param) {
        return tduckManage.copySurveyResult(param);
    }


    @Resource
    private ProjProjectInfoService projProjectInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result confirmDataForProduct(ConfirmDataForProductParam param) {
        boolean openSurveyAudit = projProjectInfoService.isOpenSurveyAudit(param.getProjectInfoId());
        if (openSurveyAudit) {
            ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
            projSurveyPlanDTO.setProjectInfoId(param.getProjectInfoId());
            projSurveyPlanDTO.setHospitalInfoId(param.getHospitalInfoId());
            projSurveyPlanDTO.setYyProductId(Long.valueOf(param.getYyProductId()));
            List<SurveyPlanTaskResp> surveyPlan = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
            List<Integer> unAuditStatusCode = new ArrayList<>();
            unAuditStatusCode.add(0);
            unAuditStatusCode.add(6);
            unAuditStatusCode.add(4);
            unAuditStatusCode.add(5);
            unAuditStatusCode.add(2);
            if (!CollectionUtils.isEmpty(surveyPlan)) {
                List<Integer> collect = surveyPlan.stream().map(SurveyPlanTaskResp::getCompleteStatus).collect(Collectors.toList());
                boolean b = collect.stream().anyMatch(unAuditStatusCode::contains);
                if (b) {
                    return Result.fail("需要将调研计划提交审核并由后端人员审核通过之后才能确认完成");
                }
            }
        }
        //更新调研待办的状态为已完成
        UpdateSurveyPlanStatusReq updateSurveyPlanStatus = new UpdateSurveyPlanStatusReq();
        updateSurveyPlanStatus.setProjectInfoId(param.getProjectInfoId());
        updateSurveyPlanStatus.setHospitalInfoId(param.getHospitalInfoId());
        updateSurveyPlanStatus.setSurveyUserId(userHelper.getCurrentUser().getSysUserId());
        updateSurveyPlanStatus.setYyProductId(Long.valueOf(param.getYyProductId()));
        updateSurveyPlanStatus.setCompleteStatus(1);
        int count = projSurveyPlanMapper.updateSurveyPlanStatus(updateSurveyPlanStatus);
        log.info("项目经理确认后，更新调研计划状态为已完成，更新数量：{}", count);

        projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);

        List<ProjProductDeliverRecord> surveyProductDeliverRecord = deliverRecordService.getSurveyProductDeliverRecord(param.getProjectInfoId(), true);
        int finishedProductCount = surveyPlanService.getFinishedProductCount(param.getProjectInfoId());
        // 需要调研的产品总数不等于已完成的调研总数
        if (surveyProductDeliverRecord.size() != finishedProductCount) {
            projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);
        }
        if (surveyProductDeliverRecord.size() == finishedProductCount) {
            projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.FINISHED);
        }

        return Result.success("成功");
    }

    @Override
    public Result<List<SaveBackLogAndDetailReq>> getCreateTaskParam(ConfirmFinalDataParam param) {
        return tduckManage.getCreateTaskParam(param);
    }

    @Override
    @Transactional
    public FmUserForm createSatisfactionSurveyForm(FormClassEnum formClass, String yyProductId, ProjProjectInfo projectInfo) {
        FmUserForm projectUserForm = null;
        try {
            projectUserForm = fmUserFormService.getProjectUserFormByProjectInfoId(projectInfo.getProjectInfoId(), yyProductId, formClass.getFormClass());
        } catch (NoProjectUserFormException e) {
            log.error("创建项目使用的填鸭问卷，目前不存在项目问卷需要正常进行流程，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (projectUserForm != null) {
            return projectUserForm;
        }
        return copyFormByTemplate(formClass, yyProductId, projectInfo);
    }


    @Override
    @Transactional
    public FmUserForm copyFormByTemplate(FormClassEnum formClass, String yyProductId, ProjProjectInfo projectInfo) {
        // 查询调研问卷模板
        FmUserForm templateForm = fmUserFormService.getTemplateUserFormByProjectInfo(yyProductId, projectInfo.getProjectType(), projectInfo.getUpgradationType(), formClass.getFormClass());

        // 调研问卷模板的表单key
        String templateFormKey = templateForm.getFormKey();

        // 创建项目实际使用的调研问卷的表单key
        String projectFormKey = this.createProjectFormKey(templateFormKey, String.valueOf(projectInfo.getProjectInfoId()), yyProductId);

        String lockKey = "create_form_" + projectFormKey;
        if (ObjectUtil.isNotEmpty(redisUtil.get(lockKey))) {
            throw new IllegalArgumentException("请勿重复操作，正在处理中，稍后再试。");
        }

        redisUtil.set(lockKey, projectFormKey, 1, TimeUnit.MINUTES);

        try {
            // 调研问卷模板的问卷名称
            String templateFormTextName = templateForm.getTextName();

            // 项目实际使用的调研问卷的问卷名称
            String projectFormTextName = projectInfo.getProjectName() + "_" + templateForm.getTextName();

            FmUserForm projectForm = new FmUserForm();
            BeanUtils.copyProperties(templateForm, projectForm);
            projectForm.setId(SnowFlakeUtil.getId());
            projectForm.setFormKey(projectFormKey);
            projectForm.setTextName(projectFormTextName);
            projectForm.setName(templateForm.getName().replace(templateFormTextName, projectFormTextName));
            projectForm.setStatus(FormStatusEnum.RELEASE);
            projectForm.setCreateTime(new Date());
            projectForm.setUpdateTime(new Date());
            projectForm.setYunYingProductId(yyProductId);
            projectForm.setProjectInfoId(projectInfo.getProjectInfoId());
            projectForm.setCustomInfoId(projectInfo.getCustomInfoId());
            projectForm.setIsTemplate(1);
            projectForm.setFolderId(formClass.getFolderId());
            int insert = fmUserFormMapper.insert(projectForm);
            if (1 != insert) {
                throw new DataSourceException("fm_user_form 添加数据失败");
            }
            // 复制题目
            List<FmUserFormItem> fmUserFormItemList = fmUserFormItemMapper.selectList(new QueryWrapper<FmUserFormItem>().eq("form_key", templateFormKey));
            if (CollectionUtils.isEmpty(fmUserFormItemList)) {
                throw new IllegalArgumentException("需要复制的表单模板没有调研项目");
            }
            // 复制之前先删除原来的题目
            fmUserFormItemMapper.delete(new QueryWrapper<FmUserFormItem>().eq("form_key", projectFormKey));
            int userFormItemAddCount = 0;
            for (FmUserFormItem fmUserFormItem : fmUserFormItemList) {
                FmUserFormItem copyFmUserFormItem = new FmUserFormItem();
                BeanUtils.copyProperties(fmUserFormItem, copyFmUserFormItem);
                copyFmUserFormItem.setId(SnowFlakeUtil.getId());
                copyFmUserFormItem.setFormKey(projectFormKey);
                copyFmUserFormItem.setCreateTime(new Date());
                copyFmUserFormItem.setUpdateTime(new Date());
                int insert1 = fmUserFormItemMapper.insert(copyFmUserFormItem);
                userFormItemAddCount = userFormItemAddCount + insert1;
            }
            if (fmUserFormItemList.size() != userFormItemAddCount) {
                throw new DataSourceException("复制题目，实际复制数量与预计复制数量不符");
            }
            // 复制题目逻辑关联
            List<FmUserFormLogic> fmUserFormLogicList = fmUserFormLogicMapper.selectList(new QueryWrapper<FmUserFormLogic>().eq("form_key", templateFormKey));
            if (!CollectionUtils.isEmpty(fmUserFormLogicList)) {
                // 复制之前先删除原来的题目逻辑关联
                fmUserFormLogicMapper.delete(new QueryWrapper<FmUserFormLogic>().eq("form_key", projectFormKey));
                int addCount = 0;
                for (FmUserFormLogic fmUserFormLogic : fmUserFormLogicList) {
                    FmUserFormLogic copyUserFormLogic = new FmUserFormLogic();
                    BeanUtils.copyProperties(fmUserFormLogic, copyUserFormLogic);
                    copyUserFormLogic.setId(SnowFlakeUtil.getId());
                    copyUserFormLogic.setFormKey(projectFormKey);
                    copyUserFormLogic.setCreateTime(new Date());
                    copyUserFormLogic.setUpdateTime(new Date());
                    int insert1 = fmUserFormLogicMapper.insert(copyUserFormLogic);
                    addCount = addCount + insert1;
                }
                if (fmUserFormLogicList.size() != addCount) {
                    throw new DataSourceException("复制题目逻辑关联，实际复制数量与预计复制数量不符");
                }
            }
            // 复制配置
            List<FmUserFormSetting> fmUserFormSettings = fmUserFormSettingMapper.selectList(new QueryWrapper<FmUserFormSetting>().eq("form_key", templateFormKey));
            if (!CollectionUtils.isEmpty(fmUserFormSettings)) {
                // 复制之前先删除原来的配置
                fmUserFormSettingMapper.delete(new QueryWrapper<FmUserFormSetting>().eq("form_key", projectFormKey));
                int addCount = 0;
                for (FmUserFormSetting fmUserFormSetting : fmUserFormSettings) {
                    FmUserFormSetting copyFmUserFormSetting = new FmUserFormSetting();
                    BeanUtils.copyProperties(fmUserFormSetting, copyFmUserFormSetting);
                    copyFmUserFormSetting.setId(SnowFlakeUtil.getId());
                    copyFmUserFormSetting.setFormKey(projectFormKey);
                    int insert1 = fmUserFormSettingMapper.insert(copyFmUserFormSetting);
                    addCount = addCount + insert1;
                }
                if (fmUserFormSettings.size() != addCount) {
                    throw new DataSourceException("复制配置，实际复制数量与预计复制数量不符");
                }
            }
            // 复制外观
            List<FmUserFormTheme> fmUserFormThemeList = fmUserFormThemeMapper.selectList(new QueryWrapper<FmUserFormTheme>().eq("form_key", templateFormKey));
            if (!CollectionUtils.isEmpty(fmUserFormThemeList)) {
                // 复制之前先删除原来的外观
                fmUserFormThemeMapper.delete(new QueryWrapper<FmUserFormTheme>().eq("form_key", projectFormKey));
                int addCount = 0;
                for (FmUserFormTheme fmUserFormTheme : fmUserFormThemeList) {
                    FmUserFormTheme copyFmUserFormTheme = new FmUserFormTheme();
                    BeanUtils.copyProperties(fmUserFormTheme, copyFmUserFormTheme);
                    copyFmUserFormTheme.setId(SnowFlakeUtil.getId());
                    copyFmUserFormTheme.setFormKey(projectFormKey);
                    copyFmUserFormTheme.setCreateTime(new Date());
                    copyFmUserFormTheme.setUpdateTime(new Date());
                    int insert1 = fmUserFormThemeMapper.insert(copyFmUserFormTheme);
                    addCount = addCount + insert1;
                }
                if (fmUserFormThemeList.size() != addCount) {
                    throw new DataSourceException("复制外观，实际复制数量与预计复制数量不符");
                }
            }
            // 复制题目选项
            List<UserFormItemOptional> userFormItemOptionalList = userFormItemOptionalMapper.selectList(new QueryWrapper<UserFormItemOptional>().eq("form_key", templateFormKey));
            if (!CollectionUtils.isEmpty(userFormItemOptionalList)) {
                // 复制之前先删除原来的题目选项
                userFormItemOptionalMapper.delete(new QueryWrapper<UserFormItemOptional>().eq("form_key", projectFormKey));
                int addCount = 0;
                for (UserFormItemOptional userFormItemOptional : userFormItemOptionalList) {
                    UserFormItemOptional copyUserFormItemOptional = new UserFormItemOptional();
                    BeanUtils.copyProperties(userFormItemOptional, copyUserFormItemOptional);
                    copyUserFormItemOptional.setId(SnowFlakeUtil.getId());
                    copyUserFormItemOptional.setFormKey(projectFormKey);
                    copyUserFormItemOptional.setCreateTime(new Date());
                    copyUserFormItemOptional.setUpdateTime(new Date());
                    int addResult = userFormItemOptionalMapper.insert(copyUserFormItemOptional);
                    addCount = addCount + addResult;
                }
                if (userFormItemOptionalList.size() != addCount) {
                    throw new DataSourceException("复制题目选项，实际复制数量与预计复制数量不符");
                }
            }
            // 复制产品待办
            List<TaskTrigger> taskTriggerList = taskTriggerMapper.selectList(new QueryWrapper<TaskTrigger>().eq("form_key", templateFormKey));
            if (!CollectionUtils.isEmpty(taskTriggerList)) {
                // 复制之前先删除原来的产品待办
                taskTriggerMapper.delete(new QueryWrapper<TaskTrigger>().eq("form_key", projectFormKey));
                int addCount = 0;
                for (TaskTrigger taskTrigger : taskTriggerList) {
                    TaskTrigger copyTaskTrigger = new TaskTrigger();
                    BeanUtils.copyProperties(taskTrigger, copyTaskTrigger);
                    copyTaskTrigger.setId(SnowFlakeUtil.getId());
                    copyTaskTrigger.setFormKey(projectFormKey);
                    copyTaskTrigger.setCreateTime(new Date());
                    copyTaskTrigger.setUpdateTime(new Date());
                    copyTaskTrigger.setProjectInfoId(projectInfo.getProjectInfoId());
                    int addResult = taskTriggerMapper.insert(copyTaskTrigger);
                    addCount = addCount + addResult;
                }
                if (taskTriggerList.size() != addCount) {
                    throw new DataSourceException("复制产品待办，实际复制数量与预计复制数量不符");
                }
            }
            // 复制产品待办生成条件
            List<TaskCondition> taskConditionList = taskConditionMapper.selectList(new QueryWrapper<TaskCondition>().eq("form_key", templateFormKey));
            if (!CollectionUtils.isEmpty(taskConditionList)) {
                // 复制之前先删除原来的产品待办生成条件
                taskConditionMapper.delete(new QueryWrapper<TaskCondition>().eq("form_key", projectFormKey));
                int addCount = 0;
                for (TaskCondition taskCondition : taskConditionList) {
                    TaskCondition copyTaskCondition = new TaskCondition();
                    BeanUtils.copyProperties(taskCondition, copyTaskCondition);
                    copyTaskCondition.setId(SnowFlakeUtil.getId());
                    copyTaskCondition.setFormKey(projectFormKey);
                    copyTaskCondition.setCreateTime(new Date());
                    copyTaskCondition.setUpdateTime(new Date());
                    copyTaskCondition.setProjectInfoId(projectInfo.getProjectInfoId());
                    int addResult = taskConditionMapper.insert(copyTaskCondition);
                    addCount = addCount + addResult;
                }
                if (taskConditionList.size() != addCount) {
                    throw new DataSourceException("复制产品待办生成条件，实际复制数量与预计复制数量不符");
                }
            }
            return projectForm;
        } finally {
            redisUtil.del(lockKey);
        }
    }
}
