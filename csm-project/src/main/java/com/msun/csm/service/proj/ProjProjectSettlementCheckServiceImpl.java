package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.MilestoneNodeEnum.PROJECT_ENTRY;
import static com.msun.csm.common.enums.projreview.ProjectStageCodeEnum.STAGE_ENTRY;
import static com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_ON_SITE;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_ON_SITE;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projreview.ProjReviewResultEnum;
import com.msun.csm.common.enums.projreview.SceneCodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckPrepayFlagEnum;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.common.enums.projsettlement.NextCheckNodeUnNormalEnum;
import com.msun.csm.common.enums.projsettlement.PaySignageEnum;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.enums.projsettlement.SettlementStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProjectStage;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecord;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementLog;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementRule;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheck;
import com.msun.csm.dao.entity.proj.ProjSyncApiLogs;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.dao.entity.report.ReportCustomInfo;
import com.msun.csm.dao.entity.sys.ConfigSendMessage;
import com.msun.csm.dao.mapper.config.ConfigSendMessageMapper;
import com.msun.csm.dao.mapper.dict.DictProjectStageMapper;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectOrderRelationMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementCheckMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementRuleMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementUnCheckMapper;
import com.msun.csm.dao.mapper.proj.ProjSyncApiLogsMapper;
import com.msun.csm.dao.mapper.report.ReportCustomInfoMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.client.yunwei.YunweiFeignClient;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeHospitalReq;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeReq;
import com.msun.csm.feign.entity.yunwei.resp.YunweiLoginResp;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.model.convert.ProjProjectSettlementCheckConvert;
import com.msun.csm.model.convert.settlement.ProjProjectSettlementLogConvert;
import com.msun.csm.model.dto.ConfirmEntryDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.projreview.ProjReviewDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckApplyEnterDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckProgressDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaveDTO;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleCheckParam;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.vo.ProjProjectInfoVO;
import com.msun.csm.model.vo.projsettlement.ApplyEnterMainInfoVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckApplyEnterVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckProcessVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckProgressVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementCheckedProgressVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementLogVO;
import com.msun.csm.model.vo.projsettlement.ShowFile;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.model.yunying.RiskAuditContractDTO;
import com.msun.csm.model.yunying.RiskAuditDTO;
import com.msun.csm.model.yunying.SyncMidOrderAuditDTO;
import com.msun.csm.model.yunying.SyncRiskAuditContractDTO;
import com.msun.csm.model.yunying.SyncRiskAuditDTO;
import com.msun.csm.model.yunying.SyncYunAuditDto;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.sysuser.SysUserService;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.util.DateUtils;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */

@Slf4j
@Service
public class ProjProjectSettlementCheckServiceImpl implements ProjProjectSettlementCheckService {

    /**
     * 运营驳回时消息
     */
    public static final String RISK_REJECTED_CONTENT = "首付款未满足";
    public static final String RISK_AUDIT_CONTENT = "首付款已满足";
    public static final String RISK_MID_ORDER_AUDIT_TITLE = "免中间件部署申请";
    public static final String RISK_AUDIT_WAIT_AUDIT_CLOUD_CONTENT = RISK_AUDIT_CONTENT + ", 待审核云资源开通确认时间";

    public static final String RISK_AUDIT_CLOUD_CONTENT = "云资源开通确认时间审核通过";

    @Value("${project.current.url}")
    private String platformUrl;

    @Resource
    private ProjProjectSettlementCheckMapper settlementCheckMapper;

    @Resource
    private ProjContractInfoMapper contractInfoMapper;

    @Resource
    private ProjProjectSettlementLogService settlementLogService;

    @Resource
    private ProjProjectSettlementService settlementService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private OnlineStepService onlineStepService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjProjectSettlementCheckSaleService settlementCheckSaleService;

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;

    @Resource
    private ProjProjectSettlementLogMapper settlementLogMapper;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private ProjProjectSettlementService projProjectSettlementService;

    @Resource
    private ConfirmEntryService confirmEntryService;

    @Resource
    private ProjProjectSettlementLogConvert settlementLogConvert;

    @Resource
    private ProjProjectFileService projectFileService;

    @Resource
    private ProjProjectSettlementRuleMapper settlementRuleMapper;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private ProjProjectSettlementMapper settlementMapper;

    @Resource
    private ProjCustomCloudServiceMapper cloudServiceMapper;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjProjectOrderRelationMapper orderRelationMapper;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;

    @Resource
    private YunweiFeignClient yunweiFeignClient;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;


    @Resource
    private ConfigSendMessageMapper configSendMessageMapper;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private ProjProjectSettlementUnCheckMapper unCheckMapper;

    @Resource
    private ProjSyncApiLogsMapper projSyncApiLogsMapper;

    @Lazy
    @Resource
    private ProjProjectReviewRecordMapper reviewRecordMapper;

    @Resource
    private ProjProjectSettlementCheckConvert settlementCheckConvert;

    @Resource
    private ReportCustomInfoMapper reportCustomInfoMapper;

    @Resource
    @Lazy
    private ProjOnlineStepMapper projOnlineStepMapper;

    @Lazy
    @Resource
    private ProjProductTaskMapper productTaskMapper;

    @Lazy
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ProjProjectReviewService reviewService;

    @Resource
    private DictProjectStageMapper dictProjectStageMapper;

    @Value("${project.current.qyWeChat-Auth-url}")
    private String weChatAuthUrl;

    @Lazy
    @Resource
    private ProjMessageInfoService messageInfoService;

    @Resource
    private CommonService commonService;
    @Resource
    private ProjProjectSettlementRuleService settlementRuleService;
    @Resource
    private ProjProjectSettlementMidOrderService settlementMidOrderService;

    @Override
    public void updateResult(Long projectSettlementId, String checkContent, CheckNodeEnum checkNodeEnum, CheckResultEnum checkResultEnum) {
        updateResult(projectSettlementId, null, checkContent, checkNodeEnum, checkResultEnum);
    }

    @Override
    public void updateResult(Long projectSettlementId, Long sysUserId, String checkContent, CheckNodeEnum checkNodeEnum, CheckResultEnum checkResultEnum) {
        ProjProjectSettlementCheck settlementCheck = new ProjProjectSettlementCheck();
        settlementCheck.setCheckTime(new Date());
        settlementCheck.setCheckContent(checkContent);
        settlementCheck.setCheckResult(checkResultEnum.getCode());
        if (ObjectUtil.isNotEmpty(sysUserId)) {
            settlementCheck.setCheckUserId(sysUserId);
        }
        settlementCheckMapper.update(settlementCheck, new QueryWrapper<ProjProjectSettlementCheck>().eq("project_settlement_id", projectSettlementId).eq("check_node", checkNodeEnum.getCode()));
    }

    public Result<Boolean> saveSettlement(ProjProjectSettlementCheckSaveDTO settlementCheckSaveDTO) {
        try {
            return settlementCheckService.saveSettlementImpl(settlementCheckSaveDTO);
        } catch (Throwable e) {
            log.error("销售提交入驻申请异常. message: {}, e=", e.getMessage(), e);
            String checkNodeName = StrUtil.EMPTY;
            CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(settlementCheckSaveDTO.getCheckNode());
            if (ObjectUtil.isNotEmpty(checkNodeEnum)) {
                assert checkNodeEnum != null;
                checkNodeName = checkNodeEnum.getDesc();
            }
            throw new CustomException("申请异常, 请联系管理员处理, 入驻阶段, " + checkNodeName + "审核异常. message: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public Result<Boolean> saveSettlementImpl(ProjProjectSettlementCheckSaveDTO settlementCheckSaveDTO) {
        long projectInfoId = Long.parseLong(settlementCheckSaveDTO.getProjectInfoId());
        if (mainService.isOldSettlement(projectInfoId)) {
            handleSettlementUpdate(projectInfoId, settlementCheckSaveDTO);
            if (CheckNodeEnum.ENSURE_SETTLE_IN.getCode() == settlementCheckSaveDTO.getCheckNode() && settlementCheckSaveDTO.getCheckResult() == CheckResultEnum.AUDIT_PASS.getCode()) {
                // 入驻则处理
                handleEnsure(projectInfoId, settlementCheckSaveDTO, false);
            } else {
                sendMessage(projectInfoId, settlementCheckSaveDTO.getCheckNode(), settlementCheckSaveDTO.getCheckResult());
            }
        } else {
            // 入驻节点, 且入驻通过
            if (CheckNodeEnum.ENSURE_SETTLE_IN.getCode() == settlementCheckSaveDTO.getCheckNode() && settlementCheckSaveDTO.getCheckResult() == CheckResultEnum.AUDIT_PASS.getCode()) {
                handleEnsure(projectInfoId, settlementCheckSaveDTO, true);
            } else {
                handleSettlementUpdate(projectInfoId, settlementCheckSaveDTO);
                // 若是入驻节点, 且入驻驳回
                if (CheckNodeEnum.ENSURE_SETTLE_IN.getCode() == settlementCheckSaveDTO.getCheckNode()) {
                    // 回更通知销售申请里程碑状态为未完成
                    updateMilestoneProjEntryNotComplete(projectInfoId);
                }
                int nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfoId, settlementCheckSaveDTO.getCheckNode(), ObjectUtil.isEmpty(settlementCheckSaveDTO.getCheckResult()) ? -1 : settlementCheckSaveDTO.getCheckResult());
                // 新pmo审核流程, 则更新【通知节点】
                if (nextCheckNode == NextCheckNodeUnNormalEnum.HAS_SETTLEMENT_NEXT_NODE.getCode()) {
                    updateProjectEntryMilestone(projectInfoId, settlementCheckSaveDTO, Long.parseLong(settlementCheckSaveDTO.getUserId()));
                } else {
                    sendMessage(projectInfoId, settlementCheckSaveDTO.getCheckNode(), settlementCheckSaveDTO.getCheckResult());
                }
            }
        }
        return Result.success();
    }

    /**
     * 更新项目的里程碑节点【通知销售申请入驻】状态为未完成
     *
     * @param projectInfoId 项目id
     */
    public void updateMilestoneProjEntryNotComplete(Long projectInfoId) {
        UpdateMilestoneDTO dto = new UpdateMilestoneDTO();
        dto.setNodeHeadId(null);
        dto.setActualCompTime(null);
        dto.setMilestoneStatus(NumberEnum.NO_0.num());
        // 查询项目通知销售里程碑节点id
        ProjMilestoneInfo milestoneInfo = milestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfoId).eq("project_stage_code", STAGE_ENTRY.getCode()).eq("milestone_node_code", PROJECT_ENTRY.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        dto.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
        milestoneInfoService.updateMilestoneById(dto);
    }

    public void updateProjectEntryMilestone(Long projectInfoId, Object dto) {
        updateProjectEntryMilestone(projectInfoId, dto, null);
    }

    public void updateProjectEntryMilestone(Long projectInfoId, Object dto, Long userId) {
        // 查询节点id
        ProjMilestoneInfo milestoneInfo = milestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfoId).eq("project_stage_code", STAGE_ENTRY.getCode()).eq("milestone_node_code", PROJECT_ENTRY.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        if (ObjectUtil.isNotEmpty(milestoneInfo)) {
            UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
            updateMilestoneDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
            updateMilestoneDTO.setNodeHeadId(ObjectUtil.isEmpty(userId) ? -1 : userId);
            updateMilestoneDTO.setMilestoneStatus(NumberEnum.NO_1.num());
            Result<Boolean> result = milestoneInfoService.compMilestone(updateMilestoneDTO);
            log.info("入驻流程-里程碑节点更新: {}, 入参: {}", result.toString(), dto);
        } else {
            log.warn("入驻流程-里程碑节点更新, 未查询到更新的节点. 入参: {}", dto);
        }
    }

    /**
     * 更新节点状态
     *
     * @param projectInfoId          项目id
     * @param settlementCheckSaveDTO 参数
     */
    private void handleSettlementUpdate(Long projectInfoId, ProjProjectSettlementCheckSaveDTO settlementCheckSaveDTO) {
        ProjProjectSettlement settlement = projProjectSettlementService.getProjectSettlement(projectInfoId);
        // 保存申请单状态
        CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(settlementCheckSaveDTO.getCheckNode());
        SettlementStatusEnum settlementStatusEnum = settlementCheckSaleService.getLastedSuccessSettlementStatusEnum(projectInfoId, settlementCheckSaveDTO.getCheckNode(), settlementCheckSaveDTO.getCheckResult());
        if (ObjectUtil.isEmpty(settlementStatusEnum)) {
            throw new RuntimeException("入驻申请审核保存, 未查询到对应的需要保存的状态");
        }
        projProjectSettlementService.updateSettlementStatus(settlement, settlementStatusEnum);
        // 更新状态
        SysUserVO sysUserVO = sysUserService.getUserById(Long.valueOf(settlementCheckSaveDTO.getUserId()));
        // 插入日志
        ProjProjectSettlementLog settlementLog = settlementLogService.insert(settlement, settlementCheckSaveDTO.getCheckContent(), checkNodeEnum, sysUserVO, settlementStatusEnum.getCode());
        // 判断如果是方案分公司复核, 则更新uncheck表logId
        if (settlementCheckSaveDTO.getCheckNode() == CheckNodeEnum.BRANCH_MANAGER_AUDIT.getCode()) {
            List<ProjProjectSettlementUnCheck> unChecks = unCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementUnCheck>().eq("project_info_id", projectInfoId).orderByDesc("create_time"));
            if (CollUtil.isNotEmpty(unChecks)) {
                ProjProjectSettlementUnCheck unCheck = unChecks.get(0);
                ProjProjectSettlementUnCheck copy = new ProjProjectSettlementUnCheck();
                copy.setProjectSettlementUnCheckId(unCheck.getProjectSettlementUnCheckId());
                copy.setProjectSettlementLogId(settlementLog.getProjectSettlementLogId());
                unCheckMapper.updateById(copy);
            }
        }
        // 更新终审表
        settlementCheckService.updateResult(settlement.getProjectSettlementId(), sysUserVO.getSysUserId(), settlementCheckSaveDTO.getCheckContent(), checkNodeEnum, CheckResultEnum.getCheckResultEnumByCode(settlementCheckSaveDTO.getCheckResult()));
        // 更新审核节点
        updateRiskaudit(projectInfoId, settlementCheckSaveDTO.getCheckNode(), settlementCheckSaveDTO.getCheckResult());
    }

    /**
     * 更新运营部审核状态
     *
     * @param projectInfoId 项目id
     * @param checkNode     当前审核节点
     * @param checkResult   审核结果
     */
    public void updateRiskaudit(Long projectInfoId, Integer checkNode, Integer checkResult) {
        // 判断若是运营部审核, 是否跳过
        int nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfoId, checkNode, ObjectUtil.isEmpty(checkResult) ? -1 : checkResult);
        if (nextCheckNode == CheckNodeEnum.RISK_AUDIT.getCode()) {
            List<ProjProjectSettlementRule> settlementRules = settlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>().eq("project_info_id", projectInfoId).in("project_rule_code", CollUtil.newArrayList(SettlementRuleCodeEnum.SETTLEMENT_PAY_ADVANCE_CHARGE.getCode(), SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM.getCode())));
            // 只判断只需审核首付款且是后续项目, 若首付款已缴纳则作废运营部审核环节
            boolean onlyPaysignAudit = CollUtil.isNotEmpty(settlementRules) && settlementRules.size() == NumberEnum.NO_1.num() && settlementRules.get(0).getProjectRuleCode().equals(SettlementRuleCodeEnum.SETTLEMENT_PAY_ADVANCE_CHARGE.getCode());
            // 是否缴纳首付款 true已缴纳, false未缴纳
            boolean needPaySignage = settlementRuleService.needPaySignage(projectInfoId);
            if (onlyPaysignAudit && !needPaySignage) {
                int count = settlementCheckMapper.delete(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_info_id", projectInfoId).eq("check_node", CheckNodeEnum.RISK_AUDIT.getCode()));
                ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
                log.info("作废运营部审核首付款节点. {}项目-{}: , count: {}", projectInfo.getProjectName(), projectInfo.getProjectNumber(), count);
            }
        }
    }

    /**
     * 处理确认入驻
     *
     * @param projectInfoId          项目id
     * @param settlementCheckSaveDTO 参数
     * @param hasPmoAudit            是否有pmo审核流程
     */
    private void handleEnsure(Long projectInfoId, ProjProjectSettlementCheckSaveDTO settlementCheckSaveDTO, boolean hasPmoAudit) {
        ConfirmEntryDTO confirmEntryDTO = new ConfirmEntryDTO();
        confirmEntryDTO.setProjectInfoId(projectInfoId);
        confirmEntryDTO.setMilestoneInfoId(Long.parseLong(settlementCheckSaveDTO.getMilestoneInfoId()));
        confirmEntryService.updateMillstone(confirmEntryDTO);
        // 添加pmo审核日志s
        if (hasPmoAudit) {
            DictProjectStage dictProjectStage = dictProjectStageMapper.selectOne(new QueryWrapper<DictProjectStage>().eq("project_stage_code", STAGE_ENTRY.getCode()));
            reviewService.insertReviewLogNew(ProjReviewResultEnum.SETTLEMENT_ENSURE, dictProjectStage.getId(), new ProjReviewDTO(), projectInfoId, SceneCodeEnum.PROJECT_REVIEW.getSceneCode(), "项目经理确认入驻", "项目经理确认入驻");
        }
        //修改项目自身状态-入驻阶段-同步运营平台节点数据后更新
        ProjProjectInfo updateProjectInfo = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        // 修改项目自身状态-入驻阶段
        // 同步运营平台工单信息-先获取项目信息
        updateYunyingNodeStatus(projectInfoId, SOFTWARE_ON_SITE, PURCHASE_SOFTWARE_ON_SITE);
        //首期项目修改数据统计表对应的入驻时间
        if (updateProjectInfo.getHisFlag() == 1) {
            ReportCustomInfo updateData = new ReportCustomInfo();
            updateData.setProjectInfoId(projectInfoId);
            updateData.setSettleInTime(updateProjectInfo.getSettleInTime());
            updateData.setUpdateTime(new Date());
            SysUserVO user = userHelper.getCurrentUser();
            Long userId = user == null ? -2L : user.getSysUserId();
            updateData.setUpdaterId(userId);
            int count = reportCustomInfoMapper.updateByProjectInfoId(updateData);
            log.info("修改统计表入驻时间,影响条数:{}, 项目id {}", count, projectInfoId);
        }
        ProjProjectInfo projProjectInfo = mainService.getProjectInfo(projectInfoId);
        // 首期项目发送消息
        if (projProjectInfo.getHisFlag() == 1) {
            //给pmo发消息
            sendToPmoMessage(projectInfoId, updateProjectInfo.getProjectName() + "入驻通知：" + updateProjectInfo.getProjectName() + "已成功入驻，请您知晓！");
        }
    }

    public void updateYunyingNodeStatus(Long projectInfoId, OrderStepEnum softStepEnum, OrderStepEnum purchaseEnum, SysUserVO sysUserVO) {
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        //获取工单信息
        ProjOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(projectInfo.getOrderInfoId());
        if (orderInfo == null) {
            throw new RuntimeException("修改项目自身状态-入驻阶段-同步运营平台节点数据后更新, 未查询到运营平台工单信息");
        }
        switch (orderInfo.getDeliveryOrderType()) {
            case 1:
                // 软件
                onlineStepService.updateNodeStatus(projectInfoId, softStepEnum, sysUserVO);
                break;
            case 8:
                // 外采软件
                onlineStepService.updateNodeStatus(projectInfoId, purchaseEnum, sysUserVO);
                break;
            default:
                break;
        }
    }

    public void updateYunyingNodeStatus(Long projectInfoId, OrderStepEnum softStepEnum, OrderStepEnum purchaseEnum) {
        updateYunyingNodeStatus(projectInfoId, softStepEnum, purchaseEnum, userHelper.getCurrentUser());
    }

    /**
     * 向pmo发送消息
     *
     * @param projectInfoId 项目id
     */
    public void sendToPmoMessage(Long projectInfoId, String content) {
        //给pmo发消息
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(projectInfoId);
        messageParam.setContent(content);
        messageParam.setMessageTypeId(4002L);
        messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
        sendMessageService.sendMessage(messageParam);
    }

    /**
     * 向下一个节点派送消息
     *
     * @param projectInfoId 项目id
     * @param checkNode     当前节点
     * @param checkResult   当前节点状态
     */
    public void sendMessage(Long projectInfoId, Integer checkNode, Integer checkResult) {
        sendMessage(projectInfoId, checkNode, checkResult, false);
    }

    /**
     * 向下一个节点派送消息
     *
     * @param projectInfoId 项目id
     * @param checkNode     当前节点
     * @param checkResult   当前节点状态
     */
    public void sendMessage(Long projectInfoId, Integer checkNode, Integer checkResult, boolean onlyMessagePush) {
        //确认入驻是最后流程
        // 查询销售申请的下一个节点
        int nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfoId, checkNode, ObjectUtil.isEmpty(checkResult) ? -1 : checkResult);
        // 发送到下一个节点
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        settlementCheckSaleService.sendMessage(checkNode, nextCheckNode, projectInfoId, projectInfo, onlyMessagePush);
    }

    @Override
    public Result<List<ProjProjectSettlementCheckProgressVO>> findCurrentCheckNode(ProjProjectSettlementCheckProgressDTO checkProgressDTO) {
        // 查询当前节点
        ProjProjectSettlement settlement = mainService.getCurrentSettlement(Long.parseLong(checkProgressDTO.getProjectInfoId()));
        List<ProjProjectSettlementLog> settlementLogs = settlementLogMapper.selectList(new QueryWrapper<ProjProjectSettlementLog>().eq("project_info_id", Long.parseLong(checkProgressDTO.getProjectInfoId())).orderByDesc("create_time"));
        ProjProjectSettlementLog settlementLog = null;
        if (CollUtil.isNotEmpty(settlementLogs)) {
            settlementLog = settlementLogs.get(0);
        }
        // 查询终审节点
        List<ProjProjectSettlementCheck> checks = settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_settlement_id", settlement.getProjectSettlementId()).orderByAsc("check_node"));
        if (CollUtil.isEmpty(checks)) {
            throw new RuntimeException("未查询到终审节点.");
        }
        List<ProjProjectSettlementCheckProgressVO> progressVOS = new ArrayList<>();
        boolean goon = true;
        ProjProjectSettlementCheck firstCheckNode = checks.get(0);
        int currentNode;
        for (ProjProjectSettlementCheck check : checks) {
            if (ObjectUtil.isEmpty(settlementLog)) {
                currentNode = firstCheckNode.getCheckNode();
            } else {
                assert settlementLog != null;
                currentNode = settlementLog.getOperateNode();
            }
            ProjProjectSettlementCheckProgressVO progressVO = new ProjProjectSettlementCheckProgressVO();
            CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(check.getCheckNode());
            progressVO.setNodeName(checkNodeEnum.getDesc());
            boolean isCurrent = check.getCheckNode() == currentNode;
            progressVO.setIsCurrent(isCurrent);
            if (goon) {
                progressVO.setOperateTime(DateUtil.formatDateTime(check.getCheckTime()));
                if (ObjectUtil.isNotEmpty(check.getCheckUserId())) {
                    SysUserVO sysUserVO = sysUserService.getUserById(check.getCheckUserId());
                    progressVO.setOperateUser(sysUserVO.getUserName());
                    progressVO.setOperateUserDeptName(sysUserVO.getDeptName());
                }
                progressVO.setOpinion(check.getCheckContent());
            }
            if (isCurrent) {
                goon = false;
            }
            progressVOS.add(progressVO);
        }
        return Result.success(progressVOS);
    }

    @Override
    public List<ProjProjectSettlementCheckedProgressVO> findCheckedNode(Long projectInfoId) {
        // 查询当前节点
        ProjProjectSettlement settlement = settlementService.getSettlementByProjectInfoId(projectInfoId);
        if (ObjectUtil.isEmpty(settlement)) {
            throw new RuntimeException("未查询到入驻申请工单.");
        }
        List<ProjProjectSettlementLog> settlementLogs = settlementLogMapper.selectList(new QueryWrapper<ProjProjectSettlementLog>().eq("project_info_id", projectInfoId).orderByDesc("create_time"));
        ProjProjectSettlementLog settlementLog = null;
        if (CollUtil.isNotEmpty(settlementLogs)) {
            settlementLog = settlementLogs.get(0);
        }
        // 查询终审节点
        List<ProjProjectSettlementCheck> checks = settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_settlement_id", settlement.getProjectSettlementId()).orderByAsc("check_node"));
        if (CollUtil.isEmpty(checks)) {
            return new ArrayList<>();
        }
        List<ProjProjectSettlementCheckedProgressVO> progressVOS = new ArrayList<>();
        for (ProjProjectSettlementCheck check : checks) {
            if (ObjectUtil.isEmpty(settlementLog)) {
                break;
            }
            ProjProjectSettlementCheckedProgressVO progressVO = new ProjProjectSettlementCheckedProgressVO();
            CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(check.getCheckNode());
            if (ObjectUtil.isEmpty(checkNodeEnum)) {
                continue;
            }
            progressVO.setNodeName(checkNodeEnum.getDesc());
            boolean isCurrent = check.getCheckNode().intValue() == settlementLog.getOperateNode();
            progressVO.setOpinion(check.getCheckContent());
            // 查询节点状态. 同意、驳回、未审批
            if (ObjectUtil.isNotEmpty(check.getCheckResult())) {
                progressVO.setCheckResult(check.getCheckResult());
            }
            progressVOS.add(progressVO);
            if (isCurrent) {
                break;
            }
        }
        return progressVOS;
    }

    @Override
    public Result<String> syncRiskAudit(SyncRiskAuditDTO syncRiskAuditDTO) {
        try {
            sysOperLogService.apiOperLogInsert(syncRiskAuditDTO, "风控首付款审核入参", StrUtil.EMPTY, Log.LogOperType.OTHER.getCode());
            return syncRiskAuditImpl(syncRiskAuditDTO);
        } catch (Throwable e) {
            SyncRiskMsgErrorRecord syncRiskMsgErrorRecord = SyncRiskMsgErrorRecord.builder().syncRiskAuditDTO(syncRiskAuditDTO).error(e).build();
            sysOperLogService.apiOperLogInsert(syncRiskMsgErrorRecord, "风控首付款审核异常", StrUtil.EMPTY, Log.LogOperType.OTHER.getCode());
            throw e;
        }
    }

    public Result<String> syncRiskAuditImpl(SyncRiskAuditDTO syncRiskAuditDTO) {
        // 查询合同号是否
        List<ProjContractInfo> contractInfos = contractInfoMapper.selectList(new QueryWrapper<ProjContractInfo>().in("yy_contract_id", syncRiskAuditDTO.getContractList().stream().map(SyncRiskAuditContractDTO::getContractNum).collect(Collectors.toList())));
        if (CollUtil.isEmpty(contractInfos)) {
            log.error("未查询到传入合同信息. param: {}", syncRiskAuditDTO);
            return Result.fail("未查询到合同信息.");
        }
        // 查询项目下的合同
        ProjProjectInfo projectInfo = commonService.getProjectInfoByYyOrderId(syncRiskAuditDTO.getYyOrderId());
        // 兼容云容灾首付款审核
        if (ObjectUtil.isEmpty(projectInfo)) {
            return Result.success();
        }
        // 查询项目所含运营合同id
        // 查询项目中所含合同
        List<ProjContractInfo> tmpContractInfos = mainService.findStandardContractInfo(projectInfo.getProjectInfoId());
        // 项目若是首期,查询云资源合同
        if (CollUtil.isEmpty(tmpContractInfos)) {
            log.error("根据运营工单未查询到合同信息. param: {}", syncRiskAuditDTO);
            throw new CustomException("未查询到合同信息.");
        }
        List<Long> yyContractIds = tmpContractInfos.stream().map(ProjContractInfo::getYyContractId).collect(Collectors.toList());
        if (syncRiskAuditDTO.getContractList().size() != yyContractIds.size()) {
            log.error("合同未匹配. 传入: {}, 存在: {}", syncRiskAuditDTO.getContractList(), yyContractIds);
            throw new CustomException("合同未匹配.");
        }
        if (syncRiskAuditDTO.getContractList().stream().noneMatch(e -> yyContractIds.stream().anyMatch(f -> f.longValue() == e.getContractNum()))) {
            log.error("合同匹配失败, 有未匹配到的合同. 传入: {}, 存在: {}", syncRiskAuditDTO.getContractList(), yyContractIds);
            throw new CustomException("合同匹配异常.");
        }
        log.info("合同匹配成功.");
        // 查询工单下所有项目
        List<RiskAuditDTO> riskAuditDTOS = findRiskAuditProjectInfoId(syncRiskAuditDTO);
        if (CollUtil.isEmpty(riskAuditDTOS)) {
            return Result.fail("未查询到需要审核的项目. 请求参数: " + syncRiskAuditDTO);
        }
        // 轮询处理符合条件的项目入驻
        for (RiskAuditDTO riskAuditDTO : riskAuditDTOS) {
            // 	对接回款更新交付合同状态后，交付流程应对销售提交审核时首付款未付后，审核过程之间同步了首付款， 在审核时应放行流程。
            //目前因处理逻辑判断交付首付款状态导致未处理入驻流程， 流程阻塞。  因此注释
          /*  if (ObjectUtil.isNotEmpty(riskAuditDTO.getNotRequireAuditCurProj()) && riskAuditDTO.getNotRequireAuditCurProj()) {
                log.info("运营部审核首付款, 当前项目无需审核. 跳过. riskAuditDto: {}", riskAuditDTO);
                continue;
            }*/
            riskHandle(riskAuditDTO.getProjectInfoId(), riskAuditDTO);
        }
        return Result.success();
    }

    /**
     * 根据运营合同id查询项目
     *
     * @param syncRiskAuditDTO 运营回调参数
     * @return 项目审核结果及集
     */
    public List<RiskAuditDTO> findRiskAuditProjectInfoId(SyncRiskAuditDTO syncRiskAuditDTO) {
        List<RiskAuditDTO> riskAuditDTOS = CollUtil.newArrayList();
        List<SyncRiskAuditContractDTO> contractDTOS = syncRiskAuditDTO.getContractList();
        List<Long> yyContractIds = contractDTOS.stream().map(SyncRiskAuditContractDTO::getContractNum).collect(Collectors.toList());
        // 查询项目信息
        List<ProjProjectInfo> projectInfos = commonService.findProjectInfoByYyContractIds(yyContractIds);
        if (CollUtil.isEmpty(projectInfos)) {
            return CollUtil.newArrayList();
        }
        List<Long> projectInfoIds2 = projectInfos.stream().map(ProjProjectInfo::getProjectInfoId).collect(Collectors.toList());
        log.info("根据运营合同id查询到项目数量: {}, 分别是: {}", projectInfoIds2.size(), projectInfos);
        // 判断这些项目哪些是可以同步审核的. 计算规则: 若所查询项目合同被包含在了被审合同中(合同数量小于等于被审合同, 且合同id匹配), 且项目的审核节点与被审核项目一致则进行记录
        for (ProjProjectInfo projectInfo : projectInfos) {
            // 查询工单
            ProjOrderInfo orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("order_info_id", projectInfo.getOrderInfoId()));
            if (ObjectUtil.isEmpty(orderInfo)) {
                log.warn("未查询到工单. projectInfoId: {}, orderInfoId: {}", projectInfo.getProjectInfoId(), projectInfo.getOrderInfoId());
                continue;
            }
            boolean currentAuditPro = orderInfo.getYyOrderId().longValue() == syncRiskAuditDTO.getYyOrderId();
            RiskAuditDTO riskAuditDTO = new RiskAuditDTO();
            riskAuditDTO.setProjectInfoId(projectInfo.getProjectInfoId());
            riskAuditDTO.setUserNam(syncRiskAuditDTO.getUserNam());
            riskAuditDTO.setYyOrderId(orderInfo.getYyOrderId());
            riskAuditDTO.setSkipAudit(false);
            // 查询项目中所含合同
            List<ProjContractInfo> tmpContractInfos = mainService.findStandardContractInfo(projectInfo.getProjectInfoId(), false);
            if (CollUtil.isEmpty(tmpContractInfos)) {
                if (currentAuditPro) {
                    riskAuditDTO.setNotRequireAuditCurProj(true);
                    riskAuditDTOS.add(riskAuditDTO);
                    log.warn("运营正在处理当前项目首付款审核, 审核节点不匹配. riskAuditDto: {}", riskAuditDTO);
                }
                continue;
            }
            List<Long> tmpYyContractIds = tmpContractInfos.stream().map(ProjContractInfo::getYyContractId).collect(Collectors.toList());
            // 匹配数量
            if (tmpYyContractIds.size() > yyContractIds.size()) {
                continue;
            }
            // id匹配
            if (tmpYyContractIds.stream().noneMatch(e -> yyContractIds.stream().anyMatch(f -> e.longValue() == f))) {
                continue;
            }
            // 是否是审核状态
            if (!isCurrentAuditNode(projectInfo.getProjectInfoId())) {
                if (currentAuditPro) {
                    riskAuditDTO.setNotRequireAuditCurProj(true);
                    riskAuditDTOS.add(riskAuditDTO);
                    log.warn("运营正在处理当前项目首付款审核, 审核节点不匹配. riskAuditDto: {}", riskAuditDTO);
                }
                continue;
            }
            List<RiskAuditContractDTO> riskAuditContractDTOS = contractDTOS.stream().filter(e -> tmpYyContractIds.stream().anyMatch(f -> f.longValue() == e.getContractNum())).map(e -> BeanUtil.copyProperties(e, RiskAuditContractDTO.class)).collect(Collectors.toList());
            // 判断项目所含合同是否在本次运营审核的合同中都存在
            if (CollUtil.isEmpty(riskAuditContractDTOS) || (riskAuditContractDTOS.size() < tmpYyContractIds.size())) {
                log.warn("未匹配到合同信息. 审批合同内容: {}, 项目合同: {}", contractDTOS, tmpContractInfos);
                continue;
            }
            if (currentAuditPro) {
                riskAuditDTO.setMsg(syncRiskAuditDTO.getMsg());
                // 存在特批, 且没有未通过审批的合同
                if (contractDTOS.stream().anyMatch(e -> e.getCode() == CheckResultEnum.JUMP_PAY_SIGN_AUDIT.getyYcode()) && contractDTOS.stream().noneMatch(e -> e.getCode() == CheckResultEnum.AUDIT_FAIL.getyYcode())) {
                    log.info("符合通过条件(含特批). param: {}", contractDTOS);
                    riskAuditDTO.setCode(CheckResultEnum.AUDIT_PASS.getyYcode());
                    // 设置是否跳过
                    riskAuditDTO.setSkipAudit(true);
                } else if (contractDTOS.stream().noneMatch(e -> e.getCode() == CheckResultEnum.AUDIT_FAIL.getyYcode())) {
                    log.info("符合通过条件(不含特批). param: {}", contractDTOS);
                    riskAuditDTO.setCode(CheckResultEnum.AUDIT_PASS.getyYcode());
                } else {
                    log.info("不符合通过条件(不含特批). param: {}", contractDTOS);
                    riskAuditDTO.setCode(CheckResultEnum.AUDIT_FAIL.getyYcode());
                }
            }
            if (!currentAuditPro) {
                // 非本次项目过滤调特批的合同进行处理
                riskAuditContractDTOS = riskAuditContractDTOS.stream().filter(e -> !(CheckResultEnum.JUMP_PAY_SIGN_AUDIT.getyYcode() == e.getCode())).collect(Collectors.toList());
                if (CollUtil.isEmpty(riskAuditContractDTOS)) {
                    log.info("没有符合处理的项目. projectInfoId: {}", projectInfo.getProjectInfoId());
                    continue;
                }
                if (contractDTOS.stream().noneMatch(e -> CheckResultEnum.AUDIT_PASS.getyYcode() == e.getCode())) {
                    log.info("不符合通过条件(非审核项目). param: {}", contractDTOS);
                    riskAuditDTO.setCode(CheckResultEnum.AUDIT_FAIL.getyYcode());
                } else {
                    log.info("符合通过条件(非审核项目). param: {}", contractDTOS);
                    riskAuditDTO.setCode(CheckResultEnum.AUDIT_PASS.getyYcode());
                }
            }
            riskAuditDTO.setContractList(riskAuditContractDTOS);
            riskAuditDTOS.add(riskAuditDTO);
        }
        log.info("运营部审核首付款回更项目审核状态, 匹配到项目有: {}, 分别是: {}", riskAuditDTOS.size(), riskAuditDTOS);
        return riskAuditDTOS;
    }

    /**
     * 是否是当前审核节点
     *
     * @param projectInfoId 项目id
     * @return true:是当前审核节点, false:不是
     */
    private Boolean isCurrentAuditNode(Long projectInfoId) {
        // 获取下一节点是运营部审核的项目
        ProjProjectSettlement settlement = settlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        if (ObjectUtil.isEmpty(settlement)) {
            return false;
        }
        int settlementStatus = ObjectUtil.isEmpty(settlement.getSettlementStatus()) ? -1 : settlement.getSettlementStatus();
        int checkResult;
        if (settlementStatus == -1) {
            checkResult = CheckResultEnum.AUDIT_PASS.getCode();
        } else {
            checkResult = Objects.requireNonNull(SettlementStatusEnum.getSettlementStatusByCode(settlementStatus)).getCheckResultEnum().getCode();
        }
        int currentCheckNode = Objects.requireNonNull(CheckNodeEnum.getCheckNodeEnumBySettlementStatusCode(settlementStatus)).getCode();
        int nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfoId, currentCheckNode, checkResult);
        return nextCheckNode == CheckNodeEnum.RISK_AUDIT.getCode();
    }

    /**
     * 根据交付工单查询项目的下一个审核节点是运营部审核的项目，记录集合
     *
     * @param orderInfos 工单集合
     * @return List<long>
     */
    @Override
    public List<Long> getProjectInfoIds(List<ProjOrderInfo> orderInfos) {
        List<Long> projectInfoIds = new ArrayList<>();
        for (ProjOrderInfo orderInfo : orderInfos) {
            List<ProjProjectInfo> projectInfos = projProjectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq("order_info_id", orderInfo.getOrderInfoId()));
            if (CollUtil.isEmpty(projectInfos)) {
                continue;
            }
            for (ProjProjectInfo projectInfo : projectInfos) {
                // 获取下一节点是运营部审核的项目
                ProjProjectSettlement settlement = settlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfo.getProjectInfoId()));
                if (ObjectUtil.isEmpty(settlement)) {
                    continue;
                }
                int settlementStatus = ObjectUtil.isEmpty(settlement.getSettlementStatus()) ? -1 : settlement.getSettlementStatus();
                int checkResult;
                if (settlementStatus == -1) {
                    checkResult = CheckResultEnum.AUDIT_PASS.getCode();
                } else {
                    checkResult = Objects.requireNonNull(SettlementStatusEnum.getSettlementStatusByCode(settlementStatus)).getCheckResultEnum().getCode();
                }
                int currentCheckNode = Objects.requireNonNull(CheckNodeEnum.getCheckNodeEnumBySettlementStatusCode(settlementStatus)).getCode();
                int nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfo.getProjectInfoId(), currentCheckNode, checkResult);
                if (nextCheckNode == CheckNodeEnum.RISK_AUDIT.getCode()) {
                    projectInfoIds.add(projectInfo.getProjectInfoId());
                }
            }
        }
        return projectInfoIds;
    }

    private void riskHandle(Long projectInfoId, RiskAuditDTO riskAuditDTO) {
        boolean passed = false;
        // 更新合同状态（是否首付款）
        for (RiskAuditContractDTO riskAuditContractDTO : riskAuditDTO.getContractList()) {
            if (riskAuditContractDTO.getCode() == CheckResultEnum.JUMP_PAY_SIGN_AUDIT.getyYcode()) {
                log.info("含特批合同, 不更新首付款标识. param: {}", riskAuditContractDTO);
                continue;
            }
            if (riskAuditContractDTO.getCode() == CheckResultEnum.AUDIT_FAIL.getyYcode()) {
                log.info("合同审批不通过, 不更新首付款标识. param: {}", riskAuditContractDTO);
                continue;
            }
            // 更新合同首付款表示
            ProjContractInfo contractInfo = new ProjContractInfo();
            contractInfo.setPaySignage(PaySignageEnum.PAY.getCode());
            int count = contractInfoMapper.update(contractInfo, new QueryWrapper<ProjContractInfo>().eq("yy_contract_id", riskAuditContractDTO.getContractNum()));
            log.info("运营部回调, 更新合同信息. count:{}", count);
            if (count <= 0) {
                throw new RuntimeException("运营部回调. 更新合同信息异常.");
            }
        }
        if (riskAuditDTO.getCode() == CheckResultEnum.AUDIT_PASS.getyYcode()) {
            passed = true;
        }
        // 若首付款审核通过, 则进行更新记录
        ProjProjectSettlement update = new ProjProjectSettlement();
        if (passed) {
            update.setCheckPrepayFlag(CheckPrepayFlagEnum.NEED_NO_CHECK_PREPAY.getCode());
        } else {
            update.setCheckPrepayFlag(CheckPrepayFlagEnum.NEED_CHECK_PREPAY.getCode());
        }
        int count = settlementMapper.update(update, new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        log.info("更新首付款缴纳标识. settlement表check_prepay_flag字段值为: {}. count: {}", update.getCheckPrepayFlag(), count);
        // 更新节点状态, 审核或者驳回
        CheckResultEnum checkResultEnum = CheckResultEnum.getCheckResultEnumByYyCode(riskAuditDTO.getCode());
        assert checkResultEnum != null;
        ProjProjectSettlementCheckSaveDTO checkSaveDTO = getRiskDTO(projectInfoId, checkResultEnum, riskAuditDTO.getUserNam(), riskAuditDTO.getMsg(), NumberEnum.NO_1.num());
        // 若审核通过且有云资源审核
        if (passed && mainService.hasCloudFormConfirm(projectInfoId)) {
            try {
                insertSettlementLog(checkSaveDTO, projectInfoId, RISK_AUDIT_WAIT_AUDIT_CLOUD_CONTENT);
            } catch (Throwable e) {
                log.error("运营部审核首付款新增日志异常. projectInfoId: {}, message: {}, e=", projectInfoId, e.getMessage(), e);
            }
            // 推送确认单审核
            settlementCheckSaleService.sendRiskMessage(mainService.getProjectInfo(projectInfoId), false, riskAuditDTO.getSkipAudit());
            return;
        }
        checkSaveDTO.setProjectInfoId(StrUtil.toString(projectInfoId));
        settlementCheckService.saveSettlement(checkSaveDTO);
    }

    /**
     * 插入日志
     *
     * @param checkSaveDTO  审核参数
     * @param projectInfoId 项目id
     */
    public void insertSettlementLog(ProjProjectSettlementCheckSaveDTO checkSaveDTO, Long projectInfoId, String operateContent) {
        // 获取用户
        SysUserVO sysUserVO = sysUserService.getUserById(Long.parseLong(checkSaveDTO.getUserId()));
        // 获取当前项目审核节点
        ProjProjectSettlement settlement = projProjectSettlementService.getProjectSettlement(projectInfoId);
        // 获取审核结果节点枚举
        CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(checkSaveDTO.getCheckNode());
        // 获取审核节点枚举
        SettlementStatusEnum settlementStatusEnum = settlementCheckSaleService.getLastedSuccessSettlementStatusEnum(projectInfoId, checkSaveDTO.getCheckNode(), checkSaveDTO.getCheckResult());
        // 插入日志
        settlementLogService.insert(settlement, operateContent, checkNodeEnum, sysUserVO, settlementStatusEnum.getCode());
    }

    @Override
    public Result<ProjProjectSettlementCheckApplyEnterVO> getApplyEnterData(ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        ProjProjectSettlementCheckApplyEnterVO applyEnterVO = new ProjProjectSettlementCheckApplyEnterVO();
        // 获取详情
        ApplyEnterMainInfoVO applyEnterMainInfoVO = getMainInfo(checkApplyEnterDTO);

        long projectInfoId = Long.parseLong(checkApplyEnterDTO.getProjectInfoId());
        ProjProjectSettlement settlement = settlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        setFrontElement(applyEnterVO, projectInfoId, settlement);
        ProjProjectInfoVO info = new ProjProjectInfoVO();
        info.setProjectInfoId(projectInfoId);
        // 获取项目首付款信息  没有首付款信息后者非正式合同的返回-1
        try {
            mainService.hasPaysign(projectInfoId);
            ProjProjectInfoVO psa = projectInfoMapper.selectPaymentInfo(info);
            applyEnterMainInfoVO.setConPreFlag(psa.getConPreFlag());
            applyEnterMainInfoVO.setConPreDesc(psa.getConPreDesc());
            if (psa.getConPreFlag() == -1) {
                applyEnterMainInfoVO.setConPreDesc("非正式合同");
            }
        } catch (Exception e) {
            log.error("项目首付款同步失败. projectInfoId: {}", projectInfoId);
            applyEnterMainInfoVO.setConPreFlag(-1);
            applyEnterMainInfoVO.setConPreDesc("非正式合同");
        }
        applyEnterVO.setApplyEnterVo(applyEnterMainInfoVO);
        // 设置终审节点
        if (ObjectUtil.isNotEmpty(settlement) && settlement.getSettlementStatus() != -1) {
            List<ProjProjectSettlementLog> settlementLogs = mainService.findSettlementCheckLogCreateTimeAsc(projectInfoId);
            if (CollUtil.isNotEmpty(settlementLogs)) {
                // 设置审核流程日志
                List<ProjProjectSettlementLogVO> settlementLogsVo = settlementLogs.stream().map(e -> {
                    ProjProjectSettlementLogVO vo = settlementLogConvert.po2Vo(e);
                    // 查询操作人员电话
                    if (ObjectUtil.isNotEmpty(vo.getOperateUserId())) {
                        SysUser sysUser = sysUserMapper.getUserById(vo.getOperateUserId());
                        if (StrUtil.isNotEmpty(sysUser.getPhone())) {
                            vo.setOperateUserPhone(sysUser.getPhone());
                        }
                        if (ObjectUtil.isNotEmpty(e.getSettlementStatus())) {
                            SettlementStatusEnum settlementStatusEnum = SettlementStatusEnum.getSettlementStatusByCode(e.getSettlementStatus());
                            assert settlementStatusEnum != null;
                            vo.setLogTitle(settlementStatusEnum.getDesc());
                        } else {
                            CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(e.getOperateNode());
                            vo.setLogTitle(ObjectUtil.isNotEmpty(checkNodeEnum) ? Objects.requireNonNull(checkNodeEnum).getDesc() : StrUtil.EMPTY);
                        }
                    }
                    return vo;
                }).collect(Collectors.toList());
                applyEnterVO.setNodeVOList(settlementLogsVo);
            } else {
                applyEnterVO.setNodeVOList(CollUtil.newArrayList());
            }
        }
        return Result.success(applyEnterVO);
    }

    @Override
    public void setFrontElement(ProjProjectSettlementCheckApplyEnterVO applyEnterVO, Long projectInfoId, ProjProjectSettlement settlement) {
        // 设置进度
        // 销售申请和入场条件, 进程为销售申请
        applyEnterVO.setStatus(CheckNodeEnum.SALE_APPLY_ENTRY.getProcessSimpleStatus());
        Integer nextCheckNode = getNextCheckNode(projectInfoId, settlement);
        if (ObjectUtil.isNotEmpty(nextCheckNode)) {
            // -1: 反馈的下一节点不存在. -2: 已经确认入住
            if (nextCheckNode == -2) {
                applyEnterVO.setStatus(CheckNodeEnum.ENSURE_SETTLE_IN.getProcessSimpleStatus());
            } else {
                applyEnterVO.setStatus(Objects.requireNonNull(CheckNodeEnum.getCheckNodeEnumByCode(nextCheckNode)).getProcessSimpleStatus());
            }
        }
        judgeNextCheckNode(applyEnterVO, nextCheckNode, projectInfoId);
    }

    /**
     * 判断是否是确认入驻节点
     *
     * @param applyEnterVO
     * @param currentNode
     * @param projectInfoId
     */
    public void judgeNextCheckNode(ProjProjectSettlementCheckApplyEnterVO applyEnterVO, Integer currentNode, Long projectInfoId) {
        if (ObjectUtil.isEmpty(currentNode)) {
            applyEnterVO.setIsCurrentNode(false);
        } else {
            applyEnterVO.setIsCurrentNode(currentNode == CheckNodeEnum.ENSURE_SETTLE_IN.getCode());
        }
        // 设置百灵app是否提示
        applyEnterVO.setShowAppOpenTip(setShowAppOpenTip(projectInfoId));
        applyEnterVO.setCanAudit(applyEnterVO.getIsCurrentNode());
    }

    /**
     * 设置百灵app是否提示
     *
     * @param projectInfoId 项目id
     */
    public boolean setShowAppOpenTip(Long projectInfoId) {
        // 百灵app是否开通（app百灵运维是否审核通过）
        // 查询是否存在患者智能服务
        boolean showAppOpenTip = false;
        Integer count = projOnlineStepMapper.hasPatient(projectInfoId, null, 1);
        // 判定逻辑不对 。应查询工单及特批产品是否包含患者智能服务。
//        boolean hasBailingProduct = mainService.hasBailingProduct(projectInfoId);
        boolean hasBailingProduct = count > 0;
        if (hasBailingProduct) {
            // 1.app审核都走新系统。
            // 原有逻辑不对。
/*            boolean isBailingOpen = mainService.isBailingAppOpen(projectInfoId);
            showAppOpenTip = !isBailingOpen;
            // 新系统 判定逻辑
            if (showAppOpenTip) {
                List<ProjProjectReviewRecord> listReviewRecord =
                        reviewRecordMapper.selectList(new QueryWrapper<ProjProjectReviewRecord>()
                                .eq("item_code", "entry_payment_instrument")
                                .eq("project_info_id", projectInfoId)
                                .eq("is_deleted", 0)
                        );
                if (CollUtil.isNotEmpty(listReviewRecord)) {
                    boolean isOpen =
                            listReviewRecord.get(0).getReviewResult() != null && listReviewRecord.get(0)
                            .getReviewResult() == 1;
                    showAppOpenTip = !isOpen;
                }
            }*/
            List<ProjProjectReviewRecord> listReviewRecord = reviewRecordMapper.selectList(new QueryWrapper<ProjProjectReviewRecord>().eq("item_code", "entry_payment_instrument").eq("project_info_id", projectInfoId).eq("is_deleted", 0));
            if (CollUtil.isNotEmpty(listReviewRecord)) {
                boolean isOpen = listReviewRecord.get(0).getReviewResult() != null && listReviewRecord.get(0).getReviewResult() == 1;
                showAppOpenTip = !isOpen;
                if (showAppOpenTip) {
                    sendMessageToUser(projectInfoId);
                }
            }
        }
        return showAppOpenTip;
    }

    /**
     * 发送消息
     *
     * @param projectInfoId
     */
    private void sendMessageToUser(Long projectInfoId) {
        try {
            ProjProductTask task = null;
            String taskCode = "hzzn_task_16";
            String taskCodeTw = "hzzn_task_25";
            List<ProjProductTask> projProductTasks = productTaskMapper.selectList(new QueryWrapper<ProjProductTask>().eq("project_info_id", projectInfoId).in("task_code", new String[] {taskCode, taskCodeTw}));
            if (projProductTasks != null && projProductTasks.size() > 0) {
                task = projProductTasks.get(0);
            }
            if (task != null) {
                String key = "hzzntj" + task.getProductTaskId();
                Object value = redisUtil.get(key);
                if (ObjectUtils.isEmpty(value)) {
                    ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
                    SysUser sysUser = sysUserMapper.selectById(projectInfo.getProjectLeaderId());
                    String userName = sysUser != null ? sysUser.getUserName() : "";
                    // 发送企业微信消息给刘忠智， 王海华
                    String content = "【" + projectInfo.getProjectName() + "】" + "APP支付开通截图已上传，请您尽快审核！项目经理【" + userName + "】";
                    MessageParam messageParam = new MessageParam();
                    messageParam.setProjectInfoId(task.getProjectInfoId());
                    messageParam.setContent(content);
                    messageParam.setTitle("【" + projectInfo.getProjectName() + "】" + "APP支付开通审核提醒");
                    // 发送消息的人 刘忠志， 王海华
                    List<Long> sysUserIds = sysUserMapper.selectUserIdByAccount(null);
                    String userIds = sysUserIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                    // TODO: 发消息的地址===https://imsp.msunhis.com/csm-front-mobile/appAudit?productTaskId=490628096959807488&sysUserId=464993504059150337
                    String businessUrl = platformUrl + "appAudit?projectInfoId=" + task.getProjectInfoId() + "&productTaskId=" + task.getProductTaskId();
                    Long msgInfoId = messageInfoService.insert(businessUrl, sysUser.getSysUserId());
                    String url = weChatAuthUrl + "?state=" + msgInfoId;
                    messageParam.setUrl(url);
                    messageParam.setMessageTypeId(7003L);
                    messageParam.setMessageToCategory(MsgToCategory.SINGLE_PERSON.getCode());
                    messageParam.setSysUserIds(sysUserIds);
                    sendMessageService.sendMessage(messageParam);
                    redisUtil.set(key, "1", 24 * 60 * 60);
                }
            }
        } catch (Exception e) {
            log.error("处理患者智能服务数据失败", e);
        }

    }

    @Override
    public Integer getNextCheckNode(Long projectInfoId, ProjProjectSettlement settlement) {
        Integer nextCheckNode = null;
        SettlementStatusEnum settlementStatusEnumTmp = SettlementStatusEnum.getSettlementStatusByCode(settlement.getSettlementStatus());
        assert settlementStatusEnumTmp != null;
        CheckResultEnum checkResultEnum = settlementStatusEnumTmp.getCheckResultEnum();
        if (ObjectUtil.isNotEmpty(settlementStatusEnumTmp) && ObjectUtil.isNotEmpty(checkResultEnum) && settlement.getSettlementStatus() != SettlementStatusEnum.INIT.getCode()) {
            // 根据日志判断, 如果
            int currentCheckNode = Objects.requireNonNull(CheckNodeEnum.getCheckNodeEnumBySettlementStatusCode(settlement.getSettlementStatus())).getCode();
            nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfoId, currentCheckNode, checkResultEnum.getCode());
        }
        return nextCheckNode;
    }

    /**
     * 根据项目名称获取保存在项目文件存储表中的文件信息
     *
     * @param projectInfoId 项目id
     * @return ShowFile
     */
    private ShowFile getShowFile(Long projectInfoId, SettlementRuleCodeEnum ruleCodeEnum) {
        List<ProjProjectSettlementRule> settlementRules = settlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>().eq("project_info_id", projectInfoId).eq("project_rule_code", ruleCodeEnum.getCode()));
        if (CollUtil.isNotEmpty(settlementRules)) {
            ProjProjectSettlementRule settlementRule = settlementRules.get(0);
            // 查询项目文件信息
            ProjProjectFile projProjectFile = projectFileService.selectByPrimaryKey(settlementRule.getProjectFileId());
            if (ObjectUtil.isNotEmpty(projProjectFile)) {
                return new ShowFile(-1L, projProjectFile.getFileName(), OBSClientUtils.getTemporaryUrl(projProjectFile.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY));
            }
        }
        return null;
    }

    /**
     * 获取详情信息
     *
     * @param checkApplyEnterDTO 请求参数
     * @return ApplyEnterMainInfoVO
     */
    private ApplyEnterMainInfoVO getMainInfo(ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        ApplyEnterMainInfoVO applyEnterMainInfoVO = new ApplyEnterMainInfoVO();
        // 设置项目名称
        ProjProjectInfo projectInfo = mainService.getProjectInfo(checkApplyEnterDTO.getProjectInfoId());
        applyEnterMainInfoVO.setProjectName(projectInfo.getProjectName());
        // 设置客户名称
        ProjCustomInfo customInfo = mainService.getCustomInfo(projectInfo.getCustomInfoId());
        applyEnterMainInfoVO.setCustomerName(customInfo.getCustomName());
        // 设置销售申请时间
        List<ProjProjectSettlementCheck> settlementChecks = mainService.findSettlementCheck(checkApplyEnterDTO.getProjectInfoId());
        if (CollUtil.isNotEmpty(settlementChecks)) {
            ProjProjectSettlementCheck settlementCheck = null;
            // 获取销售申请时间
            for (ProjProjectSettlementCheck check : settlementChecks) {
                if (check.getCheckNode() == CheckNodeEnum.SALE_APPLY_ENTRY.getCode()) {
                    settlementCheck = check;
                    break;
                }
            }
            if (ObjectUtil.isNotEmpty(settlementCheck) && ObjectUtil.isNotEmpty(settlementCheck.getCheckTime())) {
                applyEnterMainInfoVO.setApplyTime(DateUtil.formatDateTime(settlementCheck.getCheckTime()));
                // 获取提交人
                if (ObjectUtil.isNotEmpty(settlementCheck.getCheckUserId())) {
                    SysUser sysUser = sysUserMapper.getUserById(settlementCheck.getCheckUserId());
                    if (ObjectUtil.isNotEmpty(sysUser)) {
                        applyEnterMainInfoVO.setSaleUserName(sysUser.getUserName());
                    }
                }
            }
        }
        // 设置节点状态
        List<ProjProjectSettlement> settlements = settlementMapper.selectList(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", Long.parseLong(checkApplyEnterDTO.getProjectInfoId())));
        if (CollUtil.isEmpty(settlements)) {
            log.info("入驻申请. 未查询到项目相关信息. 项目id");
            throw new RuntimeException(MessageFormat.format("未查询到项目相关信息. 项目id: {0}", checkApplyEnterDTO.getProjectInfoId()));
        }
        ProjProjectSettlement settlement = settlements.get(0);
        CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumBySettlementStatusCode(settlement.getSettlementStatus());
        SettlementStatusEnum settlementStatusEnum = SettlementStatusEnum.getSettlementStatusByCode(settlement.getSettlementStatus());
        int nextCheckNode = settlementCheckSaleService.getNextCheckNode(projectInfo.getProjectInfoId(), checkNodeEnum.getCode(), settlementStatusEnum.getCheckResultEnum().getCode());
        if (ObjectUtil.isNotEmpty(settlementStatusEnum)) {
            CheckNodeEnum nextCheckNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(nextCheckNode);
            if (ObjectUtil.isNotEmpty(nextCheckNodeEnum)) {
                applyEnterMainInfoVO.setStatus(nextCheckNode);
                applyEnterMainInfoVO.setStatusStr("待" + nextCheckNodeEnum.getDesc());
            } else {
                applyEnterMainInfoVO.setStatus(checkNodeEnum.getCode());
                applyEnterMainInfoVO.setStatusStr(checkNodeEnum.getDesc());
            }
        }
        // 获取所有产品
        List<ProductInfo> productInfos = applyOrderHospitalService.getProductInfoList(Long.parseLong(checkApplyEnterDTO.getProjectInfoId()));
        if (CollUtil.isNotEmpty(productInfos)) {
            StringBuilder productStrs = new StringBuilder();
            for (ProductInfo productInfo : productInfos) {
                productStrs.append(productInfo.getProductName()).append(StrUtil.COMMA);
            }
            applyEnterMainInfoVO.setProductNames(productStrs.substring(0, productStrs.length() - 1));
        }
        // 获取需要审核的内容（确认单等）
        long projectInfoId = Long.parseLong(checkApplyEnterDTO.getProjectInfoId());
        applyEnterMainInfoVO.setCloudResourceDepConfirmShowFile(getShowFile(projectInfoId, SETTLEMENT_CLOUD_CONFIRM_FORM));
        applyEnterMainInfoVO.setPreResourceShowFile(getShowFile(projectInfoId, SettlementRuleCodeEnum.SETTLEMENT_HARDWARE_LIST_FILE));
        applyEnterMainInfoVO.setSaleArrivedListShowFile(getShowFile(projectInfoId, SettlementRuleCodeEnum.SETTLEMENT_PLAN_CLOUD_FILE));
        // 查询入场条件提交人和提交时间
        List<ProjProjectSettlementCheck> checks = settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_info_id", projectInfoId).eq("check_node", CheckNodeEnum.SUBMIT_ENTRY.getCode()));
        if (CollUtil.isNotEmpty(checks)) {
            ProjProjectSettlementCheckVO checkVO = BeanUtil.copyProperties(checks.get(0), ProjProjectSettlementCheckVO.class);
            if (ObjectUtil.isNotEmpty(checkVO.getCheckUserId())) {
                SysUser sysUser = sysUserMapper.selectById(checks.get(0).getCheckUserId());
                applyEnterMainInfoVO.setCusUserName(sysUser.getUserName());
            }
            if (ObjectUtil.isNotEmpty(checkVO.getCheckTime())) {
                String submitTime = DateUtil.formatDateTime(checkVO.getCheckTime());
                applyEnterMainInfoVO.setSubmitTime(submitTime);
            }
        }
        return applyEnterMainInfoVO;
    }

    /**
     * 运营部审核使用
     *
     * @param checkResultEnum 审核结果枚举
     * @param riskName        运营部审核人
     * @param remark          审核意见
     * @param auditType       审核类型. 1. 首付款, 2. 云资源
     * @return ProjProjectSettlementCheckSaveDTO
     */
    public ProjProjectSettlementCheckSaveDTO getRiskDTO(Long projectInfoId, CheckResultEnum checkResultEnum, String riskName, String remark, Integer auditType) {
        if (auditType == NumberEnum.NO_1.num().intValue()) {
            if (checkResultEnum == CheckResultEnum.AUDIT_FAIL) {
                remark = RISK_REJECTED_CONTENT;
            } else if (checkResultEnum == CheckResultEnum.AUDIT_PASS) {
                remark = StrUtil.isBlank(remark) ? RISK_AUDIT_CONTENT : remark;
            } else {
                throw new RuntimeException("未查询到审核结果类型. checkResultEnum: " + checkResultEnum);
            }
        } else if (auditType == NumberEnum.NO_2.num().intValue()) {
            if (checkResultEnum == CheckResultEnum.AUDIT_PASS) {
                remark = StrUtil.isBlank(remark) ? RISK_AUDIT_CLOUD_CONTENT : remark;
            }
        } else {
            throw new RuntimeException("未查询到审核类型. checkResultEnum: " + checkResultEnum);
        }
        ProjProjectSettlementCheckSaveDTO checkSaveDTO = new ProjProjectSettlementCheckSaveDTO();
        checkSaveDTO.setCheckContent(remark);
        checkSaveDTO.setCheckNode(CheckNodeEnum.RISK_AUDIT.getCode());
        checkSaveDTO.setCheckResult(checkResultEnum.getCode());
        checkSaveDTO.setProjectInfoId(StrUtil.toString(projectInfoId));
        List<SysUser> sysUsers = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_name", riskName));
        if (CollUtil.isEmpty(sysUsers)) {
            checkSaveDTO.setUserId("-1");
        } else {
            checkSaveDTO.setUserId(StrUtil.toString(sysUsers.get(0).getSysUserId()));
        }
        return checkSaveDTO;
    }

    @Override
    public void insert(ProjProjectSettlementCheck settlementCheck, SettlementRuleCheckParam checkParam) {
        ProjProjectSettlementCheck copy = BeanUtil.copyProperties(settlementCheck, ProjProjectSettlementCheck.class);
        copy.setProjectSettlementCheckId(SnowFlakeUtil.getId());
        copy.setCheckNode(checkParam.getCheckNodeEnum().getCode());
        int count = settlementCheckMapper.insert(copy);
        log.info("入驻申请插入最终审核插入初始数据. count: {}", count);
        if (count <= 0) {
            throw new RuntimeException("入驻申请插入最终审核插入初始数据失败.");
        }
    }

    @Override
    public Result<String> syncYunAudit(SyncYunAuditDto dto) {
        log.info("syncYunAudit: {}", JSONObject.toJSONString(dto));
        // 判断是否有云资源工单
        List<ProjProjectOrderRelation> orderRelations = orderRelationMapper.selectList(new QueryWrapper<ProjProjectOrderRelation>().eq("yy_order_id", dto.getCloudProjId()));
        if (CollUtil.isEmpty(orderRelations)) {
            throw new RuntimeException("云资源工单. 请求参数: " + dto);
        }
        Long projectInfoId = orderRelations.get(0).getProjectInfoId();
        ProjOrderInfo softOrder = mainService.getSoftOrderInfo(projectInfoId);
        List<Long> projectInfoIds = getProjectInfoIds(CollUtil.newArrayList(softOrder));
        if (CollUtil.isEmpty(projectInfoIds)) {
            throw new RuntimeException("未查询到需要审核的项目. orderInfo: " + softOrder);
        }
        // - 首付款通过则判断审核状态
        if (dto.getResult() == NumberEnum.NO_1.num().intValue()) {
            // 驳回
            ProjProjectSettlementCheckSaveDTO checkSaveDTO = getRiskDTO(projectInfoId, CheckResultEnum.AUDIT_FAIL, dto.getUserName(), dto.getRemark(), NumberEnum.NO_2.num());
            settlementCheckService.saveSettlement(checkSaveDTO);
            return Result.success();
        }
        // 查询oderinfoid
        ProjOrderInfo orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("yy_order_id", dto.getCloudProjId()));
        // 更新云资源开始结束时间
        List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("order_info_id", orderInfo.getOrderInfoId()).orderByDesc("create_time"));
        if (CollUtil.isEmpty(orderProducts)) {
            throw new RuntimeException("未查询到云资源工单产品.");
        }
        List<ProjCustomCloudService> cloudServices = cloudServiceMapper.selectList(new QueryWrapper<ProjCustomCloudService>().eq("yy_order_id", dto.getCloudProjId()));
        if (CollUtil.isNotEmpty(cloudServices)) {
            ProjCustomCloudService cloudService = cloudServices.get(0);
            ProjCustomCloudService copy = new ProjCustomCloudService();
            copy.setCustomCloudServiceId(cloudService.getCustomCloudServiceId());
            copy.setPlanStartTime(DateUtil.parse(dto.getDate() + " 00:00:00"));
            cloudServiceMapper.updateById(copy);
            log.info("运营部审核后. 云资源开通审核时间: {}", copy);
        }
        // 更新审核状态, 发送消息
        // - 更新时间
        ProjProjectSettlementCheckSaveDTO checkSaveDTO = getRiskDTO(projectInfoId, CheckResultEnum.AUDIT_PASS, dto.getUserName(), dto.getRemark(), NumberEnum.NO_2.num());
        settlementCheckService.saveSettlement(checkSaveDTO);
        return Result.success();
    }

    /**
     * 云资源发函到自主运维平台
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> syncYunLetterDate(SyncYunRenewDTO dto) {
        try {
            // 根据运营平台客户id查询交付客户id
            ProjCustomInfo customInfo = customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq("yy_customer_id", dto.getCustomerId()));
            Integer pemcusssolType = dto.getPemcusssolType();
            if (10 == pemcusssolType) {
                pemcusssolType = isSingleton(customInfo.getCustomInfoId());
                if (pemcusssolType == -1) {
                    log.error("派单校验失败：同意实施地客户下，云健康升级无法确定单体还是区域。");
                    return Result.fail("同意实施地客户下，云健康升级无法确定单体还是区域。");
                }
            }
            // 查询该客户下的医院信息
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", customInfo.getCustomInfoId()));
            if (CollectionUtil.isEmpty(hospitalInfoList)) {
                return Result.fail("未查询到客户下的医院信息");
            }
            ProjCustomCloudService cloudService = cloudServiceMapper.getCustomCloudServiceByyyCustomerIdSolutionType(dto.getCustomerId(), pemcusssolType);
            SyncCloudTimeReq syncCloudTimeReq = new SyncCloudTimeReq(1, pemcusssolType);
            syncCloudTimeReq.setSendLetterTime(dto.getSendLetterDate());
            List<SyncCloudTimeHospitalReq> hospitals = new ArrayList<>();
            for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                SyncCloudTimeHospitalReq syncCloudTimeHospitalReq = new SyncCloudTimeHospitalReq();
                if (ObjectUtil.isEmpty(hospitalInfo.getCloudHospitalId())) {
                    continue;
                }
                syncCloudTimeHospitalReq.setHospitalId(hospitalInfo.getCloudHospitalId());
                getHousehold(syncCloudTimeHospitalReq, cloudService);
                syncCloudTimeHospitalReq.setMainRemindFlag(dto.getMainRemindFlag());
                hospitals.add(syncCloudTimeHospitalReq);
            }
            syncCloudTimeReq.setHospitals(hospitals);
            //发函时间赋值
            ProjCustomCloudService pjCustomCloudService = new ProjCustomCloudService();
            pjCustomCloudService.setCustomCloudServiceId(cloudService.getCustomCloudServiceId());
            pjCustomCloudService.setSendLetterDate(DateUtils.dateStrToDate(dto.getSendLetterDate()));
            log.info("删除发函时间pjCustomCloudService={}", pjCustomCloudService);
            int updates = cloudServiceMapper.updateById(pjCustomCloudService);
            log.info("updates={}", updates);
            //医院赋值
            log.info("云资源发函到自主运维平台. 请求参数: {}", JSONObject.toJSONString(syncCloudTimeReq));
            String token = null;
            try {
                //获取Token
                Result<YunweiLoginResp> operationPlatformToken = yunWeiPlatFormService.getOperationPlatformToken();
                token = operationPlatformToken.getData().getBody().getToken();
                log.info("获取运维平台Token:{}", token);
                ResponseData<String> stringResponseData = yunweiFeignClient.syncCloudTime(token, syncCloudTimeReq);
                log.info("调用运维平台返回结果:{}", stringResponseData);
                if (ObjectUtil.isEmpty(stringResponseData) || !stringResponseData.getSuccess()) {
                    log.info("同步运维平台信息失败: 参数{}", JSON.toJSONString(syncCloudTimeReq));
                    // 增加日志
                    saveSyncLogs("云资源发函失败", "调用运维平台返回结果:【】" + JSONUtil.toJsonStr(stringResponseData), customInfo.getCustomName());
                    return Result.fail("发函失败," + syncCloudTimeReq);
                }
                // 增加日志
                saveSyncLogs("云资源发函成功", "调用运维平台返回结果:【】" + JSONUtil.toJsonStr(stringResponseData), customInfo.getCustomName());
                return Result.success("发函成功");
            } catch (Exception e) {
                log.error("发函失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                return Result.fail("发函失败," + e.getMessage());
            }
        } catch (Exception e) {
            log.error("发函失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("发函失败," + e.getMessage());
        }

    }

    public void getHousehold(SyncCloudTimeHospitalReq syncCloudTimeHospitalReq, ProjCustomCloudService cloudService) {
        if (cloudService != null) {
            if (ObjectUtil.isEmpty(cloudService.getPlanStartTime()) && ObjectUtil.isNotEmpty(cloudService.getSubscribeStartTime())) {
                cloudService.setPlanStartTime(cloudService.getSubscribeStartTime());
            }
            if (ObjectUtil.isEmpty(cloudService.getPlanStartTime())) {
                syncCloudTimeHospitalReq.setStartTime(DateUtil.format(cloudService.getPlanStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            if (ObjectUtil.isNotEmpty(cloudService.getPlanStartTime())) {
                syncCloudTimeHospitalReq.setStartTime(DateUtil.format(cloudService.getPlanStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            if (ObjectUtil.isNotEmpty(cloudService.getSubscribeEndTime())) {
                syncCloudTimeHospitalReq.setEndTime(DateUtil.format(cloudService.getSubscribeEndTime(), DatePattern.NORM_DATETIME_PATTERN));
            } else {
                syncCloudTimeHospitalReq.setEndTime(DateUtil.format(org.apache.commons.lang.time.DateUtils.addMonths(cloudService.getPlanStartTime(), cloudService.getServiceSubscribeTerm()), DatePattern.NORM_DATETIME_PATTERN));
            }
        }
    }

    /**
     * 储存日志信息
     *
     * @param actionName
     * @param datas
     * @param customName
     */
    void saveSyncLogs(String actionName, String datas, String customName) {
        ProjSyncApiLogs projSyncApiLogs = new ProjSyncApiLogs();
        projSyncApiLogs.setId(SnowFlakeUtil.getId());
        projSyncApiLogs.setActionname(actionName);
        projSyncApiLogs.setDatas(datas);
        projSyncApiLogs.setCustomername(customName);
        projSyncApiLogsMapper.insert(projSyncApiLogs);
    }

    /**
     * 云资源延期业务处理
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> syncYunRenew(SyncYunRenewDTO dto) {
        try {
            //  proj_custom_cloud_service  云服务订阅到期时间后 ，延期
            // 根据运营平台客户id查询交付客户id
            ProjCustomInfo customInfo = customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq("yy_customer_id", dto.getCustomerId()));
            Integer pemcusssolType = dto.getPemcusssolType();
            if (10 == pemcusssolType) {
                pemcusssolType = isSingleton(customInfo.getCustomInfoId());
                if (pemcusssolType == -1) {
                    log.error("派单校验失败：同意实施地客户下，云健康升级无法确定单体还是区域。");
                    return Result.fail("同意实施地客户下，云健康升级无法确定单体还是区域。");
                }
            }
            log.info("云资源迁回功能,{}", JSONUtil.toJsonStr(customInfo));
            // 查询到期时间
            List<ProjCustomCloudService> projCustomCloudServices = cloudServiceMapper.selectList(new QueryWrapper<ProjCustomCloudService>().eq("custom_info_id", customInfo.getCustomInfoId()).eq("solution_type", pemcusssolType).orderByDesc("create_time"));
            if (CollectionUtil.isEmpty(projCustomCloudServices)) {
                return Result.fail("未查询到客户云服务信息");
            }
            // 一对多关系 ，根据创建时间排序  获取第一条数据
            ProjCustomCloudService projCustomCloudService = projCustomCloudServices.get(0);
            // 重新设置 云资源订阅的 开始时间与结束时间
            String startTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", DateUtils.addDays(projCustomCloudService.getSubscribeEndTime(), 1));
            log.info("云资源开始时间： " + startTime);
            String endTime = null;
            if (dto.getLen() > 0) {
                Date eDate = DateUtils.addDays(projCustomCloudService.getSubscribeEndTime(), dto.getLen());
                endTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", eDate);
            }
            log.info("云资源结束时间： " + endTime);
            SyncCloudTimeReq req = new SyncCloudTimeReq(1, pemcusssolType);
            // 查询该客户下的医院信息
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", customInfo.getCustomInfoId()));
            if (CollectionUtil.isEmpty(hospitalInfoList)) {
                return Result.fail("未查询到客户下的医院信息");
            }
            List<SyncCloudTimeHospitalReq> hospitals = new ArrayList<>();
            for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                SyncCloudTimeHospitalReq syncCloudTimeHospitalReq = new SyncCloudTimeHospitalReq();
                if (ObjectUtil.isEmpty(hospitalInfo.getCloudHospitalId())) {
                    continue;
                }
                syncCloudTimeHospitalReq.setHospitalId(hospitalInfo.getCloudHospitalId());
                syncCloudTimeHospitalReq.setStartTime(startTime);
                syncCloudTimeHospitalReq.setEndTime(endTime);
                syncCloudTimeHospitalReq.setMainRemindFlag(dto.getMainRemindFlag());
                hospitals.add(syncCloudTimeHospitalReq);
            }
            //删除发函时间
            ProjCustomCloudService pjCustomCloudService = new ProjCustomCloudService();
            pjCustomCloudService.setCustomCloudServiceId(projCustomCloudService.getCustomCloudServiceId());
            pjCustomCloudService.setSendLetterDate(null);
            log.info("删除发函时间pjCustomCloudService={}", pjCustomCloudService);
            int updates = cloudServiceMapper.updateByPrimaryKey(pjCustomCloudService);
            log.info("updates={}", updates);
            //医院赋值
            req.setHospitals(hospitals);
            req.setCloudFeedback(dto.getCloudFeedback());
            log.info("云资源延期到自主运维平台. 请求参数: {}", JSONObject.toJSONString(req));
            try {
                //获取Token
                Result<YunweiLoginResp> operationPlatformToken = yunWeiPlatFormService.getOperationPlatformToken();
                String token = operationPlatformToken.getData().getBody().getToken();
                log.info("获取运维平台Token:{}", token);
                sysOperLogService.apiOperLogInsertObjAry("云资源续签-入参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(), token, req);
                ResponseData<String> stringResponseData = yunweiFeignClient.syncCloudTime(token, req);
                log.info("调用运维平台返回结果:{}", stringResponseData);
                sysOperLogService.apiOperLogInsertObjAry("云资源续签-返回值", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(), stringResponseData);
                if (ObjectUtil.isEmpty(stringResponseData) || !stringResponseData.getSuccess()) {
                    // 增加日志
                    log.info("同步运维平台信息失败: 参数{}", JSON.toJSONString(req));
                }
                return Result.success("云资源签回成功");
            } catch (Exception e) {
                log.error("云资源签回失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                sysOperLogService.apiOperLogInsertObjAry("云资源续签-异常", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(), e.getMessage());
                return Result.fail("云资源延期失败," + e.getMessage());
            }
        } catch (Exception e) {
            log.error("云资源签回失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("云资源延期失败," + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<String> syncMidOrderAudit(SyncMidOrderAuditDTO dto) {
        // 查询项目
        ProjProjectInfo projectInfo = commonService.getProjectInfoByYyOrderId(dto.getYyOrderId());
        if (ObjectUtil.isEmpty(projectInfo)) {
            log.error("未查询到项目. 免中间件服务回调. detail: {} ", JSONUtil.toJsonStr(dto));
            throw new CustomException("未查询到项目");
        }
        if (!isCurrentAuditNode(projectInfo.getProjectInfoId())) {
            throw new CustomException("免中间件服务审核回调, 未匹配到项目");
        }
        String checkContent;
        CheckResultEnum checkResultEnum;
        // 判断是否通过审核
        String title = RISK_MID_ORDER_AUDIT_TITLE + StrUtil.COMMA + StrUtil.SPACE;
        if (dto.getCode() == CheckResultEnum.AUDIT_FAIL.getCode()) {
            checkResultEnum = CheckResultEnum.AUDIT_FAIL;
            checkContent = title + checkResultEnum.getDesc();
        } else {
            checkResultEnum = CheckResultEnum.AUDIT_PASS;
            checkContent = title + checkResultEnum.getDesc();
        }
        ProjProjectSettlementCheckSaveDTO checkSaveDTO = new ProjProjectSettlementCheckSaveDTO();
        checkSaveDTO.setCheckContent(checkContent);
        checkSaveDTO.setCheckNode(CheckNodeEnum.RISK_AUDIT.getCode());
        checkSaveDTO.setCheckResult(checkResultEnum.getCode());
        checkSaveDTO.setProjectInfoId(StrUtil.toString(projectInfo.getProjectInfoId()));
        SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("account", dto.getAccount()));
        if (ObjectUtil.isEmpty(sysUser)) {
            checkSaveDTO.setUserId("-1");
        } else {
            checkSaveDTO.setUserId(StrUtil.toString(sysUser.getSysUserId()));
        }
        checkSaveDTO.setProjectInfoId(StrUtil.toString(projectInfo.getProjectInfoId()));
        // 更新审核结果
        settlementMidOrderService.updateMidOrder(checkSaveDTO.getCheckContent(), Long.parseLong(checkSaveDTO.getUserId()), checkSaveDTO.getCheckResult(), projectInfo.getProjectInfoId());
        // 若审核通过, 判断是否需要审核首付款
        if (checkResultEnum.getCode() == CheckResultEnum.AUDIT_PASS.getCode()) {
            // 运营审核首付款
            if (settlementRuleService.needPaySignage(projectInfo.getProjectInfoId())) {
                insertSettlementLog(checkSaveDTO, projectInfo.getProjectInfoId(), checkSaveDTO.getCheckContent() + "," + " 待审核首付款");
                settlementCheckSaleService.sendRiskMessage(projectInfo, false);
                return Result.success();
            }
        }
        settlementCheckService.saveSettlement(checkSaveDTO);
        return Result.success();
    }

    public Integer isSingleton(Long customInfoId) {
        log.info("客户id，customInfoId={}", customInfoId);
        List<ProjProjectInfo> pjProjectInfo = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq("custom_info_id", customInfoId).eq("is_deleted", 0));
        if (CollUtil.isEmpty(pjProjectInfo)) {
            log.info("未查询到项目信息");
            return -1;
        }
        boolean projectType1 = pjProjectInfo.stream().anyMatch(p -> 1 == p.getProjectType());
        boolean projectType2 = pjProjectInfo.stream().anyMatch(p -> 2 == p.getProjectType());
        if (projectType1 && projectType2) {
            log.info("无法确定单体还是区域={}", pjProjectInfo);
            return -1;
        }
        if (projectType1) {
            log.info("单体项目={}", pjProjectInfo);
            return 1;
        } else if (projectType2) {
            log.info("区域项目={}", pjProjectInfo);
            return 2;
        } else {
            log.info("无法确定单体还是区域={}", pjProjectInfo);
            return -1;
        }
    }

    @Override
    public Result<ProjProjectSettlementCheckProcessVO> getSettlementCheckProcess(ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        Long projectInfoId = Long.parseLong(checkApplyEnterDTO.getProjectInfoId());
        ProjProjectSettlementCheckProcessVO processVO = new ProjProjectSettlementCheckProcessVO();
        List<ProjProjectSettlementCheck> settlementChecks = settlementRuleService.findSettlementCheck(projectInfoId);
        List<ProjProjectSettlementCheckVO> settlementCheckVOS = settlementCheckConvert.po2Vo(settlementChecks);
        settlementCheckVOS.forEach(e -> {
            Integer checkNode = e.getCheckNode();
            try {
                // 设置节点名称
                CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumByCode(checkNode);
                String checkNodeName;
                if (ObjectUtil.isEmpty(checkNodeEnum)) {
                    checkNodeName = StrUtil.EMPTY;
                } else {
                    assert checkNodeEnum != null;
                    checkNodeName = checkNodeEnum.getDesc();
                }
                e.setCheckNodeName(checkNodeName);
                if (CheckNodeEnum.SUBMIT_ENTRY.getCode() == checkNode) {
                    Long sysUserId = mainService.getProjectMgrPersonId(projectInfoId);
                    e.setCheckUserName(sysUserMapper.getUserById(sysUserId).getUserName());
                } else if (CheckNodeEnum.SALE_APPLY_ENTRY.getCode() == checkNode) {
                    e.setCheckUserName(commonService.getContractCustomSalePerson(projectInfoId).getUserName());
                } else if (CheckNodeEnum.BRANCH_MANAGER_AUDIT.getCode() == checkNode) {
                    String userYunyingId = mainService.getBranchMgrLeaderYunyingId(mainService.getProjectInfo(projectInfoId).getOrderInfoId());
                    List<SysUser> sysUsers = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_yunying_id", userYunyingId));
                    if (CollUtil.isEmpty(sysUsers)) {
                        log.error("未查询到分公司审核人员信息. projectInfoId: {}", projectInfoId);
                    } else {
                        e.setCheckUserName(sysUsers.get(0).getUserName());
                    }
                } else if (CheckNodeEnum.RISK_AUDIT.getCode() == checkNode) {
                    // todo
//                    SETTLEMENT_RISK_AUDIT
                    List<ConfigSendMessage> sendMessages = configSendMessageMapper.selectList(new QueryWrapper<ConfigSendMessage>().eq("message_type_id", DictMessageTypeEnum.SETTLEMENT_RISK_AUDIT.getId()).eq("message_to_category", MsgToCategory.SINGLE_PERSON.getCode()));
                    if (CollUtil.isNotEmpty(sendMessages)) {
                        ConfigSendMessage configSendMessage = sendMessages.get(0);
                        if (ObjectUtil.isNotEmpty(configSendMessage.getMessageToId())) {
                            SysUser sysUser = sysUserMapper.selectById(configSendMessage.getMessageToId());
                            e.setCheckUserName(ObjectUtil.isEmpty(sysUser) ? StrUtil.EMPTY : sysUser.getUserName());
                        } else {
                            e.setCheckUserName(StrUtil.EMPTY);
                        }
                    } else {
                        e.setCheckUserName(StrUtil.EMPTY);
                    }
                } else if (CheckNodeEnum.ENSURE_SETTLE_IN.getCode() == checkNode) {
                    Long userId = mainService.getProjectMgrPersonId(projectInfoId);
                    e.setCheckUserName(sysUserMapper.getUserById(userId).getUserName());
                }
            } catch (Throwable ignore) {
                log.error("未查询到审核人员信息. 当前节点: " + checkNode + ", projectInfoId: " + projectInfoId);
            }
        });
        // 计算已通过节点
        List<ProjProjectSettlement> settlements = settlementMapper.selectList(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        if (CollUtil.isNotEmpty(settlements)) {
            ProjProjectSettlement settlement = settlements.get(0);
            CheckNodeEnum checkNodeEnum = CheckNodeEnum.getCheckNodeEnumBySettlementStatusCode(settlement.getSettlementStatus());
            SettlementStatusEnum settlementStatusEnum = SettlementStatusEnum.getSettlementStatusByCode(settlement.getSettlementStatus());
            assert settlementStatusEnum != null;
            if (settlementStatusEnum.getCheckResultEnum().getCode() == CheckResultEnum.AUDIT_FAIL.getCode()) {
                processVO.setThroughStep(NumberEnum.NO_0.num());
            } else {
                int currentCheckNode = checkNodeEnum.getCode();
                if (currentCheckNode == NextCheckNodeUnNormalEnum.NOT_EXIST_NEXT_NODE.getCode()) {
                    processVO.setThroughStep(0);
                } else if (currentCheckNode == NextCheckNodeUnNormalEnum.HAS_SETTLEMENT_NEXT_NODE.getCode()) {
                    processVO.setThroughStep(settlementCheckVOS.size());
                } else {
                    for (int i = 0; i < settlementCheckVOS.size(); i++) {
                        ProjProjectSettlementCheckVO check = settlementCheckVOS.get(i);
                        if (currentCheckNode == check.getCheckNode()) {
                            processVO.setThroughStep(i + 1);
                            break;
                        }
                    }
                }
            }
        } else {
            processVO.setThroughStep(NumberEnum.NO_0.num());
        }
        processVO.setSettlementCheckVOList(settlementCheckVOS);
        return Result.success(processVO);
    }

    @Override
    public Result<String> settlementUrging(ProjProjectSettlementCheckApplyEnterDTO checkApplyEnterDTO) {
        Long projectInfoId = Long.parseLong(checkApplyEnterDTO.getProjectInfoId());
        ProjProjectSettlement settlement = settlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        if (ObjectUtil.isEmpty(settlement) || settlement.getSettlementStatus() == -1) {
            // 默认向向销售推送消息
            String msg = ", " + CheckNodeEnum.SUBMIT_ENTRY.getDesc() + ", 请您知晓!";
            // 向项目经理推送消息
            settlementCheckSaleService.sendMessageToProjMgr(projectInfoId, msg);
            // 向销售发消息
            settlementCheckSaleService.sendMessageToSalePerson(projectInfoId, msg);
            return Result.success();
        }
        int settlementStatus = settlement.getSettlementStatus();
        int currentCheckNode = Objects.requireNonNull(CheckNodeEnum.getCheckNodeEnumBySettlementStatusCode(settlementStatus)).getCode();
        int checkResult = ObjectUtil.isEmpty(settlementStatus) ? CheckResultEnum.AUDIT_PASS.getCode() : Objects.requireNonNull(SettlementStatusEnum.getSettlementStatusByCode(settlementStatus)).getCheckResultEnum().getCode();
        sendMessage(projectInfoId, currentCheckNode, checkResult, true);
        return Result.success();
    }

    /**
     * 向特定角色发送消息
     *
     * @param projectInfoId
     * @param messageTypeId
     * @param content
     */
    @Override
    public void sendToRoleMessage(Long projectInfoId, Long messageTypeId, String content) {
        //给pmo发消息
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(projectInfoId);
        messageParam.setContent(content);
        messageParam.setMessageTypeId(messageTypeId);
        messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
        sendMessageService.sendMessage(messageParam);
    }

    /**
     * 运营部审核异常记录
     */
    @Data
    @Builder
    public static class SyncRiskMsgErrorRecord {
        Result<String> result;

        SyncRiskAuditDTO syncRiskAuditDTO;

        Throwable error;
    }

}
