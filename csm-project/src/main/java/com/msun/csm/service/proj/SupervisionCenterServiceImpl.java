package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.enums.FormClassEnum;
import com.msun.csm.common.enums.issue.IssueOperEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.enums.tduck.FormStatusEnum;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigIssueClassification;
import com.msun.csm.dao.entity.dict.DictAcceptanceClassification;
import com.msun.csm.dao.entity.dict.DictDeductionType;
import com.msun.csm.dao.entity.dict.DictServerType;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailInfoVO;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailVO;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailVO2;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionRecordVO;
import com.msun.csm.dao.entity.proj.CloudUserAccount;
import com.msun.csm.dao.entity.proj.DeductionClassificationVO;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecord;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecordVO;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUser;
import com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUserVO;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailCommon;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailDocument;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailInfo;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailProcess;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjIssueInfo;
import com.msun.csm.dao.entity.proj.ProjIssueOperLog;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProjectAcceptance;
import com.msun.csm.dao.entity.proj.ProjProjectClassificationScorePO;
import com.msun.csm.dao.entity.proj.ProjProjectDeductionDetail;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.QueryServerTeamDeductionRecordForBackendTeamParam;
import com.msun.csm.dao.entity.proj.ServerTeamDeductionRecord;
import com.msun.csm.dao.entity.proj.ServerTeamDeductionRecordForBackendTeamVO;
import com.msun.csm.dao.entity.proj.UpdateServerTeamDeductionRecordParam;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.tduck.FmUserForm;
import com.msun.csm.dao.mapper.config.ConfigIssueClassificationMapper;
import com.msun.csm.dao.mapper.dict.DictAcceptanceClassificationMapper;
import com.msun.csm.dao.mapper.dict.DictDeductionTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProjectAcceptRuleMapper;
import com.msun.csm.dao.mapper.dict.DictServerTypeMapper;
import com.msun.csm.dao.mapper.proj.ProductSatisfactionSurveyRecordMapper;
import com.msun.csm.dao.mapper.proj.ProductSatisfactionSurveyUserMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjDeductionDetailCommonMapper;
import com.msun.csm.dao.mapper.proj.ProjDeductionDetailInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjIssueInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjIssueOperLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectClassificationScoreMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectDeductionDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.ServerTeamDeductionRecordMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.entity.yunying.req.RewardInfo;
import com.msun.csm.model.DictDeductionTypeVO;
import com.msun.csm.model.param.BackendOperationParam;
import com.msun.csm.model.param.BackendOperationParam2;
import com.msun.csm.model.param.ChangeDeductionParam;
import com.msun.csm.model.param.DeductionDetailIdParam;
import com.msun.csm.model.param.GetDeductionClassificationParam;
import com.msun.csm.model.param.GetIssueClassificationParam;
import com.msun.csm.model.param.GetProjBackendScoreParam;
import com.msun.csm.model.param.QueryBackendDeductionRecordParam;
import com.msun.csm.model.param.QueryDeductionClassificationDictParam;
import com.msun.csm.model.param.QueryServerTeamDeductionRecordParam;
import com.msun.csm.model.param.RevertConfirmationParam;
import com.msun.csm.model.param.SaveCommonDeductionParam;
import com.msun.csm.model.param.SaveDocumentDeductionParam;
import com.msun.csm.model.param.SaveProcessDeductionParam;
import com.msun.csm.model.param.SendConfirmationParam;
import com.msun.csm.model.param.TestParam1;
import com.msun.csm.model.param.TestParam10;
import com.msun.csm.model.param.TestParam9;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.issue.SaveDeductionClassificationDictType;
import com.msun.csm.model.req.issue.SaveIssueReq;
import com.msun.csm.model.resp.issue.IssueDataResp;
import com.msun.csm.model.resp.issue.IssueDataResp2;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq2;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.tduck.FmUserFormService;
import com.msun.csm.service.tduck.TduckService;
import com.msun.csm.service.yunying.YunYingService;
import com.msun.csm.util.SnowFlakeUtil;

@Slf4j
@Service
public class SupervisionCenterServiceImpl implements SupervisionCenterService {

    //未分配状态
    private static final Long UN_ASSIGNED = 1L;

    //已分配状态
    private static final Long ASSIGNED = 2L;

    @Value("${tduck.page}")
    private String tduckDomain;

    @Resource
    private DictAcceptanceClassificationMapper dictAcceptanceClassificationMapper;

    @Resource
    private ConfigIssueClassificationMapper configIssueClassificationMapper;

    @Resource
    private ProjIssueInfoMapper projIssueInfoMapper;

    @Resource
    private DictDeductionTypeMapper dictDeductionTypeMapper;

    @Resource
    private DictServerTypeMapper dictServerTypeMapper;

    @Resource
    private ProjIssueInfoService issueInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ImplementApplicationService implementApplicationService;

    @Resource
    private ProjProjectDeductionDetailMapper projProjectDeductionDetailMapper;

    @Resource
    private ProjDeductionDetailCommonMapper projDeductionDetailCommonMapper;

    @Resource
    private ProjIssueOperLogMapper issueOperLogMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ProjDeductionDetailInfoMapper projDeductionDetailInfoMapper;

    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    @Resource
    private ServerTeamDeductionRecordMapper serverTeamDeductionRecordMapper;

    @Resource
    private ProjProjectAcceptanceMapper projProjectAcceptanceMapper;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ServerTeamScoreLogService serverTeamScoreLogService;

    @Resource
    private ProjProjectClassificationScoreMapper projProjectClassificationScoreMapper;

    @Resource
    private DictProjectAcceptRuleMapper dictProjectAcceptRuleMapper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private CommonService commonService;

    @Resource
    private YunYingService yunYingService;

    @Resource
    private ProjProjectAcceptanceService projProjectAcceptanceService;
    @Autowired
    private DictProductService dictProductService;
    @Autowired
    private ProjHospitalInfoService projHospitalInfoService;
    @Autowired
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Override
    public List<BaseCodeNameResp> getAcceptanceClassification() {
        List<DictAcceptanceClassification> dictAcceptanceClassifications = dictAcceptanceClassificationMapper.selectList(
                new QueryWrapper<DictAcceptanceClassification>()
                        .eq("is_deleted", 0)
                        .orderByAsc("sort_no")
        );
        return dictAcceptanceClassifications.stream().map(item -> new BaseCodeNameResp(item.getAcceptanceClassificationCode(), item.getAcceptanceClassificationName())).collect(Collectors.toList());
    }

    @Override
    public List<BaseCodeNameResp> getIssueClassification(GetIssueClassificationParam param) {
        ConfigIssueClassification configIssueClassification = new ConfigIssueClassification();
        configIssueClassification.setPlanItemCode(param.getPlanItemCode());
        configIssueClassification.setAcceptanceClassificationCode(param.getAcceptanceClassificationCode());
        List<ConfigIssueClassification> classificationConfigList = configIssueClassificationMapper.getConfigIssueClassification(configIssueClassification);
        List<BaseCodeNameResp> classificationRespList = classificationConfigList.stream().map(item -> {
            BaseCodeNameResp baseIdNameResp = new BaseCodeNameResp();
            baseIdNameResp.setId(String.valueOf(item.getId()));
            baseIdNameResp.setName(item.getName());
            return baseIdNameResp;
        }).collect(Collectors.toList());
        // 项目计划编码为空时，还走原来的逻辑
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getPlanItemCode())) {
            return classificationRespList;
        } else {
            //查询项目自定义的分类
            List<String> classificationNameList = projIssueInfoMapper.getClassificationList(param.getProjectInfoId());
            if (!CollectionUtils.isEmpty(classificationNameList)) {
                List<Long> collect = classificationRespList.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
                long lastId = collect.stream().max(Long::compareTo).orElse(1L);
                for (int i = 0; i < classificationNameList.size(); i++) {
                    BaseCodeNameResp baseIdNameResp = new BaseCodeNameResp();
                    baseIdNameResp.setId(String.valueOf(lastId + i + 1));
                    baseIdNameResp.setName(classificationNameList.get(i));
                    classificationRespList.add(baseIdNameResp);
                }
            }
            return classificationRespList;
        }
    }

    @Override
    public List<DeductionClassificationVO> getDeductionClassification(GetDeductionClassificationParam param) {
        List<DictDeductionType> deductionTypeByIssueClassification = dictDeductionTypeMapper.getDeductionTypeByIssueClassification(param.getIssueClassificationId());
        return deductionTypeByIssueClassification.stream().map(item -> new DeductionClassificationVO(item.getCode(), item.getName(), item.getDefaultScore() == null ? null : item.getDefaultScore().toPlainString())).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean saveIssue(SaveIssueReq req) {
        ProjProjectAcceptance projProjectAcceptance = projProjectAcceptanceMapper.selectByProjectInfoIdOne(req.getProjectInfoId());

        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectByPrimaryKey(req.getProjectInfoId());

        ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());
        if (null == projProjectAcceptance) {
            throw new IllegalArgumentException(String.format("当前项目尚未申请验收，客户名称=%s，项目交付工单号=%s", projCustomInfo.getCustomName(), projProjectInfo.getProjectNumber()));
        }
        if (Integer.valueOf(1).equals(projProjectAcceptance.getAcceptanceStatus()) || Integer.valueOf(5).equals(projProjectAcceptance.getAcceptanceStatus())) {
            throw new IllegalArgumentException(String.format("质管中心尚未接收当前项目的验收申请，请先接收验收申请后再记录问题。客户名称=%s，项目交付工单号=%s", projCustomInfo.getCustomName(), projProjectInfo.getProjectNumber()));
        }
        //  通过项目计划添加问题时传的是问题分类的描述（classification），监管中心添加问题时传的是问题分类的ID（issueClassificationId），此时问题分类的描述为空，需要根据问题分类的ID查询问题分类的描述
        if (req.getIssueClassificationId() != null && StringUtils.isBlank(req.getClassification())) {
            ConfigIssueClassification configIssueClassification = configIssueClassificationMapper.selectByPrimaryKey(req.getIssueClassificationId());
            if (configIssueClassification != null) {
                req.setClassification(configIssueClassification.getName());
            }
        }
        // 监管中心质管人员添加问题
        if ("supervisionCenter".equals(req.getOperationSource())) {
            // 扣分分值不为0
            ProjIssueInfo saveDeductionScoreResult = null;
            if (StringUtils.isNotBlank(req.getDeductionScore())) {
                if (req.getId() != null) {
                    ProjIssueInfo currentIssueInfo = projIssueInfoMapper.selectByPrimaryKey(req.getId());
                    // 修改问题时先同步删除原来的扣分记录
                    projDeductionDetailCommonMapper.updateStatusToDeletedById(currentIssueInfo.getBusinessId());
                }
                saveDeductionScoreResult = saveDeductionScore(req);
            }
            SysUserVO user = userHelper.getCurrentUser();
            Date now = new Date();
            ProjIssueInfo issueInfo = new ProjIssueInfo();
            BeanUtil.copyProperties(req, issueInfo);
            issueInfo.setUpdateTime(now);
            issueInfo.setUpdaterId(user.getSysUserId());
            if (req.getClassification() != null) {
                issueInfo.setClassification(req.getClassification().replace(StrUtil.SPACE, StrUtil.EMPTY));
            }
            if (saveDeductionScoreResult != null) {
                issueInfo.setBusinessId(saveDeductionScoreResult.getBusinessId());
            }
            int operType = -1;
            if (req.getId() != null) {
                log.info("质管中心修改问题");
                ProjIssueInfo currentIssueInfo = projIssueInfoMapper.selectByPrimaryKey(req.getId());
                if (issueInfo.getChargePerson() != null) {
                    if (currentIssueInfo.getChargePerson() == null || !issueInfo.getChargePerson().equals(currentIssueInfo.getChargePerson())) {
                        operType = IssueOperEnums.ASSIGN_CHARGE_PERSON.getCode();
                    }
                } else if (issueInfo.getDescription() != null) {
                    if (currentIssueInfo.getDescription() == null || !issueInfo.getDescription().equals(currentIssueInfo.getDescription())) {
                        operType = IssueOperEnums.MODIFY_ISSUE_DESC.getCode();
                    }
                } else if (issueInfo.getStatus() != null) {
                    if (currentIssueInfo.getStatus() == null || !issueInfo.getStatus().equals(currentIssueInfo.getStatus())) {
                        operType = IssueOperEnums.MODIFY_ISSUE_STATUS.getCode();
                    }
                }
                //分配责任人，状态自动变化
                if (operType == IssueOperEnums.ASSIGN_CHARGE_PERSON.getCode()) {
                    issueInfo.setStatus(ASSIGNED);
                }
                projIssueInfoMapper.updateByPrimaryKeySelective(issueInfo);
            } else {
                log.info("质管中心新增问题");
                if (StringUtils.isBlank(req.getDescription())) {
                    throw new IllegalArgumentException("没有填写问题描述");
                }
                //状态，如果分配了责任人，状态为已分配，否则为未分配
                if (req.getChargePerson() != null) {
                    issueInfo.setStatus(ASSIGNED);
                } else {
                    issueInfo.setStatus(UN_ASSIGNED);
                }
                issueInfo.setCreaterId(user.getSysUserId());
                issueInfo.setCreateTime(now);
                issueInfo.setIsDeleted(0);
                issueInfo.setId(SnowFlakeUtil.getId());
                projIssueInfoMapper.insert(issueInfo);
                operType = IssueOperEnums.ADD.getCode();
            }
            //保存操作日志-需要判断更新字段，所以在数据修改前记录日志
            if (operType != -1) {
                saveLog(Collections.singletonList(issueInfo), operType);
            }
            return true;
        }
        // 不是监管中心的操作，调用原来的保存方法
        issueInfoService.saveIssue(req);
        return true;
    }

    private ProjIssueInfo saveDeductionScore(SaveIssueReq req) {
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectByPrimaryKey(req.getProjectInfoId());
        Integer currentAcceptanceTimes = ProjectDeliverStatusEnums.FIRST_ACCEPTED.getCode().equals(projProjectInfo.getProjectDeliverStatus()) ? 2 : 1;

        String source = projProjectAcceptanceService.getAcceptanceType(req.getProjectInfoId());

        // 线下培训、设备接口、数据统计
        if ("offline".equals(req.getAcceptanceClassificationCode()) || "equip".equals(req.getAcceptanceClassificationCode()) || "dataStatistics".equals(req.getAcceptanceClassificationCode())) {
            String menuCode;
            // 线下培训
            if ("offline".equals(req.getAcceptanceClassificationCode())) {
                menuCode = "RapidImplementationApplication";
            } else if ("equip".equals(req.getAcceptanceClassificationCode())) {
                // 设备接口对接
                menuCode = "EquipmentInterface";
            } else {
                // 数据统计
                menuCode = "DataStatistics";
            }
            SaveCommonDeductionParam saveCommonDeductionParam = new SaveCommonDeductionParam();
            saveCommonDeductionParam.setPracticalDeduction(req.getDeductionScore());
            saveCommonDeductionParam.setRemark(req.getDescription());
            saveCommonDeductionParam.setDeductionType(req.getDeductionTypeCode());
            // 通过添加问题没有附件
            saveCommonDeductionParam.setAttachmentInfoList(new ArrayList<>());
            saveCommonDeductionParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            saveCommonDeductionParam.setMenuCode(menuCode);
            saveCommonDeductionParam.setOnlyOneCheckFlag(req.getOnlyOneCheckFlag());
            // 如果项目状态是一次验收通过，再添加问题时认为是第二次验收的问题，其他情况认为是一次验收的问题
            saveCommonDeductionParam.setCurrentAcceptanceTimes(currentAcceptanceTimes);

            ProjDeductionDetailCommon projDeductionDetailCommon = implementApplicationService.saveCommonDeduction(saveCommonDeductionParam);
            ProjIssueInfo projIssueInfo = new ProjIssueInfo();
            projIssueInfo.setBusinessId(projDeductionDetailCommon.getProjDeductionDetailInfoId());
            return projIssueInfo;
        }
        // 项目过程
        if ("process".equals(req.getAcceptanceClassificationCode())) {
            SaveProcessDeductionParam saveProcessDeductionParam = new SaveProcessDeductionParam();
            saveProcessDeductionParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            saveProcessDeductionParam.setMenuCode("RapidImplementationApplication");
            saveProcessDeductionParam.setPracticalDeduction(req.getDeductionScore());
            saveProcessDeductionParam.setRemark(req.getDescription());
            saveProcessDeductionParam.setOnlyOneCheckFlag(req.getOnlyOneCheckFlag());
            saveProcessDeductionParam.setCurrentAcceptanceTimes(currentAcceptanceTimes);
            saveProcessDeductionParam.setYyProductId(req.getProductId());
            saveProcessDeductionParam.setDeductionType(req.getDeductionTypeCode());
            saveProcessDeductionParam.setAttachmentInfoList(new ArrayList<>());
            ProjDeductionDetailProcess projDeductionDetailProcess = implementApplicationService.saveProcessDeduction(saveProcessDeductionParam);
            ProjIssueInfo projIssueInfo = new ProjIssueInfo();
            projIssueInfo.setBusinessId(projDeductionDetailProcess.getProjDeductionDetailInfoId());
            return projIssueInfo;
        }
        // 项目文档扣分
        if ("document".equals(req.getAcceptanceClassificationCode())) {
            SaveDocumentDeductionParam saveDocumentDeductionParam = new SaveDocumentDeductionParam();
            saveDocumentDeductionParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            saveDocumentDeductionParam.setMenuCode("RapidImplementationApplication");
            saveDocumentDeductionParam.setPracticalDeduction(new BigDecimal(req.getDeductionScore()));
            saveDocumentDeductionParam.setRemark(req.getDescription());
            // FIXME 项目阶段获取不到
            saveDocumentDeductionParam.setProjectStageCode("none");
            // FIXME 里程碑节点获取不到
            saveDocumentDeductionParam.setMilestoneNodeCode("none");
            saveDocumentDeductionParam.setProjectFileId(null);
            saveDocumentDeductionParam.setOnlyOneCheckFlag(req.getOnlyOneCheckFlag());
            saveDocumentDeductionParam.setCurrentAcceptanceTimes(currentAcceptanceTimes);
            saveDocumentDeductionParam.setDeductionType(req.getDeductionTypeCode());
            ProjDeductionDetailDocument projDeductionDetailDocument = implementApplicationService.saveDocumentDeduction(saveDocumentDeductionParam);
            ProjIssueInfo projIssueInfo = new ProjIssueInfo();
            projIssueInfo.setBusinessId(projDeductionDetailDocument.getProjDeductionDetailInfoId());
            return projIssueInfo;
        }

        Date now = new Date();
        ProjProjectDeductionDetail projProjectDeductionDetail = new ProjProjectDeductionDetail();
        projProjectDeductionDetail.setProjDeductionDetailInfoId(SnowFlakeUtil.getId());
        projProjectDeductionDetail.setIsDeleted(0);
        projProjectDeductionDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setCreateTime(now);
        projProjectDeductionDetail.setUpdateTime(now);
//        projProjectDeductionDetail.setCustomInfoId(projProjectInfo.getCustomInfoId());
        projProjectDeductionDetail.setProjectInfoId(projProjectInfo.getProjectInfoId());
        projProjectDeductionDetail.setYyProductId(req.getProductId());
        // 获取不到
        projProjectDeductionDetail.setFunctionCode("none");
        // 实际扣分
        projProjectDeductionDetail.setPracticalDeduction(new BigDecimal(req.getDeductionScore()));
        projProjectDeductionDetail.setRemark(req.getDescription());
        projProjectDeductionDetail.setClassificationCode("ProductFunctionApplication");
        // 预估扣分
        projProjectDeductionDetail.setEstimatedDeduction(BigDecimal.ZERO);
        projProjectDeductionDetail.setSource(source);
        projProjectDeductionDetail.setUseCount(0);
        projProjectDeductionDetail.setDeductionType(req.getDeductionTypeCode());
        projProjectDeductionDetail.setOperationType("function");
        int insert = projProjectDeductionDetailMapper.insert(projProjectDeductionDetail);
        ProjIssueInfo projIssueInfo = new ProjIssueInfo();
        projIssueInfo.setBusinessId(projProjectDeductionDetail.getProjDeductionDetailInfoId());
        return projIssueInfo;
    }

    /**
     * @param issueInfoList
     * @Description: 保存操作日志
     */
    private void saveLog(List<ProjIssueInfo> issueInfoList, int operType) {
        //判断是新增、更新还是删除
        if (issueInfoList != null && !issueInfoList.isEmpty()) {
            SysUserVO user = userHelper.getCurrentUser();
            Date now = new Date();
            List<ProjIssueOperLog> logList = issueInfoList.stream()
                    .map(item -> getProjIssueOperLog(item, user, now, operType))
                    .collect(Collectors.toList());
            issueOperLogMapper.batchInsert(logList);
        }
    }

    @NotNull
    private static ProjIssueOperLog getProjIssueOperLog(ProjIssueInfo item, SysUserVO user, Date now, int operType) {
        // 1. 创建问题 2. 分配责任人 3. 修改问题描述 4.问题状态变更 5 删除问题数据
        ProjIssueOperLog log = new ProjIssueOperLog();
        log.setId(SnowFlakeUtil.getId());
        log.setIssueInfoId(item.getId());
        log.setOperType(operType);
        log.setModifyData(JSON.toJSONString(item));
        log.setCreaterId(user.getSysUserId());
        log.setUpdaterId(user.getSysUserId());
        log.setCreateTime(now);
        log.setUpdateTime(now);
        log.setIsDeleted(0);
        return log;
    }

    @Override
    public Result<List<IssueDataResp2>> queryIssue(QueryIssueReq queryIssueReq) {
        queryIssueReq.setOperationSource("supervisionCenter");
        Result<List<IssueDataResp>> listResult = issueInfoService.queryData(queryIssueReq);
        List<IssueDataResp2> issueDataResp2s = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(listResult.getData())) {
            for (IssueDataResp issueDataResp : listResult.getData()) {
                IssueDataResp2 issueDataResp2 = new IssueDataResp2();
                BeanUtils.copyProperties(issueDataResp, issueDataResp2);
                issueDataResp2.setIssueClassificationId(String.valueOf(issueDataResp.getIssueClassificationId()));
                issueDataResp2s.add(issueDataResp2);
            }
        }
        return Result.success(issueDataResp2s);
    }

    @Override
    @Transactional
    public boolean saveDeductionClassificationDict(SaveDeductionClassificationDictType req) {
        Date now = new Date();
        // 新增
        if (req.getId() == null) {
            DictDeductionType dictDeductionType = new DictDeductionType();
            dictDeductionType.setId(SnowFlakeUtil.getId());
            dictDeductionType.setIsDeleted(0);
            dictDeductionType.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictDeductionType.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictDeductionType.setCreateTime(now);
            dictDeductionType.setUpdateTime(now);
            dictDeductionType.setName(req.getName());
            dictDeductionType.setCode(UUID.randomUUID().toString().replace("-", ""));
            dictDeductionType.setApplicableScene("none");
            dictDeductionType.setSortNo(99);
            dictDeductionType.setProjectStageCode(null);
            dictDeductionType.setIssueClassificationId(req.getIssueClassificationId());
            dictDeductionType.setServerType(req.getServerType());
            if (StringUtils.isBlank(req.getDefaultScore())) {
                dictDeductionType.setDefaultScore(null);
            } else {
                dictDeductionType.setDefaultScore(new BigDecimal(req.getDefaultScore()));
            }
            dictDeductionTypeMapper.insert(dictDeductionType);
            return true;
        }

        DictDeductionType dictDeductionType = new DictDeductionType();
        dictDeductionType.setId(req.getId());
        dictDeductionType.setIssueClassificationId(req.getIssueClassificationId());
        dictDeductionType.setName(req.getName());
        dictDeductionType.setServerType(req.getServerType());
        if (StringUtils.isBlank(req.getDefaultScore())) {
            dictDeductionType.setDefaultScore(null);
        } else {
            dictDeductionType.setDefaultScore(new BigDecimal(req.getDefaultScore()));
        }
        dictDeductionType.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        dictDeductionType.setUpdateTime(now);
        dictDeductionTypeMapper.updateDictDeductionType(dictDeductionType);
        return true;
    }

    @Override
    @Transactional
    public boolean deleteDeductionClassificationDict(Long id) {
        return 1 == dictDeductionTypeMapper.invalidateById(id);
    }

    @Override
    public List<DictDeductionTypeVO> queryDeductionClassificationDict(QueryDeductionClassificationDictParam req) {
        return dictDeductionTypeMapper.queryDeductionClassificationDict(req);
    }

    @Override
    public List<BaseCodeNameResp> queryServerTypeDict() {
        List<DictServerType> allServerType = dictServerTypeMapper.getAllServerType();
        if (CollectionUtils.isEmpty(allServerType)) {
            return Collections.emptyList();
        }
        return allServerType.stream().map(item -> new BaseCodeNameResp(item.getServerTypeCode(), item.getServerTypeName())).collect(Collectors.toList());
    }


    @Override
    @Transactional
    public List<BackendTeamDeductionRecordVO> queryServerTeamDeductionRecordForBackendTeam(QueryServerTeamDeductionRecordParam param) {
        // 数据来源，判断查询首验还是终验的数据
        String source = projProjectAcceptanceService.getAcceptanceType(param.getProjectInfoId());

        List<BackendTeamDeductionRecordVO> list = new ArrayList<>();

        ConfigCustomBackendDetailLimit businessSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 13);
        if (businessSwitch != null && Integer.valueOf(1).equals(businessSwitch.getOpenFlag())) {
            BackendTeamDeductionRecordVO businessRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
            List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), 3L);
            if (CollectionUtils.isEmpty(projProjectMembers)) {
                throw new IllegalArgumentException("当前项目没有配置业务负责人");
            }
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));
            if (businessRecord == null) {
                ServerTeamDeductionRecord serverTeamDeductionRecord = new ServerTeamDeductionRecord();
                serverTeamDeductionRecord.setServerTeamDeductionRecordId(SnowFlakeUtil.getId());
                serverTeamDeductionRecord.setIsDeleted(0);
                serverTeamDeductionRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setCreateTime(new Date());
                serverTeamDeductionRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setUpdateTime(new Date());
                serverTeamDeductionRecord.setSource(source);
                serverTeamDeductionRecord.setServerTypeCode(BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
                serverTeamDeductionRecord.setServerTeamYyId(sysUser.getDeptId());
                serverTeamDeductionRecord.setRecordStatus(1);
                serverTeamDeductionRecord.setProjectInfoId(param.getProjectInfoId());
                int i = serverTeamDeductionRecordMapper.saveServerTeamDeductionRecord(serverTeamDeductionRecord);
                businessRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
            }
            businessRecord.setTotalScore(this.getTotalScore(param.getProjectInfoId(), source));
            businessRecord.setDeductionScore(this.getDeductionScore(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM, source));
            businessRecord.setToBeConfirmedCount(this.getToBeConfirmedCount(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM, source));
            list.add(businessRecord);
        }
        ConfigCustomBackendDetailLimit dataSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 14);
        if (dataSwitch != null && Integer.valueOf(1).equals(dataSwitch.getOpenFlag())) {
            BackendTeamDeductionRecordVO dataRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM.getCode());
            List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), 5L);
            if (CollectionUtils.isEmpty(projProjectMembers)) {
                throw new IllegalArgumentException("当前项目没有配置数据服务负责人");
            }
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));
            if (dataRecord == null) {
                ServerTeamDeductionRecord serverTeamDeductionRecord = new ServerTeamDeductionRecord();
                serverTeamDeductionRecord.setServerTeamDeductionRecordId(SnowFlakeUtil.getId());
                serverTeamDeductionRecord.setIsDeleted(0);
                serverTeamDeductionRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setCreateTime(new Date());
                serverTeamDeductionRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setUpdateTime(new Date());
                serverTeamDeductionRecord.setSource(source);
                serverTeamDeductionRecord.setServerTypeCode(BackendTeamTypeEnum.DATA_TEAM.getCode());
                serverTeamDeductionRecord.setServerTeamYyId(sysUser.getDeptId());
                serverTeamDeductionRecord.setRecordStatus(1);
                serverTeamDeductionRecord.setProjectInfoId(param.getProjectInfoId());
                int i = serverTeamDeductionRecordMapper.saveServerTeamDeductionRecord(serverTeamDeductionRecord);
                dataRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM.getCode());
            }
            dataRecord.setTotalScore(this.getTotalScore(param.getProjectInfoId(), source));
            dataRecord.setDeductionScore(this.getDeductionScore(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM, source));
            dataRecord.setToBeConfirmedCount(this.getToBeConfirmedCount(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM, source));
            list.add(dataRecord);
        }

        ConfigCustomBackendDetailLimit interfaceSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 15);
        if (interfaceSwitch != null && Integer.valueOf(1).equals(interfaceSwitch.getOpenFlag())) {
            BackendTeamDeductionRecordVO interfaceRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
            List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), 7L);
            if (CollectionUtils.isEmpty(projProjectMembers)) {
                throw new IllegalArgumentException("当前项目没有配置业务负责人");
            }
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));

            if (interfaceRecord == null) {
                ServerTeamDeductionRecord serverTeamDeductionRecord = new ServerTeamDeductionRecord();
                serverTeamDeductionRecord.setServerTeamDeductionRecordId(SnowFlakeUtil.getId());
                serverTeamDeductionRecord.setIsDeleted(0);
                serverTeamDeductionRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setCreateTime(new Date());
                serverTeamDeductionRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setUpdateTime(new Date());
                serverTeamDeductionRecord.setSource(source);
                serverTeamDeductionRecord.setServerTypeCode(BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
                serverTeamDeductionRecord.setServerTeamYyId(sysUser.getDeptId());
                serverTeamDeductionRecord.setRecordStatus(1);
                serverTeamDeductionRecord.setProjectInfoId(param.getProjectInfoId());
                int i = serverTeamDeductionRecordMapper.saveServerTeamDeductionRecord(serverTeamDeductionRecord);
                interfaceRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
            }
            interfaceRecord.setTotalScore(this.getTotalScore(param.getProjectInfoId(), source));
            interfaceRecord.setDeductionScore(this.getDeductionScore(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM, source));
            interfaceRecord.setToBeConfirmedCount(this.getToBeConfirmedCount(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM, source));
            list.add(interfaceRecord);
        }
        return list;
    }

    private BigDecimal getTotalScore(Long projectInfoId, String source) {
        // 两次验收的第一次验收的评分记录
        List<ProjProjectClassificationScorePO> firstRecord = projProjectClassificationScoreMapper.getProjectClassificationScoreInfo(projectInfoId, source);

        // 快速实施应用
        ProjProjectClassificationScorePO firstItem1 = firstRecord.stream()
                .filter(score1 -> "RapidImplementationApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("RapidImplementationApplication"));

        // 云健康应用-产品应用功能点
        ProjProjectClassificationScorePO firstItem2 = firstRecord.stream()
                .filter(score1 -> "ProductFunctionApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("ProductFunctionApplication"));

        // 云健康应用-设备接口
        ProjProjectClassificationScorePO firstItem3 = firstRecord.stream()
                .filter(score1 -> "EquipmentInterface".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("EquipmentInterface"));

        // 云健康应用-数据统计
        ProjProjectClassificationScorePO firstItem4 = firstRecord.stream()
                .filter(score1 -> "DataStatistics".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("DataStatistics"));

        // 满意度调查
        ProjProjectClassificationScorePO firstItem5 = firstRecord.stream()
                .filter(score1 -> "SatisfactionSurvey".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("SatisfactionSurvey"));

        // 首验总得分
        BigDecimal firstTotalScore = firstItem1.getScore().add(firstItem2.getScore()).add(firstItem3.getScore()).add(firstItem4.getScore()).add(firstItem5.getScore());
        return firstTotalScore.stripTrailingZeros();
    }

    private BigDecimal getDeductionScore(Long projectInfoId, BackendTeamTypeEnum serverType, String source) {
        ArrayList<String> stageCodeList = new ArrayList<>();
        stageCodeList.add(source);
        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(projectInfoId);
        backendTeamDeductionDetailVO2.setServerType(serverType.getCode());
        backendTeamDeductionDetailVO2.setStageCode(stageCodeList);

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);

        return list.stream().map(BackendTeamDeductionDetailInfoVO::getDeductionAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Integer getToBeConfirmedCount(Long projectInfoId, BackendTeamTypeEnum serverType, String source) {
        ArrayList<Integer> detailStatusList = new ArrayList<>();
        detailStatusList.add(3);

        ArrayList<String> stageCodeList = new ArrayList<>();
        stageCodeList.add(source);
        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(projectInfoId);
        backendTeamDeductionDetailVO2.setServerType(serverType.getCode());
        backendTeamDeductionDetailVO2.setStageCode(stageCodeList);
        backendTeamDeductionDetailVO2.setDetailRecordStatus(detailStatusList);

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        return list.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendConfirmation(SendConfirmationParam param) {
        if (CollectionUtils.isEmpty(param.getBackendTeamDeductionRecordIdList())) {
            throw new IllegalArgumentException("发送验收确认单，参数 backendTeamDeductionRecordIdList 不可为空");
        }
        Date now = new Date();
        for (Long recordId : param.getBackendTeamDeductionRecordIdList()) {
            ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(recordId);
            if (serverTeamDeductionRecord == null) {
                throw new IllegalArgumentException("发送验收确认单，参数 backendTeamDeductionRecordIdList 存在无效的记录");
            }
            if (serverTeamDeductionRecord.getRecordStatus() == 1 || serverTeamDeductionRecord.getRecordStatus() == 3) {
                UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
                updateParam.setServerTeamDeductionRecordId(recordId);
                updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setRecordStatus(2);
                updateParam.setQualityRemark(param.getQualityRemark());
                updateParam.setApplyUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setApplyTime(now);
                serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

                SysDept sysDept = sysDeptMapper.selectYunYingId(serverTeamDeductionRecord.getServerTeamYyId());
                SysUser sysUser = sysUserMapper.selectUserIdByYungyingId(sysDept.getDeptLeaderYunyingId());

                serverTeamScoreLogService.saveServerTeamScoreLog(recordId, 1, String.format("已发送后端确认单，待【%s】%s进行确认", sysDept.getDeptName(), sysUser.getUserName()), param.getQualityRemark());
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revertConfirmation(RevertConfirmationParam param) {
        ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(param.getBackendTeamDeductionRecordId());
        if (serverTeamDeductionRecord == null) {
            throw new IllegalArgumentException("撤回验收确认单，参数 backendTeamDeductionRecordId 无效");
        }
        if (serverTeamDeductionRecord.getRecordStatus() != 2) {
            throw new IllegalArgumentException("撤回验收确认单，仅允许撤回【待后端确认】的记录");
        }
        UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
        updateParam.setServerTeamDeductionRecordId(param.getBackendTeamDeductionRecordId());
        updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        updateParam.setRecordStatus(1);
        serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

        serverTeamDeductionRecordMapper.clearUserAndTime(param.getBackendTeamDeductionRecordId(), "apply");

        serverTeamScoreLogService.saveServerTeamScoreLog(param.getBackendTeamDeductionRecordId(), 2, "质管撤回验收确认单", "");
        return true;
    }

    @Override
    @Transactional
    public BackendTeamDeductionDetailVO queryBackendTeamDeductionDetail(QueryBackendDeductionRecordParam param) {
        ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(param.getBackendTeamDeductionRecordId());
        if (serverTeamDeductionRecord == null) {
            throw new IllegalArgumentException("参数 backendTeamDeductionRecordId 无效");
        }

        BackendTeamDeductionDetailVO backendTeamDeductionDetailVO = new BackendTeamDeductionDetailVO();
        backendTeamDeductionDetailVO.setBackendTeamDeductionRecordId(serverTeamDeductionRecord.getServerTeamDeductionRecordId());
        backendTeamDeductionDetailVO.setQualityRemark(serverTeamDeductionRecord.getQualityRemark());
        backendTeamDeductionDetailVO.setBackendRemark(serverTeamDeductionRecord.getBackendRemark());
        backendTeamDeductionDetailVO.setRecordStatus(serverTeamDeductionRecord.getRecordStatus());
        backendTeamDeductionDetailVO.setRecordStatusName(serverTeamDeductionRecord.getRecordStatusName());
        backendTeamDeductionDetailVO.setTotalDeductionAmount(this.getDeductionScore(serverTeamDeductionRecord.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(serverTeamDeductionRecord.getServerTypeCode()), serverTeamDeductionRecord.getSource()));
        backendTeamDeductionDetailVO.setLogInfoList(serverTeamScoreLogService.queryOperationLogByServerTeamDeductionRecordId(param.getBackendTeamDeductionRecordId()));

        BackendTeamTypeEnum backendTeamTypeEnumByCode = BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(serverTeamDeductionRecord.getServerTypeCode());
        long roleId;
        if (BackendTeamTypeEnum.BUSINESS_TEAM.equals(backendTeamTypeEnumByCode)) {
            roleId = 3L;
        } else if (BackendTeamTypeEnum.DATA_TEAM.equals(backendTeamTypeEnumByCode)) {
            roleId = 5L;
        } else {
            roleId = 7L;
        }
        List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(serverTeamDeductionRecord.getProjectInfoId(), roleId);
        if (CollectionUtils.isEmpty(projProjectMembers)) {
            throw new IllegalArgumentException("当前项目没有配置业务负责人");
        }


        SysDept sysDept = sysDeptMapper.selectYunYingId(serverTeamDeductionRecord.getServerTeamYyId());
        SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));

        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        SysUser currentSysUser = sysUserMapper.selectBySysUserId(currentSysUserId);
        backendTeamDeductionDetailVO.setCanAudit(currentSysUser.getUserYunyingId().equals(sysDept.getDeptLeaderYunyingId()));

        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(serverTeamDeductionRecord.getProjectInfoId());
        backendTeamDeductionDetailVO2.setServerType(serverTeamDeductionRecord.getServerTypeCode());
        backendTeamDeductionDetailVO2.setStageCode(param.getProjectStageCode());
        backendTeamDeductionDetailVO2.setDetailRecordStatus(param.getRecordStatus());

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                item.setServerTeamYyId(serverTeamDeductionRecord.getServerTeamYyId());
                item.setServerTeamName(sysDept.getDeptName());
                item.setServerTeamLeaderYyId(sysDept.getDeptLeaderYunyingId());
                item.setServerTeamLeaderName(sysUser.getUserName());
            });
        }

        backendTeamDeductionDetailVO.setDetailInfoList(list);
        return backendTeamDeductionDetailVO;
    }


    @Override
    @Transactional
    public List<ServerTeamDeductionRecordForBackendTeamVO> queryServerTeamDeductionRecordForBackendTeam(QueryServerTeamDeductionRecordForBackendTeamParam param) {
        List<ServerTeamDeductionRecordForBackendTeamVO> serverTeamDeductionRecordForBackendTeamVOS = serverTeamDeductionRecordMapper.queryServerTeamDeductionRecordForBackendTeam(param);
        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        SysUser sysUser = sysUserMapper.selectBySysUserId(currentSysUserId);
        if (!CollectionUtils.isEmpty(serverTeamDeductionRecordForBackendTeamVOS)) {
            serverTeamDeductionRecordForBackendTeamVOS.forEach(item -> {
                item.setTotalScore(this.getTotalScore(item.getProjectInfoId(), item.getSource()));
                item.setDeductionScore(this.getDeductionScore(item.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(item.getServerTypeCode()), item.getSource()));
                item.setToBeConfirmedCount(this.getToBeConfirmedCount(item.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(item.getServerTypeCode()), item.getSource()));
                item.setCanAudit(sysUser.getUserYunyingId().equals(String.valueOf(item.getServerTeamLeaderYyId())));
            });
        }
        return serverTeamDeductionRecordForBackendTeamVOS;
    }

    @Override
    @Transactional
    public boolean invalidDeduction(DeductionDetailIdParam param) {
        ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
        projDeductionDetailInfo.setProjDeductionDetailInfoId(param.getBackendTeamDeductionDetailId());
        projDeductionDetailInfo.setIsDeleted(1);
        return 1 == projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
    }

    @Override
    @Transactional
    public boolean submitConfirm(DeductionDetailIdParam param) {
        ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
        projDeductionDetailInfo.setProjDeductionDetailInfoId(param.getBackendTeamDeductionDetailId());
        projDeductionDetailInfo.setDetailStatus(1);
        return 1 == projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
    }

    @Override
    @Transactional
    public boolean changeDeduction(ChangeDeductionParam param) {
        ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
        projDeductionDetailInfo.setProjDeductionDetailInfoId(param.getBackendTeamDeductionDetailId());
        projDeductionDetailInfo.setPracticalDeduction(param.getDeductionAmount());
        return 1 == projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
    }

    @Override
    @Transactional
    public boolean backendOperation(BackendOperationParam param) {
        if ("confirm".equals(param.getOperationType())) {
            for (Long id : param.getIdList()) {
                ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
                projDeductionDetailInfo.setProjDeductionDetailInfoId(id);
                projDeductionDetailInfo.setDetailStatus(2);
                projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
            }
            return true;
        } else if ("reject".equals(param.getOperationType())) {
            for (Long id : param.getIdList()) {
                ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
                projDeductionDetailInfo.setProjDeductionDetailInfoId(id);
                projDeductionDetailInfo.setDetailStatus(3);
                projDeductionDetailInfo.setBackendRemark(param.getRejectReason());
                projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
            }
            return true;
        } else if ("revert".equals(param.getOperationType())) {
            for (Long id : param.getIdList()) {
                ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
                projDeductionDetailInfo.setProjDeductionDetailInfoId(id);
                projDeductionDetailInfo.setDetailStatus(1);
                projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean backendConfirmOrReject(BackendOperationParam2 param) {
        ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(param.getRecordId());
        if (serverTeamDeductionRecord == null) {
            throw new IllegalArgumentException("发送验收确认单，参数 backendTeamDeductionRecordIdList 存在无效的记录");
        }

        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(serverTeamDeductionRecord.getProjectInfoId());
        backendTeamDeductionDetailVO2.setServerType(serverTeamDeductionRecord.getServerTypeCode());

        List<BackendTeamDeductionDetailInfoVO> detailList = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        if (!CollectionUtils.isEmpty(detailList)) {
            List<Integer> detailStatusList = detailList.stream().map(BackendTeamDeductionDetailInfoVO::getDetailStatus).collect(Collectors.toList());
            // 存在未确认的明细项
            if (detailStatusList.contains(1)) {
                throw new IllegalArgumentException("请确认完所有的扣分明细项之后再提交验收确认单");
            }
            // 存在驳回状态的扣分明细项，只能进行驳回操作
            if (detailStatusList.contains(3)) {
                param.setOperationType("reject");
            }
        }

        Date now = new Date();
        // 后端确认
        if ("confirm".equals(param.getOperationType())) {
            if (serverTeamDeductionRecord.getRecordStatus() == 2) {
                UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
                updateParam.setServerTeamDeductionRecordId(param.getRecordId());
                updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setRecordStatus(4);
                updateParam.setBackendRemark(param.getRejectReason());
                updateParam.setConfirmUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setConfirmTime(now);
                serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

                serverTeamScoreLogService.saveServerTeamScoreLog(param.getRecordId(), 4, "后端已确认", param.getRejectReason());
                try {
                    GetProjBackendScoreParam getProjBackendScoreParam = convertGetProjBackendScoreParam(serverTeamDeductionRecord);
                    Boolean projBackendScore = yunYingService.getProjBackendScore(getProjBackendScoreParam);
                } catch (Exception e) {
                    log.error("调用运营平台接口同步后端得分扣分信息失败，errMsg={}，stackInfo=", e.getMessage(), e);
                }
                return true;
            }
        } else if ("reject".equals(param.getOperationType())) {
            if (serverTeamDeductionRecord.getRecordStatus() == 2) {
                UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
                updateParam.setServerTeamDeductionRecordId(param.getRecordId());
                updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setRecordStatus(3);
                updateParam.setBackendRemark(param.getRejectReason());
                serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

                SysUser sysUser = sysUserMapper.selectBySysUserId(serverTeamDeductionRecord.getApplyUserId());
                serverTeamScoreLogService.saveServerTeamScoreLog(param.getRecordId(), 3, String.format("后端已驳回，待【%s】进行确认", sysUser.getUserName()), param.getRejectReason());
                return true;
            }
        }
        return false;
    }

    @Override
    public Result<Void> checkAcceptStatus(Long projectInfoId) {
        ProjProjectAcceptance projProjectAcceptance = projProjectAcceptanceMapper.selectByProjectInfoIdOne(projectInfoId);

        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);

        ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        if (null == projProjectAcceptance) {
            return Result.fail(String.format("当前项目尚未申请验收，客户名称=%s，项目交付工单号=%s", projCustomInfo.getCustomName(), projProjectInfo.getProjectNumber()));
        }
        if (Integer.valueOf(1).equals(projProjectAcceptance.getAcceptanceStatus()) || Integer.valueOf(5).equals(projProjectAcceptance.getAcceptanceStatus())) {
            return Result.fail(String.format("质管中心尚未接收当前项目的验收申请，请先接收验收申请后再记录问题。客户名称=%s，项目交付工单号=%s", projCustomInfo.getCustomName(), projProjectInfo.getProjectNumber()));
        }
        return Result.success();
    }

    private GetProjBackendScoreParam convertGetProjBackendScoreParam(ServerTeamDeductionRecord serverTeamDeductionRecord) {
        GetProjBackendScoreParam getProjBackendScoreParam = new GetProjBackendScoreParam();
        getProjBackendScoreParam.setUserLoginName(userHelper.getCurrentUser().getAccount());

        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(serverTeamDeductionRecord.getProjectInfoId());
        ProjOrderInfo orderInfo = commonService.getOrderInfo(projProjectInfo.getOrderInfoId());
        getProjBackendScoreParam.setProjId(orderInfo.getYyOrderId());

        getProjBackendScoreParam.setOrgType(BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(serverTeamDeductionRecord.getServerTypeCode()).getYyOrgType());

        getProjBackendScoreParam.setBackendOrgId(serverTeamDeductionRecord.getServerTeamYyId());

        BigDecimal firstScore = this.getTotalScore(projProjectInfo.getProjectInfoId(), "first");
        BigDecimal finalScore = this.getTotalScore(projProjectInfo.getProjectInfoId(), "final");
        getProjBackendScoreParam.setCheckScore(firstScore.add(finalScore));

        BigDecimal deductScore = this.getDeductionScore(serverTeamDeductionRecord.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(serverTeamDeductionRecord.getServerTypeCode()), serverTeamDeductionRecord.getSource());
        getProjBackendScoreParam.setDeductScore(deductScore.negate());

        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(serverTeamDeductionRecord.getProjectInfoId());
        backendTeamDeductionDetailVO2.setServerType(serverTeamDeductionRecord.getServerTypeCode());
        backendTeamDeductionDetailVO2.setStageCode(Collections.singletonList(serverTeamDeductionRecord.getSource()));

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        // 只获取扣分分值大于0的
        List<RewardInfo> rewardInfoList = list.stream().filter(item -> item.getDeductionAmount() != null && item.getDeductionAmount().compareTo(BigDecimal.ZERO) > 0)
                .map(item -> {
                    RewardInfo rewardInfo = new RewardInfo();
                    rewardInfo.setRewardScore(item.getDeductionAmount().negate());
                    // todo 完善name
                    if (StringUtils.isNotBlank(item.getDeductionTypeName())) {
                        rewardInfo.setRewardName(item.getDeductionTypeName());
                    } else if (StringUtils.isNotBlank(item.getDeductionTypeCode())) {
                        rewardInfo.setRewardName(item.getDeductionTypeCode());
                    }
                    return rewardInfo;
                }).collect(Collectors.toList());

        getProjBackendScoreParam.setProjBackendDetailList(rewardInfoList);
        return getProjBackendScoreParam;
    }

    @Resource
    private ProductSatisfactionSurveyRecordMapper productSatisfactionSurveyRecordMapper;

    @Resource
    private ProductSatisfactionSurveyUserMapper productSatisfactionSurveyUserMapper;

    @Resource
    private ProjProductDeliverRecordMapper projProductDeliverRecordMapper;

    @Resource
    private FmUserFormService fmUserFormService;

    @Resource
    private TduckService tduckService;

    @Resource
    private MsunCloudYywgService msunCloudYywgService;

    @Override
    @Transactional
    public List<ProductSatisfactionSurveyRecordVO> getProductSatisfactionRecord(ProjectInfoId param) {
        List<ProductSatisfactionSurveyRecordVO> productSatisfactionRecord = productSatisfactionSurveyRecordMapper.getProductSatisfactionRecord(param.getProjectInfoId());
        if (CollectionUtils.isEmpty(productSatisfactionRecord)) {
            final Date now = new Date();
            List<BaseIdNameResp> baseIdName = projProductDeliverRecordMapper.queryDeliverProductIdAndNameByProjectInfoId(param.getProjectInfoId());
            baseIdName.forEach(item -> {
                ProductSatisfactionSurveyRecord productSatisfactionSurveyRecord = new ProductSatisfactionSurveyRecord();
                productSatisfactionSurveyRecord.setProductSatisfactionSurveyRecordId(SnowFlakeUtil.getId());
                productSatisfactionSurveyRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                productSatisfactionSurveyRecord.setCreateTime(now);
                productSatisfactionSurveyRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                productSatisfactionSurveyRecord.setUpdateTime(now);
                productSatisfactionSurveyRecord.setIsDeleted(0);
                productSatisfactionSurveyRecord.setYyProductId(item.getId());
                productSatisfactionSurveyRecord.setStatus(1);
                productSatisfactionSurveyRecord.setScore(null);
                productSatisfactionSurveyRecord.setRemark(null);
                productSatisfactionSurveyRecord.setSendTime(null);
                productSatisfactionSurveyRecord.setSendUserId(null);
                productSatisfactionSurveyRecord.setScoreTime(null);
                productSatisfactionSurveyRecord.setScoreUserId(null);
                productSatisfactionSurveyRecord.setStopTime(null);
                productSatisfactionSurveyRecord.setStopUserId(null);
                productSatisfactionSurveyRecord.setProjectInfoId(param.getProjectInfoId());
                productSatisfactionSurveyRecordMapper.saveProductSatisfactionSurveyRecord(productSatisfactionSurveyRecord);
            });
        }
        productSatisfactionRecord = productSatisfactionSurveyRecordMapper.getProductSatisfactionRecord(param.getProjectInfoId());
        productSatisfactionRecord.forEach(item -> {
            int unsentCount = productSatisfactionSurveyUserMapper.getCountByProjectAndProductAndStatus(param.getProjectInfoId(), item.getYyProductId(), 1);
            item.setUnsentCount(unsentCount);

            int recycledCount = productSatisfactionSurveyUserMapper.getCountByProjectAndProductAndStatus(param.getProjectInfoId(), item.getYyProductId(), 3);
            item.setRecycledCount(recycledCount);

            if (recycledCount != 0) {
                String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(item.getProjectInfoId(), String.valueOf(item.getYyProductId()), FormClassEnum.SATISFACTION_SURVEY.getFormClass());
//                https://imsp-test.msuncloud.com/csm-survey/project/form/data?key=OU8s6p0V
                String url = tduckDomain + "/project/form/data?key=" + formKey;
                item.setRecycledCountUrl(url);
            }

            int unRecycledCount = productSatisfactionSurveyUserMapper.getCountByProjectAndProductAndStatus(param.getProjectInfoId(), item.getYyProductId(), 2);
            int sentCount = recycledCount + unRecycledCount;
            item.setSentCount(sentCount);

            if (sentCount == 0) {
                item.setRecycledRadio(BigDecimal.ZERO);
            } else {
                item.setRecycledRadio(new BigDecimal(String.valueOf(recycledCount)).divide(new BigDecimal(String.valueOf(sentCount)), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")));
            }
        });
        return productSatisfactionRecord;
    }

    @Override
    @Transactional
    public boolean saveProductScore(TestParam9 param) {
        int i = productSatisfactionSurveyRecordMapper.updateScoreById(param.getProductSatisfactionSurveyRecordId(), param.getScore(), userHelper.getCurrentSysUserIdWithDefaultValue(), param.getRemark());
        return i == 1;
    }

    @Override
    @Transactional
    public boolean saveProductSatisfactionUser(TestParam1 param) {
        if (CollectionUtils.isEmpty(param.getYyProductIdList())) {
            throw new IllegalArgumentException("请选择需要推送满意度调研问卷的产品");
        }

        if (param.getHospitalInfoId() == null) {
            throw new IllegalArgumentException("请选择需要推送满意度调研问卷的医院");
        }

        if (param.getDept() == null) {
            throw new IllegalArgumentException("请选择需要推送满意度调研问卷的医院科室");
        }

        if (CollectionUtils.isEmpty(param.getUserList())) {
            throw new IllegalArgumentException("请选择需要推送满意度调研问卷的云健康用户");
        }
        final Date now = new Date();
        for (Long yyProductId : param.getYyProductIdList()) {
            for (CloudUserAccount cloudHospitalUser : param.getUserList()) {
                ProductSatisfactionSurveyUser productSatisfactionSurveyUser = new ProductSatisfactionSurveyUser();
                productSatisfactionSurveyUser.setProductSatisfactionSurveyUserId(SnowFlakeUtil.getId());
                productSatisfactionSurveyUser.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                productSatisfactionSurveyUser.setCreateTime(now);
                productSatisfactionSurveyUser.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                productSatisfactionSurveyUser.setUpdateTime(now);
                productSatisfactionSurveyUser.setIsDeleted(0);
                productSatisfactionSurveyUser.setYyProductId(yyProductId);
                productSatisfactionSurveyUser.setHospitalInfoId(param.getHospitalInfoId());
                productSatisfactionSurveyUser.setCloudHospitalDeptId(param.getDept().getId());
                productSatisfactionSurveyUser.setCloudHospitalDeptName(param.getDept().getName());
                productSatisfactionSurveyUser.setCloudHospitalUserId(cloudHospitalUser.getId());
                productSatisfactionSurveyUser.setCloudHospitalUserAccount(cloudHospitalUser.getAccount());
                productSatisfactionSurveyUser.setCloudHospitalUserName(cloudHospitalUser.getName());
                productSatisfactionSurveyUser.setStatus(1);
                productSatisfactionSurveyUser.setProjectInfoId(param.getProjectInfoId());
                productSatisfactionSurveyUser.setCloudIdentityId(cloudHospitalUser.getIdentityId());
                productSatisfactionSurveyUserMapper.saveProductSatisfactionSurveyUser(productSatisfactionSurveyUser);
            }
        }
        return true;
    }


    @Override
    public List<ProductSatisfactionSurveyUserVO> getProductSatisfactionUser(ProjectInfoId param) {
        List<ProductSatisfactionSurveyUserVO> productSatisfactionUser = productSatisfactionSurveyUserMapper.getProductSatisfactionUser(param.getProjectInfoId());
        return productSatisfactionUser;
    }

    @Override
    @Transactional
    public boolean deleteProductSatisfactionUser(SimpleId param) {
        return 1 == productSatisfactionSurveyUserMapper.abandonUserById(param.getId());
    }

    @Override
    @Transactional
    public boolean urge(Long productSatisfactionSurveyRecordId) {
        ProductSatisfactionSurveyRecord productSatisfactionRecord = this.productSatisfactionSurveyRecordMapper.getProductSatisfactionRecordById(productSatisfactionSurveyRecordId);

        List<ProductSatisfactionSurveyUser> productSatisfactionSurveyUser = productSatisfactionSurveyUserMapper.getProductSatisfactionSurveyUser(productSatisfactionRecord.getProjectInfoId(), productSatisfactionRecord.getYyProductId(), Collections.singletonList(2));
        for (ProductSatisfactionSurveyUser item : productSatisfactionSurveyUser) {
            // 调用催办接口发送消息
            this.sendMessage(FormClassEnum.SATISFACTION_SURVEY, productSatisfactionRecord.getYyProductId(), productSatisfactionRecord.getProjectInfoId(), item);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean send(TestParam10 param10) {
        if (CollectionUtils.isEmpty(param10.getProductSatisfactionSurveyRecordIdList())) {
            throw new IllegalArgumentException("请选择需要发送满意度调研问卷的产品");
        }
        for (Long productSatisfactionSurveyRecordId : param10.getProductSatisfactionSurveyRecordIdList()) {
            // 发放后将数据改为收集中
            productSatisfactionSurveyRecordMapper.updateStatus(productSatisfactionSurveyRecordId, 2, userHelper.getCurrentSysUserIdWithDefaultValue());

            // 复制项目使用的调研问卷
            ProductSatisfactionSurveyRecord productSatisfactionRecord = productSatisfactionSurveyRecordMapper.getProductSatisfactionRecordById(productSatisfactionSurveyRecordId);
            ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(productSatisfactionRecord.getProjectInfoId());
            FmUserForm fmUserForm = this.tduckService.createSatisfactionSurveyForm(FormClassEnum.SATISFACTION_SURVEY, String.valueOf(productSatisfactionRecord.getYyProductId()), projProjectInfo);

            fmUserFormService.updateFormStatusByFormKey(FormStatusEnum.RELEASE, fmUserForm.getFormKey());

            List<Integer> statusList = Arrays.asList(1, 2);
            List<ProductSatisfactionSurveyUser> productSatisfactionSurveyUser = productSatisfactionSurveyUserMapper.getProductSatisfactionSurveyUser(productSatisfactionRecord.getProjectInfoId(), productSatisfactionRecord.getYyProductId(), statusList);

            for (ProductSatisfactionSurveyUser item : productSatisfactionSurveyUser) {
                // 未发放的改为已发放未填写
                if (Integer.valueOf(1).equals(item.getStatus())) {
                    int i = productSatisfactionSurveyUserMapper.updateStatusById(item.getProductSatisfactionSurveyUserId(), 2);
                    log.info("更新数量={}", i);
                }
                // 调用催办接口发送消息
                this.sendMessage(FormClassEnum.SATISFACTION_SURVEY, productSatisfactionRecord.getYyProductId(), projProjectInfo.getProjectInfoId(), item);
            }
        }
        return true;
    }

    private void sendMessage(FormClassEnum formClassEnum, Long yyProductId, Long projectInfoId, ProductSatisfactionSurveyUser user) {
        String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(projectInfoId, String.valueOf(yyProductId), formClassEnum.getFormClass());
        String url = tduckService.createFormUrl(formKey, user.getCloudHospitalUserId(), "satisfaction", user.getHospitalInfoId(), user.getCloudHospitalDeptName());
        int i = productSatisfactionSurveyUserMapper.updateSurveyUrl(user.getProductSatisfactionSurveyUserId(), url);
        log.info("更新用户填写的调研问卷地址，结果={}", i);

        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.getHospitalInfoById(user.getHospitalInfoId());
        String content = "为提高服务质量和产品质量，众阳健康给您推送了一份产品使用满意度调查问卷，请您及时填写";
        msunCloudYywgService.sendMsg(String.valueOf(hospitalInfo.getCloudHospitalId()), "客户服务平台", Collections.singletonList(user.getCloudIdentityId()), "产品满意度调查问卷", content, url);
    }

    @Override
    @Transactional
    public boolean stop(TestParam10 param10) {
        if (CollectionUtils.isEmpty(param10.getProductSatisfactionSurveyRecordIdList())) {
            throw new IllegalArgumentException("请选择需要停止收集问卷的产品");
        }
        for (Long productSatisfactionSurveyRecordId : param10.getProductSatisfactionSurveyRecordIdList()) {
            productSatisfactionSurveyRecordMapper.updateStatus(productSatisfactionSurveyRecordId, 3, userHelper.getCurrentSysUserIdWithDefaultValue());
            ProductSatisfactionSurveyRecord productSatisfactionRecord = productSatisfactionSurveyRecordMapper.getProductSatisfactionRecordById(productSatisfactionSurveyRecordId);
            String formKey = fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(productSatisfactionRecord.getProjectInfoId(), String.valueOf(productSatisfactionRecord.getYyProductId()), FormClassEnum.SATISFACTION_SURVEY.getFormClass());
            fmUserFormService.updateFormStatusByFormKey(FormStatusEnum.STOP, formKey);
        }
        return true;
    }

    @Override
    @Transactional
    public Result<Void> updateSatisfactionSurveyStatus(UpdateSurveyPlanStatusReq2 updateSurveyPlanStatus) {
        int count = productSatisfactionSurveyUserMapper.updateStatus(3, updateSurveyPlanStatus.getProjectInfoId(), String.valueOf(updateSurveyPlanStatus.getSurveyUserId()), updateSurveyPlanStatus.getDeptName(), updateSurveyPlanStatus.getHospitalInfoId(), updateSurveyPlanStatus.getYyProductId());
        if (count == 1) {
            return Result.success();
        }
        throw new IllegalArgumentException("更新失败，存在多条数据");
    }


}
