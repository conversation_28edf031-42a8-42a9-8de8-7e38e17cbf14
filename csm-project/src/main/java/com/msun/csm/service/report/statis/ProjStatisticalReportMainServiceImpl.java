package com.msun.csm.service.report.statis;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.BaseLableValueResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.oldimsp.OldUserNewReport;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.report.statis.*;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.oldimsp.OldUserReportMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.report.*;
import com.msun.csm.feign.client.knowledge.KnowledgeFeignClient;
import com.msun.csm.model.imsp.YunweiUpdateStatisReportDTO;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.req.projectreview.QueryInfoReq;
import com.msun.csm.model.req.projreport.statis.*;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.resp.RejectReasonAndCount;
import com.msun.csm.model.resp.projectreview.UserModelAllResp;
import com.msun.csm.model.resp.projectreview.UserModelResp;
import com.msun.csm.model.resp.projreport.MenuVO;
import com.msun.csm.model.resp.projreport.MsunReportMain;
import com.msun.csm.model.resp.statis.*;
import com.msun.csm.model.statis.ProjStatisticalReportMainUpdateStatusReq;
import com.msun.csm.model.struct.StatisticalReportMethodModel;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.config.projectreview.ConfigProjectReviewTypeUserService;
import com.msun.csm.service.proj.ProjProjectMemberService;
import com.msun.csm.service.proj.ProjProjectPlanService;
import com.msun.csm.service.proj.ProjTodoTaskService;
import com.msun.csm.service.proj.producttask.ProjProductTaskServiceImpl;
import com.msun.csm.service.sysfile.SysFileService;
import com.msun.csm.util.*;
import com.msun.csm.util.DateUtil;
import com.msun.csm.util.obs.OBSClientUtils;
import com.msun.csm.util.statis.WpsImg;
import com.msun.csm.util.statis.WpsImgUtil;
import com.obs.services.model.PutObjectResult;

/**
 * @Description:
 * @Author: zd
 * @Date: 2024/11/7
 */

@Service
@Slf4j
public class ProjStatisticalReportMainServiceImpl extends ServiceImpl<ProjStatisticalReportMainMapper, ProjStatisticalReportMainEntity> implements ProjStatisticalReportMainService {
    String indicatorCode = "indicatorCode";
    String indicatorTitle = "indicatorTitle";
    // 主表
    @Resource
    private ProjStatisticalReportMainMapper projStatisticalReportMainMapper;
    // 统计口径表
    @Resource
    private ProjStatisticalCalibrationMapper projStatisticalCalibrationMapper;
    // 科室
    @Resource
    private ConfigHospitalDeptMapper configHospitalDeptMapper;
    // 报表使用频率字典表
    @Resource
    private DictReportFrequencyMapper dictReportFrequencyMapper;
    // 项目开启裁定限制
    @Resource
    private ProjReportAdjudicationLimitMapper projReportAdjudicationLimitMapper;
    //  制作方式表
    @Resource
    private ConfigProductionMethodMapper configProductionMethodMapper;

    @Resource
    private ProjProjectMemberService projProjectMemberService;

    // 使用用途
    @Resource
    private ConfigReportPurposeMapper configReportPurposeMapper;
    // 日志
    @Resource
    private ProjReportOperationRecordMapper projReportOperationRecordMapper;
    @Lazy
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Lazy
    @Resource
    private UserHelper userHelper;
    @Lazy
    @Resource
    private ProjProjectFileMapper projProjectFileMapper;
    @Lazy
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Value("${project.obs.prePath}")
    private String prePath;
    @Lazy
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Lazy
    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;
    @Lazy
    @Resource
    private RedisUtil redisUtil;
    @Lazy
    @Resource
    private SysFileService sysFileService;
    @Lazy
    @Resource
    private OldUserReportMapper oldUserReportMapper;
    @Value("${thisProjectUrl}")
    private String tableUrlValue;
    @Resource
    private ProjProjectPlanService projProjectPlanService;
    @Resource
    private ProjTodoTaskService projTodoTaskService;
    @Lazy
    @Resource
    private KnowledgeFeignClient knowledgeFeignClient;
    @Value("${project.feign.knowledge.appId}")
    private String knowledgeAppId;
    @Value("${project.feign.knowledge.publicKey}")
    private String knowledgePublicKey;

    @Lazy
    @Resource
    private ConfigProjectReviewTypeUserService configProjectReviewTypeUserService;

    /**
     * 创建下拉框
     *
     * @param sheet
     * @param dept
     * @param firstCol
     * @param lastCol
     * @param flag
     */
    private static void extracted(XSSFSheet sheet, String[] dept, int firstCol, int lastCol, boolean flag) {
        DataValidationHelper validationHelper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(dept);
        // 假设最多1000行
        CellRangeAddressList addressList = new CellRangeAddressList(1, 300, firstCol, lastCol);
        DataValidation validation = validationHelper.createValidation(constraint, addressList);
        // 必须重下拉选择
        if (flag) {
            validation.setShowErrorBox(true);
        }
        sheet.addValidationData(validation);
    }

    /**
     * 根据配置的url判断是否为测试环境
     *
     * @param tableUrlValuePrivate
     * @return
     */
    private static String getString(String tableUrlValuePrivate) {
        if (tableUrlValuePrivate != null) {
            if (tableUrlValuePrivate.contains("-test") || tableUrlValuePrivate.contains("172")) {
                tableUrlValuePrivate = "https://************/";
//                tableUrlValuePrivate = "http://***********:30354/";
            } else {
                tableUrlValuePrivate = "http://************:30354/";
            }
        }
        return tableUrlValuePrivate;
    }

    /**
     * 批量删除数据
     *
     * @param projStatisticalReportMainDeleteReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchDeleteByIds(ProjStatisticalReportMainDeleteReq projStatisticalReportMainDeleteReq) {
        if (projStatisticalReportMainDeleteReq.getMainIds() != null && projStatisticalReportMainDeleteReq.getMainIds().size() > 0) {
//            projStatisticalReportMainMapper.deleteBatchIds(projStatisticalReportMainDeleteReq.getMainIds());
            projStatisticalCalibrationMapper.deleteByMainIds(projStatisticalReportMainDeleteReq);
            List<ProjStatisticalReportMainEntity> list = projStatisticalReportMainMapper.selectBatchIds(projStatisticalReportMainDeleteReq.getMainIds());
            List<String> requirementIds = new ArrayList<>();
            if (list != null && list.size() > 0) {
                for (ProjStatisticalReportMainEntity entity : list) {
                    // 调用报表平台删除数据
                    if (entity.getReportMainId() != null && !"".equals(entity.getReportMainId())) {
                        requirementIds.add(entity.getStatisticalReportMainId().toString());
                    }
                    entity.setIsDeleted(1);
                    entity.setReportMainId("");
                    projStatisticalReportMainMapper.updateById(entity);
                    projStatisticalReportMainMapper.deleteById(entity);
                    SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                    param.setProjectInfoId(entity.getProjectInfoId());
                    param.setHospitalInfoId(entity.getHospitalInfoId());
                    param.setUserId(entity.getAllocateUserId());
                    param.setCode(DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                    projTodoTaskService.projectTodoTaskInit(param);
                }
                if (requirementIds.size() > 0) {
                    String tableUrlValuePrivate = tableUrlValue;
                    // 调用报表平台删除数据
                    if (tableUrlValuePrivate != null) {
                        tableUrlValuePrivate = tableUrlValuePrivate.replace("csm", "");
                    }
                    String url = tableUrlValuePrivate + "msun-reportweb-app-center/designer" + "/msunReportMain/updateReportVsImspRequirementNumber";
                    Map<String, Object> param = new HashMap<>();
                    param.put("requirementIds", requirementIds);
                    try {
                        Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
                        log.error("调用报表平台删除数据返回参数=====> {}", stringObjectMap);
                    } catch (IOException e) {
                        log.error("调用报表平台删除数据失败" + e);
                    }
                }
                ProjStatisticalReportMainEntity projStatisticalReportMainEntity = list.get(0);
                projProjectPlanService.addTotalCountByProjectInfoIdAndItemCode(projStatisticalReportMainEntity.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT, -1 * projStatisticalReportMainDeleteReq.getMainIds().size());
                projTodoTaskService.todoTaskTotalCountSync(projStatisticalReportMainEntity.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT.getPlanItemCode());
                projTodoTaskService.todoTaskTotalCountSync(projStatisticalReportMainEntity.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                projTodoTaskService.todoTaskTotalCountSync(projStatisticalReportMainEntity.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.getPlanItemCode());
            }
            return Result.success("删除成功");
        } else {
            return Result.fail("请选择要删除的数据");
        }
    }

    /**
     * 查询科室数据
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryHospitalDeptData(Long projectInfoId) {
        return configHospitalDeptMapper.selectListByProjectInfoId(projectInfoId);
    }

    /**
     * 查询报表使用频率字典表
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryFrequencyData() {
        return dictReportFrequencyMapper.queryFrequencyData();
    }

    /**
     * 查询用途
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryPurposeData(Long projectInfoId) {
        return configReportPurposeMapper.queryPurposeData(projectInfoId);
    }

    /**
     * 查询状态字典表
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryStausData() {
        return dictReportFrequencyMapper.queryStausData();
    }

    /**
     * 保存数据
     *
     * @param projSurveyFormAddReqs
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveReportData(ProjStatisticalReportMainAddReq projSurveyFormAddReqs) {
        List<BaseIdNameResp> listPurposeData = configReportPurposeMapper.queryPurposeData(projSurveyFormAddReqs.getProjectInfoId());
        List<BaseIdNameResp> listDeptData = configHospitalDeptMapper.selectListByProjectInfoId(projSurveyFormAddReqs.getProjectInfoId());
        List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
        List<BaseIdNameResp> fequencyData = dictReportFrequencyMapper.queryFrequencyData();
        // 封装用途id和名称
        Map<String, Long> mapPurposeData = new HashMap<>();
        listPurposeData.forEach(e -> {
            mapPurposeData.put(e.getName(), e.getId());
        });
        // 封装科室id和名称
        Map<String, Long> mapDeptData = new HashMap<>();
        listDeptData.forEach(e -> {
            mapDeptData.put(e.getName(), e.getId());
        });
        // 封装状态id和名称
        Map<Integer, String> mapStatusData = new HashMap<>();
        statusData.forEach(e -> {
            mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
        });
        if (projSurveyFormAddReqs.getStatisticalReportMainId() != null) {
            ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(projSurveyFormAddReqs.getStatisticalReportMainId());
            if (po == null) {
                return Result.fail("根据id查询不到统计报表");
            }
            if (projSurveyFormAddReqs.getHospitalInfoId() != null) {
                po.setHospitalInfoId(projSurveyFormAddReqs.getHospitalInfoId());
            }
            // 修改名称
            if (projSurveyFormAddReqs.getReportName() != null) {
                po.setReportName(projSurveyFormAddReqs.getReportName());
            }
            // 修改路径
            if (projSurveyFormAddReqs.getMountPath() != null) {
                po.setMountPath(projSurveyFormAddReqs.getMountPath());
            }
            // 修改使用科室
            // 使用科室，无hospitalDeptName 进行新增
            if (projSurveyFormAddReqs.getHospitalDeptName() != null && !"".equals(projSurveyFormAddReqs.getHospitalDeptName())) {
                po.setHospitalDeptName(projSurveyFormAddReqs.getHospitalDeptName());
                if (mapDeptData.get(projSurveyFormAddReqs.getHospitalDeptName()) != null) {
                    po.setHospitalDeptId(mapDeptData.get(projSurveyFormAddReqs.getHospitalDeptName()));
                } else {
                    Long deptId = SnowFlakeUtil.getId();
                    ConfigHospitalDeptEntity configHospitalDeptEntity = new ConfigHospitalDeptEntity();
                    configHospitalDeptEntity.setProjectInfoId(po.getProjectInfoId());
                    configHospitalDeptEntity.setHospitalDeptName(po.getHospitalDeptName());
                    configHospitalDeptEntity.setHospitalDeptId(deptId);
                    configHospitalDeptEntity.setOrderNo(0);
                    configHospitalDeptMapper.insert(configHospitalDeptEntity);
                    po.setHospitalDeptId(deptId);
                }
            }
            // 修改用途分类
            if (projSurveyFormAddReqs.getReportPurposeName() != null && !"".equals(projSurveyFormAddReqs.getReportPurposeName())) {
                po.setReportPurposeName(projSurveyFormAddReqs.getReportPurposeName());
                if (mapPurposeData.get(projSurveyFormAddReqs.getReportPurposeName()) != null) {
                    po.setReportPurposeId(mapPurposeData.get(projSurveyFormAddReqs.getReportPurposeName()));
                } else {
                    Long purposeId = SnowFlakeUtil.getId();
                    ConfigReportPurpose configReportPurpose = new ConfigReportPurpose();
                    configReportPurpose.setProjectInfoId(po.getProjectInfoId());
                    configReportPurpose.setReportPurposeId(purposeId);
                    configReportPurpose.setReportPurposeName(po.getReportPurposeName());
                    configReportPurpose.setOrderNo(0);
                    configReportPurposeMapper.insert(configReportPurpose);
                    po.setReportPurposeId(purposeId);
                }
            }
            po.setReportFrequencyId(projSurveyFormAddReqs.getReportFrequencyId());
            // 使用频次id,reportFrequencyName 进行新增
            if (projSurveyFormAddReqs.getReportFrequencyId() != null) {
                if (fequencyData != null && fequencyData.size() > 0) {
                    for (BaseIdNameResp baseIdNameResp : fequencyData) {
                        if (projSurveyFormAddReqs.getReportFrequencyId().equals(baseIdNameResp.getId())) {
                            po.setReportFrequencyName(baseIdNameResp.getName());
                        }
                    }
                }
            }
            // 上线必备
            if (projSurveyFormAddReqs.getOnlineFlag() != null) {
                po.setOnlineFlag(projSurveyFormAddReqs.getOnlineFlag());
            }
            // 备注
            if (projSurveyFormAddReqs.getRemarks() != null && !"".equals(projSurveyFormAddReqs.getRemarks())) {
                po.setRemarks(projSurveyFormAddReqs.getRemarks());
            }
            // 统计口径
            if (projSurveyFormAddReqs.getCalibrationEntities() != null && projSurveyFormAddReqs.getCalibrationEntities().size() > 0) {
                projStatisticalCalibrationMapper.delete(new QueryWrapper<ProjStatisticalCalibrationEntity>().eq("statistical_report_main_id", po.getStatisticalReportMainId()));
                projSurveyFormAddReqs.getCalibrationEntities().forEach(e -> {
                    e.setStatisticalReportMainId(po.getStatisticalReportMainId());
                    e.setStatisticalCalibrationId(SnowFlakeUtil.getId());
                    e.setOrderNo(0);
                    projStatisticalCalibrationMapper.insert(e);
                });
            }
            // 统计报表样式
            if (projSurveyFormAddReqs.getReportStyleList() != null && projSurveyFormAddReqs.getReportStyleList().size() > 0) {
                String reportStyle = "";
                for (ProjFileReq sysFile : projSurveyFormAddReqs.getReportStyleList()) {
                    reportStyle += sysFile.getProjectFileId() + ",";
                }
                reportStyle = reportStyle.substring(0, reportStyle.length() - 1);
                po.setReportStyle(reportStyle);
            }
            // 运维提交过来的单据直接进行裁定中。
            if (projSurveyFormAddReqs.getOperationStatisticsReportId() != null && !projSurveyFormAddReqs.getOperationStatisticsReportId().isEmpty()) {
                po.setReportStatus(13);
                updateYunweiStatus(po.getOperationStatisticsReportId(), po.getStatisticalReportMainId(), 2);
            }
            // 保存并申请裁定
            if (projSurveyFormAddReqs.getSaveAndApplyAdjudicationFlag() != null && projSurveyFormAddReqs.getSaveAndApplyAdjudicationFlag() == 1) {
                log.error("保存并申请裁定===>" + po.getStatisticalReportMainId());
                // 处理是否需要裁定。 如果判定为不需要裁定，则插入数据后直接裁定通过。
                Boolean b = checkIsOpenEnableAuditVerification(projSurveyFormAddReqs.getProjectInfoId());
                if (!b) {
                    po.setAuditUserId(userHelper.getCurrentUser().getSysUserId());
                    po.setAuditTime(new Timestamp(System.currentTimeMillis()));
                    po.setReportStatus(13);
                } else {
                    po.setReportStatus(11);
                }
            }
            projStatisticalReportMainMapper.updateById(po);
            if (po.getReportStatus() != 13) {
                // 保存操作记录
                extracted(po, mapStatusData);
            }
            return Result.success();
        }
        List<ProjStatisticalReportMainEntity> savesList = projStatisticalReportMainMapper.selectList(new QueryWrapper<ProjStatisticalReportMainEntity>().eq("project_info_id", projSurveyFormAddReqs.getProjectInfoId()).eq("report_name", projSurveyFormAddReqs.getReportName().trim()));
        if (CollectionUtil.isNotEmpty(savesList)) {
            return Result.fail("该报表名称已存在");
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        Long id = SnowFlakeUtil.getId();
        ProjStatisticalReportMainEntity projStatisticalReportMainEntity = new ProjStatisticalReportMainEntity();
        BeanUtils.copyProperties(projSurveyFormAddReqs, projStatisticalReportMainEntity);
        if ((projStatisticalReportMainEntity.getCustomInfoId() == null || projStatisticalReportMainEntity.getProjectInfoId() == null) && projStatisticalReportMainEntity.getHospitalInfoId() != null) {
            List<ProjProjectInfo> ppiList = projProjectInfoMapper.selectProjectListByHospitalId(projStatisticalReportMainEntity.getHospitalInfoId());
            if (ppiList != null && ppiList.size() > 0) {
                List<ProjProjectInfo> hisPpiList = ppiList.stream().filter(ppi -> ppi.getHisFlag() == 1).collect(Collectors.toList());
                if (hisPpiList != null && hisPpiList.size() > 0) {
                    projStatisticalReportMainEntity.setProjectInfoId(hisPpiList.get(0).getProjectInfoId());
                    projStatisticalReportMainEntity.setCustomInfoId(hisPpiList.get(0).getCustomInfoId());
                } else {
                    projStatisticalReportMainEntity.setProjectInfoId(ppiList.get(0).getProjectInfoId());
                    projStatisticalReportMainEntity.setCustomInfoId(ppiList.get(0).getCustomInfoId());
                }
            }
        }
        projStatisticalReportMainEntity.setStatisticalReportMainId(id);
        List<ProjFileReq> systems = projSurveyFormAddReqs.getReportStyleList();
        String reportStyle = "";
        if (systems != null && systems.size() > 0) {
            for (ProjFileReq sysFile : systems) {
                reportStyle += sysFile.getProjectFileId() + ",";
                try {
                    ProjProjectFile file = projProjectFileMapper.selectById(sysFile.getProjectFileId());
                    file.setProjectInfoId(projStatisticalReportMainEntity.getProjectInfoId());
                    projProjectFileMapper.updateByPrimaryKey(file);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            reportStyle = reportStyle.substring(0, reportStyle.length() - 1);
        } else {
            return Result.fail("请选择报表样式");
        }
        projStatisticalReportMainEntity.setReportStyle(reportStyle);
        // 上线必备
        if (projSurveyFormAddReqs.getOnlineFlag() != null) {
            projStatisticalReportMainEntity.setOnlineFlag(projSurveyFormAddReqs.getOnlineFlag());
        } else {
            projStatisticalReportMainEntity.setOnlineFlag(1);
        }
        // 使用科室，无hospitalDeptName 进行新增
        if (projSurveyFormAddReqs.getHospitalDeptName() != null && !"".equals(projSurveyFormAddReqs.getHospitalDeptName())) {
            if (mapDeptData.get(projSurveyFormAddReqs.getHospitalDeptName()) != null) {
                projStatisticalReportMainEntity.setHospitalDeptId(mapDeptData.get(projSurveyFormAddReqs.getHospitalDeptName()));
            } else {
                Long deptId = SnowFlakeUtil.getId();
                ConfigHospitalDeptEntity configHospitalDeptEntity = new ConfigHospitalDeptEntity();
                configHospitalDeptEntity.setProjectInfoId(projSurveyFormAddReqs.getProjectInfoId());
                configHospitalDeptEntity.setHospitalDeptName(projSurveyFormAddReqs.getHospitalDeptName());
                configHospitalDeptEntity.setHospitalDeptId(deptId);
                configHospitalDeptEntity.setOrderNo(0);
                configHospitalDeptMapper.insert(configHospitalDeptEntity);
                projStatisticalReportMainEntity.setHospitalDeptId(deptId);
            }
        } else {
            return Result.fail("请选择使用科室");
        }
        // 用途分类id,reportPurposeName 进行新增
        if (projSurveyFormAddReqs.getReportPurposeName() != null && !"".equals(projSurveyFormAddReqs.getReportPurposeName())) {
            if (mapPurposeData.get(projSurveyFormAddReqs.getReportPurposeName()) != null) {
                projStatisticalReportMainEntity.setReportPurposeId(mapPurposeData.get(projSurveyFormAddReqs.getReportPurposeName()));
            } else {
                Long purposeId = SnowFlakeUtil.getId();
                ConfigReportPurpose configReportPurpose = new ConfigReportPurpose();
                configReportPurpose.setProjectInfoId(projSurveyFormAddReqs.getProjectInfoId());
                configReportPurpose.setReportPurposeId(purposeId);
                configReportPurpose.setReportPurposeName(projSurveyFormAddReqs.getReportPurposeName());
                configReportPurpose.setOrderNo(0);
                configReportPurposeMapper.insert(configReportPurpose);
                projStatisticalReportMainEntity.setReportPurposeId(purposeId);
            }
        } else {
            return Result.fail("请选择用途分类");
        }
        // 使用频次id,reportFrequencyName 进行新增
        if (projSurveyFormAddReqs.getReportFrequencyId() != null) {
            if (fequencyData != null && fequencyData.size() > 0) {
                for (BaseIdNameResp baseIdNameResp : fequencyData) {
                    if (projSurveyFormAddReqs.getReportFrequencyId().equals(baseIdNameResp.getId())) {
                        projStatisticalReportMainEntity.setReportFrequencyName(baseIdNameResp.getName());
                    }
                }
            }
        }
        // 调研人
        projStatisticalReportMainEntity.setSurveyTime(new Timestamp(System.currentTimeMillis()));
        projStatisticalReportMainEntity.setSurveyUserId(currentUser.getSysUserId());
        // 保存并申请裁定 判断是否需要裁定， 如果需要裁定则状态改为11
        if (projSurveyFormAddReqs.getSaveAndApplyAdjudicationFlag() != null && projSurveyFormAddReqs.getSaveAndApplyAdjudicationFlag() == 1) {
            // 处理是否需要裁定。 如果判定为不需要裁定，则插入数据后直接裁定通过。
            Boolean b = checkIsOpenEnableAuditVerification(projSurveyFormAddReqs.getProjectInfoId());
            if (!b) {
                projStatisticalReportMainEntity.setAuditUserId(currentUser.getSysUserId());
                projStatisticalReportMainEntity.setAuditTime(new Timestamp(System.currentTimeMillis()));
                projStatisticalReportMainEntity.setReportStatus(13);
            } else {
                projStatisticalReportMainEntity.setReportStatus(11);
            }
        } else {
            projStatisticalReportMainEntity.setReportStatus(1);
        }
        // 运维提交过来的单据直接进行裁定中。
        // 运维提交过来的单据直接进行裁定通过： 2025-05-06 修改
        if (projSurveyFormAddReqs.getOperationStatisticsReportId() != null && !projSurveyFormAddReqs.getOperationStatisticsReportId().isEmpty()) {
            projStatisticalReportMainEntity.setReportStatus(13);
            updateYunweiStatus(projStatisticalReportMainEntity.getOperationStatisticsReportId(), projStatisticalReportMainEntity.getStatisticalReportMainId(), 2);
        }
        projStatisticalReportMainMapper.insert(projStatisticalReportMainEntity);
        // 保存操纵记录
        extracted(projStatisticalReportMainEntity, mapStatusData);
        // 统计口径
        if (projSurveyFormAddReqs.getCalibrationEntities() != null && projSurveyFormAddReqs.getCalibrationEntities().size() > 0) {
            projSurveyFormAddReqs.getCalibrationEntities().forEach(e -> {
                e.setStatisticalReportMainId(id);
                e.setStatisticalCalibrationId(SnowFlakeUtil.getId());
                e.setOrderNo(0);
                projStatisticalCalibrationMapper.insert(e);
            });
        } else {
            return Result.fail("请选择统计口径");
        }
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(projSurveyFormAddReqs.getProjectInfoId());
        param.setHospitalInfoId(projSurveyFormAddReqs.getHospitalInfoId());
        param.setCode(DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT.getPlanItemCode());
        param.setUserId(currentUser.getSysUserId());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        return Result.success();
    }

    /**
     * 查询制作方式接口
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<ConfigProductionMethodResp> queryConfigProductionMethodData(Long projectInfoId) {
        List<ConfigProductionMethodResp> list = configProductionMethodMapper.selectListByProjectInfoId();
        Boolean b = checkIsOpenEnableAuditVerification(projectInfoId);
        if (!b) {
            list.forEach(e -> {
                e.setEnableFlag(0);
            });
        }
        return list;
    }

    /**
     * 查询报表分页
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjStatisticalReportPageResp<ProjStatisticalReportMainSelectResp>> findReportPage(ProjStatisticalReportMainPageReq dto) {
        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();

        List<ProjStatisticalReportMainSelectResp> reportPage = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> projStatisticalReportMainMapper.findReportPage(dto));
        reportPage.forEach(projSurveyReportResp -> {
            ProjectMemberVO projectMemberVO = projProjectMemberMapper.getProjectMemberByUserId(projSurveyReportResp.getProjectInfoId(), currentSysUserId);
            projSurveyReportResp.setCanVerification(projectMemberVO != null && Integer.valueOf(1).equals(projectMemberVO.getRoleType()));
            RejectReasonAndCount rejectReasonAndCount = this.selectReasonAndCountById(projSurveyReportResp.getStatisticalReportMainId());
            projSurveyReportResp.setRejectCount(rejectReasonAndCount.getRejectCount());
            projSurveyReportResp.setRejectReason(rejectReasonAndCount.getRejectReason());
        });
        ProjStatisticalReportPageResp<ProjStatisticalReportMainSelectResp> pageInfo = new ProjStatisticalReportPageResp<>(reportPage);
        // 查询出的数据查询图片对照表， 查询图片
        if (reportPage != null && reportPage.size() > 0) {
            reportPage.forEach(e -> {
                e.setOnlineFlagBoolean(e.getOnlineFlag() == 1);
                if (e.getCalibrationIdFirstFour() != null && !"".equals(e.getCalibrationIdFirstFour())) {
                    e.setCalibrationIdFirstFour(e.getCalibrationIdFirstFour().replaceAll("==", "\n"));
                }
                if (e.getStatisticalCalibrationNames() != null && !"".equals(e.getStatisticalCalibrationNames())) {
                    e.setStatisticalCalibrationNames(e.getStatisticalCalibrationNames().replaceAll("==", "\n"));
                }
                List<Long> list = new ArrayList<>();
                if (e.getReportStyle() != null && !"".equals(e.getReportStyle())) {
                    String[] styles = e.getReportStyle().split(",");
                    if (styles != null) {
                        for (String style : styles) {
                            try {
                                list.add(Long.parseLong(style));
                            } catch (Exception ee) {
                                log.error("统计报表样式转换异常", ee);
                            }
                        }
                    }
                }
                List<ProjFileReq> p = new ArrayList<>();
                if (list != null && list.size() > 0) {
                    List<ProjProjectFile> reportStyleList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", list));
                    if (reportStyleList != null && reportStyleList.size() > 0) {
                        reportStyleList.forEach(e1 -> {
                            ProjFileReq req = new ProjFileReq();
                            req.setProjectFileId(e1.getProjectFileId());
                            req.setName(e1.getFileName());
                            req.setUrl(OBSClientUtils.getTemporaryUrlLimit(e1.getFilePath(), 3600));
                            // 因为之前只允许传图片，获取不到文件类型时默认为图片
                            if (StringUtils.isBlank(e1.getFileType())) {
                                req.setFileType("image");
                            } else {
                                req.setFileType(e1.getFileType());
                            }
                            p.add(req);
                        });
                    }
                }
                // 按照image、pdf、word的顺序排序
                Collections.sort(p);
                e.setReportStyleList(p);
                List<BaseIdCodeNameResp> codeList = new ArrayList<>();
                /*try {
                    if (e.getReportTargetsCodeSplic() != null && !"".equals(e.getReportTargetsCodeSplic())) {
                        String[] codes = e.getReportTargetsCodeSplic().split(",");
                        ProjHospitalInfo info = projHospitalInfoMapper.selectById(e.getHospitalInfoId());
                        List<Map<String, Object>> dataZbList = getAllReportZbData(info.getOrgId());
                        if (dataZbList != null && dataZbList.size() > 0) {
                            for (Map<String, Object> map : dataZbList) {
                                for (String code : codes) {
                                    if (map.get(indicatorCode).equals(code)) {
                                        BaseIdCodeNameResp resp = new BaseIdCodeNameResp();
                                        resp.setId(code);
                                        resp.setCode(code);
                                        resp.setName(map.get(indicatorTitle).toString());
                                        codeList.add(resp);
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception eee) {
                    log.error("统计报表指标转换异常", eee);
                }*/
                e.setReportTargetsCodes(codeList);
            });
        }
        Boolean isNeedAdjudicate = checkIsOpenEnableAuditVerification(dto.getProjectInfoId());
        pageInfo.setIsNeedAdjudicate(isNeedAdjudicate ? 1 : 0);
        SysUserVO currentUser = userHelper.getCurrentUser();
        // 调研阶段确认按钮是否可点击
        Boolean isSurveyFinishedBtnFlag = false;
        // 准备阶段确认按钮是否可点击
        Boolean isReadyBtnFlag = false;
        // 分配责任人按钮是否可点击
        Boolean isResponsiblePersonsBtnFlag = false;
        List<ProjProjectMember> memberList = projProjectMemberMapper.selectList(new QueryWrapper<ProjProjectMember>().eq("project_info_id", dto.getProjectInfoId()));
        if (memberList != null) {
            for (ProjProjectMember member : memberList) {
                if (member.getProjectMemberId().equals(currentUser.getSysUserId()) && (member.getProjectMemberRoleId() == 2 || member.getProjectMemberRoleId() == 3)) {
                    isSurveyFinishedBtnFlag = true;
                    isReadyBtnFlag = true;
                    isResponsiblePersonsBtnFlag = true;
                    break;
                }
            }
        }
        if (dto.getPageSource() != null && "statistical_report_design".equals(dto.getPageSource())) {
            isSurveyFinishedBtnFlag = false;
            isReadyBtnFlag = false;
            isResponsiblePersonsBtnFlag = true;
        }
        // 如果之前判定没有权限，判断是否为后端项目经理，后端项目经理也有权限
        if (Boolean.FALSE.equals(isResponsiblePersonsBtnFlag)) {
            isResponsiblePersonsBtnFlag = projProjectMemberService.isBackendLeader(dto.getProjectInfoId());
        }
        pageInfo.setIsSurveyFinishedBtnFlag(isSurveyFinishedBtnFlag);
        pageInfo.setIsReadyBtnFlag(isReadyBtnFlag);
        pageInfo.setIsResponsiblePersonsBtnFlag(isResponsiblePersonsBtnFlag);
        return Result.success(pageInfo);
    }

    /**
     * @param statisticalReportMainId
     * @return
     */
    @Override
    public ProjStatisticalReportMainUpdateResp queryStatisticalReportById(Long statisticalReportMainId) {
        ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(statisticalReportMainId);
        ProjStatisticalReportMainUpdateResp resp = new ProjStatisticalReportMainUpdateResp();
        BeanUtils.copyProperties(po, resp);
        List<ProjStatisticalCalibrationEntity> respCalibrat = projStatisticalCalibrationMapper.selectList(new QueryWrapper<ProjStatisticalCalibrationEntity>().eq("statistical_report_main_id", statisticalReportMainId));
        resp.setCalibrationEntities(respCalibrat);

        List<BaseIdCodeNameResp> codeList = new ArrayList<>();
        try {
            if (po.getReportTargetsCodes() != null && !"".equals(po.getReportTargetsCodes())) {
                String[] codes = po.getReportTargetsCodes().split(",");
                ProjHospitalInfo info = projHospitalInfoMapper.selectById(po.getHospitalInfoId());
                List<Map<String, Object>> dataZbList = getAllReportZbData(info.getOrgId());
                if (dataZbList != null && dataZbList.size() > 0) {
                    for (Map<String, Object> map : dataZbList) {
                        for (String code : codes) {
                            if (map.get(indicatorCode).equals(code)) {
                                BaseIdCodeNameResp resep = new BaseIdCodeNameResp();
                                resep.setId(code);
                                resep.setCode(code);
                                resep.setName(map.get(indicatorTitle).toString());
                                codeList.add(resep);
                            }
                        }
                    }
                }
            }
        } catch (Exception eee) {
            log.error("统计报表指标转换异常", eee);
        }
        resp.setReportTargetsCodes(codeList);

        if (po != null && po.getReportStyle() != null) {
            List<Long> list = new ArrayList<>();
            try {
                String[] styles = po.getReportStyle().split(",");
                if (styles != null) {
                    for (String style : styles) {
                        list.add(Long.parseLong(style));
                    }
                    List<ProjFileReq> p = new ArrayList<>();
                    List<ProjProjectFile> reportStyleList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", list));
                    if (reportStyleList != null && reportStyleList.size() > 0) {
                        reportStyleList.forEach(e1 -> {
                            ProjFileReq req = new ProjFileReq();
                            req.setProjectFileId(e1.getProjectFileId());
                            req.setName(e1.getFileName());
                            req.setUrl(OBSClientUtils.getTemporaryUrlLimit(e1.getFilePath(), 3600));
                            // 因为之前只允许传图片，获取不到文件类型时默认为图片
                            if (StringUtils.isBlank(e1.getFileType())) {
                                req.setFileType("image");
                            } else {
                                req.setFileType(e1.getFileType());
                            }
                            p.add(req);
                        });
                    }
                    // 按照image、pdf、word的顺序排序
                    Collections.sort(p);
                    resp.setReportStyleList(p);
                }
            } catch (Exception es) {
                resp.setReportStyleList(new ArrayList<>());
                log.error("统计报表样式转换异常", es);
            }
        }
        resp.setOperContent(po.getRemarks());
        // 操作记录
        List<ProjReportOperationRecordSelectReq> projOperateRecordList = projReportOperationRecordMapper.selectListById(statisticalReportMainId, null);
        resp.setLogList(projOperateRecordList);
        /*if (projOperateRecordList != null && projOperateRecordList.size() > 0) {
            for (ProjReportOperationRecordSelectReq projReportOperationRecordSelectReq : projOperateRecordList) {
                if (projReportOperationRecordSelectReq.getOperStatus() == 12 || projReportOperationRecordSelectReq.getOperStatus() == 13) {
                    resp.setOperContent(projReportOperationRecordSelectReq.getOperateContent());
                    break;
                }
            }
        }*/
        return resp;
    }

    /**
     * 行内修改统计报表
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateReportMainDataById(ProjStatisticalReportMainUpdateReq dto, String updateType) {
        ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (po == null) {
            return Result.fail("根据id查询不到统计报表");
        }
        List<BaseIdNameResp> listPurposeData = configReportPurposeMapper.queryPurposeData(po.getProjectInfoId());
        List<BaseIdNameResp> listDeptData = configHospitalDeptMapper.selectListByProjectInfoId(po.getProjectInfoId());
        List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
        // 封装用途id和名称
        Map<String, Long> mapPurposeData = new HashMap<>();
        listPurposeData.forEach(e -> {
            mapPurposeData.put(e.getName(), e.getId());
        });
        // 封装科室id和名称
        Map<String, Long> mapDeptData = new HashMap<>();
        listDeptData.forEach(e -> {
            mapDeptData.put(e.getName(), e.getId());
        });
        // 封装状态id和名称
        Map<Integer, String> mapStatusData = new HashMap<>();
        statusData.forEach(e -> {
            mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
        });
        // 修改名称
        if (dto.getReportName() != null) {
            po.setReportName(dto.getReportName());
        }
        // 修改使用科室
        // 使用科室，无hospitalDeptName 进行新增
        if (dto.getHospitalDeptName() != null && !"".equals(dto.getHospitalDeptName())) {
            if (mapDeptData.get(dto.getHospitalDeptName()) != null) {
                po.setHospitalDeptId(mapDeptData.get(dto.getHospitalDeptName()));
            } else {
                Long deptId = SnowFlakeUtil.getId();
                ConfigHospitalDeptEntity configHospitalDeptEntity = new ConfigHospitalDeptEntity();
                configHospitalDeptEntity.setProjectInfoId(po.getProjectInfoId());
                configHospitalDeptEntity.setHospitalDeptName(po.getHospitalDeptName());
                configHospitalDeptEntity.setHospitalDeptId(deptId);
                configHospitalDeptEntity.setOrderNo(0);
                configHospitalDeptMapper.insert(configHospitalDeptEntity);
                po.setHospitalDeptId(deptId);
            }
        }
        // 修改用途分类
        if (dto.getReportPurposeName() != null && !"".equals(dto.getReportPurposeName())) {
            if (mapPurposeData.get(dto.getReportPurposeName()) != null) {
                po.setReportPurposeId(mapPurposeData.get(dto.getReportPurposeName()));
            } else {
                Long purposeId = SnowFlakeUtil.getId();
                ConfigReportPurpose configReportPurpose = new ConfigReportPurpose();
                configReportPurpose.setProjectInfoId(po.getProjectInfoId());
                configReportPurpose.setReportPurposeId(purposeId);
                configReportPurpose.setReportPurposeName(po.getReportPurposeName());
                configReportPurpose.setOrderNo(0);
                configReportPurposeMapper.insert(configReportPurpose);
                po.setReportPurposeId(purposeId);
            }
        }
        po.setReportFrequencyId(dto.getReportFrequencyId());
        po.setReportFrequencyName(dto.getReportFrequencyName());
        // 上线必备
        if (dto.getOnlineFlag() != null) {
            po.setOnlineFlag(dto.getOnlineFlag());
        }
        // 备注
        if (dto.getRemarks() != null && !"".equals(dto.getRemarks())) {
            po.setRemarks(dto.getRemarks());
        }
        // 统计口径
        if (dto.getCalibrationEntities() != null && dto.getCalibrationEntities().size() > 0) {
            projStatisticalCalibrationMapper.delete(new QueryWrapper<ProjStatisticalCalibrationEntity>().eq("statistical_report_main_id", po.getStatisticalReportMainId()));
            dto.getCalibrationEntities().forEach(e -> {
                e.setStatisticalReportMainId(po.getStatisticalReportMainId());
                e.setStatisticalCalibrationId(SnowFlakeUtil.getId());
                e.setOrderNo(0);
                projStatisticalCalibrationMapper.insert(e);
            });
        }
        // 统计报表样式
        if (dto.getReportStyleList() != null && dto.getReportStyleList().size() > 0) {
            String reportStyle = "";
            for (ProjFileReq sysFile : dto.getReportStyleList()) {
                reportStyle += sysFile.getProjectFileId() + ",";
            }
            reportStyle = reportStyle.substring(0, reportStyle.length() - 1);
            po.setReportStyle(reportStyle);
        }
        projStatisticalReportMainMapper.updateById(po);
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(po.getProjectInfoId());
        param.setHospitalInfoId(po.getHospitalInfoId());
        param.setUserId(po.getAllocateUserId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        projTodoTaskService.projectTodoTaskInit(param);
        return Result.success();
    }

    /**
     * 申请裁定，一键申请裁定
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateApplyAdjudication(ProjStatisticalReportMainAdjudicationReq dto) {
        SysUserVO currentUser = userHelper.getCurrentUser();
        List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
        // 封装状态id和名称
        Map<Integer, String> mapStatusData = new HashMap<>();
        statusData.forEach(e -> {
            mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
        });
        Boolean b = checkIsOpenEnableAuditVerification(dto.getProjectInfoId());
        if (dto.getIsAllAdjudicationFlag() && dto.getProjectInfoId() != null) {
            List<ProjStatisticalReportMainEntity> list = projStatisticalReportMainMapper.selectList(new QueryWrapper<ProjStatisticalReportMainEntity>().eq("project_info_id", dto.getProjectInfoId()).in("report_status", Arrays.asList(1, 12)));
            if (list != null && list.size() > 0) {
                list.forEach(e -> {
                    if (b) {
                        e.setReportStatus(11);
                    } else {
                        e.setReportStatus(13);
                    }
                    e.setSurveyTime(new Timestamp(System.currentTimeMillis()));
                    e.setSurveyUserId(currentUser.getSysUserId());
                    projStatisticalReportMainMapper.updateById(e);
                    SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                    param.setProjectInfoId(e.getProjectInfoId());
                    param.setHospitalInfoId(e.getHospitalInfoId());
                    param.setUserId(e.getAllocateUserId());
                    param.setCode(DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                    projTodoTaskService.projectTodoTaskInit(param);
                    // 保存操做记录
                    extracted(e, mapStatusData);
                });
            }
        } else if (dto.getMainIds() != null && dto.getMainIds().size() > 0) {
            List<ProjStatisticalReportMainEntity> list = projStatisticalReportMainMapper.selectList(new QueryWrapper<ProjStatisticalReportMainEntity>().in("statistical_report_main_id", dto.getMainIds()).in("report_status", Arrays.asList(1, 12)));
            if (list != null && list.size() > 0) {
                list.forEach(e -> {
                    if (b) {
                        e.setReportStatus(11);
                    } else {
                        e.setReportStatus(13);
                    }
                    e.setSurveyTime(new Timestamp(System.currentTimeMillis()));
                    e.setSurveyUserId(currentUser.getSysUserId());
                    projStatisticalReportMainMapper.updateById(e);
                    SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                    param.setProjectInfoId(e.getProjectInfoId());
                    param.setHospitalInfoId(e.getHospitalInfoId());
                    param.setUserId(e.getAllocateUserId());
                    param.setCode(DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                    projTodoTaskService.projectTodoTaskInit(param);
                    // 保存操作记录
                    extracted(e, mapStatusData);
                });
            }
        } else {
            return Result.fail("请选择申请裁定的报表");
        }
        return Result.success("已申请裁定");
    }

    /**
     * 保存操作记录
     *
     * @param e
     * @param mapStatusData
     */
    private void extracted(ProjStatisticalReportMainEntity e, Map<Integer, String> mapStatusData) {
        ProjReportOperationRecordEntity projOperateRecord = new ProjReportOperationRecordEntity();
        projOperateRecord.setReportOperationRecordId(SnowFlakeUtil.getId());
        projOperateRecord.setOperStatus(e.getReportStatus());
        projOperateRecord.setStatisticalReportMainId(e.getStatisticalReportMainId());
        projOperateRecord.setOperTitle(mapStatusData.get(e.getReportStatus()));
        projOperateRecord.setOperContent(mapStatusData.get(e.getReportStatus()));

        if (Objects.equals(e.getReportStatus(), NumberEnum.NO_11.num())) {

            projOperateRecord.setOperTitle("已提交裁定申请");
            projOperateRecord.setOperContent("已提交裁定申请");

            String content = projOperateRecord.getOperContent();
            // 查询是否有审核人数据
            QueryInfoReq dtose = new QueryInfoReq();
            dtose.setProjectInfoId(e.getProjectInfoId());
            dtose.setReviewTypeCode("tjbbcd");
            UserModelAllResp userModelAllResp = configProjectReviewTypeUserService.findUserModel(dtose);
            List<UserModelResp> listUser = userModelAllResp.getUserModelRespList();
            if (listUser != null && !listUser.isEmpty()) {
                content = content + "，下一审核人:" + listUser.stream().map(UserModelResp::getUserName).collect(Collectors.joining(","));
            }
            projOperateRecord.setOperContent(content);
        }

        projReportOperationRecordMapper.insert(projOperateRecord);
    }

    /**
     * 重新申请裁定
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateReapplyAdjudication(ProjStatisticalReportMainReapplyAdjudicationReq dto) {
        ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (po != null) {
           /* List<ConfigProductionMethodResp> list = queryConfigProductionMethodData(po.getProjectInfoId());
            if (list != null && list.size() > 0) {
                for (ConfigProductionMethodResp resp : list) {
                    if (resp.getId().equals(dto.getProductionMethodId())) {
                        if (resp.getEnableFlag() == 1) {
                            po.setReportStatus(11);
                        } else {
                            po.setReportStatus(13);
                            po.setAuditUserId(userHelper.getCurrentUser().getSysUserId());
                            po.setAuditTime(new Timestamp(System.currentTimeMillis()));
                        }
                        break;
                    }
                }
            }*/
            // 2025-05-21 前端重新申请裁定，重新设置制作方式，重新设置状态为裁定中。
            //  必须后端裁定， 写重新申请裁定原因
            po.setReportStatus(11);
            po.setProductionMethodId(dto.getProductionMethodId());
            po.setSurveyTime(new Timestamp(System.currentTimeMillis()));
            po.setSurveyUserId(userHelper.getCurrentUser().getSysUserId());
            projStatisticalReportMainMapper.updateById(po);
            List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
            // 封装状态id和名称
            Map<Integer, String> mapStatusData = new HashMap<>();
            statusData.forEach(e -> {
                mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
            });
            if (StatisticalReportMethodModel.getMethedList().contains(dto.getProductionMethodId()) && po.getReportStatus() == 13) {
                try {
                    projTodoTaskService.todoTaskTotalCountSync(po.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                } catch (Exception e) {
                    log.error("同步失败", e);
                }
            }
            // 保存操纵记录
            extractedLogRulinng(po, mapStatusData, "重新申请裁定原因:" + dto.getOperContent(), "重新申请裁定");
        }
        return Result.success("重新申请裁定成功");
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public ResponseData updateStatisticalReportStatus(ProjStatisticalReportMainUpdateStatusReq dto) {
        log.error("updateStatisticalReportStatus-{}", dto);
        if (dto.getRequirementIds() != null) {
            List<ProjStatisticalReportMainEntity> pos = projStatisticalReportMainMapper.selectBatchIds(dto.getRequirementIds());
            if (pos != null) {
                for (ProjStatisticalReportMainEntity po : pos) {
                    // 已下沉
                    po.setReportStatus(31);

                    po.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    po.setFinishUserId(dto.getUserId());
                    projStatisticalReportMainMapper.updateById(po);
                    // 下沉后同步给运维平台
                    if (po.getOperationStatisticsReportId() != null && !po.getOperationStatisticsReportId().isEmpty()) {
                        updateYunweiStatus(po.getOperationStatisticsReportId(), po.getStatisticalReportMainId(), 4);
                    }
                    // 封装状态id和名称
                    List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
                    Map<Integer, String> mapStatusData = new HashMap<>();
                    statusData.forEach(e -> {
                        mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
                    });
                    try {
                        projTodoTaskService.todoTaskTotalCountSync(po.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                    } catch (Exception e) {
                        log.error("同步失败", e);
                    }
                    // 保存操做记录
                    extracted(po, mapStatusData);
                }
            }
        } else if (dto.getRequirementId() != null && dto.getReportMainId() != null) {
            ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(dto.getRequirementId());
            if (po != null) {
                if (null != dto.getIsRelease() && dto.getIsRelease()) {
                    // 已发布
                    po.setReportStatus(41);
                    projTodoTaskService.todoTaskTotalCountSync(po.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.getPlanItemCode());
                } else {
                    po.setReportStatus(NumberEnum.NO_21.num());
                }
                po.setReportMainId(dto.getReportMainId());
                po.setReportMainName(dto.getReportMainName());
                projStatisticalReportMainMapper.updateById(po);
                // 封装状态id和名称
                List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
                Map<Integer, String> mapStatusData = new HashMap<>();
                statusData.forEach(e -> {
                    mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
                });
                // 保存操做记录
                extracted(po, mapStatusData);
            }
        } else {
            return ResponseData.error("更新失败,请检查参数。");
        }
        return ResponseData.success("更新成功");
    }

    /**
     * 撤销裁定
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> updateRevokeAdjudication(ProjStatisticalReportMainReapplyAdjudicationReq dto) {
        ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        List<ProjReportOperationRecordEntity> projOperateRecordList = projReportOperationRecordMapper.selectList(new QueryWrapper<ProjReportOperationRecordEntity>().eq("statistical_report_main_id", dto.getStatisticalReportMainId()).eq("is_deleted", 0).orderByDesc("create_time"));
        if (CollectionUtil.isNotEmpty(projOperateRecordList)) {
            Integer status = projOperateRecordList.get(0).getOperStatus();
            if (status == 11 || status == 12 || status == 13) {
                po.setReportStatus(1);
            } else {
                po.setReportStatus(projOperateRecordList.get(0).getOperStatus());
            }
        } else {
            po.setReportStatus(1);
        }
        projStatisticalReportMainMapper.updateById(po);
        return Result.success("撤销裁定成功");
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public Result<String> updateAssignPersonsById(ProjStatisticalReportMainAssignPersonsReq dto) {
        if (dto.getMainIds() != null && dto.getMainIds().size() > 0) {
            List<ProjStatisticalReportMainEntity> list = projStatisticalReportMainMapper.selectList(new QueryWrapper<ProjStatisticalReportMainEntity>().in("statistical_report_main_id", dto.getMainIds()));
            list.forEach(e -> {
                e.setAllocateUserId(dto.getAllocateUserId());
                e.setPlanFinishTime(dto.getPlanFinishTime());
                e.setAllocateTime(new Timestamp(System.currentTimeMillis()));
                projStatisticalReportMainMapper.updateById(e);
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(e.getProjectInfoId());
                param.setHospitalInfoId(e.getHospitalInfoId());
                param.setUserId(dto.getAllocateUserId());
                param.setCode(DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                projTodoTaskService.projectTodoTaskInit(param);
            });
        } else {
            return Result.fail("请选择要分配责任人的数据");
        }
        return Result.success("分配成功");
    }

    /**
     * 裁定通过/裁定驳回
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateRulingAdjudication(ProjStatisticalReportMainReapplyRulingReq dto, String updateType) {
        List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
        // 封装状态id和名称
        Map<Integer, String> mapStatusData = new HashMap<>();
        statusData.forEach(e -> {
            mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
        });
        // 13通过、12驳回
        Integer flag = dto.getRulingStatus() == 1 ? 13 : 12;
        if ("single".equals(updateType)) {
            if (dto.getStatisticalReportMainId() != null) {
                ProjStatisticalReportMainEntity e = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
                if (e != null) {
                    String reportTargets = "";
                    String reportTargetsCodes = "";
                    try {
                        List<BaseIdCodeNameResp> codeSelects = dto.getReportTargetsCodes();
                        if (codeSelects != null && codeSelects.size() > 0) {
                            for (BaseIdCodeNameResp codeSelect : codeSelects) {
                                reportTargets += codeSelect.getName() + ",";
                                reportTargetsCodes += codeSelect.getId() + ",";
                            }
                            if (reportTargets.length() > 0) {
                                reportTargets = reportTargets.substring(0, reportTargets.length() - 1);
                            }
                            if (reportTargetsCodes.length() > 0) {
                                reportTargetsCodes = reportTargetsCodes.substring(0, reportTargetsCodes.length() - 1);
                            }
                        }
                    } catch (Exception de) {
                        log.error("获取指标数据失败", de);
                    }
                    e.setProductionMethodId(dto.getProductionMethodId());
                    // 裁定指标为产品功能且通过的，直接设置状态为已发布。
                    if (StatisticalReportMethodModel.getMethedList().contains(dto.getProductionMethodId()) && flag == 13) {
                        e.setReportStatus(NumberEnum.NO_41.num());
                    } else {
                        e.setReportStatus(flag);
                    }
                    e.setAuditUserId(userHelper.getCurrentUser().getSysUserId());
                    e.setAuditTime(new Timestamp(System.currentTimeMillis()));
                    e.setReportTargets(reportTargets);
                    e.setReportTargetsCodes(reportTargetsCodes);
                    e.setRulingMarks(dto.getRulingMarks());
                    e.setRemarks(dto.getOperContent());
                    e.setOperationProductId(dto.getOperationProductId());
                    projStatisticalReportMainMapper.updateById(e);
                    // 同步运维状态
                    if (e.getOperationStatisticsReportId() != null && !e.getOperationStatisticsReportId().isEmpty()) {
                        updateYunweiStatus(e.getOperationStatisticsReportId(), e.getStatisticalReportMainId(), dto.getRulingStatus() == 1 ? 2 : 3);
                    }
                    if (StatisticalReportMethodModel.getMethedList().contains(dto.getProductionMethodId()) && flag == 13) {
                        try {
                            projTodoTaskService.todoTaskTotalCountSync(e.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                        } catch (Exception sse) {
                            log.error("同步失败", sse);
                        }
                    }
                    extractedLogRulinng(e, mapStatusData, dto.getOperContent(), null);
                }
            } else {
                return Result.fail("请选择要操作的数据");
            }
        } else {
            if (dto.getMainIds() != null && dto.getMainIds().size() > 0) {
                List<ProjStatisticalReportMainEntity> list = projStatisticalReportMainMapper.selectList(new QueryWrapper<ProjStatisticalReportMainEntity>().in("statistical_report_main_id", dto.getMainIds()));
                list.forEach(e -> {
                    e.setProductionMethodId(dto.getProductionMethodId());
                    if (StatisticalReportMethodModel.getMethedList().contains(dto.getProductionMethodId()) && flag == 13) {
                        e.setReportStatus(NumberEnum.NO_41.num());
                    } else {
                        e.setReportStatus(flag);
                    }
                    e.setAuditUserId(userHelper.getCurrentUser().getSysUserId());
                    e.setAuditTime(new Timestamp(System.currentTimeMillis()));
                    e.setOperationProductId(dto.getOperationProductId());
                    projStatisticalReportMainMapper.updateById(e);
                    if (StatisticalReportMethodModel.getMethedList().contains(dto.getProductionMethodId()) && flag == 13) {
                        try {
                            projTodoTaskService.todoTaskTotalCountSync(e.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
                        } catch (Exception sse) {
                            log.error("同步失败", sse);
                        }
                    }
                    // 同步运维状态
                    if (e.getOperationStatisticsReportId() != null && !e.getOperationStatisticsReportId().isEmpty()) {
                        updateYunweiStatus(e.getOperationStatisticsReportId(), e.getStatisticalReportMainId(), dto.getRulingStatus() == 1 ? 2 : 3);
                    }
                    extractedLogRulinng(e, mapStatusData, dto.getOperContent(), null);

                });
                if (dto.getUpdateModel() != null && dto.getUpdateModel().getStatisticalReportMainId() != null) {
                    this.saveReportData(dto.getUpdateModel());
                }
            } else {
                return Result.fail("请选择要操作的数据");
            }
        }
        return Result.success("操作成功");
    }

    /**
     * 下载模板
     *
     * @param response
     * @param projectInfoId
     * @throws IOException
     */
    @Override
    public void download(HttpServletResponse response, Long projectInfoId) throws IOException {
        // 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Template");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"报表名称", "统计口径(多个以|分割)", "使用科室", "用途分类", "使用频次", "样式图片一", "样式图片二", "样式图片三", "样式图片四", "上线必备", "挂载路径"};
        CellStyle headerCellStyle = createHeaderCellStyle(workbook);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerCellStyle);
        }
        // 设置列为必填（标红）
        CellStyle requiredCellStyle = createRequiredCellStyle(workbook);
        for (int i = 0; i < headers.length; i++) {
            if (i != 6 && i != 7 && i != 8 && i != 9) {
                Cell cell = headerRow.getCell(i);
                cell.setCellStyle(requiredCellStyle);
            }
        }
        // 自动调整列宽
        int fixedWidth = 20 * 256;
        for (int i = 0; i < headers.length; i++) {
            sheet.setColumnWidth(i, fixedWidth);
        }
        List<BaseIdNameResp> listPurposeData = configReportPurposeMapper.queryPurposeData(projectInfoId);
        List<BaseIdNameResp> listDeptData = configHospitalDeptMapper.selectListByProjectInfoId(projectInfoId);
        List<BaseIdNameResp> frequencyData = queryFrequencyData();
        String[] dept = listDeptData.stream().map(BaseIdNameResp::getName).toArray(String[]::new);
        String[] purpose = listPurposeData.stream().map(BaseIdNameResp::getName).toArray(String[]::new);
        String[] frequency = frequencyData.stream().map(BaseIdNameResp::getName).toArray(String[]::new);
        String[] onlineFlag = new String[]{"是", "否"};
        // 创建使用科室
        extracted(sheet, dept, 2, 2, false);
        // 用途
        extracted(sheet, purpose, 3, 3, false);
        // 使用频次
        extracted(sheet, frequency, 4, 4, true);
        // 使用频次
        extracted(sheet, onlineFlag, 9, 9, true);
        // 设置响应头
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
        String filename = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "统计报表调研导入模板.xlsx";
        if (projProjectInfo != null) {
            filename = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + projProjectInfo.getProjectName() + "统计报表调研导入模板.xlsx";
        }
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);
        // 将工作簿写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * @param file
     * @param request
     * @return
     */
    @Override
    public Result<String> importExcel(MultipartFile file, HttpServletRequest request, ProjStatisticalReportMainImportReq param) throws IOException {
        Long hospitalInfoId = null;
        if (param.getHospitalInfoId() != null && !"".equals(param.getHospitalInfoId())) {
            hospitalInfoId = param.getHospitalInfoId();
        } else {
            List<ProjHospitalInfo> hospitalInfos = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("is_deleted", 0).eq("custom_info_id", param.getCustomInfoId()).orderByAsc("cloud_hospital_id"));
            if (hospitalInfos != null && hospitalInfos.size() > 0) {
                for (ProjHospitalInfo p : hospitalInfos) {
                    if (p.getHealthBureauFlag() == 1) {
                        hospitalInfoId = p.getHospitalInfoId();
                        break;
                    }
                }
                if (hospitalInfoId == null) {
                    hospitalInfoId = hospitalInfos.get(0).getHospitalInfoId();
                }
            }
        }
        List<ProjStatisticalReportMainAddReq> addList = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = (XSSFSheet) workbook.getSheetAt(0);
        if (sheet == null || !sheet.iterator().hasNext()) {
            return Result.fail("Excel 文件为空或无数据");
        }
        log.info("Excel 文件数据:");
        int rows = sheet.getPhysicalNumberOfRows();
        List<String> dispStrList = null;
        StringBuffer sbReturn = new StringBuffer();
        for (int i = 1; i < rows; i++) {
            dispStrList = new ArrayList<>();
            ProjStatisticalReportMainAddReq req = new ProjStatisticalReportMainAddReq();
            req.setCustomInfoId(param.getCustomInfoId());
            req.setProjectInfoId(param.getProjectInfoId());
            req.setHospitalInfoId(hospitalInfoId);
            Row row = sheet.getRow(i);
            StringBuffer sb = new StringBuffer();
            if (row.getPhysicalNumberOfCells() < 6) {
                sb.append("第" + i + "行存在必填项数据(标题为红色)未填写，重新填写后进行上传;");
                sbReturn.append(sb);
                continue;
            }
            for (int l = 0; l < 11; l++) {
                Cell cell = row.getCell(l);
                if (cell == null) {
                    continue;
                }
                log.error("第" + i + "行第" + l + "列数据为：" + printCellData(cell));
                if (l == 0) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行报表名称不能为空;");
                    } else {
                        req.setReportName(String.valueOf(printCellData(cell)));
                    }
                }
                if (l == 1) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行统计口径不能为空;");
                    } else {
                        String[] split = row.getCell(l).getStringCellValue().split("\\|");
                        List<ProjStatisticalCalibrationEntity> calibrationEntities = new ArrayList<>();
                        for (String s : split) {
                            ProjStatisticalCalibrationEntity calibrationEntity = new ProjStatisticalCalibrationEntity();
                            calibrationEntity.setStatisticalCalibrationName(s);
                            calibrationEntities.add(calibrationEntity);
                        }
                        req.setCalibrationEntities(calibrationEntities);
                    }
                }
                if (l == 2) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行使用科室不能为空;");
                    } else {
                        req.setHospitalDeptName(String.valueOf(printCellData(cell)));
                    }
                }
                if (l == 3) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行使用途分类不能为空;");
                    } else {
                        req.setReportPurposeName(String.valueOf(printCellData(cell)));
                    }
                }
                if (l == 4) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行使用频次不能为空;");
                    } else {
                        req.setReportFrequencyName(String.valueOf(printCellData(cell)));
                    }
                }
                if (l == 5) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行,样式图片一必选传入;");
                    }
                }
                if (l == 9) {
                    if (ObjectUtil.isNotEmpty(printCellData(cell))) {
                        if (!"否".equals(row.getCell(l).getStringCellValue())) {
                            req.setOnlineFlag(0);
                        } else if ("是".equals(row.getCell(l).getStringCellValue())) {
                            req.setOnlineFlag(1);
                        } else {
                            req.setOnlineFlag(1);
                        }
                    }
                }
                if (l == 10) {
                    if (ObjectUtil.isNotEmpty(printCellData(cell))) {
                        if (!"".equals(printCellData(cell))) {
                            req.setMountPath(printCellData(cell).toString());
                        } else {
                            req.setMountPath("");
                        }
                    }
                }
                if (cell.getCellType().equals(CellType.FORMULA)) {
                    // 获取公式结果
                    String formulaResult = cell.getStringCellValue();
                    dispStrList.add(formulaResult);
                }
            }
            if (ObjectUtil.isNotEmpty(sb)) {
                sbReturn.append(sb);
                continue;
            }
            // 组装图片数据
            List<ProjFileReq> list = new ArrayList<>();
            Map<String, WpsImg> getWpsImgs = WpsImgUtil.getWpsImgs(dispStrList, file);
            dispStrList.stream().forEach(v -> {
                if (StrUtil.isNotBlank(v)) {
                    WpsImg vs = getWpsImgs.get(v);
                    if (Objects.nonNull(vs)) {
                        XSSFPictureData pa = vs.getPictureData();
                        if (Objects.nonNull(pa)) {
                            ProjFileReq projFileReq = new ProjFileReq();
                            Long id = saveImageToFile(pa.getData(), vs.getImgName());
                            projFileReq.setProjectFileId(id);
                            list.add(projFileReq);
                        }
                    }
                    log.info("图片保存完成！" + getWpsImgs.get(v));
                }
            });
            req.setReportStyleList(list);
            addList.add(req);
        }
        // 批量保存
        int sucess = 0;
        if (addList != null && addList.size() > 0) {
            for (int i = 0; i < addList.size(); i++) {
                ProjStatisticalReportMainAddReq req = addList.get(i);
                req.setReportStatus(1);
                Result r = this.saveReportData(req);
                if (r.isSuccess()) {
                    sucess++;
                    log.info(req.getReportName() + "数据保存成功");
                } else {
                    sbReturn.append(req.getReportName() + r.getMsg() + ";");
                }
            }
            if (ObjectUtil.isNotEmpty(sbReturn)) {
                return Result.success("保存成功" + sucess + "条数据; 以下存在问题的数据:" + sbReturn);
            }
        } else {
            if (ObjectUtil.isNotEmpty(sbReturn)) {
                return Result.fail("保存成功0条数据; 以下存在问题的数据:" + sbReturn);
            }
        }
        projTodoTaskService.todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_STATISTICS_REPORT.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_REPORT.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.getPlanItemCode());
        return Result.success();
    }

    private static final String REGEX = "(https?:\\/\\/[^:\\/]+):\\d+(.*)";
    private static final String IP_ADDRESS_PATTERN = "^(http|https):\\/\\/" + "((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." + "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))" + "(?::\\d+)?$";

    /**
     * 报表设计
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> reportDesignById(ProjStatisticalReportMainReportDesignReq dto, String account) {
        // 1. 查看是否有报表主键，有则跳转修改页面
        String pageUrlParamer = "statisticsReportCreate";
        String pageUrlEnd = "";
        ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (po.getReportMainId() != null && !"".equals(po.getReportMainId())) {
            pageUrlParamer = "statisticsReportUpdate";
            pageUrlEnd = "&reportMainId=" + po.getReportMainId();
        } else {
            // 没有则跳转新增页面
            pageUrlParamer = "statisticsReportCreate";
            pageUrlEnd = "&requirementId=" + po.getStatisticalReportMainId();
        }
        // 查询项目
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(po.getProjectInfoId());
        // 查询医院信息
        ProjHospitalInfo projHospitalInfoVO = projHospitalInfoMapper.selectById(po.getHospitalInfoId());
        if (projHospitalInfoVO.getCloudHospitalId() == null || projHospitalInfoVO.getOrgId() == null) {
            return Result.fail("请先完成启动阶段域名申请");
        }
        OldUserNewReport oldSysUserResp = new OldUserNewReport();
        // 当前登录人信息
        if (account != null && !"".equals(account)) {
            oldSysUserResp = oldUserReportMapper.getUserAccount(account);
        } else {
            SysUserVO currentUser = userHelper.getCurrentUser();
            oldSysUserResp = oldUserReportMapper.getUserAccount(currentUser.getAccount());
        }
        //  2. 组装返回路径
        SysFile file = new SysFile();
        file.setFileCode(pageUrlParamer);
        Result<SysFile> sysFileResult = sysFileService.selectFile(file);
        String filePath = sysFileResult.getData().getFilePath();
        // 地址信息替换
        filePath = filePath.replace("#{projectId}", projProjectInfo.getProjectInfoId().toString());
        filePath = filePath.replace("#{projectName}", projProjectInfo.getProjectName());
        filePath = filePath.replace("#{hisOrgId}", projHospitalInfoVO.getOrgId().toString());
        filePath = filePath.replace("#{hospitalId}", projHospitalInfoVO.getCloudHospitalId().toString());
        filePath = filePath.replace("#{hospitalName}", projHospitalInfoVO.getHospitalName());
        filePath = filePath.replace("#{userId}", oldSysUserResp.getYyId());
        filePath = filePath.replace("#{userName}", oldSysUserResp.getUserName());

        Pattern validator = Pattern.compile(IP_ADDRESS_PATTERN);
        String newPreProductNetwork = projHospitalInfoVO.getCloudDomain();
        Boolean b = validator.matcher(newPreProductNetwork).matches();
        if (!b) {
            Pattern pattern1 = Pattern.compile(REGEX);
            Matcher matcher1 = pattern1.matcher(newPreProductNetwork);
            if (matcher1.matches()) {
                newPreProductNetwork = matcher1.group(1) + matcher1.group(2);
            }
        }
        filePath = filePath.replace("#{preProductNetwork}", newPreProductNetwork);
        filePath = filePath.replace("#{host}", newPreProductNetwork);
        if (oldSysUserResp != null && oldSysUserResp.getLevel() != null) {
            filePath = filePath.replace("#{level}", oldSysUserResp.getLevel());
        } else {
            filePath = filePath.replace("#{level}", "");
        }
        filePath = filePath + pageUrlEnd;
        String token = Md5Util.md5(filePath);
        redisUtil.set(token, filePath, 60);
        filePath = filePath + "&token=" + token;
        return Result.success(filePath);
    }

    /**
     * 报表平台查看单个报表数据
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseData getReportById(ProjStatisticalReportMainUpdateStatusReq dto) {
        if (dto.getRequirementId() == null || "".equals(dto.getRequirementId())) {
            return ResponseData.error("需求单主键不能为空");
        }
        log.info("报表平台查看单个报表数据===>" + dto);
        ProjStatisticalReportMainPageReq reqe = new ProjStatisticalReportMainPageReq();
        reqe.setReportMainId(dto.getRequirementId());
        List<ProjStatisticalReportMainSelectResp> reportPage = projStatisticalReportMainMapper.findReportPage(reqe);
        // 查询出的数据查询图片对照表， 查询图片
        if (reportPage != null && reportPage.size() > 0) {
            reportPage.forEach(e -> {
                try {
                    String[] codes = e.getReportTargetsCodeSplic().split(",");
                    List<String> reportCodes = Arrays.asList(codes);
                    e.setReportTargetsList(reportCodes);
                } catch (Exception ees) {
                    log.error(ees.getMessage());
                }
                e.setOnlineFlagBoolean(e.getOnlineFlag() == 1);
                if (e.getCalibrationIdFirstFour() != null && !"".equals(e.getCalibrationIdFirstFour())) {
                    e.setCalibrationIdFirstFour(e.getCalibrationIdFirstFour().replaceAll("==", "\n"));
                }
                if (e.getStatisticalCalibrationNames() != null && !"".equals(e.getStatisticalCalibrationNames())) {
                    e.setStatisticalCalibrationNames(e.getStatisticalCalibrationNames().replaceAll("==", "\n"));
                }
                List<Long> list = new ArrayList<>();
                try {
                    String[] styles = e.getReportStyle().split(",");
                    if (styles != null) {
                        for (String style : styles) {
                            list.add(Long.parseLong(style));
                        }
                    }
                    List<ProjFileReq> p = new ArrayList<>();
                    List<ProjProjectFile> reportStyleList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", list));
                    if (reportStyleList != null && reportStyleList.size() > 0) {
                        reportStyleList.forEach(e1 -> {
                            ProjFileReq req = new ProjFileReq();
                            req.setProjectFileId(e1.getProjectFileId());
                            req.setName(e1.getFileName());
                            req.setUrl(OBSClientUtils.getTemporaryUrlLimit(e1.getFilePath(), 3600));
                            // 因为之前只允许传图片，获取不到文件类型时默认为图片
                            if (StringUtils.isBlank(e1.getFileType())) {
                                req.setFileType("image");
                            } else {
                                req.setFileType(e1.getFileType());
                            }
                            p.add(req);
                        });
                    }
                    // 按照image、pdf、word的顺序排序
                    Collections.sort(p);
                    e.setReportStyleList(p);
                } catch (Exception ed) {
                    e.setReportStyleList(new ArrayList<>());
                    log.error("报表平台查看单个报表数据异常===>" + ed);
                }
            });
        }
        return ResponseData.success(reportPage);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public ResponseData getReportByParamer(ProjStatisticalReportMainUpdateStatusReq dto) {
        // 根据医院id查询项目id
        List<ProjHospitalInfo> hospitalInfo = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("cloud_hospital_id", dto.getHospitalId()));
        List<Long> hospitalIds = new ArrayList<>();
        if (hospitalInfo != null && hospitalInfo.size() > 0) {
            for (ProjHospitalInfo projHospitalInfo : hospitalInfo) {
                hospitalIds.add(projHospitalInfo.getHospitalInfoId());
            }
        }
        List<ProjStatisticalReportMainSelectResp> reportPage = new ArrayList<>();
        if (hospitalIds != null) {
            ProjStatisticalReportMainPageReq reqe = new ProjStatisticalReportMainPageReq();
            reqe.setHospitalInfoIds(hospitalIds);
            if (dto.getPageNum() != null && dto.getPageNum() > 0) {
                reqe.setPageNum(dto.getPageNum());
            } else {
                reqe.setPageNum(1);
            }
            if (dto.getPageSize() != null && dto.getPageSize() > 0) {
                reqe.setPageSize(dto.getPageSize());
            } else {
                reqe.setPageSize(10);
            }
            if (dto.getStatisticalCalibrationNames() != null && !"".equals(dto.getStatisticalCalibrationNames())) {
                reqe.setReportNameORcalibration(dto.getStatisticalCalibrationNames());
            }
            reportPage = PageHelperUtil.queryPage(reqe.getPageNum(), reqe.getPageSize(), page -> projStatisticalReportMainMapper.findReportPage(reqe));
        }
        PageInfo<ProjStatisticalReportMainSelectResp> pageInfo = new PageInfo<>(reportPage);
        // 查询出的数据查询图片对照表， 查询图片
        if (reportPage != null && reportPage.size() > 0) {
            reportPage.forEach(e -> {
                e.setOnlineFlagBoolean(e.getOnlineFlag() == 1);
                if (e.getCalibrationIdFirstFour() != null && !"".equals(e.getCalibrationIdFirstFour())) {
                    e.setCalibrationIdFirstFour(e.getCalibrationIdFirstFour().replaceAll("==", "\n"));
                }
                if (e.getStatisticalCalibrationNames() != null && !"".equals(e.getStatisticalCalibrationNames())) {
                    e.setStatisticalCalibrationNames(e.getStatisticalCalibrationNames().replaceAll("==", "\n"));
                }
                List<Long> list = new ArrayList<>();
                try {
                    String[] styles = e.getReportStyle().split(",");
                    if (styles != null) {
                        for (String style : styles) {
                            list.add(Long.parseLong(style));
                        }
                    }
                    List<ProjFileReq> p = new ArrayList<>();
                    List<ProjProjectFile> reportStyleList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id", list));
                    if (reportStyleList != null && reportStyleList.size() > 0) {
                        reportStyleList.forEach(e1 -> {
                            ProjFileReq req = new ProjFileReq();
                            req.setProjectFileId(e1.getProjectFileId());
                            req.setName(e1.getFileName());
                            req.setUrl(OBSClientUtils.getTemporaryUrlLimit(e1.getFilePath(), 3600));
                            // 因为之前只允许传图片，获取不到文件类型时默认为图片
                            if (StringUtils.isBlank(e1.getFileType())) {
                                req.setFileType("image");
                            } else {
                                req.setFileType(e1.getFileType());
                            }
                            p.add(req);
                        });
                    }
                    // 按照image、pdf、word的顺序排序
                    Collections.sort(p);
                    e.setReportStyleList(p);
                } catch (Exception ss) {
                    e.setReportStyleList(new ArrayList<>());
                    log.error("报表平台查看单个报表数据异常===>" + ss);
                }
            });
        }
        return ResponseData.success(pageInfo);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public ResponseData deleteRelationshipByParamer(ProjStatisticalReportMainUpdateStatusReq dto) {
        if (dto.getRequirementIds() != null) {
            List<ProjStatisticalReportMainEntity> pos = projStatisticalReportMainMapper.selectBatchIds(dto.getRequirementIds());
            for (ProjStatisticalReportMainEntity po : pos) {
                po.setReportMainId("");
                po.setReportMainName("");
                po.setReportStatus(13);
                projStatisticalReportMainMapper.updateById(po);
            }
        }
        return ResponseData.success();
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public Result<String> reportReviewById(ProjStatisticalReportMainReportDesignReq dto) {
        // 1. 查看是否有报表主键，有则跳转修改页面
        ProjStatisticalReportMainEntity po = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        // 查询项目
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(po.getProjectInfoId());
        // 查询医院信息
        ProjHospitalInfo projHospitalInfoVO = projHospitalInfoMapper.selectById(po.getHospitalInfoId());
        if (projHospitalInfoVO.getCloudHospitalId() == null || projHospitalInfoVO.getOrgId() == null) {
            return Result.fail("请先完成启动阶段域名申请");
        }
        // 当前登录人信息
        SysUserVO currentUser = userHelper.getCurrentUser();
        OldUserNewReport oldSysUserResp = oldUserReportMapper.getUserAccount(currentUser.getAccount());
        //  2. 组装返回路径
        SysFile file = new SysFile();
        file.setFileCode("statisticsReportReview");
        Result<SysFile> sysFileResult = sysFileService.selectFile(file);
        String filePath = sysFileResult.getData().getFilePath();
        // 地址信息替换
        filePath = filePath.replace("#{projectId}", projProjectInfo.getProjectInfoId().toString());
        filePath = filePath.replace("#{projectName}", projProjectInfo.getProjectName());
        filePath = filePath.replace("#{hisOrgId}", projHospitalInfoVO.getOrgId().toString());
        filePath = filePath.replace("#{hospitalId}", projHospitalInfoVO.getCloudHospitalId().toString());
        filePath = filePath.replace("#{hospitalName}", projHospitalInfoVO.getHospitalName());
        filePath = filePath.replace("#{userId}", currentUser.getUserYunyingId());
        filePath = filePath.replace("#{userName}", currentUser.getUserName());

        Pattern validator = Pattern.compile(IP_ADDRESS_PATTERN);
        String newPreProductNetwork = projHospitalInfoVO.getCloudDomain();
        Boolean b = validator.matcher(newPreProductNetwork).matches();
        if (!b) {
            Pattern pattern1 = Pattern.compile(REGEX);
            Matcher matcher1 = pattern1.matcher(newPreProductNetwork);
            if (matcher1.matches()) {
                newPreProductNetwork = matcher1.group(1) + matcher1.group(2);
            }
        }
        filePath = filePath.replace("#{preProductNetwork}", newPreProductNetwork);
        filePath = filePath.replace("#{host}", newPreProductNetwork);

        if (oldSysUserResp != null && oldSysUserResp.getLevel() != null) {
            filePath = filePath.replace("#{level}", oldSysUserResp.getLevel());
        } else {
            filePath = filePath.replace("#{level}", "");
        }
        filePath = filePath + "&reportMainId=" + po.getReportMainId();
        String token = Md5Util.md5(filePath);
        redisUtil.set(token, filePath, 60);
        filePath = filePath + "&token=" + token;
        return Result.success(filePath);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public ResponseData selectHospitalOnlineProductByParamer(ProjStatisticalReportMainUpdateStatusReq dto) {
        List<ProjProductResp> productList = projStatisticalReportMainMapper.getProductList(dto.getHospitalId());
        List<String> codeList = new ArrayList<>();
        if (productList != null && productList.size() > 0) {
            for (int i = 0; i < productList.size(); i++) {
                codeList.add(productList.get(i).getPtProductCode());
            }
        }
        return ResponseData.success(codeList);
    }

    /**
     * 获取报表指标详情数据
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseLableValueResp> getAllReportZbDetailData(Long projectInfoId) {
        List<BaseLableValueResp> relist = new ArrayList<>();
        try {
            ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
            List<ProjHospitalInfo> hospitalInfo = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", projProjectInfo.getCustomInfoId()).eq("hos_project_type", projProjectInfo.getProjectType()));
            List<Map<String, Object>> list = getAllReportZbData(hospitalInfo.get(0).getOrgId());
            if (list != null && list.size() > 0) {
                for (Map<String, Object> map : list) {
                    BaseLableValueResp respAll = new BaseLableValueResp();
                    BaseIdCodeNameResp resp = new BaseIdCodeNameResp();
                    resp.setId(map.get(indicatorCode).toString());
                    resp.setCode(map.get(indicatorCode).toString());
                    resp.setName(map.get(indicatorTitle).toString());
                    respAll.setValue(resp);
                    respAll.setLabel(map.get(indicatorTitle).toString());
                    relist.add(respAll);
                }
            }
        } catch (Exception e) {
            log.error("获取报表指标详情数据异常===>" + e);
        }
        return relist;
    }

    /**
     * 根据医院id获取项目信息
     *
     * @param cloudHospitalId
     * @return
     */
    @Override
    public ProjProjectInfo getProjectInfoByCloudHospitalId(Long cloudHospitalId) {
        ProjProjectInfo returnProj = null;
        List<ProjProjectInfo> projInfos = projProjectInfoMapper.selectProjectListByHospitalId(cloudHospitalId);
        if (projInfos != null && projInfos.size() > 0) {
            for (ProjProjectInfo projProjectInfo : projInfos) {
                if (projProjectInfo.getHisFlag() == 1) {
                    returnProj = projProjectInfo;
                }
            }
            if (returnProj == null) {
                returnProj = projInfos.get(0);
            }
        }
        return returnProj;
    }

    @Override
    public Result<String> getStatisticsReportAddUrl(String operationStatisticsReportId, String account) {
        if (StrUtil.isEmpty(operationStatisticsReportId)) {
            return Result.fail("operationStatisticsReportId不能为空");
        }
        try {
            ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectByOperationStatisticsReportId(operationStatisticsReportId);
            if (entity == null) {
                log.error("根据operationStatisticsReportId查询不到统计报表主数据, operationStatisticsReportId: {}", operationStatisticsReportId);
                return Result.fail("根据operationStatisticsReportId查询不到统计报表主数据");
            }
            ProjStatisticalReportMainReportDesignReq req = new ProjStatisticalReportMainReportDesignReq();
            req.setStatisticalReportMainId(entity.getStatisticalReportMainId());
            return this.reportDesignById(req, account);
        } catch (Exception e) {
            log.error("查询统计报表主数据失败, operationStatisticsReportId: {}, 错误信息: {}", operationStatisticsReportId, e.getMessage());
            return Result.fail("查询统计报表主数据失败");
        }
    }

    /**
     * 运维平台传入文件交付保存
     *
     * @param req
     * @return
     */
    @Override
    public List<ProjFileReq> getFileNames(ProjStatisticalStyleFileReq req) {
        List<ProjFileReq> list = new ArrayList<>();
        if (req.getFiles() != null && req.getFiles().size() > 0) {
            list = req.getFiles();
            for (ProjFileReq fileReq : list) {
                Long id = SnowFlakeUtil.getId();
                fileReq.setProjectFileId(id);
                ProjProjectFile projProjectFile = new ProjProjectFile();
                projProjectFile.setProjectFileId(id);
                projProjectFile.setFileName(fileReq.getName());
                projProjectFile.setFilePath(fileReq.getUrl());
                projProjectFile.setProjectInfoId(0L);
                projProjectFile.setProjectStageCode("");
                projProjectFile.setMilestoneNodeCode("");
                projProjectFile.setFileDesc("");
                projProjectFileMapper.insert(projProjectFile);
            }
        }
        return list;
    }

    /**
     * 下沉
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> jfDevolveDelivery(ProjStatisticalReportMainReapplyAdjudicationReq dto) {
        // 当前人是否有下沉权限
        // 当前登录人信息
        SysUserVO currentUser = userHelper.getCurrentUser();
        OldUserNewReport oldSysUserResp = oldUserReportMapper.getUserAccount(currentUser.getAccount());
        if (oldSysUserResp == null || !"lv2".equals(oldSysUserResp.getLevel())) {
            return Result.fail("用户没有报表下沉权限");
        }
        ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (entity != null && (entity.getReportMainId() == null || "".equals(entity.getReportMainId()))) {
            return Result.fail("报表没有进行对照，请对照后进行发布");
        }
        return this.devolveDelivery(entity);
    }

    /**
     * 发布
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> jfSaveMenuDelivery(ProjStatisticalReportMainDeliveryReq dto) {
        SysUserVO currentUser = userHelper.getCurrentUser();
        OldUserNewReport oldSysUserResp = oldUserReportMapper.getUserAccount(currentUser.getAccount());
        if (oldSysUserResp == null || !"lv2".equals(oldSysUserResp.getLevel())) {
            return Result.fail("用户没有发布报表权限");
        }
        return this.saveMenuDelivery(dto);
    }

    @Override
    public Result<List<MsunReportMain>> selectReprtListDelivery(ProjStatisticalReportMainDeliveryReq dto) {
        List<MsunReportMain> mainList = this.deliveryGetReport(dto.getStatisticalReportMainId());
        return Result.success(mainList);
    }

    /**
     * 查询报表平台云健康菜单
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<MenuVO>> jfFindMenuByHosId(ProjStatisticalReportMainDeliveryReq dto) {
        List<MenuVO> klist = this.findMenuByHosId(dto.getStatisticalReportMainId());
        return Result.success(klist);
    }

    /**
     * 报表对比
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result reportComparison(ProjStatisticalReportComparisonReq dto) {
        ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (entity != null) {
            entity.setReportMainId(dto.getReportMainId());
            entity.setReportStatus(NumberEnum.NO_21.num());
            compareReportMain(entity);
            projStatisticalReportMainMapper.updateById(entity);
        }
        return Result.success();
    }

    /**
     * 制作完成
     *
     * @param dto
     * @return
     */
    @Override
    public Result makeFinishReport(ProjStatisticalReportMainReportDesignReq dto) {
        ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (entity != null) {
            entity.setReportStatus(NumberEnum.NO_22.num());
            projStatisticalReportMainMapper.updateById(entity);
            projTodoTaskService.todoTaskTotalCountSync(entity.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_STATISTICS_RELEASE.getPlanItemCode());

            // 封装状态id和名称
            List<BaseIdNameResp> statusData = dictReportFrequencyMapper.queryStausData();
            Map<Integer, String> mapStatusData = new HashMap<>();
            statusData.forEach(e -> {
                mapStatusData.put(Math.toIntExact(e.getId()), e.getName());
            });
            // 保存操做记录
            extracted(entity, mapStatusData);
        }
        return Result.success();
    }

    /**
     * 报表导出
     *
     * @param response
     * @param dto
     */
    @Override
    public void reportExportExcel(HttpServletResponse response, ProjStatisticalReportMainPageReq dto) {
        // 查询导出的数据
        List<ProjStatisticalReportMainExportResp> reportPage = projStatisticalReportMainMapper.findExportReportPage(dto);
        if (reportPage != null && !reportPage.isEmpty()) {
            reportPage.forEach(projSurveyReportResp -> {
                RejectReasonAndCount rejectReasonAndCount = this.selectReasonAndCountById(projSurveyReportResp.getStatisticalReportMainId());
                projSurveyReportResp.setRejectCount(rejectReasonAndCount.getRejectCount());
                projSurveyReportResp.setRejectReason(rejectReasonAndCount.getRejectReason());
            });
        }


        // 导出数据
        try (ExcelWriter excelWriter = EasyExcelFactory.write(getOutputStream("报表列表导出.xlsx", response),
                ProjStatisticalReportMainExportResp.class).excludeColumnFieldNames(Collections.singleton("region")).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("报表列表导出").build();
            // 导出文件
            excelWriter.write(reportPage, writeSheet);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 构建输出流
     *
     * @param fileName：文件名称
     * @param response：
     * @return
     * @throws Exception
     */
    private OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 告知浏览器下载，下载附件的形式
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        return response.getOutputStream();
    }

    private Object printCellData(Cell cell) {
        // 获取单元格的类型并打印对应的内容
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return cell.getStringCellValue();
        }
    }

    /**
     * 将图片字节数据保存到指定的文件路径
     */
    private Long saveImageToFile(byte[] imageBytes, String imageFileName) {
        // 将 byte 数组转换为 InputStream
        InputStream inputStream = new ByteArrayInputStream(imageBytes);
        try {
            String objectKey = prePath + StrUtil.SLASH + "report" + new SimpleDateFormat("yyyyMMddHHmmss").format(System.currentTimeMillis()) + StrUtil.SLASH + imageFileName;
            log.info("图片保存路径：" + objectKey);
            PutObjectResult p = OBSClientUtils.uploadFileStream(inputStream, objectKey, true);
            log.info("图片保存路径：" + p);
            Long id = SnowFlakeUtil.getId();
            UploadFileReq req = new UploadFileReq();
            if (StrUtil.isNotBlank(p.getObjectKey())) {
                //处理从项目工具入口上传附件时，没有projectInfoId和milestoneCode的问题
                if (ObjectUtil.isEmpty(req.getProjectInfoId())) {
                    req.setProjectInfoId(Long.valueOf(DateUtil.getHourMinuteSecondString()));
                }
                req.setMilestoneCode("stage_survey");
                ProjProjectFileExtend projectFile = new ProjProjectFileExtend(req, "", imageFileName, id, p.getObjectKey(), "");
                if (req.getIsPublic()) {
                    projectFile.setFileUrl(URLDecoder.decode(p.getObjectUrl()));
                } else {
                    projectFile.setFileUrl(OBSClientUtils.getTemporaryUrlLimit(objectKey, 3600));
                }
                projProjectFileMapper.insert(projectFile);
            }
            return id;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private CellStyle createHeaderCellStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    private CellStyle createRequiredCellStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    /**
     * 操作记录
     *
     * @param e
     * @param mapStatusData
     * @param operContent
     */
    private void extractedLogRulinng(ProjStatisticalReportMainEntity e, Map<Integer, String> mapStatusData, String operContent, String operTitle) {
        ProjReportOperationRecordEntity projOperateRecord = new ProjReportOperationRecordEntity();
        projOperateRecord.setReportOperationRecordId(SnowFlakeUtil.getId());
        projOperateRecord.setOperStatus(e.getReportStatus());
        projOperateRecord.setStatisticalReportMainId(e.getStatisticalReportMainId());
        projOperateRecord.setOperTitle(mapStatusData.get(e.getReportStatus()));
        projOperateRecord.setOperContent(mapStatusData.get(e.getReportStatus()));
        if (operContent != null && !operContent.isEmpty()) {
            projOperateRecord.setOperContent("备注:" + operContent);
        }
        if (Objects.equals(e.getReportStatus(), NumberEnum.NO_11.num())) {

            projOperateRecord.setOperTitle("已提交裁定申请");
            projOperateRecord.setOperContent("已提交裁定申请");

            String content = projOperateRecord.getOperContent();
            // 查询是否有审核人数据
            QueryInfoReq dtose = new QueryInfoReq();
            dtose.setProjectInfoId(e.getProjectInfoId());
            dtose.setReviewTypeCode("tjbbcd");
            UserModelAllResp userModelAllResp = configProjectReviewTypeUserService.findUserModel(dtose);
            List<UserModelResp> listUser = userModelAllResp.getUserModelRespList();
            if (listUser != null && !listUser.isEmpty()) {
                content = content + "，下一审核人:" + listUser.stream().map(UserModelResp::getUserName).collect(Collectors.joining(","));
            }
            projOperateRecord.setOperContent(content);
        }
        if (operTitle != null && !operTitle.isEmpty()) {
            projOperateRecord.setOperTitle(operTitle);
            projOperateRecord.setOperContent(operContent);
        }
        projReportOperationRecordMapper.insert(projOperateRecord);
    }

    /**
     * 是否开启统计报表裁定
     * 0 不开启/1 按项目开启：根据表控制/2 全部开启'
     *
     * @param projectInfoId
     * @return
     */
    private Boolean checkIsOpenEnableAuditVerification(Long projectInfoId) {
        if (projectInfoId == null) {
            return true;
        }
        String configCode = "calibrationLimit";
        Integer limitCustomer = 1;
        SysConfig calibrationLimit = sysConfigMapper.selectConfigByName(configCode);
        if (calibrationLimit != null) {
            try {
                limitCustomer = Integer.parseInt(calibrationLimit.getConfigValue());
            } catch (Exception e) {
                log.error("获取配置表数据异常");
            }
        }
        if (limitCustomer == 2) {
            return true;
        } else if (limitCustomer == 1) {
            List<ProjReportAdjudicationLimitEntity> list = projReportAdjudicationLimitMapper.selectList(new QueryWrapper<ProjReportAdjudicationLimitEntity>().eq("project_info_id", projectInfoId));
            if (list != null && list.size() > 0) {
                for (ProjReportAdjudicationLimitEntity projReportAdjudicationLimitEntity : list) {
                    if (projReportAdjudicationLimitEntity.getAdjudicationFlag() == 1) {
                        return true;
                    }
                }
            } else {
                return false;
            }
        } else if (limitCustomer == 0) {
            return false;
        }
        return false;
    }

    private List<Map<String, Object>> getAllReportZbData(Long hisOrgId) {
        List<Map<String, Object>> data = new ArrayList<>();
        String tableUrlValuePrivate = getString(tableUrlValue);
        String url = tableUrlValuePrivate + "msun-reportweb-app-center/indicatorNew/getProductIndicator";
        Map<String, Object> param = new HashMap<>();
        param.put("hisOrgId", hisOrgId);
        try {
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("调用报表平台指标数据返回参数=====> {}", stringObjectMap);
            if (stringObjectMap != null && (Boolean) stringObjectMap.get("success")) {
                data = (List<Map<String, Object>>) stringObjectMap.get("data");
            }
        } catch (IOException e) {
            log.error("调用报表平台指标数据返回异常：==" + e);
        }
        return data;
    }

    private void updateYunweiStatus(String feedbackId, Long reportId, Integer status) {
        //回写运维平台反馈单和报表对照关系
        try {
            YunweiUpdateStatisReportDTO feedbackDto = new YunweiUpdateStatisReportDTO();
            feedbackDto.setFeedbackId(Long.valueOf(feedbackId));
            feedbackDto.setReportId(reportId);
            feedbackDto.setStatus(status);
            String respStr = knowledgeFeignClient.acceptReportStatus(feedbackDto, getAuthorization());
            log.info("======回写运维平台反馈单和报表对照关系返回值======{}", respStr);
            ResponseResult checkResp = JSON.parseObject(respStr, ResponseResult.class);
            if (!checkResp.isSuccess()) {
                throw new CustomException("回写运维平台反馈单和接口对照关系异常:" + checkResp.getMessage());
            }
        } catch (Exception e) {
            log.error("回写运维平台反馈单和统计报表对照关系发生错误：{}", e.getMessage());
        }
    }

    private String getAuthorization() {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串 【appId、publicKey 为运维分发】
        String old = "appid=" + knowledgeAppId + "&appsecret=" + knowledgePublicKey + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, knowledgePublicKey);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        String headers = "appid=" + knowledgeAppId + ";sign=" + sign;
        return headers;
    }

    /**
     * 报表下沉
     *
     * @param entity
     * @return
     */
    private Result<String> devolveDelivery(ProjStatisticalReportMainEntity entity) {
        ProjHospitalInfo info = projHospitalInfoMapper.selectById(entity.getHospitalInfoId());
        if (info != null && info.getHospitalOpenStatus() < NumberEnum.NO_21.num()) {
            return Result.fail("环境未开通，请在环境部署后进行下沉");
        }
        String tableUrlValuePrivate = tableUrlValue;
        tableUrlValuePrivate = getString(tableUrlValuePrivate);
        String url = tableUrlValuePrivate + "msun-reportweb-app-center/designer/msunReportMain/devolveDelivery";
        Map<String, Object> param = new HashMap<>();
        param.put("hisOrgId", info.getOrgId());
        param.put("hisOrgName", info.getHospitalName());
        param.put("hospitalId", info.getCloudHospitalId());
        param.put("hospitalName", info.getHospitalName());
        param.put("preProductNetwork", info.getCloudDomain());
        param.put("reportMainIds", entity.getReportMainId());
        try {
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("调用报表平台报表下沉返回参数=====> {}", stringObjectMap);
            if (stringObjectMap != null && (Boolean) stringObjectMap.get("success")) {
                return Result.success();
            }
            if (stringObjectMap != null && !(Boolean) stringObjectMap.get("success")) {
                throw new CustomException(String.valueOf(stringObjectMap.get("message")));
            }
        } catch (IOException e) {
            log.error("调用报表平台报表下沉返回参数：==" + e);
            throw new CustomException("调用报表平台报表下沉异常");
        }
        return Result.success();
    }

    /**
     * 查询菜单
     *
     * @param statisticalReportMainId
     * @return
     */
    private List<MenuVO> findMenuByHosId(Long statisticalReportMainId) {
        ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectById(statisticalReportMainId);
        ProjHospitalInfo info = projHospitalInfoMapper.selectById(entity.getHospitalInfoId());
        List<MenuVO> data = new ArrayList<>();
        String tableUrlValuePrivate = tableUrlValue;
        tableUrlValuePrivate = getString(tableUrlValuePrivate);
        String url = tableUrlValuePrivate + "msun-reportweb-app-center/designer/msunReportMain/findMenuByHosId";
        Map<String, Object> param = new HashMap<>();
        param.put("hisOrgId", info.getOrgId());
        param.put("hospitalId", info.getCloudHospitalId());
        try {
            log.error("调用报表平台云健康菜单入参数=====> {}", param);
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("调用报表平台云健康菜单返回参数=====> {}", stringObjectMap);
            if (stringObjectMap != null && (Boolean) stringObjectMap.get("success")) {
                Object dataObj = stringObjectMap.get("data");
                if (dataObj instanceof List<?>) {
                    data = JSONObject.parseArray(JSONObject.toJSONString(dataObj), MenuVO.class);
                } else {
                    System.err.println("data 字段缺失或类型不正确");
                }
            }
        } catch (Exception e) {
            log.error("调用报表平台云健康菜单返回参数：==" + e);
        }
        return data;
    }

    /**
     * 发布
     *
     * @param dto
     * @return
     */
    private Result<String> saveMenuDelivery(ProjStatisticalReportMainDeliveryReq dto) {
        ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectById(dto.getStatisticalReportMainId());
        if (entity != null && (entity.getReportMainId() == null || "".equals(entity.getReportMainId()))) {
            return Result.fail("报表没有进行对照，请对照后进行发布");
        }
        ProjHospitalInfo info = projHospitalInfoMapper.selectById(entity.getHospitalInfoId());
        if (info != null && info.getHospitalOpenStatus() < NumberEnum.NO_21.num()) {
            return Result.fail("环境未开通，不能进行发布，请在环境部署后进行发布操作");
        }
        String tableUrlValuePrivate = tableUrlValue;
        tableUrlValuePrivate = getString(tableUrlValuePrivate);
        String url = tableUrlValuePrivate + "msun-reportweb-app-center/designer/msunReportMain/saveMenuDelivery";
        Map<String, Object> param = new HashMap<>();
        param.put("hisOrgId", info.getOrgId());
        param.put("hisOrgName", info.getHospitalName());
        param.put("hospitalId", info.getCloudHospitalId());
        param.put("hospitalName", info.getHospitalName());
        param.put("preProductNetwork", info.getCloudDomain());
        param.put("reportMainId", entity.getReportMainId());
        // 报表名称+年月日时分秒
        String menuCode = PinyinUtils.getFirstSpell(dto.getMenuName()) + System.currentTimeMillis();
        param.put("menuCode", menuCode);
        param.put("menuName", dto.getMenuName());
        param.put("parentId", dto.getParentId());
        // external?type=report&code=DXKEDP1746686992825&url=%2Freportweb%2F%23%2FreportDisplay%3FreportMainId%3D2af08c580c5d4330b8227819ad8e9327
        String urlUee = "/reportweb/#/reportDisplay?reportMainId=" + entity.getReportMainId();
        StringBuffer sb = new StringBuffer("external?type=report&code=").append(menuCode).append("&url=").append(ProjProductTaskServiceImpl.encodeURIComponent(urlUee));
        param.put("skipPath", sb.toString());
        // 默认空
        param.put("sortOrder", "");
        try {
            log.error("调用报表平台发布入参数=====> {}", param);
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("调用报表平台发布入返回参数=====> {}", stringObjectMap);
            if (stringObjectMap != null && (Boolean) stringObjectMap.get("success")) {
                return Result.success();
            }
            if (stringObjectMap != null && !(Boolean) stringObjectMap.get("success")) {
                throw new CustomException(String.valueOf(stringObjectMap.get("message")));
            }
        } catch (IOException e) {
            log.error("调用报表平台发布入返回参数：==" + e);
            throw new CustomException("调用报表平台报表发布异常");
        }
        return Result.success();
    }

    /**
     * 查询报表
     *
     * @param statisticalReportMainId
     * @return
     */
    private List<MsunReportMain> deliveryGetReport(Long statisticalReportMainId) {
        ProjStatisticalReportMainEntity entity = projStatisticalReportMainMapper.selectById(statisticalReportMainId);
        ProjHospitalInfo info = projHospitalInfoMapper.selectById(entity.getHospitalInfoId());
        List<MsunReportMain> data = new ArrayList<>();
        String tableUrlValuePrivate = tableUrlValue;
        tableUrlValuePrivate = getString(tableUrlValuePrivate);
        String url = tableUrlValuePrivate + "/msun-reportweb-app-center/designer/msunReportMain/deliveryGetReport?hisHospitalId=" + info.getCloudHospitalId();
        try {
            HttpHeaders headers = new HttpHeaders();
            Map<String, Object> stringObjectMap = HttpUtil.doGet(url, headers);
            log.error("调用报表平台报表列表返回参数=====> {}", stringObjectMap);
            if (stringObjectMap != null && (Boolean) stringObjectMap.get("success")) {
                Object dataObj = stringObjectMap.get("data");
                if (dataObj instanceof List<?>) {
                    data = JSONObject.parseArray(JSONObject.toJSONString(dataObj), MsunReportMain.class);
                } else {
                    System.err.println("data 字段缺失或类型不正确");
                }
            }
        } catch (IOException e) {
            log.error("调用报表平台报表列表返回参数：==" + e);
        }
        return data;
    }

    /**
     * 对照
     *
     * @param entity
     * @return
     */
    private void compareReportMain(ProjStatisticalReportMainEntity entity) {
        ProjHospitalInfo info = projHospitalInfoMapper.selectById(entity.getHospitalInfoId());
        String tableUrlValuePrivate = tableUrlValue;
        tableUrlValuePrivate = getString(tableUrlValuePrivate);
        String url = tableUrlValuePrivate + "msun-reportweb-app-center/designer/msunReportMain/saveRequirementNumber";
        Map<String, Object> param = new HashMap<>();
        param.put("hisOrgId", info.getOrgId());
        param.put("hisHospitalId", info.getCloudHospitalId());
        param.put("reportId", entity.getStatisticalReportMainId());
        param.put("reportMainId", entity.getReportMainId());
        param.put("hisOrgNname", info.getHospitalName());
        param.put("hisHospitalName", info.getHospitalName());
        // 默认空
        param.put("sortOrder", "");
        try {
            log.error("调用报表平台对照入参数=====> {}", param);
            Map<String, Object> stringObjectMap = HttpUtil.doPost(url, JSONObject.toJSONString(param), null);
            log.error("调用报表平台对照入返回参数=====> {}", stringObjectMap);
            if (stringObjectMap != null && (Boolean) stringObjectMap.get("success")) {
                log.error("调用报表平台对照入返回参数=====> {}", stringObjectMap);
            }
            if (stringObjectMap != null && !(Boolean) stringObjectMap.get("success")) {
                throw new CustomException(String.valueOf(stringObjectMap.get("message")));
            }
        } catch (IOException e) {
            log.error("调用报表平台对照入返回参数：==" + e);
            throw new CustomException("调用报表平台报表对照异常");
        }
    }

    @Override
    public Result<Void> updateStatisticalReportIdentifier(ProjStatisticalReportMainAssignPersonsReq2 param) {
        log.info("统计报表分配验证人，验证人ID={}", param.getIdentifierUserId());
        if (CollectionUtils.isEmpty(param.getMainIds())) {
            return Result.fail("请选择要分配验证人的统计报表");
        }
        if (param.getIdentifierUserId() == null || Long.valueOf(0L).equals(param.getIdentifierUserId()) || Long.valueOf(-1L).equals(param.getIdentifierUserId())) {
            return Result.fail("请选择要分配的验证人");
        }

        Date now = new Date();
        for (Long statisticalReportMainId : param.getMainIds()) {
            ProjStatisticalReportMainEntity projStatisticalReportMainEntity = new ProjStatisticalReportMainEntity();
            projStatisticalReportMainEntity.setStatisticalReportMainId(statisticalReportMainId);
            projStatisticalReportMainEntity.setIdentifierUserId(param.getIdentifierUserId());
            projStatisticalReportMainEntity.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            projStatisticalReportMainEntity.setUpdateTime(now);
            projStatisticalReportMainMapper.updateById(projStatisticalReportMainEntity);
        }
        return Result.success(null, "分配验证人成功");
    }

    @Override
    public boolean verificationPassed(ProjStatisticalReportMainAssignPersonsReq2 param) {
        log.info("统计报表前端验证通过，参数={}", JSON.toJSONString(param));

        Date now = new Date();
        for (Long statisticalReportMainId : param.getMainIds()) {
            ProjStatisticalReportMainEntity report = new ProjStatisticalReportMainEntity();
            report.setStatisticalReportMainId(statisticalReportMainId);
            report.setIdentifierUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
            report.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            report.setUpdateTime(now);
            report.setVerificationStatus(param.getVerificationStatus());
            this.updateById(report);

            // 保存操作记录
            if (Integer.valueOf(1).equals(param.getVerificationStatus())) {
                saveOperationLog(statisticalReportMainId, 52, "验证通过", "验证通过");
            } else {
                saveOperationLog(statisticalReportMainId, 51, param.getReason(), "验证驳回");
            }
        }
        return true;
    }

    /**
     * 记录操作日志
     *
     * @param statisticalReportMainId 统计报表主键
     * @param operStatus              51-前端验证驳回
     *                                52-前端验证通过
     * @param operContent             操作内容
     * @param operTitle               日志标题
     */
    private void saveOperationLog(Long statisticalReportMainId, Integer operStatus, String operContent, String operTitle) {
        ProjReportOperationRecordEntity projOperateRecord = new ProjReportOperationRecordEntity();
        projOperateRecord.setReportOperationRecordId(SnowFlakeUtil.getId());
        projOperateRecord.setStatisticalReportMainId(statisticalReportMainId);
        projOperateRecord.setOperStatus(operStatus);
        projOperateRecord.setOperContent(operContent);
        projOperateRecord.setOperTitle(operTitle);
        projReportOperationRecordMapper.insert(projOperateRecord);
    }

    private RejectReasonAndCount selectReasonAndCountById(Long businessId) {
        List<ProjReportOperationRecordSelectReq> projBusinessExamineLogResps = projReportOperationRecordMapper.selectListById(businessId, 51);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(projBusinessExamineLogResps)) {
            return new RejectReasonAndCount(0, "");
        }
        StringBuilder rejectReason = new StringBuilder();
        for (int i = projBusinessExamineLogResps.size(); i >= 1; i--) {
            rejectReason.append("第").append(i).append("次：").append(projBusinessExamineLogResps.get(i - 1).getOperateContent()).append("；  ");
        }
        return new RejectReasonAndCount(projBusinessExamineLogResps.size(), rejectReason.toString());
    }

}
