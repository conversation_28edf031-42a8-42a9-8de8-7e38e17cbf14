package com.msun.csm.service.tduck;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.tduck.FormStatusEnum;
import com.msun.csm.dao.entity.tduck.FmUserForm;
import com.msun.csm.dao.mapper.tduck.FmUserFormMapper;
import com.msun.csm.exception.NoProjectUserFormException;

@Slf4j
@Service
public class FmUserFormServiceImpl implements FmUserFormService {

    @Resource
    private FmUserFormMapper fmUserFormMapper;

    @Override
    public FmUserForm getProjectUserFormByProjectInfoIdAndYyProductId(Long projectInfoId, String yyProductId, Integer formClass) {
        List<FmUserForm> fmUserFormList = fmUserFormMapper.selectList(
                new QueryWrapper<FmUserForm>()
                        .eq("project_info_id", projectInfoId)
                        .eq("yun_ying_product_id", yyProductId)
                        .ne("is_deleted", 1)
                        .eq("enable_flag", 1)
                        .eq("is_template", 1)
                        .eq("form_class", formClass)
        );
        if (CollectionUtils.isEmpty(fmUserFormList)) {
            throw new IllegalArgumentException(String.format("没有查询到当前项目对应产品的调研问卷，请先通过“进入调研”完成调研，project_info_id=%s，yun_ying_product_id=%s", projectInfoId, yyProductId));
        }
        if (fmUserFormList.size() > 1) {
            throw new IllegalArgumentException(String.format("当前项目对应产品的有效调研问卷存在多份，project_info_id=%s，yun_ying_product_id=%s", projectInfoId, yyProductId));
        }
        return fmUserFormList.get(0);
    }


    @Override
    public FmUserForm getProjectUserFormByProjectInfoId(Long projectInfoId, String yyProductId, Integer formClass) throws NoProjectUserFormException {
        List<FmUserForm> fmUserFormList = fmUserFormMapper.selectList(
                new QueryWrapper<FmUserForm>()
                        .eq("project_info_id", projectInfoId)
                        .eq("yun_ying_product_id", yyProductId)
                        .ne("is_deleted", 1)
                        .eq("enable_flag", 1)
                        .eq("is_template", 1)
                        .eq("form_class", formClass)
        );
        if (CollectionUtils.isEmpty(fmUserFormList)) {
            throw new NoProjectUserFormException(String.format("没有查询到当前项目对应产品的调研问卷，projectInfoId=%s，yyProductId=%s", projectInfoId, yyProductId));
        }
        if (fmUserFormList.size() > 1) {
            throw new IllegalArgumentException(String.format("当前项目对应产品的有效调研问卷存在多份，projectInfoId=%s，yyProductId=%s", projectInfoId, yyProductId));
        }
        return fmUserFormList.get(0);
    }

    @Override
    public String getProjectFormKeyByProjectInfoIdAndYyProductId(Long projectInfoId, String yyProductId, Integer formClass) {
        return this.getProjectUserFormByProjectInfoIdAndYyProductId(projectInfoId, yyProductId, formClass).getFormKey();
    }

    @Override
    public String getProjectFormKeyByProjectInfoIdAndYyProductIdNoException(Long projectInfoId, String yyProductId, Integer formClass) {
        try {
            return this.getProjectFormKeyByProjectInfoIdAndYyProductId(projectInfoId, yyProductId, formClass);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public FmUserForm getTemplateUserFormByProjectInfo(String productId, Integer projectType, Integer upgradationType, Integer formClass) {
        log.info("获取产品对应的调研问卷模板，productId={}，projectType={}，upgradationType={}", productId, projectType, upgradationType);
        Integer one = 1;
        Integer two = 2;
        List<FmUserForm> templateFormList = fmUserFormMapper.selectList(
                new QueryWrapper<FmUserForm>()
                        .ne("is_deleted", 1)
                        .eq("enable_flag", 1)
                        .eq("is_template", 0)
                        .eq("form_class", formClass)
                        .eq("yun_ying_product_id", productId)
                        .eq(one.equals(projectType), "monomer_flag", 1)
                        .eq(two.equals(projectType), "region_flag", 1)
                        .eq(one.equals(upgradationType), "replace_old_new", 1)
                        .eq(two.equals(upgradationType), "new_customer", 1)

        );
        if (CollectionUtils.isEmpty(templateFormList)) {
            throw new IllegalArgumentException(String.format("没有查询到对应产品的调研问卷模板，请联系项目交付组解决。productId=%s，projectType=%s，upgradationType=%s", productId, projectType, upgradationType));
        }
        if (templateFormList.size() > 1) {
            throw new IllegalArgumentException(String.format("当前产品对应的调研问卷模板存在%s个，请联系交付平台解决。productId=%s，projectType=%s，upgradationType=%s", templateFormList.size(), productId, projectType, upgradationType));
        }
        return templateFormList.get(0);
    }

    @Override
    @Transactional
    public boolean updateFormStatusByFormKey(FormStatusEnum formStatusEnum, String formKey) {
        FmUserForm fmUserForm = new FmUserForm();
        fmUserForm.setStatus(formStatusEnum);

        int result = fmUserFormMapper.update(fmUserForm, new QueryWrapper<FmUserForm>().eq("form_key", formKey));
        if (result != 1) {
            throw new IllegalArgumentException("更新调研问卷状态，通过问卷key查询到多份问卷。formKey=" + formKey);
        }
        return true;
    }
}
