package com.msun.csm.service.formlibnew.impl;


import cn.hutool.core.bean.BeanUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.formlibnew.DictFormlibStandard;
import com.msun.csm.dao.entity.formlibnew.DictFormlibStandardType;
import com.msun.csm.dao.entity.formlibnew.FormlibResourceType;
import com.msun.csm.dao.mapper.formlibnew.DictFormlibStandardMapper;
import com.msun.csm.dao.mapper.formlibnew.DictFormlibStandardTypeMapper;
import com.msun.csm.dao.mapper.formlibnew.FormlibResourceTypeMapper;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq;
import com.msun.csm.model.req.formlibnew.DictFormlibStandardSaveReq;
import com.msun.csm.model.req.formlibnew.DictFormlibStandardTypeSaveReq;
import com.msun.csm.model.req.formlibnew.DictFormLibStandardTypePageReq;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardResp;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTteeResp;
import com.msun.csm.model.resp.formlibnew.DictFormlibStandardTypeSelectResp;
import com.msun.csm.service.formlibnew.DictFormlibStandardService;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.PinyinUtils;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【dict_formlib_standard(表单资源库标准字典)】的数据库操作Service实现
 * @createDate 2025-07-14 13:47:02
 */
@Service
public class DictFormlibStandardServiceImpl extends ServiceImpl<DictFormlibStandardMapper, DictFormlibStandard> implements DictFormlibStandardService {

    @Resource
    private DictFormlibStandardMapper dictFormlibStandardMapper;

    @Resource
    private DictFormlibStandardTypeMapper dictFormlibStandardTypeMapper;

    @Resource
    private FormlibResourceTypeMapper formlibResourceTypeMapper;


    /**
     * 查询标准树数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result findStandardTreeData(DictFormLibStandardPageReq dto) {
        List<DictFormlibStandardTteeResp> reslut = new ArrayList<>();
        List<DictFormlibStandardTteeResp> rootNodes = dictFormlibStandardMapper.getRootNodes(dto);
        // 递归构建树结构
        if (rootNodes == null || rootNodes.isEmpty()) {
            return Result.success(new ArrayList<>());
        }
        for (DictFormlibStandardTteeResp rootNode : rootNodes) {
            Long pid = Long.valueOf(rootNode.getValue());
            rootNode.setChildren(getChildrenRecursively(pid, dto.getIsDeleted()));
        }
        DictFormlibStandardTteeResp rootNode = new DictFormlibStandardTteeResp();
        rootNode.setChildren(new ArrayList<>());
        rootNode.setLabel("一级分类(默认分类)");
        rootNode.setValue("-1");
        reslut.add(rootNode);
        reslut.addAll(rootNodes);
        // 递归查询树数据
        return Result.success(reslut);
    }

    /**
     * 查询标准数据(表格)
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<DictFormlibStandardResp>> findDictDataPage(DictFormLibStandardPageReq dto) {
        dto.setPid(-1L);
        List<DictFormlibStandardResp> rootNodes = dictFormlibStandardMapper.getNodesByDto(dto);
        // 递归构建树结构
        if (rootNodes == null || rootNodes.isEmpty()) {
            return Result.success(new ArrayList<>());
        }
        for (DictFormlibStandardResp child : rootNodes) {
            child.setLevel(1);
            DictFormLibStandardPageReq req = new DictFormLibStandardPageReq();
            req.setPid(child.getFormlibStandardId());
            child.setChildren(getChildrenRecursivelyTable(req, child.getLevel()));
        }
        return Result.success(rootNodes);
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> saveData(DictFormlibStandardSaveReq dto) {
        String code = "";
        if (dto.getParentId() == null) {
            dto.setParentId(-1L);
        } else {
            code = dictFormlibStandardMapper.getParentCode(dto.getParentId());
        }

        DictFormlibStandard entity = new DictFormlibStandard();
        BeanUtil.copyProperties(dto, entity);
        if (dto.getFormlibStandardId() != null) {
            List lisst = dictFormlibStandardMapper.selectList(new QueryWrapper<DictFormlibStandard>().ne("formlib_standard_id", dto.getFormlibStandardId()).eq("formlib_standard_code", entity.getFormlibStandardCode()).eq("yy_product_id", dto.getYyProductId()));
            if (lisst != null && !lisst.isEmpty()) {
                return Result.fail("分类编码重复");
            }
            dictFormlibStandardMapper.updateDataById(entity);

        } else {
            // 新增
            entity.setFormlibStandardId(SnowFlakeUtil.getId());
            if (!"".equals(code)) {
                entity.setFormlibStandardCode(code + "-" + PinyinUtils.getFirstSpell(dto.getFormlibStandardName()));
            } else {
                entity.setFormlibStandardCode(PinyinUtils.getFirstSpell(dto.getFormlibStandardName()));
            }
            List lisst = dictFormlibStandardMapper.selectList(new QueryWrapper<DictFormlibStandard>().eq("formlib_standard_code", entity.getFormlibStandardCode()).eq("yy_product_id", dto.getYyProductId()));
            if (lisst != null && !lisst.isEmpty()) {
                return Result.fail("分类编码重复");
            }
            dictFormlibStandardMapper.insert(entity);
        }
        return Result.success();
    }

    /**
     * 修改启用禁用
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> updateIsStandard(DictFormlibStandardSaveReq dto) {

        if (dto.getIsDeleted() == 1) {
            List<Long> ids = dictFormlibStandardMapper.getAllChildrenIdByPid(dto.getFormlibStandardId(), 0);
            List<FormlibResourceType> list = formlibResourceTypeMapper.selectList(new QueryWrapper<FormlibResourceType>().in("formlib_standard_id", ids));
            if (list != null && !list.isEmpty()) {
                return Result.fail("该字典分类已在资源库中使用，无法禁用。请先解除相关引用后再操作");
            }
        }

        DictFormlibStandard entity = new DictFormlibStandard();
        entity.setFormlibStandardId(dto.getFormlibStandardId());
        entity.setIsDeleted(dto.getIsDeleted());
        dictFormlibStandardMapper.updateDataById(entity);
        return Result.success();
    }

    /**
     * 分页查询分类字典数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<DictFormlibStandardTypeSelectResp>> selectStandardTypeData(DictFormLibStandardTypePageReq dto) {
        if (dto.getFormlibStandardId() != null) {
            List<Long> ids = dictFormlibStandardMapper.getAllChildrenIdByPid(dto.getFormlibStandardId(), null);
            dto.setIds(ids);
        }
       return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            List<DictFormlibStandardTypeSelectResp> data = dictFormlibStandardTypeMapper.selectPageByDto(dto);
            if (data != null && !data.isEmpty()) {
                for (DictFormlibStandardTypeSelectResp item : data) {
                    if (item.getFormlibStandardId() != null) {
                        String name = dictFormlibStandardMapper.getFormlibStandardName(item.getFormlibStandardIdOld());
                        if (name != null && !"".equals(name)) {
                            name = name + "->" + item.getFormlibStandardTypeName();
                            item.setFormlibStandardTypeNameSelectShow(name);
                        }
                    }
                    if (item.getFormlibStandardTypeNameSelectShow() == null || item.getFormlibStandardTypeNameSelectShow().isEmpty()) {
                        String name = dictFormlibStandardMapper.getFormlibStandardName(item.getFormlibStandardIdOld());
                        item.setFormlibStandardTypeNameSelectShow(item.getFormlibStandardTypeName());
                    }
                }
            }
            PageInfo<DictFormlibStandardTypeSelectResp> pageInfo = new PageInfo<>(data);
            pageInfo.setTotal(page.getTotal());
            return Result.success(pageInfo);
        });
    }

    /**
     * 修改字典启用禁用
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> updateIsStandardType(DictFormLibStandardTypePageReq dto) {

        if (dto.getIsDeleted() == 1) {
            List<FormlibResourceType> list = formlibResourceTypeMapper.selectList(new QueryWrapper<FormlibResourceType>().eq("formlib_standard_type_id", dto.getFormlibStandardTypeId()));
            if (list != null && !list.isEmpty()) {
                return Result.fail("该字典已在资源库中使用，无法禁用。请先解除相关引用后再操作。");
            }
        }

        DictFormlibStandardType entity = new DictFormlibStandardType();
        entity.setFormlibStandardTypeId(dto.getFormlibStandardTypeId());
        entity.setIsDeleted(dto.getIsDeleted());
        entity.setUpdateTime(new Date());
        dictFormlibStandardTypeMapper.updateDataById(entity);
        return Result.success();
    }

    /**
     * 保存字典数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> saveDictData(DictFormlibStandardTypeSaveReq dto) {
        DictFormlibStandardType entity = new DictFormlibStandardType();
        BeanUtil.copyProperties(dto, entity);
        if (dto.getFormlibStandardTypeId() != null) {
            List lisst = dictFormlibStandardTypeMapper.selectList(new QueryWrapper<DictFormlibStandardType>().ne("formlib_standard_type_id", dto.getFormlibStandardTypeId()).eq("formlib_standard_type_code", entity.getFormlibStandardTypeCode()).eq("yy_product_id", dto.getYyProductId()));
            if (lisst != null && !lisst.isEmpty()) {
                return Result.fail("字典编码重复");
            }
            dictFormlibStandardTypeMapper.updateDataById(entity);

        } else {
            // 新增
            entity.setFormlibStandardTypeId(SnowFlakeUtil.getId());
            entity.setFormlibStandardTypeCode(PinyinUtils.getFirstSpell(dto.getFormlibStandardTypeName()));
            List lisst = dictFormlibStandardTypeMapper.selectList(new QueryWrapper<DictFormlibStandardType>().eq("formlib_standard_type_code", entity.getFormlibStandardTypeCode()).eq("yy_product_id", dto.getYyProductId()));
            if (lisst != null && !lisst.isEmpty()) {
                return Result.fail("字典编码重复");
            }
            dictFormlibStandardTypeMapper.insert(entity);
        }
        return Result.success();
    }

    /**
     * 查询字典数据，用于资源库下拉选项
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<BaseCodeNameResp>> findStandardTypeDictData(DictFormLibStandardPageReq dto) {
        List<BaseCodeNameResp> list = dictFormlibStandardTypeMapper.findStandardTypeDictData(dto);
        return Result.success(list);
    }

    /**
     * 查询表单类型
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<BaseCodeNameResp>> selectDictFormTypeList(DictFormLibStandardPageReq dto) {
        List<BaseCodeNameResp> list = dictFormlibStandardMapper.selectDictFormTypeList(dto);
        return Result.success(list);
    }

    /**
     * 递归获取子节点（表格）
     *
     * @param dto
     * @param level
     * @return
     */
    private List<DictFormlibStandardResp> getChildrenRecursivelyTable(DictFormLibStandardPageReq dto, Integer level) {
        List<DictFormlibStandardResp> children = dictFormlibStandardMapper.getNodesByDto(dto);
        if (children == null || children.isEmpty()) {
            return new ArrayList<>();
        }
        for (DictFormlibStandardResp child : children) {
            child.setLevel(level + 1);
            DictFormLibStandardPageReq req = new DictFormLibStandardPageReq();
            req.setPid(child.getFormlibStandardId());
            child.setChildren(getChildrenRecursivelyTable(req, level + 1));
        }
        return children;
    }

    /**
     * 递归获取子节点（查询条件）
     * @param parentId
     * @param isDeleted
     * @return
     */
    private List<DictFormlibStandardTteeResp> getChildrenRecursively(Long parentId, Integer isDeleted) {
        List<DictFormlibStandardTteeResp> children = dictFormlibStandardMapper.getChildrenById(parentId, isDeleted);
        if (children == null || children.isEmpty()) {
            return new ArrayList<>();
        }
        for (DictFormlibStandardTteeResp child : children) {
            Long pid = Long.valueOf(child.getValue());
            child.setChildren(getChildrenRecursively(pid, isDeleted));
        }
        return children;
    }
}




