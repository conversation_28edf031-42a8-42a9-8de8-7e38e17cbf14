package com.msun.csm.service.rule;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.model.param.GetProjectFileRuleParam;
import com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO;
import com.msun.csm.model.vo.ProjectFileUploadRuleVO;

/**
 * <AUTHOR>
 */
public interface RuleProjectRuleConfigService {

    List<ProjProjectAcceptanceRuleVO> getTemplateByCode(ProjProjectInfo projectInfo);

    /**
     * 查询项目上传文件规则配置并获取对应的已上传文件
     */
    List<ProjectFileUploadRuleVO> getProjectFileRule(GetProjectFileRuleParam params);

}
