package com.msun.csm.service.proj.producttask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.utils.AesUtil;
import com.msun.core.component.implementation.api.csm.CsmApi;
import com.msun.core.component.implementation.api.csm.dto.CsmDTO;
import com.msun.core.component.implementation.api.csm.dto.ImportSheetNurceImspDTO;
import com.msun.core.component.implementation.api.csm.vo.HospitalPrintCodeVO;
import com.msun.core.component.implementation.api.csm.vo.PersonPhoneVO;
import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.AppLoginDTO;
import com.msun.core.component.implementation.api.imsp.dto.BatchDeleteGeneralDTO;
import com.msun.core.component.implementation.api.imsp.dto.ChangeHospitalDto;
import com.msun.core.component.implementation.api.imsp.dto.GetAllAppDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.api.imsp.vo.HisLoginVO;
import com.msun.core.component.implementation.api.imsp.vo.ProductEncryptDto;
import com.msun.core.component.implementation.api.medinsur.dto.MedInsurInSettleResultDTO;
import com.msun.core.component.implementation.api.port.ApplicationCheckApi;
import com.msun.core.component.implementation.api.port.DataPreparationApi;
import com.msun.core.component.implementation.api.port.dto.ProductTaskSqlCheckDTO;
import com.msun.core.component.implementation.api.port.dto.SqlCheckApiDTO;
import com.msun.core.component.implementation.api.port.dto.ValidateSqlForProductTaskDTO;
import com.msun.core.component.implementation.api.port.vo.ProductTaskInterfaceResultVO;
import com.msun.core.component.implementation.api.port.vo.ValidateSqlForProductTaskVo;
import com.msun.core.component.implementation.api.report.ReportApi;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsDTO;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsListDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.comm.HzznDeliver;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecord;
import com.msun.csm.dao.entity.proj.producttask.ProjCloudAccountPassword;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.mapper.comm.HzznDeliverMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjCloudAccountPasswordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.entity.tduck.req.DictTaskInfoVO;
import com.msun.csm.feign.entity.tduck.req.GetDictTaskInfoParam;
import com.msun.csm.manage.TduckManage;
import com.msun.csm.model.csm.CsmParamerDTO;
import com.msun.csm.model.csm.HisLoginCsmVo;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.param.ProjProductTaskRecordParam;
import com.msun.csm.model.resp.producttask.ProjCustomerProjectTypeResp;
import com.msun.csm.model.resp.producttask.ProjProductTaskProgressResp;
import com.msun.csm.model.resp.producttask.ProjProductTaskResp;
import com.msun.csm.model.tduck.req.TDuckTaskFileInfo;
import com.msun.csm.model.vo.OneCheckResultVO;
import com.msun.csm.model.vo.OneCheckVo;
import com.msun.csm.model.vo.ProjProjectInfoVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonSearchCloudDbService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.MedicalInsuranceService;
import com.msun.csm.service.proj.ProjProductBacklogService;
import com.msun.csm.service.sysuser.SysUserService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【proj_product_task(产品待处理任务表)】的数据库操作Service实现
 * @createDate 2024-07-25 18:14:54
 */
@Service
@Slf4j
public class ProjProductTaskServiceImpl extends ServiceImpl<ProjProductTaskMapper, ProjProductTask> implements ProjProductTaskService {

    private static final String ENCODED_SPACE = "%20";
    private static final String ENCODED_HASH = "%23";
    private static final String ENCODED_LEFT_BRACKET = "%5B";
    private static final String ENCODED_RIGHT_BRACKET = "%5D";
    @Lazy
    @Resource
    SystemSettingApi systemSettingApi;
    @Resource
    private ProjProductTaskMapper projProductTaskMapper;
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Resource
    private CommonSearchCloudDbService commonSearchCloudDbService;

    @Lazy
    @Resource
    private DataPreparationApi dataPreparationApi;

    @Resource
    private ProjProductBacklogService productBacklogService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private HzznDeliverMapper hzznDeliverMapper;

    @Resource
    private ProjProjectReviewRecordMapper projProjectReviewRecordMapper;

    @Lazy
    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjCloudAccountPasswordMapper projCloudAccountPasswordMapper;

    @Resource
    private ApplicationCheckApi applicationCheckApi;

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private TduckManage tduckManage;
    @Resource
    private MedicalInsuranceService medicalInsuranceService;

    @Lazy
    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Lazy
    @Resource
    private SysUserService sysUserService;

    @Resource
    private ReportApi reportApi;

    @Resource
    private UserHelper userHelper;

    @Resource
    @Lazy
    private CsmApi csmApi;

    /**
     * 转化url
     *
     * @param value
     * @return
     */
    public static String encodeURIComponent(String value) {
        try {
            // 使用 UTF-8 字符集进行编码
            String encoded = URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
            // 替换空格、#、[ 和 ]，使其与 encodeURIComponent 行为一致
            return encoded.replace("+", ENCODED_SPACE).replace("#", ENCODED_HASH).replace("[", ENCODED_LEFT_BRACKET).replace("]", ENCODED_RIGHT_BRACKET);
        } catch (Exception e) {
            throw new RuntimeException("Encoding failed", e);
        }
    }

    @Override
    public Result<List<ProjProductTaskResp>> selectProductTaskList(ProjProductTaskParam dto) {
        log.info("各产品准备工作，查询待办任务列表实际参数={}", JSON.toJSONString(dto));
        List<ProjProductTaskResp> list = this.getProductTaskWithDictInfoList(dto);
        // 查询老项目id，客户id， 新项目id，新客户id
        ProjProjectInfoVO projProjectInfoVO = projectInfoMapper.selectProjNewAndOldByNewProjId(dto.getProjectInfoId());
        if (list != null && list.size() > 0) {
            for (ProjProductTaskResp resp : list) {
//                List<ProjProjectFile> allFlieList = projProductTaskMapper.selectAllFlieList(resp);
//                resp.setProjProjectFileList(allFlieList);
                // 判断项目是否已经上线，当上线时  赋值 online字段值
                ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq("project_info_id", dto.getProjectInfoId()));
                if (ObjectUtil.isNotEmpty(projProjectInfo) && ObjectUtil.isNotEmpty(projProjectInfo.getOnlineTime())) {
                    resp.setIsOnline(1);
                } else {
                    resp.setIsOnline(0);
                }
                if (ObjectUtil.isNotEmpty(projProjectInfoVO) && ObjectUtil.isNotEmpty(resp.getTaskExplainLink())) {
                    String projectId = projProjectInfoVO.getOldProjectId() != null ? String.valueOf(projProjectInfoVO.getOldProjectId()) : "";
                    String customerId = projProjectInfoVO.getOldCustomerId() != null ? String.valueOf(projProjectInfoVO.getOldCustomerId()) : "";
                    String urlLink = resp.getTaskExplainLink().replace("#{projectId}", projectId).replace("#{customerId}", customerId);
                    resp.setTaskExplainLink(urlLink);
                }
                resp.setHasVerifyTypeFlag(ObjectUtil.isNotEmpty(resp.getTaskValidateType()));
                resp.setPopupWindow("ybhxfw_task_00".equals(resp.getTaskCode()));
                // 急诊文书挂单
                resp.setEmisTaskFlag(resp.getTaskCode() != null && "emis_task_09".equals(resp.getTaskCode()));
            }
        } else {
            list = new ArrayList<>();
        }
        return Result.success(list);
    }

    /**
     * 确认完成状态
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean updatePorductTaskData(ProjProductTaskParam dto) {
        ProjProductTask task = projProductTaskMapper.selectById(dto.getProductTaskId());
        task.setTaskStatus(1);
        projProductTaskMapper.updateById(task);
        //  增加检测
        ProjProductTask projProductTask = projProductTaskMapper.selectById(task.getProductTaskId());
        // 检测是否全部完成，当全部完成后。更新backlog中 待处理任务完成状态 项目、医院、产品 查询状态
        List<ProjProductTask> projProductTasks = projProductTaskMapper.selectList(new QueryWrapper<ProjProductTask>().eq("project_info_id", projProductTask.getProjectInfoId()).eq("hospital_info_id", projProductTask.getHospitalInfoId()).eq("yy_product_id", projProductTask.getYyProductId()).in("task_status", Arrays.asList(0, 2)));
        if (ObjectUtil.isEmpty(projProductTasks)) {
            ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
            projProductBacklogDTO.setProjectInfoId(projProductTask.getProjectInfoId());
            projProductBacklogDTO.setHospitalInfoId(projProductTask.getHospitalInfoId());
            projProductBacklogDTO.setYyProductId(projProductTask.getYyProductId());
            projProductBacklogDTO.setTodoTaskStatus(2);
            log.info("待处理任务检测结束,更新产品待办任务表配置完成状态===={}", JSONUtil.toJsonStr(projProductBacklogDTO));
            productBacklogService.taskBacklogFinish(projProductBacklogDTO, "待处理任务一键检测");
        }
        return true;
    }

    /**
     * 获取云健康登录信息
     *
     * @param dto
     * @return
     */
    @Override
    public Object hisLogin(ProjProductTaskParam dto) {
        try {
            String url = null;
            Map<String, Object> result = new HashMap<>();
            //获取当前客户信息
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
            log.info("获取的当前客户信息:{}", projHospitalInfo);
            if (!Objects.isNull(projHospitalInfo)) {
                Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
                log.info("设定医院信息:{}", domainMap);
                domainHolder.refresh(domainMap);
                domainMap.clear();
                //根据id 查询真实地址
                Long hospitalId = projHospitalInfo.getCloudHospitalId();
                // 放置前端需要的数据
                result.put("hospitalId", hospitalId);
                log.info("跳转云健康业务域名: {}", projHospitalInfo.getCloudDomain());
                result.put("netWork", projHospitalInfo.getCloudDomain());
                Long orgId = projHospitalInfo.getOrgId();
                //        第一次调用 -------
                String host = "";
                String host1 = projHospitalInfo.getCloudDomain().replace("https://", StrUtil.EMPTY).replace("http://", StrUtil.EMPTY);
                if (host1.contains(":")) {
                    host = host1.split(":")[0];
                } else {
                    host = host1;
                }
                CsmParamerDTO csmParamerDTO = new CsmParamerDTO();
                csmParamerDTO.setCloudHospitalId(projHospitalInfo.getCloudHospitalId());
                csmParamerDTO.setHospitalInfoId(projHospitalInfo.getHospitalInfoId());
                log.warn("verifyLoginBeforeCloudHealthRedirect请求体: {}", JSON.toJSONString(csmParamerDTO));
                Result<HisLoginCsmVo> responseResult = this.verifyLoginBeforeCloudHealthRedirect(csmParamerDTO);
                log.warn("verifyLoginBeforeCloudHealthRedirect响应结果:{}", JSONObject.toJSONString(responseResult));
                HisLoginCsmVo data = responseResult.getData();
                log.warn("verifyLoginBeforeCloudHealthRedirect响应数据:{}", JSON.toJSONString(data));
                if (data.getUserId() == null || data.getUserId() == 0) {
                    throw new RuntimeException(StrUtil.format("验证登录用户信息没返回回来UserId; 参数：{}，结果：{}", JSON.toJSONString(csmParamerDTO), JSON.toJSONString(data)));
                }
                result.put("hisUserInfo", data);
                String name = "jfadmin";
                String pwd = data.getPwd();
                result.put("userName", name);
                result.put("passWord", pwd);
                // 第二次调用-------
                //继续调用云健康医院
                String token = data.getToken();
                Long msunStaffId = data.getMsunStaffId();
                SystemConfigDto dto2 = new SystemConfigDto();
                dto2.setHospitalId(hospitalId);
                dto2.setHisOrgId(orgId);
                List<ChangeHospitalDto> list2 = new ArrayList<>();
                ChangeHospitalDto cd2 = new ChangeHospitalDto();
                cd2.setHospitalId(hospitalId);
                cd2.setToken(token);
                cd2.setMsunStaffId(Convert.toStr(msunStaffId));
                list2.add(cd2);
                dto2.setData(list2);
                log.warn("systemSettingApi.changeHospital请求体:{},", JSON.toJSONString(dto2));
                ResponseResult responseResult2 = systemSettingApi.changeHospital(dto2);
                log.warn("systemSettingApi.changeHospital响应体:{}", JSONObject.toJSONString(responseResult2));
                Object data2 = responseResult2.getData();
                log.warn("systemSettingApi.changeHospital响应数据:{}", JSON.toJSONString(data2));
                result.put("currentUser", data2);
                // 第三次调用
                Long userId3 = data.getUserId();
                SystemConfigDto dto3 = new SystemConfigDto();
                dto3.setHospitalId(hospitalId);
                dto3.setHisOrgId(orgId);
                List<GetAllAppDto> list3 = new ArrayList<>();
                GetAllAppDto cd3 = new GetAllAppDto();
                cd3.setHospitaId(hospitalId);
                cd3.setUserId(Convert.toStr(userId3));
                list3.add(cd3);
                dto3.setData(list3);
                log.warn("systemSettingApi.getAllApp请求体:{},", JSON.toJSONString(dto3));
                ResponseResult responseResult3 = systemSettingApi.getAllApp(dto3);
                log.warn("systemSettingApi.getAllApp响应体:{}", JSONObject.toJSONString(responseResult3));
                Object data3 = responseResult3.getData();
                log.warn("systemSettingApi.getAllApp响应数据:{}", JSON.toJSONString(data3));
                result.put("allApp", JSON.toJSONString(data3));
                String jsonString = JSON.toJSONString(responseResult3.getData());
                TypeReference<List<Map<String, Object>>> typeReference = new TypeReference<List<Map<String, Object>>>() {
                };
                List<Map<String, Object>> data3List = JSON.parseObject(jsonString, typeReference);
                Map<String, Object> mapee = null;
                if (data3List != null) {
                    for (Map<String, Object> map : data3List) {
                        if (dto.getCloudProductCode().equals(map.get("appCode"))) {
                            mapee = map;
                        }
                    }
                }
                Map<String, Object> lastMap = null;
                if (mapee != null) {
                    String applicationListJson = JSON.toJSONString(mapee.get("applicationList"));
                    TypeReference<List<Map<String, Object>>> typeReferenceee = new TypeReference<List<Map<String, Object>>>() {
                    };
                    List<Map<String, Object>> data3DetailList = JSON.parseObject(applicationListJson, typeReferenceee);
                    if (data3DetailList != null) {
                        lastMap = data3DetailList.get(0);
                    }
                }
                Map<String, Object> selectedSystemMap = new HashMap<>();
                if (lastMap != null) {
                    selectedSystemMap.put("deptCode", lastMap.get("deptCode"));
                    selectedSystemMap.put("deptId", lastMap.get("deptId"));
                    selectedSystemMap.put("deptName", lastMap.get("deptName"));
                    selectedSystemMap.put("orgId", lastMap.get("orgId"));
                    selectedSystemMap.put("sysytemId", lastMap.get("appId"));
                    selectedSystemMap.put("systemName", lastMap.get("appName"));
                    selectedSystemMap.put("url", lastMap.get("url"));
                    selectedSystemMap.put("userSysId", lastMap.get("identityId"));
                    selectedSystemMap.put("userSysName", lastMap.get("identityName"));
                    selectedSystemMap.put("appId", lastMap.get("appId"));
                    selectedSystemMap.put("appName", lastMap.get("appName"));
                    selectedSystemMap.put("identityId", lastMap.get("identityId"));
                    selectedSystemMap.put("identityName", lastMap.get("identityName"));
                } else {
                    log.error("SessionUtil中未获取到当前客户信息11: {}", JSON.toJSONString(dto));
                    throw new RuntimeException(StrUtil.format("SessionUtil中未获取到当前客户信息！"));
                }
                Map<String, Object> hisMap = new HashMap<>();
                hisMap.put("data", dto.getData());
                hisMap.put("selectedSystem", selectedSystemMap);
                hisMap.put("userName", name);
                hisMap.put("passwd", pwd);
                hisMap.put("fromUrl", host);
                hisMap.put("hospitalId", String.valueOf(hospitalId));
                hisMap.put("scene", "JFPT");
                hisMap.put("tailRoute", dto.getTaskPageUrl());
                url = projHospitalInfo.getCloudDomain();
                //.replace(":1443", StrUtil.EMPTY);
                url = url + "/portal/#/transfer?param=" + encodeURIComponent(JSON.toJSONString(hisMap));

            } else {
                log.error("SessionUtil中未获取到当前客户信息22: {}", JSON.toJSONString(dto));
                throw new RuntimeException("SessionUtil中未获取到当前客户信息！");
            }
            return url;
        } catch (Throwable e) {
            log.error("hisLogin失败: {}", e.getMessage(), e);
            throw new RuntimeException(StrUtil.format("获取云健康登录信息异常: {}", e.getMessage(), e));
        }
    }

    /**
     * 一键检测
     *
     * @param dto
     * @return
     */
    @Override
    public Result<OneCheckResultVO> oneCheck(ProjProductTaskParam dto, String message) {
        OneCheckResultVO oneCheckResultVO = new OneCheckResultVO();
        Long zcount = projProductTaskMapper.selectCount(new QueryWrapper<ProjProductTask>()
                .eq("is_deleted", 0)
                .eq("hospital_info_id", dto.getHospitalInfoId())
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("yy_product_id", dto.getYyProductId())
                .ne("task_validate_type", "")
                .ne("verify_sql_text", "")
                .isNotNull("task_validate_type")
                .isNotNull("verify_sql_text"));
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("总共需要检测").append(zcount).append("项，已检测通过");
        dto.setTaskStatus(99);
        dto.setTaskStatusNot(1);
        dto.setSurveyTitle(null);
        // 脚本检测
        dto.setTaskValidateType("sql");
        List<ProjProductTaskResp> sqlList = this.getProductTaskWithDictInfoList(dto);
        if (CollUtil.isNotEmpty(sqlList)) {
            try {
                oneCheckCloud(sqlList);
            } catch (Exception e) {
                log.error("SQL一键检测异常:", e);
                throw new RuntimeException("SQL一键检测异常 , " + e);
            }
        }
        // 接口检测
        dto.setTaskValidateType("interface");
        List<ProjProductTaskResp> interfaceList = this.getProductTaskWithDictInfoList(dto);
        List<ValidateSqlForProductTaskVo> interfaceCheckResultVoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(interfaceList)) {
            interfaceCheckResultVoList = oneCheckCloudInterface(interfaceList);
        }
        Long oneCheckCount = projProductTaskMapper.selectCount(new QueryWrapper<ProjProductTask>()
                .eq("is_deleted", 0)
                .eq("hospital_info_id", dto.getHospitalInfoId())
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("yy_product_id", dto.getYyProductId())
                .ne("task_validate_type", "")
                .ne("verify_sql_text", "")
                .eq("task_status", 1)
                .isNotNull("task_validate_type")
                .isNotNull("verify_sql_text"));
        stringBuffer.append(oneCheckCount).append("项");
        // 判断是否全部检测通过，只要有一条未检测通过 ，返回异常信息
        if (zcount > 0 && !zcount.equals(oneCheckCount)) {
            log.info("检测待处理任务，检测未通过 , 开始检测失败数据");
            // 1、检测失败时 ，异常信息拼装
            checkFail(dto, oneCheckResultVO, interfaceCheckResultVoList);
            return Result.success(oneCheckResultVO);
        } else {
            oneCheckResultVO.setHasError(0);
            oneCheckResultVO.setSuccessMsg(stringBuffer.toString());
        }
        // 检测是否全部完成，当全部完成后。更新backlog中 待处理任务完成状态 项目、医院、产品 查询状态
        List<ProjProductTask> projProductTasks = projProductTaskMapper.selectList(new QueryWrapper<ProjProductTask>().eq("project_info_id", dto.getProjectInfoId()).eq("hospital_info_id", dto.getHospitalInfoId()).eq("yy_product_id", dto.getYyProductId()).ne("task_status", 1));
        if (ObjectUtil.isEmpty(projProductTasks)) {
            ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
            projProductBacklogDTO.setProjectInfoId(dto.getProjectInfoId());
            projProductBacklogDTO.setHospitalInfoId(dto.getHospitalInfoId());
            projProductBacklogDTO.setYyProductId(dto.getYyProductId());
            projProductBacklogDTO.setTodoTaskStatus(2);
            log.info("待处理任务检测结束,更新产品待办任务表配置完成状态===={}", JSONUtil.toJsonStr(projProductBacklogDTO));
            productBacklogService.taskBacklogFinish(projProductBacklogDTO, message);
        }
        return Result.success(oneCheckResultVO);
    }

    /**
     * 检测失败的数据返回信息组装
     *
     * @param dto
     * @param oneCheckResultVO
     * @return
     */
    void checkFail(ProjProductTaskParam dto, OneCheckResultVO oneCheckResultVO, List<ValidateSqlForProductTaskVo> interfaceCheckResultVoList) {
        List<OneCheckVo> failResult = new ArrayList<>();
        try {
            // 查询检测失败的数据 ， 去执行失败列表的 查询明细脚本
            ProjProductTaskParam queryParam = new ProjProductTaskParam();
            queryParam.setProjectInfoId(dto.getProjectInfoId());
            queryParam.setHospitalInfoId(dto.getHospitalInfoId());
            queryParam.setYyProductId(dto.getYyProductId());
            queryParam.setTaskStatus(2);
            List<ProjProductTask> failDataList;
            List<ProjProductTask> productTaskWithDictInfoList = this.getProductTaskWithDictInfo(queryParam);
            if (!CollectionUtils.isEmpty(productTaskWithDictInfoList)) {
                failDataList = productTaskWithDictInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getTaskValidateType())).collect(Collectors.toList());
            } else {
                failDataList = new ArrayList<>();
            }
            // 判断是接口返回的检测失败 ， 还是脚本查询导致的 检测失败
            if (CollUtil.isNotEmpty(failDataList)) {
                Map<String, List<ProjProductTask>> collect = failDataList.stream().collect(Collectors.groupingBy(ProjProductTask::getTaskValidateType));
                for (String key : collect.keySet()) {
                    ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
                    // 获取产品名称
                    Set<Long> productIds = new HashSet<>();
                    productIds.add(dto.getYyProductId());
                    List<BaseIdNameResp> productList = dictProductMapper.findByProductIds(productIds);
                    // sql检测处理
                    if ("sql".equals(key) && !collect.get("sql").isEmpty()) {
                        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
                        domainHolder.refresh(domainMap);
                        SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
                        List<ValidateSqlForProductTaskDTO> dtoList = new ArrayList<>();
                        for (ProjProductTask task : collect.get("sql")) {
                            // 没有检测明细脚本时  直接跳过
                            if (ObjectUtil.isEmpty(task.getValidateDetailSql())) {
                                OneCheckVo oneCheckVo = new OneCheckVo();
                                oneCheckVo.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                                oneCheckVo.setHospitalName(hospitalInfo.getHospitalName());
                                oneCheckVo.setYyProductId(task.getYyProductId());
                                oneCheckVo.setProductName(productList.get(0).getName().endsWith(StrUtil.DASHED) ? productList.get(0).getName().substring(0, productList.get(0).getName().length() - 1) : productList.get(0).getName());
                                oneCheckVo.setTaskTitle(task.getTaskTitle());
                                oneCheckVo.setValidateStandards(task.getValidateStandards());
                                oneCheckVo.setValidateResult("");
                                oneCheckVo.setValidateSuccess(false);
                                failResult.add(oneCheckVo);
                                continue;
                            }
                            ValidateSqlForProductTaskDTO validateSqlForProductTaskDTO = new ValidateSqlForProductTaskDTO();
                            validateSqlForProductTaskDTO.setProductTaskId(task.getProductTaskId());
                            String[] values = task.getValidateDetailSql().split("\\|");
                            if (values.length > 1) {
                                String str = task.getValidateDetailSql().substring(values[0].length() + 1).trim();
                                validateSqlForProductTaskDTO.setSqlStr(AesUtil.encrypt(str));
                                validateSqlForProductTaskDTO.setSqlType(values[0].trim());
                            } else {
                                throw new RuntimeException("检测明细脚本维护异常,请联系产品进行修改 ： 待处理任务【" + task.getTaskTitle() + "】");
                            }
                            dtoList.add(validateSqlForProductTaskDTO);
                        }
                        if (ObjectUtil.isNotEmpty(dtoList)) {
                            sqlCheckApiDTO.setTaskDtoList(dtoList);
                            sqlCheckApiDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
                            sqlCheckApiDTO.setOrgId(hospitalInfo.getOrgId());
                            sqlCheckApiDTO.setHisOrgId(hospitalInfo.getOrgId());
                            ResponseResult<List<ValidateSqlForProductTaskVo>> responseResult = applicationCheckApi.validateSqlForProductTask(sqlCheckApiDTO);
                            log.info("checkFail接口返回数据:{}", JSONUtil.toJsonStr(responseResult));
                            // 组装检测失败的数据
                            if (responseResult.isSuccess() && CollectionUtil.isNotEmpty(responseResult.getData())) {
                                for (ValidateSqlForProductTaskVo v : responseResult.getData()) {
                                    OneCheckVo oneCheckVo = new OneCheckVo();
                                    ProjProductTask task = projProductTaskMapper.selectById(v.getProductTaskId());
                                    if (ObjectUtil.isNotEmpty(task)) {
                                        oneCheckVo.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                                        oneCheckVo.setHospitalName(hospitalInfo.getHospitalName());
                                        oneCheckVo.setYyProductId(task.getYyProductId());
                                        oneCheckVo.setProductName(productList.get(0).getName().endsWith(StrUtil.DASHED) ? productList.get(0).getName().substring(0, productList.get(0).getName().length() - 1) : productList.get(0).getName());
                                        oneCheckVo.setTaskTitle(task.getTaskTitle());
                                        oneCheckVo.setValidateStandards(task.getValidateStandards());
                                        oneCheckVo.setValidateResult(v.getResultStr());
                                        oneCheckVo.setValidateSuccess(false);
                                        failResult.add(oneCheckVo);
                                    }
                                }
                            } else {
                                throw new RuntimeException("数据异常," + responseResult.getMessage());
                            }
                        }
                    }
                    // 接口检测处理
                    if ("interface".equals(key) && ObjectUtil.isNotEmpty(interfaceCheckResultVoList)) {
                        // 接口检测的结果不为空的时 ， 处理数据
                        for (ValidateSqlForProductTaskVo v : interfaceCheckResultVoList) {
                            OneCheckVo oneCheckVo = new OneCheckVo();
                            ProjProductTask task = projProductTaskMapper.selectById(v.getProductTaskId());
                            oneCheckVo.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                            oneCheckVo.setHospitalName(hospitalInfo.getHospitalName());
                            oneCheckVo.setYyProductId(task.getYyProductId());
                            oneCheckVo.setProductName(productList.get(0).getName());
                            oneCheckVo.setTaskTitle(task.getTaskTitle());
                            oneCheckVo.setValidateStandards(task.getValidateStandards());
                            oneCheckVo.setValidateResult(v.getResultStr());
                            oneCheckVo.setValidateSuccess(false);
                            failResult.add(oneCheckVo);
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(failResult)) {
                oneCheckResultVO.setHasError(1);
                oneCheckResultVO.setFailDataList(failResult);
            } else {
                oneCheckResultVO.setHasError(0);
            }
        } catch (Exception e) {
            log.error("产品待处理任务检测失败数据组装异常:", e);
            throw new RuntimeException("产品待处理任务检测失败数据组装异常" + e.getMessage());
        }
    }

    /**
     * 待办进度
     *
     * @param dto
     * @return
     */
    @Override
    public Object selectProductTaskProgress(ProjProductTaskParam dto) {
        ProjProductTaskProgressResp resp = new ProjProductTaskProgressResp();
        dto.setTaskStatus(99);
        dto.setSurveyTitle(null);
        log.info("各产品准备工作，查询待办任务完成进度实际参数={}", JSON.toJSONString(dto));
        List<ProjProductTaskResp> list = projProductTaskMapper.selectProductTaskList(dto);
        if (list != null && list.size() > 0) {
            resp.setTaskTotalCount(list.size());
            List<ProjProductTaskResp> finishList = list.stream().filter(item -> item.getTaskStatus() == 1).collect(Collectors.toList());
            if (finishList != null && finishList.size() > 0) {
                resp.setTaskFinishCount(finishList.size());
            } else {
                resp.setTaskFinishCount(0);
            }
        } else {
            resp.setTaskTotalCount(0);
            resp.setTaskFinishCount(0);
        }
        return resp;
    }

    /**
     * 更新信息
     *
     * @param dto
     * @return
     */
    @Override
    public String updateReviewRecordDataByParamer(ProjProductTaskRecordParam dto) {
        List<ProjProjectReviewRecord> list = projProjectReviewRecordMapper.selectList(new QueryWrapper<ProjProjectReviewRecord>().eq("project_info_id", dto.getProjectInfoId()).eq("item_code", "entry_payment_instrument"));
        SysUserVO user = userHelper.getCurrentUser();
        if (list != null && list.size() > 0) {
            for (ProjProjectReviewRecord record : list) {
                record.setReviewResult(dto.getReviewResult());
                record.setReviewMemo(dto.getReviewMemo());
                record.setUpdaterId(user.getSysUserId());
                record.setUpdateTime(new Date());
                projProjectReviewRecordMapper.updateById(record);
            }
            try {
                ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(dto.getProjectInfoId());
                String reviewMemo = dto.getReviewResult() == 1 ? "通过" : "驳回";
                String content = "【" + projectInfo.getProjectName() + "】" + "APP支付开通审核结果为【" + reviewMemo + "】,审核备注：" + dto.getReviewMemo();
                MessageParam messageParam = new MessageParam();
                messageParam.setContent(content);
                messageParam.setProjectInfoId(projectInfo.getProjectInfoId());
                messageParam.setTitle("【" + projectInfo.getProjectName() + "】" + "APP支付开通审核结果提醒");
                messageParam.setMessageTypeId(4002L);
                messageParam.setMessageToCategory(MsgToCategory.TO_PRO_MGR.getCode());
                sendMessageService.sendMessage(messageParam);
            } catch (Exception e) {
                log.error("更新项目状态异常:", e);
            }

        }
        return "审核成功";
    }

    /**
     * 查询数据
     *
     * @param productTask
     * @return
     */
    @Override
    public ProjProductTaskResp selectTaskDataByTaskId(ProjProductTaskRecordParam productTask) {
        ProjProductTaskResp resultResp = new ProjProductTaskResp();
        ProjProductTaskParam dto = new ProjProductTaskParam();
        dto.setProductTaskId(productTask.getProductTaskId());
        log.info("手机端审核所需待办数据信息，实际参数={}", JSON.toJSONString(dto));
        List<ProjProductTaskResp> list = this.getProductTaskWithDictInfoList(dto);
        // 查询老项目id，客户id， 新项目id，新客户id
        if (list != null && list.size() > 0) {
            ProjProjectInfoVO projProjectInfoVO = projectInfoMapper.selectProjNewAndOldByNewProjId(list.get(0).getProjectInfoId());
            for (ProjProductTaskResp resp : list) {
                List<ProjProjectFile> allFlieList = projProductTaskMapper.selectAllFlieList(resp);
                if (allFlieList != null && allFlieList.size() > 0) {
                    for (ProjProjectFile file : allFlieList) {
                        file.setFilePath(file.getFilePath().replace("imsp.msuncloud.com", "imsp.msunhis.com"));
                    }
                }
                resp.setProjProjectFileList(allFlieList);
                // 判断项目是否已经上线，当上线时  赋值 online字段值
                ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq("project_info_id", resp.getProjectInfoId()));
                if (ObjectUtil.isNotEmpty(projProjectInfo) && ObjectUtil.isNotEmpty(projProjectInfo.getOnlineTime())) {
                    resp.setIsOnline(1);
                } else {
                    resp.setIsOnline(0);
                }
                if (ObjectUtil.isNotEmpty(projProjectInfoVO) && ObjectUtil.isNotEmpty(resp.getTaskExplainLink())) {
                    String projectId = projProjectInfoVO.getOldProjectId() != null ? String.valueOf(projProjectInfoVO.getOldProjectId()) : "";
                    String customerId = projProjectInfoVO.getOldCustomerId() != null ? String.valueOf(projProjectInfoVO.getOldCustomerId()) : "";
                    String urlLink = resp.getTaskExplainLink().replace("#{projectId}", projectId).replace("imsp.msuncloud.com", "imsp.msunhis.com").replace("#{customerId}", customerId);
                    resp.setTaskExplainLink(urlLink);
                }
                resp.setProjectName(projProjectInfoVO.getProjectName());
            }
            resultResp = list.get(0);
        } else {
            resultResp = new ProjProductTaskResp();
        }
        // 审核状态， 审核后 + 不是审核人员不展示审核按钮。
        // 审核人
        List<Long> sysUserIds = sysUserMapper.selectUserIdByAccount(null);
        // 审核状态
        List<ProjProjectReviewRecord> listReview = projProjectReviewRecordMapper.selectList(new QueryWrapper<ProjProjectReviewRecord>().eq("project_info_id", resultResp.getProjectInfoId()).eq("item_code", "entry_payment_instrument"));
        Integer reviewResult = null;
        if (listReview != null && listReview.size() > 0) {
            for (ProjProjectReviewRecord record : listReview) {
                reviewResult = record.getReviewResult();
            }
        }
        resultResp.setIsReview(ObjectUtil.isEmpty(reviewResult) || reviewResult != 1);
        return resultResp;
    }

    /**
     * 验证登录
     *
     * @param param
     * @return
     */
    @Override
    public Result<HisLoginCsmVo> verifyLoginBeforeCloudHealthRedirect(CsmParamerDTO param) {
        /*
           逻辑：
           ├── a. 有数据
           │   ├── 1) 登录失败
           │   │   ├── 重置密码，保存到交付平台
           │   │   └── 再次登录 无论是否成功
           │   │       └── 结束
           │   └── 2) 成功
           │       └── 结束
           └── b. 无数据
               ├── 兼容之前老数据，默认账号密码，构造统一判定逻辑
               ├── 1) 登录失败
               │   ├── 重置密码，保存到交付平台
               │   └── 环境错误，失败
               │       └── 结束
               └── 2) 成功
                   └── 结束
         */
        HisLoginCsmVo data = new HisLoginCsmVo();
        String name = "jfadmin";
        String pwd = "e2hueCdVFSwQ@";
        // 查询交付平台存储的账号密码
        List<ProjCustomerProjectTypeResp> typeList = projCloudAccountPasswordMapper.selectDataByCloudHospitalId(param);
        if (typeList != null && typeList.size() > 0) {
            ProjCustomerProjectTypeResp resp = typeList.get(0);
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(resp.getHospitalInfoId());
            List<ProjCloudAccountPassword> accountList = projCloudAccountPasswordMapper.selectList(new QueryWrapper<ProjCloudAccountPassword>()
                    .eq("customer_info_id", resp.getCustomInfoId())
                    .eq("project_type", resp.getProjectType())
                    .eq("is_deleted", NumberEnum.NO_0.num()));
            // a. 有数据
            if (accountList != null && accountList.size() > 0) {
                ProjCloudAccountPassword projCloudAccountPassword = accountList.get(0);
                name = projCloudAccountPassword.getAccountName();
                pwd = projCloudAccountPassword.getPassword();
                data = this.getLoginResult(projHospitalInfo, resp, name, pwd);
            } else {
                data = this.getLoginResult(projHospitalInfo, resp, name, pwd);
            }
        } else {
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(param.getHospitalInfoId());
            ProjCustomerProjectTypeResp resp = new ProjCustomerProjectTypeResp();
            resp.setProjectType(projHospitalInfo.getProjectType());
            resp.setCustomInfoId(projHospitalInfo.getCustomInfoId());
            resp.setHospitalInfoId(projHospitalInfo.getHospitalInfoId());
            data = this.getLoginResult(projHospitalInfo, resp, name, pwd);
        }
        log.warn("云健康登录：参数：{}，结果：{}", JSON.toJSONString(param), JSON.toJSONString(data));
        if (data.getPwd() == null || "".equals(data.getPwd())) {
            data.setPwd(pwd);
        }
        if (data.getUserName() == null || "".equals(data.getUserName())) {
            data.setUserName(name);
        }
        return Result.success(data);
    }

    /**
     * @return
     */
    @Override
    public Result compensationInterface() {
        List<ProjCustomerProjectTypeResp> typeList = projCloudAccountPasswordMapper.selectAllCustomerData();
        for (ProjCustomerProjectTypeResp resp : typeList) {
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(resp.getHospitalInfoId());
            // 重置密码
            String pwd = this.resetPassword(projHospitalInfo, "jfadmin");
            try {
                if (pwd != null && !"".equals(pwd)) {
                    List<ProjCloudAccountPassword> accountList = projCloudAccountPasswordMapper.selectList(new QueryWrapper<ProjCloudAccountPassword>().eq("customer_info_id", resp.getCustomInfoId()).eq("project_type", resp.getProjectType()).eq("is_deleted", NumberEnum.NO_0.num()));
                    if (accountList != null && accountList.size() > 0) {
                        for (ProjCloudAccountPassword projCloudAccountPassword : accountList) {
                            projCloudAccountPassword.setPassword(pwd);
                            projCloudAccountPasswordMapper.updateById(projCloudAccountPassword);
                        }
                    } else {
                        // 保存密码
                        ProjCloudAccountPassword projCloudAccountPassword = new ProjCloudAccountPassword();
                        projCloudAccountPassword.setCloudAccountId(SnowFlakeUtil.getId());
                        projCloudAccountPassword.setAccountName("jfadmin");
                        projCloudAccountPassword.setPassword(pwd);
                        projCloudAccountPassword.setCustomerInfoId(resp.getCustomInfoId());
                        projCloudAccountPassword.setProjectType(resp.getProjectType());
                        projCloudAccountPasswordMapper.insert(projCloudAccountPassword);
                    }
                }
            } catch (Exception e) {
                log.error("保存密码失败", e);
            }
        }
        return Result.success();
    }

    /**
     * 获取登录结果
     *
     * @param projHospitalInfo
     * @param name
     * @param pwd
     * @return
     */
    private HisLoginCsmVo getLoginResult(ProjHospitalInfo projHospitalInfo, ProjCustomerProjectTypeResp resp, String name, String pwd) {
        HisLoginCsmVo resultLoginVo = new HisLoginCsmVo();
        HisLoginVO selectLoginVos = new HisLoginVO();
        // 第一次登录
        selectLoginVos = this.getLoginZongResult(projHospitalInfo, name, pwd);
        if (selectLoginVos != null && selectLoginVos.getToken() != null && !"".equals(selectLoginVos.getToken())) {
            BeanUtils.copyProperties(selectLoginVos, resultLoginVo);
            resultLoginVo.setName(name);
            resultLoginVo.setPwd(pwd);
            return resultLoginVo;
        } else {
            // 重置密码
            pwd = this.resetPassword(projHospitalInfo, name);
            try {
                if (pwd != null && !"".equals(pwd)) {
                    List<ProjCloudAccountPassword> accountList = projCloudAccountPasswordMapper.selectList(new QueryWrapper<ProjCloudAccountPassword>().eq("customer_info_id", resp.getCustomInfoId()).eq("project_type", resp.getProjectType()).eq("is_deleted", NumberEnum.NO_0.num()));
                    if (accountList != null && accountList.size() > 0) {
                        for (ProjCloudAccountPassword projCloudAccountPassword : accountList) {
                            projCloudAccountPassword.setPassword(pwd);
                            projCloudAccountPasswordMapper.updateById(projCloudAccountPassword);
                        }
                    } else {
                        // 保存密码
                        ProjCloudAccountPassword projCloudAccountPassword = new ProjCloudAccountPassword();
                        projCloudAccountPassword.setCloudAccountId(SnowFlakeUtil.getId());
                        projCloudAccountPassword.setAccountName(name);
                        projCloudAccountPassword.setPassword(pwd);
                        projCloudAccountPassword.setCustomerInfoId(resp.getCustomInfoId());
                        projCloudAccountPassword.setProjectType(resp.getProjectType());
                        projCloudAccountPasswordMapper.insert(projCloudAccountPassword);
                    }
                }
            } catch (Exception e) {
                log.error("保存密码失败", e);
            }
            selectLoginVos = this.getLoginZongResult(projHospitalInfo, name, pwd);
            log.warn("云健康登录结果：{}", JSON.toJSONString(selectLoginVos));
            if (selectLoginVos != null && selectLoginVos.getToken() != null && !"".equals(selectLoginVos.getToken())) {
                BeanUtils.copyProperties(selectLoginVos, resultLoginVo);
                resultLoginVo.setName(name);
                resultLoginVo.setPwd(pwd);
                return resultLoginVo;
            }
        }
        return resultLoginVo;
    }

    /**
     * 重置密码
     *
     * @param projHospitalInfo
     * @param name
     * @return
     */
    private String resetPassword(ProjHospitalInfo projHospitalInfo, String name) {
        String pwd = null;
        try {
            ProductEncryptDto dto = new ProductEncryptDto();
            dto.setHisOrgId(projHospitalInfo.getOrgId());
            dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
            dto.setProductCode(name);
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            log.info("调用云健康resetDefaultPassword的参数1:{},", dto);
            ResponseResult<String> responseResult = systemSettingApi.resetDefaultPassword(dto);
            log.info("调用resetDefaultPassword返回的结果1:{}", JSONObject.toJSONString(responseResult));
            pwd = responseResult.getData();
            log.info("调用resetDefaultPassword返回的参数1:{}", pwd);
        } catch (Exception e) {
            log.error("登录异常", e);
        }
        return pwd;
    }

    /**
     * 云健康登录
     *
     * @param projHospitalInfo
     * @param name
     * @param pwd
     * @return
     */
    private HisLoginVO getLoginZongResult(ProjHospitalInfo projHospitalInfo, String name, String pwd) {
        HisLoginVO resultLoginVo = new HisLoginVO();
        try {
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            //根据id 查询真实地址
            Long hospitalId = projHospitalInfo.getCloudHospitalId();
            Long orgId = projHospitalInfo.getOrgId();
            SystemConfigDto dtos = new SystemConfigDto();
            dtos.setHospitalId(hospitalId);
            dtos.setHisOrgId(orgId);
            List<AppLoginDTO> list = new ArrayList<>();
            AppLoginDTO app = new AppLoginDTO();
            String host = "";
            String host1 = projHospitalInfo.getCloudDomain().replace("http://", StrUtil.EMPTY).replace("https://", StrUtil.EMPTY);
            if (host1.contains(":")) {
                host = host1.split(":")[0];
            } else {
                host = host1;
            }
            app.setUserName(name);
            app.setPasswd(pwd);
            app.setFromUrl(host);
            list.add(app);
            dtos.setData(list);
            log.info("调用云健康的参数1:{},", dtos);
            ResponseResult<HisLoginVO> responseResult = systemSettingApi.hisLogin(dtos);
            log.info("调用hisLogin返回的结果1:{}", JSONObject.toJSONString(responseResult));
            resultLoginVo = responseResult.getData();
            log.info("调用hisLogin返回的参数1:{}", resultLoginVo);
        } catch (Exception e) {
            throw new CustomException("登录异常", e);
        }
        return resultLoginVo;
    }

    /**
     * 通过接口检测
     *
     * @param interfaceList
     * @return
     */
    private List<ValidateSqlForProductTaskVo> oneCheckCloudInterface(List<ProjProductTaskResp> interfaceList) {
        List<ValidateSqlForProductTaskVo> failDataList = new ArrayList<>();
        // 患者智能服务接口名称
        String hzznInterFace = "getDeliverByCustomer";
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(interfaceList.get(0).getHospitalInfoId());
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        Long projectInfoId = interfaceList.get(0).getProjectInfoId();
        List<ProductTaskSqlCheckDTO> checkDTOS = new ArrayList<>();
        for (ProjProductTaskResp resp : interfaceList) {
            if (hzznInterFace.equals(resp.getVerifySqlText().trim())) {
                try {
                    getDeliverByCustomer(projectInfoId, resp);
                } catch (Exception e) {
                    log.error("接口检测异常:{}", e);
                }
            } else if ("onlineVerification".equals(resp.getVerifySqlText().trim())) {
                try {
                    onlineVerification(projectInfoId, resp);
                } catch (Exception e) {
                    log.error("医保核心通过接口进行待办检测，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
            } else {
                ProductTaskSqlCheckDTO dto = new ProductTaskSqlCheckDTO();
                dto.setTaskCode(resp.getTaskCode());
                dto.setSqlStr(resp.getVerifySqlText());
                checkDTOS.add(dto);
            }
        }
        domainHolder.refresh(domainMap);
        BatchDeleteGeneralDTO<ProductTaskSqlCheckDTO> batchDeleteGeneralDTO = new BatchDeleteGeneralDTO<>();
        batchDeleteGeneralDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        batchDeleteGeneralDTO.setHisOrgId(hospitalInfo.getOrgId());
        batchDeleteGeneralDTO.setData(checkDTOS);
        try {
            ResponseResult<List<ProductTaskInterfaceResultVO>> responseResult = dataPreparationApi.executeProductTaskInterfaceDb(batchDeleteGeneralDTO);
            log.info("一键检测，接口检测===返回结果：{}, 检测参数：{}", responseResult, JSONUtil.toJsonStr(batchDeleteGeneralDTO));
            if (responseResult == null || CollectionUtils.isEmpty(responseResult.getData())) {
                return failDataList;
            } else {
                if (!responseResult.getSuccess()) {
                    throw new RuntimeException("一键检测，接口检测===结果返回异常" + responseResult.getMessage());
                }
            }
            List<ProductTaskInterfaceResultVO> resultList = responseResult.getData();
            if (resultList != null && resultList.size() > 0) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("taskCodeList", resultList);
                resultMap.put("projectInfoId", projectInfoId);
                projProductTaskMapper.updateTaskStatus(resultMap);
                // 获取检测失败时的 原因
                for (ProductTaskInterfaceResultVO v : resultList) {
                    List<ProjProductTask> productTasks = projProductTaskMapper.selectList(new QueryWrapper<ProjProductTask>().eq("task_code", v.getTaskCode()).eq("hospital_info_id", hospitalInfo.getHospitalInfoId()));
                    ValidateSqlForProductTaskVo validateSqlForProductTaskVo = new ValidateSqlForProductTaskVo();
                    validateSqlForProductTaskVo.setProductTaskId(productTasks.get(0).getProductTaskId());
                    validateSqlForProductTaskVo.setResultStr(v.getTaskReason());
                    failDataList.add(validateSqlForProductTaskVo);
                }
            }
        } catch (Exception e) {
            log.error("云健康接口检测异常:{}", e);
        }
        return failDataList;
    }

    /**
     * 患者智能服务接口数据
     *
     * @param projectInfoId
     * @param resp
     * @return
     */
    private int getDeliverByCustomer(Long projectInfoId, ProjProductTaskResp resp) {
        ProjProjectInfoVO projProjectInfoVO = projectInfoMapper.selectProjNewAndOldByNewProjId(projectInfoId);
        String projectId = projProjectInfoVO.getOldProjectId() != null ? String.valueOf(projProjectInfoVO.getOldProjectId()) : "";
        String customerId = projProjectInfoVO.getOldCustomerId() != null ? String.valueOf(projProjectInfoVO.getOldCustomerId()) : "";
        List<HzznDeliver> res = hzznDeliverMapper.selectList(new QueryWrapper<HzznDeliver>().eq("project_id", projectId).eq("customer_id", customerId));
        if (ObjectUtil.isNotEmpty(res) && res.size() > 0) {
            HzznDeliver hzznResp = res.get(0);
            if (ObjectUtil.isNotEmpty(hzznResp) && ObjectUtil.isNotEmpty(hzznResp.getState()) && (hzznResp.getState() == 4 || hzznResp.getState() == 5)) {
                List<ProductTaskInterfaceResultVO> resultList = new ArrayList<>();
                ProductTaskInterfaceResultVO vo = new ProductTaskInterfaceResultVO();
                ProjHospitalInfo hzznHospitalInfo = projHospitalInfoMapper.selectById(resp.getHospitalInfoId());
                vo.setHospitalId(hzznHospitalInfo.getCloudHospitalId());
                vo.setTaskCode(resp.getTaskCode());
                vo.setStatus(1);
                resultList.add(vo);
                log.error("返回结果：{}" + resultList);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("taskCodeList", resultList);
                resultMap.put("projectInfoId", projectInfoId);
                return projProductTaskMapper.updateTaskStatus(resultMap);
            }
        }
        return 0;
    }

    private int onlineVerification(Long projectInfoId, ProjProductTaskResp resp) {
        List<MedInsurInSettleResultDTO> medInsurInSettleResultDTOS = medicalInsuranceService.onlineVerification2(resp.getHospitalInfoId());
        if (CollectionUtils.isEmpty(medInsurInSettleResultDTOS)) {
            return 0;
        }
        List<ProductTaskInterfaceResultVO> resultList = new ArrayList<>();
        for (MedInsurInSettleResultDTO item : medInsurInSettleResultDTOS) {
            ProductTaskInterfaceResultVO vo = new ProductTaskInterfaceResultVO();
            vo.setHospitalId(item.getHospitalId());
            vo.setTaskCode(resp.getTaskCode());
            vo.setStatus(Boolean.TRUE.equals(item.getSuccess()) ? 1 : 0);
            vo.setTaskResult(Boolean.TRUE.equals(item.getSuccess()) ? "成功" : "医保核心接口返回检测未通过，缺少医保结算成功数据");
            resultList.add(vo);
        }
        log.error("返回结果：{}", resultList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("taskCodeList", resultList);
        resultMap.put("projectInfoId", projectInfoId);
        return projProductTaskMapper.updateTaskStatus(resultMap);
    }

    /**
     * 查询云健康数据
     *
     * @param respList
     */
    private int oneCheckCloud(List<ProjProductTaskResp> respList) {
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(respList.get(0).getHospitalInfoId());
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        Long projectInfoId = respList.get(0).getProjectInfoId();
        domainHolder.refresh(domainMap);
        BatchDeleteGeneralDTO<ProductTaskSqlCheckDTO> batchDeleteGeneralDTO = new BatchDeleteGeneralDTO<>();
        batchDeleteGeneralDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        batchDeleteGeneralDTO.setHisOrgId(hospitalInfo.getOrgId());
        List<ProductTaskSqlCheckDTO> checkDTOS = new ArrayList<>();
        for (ProjProductTaskResp resp : respList) {
            ProductTaskSqlCheckDTO dto = new ProductTaskSqlCheckDTO();
            String sqlTextValue = resp.getVerifySqlText();
            String[] values = sqlTextValue.split("\\|");
            String sqlText = null;
            String databaseName = null;
            if (values.length > 1) {
                sqlText = values[1].trim();
                databaseName = values[0].trim();
            }
            if (StrUtil.isBlank(sqlText)) {
                throw new RuntimeException(resp.getTaskTitle() + ",sqlText为空");
            }
            if (StrUtil.isBlank(databaseName)) {
                throw new RuntimeException("databaseName为空");
            }
            dto.setSqlStr(AesUtil.encrypt(sqlText));
            dto.setDataSourece(databaseName);
            dto.setTaskCode(resp.getTaskCode());
            checkDTOS.add(dto);
        }
        batchDeleteGeneralDTO.setData(checkDTOS);
        ResponseResult<List<JSONObject>> responseResult = null;
        try {
            responseResult = dataPreparationApi.executeChisOrChisappDb(batchDeleteGeneralDTO);
        } catch (Throwable e) {
            String err = StrUtil.format("一键检测调用dataPreparationApi.executeChisOrChisappDb({})失败", JSON.toJSONString(batchDeleteGeneralDTO));
            log.error(err, e);
            throw new CustomException(err, e);
        }
        log.warn("返回结果：{}", JSON.toJSONString(responseResult));
        if (responseResult == null || CollectionUtils.isEmpty(responseResult.getData())) {
            return 0;
        } else {
            if (!responseResult.getSuccess()) {
                throw new RuntimeException("结果返回异常" + JSON.toJSONString(responseResult));
            }
        }
        String jsonString = JSON.toJSONString(responseResult.getData());
        TypeReference<List<ProductTaskInterfaceResultVO>> typeReference = new TypeReference<List<ProductTaskInterfaceResultVO>>(ProductTaskInterfaceResultVO.class) {
        };
        List<ProductTaskInterfaceResultVO> resultList = JSON.parseObject(jsonString, typeReference);
        if (CollUtil.isNotEmpty(resultList)) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("taskCodeList", resultList);
            resultMap.put("projectInfoId", projectInfoId);
            return projProductTaskMapper.updateTaskStatus(resultMap);
        }
        return 0;
    }


    private List<TDuckTaskFileInfo> convertFileInfo(String surveyValue) {
        String replaced = surveyValue.trim().replace("[", "").replace("]", "");
        String[] strings = replaced.split("},");
        List<TDuckTaskFileInfo> fileInfoList = new ArrayList<>();
        for (String str : strings) {
            String replace = str.trim().replace("{", "").replace("}", "");
            String[] split = replace.split(",");
            TDuckTaskFileInfo file = new TDuckTaskFileInfo();
            file.setName(split[0].trim().replace("name=", ""));
            file.setUrl(split[1].trim().replace("url=", ""));
            fileInfoList.add(file);
        }
        return fileInfoList;
    }

    @Override
    public List<ProjProductTaskResp> getProductTaskWithDictInfoList(ProjProductTaskParam param) {
        List<ProjProductTaskResp> productTaskList = projProductTaskMapper.selectProductTaskList(param);
        if (CollectionUtils.isEmpty(productTaskList)) {
            return new ArrayList<>();
        }
        productTaskList.forEach(item -> {
            if (item.getSurveyValue().startsWith("[")) {
                List<TDuckTaskFileInfo> fileInfoList = new ArrayList<>();
                try {
                    // 老的数据格式
                    if (item.getSurveyValue().contains("name=")) {
                        fileInfoList = this.convertFileInfo(item.getSurveyValue());
                    } else {
                        fileInfoList = JSON.parseArray(JSON.toJSONString(item.getSurveyValue()), TDuckTaskFileInfo.class);
                    }
                } catch (Exception e) {
                    log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
                if (!CollectionUtils.isEmpty(fileInfoList)) {
                    List<ProjProjectFile> collect = fileInfoList.stream().map(fileInfo -> {
                        ProjProjectFile file = new ProjProjectFile();
                        file.setFileName(fileInfo.getName());
                        file.setFilePath(fileInfo.getUrl());
                        return file;
                    }).collect(Collectors.toList());
                    item.setProjProjectFileList(collect);
                }
            }
        });
        Result<List<DictTaskInfoVO>> dictTaskInfo = tduckManage.getDictTaskInfo(new GetDictTaskInfoParam());
        // 不是701，继续使用数据库查询结果
        if (dictTaskInfo.getCode() != 701) {
            return productTaskList;
        }
        List<DictTaskInfoVO> dictTaskInfoList = dictTaskInfo.getData();
        // 是701但是没查询到数据，将对应信息置空
        if (CollectionUtils.isEmpty(dictTaskInfoList)) {
            productTaskList.forEach(item -> {
                item.setTaskTitle(null);
                item.setTaskValidateType(null);
                item.setVerifySqlText(null);
                item.setTaskPageUrl(null);
                item.setCloudProductCode(null);
                item.setTaskDetail(null);
                item.setTaskExplainLink(null);
                item.setValidateStandards(null);
                item.setValidateDetailSql(null);
            });
            return productTaskList;
        }
        // 正常获取到数据
        productTaskList.forEach(item -> {
            DictTaskInfoVO dictTaskInfoVO = dictTaskInfoList.stream().filter(dictTask -> item.getTaskCode().equals(dictTask.getItemCode())).findFirst().orElse(null);
            if (dictTaskInfoVO != null) {
                item.setTaskTitle(dictTaskInfoVO.getItemName());
                item.setTaskValidateType(dictTaskInfoVO.getTaskValidateType());
                item.setVerifySqlText(dictTaskInfoVO.getTaskValidateInfo());
                item.setTaskPageUrl(dictTaskInfoVO.getTaskPageUrl());
                item.setCloudProductCode(dictTaskInfoVO.getCloudProductCode());
                item.setTaskDetail(dictTaskInfoVO.getTaskDetail());
                item.setTaskExplainLink(dictTaskInfoVO.getTaskExplainLink());
                item.setValidateStandards(dictTaskInfoVO.getValidateStandards());
                item.setValidateDetailSql(dictTaskInfoVO.getValidateDetailSql());
            }
        });
        // 调研标题非空时，进行模糊查询
        if (StringUtils.isNotBlank(param.getSurveyTitle())) {
            productTaskList = productTaskList.stream().filter(item -> {
                if (StringUtils.isNotBlank(item.getTaskTitle()) && item.getTaskTitle().contains(param.getSurveyTitle())) {
                    return true;
                }
                return StringUtils.isNotBlank(item.getSurveyTitle()) && item.getSurveyTitle().contains(param.getSurveyTitle());
            }).collect(Collectors.toList());
        }
        // 待办检测方式方式非空时，仅查询对应的检测方式的待办
        if (StringUtils.isNotBlank(param.getTaskValidateType())) {
            productTaskList = productTaskList.stream().filter(item -> param.getTaskValidateType().equals(item.getTaskValidateType()) && StringUtils.isNotBlank(item.getVerifySqlText())).collect(Collectors.toList());
        }
        return productTaskList;
    }

    @Override
    public List<ProjProductTask> getProductTaskWithDictInfo(ProjProductTaskParam param) {
        List<ProjProductTaskResp> productTaskWithDictInfoList = this.getProductTaskWithDictInfoList(param);
        if (CollectionUtils.isEmpty(productTaskWithDictInfoList)) {
            return new ArrayList<>();
        }
        return productTaskWithDictInfoList.stream().map(item -> ProjProductTask.builder().productTaskId(item.getProductTaskId()).productTaskClassCode(item.getProductTaskClassCode()).productTaskClassName(item.getProductTaskClassName()).surveyTitle(item.getSurveyTitle()).surveyValue(item.getSurveyValue()).taskTitle(item.getTaskTitle()).taskDetail(item.getTaskDetail()).remark(item.getRemark()).yyProductId(item.getYyProductId()).hospitalInfoId(item.getHospitalInfoId()).sortNo(item.getSortNo()).projectInfoId(item.getProjectInfoId()).taskPageUrl(item.getTaskPageUrl()).cloudProductCode(item.getCloudProductCode()).taskStatus(item.getTaskStatus()).userId(item.getUserId()).jobPlanTime(item.getJobPlanTime()).taskResult(item.getTaskResult()).verifySqlText(item.getVerifySqlText()).taskValidateType(item.getTaskValidateType()).taskCode(item.getTaskCode()).progressDesc(item.getProgressDesc()).taskExplainLink(item.getTaskExplainLink()).validateStandards(item.getValidateStandards()).validateDetailSql(item.getValidateDetailSql()).build()).collect(Collectors.toList());
    }

    /**
     * 查询打印平台报表打印节点数据
     *
     * @param csmDTO
     * @return
     */
    @Override
    public ResponseData getPrintCodeResultListByParamer(CsmDTO csmDTO) {
        List<HospitalPrintCodeVO> hospitalPrintCodeVOList = new ArrayList<>();
        if (csmDTO.getHospitalId() != null && csmDTO.getHospitalId() > 0) {
            hospitalPrintCodeVOList = projSurveyReportMapper.getPrintCodeResultListByParamer(csmDTO);
        } else {
            return ResponseData.error("医院ID不能为空");
        }
        return ResponseData.success(hospitalPrintCodeVOList);
    }

    /**
     * 给负责人发送消息
     *
     * @param csmDTO
     * @return
     */
    @Override
    public ResponseData sendReportPrintMsg(CsmDTO csmDTO) {
        // 判定参数必填
        if (csmDTO.getHospitalId() == null || csmDTO.getHospitalId() <= 0) {
            return ResponseData.error("医院ID(hospitalId)不能为空");
        }
        if (csmDTO.getPrintCode() == null || "".equals(csmDTO.getPrintCode())) {
            return ResponseData.error("报表节点（printCode）不能为空");
        }
        if (csmDTO.getTitle() == null || "".equals(csmDTO.getTitle())) {
            return ResponseData.error("标题(title)不能为空");
        }
        // 内容不能未空
        if (csmDTO.getDescription() == null || "".equals(csmDTO.getDescription())) {
            return ResponseData.error("描述(description)不能为空");
        }
        // 根据医院id+ 打印节点查询出对应的负责人   无负责人的话，不发送消息
        List<ProjSurveyReport> list = projSurveyReportMapper.selectSurveyReportByHospitalIdAndPrintCode(csmDTO);
        if (list != null && list.size() > 0) {
            // 发送企业微信消息给
            String content = csmDTO.getDescription();
            MessageParam messageParam = new MessageParam();
            messageParam.setProjectInfoId(list.get(0).getProjectInfoId());
            messageParam.setContent(content);
            messageParam.setTitle(csmDTO.getTitle());
            // 发送消息的人 报表节点负责人sys_user_id
            List<Long> sysUserIds = list.stream().map(ProjSurveyReport::getMakeUserId).collect(Collectors.toList());
            String userIds = sysUserIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            messageParam.setMessageTypeId(7003L);
            messageParam.setMessageToCategory(MsgToCategory.SYS.getCode());
            messageParam.setSysUserIds(sysUserIds);
            sendMessageService.sendMessage(messageParam);
            return ResponseData.success("成功");
        } else {
            return ResponseData.error("消息发送失败，未找到责任人");
        }
    }

    /**
     * 查询负责人
     *
     * @param csmDTO
     * @return
     */
    @Override
    public ResponseData getPersonByParamer(CsmDTO csmDTO) {
        // 判定参数必填
        if (csmDTO.getHospitalId() == null || csmDTO.getHospitalId() <= 0) {
            return ResponseData.error("医院ID(hospitalId)不能为空");
        }
        if (csmDTO.getPrintCode() == null || "".equals(csmDTO.getPrintCode())) {
            return ResponseData.error("报表节点（printCode）不能为空");
        }
        // 根据医院id+ 打印节点查询出对应的负责人   无负责人的话，不发送消息
        List<ProjSurveyReport> list = projSurveyReportMapper.selectSurveyReportByHospitalIdAndPrintCode(csmDTO);
        if (list != null && list.size() > 0) {
            List<SysUser> userList = sysUserService.selectBySysUserIdList(list.stream().map(ProjSurveyReport::getMakeUserId).collect(Collectors.toList()));
            List<PersonPhoneVO> returnList = new ArrayList<>();
            if (userList != null && userList.size() > 0) {
                for (SysUser sysUser : userList) {
                    PersonPhoneVO personPhoneVO = new PersonPhoneVO();
                    personPhoneVO.setUserName(sysUser.getUserName());
                    personPhoneVO.setPhone(sysUser.getPhone());
                    returnList.add(personPhoneVO);
                }
                return ResponseData.success(returnList);
            }

        }
        return ResponseData.success(new ArrayList<>());
    }

    /**
     * 上传单据打印效果
     *
     * @param reportRenderingsDTO
     * @return
     */
    @Override
    public ResponseData saveMedicalPrinterConfigRenderings(ReportRenderingsDTO reportRenderingsDTO) {
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq("cloud_hospital_id", reportRenderingsDTO.getHospitalId()).eq("org_id", reportRenderingsDTO.getHisOrgId()).last("limit 1"));
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        log.info("上传单据打印效果参数:{}", JSON.toJSONString(reportRenderingsDTO));
        ResponseResult result = reportApi.saveMedicalPrinterConfigRenderings(reportRenderingsDTO);
        log.info("上传单据打印效果结果:{}", JSON.toJSONString(result));
        if (result.isSuccess()) {
            return ResponseData.success(result);
        } else {
            return ResponseData.error(result.getMessage());
        }
    }

    /**
     * 上传协助记录附件
     *
     * @param reportRenderingsDTO
     * @return
     */
    @Override
    public ResponseData uploadAssistAttachment(ReportRenderingsListDTO reportRenderingsDTO) {
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq("cloud_hospital_id", reportRenderingsDTO.getHospitalId()).eq("org_id", reportRenderingsDTO.getHisOrgId()).last("limit 1"));
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        log.info("上传协助记录附件:{}", JSON.toJSONString(reportRenderingsDTO));
        ResponseResult result = reportApi.uploadAssistAttachment(reportRenderingsDTO);
        log.info("上传协助记录附件:{}", JSON.toJSONString(result));
        if (result.isSuccess()) {
            return ResponseData.success(result);
        } else {
            return ResponseData.error(result.getMessage());
        }
    }

    @Override
    public Result sheetPendingOrder(ProjProductTaskResp dto) {
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
        if (ObjectUtil.isEmpty(hospitalInfo) || hospitalInfo.getCloudHospitalId() == null) {
            return Result.fail("未查询到医院信息，无法进行此操作！");
        }
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        ImportSheetNurceImspDTO importSheetNurceImspDTO = new ImportSheetNurceImspDTO();
        importSheetNurceImspDTO.setHisOrgId(hospitalInfo.getOrgId());
        importSheetNurceImspDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        String[] strs = dto.getSurveyValue().split(",");
        importSheetNurceImspDTO.setSheetNameList(Arrays.asList(strs));
        ResponseResult responseResult = csmApi.sheetPendingOrderCsm(importSheetNurceImspDTO);
        if (responseResult.isSuccess()) {
            // 调用成功后进行更新状态
            ProjProductTask task = projProductTaskMapper.selectById(dto.getProductTaskId());
            task.setTaskStatus(NumberEnum.NO_1.num());
            projProductTaskMapper.updateById(task);
            return Result.success();
        } else {
            log.error("sheetPendingOrderCsm接口调用失败", responseResult);
            throw new CustomException("挂单失败" + responseResult.getMessage());
        }
    }
}
