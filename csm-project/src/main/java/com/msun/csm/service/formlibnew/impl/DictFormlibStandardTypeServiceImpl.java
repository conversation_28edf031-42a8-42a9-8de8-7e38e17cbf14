package com.msun.csm.service.formlibnew.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.msun.csm.dao.entity.formlibnew.DictFormlibStandardType;
import com.msun.csm.dao.mapper.formlibnew.DictFormlibStandardTypeMapper;
import com.msun.csm.service.formlibnew.DictFormlibStandardTypeService;

/**
 * <AUTHOR>
 * @description 针对表【dict_formlib_standard_type(表单标准名称字典)】的数据库操作Service实现
 * @createDate 2025-07-14 13:47:02
 */
@Service
public class DictFormlibStandardTypeServiceImpl extends ServiceImpl<DictFormlibStandardTypeMapper, DictFormlibStandardType> implements DictFormlibStandardTypeService {

}




