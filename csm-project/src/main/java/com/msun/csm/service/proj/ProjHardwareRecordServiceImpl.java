package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.log4j.Log4j2;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.projectfile.ProjectFileTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictHardwareInfo;
import com.msun.csm.dao.entity.proj.ProjHardwareProduct;
import com.msun.csm.dao.entity.proj.ProjHardwareRecord;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.dict.DictHardwareInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHardwareProductMapper;
import com.msun.csm.dao.mapper.proj.ProjHardwareRecommendMapper;
import com.msun.csm.dao.mapper.proj.ProjHardwareRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.report.ProjHospitalTerminalConfigMapper;
import com.msun.csm.model.dto.HardwareFinishDTO;
import com.msun.csm.model.dto.ProjHardwareRecordDTO;
import com.msun.csm.model.dto.ProjHardwareRecordImportDTO;
import com.msun.csm.model.dto.ProjHardwareRecordPageDTO;
import com.msun.csm.model.dto.ProjProductHardwareImportDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigPageReq;
import com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigResp;
import com.msun.csm.model.vo.ProjHardwareRecommendVO;
import com.msun.csm.model.vo.ProjHardwareRecordVO;
import com.msun.csm.model.vo.ProjProjectFileVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.EasyExcelData;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/14
 */

@Service
@Log4j2
public class ProjHardwareRecordServiceImpl implements ProjHardwareRecordService {

    @Resource
    private DictHardwareInfoMapper dictHardwareInfoMapper;

    @Resource
    private ProjHospitalTerminalConfigMapper projHospitalTerminalConfigMapper;

    @Resource
    private ProjHardwareRecordMapper projHardwareRecordMapper;

    @Resource
    private ProjHardwareProductMapper projHardwareProductMapper;

    @Resource
    private ProjHospitalInfoService projHospitalInfoService;

    @Resource
    private ProjHardwareRecommendMapper projHardwareRecommendMapper;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ProjProductBacklogService productBacklogService;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    /**
     * 获取小硬件字典列表
     *
     * @param hardwareName
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> selectHardwareInfo(String hardwareName) {
        return Result.success(dictHardwareInfoMapper.selectHardwareInfo(hardwareName));
    }

    /**
     * 查询小硬件采购清单
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Result<PageInfo<ProjHardwareRecordVO>> selectByPage(ProjHardwareRecordPageDTO pageDTO) {
        List<ProjHardwareRecordVO> hardwareRecordList = PageHelperUtil.queryPage(pageDTO.getPageNum(), pageDTO.getPageSize(), page -> projHardwareRecordMapper.selectByPage(pageDTO));
        for (ProjHardwareRecordVO hardwareRecord : hardwareRecordList) {
            //查询小硬件与产品对照关系
            List<ProjHardwareProduct> productList = projHardwareProductMapper.selectList(new QueryWrapper<ProjHardwareProduct>()
                    .eq("hardware_info_id", hardwareRecord.getHardwareInfoId()));
            if (CollectionUtil.isNotEmpty(productList)) {
                List<Long> productIds = productList.stream().map(pd -> pd.getYyProductId()).collect(Collectors.toList());
                hardwareRecord.setYyProductIds(productIds);
            }
        }
        return Result.success(new PageInfo<>(hardwareRecordList));
    }

    /**
     * 新增小硬件调研记录
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateHardwareRecord(ProjHardwareRecordDTO dto) {
        //查询小硬件字典信息是否已存在
        if (ObjectUtil.isEmpty(dto.getHardwareInfoId())) {
            DictHardwareInfo hardwareInfo = dictHardwareInfoMapper.selectOne(new QueryWrapper<DictHardwareInfo>()
                    .eq("hardware_name", dto.getHardwareName())
                    .last(" limit 1"));
            if (ObjectUtil.isEmpty(hardwareInfo)) {
                //保存小硬件字典信息
                DictHardwareInfo dictHardwareInfo = new DictHardwareInfo();
                BeanUtil.copyProperties(dto, dictHardwareInfo);
                dictHardwareInfo.setHardwareInfoId(SnowFlakeUtil.getId());
                dictHardwareInfoMapper.insert(dictHardwareInfo);
                dto.setHardwareInfoId(dictHardwareInfo.getHardwareInfoId());
            } else {
                dto.setHardwareInfoId(hardwareInfo.getHardwareInfoId());
            }
        }
        //保存调研记录信息
        ProjHardwareRecord hardwareRecord = new ProjHardwareRecord();
        BeanUtil.copyProperties(dto, hardwareRecord);
        if (ObjectUtil.isEmpty(dto.getHardwareRecordId())) {
            hardwareRecord.setHardwareRecordId(SnowFlakeUtil.getId());
            projHardwareRecordMapper.insert(hardwareRecord);
            projProjectPlanService.updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(dto.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_HARDWARE);
        } else {
            projHardwareRecordMapper.updateById(hardwareRecord);
        }
        return Result.success();
    }

    /**
     * 查看小硬件调研记录
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjHardwareRecordVO> viewHardwareRecord(ProjHardwareRecordDTO dto) {
        ProjHardwareRecordVO projHardwareRecordVO = new ProjHardwareRecordVO();
        ProjHardwareRecord hardwareRecord = projHardwareRecordMapper.selectById(dto.getHardwareRecordId());
        BeanUtil.copyProperties(hardwareRecord, projHardwareRecordVO);
        //查询小硬件名称
        projHardwareRecordVO.setHardwareName(dictHardwareInfoMapper.selectById(hardwareRecord.getHardwareInfoId()).getHardwareName());
        //查询小硬件与产品对照关系
        List<ProjHardwareProduct> productList = projHardwareProductMapper.selectList(new QueryWrapper<ProjHardwareProduct>()
                .eq("hardware_info_id", hardwareRecord.getHardwareInfoId()));
        if (CollectionUtil.isEmpty(productList)) {
            return Result.success(projHardwareRecordVO);
        }
        List<Long> productIds = productList.stream().map(pd -> pd.getYyProductId()).collect(Collectors.toList());
        projHardwareRecordVO.setYyProductIds(productIds);
        return Result.success(projHardwareRecordVO);
    }

    /**
     * 删除小硬件调研记录
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteHardwareRecord(ProjHardwareRecordDTO dto) {
        projHardwareRecordMapper.deleteById(dto.getHardwareRecordId());
        return Result.success();
    }

    /**
     * 下载调研模版
     *
     * @param response
     * @param projectInfoId
     */
    @Override
    public void downloadTemplate(HttpServletResponse response, Long projectInfoId) {
        projProjectPlanService.updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(projectInfoId, DictProjectPlanItemEnum.SURVEY_HARDWARE);
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
        String zipFileName = projProjectInfo.getProjectName() + "-小硬件调研模版.zip";
        //查询项目医院列表
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        Result<List<BaseIdNameResp>> findHospitalBaseList = projHospitalInfoService.findHospitalBaseList(selectHospitalDTO);
        if (ObjectUtil.isNotEmpty(findHospitalBaseList) && ObjectUtil.isNotEmpty(findHospitalBaseList.getData())) {
            List<BaseIdNameResp> hosList = findHospitalBaseList.getData();
            try {
                List<File> fileList = Lists.newArrayList();
                for (BaseIdNameResp baseIdNameResp : hosList) {
                    Workbook workbook = new XSSFWorkbook();
                    String excelFileName = baseIdNameResp.getName() + "-小硬件调研模版.xlsx";
                    Sheet sheet = workbook.createSheet(excelFileName);
                    Font font = workbook.createFont();
                    font.setBold(true);
                    sheet.setColumnWidth(0, 5000);
                    sheet.setColumnWidth(1, 6000);
                    sheet.setColumnWidth(2, 5000);
                    sheet.setColumnWidth(3, 5000);
                    sheet.setColumnWidth(4, 9000);
                    sheet.setColumnWidth(5, 5000);
                    // 创建标题行
                    Row titleRow = sheet.createRow(0);
                    titleRow.setHeightInPoints(30);
                    Cell titleCell = titleRow.createCell(0);
                    titleCell.setCellValue(baseIdNameResp.getName() + "小硬件调研模版");
                    CellStyle titleStyle = workbook.createCellStyle();
                    // 设置水平居中
                    titleStyle.setAlignment(HorizontalAlignment.CENTER);
                    // 设置垂直居中
                    titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    titleStyle.setFont(font);
                    titleCell.setCellStyle(titleStyle);
                    // 合并单元格范围
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
                    // 创建表头行
                    Row headerRow = sheet.createRow(1);
                    headerRow.setHeightInPoints(25);
                    // 创建标题样式
                    CellStyle headerStyle = workbook.createCellStyle();
                    headerStyle.setFont(font);
                    String[] headers = {"小硬件名称", "适用产品", "适用科室", "配置要求", "建议配置数量", "推荐型号", "采购属性", "现有型号", "现有数量", "采购型号", "采购数量", "备注", "医院ID"};
                    for (int i = 0; i < headers.length; i++) {
                        Cell cell = headerRow.createCell(i);
                        cell.setCellValue(headers[i]);
                        cell.setCellStyle(headerStyle);
                    }
                    //隐藏医院ID列
                    sheet.setColumnHidden(12, true);
                    //获取小硬件字典数据
                    List<ProjHardwareRecommendVO> hardwareInfoList = projHardwareRecommendMapper.findHardwareWithRecommend();
                    //获取当前项目产品信息
                    Result<List<BaseIdNameResp>> result = productBacklogService.selectYyProductAndModule(projectInfoId);
                    if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getData())) {
                        throw new CustomException("当前项目未查询到产品！");
                    }
                    List<BaseIdNameResp> productList = result.getData();
                    //查询小硬件与产品对照信息
                    List<ProjHardwareProduct> hardwareProducts = projHardwareProductMapper.selectList(new QueryWrapper<>());
                    // 创建一个下拉框的区域
                    CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, hardwareInfoList.size() + 1, 6, 6);
                    // 设置下拉框的数据有效性
                    DataValidationHelper helper = sheet.getDataValidationHelper();
                    DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[]{"需采购", "利旧", "不开展业务"});
                    DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
                    // 设置下拉框
                    dataValidation.setSuppressDropDownArrow(true);
                    dataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
                    dataValidation.setShowErrorBox(true);
                    sheet.addValidationData(dataValidation);
                    int i = 1;
                    CellStyle style = workbook.createCellStyle();
                    // 设置自动换行
                    style.setWrapText(true);
                    style.setVerticalAlignment(VerticalAlignment.CENTER);
                    for (ProjHardwareRecommendVO hardwareInfo : hardwareInfoList) {
                        //筛选小硬件对应的产品列表
                        List<ProjHardwareProduct> products = hardwareProducts.stream().filter(e -> e.getHardwareInfoId().equals(hardwareInfo.getHardwareInfoId())).collect(Collectors.toList());
                        if (CollectionUtil.isEmpty(products)) {
                            continue;
                        }
                        Boolean productFlag = false;
                        StringBuffer productName = new StringBuffer("");
                        StringBuffer deptName = new StringBuffer("");
                        StringBuffer suggestConfig = new StringBuffer("");
                        if (hardwareInfo.getCommonFlag() == 1) {
                            productFlag = true;
                            productName.append("通用,");
                            deptName.append("通用,");
                            suggestConfig.append("依需配置,");
                        } else {
                            //判断小硬件对应产品是否包含在当前项目下，包含的话封装产品名称和科室名称
                            for (ProjHardwareProduct product : products) {
                                List<BaseIdNameResp> baseIdNameResps = productList.stream().filter(e -> e.getId().equals(product.getYyProductId())).collect(Collectors.toList());
                                //判断小硬件对应产品是否包含在当前项目下，包含的话封装产品名称和科室名称
                                if (CollectionUtil.isNotEmpty(baseIdNameResps)) {
                                    productFlag = true;
                                    productName.append(baseIdNameResps.get(0).getName()).append(",");
                                    if (ObjectUtil.isNotEmpty(product.getSuitableDept())) {
                                        deptName.append(product.getSuitableDept()).append(",");
                                    }
                                    if (ObjectUtil.isNotEmpty(product.getSuggestConfig())) {
                                        suggestConfig.append(baseIdNameResps.get(0).getName()).append(":").append(product.getSuggestConfig()).append(";\n");
                                    }
                                }
                            }
                        }
                        if (!productFlag) {
                            continue;
                        }
                        i++;
                        Row rowData = sheet.createRow(i);
                        rowData.createCell(0).setCellValue(hardwareInfo.getHardwareName());
                        if (ObjectUtil.isNotEmpty(productName)) {
                            Cell productCell = rowData.createCell(1);
                            productCell.setCellStyle(style);
                            productCell.setCellValue(productName.substring(0, productName.length() - 1));
                        }
                        if (ObjectUtil.isNotEmpty(deptName)) {
                            Cell deptCell = rowData.createCell(2);
                            deptCell.setCellStyle(style);
                            deptCell.setCellValue(deptName.substring(0, deptName.length() - 1));
                        }
                        Cell configCell = rowData.createCell(3);
                        configCell.setCellStyle(style);
                        configCell.setCellValue(hardwareInfo.getConfigRequire());
                        if (ObjectUtil.isNotEmpty(suggestConfig)) {
                            Cell deptCell = rowData.createCell(4);
                            deptCell.setCellStyle(style);
                            deptCell.setCellValue(suggestConfig.substring(0, suggestConfig.length() - 1));
                        }
                        Cell recommendCell = rowData.createCell(5);
                        recommendCell.setCellStyle(style);
                        recommendCell.setCellValue(hardwareInfo.getRecommendModel());
                        rowData.createCell(12).setCellValue(baseIdNameResp.getId().toString());
                    }
                    // 写入到文件
                    try {
                        FileOutputStream out = new FileOutputStream(excelFileName);
                        workbook.write(out);
                        File file = new File(excelFileName);
                        fileList.add(file);
                    } catch (IOException e) {
                        workbook.close();
                        e.printStackTrace();
                    }
                }
                //生成zip打包文件
                createZip(zipFileName, fileList, response);
            } catch (Exception e) {
                throw new RuntimeException("下载调研模版发生错误，" + e.getMessage());
            }
        }
    }

    /**
     * 导入调研模版
     *
     * @param file
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importHardwareRecord(MultipartFile file, Long customInfoId, Long projectInfoId) {
        // 确保上传的是Excel文件
        if (!file.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("导入文件的类型必须为.xlsx格式");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(file.getInputStream(), ProjHardwareRecordImportDTO.class);
            log.info("excel数据: {}", JSONUtil.toJsonStr(easyExcelData));
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("请检查导入文件是否存在数据");
                }
            }
            log.info("小硬件调研数据-导入的Excel数据为 ：, {}", JSONUtil.toJsonStr(easyExcelData.getDatas()));
            // 数据完整性校验
            Result result = checkImportData(easyExcelData.getDatas());
            if (!result.isSuccess()) {
                return Result.fail(result.getMsg());
            }
            //导出调研数据
            result = saveHardwareRecord(easyExcelData.getDatas(), customInfoId, projectInfoId);
            return result;
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败");
        }
    }

    /**
     * 导出调研数据
     *
     * @param dto
     * @param response
     */
    @Override
    public void exportExcelDatas(ProjHardwareRecordPageDTO dto, HttpServletResponse response) {
        List<ProjHardwareRecordVO> hardwareRecordList = projHardwareRecordMapper.selectByPage(dto);
        if (CollectionUtil.isNotEmpty(hardwareRecordList)) {
            // 创建新的Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建一个工作表(sheet)
            Sheet sheet = workbook.createSheet("小硬件采购清单");
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.setHeightInPoints(25);
            headerRow.createCell(0).setCellValue("小硬件名称");
            headerRow.createCell(1).setCellValue("采购属性");
            headerRow.createCell(2).setCellValue("现有型号");
            headerRow.createCell(3).setCellValue("现有数量");
            headerRow.createCell(4).setCellValue("采购型号");
            headerRow.createCell(5).setCellValue("采购数量");
            headerRow.createCell(6).setCellValue("配置要求");
            headerRow.createCell(7).setCellValue("医院");
            headerRow.createCell(8).setCellValue("备注");
            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            for (Cell headerCell : headerRow) {
                headerCell.setCellStyle(headerStyle);
            }
            //填充数据
            for (int i = 0; i < hardwareRecordList.size(); i++) {
                ProjHardwareRecordVO hardwareRecordVO = hardwareRecordList.get(i);
                Row rowData = sheet.createRow(i + 1);
                rowData.createCell(0).setCellValue(hardwareRecordVO.getHardwareName());
                String purchaseProperty = "";
                if (hardwareRecordVO.getPurchaseProperty() == 2) {
                    purchaseProperty = "利旧";
                } else if (hardwareRecordVO.getPurchaseProperty() == 3) {
                    purchaseProperty = "不开展业务";
                } else {
                    purchaseProperty = "需采购";
                }
                rowData.createCell(1).setCellValue(purchaseProperty);
                rowData.createCell(2).setCellValue(hardwareRecordVO.getCurrentModel());
                rowData.createCell(3).setCellValue(hardwareRecordVO.getCurrentAmount());
                rowData.createCell(4).setCellValue(hardwareRecordVO.getPurchaseModel());
                rowData.createCell(5).setCellValue(hardwareRecordVO.getPurchaseAmount());
                rowData.createCell(6).setCellValue(hardwareRecordVO.getConfigRequire());
                rowData.createCell(7).setCellValue(hardwareRecordVO.getHospitalName());
                rowData.createCell(8).setCellValue(hardwareRecordVO.getMemo());
            }
            // 设置响应头信息
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(hardwareRecordList.get(0).getHospitalName() + "小硬件采购清单.xlsx"));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 写入到文件
            try {
                workbook.write(response.getOutputStream());
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 查询所有小硬件推荐型号
     *
     * @return
     */
    @Override
    public Result<List<ProjHardwareRecommendVO>> findAllHardwareRecommend() {
        List<ProjHardwareRecommendVO> recommendList = projHardwareRecommendMapper.findAllHardwareRecommend();
        return Result.success(recommendList);
    }

    /**
     * 导出小硬件推荐型号
     *
     * @param response
     */
    @Override
    public void exportRecommendDatas(HttpServletResponse response) {
        List<ProjHardwareRecommendVO> recommendList = projHardwareRecommendMapper.findAllHardwareRecommend();
        if (CollectionUtil.isNotEmpty(recommendList)) {
            // 创建新的Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建一个工作表(sheet)
            Sheet sheet = workbook.createSheet("小硬件常用推荐型号");
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.setHeightInPoints(25);
            headerRow.createCell(0).setCellValue("小硬件名称");
            headerRow.createCell(1).setCellValue("推荐品牌/型号");
            headerRow.createCell(2).setCellValue("推荐配置");
            headerRow.createCell(3).setCellValue("备注");
            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setBold(true);
            headerStyle.setFont(font);
            for (Cell headerCell : headerRow) {
                headerCell.setCellStyle(headerStyle);
            }
            //填充数据
            for (int i = 0; i < recommendList.size(); i++) {
                ProjHardwareRecommendVO hardwareRecommendVO = recommendList.get(i);
                Row rowData = sheet.createRow(i + 1);
                rowData.createCell(0).setCellValue(hardwareRecommendVO.getHardwareName());
                rowData.createCell(1).setCellValue(hardwareRecommendVO.getRecommendModel());
                rowData.createCell(2).setCellValue(hardwareRecommendVO.getConfigRequire());
                rowData.createCell(3).setCellValue(hardwareRecommendVO.getMemo());
            }
            // 设置响应头信息
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode("小硬件常用推荐型号.xlsx"));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 写入到文件
            try {
                workbook.write(response.getOutputStream());
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    workbook.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 查询采购清单附件列表
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result<List<ProjProjectFileVO>> findHardwareFiles(Long projectInfoId) {
        //查询附件列表
        List<ProjProjectFileVO> fileList = projProjectFileMapper.findProjectFiles(projectInfoId,
                ProjectFileTypeEnums.SURVEY_HARDWARE.getMilestone(),
                ProjectFileTypeEnums.SURVEY_HARDWARE.getStage());
        if (CollectionUtil.isEmpty(fileList)) {
            return Result.success();
        }
        for (ProjProjectFileVO projectFile : fileList) {
            projectFile.setFilePath(OBSClientUtils.getTemporaryUrl(projectFile.getFilePath(), 3600));
        }
        return Result.success(fileList);
    }

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    @Override
    public Result submitFinish(HardwareFinishDTO dto) {
        /**
         * TODO 这个限制先去掉，后期需要上传客户的确认单来处理
         */
//        List<ProjProjectFile> fileList = findFiles(dto.getProjectInfoId());
//        if (CollectionUtil.isEmpty(fileList)) {
//            return Result.fail("请先上传与院方确认采购清单！");
//        }
        // 查询里程碑节点信息
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(dto.getMilestoneInfoId());
        if (projMilestoneInfo.getMilestoneStatus() == 1) {
            return Result.fail("当前节点已完成，请勿重复点击");
        }
        //针对首期 老换新 单体/区域 项目进行限制（先试用后期可能只限制单体）
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
        if (projProjectInfo != null && projProjectInfo.getHisFlag() == 1 && projProjectInfo.getUpgradationType() == 1 && (projProjectInfo.getProjectType() == 1 || projProjectInfo.getProjectType() == 2)) {
            // 查询院内电脑配置检测数据
            ProjHospitalTerminalConfigPageReq tempDto = new ProjHospitalTerminalConfigPageReq();
            tempDto.setProjectInfoId(dto.getProjectInfoId());
            List<ProjHospitalTerminalConfigResp> list = projHospitalTerminalConfigMapper.findDataInfoPage(tempDto);
            if (CollectionUtil.isEmpty(list)) {
                return Result.fail("请先进行院内电脑配置检测！");
            }
        }
        //完成里程碑节点
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(dto.getMilestoneInfoId());
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setActualCompTime(new Date());
        updateMilestoneDTO.setNodeHeadId(userHelper.getCurrentUser().getSysUserId());
        milestoneInfoService.updateMilestone(updateMilestoneDTO);
        return Result.success();
    }

    /**
     * 查询小硬件附件列表
     *
     * @param projectInfoId
     * @return
     */
    private List<ProjProjectFile> findFiles(Long projectInfoId) {
        List<ProjProjectFile> fileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>()
                .eq("project_info_id", projectInfoId)
                .eq("milestone_node_code", ProjectFileTypeEnums.SURVEY_HARDWARE.getMilestone())
                .eq("project_stage_code", ProjectFileTypeEnums.SURVEY_HARDWARE.getStage()));
        return fileList;
    }

    /**
     * 生成zip打包文件
     *
     * @param fileList
     * @param response
     * @throws IOException
     */
    private void createZip(String zipFileName, List<File> fileList, HttpServletResponse response) {
        if (CollectionUtil.isEmpty(fileList)) {
            return;
        }
        try {
            ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFileName));
            for (File file : fileList) {
                // 添加文件到ZIP
                zipOut.putNextEntry(new ZipEntry(file.getName()));
                FileInputStream in = new FileInputStream(file);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    zipOut.write(buffer, 0, bytesRead);
                }
                zipOut.closeEntry();
                in.close();
                file.delete();
            }
            zipOut.close();
            downloadZip(zipFileName, response);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 下载zip文件
     *
     * @param zipFileName
     * @param response
     * @throws IOException
     */
    private void downloadZip(String zipFileName, HttpServletResponse response) throws IOException {
        InputStream fis = null;
        try {
            File file = new File(zipFileName);
            fis = new FileInputStream(file);
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(zipFileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8)));
            byte[] b = new byte[1024];
            int len;
            while ((len = fis.read(b)) != -1) {
                response.getOutputStream().write(b, 0, len);
            }
            response.flushBuffer();
        } catch (IOException e) {
            throw new RemoteException("下载出现异常," + e.getMessage());
        } finally {
            fis.close();
        }
        new File(zipFileName).delete();
    }

    /**
     * 检查导入数据完整性
     *
     * @param datas
     */
    Result checkImportData(List<Object> datas) {
        Boolean success = true;
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i < datas.size(); i++) {
            Boolean flag = false;
            Object data = datas.get(i);
            ProjHardwareRecordImportDTO dto = (ProjHardwareRecordImportDTO) data;
            StringBuilder errorMessage = new StringBuilder();
            if (ObjectUtil.isEmpty(dto.getHardwareName())) {
                flag = true;
                errorMessage.append("小硬件名称不能为空,");
            }
            if (ObjectUtil.isNotEmpty(dto.getCurrentAmountStr()) || ObjectUtil.isNotEmpty(dto.getPurchaseAmountStr())) {
                if (ObjectUtil.isEmpty(dto.getPurchasePropertyStr())) {
                    flag = true;
                    errorMessage.append("采购属性不能为空,");
                }
            }
            if (flag) {
                String errorStr = errorMessage.substring(0, errorMessage.length() - 1);
                success = false;
                sb.append("第【" + i + "】条小硬件数据缺失必要信息:" + errorStr + "；\n");
            }
        }
        if (success) {
            return Result.success();
        } else {
            return Result.fail(sb.toString());
        }
    }

    /**
     * 保存导入调研数据
     *
     * @param datas
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    Result saveHardwareRecord(List<Object> datas, Long customInfoId, Long projectInfoId) {
        try {
            //移除第一行表头列
            datas.remove(0);
            int importCount = 0;
            List<ProjHardwareRecord> recordList = Lists.newArrayList();
            //hospitalInfoId在Excel中是隐藏列，如果是新增的小硬件字典名称，次字段会为空，为空时取第一条数据的hospitalInfoId
            Long hospitalInfoId = null;
            ProjHardwareRecordImportDTO projHardwareRecordImportDTO = (ProjHardwareRecordImportDTO) datas.get(0);
            if (ObjectUtil.isNotEmpty(projHardwareRecordImportDTO.getHospitalInfoIdStr())) {
                hospitalInfoId = Long.valueOf(projHardwareRecordImportDTO.getHospitalInfoIdStr());
            }
            for (Object data : datas) {
                ProjHardwareRecordImportDTO dto = (ProjHardwareRecordImportDTO) data;
                if (ObjectUtil.isEmpty(dto.getCurrentAmountStr()) && ObjectUtil.isEmpty(dto.getPurchaseAmountStr())) {
                    continue;
                }
                ProjHardwareRecord hardwareRecord = new ProjHardwareRecord();
                BeanUtil.copyProperties(dto, hardwareRecord);
                if (ObjectUtil.isNotEmpty(dto.getCurrentAmountStr())) {
                    hardwareRecord.setCurrentAmount(Integer.valueOf(dto.getCurrentAmountStr()));
                } else {
                    hardwareRecord.setCurrentAmount(0);
                }
                if (ObjectUtil.isNotEmpty(dto.getPurchaseAmountStr())) {
                    hardwareRecord.setPurchaseAmount(Integer.valueOf(dto.getPurchaseAmountStr()));
                } else {
                    hardwareRecord.setPurchaseAmount(0);
                }
                Integer purchaseProperty = 3;
                if ("需采购".equals(dto.getPurchasePropertyStr())) {
                    purchaseProperty = 1;
                } else if ("利旧".equals(dto.getPurchasePropertyStr())) {
                    purchaseProperty = 2;
                } else if ("不开展业务".equals(dto.getPurchasePropertyStr())) {
                    purchaseProperty = 3;
                }
                hardwareRecord.setPurchaseProperty(purchaseProperty);
                if (ObjectUtil.isNotEmpty(dto.getHospitalInfoIdStr())) {
                    hardwareRecord.setHospitalInfoId(Long.valueOf(dto.getHospitalInfoIdStr()));
                } else {
                    hardwareRecord.setHospitalInfoId(hospitalInfoId);
                }
                //查询小硬件字典信息是否已存在
                DictHardwareInfo hardwareInfo = dictHardwareInfoMapper.selectOne(new QueryWrapper<DictHardwareInfo>()
                        .eq("hardware_name", dto.getHardwareName())
                        .last(" limit 1"));
                if (ObjectUtil.isEmpty(hardwareInfo)) {
                    //保存小硬件字典信息
                    DictHardwareInfo dictHardwareInfo = new DictHardwareInfo();
                    BeanUtil.copyProperties(dto, dictHardwareInfo);
                    dictHardwareInfo.setHardwareInfoId(SnowFlakeUtil.getId());
                    dictHardwareInfoMapper.insert(dictHardwareInfo);
                    hardwareRecord.setHardwareInfoId(dictHardwareInfo.getHardwareInfoId());
                } else {
                    hardwareRecord.setHardwareInfoId(hardwareInfo.getHardwareInfoId());
                }
                hardwareRecord.setHardwareRecordId(SnowFlakeUtil.getId());
                hardwareRecord.setCustomInfoId(customInfoId);
                hardwareRecord.setProjectInfoId(projectInfoId);
                hardwareRecord.setIsDeleted(0);
                hardwareRecord.setCreaterId(0L);
                hardwareRecord.setCreateTime(new Date());
                hardwareRecord.setUpdaterId(0L);
                hardwareRecord.setUpdateTime(new Date());
                recordList.add(hardwareRecord);
                importCount++;
            }
            if (CollectionUtil.isNotEmpty(recordList)) {
                projHardwareRecordMapper.batchInsert(recordList);
            }
            projProjectPlanService.updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(projectInfoId, DictProjectPlanItemEnum.SURVEY_HARDWARE);
            return Result.success("成功导入" + importCount + "条数据");
        } catch (Exception e) {
            return Result.fail("导入失败 ：" + e.getMessage());
        }
    }

    /**
     * 导入产品小硬件数据
     *
     * @param file
     * @return
     */
    @Override
    public Result importProductHardware(MultipartFile file) {
        // 确保上传的是Excel文件
        if (!file.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("导入文件的类型必须为.xlsx格式");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(file.getInputStream(), ProjProductHardwareImportDTO.class);
            log.info("excel数据: {}", JSONUtil.toJsonStr(easyExcelData));
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("请检查导入文件是否存在数据");
                }
            }
            //导出调研数据
            return saveProductHardware(easyExcelData.getDatas());
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Result saveProductHardware(List<Object> datas) {
        try {
            List<ProjHardwareProduct> productHardwareList = Lists.newArrayList();
            for (Object data : datas) {
                ProjProductHardwareImportDTO projProductHardwareImportDTO = (ProjProductHardwareImportDTO) data;
                ProjHardwareProduct projHardwareProduct = new ProjHardwareProduct();
                projHardwareProduct.setHardwareProductId(SnowFlakeUtil.getId());
                //查询小硬件字典信息是否已存在
                DictHardwareInfo hardwareInfo = dictHardwareInfoMapper.selectOne(new QueryWrapper<DictHardwareInfo>()
                        .eq("hardware_name", projProductHardwareImportDTO.getHardwareName())
                        .last(" limit 1"));
                if (ObjectUtil.isEmpty(hardwareInfo)) {
                    //保存小硬件字典信息
                    DictHardwareInfo dictHardwareInfo = new DictHardwareInfo();
                    dictHardwareInfo.setHardwareInfoId(SnowFlakeUtil.getId());
                    dictHardwareInfo.setHardwareName(projProductHardwareImportDTO.getHardwareName());
                    dictHardwareInfoMapper.insert(dictHardwareInfo);
                    projHardwareProduct.setHardwareInfoId(dictHardwareInfo.getHardwareInfoId());
                } else {
                    projHardwareProduct.setHardwareInfoId(hardwareInfo.getHardwareInfoId());
                }
                projHardwareProduct.setYyProductId(Long.valueOf(projProductHardwareImportDTO.getProductId()));
                projHardwareProduct.setSuggestConfig(projProductHardwareImportDTO.getSuggestConfig());
                projHardwareProduct.setSuitableDept(projProductHardwareImportDTO.getSuitableDept());
                if (ObjectUtil.isEmpty(projProductHardwareImportDTO.getRequireFlag())) {
                    projHardwareProduct.setRequireFlag(0);
                } else {
                    projHardwareProduct.setRequireFlag("是".equals(projProductHardwareImportDTO.getRequireFlag()) ? 1 : 0);
                }
                projHardwareProduct.setIsDeleted(0);
                projHardwareProduct.setCreaterId(0L);
                projHardwareProduct.setCreateTime(new Date());
                projHardwareProduct.setUpdaterId(0L);
                projHardwareProduct.setUpdateTime(new Date());
                productHardwareList.add(projHardwareProduct);
            }
            projHardwareProductMapper.batchInsert(productHardwareList);
        } catch (Exception e) {
            throw new CustomException(e.getMessage());
        }
        return Result.success();
    }
}
