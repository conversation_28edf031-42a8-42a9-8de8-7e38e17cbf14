package com.msun.csm.service.config.projectreview;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.projreview.ProjectStageCodeEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectReviewInfo;
import com.msun.csm.dao.entity.projectreview.DictProjectReviewType;
import com.msun.csm.dao.entity.projectreview.DictReviewMethodType;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewInfoMapper;
import com.msun.csm.dao.mapper.projectreview.DictProjectReviewTypeMapper;
import com.msun.csm.dao.mapper.projectreview.DictReviewMethodTypeMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.dto.projreview.ProjReviewAppDTO;
import com.msun.csm.model.dto.projreview.ProjReviewDTO;
import com.msun.csm.model.dto.projreview.ProjReviewProductAppInfo;
import com.msun.csm.model.req.projectreview.DictProjectReviewTypeParam;
import com.msun.csm.model.resp.projectreview.DictProjectReviewTypeResp;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【dict_project_review_type(项目审核类型字典表)】的数据库操作Service实现
 * @createDate 2025-06-18 08:30:31
 */
@Service
public class DictProjectReviewTypeServiceImpl extends ServiceImpl<DictProjectReviewTypeMapper, DictProjectReviewType> implements DictProjectReviewTypeService {

    @Resource
    private DictProjectReviewTypeMapper dictProjectReviewTypeMapper;

    @Resource
    private DictReviewMethodTypeMapper dictReviewMethodTypeMapper;

    @Lazy
    @Resource
    private ProjOrderProductService orderProductService;
    @Lazy
    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjProjectReviewInfoMapper projProjectReviewInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 查询项目审核类型字典表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<DictProjectReviewTypeResp>> findDataPage(DictProjectReviewTypeParam dto) {
        // 查询 医院信息
        List<DictProjectReviewTypeResp> pagelist = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> dictProjectReviewTypeMapper.findDataPage(dto.getDictName()));
        PageInfo<DictProjectReviewTypeResp> pageInfo = new PageInfo<>(pagelist);
        return Result.success(pageInfo);
    }

    /**
     * 保存项目审核类型字典表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> saveData(DictProjectReviewTypeParam dto) {
        if (dto.getProjectReviewTypeId() == null) {
            List<DictProjectReviewType> listModel = dictProjectReviewTypeMapper.selectList(new QueryWrapper<DictProjectReviewType>().eq("dict_code", dto.getDictCode()).eq("is_deleted", 0));
            if (listModel != null && !listModel.isEmpty()) {
                return Result.fail("审核类型字典编码已存在,重新填写");
            }
            // 新增
            DictProjectReviewType dictProjectReviewType = new DictProjectReviewType();
            BeanUtil.copyProperties(dto, dictProjectReviewType);
            dictProjectReviewType.setProjectReviewTypeId(SnowFlakeUtil.getId());
            int save = dictProjectReviewTypeMapper.insert(dictProjectReviewType);
        } else {
            // 修改
            DictProjectReviewType dictProjectReviewType = dictProjectReviewTypeMapper.selectById(dto.getProjectReviewTypeId());
            dictProjectReviewType.setDictName(dto.getDictName());
            dictProjectReviewType.setRemark(dto.getRemark());
            int update = dictProjectReviewTypeMapper.updateById(dictProjectReviewType);
        }
        return Result.success();
    }

    /**
     * 删除项目审核类型字典表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> delData(DictProjectReviewTypeParam dto) {
        if (dto != null && dto.getProjectReviewTypeId() == null) {
            return Result.fail("请选择要删除的记录");
        }
        DictProjectReviewType dictProjectReviewType = dictProjectReviewTypeMapper.selectById(dto.getProjectReviewTypeId());
        if (dictProjectReviewType == null) {
            return Result.fail("请选择要删除的记录");
        }
        Integer isFlag = dictProjectReviewTypeMapper.selectIfNotUseCount(dto);
        if (isFlag != null && isFlag > 0) {
            return Result.fail("该字典表正在使用中，请勿删除");
        }
        int del = dictProjectReviewTypeMapper.deleteById(dto.getProjectReviewTypeId());
        return Result.success("删除成功");
    }

    /**
     * 查询项目审核类型字典表
     *
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> findReviewMethodTypeDict() {
        List<DictReviewMethodType> dictReviewMethodTypes = dictReviewMethodTypeMapper.selectList(new QueryWrapper<DictReviewMethodType>().eq("is_deleted", 0));
        if (dictReviewMethodTypes != null && !dictReviewMethodTypes.isEmpty()) {
            List<BaseIdNameResp> list = dictReviewMethodTypes.stream().map(item -> new BaseIdNameResp(item.getReviewMethodTypeId(), item.getDictName())).collect(Collectors.toList());
            return Result.success(list);
        }
        return Result.success(new ArrayList<>());
    }

    /**
     * 查询所有项目审核类型字典表
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> findAllReviewTypeDict() {
        List<DictProjectReviewType> dictProjectReviewTypes = dictProjectReviewTypeMapper.selectList(new QueryWrapper<DictProjectReviewType>().eq("is_deleted", 0));
        if (dictProjectReviewTypes != null && !dictProjectReviewTypes.isEmpty()) {
            List<BaseIdNameResp> list = dictProjectReviewTypes.stream().map(item -> new BaseIdNameResp(item.getProjectReviewTypeId(), item.getDictName())).collect(Collectors.toList());
            return Result.success(list);
        }
        return Result.success(new ArrayList<>());
    }

    /**
     *查询项目审核各个阶段数据集合
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjReviewAppDTO> selectDataByParam(ProjReviewDTO dto) {

        ProjReviewAppDTO resultData = new ProjReviewAppDTO();
        BeanUtil.copyProperties(dto, resultData);
        // 固定值
        resultData.setSceneCode("project_review");
        resultData.setReviewNodeType(2);

        List<String> productName = new ArrayList<>();
        List<ProductInfo> productInfosTmp = orderProductService.findProductList(dto.getProjectInfoId());
        if (CollUtil.isNotEmpty(productInfosTmp)) {
            productName = productInfosTmp.stream().map(ProductInfo::getProductName).collect(Collectors.toList());
        }
        resultData.setProductName(productName);
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
        if (projectInfo != null) {
            resultData.setPlanOnlineTime(projectInfo.getPlanOnlineTime());
            resultData.setWorkOrderCode(projectInfo.getProjectNumber());
            resultData.setProjectName(projectInfo.getProjectName());
            ProjCustomInfo customInfo = projCustomInfoMapper.selectById(projectInfo.getCustomInfoId());
            if (customInfo != null) {
                resultData.setCustomName(customInfo.getCustomName());
            }
            SysUser sysUser = sysUserMapper.selectById(projectInfo.getProjectLeaderId());
            if (sysUser != null) {
                resultData.setProjectManagerName(sysUser.getUserName());
            }
        }
        ProjProjectReviewInfo info = projProjectReviewInfoMapper.selectOne(new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", dto.getProjectInfoId()).eq("is_deleted", 0));

        if (info != null) {
            Boolean showReviewButton = false;
            if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(dto.getProjectStageCode())) {
                showReviewButton = info.getSurveyReviewStatus() == 0;
                resultData.setReviewResult(info.getSurveyReviewStatus());
                resultData.setSpecialInstructions(info.getSurveyReviewSpecialInstructions());
                resultData.setReviewOpinion(info.getSurveyReviewOpinion());
            } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(dto.getProjectStageCode())) {
                showReviewButton = info.getEntryReviewStatus() == 0;
                resultData.setReviewResult(info.getEntryReviewStatus());
                resultData.setReviewOpinion(info.getEntryReviewOpinion());
                resultData.setSpecialInstructions(info.getEntryReviewSpecialInstructions());
            } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(dto.getProjectStageCode())) {
                showReviewButton = info.getPreparatReviewStatus() == 0;
                resultData.setReviewResult(info.getPreparatReviewStatus());
                resultData.setReviewOpinion(info.getPreparatReviewOpinion());
                resultData.setSpecialInstructions(info.getPreparatReviewSpecialInstructions());
            }
            resultData.setShowReviewButton(showReviewButton);
        } else {
            resultData.setReviewResult(0);
            resultData.setShowReviewButton(true);
        }

        List<ProjReviewProductAppInfo> productAppInfoList = new ArrayList<>();
        // 查询调研、准备工作
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(dto.getProjectStageCode())) {
            productAppInfoList = projProjectReviewInfoMapper.selectProductInfoByProjectId(dto.getProjectInfoId(), ProjectStageCodeEnum.STAGE_SURVEY.getCode());
            resultData.setProductAppInfoList(productAppInfoList);
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(dto.getProjectStageCode())) {
            productAppInfoList = projProjectReviewInfoMapper.selectProductInfoByProjectId(dto.getProjectInfoId(), ProjectStageCodeEnum.STAGE_SURVEY.getCode());
            resultData.setProductAppInfoList(productAppInfoList);
        }

        return Result.success(resultData);
    }
}




