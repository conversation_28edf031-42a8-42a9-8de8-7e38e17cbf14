package com.msun.csm.service.report.statis;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.BaseLableValueResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity;
import com.msun.csm.model.req.projreport.statis.ProjFileReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportComparisonReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainAddReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainAdjudicationReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainAssignPersonsReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainAssignPersonsReq2;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainDeleteReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainDeliveryReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainImportReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainPageReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainReapplyAdjudicationReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainReapplyRulingReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainReportDesignReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainUpdateReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalStyleFileReq;
import com.msun.csm.model.resp.projreport.MenuVO;
import com.msun.csm.model.resp.projreport.MsunReportMain;
import com.msun.csm.model.resp.statis.ConfigProductionMethodResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainSelectResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainUpdateResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportPageResp;
import com.msun.csm.model.statis.ProjStatisticalReportMainUpdateStatusReq;

/**
 * @Description:
 * @Author: zd
 * @Date: 2024/11/7
 */

public interface ProjStatisticalReportMainService extends IService<ProjStatisticalReportMainEntity> {

    /**
     * 批量删除
     * @param projStatisticalReportMainDeleteReq
     * @return
     */
    Result<String> batchDeleteByIds(ProjStatisticalReportMainDeleteReq projStatisticalReportMainDeleteReq);

    /**
     * 查询科室数据
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameResp> queryHospitalDeptData(Long projectInfoId);

    /**
     * 查询频次数据
     * @return
     */
    List<BaseIdNameResp> queryFrequencyData();

    /**
     * 查询用途数据
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameResp> queryPurposeData(Long projectInfoId);

    /**
     * 查询状态数据
     * @return
     */
    List<BaseIdNameResp> queryStausData();

    /**
     * 保存报表数据
     * @param projSurveyFormAddReqs
     * @return
     */
    Result saveReportData(ProjStatisticalReportMainAddReq projSurveyFormAddReqs);

    /**
     * 查询制作方式接口
     * @param projectInfoId
     * @return
     */
    List<ConfigProductionMethodResp> queryConfigProductionMethodData(Long projectInfoId);

    /**
     * 分页查询报表数据
     * @param dto
     * @return
     */
    Result<ProjStatisticalReportPageResp<ProjStatisticalReportMainSelectResp>> findReportPage(ProjStatisticalReportMainPageReq dto);

    /**
     * 根据id查询报表数据
     * @param statisticalReportMainId
     * @return
     */
    ProjStatisticalReportMainUpdateResp queryStatisticalReportById(Long statisticalReportMainId);

    /**
     * 修改报表数据
     * @param dto
     * @param updateType
     * @return
     */
    Result<String> updateReportMainDataById(ProjStatisticalReportMainUpdateReq dto, String updateType);

    /**
     *  申请裁定，一键申请裁定
     * @param dto
     * @return
     */
    Result<String> updateApplyAdjudication(ProjStatisticalReportMainAdjudicationReq dto);

    /**
     * 重新申请裁定
     * @param dto
     * @return
     */
    Result<String> updateReapplyAdjudication(ProjStatisticalReportMainReapplyAdjudicationReq dto);

    /**
     * 修改报表状态
     * @param dto
     * @return
     */
    ResponseData updateStatisticalReportStatus(ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 撤销裁定
     * @param dto
     * @return
     */
    Result<String> updateRevokeAdjudication(ProjStatisticalReportMainReapplyAdjudicationReq dto);

    /**
     * 分配人员
     * @param dto
     * @return
     */
    Result<String> updateAssignPersonsById(ProjStatisticalReportMainAssignPersonsReq dto);

    /**
     * 裁定通过/裁定驳回
     * @param dto
     * @param updateType 批量，单个（single）
     * @return
     */
    Result<String> updateRulingAdjudication(ProjStatisticalReportMainReapplyRulingReq dto, String updateType);

    /**
     * 下载模板
     * @param response
     */
    void download(HttpServletResponse response, Long projectInfoId) throws IOException;

    /**
     * 导入报表
     * @param file
     * @param request
     * @return
     */
    Result<String> importExcel(MultipartFile file, HttpServletRequest request, ProjStatisticalReportMainImportReq param) throws IOException;

    /**
     * 后端运维使用：导出统计报表Excel之后分配完责任人再通过此方法导入，达到分配责任人的目的
     * @param multipartFile
     * @return
     */
    Result<Void> importStatisticalReportFromExcel(MultipartFile multipartFile);

    /**
     * 根据项目id和报表状态查询
     * @param dto
     * @return
     */
    Result<String> reportDesignById(ProjStatisticalReportMainReportDesignReq dto, String account);

    /**
     * 根据id查询报表数据
     * @param dto
     * @return
     */
    ResponseData getReportById(ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 根据参数查询报表数据
     * @param dto
     * @return
     */
    ResponseData getReportByParamer(ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 根据参数删除关联数据
     * @param dto
     * @return
     */
    ResponseData deleteRelationshipByParamer(ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 报表预览
     * @param dto
     * @return
     */
    Result<String> reportReviewById(ProjStatisticalReportMainReportDesignReq dto);

    /**
     * 根据参数查询医院线上产品
     * @param dto
     * @return
     */
    ResponseData selectHospitalOnlineProductByParamer(ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 查询指标
     * @param projectInfoId
     * @return
     */
    List<BaseLableValueResp> getAllReportZbDetailData(Long projectInfoId);

    /**
     * 根据医院id获取项目信息
     * @param cloudHospitalId
     * @return
     */
    ProjProjectInfo getProjectInfoByCloudHospitalId(Long cloudHospitalId);

    /**
     * 获取报表添加url
     * @param operationStatisticsReportId
     * @return
     */
    Result<String> getStatisticsReportAddUrl(String operationStatisticsReportId, String account);

    /**
     * 运维平台传入文件交付保存
     * @param req
     * @return
     */
    List<ProjFileReq> getFileNames(ProjStatisticalStyleFileReq req);

    /**
     * 报表下沉
     * @param dto
     * @return
     */
    Result<String> jfDevolveDelivery(ProjStatisticalReportMainReapplyAdjudicationReq dto);

    /**
     * 发布
     * @param dto
     * @return
     */
    Result<String> jfSaveMenuDelivery(ProjStatisticalReportMainDeliveryReq dto);

    /**
     * 查询报表平台数据
     * @param dto
     * @return
     */
    Result<List<MsunReportMain>> selectReprtListDelivery(ProjStatisticalReportMainDeliveryReq dto);

    /**
     * 查询报表平台云健康菜单
     * @param dto
     * @return
     */
    Result<List<MenuVO>> jfFindMenuByHosId(ProjStatisticalReportMainDeliveryReq dto);

    /**
     * 报表对照
     * @param dto
     * @return
     */
    Result reportComparison(ProjStatisticalReportComparisonReq dto);

    /**
     * 制作完成
     * @param dto
     * @return
     */
    Result makeFinishReport(ProjStatisticalReportMainReportDesignReq dto);

    /**
     * 报表导出
     * @param response
     * @param dto
     */
    void reportExportExcel(HttpServletResponse response, ProjStatisticalReportMainPageReq dto);

    Result<Void> updateAllocateUserByImport(@RequestParam("file") MultipartFile multipartFile);


    Result<Void> updateStatisticalReportIdentifier(ProjStatisticalReportMainAssignPersonsReq2 param);

    boolean verificationPassed(ProjStatisticalReportMainAssignPersonsReq2 param);
}
