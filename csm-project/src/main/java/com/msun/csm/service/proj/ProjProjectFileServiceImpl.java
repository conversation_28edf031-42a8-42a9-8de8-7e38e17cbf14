package com.msun.csm.service.proj;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.req.projectfile.DelUploadFileReq;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.util.DateUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */

@Slf4j
@Service
public class ProjProjectFileServiceImpl implements ProjProjectFileService {

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private SendBusinessMessageService sendBusinessMessageService;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Value ("${project.obs.prePath}")
    private String prePath;

    @Resource
    private BaseQueryService baseQueryService;

    @Override
    public int deleteByPrimaryKey(Long projectFileId) {
        return projProjectFileMapper.deleteByPrimaryKey(projectFileId);
    }

    @Override
    public int insert(ProjProjectFile record) {
        return projProjectFileMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjProjectFile record) {
        return projProjectFileMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjProjectFile record) {
        return projProjectFileMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjProjectFile record) {
        return projProjectFileMapper.insertSelective(record);
    }

    @Override
    public ProjProjectFile selectByPrimaryKey(Long projectFileId) {
        return projProjectFileMapper.selectByPrimaryKey(projectFileId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProjectFile record) {
        return projProjectFileMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProjectFile record) {
        return projProjectFileMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjProjectFile> list) {
        return projProjectFileMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjProjectFile> list) {
        return projProjectFileMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjProjectFile> list) {
        return projProjectFileMapper.batchInsert(list);
    }

    /**
     * 上传文件
     *
     * @param req@return
     */
    @Override
    public Result<ProjProjectFileExtend> uploadFile(UploadFileReq req, HttpServletRequest request) {
        String mimeType = null;
        String fileType = null;
        try {
            if (request != null) {
                // 获取文件Part
                Part filePart = request.getPart("file");
                // 直接获取MIME类型
                mimeType = filePart.getContentType();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(mimeType)) {
                    if (mimeType.startsWith("image")) {
                        fileType = "image";
                    } else if ("application/pdf".equals(mimeType)) {
                        fileType = "pdf";
                    } else if ("application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(mimeType)) {
                        fileType = "word";
                    } else if ("application/msword".equals(mimeType)) {
                        fileType = "word";
                    }
                }
            }
        } catch (Exception e) {
            log.error("上传文件获取mimeType，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (req.getMilestoneCode() == null && req.getBusinessCode() == null) {
            throw new CustomException("参数不能同时为空");
        }
        //处理从项目工具入口上传附件时，没有projectInfoId和milestoneCode的问题
        if (ObjectUtil.isEmpty(req.getProjectInfoId())) {
            req.setProjectInfoId(Long.valueOf(DateUtil.getHourMinuteSecondString()));
        }
        String middlePath = req.getMilestoneCode();
        if (StringUtils.isNotEmpty(req.getBusinessCode())) {
            middlePath = req.getBusinessCode();
        }
        // 构建路径
        String path = prePath + req.getProjectInfoId() + StrUtil.SLASH + middlePath + StrUtil.SLASH
                + req.getFile().getOriginalFilename();
        // 获取文件名（不包括路径）
        String fileName = path.substring(path.lastIndexOf(StrUtil.SLASH) + 1);
        // 获取文件名（不包括扩展名）
        String fileNameWithoutExtension = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        // 拼接时间戳和文件扩展名
        String newFileName =
                fileNameWithoutExtension + "_" + System.currentTimeMillis() + "." + (fileName.contains(
                        ".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "");
        // 构建新的完整路径
        String objectKey = path.substring(0, path.lastIndexOf(StrUtil.SLASH) + 1) + newFileName;
        PutObjectResult putObjectResult;
        try {
            putObjectResult = OBSClientUtils.uploadMultipartFile(req.getFile(), objectKey, req.getIsPublic());
        } catch (Exception e) {
            log.error("上传文件异常.e.message:{}, e=", e.getMessage(), e);
            throw new CustomException("上传文件失败");
        }
        ProjProjectFileExtend projectFile = new ProjProjectFileExtend(req, SnowFlakeUtil.getId(), objectKey, "");
        if (req.getIsPublic()) {
            projectFile.setFileUrl(URLDecoder.decode(putObjectResult.getObjectUrl()));
        } else {
            projectFile.setFileUrl(OBSClientUtils.getTemporaryUrl(objectKey, 3600));
        }
        projectFile.setMimeType(mimeType);
        projectFile.setFileType(fileType);
        projProjectFileMapper.insert(projectFile);
        // 制定网络改造方案，提交网络改造方案
        if ("scheme_newwork".equals(req.getMilestoneCode())) {
            projProjectPlanService.updatePlanAndTodoTaskStatus(req.getProjectInfoId(), DictProjectPlanItemEnum.SCHEME_NEWWORK, ProjectPlanStatusEnum.FINISHED);

//            ProjProjectPlan projectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(req.getProjectInfoId(), DictProjectPlanItemEnum.SCHEME_NEWWORK);
//            sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projectPlan.getProjectPlanId(), ReviewTypeEnum.SCHEME_NEWWORK_RESUBMIT_FOR_REVIEW.getBusinessTable());
//            sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
//                    ReviewTypeEnum.SCHEME_NEWWORK_SUBMIT_AUDIT,
//                    projectPlan.getProjectInfoId(),
//                    projectPlan.getProjectPlanId(),
//                    // TODO 获取处罚人
//                    null,
//                    "网络改造方案审核超期",
//                    new Date()
//            );
        }
        // 小硬件调研与院方确认采购清单，上传采购清单附件
        if ("survey_hardware".equals(req.getMilestoneCode())) {
            projProjectPlanService.updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(req.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_HARDWARE);
        }
        return Result.success(projectFile);
    }

    public String uploadFileOnly(UploadFileReq req, File file, String originalfileName) {
        String objectKey = prePath + req.getProjectInfoId() + StrUtil.SLASH + req.getMilestoneCode() + StrUtil.SLASH
                + originalfileName;
        try {
            InputStream inputStream = new FileInputStream(file);
            OBSClientUtils.uploadMultipartFile(inputStream, file, originalfileName, objectKey);
        } catch (Exception e) {
            log.error("上传文件异常.e.message:{}, e=", e.getMessage(), e);
            throw new CustomException("上传文件失败");
        }
        return objectKey;
    }


    /**
     * 文件上传obs, 直接使用文件流
     *
     * @param req              请求参数
     * @param inputStream      文件流
     * @param originalfileName 文件名称
     * @return String
     */
    public String uploadFileOnly(UploadFileReq req, InputStream inputStream, String originalfileName) {
        String objectKey = prePath + req.getProjectInfoId() + StrUtil.SLASH + req.getMilestoneCode() + StrUtil.SLASH
                + originalfileName;
        try {
            OBSClientUtils.uploadMultipartFile(inputStream, objectKey);
        } catch (Exception e) {
            throw new CustomException("上传文件失败");
        }
        return objectKey;
    }

    /**
     * 删除附件
     *
     * @param req
     * @return
     */
    @Override
    public Result delUploadFile(DelUploadFileReq req) {
        projProjectFileMapper.deleteById(req.getProjectFileId());
        return Result.success();
    }

    /**
     * 上传文件带校验逻辑
     *
     * @param req
     * @param request
     * @return
     */
    @Override
    public Result uploadFileWithCheck(UploadFileReq req, HttpServletRequest request) {
        String mimeType = null;
        String fileType = null;
        try {
            if (request != null) {
                // 获取文件Part
                Part filePart = request.getPart("file");
                // 直接获取MIME类型
                mimeType = filePart.getContentType();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(mimeType)) {
                    if (mimeType.startsWith("image")) {
                        fileType = "image";
                    } else if ("application/pdf".equals(mimeType)) {
                        fileType = "pdf";
                    } else if ("application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(mimeType)) {
                        fileType = "word";
                    } else if ("application/msword".equals(mimeType)) {
                        fileType = "word";
                    }
                }
            }
        } catch (Exception e) {
            log.error("上传文件获取mimeType，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (req.getMilestoneCode() == null && req.getBusinessCode() == null) {
            log.error("上传文件参数不能同时为空");
            return Result.fail("参数不能同时为空");
        }
        //处理从项目工具入口上传附件时，没有projectInfoId和milestoneCode的问题
        if (ObjectUtil.isEmpty(req.getProjectInfoId())) {
            req.setProjectInfoId(Long.valueOf(DateUtil.getHourMinuteSecondString()));
        }
        String middlePath = req.getMilestoneCode();
        if (StringUtils.isNotEmpty(req.getBusinessCode())) {
            middlePath = req.getBusinessCode();
        }
        // 构建路径
        String path = prePath + req.getProjectInfoId() + StrUtil.SLASH + middlePath + StrUtil.SLASH
                + req.getFile().getOriginalFilename();
        // 获取文件名（不包括路径）
        String fileName = path.substring(path.lastIndexOf(StrUtil.SLASH) + 1);
        // 获取文件名（不包括扩展名）
        String fileNameWithoutExtension = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        // 拼接时间戳和文件扩展名
        String newFileName =
                fileNameWithoutExtension + "_" + System.currentTimeMillis() + "." + (fileName.contains(
                        ".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "");
        // 构建新的完整路径
        String objectKey = path.substring(0, path.lastIndexOf(StrUtil.SLASH) + 1) + newFileName;
        PutObjectResult putObjectResult;
        try {
            putObjectResult = OBSClientUtils.uploadMultipartFile(req.getFile(), objectKey, req.getIsPublic());
        } catch (Exception e) {
            log.error("上传文件异常.e.message:{}, e=", e.getMessage(), e);
            throw new CustomException("上传文件失败");
        }
        ProjProjectFileExtend projectFile = new ProjProjectFileExtend(req, SnowFlakeUtil.getId(), objectKey, "");
        if (req.getIsPublic()) {
            projectFile.setFileUrl(URLDecoder.decode(putObjectResult.getObjectUrl()));
        } else {
            projectFile.setFileUrl(OBSClientUtils.getTemporaryUrl(objectKey, 3600));
        }
        projectFile.setMimeType(mimeType);
        projectFile.setFileType(fileType);

        // 校验图片
        if ("image".equals(fileType) && req.getCheckType() != null && !req.getCheckType().isEmpty()) {
            log.info("发送图片校验消息");
            DictAgentChatReq dictAgentChatReq = new DictAgentChatReq();
            dictAgentChatReq.setScenarioCode(req.getCheckType());
            dictAgentChatReq.setFileUrls(Arrays.asList(projectFile.getFileUrl()));
            Result result = baseQueryService.sendChartMessage(dictAgentChatReq);
            if (result != null && !result.isSuccess()) {
                log.error("图片校验失败.result:{}", result);
                return Result.fail("图片校验失败");
            } else {
                if (result.getData() != null) {
                    Map map = (Map) result.getData();
                    String companyCode = (String) map.get("compliance_status");
                    if (!"是".equals(companyCode)) {
                        String code = (String) map.get("result_text");
                        log.error(code);
                        return Result.fail(code);
                    }
                }
            }
        }
        projProjectFileMapper.insert(projectFile);
        return Result.success(projectFile);
    }
}
