package com.msun.csm.service.proj;

import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.param.ConfirmFinalDataParam;
import com.msun.csm.model.param.CopySurveyResultParam;
import com.msun.csm.model.param.DifferenceProductParam;
import com.msun.csm.model.param.SurveyPlanResultParam;
import com.msun.csm.model.req.surveyplan.AddSurveyPlanReq;
import com.msun.csm.model.req.surveyplan.UpdateSurveyPlanReq;
import com.msun.csm.model.resp.surveyplan.DifferenceProductVO;
import com.msun.csm.model.resp.surveyplan.SurveyPlanResult;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProductSurveyPlanMessageVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanTaskRespVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/19
 */

public interface ProjSurveyPlanService {

    ProjSurveyPlan selectByPrimaryKey(Long surveyPlanId);

    int updateByPrimaryKeySelective(ProjSurveyPlan record);

    /**
     * 查询调研计划关联信息
     *
     * @param dto
     * @return
     */
    Result<SurveyPlanTaskRespVO> findSurveyPlanTask(ProjSurveyPlanDTO dto);

    Boolean cancelFinalResult(Long finalResultDataId);

    /**
     * 说明: 获取项目调研计划任务-医院列表
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.util.List<com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO>>
     * @author: Yhongmin
     * @createAt: 2024/8/29 11:15
     * @remark: Copyright
     */
    Result<SurveyPlanInitVO> findSurveyPlanHospitalProduct(ProjSurveyPlanDTO dto);

    /**
     * 说明: 设置医院产品列表
     *
     * @param surveyPlanInitVO
     * @param surveyPlanHospitalProductVOS
     * @param projectInfoId
     */
    void setHospitalProductVOList(SurveyPlanInitVO surveyPlanInitVO,
                                  List<SurveyPlanHospitalProductVO> surveyPlanHospitalProductVOS, Long projectInfoId);

    /**
     * 说明: 判断分院模式项目
     *
     * @param projectInfoId
     * @return
     */
    Boolean isBranchOnlineFlag(Long projectInfoId);

    /**
     * 添加调研计划任务
     *
     * @param addSurveyPlanReq
     * @return
     */
    Result addSurveyPlanTask(AddSurveyPlanReq addSurveyPlanReq);

    /**
     * 删除调研计划任务
     *
     * @param dto
     * @return
     */
    Result deleteSurveyPlanTask(ProjSurveyPlanDTO dto);

    /**
     * 批量新增调研计划任务
     *
     * @param req
     * @return
     */
    Result saveSurveyPlanTaskList(AddSurveyPlanReq req);

    /**
     * 批量修改调研计划任务
     *
     * @param req
     * @return
     */
    Result updateSurveyPlanTaskList(UpdateSurveyPlanReq req);

    /**
     * 更新调研计划任务
     *
     * @param updateSurveyPlanReq
     * @return
     */
    Result updateSurveyPlanTask(UpdateSurveyPlanReq updateSurveyPlanReq);

    /**
     * 调研总结统计
     *
     * @param param 参数
     */
    Result<List<SurveyPlanResult>> surveyPlanResult(SurveyPlanResultParam param);

    /**
     * 更新调研计划状态
     *
     * @param updateSurveyPlanStatus
     * @return
     */
    Result updateSurveyPlanStatus(UpdateSurveyPlanStatusReq updateSurveyPlanStatus);

    /**
     * 保存待办任务总数据和详情
     *
     * @param saveBackLogAndDetailReq
     * @return
     */
    Result saveBackLogAndDetail(SaveBackLogAndDetailReq saveBackLogAndDetailReq);

    /**
     * 查询调研计划关联信息
     *
     * @param projectInfoId
     * @return
     */
    Result<List<BaseIdNameResp>> getProductByProjectInfoId(SimpleId projectInfoId);

    /**
     * 保存待办任务总数据和详情
     *
     * @param saveBackLogAndDetailReq
     * @param hospitalInfoId
     * @param projectInfo
     * @param now
     * @throws Exception
     */
    void saveMilestoneTaskAndDetail(SaveBackLogAndDetailReq saveBackLogAndDetailReq, Long hospitalInfoId, ProjProjectInfo projectInfo, Date now) throws Exception;


    /**
     * 添加调研计划任务图片数据
     *
     * @param task
     */
    void addSurveyImgData(ProjProductTask task);

    /**
     * 说明: 校验分院模式下已分配的是否调研完成
     *
     * @param projectInfoId
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/9/3 9:23
     * @remark: Copyright
     */
    Boolean isConfirmData(Long projectInfoId);

    /**
     * 引用调研结果
     *
     * @param task
     * @return
     * @throws Exception
     */
    Result<Boolean> copySurveyResult(CopySurveyResultParam task) throws Exception;

    /**
     * 确认完成展示左侧列表
     *
     * @param param
     * @return
     */
    List<DifferenceProductVO> showDifferenceProduct(DifferenceProductParam param);

    /**
     * 查询产品调研节点，产品专项调研子页面数据
     *
     * @param dto
     * @return
     */
    Result<List<ProductBacklogUrlVO>> selectSurveyProductJobMenuDetail(ProjProductBacklogDTO dto);

    /**
     * 产品业务调研消息提醒
     *
     * @param dto
     * @return
     */
    Result<ProductSurveyPlanMessageVO> productSurveyPlanMessage(ProjSurveyPlanDTO dto);

    /**
     * 获取存在最终调研结果的产品数量
     *
     * @param projectInfoId
     * @return
     */
    int getFinishedProductCount(Long projectInfoId);

    /**
     * 获取分配了调研计划但是无需调研的产品数量
     *
     * @param projectInfoId
     * @return
     */
    int getNotNeedSurveyProductCount(Long projectInfoId);


    boolean submitAudit(UpdateSurveyPlanReq req);

    /**
     * 撤销提交审核
     *
     * @param surveyPlanId 调研计划ID
     */
    boolean revertSubmitAudit(Long surveyPlanId);

    /**
     * 撤销确认
     *
     * @param surveyPlanId 调研计划ID
     */
    boolean revertConfirm(Long surveyPlanId);

    List<ProjSurveyPlan> getSurveyPlan(Long projectInfoId, Long hospitalInfoId, Long yyProductId);

    boolean getOnlyOneSurveyFlag(Long projectInfoId, Long hospitalInfoId, Long yyProductId);

    Result saveBackLogAndDetailTest(ConfirmFinalDataParam confirmFinalDataParam);

    /**
     * 导出调研计划数据
     * @param response
     * @param dto
     */
    void surveyPlanExportExcel(HttpServletResponse response, ProjSurveyPlanDTO dto);
}
