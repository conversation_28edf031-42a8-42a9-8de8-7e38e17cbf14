package com.msun.csm.service.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.enums.ReviewTypeEnum;
import com.msun.csm.dao.entity.proj.EarlyWarningAndPenaltyMessages;
import com.msun.csm.dao.entity.proj.ProjIssueInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.model.req.project.GenerateEarlyWarningAndPenaltyMessageArgs;

public interface SendBusinessMessageService {

    /**
     * <p>新增待办时，发送待办任务分配提醒消息<p/>
     * <p>修改待办时，如果待办原来没有责任人，只给新分配的责任人发送待办任务分配提醒消息<p/>
     * <p>修改待办时，如果待办原来的责任人与现在的责任人是同一个人，不发送任何消息<p/>
     * <p>修改待办时，如果待办原来的责任人与现在的责任人不是同一个人，先给原来的责任人发送待办任务取消提醒消息，再给现在的责任人发送待办任务分配提醒消息<p/>
     *
     * @param projectInfoId 项目ID
     * @param oldTodoTask   修改之前的待办，新增待办时此参数传null
     * @param newTodoTask   修改之后的待办
     */
    boolean sendAllocatingOrCancelTaskMessage(Long projectInfoId, ProjTodoTask oldTodoTask, ProjTodoTask newTodoTask);


    boolean sendAllocatingOrCancelIssueMessage(Long projectInfoId, ProjIssueInfo oldTodoTask, ProjIssueInfo newTodoTask);

    /**
     * 发送产品业务调研进度消息
     */
    void sendSurveyProductProgressMessage();

    void sendAllocatingOrCancelSurveyAuditMessage(Long projectInfoId, ProjSurveyPlan oldTodoTask, ProjSurveyPlan newTodoTask);


    @Data
    class ReviewUserIds {
        @ApiModelProperty("主审批人")
        private Set<Long> main = new HashSet<>();
        @ApiModelProperty("副审批人")
        private Set<Long> sub = new HashSet<>();
        @ApiModelProperty("本次查询所有，假如本次查询mainType为1或者2那么这个all也不是全的，是这个条件下的所有人，也就是本次查询后main + sub")
        private Set<Long> all = new HashSet<>();
    }

    /**
     * 根据审核类别编码获取审核人列表
     *
     * @param dictCode
     * @return
     */
    ReviewUserIds getReviewUserIdByCode(String dictCode);

    @Data
    class ReviewUserStrs {
        private List<String> main;
        private List<String> sub;
        private List<String> all;
    }

    /**
     * 根据审批类型编码获取审批人字符串
     * @param dictCode
     * @return
     */
    ReviewUserStrs getReviewUserStrByCode(String dictCode);

    /**
     * 生成预警单和罚款单消息，分配责任人的时候调用该方法来生成罚单跟预警单
     *
     * @param args
     * @return
     */
    List<EarlyWarningAndPenaltyMessages> generateEarlyWarningAndPenaltyMessage(GenerateEarlyWarningAndPenaltyMessageArgs args);

    /**
     * 生成预警单和罚款单消息，分配责任人的时候调用该方法来生成罚单跟预警单
     *
     * @param reviewTypeEnum   处罚类型
     * @param projectInfoId    项目ID
     * @param businessId       业务表主键
     * @param penaltyPersonId  处罚人ID
     * @param penaltyReason    处罚原因
     * @param penaltyStartTime 处罚开始时间
     */
    void generateEarlyWarningAndPenaltyMessage2(ReviewTypeEnum reviewTypeEnum, Long projectInfoId, Long businessId, Long penaltyPersonId, String penaltyReason, Date penaltyStartTime);

    /**
     * 根据罚单生成规则计算罚单推送时间
     *
     * @param cfg
     * @param penaltyStartTime
     * @return
     */
    Date generatePenaltyEndTime(ConfigProjectReview cfg, Date penaltyStartTime, Boolean entryAtCron);

    /**
     * 根据罚单生成规则计算预警单推送时间
     *
     * @param cfg
     * @param penaltyEndTime
     * @return
     */
    List<Date> generateWarningTime(ConfigProjectReview cfg, Date penaltyEndTime, Boolean entryAtCron);

    /**
     * 销毁预警单和罚款单消息，责任人审批或完成任务的时候需要调用该方法作废罚单跟预警单
     *
     * @param businessId
     * @param businessTable
     */
    void destroyEarlyWarningAndPenaltyMessage(Long businessId, String businessTable);

    /**
     * 变更责任人的时候需要同步修改预警单责任人
     *
     * @param reviewTypeEnum 处罚类型
     * @param businessId     业务表主键
     * @param newPerson      新的处罚人
     */
    void updateEarlyWarningAndPenaltyPerson(ReviewTypeEnum reviewTypeEnum, Long businessId, Long newPerson);

    /**
     * 定时任务查询未发送的预警单或罚款单消息
     *
     * @param penaltyCategory
     * @param now
     * @return
     */
    List<EarlyWarningAndPenaltyMessages> cronFindNotSendEarlyWarningOrPenaltyMessages(String penaltyCategory, Date now);

    /**
     * 定时任务查询罚单下的未发送预警单
     *
     * @param penaltyId
     * @return
     */
    List<EarlyWarningAndPenaltyMessages> cronFindNotSendEarlyWarningMessagesByPenaltyId(Long penaltyId);

    /**
     * 修改预警单和罚款单消息的发送标志，定时任务发送完消息需要更新发生状态的时候调用
     *
     * @param earlyWarningAndPenaltyMessagesIds
     * @param flag
     */
    void changeEarlyWarningAndPenaltyMessagesSendFlag(List<Long> earlyWarningAndPenaltyMessagesIds, Boolean flag);

    /**
     * 获取后端处罚人信息
     *
     * @param sysUserId     后端审核人
     * @param teamTypeEnum  后端团队类型
     * @param projectInfoId 项目ID
     * @return 处罚人信息
     */
    Long getBackendPenaltyPerson(Long sysUserId, BackendTeamTypeEnum teamTypeEnum, Long projectInfoId);

    /**
     * 获取前端处罚人信息
     *
     * @param sysUserId     前端负责人
     * @param projectInfoId 项目ID
     * @return 处罚人信息
     */
    Long getPenaltyPerson(Long sysUserId, Long projectInfoId);

}
