package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.service.proj.ProjSwitchPlanServiceImpl.setDefaultStyle;
import static com.msun.csm.service.proj.ProjSwitchPlanServiceImpl.setRegionStyle;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.utils.AesUtil;
import com.msun.core.component.implementation.api.port.DataPreparationApi;
import com.msun.core.component.implementation.api.port.dto.SqlCheckApiDTO;
import com.msun.core.component.implementation.api.port.dto.SqlSelectLoginDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictSimulationBusiness;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjApplyOrderNodeRecord;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.projsimulation.ProjSimulationDetail;
import com.msun.csm.dao.entity.proj.projsimulation.ProjSimulationResult;
import com.msun.csm.dao.entity.proj.projsimulation.ProjSimulationUser;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderNodeRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSimulationDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjSimulationMapper;
import com.msun.csm.dao.mapper.proj.ProjSimulationResultMapper;
import com.msun.csm.dao.mapper.proj.ProjSimulationUserMapper;
import com.msun.csm.model.dto.CompSurveyProductMilestone;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.projsimulation.ProjSimulationUserDTO;
import com.msun.csm.model.dto.projsimulation.SaveProjSimulationRoleDTO;
import com.msun.csm.model.dto.projsimulation.SimulationResultDTO;
import com.msun.csm.model.dto.projsimulation.SimulationRoleDTO;
import com.msun.csm.model.dto.projsimulation.SimulationUserDTO;
import com.msun.csm.model.vo.projsimulation.HospitalSimulatonDetaiVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationDetailExcelVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationDetailVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationResultVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationRoleVO;
import com.msun.csm.model.vo.projsimulation.ProjSimulationUserVO;
import com.msun.csm.model.vo.projsimulation.SimulationDeptVO;
import com.msun.csm.model.vo.projsimulation.SimulationRoleVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;

@Service
@Slf4j
public class ProjSimulationServiceImpl implements ProjSimulationService {

    @Resource
    private ProjSimulationMapper projSimulationMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private DataPreparationApi dataPreparationApi;

    @Resource
    private ProjSimulationDetailMapper projSimulationDetailMapper;

    @Resource
    private ProjSimulationUserMapper projSimulationUserMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjSimulationResultMapper projSimulationResultMapper;

    @Resource
    private ProjApplyOrderMapper projApplyOrderMapper;

    @Resource
    private ProjApplyOrderNodeRecordMapper projApplyOrderNodeRecordMapper;

    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;

    /**
     * 获取模拟角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ProjSimulationRoleVO>> findSimulationRole(ProjSimulationUserDTO dto) {
        List<ProjSimulationRoleVO> projSimulationRoleVOList = Lists.newArrayList();
        List<SimulationRoleVO> simulationRoleList = projSimulationMapper.findSimulationRole(dto);
        if (CollectionUtils.isNotEmpty(simulationRoleList)) {
            for (SimulationRoleVO simulationRole : simulationRoleList) {
                ProjSimulationRoleVO projSimulationRoleVO = new ProjSimulationRoleVO();
                projSimulationRoleVO.setProjSimulationUserId(simulationRole.getProjSimulationUserId());
                projSimulationRoleVO.setRoleId(simulationRole.getRoleId());
                projSimulationRoleVO.setRoleName(simulationRole.getRoleName());
                if (simulationRole.getUniteFlag() == 1) {
                    projSimulationRoleVO.setUniteFlag(true);
                } else {
                    projSimulationRoleVO.setUniteFlag(false);
                }
                if (ObjectUtil.isNotEmpty(simulationRole.getSimulationDeptId())) {
                    SimulationDeptVO simulationDeptVO = new SimulationDeptVO();
                    simulationDeptVO.setId(simulationRole.getSimulationDeptId());
                    simulationDeptVO.setName(simulationRole.getSimulationDeptName());
                    simulationDeptVO.setWardId(simulationRole.getSimulationWardId());
                    projSimulationRoleVO.setDeptObj(simulationDeptVO);
                }
                if (ObjectUtil.isNotEmpty(simulationRole.getSimulationUserId())) {
                    projSimulationRoleVO.setUserObj(new SimulationUserDTO(simulationRole.getSimulationUserId(),
                            simulationRole.getSimulationUserName(), simulationRole.getSimulationUserAccount(),null));
                }
                projSimulationRoleVOList.add(projSimulationRoleVO);
            }
        }
        return Result.success(projSimulationRoleVOList);
    }

    /**
     * 保存模拟用户数据
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveSimulationUser(SaveProjSimulationRoleDTO dto) {
        List<ProjSimulationUser> simulationUserList = Lists.newArrayList();
        List<SimulationRoleDTO> roleUserList = dto.getRoleUserList();
        if (CollectionUtils.isNotEmpty(roleUserList)) {
            for (SimulationRoleDTO roleUser : roleUserList) {
                if (ObjectUtil.isNotEmpty(roleUser.getProjSimulationUserId())) {
                    ProjSimulationUser simulationUser = projSimulationUserMapper.selectById(roleUser.getProjSimulationUserId());
                    if (ObjectUtil.isNotEmpty(roleUser.getDeptObj())) {
                        simulationUser.setSimulationDeptId(roleUser.getDeptObj().getId());
                        simulationUser.setSimulationDeptName(roleUser.getDeptObj().getName());
                        if (ObjectUtil.isNotEmpty(roleUser.getDeptObj().getWardId())) {
                            simulationUser.setSimulationWardId(roleUser.getDeptObj().getWardId());
                        } else {
                            simulationUser.setSimulationWardId(null);
                        }
                    } else {
                        simulationUser.setSimulationDeptId(null);
                        simulationUser.setSimulationDeptName(null);
                    }
                    if (ObjectUtil.isNotEmpty(roleUser.getUserObj())) {
                        simulationUser.setSimulationUserId(roleUser.getUserObj().getId());
                        simulationUser.setSimulationUserName(roleUser.getUserObj().getName());
                        simulationUser.setSimulationUserAccount(roleUser.getUserObj().getAccount());
                    } else {
                        simulationUser.setSimulationUserId(null);
                        simulationUser.setSimulationUserName(null);
                        simulationUser.setSimulationUserAccount(null);
                    }
                    simulationUser.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                    simulationUser.setUpdateTime(new Date());
                    projSimulationUserMapper.updateSimulationUser(simulationUser);
                } else {
                    //校验是否存在多开页面操作的情况
                    if (projSimulationUserMapper.selectCount(new QueryWrapper<ProjSimulationUser>().lambda().eq(ProjSimulationUser::getCustomInfoId, dto.getCustomInfoId())
                            .eq(ProjSimulationUser::getProjectInfoId, dto.getProjectInfoId())
                            .eq(ProjSimulationUser::getHospitalInfoId, dto.getHospitalInfoId())
                            .eq(ProjSimulationUser::getDictRoleId, roleUser.getRoleId())) > 0) {
                        throw new CustomException("模拟用户已在其他账号或页面下保存过，请重新页面后重试操作！");
                    }
                    ProjSimulationUser simulationUser = new ProjSimulationUser();
                    simulationUser.setProjSimulationUserId(SnowFlakeUtil.getId());
                    simulationUser.setCustomInfoId(dto.getCustomInfoId());
                    simulationUser.setProjectInfoId(dto.getProjectInfoId());
                    simulationUser.setHospitalInfoId(dto.getHospitalInfoId());
                    simulationUser.setDictRoleId(roleUser.getRoleId());
                    if (ObjectUtil.isNotEmpty(roleUser.getDeptObj())) {
                        simulationUser.setSimulationDeptId(ObjectUtil.isEmpty(roleUser.getDeptObj().getId()) ? null : roleUser.getDeptObj().getId());
                        simulationUser.setSimulationDeptName(ObjectUtil.isEmpty(roleUser.getDeptObj().getName()) ? null : roleUser.getDeptObj().getName());
                        if (ObjectUtil.isNotEmpty(roleUser.getDeptObj().getWardId())) {
                            simulationUser.setSimulationWardId(roleUser.getDeptObj().getWardId());
                        }
                    }
                    if (ObjectUtil.isNotEmpty(roleUser.getUserObj())) {
                        simulationUser.setSimulationUserId(ObjectUtil.isEmpty(roleUser.getUserObj().getId()) ? null : roleUser.getUserObj().getId());
                        simulationUser.setSimulationUserName(ObjectUtil.isEmpty(roleUser.getUserObj().getName()) ? null : roleUser.getUserObj().getName());
                        simulationUser.setSimulationUserAccount(ObjectUtil.isEmpty(roleUser.getUserObj().getAccount()) ? null : roleUser.getUserObj().getAccount());
                    }
                    simulationUser.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                    simulationUser.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                    simulationUser.setCreateTime(new Date());
                    simulationUser.setUpdateTime(new Date());
                    simulationUser.setIsDeleted(0);
                    simulationUserList.add(simulationUser);
                }
            }
            if (CollectionUtils.isNotEmpty(simulationUserList)) {
                projSimulationUserMapper.insertBatch(simulationUserList);
            }
        }
        return Result.success();
    }
    @Value("${spring.profiles.active}")
    private String activeProfiles;
    /**
     * 获取模拟用户数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjSimulationUserVO> findSimulationUser(ProjSimulationUserDTO dto) {
//        // TODO 测试数据删除
//        if (!"prod".equals(activeProfiles)) {
//            String s = HttpUtil.get("http://127.0.0.1:9443/readProjectResourcesFile/dev/jfpt/aaa");
//            TypeReference<Result<ProjSimulationUserVO>> typeReference = new TypeReference<Result<ProjSimulationUserVO>>() {
//            };
//            return JSON.parseObject(s, typeReference);
//        }

        ProjSimulationUserVO projSimulationUserVO = new ProjSimulationUserVO();
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
        if (ObjectUtil.isEmpty(projHospitalInfo) || projHospitalInfo.getHospitalOpenStatus() < 21) {
            return Result.fail("当前医院暂未开通，无法进行此操作！");
        }
        //调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 组装调用API的参数
        SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
        sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
        // 查询科室信息
        SqlSelectLoginDTO deptDTO = new SqlSelectLoginDTO();
        deptDTO.setHospitalInfoId(projHospitalInfo.getCloudHospitalId());
        StringBuilder sqlsb = new StringBuilder("");
        sqlsb.append("select d.dept_id as id,d.dept_name as name,")
                .append("case d.category_id when '9' then d.dept_id else w.unit_dept_id end as wardId,")
                .append("case d.category_id when '9' then d.dept_name else d2.dept_name end as wardName ")
                .append("from comm.dept d ")
                .append("left join comm.ward_vs_dept wvd on d.dept_id =wvd.dept_id ")
                .append("left join comm.ward w on wvd.ward_id = w.ward_id and w.invalid_flag ='0' ")
                .append("left join comm.dept d2 on w.unit_dept_id = d2.dept_id  and d2.invalid_flag ='0' ")
                .append("where d.hospital_id = ").append(projHospitalInfo.getCloudHospitalId()).append(" and d.invalid_flag ='0'");
        deptDTO.setSqlStr(AesUtil.encrypt(sqlsb.toString()));
        sqlCheckApiDTO.setDto(deptDTO);
        ResponseResult<List<JSONObject>> deptResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
        if (deptResult == null || CollectionUtils.isEmpty(deptResult.getData())) {
            return Result.success(projSimulationUserVO);
        }
        List<SimulationDeptVO> deptVOList = JSONObject.parseArray(JSONObject.toJSONString(deptResult.getData()), SimulationDeptVO.class);
        projSimulationUserVO.setDeptVO(deptVOList);
        //查询人员信息
        SqlSelectLoginDTO userDTO = new SqlSelectLoginDTO();
        userDTO.setHospitalInfoId(projHospitalInfo.getCloudHospitalId());
        String userSql = " select u.dept_id,u.user_id,u.user_name,u.identity_id,a.account_name as user_account "
                + " from comm.\"identity\" u "
                + " left join comm.account a on u.user_id = a.user_id "
                + " where "
                + " u.hospital_id = "
                + projHospitalInfo.getCloudHospitalId()
                + " and "
                + " u.delete_flag = '0' and a.invalid_flag = '0'";
//        String userSql = "select u.dept_id,u.user_id,u.user_name,a.account_name as user_account"
//                + " from comm.user u left join comm.account a on u.user_id = a.user_id where u.hospital_id = "
//                + projHospitalInfo.getCloudHospitalId() + " and u.invalid_flag = '0' and a.invalid_flag = '0'";
        userDTO.setSqlStr(AesUtil.encrypt(userSql));
        sqlCheckApiDTO.setDto(userDTO);
        ResponseResult<List<JSONObject>> userResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
        if (userResult == null || CollectionUtils.isEmpty(userResult.getData())) {
            return Result.success(projSimulationUserVO);
        }
        List<Map> userMaps = JSONObject.parseArray(JSONObject.toJSONString(userResult.getData()), Map.class);
        //将人员信息根据deptId分组
        Map<String, List<SimulationUserDTO>> users = userMaps.stream().collect(
                Collectors.groupingBy(map -> (String) map.get("dept_id"),
                        Collectors.mapping(map -> {
                            SimulationUserDTO user = new SimulationUserDTO();
                            user.setId(Long.valueOf(map.get("user_id").toString()));
                            user.setName((String) map.get("user_name"));
                            user.setAccount((String) map.get("user_account"));
                            user.setIdentityId(Long.valueOf(map.get("identity_id").toString()));
                            return user;
                        }, Collectors.toList())));
        projSimulationUserVO.setUserVO(users);

        return Result.success(projSimulationUserVO);
    }

    /**
     * 检验模拟用例是否发生变化
     *
     * @param dto
     * @return
     */
    @Override
    public Result<Boolean> checkSimulationBusiness(ProjSimulationUserDTO dto) {
        List<ProjSimulationUser> simulationUsers = projSimulationUserMapper.selectList(new QueryWrapper<ProjSimulationUser>()
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("hospital_info_id", dto.getHospitalInfoId()));
        if (CollectionUtils.isEmpty(simulationUsers)) {
            throw new CustomException("请先完成步骤一的《选择模拟用户》再进行此操作！");
        }
        List<ProjSimulationUser> simulationUserList = simulationUsers.stream().filter(
                simulationUser -> ObjectUtil.isEmpty(simulationUser.getSimulationDeptId())
                        || ObjectUtil.isEmpty(simulationUser.getProjSimulationUserId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(simulationUserList)) {
            throw new CustomException("请先完善步骤一的《选择模拟用户》再进行此操作！");
        }
        //查询已经存在的模拟用例
        List<ProjSimulationDetail> projSimulationDetails = projSimulationDetailMapper.selectList(new QueryWrapper<ProjSimulationDetail>()
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("hospital_info_id", dto.getHospitalInfoId())
                .eq("proj_simulation_result_id", -1L)
        );
        //查询模拟用例字典
        List<DictSimulationBusiness> dictSimulationBusinesses = projSimulationMapper.findProjSimulationBusiness(dto.getProjectInfoId());
        if (CollectionUtils.isEmpty(projSimulationDetails)) {
            return Result.success(false);
        } else {
            //判断模拟用例是否发生变化
            List<Long> exitDetails = projSimulationDetails.stream().map(vo -> vo.getDictSimulationBusinessId()).collect(Collectors.toList());
            List<Long> dictDetails = dictSimulationBusinesses.stream().map(dictSimulationBusiness -> dictSimulationBusiness.getDictSimulationBusinessId()).collect(Collectors.toList());
            if (!exitDetails.containsAll(dictDetails) || !dictDetails.containsAll(exitDetails)) {
                //查询项目下的模拟角色
                List<SimulationRoleVO> simulationRoleList = projSimulationMapper.findSimulationRole(dto);
                List<Long> dictRoles = simulationRoleList.stream().map(vo -> vo.getRoleId()).collect(Collectors.toList());
                List<Long> exitRoles = simulationUsers.stream().map(vo -> vo.getDictRoleId()).collect(Collectors.toList());
                if (exitRoles.containsAll(dictRoles)) {
                    return Result.success(true);
                }
                throw new CustomException("检测到模拟用例发生变化，请先到步骤一《选择模拟用户》中完善模拟用户！");
            }
        }
        return Result.success(false);
    }

    /**
     * 导出模拟用例
     *
     * @param response
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportSimulationBusiness(HttpServletResponse response, ProjSimulationUserDTO dto) {
        Boolean createSimulationFlag = false;
        //查询已经存在的模拟用例
        List<ProjSimulationDetailVO> projSimulationDetails = projSimulationMapper.findSimulationDetail(dto);
        //查询模拟用例字典
        List<DictSimulationBusiness> dictSimulationBusinesses = projSimulationMapper.findProjSimulationBusiness(dto.getProjectInfoId());
        if (CollectionUtils.isEmpty(projSimulationDetails)) {
            createSimulationFlag = true;
        } else {
            //判断模拟用例是否发生变化
            List<Long> exitDetails = projSimulationDetails.stream().map(vo -> vo.getDictSimulationBusinessId()).collect(Collectors.toList());
            List<Long> dictDetails = dictSimulationBusinesses.stream().map(dictSimulationBusiness -> dictSimulationBusiness.getDictSimulationBusinessId()).collect(Collectors.toList());
            if (!exitDetails.containsAll(dictDetails) || !dictDetails.containsAll(exitDetails)) {
                projSimulationDetailMapper.delete(new QueryWrapper<ProjSimulationDetail>()
                        .eq("project_info_id", dto.getProjectInfoId())
                        .eq("hospital_info_id", dto.getHospitalInfoId())
                        .eq("proj_simulation_result_id", -1L));
                createSimulationFlag = true;
            }
        }
        if (createSimulationFlag) {
            //生成模拟用例
            List<ProjSimulationDetail> projSimulationDetailList = Lists.newArrayList();
            for (DictSimulationBusiness dictSimulationBusiness : dictSimulationBusinesses) {
                ProjSimulationDetail projSimulationDetail = ProjSimulationDetail.builder()
                        .projSimulationDetailId(SnowFlakeUtil.getId())
                        .projSimulationResultId(-1L)
                        .dictSimulationBusinessId(dictSimulationBusiness.getDictSimulationBusinessId())
                        .customInfoId(dto.getCustomInfoId())
                        .projectInfoId(dto.getProjectInfoId())
                        .hospitalInfoId(dto.getHospitalInfoId())
                        .passFlag(0)
                        .startTime(new Date())
                        .endTime(new Date())
                        .build();
                projSimulationDetail.setIsDeleted(0);
                projSimulationDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                projSimulationDetail.setCreateTime(new Date());
                projSimulationDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                projSimulationDetail.setUpdateTime(new Date());
                projSimulationDetailList.add(projSimulationDetail);
            }
            projSimulationDetailMapper.insertBatch(projSimulationDetailList);
        }

        projSimulationDetails = projSimulationMapper.findSimulationDetail(dto);
        List<ProjSimulationDetailExcelVO> hospitalExcelVOList = excelDataInfo(projSimulationDetails);
        //导出模拟用例
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(dto.getHospitalInfoId());
        XSSFWorkbook workbook = createExcel(projHospitalInfo, hospitalExcelVOList);
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(projHospitalInfo.getHospitalName() + "_全院流程模拟用例.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 将Excel写入输出流
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        /*try (ExcelWriter excelWriter = EasyExcelFactory.write(getOutputStream(
                        projHospitalInfo.getHospitalName() + "_全院流程模拟用例.xlsx", response),
                ProjSimulationDetailExcelVO.class).excludeColumnFieldNames(Collections.singleton("region")).build()) {
            //创建门诊sheet
            WriteSheet clinicSheet = EasyExcel.writerSheet("门诊业务演练用例").build();
            // 生成导出Excel数据
            List<ProjSimulationDetailVO> clinicSimulationDetails = projSimulationDetails.stream().filter(
                    projSimulationDetailVO -> projSimulationDetailVO.getSimulationType() == 1).collect(Collectors.toList());
            List<ProjSimulationDetailExcelVO> clinicExcelVOList = excelDataInfo(clinicSimulationDetails);
            excelWriter.write(clinicExcelVOList, clinicSheet);
            //创建住院sheet
            WriteSheet hospitalSheet = EasyExcel.writerSheet("住院业务演练用例").build();
            List<ProjSimulationDetailVO> hospitalSimulationDetails = projSimulationDetails.stream().filter(
                    projSimulationDetailVO -> projSimulationDetailVO.getSimulationType() == 2).collect(Collectors.toList());
            List<ProjSimulationDetailExcelVO> hospitalExcelVOList = excelDataInfo(hospitalSimulationDetails);
            excelWriter.write(hospitalExcelVOList, hospitalSheet);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }*/
    }

    /**
     * 生成导出Excel数据
     *
     * @param projSimulationDetails
     * @return
     */
    private List<ProjSimulationDetailExcelVO> excelDataInfo(List<ProjSimulationDetailVO> projSimulationDetails) {
        List<ProjSimulationDetailExcelVO> excelVOList = Lists.newArrayList();
        for (int i = 0; i < projSimulationDetails.size(); i++) {
            ProjSimulationDetailVO projSimulationDetailVO = projSimulationDetails.get(i);
            ProjSimulationDetailExcelVO projSimulationDetailExcelVO = new ProjSimulationDetailExcelVO();
            projSimulationDetailExcelVO.setSortNume(i + 1);
            projSimulationDetailExcelVO.setSimulationType(projSimulationDetailVO.getSimulationType());
            projSimulationDetailExcelVO.setSimulationScene(projSimulationDetailVO.getSimulationScene());
            projSimulationDetailExcelVO.setCheckSql(projSimulationDetailVO.getCheckSql());
            if (ObjectUtil.isNotEmpty(projSimulationDetailVO.getRoleName())) {
                projSimulationDetailExcelVO.setRoleName(projSimulationDetailVO.getRoleName());
            } else {
                projSimulationDetailExcelVO.setRoleName("无");
            }
            if (ObjectUtil.isNotEmpty(projSimulationDetailVO.getSimulationUserId())) {
                projSimulationDetailExcelVO.setSimulationUserId(projSimulationDetailVO.getSimulationUserId());
                projSimulationDetailExcelVO.setSimulationDeptName(projSimulationDetailVO.getSimulationDeptName());
                projSimulationDetailExcelVO.setSimulationUser(projSimulationDetailVO.getSimulationUserName() + "\r\n账号：" + projSimulationDetailVO.getSimulationUserAccount());
                projSimulationDetailExcelVO.setSimulationBusiness(projSimulationDetailVO.getSimulationBusiness().replace("#{mzysDeptName}", projSimulationDetailVO.getSimulationDeptName()));
            } else {
                projSimulationDetailExcelVO.setSimulationDeptName("无");
                projSimulationDetailExcelVO.setSimulationUser("无");
                projSimulationDetailExcelVO.setSimulationBusiness(projSimulationDetailVO.getSimulationBusiness());
            }
            projSimulationDetailExcelVO.setProductName(projSimulationDetailVO.getProductName());
            excelVOList.add(projSimulationDetailExcelVO);
        }
        return excelVOList;
    }

    /**
     * 构建输出流
     *
     * @param fileName：文件名称
     * @param response：
     * @return
     * @throws Exception
     */
    private OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 告知浏览器下载，下载附件的形式
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        return response.getOutputStream();
    }

    /**
     * 查询模拟结果
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<ProjSimulationResultVO>> findSimulationResult(SimulationResultDTO dto) {
        List<ProjSimulationResultVO> projSimulationResults = Lists.newArrayList();
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtils.isEmpty(hospitalInfoList)) {
            return Result.success(Collections.emptyList());
        }
        for (ProjHospitalInfo projHospitalInfo : hospitalInfoList) {
            ProjSimulationResultVO projSimulationResultVO = new ProjSimulationResultVO();
            projSimulationResultVO.setHospitalInfoId(projHospitalInfo.getHospitalInfoId());
            projSimulationResultVO.setHospitalName(projHospitalInfo.getHospitalName());
            projSimulationResultVO.setCloudHospitalId(projHospitalInfo.getCloudHospitalId());
            //检测是否已存在要查询的模拟结果
            Boolean isExit = checkExitSimulationResult(dto, projHospitalInfo, projSimulationResultVO);
            if (isExit) {
                projSimulationResults.add(projSimulationResultVO);
                continue;
            }

            //查询步骤一状态
            List<ProjSimulationUser> simulationUsers = projSimulationUserMapper.selectList(new QueryWrapper<ProjSimulationUser>()
                    .eq("project_info_id", dto.getProjectInfoId())
                    .eq("hospital_info_id", projHospitalInfo.getHospitalInfoId()));
            if (CollectionUtils.isNotEmpty(simulationUsers)) {
                List<ProjSimulationUser> simulationUserList = simulationUsers.stream().filter(
                        simulationUser -> ObjectUtil.isEmpty(simulationUser.getSimulationDeptId())
                                || ObjectUtil.isEmpty(simulationUser.getProjSimulationUserId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(simulationUserList)) {
                    projSimulationResultVO.setStepOneState(1);
                    //查询处理步骤二状态
                    handleStepTwoState(dto, projHospitalInfo, projSimulationResultVO);
                } else {
                    projSimulationResultVO.setProjSimulationResultId(-1L);
                    projSimulationResultVO.setStepOneState(0);
                    projSimulationResultVO.setStepTwoState(0);
                    projSimulationResultVO.setClinicCoverage("0%");
                    projSimulationResultVO.setHospitalCoverage("0%");
                    projSimulationResultVO.setSimulationState(0);
                }
            } else {
                projSimulationResultVO.setProjSimulationResultId(-1L);
                projSimulationResultVO.setStepOneState(0);
                projSimulationResultVO.setStepTwoState(0);
                projSimulationResultVO.setClinicCoverage("0%");
                projSimulationResultVO.setHospitalCoverage("0%");
                projSimulationResultVO.setSimulationState(0);
            }
            projSimulationResults.add(projSimulationResultVO);
        }
        return Result.success(projSimulationResults);
    }

    /**
     * 检测是否已存在要查询的模拟结果
     *
     * @param dto
     * @param projHospitalInfo
     * @param projSimulationResultVO
     */
    private Boolean checkExitSimulationResult(SimulationResultDTO dto, ProjHospitalInfo projHospitalInfo, ProjSimulationResultVO projSimulationResultVO) {
        List<ProjSimulationResult> simulationResults = Lists.newArrayList();
        //有查询日期参数时查询时间段内是否已生成模拟结果
        if (StringUtils.isNotBlank(dto.getStartTime()) && StringUtils.isNotBlank(dto.getEndTime())) {
            simulationResults = projSimulationResultMapper.selectList(new QueryWrapper<ProjSimulationResult>()
                    .eq("project_info_id", dto.getProjectInfoId())
                    .eq("hospital_info_id", projHospitalInfo.getHospitalInfoId())
                    .eq("start_time", DateUtil.parse(dto.getStartTime()))
                    .eq("end_time", DateUtil.parse(dto.getEndTime()))
                    .orderByDesc("create_time")
            );
        } else {
            //没有日期参数时默认查询最近一次的模拟结果
            simulationResults = projSimulationResultMapper.selectList(new QueryWrapper<ProjSimulationResult>()
                    .eq("project_info_id", dto.getProjectInfoId())
                    .eq("hospital_info_id", projHospitalInfo.getHospitalInfoId())
                    .orderByDesc("create_time")
            );
        }
        if (CollectionUtils.isEmpty(simulationResults)) {
            return false;
        }
        //查询已经存在的模拟用例
        List<ProjSimulationDetail> projSimulationDetails = projSimulationDetailMapper.selectList(new QueryWrapper<ProjSimulationDetail>()
                .eq("proj_simulation_result_id", simulationResults.get(0).getProjSimulationResultId())
        );
        //查询模拟用例字典
        List<ProjSimulationDetail> dictSimulationBusinesses = projSimulationDetailMapper.selectList(new QueryWrapper<ProjSimulationDetail>()
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("hospital_info_id", projHospitalInfo.getHospitalInfoId())
                .eq("proj_simulation_result_id", -1L));
        //判断模拟用例是否发生变化
        List<Long> exitDetails = projSimulationDetails.stream().map(vo -> vo.getDictSimulationBusinessId()).collect(Collectors.toList());
        List<Long> dictDetails = dictSimulationBusinesses.stream().map(dictSimulationBusiness -> dictSimulationBusiness.getDictSimulationBusinessId()).collect(Collectors.toList());
        if (!exitDetails.containsAll(dictDetails) || !dictDetails.containsAll(exitDetails)) {
            return false;
        }
        //如果有数据直接赋值返回，无需再次查询
        if (CollectionUtils.isNotEmpty(simulationResults)) {
            projSimulationResultVO.setProjSimulationResultId(simulationResults.get(0).getProjSimulationResultId());
            projSimulationResultVO.setStepOneState(1);
            projSimulationResultVO.setStepTwoState(1);
            projSimulationResultVO.setClinicCoverage(simulationResults.get(0).getClinicCoverage());
            projSimulationResultVO.setHospitalCoverage(simulationResults.get(0).getHospitalCoverage());
            projSimulationResultVO.setSimulationState(simulationResults.get(0).getSimulationState());
            return true;
        }
        return false;
    }

    /**
     * 查询处理步骤二状态
     *
     * @param dto
     * @param projHospitalInfo
     * @param projSimulationResultVO
     */
    private void handleStepTwoState(SimulationResultDTO dto, ProjHospitalInfo projHospitalInfo, ProjSimulationResultVO projSimulationResultVO) {
        //查询模拟明细数据
        ProjSimulationUserDTO userDTO = new ProjSimulationUserDTO();
        userDTO.setCustomInfoId(dto.getCustomInfoId());
        userDTO.setProjectInfoId(dto.getProjectInfoId());
        userDTO.setHospitalInfoId(projHospitalInfo.getHospitalInfoId());
        //查询是否导出过模拟用例模版
        List<HospitalSimulatonDetaiVO> projSimulationDetails = projSimulationMapper.findHospitalSimulatonDetail(userDTO);
        if (CollectionUtils.isNotEmpty(projSimulationDetails)) {
            //已经导出过模拟用例
            projSimulationResultVO.setStepTwoState(1);
            //处理查询时间
            handleTime(dto);
            //筛选查询时间段内是否有统计明细
            List<HospitalSimulatonDetaiVO> simulationDetails = projSimulationDetails.stream().filter(
                    projSimulationDetail -> projSimulationDetail.getStartTime().compareTo(DateUtil.parse(dto.getStartTime())) == 0
                            && projSimulationDetail.getEndTime().compareTo(DateUtil.parse(dto.getEndTime())) == 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(simulationDetails)) {
                //该时间段内已经查询过，直接返回上次的查询结果
                ProjSimulationResult projSimulationResult = projSimulationResultMapper.selectById(simulationDetails.get(0).getProjSimulationResultId());
                projSimulationResultVO.setClinicCoverage(projSimulationResult.getClinicCoverage());
                projSimulationResultVO.setHospitalCoverage(projSimulationResult.getHospitalCoverage());
                projSimulationResultVO.setSimulationState(projSimulationResult.getSimulationState());
            } else {
                //查询时间段内没有统计明细，则重新统计，取出导出用例时保存的没有projSimulationResultId的detail数据
                List<HospitalSimulatonDetaiVO> detailList = projSimulationDetails.stream().filter(
                        projSimulationDetail -> projSimulationDetail.getProjSimulationResultId().equals(-1L)).collect(Collectors.toList());
                ProjSimulationResult result = createSimulationDetails(dto, projHospitalInfo, detailList);
                projSimulationResultVO.setProjSimulationResultId(result.getProjSimulationResultId());
                projSimulationResultVO.setClinicCoverage(result.getClinicCoverage());
                projSimulationResultVO.setHospitalCoverage(result.getHospitalCoverage());
                projSimulationResultVO.setSimulationState(result.getSimulationState());
            }
        } else {
            projSimulationResultVO.setStepTwoState(0);
            projSimulationResultVO.setClinicCoverage("0%");
            projSimulationResultVO.setHospitalCoverage("0%");
            projSimulationResultVO.setSimulationState(0);
        }
    }

    /**
     * 处理查询时间
     *
     * @param dto
     * @return
     */
    private void handleTime(SimulationResultDTO dto) {
        if (ObjectUtil.isEmpty(dto.getStartTime())) {
            //前端没有选择开始时间的话默认开始时间为项目首次环境申请时，工单交付时间，默认结束时间为当前日期
            ProjApplyOrder applyOrder = projApplyOrderMapper.selectOne(new QueryWrapper<ProjApplyOrder>()
                    .eq("project_info_id", dto.getProjectInfoId())
                    .eq("apply_type", 1)
                    .last(" limit 1"));
            if (ObjectUtil.isNotEmpty(applyOrder)) {
                //查询工单的交付节点的审核记录
                ProjApplyOrderNodeRecord record = projApplyOrderNodeRecordMapper.selectOne(new QueryWrapper<ProjApplyOrderNodeRecord>()
                        .eq("apply_order_id", applyOrder.getId())
                        .eq("node_type_code", 5)
                        .last(" limit 1"));
                if (ObjectUtil.isNotEmpty(record)) {
                    dto.setStartTime(DateUtil.format(record.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
            } else {
                dto.setStartTime(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00");
            }
        }
        if (ObjectUtil.isEmpty(dto.getEndTime())) {
            dto.setEndTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }

    /**
     * 重新生成新时间段的检测明细数据
     *
     * @param dto
     * @param projHospitalInfo
     * @param projSimulationDetails
     */
    private ProjSimulationResult createSimulationDetails(SimulationResultDTO dto, ProjHospitalInfo projHospitalInfo, List<HospitalSimulatonDetaiVO> projSimulationDetails) {
        //对SQL检测通过的明细进行计数
        int clinicPassCount = 0;
        int hospitalPassCount = 0;
        Long resultId = SnowFlakeUtil.getId();
        //门诊模拟用例明细
        int clinicCount = projSimulationDetails.stream().filter(
                        projSimulationDetail -> projSimulationDetail.getSimulationType() == 1)
                .collect(Collectors.toList()).size();
        //住院模拟用例明细
        int hospitalCount = projSimulationDetails.stream().filter(
                        projSimulationDetail -> projSimulationDetail.getSimulationType() == 2)
                .collect(Collectors.toList()).size();
        //调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 组装调用API的参数
        SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
        sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
        SqlSelectLoginDTO checkDTO = new SqlSelectLoginDTO();
        checkDTO.setHospitalInfoId(projHospitalInfo.getCloudHospitalId());
        List<ProjSimulationDetail> detailList = Lists.newArrayList();
        for (HospitalSimulatonDetaiVO projSimulationDetail : projSimulationDetails) {
            ProjSimulationDetail simulationDetail = new ProjSimulationDetail();
            BeanUtil.copyProperties(projSimulationDetail, simulationDetail);
            simulationDetail.setProjSimulationDetailId(SnowFlakeUtil.getId());
            simulationDetail.setProjSimulationResultId(resultId);
            simulationDetail.setCreateTime(DateUtil.parse(dto.getStartTime()));
            simulationDetail.setEndTime(DateUtil.parse(dto.getEndTime()));
            simulationDetail.setIsDeleted(0);
            simulationDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            simulationDetail.setCreateTime(new Date());
            simulationDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            simulationDetail.setUpdateTime(new Date());
            if (ObjectUtil.isNotEmpty(projSimulationDetail.getCheckSql())) {
                String schema = "chis";
                //配置了检测脚本，没有维护模拟用户，则默认不通过
                if (ObjectUtil.isEmpty(projSimulationDetail.getSimulationUserId())) {
                    simulationDetail.setPassFlag(0);
                    detailList.add(simulationDetail);
                    continue;
                }
                String checkSql = projSimulationDetail.getCheckSql();
                if (checkSql.startsWith("chisapp|")) {
                    schema = "chisapp";
                    checkSql = checkSql.replace("chisapp|", "");
                    //查询chisapp的脚本需要先在chis库中查询出用户的角色ID
                    String roleSql = "select identity_id from comm.identity i  where user_id in (" + projSimulationDetail.getSimulationUserId() + ")";
                    checkDTO.setSqlStr(AesUtil.encrypt(roleSql));
                    sqlCheckApiDTO.setDto(checkDTO);
                    ResponseResult<List<JSONObject>> roleResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
                    if (roleResult == null || !roleResult.isSuccess() || CollectionUtils.isEmpty(roleResult.getData())) {
                        simulationDetail.setPassFlag(0);
                        detailList.add(simulationDetail);
                        continue;
                    }
                    List<JSONObject> roleObjects = JSONObject.parseArray(JSONObject.toJSONString(roleResult.getData()), JSONObject.class);
                    String simulationRoleIds = "";
                    for (JSONObject roleObject : roleObjects) {
                        simulationRoleIds += roleObject.getString("identity_id") + ",";
                    }
                    if (StringUtils.isNotEmpty(simulationRoleIds)) {
                        simulationRoleIds = simulationRoleIds.substring(0, simulationRoleIds.length() - 1);
                    }
                    checkSql = checkSql.replace("#{simulationUserId}", simulationRoleIds);
                }
                if (checkSql.contains(";")) {
                    //查询项目下的实施产品
                    List<ProjProductDeliverRecord> deliverRecords = productDeliverRecordMapper.selectList(new QueryWrapper<ProjProductDeliverRecord>().
                            eq("project_info_id", dto.getProjectInfoId()));
                    List<Long> deliverProductIds = deliverRecords.stream().map(ProjProductDeliverRecord::getProductDeliverId).collect(Collectors.toList());
                    //检测脚本中包含分号，表示需要根据项目下的实施产品动态调整检测脚本
                    String[] productSql = checkSql.split(";");
                    StringBuilder productSqlSb = new StringBuilder("");
                    for (String sql : productSql) {
                        //每个产品的脚本用“产品ID|检测脚本”的格式存储
                        String[] sqlAry = sql.split("\\|");
                        if (deliverProductIds.contains(Long.valueOf(sqlAry[0]))) {
                            if (ObjectUtil.isNotEmpty(productSqlSb)) {
                                productSqlSb.append(" union all ");
                            }
                            productSqlSb.append(sqlAry[1]);
                        }
                    }
                    checkSql = productSqlSb.toString();
                }
                //替换检测脚本中的查询时间和用户ID参数
                checkSql = checkSql.replace("#{startTime}", dto.getStartTime())
                        .replace("#{endTime}", dto.getEndTime())
                        .replace("#{simulationUserId}", projSimulationDetail.getSimulationUserId().toString());
                checkDTO.setSqlStr(AesUtil.encrypt(checkSql));
                sqlCheckApiDTO.setDto(checkDTO);
                ResponseResult<List<JSONObject>> checkResult = new ResponseResult<>();
                if ("chis".equals(schema)) {
                    checkResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
                } else {
                    checkResult = dataPreparationApi.executeChisappDb(sqlCheckApiDTO);
                }
                if (checkResult == null || !checkResult.isSuccess() || CollectionUtils.isEmpty(checkResult.getData())) {
                    simulationDetail.setPassFlag(0);
                } else {
                    List<JSONObject> jsonObjects = JSONObject.parseArray(JSONObject.toJSONString(checkResult.getData()), JSONObject.class);
                    //检测脚本返回的数据每行的num字段都为1时表示检测通过
                    Boolean passFlag = Boolean.TRUE;
                    for (JSONObject json : jsonObjects) {
                        if (json.getInteger("num") == 0) {
                            passFlag = false;
                            break;
                        }
                    }
                    if (passFlag) {
                        simulationDetail.setPassFlag(1);
                        if (projSimulationDetail.getSimulationType() == 1) {
                            clinicPassCount++;
                        } else {
                            hospitalPassCount++;
                        }
                    } else {
                        simulationDetail.setPassFlag(0);
                    }

                }
            } else {
                //没有维护检测脚本默认通过
                simulationDetail.setPassFlag(1);
                if (projSimulationDetail.getSimulationType() == 1) {
                    clinicPassCount++;
                } else {
                    hospitalPassCount++;
                }
            }
            detailList.add(simulationDetail);
        }
        if (CollectionUtils.isNotEmpty(detailList)) {
            projSimulationDetailMapper.insertBatch(detailList);
        }
        //创建模拟结果数据
        ProjSimulationResult projSimulationResult = ProjSimulationResult.builder()
                .projSimulationResultId(resultId)
                .customInfoId(dto.getCustomInfoId())
                .projectInfoId(dto.getProjectInfoId())
                .hospitalInfoId(projSimulationDetails.get(0).getHospitalInfoId())
                .startTime(DateUtil.parse(dto.getStartTime()))
                .endTime(DateUtil.parse(dto.getEndTime())).build();
        Boolean clinicPass = false;
        Boolean hospitalPass = false;
        if (clinicCount > 0) {
            projSimulationResult.setClinicCoverage(String.format("%.2f", clinicPassCount * 100.0 / clinicCount) + "%");
            if ((Double.valueOf(clinicPassCount) / Double.valueOf(clinicCount)) >= 0.8) {
                clinicPass = true;
            }
        } else {
            clinicPass = true;
            projSimulationResult.setClinicCoverage("100%");
        }
        if (hospitalCount > 0) {
            projSimulationResult.setHospitalCoverage(String.format("%.2f", hospitalPassCount * 100.0 / hospitalCount) + "%");
            if ((Double.valueOf(hospitalPassCount) / Double.valueOf(hospitalCount)) >= 0.8) {
                hospitalPass = true;
            }
        } else {
            hospitalPass = true;
            projSimulationResult.setHospitalCoverage("100%");
        }
        if (clinicPass && hospitalPass) {
            projSimulationResult.setSimulationState(3);
        } else if (clinicCount > 0 && hospitalCount > 0) {
            projSimulationResult.setSimulationState(2);
        } else {
            projSimulationResult.setSimulationState(1);
        }
        projSimulationResultMapper.insert(projSimulationResult);
        return projSimulationResult;
    }

    /**
     * 查询模拟结果明细
     *
     * @param projSimulationResultId
     * @return
     */
    @Override
    public Result<List<ProjSimulationDetailVO>> findSimulationDetail(Long projSimulationResultId, Long simulationType) {
        if (projSimulationResultId == -1) {
            return Result.success(Collections.emptyList());
        }
        List<ProjSimulationDetailVO> projSimulationDetails = projSimulationMapper.findSimulationDetailByResultId(projSimulationResultId, simulationType);
        for (ProjSimulationDetailVO projSimulationDetail : projSimulationDetails) {
            if (ObjectUtil.isNotEmpty(projSimulationDetail.getSimulationUserName())) {
                projSimulationDetail.setSimulationUserName(projSimulationDetail.getSimulationUserName() + "\r\n账号：" + projSimulationDetail.getSimulationUserAccount());
            }
            if (ObjectUtil.isNotEmpty(projSimulationDetail.getSimulationDeptName())) {
                projSimulationDetail.setSimulationBusiness(projSimulationDetail.getSimulationBusiness().replace("#{mzysDeptName}", projSimulationDetail.getSimulationDeptName()));
            }
        }
        return Result.success(projSimulationDetails);
    }

    /**
     * 确认完成校验
     *
     * @param dto
     * @return
     */
    @Override
    public Result<Boolean> checkComplete(CompSurveyProductMilestone dto) {
        //检测项目下是否有医院已完成自动化测试
        //TODO
        //检测项目下是否有医院已完成全院流程模拟
        List<ProjSimulationResult> simulationResults = projSimulationResultMapper.selectList(new LambdaQueryWrapper<ProjSimulationResult>()
                .eq(ProjSimulationResult::getProjectInfoId, dto.getProjectInfoId())
                .eq(ProjSimulationResult::getSimulationState, 3));
        if (org.springframework.util.CollectionUtils.isEmpty(simulationResults)) {
            return Result.success(false);
        }
        return Result.success(simulationResults.size() > 0);
    }

    /**
     * 创建Excel文件
     *
     * @param projSimulationDetails
     * @return
     */
    @NotNull
    private XSSFWorkbook createExcel(ProjHospitalInfo projHospitalInfo, List<ProjSimulationDetailExcelVO> projSimulationDetails) {
        // 创建Excel对象
        XSSFWorkbook workbook = new XSSFWorkbook();
        //调用API时设置domain信息
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 组装调用API的参数
        SqlCheckApiDTO sqlCheckApiDTO = new SqlCheckApiDTO();
        sqlCheckApiDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
        sqlCheckApiDTO.setOrgId(projHospitalInfo.getOrgId());
        sqlCheckApiDTO.setHisOrgId(projHospitalInfo.getOrgId());
        SqlSelectLoginDTO checkDTO = new SqlSelectLoginDTO();
        checkDTO.setHospitalInfoId(projHospitalInfo.getCloudHospitalId());
        SimulationResultDTO dto = new SimulationResultDTO();
        ProjSimulationResult projSimulationResult = projSimulationResultMapper.selectOne(new QueryWrapper<ProjSimulationResult>()
                .eq("hospital_info_id", projHospitalInfo.getHospitalInfoId())
                .orderByDesc("create_time").last(" limit 1")
        );
        if (ObjectUtil.isNotEmpty(projSimulationResult)) {
            dto.setStartTime(DateUtil.format(projSimulationResult.getStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            dto.setEndTime(DateUtil.format(projSimulationResult.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
        } else {
            handleTime(dto);
        }
        //按照门诊和住院分类
        Map<Integer, List<ProjSimulationDetailExcelVO>> details = projSimulationDetails.stream().collect(Collectors.groupingBy(ProjSimulationDetailExcelVO::getSimulationType, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<Integer, List<ProjSimulationDetailExcelVO>> entry : details.entrySet()) {
            createSheet(workbook, entry, dto, sqlCheckApiDTO, checkDTO);
        }
        return workbook;
    }

    /**
     * 生成ExcelSheet页
     *
     * @param workbook
     * @param entry
     * @param dto
     * @param sqlCheckApiDTO
     * @param checkDTO
     */
    private void createSheet(XSSFWorkbook workbook, Map.Entry<Integer, List<ProjSimulationDetailExcelVO>> entry, SimulationResultDTO dto, SqlCheckApiDTO sqlCheckApiDTO, SqlSelectLoginDTO checkDTO) {
        String sheetName = "";
        if (entry.getKey() == 1) {
            sheetName = "门诊业务演练用例";
        } else {
            sheetName = "住院业务演练用例";
        }
        Sheet sheet = workbook.createSheet(sheetName);
        // 设置单元格宽度
        sheet.setColumnWidth(0, 2000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 4000);
        sheet.setColumnWidth(4, 5000);
        sheet.setColumnWidth(5, 22000);
        sheet.setColumnWidth(6, 8000);
        sheet.setColumnWidth(7, 3000);
        sheet.setColumnWidth(8, 20000);
        // 标题行
        Row titleRow = sheet.createRow(0);
        // 创建单元格并填充数据
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(sheetName);
        // 创建字体样式对象
        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体"); // 设置字体名称
        titleFont.setFontHeightInPoints((short) 20); // 设置字体大小
        titleFont.setBold(true); // 设置字体加粗
        // 创建单元格样式对象
        CellStyle titleStyle = setDefaultStyle(workbook);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleCell.setCellStyle(titleStyle);
        // 合并单元格范围
        CellRangeAddress title = new CellRangeAddress(0, 0, 0, 8);
        sheet.addMergedRegion(title);
        // 合并之后为合并的单元格设置样式
        setRegionStyle(sheet, title, titleStyle);
        // 标题行
        Row subTitleRow = sheet.createRow(1);
        // 创建单元格并填充数据
        Cell subTitleCell0 = subTitleRow.createCell(0);
        subTitleCell0.setCellValue("序号");
        Cell subTitleCell1 = subTitleRow.createCell(1);
        subTitleCell1.setCellValue("场景");
        Cell subTitleCell2 = subTitleRow.createCell(2);
        subTitleCell2.setCellValue("应用角色");
        Cell subTitleCell3 = subTitleRow.createCell(3);
        subTitleCell3.setCellValue("演练科室");
        Cell subTitleCell4 = subTitleRow.createCell(4);
        subTitleCell4.setCellValue("负责人");
        Cell subTitleCell5 = subTitleRow.createCell(5);
        subTitleCell5.setCellValue("模拟内容点");
        Cell subTitleCell6 = subTitleRow.createCell(6);
        subTitleCell6.setCellValue("设计产品");
        Cell subTitleCell7 = subTitleRow.createCell(7);
        subTitleCell7.setCellValue("是否通过");
        Cell subTitleCell8 = subTitleRow.createCell(8);
        subTitleCell8.setCellValue("问题记录");
        // 创建字体样式对象
        Font subTitleFont = workbook.createFont();
        subTitleFont.setFontName("宋体"); // 设置字体名称
        subTitleFont.setFontHeightInPoints((short) 12); // 设置字体大小
        subTitleFont.setBold(true); // 设置字体加粗
        // 创建单元格样式对象
        CellStyle subTitleStyle = setDefaultStyle(workbook);
        subTitleStyle.setFont(subTitleFont);
        subTitleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置背景颜色（这里我们用浅灰色）
        subTitleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        subTitleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        subTitleCell0.setCellStyle(subTitleStyle);
        subTitleCell1.setCellStyle(subTitleStyle);
        subTitleCell2.setCellStyle(subTitleStyle);
        subTitleCell3.setCellStyle(subTitleStyle);
        subTitleCell4.setCellStyle(subTitleStyle);
        subTitleCell5.setCellStyle(subTitleStyle);
        subTitleCell6.setCellStyle(subTitleStyle);
        subTitleCell7.setCellStyle(subTitleStyle);
        subTitleCell8.setCellStyle(subTitleStyle);
        //处理切换方案
        int currentRow = 2;
        int rowIndex = 2;
        //创建字体样式对象
        Font font = workbook.createFont();
        font.setFontName("宋体"); // 设置字体名称
        font.setFontHeightInPoints((short) 12); // 设置字体大小
        // 创建单元格样式对象
        CellStyle style = setDefaultStyle(workbook);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle style1 = setDefaultStyle(workbook);
        style1.setFont(font);
        style1.setAlignment(HorizontalAlignment.CENTER);
        style1.setVerticalAlignment(VerticalAlignment.CENTER);
        style1.setWrapText(true);
        CellStyle style2 = setDefaultStyle(workbook);
        style2.setFont(font);
        style2.setAlignment(HorizontalAlignment.LEFT);
        style2.setVerticalAlignment(VerticalAlignment.CENTER);
        style2.setWrapText(true);
        //填充单元格数据
        List<ProjSimulationDetailExcelVO> businessDetails = entry.getValue();
        int m = 0;
        //按照场景分类
        Map<String, List<ProjSimulationDetailExcelVO>> scenceMap = businessDetails.stream().collect(Collectors.groupingBy(ProjSimulationDetailExcelVO::getSimulationScene, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<ProjSimulationDetailExcelVO>> scene : scenceMap.entrySet()) {
            //按照应用角色分类
            Map<String, List<ProjSimulationDetailExcelVO>> roleMap = scene.getValue().stream().collect(Collectors.groupingBy(ProjSimulationDetailExcelVO::getRoleName, LinkedHashMap::new, Collectors.toList()));
            List<ProjSimulationDetailExcelVO> senceDetails = scene.getValue();
            for (Map.Entry<String, List<ProjSimulationDetailExcelVO>> role : roleMap.entrySet()) {
                List<ProjSimulationDetailExcelVO> roleDetails = role.getValue();
                for (int k = 0; k < roleDetails.size(); k++) {
                    m++;
                    ProjSimulationDetailExcelVO detail = roleDetails.get(k);
                    //创建一行
                    Row row = sheet.createRow(currentRow + k);
                    //填充数据
                    Cell cell0 = row.createCell(0);
                    cell0.setCellValue(m);
                    Cell cell1 = row.createCell(1);
                    cell1.setCellValue(detail.getSimulationScene());
                    Cell cell2 = row.createCell(2);
                    cell2.setCellValue(role.getKey());
                    Cell cell3 = row.createCell(3);
                    cell3.setCellValue(detail.getSimulationDeptName());
                    Cell cell4 = row.createCell(4);
                    cell4.setCellValue(detail.getSimulationUser());
                    Cell cell5 = row.createCell(5);
                    cell5.setCellValue(detail.getSimulationBusiness());
                    Cell cell6 = row.createCell(6);
                    cell6.setCellValue(detail.getProductName());
                    //校验是否通过
                    Cell cell7 = row.createCell(7);
                    Boolean checkResult = false;
                    if (ObjectUtil.isEmpty(detail.getCheckSql())) {
                        checkResult = true;
                    } else {
                        checkResult = checkSqlResult(dto, sqlCheckApiDTO, checkDTO, detail);
                    }
                    if (checkResult) {
                        cell7.setCellValue("是");
                    } else {
                        cell7.setCellValue("否");
                    }
                    Cell cell8 = row.createCell(8);

                    cell0.setCellStyle(style);
                    cell1.setCellStyle(style);
                    cell2.setCellStyle(style);
                    cell3.setCellStyle(style);
                    cell4.setCellStyle(style1);
                    cell5.setCellStyle(style2);
                    cell6.setCellStyle(style2);
                    cell7.setCellStyle(style);
                    cell8.setCellStyle(style2);
                }
                if (roleDetails.size() > 1) {
                    //合并单元格范围
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow - 1 + roleDetails.size(), 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow - 1 + roleDetails.size(), 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow - 1 + roleDetails.size(), 4, 4));
                }
                currentRow += roleDetails.size();
            }
            if (senceDetails.size() > 1) {
                //合并单元格范围
                CellRangeAddress sceneRange = new CellRangeAddress(rowIndex, rowIndex - 1 + senceDetails.size(), 1, 1);
                sheet.addMergedRegion(sceneRange);
            }
            rowIndex += senceDetails.size();
        }
    }

    /**
     * 检测是否通过
     *
     * @param dto
     * @param sqlCheckApiDTO
     * @param checkDTO
     * @param projSimulationDetail
     * @return
     */
    private Boolean checkSqlResult(SimulationResultDTO dto, SqlCheckApiDTO sqlCheckApiDTO, SqlSelectLoginDTO checkDTO, ProjSimulationDetailExcelVO projSimulationDetail) {
        String schema = "chis";
        //配置了检测脚本，没有维护模拟用户，则默认不通过
        if (ObjectUtil.isEmpty(projSimulationDetail.getSimulationUserId())) {
            return false;
        }
        String checkSql = projSimulationDetail.getCheckSql();
        if (checkSql.startsWith("chisapp|")) {
            schema = "chisapp";
            checkSql = checkSql.replace("chisapp|", "");
            //查询chisapp的脚本需要先在chis库中查询出用户的角色ID
            String roleSql = "select identity_id from comm.identity i  where user_id in (" + projSimulationDetail.getSimulationUserId() + ")";
            checkDTO.setSqlStr(AesUtil.encrypt(roleSql));
            sqlCheckApiDTO.setDto(checkDTO);
            ResponseResult<List<JSONObject>> roleResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
            if (roleResult == null || !roleResult.isSuccess() || CollectionUtils.isEmpty(roleResult.getData())) {
                return false;
            }
            List<JSONObject> roleObjects = JSONObject.parseArray(JSONObject.toJSONString(roleResult.getData()), JSONObject.class);
            String simulationRoleIds = "";
            for (JSONObject roleObject : roleObjects) {
                simulationRoleIds += roleObject.getString("identity_id") + ",";
            }
            if (StringUtils.isNotEmpty(simulationRoleIds)) {
                simulationRoleIds = simulationRoleIds.substring(0, simulationRoleIds.length() - 1);
            }
            checkSql = checkSql.replace("#{simulationUserId}", simulationRoleIds);
        }
        if (checkSql.contains(";")) {
            //查询项目下的实施产品
            List<ProjProductDeliverRecord> deliverRecords = productDeliverRecordMapper.selectList(new QueryWrapper<ProjProductDeliverRecord>().
                    eq("project_info_id", dto.getProjectInfoId()));
            List<Long> deliverProductIds = deliverRecords.stream().map(ProjProductDeliverRecord::getProductDeliverId).collect(Collectors.toList());
            //检测脚本中包含分号，表示需要根据项目下的实施产品动态调整检测脚本
            String[] productSql = checkSql.split(";");
            StringBuilder productSqlSb = new StringBuilder("");
            for (String sql : productSql) {
                //每个产品的脚本用“产品ID|检测脚本”的格式存储
                String[] sqlAry = sql.split("\\|");
                if (deliverProductIds.contains(Long.valueOf(sqlAry[0]))) {
                    if (ObjectUtil.isNotEmpty(productSqlSb)) {
                        productSqlSb.append(" union all ");
                    }
                    productSqlSb.append(sqlAry[1]);
                }
            }
            checkSql = productSqlSb.toString();
        }
        //替换检测脚本中的查询时间和用户ID参数
        checkSql = checkSql.replace("#{startTime}", dto.getStartTime())
                .replace("#{endTime}", dto.getEndTime())
                .replace("#{simulationUserId}", projSimulationDetail.getSimulationUserId().toString());
        checkDTO.setSqlStr(AesUtil.encrypt(checkSql));
        sqlCheckApiDTO.setDto(checkDTO);
        ResponseResult<List<JSONObject>> checkResult = new ResponseResult<>();
        if ("chis".equals(schema)) {
            checkResult = dataPreparationApi.executeChisDb(sqlCheckApiDTO);
        } else {
            checkResult = dataPreparationApi.executeChisappDb(sqlCheckApiDTO);
        }
        if (checkResult == null || !checkResult.isSuccess() || CollectionUtils.isEmpty(checkResult.getData())) {
            return false;
        } else {
            List<JSONObject> jsonObjects = JSONObject.parseArray(JSONObject.toJSONString(checkResult.getData()), JSONObject.class);
            //检测脚本返回的数据每行的num字段都为1时表示检测通过
            for (JSONObject json : jsonObjects) {
                if (json.getInteger("num") == 0) {
                    return false;
                }
            }
            return true;
        }
    }
}
