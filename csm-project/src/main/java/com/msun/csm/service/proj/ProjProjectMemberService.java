package com.msun.csm.service.proj;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.model.dto.AddProjectMemberDTO;
import com.msun.csm.model.dto.ImspProjectUserDTO;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.dto.ProjectMemberInsertToParamDTO;
import com.msun.csm.model.imsp.UpdateProjectUserRelationReq;
import com.msun.csm.model.param.SelectProjectMemberParam;
import com.msun.csm.model.req.project.ProjectMemberReq;
import com.msun.csm.model.vo.ProjProjectMemberVO;
import com.msun.csm.model.vo.ProjectMemberAndUserParamVO;
import com.msun.csm.model.vo.UpdateMemberToParamVO;
import com.msun.csm.model.vo.user.SysUserVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface ProjProjectMemberService {

    int deleteByPrimaryKey(Long projectMemberInfoId);

    int insert(ProjProjectMember record);

    int insertOrUpdate(ProjProjectMember record);

    int insertOrUpdateSelective(ProjProjectMember record);

    int insertSelective(ProjProjectMember record);

    ProjProjectMember selectByPrimaryKey(Long projectMemberInfoId);

    int updateByPrimaryKeySelective(ProjProjectMember record);

    int updateByPrimaryKey(ProjProjectMember record);

    int updateBatch(List<ProjProjectMember> list);

    int updateBatchSelective(List<ProjProjectMember> list);

    int batchInsert(List<ProjProjectMember> list);

    List<ProjProjectMemberVO> getProjMemberListByProjectInfoId(String projectInfoId);

    /**
     * 查询项目下人员信息
     *
     * @return
     */
    Result<ProjectMemberAndUserParamVO> selectProjectMember(SelectProjectMemberParam dto);

    /**
     * 添加项目组成员
     *
     * @param dto
     * @return
     */
    Result insertMember(AddProjectMemberDTO dto);

    /**
     * 修改项目组成员
     *
     * @param dto
     * @return
     */
    Result updateMember(ProjProjectMemberDTO dto);

    /**
     * 删除项目组成员
     *
     * @param dto
     * @return
     */
    Result deleteMember(ProjProjectMemberDTO dto);

    /**
     * 修改项目组成员时参数列表
     *
     * @param dto
     * @return
     */
    Result<UpdateMemberToParamVO> updateMemberToParam(SelectProjectMemberParam dto);

    /**
     * 项目成员添加时参数列表
     *
     * @param dto
     * @return
     */
    Result<PageInfo<SysUserVO>> projectMemberInsertToParam(@RequestBody ProjectMemberInsertToParamDTO dto);

    /**
     * 向老系统添加项目成员
     *
     * @param dtoList
     * @return
     */
    Result saveImspProjectUser(List<ImspProjectUserDTO> dtoList);

    /**
     * 保存项目成员关系
     *
     * @param req
     * @return
     */
    Result updateProjectUserRelation(UpdateProjectUserRelationReq req);

    /**
     * 项目成员下拉列表
     *
     * @param dto
     * @return
     */
    Result<List<BaseIdNameResp>> selectProjectMemberDropDownList(ProjProjectMemberDTO dto);

    /**
     * 查询是否可修改为项目经理
     *
     * @param dto
     * @return
     */
    Result<Boolean> selectProjectMemberFlag(ProjectMemberReq dto);

    /**
     * 校验当前登陆人是否为项目经理
     *
     * @param projectInfoId 项目ID
     * @return true-是项目经理；false-不是项目经理;
     */
    boolean isBackendLeader(Long projectInfoId);

    /**
     * 根据项目ID和服务团队类型获取对应的服务经理
     *
     * @param projectInfoId       项目ID
     * @param backendTeamTypeEnum 服务团队类型
     * @return 对应服务团队的服务经理
     */
    SysUser getBackendLeader(Long projectInfoId, BackendTeamTypeEnum backendTeamTypeEnum);
}
