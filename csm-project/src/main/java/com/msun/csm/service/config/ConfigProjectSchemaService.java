package com.msun.csm.service.config;

import lombok.Data;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/8/1 9:37
 */
public interface ConfigProjectSchemaService {

    @Data
    class GenerateProjectPlanBaseArgs {
        /**
         * 项目模式配置方式类型编码：工单类型的编码、产品分类的编码、业务场景的编码
         */
        private String configTypeCode;
        /**
         * 实施类型：-1.通用；1.老换新；2.新上线
         */
        private Integer upgradationType = -1;
        /**
         * 项目首期标识：-1.通用；0.否；1.是
         */
        private Integer hisFlag = -1;
        /**
         * 电销是否可用：-1.通用；0.否；1.是
         */
        private Integer telesalesFlag = -1;
        /**
         * 单体是否可用：-1.通用；0.否；1.是
         */
        private Integer monomerFlag = -1;
        /**
         * 区域是否可用：-1.通用；0.否；1.是
         */
        private Integer regionFlag = -1;
    }

    /**
     * 通过业务场景生成项目计划配置
     * @param args
     * @return
     */
    Object generateProjectPlanByBizType(GenerateProjectPlanBaseArgs args);

    /**
     * 通过产品分类生成项目计划配置
     * @param args
     * @return
     */
    Object generateProjectPlanByProductType(GenerateProjectPlanBaseArgs args);

    /**
     * 通过工单类型生成项目计划配置
     * @param args
     * @return
     */
    Object generateProjectPlanByDeliveryOrderType(GenerateProjectPlanBaseArgs args);
}
