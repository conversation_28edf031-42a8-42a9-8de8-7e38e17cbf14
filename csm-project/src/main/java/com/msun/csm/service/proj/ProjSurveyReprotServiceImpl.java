package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFPictureData;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.csm.CsmApi;
import com.msun.core.component.implementation.api.csm.dto.CsmReportDTO;
import com.msun.core.component.implementation.api.csm.vo.PrintReportDataVO;
import com.msun.core.component.implementation.api.imsp.SystemSettingWorkflowApi;
import com.msun.core.component.implementation.api.imsp.dto.PrinterQueryParam;
import com.msun.core.component.implementation.api.imsp.vo.PrinterDTO;
import com.msun.core.component.implementation.api.report.ReportApi;
import com.msun.core.component.implementation.api.report.entity.dto.ReportHistoryFileDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.ExamineStatusEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ReviewTypeEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.projform.ReportDetailStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseHospitalNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.ProjectMemberVO;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.dao.entity.proj.projreport.ProjOpenprintReportInfo;
import com.msun.csm.dao.entity.proj.projreport.ProjPrinterConfigInfo;
import com.msun.csm.dao.entity.proj.projreport.ProjProjectFormTerminal;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.entity.report.DictDeliverproVsPrintreportpro;
import com.msun.csm.dao.mapper.comm.ProjBusinessExamineLogMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.formlibrary.ProjProjectFormTerminalMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOpenprintReportInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.ProjReportExamineLogMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.report.DictDeliverproVsPrintreportproMapper;
import com.msun.csm.dao.mapper.report.ProjPrinterConfigInfoMapper;
import com.msun.csm.model.csm.PrintInfoMainCsmDTO;
import com.msun.csm.model.csm.PrinterCsmDTO;
import com.msun.csm.model.dto.ProjHospitalInfoPageDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projreport.PrintReportVerificationPassedParam;
import com.msun.csm.model.req.projreport.ProjSurveyReportDeleteReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportDetailReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportExamineReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintParmerReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReviewExamineReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportTagReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateListReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainImportReq;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.resp.RejectReasonAndCount;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormPageResp;
import com.msun.csm.model.resp.projreport.ProjProjectFormTerminalResp;
import com.msun.csm.model.resp.projreport.ProjProjectSendMsgDataResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportMenuResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportPrintNodeCodeResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReprotDetailPageResp;
import com.msun.csm.model.statis.ProjCustomReq;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.dict.DictBusinessStatusService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;
import com.msun.csm.util.CustomImageModifyHandler;
import com.msun.csm.util.DateUtil;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.HttpUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SignatureReportUtils;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;
import com.msun.csm.util.statis.WpsImg;
import com.msun.csm.util.statis.WpsImgUtil;
import com.obs.services.model.PutObjectResult;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */
@Slf4j
@Service
public class ProjSurveyReprotServiceImpl extends ServiceImpl<ProjSurveyReportMapper, ProjSurveyReport> implements ProjSurveyReportService {

    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjReportExamineLogMapper projReportExamineLogMapper;

    @Resource
    private ReportApi reportApi;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private ProjProductBacklogMapper productBacklogMapper;

    @Resource
    private DictProductVsModulesMapper productVsModulesMapper;

    @Lazy
    @Resource
    private ProjProductTaskService projProductTaskService;

    @Resource
    @Lazy
    private ProjProjectInfoService projProjectInfoService;

    @Lazy
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Value("${requestUrl}")
    private String requestUrl;
    @Lazy
    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;
    @Resource
    private UserHelper userHelper;

    @Resource
    private CsmApi csmApi;

    @Lazy
    @Resource
    private ProjApplyOrderHospitalService projApplyOrderHospitalService;

    @Resource
    private ProjProjectFormTerminalMapper projProjectFormTerminalMapper;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    private ProjBusinessExamineLogService projBusinessExamineLogService;

    @Lazy
    @Resource
    private BaseQueryService baseQueryService;


    @Lazy
    @Resource
    private ProjProductBacklogService productBacklogService;

    @Value("${project.obs.prePath}")
    private String prePath;

    @Lazy
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Lazy
    @Resource
    private ProjBusinessExamineLogMapper projBusinessExamineLogMapper;

    @Lazy
    @Resource
    private SendMessageService sendMessageService;

    @Lazy
    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;

    @Value("${thisProjectUrl}")
    private String tableUrlValue;

    @Lazy
    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Lazy
    @Resource
    private ProjOpenprintReportInfoMapper projOpenprintReportInfoMapper;

    @Lazy
    @Resource
    private SystemSettingWorkflowApi systemSettingWorkflowApi;

    @Lazy
    @Resource
    private ProjPrinterConfigInfoMapper projPrinterConfigInfoMapper;

    @Lazy
    @Resource
    private DictDeliverproVsPrintreportproMapper dictDeliverproVsPrintreportproMapper;

    @Lazy
    @Resource
    private ProjProjectConfigService projectConfigService;

    @Value("${spring.profiles.active}")
    private String activeProfiles;
    @Autowired
    private DictBusinessStatusService dictBusinessStatusService;

    @Resource
    private SendBusinessMessageService sendBusinessMessageService;

    /**
     * 创建下拉框
     *
     * @param sheet
     * @param dept
     * @param firstCol
     * @param lastCol
     * @param flag
     */
    private static void extracted(XSSFSheet sheet, String[] dept, int firstCol, int lastCol, boolean flag) {
        DataValidationHelper validationHelper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(dept);
        // 假设最多1000行
        CellRangeAddressList addressList = new CellRangeAddressList(1, 300, firstCol, lastCol);
        DataValidation validation = validationHelper.createValidation(constraint, addressList);
        // 必须重下拉选择
        if (flag) {
            validation.setShowErrorBox(true);
        }
        sheet.addValidationData(validation);
    }

    /**
     * 获取打印报表的请求头,
     *
     * @param userIsLong
     * @return
     */
    public static HttpHeaders getHeadersByUserId(Long userIsLong) {
        HttpHeaders headers = new HttpHeaders();
        String userId = "3899";
        if (userIsLong != null && userIsLong > 0) {
            userId = String.valueOf(userIsLong);
        }
        String timestamp = String.valueOf(System.currentTimeMillis());
        String datazy = String.format("userId=%s&timestamp=%s", userId, timestamp);
        String sign = SignatureReportUtils.generateSignatureUtils(datazy);
        headers.add("userId", userId);
        headers.add("timestamp", timestamp);
        headers.add("signature", sign);
        headers.add("Content-Type", "application/json;charset=utf-8");
        return headers;
    }

    /**
     * 分页查询报表信息
     *
     * @param projSurveyReportReq
     * @return
     */
    @Override
    public Result<ProjSurveyReprotFormPageResp<ProjSurveyReportResp>> selectSurveyReportByPage(ProjSurveyReportReq projSurveyReportReq) {
        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        List<ProjSurveyReprotFormCount> projSurveyReprotFormPageResp = projSurveyReportMapper.selectSurveyReportCount(projSurveyReportReq);
        // 列表查询
        List<ProjSurveyReportResp> projSurveyReportResps = PageHelperUtil.queryPage(projSurveyReportReq.getPageNum(), projSurveyReportReq.getPageSize(), page -> projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq));
        projSurveyReportResps.forEach(projSurveyReportResp -> {
            ProjectMemberVO projectMemberVO = projProjectMemberMapper.getProjectMemberByUserId(projSurveyReportResp.getProjectInfoId(), currentSysUserId);
            projSurveyReportResp.setCanVerification(projectMemberVO != null && Integer.valueOf(1).equals(projectMemberVO.getRoleType()));
            RejectReasonAndCount verifyRejection = projBusinessExamineLogService.selectReasonAndCountById(projSurveyReportResp.getSurveyReportId(), ExamineStatusEnum.VERIFY_REJECTION);
            projSurveyReportResp.setRejectCount(verifyRejection.getRejectCount());
            projSurveyReportResp.setRejectReason(verifyRejection.getRejectReason());

            RejectReasonAndCount surveyReject = projBusinessExamineLogService.selectReasonAndCountById(projSurveyReportResp.getSurveyReportId(), ExamineStatusEnum.BACKEND_REJECTION);
            projSurveyReportResp.setSurveyRejectCount(surveyReject.getRejectCount());
            projSurveyReportResp.setSurveyRejectReason(surveyReject.getRejectReason());
        });
        ProjSurveyReprotFormPageResp<ProjSurveyReportResp> returnPage = new ProjSurveyReprotFormPageResp<>(projSurveyReportResps);
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyReportReq.getProjectInfoId());
        // 查询该项目是否需要审核人，提交审核
        if (projectInfo != null && projectInfo.getProjectInfoId() != null) {
            ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 11);
            returnPage.setIsNeedAuditorFlag(limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0);
        }
        if (projSurveyReportReq.getPageSource() != null && ("print_report_design".equals(projSurveyReportReq.getPageSource()) || "print_report_audit".equals(projSurveyReportReq.getPageSource()))) {
            returnPage.setIsNeedAuditorFlag(true);
        }
        // 展示设计制作，验收后不展示此按钮
        Boolean isDesignMakeFlag = true;
        if (projectInfo != null && projectInfo.getAcceptTime() != null) {
            isDesignMakeFlag = false;
        }
        if (projSurveyReportResps != null && projSurveyReportResps.size() > 0) {
            Set<Long> projectInfoIds = projSurveyReportResps.stream().map(ProjSurveyReportResp::getProjectInfoId).collect(Collectors.toSet());
            List<ProjProjectInfo> projList = projProjectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().in("project_info_id", projectInfoIds));
            Map<Long, ProjProjectInfo> projectInfoMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(projList)) {
                projectInfoMap = projList.stream().collect(Collectors.toMap(ProjProjectInfo::getProjectInfoId, Function.identity()));
            }
            for (ProjSurveyReportResp projSurveyReportResp : projSurveyReportResps) {
                ProjProjectInfo projectInfoss = projectInfoMap.get(projSurveyReportResp.getProjectInfoId());
                projSurveyReportResp.setIsLeaderOrProjectManagerFlag(userHelper != null && userHelper.getCurrentUser() != null && userHelper.getCurrentUser().getSysUserId() != null && projectInfoss != null && projectInfoss.getProjectInfoId() != null);
                if (projSurveyReportReq.getPageSource() != null && ("print_report_design".equals(projSurveyReportReq.getPageSource()) || "print_report_audit".equals(projSurveyReportReq.getPageSource()))) {
                    isDesignMakeFlag = true;
                    projSurveyReportResp.setIsLeaderOrProjectManagerFlag(true);
                }
                // 获取文件信息
                String[] surveyImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getSurveyImgs())) {
                    surveyImgs = projSurveyReportResp.getSurveyImgs().split(",");
                }
                String[] finishImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getFinishImgs())) {
                    finishImgs = projSurveyReportResp.getFinishImgs().split(",");
                }
                List<ProjProjectFile> surveyImgsFileList = new ArrayList<>();
                if (surveyImgs != null && surveyImgs.length > 0) {
                    List<Long> surveyImgsList = new ArrayList<>();
                    for (String surveyImg : surveyImgs) {
                        try {
                            surveyImgsList.add(Long.valueOf(surveyImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (!surveyImgsList.isEmpty()) {
                        surveyImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", surveyImgsList));
                        if (CollectionUtil.isNotEmpty(surveyImgsFileList)) {
                            surveyImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        surveyImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(surveyImgsFileList);
                    }
                }
                List<ProjProjectFile> finishImgsFileList = new ArrayList<>();
                if (finishImgs != null && finishImgs.length > 0) {
                    List<Long> finishImgsList = new ArrayList<>();
                    for (String finishImg : finishImgs) {
                        try {
                            finishImgsList.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (!finishImgsList.isEmpty()) {
                        finishImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", finishImgsList));
                        if (CollectionUtil.isNotEmpty(finishImgsFileList)) {
                            finishImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        finishImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(finishImgsFileList);
                    }
                }
                projSurveyReportResp.setSurveyImgsList(surveyImgsFileList);
                projSurveyReportResp.setFinishImgsList(finishImgsFileList);
                if (projSurveyReportResp.getFinishStatus() != null && projSurveyReportResp.getFinishStatus() == 7) {
                    isDesignMakeFlag = false;
                }
                projSurveyReportResp.setIsDesignMakeFlag(isDesignMakeFlag);
                List<Integer> integers = Arrays.asList(4, 5, 6);
                if (integers.contains(projSurveyReportResp.getFinishStatus()) && !returnPage.getIsNeedAuditorFlag()) {
                    projSurveyReportResp.setFinishStatus(0);
                }
            }
        }
        if (projSurveyReprotFormPageResp != null && projSurveyReprotFormPageResp.size() > 0) {
            returnPage.setTotalCount(projSurveyReprotFormPageResp.get(0).getTotalCount());
            returnPage.setPreLaunchCompletionCount(projSurveyReprotFormPageResp.get(0).getPreLaunchCompletionCount());

            List<Integer> unStart = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode(), 1);
            List<Integer> underway = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode(), 2);
            int unStartCount = this.projSurveyReportMapper.getFinishedReportCount(projSurveyReportReq.getProjectInfoId(), null, null, unStart, 1);
            log.info("未开始数量={}", unStartCount);
            int underwayCount = this.projSurveyReportMapper.getFinishedReportCount(projSurveyReportReq.getProjectInfoId(), null, null, underway, 1);
            log.info("进行中数量={}", underwayCount);

            returnPage.setIncompleteCount(unStartCount + underwayCount);
            returnPage.setRejectedCount(projSurveyReprotFormPageResp.get(0).getRejectedCount());
        }
        return Result.success(returnPage);
    }

    public static int compareTo(String fileType1, String fileType2) {
        // 定义文件类型的优先级
        Map<String, Integer> priorityMap = new HashMap<>();
        priorityMap.put("image", 1);
        priorityMap.put("pdf", 2);
        priorityMap.put("word", 3);
        return Integer.compare(priorityMap.get(fileType1), priorityMap.get(fileType2));
    }

    /**
     * 审核报表
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateExamineReportStatus(ProjSurveyReportExamineReq projSurveyReportExamineReq) {
        ProjSurveyReport projSurveyReport = this.getById(projSurveyReportExamineReq.getSurveyReportId());
        projSurveyReport.setExamineOpinion(projSurveyReportExamineReq.getExamineOpinion());
        projSurveyReport.setFinishStatus(projSurveyReportExamineReq.getFinishStatus());
        this.updateById(projSurveyReport);

        projBusinessExamineLogService.saveOperationLog("printreport", 22, projSurveyReportExamineReq.getExamineOpinion(), projSurveyReportExamineReq.getSurveyReportId());

        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(projSurveyReport.getProjectInfoId());
        param.setHospitalInfoId(projSurveyReport.getHospitalInfoId());
        param.setUserId(projSurveyReport.getMakeUserId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        return Result.success();
    }

    /**
     * 更新报表
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result updateReport(ProjSurveyReportUpdateReq projSurveyReportExamineReq) {
        Boolean isAddFlag = false;
        ProjSurveyReport projSurveyReport = new ProjSurveyReport();
        BeanUtils.copyProperties(projSurveyReportExamineReq, projSurveyReport);
        if (projSurveyReportExamineReq.getSurveyReportId() != null) {
            projSurveyReport = this.getById(projSurveyReportExamineReq.getSurveyReportId());
        } else {
            List<ProjSurveyReport> repeatList = this.list(new QueryWrapper<ProjSurveyReport>().eq("print_data_code", projSurveyReportExamineReq.getPrintDataCode()).eq("project_info_id", projSurveyReportExamineReq.getProjectInfoId()));
            if (repeatList != null && repeatList.size() > 0) {
                return Result.fail("已存[" + projSurveyReportExamineReq.getPrintDataCode() + "]节点信息，请勿重复新增");
            }
            projSurveyReport.setSurveyReportId(SnowFlakeUtil.getId());
            isAddFlag = true;
        }
        projSurveyReport.setReportPaperSize(projSurveyReportExamineReq.getReportPaperSize());
        projSurveyReport.setReportName(projSurveyReportExamineReq.getReportName());
        projSurveyReport.setSurveyImgs(projSurveyReportExamineReq.getSurveyImgs());
        projSurveyReport.setFinishImgs(projSurveyReportExamineReq.getFinishImgs());
        projSurveyReport.setOnlineEssential(projSurveyReportExamineReq.getOnlineEssential());
        String surveyImgs = "";
        if ((projSurveyReport.getCustomerInfoId() == null || projSurveyReport.getProjectInfoId() == null) && projSurveyReport.getHospitalInfoId() != null) {
            List<ProjProjectInfo> ppiList = projProjectInfoMapper.selectProjectListByHospitalId(projSurveyReport.getHospitalInfoId());
            if (ppiList != null && ppiList.size() > 0) {
                List<ProjProjectInfo> hisPpiList = ppiList.stream().filter(ppi -> ppi.getHisFlag() == 1).collect(Collectors.toList());
                if (hisPpiList != null && hisPpiList.size() > 0) {
                    projSurveyReport.setProjectInfoId(hisPpiList.get(0).getProjectInfoId());
                    projSurveyReport.setCustomerInfoId(hisPpiList.get(0).getCustomInfoId());
                } else {
                    projSurveyReport.setProjectInfoId(ppiList.get(0).getProjectInfoId());
                    projSurveyReport.setCustomerInfoId(ppiList.get(0).getCustomInfoId());
                }
            }
        }
        if (projSurveyReportExamineReq.getSurveyImgsList() != null && projSurveyReportExamineReq.getSurveyImgsList().size() > 0) {
            for (ProjProjectFile projProjectFile : projSurveyReportExamineReq.getSurveyImgsList()) {
                if (projProjectFile.getProjectFileId() != null) {
                    surveyImgs += projProjectFile.getProjectFileId() + ",";
                    try {
                        ProjProjectFile file = projProjectFileMapper.selectById(projProjectFile.getProjectFileId());
                        file.setProjectInfoId(projSurveyReport.getProjectInfoId());
                        projProjectFileMapper.updateByPrimaryKey(file);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
            }
            if (surveyImgs.length() > 0) {
                surveyImgs = surveyImgs.substring(0, surveyImgs.length() - 1);
            }
        }
        projSurveyReport.setSurveyImgs(surveyImgs);
        String finishImgs = "";
        if (projSurveyReportExamineReq.getFinishImgsList() != null && projSurveyReportExamineReq.getFinishImgsList().size() > 0) {
            for (ProjProjectFile projProjectFile : projSurveyReportExamineReq.getFinishImgsList()) {
                if (projProjectFile.getProjectFileId() != null) {
                    try {
                        ProjProjectFile file = projProjectFileMapper.selectById(projProjectFile.getProjectFileId());
                        file.setProjectInfoId(projSurveyReport.getProjectInfoId());
                        projProjectFileMapper.updateByPrimaryKey(file);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                    finishImgs += projProjectFile.getProjectFileId() + ",";
                }
            }
            if (finishImgs.length() > 0) {
                finishImgs = finishImgs.substring(0, finishImgs.length() - 1);
            }
        }
        projSurveyReport.setFinishImgs(finishImgs);
        if (projSurveyReportExamineReq.getDefaultFlag() != null) {
            projSurveyReport.setDefaultFlag(projSurveyReportExamineReq.getDefaultFlag());
        }
        // 新增修改报表
        if (isAddFlag) {
            if (projSurveyReport.getDefaultFlag() == null) {
                // 默认自定义调研
                projSurveyReport.setDefaultFlag(0);
            }
            if (projSurveyReportExamineReq.getSaveAndApplyAdjudicationFlag() != null && projSurveyReportExamineReq.getSaveAndApplyAdjudicationFlag() == 1) {
                projSurveyReport.setFinishStatus(4);
            }
            // 新增是判断是已部署， 部署后将报表大打印状态设置为1
            try {
                Boolean isPrintStatus = projSurveyReportMapper.getPrintStatusByProjectInfoId(projSurveyReport.getProjectInfoId());
                if (isPrintStatus) {
                    projSurveyReport.setPrintStatus(1);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            this.save(projSurveyReport);
            // 保存日志
            this.saveLogs(projSurveyReport);
        } else {
            projSurveyReport.setReportTaskId("");
            if (projSurveyReport.getFinishStatus() == NumberEnum.NO_7.num()) {
                projSurveyReport.setFinishStatus(0);
            }
            if (projSurveyReportExamineReq.getSaveAndApplyAdjudicationFlag() != null && projSurveyReportExamineReq.getSaveAndApplyAdjudicationFlag() == 1) {
                projSurveyReport.setFinishStatus(4);
            }
            this.updateById(projSurveyReport);
            // 保存日志
            this.saveLogs(projSurveyReport);
        }
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(projSurveyReport.getProjectInfoId());
        param.setHospitalInfoId(projSurveyReport.getHospitalInfoId());
        param.setUserId(projSurveyReport.getMakeUserId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        return Result.success();
    }

    /**
     * 保存日志
     *
     * @param updateProjSurveyReport
     */
    private void saveLogs(ProjSurveyReport updateProjSurveyReport) {
        try {
            log.info("记录操作日志，updateProjSurveyReport={}", JSON.toJSONString(updateProjSurveyReport));

            // 保存报表
            if (Integer.valueOf(0).equals(updateProjSurveyReport.getFinishStatus())) {
                projBusinessExamineLogService.saveOperationLog("printreport", 41, "保存打印报表", updateProjSurveyReport.getSurveyReportId());
            }

            // 提交后端审核
            if (Integer.valueOf(4).equals(updateProjSurveyReport.getFinishStatus())) {
                projBusinessExamineLogService.saveOperationLog("printreport", 0, "提交审核", updateProjSurveyReport.getSurveyReportId());
            }
            // 后端审核通过
            if (Integer.valueOf(5).equals(updateProjSurveyReport.getFinishStatus())) {
                projBusinessExamineLogService.saveOperationLog("printreport", 1, "审核通过", updateProjSurveyReport.getSurveyReportId());
            }
        } catch (Exception e) {
            log.error("记录报错操作日志，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 提交审核
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateReportFinishStatus(ProjSurveyReportUpdateReq projSurveyReportExamineReq) {
        ProjSurveyReport projSurveyReportBack = this.getById(projSurveyReportExamineReq.getSurveyReportId());
        ProjSurveyReport projSurveyReport = this.getById(projSurveyReportExamineReq.getSurveyReportId());
        projSurveyReport.setFinishStatus(1);
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyReport.getProjectInfoId());
        // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
        // 查询该项目是否需要审核人，提交审核
        if (projectInfo != null && projectInfo.getProjectInfoId() != null) {
            ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 11);
            if (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0) {
                if (!Integer.valueOf(5).equals(projSurveyReportBack.getFinishStatus()) && !Integer.valueOf(1).equals(projSurveyReportBack.getFinishStatus()) && !Integer.valueOf(2).equals(projSurveyReportBack.getFinishStatus())) {
                    log.error(StrUtil.format("当前报表尚未通过后端人员审核，请等待审核通过后再提交完成。报表信息: {}", JSON.toJSONString(projSurveyReportBack)));
                    return Result.fail(422, "当前报表尚未通过后端人员审核，请等待审核通过后再提交完成。");
                }
            }
        }
        List<ProjSurveyReport> listFinish = this.list(new QueryWrapper<ProjSurveyReport>().eq("yy_product_id", projSurveyReport.getYyProductId()).eq("yy_module_id", projSurveyReport.getYyModuleId()).notIn("finish_status", new ArrayList<Integer>(Arrays.asList(1, 3))));
        if (!(listFinish != null && listFinish.size() > 0)) {
            // 不存在未完成的，更新产品报表的状态
            // 处理产品明细节点状态
            ProjProductBacklog projProductBacklog = new ProjProductBacklog();
            projProductBacklog.setReportDataStatus(1);
            QueryWrapper<ProjProductBacklog> wrapper = new QueryWrapper<>();
            wrapper.eq("project_info_id", projSurveyReport.getProjectInfoId());
            // 判断传入的产品是否不是模块。当为模块时 赋值模块id
            DictProductVsModules modules = productVsModulesMapper.selectOne(new QueryWrapper<DictProductVsModules>().eq("yy_module_id", projSurveyReport.getYyProductId()));
            if (ObjectUtil.isNotEmpty(modules)) {
                wrapper.eq("yy_product_module_id", projSurveyReport.getYyProductId());
            } else {
                wrapper.eq("yy_product_id", projSurveyReport.getYyProductId());
            }
            wrapper.eq("hospital_info_id", projSurveyReport.getHospitalInfoId());
            productBacklogMapper.update(projProductBacklog, wrapper);
        }
        projSurveyReport.setCommitFinishTime(new Timestamp(System.currentTimeMillis()));
        projSurveyReport.setExamineOpinion(" ");
        this.updateById(projSurveyReport);
        projBusinessExamineLogService.saveOperationLog("printreport", 20, "打印报表制作完成，提交验证", projSurveyReportExamineReq.getSurveyReportId());
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(projSurveyReport.getProjectInfoId());
        param.setHospitalInfoId(projSurveyReport.getHospitalInfoId());
        param.setUserId(projSurveyReport.getMakeUserId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        return Result.success();
    }

    /**
     * 设计制作
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result reprotMake(ProjSurveyReportExamineReq projSurveyReportExamineReq) {
        try {
            ProjSurveyReport projSurveyReport = this.getById(projSurveyReportExamineReq.getSurveyReportId());
            if (!(projSurveyReport.getPrintDataCode() != null && !"".equals(projSurveyReport.getPrintDataCode()))) {
                return Result.fail("未找到报表对应的节点，请联系管理员");
            }
            // 上线后跳转云健康
            Boolean isPrintStatus = false;
            try {
                isPrintStatus = projSurveyReportMapper.getPrintStatusByProjectInfoId(projSurveyReport.getProjectInfoId());
            } catch (Exception e) {
                log.error("获取项目是否已上线，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }

            ProjOpenprintReportInfo info = projOpenprintReportInfoMapper.selectById(projSurveyReport.getHospitalInfoId());
            if (!isPrintStatus && info != null && info.getHospitalInfoId() != null && (projSurveyReport.getPrintStatus() == null || (projSurveyReport.getPrintStatus() != null && projSurveyReport.getPrintStatus() == 0))) {
                return getPrintReportUrl(projSurveyReport);
            }
            ProjProductTaskParam dto = new ProjProductTaskParam();
            dto.setHospitalInfoId(projSurveyReport.getHospitalInfoId());
            dto.setCloudProductCode("baseprint");
            dto.setTaskPageUrl("#/report-edit-eare/report-edit-eare");
            Map<String, Object> map = new HashMap<>();
            map.put("reportId", projSurveyReport.getSurveyReportId());
            map.put("reportFileTag", projSurveyReport.getReportFileTag());
            map.put("printDataCode", projSurveyReport.getPrintDataCode());
            dto.setData(JSON.toJSONString(map));
            Object str = projProductTaskService.hisLogin(dto);
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projSurveyReport.getProjectInfoId());
            param.setHospitalInfoId(projSurveyReport.getHospitalInfoId());
            param.setUserId(projSurveyReport.getMakeUserId());
            param.setCode(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
            projTodoTaskService.updateProjectTodoTaskStatus(param);
            if (str != null && !"".equals(str.toString())) {
                return Result.success(str);
            } else {
                throw new CustomException("未检测到医院信息，请联系管理员");
            }
        } catch (Exception e) {
            throw new CustomException("未检测到医院信息，请联系管理员:" + e.getMessage(), e);
        }
    }

    /**
     * 批量保存
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result batchReprotSave(List<ProjSurveyReportUpdateReq> projSurveyReportExamineReq) {
        if (CollectionUtil.isEmpty(projSurveyReportExamineReq)) {
            return Result.success("无需保存");
        }
        // 调研负责人
        Long surveyUserId = null;
        List<ProjSurveyPlan> surveyPlan = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().eq("project_info_id", projSurveyReportExamineReq.get(NumberEnum.NO_0.num()).getProjectInfoId()).eq("is_deleted", 0).eq("yy_product_id", projSurveyReportExamineReq.get(NumberEnum.NO_0.num()).getYyProductId()));
        if (surveyPlan != null && !surveyPlan.isEmpty()) {
            surveyUserId = surveyPlan.get(NumberEnum.NO_0.num()).getSurveyUserId();
        }
        // 循环传过来的数据
        for (ProjSurveyReportUpdateReq req : projSurveyReportExamineReq) {
            // 更新or新增
            List<ProjSurveyReport> list = this.list(
                    new QueryWrapper<ProjSurveyReport>()
                            .eq("is_deleted", 0)
                            .eq("report_name", req.getReportName())
                            .eq(ObjectUtil.isNotEmpty(req.getProjectInfoId()), "project_info_id", req.getProjectInfoId())
                            .eq(ObjectUtil.isNotEmpty(req.getYyProductId()), "yy_product_id", req.getYyProductId())
                            .eq(ObjectUtil.isNotEmpty(req.getYyModuleId()), "yy_module_id", req.getYyProductId())
            );
            if (list != null && !list.isEmpty()) {
                ProjSurveyReport updateProjSurveyReport = list.get(0);
                // 尺寸信息不变以及图片信息不变时。不进行修改
                if (this.isSkipCreate(updateProjSurveyReport, req)) {
                    continue;
                }
                log.info("批量保存报表，当前报表信息，printDataCode={}", req.getPrintDataCode());
                List<Long> listLong = addSurveyImgData(req);
                String surveyImgs = "";
                if (listLong != null) {
                    for (Long id : listLong) {
                        surveyImgs += id + ",";
                    }
                    if (!"".equals(surveyImgs)) {
                        surveyImgs = surveyImgs.substring(0, surveyImgs.length() - 1);
                        updateProjSurveyReport.setSurveyImgs(surveyImgs);
                    }
                }
                updateProjSurveyReport.setReportName(req.getReportName());
                updateProjSurveyReport.setPrintDataCode(req.getPrintDataCode());
                updateProjSurveyReport.setReportFileTag(req.getReportFileTag());
                updateProjSurveyReport.setSurveyUserId(surveyUserId);
                updateProjSurveyReport.setReportPaperSize(req.getReportPaperSize());
                if (req.getDefaultFlag() != null) {
                    updateProjSurveyReport.setDefaultFlag(req.getDefaultFlag());
                }
                updateProjSurveyReport.setReportTaskId("");
                saveLogs(updateProjSurveyReport);
                log.info("更新了报表：项目：{} -- 客户：{} -- 医院：{} -- 报表名称：{} -- 状态：{}", updateProjSurveyReport.getProjectInfoId(), updateProjSurveyReport.getCustomerInfoId(), updateProjSurveyReport.getHospitalInfoId(), updateProjSurveyReport.getReportName(), updateProjSurveyReport.getFinishStatus());
                this.updateById(updateProjSurveyReport);
            } else {
                log.info("批量保存报表，当前报表信息，新建，printDataCode={}", req.getPrintDataCode());
                ProjSurveyReport projSurveyReport = new ProjSurveyReport();
                BeanUtil.copyProperties(req, projSurveyReport);
                // 根据参数查询数据
                // 保存图片
                List<Long> listLong = addSurveyImgData(req);
                String surveyImgs = "";
                if (listLong != null) {
                    for (Long id : listLong) {
                        surveyImgs += id + ",";
                    }
                    if (!"".equals(surveyImgs)) {
                        surveyImgs = surveyImgs.substring(0, surveyImgs.length() - 1);
                        projSurveyReport.setSurveyImgs(surveyImgs);
                    }
                }
                if (req.getDefaultFlag() != null) {
                    projSurveyReport.setDefaultFlag(projSurveyReport.getDefaultFlag());
                }
                projSurveyReport.setSurveyReportId(SnowFlakeUtil.getId());
                projSurveyReport.setSurveyUserId(surveyUserId);
                log.info("新建了报表：项目：{} -- 客户：{} -- 医院：{} -- 报表名称：{} -- 状态：{}", projSurveyReport.getProjectInfoId(), projSurveyReport.getCustomerInfoId(), projSurveyReport.getHospitalInfoId(), projSurveyReport.getReportName(), projSurveyReport.getFinishStatus());

                saveLogs(projSurveyReport);
                //  部署后应跳转云健康
                try {
                    Boolean isPrintStatus = projSurveyReportMapper.getPrintStatusByProjectInfoId(projSurveyReport.getProjectInfoId());
                    if (isPrintStatus) {
                        projSurveyReport.setPrintStatus(1);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
                this.save(projSurveyReport);
            }
        }
        return Result.success();
    }

    // 是否跳过重新生成，当报表图片和尺寸都不发生变化时，跳过重新生成
    private boolean isSkipCreate(ProjSurveyReport dataSource, ProjSurveyReportUpdateReq req) {
        // 尺寸信息为空，正常进行生成
        log.info("判断报表尺寸，req={}", JSON.toJSONString(req));
        if (org.apache.commons.lang3.StringUtils.isBlank(req.getReportPaperSize())) {
            log.warn("表表【{}】尺寸信息为空，正常进行生成", req.getSurveyReportId());
            return false;
        }

        log.info("判断尺寸信息是否相等");
        // 尺寸信息不相等，正常进行生成
        if (!req.getReportPaperSize().equals(dataSource.getReportPaperSize())) {
            log.warn("表表【{}】尺寸信息不相等，正常进行生成", req.getSurveyReportId());
            return false;
        }
        log.info("判断没有调研图片");
        // 没有调研图片，正常进行生成
        if (org.apache.commons.lang3.StringUtils.isBlank(req.getSurveyImgs())) {
            log.warn("表表【{}】没有调研图片，正常进行生成", req.getSurveyReportId());
            return false;
        }
        log.info("判断源报表没有调研图片");
        if (org.apache.commons.lang3.StringUtils.isBlank(dataSource.getSurveyImgs())) {
            log.warn("表表【{}】源报表【{}】没有调研图片，正常进行生成", req.getSurveyReportId(), dataSource.getSurveyReportId());
            return false;
        }
        log.info("判断报表调研图片是否相等");
        List<String> surveyImagePathList;
        if (org.apache.commons.lang3.StringUtils.isBlank(req.getSurveyImgs())) {
            surveyImagePathList = new ArrayList<>();
        } else {
            surveyImagePathList = Arrays.stream(req.getSurveyImgs().split(",")).collect(Collectors.toList());
        }
        log.info("判断报表调研图片是否相等，surveyImagePathList={}", JSON.toJSONString(surveyImagePathList));

        List<Long> surveyImageFileId;
        if (org.apache.commons.lang3.StringUtils.isBlank(dataSource.getSurveyImgs())) {
            surveyImageFileId = new ArrayList<>();
        } else {
            surveyImageFileId = Arrays.stream(dataSource.getSurveyImgs().split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        log.info("判断报表调研图片是否相等，surveyImageFileId={}", JSON.toJSONString(surveyImageFileId));

        List<String> dataSourceSurveyImage;
        if (CollectionUtils.isEmpty(surveyImageFileId)) {
            dataSourceSurveyImage = new ArrayList<>();
        } else {
            List<ProjProjectFile> projProjectFiles = projProjectFileMapper.selectList(
                    new QueryWrapper<ProjProjectFile>()
                            .eq("is_deleted", 0)
                            .in("project_file_id", surveyImageFileId)
            );
            dataSourceSurveyImage = projProjectFiles.stream().map(ProjProjectFile::getFilePath).collect(Collectors.toList());
        }

        // 排序两个列表并逐个比较元素
        Collections.sort(surveyImagePathList);
        Collections.sort(dataSourceSurveyImage);
        if (surveyImagePathList.size() != dataSourceSurveyImage.size()) {
            log.warn("表表【{}】元素不同，正常进行生成", req.getSurveyReportId());
            return false;
        }
        return surveyImagePathList.equals(dataSourceSurveyImage);
    }

    /**
     * 根据报表打印节点查询报表标识
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result getReportFileTagByPrintCode(ProjSurveyReportTagReq projSurveyReportExamineReq) {
        if (!(projSurveyReportExamineReq.getReportCodes() != null && projSurveyReportExamineReq.getReportCodes().size() > 0)) {
            return Result.fail("未检测到报表打印节点");
        }
        if (projSurveyReportExamineReq.getHospitalInfoId() == null) {
            return Result.fail("未检测到医院信息请传入");
        }
        try {
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(projSurveyReportExamineReq.getHospitalInfoId());
            if (projHospitalInfo != null) {
                Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
                log.info("设定医院信息:{}", domainMap);
                domainHolder.refresh(domainMap);
                ReportHistoryFileDTO reportHistoryFileDTO = new ReportHistoryFileDTO();
                reportHistoryFileDTO.setHospitalId(projHospitalInfo.getCloudHospitalId());
                reportHistoryFileDTO.setHisOrgId(projHospitalInfo.getOrgId());
                reportHistoryFileDTO.setHisHospitalId(projHospitalInfo.getCloudHospitalId());
                reportHistoryFileDTO.setPrintNodeCodes(projSurveyReportExamineReq.getReportCodes());
                ResponseResult result = reportApi.findReportFileDataByCode(reportHistoryFileDTO);
                if (result.getSuccess()) {
                    log.info("云健康环境返回数据：{}", JSON.toJSONString(result.getData()));
                    return Result.success(result.getData());
                }
            }
        } catch (Exception e) {
            throw new CustomException("未检测到医院信息，请检查是否部署，如果始终未解决，请联系管理员");
        }
        return null;
    }

    /**
     * 查询项目下报表内容模块数据
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result<List<ProjSurveyReportMenuResp>> getReportMenuByProjectId(ProjSurveyReportTagReq projSurveyReportExamineReq) {
        List<ProjSurveyReportMenuResp> list = new ArrayList<>();
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyReportExamineReq.getProjectInfoId());
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projSurveyReportExamineReq.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_REPORT.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        Boolean isNewFlow = milestoneInfoList != null && milestoneInfoList.size() > 0 && milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent());
        // 单体
        if (projectInfo.getProjectType() != null && NumberEnum.NO_1.num().equals(projectInfo.getProjectType())) {
//            list.add(new ProjSurveyReportMenuResp("AreaReportInit", "报表初始化"));
            if (isNewFlow) {
                list.add(new ProjSurveyReportMenuResp("ReportSurvey", "报表任务分配及制作"));
            } else {
                list.add(new ProjSurveyReportMenuResp("ReportTaskAllocAndMake", "报表任务分配及制作"));
            }
        } else {
            list.add(new ProjSurveyReportMenuResp("AreaReportInit", "区域报表初始化"));
            if (isNewFlow) {
                list.add(new ProjSurveyReportMenuResp("ReportSurvey", "报表任务分配及制作"));
            } else {
                list.add(new ProjSurveyReportMenuResp("ReportTaskAllocAndMake", "报表任务分配及制作"));
            }
        }
        list.add(new ProjSurveyReportMenuResp("ReportBatchCopy", "报表批量复制"));
        return Result.success(list);
    }

    /**
     * 初始化路径
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result<String> getPacsStartPath(ProjSurveyReportTagReq projSurveyReportExamineReq) {
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(projSurveyReportExamineReq.getHospitalInfoId());
        StringBuffer sb = new StringBuffer(requestUrl);
        sb.append("/surveyConfigController/autoGenerateConfigView?projectId=").append(projSurveyReportExamineReq.getOldProjectId());
        if (hospitalInfo != null) {
            sb.append("&hisOrgId=").append(hospitalInfo.getOrgId());
        } else {
            return Result.fail("【云资源划分及部署】节点完成后，在进行数据初始化");
        }
        sb.append("&type=1&itemId=8&transferType=1");
        return Result.success(sb.toString());
    }

    /**
     * 批量修改
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateReportResponsibilities(ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        if (projSurveyFormReq.getIds() != null && projSurveyFormReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyReport report = this.getById(surveyFormId);
                if (projSurveyFormReq.getMakeUserId() != null && !"0".equals(projSurveyFormReq.getMakeUserId())) {
                    log.info("修改报表制作人：{}", projSurveyFormReq.getMakeUserId());
                    report.setMakeUserId(projSurveyFormReq.getMakeUserId());
                }
                if (projSurveyFormReq.getOnlineEssential() != null) {
                    report.setOnlineEssential(projSurveyFormReq.getOnlineEssential());
                }
                this.updateById(report);
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(report.getProjectInfoId());
                param.setHospitalInfoId(report.getHospitalInfoId());
                param.setUserId(projSurveyFormReq.getMakeUserId());
                param.setCode(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
                projTodoTaskService.projectTodoTaskInit(param);
            }
        } else {
            return Result.fail("请选择要更新的报表");
        }
        return Result.success("更新成功");
    }

    /**
     * 刷新报表终端明细
     *
     * @param projSurveyReportReq
     * @return
     */
    @Override
    public Result syncReportTerminalData(ProjSurveyReportReq projSurveyReportReq) {
        if (projSurveyReportReq.getProjectInfoId() == null) {
            return Result.fail("项目id不能为空");
        }
        List<ProjHospitalInfo> hospitalInfoList = new ArrayList<>();
        if (projSurveyReportReq.getHospitalInfoId() != null) {
            ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq("hospital_info_id", projSurveyReportReq.getHospitalInfoId()));
            hospitalInfoList.add(projHospitalInfo);
        } else {
            hospitalInfoList = projApplyOrderHospitalService.findHospitalInfo(projSurveyReportReq.getProjectInfoId());
            hospitalInfoList = hospitalInfoList.stream().filter(hospitalInfo -> hospitalInfo.getCloudHospitalId() != null).collect(Collectors.collectingAndThen(Collectors.toMap(ProjHospitalInfo::getCloudHospitalId, hospitalInfo -> hospitalInfo, (existing, replacement) -> existing), map -> new ArrayList<>(map.values())));
        }
        List<ProjProjectFormTerminal> projProjectFormTerminalList = null;
        if (hospitalInfoList != null && hospitalInfoList.size() > 0) {
            List<Long> hospitalIds = hospitalInfoList.stream().map(ProjHospitalInfo::getCloudHospitalId).collect(Collectors.toList());
            projProjectFormTerminalList = projProjectFormTerminalMapper.selectList(new QueryWrapper<ProjProjectFormTerminal>().eq("is_deleted", NumberEnum.NO_0.num()).in("cloud_hospital_id", hospitalIds));
            for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                try {
                    Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
                    log.info("设定医院信息:{}", domainMap);
                    domainHolder.refresh(domainMap);
                    domainMap.clear();
                    CsmReportDTO csmReportDTO = new CsmReportDTO();
                    csmReportDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
                    csmReportDTO.setHisOrgId(hospitalInfo.getOrgId());
                    ResponseResult<List<PrintReportDataVO>> result = csmApi.getPrintReportStatusByParamer(csmReportDTO);
                    if (result.isSuccess()) {
                        try {
                            insertIntojProjectFormTerminal(result.getData(), projProjectFormTerminalList, hospitalInfo.getCloudHospitalId());
                        } catch (Exception e) {
                            log.error("拉取明细异常：{}", e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.error("获取报表终端明细异常：{}", e.getMessage());
                }
            }
        }
        return Result.success("成功");
    }

    /**
     * @param projSurveyReportReq
     * @return
     */
    @Override
    public Result<ProjSurveyReprotDetailPageResp<ProjProjectFormTerminalResp>> selectSurveyReportDetailByPage(ProjSurveyReportDetailReq projSurveyReportReq) {
        List<ProjProjectFormTerminalResp> projSurveyReportResps = PageHelperUtil.queryPage(projSurveyReportReq.getPageNum(), projSurveyReportReq.getPageSize(), page -> projProjectFormTerminalMapper.selectListByParamer(projSurveyReportReq));
        ProjSurveyReprotDetailPageResp<ProjProjectFormTerminalResp> returnPage = new ProjSurveyReprotDetailPageResp<ProjProjectFormTerminalResp>(projSurveyReportResps);
        return Result.success(returnPage);
    }

    /**
     * @param projSurveyReportReq
     * @return
     */
    @Override
    public Result<List<BaseCodeNameResp>> selectSatusDictList(ProjSurveyReportDetailReq projSurveyReportReq) {
        List<BaseCodeNameResp> list = new ArrayList<>();
        for (ReportDetailStatusEnum e : ReportDetailStatusEnum.values()) {
            list.add(new BaseCodeNameResp(e.getCode(), e.getName()));
        }
        return Result.success(list);
    }

    /**
     * 下载
     *
     * @param response
     * @param projectInfoId
     */
    @Override
    public void download(HttpServletResponse response, Long projectInfoId) throws IOException {
        // 创建一个新的工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Template");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"报表名称", "打印节点", "医院", "产品", "来源", "样式图片一", "样式图片二", "样式图片三", "样式图片四", "上线必备", "纸张尺寸"};
        CellStyle headerCellStyle = createHeaderCellStyle(workbook);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerCellStyle);
        }
        // 设置列为必填（标红）
        CellStyle requiredCellStyle = createRequiredCellStyle(workbook);
        for (int i = 0; i < headers.length; i++) {
            if (i != 6 && i != 7 && i != 8 && i != 9) {
                Cell cell = headerRow.getCell(i);
                cell.setCellStyle(requiredCellStyle);
            }
        }
        // 自动调整列宽
        int fixedWidth = 20 * 256;
        for (int i = 0; i < headers.length; i++) {
            sheet.setColumnWidth(i, fixedWidth);
        }
        List<BaseHospitalNameResp> hospitalsData = baseQueryService.queryHospital("", false, null, projectInfoId);
        List<BaseIdNameResp> listProductData = new ArrayList<>();
        try {
            listProductData = productBacklogService.selectYyProductAndModule(projectInfoId).getData();
        } catch (Exception e) {
            e.printStackTrace();
        }
        String[] hospitals = hospitalsData.stream().map(BaseIdNameResp::getName).toArray(String[]::new);
        String[] products = listProductData.stream().map(BaseIdNameResp::getName).toArray(String[]::new);
        String[] sourceList = new String[]{"系统默认", "自定义"};
        String[] onlineFlag = new String[]{"是", "否"};
        // 医院
        extracted(sheet, hospitals, 2, 2, false);
        // 产品
        extracted(sheet, products, 3, 3, false);
        // 来源
        extracted(sheet, sourceList, 4, 4, true);
        // 上线必备
        extracted(sheet, onlineFlag, 9, 9, true);
        // 设置响应头
        ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(projectInfoId);
        String filename = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "统计报表调研导入模板.xlsx";
        if (projProjectInfo != null) {
            filename = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + projProjectInfo.getProjectName() + "统计报表调研导入模板.xlsx";
        }
        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + encodedFilename);
        // 将工作簿写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 导入
     *
     * @param file
     * @param request
     * @param param
     * @return
     * @throws IOException
     */
    @Override
    public Result<String> importExcel(MultipartFile file, HttpServletRequest request, ProjStatisticalReportMainImportReq param) throws IOException {
        ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(param.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("is_deleted", 0).eq("custom_info_id", param.getCustomInfoId()).eq("hos_project_type", projProjectInfo.getProjectType()).orderByAsc("cloud_hospital_id"));
        List<BaseIdNameResp> listProductData = new ArrayList<>();
        try {
            listProductData = productBacklogService.selectYyProductAndModule(param.getProjectInfoId()).getData();
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<ProjSurveyReportUpdateReq> addList = new ArrayList<>();
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = (XSSFSheet) workbook.getSheetAt(0);
        if (sheet == null || !sheet.iterator().hasNext()) {
            return Result.fail("Excel 文件为空或无数据");
        }
        log.info("Excel 文件数据:");
        int rows = sheet.getPhysicalNumberOfRows();
        List<String> dispStrList = null;
        StringBuffer sbReturn = new StringBuffer();
        for (int i = 1; i < rows; i++) {
            dispStrList = new ArrayList<>();
            ProjSurveyReportUpdateReq req = new ProjSurveyReportUpdateReq();
            req.setCustomerInfoId(param.getCustomInfoId());
            req.setProjectInfoId(param.getProjectInfoId());
            Row row = sheet.getRow(i);
            StringBuffer sb = new StringBuffer();
            if (row.getPhysicalNumberOfCells() < 6) {
                sb.append("第" + i + "行存在必填项数据(标题为红色)未填写，重新填写后进行上传;");
                sbReturn.append(sb);
                continue;
            }
            for (int l = 0; l < 11; l++) {
                Cell cell = row.getCell(l);
                if (cell == null) {
                    continue;
                }
                log.error("第" + i + "行第" + l + "列数据为：" + printCellData(cell));
                if (l == 0) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行报表名称不能为空;");
                    } else {
                        req.setReportName(String.valueOf(printCellData(cell)));
                    }
                }
                if (l == 1) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行打印节点不能为空;");
                    } else {
                        req.setPrintDataCode(String.valueOf(printCellData(cell)));
                    }
                }
                if (l == 2) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行使用医院不能为空;");
                    } else {
                        for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
                            if (hospitalInfo.getHospitalName().equals(printCellData(cell))) {
                                req.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                                break;
                            }
                        }
                    }
                }
                if (l == 3) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行产品不能为空;");
                    } else {
                        for (BaseIdNameResp baseIdNameResp : listProductData) {
                            if (baseIdNameResp.getName().equals(printCellData(cell))) {
                                req.setYyProductId(baseIdNameResp.getId());
                                break;
                            }
                        }
                    }
                }
                if (l == 4) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行来源不能为空;");
                    } else {
                        if ("系统默认".equals(printCellData(cell))) {
                            req.setDefaultFlag(1);
                        } else {
                            req.setDefaultFlag(0);
                        }
                    }
                }
                if (l == 5) {
                    if (ObjectUtil.isEmpty(printCellData(cell))) {
                        sb.append("第" + i + "行,样式图片一必选传入;");
                    }
                }
                if (l == 9) {
                    if (ObjectUtil.isNotEmpty(printCellData(cell))) {
                        if (!"否".equals(row.getCell(l).getStringCellValue())) {
                            req.setOnlineEssential(0);
                        } else if ("是".equals(row.getCell(l).getStringCellValue())) {
                            req.setOnlineEssential(1);
                        } else {
                            req.setOnlineEssential(1);
                        }
                    }
                }
                if (l == 10) {
                    if (ObjectUtil.isNotEmpty(printCellData(cell))) {
                        if (!"".equals(printCellData(cell))) {
                            req.setReportPaperSize(printCellData(cell).toString());
                        }
                    }
                }
                if (cell.getCellType().equals(CellType.FORMULA)) {
                    // 获取公式结果
                    String formulaResult = cell.getStringCellValue();
                    dispStrList.add(formulaResult);
                }
            }
            if (ObjectUtil.isNotEmpty(sb)) {
                sbReturn.append(sb);
                continue;
            }
            // 组装图片数据
            List<ProjProjectFile> list = new ArrayList<>();
            Map<String, WpsImg> getWpsImgs = WpsImgUtil.getWpsImgs(dispStrList, file);
            dispStrList.stream().forEach(v -> {
                if (StrUtil.isNotBlank(v)) {
                    WpsImg vs = getWpsImgs.get(v);
                    if (Objects.nonNull(vs)) {
                        XSSFPictureData pa = vs.getPictureData();
                        if (Objects.nonNull(pa)) {
                            ProjProjectFile projFileReq = new ProjProjectFile();
                            Long id = saveImageToFile(pa.getData(), vs.getImgName());
                            projFileReq.setProjectFileId(id);
                            list.add(projFileReq);
                        }
                    }
                    log.info("图片保存完成！" + getWpsImgs.get(v));
                }
            });
            req.setSurveyImgsList(list);
            addList.add(req);
        }
        // 批量保存
        int sucess = 0;
        if (addList != null && addList.size() > 0) {
            for (int i = 0; i < addList.size(); i++) {
                ProjSurveyReportUpdateReq req = addList.get(i);
                Result r = this.batchReprotSave(new ArrayList<>(Collections.singletonList(req)));
                if (r.isSuccess()) {
                    sucess++;
                    log.info(req.getReportName() + "数据保存成功");
                } else {
                    sbReturn.append(req.getReportName() + r.getMsg() + ";");
                }
            }
            if (ObjectUtil.isNotEmpty(sbReturn)) {
                return Result.success("保存成功" + sucess + "条数据; 以下存在问题的数据:" + sbReturn);
            }
        } else {
            if (ObjectUtil.isNotEmpty(sbReturn)) {
                return Result.fail("保存成功0条数据; 以下存在问题的数据:" + sbReturn);
            }
        }
        projTodoTaskService.todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
        return Result.success();
    }

    /**
     * 批量提交审核人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateReportReviewer(ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        if (projSurveyFormReq.getIds() != null && projSurveyFormReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyReport report = this.getById(surveyFormId);
                if (projSurveyFormReq.getReviewerUserId() != null && !"0".equals(projSurveyFormReq.getReviewerUserId())) {
                    log.info("修改报表审核人：{}", projSurveyFormReq.getReviewerUserId());
                    report.setReviewerUserId(projSurveyFormReq.getReviewerUserId());
                    sendBusinessMessageService.updateEarlyWarningAndPenaltyPerson(ReviewTypeEnum.PRINT_REPORT_SUBMIT_AUDIT, surveyFormId, projSurveyFormReq.getReviewerUserId());
                }
                this.updateById(report);
            }
        } else {
            return Result.fail("请选择要更新的报表");
        }
        return Result.success("更新成功");
    }

    /**
     * 批量审核
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateReportExamine(ProjSurveyReportReviewExamineReq projSurveyFormReq) {
        int count = 0;
        Date now = new Date();
        if (projSurveyFormReq.getIds() != null && !projSurveyFormReq.getIds().isEmpty()) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyReport report = this.getById(surveyFormId);
                ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(report.getProjectInfoId());
                // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
                // 查询该项目是否需要审核人，提交审核
                ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 11);
                // 开启表单审核的且提交审核的才需要审核
                //TODO 这里有问题，需要张迪确认
                List<ProjBusinessExamineLog> reportList = projBusinessExamineLogMapper.selectListByParamer(Collections.singletonList(report.getSurveyReportId()), Arrays.asList(0, 1, 2));
                if ((limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0) && (reportList != null && reportList.size() > 0 && reportList.get(0).getExamineStatus() == 0)) {
                    if (projSurveyFormReq.getExamineStatus() != null && projSurveyFormReq.getExamineStatus() == 1) {
                        report.setFinishStatus(5);
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(report.getSurveyReportId(), ReviewTypeEnum.PRINT_REPORT_SUBMIT_AUDIT.getBusinessTable());
                    }
                    if (projSurveyFormReq.getExamineStatus() != null && projSurveyFormReq.getExamineStatus() != 1) {
                        projSurveyFormReq.setExamineStatus(2);
                        report.setFinishStatus(6);
                        // 调研审核的预警单和罚单
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(report.getSurveyReportId(), ReviewTypeEnum.PRINT_REPORT_SUBMIT_AUDIT.getBusinessTable());
                        sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
                                ReviewTypeEnum.PRINT_REPORT_RESUBMIT_FOR_REVIEW,
                                report.getProjectInfoId(),
                                report.getSurveyReportId(),
                                sendBusinessMessageService.getPenaltyPerson(report.getSurveyUserId(), report.getProjectInfoId()),
                                "打印报表调研提交审核超期",
                                now
                        );
                    }
                    report.setReviewerUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
                    this.updateById(report);
                    //  审核  1是通过  2驳回
                    log.info("/projSurveyReport/updateReportExamine接口，examineStatus={}，examineOpinion={}", projSurveyFormReq.getExamineStatus(), projSurveyFormReq.getExamineOpinion());
                    projBusinessExamineLogService.saveOperationLog("printreport", projSurveyFormReq.getExamineStatus(), projSurveyFormReq.getExamineOpinion(), surveyFormId);
                    projTodoTaskService.todoTaskTotalCountSync(report.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode());
                } else {
                    count++;
                }
            }
        } else {
            return Result.fail("请选择要审核的报表");
        }
        if (count > 0) {
            return Result.success(null, "审核成功" + (projSurveyFormReq.getIds().size() - count) + "条数据; 无需审核的数据有" + count + "条数据");
        }
        return Result.success("更新成功");
    }

    @Override
    public Result updateReportExamineStatus(ProjSurveyReportReviewExamineReq projSurveyFormReq) {
        // 无需提交审核的数据
        int count = 0;
        // 状态为调研信息待补充的数据
        int surveyStatusNotCount = 0;
        Date now = new Date();
        if (projSurveyFormReq.getIds() != null && projSurveyFormReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyReport report = this.getById(surveyFormId);
                ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(report.getProjectInfoId());
                // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
                // 查询该项目是否需要审核人，提交审核
                ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 11);
                // 开启表单审核的且提交审核的才需要审核
                List<ProjBusinessExamineLog> reportList = projBusinessExamineLogMapper.selectListByParamer(Collections.singletonList(report.getSurveyReportId()), Arrays.asList(0, 1));
                if ((limit == null || (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() == 0)) && (reportList != null && reportList.size() > 0)) {
                    count++;
                } else {
                    //  调研信息待补充
                    if (report.getFinishStatus() == 7) {
                        count++;
                        surveyStatusNotCount++;
                    } else {
                        report.setFinishStatus(4);
                        this.updateById(report);
                        //  提交审核  0是待审核
                        projBusinessExamineLogService.saveOperationLog("printreport", 0, null, surveyFormId);
                        // 调研审核的预警单和罚单
                        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(report.getSurveyReportId(), ReviewTypeEnum.PRINT_REPORT_RESUBMIT_FOR_REVIEW.getBusinessTable());

                        sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
                                ReviewTypeEnum.PRINT_REPORT_SUBMIT_AUDIT,
                                report.getProjectInfoId(),
                                report.getSurveyReportId(),
                                sendBusinessMessageService.getBackendPenaltyPerson(report.getReviewerUserId(), BackendTeamTypeEnum.BUSINESS_TEAM, report.getProjectInfoId()),
                                "打印报表调研审核超期",
                                now
                        );
                    }
                }
            }
        } else {
            return Result.fail("请选择要提交的报表");
        }
        if (count > 0) {
            String msg = "提交成功" + (projSurveyFormReq.getIds().size() - count) + "条数据; 无需提交/提交失败的数据有" + count + "条数据;";
            if (surveyStatusNotCount > 0) {
                msg += "其中调研信息待补充有" + surveyStatusNotCount + "条数据";
            }
            Result result = new Result();
            result.setSuccess(true);
            result.setCode(606);
            result.setMsg(msg);
            return result;
        } else {
            return Result.success(null, "提交成功" + projSurveyFormReq.getIds().size() + "条数据;");
        }
    }

    /**
     * 发送消息给审核人或者负责人
     */
    @Override
    public void getSendMsg() {
        log.error("执行发送消息逻辑======");
        List<ProjProjectSendMsgDataResp> list = projSurveyReportMapper.selectSendMsgData();
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                sendMsgPrivate(list.get(i), i);
            }
        }
    }

    private void sendMsgPrivate(ProjProjectSendMsgDataResp resp, int i) {
        ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(resp.getProjectInfoId());
        log.error("发送消息给审核人或者负责人=====zoule=" + i);
        List<ProjProjectMember> member = projProjectMemberMapper.selectList(new QueryWrapper<ProjProjectMember>().eq("project_info_id", resp.getProjectInfoId()).in("project_member_role_id", Arrays.asList(2, 3)).eq("is_deleted", 0));
        String allCount = "";
        String operCount = "";
        String errCount = "";
        String time = "";
        int dateHour = 24;
        // 日期设定： 查询项目创建时间， 在7天之内则 +24小时， 超过7天则按7天到达时间推送
        // 查询业务线创建时间
        ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projProjectInfo.getProjectInfoId(), 11);
        if (limit != null && limit.getLimitRatio() != null) {
            dateHour = limit.getLimitRatio();
        }
        if (projProjectInfo.getCreateTime() != null) {
            Date date = projProjectInfo.getCreateTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, 7);
            // 判断是否超过7天
            if (calendar.getTime().getTime() > System.currentTimeMillis()) {
                time = DateUtils.format(calendar.getTime(), DateUtils.DATE_FORMAT_16);
            } else {
                Calendar calendar2 = Calendar.getInstance();
                calendar2.setTime(date);
                calendar2.add(Calendar.HOUR_OF_DAY, dateHour);
                time = DateUtils.format(calendar2.getTime(), DateUtils.DATE_FORMAT_16);
            }
        }
        if ("report".equals(resp.getDataType())) {
            List<Long> userIds = new ArrayList<>();
            if (resp.getUserIds() != null && resp.getUserIds().length() > 0) {
                String[] split = resp.getUserIds().split(",");
                for (String s : split) {
                    userIds.add(Long.valueOf(s));
                }
            }
            if (member != null && member.size() > 0) {
                for (ProjProjectMember projProjectMember : member) {
                    if (!userIds.contains(projProjectMember.getProjectMemberInfoId()) && projProjectMember.getProjectMemberRoleId() == 3) {
                        userIds.add(projProjectMember.getProjectMemberInfoId());
                    }
                }
            }
            if (resp.getCommitCount() > 0) {
                allCount = resp.getCommitCount() + "";
                operCount = (resp.getOperCount() + resp.getErrCount()) + "";
                sendMsgPrivateNext(resp, DictMessageTypeEnum.REPORT_COMPLETE_KOWN.getId(), userIds, allCount, operCount, errCount, time);
            }
            List<Long> userSendIds = new ArrayList<>();
            if (resp.getCommitUserIds() != null && resp.getCommitUserIds().length() > 0) {
                String[] split = resp.getCommitUserIds().split(",");
                for (String s : split) {
                    userSendIds.add(Long.valueOf(s));
                }
            }
            if (member != null && member.size() > 0) {
                for (ProjProjectMember projProjectMember : member) {
                    if (!userIds.contains(projProjectMember.getProjectMemberInfoId()) && projProjectMember.getProjectMemberRoleId() == 2) {
                        userIds.add(projProjectMember.getProjectMemberInfoId());
                    }
                }
            }
            allCount = resp.getAllCount() + "";
            operCount = resp.getOperCount() + "";
            errCount = resp.getErrCount() + "";
            sendMsgPrivateNext(resp, DictMessageTypeEnum.REPORT_EXE_KOWN.getId(), userSendIds, allCount, operCount, errCount, time);
        } else {
            List<Long> userIds = new ArrayList<>();
            if (resp.getUserIds() != null && resp.getUserIds().length() > 0) {
                String[] split = resp.getUserIds().split(",");
                for (String s : split) {
                    userIds.add(Long.valueOf(s));
                }
            }
            if (member != null && member.size() > 0) {
                for (ProjProjectMember projProjectMember : member) {
                    if (!userIds.contains(projProjectMember.getProjectMemberInfoId()) && projProjectMember.getProjectMemberRoleId() == 3) {
                        userIds.add(projProjectMember.getProjectMemberInfoId());
                    }
                }
            }
            if (resp.getCommitCount() > 0) {
                allCount = resp.getCommitCount() + "";
                operCount = (resp.getOperCount() + resp.getErrCount()) + "";
                sendMsgPrivateNext(resp, DictMessageTypeEnum.FORM_COMPLETE_KOWN.getId(), userIds, allCount, operCount, errCount, time);
            }
            List<Long> userSendIds = new ArrayList<>();
            if (resp.getCommitUserIds() != null && resp.getCommitUserIds().length() > 0) {
                String[] split = resp.getCommitUserIds().split(",");
                for (String s : split) {
                    userSendIds.add(Long.valueOf(s));
                }
            }
            if (member != null && member.size() > 0) {
                for (ProjProjectMember projProjectMember : member) {
                    if (!userIds.contains(projProjectMember.getProjectMemberInfoId()) && projProjectMember.getProjectMemberRoleId() == 2) {
                        userIds.add(projProjectMember.getProjectMemberInfoId());
                    }
                }
            }
            allCount = resp.getAllCount() + "";
            operCount = resp.getOperCount() + "";
            errCount = resp.getErrCount() + "";
            sendMsgPrivateNext(resp, DictMessageTypeEnum.FORM_EXE_KOWN.getId(), userSendIds, allCount, operCount, errCount, time);
        }
    }

    private void sendMsgPrivateNext(ProjProjectSendMsgDataResp resp, Long messageTypeId, List<Long> userIds, String allCount, String operCount, String errCount, String time) {
        log.error("messageTypeId==" + messageTypeId);
        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", resp.getCustomName());
        messageContentParam.put("projectNumber", resp.getProjectNumber());
        if (Integer.valueOf(1).equals(resp.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }
        messageContentParam.put("allCount", allCount);
        messageContentParam.put("operCount", operCount);
        messageContentParam.put("errCount", errCount);
        messageContentParam.put("time", time);
        SendMessageParam messageParam = new SendMessageParam();
        // 消息类型
        messageParam.setMessageTypeId(messageTypeId);
        messageParam.setProjectInfoId(resp.getProjectInfoId());
        messageParam.setMessageContentParam(messageContentParam);
        messageParam.setSysUserIds(userIds);
        sendMessageService.sendMessage2(messageParam);
    }

    /**
     * 将图片字节数据保存到指定的文件路径
     */
    private Long saveImageToFile(byte[] imageBytes, String imageFileName) {
        // 将 byte 数组转换为 InputStream
        InputStream inputStream = new ByteArrayInputStream(imageBytes);
        try {
            String objectKey = prePath + StrUtil.SLASH + "report" + new SimpleDateFormat("yyyyMMddHHmmss").format(System.currentTimeMillis()) + StrUtil.SLASH + imageFileName;
            log.info("图片保存路径：" + objectKey);
            PutObjectResult p = OBSClientUtils.uploadFileStream(inputStream, objectKey, true);
            log.info("图片保存路径：" + p);
            Long id = SnowFlakeUtil.getId();
            UploadFileReq req = new UploadFileReq();
            if (StrUtil.isNotBlank(p.getObjectKey())) {
                //处理从项目工具入口上传附件时，没有projectInfoId和milestoneCode的问题
                if (ObjectUtil.isEmpty(req.getProjectInfoId())) {
                    req.setProjectInfoId(Long.valueOf(DateUtil.getHourMinuteSecondString()));
                }
                req.setMilestoneCode("stage_survey");
                ProjProjectFileExtend projectFile = new ProjProjectFileExtend(req, "", imageFileName, id, p.getObjectKey(), "");
                if (req.getIsPublic()) {
                    projectFile.setFileUrl(URLDecoder.decode(p.getObjectUrl()));
                } else {
                    projectFile.setFileUrl(OBSClientUtils.getTemporaryUrl(objectKey, 3600));
                }
                projProjectFileMapper.insert(projectFile);
            }
            return id;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Object printCellData(Cell cell) {
        // 获取单元格的类型并打印对应的内容
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return cell.getStringCellValue();
        }
    }

    private CellStyle createRequiredCellStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    private CellStyle createHeaderCellStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    /**
     * 保存数据
     *
     * @param listCloudReportData
     * @param projProjectFormTerminalList
     * @param hospitalId
     */
    private void insertIntojProjectFormTerminal(List<PrintReportDataVO> listCloudReportData, List<ProjProjectFormTerminal> projProjectFormTerminalList, Long hospitalId) {
        List<String> ids = new ArrayList<>();
        if (projProjectFormTerminalList != null && projProjectFormTerminalList.size() > 0) {
            for (ProjProjectFormTerminal projProjectFormTerminal : projProjectFormTerminalList) {
                ids.add(projProjectFormTerminal.getId() + projProjectFormTerminal.getCloudHospitalId());
            }
        }
        if (listCloudReportData != null && listCloudReportData.size() > 0) {
            for (PrintReportDataVO printReportDataVO : listCloudReportData) {
                if (!ids.contains(printReportDataVO.getId() + hospitalId)) {
                    ProjProjectFormTerminal terminal = new ProjProjectFormTerminal();
                    BeanUtils.copyProperties(printReportDataVO, terminal);
                    terminal.setFormTerminalId(SnowFlakeUtil.getId());
                    terminal.setFinishStatus(this.finishStatus(printReportDataVO.getStatus()));
                    terminal.setIsDeleted(0);
                    terminal.setCloudHospitalId(hospitalId);
                    projProjectFormTerminalMapper.insert(terminal);
                } else {
                    ProjProjectFormTerminal terminal = projProjectFormTerminalList.stream().filter(ProjProjectFormTerminal -> ProjProjectFormTerminal.getId().equals(printReportDataVO.getId())).findFirst().get();
                    BeanUtils.copyProperties(printReportDataVO, terminal);
                    terminal.setCloudHospitalId(hospitalId);
                    terminal.setFinishStatus(this.finishStatus(printReportDataVO.getStatus()));
                    projProjectFormTerminalMapper.updateById(terminal);
                }
            }
        }
    }

    /**
     * 判定是否完成
     * 当前电脑配置的单据状态：10未开始11协助中12已协助
     * 20已通过21已跳过22已完成
     *
     * @param status
     * @return
     */
    private Integer finishStatus(String status) {
        try {
            if (StringUtils.isNotBlank(status)) {
                int integerStatus = Integer.valueOf(status);
                if (integerStatus >= 10 && integerStatus < 20) {
                    return 0;
                } else if (integerStatus >= 20 && integerStatus < 30) {
                    return 1;
                }
            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }

    /**
     * 新增文件数据
     *
     * @param req
     * @return
     */
    public List<Long> addSurveyImgData(ProjSurveyReportUpdateReq req) {
        List<Long> imgIds = new ArrayList<>();
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getSurveyImgs())) {
                String surveyValue = req.getSurveyImgs();
                String[] imgList = surveyValue.split(",");
                if (imgList != null) {
                    for (String img : imgList) {
                        Long id = SnowFlakeUtil.getId();
                        String fileName = "-";
                        String url = String.valueOf(img);
                        String fileUrl = url;
                        ProjProjectFile file = new ProjProjectFile();
                        file.setProjectInfoId(req.getProjectInfoId());
                        file.setProjectFileId(id);
                        file.setProjectStageCode("stage_survey");
                        file.setMilestoneNodeCode("submit_survey_report");
                        file.setFileName(fileName);
                        file.setFilePath(fileUrl);
                        file.setFileDesc("-");
                        file.setCreaterId(req.getCreaterId());
                        file.setUpdaterId(req.getUpdaterId());
                        projProjectFileMapper.insert(file);
                        imgIds.add(id);
                    }
                }
            }
        } catch (Exception e) {
            log.error("【{}】保存报表图片失败", req.getSurveyReportId(), e);
        }
        return imgIds;
    }

    @Override
    @Transactional
    public Result<Void> deleteReport(ProjSurveyReportDeleteReq projSurveyFormReq) {
        if (CollectionUtils.isEmpty(projSurveyFormReq.getIds())) {
            return Result.fail("没有选中需要删除的打印报表");
        }
        int i = projSurveyReportMapper.deleteSurveyReport(projSurveyFormReq.getIds(), userHelper.getCurrentSysUserIdWithDefaultValue());
        log.info("批量删除打印报表，成功删除{}条数据", i);
        for (Long surveyReportId : projSurveyFormReq.getIds()) {
            ProjSurveyReport surveyReportId1 = projSurveyReportMapper.selectBySurveyReportId(surveyReportId);
            sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(surveyReportId, ReviewTypeEnum.PRINT_REPORT_SUBMIT_AUDIT.getBusinessTable());
            sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(surveyReportId, ReviewTypeEnum.PRINT_REPORT_RESUBMIT_FOR_REVIEW.getBusinessTable());
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(surveyReportId1.getProjectInfoId());
            param.setHospitalInfoId(surveyReportId1.getHospitalInfoId());
            param.setUserId(surveyReportId1.getMakeUserId());
            param.setCode(DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
            projTodoTaskService.updateProjectTodoTaskStatus(param);
        }
        return Result.success(null, "删除成功");
    }

    @Override
    public Result<String> updatePrintReportStatusData(ProjCustomReq dto) {
        log.error("入参：{}", dto);
        try {
            projSurveyReportMapper.updatePrintReportStatusData(dto.getCustomInfoId());
            return Result.success("更新成功");
        } catch (Exception e) {
            log.error("更新失败", e);
        }
        return Result.fail("更新失败");
    }

    /**
     * 创建预上线医院打印报表模板信息
     *
     * @param customerId
     * @return
     */
    @Override
    public String sendStartPrintReportData(Long customerId) {
        ProjCustomInfo customInfo = projCustomInfoMapper.selectByPrimaryKey(customerId);
        String customName = "";
        if (customInfo != null) {
            customName = customInfo.getCustomName();
        }
        String data = "失败";
        String tableUrlValuePrivate = getString();
        String url = tableUrlValuePrivate + "msun-print-app-center/preLaunchHospitalManagement/addPreLaunchHospitalFiles";
        Map<String, Object> param = new HashMap<>();
        param.put("customName", customName);
        param.put("customerId", customerId);
        param.put("templateDescription", "交付平台初始化");
        param.put("templateId", 1);
        HttpHeaders headers = getHeadersByUserId(null);
        try {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return HttpUtil.doPost(url, JSONObject.toJSONString(param), headers);
                } catch (Exception e) {
                    log.error("HTTP请求失败", e);
                    return null;
                }
            });
            // 设置超时时间为2秒
            Map<String, Object> stringObjectMap = future.get(2, TimeUnit.SECONDS);
            log.error("HTTP请求结果：{}", stringObjectMap);
            if (stringObjectMap != null && Boolean.TRUE.equals(stringObjectMap.get("success"))) {
                data = "成功";
            } else {
                data = "失败";
            }
        } catch (TimeoutException e) {
            log.error("请求超时", e);
            data = "失败";
        } catch (InterruptedException | ExecutionException e) {
            log.error("执行异常", e);
            data = "失败";
        }
        // 向固定表记录使用交付的数据
        try {
            projOpenprintReportInfoMapper.insertHospitalInfo(customerId);
        } catch (Exception e) {
            log.error("插入失败", e);
        }
        return data;
    }

    /**
     * 获取打印报表的url
     *
     * @return
     */
    @Nullable
    private String getString() {
        String tableUrlValuePrivate = tableUrlValue;
        if (tableUrlValuePrivate != null) {
            if (tableUrlValuePrivate.contains("-test") || tableUrlValuePrivate.contains("172")) {
                tableUrlValuePrivate = "https://172.16.4.151/";
            } else {
                tableUrlValuePrivate = "http://10.201.0.197:30355/";
            }
        }
        return tableUrlValuePrivate;
    }

    /**
     * 获取打印报表的url
     *
     * @param projSurveyReport
     * @return
     */
    private Result<String> getPrintReportUrl(ProjSurveyReport projSurveyReport) {
        SysUserVO vo = userHelper.getCurrentUser();
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(projSurveyReport.getHospitalInfoId());
        String tableUrlValuePrivate = tableUrlValue;
        if (tableUrlValuePrivate != null) {
            if (tableUrlValuePrivate.contains("-test") || tableUrlValuePrivate.contains("172")) {
                tableUrlValuePrivate = "https://172.16.4.151/";
            } else {
                tableUrlValuePrivate = tableUrlValuePrivate.replace("csm", "");
            }
        }
        String userId = String.valueOf(vo.getSysUserId());
        String timestamp = String.valueOf(System.currentTimeMillis());
        String data = String.format("userId=%s&timestamp=%s", userId, timestamp);
        String sign = SignatureReportUtils.generateSignatureUtils(data);
        String url = tableUrlValuePrivate + "printcenter/#/unifiedEntrance?userId=" + userId + "&userName=" + vo.getUserName() + "&hospitalId=" + hospitalInfo.getCustomInfoId() + "&printNodeCode=" + projSurveyReport.getPrintDataCode();
        url = url + "&timestamp=" + timestamp + "&signature=" + sign + "&transferId=" + projSurveyReport.getReportTaskId();
        return Result.success(url);
    }

    /**
     * 根据产品获取打印节点,用与打印报表调研
     *
     * @param model
     * @return
     */
    @Override
    public Result getAllNode(ProjSurveyReportPrintParmerReq model) {
        // 查询对照表产品名称，无数据则抛出异常
        List<DictDeliverproVsPrintreportpro> listProduct = dictDeliverproVsPrintreportproMapper.selectList(new QueryWrapper<DictDeliverproVsPrintreportpro>().eq("deliver_product_id", model.getProductId()));
        if (listProduct == null || listProduct.isEmpty()) {
            throw new CustomException("产品未对照，请联系交付平台管理员进行对照");
        }
        String productName = listProduct.get(0).getPrintProductName();
        List<ProjSurveyReportPrintNodeCodeResp> list = new ArrayList<>();
        String tableUrlValuePrivate = getString();
        String url = tableUrlValuePrivate + "msun-print-app-center/node/findNodeInfoByProductName";
        List<String> nodeCodeList = new ArrayList<>();
        List<ProjSurveyReport> listSurveyReport = projSurveyReportMapper.selectList(new QueryWrapper<ProjSurveyReport>().eq("project_info_id", model.getProjectInfoId()).eq("is_deleted", 0));
        if (listSurveyReport != null && !listSurveyReport.isEmpty()) {
            for (ProjSurveyReport surveyReport : listSurveyReport) {
                nodeCodeList.add(surveyReport.getPrintDataCode());
            }
        }
        boolean planModel = projectConfigService.isPlanModel(model.getProjectInfoId());
        try {
            // 组装校验参数
            HttpHeaders headers = getHeadersByUserId(null);
            Map param = new HashMap();
            param.put("productName", productName);
            Map map = HttpUtil.doPost(url, JSONObject.toJSONString(param), headers);
            List<Map> data = (List<Map>) map.get("data");
            if (data != null && !data.isEmpty()) {
                for (Map mapModel : data) {
                    ProjSurveyReportPrintNodeCodeResp resp = new ProjSurveyReportPrintNodeCodeResp();
                    resp.setUsePurposeImg(com.msun.csm.util.StringUtils.nvl(mapModel.get("usePurposeImg")));
                    resp.setDefaultImg(com.msun.csm.util.StringUtils.nvl(mapModel.get("systemMenuFile")));
                    resp.setPrintDataCode(com.msun.csm.util.StringUtils.nvl(mapModel.get("printNodeCode")));
                    resp.setReportName(com.msun.csm.util.StringUtils.nvl(mapModel.get("printNodeName")));
                    resp.setSystemName(com.msun.csm.util.StringUtils.nvl(mapModel.get("systemName")));
                    resp.setIsSaveFlag(nodeCodeList.contains(resp.getPrintDataCode()));
                    resp.setIsBeforeAndAfterProjectsFlag(planModel);
                    if (listSurveyReport != null && !listSurveyReport.isEmpty() && resp.getIsSaveFlag()) {
                        for (ProjSurveyReport surveyReport : listSurveyReport) {
                            if (resp.getPrintDataCode().equals(surveyReport.getPrintDataCode())) {
                                resp.setSurveyReportId(surveyReport.getSurveyReportId());
                            }
                        }
                    }
                    if (model.getPrintNodeName() != null && !model.getPrintNodeName().isEmpty() && resp.getReportName().contains(model.getPrintNodeName())) {
                        list.add(resp);
                    } else if (model.getPrintNodeName() == null || model.getPrintNodeName().isEmpty()) {
                        list.add(resp);
                    }
                }
            }
            log.error("HTTP请求结果：{}", map);
        } catch (Exception e) {
            log.error("请求超时", e);
        }
        return Result.success(list);
    }

    /**
     * 保存批量保存
     *
     * @param saveModel
     * @return
     */
    @Override
    public Result saveReprotSaveList(ProjSurveyReportUpdateListReq saveModel) {
        List<ProjSurveyReportPrintNodeCodeResp> projSurveyReportExamineReq = saveModel.getList();
        if (CollectionUtil.isEmpty(projSurveyReportExamineReq)) {
            return Result.success("无需保存");
        }
        // 调研负责人
        Long surveyUserId = null;
        List<ProjSurveyPlan> surveyPlan = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().eq("project_info_id", saveModel.getProjectInfoId()).eq("is_deleted", 0).eq("yy_product_id", saveModel.getYyProductId()));
        if (surveyPlan != null && !surveyPlan.isEmpty()) {
            surveyUserId = surveyPlan.get(NumberEnum.NO_0.num()).getSurveyUserId();
        }
        String reslut = "";
        // 循环传过来的数据
        for (ProjSurveyReportPrintNodeCodeResp req : projSurveyReportExamineReq) {
            ProjSurveyReport projSurveyReport = new ProjSurveyReport();
            projSurveyReport.setProjectInfoId(saveModel.getProjectInfoId());
            projSurveyReport.setHospitalInfoId(saveModel.getHospitalInfoId());
            projSurveyReport.setYyProductId(saveModel.getYyProductId());
            // 默认自定义调研
            projSurveyReport.setDefaultFlag(0);
            // 更新or新增
            List<ProjSurveyReport> list = this.list(new QueryWrapper<ProjSurveyReport>().eq("is_deleted", 0).eq("print_data_code", req.getPrintDataCode()).eq(ObjectUtil.isNotEmpty(saveModel.getProjectInfoId()), "project_info_id", saveModel.getProjectInfoId()).eq(ObjectUtil.isNotEmpty(saveModel.getYyProductId()), "yy_product_id", saveModel.getYyProductId()));
            if (list != null && !list.isEmpty()) {
                reslut += "打印节点" + req.getPrintDataCode() + "已存在，无需选择；";
                log.error("已有数据处理");
            } else {
                BeanUtil.copyProperties(req, projSurveyReport);
                // 状态
                projSurveyReport.setFinishStatus(7);
                projSurveyReport.setSurveyReportId(SnowFlakeUtil.getId());
                projSurveyReport.setSurveyUserId(surveyUserId);
                this.save(projSurveyReport);
            }
        }
        return Result.success(reslut);
    }

    /**
     * 根据id查询报表对象信息
     *
     * @param projSurveyReportReq
     * @return
     */
    @Override
    public ProjSurveyReportResp selectSurveyReportById(ProjSurveyReportReq projSurveyReportReq) {
        ProjSurveyReportResp resp = new ProjSurveyReportResp();
        // 列表查询
        List<ProjSurveyReportResp> projSurveyReportResps = projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq);
        if (projSurveyReportResps != null && !projSurveyReportResps.isEmpty()) {
            // 获取前后端项目
            boolean planModel = projectConfigService.isPlanModel(projSurveyReportResps.get(NumberEnum.NO_0.num()).getProjectInfoId());
            for (ProjSurveyReportResp projSurveyReportResp : projSurveyReportResps) {
                projSurveyReportResp.setIsBeforeAndAfterProjectsFlag(planModel);
                // 获取文件信息
                String[] surveyImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getSurveyImgs())) {
                    surveyImgs = projSurveyReportResp.getSurveyImgs().split(",");
                }
                String[] finishImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getFinishImgs())) {
                    finishImgs = projSurveyReportResp.getFinishImgs().split(",");
                }
                List<ProjProjectFile> surveyImgsFileList = new ArrayList<>();
                if (surveyImgs != null && surveyImgs.length > 0) {
                    List<Long> surveyImgsList = new ArrayList<>();
                    for (String surveyImg : surveyImgs) {
                        try {
                            surveyImgsList.add(Long.valueOf(surveyImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (surveyImgsList != null && surveyImgsList.size() > 0) {
                        surveyImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", surveyImgsList));
                        if (CollectionUtil.isNotEmpty(surveyImgsFileList)) {
                            surveyImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        surveyImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(surveyImgsFileList);
                    }
                }
                List<ProjProjectFile> finishImgsFileList = new ArrayList<>();
                if (finishImgs != null && finishImgs.length > 0) {
                    List<Long> finishImgsList = new ArrayList<>();
                    for (String finishImg : finishImgs) {
                        try {
                            finishImgsList.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (finishImgsList != null && finishImgsList.size() > 0) {
                        finishImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", finishImgsList));
                        if (CollectionUtil.isNotEmpty(finishImgsFileList)) {
                            finishImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        finishImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(finishImgsFileList);
                    }
                }
                projSurveyReportResp.setSurveyImgsList(surveyImgsFileList);
                projSurveyReportResp.setFinishImgsList(finishImgsFileList);
            }
            resp = projSurveyReportResps.get(0);
        }
        return resp;
    }

    @Override
    public String preLaunchHospitalPushOnSite(Long customerId, Long hisHospitalId, Long hisOrgId) {
        log.error("start ==> preLaunchHospitalPushOnSite===>open");
        String data = "失败";
        String tableUrlValuePrivate = getString();
        String url = tableUrlValuePrivate + "msun-print-app-center/preLaunchHospitalManagement/preLaunchHospitalPushOnSite";
        Map<String, Object> param = new HashMap<>();
        param.put("hisHospitalId", hisHospitalId);
        param.put("customerId", customerId);
        param.put("hisOrgId", hisOrgId);
        HttpHeaders headers = getHeadersByUserId(null);
        try {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return HttpUtil.doPost(url, JSONObject.toJSONString(param), headers);
                } catch (Exception e) {
                    log.error("HTTP请求失败xiachen", e);
                    return null;
                }
            });
            // 设置超时时间为2秒
            Map<String, Object> stringObjectMap = future.get(2, TimeUnit.SECONDS);
            log.error("HTTP请求结果preLaunchHospitalPushOnSite：{}", stringObjectMap);
            if (stringObjectMap != null && Boolean.TRUE.equals(stringObjectMap.get("success"))) {
                data = "成功";
            } else {
                data = "失败";
            }
        } catch (TimeoutException e) {
            log.error("请求超时preLaunchHospitalPushOnSite", e);
            data = "失败";
        } catch (InterruptedException | ExecutionException e) {
            log.error("执行异常preLaunchHospitalPushOnSite", e);
            data = "失败";
        }
        return data;
    }

    @Override
    public Result<String> preLaunchHospitalPushOnSiteSend(Long projectInfoId) {
        log.error("start=>preLaunchHospitalPushOnSiteSend==>dayinbaobiaoxiachen");
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectByPrimaryKey(projectInfoId);
        ProjHospitalInfoPageDTO dto = new ProjHospitalInfoPageDTO();
        dto.setCustomInfoId(projectInfo.getCustomInfoId());
        dto.setProjectType(projectInfo.getProjectType());
        List<ProjHospitalInfoVO> list = projHospitalInfoMapper.selectHospitalInfoList(dto);
        if (list != null && list.size() > 0) {
            for (ProjHospitalInfoVO vo : list) {
                Long hospitalId = Long.valueOf(vo.getHospitalInfoId());
                ProjOpenprintReportInfo info = projOpenprintReportInfoMapper.selectById(hospitalId);
                if (info != null && info.getHospitalInfoId() != null) {
                    preLaunchHospitalPushOnSite(projectInfo.getCustomInfoId(), vo.getCloudHospitalId(), vo.getOrgId());
                    break;
                }
            }
        }
        return null;
    }

    /**
     * 查询打印信息
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<PrinterDTO> queryPrintInfo(ProjSurveyReportPrintReq dto) {
        PrinterQueryParam param = new PrinterQueryParam();
        BeanUtils.copyProperties(dto, param);
        // 根据项目id查询医院信息
        SelectHospitalDTO dtoHos = new SelectHospitalDTO();
        dtoHos.setProjectInfoId(dto.getProjectInfoId());
        List<ProjHospitalInfo> info = projHospitalInfoMapper.getHospitalInfoByProjectId(dtoHos);
        if (info != null && !info.isEmpty()) {
            ProjHospitalInfo infoOne = info.get(0);
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(infoOne);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            if (infoOne.getCloudDomain() == null || infoOne.getCloudDomain().isEmpty()) {
                throw new RuntimeException("请在部署云健康后再进行查询");
            }
            param.setHisOrgId(infoOne.getOrgId());
            param.setHospitalId(infoOne.getCloudHospitalId());
        }
        log.error("打印信息查询===>{}", JSONObject.toJSONString(param));
        ResponseResult<PageInfo<PrinterDTO>> loglist = systemSettingWorkflowApi.queryPrintInfo(param);
        if (!loglist.isSuccess()) {
            throw new CustomException("查询系统管理报错，" + loglist.getMessage());
        }
        if (loglist.getData() == null) {
            throw new CustomException("查询系统管理接口未查询到数据");
        }
        log.error("打印信息查询结果===>{}", loglist);
        return loglist.getData();
    }

    /**
     * 查询打印报表信息（从交付表查询）
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<ProjPrinterConfigInfo> queryPrintInfoByJf(ProjSurveyReportPrintReq dto) {
        List<ProjPrinterConfigInfo> list = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> projPrinterConfigInfoMapper.queryPrintInfoByJf(dto));
        PageInfo<ProjPrinterConfigInfo> loglist = new PageInfo<>(list);
        return loglist;
    }

    /**
     * 保存打印报表信息
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseResult<String> savePrintInfo(PrintInfoMainCsmDTO dto) {
        List<PrinterCsmDTO> list = dto.getList();
        if (list != null && !list.isEmpty()) {
            PrinterCsmDTO paramer = list.get(NumberEnum.NO_0.num());
            if (paramer == null || paramer.getHospitalId() == null) {
                return ResponseResult.success("请选择医院");
            }
            // 根据医院id查询交付平台已有的数据集：
            List<ProjPrinterConfigInfo> haveList = projPrinterConfigInfoMapper.selectNotrepeatList(paramer);
            List<String> haveListNameList = new ArrayList<>();
            // 验证空数据
            if (haveList != null && !haveList.isEmpty()) {
                haveListNameList = haveList.stream().map(ProjPrinterConfigInfo::getPrinterName).collect(Collectors.toList());
            }
            // 将传入数据循环遍历，将数据保存到数据库中。
            for (PrinterCsmDTO printer : list) {
                ProjPrinterConfigInfo info = new ProjPrinterConfigInfo();
                BeanUtils.copyProperties(printer, info);
                info.setId(SnowFlakeUtil.getId());
                // 批量保存 检查是否存在 不存在新增，存在则不进行处理。
                log.error("parameter{}", printer);
                // 根据mac+ip+打印机名称+ 打印机驱动确定唯一设备
                String printerName = String.format("%s%s%s%s", printer.getMac(), printer.getIpAddress(), printer.getPrinterName(), printer.getPrinterDriver());
                if (!haveListNameList.contains(printerName)) {
                    try {
                        haveListNameList.add(printerName);
                        projPrinterConfigInfoMapper.insert(info);
                    } catch (Exception e) {
                        log.error("保存打印信息报错{}", e);
                    }
                }
            }
        }
        return ResponseResult.success("存储成功");
    }

    /**
     * 交付挂载统计报表资源库页面
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result getReportLibraryPageLink(ProjSurveyReportExamineReq projSurveyReportExamineReq) {
        String linkStr = "reportweb/#/resourceLibrary?org_id=-1_-1_-1&hisHospitalId=-1&display=false";
        try {
            String tableUrlValuePrivate = tableUrlValue;
            if (tableUrlValuePrivate != null) {
                if (tableUrlValuePrivate.contains("-test") || tableUrlValuePrivate.contains("172")) {
                    tableUrlValuePrivate = "https://172.16.4.151/";
                } else {
                    tableUrlValuePrivate = tableUrlValuePrivate.replace("csm", "");
                }
            }
            linkStr = tableUrlValuePrivate + linkStr;
            SysUserVO vo = userHelper.getCurrentUser();
            String userId = String.valueOf(vo.getSysUserId());
            String timestamp = String.valueOf(System.currentTimeMillis());
            String data = String.format("userId=%s&timestamp=%s", userId, timestamp);
            String sign = SignatureReportUtils.generateSignatureUtils(data);
            linkStr = linkStr + "&timestamp=" + timestamp + "&signature=" + sign + "&userId=" + userId + "&userName=" + vo.getUserName();

        } catch (Exception e) {
            throw new CustomException("获取链接失败,重新打开页面尝试");
        }
        return Result.success(linkStr);
    }


    @Override
    public Result<Void> updateReportIdentifier(ProjSurveyFormResponsibilitiesReq param) {
        log.info("打印报表分配验证人，验证人ID={}", param.getIdentifierUserId());
        if (CollectionUtils.isEmpty(param.getIds())) {
            return Result.fail("请选择要分配验证人的打印报表");
        }
        if (param.getIdentifierUserId() == null || Long.valueOf(0L).equals(param.getIdentifierUserId()) || Long.valueOf(-1L).equals(param.getIdentifierUserId())) {
            return Result.fail("请选择要分配的验证人");
        }

        Date now = new Date();
        for (Long surveyReportId : param.getIds()) {
            ProjSurveyReport report = new ProjSurveyReport();
            report.setSurveyReportId(surveyReportId);
            report.setIdentifierUserId(param.getIdentifierUserId());
            report.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            report.setUpdateTime(now);
            this.updateById(report);
        }
        return Result.success(null, "分配验证人成功");
    }


    @Override
    public boolean verificationPassed(PrintReportVerificationPassedParam param) {
        log.info("打印报表前端验证通过，参数={}", JSON.toJSONString(param));

        Date now = new Date();
        for (Long surveyReportId : param.getSurveyReportIdList()) {
            ProjSurveyReport report = new ProjSurveyReport();
            report.setSurveyReportId(surveyReportId);
            report.setIdentifierUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
            report.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            report.setUpdateTime(now);
            report.setFinishStatus(8);
            this.updateById(report);
            projBusinessExamineLogService.saveOperationLog("printreport", 21, "验证通过", surveyReportId);
        }

        return true;
    }

    /**
     * 报表打印导出Excel
     *
     * @param response
     * @param dto
     */
    @Override
    public void reportPrintExportExcel(HttpServletResponse response, ProjSurveyReportReq dto) {

        // 1. 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = "报表列表导出_" + System.currentTimeMillis();
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("编码失败");
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 2查询导出的数据
        List<ProjSurveyReportResp> projSurveyReportResps = projSurveyReportMapper.selectSurveyReportByPage(dto);
        if (projSurveyReportResps != null && projSurveyReportResps.size() > 0) {
            Set<Long> projectInfoIds = projSurveyReportResps.stream().map(ProjSurveyReportResp::getProjectInfoId).collect(Collectors.toSet());
            List<ProjProjectInfo> projList = projProjectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().in("project_info_id", projectInfoIds));
            Map<Long, ProjProjectInfo> projectInfoMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(projList)) {
                projectInfoMap = projList.stream().collect(Collectors.toMap(ProjProjectInfo::getProjectInfoId, Function.identity()));
            }
            for (ProjSurveyReportResp projSurveyReportResp : projSurveyReportResps) {
                // 获取驳回次数和原因
                RejectReasonAndCount verifyRejection = projBusinessExamineLogService.selectReasonAndCountById(projSurveyReportResp.getSurveyReportId(), ExamineStatusEnum.VERIFY_REJECTION);
                projSurveyReportResp.setRejectCount(verifyRejection.getRejectCount());
                projSurveyReportResp.setRejectReason(verifyRejection.getRejectReason());

                RejectReasonAndCount surveyReject = projBusinessExamineLogService.selectReasonAndCountById(projSurveyReportResp.getSurveyReportId(), ExamineStatusEnum.BACKEND_REJECTION);
                projSurveyReportResp.setSurveyRejectCount(surveyReject.getRejectCount());
                projSurveyReportResp.setSurveyRejectReason(surveyReject.getRejectReason());

                ProjProjectInfo projectInfoss = projectInfoMap.get(projSurveyReportResp.getProjectInfoId());
                // 获取文件信息
                String[] surveyImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getSurveyImgs())) {
                    surveyImgs = projSurveyReportResp.getSurveyImgs().split(",");
                }
                String[] finishImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getFinishImgs())) {
                    finishImgs = projSurveyReportResp.getFinishImgs().split(",");
                }
                List<ProjProjectFile> surveyImgsFileList = new ArrayList<>();
                List<URL> surveyImgsFilePathList = new ArrayList<>();
                if (surveyImgs != null && surveyImgs.length > 0) {
                    List<Long> surveyImgsList = new ArrayList<>();
                    for (String surveyImg : surveyImgs) {
                        try {
                            surveyImgsList.add(Long.valueOf(surveyImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (surveyImgsList != null && surveyImgsList.size() > 0) {
                        surveyImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", surveyImgsList));
                        if (CollectionUtil.isNotEmpty(surveyImgsFileList)) {
                            surveyImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        surveyImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(surveyImgsFileList);
                        List<String> surveyImgsFilePathList1 = surveyImgsFileList.stream().filter(item -> item.getFileType().equals("image")).map(item -> item.getFilePath()).collect(Collectors.toList());
                        if (surveyImgsFilePathList1 != null && surveyImgsFilePathList1.size() > 0) {
                            for (String url : surveyImgsFilePathList1) {
                                try {
                                    surveyImgsFilePathList.add(getUrl(url));
                                } catch (IOException e) {
                                    log.error("转换失败", e);
                                }
                            }
                        }
                    }
                }
                List<ProjProjectFile> finishImgsFileList = new ArrayList<>();
                List<URL> finishImgsFilePathList = new ArrayList<>();
                if (finishImgs != null && finishImgs.length > 0) {
                    List<Long> finishImgsList = new ArrayList<>();
                    for (String finishImg : finishImgs) {
                        try {
                            finishImgsList.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (finishImgsList != null && finishImgsList.size() > 0) {
                        finishImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", finishImgsList));
                        if (CollectionUtil.isNotEmpty(finishImgsFileList)) {
                            finishImgsFileList.forEach(item -> {
                                // 因为之前只允许传图片，获取不到文件类型时默认为图片
                                if (StringUtils.isBlank(item.getFileType())) {
                                    item.setFileType("image");
                                }
                            });
                        }
                        finishImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                        // 按照image、pdf、word的顺序排序
                        Collections.sort(surveyImgsFileList);
                        List<String> finishImgsFilePathList1 = finishImgsFileList.stream().filter(item -> item.getFileType().equals("image")).map(item -> item.getFilePath()).collect(Collectors.toList());
                        if (finishImgsFilePathList1 != null && finishImgsFilePathList1.size() > 0) {
                            for (String url : finishImgsFilePathList1) {
                                try {
                                    finishImgsFilePathList.add(getUrl(url));
                                } catch (IOException e) {
                                    log.error("转换失败", e);
                                }
                            }
                        }
                    }
                }
                projSurveyReportResp.setSurveyImgStrList(surveyImgsFilePathList);
                projSurveyReportResp.setMakeImgStrList(finishImgsFilePathList);

            }
        }
        //图片列最大图片数
        AtomicReference<Integer> maxImageSize = new AtomicReference<>(0);
        AtomicInteger maxBefore = new AtomicInteger(0);
        AtomicInteger maxAfter = new AtomicInteger(0);
        if (projSurveyReportResps != null) {
            projSurveyReportResps.forEach(item -> {
                //最大图片数大小
                if (!com.alibaba.nacos.client.naming.utils.CollectionUtils.isEmpty(item.getSurveyImgStrList())) {
                    maxBefore.updateAndGet(current -> Math.max(current, item.getSurveyImgStrList().size()));
                }
                if (!com.alibaba.nacos.client.naming.utils.CollectionUtils.isEmpty(item.getMakeImgStrList())) {
                    maxAfter.updateAndGet(current -> Math.max(current, item.getMakeImgStrList().size()));
                }
            });
        }
        maxImageSize.set(Math.max(maxBefore.get(), maxAfter.get()));
        // 3. 执行导出
        try {
            //导出临时记录的数据（带图片）
            EasyExcel.write(response.getOutputStream(), ProjSurveyReportResp.class).autoCloseStream(true).registerWriteHandler(new CustomImageModifyHandler(60, 32)).sheet("sheet").doWrite(projSurveyReportResps);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 服务器地址转base64文件
     *
     * @param obsUrl
     * @return
     */
    private URL getUrl(String obsUrl) throws IOException {
        if (StringUtils.isNotEmpty(obsUrl) && !obsUrl.startsWith("http")) {
            obsUrl = OBSClientUtils.getTemporaryUrl(obsUrl, 3600);
        }
        if (StringUtils.isNotEmpty(obsUrl) && !obsUrl.startsWith("http")) {
            String urlee = obsUrl.substring(obsUrl.indexOf("tduck-api"));
            if ("prod".equals(activeProfiles)) {
                obsUrl = "http://10.201.0.194/" + urlee;
            } else {
                obsUrl = "http://172.16.2.239/" + urlee;
            }
        }
        // 从OBS链接获取文件内容
        URL url = new URL(obsUrl);
        return url;
    }

    @Override
    public Integer getReportCount(Long projectInfoId, Long hospitalInfoId, Long yyProductId, MilestoneNodeEnum nodeEnum) {
        if (projectInfoId == null) {
            throw new IllegalArgumentException("查询上线必备的打印报表数量，项目ID不可为null");
        }
        Map<String, Object> projMilestoneInfo = new HashMap<>();
        projMilestoneInfo.put("projectInfoId", projectInfoId);
        projMilestoneInfo.put("hospitalInfoId", hospitalInfoId);
        projMilestoneInfo.put("yyProductId", yyProductId);
        if (MilestoneNodeEnum.SURVEY_REPORT.equals(nodeEnum) || MilestoneNodeEnum.PREPARAT_REPORT.equals(nodeEnum)) {
            List<Integer> finishedStatus = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass(nodeEnum.getCode(), 3);
            if (MilestoneNodeEnum.SURVEY_REPORT.equals(nodeEnum) && projProjectInfoService.isOpenSurveyReportAudit(projectInfoId)) {
                finishedStatus = finishedStatus.stream().filter(item -> !Integer.valueOf(0).equals(item)).collect(Collectors.toList());
            }
            log.info("获取上线必备的报表中未完成的打印报表数量，当前里程碑节点={}，被判定为完成的状态={}", nodeEnum, finishedStatus);
            if (CollectionUtils.isNotEmpty(finishedStatus)) {
                projMilestoneInfo.put("finishedStatus", finishedStatus);
            }
        }

        return projMilestoneInfoMapper.getReportCount(projMilestoneInfo);
    }
}
