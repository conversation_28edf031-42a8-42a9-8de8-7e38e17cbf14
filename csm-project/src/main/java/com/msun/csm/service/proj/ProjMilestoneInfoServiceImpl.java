package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.map.ListOrderedMap;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.MilestoneStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.ProjectViewModelEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectUpgradationTypeEnums;
import com.msun.csm.common.enums.projreview.ProjectStageCodeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.Global;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.dict.DictProjectPlanItem;
import com.msun.csm.dao.entity.dict.DictProjectStage;
import com.msun.csm.dao.entity.oldimsp.ImspProject;
import com.msun.csm.dao.entity.proj.AddProjProjectConfigParamDTO;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjCustomVsProjectTypeEntity;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneManualUpdateLog;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjPrereleaseMsg;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectConfig;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjProjectPlanVO;
import com.msun.csm.dao.entity.proj.ProjProjectStageInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.dao.entity.proj.QueryProjectPlanParam;
import com.msun.csm.dao.entity.proj.UrlParam;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendLimit;
import com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity;
import com.msun.csm.dao.entity.sys.DictMessageType;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.dict.DictAgentScenarioConfigMapper;
import com.msun.csm.dao.mapper.dict.DictMessageTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper;
import com.msun.csm.dao.mapper.dict.DictProjectStageMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspProjectMapper;
import com.msun.csm.dao.mapper.proj.OldTbProjectReadyWorkMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneManualUpdateLogMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjPrereleaseMsgMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjTodoTaskMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ProjStatisticalReportMainMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.feign.entity.yunying.req.NodeBaseData;
import com.msun.csm.feign.entity.yunying.req.ProductReq;
import com.msun.csm.feign.entity.yunying.req.UpdateNodeStatusDTO;
import com.msun.csm.model.dto.CompSurveyProductMilestone;
import com.msun.csm.model.dto.MilestoneInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneManualUpdateLogQueryDto;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.param.ConfirmFinalDataParam;
import com.msun.csm.model.param.GetProjectFileRuleParam;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.param.OnlineValidateParam;
import com.msun.csm.model.param.UpdateProjMilestoneInfoAfterAuditPmoParam;
import com.msun.csm.model.req.CheckResult;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.req.project.UpdateProjectContentReq;
import com.msun.csm.model.req.project.UpdateProjectPlanReq;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projreport.ConfigCustomBackendLimitReq;
import com.msun.csm.model.req.projreport.PrintReportVerificationPassedParam;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainAssignPersonsReq2;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainPageReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.resp.project.MileStoneInfoResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainSelectResp;
import com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp;
import com.msun.csm.model.vo.DictProjectStageVO;
import com.msun.csm.model.vo.HospitalPlanVO;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.ProjMilestoneInfoVO;
import com.msun.csm.model.vo.ProjProjectFileVO;
import com.msun.csm.model.vo.ProjectFileUploadRuleVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanTaskRespVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.dict.DictBusinessStatusService;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.projform.ProjSurveyFormService;
import com.msun.csm.service.report.ConfigCustomBackendLimitService;
import com.msun.csm.service.report.statis.ProjStatisticalReportMainService;
import com.msun.csm.service.rule.RuleProjectRuleConfigService;
import com.msun.csm.util.ListUtils;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * @Description:
 * @Author: zhouzhaoyu
 * @Date: 2024/5/06
 */
@Slf4j
@Service
public class ProjMilestoneInfoServiceImpl implements ProjMilestoneInfoService {
    /**
     * 里程碑大阶段完成节点与项目交付状态对照关系
     */
    private static final ListOrderedMap<MilestoneNodeEnum, Integer> COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP = new ListOrderedMap();

    static {
        COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.put(MilestoneNodeEnum.PROJECT_ACCEPT, 6);
        COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.put(MilestoneNodeEnum.PROJECT_LAUNCH, 5);
        COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.put(MilestoneNodeEnum.PREPARAT_CHECK, 4);
        COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.put(MilestoneNodeEnum.SETTLED_PMO_AUDIT, 3);
        COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.put(MilestoneNodeEnum.SURVEY_SUMMARY, 2);
    }

    @Resource
    private MedicalInsuranceService medicalInsuranceService;
    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private DictBusinessStatusService dictBusinessStatusService;
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private ProjSurveyReportService projSurveyReportService;
    @Resource
    private ProjProjectStageInfoService projectStageInfoService;
    @Resource
    private DictProductVsModulesMapper dictProductVsModulesMapper;
    @Resource
    private UserHelper userHelper;
    @Resource
    private ProjSurveyPlanService surveyPlanService;
    @Resource
    private ProjProjectInfoService projProjectInfoService;
    @Resource
    private ProjHospitalInfoService hospitalInfoService;
    @Resource
    private ImspProjectMapper imspProjectMapper;
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private OldTbProjectReadyWorkMapper oldTbProjectReadyWorkMapper;
    @Resource
    private SendMessageService sendMessageService;
    @Resource
    private DictProjectStageMapper dictProjectStageMapper;
    @Resource
    private ProjApplyOrderMapper applyOrderMapper;
    @Resource
    private ProjOrderInfoMapper orderInfoMapper;
    @Resource
    private OnlineStepService onlineStepService;
    @Lazy
    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;
    @Lazy
    @Resource
    private ProjOrderProductMapper orderProductMapper;
    @Lazy
    @Resource
    private ProjProductBacklogMapper projProductBacklogMapper;
    @Lazy
    @Resource
    private ProjContractInfoMapper contractInfoMapper;
    @Lazy
    @Resource
    private YunyingFeignClient yunyingFeignClient;
    @Lazy
    @Resource
    private ProjMilestoneManualUpdateLogMapper projMilestoneManualUpdateLogMapper;
    @Lazy
    @Resource
    private DictProductMapper dictProductMapper;
    @Lazy
    @Resource
    private ProjCustomInfoService projCustomInfoService;
    @Lazy
    @Resource
    private ProjProjectFileMapper projProjectFileMapper;
    @Lazy
    @Resource
    private ConfigCustomBackendLimitService configCustomBackendLimitService;
    @Resource
    private ProjProjectPlanMapper projectPlanMapper;
    @Resource
    private ProjTodoTaskMapper todoTaskMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjPrereleaseMsgMapper projPrereleaseMsgMapper;

    @Resource
    private DictProjectPlanItemMapper dictProjectPlanItemMapper;

    @Resource
    private DictMessageTypeMapper dictMessageTypeMapper;

    // 主表
    @Resource
    @Lazy
    private ProjStatisticalReportMainMapper projStatisticalReportMainMapper;

    @Resource
    private ProjProjectPlanService projProjectPlanService;


    @Resource
    private ProjProductDeliverRecordService projProductDeliverRecordService;

    @Lazy
    @Resource
    private ProjCustomVsProjectTypeMapper projCustomVsProjectTypeMapper;

    @Resource
    private ProjProjectConfigService projectConfigService;


    @Resource
    @Lazy
    private BaseQueryService baseQueryService;

    @Resource
    @Lazy
    private ProjProjectPlanBaseService projProjectPlanBaseService;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private RuleProjectRuleConfigService ruleProjectRuleConfigService;

    @Resource
    private DictAgentScenarioConfigMapper dictAgentScenarioConfigMapper;

    @Override
    public List<ProjMilestoneInfo> getMilestoneInfoByProjectInfoId(ProjMilestoneInfoDTO milestoneInfoDTO) {
        return projMilestoneInfoMapper.getMilestoneInfoByProjectInfoId(milestoneInfoDTO);
    }

    @Override
    @Transactional
    public Result<MileStoneInfoResp> generateMilestoneInfo(ProjMilestoneInfoDTO dto) {
        ProjProjectInfo info = projProjectInfoService.selectByPrimaryKey(dto.getProjectInfoId());
        if (ObjectUtil.isEmpty(info)) {
            return Result.fail("项目信息为空!!!");
        }
        try {
            // 根据项目ID获取项目配置
            ProjProjectConfig projectConfig = projectConfigService.getConfigByProjectInfoId(info.getProjectInfoId());
            // 项目配置不存在时进行新增操作
            if (projectConfig == null) {
                AddProjProjectConfigParamDTO addParamDTO = new AddProjProjectConfigParamDTO();
                addParamDTO.setViewModel(ProjectViewModelEnum.MILESTONE);
                addParamDTO.setProjectInfoId(info.getProjectInfoId());
                projectConfigService.addProjectConfig(addParamDTO);
            }
        } catch (Exception e) {
            log.error("新增项目配置，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }


        // 初始化项目上线步骤表信息 当上线步骤标志为0时 ， 代表没进行初始化上线步骤表数据，需要进行初始化
        if (info.getOnlineStepFlag() == 0) {
            ProjOnlineStepDTO projOnlineStepDTO = new ProjOnlineStepDTO();
            projOnlineStepDTO.setProjectInfoId(info.getProjectInfoId());
            projOnlineStepDTO.setCustomInfoId(info.getCustomInfoId());
            onlineStepService.saveOnlineStep(projOnlineStepDTO);
            log.info("初始化项目上线步骤信息成功 , {}", JSONUtil.toJsonStr(projOnlineStepDTO));
            // 更新项目表 上线步骤初始化标志为 已初始化
            ProjProjectInfo projProjectInfo = new ProjProjectInfo();
            projProjectInfo.setProjectInfoId(dto.getProjectInfoId());
            projProjectInfo.setOnlineStepFlag(1);
            projProjectInfoService.updateByPrimaryKeySelective(projProjectInfo);
        }
        ProjMilestoneInfoDTO milestoneInfoDTO = new ProjMilestoneInfoDTO();
        milestoneInfoDTO.setProjectInfoId(dto.getProjectInfoId());
        if (NumberEnum.NO_1.num().equals(info.getProjectType())) {
            milestoneInfoDTO.setMonomerFlag(NumberEnum.NO_1.num());
        }
        if (NumberEnum.NO_2.num().equals(info.getProjectType())) {
            milestoneInfoDTO.setRegionFlag(NumberEnum.NO_1.num());
        }
        milestoneInfoDTO.setHisFlag(info.getHisFlag());
        milestoneInfoDTO.setUpgradationType(info.getUpgradationType());
        //先判断项目信息是否进行过初始化，如果未初始化，则先进行初始化
        //项目里程碑节点开始初始化 、 0903 新增需求。初始化时 增加根据工单类型过滤里程碑节点信息【自研软件与外采软件】
        // 查询项目的工单信息
        ProjOrderInfo projOrderInfo = orderInfoMapper.selectById(info.getOrderInfoId());
        switch (projOrderInfo.getDeliveryOrderType()) {
            case 1:
                milestoneInfoDTO.setSelfSoftwareFlag(NumberEnum.NO_1.num());
                break;
            case 8:
                milestoneInfoDTO.setExternalSoftwareFlag(NumberEnum.NO_1.num());
                break;
            default:
                throw new CustomException("项目工单类型异常");
        }
        if (0 == info.getMemberInitFlag()) {
            //判断如果是手动更改的初始化标识，则先把原先的节点信息作废掉
            List<ProjProjectStageInfo> stageInfos =
                    projectStageInfoService.getStageInfoByProjectInfoId(info.getProjectInfoId());
            List<ProjMilestoneInfo> milestoneInfos = this.getProjMileStoneInfoByProjectId(info.getProjectInfoId());
            if (!CollectionUtils.isEmpty(stageInfos)) {
                projectStageInfoService.delStageInfoByProjectInfoId(info.getProjectInfoId());
            }
            if (!CollectionUtils.isEmpty(milestoneInfos)) {
                this.delMileStoneInfoByProjectId(info.getProjectInfoId());
            }
            //项目里程碑节点开始初始化 、 0903 新增需求。初始化时 增加根据工单类型过滤里程碑节点信息【自研软件与外采软件】
            List<ProjMilestoneInfo> projMilestoneInfos = this.getMilestoneInfoByProjectInfoId(milestoneInfoDTO);
            projMilestoneInfos.forEach(v -> {
                v.setMilestoneInfoId(SnowFlakeUtil.getId());
                v.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                v.setCreateTime(new Date());
                v.setMilestoneStatus(0);
                v.setInvalidFlag(0);
                v.setProjectInfoId(dto.getProjectInfoId());
            });
            //项目里程碑节点表插入数据
            Integer batchInsert = projMilestoneInfoMapper.batchInsert(projMilestoneInfos);
            if (batchInsert > 0) {
                projProjectInfoService.updateInitFlag(dto.getProjectInfoId());
            } else {
                throw new CustomException("初始化失败!!!");
            }
            //回更老项目的状态
            List<TmpProjectNewVsOld> tmpProjectNewVsOlds = tmpProjectNewVsOldMapper.selectByNewProjectIds(Collections.singletonList(dto.getProjectInfoId()));
            if (CollectionUtils.isEmpty(tmpProjectNewVsOlds)) {
                throw new CustomException("数据错误");
            }
            ImspProject imspProject = getImspProject(tmpProjectNewVsOlds.get(0).getOldProjectInfoId(), info);
            imspProjectMapper.updateById(imspProject);
            projProjectInfoService.addDictData(dto.getProjectInfoId());
        }
        //初始化项目计划工作项
        if (0 == info.getProjectPlanFlag()) {
            //0. 删除已有历史数据-只删除预置的
            QueryProjectPlanParam param = new QueryProjectPlanParam();
            param.setProjectInfoId(info.getProjectInfoId());
            param.setPlanType(2);
            List<ProjProjectPlan> exists = projectPlanMapper.getProjectPlanByProjectInfoId(param);
            if (!CollectionUtils.isEmpty(exists)) {
                projectPlanMapper.deleteBatchIds(exists.stream().map(ProjProjectPlan::getProjectPlanId).collect(Collectors.toList()));
                todoTaskMapper.deleteBatchPIds(exists.stream().map(ProjProjectPlan::getProjectPlanId).collect(Collectors.toList()));
            }
            //1. 获取项目计划工作项-ProjProjectPlan
            List<ProjProjectPlan> projectPlans = this.getConfigProjectPlanItemByParams(milestoneInfoDTO);
            projectPlans.forEach(v -> {
                v.setProjectPlanId(SnowFlakeUtil.getId());
                v.setProjectInfoId(dto.getProjectInfoId());
                v.setStatus(0);
                v.setCompleteCount(0);
                v.setTotalCount(this.convertPlanTotalCount(v));
                //1 里程碑页面
                v.setJumpType(Integer.valueOf(1).equals(v.getMilestoneFlag()) ? 1 : 0);
                //默认全部生成对应的待办任务
                v.setGenerateTodoFlag(1);
                //1 自定义的  2 预置的
                v.setPlanType(2);
                //关注状态-默认 0
                v.setAttentionFlag(0);
                v.setCreaterId(info.getProjectLeaderId());
                v.setUpdaterId(info.getProjectLeaderId());
                v.setCreateTime(new Date());
                v.setUpdateTime(new Date());
                v.setIsDeleted(0);
            });
            projProjectPlanService.batchInsert(projectPlans);
            //2.处理待办数据-ProjTodoTask
            List<ProjTodoTask> todoTasks = projectPlans.stream().map(v -> {
                ProjTodoTask todoTask = new ProjTodoTask();
                BeanUtil.copyProperties(v, todoTask);
                todoTask.setTodoTaskId(SnowFlakeUtil.getId());
                return todoTask;
            }).collect(Collectors.toList());
            todoTaskMapper.batchInsert(todoTasks);
            projectInfoMapper.updateProjectPlanFlag(dto.getProjectInfoId());

            // 3.处理消息
            todoTasks.forEach(v -> {
                // 任务待办提醒预发消息
                ProjProjectPlan projProjectPlan = projectPlanMapper.selectByPrimaryKey(v.getProjectPlanId());

                // 存在前置工作
                if (projProjectPlan != null && Integer.valueOf(1).equals(projProjectPlan.getPriorProjectPlanItemFlag()) && StringUtils.isNotBlank(projProjectPlan.getPriorProjectPlanItemId())) {
                    DictProjectPlanItem dictProjectPlanItem = dictProjectPlanItemMapper.selectByPrimaryKey(projProjectPlan.getProjectPlanItemId());

                    // 当前待办对应的所有前置工作的工作项ID
                    DictMessageType dictMessageType = dictMessageTypeMapper.getDictMessageTypeById(100001L);

                    List<ProjPrereleaseMsg> projPrereleaseMsgs = projPrereleaseMsgMapper.selectList(new QueryWrapper<ProjPrereleaseMsg>()
                            .eq("is_deleted", 0)
                            .eq("msg_class_id", 100001L)
                            .eq("project_info_id", v.getProjectInfoId())
                            .eq("plan_item_code", dictProjectPlanItem.getProjectPlanItemCode())
                            .eq("pre_plan_item_code", dictProjectPlanItem.getProjectPlanItemCode())
                    );
                    if (CollectionUtils.isEmpty(projPrereleaseMsgs) && dictMessageType != null) {
                        ProjPrereleaseMsg prereleaseMsg = new ProjPrereleaseMsg();
                        prereleaseMsg.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                        prereleaseMsg.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                        prereleaseMsg.setPlanItemCode(dictProjectPlanItem.getProjectPlanItemCode());
                        prereleaseMsg.setPrePlanItemCode(dictProjectPlanItem.getProjectPlanItemCode());
                        prereleaseMsg.setHospitalInfoId(null);
                        prereleaseMsg.setProjectInfoId(v.getProjectInfoId());
                        // TODO 固定消息类型号段
                        prereleaseMsg.setMsgClassId(100001L);
                        prereleaseMsg.setPrereleaseMsgId(SnowFlakeUtil.getId());
                        projPrereleaseMsgMapper.insertData(prereleaseMsg);
                    }
                }
            });
        }
        //组装里程碑节点展示所需参数
        AtomicInteger orderNo = new AtomicInteger(-1);
        Result<List<DictProjectStageVO>> listResult = projectStageInfoService.getProjProjectStageInfoByProjectInfoId(info);
        if (!listResult.isSuccess()) {
            return Result.fail(listResult.getMsg());
        }
        List<DictProjectStageVO> allProjectStage = listResult.getData();
        List<ProjMilestoneInfoVO> allList = new ArrayList<>();
        allProjectStage.forEach(v -> {
            List<ProjMilestoneInfoVO> milestoneInfo = getMilestoneInfo(dto.getProjectInfoId(), v.getId());
            List<ProjProjectPlanVO> projectPlanList = projProjectPlanService.getProjectPlanViewByProjectInfoId(new QueryProjectPlanParam(dto.getProjectInfoId(), 2));
            this.dealPrincipal(milestoneInfo, projectPlanList);
            allList.addAll(milestoneInfo);
            List<ProjMilestoneInfoVO> collect = milestoneInfo.stream().filter(e -> e.getMilestoneStatus().equals(MilestoneStatusEnum.UN_COMPLETE.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                v.setStageState("finished");
                orderNo.set(v.getOrderNo());
            } else {
                v.setStageState("unfinished");
            }
            v.setVoList(milestoneInfo);
        });
        MileStoneInfoResp resp = handleReq(allList);
        resp.setFinishStep(orderNo.get());
        resp.setStageVOList(allProjectStage);
        UrlParam params = projMilestoneInfoMapper.getUrlParams(dto.getProjectInfoId());
        params.setUserId(userHelper.getCurrentUser().getSysUserId());
        params.setUserName(userHelper.getCurrentUser().getUserName());
        params.setProjectNumber(info.getProjectNumber());
        resp.setParam(params);
        return Result.success(resp);
    }

    /**
     * 初始化时项目计划总数
     *
     * @param projProjectPlan 项目计划
     */
    private int convertPlanTotalCount(ProjProjectPlan projProjectPlan) {
        // 产品业务调研
        DictProjectPlanItem surveyProduct = dictProjectPlanItemMapper.selectByCode(DictProjectPlanItemEnum.SURVEY_PRODUCT.getPlanItemCode());
        // 产品准备工作
        DictProjectPlanItem preparatProduct = dictProjectPlanItemMapper.selectByCode(DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
        if (surveyProduct.getProjectPlanItemId().equals(projProjectPlan.getProjectPlanItemId())) {
            // 查询所有实施产品
            List<ProjProductDeliverRecord> productDeliverRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(projProjectPlan.getProjectInfoId(), null);
            return productDeliverRecord.size();
        }
        if (preparatProduct.getProjectPlanItemId().equals(projProjectPlan.getProjectPlanItemId())) {
            // 仅查询需要调研的产品总数，初始化时总数先按照这个数量初始化。后续生成各产品准备工作之后再进行更新
            List<ProjProductDeliverRecord> productDeliverRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(projProjectPlan.getProjectInfoId(), true);
            return productDeliverRecord.size();
        }
        return 0;
    }

    /**
     * 将项目计划的负责人信息复制到里程碑信息中
     *
     * @param milestoneInfoList 里程碑节点集合
     * @param projectPlanList   项目计划集合
     */
    private void dealPrincipal(List<ProjMilestoneInfoVO> milestoneInfoList, List<ProjProjectPlanVO> projectPlanList) {
        if (!CollectionUtils.isEmpty(milestoneInfoList) && !CollectionUtils.isEmpty(projectPlanList)) {
            milestoneInfoList.forEach(milestoneInfo -> {
                ProjProjectPlanVO projProjectPlanVO = projectPlanList.stream().filter(plan -> milestoneInfo.getProjectStageCode().equals(plan.getPlanStageCode()) && milestoneInfo.getMilestoneNodeCode().equals(plan.getPlanItemCode())).findFirst().orElse(null);
                if (projProjectPlanVO != null) {
                    milestoneInfo.setBackendEngineerId(projProjectPlanVO.getBackendEngineerId());
                    milestoneInfo.setImplementationEngineerId(projProjectPlanVO.getImplementationEngineerId());
                }
            });
        }
    }


    public List<ProjMilestoneInfo> createMilestoneInfo(ProjMilestoneInfoDTO milestoneInfoDTO, ProjProjectInfo info) {
        Long projectInfoId = info.getProjectInfoId();
        //判断如果是手动更改的初始化标识，则先把原先的节点信息作废掉
        List<ProjProjectStageInfo> stageInfos = projectStageInfoService.getStageInfoByProjectInfoId(info.getProjectInfoId());
        List<ProjMilestoneInfo> milestoneInfos = this.getProjMileStoneInfoByProjectId(info.getProjectInfoId());
        if (!CollectionUtils.isEmpty(stageInfos)) {
            projectStageInfoService.delStageInfoByProjectInfoId(info.getProjectInfoId());
        }
        if (!CollectionUtils.isEmpty(milestoneInfos)) {
            this.delMileStoneInfoByProjectId(info.getProjectInfoId());
        }
        //项目里程碑节点开始初始化 、 0903 新增需求。初始化时 增加根据工单类型过滤里程碑节点信息【自研软件与外采软件】
        // 查询项目的工单信息
        ProjOrderInfo projOrderInfo = orderInfoMapper.selectById(info.getOrderInfoId());
        switch (projOrderInfo.getDeliveryOrderType()) {
            case 1:
                milestoneInfoDTO.setSelfSoftwareFlag(NumberEnum.NO_1.num());
                break;
            case 8:
                milestoneInfoDTO.setExternalSoftwareFlag(NumberEnum.NO_1.num());
                break;
            default:
                throw new CustomException("项目工单类型异常");
        }
        List<ProjMilestoneInfo> projMilestoneInfos = this.getMilestoneInfoByProjectInfoId(milestoneInfoDTO).stream().distinct().collect(Collectors.toList());
        projMilestoneInfos.forEach(v -> {
            v.setMilestoneInfoId(SnowFlakeUtil.getId());
            v.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            v.setCreateTime(new Date());
            v.setMilestoneStatus(0);
            v.setInvalidFlag(0);
            v.setProjectInfoId(projectInfoId);
        });
        return projMilestoneInfos;
    }

    /**
     * 获取项目计划工作项
     *
     * @param dto
     * @return
     */
    private List<ProjProjectPlan> getConfigProjectPlanItemByParams(ProjMilestoneInfoDTO dto) {
        List<ProjProjectPlan> projectPlans = projectPlanMapper.getConfigProjectPlanItemByParams(dto);
        return projectPlans;
    }

    //处理老平台项目信息
    private @NotNull ImspProject getImspProject(Long oldProjectId, ProjProjectInfo projectInfo) {
        ImspProject imspProject = new ImspProject();
        imspProject.setId(oldProjectId);
        imspProject.setManagerVerify(1);
        imspProject.setIsArea(projectInfo.getProjectType().equals(ProjectTypeEnums.REGION.getCode()) ? "Y" : "F");
        imspProject.setProjectCustomerType(projectInfo.getUpgradationType().equals(ProjectUpgradationTypeEnums.LHX.getCode()) ? "lhx" : "xkh");
        return imspProject;
    }

    @Override
    public Boolean updateMilestone(UpdateMilestoneDTO dto) {
        SysUserVO sysUserVO = new SysUserVO();
        if (ObjectUtil.isEmpty(dto.getNodeHeadId())) {
            sysUserVO.setSysUserId(userHelper.getCurrentUser().getSysUserId());
        } else {
            sysUserVO.setSysUserId(dto.getNodeHeadId());
        }
        return updateMilestoneImpl(dto, sysUserVO);
    }

    public Boolean updateMilestoneImpl(UpdateMilestoneDTO dto, SysUserVO sysUserVO) {
        log.info("更新里程碑节点时发送消息，参数={}", JSON.toJSONString(dto));
        dto.setNodeHeadId(sysUserVO.getSysUserId());
        dto.setActualCompTime(new Date());
        Integer updated = projMilestoneInfoMapper.updateMilestone(dto);
        ProjMilestoneInfo milestoneInfo = projMilestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().eq("milestone_info_id", dto.getMilestoneInfoId()));
        // 里程碑更新为完成时，项目计划完成数+1，同时更改项目计划状态为已完成
        projProjectPlanService.updatePlanAndTodoTaskStatus(milestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(milestoneInfo.getMilestoneNodeCode()), ProjectPlanStatusEnum.FINISHED);
        ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(milestoneInfo.getProjectInfoId());
        DictProjectStage dictProjectStage = dictProjectStageMapper.getDictProjectStageByStageCode(milestoneInfo.getProjectStageCode());
        String projectName = projProjectInfo.getProjectName().endsWith("项目") ? projProjectInfo.getProjectName() : projProjectInfo.getProjectName() + "项目";
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(milestoneInfo.getProjectInfoId());
        messageParam.setContent(String.format("%s，工单编号为%s，%s，%s节点已完成，请知晓！", projectName, projProjectInfo.getProjectNumber(), dictProjectStage.getProjectStageName(), milestoneInfo.getMilestoneNodeName()));
        messageParam.setMessageTypeId(4001L);
        messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
        sendMessageService.sendMessage(messageParam);
        return updated > 0;
    }

    /**
     * @param dto 更新的里程碑
     * @return boolean
     */
    @Override
    public Boolean updateMilestoneById(UpdateMilestoneDTO dto) {
        int count = projMilestoneInfoMapper.updateMilestoneById(dto);
        return count > 0;
    }

    /**
     * 说明: 完成里程碑节点信息
     *
     * @param dto
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/5/31 9:58
     * @remark: Copyright
     */
    @Override
    public Result<Boolean> compMilestone(UpdateMilestoneDTO dto) {
        Long sysUserId = -1L;
        if (ObjectUtil.isNotEmpty(dto.getNodeHeadId())) {
            dto.setNodeHeadId(dto.getNodeHeadId());
        } else {
            try {
                sysUserId = userHelper.getCurrentUser().getSysUserId();
            } catch (Throwable e) {
                log.error("未登录, e.message: {}, e=", e.getMessage(), e);
            }
            dto.setNodeHeadId(sysUserId);
        }
        dto.setActualCompTime(ObjectUtils.isEmpty(dto.getActualCompTime()) ? new Date() : dto.getActualCompTime());
        //获取里程碑信息
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(dto.getMilestoneInfoId());
        if (projMilestoneInfo == null) {
            return Result.fail("节点信息不匹配");
        }
        //不允许重复完成里程碑，只保留第一次完成时间
        if (projMilestoneInfo.getMilestoneStatus() == MilestoneStatusEnum.COMPLETED.getCode().intValue()) {
            boolean planModel = projectConfigService.isPlanModel(projMilestoneInfo.getProjectInfoId());
            if (planModel) {
                ProjProjectPlan projectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projMilestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(projMilestoneInfo.getMilestoneNodeCode()));
                if (!ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projectPlan.getStatus())) {
                    projProjectPlanService.updatePlanAndTodoTaskStatus(projMilestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(projMilestoneInfo.getMilestoneNodeCode()), ProjectPlanStatusEnum.FINISHED);
                }
            }
            return Result.success();
        }
        //获取里程碑枚举信息
        MilestoneNodeEnum milestoneNodeEnum = MilestoneNodeEnum.getByCode(projMilestoneInfo.getMilestoneNodeCode());
        if (milestoneNodeEnum == null || StringUtils.isEmpty(milestoneNodeEnum.getName())) {
            int updated = projMilestoneInfoMapper.updateMilestone(dto);
            boolean planModel = projectConfigService.isPlanModel(projMilestoneInfo.getProjectInfoId());
            if (planModel) {
                ProjProjectPlan projectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projMilestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(projMilestoneInfo.getMilestoneNodeCode()));
                if (!ProjectPlanStatusEnum.FINISHED.getStatusCode().equals(projectPlan.getStatus())) {
                    projProjectPlanService.updatePlanAndTodoTaskStatus(projMilestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(projMilestoneInfo.getMilestoneNodeCode()), ProjectPlanStatusEnum.FINISHED);
                }
            }
            return updated > 0 ? Result.success() : Result.fail("更新节点状态失败");
        }
        Boolean flag = false;
        switch (milestoneNodeEnum) {
            case PREPARAT_PRODUCT:
                //校验产品准备工作是否全部完成
                flag = verifyPreparatProduct(projMilestoneInfo.getProjectInfoId(), NumberEnum.NO_3.num(), NumberEnum.NO_0.num());
                break;
/*            case SURVEY_PRODUCT:
                //校验产品业务调研是否全部完成
                flag = verifyPreparatProduct(projMilestoneInfo.getProjectInfoId(), NumberEnum.NO_1.num(),
                        NumberEnum.NO_0.num());
                break;*/

            // 表单调研
            case SURVEY_FORM:
                // 表单调研判断是否完成
                flag = verifySurveyForm(projMilestoneInfo);
                break;

            // 打印报表调研
            case SURVEY_REPORT:
                flag = verifyPreparatReport(projMilestoneInfo, 1);
                break;

            // 打印报表制作
            case PREPARAT_REPORT:
                // 打印报表准备制作弯沉更新为一验证
                this.printReportUpdate(projMilestoneInfo.getProjectInfoId());
                flag = verifyPreparatReport(projMilestoneInfo, 2);
                break;

            // 统计报表调研
            case SURVEY_STATISTICS_REPORT:
                flag = verifyPreparatStatisticsReport(projMilestoneInfo, "survey");
                break;

            // 统计报表制作
            case PREPARAT_REPORT_STATISTICS:
                // 统计报表准备，制作完成、已下沉、已发布更新为验证通过
                this.statisticalReportUpdate(projMilestoneInfo.getProjectInfoId());
                flag = verifyPreparatStatisticsReport(projMilestoneInfo, "preparat");
                break;

            // 统计报表下沉发布
            case PREPARAT_STATISTICS_RELEASE:
                flag = verifyPreparatStatisticsReport(projMilestoneInfo, "release");
                break;
            case PREPARAT_FORM:
                // 表单准备，更新制作中为验证通过
                this.formUpdate(projMilestoneInfo.getProjectInfoId());
                flag = verifyPreparatForm(projMilestoneInfo);
                break;
            // 医院部署 TODO:
            case MAINTENANCE_HOSPITAL:
                flag = verifyHospitalInfo(projMilestoneInfo);
                break;
            case MEDICAL_INSURANCE:
                try {
                    flag = !verifyMedicalInsurance(projMilestoneInfo);
                } catch (Exception e) {
                    log.error("入驻前校验医保核心代码是否开发完成，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
            default:
                break;
        }
        if (flag) {
            return Result.fail(milestoneNodeEnum.getName() + "信息未全部完成");
        }
        if (MilestoneNodeEnum.PREPARAT_REPORT.getCode().equals(projMilestoneInfo.getMilestoneNodeCode())) {
            ConfigCustomBackendLimit isOpenModel = configCustomBackendLimitService.isOpenByParamer(new ConfigCustomBackendLimitReq(null, projMilestoneInfo.getProjectInfoId(), 1));
            if (isOpenModel != null && isOpenModel.getOpenFlag() == 1) {
                ProjSurveyReportReq projSurveyReportReq = new ProjSurveyReportReq();
                projSurveyReportReq.setProjectInfoId(projMilestoneInfo.getProjectInfoId());
                projSurveyReportReq.setFinishStatus(1);
                List<ProjSurveyReportResp> projSurveyReportResps = projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq);
                if (projSurveyReportResps != null) {
                    for (ProjSurveyReportResp projSurveyReportResp : projSurveyReportResps) {
                        String finishRatio = projSurveyReportResp.getFinishRatio();
                        if (StringUtils.isNotBlank(finishRatio)) {
                            Integer finishRatioInt = Integer.parseInt(finishRatio.replace("%", ""));
                            if (finishRatioInt < isOpenModel.getLimitRatio()) {
                                return Result.fail("上线必备的报表 【" + projSurveyReportResp.getReportName() + "】客户打印验证不足" + isOpenModel.getLimitRatio() + "%，请及时联系客户验证。若关闭客户验证，后端经理可在后端运维处关闭【医护验证打印】");
                            }
                        } else {
                            return Result.fail("上线必备的报表 【" + projSurveyReportResp.getReportName() + "】客户打印验证不足" + isOpenModel.getLimitRatio() + "%，请及时联系客户验证。若关闭客户验证，后端经理可在后端运维处关闭【医护验证打印】");
                        }
                    }
                }

            }
        }
        int updated = projMilestoneInfoMapper.updateMilestone(dto);
        projProjectPlanService.updatePlanAndTodoTaskStatus(projMilestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(projMilestoneInfo.getMilestoneNodeCode()), ProjectPlanStatusEnum.FINISHED);
        return updated > 0 ? Result.success() : Result.fail("更新节点状态失败");
    }

    /**
     * 调研完成判断
     *
     * @param projMilestoneInfo
     * @return
     */
    private Boolean verifySurveyForm(ProjMilestoneInfo projMilestoneInfo) {
        Map<String, Object> mao = new HashMap<>();
        Boolean isNeedAuditorFlag = false;
        mao.put("projectInfoId", projMilestoneInfo.getProjectInfoId());
        mao.put("isNeedAuditorFlag", "0");
        if (projMilestoneInfo != null && projMilestoneInfo.getProjectInfoId() != null) {
            ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projMilestoneInfo.getProjectInfoId(), 12);
            if (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0) {
                mao.put("isNeedAuditorFlag", "1");
                isNeedAuditorFlag = true;
            }
        }
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projMilestoneInfo.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        Boolean isNewFlow = milestoneInfoList != null && !milestoneInfoList.isEmpty() && milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent());
        Integer reportCount = 0;
        if (isNewFlow && isNeedAuditorFlag) {
            List<Integer> surveyForm = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass("survey_form", 3);
            mao.put("statusList", surveyForm);
            reportCount = projMilestoneInfoMapper.getFormSurveyFinishCount(mao);
        }
        return reportCount > 0;
    }

    /**
     * 说明: 校验医院申请是否完成
     *
     * @param projMilestoneInfo
     * @return
     */
    private Boolean verifyHospitalInfo(ProjMilestoneInfo projMilestoneInfo) {
        try {
            if (projMilestoneInfo == null) {
                // 或者根据业务需求返回适当的默认值
                return true;
            }
            ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(projMilestoneInfo.getProjectInfoId());
            if (projProjectInfo == null) {
                // 或者根据业务需求返回适当的默认值
                return true;
            }
            List<ProjCustomVsProjectTypeEntity> listType = projCustomVsProjectTypeMapper.selectList(new QueryWrapper<ProjCustomVsProjectTypeEntity>().eq("custom_info_id", projProjectInfo.getCustomInfoId()).eq("project_type", projProjectInfo.getProjectType()));
            if (CollUtil.isNotEmpty(listType)) {
                ProjCustomVsProjectTypeEntity entity = listType.get(0);
                Boolean eFlag = true;
                if (entity.getCustomLevel() != null && !entity.getCustomLevel().isEmpty() && entity.getBranchNum() != null && !"".equals(entity.getBranchNum()) && entity.getSupervisorFlag() != null && !"".equals(entity.getSupervisorFlag())) {
                    eFlag = false;
                }
                return entity.getPreSubmitId() == null || entity.getPreSubmitId() == 0 || eFlag;
            }
        } catch (Exception e) {
            log.error("提前获取域名===" + e.getMessage());
        }
        return true;
    }


    /**
     * 说明: 校验产品准备工作是否全部完成
     *
     * @param projMilestoneInfo
     * @param survey
     * @return
     */
    private Boolean verifyPreparatStatisticsReport(ProjMilestoneInfo projMilestoneInfo, String survey) {
        int reportStatus;
        // 调研
        if ("survey".equals(survey)) {
            List<Integer> surveyStatisticsReport = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass("survey_statistics_report", 3);
            int countByProjectIdAndStatus = projStatisticalReportMainMapper.getCountByProjectIdAndStatus(projMilestoneInfo.getProjectInfoId(), surveyStatisticsReport);
            return countByProjectIdAndStatus > 0;
            // 准备
        } else if ("preparat".equals(survey)) {
            reportStatus = 22;
            // 下沉发布
        } else if ("release".equals(survey)) {
            reportStatus = 41;
        } else {
            return false;
        }
        List<ProjStatisticalReportMainEntity> projStatisticalReportMainList = projStatisticalReportMainMapper.selectListByProjectIdAndStatus(projMilestoneInfo.getProjectInfoId(), reportStatus);
        return projStatisticalReportMainList != null && !projStatisticalReportMainList.isEmpty();
    }

    /**
     * 说明: 校验表单制作是否完成
     *
     * @param projMilestoneInfo
     * @return
     */
    private Boolean verifyPreparatForm(ProjMilestoneInfo projMilestoneInfo) {
        Map<String, Object> mao = new HashMap<>();
        mao.put("projectInfoId", projMilestoneInfo.getProjectInfoId());
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projMilestoneInfo.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        Boolean isNewFlow = milestoneInfoList != null && milestoneInfoList.size() > 0 && milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent());
        Integer reportCount = 0;
        if (isNewFlow) {
            reportCount = projMilestoneInfoMapper.getFormCount(mao);
        }
        return reportCount > 0;
    }

    private Boolean verifyMedicalInsurance(ProjMilestoneInfo projMilestoneInfo) {
        ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(projMilestoneInfo.getProjectInfoId());
        List<ProjHospitalInfoVO> hospitalInfoByCustomeInfoId = hospitalInfoService.getHospitalInfoByCustomeInfoId(projProjectInfo.getCustomInfoId());
        if (CollectionUtils.isEmpty(hospitalInfoByCustomeInfoId)) {
            throw new IllegalArgumentException(String.format("没有根据客户ID获取到医院信息，customInfoId=%s", projProjectInfo.getCustomInfoId()));
        }
        ProjHospitalInfoVO hospitalInfoVO = hospitalInfoByCustomeInfoId.stream().filter(item -> Integer.valueOf(1).equals(item.getHealthBureauFlag())).findFirst().orElse(null);
        if (null == hospitalInfoVO) {
            throw new IllegalArgumentException(String.format("没有根据客户ID获取到主院信息，customInfoId=%s", projProjectInfo.getCustomInfoId()));
        }
        OnlineValidateParam onlineVerificationParam = new OnlineValidateParam();
        onlineVerificationParam.setCustomInfoId(projProjectInfo.getCustomInfoId());
        onlineVerificationParam.setHospitalInfoId(Long.valueOf(hospitalInfoVO.getHospitalInfoId()));
        return medicalInsuranceService.onlineVerification(onlineVerificationParam);
    }

    @Resource
    private DictProductService dictProductService;

    /**
     * 说明: 完成里程碑节点信息
     *
     * @param dto
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/5/31 9:58
     * @remark: Copyright
     */
    @Override
    public Result<Boolean> compSurveyProductMilestone(CompSurveyProductMilestone dto) {
        ProjMilestoneInfo milestoneInfo = projMilestoneInfoMapper.selectById(dto.getMilestoneInfoId());
        if (milestoneInfo == null) {
            return Result.fail("节点信息不匹配");
        }
        boolean openSurveyAudit = projProjectInfoService.isOpenSurveyAudit(dto.getProjectInfoId());
        if (openSurveyAudit) {
            ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
            projSurveyPlanDTO.setProjectInfoId(dto.getProjectInfoId());
            List<Long> notSurveyYyProductId = dictProductService.getNotSurveyYyProductId();
            List<SurveyPlanTaskResp> surveyPlan = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
            // 未完成状态
            List<Integer> unAuditStatusCode1 = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass("survey_product", 1);
            List<Integer> unAuditStatusCode2 = dictBusinessStatusService.getStatusIdByBusinessCodeAndStatusClass("survey_product", 2);
            List<Integer> unAuditStatusCode = new ArrayList<>();
            unAuditStatusCode.addAll(unAuditStatusCode1);
            unAuditStatusCode.addAll(unAuditStatusCode2);
            log.info("产品业务调研确认完成，未完成状态={}",JSON.toJSONString(unAuditStatusCode));
            if (!CollectionUtils.isEmpty(surveyPlan)) {
                List<Integer> collect = surveyPlan.stream().filter(item -> !notSurveyYyProductId.contains(item.getYyProductId())).map(SurveyPlanTaskResp::getCompleteStatus).collect(Collectors.toList());
                log.info("产品业务调研确认完成，调研计划状态={}",JSON.toJSONString(collect));
                boolean b = collect.stream().anyMatch(unAuditStatusCode::contains);
                if (b) {
                    return Result.fail("需要所有的调研计划全部提交后端审核之后才能确认完成");
                }
            }
        }

        // TODO 测试临时注释，记得删除
        Result result = verifySurveyProduct(dto.getProjectInfoId());
        if (!result.isSuccess()) {
            return result;
        }
        // 如果还没有生成待办，就生成待办，生成了待办的不再重新生成
        ConfirmFinalDataParam confirmFinalDataParam = new ConfirmFinalDataParam();
        confirmFinalDataParam.setProjectInfoId(dto.getProjectInfoId());
        confirmFinalDataParam.setSysUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
        Result b = surveyPlanService.saveBackLogAndDetailTest(confirmFinalDataParam);
        if (!Boolean.TRUE.equals(b.isSuccess())) {
            throw new IllegalArgumentException(b.getMsg());
        }
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setMilestoneInfoId(dto.getMilestoneInfoId());
        updateMilestoneDTO.setNodeHeadId(Global.getSysUserVO().getSysUserId());
        updateMilestoneDTO.setActualCompTime(new Date());
        int updated = projMilestoneInfoMapper.updateMilestone(updateMilestoneDTO);

        // 未开启后端审核
        if (!projectInfoService.isOpenSurveyAudit(milestoneInfo.getProjectInfoId())) {
            // 当前项目所有调研计划，更新为已完成
            List<ProjSurveyPlan> projSurveyPlans = projSurveyPlanMapper.selectList(
                    new QueryWrapper<ProjSurveyPlan>()
                            .eq("is_deleted", 0)
                            .eq("project_info_id", milestoneInfo.getProjectInfoId())
            );
            if (!CollectionUtils.isEmpty(projSurveyPlans)) {
                Date now = new Date();
                for (ProjSurveyPlan item : projSurveyPlans) {
                    ProjSurveyPlan updateParam = new ProjSurveyPlan();
                    updateParam.setSurveyPlanId(item.getSurveyPlanId());
                    updateParam.setCompleteStatus(1);
                    updateParam.setUpdateTime(now);
                    projSurveyPlanMapper.updateById(updateParam);
                }
            }
        }

        projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(milestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
        projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(milestoneInfo.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(milestoneInfo.getMilestoneNodeCode()), ProjectPlanStatusEnum.FINISHED);
        return Result.success(updated > 0);
    }

    /**
     * 查询报表是否都已完成
     *
     * @param projMilestoneInfo
     * @param projectPlanItemCode 1调研 2 准备
     * @return
     */
    private Boolean verifyPreparatReport(ProjMilestoneInfo projMilestoneInfo, Integer projectPlanItemCode) {
        Map<String, Object> mao = new HashMap<>();
        mao.put("projectInfoId", projMilestoneInfo.getProjectInfoId());
        mao.put("projectPlanItemCode", projectPlanItemCode);
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projMilestoneInfo.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_REPORT.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        Boolean isNewFlow = milestoneInfoList != null && milestoneInfoList.size() > 0 && milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent());
        Integer reportCount = 0;
        if (isNewFlow) {
            MilestoneNodeEnum nodeEnum = null;
            if (1 == projectPlanItemCode) {
                nodeEnum = MilestoneNodeEnum.SURVEY_REPORT;
            } else if (2 == projectPlanItemCode) {
                nodeEnum = MilestoneNodeEnum.PREPARAT_REPORT;
            }
            reportCount = projSurveyReportService.getReportCount(projMilestoneInfo.getProjectInfoId(), null, null, nodeEnum);
        } else {
            reportCount = projMilestoneInfoMapper.getOldReportCount(mao);
        }
        return reportCount > 0;
    }

    /**
     * 说明: 校验各产品准备工作是否全部完成
     *
     * @param projectInfoId
     * @param stage
     * @param researchStatus
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/6/5 11:25
     * @remark: Copyright
     */
    public Boolean verifyPreparatProduct(Long projectInfoId, Integer stage, Integer researchStatus) {
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        selectHospitalDTO.setStage(stage);
        selectHospitalDTO.setResearchStatus(researchStatus);
        Result<List<HospitalPlanVO>> hospitalInfos = hospitalInfoService.findHospitalPlan(selectHospitalDTO);
        return hospitalInfos.getData() != null && hospitalInfos.getData().size() != 0;
    }

    public Result verifySurveyProduct(Long projectInfoId) {
        //判断是否是后续分院模式
        Boolean flag = projProjectInfoService.isBranchHospital(projectInfoId);
        if (flag) {
            log.info("分院实施模式");
            //查询所有已分配的调研计划
            List<ProjSurveyPlan> needSurveyPlans = projSurveyPlanMapper.findBranchHospitalSurveyPlan(projectInfoId, NumberEnum.NO_0.num());
            log.info("分院实施模式，needSurveyPlans={}", JSON.toJSONString(needSurveyPlans));
            if (needSurveyPlans.size() > 0) {
                //产品拓展表不需要调研的
                List<ProjSurveyPlan> completeSurveyPlans = projSurveyPlanMapper.findNotNeedSurveyPlan();
                log.info("分院实施模式，completeSurveyPlans={}", JSON.toJSONString(completeSurveyPlans));
                List<Long> yyProductIds = needSurveyPlans.stream().map(ProjSurveyPlan::getYyProductId).collect(Collectors.toList());
                log.info("分院实施模式，yyProductIds={}", JSON.toJSONString(yyProductIds));
                //运营平台产品字典对照模块表不需要调研的
                List<Long> completeYyProducts = completeSurveyPlans.stream().map(ProjSurveyPlan::getYyProductId).collect(Collectors.toList());
                log.info("分院实施模式，completeYyProducts={}", JSON.toJSONString(completeYyProducts));
                List<DictProductVsModules> productVsModules = dictProductVsModulesMapper.selectList(new QueryWrapper<DictProductVsModules>().in("yy_product_id", yyProductIds).eq("is_deleted", 0).eq("need_survey", 0));
                log.info("分院实施模式，productVsModules={}", JSON.toJSONString(productVsModules));
                if (productVsModules.size() > 0) {
                    List<Long> yyProductIds1 = productVsModules.stream().map(DictProductVsModules::getYyProductId).collect(Collectors.toList());
                    log.info("分院实施模式，yyProductIds1={}", JSON.toJSONString(yyProductIds1));
                    yyProductIds.removeAll(yyProductIds1);
                }
                yyProductIds.removeAll(completeYyProducts);
                if (yyProductIds.size() > 0) {
                    //TODO 展示哪些产品没有完成
                    return Result.fail("调研计划未全部完成");
                }
            }
        } else {
            ProjSurveyPlanDTO dto = new ProjSurveyPlanDTO();
            dto.setProjectInfoId(projectInfoId);
            Result<SurveyPlanTaskRespVO> surveyPlanTask = surveyPlanService.findSurveyPlanTask(dto);
            log.info("不是分院实施模式，surveyPlan={}", JSON.toJSONString(surveyPlanTask));
            if (surveyPlanTask.isSuccess() && surveyPlanTask.getData().getSurveyPlanTaskResp() == null || surveyPlanTask.getData().getSurveyPlanTaskResp().isEmpty()) {
                return Result.success();
            }
            List<SurveyPlanTaskResp> surveyPlanTaskResp = surveyPlanTask.getData().getSurveyPlanTaskResp();
            log.info("不是分院实施模式，surveyPlanTaskResp={}", JSON.toJSONString(surveyPlanTaskResp));
            // 判断是否存在未完成调研的产品 【检查需要调研的产品是否存在未完成的数据】
            Boolean flag1 = surveyPlanTaskResp.stream().anyMatch(resp -> resp.getSurveyFlag() == 1 && Integer.valueOf(0).equals(resp.getCompleteStatus()));
            if (flag1) {
                //TODO 展示哪些产品没有完成
                return Result.fail("调研计划未全部完成");
            }

        }
        return Result.success();
    }

    /**
     * 通过阶段Id和项目Id查询里程碑节点信息
     *
     * @param projectInfoId
     * @param projectStageId
     * @return
     */
    public List<ProjMilestoneInfoVO> getMilestoneInfo(Long projectInfoId, Long projectStageId) {
        return projMilestoneInfoMapper.getMilestoneInfo(projectInfoId, projectStageId);
    }

    /**
     * 说明: 校验当前节点能否操作
     *
     * @param id
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/10 11:31
     * @remark: Copyright
     */
    @Override
    public Result verifyProjMilestoneInfoVO(Long id) {
        ProjMilestoneInfoVO proposalInfoVO = projMilestoneInfoMapper.getProjMilestoneInfoVOById(id);
        if (proposalInfoVO == null) {
            return Result.fail("节点信息不匹配");
        }
        //判断前直接点标识
        if (NumberEnum.NO_0.num().intValue() == proposalInfoVO.getPreNodeFlag()) {
            return Result.success(proposalInfoVO.getMilestoneNodeUrl());
        }
        ProjMilestoneInfo proposalInfo = new ProjMilestoneInfo();
        proposalInfo.setProjectInfoId(proposalInfoVO.getProjectInfoId());
        proposalInfo.setPreNodeId(proposalInfoVO.getPreNodeId());
        if (ObjectUtil.isNotEmpty(proposalInfoVO.getPreNodeId())) {
            List<String> userIds = Arrays.asList(proposalInfoVO.getPreNodeId().split(","));
            List<Long> proposalInfoList = userIds.stream().map(Long::parseLong).collect(Collectors.toList());
            List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", proposalInfoVO.getProjectInfoId()).in("milestone_node_id", proposalInfoList).eq("invalid_flag", NumberEnum.NO_0.num()));
            if (ObjectUtil.isEmpty(milestoneInfoList)) {
                return Result.fail("前置节点信息配置有误");
            }
            StringBuffer sb = new StringBuffer();
            milestoneInfoList.forEach(item -> {
                if (NumberEnum.NO_0.num().intValue() == item.getMilestoneStatus()) {
                    sb.append("【").append(item.getMilestoneNodeName()).append("】");
                }
            });
            if (ObjectUtil.isNotEmpty(sb)) {
                return Result.fail("请完成" + sb + "后再进行此操作！");
            }
        }
        return Result.success(proposalInfoVO.getMilestoneNodeUrl());
    }

    /**
     * 说明: 是否完成入驻
     *
     * @param projectInfoId
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/7/2 17:26
     * @remark: Copyright
     */
    @Override
    public Result verifyStageEntry(Long projectInfoId) {
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
        if (tmpProjectNewVsOld == null) {
            return Result.fail("项目信息不匹配");
        }
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfoId).eq("milestone_node_code", MilestoneNodeEnum.PREPARAT_CHECK.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        int count = oldTbProjectReadyWorkMapper.getCount(tmpProjectNewVsOld.getOldProjectInfoId(), projMilestoneInfo.getMilestoneInfoId());
        if (count == 0) {
            return Result.success(false);
        }
        return Result.success(true);
    }

    /**
     * 说明: 校验当前节点是否已经提交完成状态
     *
     * @param milestoneInfoId
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/8/28 9:36
     * @remark: Copyright
     */
    @Override
    public Boolean verifyMilestoneStatus(Long milestoneInfoId) {
        Long count = projMilestoneInfoMapper.selectCount(new QueryWrapper<ProjMilestoneInfo>().eq("milestone_info_id", milestoneInfoId).eq("milestone_status", NumberEnum.NO_1.num()).eq("invalid_flag", NumberEnum.NO_0.num()));
        return count > 0;
    }

    /**
     * 将参数提取到新的方法
     *
     * @param allList
     * @return
     */
    MileStoneInfoResp handleReq(List<ProjMilestoneInfoVO> allList) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        MileStoneInfoResp req = new MileStoneInfoResp();
        Date startTime;
        Date endTime;
        Long count = allList.stream().filter(e -> e.getMilestoneStatus().equals(MilestoneStatusEnum.COMPLETED.getCode())).count();
        startTime = allList.stream().sorted(Comparator.comparing(ProjMilestoneInfoVO::getCreateTime)).findFirst().get().getCreateTime();
        List<ProjMilestoneInfoVO> collect = allList.stream().filter(e -> e.getExpectCompTime() != null).collect(Collectors.toList());
        if (collect == null || collect.size() == 0) {
            endTime = null;
        } else {
            endTime = collect.stream().sorted(Comparator.comparing(ProjMilestoneInfoVO::getExpectCompTime).reversed()).findFirst().get().getExpectCompTime();
        }
        BigDecimal complete = BigDecimal.valueOf(count);
        BigDecimal all = BigDecimal.valueOf(allList.size());
        BigDecimal percent = complete.divide(all, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        req.setPercent(percent);
        if (endTime != null) {
            req.setPeriod(format.format(startTime) + "~" + format.format(endTime));
        } else {
            req.setPeriod(format.format(startTime) + "~");
        }
        return req;
    }

    /**
     * 通过项目id查询准备阶段和测试阶段的节点信息
     *
     * @param dto
     * @return
     */
    @Override
    public List<ProjMilestoneInfoVO> getMilestoneInfoByProjectId(SelectHospitalDTO dto) {
        List<ProjMilestoneInfoVO> info = projMilestoneInfoMapper.getMilestoneInfoByProjectId(dto);
        info.forEach(v -> {
            String start = DateUtil.formatDateTime(v.getExpectStartTime());
            String end = DateUtil.formatDateTime(v.getExpectCompTime());
            List<String> dateList = new ArrayList<>();
            dateList.add(start);
            dateList.add(end);
            v.setDateList(dateList);
        });
        return info;
    }

    @Override
    public Integer batchUpdateMilestoneInfo(List<MilestoneInfoDTO> list) {
        list.forEach(v -> {
            v.setUpdateTime(new Date());
            v.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            v.setExpectStartTime(DateUtil.beginOfDay(v.getExpectStartTime()));
            v.setExpectCompTime(DateUtil.endOfDay(v.getExpectCompTime()));
        });
        return projMilestoneInfoMapper.batchUpdateMilestoneInfo(list);
    }

    @Override
    public ProjMilestoneInfo getMilestoneInfo(Long projectInfoId, String code) {
        LambdaQueryWrapper<ProjMilestoneInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProjMilestoneInfo::getProjectInfoId, projectInfoId);
        lqw.eq(ProjMilestoneInfo::getMilestoneNodeCode, code);
        lqw.eq(ProjMilestoneInfo::getInvalidFlag, 0);
        return projMilestoneInfoMapper.selectOne(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean revertProjMilestoneInfoStatus(UpdateProjMilestoneInfoAfterAuditPmoParam param) {
        log.info("回退里程碑节点状态，参数={}", JSON.toJSONString(param));
        // 先获取新老项目对照关系
        QueryWrapper<TmpProjectNewVsOld> queryWrapper = new QueryWrapper<TmpProjectNewVsOld>().eq("old_project_info_id", param.getOldProjectId());
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(queryWrapper);
        log.info("回退里程碑节点状态，新老项目对照关系={}", JSON.toJSONString(tmpProjectNewVsOld));
        if (null == tmpProjectNewVsOld) {
            log.info("回退里程碑节点状态，新老项目对照关系不存在，老项目ID={}", param.getOldProjectId());
            return false;
        }
        SysUser sysUser = null;
        if (null != param.getYunYingUserId()) {
            sysUser = sysUserMapper.selectUserIdByYungyingId(String.valueOf(param.getYunYingUserId()));
        }
        // 根据新系统项目ID和里程碑节点编码获取里程碑信息
        ProjMilestoneInfo proposalInfo = new ProjMilestoneInfo();
        proposalInfo.setProjectInfoId(tmpProjectNewVsOld.getNewProjectInfoId());
        proposalInfo.setMilestoneNodeCode(param.getMilestoneNodeCode());
        ProjMilestoneInfoVO milestoneInfoVO = projMilestoneInfoMapper.getMilestoneInfoByParams(proposalInfo);
        log.info("回退里程碑节点状态，里程碑信息={}", JSON.toJSONString(milestoneInfoVO));
        // 更新条件
        UpdateWrapper<ProjMilestoneInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("milestone_info_id", milestoneInfoVO.getMilestoneInfoId());
        Date updateDate = new Date();
        ProjMilestoneInfo product = new ProjMilestoneInfo();
        product.setMilestoneStatus(Integer.valueOf("0"));
        product.setNodeHeadId(null);
        product.setUpdaterId(sysUser == null ? null : sysUser.getSysUserId());
        product.setUpdateTime(updateDate);
        product.setActualCompTime(updateDate);
        int update = projMilestoneInfoMapper.update(product, updateWrapper);
        return update > 0;
    }

    @Override
    public Result<Boolean> compMilestoneSpetial(UpdateMilestoneDTO dto) {
        ProjMilestoneInfo milestoneInfo = projMilestoneInfoMapper.selectById(dto.getMilestoneInfoId());
        if (milestoneInfo.getMilestoneStatus() == MilestoneStatusEnum.COMPLETED.getCode().intValue()) {
            log.warn("里程碑节点状态已完成, 无需再进行更新. dto: {}", dto);
            return Result.success(true);
        }
        List<ProjApplyOrder> applyOrders = applyOrderMapper.selectList(new QueryWrapper<ProjApplyOrder>().eq("project_info_id", milestoneInfo.getProjectInfoId()));
        if (CollUtil.isNotEmpty(applyOrders)) {
            log.warn("无法进行更新, 存在部署申请工单, 需在验收后进行更新即可. dto: {}", dto);
            return Result.success(true);
        }
        return compMilestone(dto);
    }

    /**
     * 根据id查询里程碑节点信息
     *
     * @param milestoneInfoId
     * @return
     */
    @Override
    public Result<ProjMilestoneInfo> selectById(Long milestoneInfoId) {
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(milestoneInfoId);
        return Result.success(projMilestoneInfo);
    }

    /**
     * 是否走项目计划
     *
     * @param updateType 6. 项目计划更新为已完成 7. 项目计划更新为未完成 8. 取消项目计划前置节点限制 9. 我的待办更新为已完成 10.我的待办更新为未完成
     * @return true-走项目计划；false-不走项目计划
     */
    private boolean isProjectPlan(Integer updateType) {
        if (Integer.valueOf(6).equals(updateType)) {
            return true;
        }
        if (Integer.valueOf(7).equals(updateType)) {
            return true;
        }
        if (Integer.valueOf(8).equals(updateType)) {
            return true;
        }
        if (Integer.valueOf(9).equals(updateType)) {
            return true;
        }
        return Integer.valueOf(10).equals(updateType);
    }

    /**
     * 更新项目里程碑节点产品信息
     *
     * @param updateProjectContentReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateProjectMilestoneProductData(UpdateProjectContentReq updateProjectContentReq) {
        if (this.isProjectPlan(updateProjectContentReq.getUpdateType())) {
            UpdateProjectPlanReq updateProjectPlanReq = new UpdateProjectPlanReq();
            updateProjectPlanReq.setCustomerInfoId(updateProjectContentReq.getCustomerInfoId());
            updateProjectPlanReq.setProjectInfoId(updateProjectContentReq.getProjectInfoId());
            updateProjectPlanReq.setProjectPlanId(updateProjectContentReq.getProjectPlanId());
            updateProjectPlanReq.setUpdateType(updateProjectContentReq.getUpdateType());
            updateProjectPlanReq.setOperatorAccount(updateProjectContentReq.getOperatorAccount());
            updateProjectPlanReq.setUpdateTime(updateProjectContentReq.getUpdateTime());
            updateProjectPlanReq.setRemark(updateProjectContentReq.getRemark());
            updateProjectPlanReq.setProjectFileId(updateProjectContentReq.getProjectFileId());
            updateProjectPlanReq.setTodoTaskIdList(updateProjectContentReq.getTodoTaskIdList());

            return projProjectPlanService.updateProjectPlanInfo(updateProjectPlanReq);
        }
        Result result = null;
        if (updateProjectContentReq.getUpdateType() != null) {
            if (ObjectUtil.isEmpty(updateProjectContentReq.getCustomerInfoId())) {
                result = Result.fail("客户id不能为空");
            } else if (ObjectUtil.isEmpty(updateProjectContentReq.getProjectInfoId())) {
                result = Result.fail("项目id不能为空");
            } else {
                switch (updateProjectContentReq.getUpdateType()) {
                    case 1:
                        // 更新里程碑状态：已完成
                        if (ObjectUtil.isEmpty(updateProjectContentReq.getUpdateTime())) {
                            result = Result.fail("更新时间不能为空");
                        }
                        break;
                    case 3:
                        if (StrUtil.isBlank(updateProjectContentReq.getOperatorAccount())) {
                            result = Result.fail("操作人帐号不能为空");
                        } else if (ObjectUtil.isEmpty(updateProjectContentReq.getUpdateTime())) {
                            result = Result.fail("更新时间不能为空");
                        }
                        break;
                    default:
                        break;
                }
                if (ObjectUtil.isNull(result)) {
                    switch (updateProjectContentReq.getUpdateType()) {
                        case 1:
                            // 更新产品状态 已完成
                            result = updateProductStatus(updateProjectContentReq, true);
                            break;
                        case 2:
                            // 更新产品状态 未完成
                            result = updateProductStatus(updateProjectContentReq, false);
                            break;
                        case 3:
                            // 更新里程碑状态：已完成
                            result = updateMilestoneStatus(updateProjectContentReq, true);
                            break;
                        case 4:
                            // 更新里程碑状态：未完成
                            result = updateMilestoneStatus(updateProjectContentReq, false);
                            break;
                        case 5:
                            // 取消前置节点限制
                            result = updateNodeStatus(updateProjectContentReq);
                            break;
                        default:
                            result = Result.fail("更新类型不存在");
                            break;
                    }
                }
            }
        } else {
            result = Result.fail("更新类型不能为空");
        }
        return result;
    }

    /**
     * 取消前置节点内容
     */
    private Result updateNodeStatus(UpdateProjectContentReq updateProjectContentReq) {
        if (ObjectUtil.isEmpty(updateProjectContentReq.getMilestoneInfoId())) {
            return Result.fail("里程碑节点id不能为空");
        }
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(updateProjectContentReq.getProjectInfoId());
        if (ObjectUtil.isEmpty(projectInfo)) {
            return Result.fail(String.format("查不到该项目信息（projectInfoId=%s）", updateProjectContentReq.getProjectInfoId()));
        }
        ProjCustomInfo customInfo = projCustomInfoService.selectByPrimaryKey(updateProjectContentReq.getCustomerInfoId());
        if (ObjectUtil.isEmpty(projectInfo)) {
            return Result.fail(String.format("查不到该客户信息（customerInfoId=%s）", updateProjectContentReq.getCustomerInfoId()));
        }
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(updateProjectContentReq.getMilestoneInfoId());
        projMilestoneInfo.setPreNodeId("");
        projMilestoneInfo.setPreNodeFlag(0);
        projMilestoneInfoMapper.updateById(projMilestoneInfo);
        // 操作日志信息
        ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(projectInfo.getProjectInfoId(), projectInfo.getCustomInfoId(), projMilestoneInfo.getMilestoneInfoId());
        manualUpdateLog.setOperateTitle("取消前置节点限制");
        manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
        List<String> operateContentList = new ArrayList<>();
        operateContentList.add(JSON.toJSONString(updateProjectContentReq));
        manualUpdateLog.setOperateContent(JSON.toJSONString(operateContentList));
        manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
        manualUpdateLog.setSyncYyStatusFlag(updateProjectContentReq.getSyncYunyingProjStatusFlag());
        manualUpdateLog.setCustomerName(customInfo.getCustomName());
        manualUpdateLog.setProjectName(projectInfo.getProjectName());
        manualUpdateLog.setMilestoneName(projMilestoneInfo.getMilestoneNodeName());
        manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
        projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
        return Result.success("操作成功");
    }

    /**
     * 更新里程碑节点状态
     * <p>
     * 里程碑更新成已完成/里程碑更新成未完成：将选择的里程碑节点完成状态更新成已完成/未完成，注意：如果更新的是每个阶段最后一个里程碑节点，需要同步更新阶段完成状态
     *
     * @param updateProjectContentReq
     * @param b
     * @return
     */
    private Result updateMilestoneStatus(UpdateProjectContentReq updateProjectContentReq, boolean b) {
        Result result = null;
        if (ObjectUtil.isEmpty(updateProjectContentReq.getMilestoneInfoId())) {
            result = Result.fail("里程碑节点id不能为空");
        } else if (ObjectUtil.isEmpty(updateProjectContentReq.getProjectInfoId())) {
            result = Result.fail("项目id不能为空");
        }
        ProjProjectInfo projectInfo = null;
        ProjMilestoneInfo projMilestoneInfo = null;
        ProjCustomInfo customInfo = null;
        if (result == null) {
            customInfo = projCustomInfoService.selectByPrimaryKey(updateProjectContentReq.getCustomerInfoId());
            if (ObjectUtil.isEmpty(customInfo)) {
                result = Result.fail(String.format("查不到该客户信息（customerInfoId=%s）", updateProjectContentReq.getCustomerInfoId()));
            }
        }
        if (result == null) {
            projectInfo = projProjectInfoService.selectByPrimaryKey(updateProjectContentReq.getProjectInfoId());
            if (ObjectUtil.isEmpty(projectInfo)) {
                result = Result.fail(String.format("查不到该项目信息（projectInfoId=%s）", updateProjectContentReq.getProjectInfoId()));
            } else {
                projMilestoneInfo = projMilestoneInfoMapper.selectById(updateProjectContentReq.getMilestoneInfoId());
            }
        }
        if (result != null) {
            return result;
        }
        // 操作日志信息
        ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(projectInfo.getProjectInfoId(), projectInfo.getCustomInfoId(), projMilestoneInfo.getMilestoneInfoId());
        manualUpdateLog.setOperateTitle(String.format("里程碑更新成%s", b ? "已完成" : "未完成"));
        manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
        manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
        manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
        manualUpdateLog.setSyncYyStatusFlag(updateProjectContentReq.getSyncYunyingProjStatusFlag());
        manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
        manualUpdateLog.setCustomerName(customInfo.getCustomName());
        manualUpdateLog.setProjectName(projectInfo.getProjectName());
        manualUpdateLog.setMilestoneName(projMilestoneInfo.getMilestoneNodeName());
        manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
        if (projMilestoneInfo != null) {
            SysUser operatorSysUser = sysUserMapper.selectByAccount(updateProjectContentReq.getOperatorAccount());
            // 里程碑状态是完成状态的操作帐号必须
            if (ObjectUtil.isEmpty(operatorSysUser) && b) {
                return Result.fail(String.format("查不到该帐号的用户信息：%s", updateProjectContentReq.getOperatorAccount()));
            }
            // 更新里程碑节点状态与时间
            projMilestoneInfo.setMilestoneStatus(b ? MilestoneStatusEnum.COMPLETED.getCode().intValue() : MilestoneStatusEnum.UN_COMPLETE.getCode().intValue());
            projMilestoneInfo.setActualCompTime(b ? updateProjectContentReq.getUpdateTime() : null);
            projMilestoneInfo.setNodeHeadId(b ? operatorSysUser.getSysUserId() : null);
            LambdaUpdateWrapper<ProjMilestoneInfo> mileUpdateWrapper = new LambdaUpdateWrapper<>();
            mileUpdateWrapper.set(ObjectUtil.isNull(projMilestoneInfo.getActualCompTime()), ProjMilestoneInfo::getActualCompTime, projMilestoneInfo.getActualCompTime());
            mileUpdateWrapper.set(ObjectUtil.isNull(projMilestoneInfo.getNodeHeadId()), ProjMilestoneInfo::getNodeHeadId, projMilestoneInfo.getNodeHeadId());
            mileUpdateWrapper.eq(ProjMilestoneInfo::getMilestoneInfoId, projMilestoneInfo.getMilestoneInfoId());
            projMilestoneInfoMapper.update(projMilestoneInfo, mileUpdateWrapper);
            // 更新完里程碑记录日志
            projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
            // 更新项目主表状态与相关完成时间
            refreshProjDeliveryStatusFromMilestone(projectInfo.getProjectInfoId());
            projectInfo = projProjectInfoService.selectByPrimaryKey(updateProjectContentReq.getProjectInfoId());
            // 同步运营平台项目状态
            if (b && NumberEnum.NO_1.num().equals(updateProjectContentReq.getSyncYunyingProjStatusFlag())) {
                // 获取项目工单信息
                ProjOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(projectInfo.getOrderInfoId());
                if (orderInfo == null) {
                    projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
                    return Result.fail(String.format("同步运营平台项目状态时，查不到该项目的工单信息（projectInfoId=%s, orderInfoId=%s）", updateProjectContentReq.getProjectInfoId(), projectInfo.getOrderInfoId()));
                }
                // 获取项目合同信息
                ProjContractInfo contractInfo = contractInfoMapper.selectByPrimaryKey(orderInfo.getContractInfoId());
                if (orderInfo == null) {
                    projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
                    return Result.fail(String.format("同步运营平台项目状态时，查不到该项目的合同信息（projectInfoId=%s, orderInfoId=%s, " + "contractInfoId=%s）", updateProjectContentReq.getProjectInfoId(), projectInfo.getOrderInfoId(), orderInfo.getContractInfoId()));
                }
                // 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动
                MilestoneNodeEnum optMilestoneNodeEnum = MilestoneNodeEnum.getByCode(projMilestoneInfo.getMilestoneNodeCode());
                Integer projectDeliverStatus = COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.get(optMilestoneNodeEnum);
                OrderStepEnum yyOrderStep = null;
                switch (orderInfo.getDeliveryOrderType()) {
                    case 1:// 软件
                        if (NumberEnum.NO_2.num().equals(projectDeliverStatus)) {
                            // 2已调研
                            yyOrderStep = OrderStepEnum.SOFTWARE_SURVEY;
                        } else if (NumberEnum.NO_3.num().equals(projectDeliverStatus)) {
                            // 3已入驻
                            yyOrderStep = OrderStepEnum.SOFTWARE_ON_SITE;
                        } else if (NumberEnum.NO_4.num().equals(projectDeliverStatus)) {
                            // 4、准备完成
                            yyOrderStep = OrderStepEnum.SOFTWARE_TEST;
                        } else if (NumberEnum.NO_5.num().equals(projectDeliverStatus)) {
                            // 5已上线
                            yyOrderStep = OrderStepEnum.SOFTWARE_ONLINE;
                        } else if (NumberEnum.NO_6.num().equals(projectDeliverStatus)) {
                            // 6已验收
                            yyOrderStep = OrderStepEnum.SOFTWARE_CHECK;
                        }
                        break;
                    case 8:// 外采软件
                        if (NumberEnum.NO_2.num().equals(projectDeliverStatus)) {
                            // 2已调研
                            yyOrderStep = OrderStepEnum.PURCHASE_SOFTWARE_SURVEY;
                        } else if (NumberEnum.NO_3.num().equals(projectDeliverStatus)) {
                            // 3已入驻
                            yyOrderStep = OrderStepEnum.PURCHASE_SOFTWARE_ON_SITE;
                        } else if (NumberEnum.NO_4.num().equals(projectDeliverStatus)) {
                            // 4、准备完成
                            yyOrderStep = OrderStepEnum.PURCHASE_SOFTWARE_TEST;
                        } else if (NumberEnum.NO_5.num().equals(projectDeliverStatus)) {
                            // 5已上线
                            yyOrderStep = OrderStepEnum.PURCHASE_SOFTWARE_ONLINE;
                        } else if (NumberEnum.NO_6.num().equals(projectDeliverStatus)) {
                            // 6已验收
                            yyOrderStep = OrderStepEnum.PURCHASE_SOFTWARE_CHECK;
                        }
                        break;
                    default:
                        break;
                }
                if (!ObjectUtil.isEmpty(yyOrderStep)) {
                    UpdateNodeStatusDTO updateNodeStatusDTO = new UpdateNodeStatusDTO();
                    updateNodeStatusDTO.setStartDate(DateUtil.formatDateTime(updateProjectContentReq.getUpdateTime()));
                    updateNodeStatusDTO.setEndDate(DateUtil.formatDate(updateProjectContentReq.getUpdateTime()));
                    updateNodeStatusDTO.setContractNum(contractInfo.getYyContractId());
                    updateNodeStatusDTO.setProjectNum(orderInfo.getYyProjectNumber());
                    updateNodeStatusDTO.setWorkOrderId(orderInfo.getYyOrderId());
                    updateNodeStatusDTO.setStepId(yyOrderStep.getStepId().toString());
                    updateNodeStatusDTO.setPushFlag("1");
                    // 节点信息
                    NodeBaseData nodeBaseData = new NodeBaseData(operatorSysUser.getUserYunyingId(), operatorSysUser.getUserName(), yyOrderStep.getOperDesc(), DateUtil.formatDateTime(updateProjectContentReq.getUpdateTime()));
                    //产品信息
                    List<ProductReq> productReqList = orderProductMapper.getYyProductByOrderInfoId(orderInfo.getOrderInfoId());
                    //软件项目数量默认1
                    productReqList.stream().forEach(a -> a.setNumber(1));
                    nodeBaseData.setProduct(productReqList);
                    updateNodeStatusDTO.setData(Collections.singletonList(nodeBaseData));
                    log.info("手动更新运营平台工单状态接口信息: {}", JSON.toJSONString(updateNodeStatusDTO));
                    // 调用运营平台项目里程碑状态更新接口
                    String yySyncResult = yunyingFeignClient.updateNodeStatus(operatorSysUser.getAccount(), updateNodeStatusDTO);
                    manualUpdateLog.setYyApiRequest(JSON.toJSONString(new Object[]{operatorSysUser.getAccount(), updateNodeStatusDTO}));
                    manualUpdateLog.setYyApiResponse(yySyncResult);
                    JSONObject yySyncResultJson = JSON.parseObject(yySyncResult);
                    if (!"200".equals(yySyncResultJson.getString("code"))) {
                        projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
                        throw new CustomException(String.format("同步运营平台项目状态失败：%s,%s", yySyncResultJson.getString("msg"), "请核对运营平台是否有对应工单数据或者此节点相关信息是否已同步"));
                    }
                } else {
                    log.info(String.format("同步运营平台项目状态时，运营平台项目状态对照为空（projectInfoId=%s, projectStageCode=%s, " + "deliveryOrderType=%s）", updateProjectContentReq.getProjectInfoId(), projMilestoneInfo.getProjectStageCode(), orderInfo.getDeliveryOrderType()));
                }
            }
        }
//        projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
        return Result.success();
    }

    /**
     * 更新项目状态
     *
     * @param projMilestoneInfo
     */
    private void updateProjectStageStatus(ProjMilestoneInfo projMilestoneInfo) {
        // 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projMilestoneInfo.getProjectInfoId());
        if (projectInfo != null) {
            if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                projectInfo.setSurveyCompleteTime(projMilestoneInfo.getActualCompTime());
                projectInfo.setProjectDeliverStatus(NumberEnum.NO_2.num());
            } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                projectInfo.setSettleInTime(projMilestoneInfo.getActualCompTime());
                projectInfo.setProjectDeliverStatus(NumberEnum.NO_3.num());
            } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                projectInfo.setPreCompleteTime(projMilestoneInfo.getActualCompTime());
                projectInfo.setProjectDeliverStatus(NumberEnum.NO_4.num());
            } else if (ProjectStageCodeEnum.STAGE_TEST.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                projectInfo.setOnlineTime(projMilestoneInfo.getActualCompTime());
                projectInfo.setProjectDeliverStatus(NumberEnum.NO_5.num());
            } else if (ProjectStageCodeEnum.STAGE_ACCEPT.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                projectInfo.setAcceptTime(projMilestoneInfo.getActualCompTime());
                projectInfo.setProjectDeliverStatus(NumberEnum.NO_6.num());
            }
            projectInfo.setUpdateTime(new Date());
            projProjectInfoService.updateByPrimaryKey(projectInfo);
        }
    }

    /**
     * 根据项目所有里程碑状态刷新项目主表交付状态及各关键节点完成时间
     *
     * @param projectInfoId
     * @return void
     */
    private void refreshProjDeliveryStatusFromMilestone(Long projectInfoId) {
        boolean isValid = !ObjectUtil.isEmpty(projectInfoId);
        ProjProjectInfo projectInfo = null;
        List<ProjMilestoneInfo> milestoneList = null;
        if (isValid) {
            projectInfo = projProjectInfoService.selectByPrimaryKey(projectInfoId);
            if (ObjectUtil.isEmpty(projectInfo)) {
                isValid = false;
            }
        }
        if (isValid) {
            milestoneList = getProjMileStoneInfoByProjectId(projectInfoId);
            if (CollUtil.isEmpty(milestoneList)) {
                isValid = false;
            }
        }
        if (!isValid) {
            return;
        }
        Integer deliveryStatus = null;
        for (MilestoneNodeEnum milestoneNodeEnum : COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.keyList()) {
            ProjMilestoneInfo milestoneInfo = getMilestoneStatusCompleteFromList(milestoneList, milestoneNodeEnum);
            if (deliveryStatus == null && milestoneInfo != null) {
                deliveryStatus = COMPLETE_MILESTONE_NODE_DELIVERY_STATUS_MAP.get(milestoneNodeEnum);
                // 设置当前项目交付状态
                projectInfo.setProjectDeliverStatus(deliveryStatus);
            }
            // 重新计算当前项目各关键环节的完成时间
            // 里程碑状态：0.未完成；1.已完成
            if (!ObjectUtil.isEmpty(milestoneInfo) && NumberEnum.NO_1.num().equals(milestoneInfo.getMilestoneStatus())) {
                if (MilestoneNodeEnum.SURVEY_SUMMARY.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setSurveyCompleteTime(milestoneInfo.getActualCompTime());
                } else if (MilestoneNodeEnum.SETTLED_PMO_AUDIT.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setSettleInTime(milestoneInfo.getActualCompTime());
                } else if (MilestoneNodeEnum.PREPARAT_CHECK.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setPreCompleteTime(milestoneInfo.getActualCompTime());
                } else if (MilestoneNodeEnum.PROJECT_LAUNCH.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setOnlineTime(milestoneInfo.getActualCompTime());
                } else if (MilestoneNodeEnum.PROJECT_ACCEPT.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setAcceptTime(milestoneInfo.getActualCompTime());
                }
            } else if (ObjectUtil.isEmpty(milestoneInfo) || NumberEnum.NO_0.num().equals(milestoneInfo.getMilestoneStatus())) {
                if (MilestoneNodeEnum.SURVEY_SUMMARY.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setSurveyCompleteTime(null);
                } else if (MilestoneNodeEnum.SETTLED_PMO_AUDIT.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setSettleInTime(null);
                } else if (MilestoneNodeEnum.PREPARAT_CHECK.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setPreCompleteTime(null);
                } else if (MilestoneNodeEnum.PROJECT_LAUNCH.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setOnlineTime(null);
                } else if (MilestoneNodeEnum.PROJECT_ACCEPT.getCode().equals(milestoneNodeEnum.getCode())) {
                    projectInfo.setAcceptTime(null);
                }
            }
        }
        // 更新projectInfo表
        projectInfo.setUpdateTime(new Date());
        projProjectInfoService.updateByPrimaryKey(projectInfo);
    }

    private ProjMilestoneInfo getMilestoneStatusCompleteFromList(List<ProjMilestoneInfo> milestoneList, MilestoneNodeEnum milestoneNodeEnum) {
        if (CollUtil.isEmpty(milestoneList)) {
            return null;
        }
        return milestoneList.stream().filter(d -> milestoneNodeEnum.getCode().equals(d.getMilestoneNodeCode()) && NumberEnum.NO_1.num().equals(d.getMilestoneStatus())).findFirst().orElse(null);
    }

    /***
     * 产品更新成已完成/产品更新成未完成：主要更新调研阶段和准备阶段产品的完成状态
     * @param updateProjectContentReq
     * @param b
     * @return
     */
    private Result updateProductStatus(UpdateProjectContentReq updateProjectContentReq, boolean b) {
        Result result = null;
        if (ObjectUtil.isEmpty(updateProjectContentReq.getProductId())) {
            result = Result.fail("产品id不能为空");
        } else if (ObjectUtil.isEmpty(updateProjectContentReq.getMilestoneInfoId())) {
            result = Result.fail("里程碑节点id不能为空");
        }
        ProjCustomInfo customInfo = null;
        ProjProjectInfo projectInfo = null;
        if (result == null) {
            customInfo = projCustomInfoService.selectByPrimaryKey(updateProjectContentReq.getCustomerInfoId());
            if (ObjectUtil.isEmpty(customInfo)) {
                result = Result.fail(String.format("查不到该客户信息（customerInfoId=%s）", updateProjectContentReq.getCustomerInfoId()));
            }
        }
        if (result == null) {
            projectInfo = projProjectInfoService.selectByPrimaryKey(updateProjectContentReq.getProjectInfoId());
            if (ObjectUtil.isEmpty(projectInfo)) {
                result = Result.fail(String.format("查不到该项目信息（projectInfoId=%s）", updateProjectContentReq.getProjectInfoId()));
            }
        }
        String dictProduct = null;
        if (result == null) {
            dictProduct = dictProductService.getProductNameByYyProductId(updateProjectContentReq.getProductId());
            if (org.apache.commons.lang3.StringUtils.isBlank(dictProduct)) {
                result = Result.fail(String.format("查不到该产品信息（productId=%s）", updateProjectContentReq.getProductId()));
            }
        }
        if (result != null) {
            return result;
        }
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(updateProjectContentReq.getMilestoneInfoId());
        if (projMilestoneInfo != null) {
            if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                // 更新调研阶段产品状态为已完成
                List<ProjSurveyPlan> respList = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().eq("yy_product_id", updateProjectContentReq.getProductId()).eq("is_deleted", 0).eq("project_info_id", updateProjectContentReq.getProjectInfoId()));
                if (CollUtil.isNotEmpty(respList)) {
                    respList.forEach(item -> {
                        item.setCompleteStatus(b ? 1 : 0);
                        item.setUpdateTime(new Date());
                        item.setActualCompTime(b ? ObjectUtil.defaultIfNull(updateProjectContentReq.getUpdateTime(), new Date()) : null);
                        LambdaUpdateWrapper<ProjSurveyPlan> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(ObjectUtil.isNull(item.getActualCompTime()), ProjSurveyPlan::getActualCompTime, item.getActualCompTime());
                        updateWrapper.eq(ProjSurveyPlan::getSurveyPlanId, item.getSurveyPlanId());
                        projSurveyPlanMapper.update(item, updateWrapper);
                    });
                }
            } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(projMilestoneInfo.getProjectStageCode())) {
                List<ProjProductBacklog> respList = projProductBacklogMapper.selectByDataParamer(updateProjectContentReq);
                if (CollUtil.isNotEmpty(respList)) {
                    respList.forEach(item -> {
                        item.setUpdateTime(ObjectUtil.defaultIfNull(updateProjectContentReq.getUpdateTime(), new Date()));
                        item.setBaseDataStatus(item.getBaseDataStatus() != 0 && b ? 2 : item.getBaseDataStatus() != 0 && !b ? 1 : 0);
                        item.setConfigDataStatus(item.getConfigDataStatus() != 0 && b ? 2 : item.getConfigDataStatus() != 0 && !b ? 1 : 0);
                        item.setTodoTaskStatus(item.getTodoTaskStatus() != 0 && b ? 2 : item.getTodoTaskStatus() != 0 && !b ? 1 : 0);
                        item.setFormDataStatus(item.getFormDataStatus() != 0 && b ? 2 : item.getFormDataStatus() != 0 && !b ? 1 : 0);
                        item.setReportDataStatus(item.getReportDataStatus() != 0 && b ? 2 : item.getReportDataStatus() != 0 && !b ? 1 : 0);
                        projProductBacklogMapper.updateById(item);
                    });
                }
                updateProjectContentReq.setUpdateType(b ? 1 : 0);
                projProductBacklogMapper.updateMilstoneData(updateProjectContentReq);
            } else {
                return Result.fail("更新产品时必须选择调研阶段或准备阶段的里程碑节点");
            }
        } else {
            return Result.fail("里程碑节点不存在");
        }
        // 操作日志信息
        ProjMilestoneManualUpdateLog manualUpdateLog = buildCurrentUserMilestoneManualOptLogEntity(projectInfo.getProjectInfoId(), projectInfo.getCustomInfoId(), projMilestoneInfo.getMilestoneInfoId());
        manualUpdateLog.setCustomerName(customInfo.getCustomName());
        manualUpdateLog.setProjectName(projectInfo.getProjectName());
        manualUpdateLog.setMilestoneName(projMilestoneInfo.getMilestoneNodeName());
        manualUpdateLog.setProductId(updateProjectContentReq.getProductId());
        manualUpdateLog.setProductName(dictProduct);
        manualUpdateLog.setOperateTitle(String.format("更新产品成%s", b ? "已完成" : "未完成"));
        manualUpdateLog.setOperateContent(JSON.toJSONString(updateProjectContentReq));
        manualUpdateLog.setUpdateTime(updateProjectContentReq.getUpdateTime());
        manualUpdateLog.setProjStatusOperatorAccount(updateProjectContentReq.getOperatorAccount());
        manualUpdateLog.setSyncYyStatusFlag(updateProjectContentReq.getSyncYunyingProjStatusFlag());
        manualUpdateLog.setRemark(updateProjectContentReq.getRemark());
        manualUpdateLog.setProjectFileId(updateProjectContentReq.getProjectFileId());
        projMilestoneManualUpdateLogMapper.insert(manualUpdateLog);
        return Result.success("操作成功");
    }

    /**
     * 构建基于当前登录用户的里程碑手动操作日志实体
     *
     * @param projectInfoId
     * @param customInfoId
     * @param milestoneInfoId
     * @return com.msun.csm.dao.entity.proj.ProjMilestoneManualUpdateLog
     */
    private ProjMilestoneManualUpdateLog buildCurrentUserMilestoneManualOptLogEntity(Long projectInfoId, Long customInfoId, Long milestoneInfoId) {
        ProjMilestoneManualUpdateLog manualUpdateLog = new ProjMilestoneManualUpdateLog();
        manualUpdateLog.setProjMilestoneManualUpdateLogId(SnowFlakeUtil.getId());
        SysUserVO currentUser = userHelper.getCurrentUser();
        manualUpdateLog.setCreaterId(currentUser.getSysUserId());
        manualUpdateLog.setUpdaterId(currentUser.getSysUserId());
        manualUpdateLog.setCreateTime(new Date());
        manualUpdateLog.setUpdateTime(new Date());
        manualUpdateLog.setOperaterUserId(currentUser.getSysUserId());
        manualUpdateLog.setOperaterUserName(currentUser.getUserName());
        manualUpdateLog.setOperateTime(new Date());
        manualUpdateLog.setOperateTitle("");
        manualUpdateLog.setOperateContent("");
        manualUpdateLog.setProjectInfoId(projectInfoId);
        manualUpdateLog.setCustomerInfoId(customInfoId);
        manualUpdateLog.setMilestoneInfoId(milestoneInfoId);
        manualUpdateLog.setProductId(null);
        manualUpdateLog.setProjStatusOperatorAccount(null);
        manualUpdateLog.setSyncYyStatusFlag(null);
        manualUpdateLog.setYyApiRequest(null);
        manualUpdateLog.setYyApiResponse(null);
        return manualUpdateLog;
    }

    /**
     * 查询项目表里面的里程碑节点信息
     *
     * @param updateProjectContentReq
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> getMilstoneAndProductParamer(UpdateProjectContentReq updateProjectContentReq) {
        List<BaseIdNameResp> milstoneReturnList = new ArrayList<>();
        // 项目里程碑信息
        ProjProjectInfo info = projProjectInfoService.selectByPrimaryKey(updateProjectContentReq.getProjectInfoId());
        if (info != null) {
            ProjMilestoneInfoDTO dto = new ProjMilestoneInfoDTO();
            dto.setProjectInfoId(updateProjectContentReq.getProjectInfoId());
            Result<MileStoneInfoResp> listResult = generateMilestoneInfo(dto);
            List<DictProjectStageVO> stageVOList = listResult.getData().getStageVOList();
            List<ProjMilestoneInfoVO> voList = new ArrayList<>();
            stageVOList.forEach(item -> {
                voList.addAll(item.getVoList());
            });
            if (voList != null && voList.size() > 0) {
                milstoneReturnList = voList.stream().map(v -> {
                    BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
                    baseIdNameResp.setId(v.getMilestoneInfoId());
                    baseIdNameResp.setName(v.getMilestoneNodeName());
                    return baseIdNameResp;
                }).collect(Collectors.toList());
            }
        }
        return Result.success(milstoneReturnList);
    }

    /**
     * 通过项目id去查询当前项目下生成的项目表里面的里程碑节点信息
     *
     * @param projectInfoId
     * @return
     */
    List<ProjMilestoneInfo> getProjMileStoneInfoByProjectId(Long projectInfoId) {
        LambdaQueryWrapper<ProjMilestoneInfo> lqw = new LambdaQueryWrapper();
        lqw.eq(ProjMilestoneInfo::getProjectInfoId, projectInfoId);
        lqw.eq(ProjMilestoneInfo::getInvalidFlag, NumberEnum.NO_0.num());
        return projMilestoneInfoMapper.selectList(lqw);
    }

    /**
     * 通过项目id作废节点信息
     *
     * @param projectInfoId
     * @return
     */
    Integer delMileStoneInfoByProjectId(Long projectInfoId) {
        LambdaUpdateWrapper<ProjMilestoneInfo> luw = new LambdaUpdateWrapper<>();
        luw.eq(ProjMilestoneInfo::getProjectInfoId, projectInfoId).eq(ProjMilestoneInfo::getInvalidFlag, NumberEnum.NO_0.num());
        ProjMilestoneInfo info = new ProjMilestoneInfo();
        info.setInvalidFlag(NumberEnum.NO_1.num());
        return projMilestoneInfoMapper.update(info, luw);
    }

    /**
     * 获取项目里程碑手动更新操作日志的分页列表
     */
    @Override
    public Result<PageInfo<ProjMilestoneManualUpdateLog>> queryMilestoneStatusOperateLogPageList(ProjMilestoneManualUpdateLogQueryDto queryDto) {
        if (ObjectUtil.isNull(queryDto)) {
            queryDto = new ProjMilestoneManualUpdateLogQueryDto();
        }
        if (ObjectUtil.isNull(queryDto.getPageNum()) || queryDto.getPageNum() < 1) {
            queryDto.setPageNum(1);
        }
        if (ObjectUtil.isNull(queryDto.getPageSize()) || queryDto.getPageSize() < 1) {
            queryDto.setPageSize(10);
        }
        LambdaQueryWrapper<ProjMilestoneManualUpdateLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(queryDto.getCustomerInfoId()), ProjMilestoneManualUpdateLog::getCustomerInfoId, queryDto.getCustomerInfoId());
        queryWrapper.eq(ObjectUtil.isNotNull(queryDto.getProjectInfoId()), ProjMilestoneManualUpdateLog::getProjectInfoId, queryDto.getProjectInfoId());
        queryWrapper.eq(ObjectUtil.isNotNull(queryDto.getMilestoneInfoId()), ProjMilestoneManualUpdateLog::getMilestoneInfoId, queryDto.getMilestoneInfoId());
        queryWrapper.orderByDesc(ProjMilestoneManualUpdateLog::getCreateTime);
        return PageHelperUtil.queryPage(queryDto.getPageNum(), queryDto.getPageSize(), page -> {
            List<ProjMilestoneManualUpdateLog> queryList = projMilestoneManualUpdateLogMapper.selectList(queryWrapper);
            List<Long> listFileIds = new ArrayList<>();
            if (queryList != null && queryList.size() > 0) {
                for (ProjMilestoneManualUpdateLog log : queryList) {
                    if (!listFileIds.contains(log.getProjectFileId())) {
                        listFileIds.add(log.getProjectFileId());
                    }
                }
                if (listFileIds != null && listFileIds.size() > 0) {
                    LambdaQueryWrapper<ProjProjectFile> queryFileWrapper = new LambdaQueryWrapper<>();
                    queryFileWrapper.in(ProjProjectFile::getProjectFileId, listFileIds);
                    List<ProjProjectFile> fileList = projProjectFileMapper.selectList(queryFileWrapper);
                    if (fileList != null && fileList.size() > 0) {
                        fileList.forEach(item -> {
                            item.setFilePath(item.getFilePath().replace("\\", "/"));
                            if (ObjectUtil.isNotEmpty(item)) {
                                item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY));
                            }
                        });
                        Map<Long, ProjProjectFile> fileMap = fileList.parallelStream().filter(projProjectFile -> projProjectFile.getProjectFileId() != null).collect(Collectors.toMap(ProjProjectFile::getProjectFileId, projProjectFile -> projProjectFile, (existingValue, newValue) -> existingValue // 覆盖重复键值
                        ));
                        for (ProjMilestoneManualUpdateLog log : queryList) {
                            if (fileMap.containsKey(log.getProjectFileId())) {
                                log.setProjectFile(fileMap.get(log.getProjectFileId()));
                            }
                        }
                    }
                }
            }
            PageInfo<ProjMilestoneManualUpdateLog> listPage = PageInfo.of(queryList);
            listPage.setPageNum(page.getPageNum());
            listPage.setPageSize(page.getPageSize());
            listPage.setTotal(page.getTotal());
            return Result.success(listPage);
        });
    }

    /**
     * 查询当前项目状态，校验当前节点可进行的操作
     * * 改为判断已入驻并且已部署的，才允许进行操作，否则只能查看
     * 已部署未入驻
     * 已入驻未部署
     * 未入驻未部署
     * 已入驻并且已部署
     *
     * @param id
     * @return
     */
    @Override
    public Result<Integer> verifyProjMilestoneInfoIsCanUseType(Long id) {
        // 是否入驻： 1是 0否
        Integer setteFlag = 0;
        // 是否部署  1是 0否
        Integer applyFlag = 0;
        if (id == null) {
            throw new CustomException("项目id不能为传空");
        }
        ProjProjectInfo proposalInfoVO = projectInfoMapper.selectById(id);
        if (proposalInfoVO == null || proposalInfoVO.getProjectInfoId() == null) {
            throw new CustomException("未查询到项目信息，入参：" + id);
        }
        boolean planModel = projectConfigService.isPlanModel(proposalInfoVO.getProjectInfoId());
        // 前后端模式
        if (planModel) {
            QueryProjectPlanParam queryProjectPlanParam = new QueryProjectPlanParam(proposalInfoVO.getProjectInfoId(), null);
            List<ProjProjectPlan> list = projProjectPlanBaseService.getProjectPlanByProjectInfoId(queryProjectPlanParam);
            if (list != null && !list.isEmpty()) {
                for (ProjProjectPlan info : list) {
                    // 获取入驻状态
                    if (info.getProjectPlanItemId().equals(DictProjectPlanItemEnum.SETTLED_PMO_AUDIT.getPlanItemId())) {
                        if (info.getStatus() == 1) {
                            setteFlag = 1;
                        }
                    }
                    // 获取部署状态
                    if (info.getProjectPlanItemId().equals(DictProjectPlanItemEnum.CLOUD_RESOURCE.getPlanItemId()) || info.getProjectPlanItemId().equals(DictProjectPlanItemEnum.PRODUCT_IMPOWER.getPlanItemId())) {
                        if (info.getStatus() == 1) {
                            applyFlag = 1;
                        }
                    }

                }
            }
        } else {
            // 非前后端模式
            List<ProjMilestoneInfo> listli = getProjMileStoneInfoByProjectId(proposalInfoVO.getProjectInfoId());
            if (listli != null && !listli.isEmpty()) {
                for (ProjMilestoneInfo info : listli) {
                    // 获取入驻状态
                    if (info.getMilestoneNodeCode().equals(MilestoneNodeEnum.SETTLED_PMO_AUDIT.getCode())) {
                        if (info.getMilestoneStatus() == 1) {
                            setteFlag = 1;
                        }
                    }
                    // 获取部署状态
                    if (info.getMilestoneNodeCode().equals(MilestoneNodeEnum.CLOUD_RESOURCE.getCode()) || info.getMilestoneNodeCode().equals(MilestoneNodeEnum.PRODUCT_IMPOWER.getCode())) {
                        if (info.getMilestoneStatus() == 1) {
                            applyFlag = 1;
                        }
                    }

                }
            }
        }
        // 根据入驻和部署状态返回不同的结果
        int result = 0;
        if (setteFlag == 1 && applyFlag == 1) {
            // 已部署已入驻
            result = 1;
        } else if (setteFlag == 0 && applyFlag == 1) {
            // 已部署未入驻
            result = 2;
        } else if (setteFlag == 0 && applyFlag == 0) {
            // 未部署未入驻
            result = 0;
        } else if (setteFlag == 1 && applyFlag == 0) {
            // 未部署已入驻
            result = 3;
        }
        return Result.success(result);
    }


    // 将 List<String> 按照每6个一组进行分组
    public static List<List<String>> splitList(List<String> originalList, int groupSize) {
        List<List<String>> groupedList = new ArrayList<>();

        // 遍历原始列表，将其拆分成子列表
        for (int i = 0; i < originalList.size(); i += groupSize) {
            int end = Math.min(i + groupSize, originalList.size()); // 确保不会超出范围
            groupedList.add(originalList.subList(i, end));
        }

        return groupedList;
    }

    @Override
    public Result<Boolean> preCompleteMilestoneValidate(UpdateMilestoneDTO dto) {
        // 获取里程碑信息
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(dto.getMilestoneInfoId());
        if (projMilestoneInfo == null) {
            return Result.fail("里程碑节点信息不匹配");
        }
        //获取里程碑枚举信息
        MilestoneNodeEnum milestoneNodeEnum = MilestoneNodeEnum.getByCode(projMilestoneInfo.getMilestoneNodeCode());
//        // 表单调研、打印报表调研、统计报表调研、设备调研、三方接口调研的确认完成逻辑修改
//        if (MilestoneNodeEnum.SURVEY_FORM.equals(milestoneNodeEnum)
//                || MilestoneNodeEnum.SURVEY_REPORT.equals(milestoneNodeEnum)
//                || MilestoneNodeEnum.SURVEY_STATISTICS_REPORT.equals(milestoneNodeEnum)
//                || MilestoneNodeEnum.SURVEY_DEVICE.equals(milestoneNodeEnum)
//                || MilestoneNodeEnum.SURVEY_THIRD_PART.equals(milestoneNodeEnum)) {
//
//            Result<Boolean> booleanResult = this.checkFileRule(projMilestoneInfo.getProjectInfoId(), milestoneNodeEnum);
//            if (booleanResult != null) {
//                return booleanResult;
//            }
//        }

        // 表单制作
        if (MilestoneNodeEnum.PREPARAT_FORM.equals(milestoneNodeEnum)) {
            ProjSurveyFormReq surveyFormReq = new ProjSurveyFormReq();
            surveyFormReq.setProjectInfoId(projMilestoneInfo.getProjectInfoId());
            // 表单
            List<ProjSurveyFormResp> formList = projSurveyFormMapper.selectSurveyFormByPage(surveyFormReq);
            // 只查询上线必备的
            formList = formList.stream().filter(item -> Integer.valueOf(1).equals(item.getOnlineEssential())).collect(Collectors.toList());
            // 表单状态
            List<Integer> statusList = formList.stream().map(ProjSurveyFormResp::getFinishStatus).collect(Collectors.toList());

            // 只有验证通过
            boolean allCompleted = statusList.stream().allMatch(i -> Integer.valueOf(8).equals(i));
            if (allCompleted) {
                this.compMilestone(dto);
                return new Result<>(true, 10, "表单制作验证通过，执行里程碑完成逻辑");
            }
            // 只有验证通过或者制作完成待验证的
            boolean canValidate = statusList.stream().allMatch(i -> Integer.valueOf(8).equals(i) || Integer.valueOf(1).equals(i));
            if (canValidate) {
                long formCount = formList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus())).count();
                return new Result<>(true, 11, String.format("目前仍有【%s】个表单制作完成尚未验证通过，是否一键验证通过？", formCount));
            }
            return Result.fail("上线必备的表单尚未全部制作完成，请前往表单列表查看明细");
        }

        if (MilestoneNodeEnum.PREPARAT_REPORT.equals(milestoneNodeEnum)) {
            ProjSurveyReportReq projSurveyReportReq = new ProjSurveyReportReq();
            projSurveyReportReq.setProjectInfoId(projMilestoneInfo.getProjectInfoId());
            // 打印报表
            List<ProjSurveyReportResp> printReportList = projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq);
            // 只查询上线必备的
            printReportList = printReportList.stream().filter(item -> Integer.valueOf(1).equals(item.getOnlineEssential())).collect(Collectors.toList());
            // 表单状态
            List<Integer> statusList = printReportList.stream().map(ProjSurveyReportResp::getFinishStatus).collect(Collectors.toList());

            // 只有验证通过
            boolean allCompleted = statusList.stream().allMatch(i -> Integer.valueOf(8).equals(i));
            if (allCompleted) {
                this.compMilestone(dto);
                return new Result<>(true, 10, "打印报表验证通过，执行里程碑完成逻辑");
            }
            // 只有验证通过或者制作完成待验证的
            boolean canValidate = statusList.stream().allMatch(i -> Integer.valueOf(8).equals(i) || Integer.valueOf(1).equals(i));
            if (canValidate) {
                long printReportCount = printReportList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus())).count();
                return new Result<>(true, 11, String.format("目前仍有【%s】个打印报表制作完成尚未验证通过，是否一键验证通过？", printReportCount));
            }
            return Result.fail("上线必备的打印报表尚未全部制作完成，请前往打印报表列表查看明细");
        }

        if (MilestoneNodeEnum.PREPARAT_REPORT_STATISTICS.equals(milestoneNodeEnum)) {
            ProjStatisticalReportMainPageReq reportMainPageReq = new ProjStatisticalReportMainPageReq();
            reportMainPageReq.setProjectInfoId(projMilestoneInfo.getProjectInfoId());
            // 统计报表
            List<ProjStatisticalReportMainSelectResp> statisticalReportList = projStatisticalReportMainMapper.findReportPage(reportMainPageReq);
            // 只查询上线必备的
            statisticalReportList = statisticalReportList.stream().filter(item -> Integer.valueOf(1).equals(item.getOnlineFlag())).collect(Collectors.toList());

            // 验证状态
            List<Integer> verificationStatusList = statisticalReportList.stream().map(ProjStatisticalReportMainSelectResp::getVerificationStatus).collect(Collectors.toList());

            // 报表状态
            List<Integer> statusList = statisticalReportList.stream().map(ProjStatisticalReportMainSelectResp::getReportStatus).collect(Collectors.toList());

            // 只有验证通过
            boolean allCompleted = verificationStatusList.stream().allMatch(i -> Integer.valueOf(1).equals(i));

            // 只有已发布
            boolean canValidate = statusList.stream().allMatch(i -> Integer.valueOf(41).equals(i));

            // 只有已发布并且都是验证通过
            if (canValidate && allCompleted) {
                this.compMilestone(dto);
                return new Result<>(true, 10, "统计报表验证通过，执行里程碑完成逻辑");
            }
            // 只有验证通过
            boolean existReject = verificationStatusList.stream().anyMatch(i -> Integer.valueOf(2).equals(i));
            if (existReject) {
                return Result.fail("上线必备的统计报表存在验证不通过的数据，请前往打印报表列表查看明细");
            }

            if (canValidate) {
                long printReportCount = statisticalReportList.stream().filter(item -> !Integer.valueOf(1).equals(item.getVerificationStatus())).count();
                return new Result<>(true, 11, String.format("目前仍有【%s】个统计报表制作完成尚未验证通过，是否一键验证通过？", printReportCount));
            }
            return Result.fail("上线必备的统计报表尚未全部制作完成，请前往打印报表列表查看明细");
        }
        return this.compMilestone(dto);
    }

    @Resource
    private ProjSurveyFormService projSurveyFormService;

    @Resource
    private ProjStatisticalReportMainService projStatisticalReportMainService;

    private boolean formUpdate(Long projectInfoId) {
        ProjSurveyFormReq surveyFormReq = new ProjSurveyFormReq();
        surveyFormReq.setProjectInfoId(projectInfoId);
        // 表单
        List<ProjSurveyFormResp> formList = projSurveyFormMapper.selectSurveyFormByPage(surveyFormReq);
        // 只查询上线必备的
        formList = formList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus())).collect(Collectors.toList());
        // 表单状态
        List<Long> ids = formList.stream().map(ProjSurveyFormResp::getSurveyFormId).collect(Collectors.toList());

        ProjSurveyFormResponsibilitiesReq req = new ProjSurveyFormResponsibilitiesReq();
        req.setIds(ids);
        return projSurveyFormService.verificationPassed(req);
    }

    private boolean printReportUpdate(Long projectInfoId) {
        ProjSurveyReportReq projSurveyReportReq = new ProjSurveyReportReq();
        projSurveyReportReq.setProjectInfoId(projectInfoId);
        // 打印报表
        List<ProjSurveyReportResp> printReportList = projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq);
        // 只查询上线必备的
        printReportList = printReportList.stream().filter(item -> Integer.valueOf(1).equals(item.getFinishStatus())).collect(Collectors.toList());


        // 表单状态
        List<Long> statusList = printReportList.stream().map(ProjSurveyReportResp::getSurveyReportId).collect(Collectors.toList());

        PrintReportVerificationPassedParam param = new PrintReportVerificationPassedParam();
        param.setSurveyReportIdList(statusList);
        return projSurveyReportService.verificationPassed(param);
    }

    private boolean statisticalReportUpdate(Long projectInfoId) {
        ProjStatisticalReportMainPageReq reportMainPageReq = new ProjStatisticalReportMainPageReq();
        reportMainPageReq.setProjectInfoId(projectInfoId);
        // 统计报表
        List<ProjStatisticalReportMainSelectResp> statisticalReportList = projStatisticalReportMainMapper.findReportPage(reportMainPageReq);
        // 只查询制作完成、已下沉、已发布的
        statisticalReportList = statisticalReportList.stream().filter(item -> Integer.valueOf(41).equals(item.getReportStatus()) || Integer.valueOf(31).equals(item.getReportStatus()) || Integer.valueOf(22).equals(item.getReportStatus())).collect(Collectors.toList());

        // 验证状态
        List<Long> verificationStatusList = statisticalReportList.stream().map(ProjStatisticalReportMainSelectResp::getStatisticalReportMainId).collect(Collectors.toList());

        ProjStatisticalReportMainAssignPersonsReq2 param = new ProjStatisticalReportMainAssignPersonsReq2();
        param.setMainIds(verificationStatusList);
        param.setVerificationStatus(1);
        return projStatisticalReportMainService.verificationPassed(param);
    }

    @Override
    public Result<Boolean> checkFileRule(Long projectInfoId, MilestoneNodeEnum milestoneNodeEnum) {
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);

        ProjCustomInfo projCustomInfo = customInfoService.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        GetProjectFileRuleParam getProjectFileRuleParam = GetProjectFileRuleParam.builder()
                .hisFlag(projProjectInfo.getHisFlag())
                .upgradationType(projProjectInfo.getUpgradationType())
                .telesalesFlag(projCustomInfo.getTelesalesFlag())
                .milestoneNodeFlag(1)
                .milestoneNodeCode(milestoneNodeEnum.getCode())
                .projectInfoId(projectInfoId)
                .build();
        if (Integer.valueOf(1).equals(projProjectInfo.getProjectType())) {
            getProjectFileRuleParam.setMonomerFlag(1);
        }
        if (Integer.valueOf(2).equals(projProjectInfo.getProjectType())) {
            getProjectFileRuleParam.setRegionFlag(1);
        }
        List<ProjectFileUploadRuleVO> projectFileRule = ruleProjectRuleConfigService.getProjectFileRule(getProjectFileRuleParam);
        // 上传文件规则为空，认为校验通过
        if (CollectionUtils.isEmpty(projectFileRule)) {
            return null;
        }
        // 上传文件规则非空时，进行校验
        AtomicReference<String> uploadCheck = new AtomicReference<>("");

        AtomicReference<String> imageAiCheck = new AtomicReference<>("");

        projectFileRule.forEach(item -> {
            if (Integer.valueOf(1).equals(item.getRequiredType()) && org.apache.commons.collections4.CollectionUtils.isEmpty(item.getFileList())) {
                uploadCheck.set(uploadCheck + "需上传" + item.getProjectRuleContent() + "。");
            }

            if (Integer.valueOf(2).equals(item.getRequiredType()) && org.apache.commons.collections4.CollectionUtils.isEmpty(item.getFileList()) && !Boolean.FALSE.equals(item.getUseFlag())) {
                uploadCheck.set(uploadCheck + "需上传" + item.getProjectRuleContent() + "或勾选不使用。");
            }

            DictAgentChatReq dictAgentChatReq = new DictAgentChatReq();
            dictAgentChatReq.setScenarioCode(item.getSceneCode());

            // AI工具检测配置
            DictAgentChatScenarioConfigResp resp = dictAgentScenarioConfigMapper.selectByParam(dictAgentChatReq);
            // AI工具检测配置非空时，进行AI检测
            if (resp != null) {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(item.getFileList())) {
                    List<String> imageList = item.getFileList().stream().map(ProjProjectFileVO::getFilePath).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());

                    List<List<String>> groupedImageList = ListUtils.splitImageList(imageList, 6);

                    List<CheckResult> checkResultList = new ArrayList<>();
                    for (List<String> image : groupedImageList) {
                        DictAgentChatReq agentChatReq = new DictAgentChatReq();
                        agentChatReq.setScenarioCode(item.getSceneCode());
                        agentChatReq.setFileUrls(image);
                        Result result = null;
                        try {
                            result = baseQueryService.sendChartMessage(agentChatReq);
                        } catch (Exception e) {
                            log.error("调用图片智能体进行图片检测，发生异常，参数={}，errMsg={}，stackInfo=", JSON.toJSONString(agentChatReq), e.getMessage(), e);
                            CheckResult checkResult = new CheckResult();
                            checkResult.setCheckCode(3);
                            checkResult.setCheckResult("错误：" + e.getMessage());
                            checkResultList.add(checkResult);
                        }
                        if (result != null) {
                            if (!result.isSuccess()) {
                                CheckResult checkResult = new CheckResult();
                                checkResult.setCheckCode(3);
                                checkResult.setCheckResult("错误：图片检测失败");
                                checkResultList.add(checkResult);
                            } else {
                                if (result.getData() == null) {
                                    CheckResult checkResult = new CheckResult();
                                    checkResult.setCheckCode(3);
                                    checkResult.setCheckResult("错误：图片检测失败");
                                    checkResultList.add(checkResult);
                                } else {
                                    TypeReference<Map<String, Object>> type = new TypeReference<Map<String, Object>>() {
                                    };
                                    Map<String, Object> map = JSON.parseObject(JSON.toJSONString(result.getData()), type);
                                    // 检测状态：是-符合条件；否-不符合条件
                                    String companyCode = (String) map.get("compliance_status");

                                    String resultText = (String) map.get("result_text");
                                    if ("是".equals(companyCode)) {
                                        CheckResult checkResult = new CheckResult();
                                        checkResult.setCheckCode(1);
                                        checkResult.setCheckResult(resultText);
                                        checkResultList.add(checkResult);
                                    }
                                    if (!"是".equals(companyCode)) {
                                        CheckResult checkResult = new CheckResult();
                                        checkResult.setCheckCode(2);
                                        checkResult.setCheckResult(resultText);
                                        checkResultList.add(checkResult);
                                    }
                                }
                            }
                        }
                    }

                    // 没有检测通过的
                    if (checkResultList.stream().noneMatch(checkResult -> checkResult.getCheckCode() == 1)) {
                        imageAiCheck.set(imageAiCheck + "图片检测不通过。检测结果：" + checkResultList.stream().filter(checkResult -> checkResult.getCheckCode() != 1).map(CheckResult::getCheckResult).collect(Collectors.joining(",")));
                    }
                }
            }
        });

        if (org.apache.commons.lang3.StringUtils.isNotBlank(uploadCheck.get())) {
            return Result.fail(uploadCheck.get());
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(imageAiCheck.get())) {
            return Result.fail(imageAiCheck.get());
        }
        return null;
    }
}
