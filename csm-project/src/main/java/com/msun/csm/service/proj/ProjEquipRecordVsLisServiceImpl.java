package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.AvailableLabQueryDTO;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusDto;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.LisEquipmentItemDTO;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.constants.DictEquipTypeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.DuplexFlagEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.ReviewTypeEnum;
import com.msun.csm.common.enums.device.EquipStatusEnum;
import com.msun.csm.common.enums.device.LisDeviceRuleCodeEnum;
import com.msun.csm.common.enums.projproduct.ProjProductEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigEquipSurvey;
import com.msun.csm.dao.entity.dict.DictEquipAttributes;
import com.msun.csm.dao.entity.dict.DictEquipFactory;
import com.msun.csm.dao.entity.dict.DictEquipInfo;
import com.msun.csm.dao.entity.dict.DictEquipType;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjEquipCheckVsLis;
import com.msun.csm.dao.entity.proj.ProjEquipFiles;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.ProjTipRecord;
import com.msun.csm.dao.entity.report.ReportCustomInfo;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.dao.entity.tmp.TmpEquipCheckDetail;
import com.msun.csm.dao.entity.tmp.TmpOldEquipVsNew;
import com.msun.csm.dao.mapper.config.ConfigEquipSurveyMapper;
import com.msun.csm.dao.mapper.dict.DictEquipAttributesMapper;
import com.msun.csm.dao.mapper.dict.DictEquipFactoryMapper;
import com.msun.csm.dao.mapper.dict.DictEquipInfoMapper;
import com.msun.csm.dao.mapper.dict.DictEquipTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.oldimsp.OldCustomerInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipCheckVsLisMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipFilesMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsLisMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjTipRecordMapper;
import com.msun.csm.dao.mapper.proj.TmpOldEquipVsNewMapper;
import com.msun.csm.dao.mapper.report.ReportCustomInfoMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpEquipCheckDetailMapper;
import com.msun.csm.feign.client.analymanager.LisAnalyManagerClient;
import com.msun.csm.model.convert.ProjEquipFilesConvert;
import com.msun.csm.model.convert.ProjEquipRecordConvert;
import com.msun.csm.model.dto.BaseCodeNameDTO;
import com.msun.csm.model.dto.CheckEquipToAnalyManagerDTO;
import com.msun.csm.model.dto.DeleteEquipForLisDTO;
import com.msun.csm.model.dto.EquipRecordVsLisExcelDTO;
import com.msun.csm.model.dto.EquipSendFilesToLisDTO;
import com.msun.csm.model.dto.EquipSendToLisDTO;
import com.msun.csm.model.dto.EquipStatusUpdateDto;
import com.msun.csm.model.dto.HospitalInfoDTO;
import com.msun.csm.model.dto.LisCloudEquipSelectDTO;
import com.msun.csm.model.dto.LisEquipFileExtendDTO;
import com.msun.csm.model.dto.LisEquipSelectDTO;
import com.msun.csm.model.dto.MilestoneInfoDTO;
import com.msun.csm.model.dto.MultipartFileImpl;
import com.msun.csm.model.dto.OldEquipImportDTO;
import com.msun.csm.model.dto.OldLisEquipImgDTO;
import com.msun.csm.model.dto.OldLisImportDTO;
import com.msun.csm.model.dto.ProjEquipFilesDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisExtendDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisListSaveDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisSaveDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.ResearchPlanDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.SendCsmEquipToOldLisDTO;
import com.msun.csm.model.dto.UpdateLisCloudEquipDTO;
import com.msun.csm.model.dto.ViewProjEquipRecordDTO;
import com.msun.csm.model.dto.device.EquipDetailItemDTO;
import com.msun.csm.model.dto.device.SaveEquipFileDTO;
import com.msun.csm.model.dto.device.analyse.AnalyseEquipDetailItemDTO;
import com.msun.csm.model.dto.tip.ProjTipRecordDto;
import com.msun.csm.model.resp.device.analyse.EquipLisDetailItemResp;
import com.msun.csm.model.vo.BaseCodeNameVO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.DictEquipInfoVO;
import com.msun.csm.model.vo.ItemInfoVO;
import com.msun.csm.model.vo.LisEquipFileExtendVO;
import com.msun.csm.model.vo.LisEquipVo;
import com.msun.csm.model.vo.ProjEquipCheckVsLisVO;
import com.msun.csm.model.vo.ProjEquipFilesVO;
import com.msun.csm.model.vo.ProjEquipLisFileTypeLimitVO;
import com.msun.csm.model.vo.ProjEquipRecordLisResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisExtendVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisWrapperVO;
import com.msun.csm.model.vo.ProjProjectFileRuleVO;
import com.msun.csm.model.vo.device.EquipLisDetailItemVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.dict.DictEquipCommService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.EasyExcelData;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.Sm4Util;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.ObsObject;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

@Service
@Slf4j
public class ProjEquipRecordVsLisServiceImpl extends ServiceImpl<ProjEquipRecordVsLisMapper, ProjEquipRecordVsLis> implements ProjEquipRecordVsLisService {

    private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();

    private static Validator validator = validatorFactory.getValidator();

    @Resource
    private SystemSettingApi systemSettingApi;
    @Resource
    private ProjEquipRecordVsPacsService equipRecordVsPacsService;

    @Resource
    private ProjEquipRecordVsAimsService equipRecordVsAimsService;

    @Resource
    private TmpOldEquipVsNewMapper tmpOldEquipVsNewMapper;

    @Resource
    private OldCustomerInfoMapper oldCustomerInfoMapper;

    @Resource
    private DictEquipTypeMapper dictEquipTypeMapper;

    @Resource
    private DictEquipFactoryMapper dictEquipFactoryMapper;

    @Resource
    private DictEquipInfoMapper dictEquipInfoMapper;

    @Resource
    private ProjEquipRecordVsLisMapper projEquipRecordVsLisMapper;

    @Resource
    private ProjEquipRecordMapper equipRecordMapper;

    @Autowired
    private SendBusinessMessageService sendBusinessMessageService;

    @Resource
    private ProjEquipRecordConvert projEquipRecordConvert;

    @Resource
    private ProjEquipFilesMapper projEquipFilesMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private LisAnalyManagerClient lisAnalyManagerClient;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private ProjEquipFilesConvert projEquipFilesConvert;

    @Resource
    private ProjEquipFilesService projEquipFilesService;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private DictEquipCommService dictEquipCommService;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private ProjEquipSummaryService equipSummaryService;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjEquipCheckVsLisMapper projEquipCheckVsLisMapper;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;

    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private DictEquipAttributesMapper dictEquipAttributesMapper;

    @Resource
    private RuleProjectRuleConfigMapper ruleProjectRuleConfigMapper;

    @Value("${project.obs.bucketName}")
    private String bucketName;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ImspSysUserMapper imspSysUserMapper;

    @Resource
    private ProjMilestoneTaskMapper projMilestoneTaskMapper;

    @Resource
    private ProjMilestoneTaskDetailMapper projMilestoneTaskDetailMapper;

    @Resource
    private ReportCustomInfoMapper reportCustomInfoMapper;

    @Resource
    private ProjTipRecordMapper projTipRecordMapper;

    @Resource
    private TmpEquipCheckDetailMapper tmpEquipCheckDetailMapper;

    @Resource
    private ProjEquipRecordCommonService equipRecordCommonService;

    @Resource
    private CommonService commonService;

    @Resource
    private ConfigEquipSurveyMapper configEquipSurveyMapper;

    @Resource
    private ProjEquipRecordVsLisServiceImplExtend equipRecordVsLisServiceImplExtend;

    @Resource
    private ProjEquipRecordLogService projEquipRecordLogService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    /**
     * 根据obs链接转换成文件格式
     *
     * @param obsUrl
     * @param fileName
     * @return
     * @throws IOException
     */
    public MultipartFile convertOBSUrlToMultipartFile(String obsUrl, String fileName) throws IOException {
        List<ObsObject> obsObjectList = OBSClientUtils.getAllObjects(Collections.singletonList(obsUrl));
        if (CollUtil.isEmpty(obsObjectList)) {
            log.warn("obs中未查询到要下载的文件. filePath: {}", obsUrl);
            throw new RuntimeException("obs中未查询到要下载的文件. filePath: " + obsUrl);
        }
        ObsObject obsObject = obsObjectList.get(0);
        String objectKey = obsObject.getObjectKey();
        obsObject = OBSClientUtils.getObsClient().getObject(bucketName, objectKey);
        // 下载到本地, 修改文件名再读取
        InputStream in = obsObject.getObjectContent();
        try (InputStream inputStream = in) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[16384]; // 16Kb 的缓冲区
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            byte[] fileContent = buffer.toByteArray();
            // 创建并返回自定义的MultipartFile实现
            return new MultipartFileImpl(fileName, fileName, "application/octet-stream", fileContent);
        }
    }

    @Override
    public int insert(ProjEquipRecordVsLis record) {
        return projEquipRecordVsLisMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjEquipRecordVsLis record) {
        return projEquipRecordVsLisMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjEquipRecordVsLis record) {
        return projEquipRecordVsLisMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjEquipRecordVsLis record) {
        return projEquipRecordVsLisMapper.insertSelective(record);
    }

    @Override
    public int batchInsert(List<ProjEquipRecordVsLis> list) {
        return projEquipRecordVsLisMapper.batchInsert(list);
    }

    /**
     * 查询Lis设备列表数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjEquipRecordLisResultVO> selectLisEquipData(LisEquipSelectDTO dto) {
        ProjEquipRecordLisResultVO projEquipRecordLisResultVO = new ProjEquipRecordLisResultVO();
        //查询产品ID
        List<DictProduct> dictProducts = productMapper.selectOneByProductName(ProjProductEnum.LIS.getProductName());
        if (CollUtil.isEmpty(dictProducts)) {
            throw new CustomException(StrUtil.format("查询产品报错：{}", ProjProductEnum.LIS.getProductName()));
        }
        DictProduct product = CollUtil.getFirst(dictProducts);
        projEquipRecordLisResultVO.setProductId(product.getYyProductId());
        //查询设备记录
        List<LisEquipVo> lisEquipVos = projEquipRecordVsLisMapper.selectLisEquipData(dto);
        ProjProjectInfo projectInfo = commonService.getProjectInfo(dto.getProjectInfoId());
        log.info("查询设备, 获取到该项目类型为: projectType: {}", projectInfo.getProjectType());
        for (LisEquipVo vo : lisEquipVos) {
            // 组装前端展示的设备名称
            vo.setEquipModelNameStr(vo.getEquipFactoryName() + "/" + vo.getEquipTypeName() + "/" + vo.getEquipModelName());
            // 设置是否必须对接
            vo.setRequiredFlagBool(vo.getRequiredFlag() == 1);
            //回显通讯模式对象
            BaseCodeNameDTO codeNameDTO = new BaseCodeNameDTO();
            codeNameDTO.setId(vo.getCommModeKey());
            codeNameDTO.setName(vo.getCommMode());
            vo.setCommModeObjExtend(codeNameDTO);
            // 适配移动端使用
            vo.setCommModeObj(Convert.convert(BaseCodeNameResp.class, codeNameDTO));
            //返回普通结果原始串地址
            ProjEquipFiles equipFile = projEquipFilesMapper.selectOne(new QueryWrapper<ProjEquipFiles>()
                    .eq("product_equip_record_id", vo.getEquipRecordVsLisId())
                    .eq("file_item_code", "lis_device")
                    .eq("file_type_code", "putong_yuanshichuan")
                    .last(" limit 1"));
            if (ObjectUtil.isNotEmpty(equipFile)) {
                ProjProjectFile file = projProjectFileMapper.selectById(equipFile.getProjectFileId());
                if (ObjectUtil.isNotEmpty(file)) {
                    vo.setOriginStrUrl(OBSClientUtils.getTemporaryUrl(file.getFilePath(), 3600));
                }
            }
            // 判断是否单体, 若区域项目不进行判断是否待上传
            if (projectInfo.getProjectType() != ProjectTypeEnums.SINGLE.getCode().intValue()) {
                continue;
            }
            // 只在设备状态未申请时判断是否有待上传的附件
            if (vo.getEquipStatus() < EquipStatusEnum.APPLYED.getCode()) {
                List<ProjEquipFiles> list = projEquipFilesService.list(new QueryWrapper<ProjEquipFiles>().eq(
                        "product_equip_record_id", vo.getEquipRecordVsLisId()));
                // 根据LIS数据校验数据完整性 查看附件是否都已经上传
                ProjEquipRecordVsLis vsLis = Convert.convert(ProjEquipRecordVsLis.class, vo);
                ProjEquipRecordVsLisExtendDTO extendDTO = equipRecordVsLisServiceImplExtend.transforFileDto(list);
                try {
                    equipRecordVsLisServiceImplExtend.validateNecessary(vo.getCommModeKey(), extendDTO, vsLis);
                } catch (Throwable e) {
                    log.warn("项目: {}, 有待上传的附件.",
                            projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber());
                    vo.setEquipStatus(EquipStatusEnum.WAIT_UPLOAD.getCode());
                }
            }
        }
        projEquipRecordLisResultVO.setRecordList(lisEquipVos);
        return Result.success(projEquipRecordLisResultVO);
    }

    /**
     * 设备信息发送到LIS解析平台
     *
     * @param equipRecordVsLisIds 设备拓展表主键集合,获取设备使用
     * @return 处理结果.
     */
    @Override
    @Transactional
    public Result<String> equipSendToLis(List<Long> equipRecordVsLisIds) {
        return equipSendToLis(equipRecordVsLisIds, true);
    }

    /**
     * 申请,更新设备信息
     *
     * @param equipRecordVsLisIds 设备拓展表主键, 获取设备使用
     * @param updateStatusFlag    是否更新设备申请状态标识, true: 更新状态为已申请, false: 不做更新, 只同步设备信息
     * @return
     */
    private Result<String> equipSendToLis(List<Long> equipRecordVsLisIds, boolean updateStatusFlag) {
        Date now = new Date();
        try {
            for (Long id : equipRecordVsLisIds) {
                LisEquipSelectDTO dto = new LisEquipSelectDTO();
                dto.setEquipRecordVsLisId(id);
                // 查询LIS设备信息
                LisEquipVo lisEquipVo = projEquipRecordVsLisMapper.selectLisEquipData(dto).get(0);
                if (ObjectUtil.isEmpty(lisEquipVo.getHospitalInfoId())) {
                    return Result.fail("未查询到医院信息，请先修改来源医院！");
                }
                // 附件信息查询
                List<ProjEquipFiles> list = projEquipFilesService.list(new QueryWrapper<ProjEquipFiles>().eq(
                        "product_equip_record_id", id));
                // 根据LIS数据校验数据完整性 查看附件是否都已经上传
                ProjEquipRecordVsLis vsLis = Convert.convert(ProjEquipRecordVsLis.class, lisEquipVo);
                ProjEquipRecordVsLisExtendDTO extendDTO = equipRecordVsLisServiceImplExtend.transforFileDto(list);
                // 验证必传项
                equipRecordVsLisServiceImplExtend.validateNecessarySendToLis(lisEquipVo.getCommModeKey(), extendDTO,
                        vsLis);
                // 把lis设备信息发送到LIS解析平台
                EquipSendToLisDTO equipSendToLis = new EquipSendToLisDTO();
                // 组装设备信息
                infoDataForLis(lisEquipVo, equipSendToLis);
                JSONObject entries = lisAnalyManagerClient.insertEquip(equipSendToLis.getId(), equipSendToLis);
                log.info("设备发送结果: {}", entries);
                if (!(Boolean) entries.get("success")) {
                    throw new RuntimeException(/*"设备发送失败 , " +*/ String.valueOf(entries.get("message")));
                }
                // 设置附件参数。。只能进行单个发送
                for (ProjEquipFiles file : list) {
                    //跳过没有选择附件的对象
                    if (file.getProjectFileId() == 0) {
                        continue;
                    }
                    EquipSendFilesToLisDTO d = new EquipSendFilesToLisDTO();
                    d.setId(file.getProductEquipRecordId());
                    // 获取obs上的文件，转换成文件流
                    ProjProjectFile projProjectFile = projProjectFileMapper.selectById(file.getProjectFileId());
                    MultipartFile multipartFile = convertOBSUrlToMultipartFile(projProjectFile.getFilePath(),
                            projProjectFile.getFileName());
                    switch (file.getFileTypeCode()) {
                        case "tongxun_text":
                            //通讯协议文档
                            d.setFile1(multipartFile);
                            break;
                        case "other_pic":
                            // 其他图片
                            d.setFile4(multipartFile);
                            break;
                        case "instrument_appear":
                            // 设备全貌图片
                            d.setFile5(multipartFile);
                            break;
                        case "instrument_config":
                            // 仪器配置文档
                            d.setFile6(multipartFile);
                            break;
                        case "yangben_result":
                            // 样本结果
                            d.setFile7(multipartFile);
                            break;
                        case "putong_yuanshichuan":
                            // 普通结果原始串
                            d.setFile21(multipartFile);
                            break;
                        case "jizhen_result":
                            // .急诊结果原始串
                            d.setFile22(multipartFile);
                            break;
                        case "zhikong_result":
                            // .质控结果原始串
                            d.setFile23(multipartFile);
                            break;
                        case "duplex_tongxun":
                            // .双工通信日志
                            d.setFile24(multipartFile);
                            break;
                        case "duplex":
                            // .双工请求
                            d.setFile25(multipartFile);
                            break;
                        case "db_file":
                            // .双工请求
                            d.setFile13(multipartFile);
                            break;
                        default:
                            break;
                    }
                    try {
                        JSONObject entrie = lisAnalyManagerClient.uploadFiles(d.getId(), null, d.getFile1(),
                                d.getFile2(), d.getFile21(),
                                d.getFile22(), d.getFile23(), d.getFile24(), d.getFile25(), d.getFile3(), d.getFile4(),
                                d.getFile5(), d.getFile6(), d.getFile7(), d.getFile13());
                        log.info("LIS开放平台上传附件信息 结束 , " + entrie);
                    } catch (Exception e) {
                        log.info("LIS开放平台上传附件信息失败 ， " + e);
                    }
                }
                if (updateStatusFlag) {
                    ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                    projEquipRecord.setEquipRecordId(lisEquipVo.getEquipRecordId());
                    projEquipRecord.setEquipStatus(1);
                    projEquipRecord.setApplyTime(new Date());
                    int count = equipRecordMapper.updateById(projEquipRecord);
                    log.info("更新lis设备状态. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipRecord));

                    // 调研管控
                    sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(lisEquipVo.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_RESUBMIT_FOR_REVIEW.getBusinessTable());
                    ProjEquipRecord projEquipRecord2 = equipRecordMapper.selectById(lisEquipVo.getEquipRecordId());
//                    sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
//                            ReviewTypeEnum.LIS_EQUIP_SUBMIT_AUDIT,
//                            projEquipRecord2.getProjectInfoId(),
//                            lisEquipVo.getEquipRecordId(),
//                            null,
//                            "LIS设备审核超期",
//                            now
//                    );


                    String apiDevYunyingUid = lisEquipVo.getApiDevYunyingUid();
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(apiDevYunyingUid)) {
                        SysUser sysUser = sysUserMapper.selectUserIdByYungyingId(apiDevYunyingUid);
                        if (sysUser != null) {
                            if (Integer.valueOf(0).equals(lisEquipVo.getDuplexFlag())) {
                                // 再开启审核超期处罚
                                sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
                                        ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_DG,
                                        projEquipRecord2.getProjectInfoId(),
                                        lisEquipVo.getEquipRecordId(),
                                        sysUser.getSysUserId(),
                                        String.format("LIS单工设备制作超期【%s/%s】", projEquipRecord2.getEquipFactoryName(), projEquipRecord2.getEquipTypeName()),
                                        now
                                );
                            } else {
                                // 再开启审核超期处罚
                                sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
                                        ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_SG,
                                        projEquipRecord2.getProjectInfoId(),
                                        lisEquipVo.getEquipRecordId(),
                                        sysUser.getSysUserId(),
                                        String.format("LIS双工设备制作超期【%s/%s】", projEquipRecord2.getEquipFactoryName(), projEquipRecord2.getEquipTypeName()),
                                        now
                                );
                            }
                        }
                    }
                }
                ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(lisEquipVo.getEquipRecordId());
                equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
                //保存操作日志
                ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
                projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
                projEquipRecordLog.setEquipRecordBusinessId(id);
                projEquipRecordLog.setOperateName("申请设备");
                projEquipRecordLogService.saveLog(projEquipRecordLog);
            }
            return Result.success("设备发送成功");
        } catch (Exception e) {
            log.error("设备发送失败, message: {}, e=", e.getMessage(), e);
            throw new CustomException("设备发送失败 , " + e.getMessage());
            // return Result.fail("设备发送失败 , " + e.getMessage());
        }
    }

    @Override
    public Result<?> lisApiDevAssignCallback(Long equipRecordVsLisId, String userYunyingId) {
        SysUser user = new LambdaQueryChainWrapper<>(sysUserMapper)
                .eq(SysUser::getUserYunyingId, userYunyingId)
                .one();
        if (user == null) {
            throw new CustomException("请检查运营平台用户ID是否正确，一般userYunyingId长度为3~5个数字组成");
        }
        new LambdaUpdateChainWrapper<>(projEquipRecordVsLisMapper)
                .eq(ProjEquipRecordVsLis::getEquipRecordVsLisId, equipRecordVsLisId)
                .set(ProjEquipRecordVsLis::getApiDevYunyingUid, userYunyingId)
                .set(ProjEquipRecordVsLis::getUpdaterId, user.getSysUserId())
                .set(ProjEquipRecordVsLis::getUpdateTime, new Date())
                .update();
        //保存操作日志
        ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
        projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
        projEquipRecordLog.setEquipRecordBusinessId(equipRecordVsLisId);
        projEquipRecordLog.setOperateName("已分配接口制作人");
        projEquipRecordLog.setOperateContent(StrUtil.format("已将接口制作人分配给{}", user.getUserName()));
        projEquipRecordLogService.saveLog(projEquipRecordLog);
        return Result.success();
    }

    /**
     * LIS设备附件信息检测
     *
     * @param list
     * @param lisEquipVo
     * @return
     */
    private Result checkFileToLis(List<ProjEquipFiles> list, LisEquipVo lisEquipVo) {
        StringBuilder sb = new StringBuilder();
        sb.append("设备：" + lisEquipVo.getEquipModelName());
        // 检查设备是否缺少图片上传
        if (list.stream().noneMatch(x -> "yangben_result".equals(x.getFileTypeCode()))) {
            sb.append("缺少样本结果文件;");
        }
        if (list.stream().noneMatch(x -> "instrument_config".equals(x.getFileTypeCode()))) {
            sb.append("缺少仪器配置文档;");
        }
        if (list.stream().noneMatch(x -> "putong_yuanshichuan".equals(x.getFileTypeCode()))) {
            sb.append("缺少普通结果文件;");
        }
        if (list.stream().noneMatch(x -> "instrument_appear".equals(x.getFileTypeCode()))) {
            sb.append("缺少仪器外观图片;");
        }
        if (lisEquipVo.getEmergencyFlag() == 1) {
            if (list.stream().noneMatch(x -> "jizhen_result".equals(x.getFileTypeCode()))) {
                sb.append("缺少急诊结果文件;");
            }
        }
        if (lisEquipVo.getQualityControlFlag() == 1) {
            if (list.stream().noneMatch(x -> "zhikong_result".equals(x.getFileTypeCode()))) {
                sb.append("缺少质控结果文件;");
            }
        }
        if (lisEquipVo.getDuplexFlag() == 1) {
            if (list.stream().noneMatch(x -> "duplex_tongxun".equals(x.getFileTypeCode()))) {
                sb.append("缺少双工通讯日志;");
            }
            if (list.stream().noneMatch(x -> "duplex".equals(x.getFileTypeCode()))) {
                sb.append("缺少双工请求;");
            }
            if (list.stream().noneMatch(x -> "tongxun_text".equals(x.getFileTypeCode()))) {
                sb.append("缺少通讯文档;");
            }

        }
        for (ProjEquipFiles file : list) {
            //  必传数据校验
            if ("yangben_result".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("样本结果不能为空;");
                }
            }
            if ("putong_yuanshichuan".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("普通结果原始串不能为空;");
                }
            }
            if ("instrument_config".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("仪器配置文档不能为空;");
                }
            }
            if ("instrument_appear".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("仪器外观不能为空;");
                }
            }
            if (lisEquipVo.getEmergencyFlag() == 1 && "jizhen_result".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("急诊结果文件不能为空;");
                }
            }
            if (lisEquipVo.getQualityControlFlag() == 1 && "zhikong_result".equals(file.getFileTypeCode())) {
                if (file.getProjectFileId() == 0) {
                    sb.append("质控结果文件不能为空;");
                }
            }
            if (lisEquipVo.getDuplexFlag() == 1) {
                if ("duplex_tongxun".equals(file.getFileTypeCode()) && file.getProjectFileId() == 0) {
                    sb.append("双工通讯日志不能为空;");
                }
                if ("duplex".equals(file.getFileTypeCode()) && file.getProjectFileId() == 0) {
                    sb.append("双工请求不能为空;");
                }
                if ("tongxun_text".equals(file.getFileTypeCode()) && file.getProjectFileId() == 0) {
                    sb.append("通讯文档不能为空;");
                }

            }
        }
        if (sb.indexOf("为空") != -1 || sb.indexOf("缺少") != -1) {
            return Result.fail(sb + "请修改后重新提交。");
        } else {
            return Result.success();
        }
    }

    /**
     * 组装LIS开发平台数据
     *
     * @param lisEquipVo
     * @param equipSendToLis
     */
    private void infoDataForLis(LisEquipVo lisEquipVo, EquipSendToLisDTO equipSendToLis) {
        equipSendToLis.setId(lisEquipVo.getEquipRecordVsLisId());
        // 老换新导入的设备 根据标识判断。 老换新导入的数据取资源库的设备
        if (lisEquipVo.getOldLisImportFlag() == 1) {
            equipSendToLis.setEquipFactoryId(ObjectUtil.isNotEmpty(lisEquipVo.getResourceEquipFactoryId())
                    ? lisEquipVo.getResourceEquipFactoryId() : lisEquipVo.getEquipFactoryId());
            equipSendToLis.setEquipClassId(ObjectUtil.isNotEmpty(lisEquipVo.getResourceEquipTypeId())
                    ? lisEquipVo.getResourceEquipTypeId() : lisEquipVo.getEquipTypeId());
            equipSendToLis.setEquipNameId(ObjectUtil.isNotEmpty(lisEquipVo.getResourceEquipInfoId())
                    ? lisEquipVo.getResourceEquipInfoId() : lisEquipVo.getEquipInfoId());
        } else {
            equipSendToLis.setEquipClassId(lisEquipVo.getEquipTypeId());
            equipSendToLis.setEquipFactoryId(lisEquipVo.getEquipFactoryId());
            equipSendToLis.setEquipNameId(lisEquipVo.getEquipInfoId());
        }
        // 处理设备属性手填的情况
        setEquipCustomFilled(lisEquipVo, equipSendToLis);
        equipSendToLis.setHisCreaterId(Convert.toLong(userHelper.getCurrentUser().getSysUserId()));
        equipSendToLis.setHisUpdaterId(Convert.toLong(userHelper.getCurrentUser().getSysUserId()));
        equipSendToLis.setLinkman(null);
        equipSendToLis.setTelephone(lisEquipVo.getEquipFactoryPhone());
        equipSendToLis.setCustomerId(lisEquipVo.getCustomInfoId());
        equipSendToLis.setStatus(1);
        equipSendToLis.setProductId(lisEquipVo.getYyProductId());
        // 查询通讯方式
        equipSendToLis.setDataPipe(lisEquipVo.getCommModeKey());
        equipSendToLis.setCommModelName(lisEquipVo.getCommMode());
        Integer duplexFlag = null;
        // 交付与lis状态不一致, 需转换
        if (ObjectUtil.isNotEmpty(lisEquipVo.getDuplexFlag())) {
            if (lisEquipVo.getDuplexFlag() == DuplexFlagEnum.DUPLEX_SAMPLE.getCode().intValue()) {
                duplexFlag = NumberEnum.NO_2.num();
            } else if (lisEquipVo.getDuplexFlag() == DuplexFlagEnum.DUPLEX_BAR.getCode().intValue()) {
                duplexFlag = NumberEnum.NO_1.num();
            }
        }
        equipSendToLis.setFlagDuplex(duplexFlag);
        equipSendToLis.setIsRecheck(lisEquipVo.getIsRecheckFlag());
        //查询客户名称
        ProjCustomInfo customInfo = customInfoMapper.selectById(lisEquipVo.getCustomInfoId());
        equipSendToLis.setCustomerName(customInfo.getCustomName());
        equipSendToLis.setDataSource(3);
        equipSendToLis.setIsEmergency(lisEquipVo.getEmergencyFlag());
        equipSendToLis.setIsQualityControl(lisEquipVo.getQualityControlFlag());
        equipSendToLis.setLocation(lisEquipVo.getEquipPosition());
        equipSendToLis.setContent(lisEquipVo.getMemo());
        // 查询产品名称
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("yy_product_id",
                lisEquipVo.getYyProductId()));
        equipSendToLis.setProductName(product.getProductName());
        JSONArray jsonArray = new JSONArray();
        if (StrUtil.isNotBlank(lisEquipVo.getItemChannel01())) {
            JSONObject itemExtendJson = new JSONObject();
            itemExtendJson.set("itemChannel", lisEquipVo.getItemChannel01());
            itemExtendJson.set("itemResult", lisEquipVo.getItemResult01());
            jsonArray.add(itemExtendJson);
        }
        if (StrUtil.isNotBlank(lisEquipVo.getItemChannel02())) {
            JSONObject itemExtendJson = new JSONObject();
            itemExtendJson.set("itemChannel", lisEquipVo.getItemChannel02());
            itemExtendJson.set("itemResult", lisEquipVo.getItemResult02());
            jsonArray.add(itemExtendJson);
        }
        equipSendToLis.setItemExtendJson(jsonArray.toString());
        equipSendToLis.setSampleNo(lisEquipVo.getBarCode());
        equipSendToLis.setQcSampleNo(lisEquipVo.getQuaBarCode());
        equipSendToLis.setQcItemResult(lisEquipVo.getQuaResult());
        equipSendToLis.setEmergencySampleNo(lisEquipVo.getEmerBarCode());
        equipSendToLis.setExistPic(lisEquipVo.getReportWithImgFlag());
        equipSendToLis.setQuerySql(StrUtil.isNotBlank(lisEquipVo.getQuerySql())
                ? Sm4Util.encrypt(lisEquipVo.getQuerySql()) : StrUtil.EMPTY);
        equipSendToLis.setDbFilePath(lisEquipVo.getDbFilePath());
        equipSendToLis.setDbPassWord(lisEquipVo.getDbPassword());
        log.info("目标参数: {}, 源参数: {}", JSONUtil.toJsonStr(equipSendToLis), JSONUtil.toJsonStr(lisEquipVo));
    }

    /**
     * 处理手动填写的设备属性. 若未查询到对应的设备字典内容, 设置为手动填写的设备信息
     *
     * @param lisEquipVo     保存的设备信息
     * @param equipSendToLis 目标对象
     */
    private void setEquipCustomFilled(LisEquipVo lisEquipVo, EquipSendToLisDTO equipSendToLis) {
        // 手填厂商
        Result<List<BaseIdNameResp>> lisEquipFactory = equipSummaryService.selectSurveyEquipFactory("Lis",
                lisEquipVo.getEquipFactoryId());
        equipSendToLis.setUnreviewedEquipFactoryName(CollectionUtil.isEmpty(lisEquipFactory.getData())
                ? lisEquipVo.getEquipFactoryName() : null);
        // 手填类型
        Result<List<BaseIdNameResp>> lisEquipType = equipSummaryService.selectSurveyEquipType(
                "Lis", lisEquipVo.getEquipTypeId()
        );
        equipSendToLis.setUnreviewedEquipClassName(CollectionUtil.isEmpty(lisEquipType.getData())
                ? lisEquipVo.getEquipTypeName() : null);
        // 手填型号
        Result<List<DictEquipInfoVO>> lisEquipInfo = equipSummaryService.selectSurveyEquipInfo(
                "Lis", lisEquipVo.getEquipInfoId()
        );
        equipSendToLis.setUnreviewedEquipName(CollectionUtil.isEmpty(lisEquipInfo.getData())
                ? lisEquipVo.getEquipModelName() : null);
    }

    /**
     * 更新Lis开放平台设备信息
     *
     * @param equipRecordVsLisId
     * @return
     */
    private Result updateEquipToLisAnalyManager(Long equipRecordVsLisId) {
        try {
            EquipSendToLisDTO equipSendToLis = new EquipSendToLisDTO();
            // 查询LIS设备信息
            LisEquipSelectDTO lisEquipSelectDTO = new LisEquipSelectDTO();
            lisEquipSelectDTO.setEquipRecordVsLisId(equipRecordVsLisId);
            LisEquipVo lisEquipVo = projEquipRecordVsLisMapper.selectLisEquipData(lisEquipSelectDTO).get(0);
            // 组装更新数据参数
            infoDataForLis(lisEquipVo, equipSendToLis);
            JSONObject entries = lisAnalyManagerClient.updateEquip(equipSendToLis);
            log.info("Lis开发平台更新设备结果: {}", entries);
            if (!(Boolean) entries.get("success")) {
                throw new RuntimeException("Lis开发平台更新设备失败 , " + entries.get("message"));
            }
            return Result.success();
        } catch (Exception e) {
            log.info("更新设备信息失败 , {}", e);
            return Result.fail("更新LIS设备失败 , " + e.getMessage());
        }
    }

    /**
     * 删除LIS设备信息
     *
     * @param equipRecordVsLisId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteEquipToLis(Long equipRecordVsLisId) {
        // 根据LIS设备id查询公共设备表id ， 同步删除公共表数据（撤销申请时LIS开放平台会同步删除数据，因此这里无需再单独调用删除接口）
        ProjEquipRecordVsLis projEquipRecordVsLis = projEquipRecordVsLisMapper.selectById(equipRecordVsLisId);
        projEquipRecordVsLisMapper.deleteById(equipRecordVsLisId);
        Long equipRecordId = projEquipRecordVsLis.getEquipRecordId();
        ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(equipRecordId);
        equipRecordMapper.deleteById(equipRecordId);
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
        // 删除 Lis设备附件信息
        projEquipFilesMapper.delete(new QueryWrapper<ProjEquipFiles>().eq("product_equip_record_id", equipRecordVsLisId));

        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(equipRecordId, ReviewTypeEnum.LIS_EQUIP_RESUBMIT_FOR_REVIEW.getBusinessTable());
        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(equipRecordId, ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_DG.getBusinessTable());
        sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(equipRecordId, ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_SG.getBusinessTable());
        return Result.success();
    }

    /**
     * 撤销LIS接口申请
     *
     * @param equipRecordVsLisId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result equipRevokeToLis(Long equipRecordVsLisId) {
        try {
            // 查询该接口数据
            ProjEquipRecordVsLis projEquipRecordVsLis = projEquipRecordVsLisMapper.selectById(equipRecordVsLisId);
            // 撤销LIS解析平台的接口申请
            DeleteEquipForLisDTO deleteEquipForLisDTO = new DeleteEquipForLisDTO();
            deleteEquipForLisDTO.setId(equipRecordVsLisId);
            deleteEquipForLisDTO.setOperatorId(Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()));
            JSONObject entries = lisAnalyManagerClient.cancelEquipApply(deleteEquipForLisDTO.getId(),
                    deleteEquipForLisDTO.getOperatorId());
            log.info("Lis开发平台撤销设备结果: {}", entries);
            if (!(Boolean) entries.get("success")) {
                throw new RuntimeException("Lis开发平台撤销设备失败 , " + entries.get("message"));
            }
            // 更新公共表该设备的状态为 0
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipRecordId(projEquipRecordVsLis.getEquipRecordId());
            projEquipRecord.setEquipStatus(0);
            equipRecordMapper.updateById(projEquipRecord);

            sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_DG.getBusinessTable());
            sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projEquipRecord.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_MAKE_FOR_SG.getBusinessTable());
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(equipRecordVsLisId);
            projEquipRecordLog.setOperateName("撤销申请");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
            return Result.success();
        } catch (Exception e) {
            log.info("撤销失败 , {}", e);
            return Result.fail("撤销失败 , " + e.getMessage());
        }
    }

    /**
     * 新增LIS设备信息
     *
     * @param dto 保存时入参, 手机端或者pc端, 根据isMobile区分
     * @return 成功失败
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<String> saveDevice(ProjEquipRecordVsLisSaveDTO dto) {
        // 默认pc端保存
        if (ObjectUtil.isEmpty(dto.getIsMobile())) {
            dto.setIsMobile(NumberEnum.NO_0.num());
        }
        // 使用国密4解密, 避免被拦截
        String sql = dto.getEquipRecordVsLisExtendDTO().getQuerySql();
        if (StrUtil.isNotBlank(sql)) {
            dto.getEquipRecordVsLisExtendDTO().setQuerySql(Sm4Util.decrypt(sql));
        }
        ProjEquipRecordVsLisExtendDTO lisExtendDto = dto.getEquipRecordVsLisExtendDTO();
        lisExtendDto.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
        // 保存设备记录表
        ProjEquipRecord projEquipRecord = saveEquipRecord(dto.getEquipRecordVsLisExtendDTO());
        // 保存拓展表
        //保存Lis设备记录
        ProjEquipRecordVsLis projEquipRecordVsLis = new ProjEquipRecordVsLis();
        BeanUtil.copyProperties(lisExtendDto, projEquipRecordVsLis);
        // 处理场景切换时保存记录, 会将不使用的字段置空, 根据场景展示字段处理
        BaseCodeNameDTO baseCodeNameDTO = lisExtendDto.getCommModeObjExtend();
        if (ObjectUtil.isEmpty(baseCodeNameDTO) || CollUtil.isEmpty(baseCodeNameDTO.getSurveyConfigFields())) {
            log.error("保存失败, 未指定要保存的字段. baseNameDto: {}", JSONUtil.toJsonStr(baseCodeNameDTO));
            throw new CustomException("保存失败, 未指定要保存的字段.");
        }
        List<String> configFields = baseCodeNameDTO.getSurveyConfigFields();
        // 转换map, 获取字段时提高效率
        Map<String, String> surveyMap = equipRecordVsLisServiceImplExtend.findSurveyMap(configFields);
        // 匹配字段, 保存
        try {
            // 设置字段为空
            setEmpty(surveyMap, projEquipRecordVsLis, "querySql", "quaResult", "quaBarCode", "memo",
                    "itemName01", "itemName02", "itemResult01", "itemResult02", "itemChannel02", "itemChannel01",
                    "equipTypeName",
                    "equipFactoryName", "barCode",
                    "reportWithImgFlag", "dbFilePath", "dbPassword");
        } catch (Exception e) {
            log.error("lis设备保存失败. message: {}, e=", e.getMessage(), e);
            throw new CustomException("保存失败.");
        }
        // 判断必传值是否为空, 若未传或为空则抛出异常
        equipRecordVsLisServiceImplExtend.validateNecessary(surveyMap, projEquipRecordVsLis, dto.getIsMobile(),
                lisExtendDto);
        // 设置设备记录主键
        projEquipRecordVsLis.setEquipRecordId(projEquipRecord.getEquipRecordId());
        projEquipRecordVsLis.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        projEquipRecordVsLis.setUpdateTime(new Date());
        if (projEquipRecordVsLis.getEquipRecordVsLisId() == null) {
            projEquipRecordVsLis.setEquipRecordVsLisId(SnowFlakeUtil.getId());
            this.insert(projEquipRecordVsLis);
        } else {
            int count = projEquipRecordVsLisMapper.updateVsLis(projEquipRecordVsLis);
            log.info("更新lis设备详情. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipRecordVsLis));
        }
        // 获取存在的设备文件id
        List<Long> existEquipFileIds = CollUtil.newArrayList();
        // 保存附件
        SaveEquipFileDTO saveEquipFileDTO = SaveEquipFileDTO.builder()
                .projEquipRecordVsLis(projEquipRecordVsLis)
                .lisExtendDto(lisExtendDto)
                .projEquipRecord(projEquipRecord)
                .existEquipFileIds(existEquipFileIds)
                .build();
        updateOrSaveEquipFile(lisExtendDto.getDbFile(), LisDeviceRuleCodeEnum.DB_FILE.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getEmergencyResultString(), LisDeviceRuleCodeEnum.JIZHEN_RESULT.getCode(),
                saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getCommunicationProtocolDoc(),
                LisDeviceRuleCodeEnum.TONGXUN_TEXT.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getDuplexCommunicationLog(),
                LisDeviceRuleCodeEnum.DUPLEX_TONGXUN.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getDeviceFullImg(),
                LisDeviceRuleCodeEnum.INSTRUMENT_APPEAR.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getDeviceConfigImg(),
                LisDeviceRuleCodeEnum.INSTRUMENT_CONFIG.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getNormalResultString(),
                LisDeviceRuleCodeEnum.PUTONG_YUANSHICHUAN.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getQualityControlResultString(),
                LisDeviceRuleCodeEnum.ZHIKONG_RESULT.getCode(), saveEquipFileDTO);
        updateOrSaveEquipFile(lisExtendDto.getSampleResult(), LisDeviceRuleCodeEnum.YANGBEN_RESULT.getCode(),
                saveEquipFileDTO);
        // 查询不再使用的附件进行删除
        if (CollUtil.isNotEmpty(existEquipFileIds)) {
            List<ProjEquipFiles> deleteFiles = projEquipFilesMapper.selectList(new QueryWrapper<ProjEquipFiles>().eq(
                    "product_equip_record_id",
                    lisExtendDto.getEquipRecordVsLisId()).notIn("equip_files_id", existEquipFileIds));
            if (CollUtil.isNotEmpty(deleteFiles)) {
                int count =
                        projEquipFilesMapper.deleteBatchIds(deleteFiles.stream().map(ProjEquipFiles::getEquipFilesId).collect(Collectors.toList()));
                log.info("作废附件. count: {}, detail: {}", count, JSONUtil.toJsonStr(deleteFiles));
            }
        }
        //新增时保存操作日志
        if (ObjectUtil.isEmpty(lisExtendDto.getEquipRecordVsLisId())) {
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(projEquipRecordVsLis.getEquipRecordVsLisId());
            projEquipRecordLog.setOperateName("添加设备");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
        }
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(dto.getEquipRecordVsLisExtendDTO().getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
        return Result.success();
    }

    /**
     * 保存设备记录表
     * <p>
     * 该表数据为公共字段, 即使切换通讯方式, 也都会存在.
     * </p>
     *
     * @param dto 需要保存的内容
     */
    public ProjEquipRecord saveEquipRecord(ProjEquipRecordVsLisExtendDTO dto) {
        //保存设备记录信息
        ProjEquipRecord projEquipRecord = projEquipRecordConvert.dto2Po(dto);
        //设备类型
        projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_TYPE,
                dto.getEquipTypeName()));
        //设备厂商
        projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY, dto.getEquipFactoryName()));
        //设备型号
        projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO,
                dto.getEquipModelName()));
        //通讯方式
        projEquipRecord.setCommModeKey(dto.getCommModeObjExtend().getId());
        projEquipRecord.setCommMode(dto.getCommModeObjExtend().getName());
        //查询LIS产品id
        if (dto.getYyProductId() == null) {
            DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                    ProjProductEnum.LIS.getProductName()).last(" limit 1"));
            projEquipRecord.setYyProductId(product.getYyProductId());
        }
        //判断是否对接，如果更新为是，则将不对接原因置空
        if (dto.getRequiredFlag() == 1) {
            projEquipRecord.setStopReason(StrUtil.EMPTY);
        }
        if (projEquipRecord.getEquipRecordId() == null) {
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            equipRecordMapper.insert(projEquipRecord);
        } else {
            equipRecordMapper.updateById(projEquipRecord);
        }
        return projEquipRecord;
    }

    /**
     * 更新设备文件
     *
     * @param saveEquipFileDTO 带保存的设备记录表内容
     */
    public void updateOrSaveEquipFile(List<LisEquipFileExtendDTO> extendDTOS, String fileTypeCode,
                                      SaveEquipFileDTO saveEquipFileDTO) {
        if (CollUtil.isNotEmpty(extendDTOS)) {
            String fileSceneCode = ProjProductEnum.LIS.getFileSceneCode();
            // 查询模板信息, 保存用
            RuleProjectRuleConfig ruleProjectRuleConfig =
                    ruleProjectRuleConfigMapper.selectOne(new QueryWrapper<RuleProjectRuleConfig>().eq(
                            "scene_code",
                            fileSceneCode).eq("project_rule_code", fileTypeCode));
            if (ObjectUtil.isEmpty(ruleProjectRuleConfig)) {
                log.warn("保存lis设备时未查询到附件模板信息, 跳过. fileTypeCode: {}, detail: {}", fileTypeCode,
                        JSONUtil.toJsonStr(saveEquipFileDTO));
                return;
            }
            Long hospitalInfoId = saveEquipFileDTO.getLisExtendDto().getHospitalInfoId();
            LisEquipFileExtendDTO extendDTO = extendDTOS.get(0);
            // 转换对象
            ProjEquipFiles projEquipFiles = new ProjEquipFiles();
            projEquipFiles.setProjectFileId(extendDTO.getProjectFileId());
            projEquipFiles.setProductEquipRecordId(saveEquipFileDTO.getProjEquipRecordVsLis().getEquipRecordVsLisId());
            projEquipFiles.setHospitalInfoId(hospitalInfoId);
            projEquipFiles.setProjectInfoId(saveEquipFileDTO.getProjEquipRecord().getProjectInfoId());
            projEquipFiles.setFileItemCode(fileSceneCode);
            projEquipFiles.setFileTypeCode(fileTypeCode);
            projEquipFiles.setFileTypeName(ruleProjectRuleConfig.getProjectRuleContent());
            // 查询equipFile表中是否存在
            List<ProjEquipFiles> equipFiles = projEquipFilesMapper.selectList(new QueryWrapper<ProjEquipFiles>()
                    .eq("hospital_info_id", hospitalInfoId)
                    .eq("file_item_code", fileSceneCode)
                    .eq("file_type_code", fileTypeCode).eq("project_file_id", extendDTO.getProjectFileId()));
            if (CollUtil.isEmpty(equipFiles)) {
                projEquipFiles.setEquipFilesId(SnowFlakeUtil.getId());
                int count = projEquipFilesMapper.insert(projEquipFiles);
                log.info("新增设备文件. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipFiles));
            } else {
                projEquipFiles.setEquipFilesId(equipFiles.get(0).getEquipFilesId());
                int count = projEquipFilesMapper.updateById(projEquipFiles);
                log.info("更新设备文件. count: {}, detail: {}", count, JSONUtil.toJsonStr(projEquipFiles));
            }
            saveEquipFileDTO.getExistEquipFileIds().add(projEquipFiles.getEquipFilesId());
        }
    }

    /**
     * 将不需要保存的字段置空, 这样做, 在切换通讯方式时会将上一种通讯方式中不需要的字段置空
     * <p>
     * 使用此方法只能保存字符串类型的字段
     * </p>
     *
     * @param surveyMap            此次保存内容, map形式
     * @param projEquipRecordVsLis 带保存的对象
     * @param fieldCodes           字段编码, 可用数组的方式传入, 所有字段
     */
    private void setEmpty(Map<String, String> surveyMap, ProjEquipRecordVsLis projEquipRecordVsLis,
                          String... fieldCodes) throws NoSuchFieldException, IllegalAccessException {
        // 使用反射的方式设置内容
        Class<? extends ProjEquipRecordVsLis> vsLisClass = projEquipRecordVsLis.getClass();
        for (String fieldCode : fieldCodes) {
            if (surveyMap.containsKey(fieldCode)) {
                continue;
            }
            Field field = vsLisClass.getDeclaredField(fieldCode);
            field.setAccessible(true);
            field.set(projEquipRecordVsLis, null);
        }
    }


    /**
     * 新增LIS设备信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateEquipToLis(ProjEquipRecordVsLisDTO dto) {
        //保存设备记录信息
        ProjEquipRecord projEquipRecord = projEquipRecordConvert.dto2Po(dto);
        //设备类型
        projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_TYPE,
                dto.getEquipTypeName()));
        //设备厂商
        projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY, dto.getEquipFactoryName()));
        //设备型号
        projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO,
                dto.getEquipModelName()));
        //通讯方式
        projEquipRecord.setCommModeKey(dto.getCommModeObj().getId());
        projEquipRecord.setCommMode(dto.getCommModeObj().getName());
        //查询LIS产品id
        if (dto.getYyProductId() == null) {
            DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                    ProjProductEnum.LIS.getProductName()).last(" limit 1"));
            projEquipRecord.setYyProductId(product.getYyProductId());
        }
        //判断是否对接，如果更新为是，则将不对接原因置空
        if (dto.getRequiredFlag() == 1) {
            projEquipRecord.setStopReason("");
        }
        if (projEquipRecord.getEquipRecordId() == null) {
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            equipRecordMapper.insert(projEquipRecord);
        } else {
            equipRecordMapper.updateById(projEquipRecord);
        }
        //保存Lis设备记录
        ProjEquipRecordVsLis projEquipRecordVsLis = new ProjEquipRecordVsLis();
        BeanUtil.copyProperties(dto, projEquipRecordVsLis);
        projEquipRecordVsLis.setEquipRecordId(projEquipRecord.getEquipRecordId());
        if (projEquipRecordVsLis.getEquipRecordVsLisId() == null) {
            projEquipRecordVsLis.setEquipRecordVsLisId(SnowFlakeUtil.getId());
            this.insert(projEquipRecordVsLis);
        } else {
            this.updateById(projEquipRecordVsLis);
        }
        //保存设备附件
        List<ProjEquipFilesDTO> equipFileList = dto.getEquipFileList();
        if (CollectionUtil.isNotEmpty(equipFileList)) {
            // 校验哪些附件信息没进行保存。 存储模板信息到设备附件表中
            List<String> fileCodeList =
                    equipFileList.stream().map(ProjEquipFilesDTO::getFileTypeCode).collect(Collectors.toList());
            List<RuleProjectRuleConfig> ruleProjectRuleConfigs =
                    ruleProjectRuleConfigMapper.selectList(new QueryWrapper<RuleProjectRuleConfig>()
                            .eq("scene_code", dto.getEquipFileList().get(0).getFileItemCode())
                            .notIn("project_rule_code", fileCodeList)
                    );
            if (CollectionUtil.isNotEmpty(ruleProjectRuleConfigs)) {
                for (RuleProjectRuleConfig ruleProjectRuleConfig : ruleProjectRuleConfigs) {
                    // 判断当前附件是否已在业务表 存在
                    List<ProjEquipFiles> projEquipFiles =
                            projEquipFilesMapper.selectList(new QueryWrapper<ProjEquipFiles>()
                                    .eq("file_type_code", ruleProjectRuleConfig.getProjectRuleCode())
                                    .eq("product_equip_record_id", projEquipRecordVsLis.getEquipRecordVsLisId())
                            );
                    if (CollectionUtil.isEmpty(projEquipFiles)) {
                        ProjEquipFilesDTO projEquipFilesDTO = new ProjEquipFilesDTO();
                        projEquipFilesDTO.setFileTypeCode(ruleProjectRuleConfig.getProjectRuleCode());
                        projEquipFilesDTO.setFileTypeName(ruleProjectRuleConfig.getProjectRuleContent());
                        projEquipFilesDTO.setFileItemCode(ruleProjectRuleConfig.getSceneCode());
                        projEquipFilesDTO.setOrderNo(ruleProjectRuleConfig.getOrderNo());
                        projEquipFilesDTO.setRequiredFlag(ruleProjectRuleConfig.getRequiredFlag());
                        equipFileList.add(projEquipFilesDTO);
                    }
                }
            }
            for (ProjEquipFilesDTO projEquipFilesDTO : equipFileList) {
                ProjEquipFiles projEquipFiles = projEquipFilesConvert.dto2Po(projEquipFilesDTO);
                if (projEquipFilesDTO.getEquipFilesId() == null) {
                    projEquipFiles.setEquipFilesId(SnowFlakeUtil.getId());
                    projEquipFiles.setProductEquipRecordId(projEquipRecordVsLis.getEquipRecordVsLisId());
                    projEquipFiles.setHospitalInfoId(dto.getHospitalInfoId());
                    projEquipFiles.setProjectInfoId(projEquipRecord.getProjectInfoId());
                    projEquipFilesMapper.insert(projEquipFiles);
                } else {
                    projEquipFilesMapper.updateById(projEquipFiles);
                }
            }
        }
        //如果是更新,需要同步数据给Lis
        if (dto.getEquipRecordVsLisId() != null) {
            updateEquipToLisAnalyManager(projEquipRecordVsLis.getEquipRecordVsLisId());
        }
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(dto.getProjectInfoId(), dto.getHospitalInfoId(), ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
        return Result.success();
    }

    /**
     * 查看LIS设备信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjEquipRecordVsLisVO> viewEquipToLis(ViewProjEquipRecordDTO dto) {
        ProjEquipRecordVsLisVO projEquipRecordVsLisVO = new ProjEquipRecordVsLisVO();
        //查询LIS设备信息
        ProjEquipRecordVsLis projEquipRecordVsLis = projEquipRecordVsLisMapper.selectById(dto.getEquipRecordVsLisId());
        if (ObjectUtil.isEmpty(projEquipRecordVsLis)) {
            return Result.fail("未查询到设备记录信息！");
        }
        BeanUtil.copyProperties(projEquipRecordVsLis, projEquipRecordVsLisVO);
        //查询设备记录信息
        ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(projEquipRecordVsLis.getEquipRecordId());
        // 查询对应的医院信息
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(projEquipRecord.getHospitalInfoId());
        if (ObjectUtil.isNotEmpty(hospitalInfo)) {
            projEquipRecordVsLisVO.setHospitalInfoName(hospitalInfo.getHospitalName());
        }
        BeanUtil.copyProperties(projEquipRecord, projEquipRecordVsLisVO);
        BaseCodeNameResp baseCodeNameResp = new BaseCodeNameResp();
        baseCodeNameResp.setId(projEquipRecord.getCommModeKey());
        baseCodeNameResp.setName(projEquipRecord.getCommMode());
        projEquipRecordVsLisVO.setCommModeObj(baseCodeNameResp);
        //查询附件信息
        List<ProjEquipFilesVO> fileList = Lists.newArrayList();
        fileList = projEquipFilesMapper.findEquipFiles(dto.getEquipRecordVsLisId(), dto.getIsMobile());
        if (CollectionUtil.isNotEmpty(fileList)) {
            fileList.forEach(v -> {
                //获取附件临时访问路径
                if (StringUtils.isNotEmpty(v.getFilePath())) {
                    String temporaryUrlFile = OBSClientUtils.getTemporaryUrl(v.getFilePath(), 3600);
                    v.setFilePath(temporaryUrlFile);
                }
                //返回上传附件限制类型
                if (ObjectUtil.isNotEmpty(v.getLimitType())) {
                    v.setLimitTypeArray(v.getLimitType().split(","));
                }
            });
        } else {
            //老系统导入的设备记录没有附件列表，需要查询附件模版返回给前端
            List<ProjProjectFileRuleVO> ruleList =
                    ruleProjectRuleConfigMapper.getTemplateByProductCode(
                            ProjProductEnum.LIS.getFileSceneCode(), dto.getIsMobile());
            for (ProjProjectFileRuleVO projProjectFileRuleVO : ruleList) {
                ProjEquipFilesVO projEquipFilesVO = new ProjEquipFilesVO();
                BeanUtil.copyProperties(projProjectFileRuleVO, projEquipFilesVO);
                if (ObjectUtil.isNotEmpty(projEquipFilesVO.getLimitType())) {
                    projEquipFilesVO.setLimitTypeArray(projEquipFilesVO.getLimitType().split(","));
                }
                fileList.add(projEquipFilesVO);
            }
        }
        projEquipRecordVsLisVO.setEquipFileList(fileList);
        // 医院id为 -1 时 赋值为null
        if (projEquipRecordVsLisVO.getHospitalInfoId() != null && projEquipRecordVsLisVO.getHospitalInfoId() == -1) {
            projEquipRecordVsLisVO.setHospitalInfoId(null);
        }
        return Result.success(projEquipRecordVsLisVO);
    }

    /**
     * 查看LIS设备信息
     *
     * @param dto 请求值
     * @return 结果, 带数据, 新增、编辑使用
     */
    public Result<ProjEquipRecordVsLisWrapperVO> getEquipToLis(ViewProjEquipRecordDTO dto) {
        if (ObjectUtil.isEmpty(dto.getIsMobile())) {
            dto.setIsMobile(NumberEnum.NO_0.num());
        }
        ProjEquipRecordVsLisWrapperVO wrapperVO = new ProjEquipRecordVsLisWrapperVO();
        ProjEquipLisFileTypeLimitVO lisFileTypeLimitVO = new ProjEquipLisFileTypeLimitVO();
        wrapperVO.setLisFileTypeLimitVO(lisFileTypeLimitVO);
        // 表单内容
        ProjEquipRecordVsLisExtendVO projEquipRecordVsLisVO = new ProjEquipRecordVsLisExtendVO();
        projEquipRecordVsLisVO.setCustomInfoId(dto.getCustomInfoId());
        projEquipRecordVsLisVO.setProjectInfoId(dto.getProjectInfoId());
        wrapperVO.setEquipRecordVsLisExtendVO(projEquipRecordVsLisVO);
        //查询LIS设备信息, 新增时不处理
        if (ObjectUtil.isNotEmpty(dto.getEquipRecordVsLisId())) {
            ProjEquipRecordVsLis projEquipRecordVsLis =
                    projEquipRecordVsLisMapper.selectById(dto.getEquipRecordVsLisId());
            if (ObjectUtil.isEmpty(projEquipRecordVsLis)) {
                return Result.fail("未查询到设备记录信息！");
            }
            BeanUtil.copyProperties(projEquipRecordVsLis, projEquipRecordVsLisVO);
            //查询设备记录信息
            ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(projEquipRecordVsLis.getEquipRecordId());
            // 查询对应的医院信息
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(projEquipRecord.getHospitalInfoId());
            if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                projEquipRecordVsLisVO.setHospitalInfoName(hospitalInfo.getHospitalName());
            }
            BeanUtil.copyProperties(projEquipRecord, projEquipRecordVsLisVO);
            BaseCodeNameVO extend = new BaseCodeNameVO();
            extend.setId(projEquipRecord.getCommModeKey());
            extend.setName(projEquipRecord.getCommMode());
            // 获取字段
            List<String> configEquipSurveys =
                    equipRecordVsLisServiceImplExtend.findConfigEquipSurveysStr(projEquipRecord.getCommModeKey());
            extend.setSurveyConfigFields(configEquipSurveys);
            projEquipRecordVsLisVO.setCommModeObjExtend(extend);
        }
        // 设置限制类型
        List<ProjProjectFileRuleVO> ruleList =
                ruleProjectRuleConfigMapper.getTemplateByProductCode(
                        ProjProductEnum.LIS.getFileSceneCode(), dto.getIsMobile());
        // 设置文件类型限制
        initLimitAndFileList(ruleList, lisFileTypeLimitVO, projEquipRecordVsLisVO);
        //查询附件信息
        List<ProjEquipFilesVO> fileList = projEquipFilesMapper.findEquipFiles(dto.getEquipRecordVsLisId(),
                dto.getIsMobile());
        // 过滤附件id不为null和0的
        if (CollUtil.isNotEmpty(fileList)) {
            fileList = fileList.stream().filter(file -> file.getProjectFileId() != null && file.getProjectFileId() != 0).collect(Collectors.toList());
        }
        // 设置表单内容
        if (CollUtil.isNotEmpty(fileList)) {
            setVsLisVO(fileList, projEquipRecordVsLisVO);
        }
        // 医院id为 -1 时 赋值为null
        if (projEquipRecordVsLisVO.getHospitalInfoId() != null && projEquipRecordVsLisVO.getHospitalInfoId() == -1) {
            projEquipRecordVsLisVO.setHospitalInfoId(null);
        }
        return Result.success(wrapperVO);
    }

    /**
     * 初始化文件类型限制和附件
     *
     * @param ruleList               规则配置
     * @param lisFileTypeLimitVO     类型限制
     * @param projEquipRecordVsLisVO 表单内容
     */
    private void initLimitAndFileList(List<ProjProjectFileRuleVO> ruleList,
                                      ProjEquipLisFileTypeLimitVO lisFileTypeLimitVO,
                                      ProjEquipRecordVsLisExtendVO projEquipRecordVsLisVO) {
        for (ProjProjectFileRuleVO projProjectFileRuleVO : ruleList) {
            List<String> limitList = Arrays.stream(projProjectFileRuleVO.getLimitType().split(
                    StrUtil.COMMA)).collect(Collectors.toList());
            // 设置全貌图片
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "instrument_appear")) {
                lisFileTypeLimitVO.setDeviceFullImg(limitList);
                projEquipRecordVsLisVO.setDeviceFullImg(CollUtil.newArrayList());
            }
            // 设置仪器配置图片
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "instrument_config")) {
                lisFileTypeLimitVO.setDeviceConfigImg(limitList);
                projEquipRecordVsLisVO.setDeviceConfigImg(CollUtil.newArrayList());
            }
            // 设置双工通信日志
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "duplex_tongxun")) {
                lisFileTypeLimitVO.setDuplexCommunicationLog(limitList);
                projEquipRecordVsLisVO.setDuplexCommunicationLog(CollUtil.newArrayList());
            }
            // 设置通讯协议文档
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "tongxun_text")) {
                lisFileTypeLimitVO.setCommunicationProtocolDoc(limitList);
                projEquipRecordVsLisVO.setCommunicationProtocolDoc(CollUtil.newArrayList());
            }
            // 设置样本结果图片
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "yangben_result")) {
                lisFileTypeLimitVO.setSampleResult(limitList);
                projEquipRecordVsLisVO.setSampleResult(CollUtil.newArrayList());
            }
            // 设置质控结果串
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "zhikong_result")) {
                lisFileTypeLimitVO.setQualityControlResultString(limitList);
                projEquipRecordVsLisVO.setQualityControlResultString(CollUtil.newArrayList());
            }
            // 设置急诊结果原始串
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "jizhen_result")) {
                lisFileTypeLimitVO.setEmergencyResultString(limitList);
                projEquipRecordVsLisVO.setEmergencyResultString(CollUtil.newArrayList());
            }
            // 设置普通结果原始串
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "putong_yuanshichuan")) {
                lisFileTypeLimitVO.setNormalResultString(limitList);
                projEquipRecordVsLisVO.setNormalResultString(CollUtil.newArrayList());
            }
            // 设置数据库文件
            if (StrUtil.equals(projProjectFileRuleVO.getFileTypeCode(), "db_file")) {
                lisFileTypeLimitVO.setDbFile(limitList);
                projEquipRecordVsLisVO.setDbFile(CollUtil.newArrayList());
            }
        }
    }

    /**
     * 设置表单附件内容
     *
     * @param fileList               以上传的附件内容
     * @param projEquipRecordVsLisVO 表单对象
     */
    public void setVsLisVO(List<ProjEquipFilesVO> fileList, ProjEquipRecordVsLisExtendVO projEquipRecordVsLisVO) {
        fileList.forEach(v -> {
            String filePath = StrUtil.EMPTY;
            //获取附件临时访问路径
            if (StringUtils.isNotEmpty(v.getFilePath())) {
                filePath = OBSClientUtils.getTemporaryUrl(v.getFilePath(), 3600);
            }
            LisEquipFileExtendVO vo = LisEquipFileExtendVO.builder()
                    .url(filePath)
                    .projectFileId(ObjectUtil.isEmpty(v.getProjectFileId()) ? StrUtil.EMPTY
                            : StrUtil.toString(v.getProjectFileId()))
                    .name(StrUtil.isBlank(v.getFileName()) ? StrUtil.EMPTY : v.getFileName())
                    .build();
            // 设置全貌图片
            if (StrUtil.equals(v.getFileTypeCode(), "instrument_appear")) {
                projEquipRecordVsLisVO.getDeviceFullImg().add(vo);
            }
            // 设置仪器配置图片
            if (StrUtil.equals(v.getFileTypeCode(), "instrument_config")) {
                projEquipRecordVsLisVO.getDeviceConfigImg().add(vo);
            }
            // 设置双工通信日志
            if (StrUtil.equals(v.getFileTypeCode(), "duplex_tongxun")) {
                projEquipRecordVsLisVO.getDuplexCommunicationLog().add(vo);
            }
            // 设置通讯协议文档
            if (StrUtil.equals(v.getFileTypeCode(), "tongxun_text")) {
                projEquipRecordVsLisVO.getCommunicationProtocolDoc().add(vo);
            }
            // 设置样本结果图片
            if (StrUtil.equals(v.getFileTypeCode(), "yangben_result")) {
                projEquipRecordVsLisVO.getSampleResult().add(vo);
            }
            // 设置质控结果串
            if (StrUtil.equals(v.getFileTypeCode(), "zhikong_result")) {
                projEquipRecordVsLisVO.getQualityControlResultString().add(vo);
            }
            // 设置急诊结果原始串
            if (StrUtil.equals(v.getFileTypeCode(), "jizhen_result")) {
                projEquipRecordVsLisVO.getEmergencyResultString().add(vo);
            }
            // 设置普通结果原始串
            if (StrUtil.equals(v.getFileTypeCode(), "putong_yuanshichuan")) {
                projEquipRecordVsLisVO.getNormalResultString().add(vo);
            }
            // 设置数据库文件
            if (StrUtil.equals(v.getFileTypeCode(), "db_file")) {
                projEquipRecordVsLisVO.getDbFile().add(vo);
            }
        });
    }

    /**
     * 查询LIS系统的云健康设备数据
     *
     * @param selectDTO
     * @return
     */
    @Override
    public Map<Long, List<CloudEquipVO>> selectCloudEquipData(LisCloudEquipSelectDTO selectDTO) {
        return equipRecordCommonService.findEquipmentDataBatch(selectDTO.getCustomInfoId(),
                selectDTO.getProjectType(), ProjProductEnum.LIS.getProductCode(),
                dto -> {
                    CloudEquipVO cloudEquipVO = new CloudEquipVO();
                    cloudEquipVO.setCloudEquipId(dto.getId());
                    StringBuffer sb = new StringBuffer();
                    // 医院名称
                    if (ObjectUtil.isNotEmpty(dto.getHospitalName())) {
                        sb.append(dto.getHospitalName() + "-");
                    }
                    // 厂商
                    if (ObjectUtil.isNotEmpty(dto.getFactoryName())) {
                        sb.append(dto.getFactoryName() + "/");
                    }
                    // 类型
                    if (ObjectUtil.isNotEmpty(dto.getTypeName())) {
                        sb.append(dto.getTypeName() + "/");
                    }
                    // 名称
                    if (ObjectUtil.isNotEmpty(dto.getModelName())) {
                        sb.append(dto.getModelName() + "/");
                    }
                    // 型号
                    if (ObjectUtil.isNotEmpty(dto.getTypeName())) {
                        sb.append(dto.getStyleName());
                    }
                    cloudEquipVO.setCloudEquipNameAndHospital(sb.toString());
                    cloudEquipVO.setCloudEquipName(dto.getModelName());
                    return cloudEquipVO;
                });
    }

    /**
     * Lis设备自动对照云健康设备信息（根据设备名称进行匹配）
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<LisEquipVo>> compareEquipmentToLis(LisEquipSelectDTO dto) {
        // 查询该项目下的Lis设备数据
        dto.setRequiredFlag(1);
        List<LisEquipVo> lisEquipVos = projEquipRecordVsLisMapper.selectLisEquipData(dto);
        for (LisEquipVo vo : lisEquipVos) {
            // 组装前端展示的设备名称
            vo.setEquipModelNameStr(vo.getEquipFactoryName() + "/" + vo.getEquipTypeName() + "/" + vo.getEquipModelName());
        }
        // 查询 LIS系统云健康设备数据
        LisCloudEquipSelectDTO lisEquipSelectDTO = new LisCloudEquipSelectDTO();
        lisEquipSelectDTO.setCustomInfoId(dto.getCustomInfoId());
        // 查询项目类型
        ProjProjectInfo projectInfo = commonService.getProjectInfo(dto.getProjectInfoId());
        lisEquipSelectDTO.setProjectType(projectInfo.getProjectType());
        Map<Long, List<CloudEquipVO>> result = selectCloudEquipData(lisEquipSelectDTO);
        // 设备匹配
        equipRecordCommonService.compareEquip(result, lisEquipVos, ProductEquipSurveyMenuEnum.LISEQUIP.getName());
        return Result.success(lisEquipVos);
    }

    /**
     * 调用Lis开放平台进行设备检测
     *
     * @param equipRecordVsLisIds
     * @return
     */
    @Override
    public Result checkEquipToLisAnalyManager(List<Long> equipRecordVsLisIds) {
        List<CheckEquipToAnalyManagerDTO> dtoList = new ArrayList<>();
        // 检测设备数据完整性
        for (Long equipRecordVsLisId : equipRecordVsLisIds) {
            // 查询LIS设备数据
            LisEquipSelectDTO lisEquipSelectDTO = new LisEquipSelectDTO();
            lisEquipSelectDTO.setEquipRecordVsLisId(equipRecordVsLisId);
            List<LisEquipVo> lisEquipVos = projEquipRecordVsLisMapper.selectLisEquipData(lisEquipSelectDTO);
            if (CollectionUtil.isEmpty(lisEquipVos)) {
                return Result.fail("请检查选择的设备数据是否正常");
            }
            LisEquipVo lisEquipVo = lisEquipVos.get(0);
            // 判断设备是否需要对接，当不需要时 先让设置成 需要对接
            if (!lisEquipVo.getRequiredFlagBool()) {
                return Result.fail(lisEquipVo.getEquipModelName() + " : 该设备请先设置成需要对接后,再进行检测");
            }
            CheckEquipToAnalyManagerDTO checkEquipToAnalyManagerDTO = new CheckEquipToAnalyManagerDTO();
            checkEquipToAnalyManagerDTO.setHospitalId(lisEquipVo.getHospitalInfoId());
            checkEquipToAnalyManagerDTO.setProductEquipId(lisEquipVo.getEquipRecordVsLisId());
            checkEquipToAnalyManagerDTO.setProductCode("LIS");
            checkEquipToAnalyManagerDTO.setCloudEquipId(lisEquipVo.getCloudEquipId());
            checkEquipToAnalyManagerDTO.setYyProductId(lisEquipVo.getYyProductId());
            dtoList.add(checkEquipToAnalyManagerDTO);
        }
        // 检测设备数据
        log.info("LIS设备开始进行设备检测 , {}", JSONUtil.toJsonStr(dtoList));
        Result result = equipSummaryService.checkEquipToLisAnalyManager(dtoList);
        log.info("LIS设备设备检测结束 , {}", JSONUtil.toJsonStr(result));
        if (result.isSuccess()) {
            // 当检测通过时 更新LIS设备状态为 已通过
            return Result.success();
        } else {
            return Result.fail(result.getMsg());
        }
    }

    /**
     * LIS系统修改云健康对照设备
     *
     * @param dtoList
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<String> updateCloudEquipToLis(List<UpdateLisCloudEquipDTO> dtoList) {
        List<Long> cloudEquipIds = dtoList.stream()
                .map(vo -> {
                    if (ObjectUtil.isNotEmpty(vo.getCloudEquipVO())) {
                        return vo.getCloudEquipVO().getCloudEquipId();
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (UpdateLisCloudEquipDTO dto : dtoList) {
            // 设备未进行对照, 不做处理
            if (ObjectUtil.isEmpty(dto.getCloudEquipVO())) {
                continue;
            }
            int count = 0;
            // 若交付设备对照多次进行记录
            for (Long cloudEquipId : cloudEquipIds) {
                if (dto.getCloudEquipVO().getCloudEquipId().equals(cloudEquipId)) {
                    count++;
                }
            }
            // 获取设备记录lis拓展表数据
            ProjEquipRecordVsLis projEquipRecordVsLis =
                    projEquipRecordVsLisMapper.selectById(dto.getEquipRecordVsLisId());
            // 获取历史对照记录
            ProjEquipRecord projEquipRecordHistory =
                    equipRecordMapper.selectById(projEquipRecordVsLis.getEquipRecordId());
            // 定义新的对照记录
            Long equipRecordId = projEquipRecordVsLis.getEquipRecordId();
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipRecordId(equipRecordId);
            if (ObjectUtil.isNotEmpty(dto.getCloudEquipVO())) {
                projEquipRecord.setCloudEquipId(dto.getCloudEquipVO().getCloudEquipId());
                projEquipRecord.setCloudEquipName(dto.getCloudEquipVO().getCloudEquipName());
            }
            // 一个云健康设备禁止重复对照多个交付设备 【赵飞】
            if (count > 1) {
                sb.append(dto.getCloudEquipVO().getCloudEquipName())
                        .append("已对照交付设备【")
                        .append(projEquipRecordHistory.getEquipFactoryName())
                        .append("/")
                        .append(projEquipRecordHistory.getEquipTypeName())
                        .append("/")
                        .append(projEquipRecordHistory.getEquipModelName())
                        .append("】，请勿重复对照;");
                sb.append("\n");
                log.warn(sb.toString());
                continue;
            }
            // 如果云健康设备已经对照, 不做处理
            if (ObjectUtil.isNotEmpty(projEquipRecordHistory.getCloudEquipId())
                    && dto.getCloudEquipVO().getCloudEquipId().longValue() == projEquipRecordHistory.getCloudEquipId()) {
                log.warn("此lis设备已对照,不做处理. cloudEquipId: {}",
                        projEquipRecordHistory.getCloudEquipName() + "-" + projEquipRecordHistory.getCloudEquipId());
                continue;
            }
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(projEquipRecordHistory.getHospitalInfoId());
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                log.warn("未查询到医院信息. hospitalInfoId: {}", projEquipRecordHistory.getHospitalInfoId());
                continue;
            }
            // 若历史对照数据中云健康设备id不等于空, 则作废
            handlePreviousRecord(projEquipRecordHistory, projEquipRecord, hospitalInfo);
            // 修改对照后，检查临时检测表中是否存在数据，当存在时候 更新检测进度以及检测明细数据
            handleLisCheckDetails(projEquipRecordHistory, projEquipRecord, hospitalInfo);
            count = equipRecordMapper.updateById(projEquipRecord);
            log.info("更新项目检测记录. count: {} equipRecord: {}", count, projEquipRecord);
        }
        if (ObjectUtil.isNotEmpty(sb)) {
            return Result.fail(sb.toString());
        } else {
            // 获取结果详情
            if (CollUtil.isNotEmpty(dtoList)) {
                UpdateLisCloudEquipDTO updateLisCloudEquipDTO = dtoList.get(0);
                ProjEquipRecordVsLis vsLis =
                        projEquipRecordVsLisMapper.selectOne(new QueryWrapper<ProjEquipRecordVsLis>().eq(
                                "equip_record_vs_lis_id", updateLisCloudEquipDTO.getEquipRecordVsLisId()));
                if (ObjectUtil.isNotEmpty(vsLis)) {
                    ProjEquipRecord equipRecord = equipRecordMapper.selectOne(new QueryWrapper<ProjEquipRecord>().eq(
                            "equip_record_id",
                            vsLis.getEquipRecordId()));
                    if (ObjectUtil.isNotEmpty(equipRecord)) {
                        ProjEquipSendToCloudDTO cloudDTO = new ProjEquipSendToCloudDTO();
                        cloudDTO.setCustomInfoId(equipRecord.getCustomInfoId());
                        cloudDTO.setProjectInfoId(equipRecord.getProjectInfoId());
                        try {
                            Result<String> checkResult = checkSendToMsunVsLis(cloudDTO, NumberEnum.NO_0.num());
                            log.info("lis设备对照, 结果详情获取完成. dto: {}, result: {}", JSONUtil.toJsonStr(cloudDTO),
                                    JSONUtil.toJsonStr(checkResult));
                        } catch (Throwable e) {
                            log.error("设备对照, 获取检测结果详情异常. message: {}, e=", e.getMessage(), e);
                        }
                    }
                }
            }
            return Result.success();
        }
    }

    /**
     * 处理原云健康设备的对照数据
     *
     * @param projEquipRecordHistory 历史对照记录
     * @param projEquipRecord        新对照记录
     * @param hospitalInfo           医院信息
     */
    private void handlePreviousRecord(ProjEquipRecord projEquipRecordHistory, ProjEquipRecord projEquipRecord,
                                      ProjHospitalInfo hospitalInfo) {
        if (ObjectUtil.isEmpty(projEquipRecordHistory.getCloudEquipId())) {
            return;
        }
        log.info("处理重新对照的lis设备. 对照前(交付设备id-设备名称(云健康设备id)): {}, 对照后(交付设备id-设备名称(云健康设备id)): {}",
                projEquipRecordHistory.getEquipRecordId() + "-" + projEquipRecordHistory.getCloudEquipName()
                        + "(" + projEquipRecordHistory.getCloudEquipId() + ")",
                projEquipRecord.getEquipRecordId() + "-" + projEquipRecord.getCloudEquipName()
                        + "(" + projEquipRecord.getCloudEquipId() + ")"
        );
        // 更新标识为未检测
        ProjEquipRecord update = new ProjEquipRecord();
        update.setEquipRecordId(projEquipRecord.getEquipRecordId());
        update.setEquipStatus(EquipStatusEnum.DEVELOPED.getCode());
        update.setTestProgress("0%");
        int count = equipRecordMapper.updateById(update);
        log.info("重置lis项目设备记录. count: {}, record: {}", count, update);
        // 查询原对照云健康设备的检测结果明细, 做删除
        Long historyCloudEquipId = projEquipRecordHistory.getCloudEquipId();
        Long cloudHospitalId = hospitalInfo.getCloudHospitalId();
        count = projEquipCheckVsLisMapper.delete(new QueryWrapper<ProjEquipCheckVsLis>()
                .eq("equip_record_vs_lis_id", historyCloudEquipId)
                .eq("cloud_hospital_id", cloudHospitalId));
        log.info("作废lis设备检测结果明细. count: {}, condition: {}", count, historyCloudEquipId + "-" + cloudHospitalId);
    }

    /**
     * 自动检测临时表处理, 若存在则先获取，再删除, 将获取的设备检测结果取出进行更新, 取出检测明心进行新增
     *
     * @param projEquipRecordHistory 主表历史记录
     * @param projEquipRecord        项目设备检测主表
     */
    private void handleLisCheckDetails(ProjEquipRecord projEquipRecordHistory,
                                       ProjEquipRecord projEquipRecord,
                                       ProjHospitalInfo hospitalInfo) {

        List<TmpEquipCheckDetail> tmpEquipCheckDetails =
                tmpEquipCheckDetailMapper.selectList(new QueryWrapper<TmpEquipCheckDetail>()
                        .in("cloud_equip_id", projEquipRecord.getCloudEquipId(),
                                projEquipRecordHistory.getCloudEquipId())
                        .eq("cloud_hospital_id", hospitalInfo.getCloudHospitalId())
                );
        // 若暂存表有数据, 先更新, 再作废
        if (CollectionUtil.isNotEmpty(tmpEquipCheckDetails)) {
            for (TmpEquipCheckDetail tmpEquipCheckDetail : tmpEquipCheckDetails) {
                // 当已经匹配上后 ，直接删除临时表中的数据
                int count = tmpEquipCheckDetailMapper.deleteById(tmpEquipCheckDetail.getTmpEquipCheckVsLisId());
                log.info("删除暂存表lis设备检测结果明细. count: {}, detail: {}", count, tmpEquipCheckDetail);
                // 更新检测结果, 检测成功或者检测失败
                if (tmpEquipCheckDetail.getStatus() == EquipStatusEnum.TEST_PASS.getCode().intValue()
                        || tmpEquipCheckDetail.getStatus() == EquipStatusEnum.TEST_FAIL.getCode().intValue()) {
                    projEquipRecord.setEquipStatus(tmpEquipCheckDetail.getStatus());
                }
                projEquipRecord.setTestProgress(tmpEquipCheckDetail.getTestProgress());
                //先删除原来保存的设备进度记录
                count = projEquipCheckVsLisMapper.delete(new QueryWrapper<ProjEquipCheckVsLis>()
                        .eq("equip_record_vs_lis_id", projEquipRecordHistory.getCloudEquipId())
                        .eq("cloud_hospital_id", hospitalInfo.getCloudHospitalId())
                );
                log.info("删除lis设备检测结果明细. count: {}, condition: {}", count,
                        "equip_record_vs_lis_id: " + projEquipRecordHistory.getCloudEquipId() + ", cloud_hospital_id:"
                                + " " + hospitalInfo.getCloudHospitalId());
                //保存接口返回的记录
                List<ItemInfoVO> listData = JSON.parseArray(tmpEquipCheckDetail.getItemInfo(), ItemInfoVO.class);
                if (CollUtil.isEmpty(listData)) {
                    log.warn("暂存表lis设备检测结果明细为空. itemInfo: {}", tmpEquipCheckDetail.getItemInfo());
                    continue;
                }
                for (ItemInfoVO item : listData) {
                    ProjEquipCheckVsLis equipCheck = new ProjEquipCheckVsLis();
                    BeanUtil.copyProperties(item, equipCheck);
                    equipCheck.setEquipCheckVsLisId(SnowFlakeUtil.getId());
                    if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                        equipCheck.setHospitalName(hospitalInfo.getHospitalName());
                    }
                    equipCheck.setEquipRecordVsLisId(projEquipRecord.getCloudEquipId());
                    equipCheck.setCloudHospitalId(hospitalInfo.getCloudHospitalId());
                    log.info("设备检测进度保存参数:{}", JSONUtil.toJsonStr(equipCheck));
                    count = projEquipCheckVsLisMapper.insert(equipCheck);
                    log.info("更新设备lis检测结果明细. count: {}", count);
                }
            }
        }
    }

    /**
     * 老换新LIS设备自动对照
     *
     * @param yunEquipmentList
     */
    public void autoCompareCloudEquip(List<CloudEquipVO> yunEquipmentList, List<LisEquipVo> lisEquipVos) {
        // 判断设备名称是否一致，当一致时 对照成功 【已经对照过的设备不再进行自动对照】
        for (LisEquipVo lisEquipVo : lisEquipVos) {
            if (ObjectUtil.isEmpty(lisEquipVo.getCloudEquipId())) {
                for (CloudEquipVO vo : yunEquipmentList) {
                    if (lisEquipVo.getEquipModelName().equals(vo.getCloudEquipName())) {
                        ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                        projEquipRecord.setEquipRecordId(lisEquipVo.getEquipRecordId());
                        projEquipRecord.setCloudEquipId(vo.getCloudEquipId());
                        projEquipRecord.setCloudEquipName(vo.getCloudEquipName());
                        // 更新云健康设备数据信息
                        equipRecordMapper.updateById(projEquipRecord);
                        //首次完成自动对照的直接赋值云健康设备VO
                        CloudEquipVO vo1 = new CloudEquipVO();
                        vo1.setCloudEquipId(vo.getCloudEquipId());
                        vo1.setCloudEquipNameAndHospital(vo.getCloudEquipNameAndHospital());
                        vo1.setCloudEquipName(vo.getCloudEquipName());
                        lisEquipVo.setCloudEquipVO(vo1);
                    }
                }
            } else {
                for (CloudEquipVO vo : yunEquipmentList) {
                    if (lisEquipVo.getCloudEquipId().equals(vo.getCloudEquipId())) {
                        CloudEquipVO vo1 = new CloudEquipVO();
                        vo1.setCloudEquipId(vo.getCloudEquipId());
                        vo1.setCloudEquipNameAndHospital(vo.getCloudEquipNameAndHospital());
                        vo1.setCloudEquipName(vo.getCloudEquipName());
                        lisEquipVo.setCloudEquipVO(vo1);
                    }
                }
            }
        }
    }

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    @Override
    public Result commitFinish(ProjEquipVsProductFinishDTO dto) {
        //查询不满足提交完成操作的设备记录状态数量 【当时设计的前端传入 设备调研、设备准备里程碑节点id。后期前端成传入产品业务调研、产品准备节点id】
        // 判断节点类型【调研阶段、准备阶段】
        String nodeCode = "";
        Result<ProjMilestoneInfo> milestoneInfoResult = milestoneInfoService.selectById(dto.getMilestoneInfoId());
        String milestoneNodeCode = milestoneInfoResult.getData().getMilestoneNodeCode();
        Long projectInfoId1 = milestoneInfoResult.getData().getProjectInfoId();
        if (MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(0);
            statusList.add(2);
            Integer count = projEquipRecordVsLisMapper.getNoMeetRecordCount(statusList, projectInfoId1);
            if (count > 0) {
                return Result.fail("设置对接的设备提交申请后，需全部审核通过，才可提交完成！");
            }
            nodeCode = MilestoneNodeEnum.SURVEY_DEVICE.getCode();
        } else if (MilestoneNodeEnum.PREPARAT_PRODUCT.getCode().equals(milestoneNodeCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(0);
            statusList.add(1);
            statusList.add(2);
            statusList.add(3);
            statusList.add(4);
            statusList.add(6);
            Integer count = projEquipRecordVsLisMapper.getNoMeetRecordCount(statusList, projectInfoId1);
            if (count > 0) {
                return Result.fail("存在需要对接的设备未测试通过,请全部测试通过后再进此操作");
            }
            nodeCode = MilestoneNodeEnum.PREPARAT_DEVICE.getCode();
        }
        //更新任务计划明细状态
        List<ProjMilestoneTask> projMilestoneTasks =
                milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                        .eq("project_info_id", milestoneInfoResult.getData().getProjectInfoId())
                        .eq("milestone_node_code", nodeCode)
                );
        if (CollectionUtil.isEmpty(projMilestoneTasks)) {
            return Result.fail("请先分配产品业务调研后再进行提交完成");
        }
        //查询LIS产品id
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                ProjProductEnum.LIS.getProductName()).last(" limit 1"));
        List<Long> collect =
                projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
        List<ProjMilestoneTaskDetail> projMilestoneTaskDetails =
                milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("product_deliver_id", product.getYyProductId())
                        .in("milestone_task_id", collect)
                );
        if (CollectionUtil.isEmpty(projMilestoneTaskDetails)) {
            return Result.fail("Lis产品未查询到对应任务");
        }
        for (ProjMilestoneTaskDetail taskDetail : projMilestoneTaskDetails) {
            taskDetail.setCompleteStatus(1);
            milestoneTaskDetailMapper.updateById(taskDetail);
        }

        projTodoTaskService.updateTodoTaskStatusOne(projectInfoId1, DictProjectPlanItemEnum.SURVEY_DEVICE, product.getYyProductId(), ProjectPlanStatusEnum.FINISHED);
        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> checkSendToMsunVsLis(ProjEquipSendToCloudDTO dto) {
        return checkSendToMsunVsLis(dto, NumberEnum.NO_1.num());

    }

    public Result<String> checkSendToMsunVsLis(ProjEquipSendToCloudDTO dto, Integer updateStatusFlag) {
        ProjProjectInfo projectInfo = commonService.getProjectInfo(dto.getProjectInfoId());
        dto.setProjectType(projectInfo.getProjectType());
        //查询设备记录
        List<ProductEquipmentDto> equipmentDataDto = projEquipRecordVsLisMapper.findToMsunEquipRecord(dto);
        if (CollectionUtil.isEmpty(equipmentDataDto)) {
            log.warn("lis主动检测, 未查询到设备, 未对照云健康设备或设备已测试成功, 无需检测. dto: {}", JSONUtil.toJsonStr(dto));
            return Result.success();
        }

        // 有云健康设备id的设备（发送对照或从云健康手动对照）的正常走流程，没有云健康设备id的进行提示进行对照
        List<ProductEquipmentDto> equipmentData = equipmentDataDto.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOldEquipId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(equipmentData)) {
            StringBuilder sb = new StringBuilder();
            for (ProductEquipmentDto item : equipmentData) {
                if (ObjectUtil.isEmpty(item.getOldEquipId())) {
                    sb.append(item.getModel()).append("未对照云健康设备 \n");
                }
            }
            if (sb.length() > 0) {
                return Result.fail(sb.toString());
            }
            // 查询已开通可用的医院信息
            List<ProjHospitalInfo> hospitalInfoList = equipRecordCommonService.findHospitalInfos(dto.getCustomInfoId(),
                    dto.getProjectInfoId());
            if (CollectionUtil.isEmpty(hospitalInfoList)) {
                log.warn("lis主动检测, 未查询到医院, 请检查客户下的医院信息");
                return Result.fail("请检查客户下的医院信息");
            }
            for (ProductEquipmentDto dto1 : equipmentData) {
                if (ObjectUtil.isEmpty(dto1.getHospitalId())) {
                    log.error("lis主动检测, , 不可为空. equipment: {}, dto: {}", JSONUtil.toJsonStr(dto1), JSONUtil.toJsonStr(dto));
                    throw new CustomException("请维护设备医院信息, 不可为空.");
                }
            }
            ProjHospitalInfo hospitalInfo = hospitalInfoList.get(0);
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            String productCode = ProjProductEnum.LIS.getProductCode();
            try {
                //调用接口进行验证
                Map<Long, EquipmentStatusWrapperResult> mapData = equipRecordCommonService.getEquipmentStatusBatch(productCode, hospitalInfo, equipmentData);
                log.info("lis主动检测, 设备检测返回结果: {}", mapData);
                log.info("设备检测日志，返回结果={}", JSON.toJSONString(mapData));
                //更新数据状态
                EquipStatusUpdateDto updateDto = EquipStatusUpdateDto.builder()
                        .updateStatusFlag(updateStatusFlag)
                        .mapData(mapData)
                        .hospitalInfoList(hospitalInfoList)
                        .projectInfoId(dto.getProjectInfoId())
                        .build();
                equipRecordCommonService.updateEquipStatus(updateDto, productCode, equipmentData);
                equipRecordCommonService.updateEquipProjectTodoTaskStatus(projectInfo.getProjectInfoId(), null, ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
            } catch (Exception e) {
                log.error("lis设备检测失败, message: {}, e=", e.getMessage(), e);
                String errorMsg;
                if (e instanceof NullPointerException) {
                    errorMsg = "程序异常";
                } else {
                    errorMsg = e.getMessage();
                }
                return Result.fail(errorMsg);
            }
        }
        // 未对照过云健康设备的进行提示
        List<ProductEquipmentDto> unSendCloudEquipData = equipmentDataDto.stream().filter(e -> ObjectUtil.isEmpty(e.getOldEquipId())).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (ProductEquipmentDto item : unSendCloudEquipData) {
            if (ObjectUtil.isEmpty(item.getOldEquipId())) {
                sb.append(item.getModel() + "未对照云健康设备 \n");
            }
        }
        if (sb.length() > 0) {
//            return Result.fail(sb.toString());
            return Result.fail(102, "存在未对照的设备，请进行云健康设备对照后进行检测");
        }

        return Result.success();
    }

    /**
     * 保存设备进度
     * <p>
     * 删除设备结果, 保存最新记录
     * </p>
     *
     * @param equipment    新的设备信息
     * @param hospitalInfo 医院信息
     */
    public void batchInsertLisCheck(EquipmentStatusDto equipment, ProjHospitalInfo hospitalInfo) {
        log.info("设备检测日志，batchInsertLisCheck 方法，equipment={}，hospitalInfo={}", JSON.toJSONString(equipment), JSON.toJSONString(hospitalInfo));

        // 查询医院名称
        //先删除原来保存的设备进度记录
        int count = projEquipCheckVsLisMapper.delete(new QueryWrapper<ProjEquipCheckVsLis>()
                .eq("equip_record_vs_lis_id", equipment.getId())
                .eq("cloud_hospital_id", hospitalInfo.getCloudHospitalId())
        );
        log.info("设备检测日志，batchInsertLisCheck 方法，批量插入lis结果详情前删除历史记录. count: {}, equip_record_vs_lis_id:{}, cloud_hospital_id: {}, hospitalInfo:{}",
                count,
                equipment.getId(), hospitalInfo.getCloudHospitalId(), JSONUtil.toJsonStr(hospitalInfo));
        //保存接口返回的记录
        List<ItemInfoVO> listData = JSON.parseArray(equipment.getItemInfo(), ItemInfoVO.class);
        if (CollUtil.isEmpty(listData)) {
            log.warn("设备检测日志，batchInsertLisCheck 方法，未获取到lis设备结果明细. equipment: {}", JSONUtil.toJsonStr(equipment));
            return;
        }
        log.info("设备检测日志，batchInsertLisCheck 方法，listData={}", JSON.toJSONString(listData));
        for (ItemInfoVO item : listData) {
            ProjEquipCheckVsLis equipCheck = new ProjEquipCheckVsLis();
            BeanUtil.copyProperties(item, equipCheck);
            equipCheck.setEquipCheckVsLisId(SnowFlakeUtil.getId());
            if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                equipCheck.setHospitalName(hospitalInfo.getHospitalName());
            }
            equipCheck.setEquipRecordVsLisId(equipment.getId());
            equipCheck.setCloudHospitalId(equipment.getHospitalId());
            count = projEquipCheckVsLisMapper.insert(equipCheck);
            log.info("设备检测日志，batchInsertLisCheck 方法，批量插入lis结果详情, count: {}, detail: {}", count, JSONUtil.toJsonStr(equipCheck));
        }
    }

    /**
     * 查看检测进度
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ProjEquipCheckVsLisVO>> viewCheckProgress(ProjEquipSendToCloudDTO dto) {
        // 查询设备对应的云健康id
        ProjEquipRecordVsLis projEquipRecordVsLis =
                projEquipRecordVsLisMapper.selectById(dto.getEquipRecordVsProductId());
        ProjEquipRecord record = equipRecordMapper.selectById(projEquipRecordVsLis.getEquipRecordId());
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(record.getHospitalInfoId());
        dto.setEquipRecordVsProductId(record.getCloudEquipId());
        dto.setCloudHospitalId(hospitalInfo.getCloudHospitalId());
        List<ProjEquipCheckVsLisVO> checkList = projEquipCheckVsLisMapper.findCheckProgress(dto);
        Map<String, ProjEquipCheckVsLisVO> map = new HashMap<>();
        for (ProjEquipCheckVsLisVO v : checkList) {
            v.setItemName("(" + v.getItemNo() + ")" + v.getItemName());
            map.merge(v.getItemChannel(), v, (r1, r2) -> {
                r1.setItemName(r1.getItemName() + "," + r2.getItemName());
                return r1;
            });
        }
        List<ProjEquipCheckVsLisVO> result = new ArrayList<>(map.values());
        List<ProjEquipCheckVsLisVO> sortedResult = result.stream()
                .sorted(Comparator.comparingInt(ProjEquipCheckVsLisVO::getState))
                .collect(Collectors.toList());
        return Result.success(sortedResult);
    }

    /**
     * 下载模版
     *
     * @param response
     */
    @Override
    public void downloadTemplate(HttpServletResponse response, Long projectInfoId) {
        //查询客户名称
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("LIS设备导入模版");
        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        // 创建带星号(*)的标题行字体颜色为红色
        CellStyle redFontStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setBold(true);
        redFontStyle.setFont(redFont);
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        String[] headers = {"*设备型号", "*设备类型", "*设备厂商", "*通讯方式", "*医院名称", "厂商电话", "设备位置", "*是否对接", "*是否双工", "*是否急诊",
                "*是否质控", "*是否复检", "不对接原因", "备注说明"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (headers[i].startsWith("*")) {
                cell.setCellStyle(redFontStyle);
            } else {
                cell.setCellStyle(headerStyle);
            }
        }
        // 创建一个下拉框的区域，从第2-500行，第8-11列
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 500, 7, 11);
        // 设置下拉框的数据有效性
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[]{"是", "否"});
        DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
        // 设置下拉框
        dataValidation.setSuppressDropDownArrow(true);
        dataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
        dataValidation.setShowErrorBox(true);
        sheet.addValidationData(dataValidation);
        // 创建是否的下拉框区域，从第2-499行，第4列【通讯方式】
        List<BaseCodeNameResp> attributes =
                equipRecordMapper.selectSurveyEquipAttributes(DictEquipTypeConsts.DICT_EQUIP_COMM_MODE);
        if (CollectionUtil.isNotEmpty(attributes)) {
            String[] choices2 = new String[attributes.size()];
            for (int i = 0; i < attributes.size(); i++) {
                choices2[i] = attributes.get(i).getName();
            }
            CellRangeAddressList addressList2 = new CellRangeAddressList(1, 500, 3, 3);
            DataValidation dataValidation2 = helper.createValidation(helper.createExplicitListConstraint(choices2),
                    addressList2);
            dataValidation2.setSuppressDropDownArrow(true);
            dataValidation2.createErrorBox("错误", "请从列表中选择一个选项。");
            dataValidation2.setShowErrorBox(true);
            sheet.addValidationData(dataValidation2);
        }
        // 创建是否的下拉框区域，从第2-499行，第6列【来源医院】
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtil.isNotEmpty(hospitalInfoList)) {
            String[] hospitalInfoListChoices = new String[hospitalInfoList.size()];
            for (int i = 0; i < hospitalInfoList.size(); i++) {
                hospitalInfoListChoices[i] = hospitalInfoList.get(i).getHospitalName();
            }
            CellRangeAddressList hospitalInfoListAddress = new CellRangeAddressList(1, 500, 4, 4);
            DataValidation hospitalInfoDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(hospitalInfoListChoices),
                            hospitalInfoListAddress);
            hospitalInfoDataValidation.setSuppressDropDownArrow(true);
            hospitalInfoDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            hospitalInfoDataValidation.setShowErrorBox(true);
            sheet.addValidationData(hospitalInfoDataValidation);
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(customInfo.getCustomName() + "-LIS设备导入模版.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Override
    public Result importExcelDatas(MultipartFile multipartFile, Long customInfoId, Long projectInfoId) {
        // 确保上传的是Excel文件
        if (!multipartFile.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("导入失败,请选择.xlsx格式的Excel文件");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(multipartFile.getInputStream(), EquipRecordVsLisExcelDTO.class);
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("导入失败,请检查导入文件是否存在数据");
                }
            }
            Result result = saveDatas(easyExcelData.getDatas(), customInfoId, projectInfoId);
            //导入Excel数据
            return result;
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败，失败信息：" + e.getMessage());
        }
    }

    /**
     * 保存模版导入数据
     *
     * @param dtoList
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result saveDatas(List<Object> dtoList, Long customInfoId, Long projectInfoId) {
        for (Object dto : dtoList) {
            EquipRecordVsLisExcelDTO excelDTO = (EquipRecordVsLisExcelDTO) dto;
            //检验数据中必填选项
            Set<ConstraintViolation<EquipRecordVsLisExcelDTO>> violations = validator.validate(excelDTO);
            if (!violations.isEmpty()) {
                return Result.fail("导入失败，Excel中存在必填的选项为空，请检查标题前带*的内容是否填写完整！");
            }
            //封装设备记录实体
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            BeanUtil.copyProperties(excelDTO, projEquipRecord);
            //查询医院id
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_name", excelDTO.getHospitalName()).last("limit 1"));
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                return Result.fail("导入失败,《" + excelDTO.getHospitalName() + "》在系统中未匹配到对应医院，请核对数据！");
            }
            if (!customInfoId.equals(hospitalInfo.getCustomInfoId())) {
                return Result.fail("导入失败,项目下不存在《" + excelDTO.getHospitalName() + "》，请核对数据！");
            }
            projEquipRecord.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
            //通讯方式
            DictEquipAttributes dictEquipAttributes =
                    dictEquipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                            .eq("equip_attributes_code", DictEquipTypeConsts.DICT_EQUIP_COMM_MODE)
                            .eq("equip_attributes_value", excelDTO.getCommModeValue())
                            .last(" limit 1"));
            if (ObjectUtil.isEmpty(dictEquipAttributes)) {
                return Result.fail("导入失败,《" + excelDTO.getCommModeValue() + "》在系统中未匹配到对应通讯方式，请核对数据！");
            }
            projEquipRecord.setCommMode(excelDTO.getCommModeValue());
            projEquipRecord.setCommModeKey(dictEquipAttributes.getEquipAttributesKey());
            //处理设备类型
            projEquipRecord.setEquipTypeId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_TYPE, projEquipRecord.getEquipTypeName()));
            //设备厂商
            projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY, projEquipRecord.getEquipFactoryName()));
            //设备型号
            projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO, projEquipRecord.getEquipModelName()));
            //产品ID
            DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                    ProjProductEnum.LIS.getProductName()).last(" limit 1"));
            projEquipRecord.setYyProductId(product.getYyProductId());
            //是否对接
            projEquipRecord.setRequiredFlag("是".equals(excelDTO.getRequiredFlagExcel()) ? 1 : 0);
            //保存设备记录信息
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecord.setCustomInfoId(customInfoId);
            projEquipRecord.setProjectInfoId(projectInfoId);
            equipRecordMapper.insert(projEquipRecord);
            //保存Lis设备记录
            ProjEquipRecordVsLis projEquipRecordVsLis = new ProjEquipRecordVsLis();
            BeanUtil.copyProperties(excelDTO, projEquipRecordVsLis);
            projEquipRecordVsLis.setDuplexFlag("是".equals(excelDTO.getDuplexFlagExcel()) ? 1 : 0);
            projEquipRecordVsLis.setEmergencyFlag("是".equals(excelDTO.getEmergencyFlagExcel()) ? 1 : 0);
            projEquipRecordVsLis.setQualityControlFlag("是".equals(excelDTO.getRequiredFlagExcel()) ? 1 : 0);
            projEquipRecordVsLis.setIsRecheckFlag("是".equals(excelDTO.getIsRecheckFlagExcel()) ? 1 : 0);
            projEquipRecordVsLis.setEquipRecordVsLisId(SnowFlakeUtil.getId());
            projEquipRecordVsLis.setEquipRecordId(projEquipRecord.getEquipRecordId());
            projEquipRecordVsLisMapper.insert(projEquipRecordVsLis);
        }
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        return Result.success();
    }

    /**
     * 导出数据
     *
     * @param dto
     */
    @Override
    public void exportExcelDatas(LisEquipSelectDTO dto, HttpServletResponse response) {
        List<LisEquipVo> lisEquipVos = projEquipRecordVsLisMapper.selectLisEquipData(dto);
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("LIS设备记录");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        headerRow.createCell(0).setCellValue("设备型号");
        headerRow.createCell(1).setCellValue("设备类型");
        headerRow.createCell(2).setCellValue("设备厂商");
        headerRow.createCell(3).setCellValue("通讯方式");
        headerRow.createCell(4).setCellValue("医院名称");
        headerRow.createCell(5).setCellValue("厂商电话");
        headerRow.createCell(6).setCellValue("设备位置");
        headerRow.createCell(7).setCellValue("是否对接");
        headerRow.createCell(8).setCellValue("不对接原因");
        headerRow.createCell(9).setCellValue("是否双工");
        headerRow.createCell(10).setCellValue("是否急诊");
        headerRow.createCell(11).setCellValue("是否质控");
        headerRow.createCell(12).setCellValue("是否复检");
        headerRow.createCell(13).setCellValue("备注说明");
        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        for (Cell headerCell : headerRow) {
            headerCell.setCellStyle(headerStyle);
        }
        //填充数据
        for (int i = 0; i < lisEquipVos.size(); i++) {
            LisEquipVo lisEquipVo = lisEquipVos.get(i);
            Row rowData = sheet.createRow(i + 1);
            rowData.createCell(0).setCellValue(lisEquipVo.getEquipModelName());
            rowData.createCell(1).setCellValue(lisEquipVo.getEquipTypeName());
            rowData.createCell(2).setCellValue(lisEquipVo.getEquipFactoryName());
            rowData.createCell(3).setCellValue(lisEquipVo.getCommMode());
            rowData.createCell(4).setCellValue(lisEquipVo.getHospitalInfoName());
            rowData.createCell(5).setCellValue(lisEquipVo.getEquipFactoryPhone());
            rowData.createCell(6).setCellValue(lisEquipVo.getEquipPosition());
            rowData.createCell(7).setCellValue(lisEquipVo.getRequiredFlag() == 1 ? "是" : "否");
            rowData.createCell(8).setCellValue(lisEquipVo.getStopReason());
            rowData.createCell(9).setCellValue(lisEquipVo.getDuplexFlag() == 1 ? "是" : "否");
            rowData.createCell(10).setCellValue(lisEquipVo.getEmergencyFlag() == 1 ? "是" : "否");
            rowData.createCell(11).setCellValue(lisEquipVo.getQualityControlFlag() == 1 ? "是" : "否");
            rowData.createCell(12).setCellValue(lisEquipVo.getQualityControlFlag() == 1 ? "是" : "否");
            rowData.createCell(13).setCellValue(lisEquipVo.getIsRecheckFlag() == 1 ? "是" : "否");
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("LIS设备记录.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 老换新设备字典获取执行
     *
     * @param dto
     * @return
     */
    @Override
    public Result executeImport(OldEquipImportDTO dto) {
        String productCode = dto.getProductCode();
        //LIS设备导入
        if (productCode.equals(ProjProductEnum.LIS.getProductCode())) {
            if (ObjectUtil.isNotEmpty(dto.getJsonData())) {
                return executeImportVsLis(dto);
            } else {
                return Result.fail("Lis老换新设备无数据，请检查");
            }
        }
        if (productCode.equals(ProjProductEnum.PACS.getProductCode())) {
            if (ObjectUtil.isNotEmpty(dto.getJsonData())) {
                return equipRecordVsPacsService.executeImport(dto);
            } else {
                return Result.fail("Pacs老换新设备无数据，请检查");
            }
        }
        if (productCode.equals(ProjProductEnum.AIMS.getProductCode())) {
            if (ObjectUtil.isNotEmpty(dto.getJsonData())) {
                return equipRecordVsAimsService.executeImportForAims(dto);
            } else {
                return Result.fail("手麻老换新设备无数据，请检查");
            }
        }
        return Result.success();
    }

    /**
     * 保存老系统LIS设备记录
     *
     * @param dto
     * @return
     */
    private Result executeImportVsLis(OldEquipImportDTO dto) {
        if (StringUtils.isBlank(dto.getJsonData())) {
            return Result.fail("未查询到需要导入的设备数据");
        }
        JSONArray jsonArray = JSON.parseArray(dto.getJsonData());
        for (Object jsonObj : jsonArray) {
            com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) jsonObj;
            StringBuilder memoBuilder = new StringBuilder("老系统导入设备");
            //封装设备记录实体
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipModelName(jsonObject.getString("仪器型号"));
            projEquipRecord.setEquipFactoryName(jsonObject.getString("厂家"));
            projEquipRecord.setCommMode(jsonObject.getString("通讯方式"));
            //通讯方式
            DictEquipAttributes dictEquipAttributes =
                    dictEquipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                            .eq("equip_attributes_code", DictEquipTypeConsts.DICT_EQUIP_COMM_MODE)
                            .eq("equip_attributes_value", jsonObject.getString("通讯方式"))
                            .last(" limit 1"));
            if (ObjectUtil.isNotEmpty(dictEquipAttributes)) {
                projEquipRecord.setCommModeKey(dictEquipAttributes.getEquipAttributesKey());
            }
            //医院
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_name", jsonObject.getString("医院名称")).last("limit 1"));
            if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                projEquipRecord.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
            } else {
                memoBuilder.append(",医院名称：").append(jsonObject.getString("医院名称"));
            }
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
            projEquipRecord.setCustomInfoId(dto.getCustomInfoId());
            projEquipRecord.setProjectInfoId(dto.getProjectInfoId());
            projEquipRecord.setMemo(memoBuilder.toString());
            projEquipRecord.setEquipStatus(EquipStatusEnum.BE_SURE.getCode());
            projEquipRecord.setCloudEquipId(Convert.toLong(jsonObject.getString("云健康仪器id")));
            projEquipRecord.setCloudEquipName(jsonObject.getString("仪器名称"));
            equipRecordMapper.insert(projEquipRecord);
            //封装LIS设备记录实体
            ProjEquipRecordVsLis projEquipRecordVsLis = new ProjEquipRecordVsLis();
            projEquipRecordVsLis.setDuplexFlag("双工".equals(jsonObject.getString("是否双工")) ? 1 : 0);
            projEquipRecordVsLis.setEquipRecordVsLisId(SnowFlakeUtil.getId());
            projEquipRecordVsLis.setEquipRecordId(projEquipRecord.getEquipRecordId());
            projEquipRecordVsLis.setOldLisImportFlag(1);
            projEquipRecordVsLisMapper.insert(projEquipRecordVsLis);
        }
        return Result.success();
    }

    /**
     * 老系统Lis设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    @Override
    public Result sendEquipToOldLis(Long projectInfoId, Long oldEquipId) {
        // 查询老系统Lis设备数据
        List<SendCsmEquipToOldLisDTO> sendCsmEquipToOldLisDTOS =
                projEquipRecordVsLisMapper.selectOldEquipDataToLis(projectInfoId, oldEquipId);
        if (CollectionUtil.isNotEmpty(sendCsmEquipToOldLisDTOS)) {
            for (SendCsmEquipToOldLisDTO dto : sendCsmEquipToOldLisDTOS) {
                TmpOldEquipVsNew tmpOldEquipVsNew = new TmpOldEquipVsNew();
                tmpOldEquipVsNew.setId(SnowFlakeUtil.getId());
                tmpOldEquipVsNew.setOldEquipId(dto.getId());
                tmpOldEquipVsNew.setNewEquipId(dto.getId());
                tmpOldEquipVsNew.setProductName("Lis");
                // 获取当前数据的创建人信息 csm系统人员
                ImspSysUser imspSysUser = imspSysUserMapper.selectById(dto.getCreateId());
                Long userId = null;
                if (ObjectUtil.isNotEmpty(imspSysUser)) {
                    SysUser user = sysUserMapper.selectUserIdByYungyingId(imspSysUser.getUserYunyingId().toString());
                    userId = ObjectUtil.isNotEmpty(user) && ObjectUtil.isNotEmpty(user.getSysUserId())
                            ? user.getSysUserId() : null;
                }
                try {
                    // 封装csm的Lis设备数据
                    ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                    projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
                    projEquipRecord.setCustomInfoId(dto.getCustomInfoId());
                    if (ObjectUtil.isEmpty(dto.getNewProjectInfoId())) {
                        // 查询对应客户下 存在lis产品的项目id 【默认放在存在lis产品下的项目中】
                        List<ReportCustomInfo> projectInfoList =
                                reportCustomInfoMapper.selectList(new QueryWrapper<ReportCustomInfo>()
                                        .eq("custom_info_id", dto.getCustomInfoId())
                                );
                        projEquipRecord.setProjectInfoId(projectInfoList.get(0).getProjectInfoId());
                        dto.setNewProjectInfoId(projectInfoList.get(0).getProjectInfoId());
                    } else {
                        projEquipRecord.setProjectInfoId(dto.getNewProjectInfoId());
                    }
                    tmpOldEquipVsNew.setNewProjectInfoId(projEquipRecord.getProjectInfoId());
                    // 转换新系统医院id
                    OldCustomerInfo oldCustomerInfo = oldCustomerInfoMapper.selectById(dto.getHospitalInfoId());
                    projEquipRecord.setHospitalInfoId(ObjectUtil.isNotEmpty(oldCustomerInfo)
                            && ObjectUtil.isNotEmpty(oldCustomerInfo.getCsmHospitalInfoId())
                            ? oldCustomerInfo.getCsmHospitalInfoId() : -1);
                    projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
                    projEquipRecord.setEquipFactoryId(ObjectUtil.isNotEmpty(dto.getDictEquipFactoryId())
                            ? dto.getDictEquipFactoryId() : null);
                    projEquipRecord.setEquipFactoryName(ObjectUtil.isNotEmpty(dto.getDictEquipFactoryName())
                            ? dto.getDictEquipFactoryName() : dto.getEquipFactoryName());
                    projEquipRecord.setEquipTypeId(ObjectUtil.isNotEmpty(dto.getDictEquipTypeId())
                            ? dto.getDictEquipTypeId() : null);
                    projEquipRecord.setEquipTypeName(ObjectUtil.isNotEmpty(dto.getDictEquipTypeName())
                            ? dto.getDictEquipTypeName() : dto.getEquipTypeName());
                    projEquipRecord.setEquipInfoId(ObjectUtil.isNotEmpty(dto.getDictEquipModelId())
                            ? dto.getDictEquipModelId() : null);
                    projEquipRecord.setEquipModelName(ObjectUtil.isNotEmpty(dto.getDictEquipModelName())
                            ? dto.getDictEquipModelName() : dto.getEquipModelName());
                    projEquipRecord.setRequiredFlag(dto.getRequiredFlag() == 1 ? 1 : 0);
                    projEquipRecord.setStopReason(dto.getStopReason());
                    // 设备状态 【新老系统设备状态统一， 不需要额外处理】
                    projEquipRecord.setEquipStatus(dto.getEquipStatus());
                    projEquipRecord.setEquipPosition(dto.getEquipPosition());
                    projEquipRecord.setEquipFactoryPhone(dto.getEquipFactoryPhone());
                    projEquipRecord.setMemo(dto.getMemo());
                    projEquipRecord.setApplyTime(dto.getApplyTime());
                    projEquipRecord.setCloudEquipId(dto.getCloudEquipId());
                    projEquipRecord.setCloudEquipName(dto.getCloudEquipName());
                    // 查询通讯方式的key
                    DictEquipAttributes dictEquipAttributes =
                            dictEquipAttributesMapper.selectOne(new QueryWrapper<DictEquipAttributes>()
                                    .eq("equip_attributes_code", DictEquipTypeConsts.DICT_EQUIP_COMM_MODE)
                                    .eq("equip_attributes_key", dto.getCommMode())
                                    .last(" limit 1"));
                    projEquipRecord.setCommModeKey(ObjectUtil.isNotEmpty(dictEquipAttributes)
                            ? dictEquipAttributes.getEquipAttributesKey() : "-1");
                    projEquipRecord.setCommMode(ObjectUtil.isNotEmpty(dictEquipAttributes)
                            ? dictEquipAttributes.getEquipAttributesValue() : "-1");
                    projEquipRecord.setCheckResult(dto.getCheckResult());
                    projEquipRecord.setCreaterId(userId);
                    projEquipRecord.setUpdaterId(userId);
                    // Lis设备纪录信息保存
                    ProjEquipRecordVsLis projEquipRecordVsLis = new ProjEquipRecordVsLis();
                    projEquipRecordVsLis.setEquipRecordVsLisId(dto.getId());
                    projEquipRecordVsLis.setEquipRecordId(projEquipRecord.getEquipRecordId());
                    projEquipRecordVsLis.setDuplexFlag(ObjectUtil.isNotEmpty(dto.getIsDouble())
                            && Convert.toInt(dto.getIsDouble()) == 1 ? 1 : 0);
                    projEquipRecordVsLis.setEmergencyFlag(dto.getIsEmergency());
                    projEquipRecordVsLis.setQualityControlFlag(dto.getIsQuality());
                    projEquipRecordVsLis.setUpdaterId(userId);
                    // 判断是否已经存在当前数据
                    ProjEquipRecordVsLis projEquipRecordVsLis1 =
                            projEquipRecordVsLisMapper.selectById(projEquipRecordVsLis.getEquipRecordVsLisId());
                    if (ObjectUtil.isEmpty(projEquipRecordVsLis1)) {
                        equipRecordMapper.insert(projEquipRecord);
                        projEquipRecordVsLisMapper.insert(projEquipRecordVsLis);
                        // tmp记录表 记录数据
                        tmpOldEquipVsNew.setFlag(1);
                        tmpOldEquipVsNewMapper.insert(tmpOldEquipVsNew);
                    }
                    ProjSurveyPlan projSurveyPlan = new ProjSurveyPlan();
                    projSurveyPlan.setCustomInfoId(dto.getCustomInfoId());
                    projSurveyPlan.setProjectInfoId(dto.getNewProjectInfoId());
                    // 赋值当前项目的主院id
                    SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
                    selectHospitalDTO.setCustomInfoId(dto.getCustomInfoId());
                    selectHospitalDTO.setProjectInfoId(dto.getNewProjectInfoId());
                    selectHospitalDTO.setHealthBureauFlag(1);
                    List<ProjHospitalInfo> hospitalInfoList =
                            hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
                    if (CollectionUtil.isNotEmpty(hospitalInfoList) && hospitalInfoList.size() > 0) {
                        projSurveyPlan.setHospitalInfoId(hospitalInfoList.get(0).getHospitalInfoId());
                        projSurveyPlan.setYyProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
                        // 生成调研阶段里程碑任务
                        addEquipSurveyTask(userId, projSurveyPlan, MilestoneNodeEnum.SURVEY_DEVICE.getCode());
                        // 生成准备阶段里程碑任务
                        addEquipSurveyTask(userId, projSurveyPlan, MilestoneNodeEnum.PREPARAT_DEVICE.getCode());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("设备迁移失败 , 迁移失败数据 ， {}", JSONUtil.toJsonStr(dto));
                    tmpOldEquipVsNew.setFlag(0);
                    tmpOldEquipVsNew.setId(SnowFlakeUtil.getId());
                    tmpOldEquipVsNew.setMessage(e.toString());
                    tmpOldEquipVsNewMapper.insert(tmpOldEquipVsNew);
                }
            }
        }
        return Result.success();
    }

    /**
     * 生成设备里程碑节点任务
     *
     * @param userId
     * @param projSurveyPlan
     */
    @Override
    public void addEquipSurveyTask(Long userId, ProjSurveyPlan projSurveyPlan, String code) {
        // 检查是否已经存在任务，当不存在时候 进行创建
        ProjMilestoneTask milestoneTask = projMilestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                .eq("milestone_node_code", code)
                .eq("customer_info_id", projSurveyPlan.getCustomInfoId())
                .eq("project_info_id", projSurveyPlan.getProjectInfoId())
                .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
        );
        if (ObjectUtil.isEmpty(milestoneTask)) {
            // 当责任人不为空时候 说明指定了负责人。同步设置 设备调研负责人信息， milestoneTaks表创建 设备调研任务
            HospitalInfoDTO hospitalInfoDTO = new HospitalInfoDTO();
            hospitalInfoDTO.setCustomInfoId(projSurveyPlan.getCustomInfoId());
            hospitalInfoDTO.setProjectInfoId(projSurveyPlan.getProjectInfoId());
            hospitalInfoDTO.setHospitalInfoId(projSurveyPlan.getHospitalInfoId());
            // 查询设备调研里程碑节点信息
            ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(projSurveyPlan.getProjectInfoId(),
                    code);
            if (ObjectUtil.isNotEmpty(milestoneInfo)) {
                MilestoneInfoDTO milestoneInfoDTO = new MilestoneInfoDTO();
                milestoneInfoDTO.setMilestoneNodeCode(code);
                milestoneInfoDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
                milestoneInfoDTO.setProjectStageId(milestoneInfo.getProjectStageId());
                milestoneInfoDTO.setProjectStageCode(milestoneInfo.getProjectStageCode());
                // 组装任务数据
                ResearchPlanDTO researchPlanDTO = getResearchPlanDTO(hospitalInfoDTO, milestoneInfoDTO, null,
                        new Date(), userId);
                log.info("生成设备调研的里程碑节点任务数据 , 数据参数信息 , {}", JSONUtil.toJsonStr(researchPlanDTO));
                projMilestoneTaskMapper.insertResearchPlans(Arrays.asList(researchPlanDTO));
            }
        }
        ProjMilestoneTask milestoneTask2 =
                projMilestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                        .eq("milestone_node_code", code)
                        .eq("customer_info_id", projSurveyPlan.getCustomInfoId())
                        .eq("project_info_id", projSurveyPlan.getProjectInfoId())
                        .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                );
        ProjMilestoneTaskDetail projMilestoneTaskDetail1 =
                projMilestoneTaskDetailMapper.selectOne(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("milestone_task_id", milestoneTask2.getMilestoneTaskId())
                        .eq("product_deliver_id", projSurveyPlan.getYyProductId())
                );
        if (ObjectUtil.isEmpty(projMilestoneTaskDetail1) && ObjectUtil.isNotEmpty(milestoneTask2)) {
            // 生成 设备调研里程碑节点明细任务数据
            ProjMilestoneTaskDetail projMilestoneTaskDetail = infoDataForTaskDetail(milestoneTask2,
                    projSurveyPlan, userId);
            log.info("生成设备调研的里程碑节点明细任务 , 数据参数信息 , {}", JSONUtil.toJsonStr(projMilestoneTaskDetail));
            projMilestoneTaskDetailMapper.insert(projMilestoneTaskDetail);
        }
    }

    /**
     * 老系统Lis设备图片迁移到新系统
     *
     * @param oldLisEquipImgDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result sendEquipImgToOldLis(OldLisEquipImgDTO oldLisEquipImgDTO) {
        return equipRecordVsLisServiceImplExtend.sendEquipImgToOldLis(oldLisEquipImgDTO);
    }

    /**
     * Lis老换新导入的设备确认资源库设备信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateResourceEquipData(OldLisImportDTO dto) {
        ProjEquipRecordVsLis projEquipRecordVsLis = projEquipRecordVsLisMapper.selectById(dto.getEquipRecordVsLisId());
        ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(projEquipRecordVsLis.getEquipRecordId());
        // 校验是否有对应的设备
        if (!dto.getResourceNotExistFlag()) {
            // 处理设备数据,进行资源库数据处理
            ProjEquipRecordVsLis projEquipRecordVsLis1 = new ProjEquipRecordVsLis();
            BeanUtil.copyProperties(dto, projEquipRecordVsLis1);
            // 根据传入的id 获取资源库设备名称
            DictEquipFactory dictEquipFactory = dictEquipFactoryMapper.selectById(dto.getResourceEquipFactoryId());
            DictEquipType dictEquipType = dictEquipTypeMapper.selectById(dto.getResourceEquipTypeId());
            DictEquipInfo dictEquipInfo = dictEquipInfoMapper.selectById(dto.getResourceEquipInfoId());
            projEquipRecordVsLis1.setResourceEquipFactoryName(dictEquipFactory.getEquipFactoryName());
            projEquipRecordVsLis1.setResourceEquipTypeName(dictEquipType.getEquipTypeName());
            projEquipRecordVsLis1.setResourceEquipModelName(dictEquipInfo.getEquipModelName());
            log.info("Lis老换新导入的设备确认资源库设备信息=== 复制后的设备数据 , {}", JSONUtil.toJsonStr(projEquipRecordVsLis1));
            projEquipRecordVsLisMapper.updateById(projEquipRecordVsLis1);
        }
        // 更新设备状态。
        ProjEquipRecord projEquipRecord1 = new ProjEquipRecord();
        projEquipRecord1.setEquipRecordId(projEquipRecord.getEquipRecordId());
        projEquipRecord1.setEquipStatus(0);
        equipRecordMapper.updateById(projEquipRecord1);
        return Result.success();
    }

    @Override
    public Result<List<BaseCodeNameVO>> selectSurveyAttributesInfo(String equipAttributesCode) {
        List<BaseCodeNameResp> resps = equipRecordMapper.selectSurveyEquipAttributes(equipAttributesCode);
        if (CollUtil.isEmpty(resps)) {
            return Result.success(CollUtil.newArrayList());
        }
        List<BaseCodeNameVO> vos = Convert.toList(BaseCodeNameVO.class, resps);
        // 查询要展示的字段
        for (BaseCodeNameVO vo : vos) {
            List<ConfigEquipSurvey> equipSurveys =
                    configEquipSurveyMapper.selectList(new QueryWrapper<ConfigEquipSurvey>().eq(
                            "yy_product_id",
                            ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId()).like("sence_code", vo.getId()));
            if (CollUtil.isEmpty(equipSurveys)) {
                continue;
            }
            vo.setSurveyConfigFields(Convert.toList(String.class,
                    equipSurveys.stream().map(ConfigEquipSurvey::getFieldCode).collect(Collectors.toList())));
        }
        return Result.success(vos);
    }

    @Override
    public Result<EquipLisDetailItemVO> findEquipDetail(EquipDetailItemDTO equipDetailItemDTO) {
        // 验证不能都为空
        if (ObjectUtil.isAllEmpty(equipDetailItemDTO.getEquipTypeName(), equipDetailItemDTO.getEquipTypeId())) {
            return Result.fail("获取明细项参数不能为空");
        }
        AnalyseEquipDetailItemDTO dto = AnalyseEquipDetailItemDTO.builder()
                .equipName(equipDetailItemDTO.getEquipTypeName())
                .equipId(equipDetailItemDTO.getEquipTypeId())
                .build();
        EquipLisDetailItemVO itemVO = EquipLisDetailItemVO.builder().build();
        JSONObject entries = lisAnalyManagerClient.getEquipItemByEquipNameOrId(dto);
        log.info("Lis开放平台, 获取设备明细项: {}", entries);
        if (ObjectUtil.isEmpty(entries)) {
            log.error("未查询到明细项. {}", JSONUtil.toJsonStr(dto));
            return Result.success();
        }
        if (ObjectUtil.isEmpty(entries.get("success"))) {
            log.error("Lis开放平台, 获取设备明细项失败. {}", JSONUtil.toJsonStr(dto));
            return Result.success();
        }
        if (!(Boolean) entries.get("success")) {
            log.error("Lis开放平台, 获取设备明细项失败 , " + entries.get("message"));
            return Result.success();
        }
        if (ObjectUtil.isEmpty(entries.get("data"))) {
            log.error("未查询到明细项. dto: {}, data: {}", JSONUtil.toJsonStr(dto), entries);
            return Result.success();
        }
        List<EquipLisDetailItemResp> list = JSONArray.parseArray(entries.get("data").toString(),
                EquipLisDetailItemResp.class);
        if (CollUtil.isNotEmpty(list)) {
            // 最多只有两个明细项, 或者1个, 或者没有
            itemVO.setItemName01(list.get(0).getItemCname());
            itemVO.setItemChannel01(list.get(0).getItemChannel());
            if (list.size() > 1) {
                itemVO.setItemName01(list.get(1).getItemCname());
                itemVO.setItemChannel01(list.get(1).getItemChannel());
            }
        }
        return Result.success(itemVO);
    }

    @Override
    public Result<String> saveDeviceList(ProjEquipRecordVsLisListSaveDTO dto) {
        if (dto.getRequiredFlag() == 1) {
            dto.setStopReason(StrUtil.EMPTY);
        }
        // 非必须对接
        if (dto.getRequiredFlag() == 0) {
            sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(dto.getEquipRecordId(), ReviewTypeEnum.LIS_EQUIP_RESUBMIT_FOR_REVIEW.getBusinessTable());
        }
        ProjEquipRecord update = new ProjEquipRecord();
        update.setEquipRecordId(dto.getEquipRecordId());
        update.setStopReason(dto.getStopReason());
        update.setRequiredFlag(dto.getRequiredFlag());
        int count = equipRecordMapper.updateById(update);
        log.info("lis列表更新是否对接标识. count: {}, detail: {}, dto: {}", count, JSONUtil.toJsonStr(update),
                JSONUtil.toJsonStr(dto));
        ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(dto.getEquipRecordId());
        projTodoTaskService.todoTaskTotalCountSync(projEquipRecord.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(projEquipRecord.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        return Result.success();
    }

    @Override
    public Result<Map<String, List<CloudEquipVO>>> selectCloudEquipDataTransfer(LisCloudEquipSelectDTO selectDTO) {
        Map<Long, List<CloudEquipVO>> listMap = selectCloudEquipData(selectDTO);
        Map<String, List<CloudEquipVO>> stringListMap = MapUtil.newHashMap();
        // 转换hospitalId为str
        if (MapUtil.isNotEmpty(listMap)) {
            for (Long hosId : listMap.keySet()) {
                stringListMap.put(StrUtil.toString(hosId), listMap.get(hosId));
            }
        }
        if (MapUtil.isEmpty(stringListMap)) {
            return Result.fail("未获取到云健康设备信息.");
        }
        return Result.success(stringListMap);
    }


    /**
     * 保存老换新设备字典获取操作已读
     *
     * @param dto
     * @return
     */
    @Override
    public Result saveManualRead(ProjTipRecordDto dto) {
        ProjTipRecord projTipRecord = ProjTipRecord.builder()
                .projTipRecordId(SnowFlakeUtil.getId())
                .projectInfoId(dto.getProjectInfoId())
                .configTipId(-1L)
                .readFlag(1)
                .busiCode(dto.getBusiCode())
                .build();
        projTipRecordMapper.insert(projTipRecord);
        return Result.success();
    }

    /**
     * 查询老换新设备字典获取操作是否已读
     *
     * @param dto
     * @return
     */
    @Override
    public Result<Boolean> getManualRead(ProjTipRecordDto dto) {
        ProjTipRecord projTipRecord = projTipRecordMapper.selectOne(new QueryWrapper<ProjTipRecord>()
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("config_tip_id", -1)
                .eq("busi_code", dto.getBusiCode())
                .eq("creater_id", userHelper.getCurrentUser().getSysUserId())
        );
        if (ObjectUtil.isNotEmpty(projTipRecord) && projTipRecord.getReadFlag() == 1) {
            return Result.success(true);
        } else {
            return Result.success(false);
        }
    }

    private ResearchPlanDTO getResearchPlanDTO(HospitalInfoDTO v, MilestoneInfoDTO infoDTO, String secondLeaders,
                                               Date createTime, Long userId) {
        ResearchPlanDTO planDto = new ResearchPlanDTO();
        planDto.setProjResearchPlanId(SnowFlakeUtil.getId());
        planDto.setHospitalId(v.getHospitalInfoId() == null ? 0 : v.getHospitalInfoId());
        planDto.setProjectInfoId(v.getProjectInfoId());
        planDto.setCustomerInfoId(v.getCustomInfoId());
        planDto.setResearchCode(infoDTO.getMilestoneNodeCode() == null ? "" : infoDTO.getMilestoneNodeCode());
        planDto.setCreaterId(ObjectUtil.isNotEmpty(userId) ? userId : -1);
        planDto.setCreateTime(createTime);
        planDto.setUpdaterId(ObjectUtil.isNotEmpty(userId) ? userId : -1);
        planDto.setUpdateTime(createTime);
        planDto.setPlanStartTime(ObjectUtil.isNotEmpty(infoDTO.getExpectStartTime())
                ? DateUtil.beginOfDay(infoDTO.getExpectStartTime()) : null);
        planDto.setPlanEndTime(ObjectUtil.isNotEmpty(infoDTO.getExpectCompTime())
                ? DateUtil.endOfDay(infoDTO.getExpectCompTime()) : null);
        planDto.setLeaderId(v.getLeaderId() == null ? 0 : v.getLeaderId());
        planDto.setSecondLeaderId(secondLeaders == null ? "" : secondLeaders);
        planDto.setResultSourceId(40004L);
        planDto.setIsDeleted(0);
        planDto.setMilestoneInfoId(infoDTO.getMilestoneInfoId());
        planDto.setProjectStageId(infoDTO.getProjectStageId());
        planDto.setProjectStageCode(infoDTO.getProjectStageCode());
        return planDto;
    }

    private ProjMilestoneTaskDetail infoDataForTaskDetail(ProjMilestoneTask milestoneTask,
                                                          ProjSurveyPlan projSurveyPlan, Long surveyUserId) {
        ProjMilestoneTaskDetail detail = new ProjMilestoneTaskDetail();
        detail.setMilestoneTaskDetailId(SnowFlakeUtil.getId());
        detail.setLeaderId(surveyUserId);
        detail.setProductDeliverId(projSurveyPlan.getYyProductId());
        detail.setMilestoneTaskId(milestoneTask.getMilestoneTaskId());
        detail.setProductDeliverRecordId(projSurveyPlan.getYyProductId());
        detail.setCompleteStatus(0);
        return detail;
    }

    @Override
    public Result<List<LisEquipmentItemDTO>> queryLabItemByEquipment(AvailableLabQueryDTO dto) {
        List<ProjHospitalInfoRelative> hospitalInfos = hospitalInfoMapper.findHospitalByCloudHospitalId(dto.getHosId());
        if (CollUtil.isEmpty(hospitalInfos)) {
            throw new CustomException("没找到医院信息");
        }
        ProjHospitalInfoRelative hospitalInfo = CollUtil.getFirst(hospitalInfos);
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        dto.setHospitalId(hospitalInfo.getCloudHospitalId());
        dto.setHisOrgId(hospitalInfo.getOrgId());
        ResponseResult<List<LisEquipmentItemDTO>> res = systemSettingApi.queryLabItemByEquipment(dto);
        if (!res.isSuccess()) {
            throw new CustomException(StrUtil.format("获取Lis推荐检测项目失败：" + res.getMessage()));
        }
        return Result.success(res.getData());
    }
}
