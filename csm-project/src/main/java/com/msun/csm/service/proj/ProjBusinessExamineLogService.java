package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.enums.ExamineStatusEnum;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.model.resp.RejectReasonAndCount;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;

public interface ProjBusinessExamineLogService {

    /**
     * 保存业务操作日志
     *
     * @param businessType   业务类型
     *                       <p>survey-产品业务调研</p>
     *                       <p>printreport-打印报表</p>
     *                       <p>form-表单</p>
     * @param examineStatus  审核状态
     *                       <p>0-产品业务调研、表单、打印报表提交后端审核，待后端审核</p>
     *                       <p>1-产品业务调研、表单、打印报表后端审核通过</p>
     *                       <p>2-产品业务调研、表单、打印报表后端审核驳回</p>
     *                       <p>11-产品业务调研撤销确认最终结果</p>
     *                       <p>12-产品业务调研撤销提交后端审核</p>
     *                       <p>20-表单、打印报表制作完成提交前端人员验证</p>
     *                       <p>21-表单、打印报表前端人员验证通过</p>
     *                       <p>22-表单、打印报表前端人员验证驳回</p>
     *                       <p>31-表单设置不使用</p>
     *                       <p>41-保存打印报表</p>
     * @param examineOpinion 审核意见
     * @param businessId     业务ID
     */
    boolean saveOperationLog(String businessType, Integer examineStatus, String examineOpinion, Long businessId);


    /**
     * 根据业务主键查询操作日志
     *
     * @param businessId 业务主键
     * @return 操作日志
     */
    List<ProjBusinessExamineLogResp> selectLogById(Long businessId);

    /**
     * 根据业务id查询最后一笔是已完成的
     * @param businessIds
     * @return
     */
    List<ProjBusinessExamineLog> selectListByIds(List<Long> businessIds);

    /**
     * 获取制作阶段验证驳回原因及次数
     *
     * @param businessId 业务ID
     * @return 制作阶段验证驳回原因及次数
     */
    RejectReasonAndCount selectReasonAndCountById(Long businessId, ExamineStatusEnum examineStatusEnum);


    /**
     * 根据业务类型、业务ID、状态获取日志
     *
     * @param businessType  业务类型
     *                      <p>survey-产品业务调研</p>
     *                      <p>printreport-打印报表</p>
     *                      <p>form-表单</p>
     * @param examineStatus 审核状态
     *                      <p>0-产品业务调研、表单、打印报表提交后端审核，待后端审核</p>
     *                      <p>1-产品业务调研、表单、打印报表后端审核通过</p>
     *                      <p>2-产品业务调研、表单、打印报表后端审核驳回</p>
     *                      <p>11-产品业务调研撤销确认最终结果</p>
     *                      <p>12-产品业务调研撤销提交后端审核</p>
     *                      <p>20-表单、打印报表制作完成提交前端人员验证</p>
     *                      <p>21-表单、打印报表前端人员验证通过</p>
     *                      <p>22-表单、打印报表前端人员验证驳回</p>
     *                      <p>31-表单设置不使用</p>
     * @param businessId    业务主键
     * @return 操作日志
     */
    List<ProjBusinessExamineLog> getLogByBusinessAndStatus(String businessType, Integer examineStatus, Long businessId);


}
