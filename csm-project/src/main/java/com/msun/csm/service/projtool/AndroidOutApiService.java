package com.msun.csm.service.projtool;

import javax.servlet.http.HttpServletResponse;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projtool.AndroidOutToolReq;

/**
 * @ClassName: DataImportService
 * @Description: 去球
 * @Author: zd
 * @Date: 13:54 2024/5/28
 */
public interface AndroidOutApiService {


    /**
     * 下载linux
     * @param androidOutToolReq
     * @param response
     * @return
     */
    Result<String> downloadOfLinux(AndroidOutToolReq androidOutToolReq, HttpServletResponse response);

    /**
     * ping打包服务是否可联通
     * @param cloudHospitalId
     * @return
     */
    Result<Boolean> pingLinuxIp(Long cloudHospitalId);

    /**
     * 打包
     * @param androidOutToolReq
     * @param response
     * @return
     */
    Result<String> reviseHospitalConfiguration(AndroidOutToolReq androidOutToolReq, HttpServletResponse response);

    /**
     * 批量安装工具
     * @param androidOutToolReq
     * @param response
     * @return
     */
    Result<String> batchInstallationTool(AndroidOutToolReq androidOutToolReq, HttpServletResponse response);
}
