package com.msun.csm.service.config.projectreview;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReviewTypeUser;
import com.msun.csm.dao.entity.projectreview.DictReviewMethodType;
import com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewTypeUserMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserReq;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserSaveReq;
import com.msun.csm.model.req.projectreview.QueryInfoReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewTypeUserResp;
import com.msun.csm.model.resp.projectreview.UserModelAllResp;
import com.msun.csm.model.resp.projectreview.UserModelResp;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【config_project_review_type_user(项目审核类型对应人员配置表)】的数据库操作Service实现
 * @createDate 2025-06-18 08:30:31
 */
@Service
public class ConfigProjectReviewTypeUserServiceImpl extends ServiceImpl<ConfigProjectReviewTypeUserMapper, ConfigProjectReviewTypeUser> implements ConfigProjectReviewTypeUserService {

    @Resource
    private ConfigProjectReviewTypeUserMapper configProjectReviewTypeUserMapper;
    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 查询项目审核类型对应人员配置表分页数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ConfigProjectReviewTypeUserResp>> findDataPage(ConfigProjectReviewTypeUserReq dto) {
        // 查询 医院信息
        List<ConfigProjectReviewTypeUserResp> pagelist = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> configProjectReviewTypeUserMapper.findDataPage(dto));
        PageInfo<ConfigProjectReviewTypeUserResp> pageInfo = new PageInfo<>(pagelist);
        if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
            for (ConfigProjectReviewTypeUserResp item : pageInfo.getList()) {
                List<String> userIds = new ArrayList<>();
                List<Long> userIdLongs = new ArrayList<>();
                String[] reviewUserIdList = item.getReviewUserId().split(",");
                if (reviewUserIdList != null && reviewUserIdList.length > 0) {
                    for (String userId : reviewUserIdList) {
                        userIds.add(userId);
                        userIdLongs.add(Long.valueOf(userId));
                    }
                }
                item.setReviewUserListIds(userIds);
                List<SysUser> sysUsers = sysUserMapper.selectList(new QueryWrapper<SysUser>().in("sys_user_id", userIdLongs));
                if (sysUsers != null && !sysUsers.isEmpty()) {
                    item.setReviewUserNames(sysUsers.stream().map(SysUser::getUserName).collect(Collectors.joining(",")));
                }
            }
        }
        return Result.success(pageInfo);
    }

    /**
     * 项目审核人员配置表删除
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> delData(ConfigProjectReviewTypeUserReq dto) {
        if (dto.getProjectReviewUserId() == null) {
            return Result.fail("请选择要删除的记录");
        }
        // 判断是否正在使用
        Integer isFlag = configProjectReviewTypeUserMapper.selectIfNotUseCount(dto);
        if (isFlag != null && isFlag > 0) {
            return Result.fail("该字典表正在使用中，请勿删除");
        }
        int del = configProjectReviewTypeUserMapper.deleteById(dto.getProjectReviewUserId());
        return Result.success("删除成功");
    }

    /**
     * 项目审核人员配置表保存修改
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> saveData(ConfigProjectReviewTypeUserSaveReq dto) {
        /*
         * 新增 校验同一审核方式、类型下，是否已存在相同团队
         *
         * 若为编辑操作，校验时需排除当前记录ID，避免误判重复。
         */
        if (CollUtil.isEmpty(dto.getReviewUserListIds())) {
            return Result.fail("请选择审核人员");
        }
        if (dto.getReviewLeaderUserId() == null || dto.getReviewLeaderUserId() <= 0) {
            return Result.fail("请选择主审核人员");
        }

        Integer listModel = configProjectReviewTypeUserMapper.selectListByParamer(dto);
        if (listModel != null && listModel > 0) {
            return Result.fail("同一审核类型下，审核方式组合已存在,请勿重复操作！");
        }
        if (dto.getProjectReviewUserId() == null) {
            // 添加
            ConfigProjectReviewTypeUser configProjectReviewTypeUser = new ConfigProjectReviewTypeUser();
            BeanUtil.copyProperties(dto, configProjectReviewTypeUser);
            configProjectReviewTypeUser.setReviewUserId(dto.getReviewUserListIds().stream().map(Object::toString).collect(Collectors.joining(",")));

            configProjectReviewTypeUser.setProjectReviewUserId(SnowFlakeUtil.getId());
            int insert = configProjectReviewTypeUserMapper.insert(configProjectReviewTypeUser);
        } else {
            // 修改
            ConfigProjectReviewTypeUser configProjectReviewTypeUser = configProjectReviewTypeUserMapper.selectById(dto.getProjectReviewUserId());
            BeanUtil.copyProperties(dto, configProjectReviewTypeUser);
            configProjectReviewTypeUser.setReviewUserId(dto.getReviewUserListIds().stream().map(Object::toString).collect(Collectors.joining(",")));

            int update = configProjectReviewTypeUserMapper.updateById(configProjectReviewTypeUser);
        }
        return Result.success();
    }

    /**
     * 查询审核人集合
     *
     * @param dto
     * @return
     */
    @Override
    public UserModelAllResp findUserModel(QueryInfoReq dto) {
        UserModelAllResp userModelAllResp = new UserModelAllResp();
        userModelAllResp.setIsNeedReview(true);
        userModelAllResp.setIsConfigFlag(true);
        userModelAllResp.setUserModelRespList(new ArrayList<>());
        // 查看项目是否已配置审核
        List<DictReviewMethodType> listConfig = configProjectReviewTypeUserMapper.selectListByDto(dto);
        if (listConfig == null || listConfig.isEmpty()) {
            userModelAllResp.setIsConfigFlag(false);
        } else {
            String reviewMethodCode = listConfig.get(0).getDictCode();
            dto.setReviewMethodCode(reviewMethodCode);
            userModelAllResp.setIsNeedReview(!"bsh".equals(reviewMethodCode));
        }

        // 无需审核直接返回
        if (!userModelAllResp.getIsNeedReview()) {
            return userModelAllResp;
        }
        List<UserModelResp> list = configProjectReviewTypeUserMapper.findUserModel(dto);
        if (list != null && !list.isEmpty()) {
            userModelAllResp.setUserModelRespList(list);
        } else {
            userModelAllResp.setIsConfigFlag(false);
        }
        return userModelAllResp;
    }
}




