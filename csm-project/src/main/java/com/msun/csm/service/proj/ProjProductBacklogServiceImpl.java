package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.commons.error.BaseErrorCode;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.BatchInsertDTO;
import com.msun.core.component.implementation.api.imsp.dto.SurveyValueAndSurveyContent;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.api.imsp.vo.SystemSettingResultDto;
import com.msun.core.component.implementation.api.productconfig.ProductConfigApi;
import com.msun.core.component.implementation.api.productconfig.dto.ProductConfigImportDTO;
import com.msun.core.component.implementation.api.productconfig.dto.ProductConfigImportVO;
import com.msun.core.component.implementation.api.productconfig.dto.RequestImportDTO;
import com.msun.core.component.implementation.api.productconfig.vo.ProductConfigReturnVO;
import com.msun.core.component.implementation.api.test.ImplementationTestApi;
import com.msun.core.component.implementation.api.test.dto.TestDTO;
import com.msun.core.component.implementation.api.test.dto.TestDatabaseDTO;
import com.msun.core.component.implementation.api.test.dto.TestReturnDataDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysOperLog;
import com.msun.csm.dao.entity.config.ConfigProductJobMenuDetail;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.oldimsp.OldProduct;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProductConfig;
import com.msun.csm.dao.entity.proj.ProjProductConfigLog;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendLimit;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.entity.tduck.FmUserFormData;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.oldimsp.OldProductMapper;
import com.msun.csm.dao.mapper.operlog.OperLogMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormDataMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.model.dto.GetProductJobMenuDetailParam;
import com.msun.csm.model.dto.HospitalInfoDTO;
import com.msun.csm.model.dto.MilestoneInfoDTO;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickDetectDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickImportDTO;
import com.msun.csm.model.dto.ResearchPlanDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.param.CreateTduckFormParam;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.req.projreport.ConfigCustomBackendLimitReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.req.surveyplan.QuerySurveyPlanProductReq;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;
import com.msun.csm.model.resp.surveyplan.SurveyPlanProductResp;
import com.msun.csm.model.vo.ConfigImportCloudVO;
import com.msun.csm.model.vo.ConfigProductJobMenuDetailVO;
import com.msun.csm.model.vo.OneCheckResultVO;
import com.msun.csm.model.vo.OneCheckVo;
import com.msun.csm.model.vo.OneClickDetectionVO;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProjProductBacklogDataVO;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.model.vo.ProjProductConfigLogVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.dict.DictProductVsDeliverService;
import com.msun.csm.service.oldimsp.OldCustomerInfoService;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;
import com.msun.csm.service.report.ConfigCustomBackendLimitService;
import com.msun.csm.service.tduck.FmUserFormService;
import com.msun.csm.service.tduck.TduckService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/25/14:59
 */
@Service
@Slf4j
public class ProjProductBacklogServiceImpl implements ProjProductBacklogService {

    private static final String TODOWORK_ITEMNAME = "待办";
    private static final String TODOWORK_PRODUCT_ITEMNAME = "产品待办";
    @Resource
    UserHelper userHelper;
    @Resource
    private ProjProductBacklogMapper productBacklogMapper;
    @Resource
    private DictProductMapper productMapper;
    @Resource
    private DictProductVsModulesMapper productVsModulesMapper;

    @Resource
    private DictProductService dictProductService;
    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;
    @Resource
    private ConfigProductJobMenuDetailMapper configProductJobMenuDetailMapper;
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    @Resource
    private ImspSysUserMapper imspSysUserMapper;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private OldCustomerInfoService oldCustomerInfoService;
    @Resource
    private OldProductMapper oldProductMapper;
    @Resource
    private ProjProductConfigMapper productConfigMapper;
    @Resource
    private SystemSettingApi systemSettingApi;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Resource
    private ProjProductConfigLogMapper productConfigLogMapper;
    @Resource
    private ImplementationTestApi testApi;
    @Resource
    private TduckService tduckService;
    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;
    @Resource
    private ProjMilestoneInfoService milestoneInfoService;
    @Resource
    private ProjProjectInfoService projectInfoService;
    @Resource
    private ProjProductBacklogService productBacklogService;
    @Resource
    private ProjProjectConfigService projectConfigService;

    @Resource
    private ProjProjectPlanService projectPlanService;
    @Resource
    private FmUserFormDataMapper fmUserFormDataMapper;
    @Resource
    private ProjProductDeliverRecordMapper deliverRecordMapper;
    @Resource
    private ProjProductTaskMapper productTaskMapper;
    @Resource
    private ProductConfigApi productConfigApi;
    @Lazy
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private ProjSurveyReportService projSurveyReportService;
    @Resource
    private FmUserFormService fmUserFormService;
    @Resource
    private ProjHospitalInfoService hospitalInfoService;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Lazy
    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private ProjProductTaskService productTaskService;
    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;

    @Resource
    private ProjSurveyPlanMapper surveyPlanMapper;

    @Resource
    private OperLogMapper operLogMapper;

    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;

    @Lazy
    @Resource
    private ProjSurveyPlanService projSurveyPlanService;

    @Lazy
    @Resource
    private ConfigCustomBackendLimitService configCustomBackendLimitService;

    @Resource
    private ProjTodoTaskService todoTaskService;

    /**
     * 拼接所有医院准备项目完成数目
     *
     * @param finalBuilder  返回结果
     * @param allCountFin   完成此项的医院个数
     * @param hospitalCount 医院数量
     */
    public static void appendAllHospitalBuilder(StringBuilder finalBuilder, int allCountFin, int hospitalCount,
                                                String itemName) {
        finalBuilder.append("全部").append(itemName).append("完成").append(allCountFin).append("家, ")
                .append("存在未完成待办").append(hospitalCount - allCountFin).append("家.").append(StrUtil.SPACE);
    }

    /**
     * 更新所有医院项目的完成数量, 若所有产品此项记录已完成, 则加一
     *
     * @param productCount     产品数量
     * @param itemCountFinAtom 医院中完成此项的产品数量
     * @param allCountFinAtom  此项全部完成的医院数量
     */
    public static void updateAllCountFin(int productCount, AtomicInteger itemCountFinAtom,
                                         AtomicInteger allCountFinAtom) {
        if (productCount == itemCountFinAtom.get()) {
            allCountFinAtom.getAndIncrement();
        }
    }

    /**
     * 拼接医院产品准备记录
     *
     * @param hospitalBuilder 记录器
     * @param itemName        准备项名称
     * @param finCount        完成数量
     * @param productCount    产品数量
     */
    public static void appendHospitalBuilder(StringBuilder hospitalBuilder, String itemName, int finCount,
                                             int productCount) {
        hospitalBuilder.append("完成").append(finCount).append("个").append(itemName).append(StrUtil.COMMA)
                .append(StrUtil.SPACE).append("还有").append(productCount - finCount).append("个产品未完成.")
                .append(StrUtil.SPACE);
    }
//    /**
//     * 查询产品的任务下拉
//     *
//     * @param productBacklog
//     * @return
//     */
//    @Override
//    public Result<List<BaseIdNameResp>> selectProductJobMenuList(ProjProductBacklog productBacklog) {
//        // 处理产品的模块数据
//        if (ObjectUtil.isNotEmpty(productBacklog.getYyProductId())) {
//            // 当前端进行产品查询时，可能查询模块产品，需要进行判断选择的产品是否为模块产品。是的时候 重新赋值查询字段数据
//            DictProduct dictProduct = productMapper.selectOne(
//                    new QueryWrapper<DictProduct>().eq("yy_product_id", productBacklog.getYyProductId()));
//            if (ObjectUtil.isEmpty(dictProduct)) {
//                // 当产品数据为空时  表明是模块产品
//                DictProductVsModules modules = productVsModulesMapper.selectOne(
//                        new QueryWrapper<DictProductVsModules>().eq("yy_module_id", productBacklog.getYyProductId()));
//                if (ObjectUtil.isNotEmpty(modules)) {
//                    productBacklog.setYyProductId(modules.getYyProductId());
//                    productBacklog.setYyProductModuleId(modules.getYyModuleId());
//                }
//            }
//        }
//        List<BaseIdNameResp> list = new ArrayList<>();
//        // 根据项目、医院、产品id查询当前产品已有的下拉数据
//        List<ProjProductBacklog> projProductBacklogs = productBacklogMapper.selectList(new QueryWrapper<ProjProductBacklog>()
//                .eq(ObjectUtil.isNotEmpty(productBacklog.getYyProductId()), "yy_product_id", productBacklog.getYyProductId())
//                .eq(ObjectUtil.isNotEmpty(productBacklog.getYyProductModuleId()), "yy_product_module_id",
//                        productBacklog.getYyProductModuleId())
//                .eq("project_info_id", productBacklog.getProjectInfoId())
//                .eq("hospital_info_id", productBacklog.getHospitalInfoId())
//                .last("limit 1")
//        );
//        BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
//        baseIdNameResp.setId(-1L);
//        baseIdNameResp.setName("产品总状态");
//        list.add(baseIdNameResp);
//        if (CollectionUtil.isNotEmpty(projProductBacklogs)) {
//            ProjProductBacklog backlog = projProductBacklogs.get(0);
//            // 基础数据、配置、待处理任务、报表、表单
//            if (backlog.getBaseDataStatus() != 0) {
//                BaseIdNameResp baseIdNameResp2 = new BaseIdNameResp();
//                baseIdNameResp2.setId(2L);
//                baseIdNameResp2.setName("基础数据");
//                list.add(baseIdNameResp2);
//            }
//            if (backlog.getConfigDataStatus() != 0) {
//                BaseIdNameResp baseIdNameResp2 = new BaseIdNameResp();
//                baseIdNameResp2.setId(4L);
//                baseIdNameResp2.setName("配置");
//                list.add(baseIdNameResp2);
//            }
//            if (backlog.getTodoTaskStatus() != 0) {
//                BaseIdNameResp baseIdNameResp2 = new BaseIdNameResp();
//                baseIdNameResp2.setId(8L);
//                baseIdNameResp2.setName("待处理任务");
//                list.add(baseIdNameResp2);
//            }
//            if (backlog.getReportDataStatus() != 0) {
//                BaseIdNameResp baseIdNameResp2 = new BaseIdNameResp();
//                baseIdNameResp2.setId(6L);
//                baseIdNameResp2.setName("报表");
//                list.add(baseIdNameResp2);
//            }
//            if (backlog.getFormDataStatus() != 0) {
//                BaseIdNameResp baseIdNameResp2 = new BaseIdNameResp();
//                baseIdNameResp2.setId(7L);
//                baseIdNameResp2.setName("表单");
//                list.add(baseIdNameResp2);
//            }
//        }
//        return Result.success(list);
//    }

    /**
     * 批量插入产品待办任务数据
     *
     * @param list
     * @return
     */
    @Override
    public Result batchInsert(List<ProjProductBacklog> list) {
        return Result.success();
    }

    /**
     * 查询代办产品
     *
     * @param dto 请求参数
     * @return List<ProjProductBacklogVO>
     */
    @Override
    public List<ProjProductBacklogVO> findProductBacklogVOS(ProjProductBacklogDTO dto) {
        if (ObjectUtil.isNotEmpty(dto.getYyProductId())) {
            // 当前端进行产品查询时，可能查询模块产品，需要进行判断选择的产品是否为模块产品。是的时候 重新赋值查询字段数据
            DictProduct dictProduct = productMapper.selectOne(
                    new QueryWrapper<DictProduct>().eq("yy_product_id", dto.getYyProductId()));
            if (ObjectUtil.isEmpty(dictProduct)) {
                // 当产品数据为空时  表明是模块产品
                DictProductVsModules modules = productVsModulesMapper.selectOne(
                        new QueryWrapper<DictProductVsModules>().eq("yy_module_id", dto.getYyProductId()));
                if (ObjectUtil.isNotEmpty(modules)) {
                    dto.setYyProductId(modules.getYyProductId());
                    dto.setYyProductModuleId(modules.getYyModuleId());
                }
            }
        }
        dto.setMilestoneNodeCode(MilestoneNodeEnum.PREPARAT_PRODUCT.getCode());
        if (dto.getTodoType() != null && dto.getTodoType() == 2 && dto.getCompleteStatus() != null) {
            dto.setBaseDataStatus(dto.getCompleteStatus() == 1 ? 2 : 1);
            dto.setCompleteStatus(null);
        } else if (dto.getTodoType() != null && dto.getTodoType() == 4 && dto.getCompleteStatus() != null) {
            dto.setConfigDataStatus(dto.getCompleteStatus() == 1 ? 2 : 1);
            dto.setCompleteStatus(null);
        } else if (dto.getTodoType() != null && dto.getTodoType() == 6 && dto.getCompleteStatus() != null) {
            dto.setReportDataStatus(dto.getCompleteStatus() == 1 ? 2 : 1);
            dto.setCompleteStatus(null);
        } else if (dto.getTodoType() != null && dto.getTodoType() == 7 && dto.getCompleteStatus() != null) {
            dto.setFormDataStatus(dto.getCompleteStatus() == 1 ? 2 : 1);
            dto.setCompleteStatus(null);
        } else if (dto.getTodoType() != null && dto.getTodoType() == 8 && dto.getCompleteStatus() != null) {
            dto.setTodoTaskStatus(dto.getCompleteStatus() == 1 ? 2 : 1);
            dto.setCompleteStatus(null);
        }
        // 查询条件处理 【产品的下拉以及状态处理】
        if (ObjectUtil.isNotEmpty(dto.getProductJobMenuId()) && dto.getProductJobMenuId() == 2) {
            // 基础数据
            if (ObjectUtil.isNotEmpty(dto.getProductJobMenuStatus())) {
                dto.setBaseDataStatus(dto.getProductJobMenuStatus() == 1 ? 2 : 1);
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getProductJobMenuId()) && dto.getProductJobMenuId() == 4) {
            // 配置
            if (ObjectUtil.isNotEmpty(dto.getProductJobMenuStatus())) {
                dto.setConfigDataStatus(dto.getProductJobMenuStatus() == 1 ? 2 : 1);
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getProductJobMenuId()) && dto.getProductJobMenuId() == 8) {
            // 待处理任务
            if (ObjectUtil.isNotEmpty(dto.getProductJobMenuStatus())) {
                dto.setTodoTaskStatus(dto.getProductJobMenuStatus() == 1 ? 2 : 1);
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getProductJobMenuId()) && dto.getProductJobMenuId() == 6) {
            // 报表
            if (ObjectUtil.isNotEmpty(dto.getProductJobMenuStatus())) {
                dto.setReportDataStatus(dto.getProductJobMenuStatus() == 1 ? 2 : 1);
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getProductJobMenuId()) && dto.getProductJobMenuId() == 7) {
            // 表单
            if (ObjectUtil.isNotEmpty(dto.getProductJobMenuStatus())) {
                dto.setFormDataStatus(dto.getProductJobMenuStatus() == 1 ? 2 : 1);
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getProductJobMenuId()) && dto.getProductJobMenuId() == -1) {
            // 产品总体状态
            dto.setCompleteStatus(dto.getProductJobMenuStatus());
        }
        log.warn("selectProductBacklog-selectProductBacklog-args:{}", JSON.toJSONString(dto));
        return productBacklogMapper.selectProductBacklog(dto);
    }

    /**
     * 查询产品待办任务列表数据
     *
     * @param dto 请求参数
     * @return ProjProductBacklogDataVO
     */
    @Override
    public ProjProductBacklogDataVO selectProductBacklog(ProjProductBacklogDTO dto) {
        ProjProductBacklogDataVO projProductBacklogDataVO = new ProjProductBacklogDataVO();
        boolean smallFrontBigBack = projectInfoService.isSmallFrontBigBack(dto.getProjectInfoId());
        boolean planModel = projectConfigService.isPlanModel(dto.getProjectInfoId());
        // 开启项目计划并且开启小前端大后端
        projProductBacklogDataVO.setSmallFrontBigBackFlag(planModel && smallFrontBigBack);
        if (planModel && smallFrontBigBack) {
            ProjProjectPlan projectPlan = projectPlanService.getProjectPlanByProjectInfoIdAndItemCode(dto.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
            projProductBacklogDataVO.setBackendPlanSysUserId(projectPlan.getBackendEngineerId());
        }
        // 查询产品
        List<ProjProductBacklogVO> projProductBacklogVOS = findProductBacklogVOS(dto);
        projProductBacklogVOS.forEach(projProductBacklogVO -> {
            if (ObjectUtil.isEmpty(projProductBacklogVO)) {
                return;
            }
            // 赋值 状态名称
            projProductBacklogVO.setCompleteStatusName(ObjectUtil.isNotEmpty(projProductBacklogVO.getCompleteStatus())
                    && projProductBacklogVO.getCompleteStatus() == 1 ? "已完成" : "未完成");
            // 基础数据
            if (projProductBacklogVO.getBaseDataStatus() == 0) {
                projProductBacklogVO.setBaseDataStatusName("");
            } else {
                projProductBacklogVO.setBaseDataStatusName(
                        ObjectUtil.isNotEmpty(projProductBacklogVO.getBaseDataStatus())
                                && projProductBacklogVO.getBaseDataStatus() == 1 ? "未完成" : "已完成");
            }
            // 配置
            if (projProductBacklogVO.getConfigDataStatus() == 0) {
                projProductBacklogVO.setConfigDataStatusName("");
            } else {
                // 当配置的状态是未完成的时候 ， 判断配置表中该产品有没有数据，有数据的时候才展示未完成，没数据时 展示为空。已完成的 不进行不处理(8.15 董博培)
                if (ObjectUtil.isNotEmpty(projProductBacklogVO.getConfigDataStatus())
                        && projProductBacklogVO.getConfigDataStatus() == 1) {
                    List<ProjProductConfig> productConfigs = productConfigMapper.selectList(
                            new QueryWrapper<ProjProductConfig>().eq(projProductBacklogVO.getYyProductModuleId() != -1,
                                            "yy_product_id", projProductBacklogVO.getYyProductModuleId())
                                    .eq(projProductBacklogVO.getYyProductModuleId() == -1, "yy_product_id",
                                            projProductBacklogVO.getYyProductId())
                                    .eq("project_info_id", projProductBacklogVO.getProjectInfoId())
                                    .eq("hospital_info_id", projProductBacklogVO.getHospitalInfoId()));
                    if (CollectionUtil.isNotEmpty(productConfigs)) {
                        projProductBacklogVO.setConfigDataStatusName("未完成");
                    } else {
                        projProductBacklogVO.setConfigDataStatusName("");
                        projProductBacklogVO.setConfigDataStatus(0);
                    }
                } else if (ObjectUtil.isNotEmpty(projProductBacklogVO.getConfigDataStatus())
                        && projProductBacklogVO.getConfigDataStatus() == 2) {
                    projProductBacklogVO.setConfigDataStatusName("已完成");
                }
            }
            // 报表
            if (projProductBacklogVO.getReportDataStatus() == 0) {
                projProductBacklogVO.setReportDataStatusName("");
            } else {
                projProductBacklogVO.setReportDataStatusName(
                        ObjectUtil.isNotEmpty(projProductBacklogVO.getReportDataStatus())
                                && projProductBacklogVO.getReportDataStatus() == 1 ? "未完成" : "已完成");
            }
            // 表单
            if (projProductBacklogVO.getFormDataStatus() == 0) {
                projProductBacklogVO.setFormDataStatusName("");
            } else {
                projProductBacklogVO.setFormDataStatusName(
                        ObjectUtil.isNotEmpty(projProductBacklogVO.getFormDataStatus())
                                && projProductBacklogVO.getFormDataStatus() == 1 ? "未完成" : "已完成");
            }
            // 待处理任务
            if (projProductBacklogVO.getTodoTaskStatus() == 0) {
                projProductBacklogVO.setTodoTaskStatusName("");
            } else {
                // 当待处理任务的状态是未完成的时候 ， 判断配置表中该产品有没有数据，有数据的时候才展示未完成，没数据时 展示为空。已完成的 不进行不处理(8.15 董博培)
                if (ObjectUtil.isNotEmpty(projProductBacklogVO.getTodoTaskStatus())
                        && projProductBacklogVO.getTodoTaskStatus() == 1) {
                    List<ProjProductTask> productTasks = productTaskMapper.selectList(
                            new QueryWrapper<ProjProductTask>()
                                    .eq(projProductBacklogVO.getYyProductModuleId() != -1, "yy_product_id", projProductBacklogVO.getYyProductModuleId())
                                    .eq(projProductBacklogVO.getYyProductModuleId() == -1, "yy_product_id", projProductBacklogVO.getYyProductId())
                                    .eq("project_info_id", projProductBacklogVO.getProjectInfoId())
                                    .eq("hospital_info_id", projProductBacklogVO.getHospitalInfoId()));
                    if (CollectionUtil.isNotEmpty(productTasks)) {
                        projProductBacklogVO.setTodoTaskStatusName("未完成");
                    } else {
                        projProductBacklogVO.setTodoTaskStatusName("");
                        projProductBacklogVO.setTodoTaskStatus(0);
                    }
                } else if (ObjectUtil.isNotEmpty(projProductBacklogVO.getTodoTaskStatus())
                        && projProductBacklogVO.getTodoTaskStatus() == 2) {
                    projProductBacklogVO.setTodoTaskStatusName("已完成");
                }
            }
            // 用时 (有实际完成实际->实际完成时间-开始时间；没有实际完成时间->当前时间-开始时间) *天*小时*分
            if (projProductBacklogVO.getStartTime() != null) {
                // 有完成时间取完成时间没完成时间则为当前时间
                Date endTime = projProductBacklogVO.getActualCompTime() != null ? projProductBacklogVO.getActualCompTime() : new Date();
                // 计算用时
                long durationInMillis = endTime.getTime() - projProductBacklogVO.getStartTime().getTime();
                long days = durationInMillis / (24 * 60 * 60 * 1000);
                long hours = (durationInMillis % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
                long minutes = (durationInMillis % (60 * 60 * 1000)) / (60 * 1000);
                // 格式化用时
                StringBuilder useTimeBuilder = new StringBuilder();
                if (days > 0) {
                    useTimeBuilder.append(days).append("天");
                }
                if (hours > 0 || days > 0) {
                    useTimeBuilder.append(hours).append("小时");
                }
                useTimeBuilder.append(minutes).append("分钟");
                projProductBacklogVO.setUseTime(useTimeBuilder.toString());
            }
        });
        // 排序
        List<ProjProductBacklogVO> sortedItems = projProductBacklogVOS.stream()
                .sorted(Comparator.comparing(ProjProductBacklogVO::getOrderNo,
                        Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        projProductBacklogDataVO.setBacklogList(sortedItems);
        return projProductBacklogDataVO;
    }

    /**
     * 查询运维平台产品 **下拉使用**
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> selectYyProductAndModule(Long projectInfoId) {
        // 孟哥教的产品下拉数据
        List<ProjProductDeliverRecord> deliverRecordList = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(projectInfoId);
        Set<Long> productIds = deliverRecordList.stream().map(e -> e.getProductDeliverId()).collect(Collectors.toSet());
        List<BaseIdNameResp> productInfoList = productMapper.findByProductIds(productIds);
        List<BaseIdNameResp> yyProductVOS2 = new ArrayList<>();
        for (BaseIdNameResp productInfo : productInfoList) {
            BaseIdNameResp yyProductVO = new BaseIdNameResp();
            yyProductVO.setId(productInfo.getId());
            yyProductVO.setName(productInfo.getName().endsWith(StrUtil.DASHED) ? productInfo.getName()
                    .substring(0, productInfo.getName().length() - 1) : productInfo.getName());
            yyProductVOS2.add(yyProductVO);
            //@TODO 需要过滤掉不需要调研的产品
        }
        return Result.success(yyProductVOS2);
    }

    /**
     * 分配调研计划查询产品
     *
     * @param req@return
     */
    @Override
    public Result<List<SurveyPlanProductResp>> selectSurveyPlanProducts(QuerySurveyPlanProductReq req) {
        List<ProjProductDeliverRecord> deliverRecordList = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(req.getProjectInfoId());
        Set<Long> productIds = deliverRecordList.stream().map(e -> e.getProductDeliverId()).collect(Collectors.toSet());
        List<BaseIdNameResp> productInfoList = productMapper.findByProductIds(productIds);
        //查询已经分配的调研计划产品
        List<ProjSurveyPlan> surveyPlanList = surveyPlanMapper.findSurveyPlanByProjectAndHospital(
                req.getProjectInfoId(), req.getHospitalInfoId());
        Set<Long> kasAllocatedProducts = surveyPlanList.stream().map(e -> e.getYyProductId())
                .collect(Collectors.toSet());
        List<SurveyPlanProductResp> resps = productInfoList.stream().map(item -> {
            SurveyPlanProductResp resp = new SurveyPlanProductResp();
            resp.setId(item.getId());
            resp.setName(item.getName().endsWith(StrUtil.DASHED) ? item.getName()
                    .substring(0, item.getName().length() - 1) : item.getName());
            resp.setAllocated(kasAllocatedProducts.contains(item.getId()));
            return resp;
        }).collect(Collectors.toList());
        return Result.success(resps);
    }

    /**
     * 更新产品待办任务责任人
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateProductBacklogLeader(ProjProductBacklogDTO dto) {
        // 允许只分配产品准备工作的后端负责人
        if (dto.getBackendSysUserId() != null && dto.getUserId() == null) {
            ProjMilestoneTaskDetail projMilestoneTaskDetail = new ProjMilestoneTaskDetail();
            projMilestoneTaskDetail.setMilestoneTaskDetailId(dto.getMilestoneTaskDetailId());
            projMilestoneTaskDetail.setExpectCompTime(dto.getExpectCompTime());
            projMilestoneTaskDetail.setBackendSysUserId(dto.getBackendSysUserId());
            milestoneTaskDetailMapper.updateById(projMilestoneTaskDetail);
            log.warn("updateProductBacklogLeader-只分配后端负责人, 不分配前端负责人, dto:{}", JSON.toJSONString(dto));
            return Result.success();
        }
        ProjMilestoneTaskDetail projMilestoneTaskDetail = new ProjMilestoneTaskDetail();
        projMilestoneTaskDetail.setMilestoneTaskDetailId(dto.getMilestoneTaskDetailId());
        projMilestoneTaskDetail.setLeaderId(dto.getUserId());
        projMilestoneTaskDetail.setExpectCompTime(dto.getExpectCompTime());
        projMilestoneTaskDetail.setBackendSysUserId(dto.getBackendSysUserId());
        milestoneTaskDetailMapper.updateById(projMilestoneTaskDetail);
        // 查询任务明细。
        ProjMilestoneTaskDetail newBean = milestoneTaskDetailMapper.selectById(
                projMilestoneTaskDetail.getMilestoneTaskDetailId());
        // 查询任务
        ProjMilestoneTask taskData = milestoneTaskMapper.selectById(newBean.getMilestoneTaskId());
        // 查询项目信息
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(taskData.getProjectInfoId());
        Long userId = dto.getUserId();
        String productId = String.valueOf(newBean.getProductDeliverId());
        // 同步更新TDUCK 负责人
        String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(projectInfo.getProjectInfoId(), productId, 1);
        List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                new QueryWrapper<FmUserFormData>().eq("form_key", formKey).eq("source", "config")
                        .eq("hospital_info_id", taskData.getHospitalInfoId()));
        if (!CollectionUtils.isEmpty(fmUserFormDataList)) {
            fmUserFormDataList.forEach(fmUserFormData -> {
                FmUserFormData fmUserFormDataUpdate = new FmUserFormData();
                fmUserFormDataUpdate.setId(fmUserFormData.getId());
                fmUserFormDataUpdate.setSysUserId(userId);
                fmUserFormDataMapper.updateById(fmUserFormDataUpdate);
            });
        }
        try {
            // 分配表单负责人
            List<ProjSurveyForm> formList = projSurveyFormMapper.selectList(new QueryWrapper<ProjSurveyForm>()
                    .eq("project_info_id", projectInfo.getProjectInfoId())
                    .eq("yy_product_id", newBean.getProductDeliverId())
                    .eq("hospital_info_id", taskData.getHospitalInfoId())
            );
            if (!CollectionUtils.isEmpty(formList)) {
                formList.forEach(projSurveyForm -> {
                    if (ObjectUtil.isEmpty(projSurveyForm.getMakeUserId())) {
                        projSurveyForm.setMakeUserId(userId);
                        projSurveyFormMapper.updateById(projSurveyForm);
                    }
                });
            }
            // 分配报表负责人
            List<ProjSurveyReport> reportList = projSurveyReportMapper.selectList(new QueryWrapper<ProjSurveyReport>()
                    .eq("project_info_id", projectInfo.getProjectInfoId())
                    .eq("yy_product_id", newBean.getProductDeliverId())
                    .eq("hospital_info_id", taskData.getHospitalInfoId())
            );
            if (!CollectionUtils.isEmpty(reportList)) {
                reportList.forEach(surveyReport -> {
                    if (ObjectUtil.isEmpty(surveyReport.getMakeUserId())) {
                        surveyReport.setMakeUserId(userId);
                        projSurveyReportMapper.updateById(surveyReport);
                    }
                });
            }
            // 分配设备责任人
            List<ProjEquipRecord> equipInfoList = projEquipRecordMapper.selectList(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projectInfo.getProjectInfoId())
                    .eq("yy_product_id", newBean.getProductDeliverId())
                    .eq("hospital_info_id", taskData.getHospitalInfoId())
            );
            if (!CollectionUtils.isEmpty(equipInfoList)) {
                equipInfoList.forEach(equipRecord -> {
                    if (ObjectUtil.isEmpty(equipRecord.getDirUserId())) {
                        equipRecord.setDirUserId(userId);
                        projEquipRecordMapper.updateById(equipRecord);
                    }
                });
            }
        } catch (Exception e) {
            log.error("分配责任人失败异常信息: {}", e.getMessage(), e);
            return Result.fail("分配责任人失败;" + e);
        }
        // 生成设备准备的责任人数据
        equipInfoTask(taskData.getCustomerInfoId(), taskData.getProjectInfoId(), taskData.getHospitalInfoId(),
                newBean.getProductDeliverId(), userId, dto.getExpectCompTime());
        // 生成小前端大后端待办任务
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(projectInfo.getProjectInfoId());
        param.setHospitalInfoId(taskData.getHospitalInfoId());
        param.setYyProductId(newBean.getProductDeliverId());
        param.setPlanTime(dto.getExpectCompTime());
        param.setUserId(userId);
        param.setCode(DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
        param.setBackendSysUserId(dto.getBackendSysUserId());
        todoTaskService.projectTodoTaskInit(param);
        return Result.success();
    }

    void equipInfoTask(Long customInfoId, Long projectInfoId, Long hospitalInfoId, Long yyProductId, Long userId, Date expectComTime) {
        // 查询当前产品是否包含设备。当不包含时 跳过
        ConfigProductJobMenuDetail configProductJobMenuDetail = configProductJobMenuDetailMapper.selectOne(
                new QueryWrapper<ConfigProductJobMenuDetail>()
                        .eq("product_job_menu_id", 5)
                        .eq("yy_product_id", yyProductId)
        );
        if (ObjectUtil.isNotEmpty(configProductJobMenuDetail)) {
            // 当责任人不为空时候 说明指定了负责人。同步设置 设备调研负责人信息， milestoneTaks表创建 设备调研任务
            HospitalInfoDTO hospitalInfoDTO = new HospitalInfoDTO();
            hospitalInfoDTO.setCustomInfoId(customInfoId);
            hospitalInfoDTO.setProjectInfoId(projectInfoId);
            hospitalInfoDTO.setHospitalInfoId(hospitalInfoId);
            // 查询设备调研里程碑节点信息
            ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(projectInfoId,
                    MilestoneNodeEnum.PREPARAT_DEVICE.getCode());
            MilestoneInfoDTO milestoneInfoDTO = new MilestoneInfoDTO();
            milestoneInfoDTO.setMilestoneNodeCode(MilestoneNodeEnum.PREPARAT_DEVICE.getCode());
            milestoneInfoDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
            milestoneInfoDTO.setProjectStageId(milestoneInfo.getProjectStageId());
            milestoneInfoDTO.setProjectStageCode(milestoneInfo.getProjectStageCode());
            // 组装任务数据
            ResearchPlanDTO researchPlanDTO = getResearchPlanDTO(hospitalInfoDTO, milestoneInfoDTO, null, new Date());
            // 检查是否已经存在任务，当不存在时候 进行创建
            ProjMilestoneTask milestoneTask = milestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                    .eq("milestone_node_code", MilestoneNodeEnum.PREPARAT_DEVICE.getCode())
                    .eq("customer_info_id", customInfoId)
                    .eq("project_info_id", projectInfoId)
                    .eq("hospital_info_id", hospitalInfoId)
            );
            if (ObjectUtil.isEmpty(milestoneTask)) {
                log.info("生成设备调研的里程碑节点任务数据 , 数据参数信息 , {}", JSONUtil.toJsonStr(researchPlanDTO));
                int i = milestoneTaskMapper.insertResearchPlans(Arrays.asList(researchPlanDTO));
                log.warn("生成设备调研的里程碑节点任务数据 , 影响行数 , {}", i);
            }
            ProjMilestoneTask milestoneTask2 =
                    milestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                            .eq("milestone_node_code", MilestoneNodeEnum.PREPARAT_DEVICE.getCode())
                            .eq("customer_info_id", customInfoId)
                            .eq("project_info_id", projectInfoId)
                            .eq("hospital_info_id", hospitalInfoId)
                    );
            // 生成 设备调研里程碑节点明细任务数据
            ProjMilestoneTaskDetail projMilestoneTaskDetail = infoDataForTaskDetail(milestoneTask2,
                    yyProductId, userId);
            // 检查是否已经生成了明细任务
            ProjMilestoneTaskDetail projMilestoneTaskDetail1 =
                    milestoneTaskDetailMapper.selectOne(new QueryWrapper<ProjMilestoneTaskDetail>()
                            .eq("milestone_task_id", milestoneTask2.getMilestoneTaskId())
                            .eq("product_deliver_id", yyProductId)
                    );
            if (ObjectUtil.isNotEmpty(projMilestoneTaskDetail1)) {
                projMilestoneTaskDetail.setMilestoneTaskDetailId(projMilestoneTaskDetail1.getMilestoneTaskDetailId());
                log.info("更新设备调研的里程碑节点明细任务 , 数据参数信息 , {}",
                        JSONUtil.toJsonStr(projMilestoneTaskDetail));
                int i = milestoneTaskDetailMapper.updateById(projMilestoneTaskDetail);
                log.warn("更新设备调研的里程碑节点明细任务 , 影响行数 , {}", i);
            } else {
                log.info("生成设备调研的里程碑节点明细任务 , 数据参数信息 , {}",
                        JSONUtil.toJsonStr(projMilestoneTaskDetail));
                int i = milestoneTaskDetailMapper.insert(projMilestoneTaskDetail);
                log.warn("生成设备调研的里程碑节点明细任务 , 影响行数 , {}", i);
            }

        }

    }

    /**
     * 组装设备调研明细任务数据
     *
     * @param milestoneTask
     * @param yyProductId
     * @param surveyUserId
     * @return
     */
    private ProjMilestoneTaskDetail infoDataForTaskDetail(ProjMilestoneTask milestoneTask,
                                                          Long yyProductId, Long surveyUserId) {
        ProjMilestoneTaskDetail detail = new ProjMilestoneTaskDetail();
        detail.setMilestoneTaskDetailId(SnowFlakeUtil.getId());
        detail.setLeaderId(surveyUserId);
        detail.setProductDeliverId(yyProductId);
        detail.setMilestoneTaskId(milestoneTask.getMilestoneTaskId());
        detail.setProductDeliverRecordId(yyProductId);
        detail.setCompleteStatus(0);
        return detail;
    }

    /**
     * 组装调研计划数据
     *
     * @param v
     * @param infoDTO
     * @param secondLeaders
     * @param createTime
     * @return
     */
    private ResearchPlanDTO getResearchPlanDTO(HospitalInfoDTO v, MilestoneInfoDTO infoDTO, String secondLeaders,
                                               Date createTime) {
        ResearchPlanDTO planDto = new ResearchPlanDTO();
        planDto.setProjResearchPlanId(SnowFlakeUtil.getId());
        planDto.setHospitalId(v.getHospitalInfoId() == null ? 0 : v.getHospitalInfoId());
        planDto.setProjectInfoId(v.getProjectInfoId());
        planDto.setCustomerInfoId(v.getCustomInfoId());
        planDto.setResearchCode(infoDTO.getMilestoneNodeCode() == null ? "" : infoDTO.getMilestoneNodeCode());
        planDto.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        planDto.setCreateTime(createTime);
        planDto.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        planDto.setUpdateTime(createTime);
        planDto.setPlanStartTime(ObjectUtil.isNotEmpty(infoDTO.getExpectStartTime())
                ? DateUtil.beginOfDay(infoDTO.getExpectStartTime()) : null);
        planDto.setPlanEndTime(ObjectUtil.isNotEmpty(infoDTO.getExpectCompTime())
                ? DateUtil.endOfDay(infoDTO.getExpectCompTime()) : null);
        planDto.setLeaderId(v.getLeaderId() == null ? 0 : v.getLeaderId());
        planDto.setSecondLeaderId(secondLeaders == null ? "" : secondLeaders);
        planDto.setResultSourceId(40004L);
        planDto.setIsDeleted(0);
        planDto.setMilestoneInfoId(infoDTO.getMilestoneInfoId());
        planDto.setProjectStageId(infoDTO.getProjectStageId());
        planDto.setProjectStageCode(infoDTO.getProjectStageCode());
        return planDto;
    }

    @Override
    @Transactional
    public Result batchUpdateProductBacklogLeaderList(ProjProductBacklogDTO dto) {
        if (CollUtil.isEmpty(dto.getHospitalInfoIdList()) && CollUtil.isEmpty(dto.getYyProductIdList())) {
            log.error("批量分配失败，医院和运营产品不能同时为空，参数:{}", JSON.toJSONString(dto));
            return Result.fail("批量分配失败，医院和运营产品不能同时为空");
        }
        //判断是否全部医院
        boolean isAllHosp = dto.getHospitalInfoIdList().contains(-999L);
        List<Long> hospitalInfoIdList = dto.getHospitalInfoIdList();
        if (isAllHosp) {
            SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
            selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
            hospitalInfoIdList = hospitalInfoList.stream().map(ProjHospitalInfo::getHospitalInfoId).collect(Collectors.toList());
        }
        //根据项目id和医院id查询待办任务表  project_info_id =503580431407218688 and hospital_info_id =901159963760726016
        List<ProjMilestoneTask> taskList = milestoneTaskMapper.selectList(
                new QueryWrapper<ProjMilestoneTask>()
                        .in("hospital_info_id", hospitalInfoIdList)
                        .eq("project_info_id", dto.getProjectInfoId())
                        .eq("is_deleted", 0)
        );
        if (CollUtil.isEmpty(taskList)) {
            log.error("批量分配失败，通过项目匹配医院任务查询为空，参数:{}", JSON.toJSONString(dto));
            return Result.fail("批量分配失败，通过项目匹配医院任务查询为空");
        }
        //判断是否全部医院
        boolean isAllProduct = dto.getYyProductIdList().contains(-999L);
        List<Long> yyProductIdList = dto.getYyProductIdList();
        if (isAllProduct) {
            Result<List<BaseIdNameResp>> yyProductAndModule = this.selectYyProductAndModule(dto.getProjectInfoId());
            yyProductIdList = yyProductAndModule.getData().stream().map(BaseIdNameResp::getId).collect(Collectors.toList());

        }
        //解析出任务表id
        List<Long> taskIds = taskList.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
        //根据任务表id和产品id查询产品待办任务明细表
        List<ProjMilestoneTaskDetail> taskDetailList = milestoneTaskDetailMapper.selectList(
                new QueryWrapper<ProjMilestoneTaskDetail>()
                        .in("milestone_task_id", taskIds)
                        .in("product_deliver_id", yyProductIdList)
                        .eq("is_deleted", 0)
        );
        if (CollUtil.isEmpty(taskDetailList)) {
            log.error("批量分配失败，通过产品匹配任务明细查询为空，参数:{}", JSON.toJSONString(dto));
            return Result.fail("批量分配失败，通过产品匹配任务明细查询为空");
        }
        //根据项目id和医院id查询待办任务表
        List<Long> taskDetailIds =
                taskDetailList.stream().map(e -> e.getMilestoneTaskDetailId()).collect(Collectors.toList());
        //调用分配方法
        taskDetailIds.stream().forEach(
                taskDetailId -> {
                    ProjProductBacklogDTO newDto = new ProjProductBacklogDTO();
                    newDto.setMilestoneTaskDetailId(taskDetailId);
                    newDto.setUserId(dto.getUserId());
                    newDto.setExpectCompTime(dto.getExpectCompTime());
                    newDto.setBackendSysUserId(dto.getBackendSysUserId());
                    updateProductBacklogLeader(newDto);
                }
        );
        return Result.success();
    }

    @Resource
    private DictProductVsDeliverService dictProductVsDeliverService;

    /**
     * 查询明细页面动态菜单数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ProductBacklogUrlVO>> selectBacklogUrlList(ProjProductBacklogDTO dto) {
        // 查询产品菜单配置表数据
        dto.setPrepareUseFlag(1);
        GetProductJobMenuDetailParam param = new GetProductJobMenuDetailParam();
        param.setUpgradationType(dictProductVsDeliverService.getUpgradationTypeByDeliverProductId(dto.getYyProductId(), dto.getProjectInfoId()));
        param.setYyProductId(dto.getYyProductId());
        param.setPrepareUseFlag(1);
        List<ConfigProductJobMenuDetailVO> configProductJobMenuDetailVOS = configProductJobMenuDetailMapper.getProductJobMenuDetail(param);
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(
                new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", dto.getProjectInfoId())
                        .in("milestone_node_code", MilestoneNodeEnum.SURVEY_REPORT.getCode())
                        .eq("invalid_flag", NumberEnum.NO_0.num()));
        // 查询项目下调研阶段表单是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoFormList = projMilestoneInfoMapper.selectList(
                new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", dto.getProjectInfoId())
                        .in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode())
                        .eq("invalid_flag", NumberEnum.NO_0.num()));
        // 过滤只需要基础数据的菜单信息
        //2	baseData 基础数据
        //4	setting 配置
        //8	other 待处理任务
        //5	equip 设备
        //6	report 报表
        //7	form 表单
        //0 不包含  1 未完成  2 已完成
        List<ConfigProductJobMenuDetailVO> menuDetailVOList = configProductJobMenuDetailVOS.stream()
                .filter(vo -> vo.getProductJobMenuId() == 2 || vo.getProductJobMenuId() == 4
                        || vo.getProductJobMenuId() == 8 || vo.getProductJobMenuId() == 6
                        || vo.getProductJobMenuId() == 7).collect(Collectors.toList());
        ProjProductBacklog productBacklog = productBacklogMapper.selectByParam(dto);
        if (ObjectUtils.isNotEmpty(productBacklog)) {
            if (productBacklog.getBaseDataStatus() == 0) {
                menuDetailVOList.removeIf(vo -> vo.getProductJobMenuId() == 2);
            }
            if (productBacklog.getConfigDataStatus() == 0) {
                menuDetailVOList.removeIf(vo -> vo.getProductJobMenuId() == 4);
            }
            if (productBacklog.getTodoTaskStatus() == 0) {
                menuDetailVOList.removeIf(vo -> vo.getProductJobMenuId() == 8);
            }
            if (productBacklog.getReportDataStatus() == 0) {
                menuDetailVOList.removeIf(vo -> vo.getProductJobMenuId() == 6);
            }
            if (productBacklog.getFormDataStatus() == 0) {
                menuDetailVOList.removeIf(vo -> vo.getProductJobMenuId() == 7);
            }
        }
        List<ProductBacklogUrlVO> result = new ArrayList<>();
        // 处理 基础数据的地址信息。拼接参数。部分链接为完成链接，存在http时 跳过
        menuDetailVOList.forEach(vo -> {
            List<ProjSurveyPlan> projSurveyPlanList = surveyPlanMapper.selectList(
                    new QueryWrapper<ProjSurveyPlan>()
                            .eq("is_deleted", 0)
                            .eq("project_info_id", dto.getProjectInfoId())
                            .eq("hospital_info_id", dto.getHospitalInfoId())
                            .eq("yy_product_id", dto.getYyProductId())
                            .eq("complete_status", 1)
            );
            if (CollectionUtils.isEmpty(projSurveyPlanList)) {
                String productName = dictProductService.getProductNameByYyProductId(dto.getYyProductId());
                throw new IllegalArgumentException(String.format("【%s】尚未确认调研结果，请先前往产品业务调研完成确认，再进行产品准备工作。产品ID=%s", productName, dto.getYyProductId()));
            }
            ProductBacklogUrlVO backlogUrlVO = new ProductBacklogUrlVO();
            if (vo.getProductJobMenuId() == 2) {
                // 组装基础数据的链接地址（老系统程序链接）
                infoDataBaseUrl(vo, dto);
                backlogUrlVO.setMenuUrl(vo.getMenuUrl());
                backlogUrlVO.setMenuName(vo.getMenuName());
                backlogUrlVO.setMenuCode("BasicData");
            } else if (vo.getProductJobMenuId() == 4) {
                // 配置的菜单需要调用填鸭表单
                CreateTduckFormParam createTduckFormParam = new CreateTduckFormParam();
                createTduckFormParam.setProjectInfoId(dto.getProjectInfoId());
                createTduckFormParam.setProductId(dto.getYyProductId().toString());
                createTduckFormParam.setHospitalInfoId(dto.getHospitalInfoId());
                createTduckFormParam.setDeptName(projSurveyPlanList.get(0).getDeptName());
                createTduckFormParam.setSysUserId(dto.getUserId());
                createTduckFormParam.setSource("config");
                createTduckFormParam.setOperationSource("ready");
                String configUrl = tduckService.createTduckForm(createTduckFormParam);
                // 判断项目是否上线，当上线后增加 禁止修改调研结果参数
                ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(dto.getProjectInfoId());
                if (ObjectUtil.isNotEmpty(projProjectInfo) && projProjectInfo.getProjectDeliverStatus() >= 5
                        && ObjectUtil.isNotEmpty(projProjectInfo.getOnlineTime())) {
                    configUrl = configUrl + "&submit=disabled";
                }
                backlogUrlVO.setMenuUrl(configUrl);
                backlogUrlVO.setMenuName(vo.getMenuName());
                backlogUrlVO.setMenuCode("ToConfigure");
            } else if (vo.getProductJobMenuId() == 8) {
                // 待处理任务也添加查看调研结果的链接
                CreateTduckFormParam createTduckFormParam = new CreateTduckFormParam();
                createTduckFormParam.setProjectInfoId(dto.getProjectInfoId());
                createTduckFormParam.setProductId(dto.getYyProductId().toString());
                createTduckFormParam.setHospitalInfoId(dto.getHospitalInfoId());
                createTduckFormParam.setDeptName(projSurveyPlanList.get(0).getDeptName());
                createTduckFormParam.setSysUserId(dto.getUserId());
                createTduckFormParam.setSource("config");
                createTduckFormParam.setOperationSource("ready");
                String configUrl = tduckService.createTduckForm(createTduckFormParam);
                // 判断项目是否上线，当上线后增加 禁止修改调研结果参数
                ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(dto.getProjectInfoId());
                if (ObjectUtil.isNotEmpty(projProjectInfo) && projProjectInfo.getProjectDeliverStatus() >= 5
                        && ObjectUtil.isNotEmpty(projProjectInfo.getOnlineTime())) {
                    configUrl = configUrl + "&submit=disabled";
                }
                backlogUrlVO.setMenuUrl(configUrl);
                backlogUrlVO.setMenuName(vo.getMenuName());
                backlogUrlVO.setMenuCode("PendingTasks");
            } else if (vo.getProductJobMenuId() == 7) {
                // 设置表单链接
                infoDataBaseUrl(vo, dto);
                backlogUrlVO.setMenuName(vo.getMenuName());
                backlogUrlVO.setMenuCode("Form");
                backlogUrlVO.setMenuUrl(vo.getMenuUrl());
                if (milestoneInfoFormList != null && milestoneInfoFormList.size() > 0) {
                    Boolean isShowFormNew = milestoneInfoFormList.get(0).getIsComponent() != null && !"".equals(
                            milestoneInfoFormList.get(0).getIsComponent());
                    if (isShowFormNew) {
                        backlogUrlVO.setMenuCode("FormSurvey");
                    }
                }
            } else if (vo.getProductJobMenuId() == 6) {
                // 设置报表连接
                infoDataBaseUrl(vo, dto);
                backlogUrlVO.setMenuName(vo.getMenuName());
                backlogUrlVO.setMenuCode("Report");
                backlogUrlVO.setMenuUrl(vo.getMenuUrl());
                if (milestoneInfoList != null && milestoneInfoList.size() > 0) {
                    Boolean isShowNew = milestoneInfoList.get(0).getIsComponent() != null && !"".equals(
                            milestoneInfoList.get(0).getIsComponent());
                    if (isShowNew) {
                        backlogUrlVO.setMenuCode("ReportSurvey");
                    }
                }
            }
            result.add(backlogUrlVO);
        });
        return Result.success(result);
    }

    /**
     * 配置数据导入云健康
     *
     * @param dto
     * @return
     */
    @Override
    public Result importHealthConfig(ProjProductBacklogDTO dto) {
        Result result = null;
        // 查询配置数据
        List<ProjProductConfig> configList = productConfigMapper.selectList(
                new QueryWrapper<ProjProductConfig>().eq("project_info_id", dto.getProjectInfoId())
                        .eq("hospital_info_id", dto.getHospitalInfoId()).eq("yy_product_id", dto.getYyProductId()));
        if (CollectionUtil.isEmpty(configList)) {
            return Result.fail("未查询到配置数据");
        }
        // 获取配置中 code为系统管理的配置数据
        List<ProjProductConfig> sys = configList.stream().filter(config -> config.getConfigType().equals("sys"))
                .collect(Collectors.toList());
        List<ProjProductConfig> productConfigs = configList.stream()
                .filter(config -> !config.getConfigType().equals("sys")).collect(Collectors.toList());
        // 导入系统管理配置数据
        if (CollectionUtil.isNotEmpty(sys)) {
            result = systemConfigImport(sys);
            log.info("系统配置导入结果 ， ============ , {}", JSONUtil.toJsonStr(result));
        }
        // 产品导入配置数据
        if (CollectionUtil.isNotEmpty(productConfigs)) {
            result = productConfigImport(productConfigs, productConfigs.get(0).getConfigType());
            log.info("产品导入配置结果 ， ============ , {}", JSONUtil.toJsonStr(result));
        }
        if (result.isSuccess()) {
            // 配置导入结束后 ， 更新backlog表中 该产品的配置完成状态
            ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
            projProductBacklogDTO.setProjectInfoId(dto.getProjectInfoId());
            projProductBacklogDTO.setHospitalInfoId(dto.getHospitalInfoId());
            projProductBacklogDTO.setYyProductId(dto.getYyProductId());
            projProductBacklogDTO.setConfigDataStatus(2);
            log.info("配置导入结束,更新产品待办任务表配置完成状态====" + JSONUtil.toJsonStr(projProductBacklogDTO));
            taskBacklogFinish(projProductBacklogDTO, "配置一键导入云健康");
        }
        return result;
    }

    /**
     * 各产品单独处理数据
     *
     * @param configList
     * @param type
     * @return
     */
    private Result productConfigImport(List<ProjProductConfig> configList, String type) {
        if (CollectionUtil.isNotEmpty(configList)) {
            ProjProductConfig configOne = configList.get(0);
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(configOne.getHospitalInfoId());
            if (hospitalInfo == null) {
                return Result.fail("未查询到医院信息");
            }
            RequestImportDTO dto = new RequestImportDTO();
            dto.setHospitalId(hospitalInfo.getCloudHospitalId());
            dto.setHisOrgId(hospitalInfo.getOrgId());
            List<ProductConfigImportDTO> dataList = new ArrayList<>();
            configList.forEach(config -> {
                ProductConfigImportDTO productConfigImportDTO = new ProductConfigImportDTO();
                productConfigImportDTO.setConfigCode(config.getConfigCode());
                productConfigImportDTO.setConfigValue(config.getConfigValue());
                productConfigImportDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
                productConfigImportDTO.setHisOrgId(hospitalInfo.getOrgId());
                productConfigImportDTO.setConfigName(config.getConfigName());
                dataList.add(productConfigImportDTO);
            });
            dto.setDataList(dataList);
            //API 调用，执行刷新 domain
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            ResponseResult<ProductConfigReturnVO<ProductConfigImportVO>> result = new ResponseResult<>();
            // 根据配置类型区分需要导入哪个云健康产品
            switch (type) {
                // 护士站
                case "nurse":
                    result = productConfigApi.hszConfigImport(dto);
                    break;
                // 院感
                case "hims":
                    result = productConfigApi.himsConfigImport(dto);
                    break;
                // 不良事件
                case "zyares":
                    result = productConfigApi.zyaresConfigImport(dto);
                    break;
                // 手麻
                case "aims":
                    result = productConfigApi.aimsConfigImport(dto);
                    break;
                // 固定资产
                case "equipment":
                    result = productConfigApi.equipmentConfigImport(dto);
                    break;
                // 供应室追溯系统
                case "cssd":
                    result = productConfigApi.cssdConfigImport(dto);
                    break;
                // 医保核心服务
                case "medinsur":
                    result = productConfigApi.medinsurConfigImport(dto);
                    log.info("导入医保配置，结果={}", JSON.toJSONString(result));
                    break;
                default:
                    result = new ResponseResult<>();
                    break;
            }
            log.info("系统管理配置数据导入===返回结果:{}", result);
            if (result.getSuccess()) {
                //成功后更新数据库配置
                UpdateWrapper<ProjProductConfig> objectUpdateWrapper = new UpdateWrapper<>();
                objectUpdateWrapper.eq("project_info_id", configList.get(0).getProjectInfoId());
                objectUpdateWrapper.eq("hospital_info_id", configList.get(0).getHospitalInfoId());
                objectUpdateWrapper.eq("yy_product_id", configList.get(0).getYyProductId());
                objectUpdateWrapper.ne("config_type", "sys");
                ProjProductConfig projProductConfig = new ProjProductConfig();
                projProductConfig.setConfigStatus(1);
                productConfigMapper.update(projProductConfig, objectUpdateWrapper);
                log.info("系统管理配置导入成功");
                // 修改日志信息
                for (ProjProductConfig config : configList) {
                    updateProductConfigLog(config);
                }
                return Result.success("云护理配置导入成功");
            } else {
                return Result.fail("系统管理配置导入失败 , " + result.getMessage());
            }
        }
        return Result.fail("未查询到配置数据");
    }

    /**
     * 测试csm是否通云健康环境
     *
     * @param customInfoId
     * @return
     */
    @Override
    public Result testCsmToApi(Long customInfoId) {
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(
                new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", customInfoId));
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            return Result.fail("未查询到医院信息");
        }
        //API 调用，执行刷新 domain
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfoList.get(0));
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        log.info("医院信息:{}", JSON.toJSONString(hospitalInfoList.get(0)));
        // 2.拼装提交参数
        TestDTO testDTO = new TestDTO();
        testDTO.setHisOrgId(hospitalInfoList.get(0).getOrgId());
        testDTO.setHospitalId(hospitalInfoList.get(0).getCloudHospitalId());
        List<TestDTO> datas = new ArrayList<>();
        datas.add(testDTO);
        BatchInsertDTO<TestDTO> batchInsertDTO = new BatchInsertDTO<>();
        batchInsertDTO.setData(datas);
        batchInsertDTO.setHisOrgId(hospitalInfoList.get(0).getOrgId());
        batchInsertDTO.setHospitalId(hospitalInfoList.get(0).getCloudHospitalId());
        ResponseResult result = new ResponseResult<>();
        try {
            result = testApi.testImspToAPI(batchInsertDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.success(ResponseResult.error(BaseErrorCode.FALLBACK, "网络环境错误"));
        }
    }

    /**
     * 检测交付平台访问云健康数据库
     *
     * @param customInfoId
     * @return
     */
    @Override
    public Result checkNetwork(Long customInfoId) {
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(
                new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", customInfoId));
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            return Result.fail("未查询到医院信息");
        }
        //API 调用，执行刷新 domain
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfoList.get(0));
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        log.info("医院信息:{}", com.alibaba.fastjson.JSON.toJSONString(hospitalInfoList.get(0)));
        // 2.拼装提交参数
        TestDTO testDTO = new TestDTO();
        testDTO.setHisOrgId(hospitalInfoList.get(0).getOrgId());
        testDTO.setHospitalId(hospitalInfoList.get(0).getCloudHospitalId());
        List<TestDTO> datas = new ArrayList<>();
        datas.add(testDTO);
        BatchInsertDTO<TestDTO> batchInsertDTO = new BatchInsertDTO<>();
        batchInsertDTO.setData(datas);
        batchInsertDTO.setHisOrgId(hospitalInfoList.get(0).getOrgId());
        batchInsertDTO.setHospitalId(hospitalInfoList.get(0).getCloudHospitalId());
        ResponseResult result = new ResponseResult<>();
        try {
            result = testApi.checkNetworkIsPass(batchInsertDTO);
            log.info("网络环境----返回的信息:    {}", result);
            return Result.success(result);
        } catch (Exception e) {
            log.error("网络环境，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            log.info("导入结果:{}", com.alibaba.fastjson.JSON.toJSONString(result));
            return Result.success(ResponseResult.error(BaseErrorCode.FALLBACK, "网络环境错误"));
        }
    }

    /**
     * 查询云健康表操作权限
     *
     * @param customInfoId
     * @return
     */
    @Override
    public Result chisDictTableOperationPermissions(Long customInfoId) {
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectList(
                new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", customInfoId));
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            return Result.fail("未查询到医院信息");
        }
        //API 调用，执行刷新 domain
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfoList.get(0));
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        log.info("医院信息:{}", com.alibaba.fastjson.JSON.toJSONString(hospitalInfoList.get(0)));
        // 2.拼装提交参数
        List<TestDatabaseDTO> datas = new ArrayList<>();
        TestDatabaseDTO testDatabaseDTO = new TestDatabaseDTO();
        testDatabaseDTO.setHisOrgId(hospitalInfoList.get(0).getOrgId());
        testDatabaseDTO.setHospitalId(hospitalInfoList.get(0).getCloudHospitalId());
        testDatabaseDTO.setTableSource("chis");
        testDatabaseDTO.setTableSchema("comm");
        testDatabaseDTO.setTableName("hospital");
        datas.add(testDatabaseDTO);
        //组装发送参数，查询权限
        BatchInsertDTO<TestDatabaseDTO> batchInsertDTO = new BatchInsertDTO<>();
        batchInsertDTO.setData(datas);
        batchInsertDTO.setHisOrgId(hospitalInfoList.get(0).getOrgId());
        batchInsertDTO.setHospitalId(hospitalInfoList.get(0).getCloudHospitalId());
        ResponseResult<TestReturnDataDTO<TestDatabaseDTO>> result = new ResponseResult<>();
        try {
            result = testApi.testTableOperationPermissions(batchInsertDTO);
        } catch (Exception e) {
            log.error("==========测试云健康接口数据表权限错误，" + e.getMessage());
        }
        //判断获取是否为null
        if (!result.isSuccess()) {
            return Result.success(ResponseResult.error(BaseErrorCode.FALLBACK, "查询云健康表权限失败"));
        }
        List<TestDatabaseDTO> tablePermissions = result.getData().getTablePermissions();
        //不具备权限
        if (ObjectUtil.isEmpty(tablePermissions)) {
            return Result.success(ResponseResult.error(BaseErrorCode.FALLBACK, "未拥有权限"));
        }
        //过滤出所有没有“删改查”权限的结果
        List<TestDatabaseDTO> res = tablePermissions.stream()
                .filter(tableRes -> !new HashSet<>(tableRes.getOperationPermissions()).containsAll(
                        Arrays.asList("SELECT", "INSERT"))).collect(Collectors.toList());
        //设置提示信息
        if (!res.isEmpty()) {
            List<HashMap<String, String>> resInfo = new ArrayList<>(res.size());
            res.forEach(item -> {
                StringBuilder noPermission = new StringBuilder();
                if (!item.getOperationPermissions().contains("SELECT")) {
                    noPermission.append("查,");
                }
                if (!item.getOperationPermissions().contains("INSERT")) {
                    noPermission.append("增");
                }
                resInfo.add(new HashMap<String, String>(2) {
                    {
                        put("tableName", item.getTableName());
                        put("noPermission", noPermission.toString());
                    }
                });
            });
            return Result.success(ResponseResult.error(BaseErrorCode.FALLBACK, JSONUtil.toJsonStr(resInfo)));
        } else {
            return Result.success(ResponseResult.success("云健康接口拥有该业务相关表操作权限"));
        }
    }

    /**
     * 查询配置导入日志信息
     *
     * @param log
     * @return
     */
    @Override
    public Result<List<ProjProductConfigLogVO>> selectConfigLogList(ProjProductConfigLog log) {
        List<ProjProductConfigLogVO> projProductConfigLogVOS = productConfigLogMapper.selectLogList(log);
        // 判断操作类型。根据操作类型赋值操作人
        for (ProjProductConfigLogVO projProductConfigLogVO : projProductConfigLogVOS) {
            switch (projProductConfigLogVO.getConfigStatus()) {
                case 0:
                    projProductConfigLogVO.setUserName(projProductConfigLogVO.getCreaterName());
                    break;
                case 1:
                case 2:
                    projProductConfigLogVO.setUserName(projProductConfigLogVO.getUpdateName());
                    break;
                default:
                    break;
            }
        }
        return Result.success(projProductConfigLogVOS);
    }

    /**
     * 产品待办任务完成
     * 7
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result taskBacklogFinish(ProjProductBacklogDTO dto, String message) {
        // 判断传入的产品是否不是模块。当为模块时 赋值模块id
        String productName = "";
        DictProductVsModules modules = productVsModulesMapper.selectOne(
                new QueryWrapper<DictProductVsModules>().eq("yy_module_id", dto.getYyProductId()));
        if (ObjectUtil.isEmpty(modules)) {
            DictProduct product = productMapper.selectOne(
                    new QueryWrapper<DictProduct>().eq("yy_product_id", dto.getYyProductId()));
            productName = product.getProductName();
        } else {
            productName = modules.getYyModuleName();
        }
        // 判断传入的值是否为 产品总体完成状态
        if (ObjectUtil.isNotEmpty(dto.getCompleteStatus())) {
            // 修改待处理任务  task_detail表中的 completeStatus
            ProjMilestoneTaskDetail projMilestoneTaskDetail = new ProjMilestoneTaskDetail();
            projMilestoneTaskDetail.setCompleteStatus(dto.getCompleteStatus());
            projMilestoneTaskDetail.setMilestoneTaskDetailId(dto.getMilestoneTaskDetailId());
            milestoneTaskDetailMapper.updateById(projMilestoneTaskDetail);
            saveOperLog(dto.getProjectInfoId(), productName + "==>产品总体状态确认完成1==>" + message, dto);
            //更新产品准备待办状态及进度
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(dto.getProjectInfoId());
            param.setHospitalInfoId(dto.getHospitalInfoId());
            param.setYyProductId(dto.getYyProductId());
            param.setCode(DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
            todoTaskService.updateProjectTodoTaskStatus(param);
            return Result.success("产品总体状态确认完成");
        }
        // 处理产品明细节点状态
        ProjProductBacklog projProductBacklog = new ProjProductBacklog();
        if (ObjectUtil.isNotEmpty(dto.getBaseDataStatus())) {
            projProductBacklog.setBaseDataStatus(dto.getBaseDataStatus());
            saveOperLog(dto.getProjectInfoId(), productName + "==>基础数据节点完成==>" + message, dto);
        }
        if (ObjectUtil.isNotEmpty(dto.getConfigDataStatus())) {
            projProductBacklog.setConfigDataStatus(dto.getConfigDataStatus());
            saveOperLog(dto.getProjectInfoId(), productName + "==>配置节点完成==>" + message, dto);
        }
        if (ObjectUtil.isNotEmpty(dto.getTodoTaskStatus())) {
            projProductBacklog.setTodoTaskStatus(dto.getTodoTaskStatus());
            saveOperLog(dto.getProjectInfoId(), productName + "==>待处理任务节点完成==>" + message, dto);
        }
        if (ObjectUtil.isNotEmpty(dto.getReportDataStatus())) {
            Map<String, Object> mao = new HashMap<>();
            mao.put("projectInfoId", dto.getProjectInfoId());
            mao.put("hospitalInfoId", dto.getHospitalInfoId());
            mao.put("yyProductId", dto.getYyProductId());
            // 查询项目下调研阶段报表是否使用的新流程
            List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(
                    new QueryWrapper<ProjMilestoneInfo>()
                            .eq("project_info_id", dto.getProjectInfoId())
                            .in("milestone_node_code", MilestoneNodeEnum.SURVEY_REPORT.getCode())
                            .eq("invalid_flag", NumberEnum.NO_0.num())
            );
            Boolean isNewFlow = milestoneInfoList != null && milestoneInfoList.size() > 0
                    && milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(
                    milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent());
            Integer countsd = 0;
            if (isNewFlow) {
                countsd = projSurveyReportService.getReportCount(dto.getProjectInfoId(), dto.getHospitalInfoId(), dto.getYyProductId(), null);
            } else {
                countsd = projMilestoneInfoMapper.getOldReportCount(mao);
            }
            if (countsd > 0) {
                return Result.fail("上线必备的报表未制作完成，无法提交完成");
            } else {
                ConfigCustomBackendLimit isOpenModel =
                        configCustomBackendLimitService.isOpenByParamer(new ConfigCustomBackendLimitReq(dto.getCustomInfoId(),
                                dto.getProjectInfoId(), 1));
                if (isOpenModel != null && isOpenModel.getOpenFlag() == 1) {
                    ProjSurveyReportReq projSurveyReportReq = new ProjSurveyReportReq();
                    projSurveyReportReq.setProjectInfoId(dto.getProjectInfoId());
                    projSurveyReportReq.setYyProductId(dto.getYyProductId());
                    projSurveyReportReq.setHospitalInfoId(dto.getHospitalInfoId());
                    projSurveyReportReq.setFinishStatus(1);
                    projSurveyReportReq.setOnlineEssential(1);
                    List<ProjSurveyReportResp> projSurveyReportResps = projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq);
                    if (projSurveyReportResps != null) {
                        for (ProjSurveyReportResp projSurveyReportResp : projSurveyReportResps) {
                            String finishRatio = projSurveyReportResp.getFinishRatio();
                            if (StringUtils.isNotBlank(finishRatio)) {
                                Integer finishRatioInt = Integer.parseInt(finishRatio.replace("%", ""));
                                if (finishRatioInt < isOpenModel.getLimitRatio()) {
                                    return Result.fail("上线必备的报表 【" + projSurveyReportResp.getReportName() + "】客户打印验证不足" + isOpenModel.getLimitRatio() + "%，请及时联系客户验证。若关闭客户验证，后端经理可在后端运维处关闭【医护验证打印】");
                                }
                            } else {
                                return Result.fail("上线必备的报表 【" + projSurveyReportResp.getReportName() + "】客户打印验证不足" + isOpenModel.getLimitRatio() + "%，请及时联系客户验证。若关闭客户验证，后端经理可在后端运维处关闭【医护验证打印】");
                            }
                        }
                    }

                }
                projProductBacklog.setReportDataStatus(dto.getReportDataStatus());
                ProjSurveyReport projSurveyReport = new ProjSurveyReport();
                projSurveyReport.setFinishStatus(1);
                projSurveyReport.setExamineOpinion("");
                projSurveyReport.setProjectInfoId(dto.getProjectInfoId());
                projSurveyReport.setYyProductId(dto.getYyProductId());
                projSurveyReport.setHospitalInfoId(dto.getHospitalInfoId());
                projSurveyReport.setCommitFinishTime(new Timestamp(System.currentTimeMillis()));
                projSurveyReportMapper.updateReprotStatusData(projSurveyReport);
                saveOperLog(dto.getProjectInfoId(), productName + "==>报表节点完成==> 报表确认完成", dto);
            }
        }
        if (ObjectUtil.isNotEmpty(dto.getFormDataStatus())) {
            Map<String, Object> mao = new HashMap<>();
            mao.put("projectInfoId", dto.getProjectInfoId());
            mao.put("hospitalInfoId", dto.getHospitalInfoId());
            mao.put("yyProductId", dto.getYyProductId());
            // 查询项目下调研阶段报表是否使用的新流程
            List<ProjMilestoneInfo> milestoneInfoFormList = projMilestoneInfoMapper.selectList(
                    new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", dto.getProjectInfoId())
                            .in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode())
                            .eq("invalid_flag", NumberEnum.NO_0.num()));
            Boolean isNewFlow = milestoneInfoFormList != null && milestoneInfoFormList.size() > 0
                    && milestoneInfoFormList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(
                    milestoneInfoFormList.get(NumberEnum.NO_0.num()).getIsComponent());
            Integer countsd = 0;
            if (isNewFlow) {
                countsd = projMilestoneInfoMapper.getFormCount(mao);
            }
            if (countsd > 0) {
                return Result.fail("上线必备的表单未制作完成，无法提交完成");
            } else {
                projProductBacklog.setFormDataStatus(dto.getFormDataStatus());
                ProjSurveyForm projSurveyForm = new ProjSurveyForm();
                projSurveyForm.setFinishStatus(1);
                projSurveyForm.setExamineOpinion("");
                projSurveyForm.setCommitFinishTime(new Timestamp(System.currentTimeMillis()));
                projSurveyForm.setProjectInfoId(dto.getProjectInfoId());
                projSurveyForm.setYyProductId(dto.getYyProductId());
                projSurveyForm.setHospitalInfoId(dto.getHospitalInfoId());
                projSurveyFormMapper.updateFormStatusData(projSurveyForm);
                saveOperLog(dto.getProjectInfoId(), productName + "==>表单节点完成==>表单确认完成", dto);
            }
        }
        QueryWrapper<ProjProductBacklog> wrapper = new QueryWrapper<>();
        wrapper.eq("project_info_id", dto.getProjectInfoId());
        if (ObjectUtil.isNotEmpty(modules)) {
            wrapper.eq("yy_product_module_id", dto.getYyProductId());
        } else {
            wrapper.eq("yy_product_id", dto.getYyProductId());
        }
        wrapper.eq("hospital_info_id", dto.getHospitalInfoId());
        productBacklogMapper.update(projProductBacklog, wrapper);
        // 判断当前产品的backlog数据是否全部已完成，当全部完成后，更新task_detail表中 产品总体完成状态
        ProjProductBacklog projProductBacklog1 = productBacklogMapper.selectOne(
                new QueryWrapper<ProjProductBacklog>().eq("project_info_id", dto.getProjectInfoId())
                        .eq(ObjectUtil.isNotEmpty(modules), "yy_product_module_id", dto.getYyProductId())
                        .eq(ObjectUtil.isEmpty(modules), "yy_product_id", dto.getYyProductId())
                        .eq("hospital_info_id", dto.getHospitalInfoId()));
        if (ObjectUtil.isNotEmpty(projProductBacklog1) && projProductBacklog1.getBaseDataStatus() != 1
                && projProductBacklog1.getConfigDataStatus() != 1 && projProductBacklog1.getTodoTaskStatus() != 1
                && projProductBacklog1.getReportDataStatus() != 1 && projProductBacklog1.getFormDataStatus() != 1) {
            // 查询具体的task 表数据
            ProjMilestoneTask milestoneTask = milestoneTaskMapper.selectOne(
                    new QueryWrapper<ProjMilestoneTask>().eq("project_info_id", dto.getProjectInfoId())
                            .eq("hospital_info_id", dto.getHospitalInfoId())
                            .eq("milestone_node_code", "preparat_product"));
            // 根据产品查询 milestone_task_detail 表数据
            ProjMilestoneTaskDetail milestoneTaskDetail = new LambdaQueryChainWrapper<>(milestoneTaskDetailMapper)
                    .eq(ProjMilestoneTaskDetail::getMilestoneTaskId, milestoneTask.getMilestoneTaskId())
                    .eq(ProjMilestoneTaskDetail::getProductDeliverId, dto.getYyProductId())
                    .eq(ProjMilestoneTaskDetail::getIsDeleted, 0)
                    .one();
            if (ObjectUtil.isNotEmpty(milestoneTaskDetail)) {
                ProjMilestoneTaskDetail taskDetail = new ProjMilestoneTaskDetail();
                taskDetail.setMilestoneTaskDetailId(milestoneTaskDetail.getMilestoneTaskDetailId());
                taskDetail.setCompleteStatus(1);
                milestoneTaskDetailMapper.updateById(taskDetail);
                saveOperLog(dto.getProjectInfoId(), productName + "==>产品总体状态完成2==>" + message, dto);
            } else {
                return Result.fail("产品信息错误");
            }
            // 查询当前项目下的 milestone_task 表数据 (当前项目下 可能存在多个卫生院数据，需要查询当前项目下的全部 task_id数据，再获取 task_detail表数据)
            List<ProjMilestoneTask> projMilestoneTasks = milestoneTaskMapper.selectList(
                    new QueryWrapper<ProjMilestoneTask>().eq("project_info_id", dto.getProjectInfoId()));
            List<Long> taskIds = projMilestoneTasks.stream().map(vo -> vo.getMilestoneTaskId())
                    .collect(Collectors.toList());
            // 查询task_detail 表数据，当前 task_id 下的明细任务全部完成后，更新里程碑节点状态
            List<ProjMilestoneTaskDetail> projMilestoneTaskDetails = milestoneTaskDetailMapper.selectList(
                    new QueryWrapper<ProjMilestoneTaskDetail>().in("milestone_task_id", taskIds)
                            .eq("complete_status", 0));
            if (ObjectUtil.isEmpty(projMilestoneTaskDetails)) {
                // 不存在没完成的数据后，更新里程碑状态
                ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(dto.getProjectInfoId(),
                        MilestoneNodeEnum.PREPARAT_PRODUCT.getCode());
                UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
                updateMilestoneDTO.setMilestoneStatus(1);
                updateMilestoneDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
                saveOperLog(dto.getProjectInfoId(), productName + "==>里程碑节点完成==>" + message, dto);
                milestoneInfoService.updateMilestone(updateMilestoneDTO);
            }
        }
        //更新产品准备待办状态及进度
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(dto.getProjectInfoId());
        param.setHospitalInfoId(dto.getHospitalInfoId());
        param.setYyProductId(dto.getYyProductId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
        todoTaskService.updateProjectTodoTaskStatus(param);
        return Result.success();
    }

    /**
     * 产品准备工作增加日志
     *
     * @param projectInfoId
     * @param updateMessage
     */
    void saveOperLog(Long projectInfoId, String updateMessage, ProjProductBacklogDTO dto) {
        // 增加操作日志【董博培。要求先加上】
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
        SysOperLog sysOperLog = new SysOperLog();
        sysOperLog.setSysOperLogId(SnowFlakeUtil.getId());
        sysOperLog.setOperName(updateMessage);
        sysOperLog.setOperDetail("操作参数信息===" + JSONUtil.toJsonStr(dto));
        sysOperLog.setOperKeyWord("product_prepare");
        sysOperLog.setCreaterName(userHelper.getCurrentUser().getUserName());
        sysOperLog.setOperType(5);
        sysOperLog.setOperModule("产品准备工作");
        sysOperLog.setOperNode("产品准备工作");
        sysOperLog.setProjectName(projProjectInfo.getProjectName());
        operLogMapper.insert(sysOperLog);
    }

    /**
     * 配置是否可导入健康配置
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ConfigImportCloudVO> configIsImportHealth(ProjProductBacklogDTO dto) {
        ConfigImportCloudVO configImportCloudVO = new ConfigImportCloudVO();
        // 查询配置表数据 ， 当status为已导入的时候， 禁止点击导入云健康按钮。当为未导入的时候， 允许点击导入云健康按钮。
        List<ProjProductConfig> productConfigs = productConfigMapper.selectList(
                new QueryWrapper<ProjProductConfig>().eq("project_info_id", dto.getProjectInfoId())
                        .eq("hospital_info_id", dto.getHospitalInfoId()).eq("yy_product_id", dto.getYyProductId())
                        .eq("config_status", 0));
        // 可点击
        // 不可点击
        configImportCloudVO.setCanDoImport(CollectionUtil.isNotEmpty(productConfigs));
        // 判断当前项目是否已经上线，当上线时 也不允许进行导入功能操作
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(dto.getProjectInfoId());
        configImportCloudVO.setIsOnline(
                ObjectUtil.isNotEmpty(projProjectInfo) && projProjectInfo.getProjectDeliverStatus() >= 5
                        && ObjectUtil.isNotEmpty(projProjectInfo.getOnlineTime()));
        return Result.success(configImportCloudVO);
    }

    @Override
    public Result<Integer> updateProductConfigStatus(ProjProductBacklogDTO dto) {
        ProjProductConfig projProductConfig = new ProjProductConfig();
        projProductConfig.setConfigStatus(0);
        int update = productConfigMapper.update(projProductConfig,
                new UpdateWrapper<ProjProductConfig>().eq("project_info_id", dto.getProjectInfoId())
                        .eq("hospital_info_id", dto.getHospitalInfoId()).eq("yy_product_id", dto.getYyProductId()));
        return Result.success(update);
    }

    /**
     * 修改产品准备节点里程碑完成状态
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateProductBacklogMilestoneInfo(ProjProductBacklogDTO dto) {
        // 检测产品准备节点的产品数据是否已经全部完成(同一个项目，不同的医院会生成多条数据)
        List<ProjMilestoneTask> projMilestoneTasks = new LambdaQueryChainWrapper<>(milestoneTaskMapper)
                .eq(ProjMilestoneTask::getProjectInfoId, dto.getProjectInfoId())
                .eq(ProjMilestoneTask::getMilestoneNodeCode, MilestoneNodeEnum.PREPARAT_PRODUCT.getCode())
                .eq(ProjMilestoneTask::getIsDeleted, 0)
                .list();
        if (CollectionUtils.isEmpty(projMilestoneTasks)) {
            //无待处理任务，直接修改节点返回成功
            this.updateMilestoneInfo(dto.getMilestoneInfoId());
            return Result.success();
        }
        List<Long> milestoneTaskIds = projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId)
                .collect(Collectors.toList());
        // 查询task_detail 表数据。当所有的产品都是已完成时 进行更新里程碑。否则返回错误

        List<ProjMilestoneTaskDetail> projMilestoneTaskDetails = new LambdaQueryChainWrapper<>(milestoneTaskDetailMapper)
                .in(ProjMilestoneTaskDetail::getMilestoneTaskId, milestoneTaskIds)
                .eq(ProjMilestoneTaskDetail::getCompleteStatus, 0)
                .eq(ProjMilestoneTaskDetail::getIsDeleted, 0)
                .list();
        if (CollectionUtil.isEmpty(projMilestoneTaskDetails)) {
            this.updateMilestoneInfo(dto.getMilestoneInfoId());
        } else {
            return Result.fail("产品准备节点产品数据未全部完成");
        }
        return Result.success();
    }

    private void updateMilestoneInfo(Long milestoneInfoId) {
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(milestoneInfoId);
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setActualCompTime(new Date());
        updateMilestoneDTO.setNodeHeadId(userHelper.getCurrentUser().getSysUserId());
        milestoneInfoService.updateMilestone(updateMilestoneDTO);
    }

    @Override
    public Result<OneClickDetectionVO> oneClickDetection(ProjProductBacklogOneClickDetectDTO oneClickDTO) {
        OneClickDetectionVO oneClickDetectionVO = new OneClickDetectionVO();
        List<OneCheckVo> failDataList = new ArrayList<>();
        // 对每个医院进行查询
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(oneClickDTO.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollUtil.isEmpty(hospitalInfos)) {
            return Result.fail("未查询到医院信息.");
        }
        List<String> results = CollUtil.newArrayList();
        // 轮询医院汇总统计检测情况
        StringBuilder finalBuilder = new StringBuilder();
        // 所有医院完成情况描述
        finalBuilder.append("本次检测").append(hospitalInfos.size()).append("家医院").append(StrUtil.COMMA)
                .append(StrUtil.SPACE);
        // 记录所有医院配置
        AtomicInteger allCompleteCountFin = new AtomicInteger();
        List<StringBuilder> hospitalBuilders = CollUtil.newArrayList();
        for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
            StringBuilder hospitalBuilder = new StringBuilder();
            // 汇总医院未完成
            hospitalBuilder.append(hospitalInfo.getHospitalName()).append(StrUtil.COMMA).append(StrUtil.SPACE);
            List<ProjProductBacklogVO> backlogVOS = findProjProductBacklogWithDelectResultVO(oneClickDTO,
                    hospitalInfo.getHospitalInfoId());
            // 产品个数
            int productCount = backlogVOS.size();
            // 配置完成数量, 基础数据完成数量, 代办任务完成数量, 报表完成数量, 表单完成数量
            AtomicInteger completeCountFin = new AtomicInteger();
            if (CollUtil.isNotEmpty(backlogVOS)) {
                // 轮询进一键检测
                for (ProjProductBacklogVO backlogVO : backlogVOS) {
                    // 没有分配责任人且是未完成状态则进行检测
                    if (ObjectUtil.isEmpty(backlogVO.getUserId())) {
                        continue;
                    }
                    if (backlogVO.getTodoTaskStatus() == NumberEnum.NO_0.num().intValue()) {
                        continue;
                    }
                    ProjProductTaskParam taskParam = new ProjProductTaskParam();
                    taskParam.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                    taskParam.setYyProductId(backlogVO.getYyProductModuleId() == -1 ? backlogVO.getYyProductId()
                            : backlogVO.getYyProductModuleId());
                    taskParam.setProjectInfoId(oneClickDTO.getProjectInfoId());
                    try {
                        Result<OneCheckResultVO> oneCheckResultVOResult = productTaskService.oneCheck(taskParam,
                                "外层一键检测");
                        log.info("一键检测. 请求参数: {}, 检测结果: {}", taskParam,
                                ObjectUtil.isNotEmpty(oneCheckResultVOResult)
                                        ? oneCheckResultVOResult.getData() : StrUtil.EMPTY);
                        if (oneCheckResultVOResult.isSuccess() && CollectionUtil.isNotEmpty(
                                oneCheckResultVOResult.getData().getFailDataList())) {
                            for (OneCheckVo v : oneCheckResultVOResult.getData().getFailDataList()) {
                                failDataList.add(v);
                            }
                        }
                    } catch (Exception e) {
                        log.error("一键检测异常: {}", e);
                        return Result.fail(e.getMessage());
                    }
                }
                backlogVOS = findProjProductBacklogWithDelectResultVO(oneClickDTO,
                        hospitalInfo.getHospitalInfoId());
                // 计算产品完成项
                backlogVOS.forEach(e -> {
                    addHospitalCountFin(completeCountFin, e, oneClickDTO.getProjectInfoId(),
                            hospitalInfo.getHospitalInfoId());
                });
                // 拼接产品计算结果
                appendHospitalBuilder(hospitalBuilder, TODOWORK_PRODUCT_ITEMNAME, completeCountFin.get(), productCount);
                hospitalBuilders.add(hospitalBuilder);
                // 更新所有医院个项完成的记录, 若所有产品此项记录已完成, 则加一
                updateAllCountFin(productCount, completeCountFin, allCompleteCountFin);
            }
        }
        // 拼接医院完成结果
        appendAllHospitalBuilder(finalBuilder, allCompleteCountFin.get(), hospitalInfos.size(), TODOWORK_ITEMNAME);
        results.add(finalBuilder.toString());
        if (CollUtil.isNotEmpty(hospitalBuilders)) {
            hospitalBuilders.forEach(e -> {
                results.add(e.toString());
            });
        }
        oneClickDetectionVO.setCheckList(results);
        oneClickDetectionVO.setFailDataList(failDataList);
        return Result.success(oneClickDetectionVO);
    }

    /**
     * 查询产品检测结果集合
     *
     * @param oneClickDTO    请求参数
     * @param hospitalInfoId 医院id
     * @return List<ProjProductBacklogVO>
     */
    private List<ProjProductBacklogVO> findProjProductBacklogWithDelectResultVO(
            ProjProductBacklogOneClickDetectDTO oneClickDTO, Long hospitalInfoId) {
        ProjProductBacklogDTO backlogDTO = BeanUtil.copyProperties(oneClickDTO, ProjProductBacklogDTO.class);
        backlogDTO.setHospitalInfoId(hospitalInfoId);
        ProjProductBacklogDataVO dataVO = selectProductBacklog(backlogDTO);
        return dataVO.getBacklogList();
    }

    /**
     * 添加产品项完成记录
     *
     * @param dataCountFin   数据项记录
     * @param backlogVO      记录
     * @param prjectInfoId   项目id
     * @param hospitalInfoId 医院id
     */
    public void addHospitalCountFin(AtomicInteger dataCountFin, ProjProductBacklogVO backlogVO, Long prjectInfoId,
                                    Long hospitalInfoId) {
        if (backlogVO.getCompleteStatus() == NumberEnum.NO_1.num().intValue()) {
            dataCountFin.getAndIncrement();
        } else {
            if (backlogVO.getConfigDataStatus() != NumberEnum.NO_1.num().intValue()
                    && backlogVO.getBaseDataStatus() != NumberEnum.NO_1.num().intValue()
                    && backlogVO.getTodoTaskStatus() != NumberEnum.NO_1.num().intValue()
                    && backlogVO.getReportDataStatus() != NumberEnum.NO_1.num().intValue()
                    && backlogVO.getFormDataStatus() != NumberEnum.NO_1.num().intValue()) {
                ProjMilestoneTask milestoneTask = milestoneTaskMapper.selectOne(
                        new QueryWrapper<ProjMilestoneTask>().eq("project_info_id", prjectInfoId)
                                .eq("hospital_info_id", hospitalInfoId)
                                .eq("milestone_node_code", MilestoneNodeEnum.PREPARAT_PRODUCT.getCode()));
                // 若存在moduleId, 即不等于-1的情况, 则用做产品id
                Long tmpYyProductId = backlogVO.getYyProductId();
                if (backlogVO.getYyProductModuleId() != -1) {
                    tmpYyProductId = backlogVO.getYyProductModuleId();
                }
                // 更新产品准备工作产品完成状态
                ProjMilestoneTaskDetail milestoneTaskDetail = new ProjMilestoneTaskDetail();
                milestoneTaskDetail.setCompleteStatus(NumberEnum.NO_1.num());
                int count = milestoneTaskDetailMapper.update(milestoneTaskDetail,
                        new QueryWrapper<ProjMilestoneTaskDetail>().eq("milestone_task_id",
                                milestoneTask.getMilestoneTaskId()).eq("product_deliver_id", tmpYyProductId));
                log.info("更新产品完成状态. count: {}", count);
                dataCountFin.getAndIncrement();
            }
        }
    }

    @Override
    public Result<List<String>> oneClickImportCloudHealth(ProjProductBacklogOneClickImportDTO importDTO) {
        // 查询医院
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(importDTO.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfos = hospitalInfoService.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollUtil.isEmpty(hospitalInfos)) {
            return Result.fail("未查询到医院.");
        }
        List<String> resultMsg = CollUtil.newArrayList();
        // 轮询导入云健康配置
        for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
            StringBuilder importRecord = new StringBuilder();
            // 查询产品配置
            List<ProjProductBacklog> productBacklogs = productBacklogMapper.selectList(
                    new QueryWrapper<ProjProductBacklog>()
                            .eq("project_info_id", importDTO.getProjectInfoId())
                            .eq("hospital_info_id", hospitalInfo.getHospitalInfoId())
                            .ne("config_data_status", NumberEnum.NO_0.num()));
            if (CollUtil.isEmpty(productBacklogs)) {
                importRecord.append(hospitalInfo.getHospitalName()).append("无产品信息;");
            }
            for (ProjProductBacklog productBacklog : productBacklogs) {
                ProjProductBacklogDTO backlogDTO = new ProjProductBacklogDTO();
                backlogDTO.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                if (productBacklog.getYyProductModuleId() != -1) {
                    backlogDTO.setYyProductId(productBacklog.getYyProductModuleId());
                } else {
                    backlogDTO.setYyProductId(productBacklog.getYyProductId());
                }
                backlogDTO.setProjectInfoId(importDTO.getProjectInfoId());
                try {
                    Result result = importHealthConfig(backlogDTO);
                    if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
                        if (!importRecord.toString().contains(productBacklog.getProductName())
                                && !importRecord.toString().contains(hospitalInfo.getHospitalName())) {
                            importRecord.append(hospitalInfo.getHospitalName() + StrUtil.COLON
                                    + productBacklog.getProductName()).append("导入失败;");
                        }
                    }
                } catch (Throwable e) {
                    log.error("导入云健康配置失败. message: {}, e=", e.getMessage(), e);
                    importRecord.append(hospitalInfo.getHospitalName() + StrUtil.COLON
                            + productBacklog.getProductName()).append("导入失败;");
                }
            }
            if (StrUtil.isNotBlank(importRecord)) {
                resultMsg.add(importRecord.toString());
            }
        }
        if (CollUtil.isNotEmpty(resultMsg)) {
            Result<List<String>> result = Result.fail(resultMsg);
            result.setMsg("一键导入云健康失败.");
            return result;
        }
        return Result.success();
    }

    /**
     * 产品准备节点左侧医院列表信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<SurveyPlanInitVO> selectProductPrepareHospital(ProjProductBacklogDTO dto) {
        // 实体返回的数据和调研节点一致，要求快速开发  没创建新的VO
        SurveyPlanInitVO surveyPlanInitVO = new SurveyPlanInitVO();
        List<SurveyPlanHospitalProductVO> productPrepareHospitalProductList = new ArrayList<>();
        // 查询项目下的医院信息
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        selectHospitalDTO.setHospitalName(dto.getHospitalName());
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
            SurveyPlanHospitalProductVO surveyPlanHospitalProductVO = new SurveyPlanHospitalProductVO();
            // 根据项目、医院id查询产品准备节点的产品数量与已完成数量
            List<ProjProductBacklog> projProductBacklogs = productBacklogMapper.selectList(
                    new QueryWrapper<ProjProductBacklog>().eq("project_info_id", dto.getProjectInfoId())
                            .eq("hospital_info_id", hospitalInfo.getHospitalInfoId()));
            // 判断共多少个产品。按照产品名称进行统计
            Map<String, Long> productMap = projProductBacklogs.stream()
                    .collect(Collectors.groupingBy(ProjProductBacklog::getProductName, Collectors.counting()));
            // 只会存在一个task 。 多个的话 就是数据问题
            List<ProjMilestoneTask> projMilestoneTasks = milestoneTaskMapper.selectList(
                    new QueryWrapper<ProjMilestoneTask>().eq("project_info_id", dto.getProjectInfoId())
                            .eq("hospital_info_id", hospitalInfo.getHospitalInfoId())
                            .eq("milestone_node_code", MilestoneNodeEnum.PREPARAT_PRODUCT.getCode()));
            List<ProjMilestoneTaskDetail> projMilestoneTaskDetails = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(projMilestoneTasks)) {
                // 查询该医院下的产品有哪些完成了。记录数量
                projMilestoneTaskDetails = milestoneTaskDetailMapper.selectList(
                        new QueryWrapper<ProjMilestoneTaskDetail>().eq("milestone_task_id",
                                projMilestoneTasks.get(0).getMilestoneTaskId()).eq("complete_status", 1));
            }
            surveyPlanHospitalProductVO.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
            surveyPlanHospitalProductVO.setHospitalName(hospitalInfo.getHospitalName());
            surveyPlanHospitalProductVO.setProductNum(Convert.toLong(productMap.size()));
            surveyPlanHospitalProductVO.setCompleteProductNum(Convert.toLong(projMilestoneTaskDetails.size()));
            surveyPlanHospitalProductVO.setIsOnlineFlag(false);
            productPrepareHospitalProductList.add(surveyPlanHospitalProductVO);
        }
        // 是否可点击完成
        Result<ProjMilestoneInfo> result = milestoneInfoService.selectById(dto.getMilestoneInfoId());
        if (result.isSuccess() && result.getData() != null) {
            surveyPlanInitVO.setConfirmComplete(result.getData().getMilestoneStatus() == 0);
        }
        boolean isBranchOnlineFlag = projSurveyPlanService.isBranchOnlineFlag(dto.getProjectInfoId());
        // 分院模式下，上线的医院不可进行调研分配
        if (isBranchOnlineFlag) {
            projSurveyPlanService.setHospitalProductVOList(surveyPlanInitVO, productPrepareHospitalProductList, dto.getProjectInfoId());
        } else {
            surveyPlanInitVO.setDefaultHospitalInfoId(productPrepareHospitalProductList.get(0).getHospitalInfoId());
        }
        surveyPlanInitVO.setHospitalProductVOList(productPrepareHospitalProductList);
        return Result.success(surveyPlanInitVO);
    }

    /**
     * 处理基础数据链接
     *
     * @param vo
     * @param dto
     * @return
     */
    @Override
    public void infoDataBaseUrl(ConfigProductJobMenuDetailVO vo, ProjProductBacklogDTO dto) {
        // 基础数据为 imsp系统页面，需要转换为老系统参数
        // 项目id
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(
                new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", dto.getProjectInfoId())
                        .eq("new_custom_info_id", dto.getCustomInfoId()));
        Long projectId = tmpProjectNewVsOld.getOldProjectInfoId();
        // 客户id
        Long customerId = tmpProjectNewVsOld.getOldCustomId();
        // 人员id
        ImspSysUser imspSysUser = imspSysUserMapper.selectOne(new QueryWrapper<ImspSysUser>()
                .eq("user_yunying_id", Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()))
                .eq("status", "ENABLE")
        );
        Long userId = imspSysUser.getUserId();
        // 人员名称
        String userName = imspSysUser.getName();
        // 云健康医院id
        ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(dto.getHospitalInfoId());
        Long cloudHospitalId = hospitalInfo.getCloudHospitalId();
        // 云健康医院名称
        String hospitalName = hospitalInfo.getHospitalName();
        // 交付平台医院id
        OldCustomerInfo oldCustomerInfo = new OldCustomerInfo();
        oldCustomerInfo.setProjectId(Convert.toInt(projectId));
        oldCustomerInfo.setCsmHospitalInfoId(hospitalInfo.getHospitalInfoId());
        log.info("selectCustomerInfoByCsm，oldCustomerInfo={}", JSON.toJSONString(oldCustomerInfo));
        Result<OldCustomerInfo> oldCustomerInfoResult = oldCustomerInfoService.selectCustomerInfoByCsm(oldCustomerInfo);
        log.info("selectCustomerInfoByCsm，oldCustomerInfoResult={}", JSON.toJSONString(oldCustomerInfoResult));
        if (oldCustomerInfoResult.getData() == null) {
            // 【10.22 董博培】：老系统中查询不到医院信息时，补录医院信息
            // 根据新系统医院信息生成老系统医院数据
            OldCustomerInfo oldCustomerInfo1 = saveCustomerInfo(hospitalInfo, projectId, customerId);
            oldCustomerInfoResult.setData(oldCustomerInfo1);
//            throw new IllegalArgumentException(
//                    String.format("没有在老系统中获取到客户信息，projectId=%s，csmHospitalInfoId=%s", projectId,
//                            hospitalInfo.getHospitalInfoId()));
        }
        Long imspCustomerInfoId = oldCustomerInfoResult.getData().getId();
        // 云健康医院地址
        String cloudDomain = hospitalInfo.getCloudDomain();
        // 产品id（交付平台老系统中产品id）
        OldProduct oldProduct = oldProductMapper.selectOne(
                new QueryWrapper<OldProduct>().eq("product_yunying_id", dto.getYyProductId()).eq("del_flag", 0));
        if (ObjectUtil.isNotEmpty(vo.getMenuUrl())) {
            if (vo.getProductJobMenuId() == 6 || vo.getProductJobMenuId() == 7) {
                // 报表、表单调研
                String url = vo.getMenuUrl();
                url = url.replace("#{projectId}", projectId.toString());
                url = url.replace("#{customerId}", customerId.toString());
                url = url.replace("#{productId}", oldProduct.getId().toString());
                url = url.replace("#{detailId}", "-1");
                vo.setMenuUrl(url);
            } else if (vo.getProductJobMenuId() == 15) {
                // 小硬件调研
                String url = vo.getMenuUrl();
                url = url.replace("#{projectId}", projectId.toString());
                url = url.replace("#{customerId}", customerId.toString());
                url = url.replace("#{productId}", oldProduct.getId().toString());
                url = url.replace("#{productSurvey}", "1");
                vo.setMenuUrl(url);
            } else if (vo.getProductJobMenuId() == 2 && !vo.getMenuUrl().contains("/")) {
                vo.setMenuUrl(vo.getMenuUrl());
            } else {
                if (vo.getMenuUrl().contains("?")) {
                    String urlStr = vo.getMenuUrl() + "&projectId=" + projectId + "&customerId=" + customerId
                            + "&userId" + "=" + userId + "&userName=" + userName + "&hospitalId=" + cloudHospitalId
                            + "&hospitalName=" + hospitalName + "&customerInfoId=" + imspCustomerInfoId + "&productId="
                            + oldProduct.getId() + "&preProductNetwork=" + cloudDomain;
                    vo.setMenuUrl(urlStr);
                } else {
                    String urlStr = vo.getMenuUrl() + "?projectId=" + projectId + "&customerId=" + customerId
                            + "&userId=" + userId + "&userName=" + userName + "&hospitalId=" + cloudHospitalId
                            + "&hospitalName=" + hospitalName + "&customerInfoId=" + imspCustomerInfoId + "&productId="
                            + oldProduct.getId() + "&preProductNetwork=" + cloudDomain;
                    vo.setMenuUrl(urlStr);
                }
            }

        }
    }

    /**
     * 保存医院信息
     *
     * @param hospitalInfo
     * @param projectId
     * @param customerId
     * @return
     */
    OldCustomerInfo saveCustomerInfo(ProjHospitalInfo hospitalInfo, Long projectId, Long customerId) {
        // 查询当前医院类型
        List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectList(
                new QueryWrapper<ProjHospitalVsProjectType>()
                        .eq("hospital_info_id", hospitalInfo.getHospitalInfoId())
        );
        OldCustomerInfo customerInfo = new OldCustomerInfo();
        customerInfo.setId(SnowFlakeUtil.getId());
        customerInfo.setCsmHospitalInfoId(hospitalInfo.getHospitalInfoId());
        customerInfo.setCustomerName(hospitalInfo.getHospitalName());
        customerInfo.setHospitalType(Convert.toStr(hospitalVsProjectTypes.get(0).getProjectType()));
        customerInfo.setHospitalStatus(0);
        customerInfo.setProjectId(Convert.toInt(projectId));
        customerInfo.setCustomerId(customerId);
        // 省市县
        customerInfo.setProvinceId(Convert.toInt(hospitalInfo.getProvinceId()));
        customerInfo.setCityId(Convert.toInt(hospitalInfo.getCityId()));
        customerInfo.setTownId(Convert.toInt(hospitalInfo.getTownId()));
        // 人口数
        customerInfo.setPeopleCount(hospitalInfo.getPopulation());
        // 终端数
        customerInfo.setTerminalAmount(hospitalInfo.getTerminalCount());
        // 村医数量
        customerInfo.setVillageDoctorAmount(hospitalInfo.getVillageDoctorsCount());
        // 医院床位数
        customerInfo.setBedCount(hospitalInfo.getHospitalBedCount());
        customerInfo.setRemark("产品业务调研：查询不到医院 ， 重新补录");
        // 云健康医院信息
        customerInfo.setHospitalId(hospitalInfo.getCloudHospitalId());
        customerInfo.setOrgId(hospitalInfo.getOrgId());
        customerInfo.setProductNetwork(hospitalInfo.getCloudDomain());
        customerInfo.setPreProductNetwork(hospitalInfo.getCloudDomain());
        customerInfo.setCreateTime(new Date());
        customerInfo.setUpdateTime(new Date());
        customerInfo.setHost(ObjectUtil.isNotEmpty(hospitalInfo.getCloudDomain())
                ? hospitalInfo.getCloudDomain().replace("https://", "") : null);
        customerInfo.setPreHost(ObjectUtil.isNotEmpty(hospitalInfo.getCloudDomain())
                ? hospitalInfo.getCloudDomain().replace("https://", "") : null);
        customerInfo.setEnvId(hospitalInfo.getEnvId());
        customerInfo.setEnvName(hospitalInfo.getEnvName());
        oldCustomerInfoService.save(customerInfo);
        log.info("没有在老系统中获取到客户信息，新系统医院信息 , {} ,补录医院信息 , {}",
                JSONUtil.toJsonStr(customerInfo),
                JSONUtil.toJsonStr(hospitalInfo));
        return customerInfo;

    }

    /**
     * 系统管理配置数据导入
     *
     * @param configList
     * @return
     */
    private Result systemConfigImport(List<ProjProductConfig> configList) {
        try {
            SystemConfigDto<SurveyValueAndSurveyContent> param = new SystemConfigDto<>();
            //基础参数
            // 查询医院信息
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(configList.get(0).getHospitalInfoId());
            param.setHospitalId(hospitalInfo.getCloudHospitalId());
            param.setOrgId(hospitalInfo.getOrgId());
            param.setHisOrgId(hospitalInfo.getOrgId());
            UpdateWrapper<ProjProductConfig> oldDataWrapper = new UpdateWrapper<>();
            oldDataWrapper.eq("project_info_id", configList.get(0).getProjectInfoId());
            oldDataWrapper.eq("hospital_info_id", configList.get(0).getHospitalInfoId());
            oldDataWrapper.eq("yy_product_id", configList.get(0).getYyProductId());
            // 配置参数组装
            List<SurveyValueAndSurveyContent> valueAndContentList = new ArrayList<>();
            for (ProjProductConfig config : configList) {
                SurveyValueAndSurveyContent surveyValueAndSurveyContent = new SurveyValueAndSurveyContent();
                surveyValueAndSurveyContent.setConfigNumber(config.getConfigCode());
                surveyValueAndSurveyContent.setTitle(config.getConfigName());
                surveyValueAndSurveyContent.setConfigType("string");
                surveyValueAndSurveyContent.setItemValue(config.getConfigValue());
                valueAndContentList.add(surveyValueAndSurveyContent);
                // 增加日志信息 ====  每次新增、修改、导入全部都要添加日志 每条数据一个日志。看不懂,弄这么麻烦。反正怎么都能写，这个设计  无敌
                // 2024-08-16 （说的不错，以后别说了） 需求变更： 要求数据发生变动后，记录原值及变动后的值。
                // 原话： 我不管触发时机， 我就要结果既有原值，又有新值。 优秀。
                updateProductConfigLog(config);
            }
            param.setData(valueAndContentList);
            //API 调用，执行刷新 domain
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            log.info("系统管理配置数据导入===参数信息:{}", JSONUtil.toJsonStr(param));
            ResponseResult<SystemSettingResultDto> result = systemSettingApi.sendHisSystemConfig(param);
            log.info("系统管理配置数据导入===返回结果:{}", result);
            if (result.getSuccess()) {
                //成功后更新数据库配置
                UpdateWrapper<ProjProductConfig> objectUpdateWrapper = new UpdateWrapper<>();
                objectUpdateWrapper.eq("project_info_id", configList.get(0).getProjectInfoId());
                objectUpdateWrapper.eq("hospital_info_id", configList.get(0).getHospitalInfoId());
                objectUpdateWrapper.eq("yy_product_id", configList.get(0).getYyProductId());
                objectUpdateWrapper.eq("config_type", "sys");
                ProjProductConfig projProductConfig = new ProjProductConfig();
                projProductConfig.setConfigStatus(1);
                productConfigMapper.update(projProductConfig, objectUpdateWrapper);
                log.info("系统管理配置导入成功");
                return Result.success();
            } else {
                return Result.fail("系统管理配置导入失败 , " + result.getMessage());
            }

        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("系统管理配置导入失败 , " + e.getMessage());
        }

    }

    /**
     * 配置导入成功后，更新最新一条的日志为已导入
     */
    void updateProductConfigLog(ProjProductConfig config) {
        log.info("配置导入成功后 修改日志");
        // 查询最新的一条修改日志
        ProjProductConfigLog productConfigLog = productConfigLogMapper.selectOne(
                new QueryWrapper<ProjProductConfigLog>().eq("project_info_id", config.getProjectInfoId())
                        .eq("hospital_info_id", config.getHospitalInfoId()).eq("yy_product_id", config.getYyProductId())
                        .eq("config_code", config.getConfigCode()).orderByDesc("update_time").last("limit 1"));
        ProjProductConfigLog log = new ProjProductConfigLog();
        log.setConfigStatus(1);
        log.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        log.setUpdateTime(new Date());
        log.setProductConfigLogId(productConfigLog.getProductConfigLogId());
        productConfigLogMapper.updateById(log);
    }

    /**
     * 根据参数查询产品待办事项
     *
     * @param dto 查询参数封装对象，包含查询产品待办事项的各种条件
     * @return 返回查询到的产品待办事项对象，如果没有查询到，则返回null
     */
    @Override
    public ProjProductBacklog selectByParam(ProjProductBacklogDTO dto) {
        return productBacklogMapper.selectByParam(dto);
    }

    /**
     * 确认开始，设置当前时间为开始时间
     *
     * @param dto 包含必要参数的DTO对象
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result confirmStart(ProjProductBacklogDTO dto) {
        if (dto.getMilestoneTaskDetailId() == null) {
            return Result.fail("任务明细ID不能为空");
        }
        // 先获取菜单如果正确返回再修改开始时间否则提示具体错误（为了防止已经确认开始计时但是获取菜单失败无法实施）
        try {
            // 组装selectBacklogUrlList所需的DTO
            ProjProductBacklogDTO menuDto = new ProjProductBacklogDTO();
            // 查询任务明细，获取项目ID等信息
            ProjMilestoneTaskDetail taskDetailInfo = milestoneTaskDetailMapper.selectById(dto.getMilestoneTaskDetailId());
            // 查询里程碑任务，获取项目ID
            ProjMilestoneTask projMilestoneTask = milestoneTaskMapper.selectById(taskDetailInfo.getMilestoneTaskId());
            menuDto.setCustomInfoId(projMilestoneTask.getCustomerInfoId());
            menuDto.setHospitalInfoId(projMilestoneTask.getHospitalInfoId());
            menuDto.setProjectInfoId(projMilestoneTask.getProjectInfoId());
            menuDto.setUserId(taskDetailInfo.getLeaderId());
            menuDto.setYyProductId(taskDetailInfo.getProductDeliverId());
            // 调用selectBacklogUrlList方法
            productBacklogService.selectBacklogUrlList(menuDto);
        } catch (Exception e) {
            return Result.fail("确认开始失败" + e.getMessage());
        }
        // 获取当前时间
        Date now = new Date();
        // 更新里程碑任务明细表的开始时间
        ProjMilestoneTaskDetail taskDetail = new ProjMilestoneTaskDetail();
        // 获取任务明细ID
        taskDetail.setMilestoneTaskDetailId(dto.getMilestoneTaskDetailId());
        taskDetail.setStartTime(now); // 设置开始时间为当前时间
        taskDetail.setUpdateTime(now); // 设置更新时间为当前时间
        taskDetail.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        int rows = milestoneTaskDetailMapper.updateById(taskDetail);
        if (rows > 0) {
            // 查询任务明细，获取项目ID等信息
            ProjMilestoneTaskDetail detailInfo = milestoneTaskDetailMapper.selectById(dto.getMilestoneTaskDetailId());
            if (detailInfo != null && detailInfo.getMilestoneTaskId() != null) {
                // 通过里程碑任务ID获取项目ID
                ProjMilestoneTask milestoneTask = milestoneTaskMapper.selectById(detailInfo.getMilestoneTaskId());
                if (milestoneTask != null && milestoneTask.getProjectInfoId() != null) {
                    // 添加操作日志
                    saveOperLog(milestoneTask.getProjectInfoId(), "确认开始，设置当前时间为开始时间", dto);
                }
            }
            return Result.success(null, "确认开始成功");
        } else {
            return Result.fail("确认开始失败");
        }
    }

    /**
     * 根据项目id查询当前客户下该项目类型上线的产品 **下拉使用**
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result selectYyProductAndModuleByCustomInfoId(Long projectInfoId) {
        List<BaseIdNameResp> yyProductVOS2 = new ArrayList<>();
        List<ProjProjectInfo> projectInfoIdList = projProjectInfoMapper.selectByProjectLists(projectInfoId);
        if (projectInfoIdList == null || projectInfoIdList.isEmpty()) {
            return Result.success(yyProductVOS2);
        }
        List<Long> projectInfoIds = projectInfoIdList.stream().map(e -> e.getProjectInfoId()).collect(Collectors.toList());
        if (projectInfoIds.isEmpty()) {
            return Result.success(yyProductVOS2);
        }
        List<ProjProductDeliverRecord> deliverRecordList = new ArrayList<>();
        for (Long projectInfoIdSelect : projectInfoIds) {
            List<ProjProductDeliverRecord> deliverRecordListIn = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(projectInfoIdSelect);
            deliverRecordList.addAll(deliverRecordListIn);
        }
        if (deliverRecordList.isEmpty()) {
            return Result.success(yyProductVOS2);
        }
        Set<Long> productIds = deliverRecordList.stream().map(e -> e.getProductDeliverId()).collect(Collectors.toSet());
        List<BaseIdNameResp> productInfoList = productMapper.findByProductIds(productIds);

        for (BaseIdNameResp productInfo : productInfoList) {
            BaseIdNameResp yyProductVO = new BaseIdNameResp();
            yyProductVO.setId(productInfo.getId());
            yyProductVO.setName(productInfo.getName().endsWith(StrUtil.DASHED) ? productInfo.getName()
                    .substring(0, productInfo.getName().length() - 1) : productInfo.getName());
            yyProductVOS2.add(yyProductVO);
        }
        return Result.success(yyProductVOS2);
    }
}
