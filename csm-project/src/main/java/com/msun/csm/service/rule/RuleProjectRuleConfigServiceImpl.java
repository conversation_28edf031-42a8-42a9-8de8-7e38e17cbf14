package com.msun.csm.service.rule;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.model.param.GetProjectFileRuleParam;
import com.msun.csm.model.param.ProjectCommonFileVsRuleParam;
import com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO;
import com.msun.csm.model.vo.ProjProjectFileVO;
import com.msun.csm.model.vo.ProjectFileUploadRuleVO;
import com.msun.csm.service.proj.ProjectCommonFileVsRuleService;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 */
@Service
public class RuleProjectRuleConfigServiceImpl implements RuleProjectRuleConfigService {

    @Resource
    private RuleProjectRuleConfigMapper ruleProjectRuleConfigMapper;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjectCommonFileVsRuleService projectCommonFileVsRuleService;

    @Override
    public List<ProjProjectAcceptanceRuleVO> getTemplateByCode(ProjProjectInfo projectInfo) {
        return ruleProjectRuleConfigMapper.getTemplateByCode(projectInfo);
    }

    @Override
    public List<ProjectFileUploadRuleVO> getProjectFileRule(GetProjectFileRuleParam params) {
        List<ProjectFileUploadRuleVO> projectFileRule = ruleProjectRuleConfigMapper.getProjectFileRule(params);
        if (CollectionUtils.isEmpty(projectFileRule)) {
            return new ArrayList<>();
        }
        projectFileRule.forEach(item -> {
            if (StringUtils.isNotBlank(item.getLimitType())) {
                List<String> fileLimitType = Arrays.asList(item.getLimitType().split(","));
                fileLimitType = fileLimitType.stream().filter(StringUtils::isNotBlank)
                        .map(limitType -> {
                                    String trim = limitType.trim();
                                    if (trim.startsWith(".")) {
                                        return trim.replace(".", "");
                                    }
                                    return trim;
                                }
                        ).collect(Collectors.toList());
                item.setFileLimitType(fileLimitType);
            }

            ProjectCommonFileVsRuleParam selectParam = new ProjectCommonFileVsRuleParam();
            selectParam.setSceneCode(item.getSceneCode());
            selectParam.setProjectRuleCode(item.getProjectRuleCode());
            selectParam.setNodeCode(item.getNodeCode());
            selectParam.setClassCode(item.getClassCode());
            selectParam.setItemCode(item.getItemCode());
            selectParam.setProjectInfoId(params.getProjectInfoId());
            // 当前节点已上传的文件
            ProjectCommonFileVsRule existData = projectCommonFileVsRuleService.selectByParam(selectParam);
            if (existData == null) {
                item.setFileList(new ArrayList<>());
            } else {
                item.setCheckResult(existData.getCheckResult());
                item.setCheckResultText(existData.getCheckResultText());
                item.setUseFlag(existData.getUseFlag());
                item.setProjectCommonFileId(existData.getProjectCommonFileId());
                if (StringUtils.isBlank(existData.getProjectFileIds())) {
                    item.setFileList(new ArrayList<>());
                } else {
                    String projectFileIds = existData.getProjectFileIds();
                    List<Long> fileIdList = Arrays.stream(projectFileIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
                    List<ProjProjectFile> projProjectFiles = projProjectFileMapper.selectProjectFileByIds(fileIdList);
                    List<ProjProjectFileVO> fileList = projProjectFiles.stream().map(projectFile -> {
                        ProjProjectFileVO projProjectFileVO = new ProjProjectFileVO();
                        projProjectFileVO.setProjectFileId(projectFile.getProjectFileId());
                        projProjectFileVO.setFileName(projectFile.getFileName());
                        projProjectFileVO.setFilePath(OBSClientUtils.getTemporaryUrl(projectFile.getFilePath(), 3600));
                        projProjectFileVO.setCreateTime(projectFile.getCreateTime());
                        return projProjectFileVO;
                    }).collect(Collectors.toList());
                    item.setFileList(fileList);
                }
            }
        });
        return projectFileRule;
    }
}




