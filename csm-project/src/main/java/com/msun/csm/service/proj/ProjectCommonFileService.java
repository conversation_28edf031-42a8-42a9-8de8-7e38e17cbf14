package com.msun.csm.service.proj;

import javax.servlet.http.HttpServletRequest;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.model.req.GetProjectFileRuleReq;
import com.msun.csm.model.req.UpdateUseFlagReq;
import com.msun.csm.model.req.projectfile.DeleteFileReq;
import com.msun.csm.model.req.projectfile.UploadFileReq2;
import com.msun.csm.model.vo.FileUploadRuleVO;

public interface ProjectCommonFileService {

    /**
     * 查询各节点文件上传规则及已上传的文件
     *
     * @param req 参数
     * @return 文件上传规则及已上传的文件
     */
    FileUploadRuleVO getProjectFileRule(GetProjectFileRuleReq req);

    /**
     * 基于文件上传规则的文件上传
     *
     * @param req     参数
     * @param request 请求
     * @return 上传成功后文件信息
     */
    Result<ProjProjectFileExtend> uploadFileWithRule(UploadFileReq2 req, HttpServletRequest request);

    /**
     * 删除上传的文件
     *
     * @param req 参数
     */
    Result<Void> deleteFile(DeleteFileReq req);

    /**
     * 更新使用标识
     *
     * @param req 参数
     */
    Result<Void> updateUseFlag(UpdateUseFlagReq req);

    /**
     * 保存并根据规则检测上传的图片
     *
     * @param req 参数
     */
    Result<Void> saveProjectCommonFile(GetProjectFileRuleReq req);

}
