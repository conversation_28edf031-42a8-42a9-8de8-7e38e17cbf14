package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor;
import com.msun.csm.feign.entity.dataapplication.ResDataResp;
import com.msun.csm.feign.entity.openapi.resp.ApiGroupResp;
import com.msun.csm.feign.entity.openapi.resp.ApiInterfaceResp;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */

public interface ProjInterfaceVsAuthorService {

    int deleteByPrimaryKey(Long interfaceVsAuthorId);

    int insert(ProjInterfaceVsAuthor record);

    int insertOrUpdate(ProjInterfaceVsAuthor record);

    int insertOrUpdateSelective(ProjInterfaceVsAuthor record);

    int insertSelective(ProjInterfaceVsAuthor record);

    ProjInterfaceVsAuthor selectByPrimaryKey(Long interfaceVsAuthorId);

    int updateByPrimaryKeySelective(ProjInterfaceVsAuthor record);

    int updateByPrimaryKey(ProjInterfaceVsAuthor record);

    int updateBatch(List<ProjInterfaceVsAuthor> list);

    int updateBatchSelective(List<ProjInterfaceVsAuthor> list);

    int batchInsert(List<ProjInterfaceVsAuthor> list);


    /**
     * 能力开放平台-获取接口分组列表
     *
     * @return
     */
    List<ApiGroupResp> getApiGroupList();

    /**
     * 能力开放平台-获取接口列表
     *
     * @param groupId
     * @return
     */
    List<ApiInterfaceResp> getApiList(Long groupId);

    /**
     * 数据中台-获取数据集
     *
     * @return
     */
    List<ResDataResp> getResDataList();

    /**
     * 数据中台-获取访问令牌
     *
     * @return
     */
    String getAccessToken();

    /**
     * 获取埋点下拉数据--系统管理接口
     * @param projectInfoId
     * @return
     */
    Result getWorkflowDataList(Long projectInfoId);

    /**
     * 更新接口分类
     * @return
     */
    Result updateInterfaceCategory();
}
