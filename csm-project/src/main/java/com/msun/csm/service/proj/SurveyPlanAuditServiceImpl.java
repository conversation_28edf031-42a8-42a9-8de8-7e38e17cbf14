package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.ExamineStatusEnum;
import com.msun.csm.common.enums.ReviewTypeEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.tduck.FmUserFormData;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.sysuser.UserVsRoleMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormDataMapper;
import com.msun.csm.model.dto.AllocatingAuditorParam;
import com.msun.csm.model.dto.GetSurveyPlanAuditListParam;
import com.msun.csm.model.dto.RejectOrAcceptParam;
import com.msun.csm.model.dto.SurveyPlanAuditDetail;
import com.msun.csm.model.dto.SurveyPlanAuditInfo;
import com.msun.csm.model.dto.SurveyPlanAuditLog;
import com.msun.csm.model.param.CreateTduckFormParam;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.resp.RejectReasonAndCount;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.tduck.FmUserFormService;
import com.msun.csm.service.tduck.TduckService;
import com.msun.csm.util.PageUtil;

@Slf4j
@Service
public class SurveyPlanAuditServiceImpl implements SurveyPlanAuditService {

    @Resource
    private ProjProductDeliverRecordMapper deliverRecordMapper;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private UserVsRoleMapper userVsRoleMapper;

    @Resource
    private ProjSurveyPlanService projSurveyPlanService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private TduckService tduckService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private DictProductService dictProductService;

    @Resource
    private ProjBusinessExamineLogService projBusinessExamineLogService;

    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private SendBusinessMessageService sendBusinessMessageService;

    @Resource
    private FmUserFormDataMapper fmUserFormDataMapper;

    @Resource
    private FmUserFormService fmUserFormService;

    @Override
    public List<BaseIdNameResp> getCanSurveyProduct(Long projectInfoId) {
        List<ProjProductDeliverRecord> deliverRecordList = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(projectInfoId);
        Set<Long> productIds = deliverRecordList.stream().map(ProjProductDeliverRecord::getProductDeliverId).collect(Collectors.toSet());
        List<BaseIdNameResp> byProductIds = productMapper.findByProductIds(productIds);
        if (!CollectionUtils.isEmpty(byProductIds)) {
            byProductIds.forEach(item -> {
                if (item.getName().endsWith("-")) {
                    item.setName(item.getName().substring(0, item.getName().lastIndexOf("-")));
                }
            });
        }
        return byProductIds;
    }

    @Override
    public List<BaseIdCodeNameResp> getAuditor(Long projectInfoId) {
        return userVsRoleMapper.getAuditor();
    }

    @Override
    public PageList<SurveyPlanAuditInfo> getSurveyPlanAuditList(GetSurveyPlanAuditListParam param) {
        List<SurveyPlanAuditInfo> surveyPlanAuditList = projSurveyPlanMapper.getSurveyPlanAuditList(param);

        PageList<SurveyPlanAuditInfo> pageList = PageUtil.getPageList(surveyPlanAuditList, param.getPageIndex(), param.getPageSize());

        if (!CollectionUtils.isEmpty(pageList.getPageResultList())) {
            pageList.getPageResultList().forEach(item -> {
                if (item.getAuditSysUserId() != null) {
                    RejectReasonAndCount surveyReject = projBusinessExamineLogService.selectReasonAndCountById(item.getSurveyPlanId(), ExamineStatusEnum.BACKEND_REJECTION);
                    item.setSurveyRejectCount(surveyReject.getRejectCount());
                    item.setSurveyRejectReason(surveyReject.getRejectReason());
                }
            });
        }
        return pageList;
    }

    @Override
    @Transactional
    public boolean allocatingAuditor(List<AllocatingAuditorParam> param) {
        if (CollectionUtils.isEmpty(param)) {
            return false;
        }
        for (AllocatingAuditorParam allocatingAuditorParam : param) {
            ProjSurveyPlan oldSurveyPlan = projSurveyPlanService.selectByPrimaryKey(allocatingAuditorParam.getSurveyPlanId());
            ProjSurveyPlan projSurveyPlan = new ProjSurveyPlan();
            projSurveyPlan.setSurveyPlanId(allocatingAuditorParam.getSurveyPlanId());
            projSurveyPlan.setAuditSysUserId(allocatingAuditorParam.getAuditSysUserId());
            projSurveyPlan.setPlanAuditTime(allocatingAuditorParam.getPlanAuditTime());
            projSurveyPlanService.updateByPrimaryKeySelective(projSurveyPlan);
            ProjSurveyPlan newSurveyPlan = projSurveyPlanService.selectByPrimaryKey(allocatingAuditorParam.getSurveyPlanId());
            sendBusinessMessageService.sendAllocatingOrCancelSurveyAuditMessage(newSurveyPlan.getProjectInfoId(), oldSurveyPlan, newSurveyPlan);

            sendBusinessMessageService.updateEarlyWarningAndPenaltyPerson(ReviewTypeEnum.PRODUCT_SUBMIT_AUDIT, allocatingAuditorParam.getSurveyPlanId(), allocatingAuditorParam.getAuditSysUserId());
        }
        return true;
    }

    @Override
    public SurveyPlanAuditDetail getSurveyPlanAuditDetail(AllocatingAuditorParam param) {
        ProjSurveyPlan projSurveyPlan = projSurveyPlanService.selectByPrimaryKey(param.getSurveyPlanId());

        boolean onlyOneSurvey = projSurveyPlanService.getOnlyOneSurveyFlag(projSurveyPlan.getProjectInfoId(), projSurveyPlan.getHospitalInfoId(), projSurveyPlan.getYyProductId());

        CreateTduckFormParam createTduckFormParam = new CreateTduckFormParam();
        createTduckFormParam.setProjectInfoId(projSurveyPlan.getProjectInfoId());
        createTduckFormParam.setHospitalInfoId(projSurveyPlan.getHospitalInfoId());
        createTduckFormParam.setProductId(projSurveyPlan.getYyProductId().toString());
        createTduckFormParam.setDeptName(projSurveyPlan.getDeptName());
        createTduckFormParam.setSysUserId(projSurveyPlan.getSurveyUserId());
        createTduckFormParam.setSource("config");
        createTduckFormParam.setOperationSource("audit");
        createTduckFormParam.setOnlyOneSurvey(onlyOneSurvey);
        String configUrl = tduckService.createTduckForm(createTduckFormParam);
        SurveyPlanAuditDetail surveyPlanAuditDetail = new SurveyPlanAuditDetail();
        surveyPlanAuditDetail.setSurveyFormUrl(configUrl);
        List<ProjBusinessExamineLogResp> businessExamineLogResps = projBusinessExamineLogService.selectLogById(param.getSurveyPlanId());
        if (!CollectionUtils.isEmpty(businessExamineLogResps)) {
            List<SurveyPlanAuditLog> collect = businessExamineLogResps.stream().map(item -> new SurveyPlanAuditLog(item.getBusinessExamineLogId(), item.getExamineStatus(), item.getLogTitle(), item.getOperateContent(), item.getOperatorSysUserId(), item.getOperateUserName(), item.getOperateTime())).collect(Collectors.toList());
            surveyPlanAuditDetail.setAuditLog(collect);
        }
        return surveyPlanAuditDetail;
    }

    @Override
    @Transactional
    public boolean rejectOrAccept(RejectOrAcceptParam param) {
        Date now = new Date();
        // 审核通过
        if (Integer.valueOf(1).equals(param.getOperationType())) {
            List<Long> surveyPlanIdList = param.getSurveyPlanIdList();
            for (Long surveyPlanId : surveyPlanIdList) {
                ProjSurveyPlan projSurveyPlan = projSurveyPlanService.selectByPrimaryKey(surveyPlanId);
                if (!Integer.valueOf(4).equals(projSurveyPlan.getCompleteStatus()) && !Integer.valueOf(5).equals(projSurveyPlan.getCompleteStatus())) {
                    throw new IllegalArgumentException("存在不是【待审核，已驳回】状态的调研计划，不允许进行审核操作。");
                }
                ProjSurveyPlan updateSurveyPlanParam = new ProjSurveyPlan();
                updateSurveyPlanParam.setSurveyPlanId(surveyPlanId);
                updateSurveyPlanParam.setActualAuditTime(now);
                updateSurveyPlanParam.setCompleteStatus(1);
                updateSurveyPlanParam.setUpdateTime(now);
                projSurveyPlanService.updateByPrimaryKeySelective(updateSurveyPlanParam);

                projBusinessExamineLogService.saveOperationLog("survey", 1, "审核通过", surveyPlanId);

                SendMessageParam rejectMessageParam = this.getAcceptMessageParam(surveyPlanId);
                sendMessageService.sendMessage2(rejectMessageParam);

                sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projSurveyPlan.getSurveyPlanId(), ReviewTypeEnum.PRODUCT_SUBMIT_AUDIT.getBusinessTable());
            }
            return true;
        }
        // 驳回
        if (Integer.valueOf(2).equals(param.getOperationType())) {
            List<Long> surveyPlanIdList = param.getSurveyPlanIdList();
            for (Long surveyPlanId : surveyPlanIdList) {
                ProjSurveyPlan projSurveyPlan = projSurveyPlanService.selectByPrimaryKey(surveyPlanId);
                if (!Integer.valueOf(4).equals(projSurveyPlan.getCompleteStatus()) && !Integer.valueOf(5).equals(projSurveyPlan.getCompleteStatus())) {
                    throw new IllegalArgumentException("存在不是【待审核，已驳回】状态的调研计划，不允许进行审核操作。");
                }
                ProjSurveyPlan updateSurveyPlanParam = new ProjSurveyPlan();
                updateSurveyPlanParam.setSurveyPlanId(surveyPlanId);
                updateSurveyPlanParam.setCompleteStatus(5);
                updateSurveyPlanParam.setUpdateTime(now);
                projSurveyPlanService.updateByPrimaryKeySelective(updateSurveyPlanParam);

                projBusinessExamineLogService.saveOperationLog("survey", 2, param.getExamineOpinion(), surveyPlanId);

                SendMessageParam rejectMessageParam = this.getRejectMessageParam(surveyPlanId, param.getExamineOpinion());
                sendMessageService.sendMessage2(rejectMessageParam);

                String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(projSurveyPlan.getProjectInfoId(), String.valueOf(projSurveyPlan.getYyProductId()),1);

                int delete = fmUserFormDataMapper.delete(
                        new QueryWrapper<FmUserFormData>()
                                .eq("form_key", formKey)
                                .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                                .eq("source", "config")
                );
                log.info("撤销审核时删除最终调研结果，删除成功数据条数={}", delete);

                sendBusinessMessageService.destroyEarlyWarningAndPenaltyMessage(projSurveyPlan.getSurveyPlanId(), ReviewTypeEnum.PRODUCT_SUBMIT_AUDIT.getBusinessTable());
                sendBusinessMessageService.generateEarlyWarningAndPenaltyMessage2(
                        ReviewTypeEnum.PRODUCT_RESUBMIT_FOR_REVIEW,
                        projSurveyPlan.getProjectInfoId(),
                        projSurveyPlan.getSurveyPlanId(),
                        sendBusinessMessageService.getPenaltyPerson(projSurveyPlan.getSurveyUserId(), projSurveyPlan.getProjectInfoId()),
                        "产品业务调研驳回后提交审核超期",
                        now
                );
            }

            return true;
        }
        throw new IllegalArgumentException("参数【operationType】的值错误");
    }


    private SendMessageParam getRejectMessageParam(Long surveyPlanId, String reason) {
        // 调研计划
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projSurveyPlan.getProjectInfoId());
        // 客户信息
        ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }

        String productName = dictProductService.getProductNameByYyProductId(projSurveyPlan.getYyProductId());
        if (StringUtils.isBlank(productName)) {
            messageContentParam.put("productName", "");
        } else {
            messageContentParam.put("productName", "（" + productName + "）");
        }

        messageContentParam.put("reason", StringUtils.isBlank(reason) ? "请登录平台查看具体驳回原因" : reason);

        List<Long> surveyUserId = new ArrayList<>();
        surveyUserId.add(projSurveyPlan.getSurveyUserId());
        SendMessageParam messageParam = new SendMessageParam();
        messageParam.setMessageTypeId(DictMessageTypeEnum.SURVEY_REJECT_MESSAGE.getId());
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setMessageContentParam(messageContentParam);
        messageParam.setSysUserIds(surveyUserId);
        return messageParam;
    }

    private SendMessageParam getAcceptMessageParam(Long surveyPlanId) {
        // 调研计划
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projSurveyPlan.getProjectInfoId());
        // 客户信息
        ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }

        String productName = dictProductService.getProductNameByYyProductId(projSurveyPlan.getYyProductId());
        if (StringUtils.isBlank(productName)) {
            messageContentParam.put("productName", "");
        } else {
            messageContentParam.put("productName", "（" + productName + "）");
        }

        List<Long> surveyUserId = new ArrayList<>();
        surveyUserId.add(projSurveyPlan.getSurveyUserId());
        SendMessageParam messageParam = new SendMessageParam();
        messageParam.setMessageTypeId(DictMessageTypeEnum.SURVEY_ACCEPT_MESSAGE.getId());
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setMessageContentParam(messageContentParam);
        messageParam.setSysUserIds(surveyUserId);
        return messageParam;
    }

}
