package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusDto;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.device.EquipStatusEnum;
import com.msun.csm.common.enums.projproduct.ProjProductEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigProductJobMenuDetail;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjEquipCheckVsLis;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsAims;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsPacs;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSpecialProductRecord;
import com.msun.csm.dao.entity.tmp.TmpEquipCheckDetail;
import com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipCheckVsLisMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipDocMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordLogMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsAimsMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsEcgMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSpecialProductRecordMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpEquipCheckDetailMapper;
import com.msun.csm.feign.client.analymanager.LisAnalyManagerClient;
import com.msun.csm.model.dto.CheckEquipToAnalyManagerDTO;
import com.msun.csm.model.dto.ProjEquipDocDTO;
import com.msun.csm.model.dto.ProjEquipSummaryDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.UrgeProcessingDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.vo.DictEquipInfoVO;
import com.msun.csm.model.vo.EquipSummaryVo;
import com.msun.csm.model.vo.ItemInfoVO;
import com.msun.csm.model.vo.OldToEquipConfigVO;
import com.msun.csm.model.vo.ProjEquipDocVO;
import com.msun.csm.model.vo.ProjEquipReadySummaryVO;
import com.msun.csm.model.vo.ProjEquipRecordLogVO;
import com.msun.csm.model.vo.ProjEquipSummaryVO;
import com.msun.csm.model.vo.ProjProjectFileRuleVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * @description:
 * @fileName: ProjEquipSummaryServiceImpl.java
 * @author: lius3
 * @createAt: 2024/10/11 9:23
 * @updateBy: lius3
 * @remark: Copyright
 */
@Slf4j
@Service
public class ProjEquipSummaryServiceImpl implements ProjEquipSummaryService {


    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private TmpEquipCheckDetailMapper tmpEquipCheckDetailMapper;

    @Resource
    private ProjEquipCheckVsLisMapper equipCheckVsLisMapper;
    @Resource
    private DictProductVsModulesMapper dictProductVsModulesMapper;

    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;

    @Resource
    private ProjProductDeliverRecordMapper projProductDeliverRecordMapper;

    @Resource
    private ConfigProductJobMenuDetailMapper configProductJobMenuDetailMapper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;

    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private RuleProjectRuleConfigMapper ruleProjectRuleConfigMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private SystemSettingApi systemSettingApi;

    @Resource
    private ProjEquipDocMapper projEquipDocMapper;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private ProjEquipRecordVsLisService equipRecordVsLisService;

    @Resource
    private ProjEquipRecordVsPacsService equipRecordVsPacsService;

    @Resource
    private ProjEquipRecordVsHdService equipRecordVsHdService;

    @Resource
    private ProjEquipRecordVsAimsService equipRecordVsAimsService;

    @Resource
    private ProjEquipRecordVsEcgService equipRecordVsEcgService;

    @Resource
    private ProjSpecialProductRecordMapper projSpecialProductRecordMapper;

    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjEquipRecordVsAimsMapper equipRecordVsAimsMapper;

    @Resource
    private ProjEquipSummaryService equipSummaryService;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjEquipRecordCommonService equipRecordCommonService;

    @Resource
    private ProjEquipRecordLogMapper projEquipRecordLogMapper;

    @Resource
    private ProjEquipRecordVsEcgMapper equipRecordVsEcgMapper;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private LisAnalyManagerClient lisAnalyManagerClient;

    @Resource
    private ProjProjectPlanService projProjectPlanService;


    /**
     * 查询设备调研汇总信息
     *
     * @param projEquipSummaryDTO
     * @return
     */
    @Override
    public Result<List<ProjEquipSummaryVO>> findProjEquipSummaryInfo(ProjEquipSummaryDTO projEquipSummaryDTO) {
        List<ProjEquipSummaryVO> equipSummaryVOList = new ArrayList<>();
        // 重新设备汇总功能
        // 查询当前项目的派工产品
        List<ProjProductDeliverRecord> projProductDeliverRecords = projProductDeliverRecordMapper.selectList(
                new QueryWrapper<ProjProductDeliverRecord>()
                        .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                        .eq("is_deleted", 0)
        );
        List<Long> yyProductIds = projProductDeliverRecords.stream().map(ProjProductDeliverRecord::getProductDeliverId)
                .collect(Collectors.toList());
        // 查询哪些产品存在设备菜单
        List<ConfigProductJobMenuDetail> configProductJobMenuDetails = configProductJobMenuDetailMapper.selectList(
                new QueryWrapper<ConfigProductJobMenuDetail>()
                        .in("yy_product_id", yyProductIds)
                        .eq("product_job_menu_id", 5)
        );
        // 获取存在设备菜单的产品名称
        List<Long> productIds = configProductJobMenuDetails.stream().map(ConfigProductJobMenuDetail::getYyProductId)
                .collect(Collectors.toList());
        for (Long yyproductId : productIds) {
            // 校验当前产品是否已经对接设备
            List<Long> menuProductIds =
                    Arrays.stream(ProductEquipSurveyMenuEnum.values()).map(vo -> vo.getYyProductId()).collect(Collectors.toList());
            if (!menuProductIds.contains(yyproductId)) {
                continue;
            }
            ProjEquipSummaryVO projEquipSummaryVO = new ProjEquipSummaryVO();
            // 查询实施产品中 存在设备调研功能的 汇总数据
            DictProduct product = dictProductMapper.selectOne(new QueryWrapper<DictProduct>()
                    .eq("yy_product_id", yyproductId));
            //产品名称 或 模块名称
            if (ObjectUtil.isNotEmpty(product)) {
                projEquipSummaryVO.setProductName(product.getProductName());
                projEquipSummaryVO.setOrderNo(product.getOrderNo());
            } else {
                DictProductVsModules modules =
                        dictProductVsModulesMapper.selectOne(new QueryWrapper<DictProductVsModules>()
                                .eq("yy_module_id", yyproductId)
                        );
                projEquipSummaryVO.setProductName(modules.getYyModuleName());
            }
            String code = ProductEquipSurveyMenuEnum.getByYyProductId(product.getYyProductId()).getCode();
            projEquipSummaryVO.setProductCode(code);
            //调研设备总数
            Long surveyAllCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId));
            projEquipSummaryVO.setEquipCount(Convert.toInt(surveyAllCount));
            //需对接数
            Long requiredFlagCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId)
                    .eq("required_flag", 1)
            );
            projEquipSummaryVO.setRequiredCount(Convert.toInt(requiredFlagCount));
            //暂不对接数
            Long norequiredFlagCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId)
                    .eq("required_flag", 0)
            );
            projEquipSummaryVO.setNorequiredCount(Convert.toInt(norequiredFlagCount));
            //未申请数量 【调研节点】
            Long notApplyCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId)
                    .eq("equip_status", 0)
            );
            projEquipSummaryVO.setNotApplyCount(Convert.toInt(notApplyCount));
            //已驳回数量 【调研节点】
            Long rejectCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId)
                    .eq("equip_status", 2)
            );
            projEquipSummaryVO.setRejectCount(Convert.toInt(rejectCount));
            //调研完成数量 【调研节点】
            // Lis、血透 研发中的状态以及后续状态都算 调研按成。 PACS 添加了设备就算
            Long completeCount = 0L;
            if (yyproductId.equals(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId()) || yyproductId.equals(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId())) {
                completeCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                        .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                        .eq("yy_product_id", yyproductId)
                        .in("equip_status", Arrays.asList(3, 4, 5, 6))
                );
            } else if (yyproductId.equals(ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId())) {
                completeCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                        .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                        .eq("yy_product_id", yyproductId)
                );
            }
            projEquipSummaryVO.setCompleteCount(Convert.toInt(completeCount));
            //调研说明 【调研节点】 产品待办菜单明细配置表中
            ConfigProductJobMenuDetail configProductJobMenuDetail = configProductJobMenuDetailMapper.selectOne(
                    new QueryWrapper<ConfigProductJobMenuDetail>()
                            .eq("yy_product_id", yyproductId)
                            .eq("product_job_menu_id", 5)
            );
            projEquipSummaryVO.setSurveyRemark(configProductJobMenuDetail.getSurveyRemark());
            //检测通过标准 【准备节点】
            projEquipSummaryVO.setPassStandard(configProductJobMenuDetail.getPassStandard());
            //测试成功数量 【准备节点】
            Long testSuccessCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId)
                    .eq("required_flag", 1)
                    .eq("equip_status", 5)
            );
            projEquipSummaryVO.setTestSuccessCount(Convert.toInt(testSuccessCount));
            //测试失败数量 【准备节点】
            Long testErrorCount = projEquipRecordMapper.selectCount(new QueryWrapper<ProjEquipRecord>()
                    .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                    .eq("yy_product_id", yyproductId)
                    .eq("required_flag", 1)
                    .eq("equip_status", 6)
            );
            projEquipSummaryVO.setTestErrorCount(Convert.toInt(testErrorCount));
            //责任人
            //完成状态【调研节点、准备节点共用】
            // 校验是调研阶段还是准备阶段
            Result<ProjMilestoneInfo> projMilestoneInfoResult =
                    milestoneInfoService.selectById(projEquipSummaryDTO.getMilestoneInfoId());
            if (ObjectUtil.isEmpty(projMilestoneInfoResult.getData())) {
                projEquipSummaryVO.setFinishStatus(0);
            } else if (projMilestoneInfoResult.getData().getMilestoneNodeCode().equals(MilestoneNodeEnum.SURVEY_DEVICE.getCode())) {
                // 查询里程碑节点任务
                List<ProjMilestoneTask> milestoneTaskList =
                        milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                                .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                                .eq("milestone_node_code", MilestoneNodeEnum.SURVEY_DEVICE.getCode())
                        );
                if (CollectionUtil.isNotEmpty(milestoneTaskList)) {
                    List<Long> taskIds =
                            milestoneTaskList.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
                    List<ProjMilestoneTaskDetail> projMilestoneTaskDetailList = CollUtil.newArrayList();
                    if (CollUtil.isNotEmpty(taskIds)) {
                        // 查询责任人以及完成状态
                        projMilestoneTaskDetailList =
                                milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                                        .in("milestone_task_id", taskIds)
                                        .eq("product_deliver_id", yyproductId)
                                        .eq("complete_status", 1)
                                );
                    }
                    // 当为空的时候 代表都没完成。那产品的状态就是已完成。否则就是未完成
                    if (CollectionUtil.isNotEmpty(projMilestoneTaskDetailList)) {
                        projEquipSummaryVO.setFinishStatus(1);
                    } else {
                        projEquipSummaryVO.setFinishStatus(0);
                    }
                }
            } else if (projMilestoneInfoResult.getData().getMilestoneNodeCode().equals(MilestoneNodeEnum.PREPARAT_DEVICE.getCode())) {
                // 查询里程碑节点任务
                List<ProjMilestoneTask> milestoneTaskList =
                        milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                                .eq("project_info_id", projEquipSummaryDTO.getProjectInfoId())
                                .eq("milestone_node_code", MilestoneNodeEnum.PREPARAT_DEVICE.getCode())
                        );
                List<Long> taskIds =
                        milestoneTaskList.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
                List<ProjMilestoneTaskDetail> projMilestoneTaskDetailList = CollUtil.newArrayList();
                if (CollUtil.isNotEmpty(taskIds)) {
                    // 查询责任人以及完成状态
                    projMilestoneTaskDetailList =
                            milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                                    .in("milestone_task_id", taskIds)
                                    .eq("product_deliver_id", yyproductId)
                                    .eq("complete_status", 1)
                            );
                }
                // 当为空的时候 代表都没完成。那产品的状态就是已完成。否则就是未完成
                if (CollectionUtil.isNotEmpty(projMilestoneTaskDetailList)) {
                    projEquipSummaryVO.setFinishStatus(1);
                } else {
                    projEquipSummaryVO.setFinishStatus(0);
                }
            }
            equipSummaryVOList.add(projEquipSummaryVO);
        }
        // 根据产品的OrderNo进行排序
        List<ProjEquipSummaryVO> resultList =
                equipSummaryVOList.stream().sorted(Comparator.comparing(ProjEquipSummaryVO::getOrderNo)).collect(Collectors.toList());
        return Result.success(resultList);
    }

    /**
     * 获取项目的设备动态菜单
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result<List<EquipSummaryVo>> selectSurveyEquipMenu(Long projectInfoId) {
        List<EquipSummaryVo> voList = new ArrayList<>();
        // 查询当前项目的派工产品
        List<ProjProductDeliverRecord> projProductDeliverRecords = projProductDeliverRecordMapper.selectList(
                new QueryWrapper<ProjProductDeliverRecord>()
                        .eq("project_info_id", projectInfoId)
                        .eq("is_deleted", 0)
        );
        List<Long> yyProductIds = projProductDeliverRecords.stream().map(ProjProductDeliverRecord::getProductDeliverId)
                .collect(Collectors.toList());
        // 查询哪些产品存在设备菜单
        List<ConfigProductJobMenuDetail> configProductJobMenuDetails = configProductJobMenuDetailMapper.selectList(
                new QueryWrapper<ConfigProductJobMenuDetail>()
                        .in("yy_product_id", yyProductIds)
                        .eq("product_job_menu_id", 5)
        );
        // 获取存在设备菜单的产品名称
        List<Long> productIds = configProductJobMenuDetails.stream().map(ConfigProductJobMenuDetail::getYyProductId)
                .collect(Collectors.toList());
        List<DictProduct> dictProducts = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(productIds)) {
            dictProducts = dictProductMapper.selectList(new QueryWrapper<DictProduct>()
                    .in("yy_product_id", productIds)
                    .orderByAsc("order_no")
            );
        }
        List<Long> menuProductIds =
                Arrays.stream(ProductEquipSurveyMenuEnum.values()).map(vo -> vo.getYyProductId()).collect(Collectors.toList());
        // 封装返回数据
        // 添加汇总页面
        EquipSummaryVo equipSummaryVo = new EquipSummaryVo();
        equipSummaryVo.setTitle("设备调研汇总");
        equipSummaryVo.setName(ProductEquipSurveyMenuEnum.SURVEYEQUIPALL.getCode());
        voList.add(equipSummaryVo);
        for (DictProduct product : dictProducts) {
            if (menuProductIds.contains(product.getYyProductId())) {
                EquipSummaryVo equipSummaryVo2 = new EquipSummaryVo();
                String code = ProductEquipSurveyMenuEnum.getByYyProductId(product.getYyProductId()).getCode();
                equipSummaryVo2.setTitle(product.getProductName());
                equipSummaryVo2.setName(code);
                voList.add(equipSummaryVo2);
            }
        }
        return Result.success(voList);
    }

    @Resource
    private ProjMilestoneInfoService projMilestoneInfoService;

    /**
     * 更新设备调研 / 设备准备里程碑节点状态
     *
     * @param milestoneInfoId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateMilestoneInfo(Long milestoneInfoId) {
        Result<ProjMilestoneInfo> projMilestoneInfoResult = milestoneInfoService.selectById(milestoneInfoId);
        ProjMilestoneInfo milestoneInfoResultData = projMilestoneInfoResult.getData();

//        MilestoneNodeEnum milestoneNodeEnum = MilestoneNodeEnum.getByCode(milestoneInfoResultData.getMilestoneNodeCode());
//        Result<Boolean> booleanResult = projMilestoneInfoService.checkFileRule(milestoneInfoResultData.getProjectInfoId(), milestoneNodeEnum);
//        if (booleanResult != null) {
//            return booleanResult;
//        }
        // 判断是否存在对应里程碑节点的任务，当不存在任务时直接完成里程碑节点
        List<ProjMilestoneTask> hasTaskList = milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                .eq("milestone_info_id", milestoneInfoResultData.getMilestoneInfoId())
        );
        // 查询当前项目的派工产品
        List<ProjProductDeliverRecord> projProductDeliverRecords = projProductDeliverRecordMapper.selectList(
                new QueryWrapper<ProjProductDeliverRecord>()
                        .eq("project_info_id", projMilestoneInfoResult.getData().getProjectInfoId())
                        .eq("is_deleted", 0)
        );
        List<Long> yyProductIds = projProductDeliverRecords.stream().map(ProjProductDeliverRecord::getProductDeliverId)
                .collect(Collectors.toList());
        String str = "";
        String productMessage = "";
        if (CollectionUtil.isNotEmpty(hasTaskList) && hasTaskList.size() > 0) {
            Long productMilestoneInfoId = 0L;
            // 判断传入的节点id是调研阶段还是准备阶段
            if (MilestoneNodeEnum.SURVEY_DEVICE.getCode().equals(milestoneInfoResultData.getMilestoneNodeCode())) {
                ProjMilestoneInfo milestoneInfo =
                        milestoneInfoService.getMilestoneInfo(milestoneInfoResultData.getProjectInfoId(),
                                MilestoneNodeEnum.SURVEY_PRODUCT.getCode());
                productMilestoneInfoId = milestoneInfo.getMilestoneInfoId();
                productMessage = "产品调研";
            } else {
                ProjMilestoneInfo milestoneInfo =
                        milestoneInfoService.getMilestoneInfo(milestoneInfoResultData.getProjectInfoId(),
                                MilestoneNodeEnum.PREPARAT_PRODUCT.getCode());
                productMilestoneInfoId = milestoneInfo.getMilestoneInfoId();
                productMessage = "产品准备";
            }
            // 更新所有产品的设备状态 【再次校验并更新各产品的设备完成状态  赵飞  多此一举，已经有提交完成功能，但是又在确认完成时再次重复校验】
            // 1 Lis
            if (yyProductIds.contains(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId())) {
                ProjEquipVsProductFinishDTO projEquipVsProductFinishLisDTO = new ProjEquipVsProductFinishDTO();
                projEquipVsProductFinishLisDTO.setProductId(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId());
                projEquipVsProductFinishLisDTO.setMilestoneInfoId(productMilestoneInfoId);
                projEquipVsProductFinishLisDTO.setProjectInfoId(milestoneInfoResultData.getProjectInfoId());
                Result result = equipRecordVsLisService.commitFinish(projEquipVsProductFinishLisDTO);
                if (!result.isSuccess()) {
                    str = str + "【" + ProductEquipSurveyMenuEnum.LISEQUIP.getName() + "设备】" + result.getMsg() + ";\n";
                }
            }
            // 2 Pacs
            if (yyProductIds.contains(ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId())) {
                ProjEquipVsProductFinishDTO projEquipVsProductFinishPacsDTO = new ProjEquipVsProductFinishDTO();
                projEquipVsProductFinishPacsDTO.setProductId(ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId());
                projEquipVsProductFinishPacsDTO.setMilestoneInfoId(productMilestoneInfoId);
                projEquipVsProductFinishPacsDTO.setProjectInfoId(milestoneInfoResultData.getProjectInfoId());
                Result result1 = equipRecordVsPacsService.commitFinish(projEquipVsProductFinishPacsDTO);
                if (!result1.isSuccess()) {
                    str = str + "【" + ProductEquipSurveyMenuEnum.PACSEQUIP.getName() + "设备】" + result1.getMsg() + ";\n";
                }
            }
            if (yyProductIds.contains(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId())) {
                // 3 血透
                ProjEquipVsProductFinishDTO projEquipVsProductFinishHdDTO = new ProjEquipVsProductFinishDTO();
                projEquipVsProductFinishHdDTO.setProductId(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId());
                projEquipVsProductFinishHdDTO.setMilestoneInfoId(productMilestoneInfoId);
                projEquipVsProductFinishHdDTO.setProjectInfoId(milestoneInfoResultData.getProjectInfoId());
                Result result2 = equipRecordVsHdService.commitFinish(projEquipVsProductFinishHdDTO);
                if (!result2.isSuccess()) {
                    str = str + "【" + ProductEquipSurveyMenuEnum.HDEQUIP.getName() + "设备】" + result2.getMsg() + ";\n";
                }
            }
            if (yyProductIds.contains(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId())) {
                // 4 手麻
                ProjEquipVsProductFinishDTO projEquipVsProductFinishHdDTO = new ProjEquipVsProductFinishDTO();
                projEquipVsProductFinishHdDTO.setProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
                projEquipVsProductFinishHdDTO.setMilestoneInfoId(productMilestoneInfoId);
                projEquipVsProductFinishHdDTO.setProjectInfoId(milestoneInfoResultData.getProjectInfoId());
                Result result2 = equipRecordVsAimsService.commitFinish(projEquipVsProductFinishHdDTO);
                if (!result2.isSuccess()) {
                    str = str + "【" + ProductEquipSurveyMenuEnum.AIMSEQUIP.getName() + "设备】" + result2.getMsg() + ";\n";
                }
            }
            // 心电处理
            if (yyProductIds.contains(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId())) {
                ProjEquipVsProductFinishDTO projEquipVsProductFinishHdDTO = new ProjEquipVsProductFinishDTO();
                projEquipVsProductFinishHdDTO.setProductId(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
                projEquipVsProductFinishHdDTO.setMilestoneInfoId(productMilestoneInfoId);
                projEquipVsProductFinishHdDTO.setProjectInfoId(milestoneInfoResultData.getProjectInfoId());
                Result result2 = equipRecordVsEcgService.commitFinish(projEquipVsProductFinishHdDTO);
                if (!result2.isSuccess()) {
                    str = str + "【" + ProductEquipSurveyMenuEnum.ECGEQUIP.getName() + "设备】" + result2.getMsg() + ";\n";
                }
            }
            if (!str.trim().isEmpty()) {
                return Result.fail(str + "请重新指定" + productMessage + "责任人");
            }
            // 判断所有产品的设备是否都已经完成  milestoneTaskDetail中的 状态都是完成
            Long projectInfoId = milestoneInfoResultData.getProjectInfoId();
            String milestoneInfoCode = projMilestoneInfoResult.getData().getMilestoneNodeCode();
            List<ProjMilestoneTask> projMilestoneTasks =
                    milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                            .eq("project_info_id", projectInfoId)
                            .eq("milestone_node_code", milestoneInfoCode)
                            .eq("is_deleted", 0)
                    );
            List<Long> collect =
                    projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
            List<ProjMilestoneTaskDetail> projMilestoneTaskDetails =
                    milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                            .eq("is_deleted", 0)
                            .eq("complete_status", 0)
                            .in("milestone_task_id", collect)
                    );
            // 根据code判断是调研阶段还是 准备节点
            if (CollectionUtil.isNotEmpty(projMilestoneTaskDetails)) {
                if (MilestoneNodeEnum.SURVEY_DEVICE.getCode().equals(milestoneInfoCode)) {
                    return Result.fail("调研阶段产品所属设备未全部完成，请前往产品页面进行提交完成");
                } else {
                    return Result.fail("准备阶段产品所属设备未全部完成，请前往产品页面进行提交完成");
                }
            }
        }
        log.info("更新设备调研 / 设备准备里程碑节点状态，milestoneInfoId={}", milestoneInfoId);
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(milestoneInfoId);
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setActualCompTime(new Date());
        updateMilestoneDTO.setNodeHeadId(userHelper.getCurrentUser().getSysUserId());
        milestoneInfoService.updateMilestone(updateMilestoneDTO);
        projProjectPlanService.updatePlanStatusByProjectInfoIdAndItemCode(milestoneInfoResultData.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_DEVICE, ProjectPlanStatusEnum.FINISHED);
        return Result.success();
    }


    /**
     * 设备提交完成, 会更新调研或准备阶段任务的完成状态
     *
     * @param projectInfoId       项目id
     * @param milestoneNodeCode   里程碑节点code
     * @param equipSurveyMenuEnum 产品设备菜单枚举, 获取产品编码用
     * @return 处理结果, 处理完成或失败
     */
    public Result<String> commitFinish(Long projectInfoId, String milestoneNodeCode,
                                       ProductEquipSurveyMenuEnum equipSurveyMenuEnum,
                                       ProjProductEnum projProductEnum) {
        // 更新任务计划明细状态
        List<ProjMilestoneTask> projMilestoneTasks =
                milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                        .eq("project_info_id", projectInfoId)
                        .eq("milestone_node_code", milestoneNodeCode)
                );
        if (CollectionUtil.isEmpty(projMilestoneTasks)) {
            return Result.fail(equipSurveyMenuEnum.getName() + "请先分配产品业务调研后再进行提交完成");
        }
        // 查询产品
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                projProductEnum.getProductName()).last(" limit 1"));
        List<Long> collect =
                projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
        List<ProjMilestoneTaskDetail> projMilestoneTaskDetails =
                milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("product_deliver_id", product.getYyProductId())
                        .in("milestone_task_id", collect)
                );
        if (CollectionUtil.isEmpty(projMilestoneTaskDetails)) {
            return Result.fail(MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)
                    ? equipSurveyMenuEnum.getName() + "产品未查询到对应任务,请在产品调研重新指定责任人"
                    : equipSurveyMenuEnum.getName() + "产品未查询到对应任务");
        }
        for (ProjMilestoneTaskDetail taskDetail : projMilestoneTaskDetails) {
            taskDetail.setCompleteStatus(1);
            milestoneTaskDetailMapper.updateById(taskDetail);
        }
        return Result.success();
    }

    /**
     * 获取手麻nodeCode, 适用于调研和准备阶段
     *
     * @param milestoneNodeCode 里程碑节点code
     * @param projectInfoId     项目id
     * @param surveyStatusList  调研状态
     * @param prepareStatusList 设备检测状态, 如设置5, 若存在不等于5的设备则说明有设备未检测完成
     * @return 获取结果, success：true时，取data, false为空
     */
    private Result<String> getEcgMilestoneNodeCode(String milestoneNodeCode, Long projectInfoId,
                                                   List<Integer> surveyStatusList, List<Integer> prepareStatusList,
                                                   ProductEquipSurveyMenuEnum equipSurveyMenuEnum) {
        String currentMilestoneNode;
        if (MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)) {
            if (CollUtil.isNotEmpty(surveyStatusList)) {
                Integer count = equipRecordVsEcgMapper.getNotApplyRecordCount(surveyStatusList, projectInfoId);
                if (count > 0) {
                    return Result.fail(equipSurveyMenuEnum.getName() + "设置对接的设备必须提交申请后，才可提交完成！");
                }
            }
            currentMilestoneNode = MilestoneNodeEnum.SURVEY_DEVICE.getCode();
        } else if (MilestoneNodeEnum.PREPARAT_PRODUCT.getCode().equals(milestoneNodeCode)) {
            if (CollUtil.isNotEmpty(prepareStatusList)) {
                Integer count = equipRecordVsEcgMapper.getNotApplyRecordCount(prepareStatusList, projectInfoId);
                if (count > 0) {
                    return Result.fail(equipSurveyMenuEnum.getName() + "存在需要对接的设备未测试通过,请全部测试通过后再进此操作");
                }
            }
            currentMilestoneNode = MilestoneNodeEnum.SURVEY_PRODUCT.getCode();
        } else {
            return Result.fail(equipSurveyMenuEnum.getName() + "未匹配到里程碑节点");
        }
        return Result.success(currentMilestoneNode);
    }

    /**
     * 获取设备分类
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> selectSurveyEquipClass(Long projectInfoId) {
        return Result.success(this.projEquipRecordMapper.selectSurveyEquipClass(projectInfoId));
    }

    /**
     * 获取设备厂商
     *
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> selectSurveyEquipFactory(String productCode, Long id) {
        List<BaseIdNameResp> baseIdNameResps = new ArrayList<>();
        switch (productCode) {
            case "Lis":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipFactory(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId(), id);
                break;
            case "Pacs":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipFactory(ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId(), id);
                break;
            case "Hd":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipFactory(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId(), id);
                break;
            case "Aims":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipFactory(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId(), id);
            case "Ecg":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipFactory(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId(), id);
                break;
            default:
                return Result.fail("产品错误,请检查");
        }
        return Result.success(baseIdNameResps);
    }

    /**
     * 获取设备类型
     *
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> selectSurveyEquipType(String productCode, Long id) {
        List<BaseIdNameResp> baseIdNameResps = new ArrayList<>();
        switch (productCode) {
            case "Lis":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipType(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId(), id);
                break;
            case "Pacs":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipType(ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId(), id);
                break;
            case "Hd":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipType(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId(), id);
                break;
            case "Aims":
                baseIdNameResps =
                        this.projEquipRecordMapper.selectSurveyEquipType(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId(), id);
                break;
            default:
                return Result.fail("产品错误,请检查");
        }
        return Result.success(baseIdNameResps);
    }

    /**
     * 获取设备字典
     *
     * @return
     */
    @Override
    public Result<List<DictEquipInfoVO>> selectSurveyEquipInfo(String productCode, Long id) {
        List<DictEquipInfoVO> dictEquipInfoVOS = new ArrayList<>();
        switch (productCode) {
            case "Lis":
                dictEquipInfoVOS =
                        this.projEquipRecordMapper.selectSurveyEquipInfo(ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId(), id);
                break;
            case "Pacs":
                dictEquipInfoVOS =
                        this.projEquipRecordMapper.selectSurveyEquipInfo(ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId(), id);
                break;
            case "Hd":
                dictEquipInfoVOS =
                        this.projEquipRecordMapper.selectSurveyEquipInfo(ProductEquipSurveyMenuEnum.HDEQUIP.getYyProductId(), id);
                break;
            case "Aims":
                dictEquipInfoVOS =
                        this.projEquipRecordMapper.selectSurveyEquipInfo(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId(), id);
                break;
            case "Ecg":
                dictEquipInfoVOS =
                        this.projEquipRecordMapper.selectSurveyEquipInfo(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId(), id);
                break;
            default:
                return Result.fail("产品错误,请检查");
        }
        return Result.success(dictEquipInfoVOS);
    }

    /**
     * 获取设备属性字典
     *
     * @param equipAttributesCode
     * @return
     */
    @Override
    public Result<List<BaseCodeNameResp>> selectSurveyAttributesInfo(@Param("equipAttributesCode") String equipAttributesCode) {
        return Result.success(this.projEquipRecordMapper.selectSurveyEquipAttributes(equipAttributesCode));
    }

    /**
     * 查询附件配置信息
     *
     * @param sceneCode
     * @param isMobile
     * @return
     */
    @Override
    public Result<List<ProjProjectFileRuleVO>> selectProjectFileConfig(String sceneCode, Integer isMobile) {
        List<ProjProjectFileRuleVO> ruleList = ruleProjectRuleConfigMapper.getTemplateByProductCode(sceneCode,
                isMobile);
        for (ProjProjectFileRuleVO projProjectFileRuleVO : ruleList) {
            if (ObjectUtil.isNotEmpty(projProjectFileRuleVO.getLimitType())) {
                projProjectFileRuleVO.setLimitTypeArray(projProjectFileRuleVO.getLimitType().split(","));
            }
        }
        return Result.success(ruleList);
    }

    /**
     * 调用云健康进行设备检测
     *
     * @param dtoList
     * @return
     */
    @Override
    public Result checkEquipToLisAnalyManager(List<CheckEquipToAnalyManagerDTO> dtoList) {
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(dtoList.get(0).getHospitalId());
        if (hospitalInfo.getCloudHospitalId() == null) {
            return Result.fail("医院未部署云健康!");
        }
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 设置检测参数
        List<ProductEquipmentDto> param = new ArrayList<>();
        for (CheckEquipToAnalyManagerDTO dto : dtoList) {
            ProductEquipmentDto productEquipmentDto = new ProductEquipmentDto();
            productEquipmentDto.setOldEquipId(dto.getCloudEquipId());
            productEquipmentDto.setProductId(Convert.toInt(dto.getYyProductId()));
            productEquipmentDto.setProductCode(dto.getProductCode());
            productEquipmentDto.setId(dto.getProductEquipId());
            productEquipmentDto.setHospitalId(hospitalInfo.getCloudHospitalId());
            param.add(productEquipmentDto);
        }
        SystemConfigDto<ProductEquipmentDto> configDto = new SystemConfigDto<>();
        configDto.setHospitalId(hospitalInfo.getCloudHospitalId());
        configDto.setOrgId(hospitalInfo.getOrgId());
        configDto.setHisOrgId(hospitalInfo.getOrgId());
        configDto.setData(param);
        ResponseResult equipmentStatus = systemSettingApi.getEquipmentStatus(configDto);
        List<EquipmentStatusDto> listData = Convert.toList(EquipmentStatusDto.class, equipmentStatus.getData());
        return Result.success(listData);
    }

    /**
     * 查询设备准备汇总信息
     *
     * @param projEquipSummaryDTO
     * @return
     */
    @Override
    public Result<List<ProjEquipReadySummaryVO>> findProjEquipReadySummaryInfo(ProjEquipSummaryDTO projEquipSummaryDTO) {
        return Result.success(projEquipRecordMapper.findProjEquipReadySummaryInfo(projEquipSummaryDTO));
    }

    /**
     * 查询设备文档
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ProjEquipDocVO>> findProjEquipDoc(ProjEquipDocDTO dto) {
        List<ProjEquipDocVO> equipDocList = projEquipDocMapper.findProjEquipDoc(dto);
        return Result.success(equipDocList);
    }

    /**
     * 查询老换新设备字典配置信息
     *
     * @param customInfoId
     * @return
     */
    @Override
    public Result<OldToEquipConfigVO> getOldToEquipConfig(Long customInfoId) {
        OldToEquipConfigVO oldToEquipConfigVO = new OldToEquipConfigVO();
        SysFile sysFileDTO = new SysFile();
        //查询LIS原始串采集操作说明
        sysFileDTO.setFileCode("lis_original_str_remark");
        SysFile file1 = sysFileMapper.selectByFileCodeTopOne(sysFileDTO);
        oldToEquipConfigVO.setOriginalStrRemarkPath(file1.getFilePath());
        //查询LIS原始串采集工具
        sysFileDTO.setFileCode("lis_original_str_tool");
        SysFile file2 = sysFileMapper.selectByFileCodeTopOne(sysFileDTO);
        oldToEquipConfigVO.setOriginalStrToolPath(file2.getFilePath());
        //查询浏览器设置说明
        sysFileDTO.setFileCode("browser_reamrk");
        SysFile file3 = sysFileMapper.selectByFileCodeTopOne(sysFileDTO);
        oldToEquipConfigVO.setBrowserRemarkPath(file3.getFilePath());
        //查询HIS切换工具
        sysFileDTO.setFileCode("his_cut_tool");
        SysFile file4 = sysFileMapper.selectByFileCodeTopOne(sysFileDTO);
        oldToEquipConfigVO.setHisCutToolPath(OBSClientUtils.getTemporaryUrl(file4.getFilePath(), 3600));
        //查询老His的ip和端口号
        ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectById(customInfoId);
        oldToEquipConfigVO.setOldHisIp(projCustomInfo.getIp());
        oldToEquipConfigVO.setOldHisPort(projCustomInfo.getPort());
        return Result.success(oldToEquipConfigVO);
    }

    /**
     * 客户的首期项目上没有Lis、Pacs时特批添加
     *
     * @param customInfoId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveProductFroHisProjectInfo(Long customInfoId) {
        //1、 Lis产品处理
        // 查询没有Lis产品的客户下的首期项目列表
        List<ProjProjectInfo> notHasLisListForProjectInfo = projEquipRecordMapper.selectCustomNotHasLis(customInfoId);
        // 赋值Lis产品到当前项目中 实施产品表、特批产品表
        for (ProjProjectInfo projectInfo : notHasLisListForProjectInfo) {
            // 特批产品表
            ProjSpecialProductRecord projSpecialProductRecord = new ProjSpecialProductRecord();
            projSpecialProductRecord.setSpecialProductRecordId(SnowFlakeUtil.getId());
            projSpecialProductRecord.setSpecialProductId(4073L);
            projSpecialProductRecord.setProjectInfoId(projectInfo.getProjectInfoId());
            projSpecialProductRecord.setProductExcutionStatus(0);
            projSpecialProductRecord.setProductOpenStatus(0);
            projSpecialProductRecord.setProductBuyMode(1);
            projSpecialProductRecord.setOrderInfoId(projectInfo.getOrderInfoId());
            projSpecialProductRecord.setSpecialInfo("设备产品处理，首期项目增加Lis设备");
            projSpecialProductRecord.setIsDeleted(0);
            projSpecialProductRecord.setCreaterId(-1L);
            projSpecialProductRecord.setCreateTime(new Date());
            projSpecialProductRecord.setUpdaterId(-1L);
            projSpecialProductRecord.setUpdateTime(new Date());
            projSpecialProductRecordMapper.insert(projSpecialProductRecord);
            log.info("项目【{}】增加Lis设备=== 特批产品表", projectInfo.getProjectName());
            ProjProductDeliverRecord projProductDeliverRecord = new ProjProductDeliverRecord();
            projProductDeliverRecord.setProductDeliverRecordId(SnowFlakeUtil.getId());
            projProductDeliverRecord.setProductDeliverId(4073L);
            projProductDeliverRecord.setCustomInfoId(projectInfo.getCustomInfoId());
            projProductDeliverRecord.setYyOrderProductId(4073L);
            projProductDeliverRecord.setProjectInfoId(projectInfo.getProjectInfoId());
            projProductDeliverRecord.setIsDeleted(0);
            projProductDeliverRecord.setCreaterId(-1L);
            projProductDeliverRecord.setCreateTime(new Date());
            projProductDeliverRecord.setUpdaterId(-1L);
            projProductDeliverRecord.setUpdateTime(new Date());
            productDeliverRecordMapper.insert(projProductDeliverRecord);
            log.info("项目【{}】增加Lis设备=== 实施产品表", projectInfo.getProjectName());
        }
        List<ProjProjectInfo> notHasPacsListForProjectInfo = projEquipRecordMapper.selectCustomNotHasPacs(customInfoId);
        // 赋值Pacs产品到当前项目中 实施产品表、特批产品表
        for (ProjProjectInfo projectInfo : notHasPacsListForProjectInfo) {
            // 特批产品表
            ProjSpecialProductRecord projSpecialProductRecord = new ProjSpecialProductRecord();
            projSpecialProductRecord.setSpecialProductRecordId(SnowFlakeUtil.getId());
            projSpecialProductRecord.setSpecialProductId(4063L);
            projSpecialProductRecord.setProjectInfoId(projectInfo.getProjectInfoId());
            projSpecialProductRecord.setProductExcutionStatus(0);
            projSpecialProductRecord.setProductOpenStatus(0);
            projSpecialProductRecord.setProductBuyMode(1);
            projSpecialProductRecord.setOrderInfoId(projectInfo.getOrderInfoId());
            projSpecialProductRecord.setSpecialInfo("设备产品处理，首期项目增加Pacs设备");
            projSpecialProductRecord.setIsDeleted(0);
            projSpecialProductRecord.setCreaterId(-1L);
            projSpecialProductRecord.setCreateTime(new Date());
            projSpecialProductRecord.setUpdaterId(-1L);
            projSpecialProductRecord.setUpdateTime(new Date());
            projSpecialProductRecordMapper.insert(projSpecialProductRecord);
            log.info("项目【{}】增加Pacs设备=== 特批产品表", projectInfo.getProjectName());
            ProjProductDeliverRecord projProductDeliverRecord = new ProjProductDeliverRecord();
            projProductDeliverRecord.setProductDeliverRecordId(SnowFlakeUtil.getId());
            projProductDeliverRecord.setProductDeliverId(4063L);
            projProductDeliverRecord.setCustomInfoId(projectInfo.getCustomInfoId());
            projProductDeliverRecord.setYyOrderProductId(4063L);
            projProductDeliverRecord.setProjectInfoId(projectInfo.getProjectInfoId());
            projProductDeliverRecord.setIsDeleted(0);
            projProductDeliverRecord.setCreaterId(-1L);
            projProductDeliverRecord.setCreateTime(new Date());
            projProductDeliverRecord.setUpdaterId(-1L);
            projProductDeliverRecord.setUpdateTime(new Date());
            productDeliverRecordMapper.insert(projProductDeliverRecord);
            log.info("项目【{}】增加Pacs设备=== 实施产品表", projectInfo.getProjectName());
        }
        return Result.success();
    }

    /**
     * 各产品设备自动检测
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> csmAutoCheckEquip(EquipAutoCheckDTO dto) {
        log.info("设备自动检测,======{}", JSONUtil.toJsonStr(dto));
        // 根据云健康设备id、医院id查询设备信息
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                .eq("cloud_hospital_id", dto.getHospitalId())
                .last("limit 1")
        );
        if (ObjectUtil.isEmpty(hospitalInfo)) {
            log.warn("设备自动检测, 未查询到医院信息. dto: {}", JSONUtil.toJsonStr(dto));
            return Result.fail("未查询到医院信息");
        }
        log.info("设备自动检测==根据云健康医院id查询医院信息:{}", JSONUtil.toJsonStr(hospitalInfo));
        Long yyProductId = -1L;
        // 首先根据产品进行处理数据
        switch (dto.getProductCode()) {
            case "Lis":
                Result<String> result = lisEquipAutoCheck(dto, hospitalInfo);
                if (!result.isSuccess()) {
                    return Result.fail(result.getMsg());
                }
                yyProductId = ProductEquipSurveyMenuEnum.LISEQUIP.getYyProductId();
                break;
            case "Pacs":
                Result<String> result1 = pacsEquipAutoCheck(dto, hospitalInfo);
                if (!result1.isSuccess()) {
                    return Result.fail(result1.getMsg());
                }
                yyProductId = ProductEquipSurveyMenuEnum.PACSEQUIP.getYyProductId();
                break;
            case "Aims":
                Result<String> result3 = equipSummaryService.aimsEquipAutoCheck(dto, hospitalInfo);
                if (!result3.isSuccess()) {
                    return Result.fail(result3.getMsg());
                }
                yyProductId = ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId();
                break;
            default:
                return Result.fail("暂不支持该产品自动检测," + dto.getProductCode());
        }
        QueryWrapper<ProjEquipRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hospital_info_id", hospitalInfo.getHospitalInfoId());
        queryWrapper.eq("cloud_equip_id", dto.getCloudEquipId());
        queryWrapper.eq("is_deleted", 0);
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectOne(queryWrapper);
        if (!ObjectUtil.isEmpty(projEquipRecord)) {
            equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), hospitalInfo.getHospitalInfoId(), yyProductId);
        }
        return Result.success();
    }

    @Override
    public Result<String> csmAutoCheckEquipWrapper(EquipAutoCheckDTO dto) {
        try {
            return csmAutoCheckEquip(dto);
        } catch (Throwable e) {
            log.error("设备自动检测接口异常. message: {}, e=", e.getMessage(), e);
            exceptionMessageService.sendToSystemManager(-1L, "设备自动检测接口异常. 请求: " + dto);
            return Result.fail("程序异常.");
        }
    }

    /**
     * 查询老换新ip,端口，下载路径
     *
     * @param customInfoId
     * @return
     */
    @Override
    public Result<OldToEquipConfigVO> getOldPlatformToolConfig(Long customInfoId) {
        OldToEquipConfigVO oldToEquipConfigVO = new OldToEquipConfigVO();
        SysFile sysFileDTO = new SysFile();
        //查询浏览器设置说明
        sysFileDTO.setFileCode("browser_reamrk");
        SysFile file3 = sysFileMapper.selectByFileCodeTopOne(sysFileDTO);
        oldToEquipConfigVO.setBrowserRemarkPath(file3.getFilePath());
        //查询HIS切换工具
        sysFileDTO.setFileCode("his_cut_tool");
        SysFile file4 = sysFileMapper.selectByFileCodeTopOne(sysFileDTO);
        oldToEquipConfigVO.setHisCutToolPath(OBSClientUtils.getTemporaryUrl(file4.getFilePath(), 3600));
        //查询老His的ip和端口号
        ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectById(customInfoId);
        oldToEquipConfigVO.setOldHisIp(projCustomInfo.getIp());
        oldToEquipConfigVO.setOldHisPort(projCustomInfo.getPort());
        return Result.success(oldToEquipConfigVO);
    }

    public Result<String> lisEquipAutoCheck(EquipAutoCheckDTO dto, ProjHospitalInfo hospitalInfo) {
        Long equipRecordId;
        //  检查返回的交付设备id是否为 -1 ，当为-1时 说明云健康、交付设备id不统一。改为根据云健康设备id、医院id查询
        if (dto.getCsmEquipId() == -1L) {
            // 一个云健康设备只会对照一个交付设备 【未对照时 设备id设置为-1】
            List<ProjEquipRecord> projEquipRecords =
                    projEquipRecordMapper.selectList(new QueryWrapper<ProjEquipRecord>()
                            .eq("cloud_equip_id", dto.getCloudEquipId())
                            .eq("hospital_info_id", hospitalInfo.getHospitalInfoId())
                    );
            log.info("设备自动检测==根据云健康设备id查询设备信息:{}", JSONUtil.toJsonStr(projEquipRecords));
            if (CollectionUtil.isNotEmpty(projEquipRecords)) {
                equipRecordId = projEquipRecords.get(0).getEquipRecordId();
                ProjEquipRecordVsLis one = equipRecordVsLisService.getOne(new QueryWrapper<ProjEquipRecordVsLis>()
                        .eq("equip_record_id", equipRecordId)
                );
            } else {
                equipRecordId = -1L;
            }
        } else {
            ProjEquipRecordVsLis lisEquip = equipRecordVsLisService.getById(dto.getCsmEquipId());
            if (ObjectUtil.isEmpty(lisEquip)) {
                return Result.fail("未查询到设备记录信息,请检查设备id是否在交付存在, " + JSONUtil.toJsonStr(dto));
            }
            equipRecordId = lisEquip.getEquipRecordId();
        }
        // 检查是否已经对照了云健康。当未对照时。企业微信发送消息提示客服进行对照。数据先暂存，对照后，自动匹配, 匹配成功后 物理删除该部分数据
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectById(equipRecordId);
        log.info("设备自动检测==根据交付设备id查询设备信息:{}", JSONUtil.toJsonStr(projEquipRecord));
        if ((ObjectUtil.isNotEmpty(projEquipRecord) && ObjectUtil.isEmpty(projEquipRecord.getCloudEquipId())) || ObjectUtil.isEmpty(projEquipRecord)) {
            ProductEquipSurveyMenuEnum surveyMenuEnum = ProductEquipSurveyMenuEnum.LISEQUIP;
            List<ProjProjectInfo> projectInfoList = findAvailableProjectInfo(surveyMenuEnum, hospitalInfo);
            // 先暂存，再发消息
            TmpEquipCheckDetail tmpEquipCheckDetail = new TmpEquipCheckDetail();
            tmpEquipCheckDetail.setTmpEquipCheckVsLisId(SnowFlakeUtil.getId());
            tmpEquipCheckDetail.setCloudEquipId(dto.getCloudEquipId());
            tmpEquipCheckDetail.setCloudHospitalId(dto.getHospitalId());
            tmpEquipCheckDetail.setTestProgress(equipRecordCommonService.transferProgress(dto));
            tmpEquipCheckDetail.setStatus(dto.getEquipmentState());
            tmpEquipCheckDetail.setYyProductId(surveyMenuEnum.getYyProductId());
            tmpEquipCheckDetail.setItemInfo(dto.getItemInfo());
            log.info("设备自动检测==暂存设备检测进度参数:{}", JSONUtil.toJsonStr(tmpEquipCheckDetail));
            int count = tmpEquipCheckDetailMapper.insert(tmpEquipCheckDetail);
            // 发送消息
            log.info(surveyMenuEnum.getName() + "设备自动检测, 增加暂存设备信息. count: {}, detail: {}", count,
                    tmpEquipCheckDetail);
            sendNoticeToProject(projectInfoList, dto.getCloudEquipName(), surveyMenuEnum.getName());
        } else {
            if (equipRecordCommonService.canUpdateEquip(projEquipRecord, ProjProductEnum.LIS.getProductCode())) {
                // 更新检测进度
                ProjEquipRecord projEquipRecord1 = new ProjEquipRecord();
                projEquipRecord1.setEquipRecordId(projEquipRecord.getEquipRecordId());
                projEquipRecord1.setEquipStatus(dto.getEquipmentState());
                projEquipRecord1.setTestProgress(equipRecordCommonService.transferProgress(dto));
                log.info("更新检测状态，检测参数：{}", projEquipRecord1);
                projEquipRecordMapper.updateById(projEquipRecord1);
                // 更新检测明细
                if (ObjectUtil.isNotEmpty(dto.getItemInfo())) {
                    EquipmentStatusDto equipmentStatusDto = new EquipmentStatusDto();
                    equipmentStatusDto.setId(projEquipRecord.getCloudEquipId());
                    equipmentStatusDto.setHospitalId(dto.getHospitalId());
                    equipmentStatusDto.setItemInfo(dto.getItemInfo());
                    batchInsertLisCheck(equipmentStatusDto);
                }
            } else {
                log.warn("lis设备自动检测, 查询到设备状态已检测通过, 不再处理. record: {}, dto: {}, hospitalInfo: {}",
                        projEquipRecord, dto, hospitalInfo);
            }

        }
        return Result.success();
    }

    /**
     * Pacs设备自动检测
     *
     * @param dto
     * @param hospitalInfo
     * @return
     */
    public Result<String> pacsEquipAutoCheck(EquipAutoCheckDTO dto, ProjHospitalInfo hospitalInfo) {
        Long equipRecordId = null;
        //  检查返回的交付设备id是否为 -1 ，当为-1时 说明云健康、交付设备id不统一。改为根据云健康设备id、医院id查询
        if (dto.getCsmEquipId() == -1L) {
            // 一个云健康设备只会对照一个交付设备 【未对照时 设备id设置为-1】
            List<ProjEquipRecord> projEquipRecords =
                    projEquipRecordMapper.selectList(new QueryWrapper<ProjEquipRecord>()
                            .eq("cloud_equip_id", dto.getCloudEquipId())
                            .eq("hospital_info_id", hospitalInfo.getHospitalInfoId())
                    );
            log.info("根据云健康设备id、医院id查询设备记录信息：{}", JSONUtil.toJsonStr(projEquipRecords));
            if (CollectionUtil.isNotEmpty(projEquipRecords)) {
                equipRecordId = projEquipRecords.get(0).getEquipRecordId();
                ProjEquipRecordVsPacs one = equipRecordVsPacsService.getOne(new QueryWrapper<ProjEquipRecordVsPacs>()
                        .eq("equip_record_id", equipRecordId)
                );
                log.info("根据设备记录id查询设备记录vsPacs交付设备信息：{}", JSONUtil.toJsonStr(one));
            } else {
                equipRecordId = -1L;
            }
        } else {
            ProjEquipRecordVsPacs pacsEquip = equipRecordVsPacsService.getById(dto.getCsmEquipId());
            if (ObjectUtil.isEmpty(pacsEquip)) {
                return Result.fail("未查询到设备记录信息,请检查设备id是否在交付存在, " + JSONUtil.toJsonStr(dto));
            }
            equipRecordId = pacsEquip.getEquipRecordId();
        }
        // 检查是否已经对照了云健康。当未对照时。企业微信发送消息提示客服进行对照。对照后，自动匹配, 匹配成功后 物理删除该部分数据
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectById(equipRecordId);
        log.info("根据设备记录id查询设备是否对照了云健康设备信息：{}", JSONUtil.toJsonStr(projEquipRecord));
        if ((ObjectUtil.isNotEmpty(projEquipRecord) && ObjectUtil.isEmpty(projEquipRecord.getCloudEquipId())) || ObjectUtil.isEmpty(projEquipRecord)) {
            ProductEquipSurveyMenuEnum surveyMenuEnum = ProductEquipSurveyMenuEnum.PACSEQUIP;
            List<ProjProjectInfo> projectInfoList = findAvailableProjectInfo(surveyMenuEnum, hospitalInfo);
            // 数据暂存
            TmpEquipCheckDetail tmpEquipCheckDetail = new TmpEquipCheckDetail();
            tmpEquipCheckDetail.setTmpEquipCheckVsLisId(SnowFlakeUtil.getId());
            tmpEquipCheckDetail.setCloudEquipId(dto.getCloudEquipId());
            tmpEquipCheckDetail.setCloudHospitalId(dto.getHospitalId());
            tmpEquipCheckDetail.setStatus(dto.getEquipmentState());
            tmpEquipCheckDetail.setYyProductId(surveyMenuEnum.getYyProductId());
            log.info("暂存设备信息：{}", JSONUtil.toJsonStr(tmpEquipCheckDetail));
            int count = tmpEquipCheckDetailMapper.insert(tmpEquipCheckDetail);
            // 发送消息
            log.info(surveyMenuEnum.getName() + "设备自动检测, 增加暂存设备信息. count: {}, detail: {}", count,
                    tmpEquipCheckDetail);
            sendNoticeToProject(projectInfoList, dto.getCloudEquipName(), surveyMenuEnum.getName());
        } else {
            if (equipRecordCommonService.canUpdateEquip(projEquipRecord, ProjProductEnum.PACS.getProductCode())) {
                // 更新检测状态
                ProjEquipRecord projEquipRecord1 = new ProjEquipRecord();
                projEquipRecord1.setEquipRecordId(projEquipRecord.getEquipRecordId());
                projEquipRecord1.setEquipStatus(dto.getEquipmentState());
                log.info("更新检测状态，检测参数：{}", projEquipRecord1);
                projEquipRecordMapper.updateById(projEquipRecord1);
            } else {
                log.warn("pacs设备自动检测, 查询到设备状态已检测通过, 不再处理. record: {}, dto: {}, hospitalInfo: {}",
                        projEquipRecord, dto, hospitalInfo);
            }

        }
        return Result.success();
    }

    /**
     * 手麻设备自动检测
     *
     * @param dto          请求参数
     * @param hospitalInfo 医院信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> aimsEquipAutoCheck(EquipAutoCheckDTO dto, ProjHospitalInfo hospitalInfo) {
        // 判断状态是否合法
        if (dto.getEquipmentState() != EquipStatusEnum.TEST_FAIL.getCode().intValue()
                && dto.getEquipmentState() != EquipStatusEnum.TEST_PASS.getCode().intValue()) {
            throw new CustomException("检测状态不合法. dto:" + dto);
        }
        Long equipRecordId;
        //  检查返回的交付设备id是否为 -1 ，当为-1时 说明云健康、交付设备id不统一。改为根据云健康设备id、医院id查询
        if (dto.getCsmEquipId() == -1L) {
            // 一个云健康设备只会对照一个交付设备 【未对照时 设备id设置为-1】
            List<ProjEquipRecord> projEquipRecords =
                    projEquipRecordMapper.selectList(new QueryWrapper<ProjEquipRecord>()
                            .eq("cloud_equip_id", dto.getCloudEquipId())
                            .eq("hospital_info_id", hospitalInfo.getHospitalInfoId())
                    );
            log.info("根据云健康设备id、医院id查询设备记录信息：{}", JSONUtil.toJsonStr(projEquipRecords));
            if (CollectionUtil.isNotEmpty(projEquipRecords)) {
                equipRecordId = projEquipRecords.get(0).getEquipRecordId();
                ProjEquipRecordVsAims equipRecordVsAims =
                        equipRecordVsAimsMapper.selectOne(new QueryWrapper<ProjEquipRecordVsAims>().eq(
                                "equip_record_id",
                                equipRecordId));
                log.info("根据设备记录id查询设备记录vsAims交付设备信息：{}", JSONUtil.toJsonStr(equipRecordVsAims));
            } else {
                equipRecordId = -1L;
            }
        } else {
            ProjEquipRecordVsAims equipRecordVsAims = equipRecordVsAimsMapper.selectById(dto.getCsmEquipId());
            if (ObjectUtil.isEmpty(equipRecordVsAims)) {
                log.warn("未查询到设备记录信息,请检查设备id是否在交付存在, " + JSONUtil.toJsonStr(dto));
                return Result.fail("未查询到设备记录信息,请检查设备id是否在交付存在, " + JSONUtil.toJsonStr(dto));
            }
            equipRecordId = equipRecordVsAims.getEquipRecordId();
        }
        // 检查是否已经对照了云健康。当未对照时。企业微信发送消息提示客服进行对照。对照后，自动匹配, 匹配成功后 物理删除该部分数据
        ProjEquipRecord projEquipRecord = projEquipRecordMapper.selectById(equipRecordId);
        log.info("根据设备记录id查询设备是否对照了云健康设备信息：{}", JSONUtil.toJsonStr(projEquipRecord));
        if ((ObjectUtil.isNotEmpty(projEquipRecord) && ObjectUtil.isEmpty(projEquipRecord.getCloudEquipId())) || ObjectUtil.isEmpty(projEquipRecord)) {
            // 查询设备是否存在, 并且是检测未通过状态才会更新, 检测成功不再更新
            TmpEquipCheckDetail checkDetail =
                    tmpEquipCheckDetailMapper.selectOne(new QueryWrapper<TmpEquipCheckDetail>().eq(
                            "cloud_equip_id",
                            dto.getCloudEquipId()).eq("cloud_hospital_id", dto.getHospitalId()));
            if (ObjectUtil.isNotEmpty(checkDetail)) {
                log.info("手麻设备临时表数据已存在. detail: {}", checkDetail);
                // 判断状态
                if (checkDetail.getStatus() != EquipStatusEnum.TEST_PASS.getCode().intValue()) {
                    TmpEquipCheckDetail update = new TmpEquipCheckDetail();
                    update.setTmpEquipCheckVsLisId(checkDetail.getTmpEquipCheckVsLisId());
                    update.setStatus(dto.getEquipmentState());
                    update.setUpdateTime(new Date());
                    int count = tmpEquipCheckDetailMapper.updateById(update);
                    log.info("手麻设备自动检测, 查询到已存在临时数据, 更新. count: {}, update: {}", count, update);
                } else {
                    log.warn("手麻设备自动检测, 查询到临时表设备状态已检测通过, 不再处理. checkDetail: {}, dto: {}, hospitalInfo: {}",
                            checkDetail, dto, hospitalInfo);
                }
            } else {
                ProductEquipSurveyMenuEnum surveyMenuEnum = ProductEquipSurveyMenuEnum.AIMSEQUIP;
                // 查询手麻产品所在项目
                List<ProjProjectInfo> projectInfoList = findAvailableProjectInfo(surveyMenuEnum, hospitalInfo);
                // 根据医院查询在 准备阶段的项目信息
                log.info("手麻设备自动检测, 查询到匹配的项目. infos: {}", JSONUtil.toJsonStr(projectInfoList));
                // 不存在则新增
                TmpEquipCheckDetail tmpEquipCheckDetail = new TmpEquipCheckDetail();
                tmpEquipCheckDetail.setTmpEquipCheckVsLisId(SnowFlakeUtil.getId());
                tmpEquipCheckDetail.setCloudEquipId(dto.getCloudEquipId());
                tmpEquipCheckDetail.setCloudHospitalId(dto.getHospitalId());
                tmpEquipCheckDetail.setStatus(dto.getEquipmentState());
                tmpEquipCheckDetail.setYyProductId(surveyMenuEnum.getYyProductId());
                log.info("暂存设备信息：{}", JSONUtil.toJsonStr(tmpEquipCheckDetail));
                int count = tmpEquipCheckDetailMapper.insert(tmpEquipCheckDetail);
                // 发送消息
                log.info(surveyMenuEnum.getName() + "设备自动检测, 增加暂存设备信息. count: {}, detail: {}", count,
                        tmpEquipCheckDetail);
                sendNoticeToProject(projectInfoList, dto.getCloudEquipName(), surveyMenuEnum.getName());
            }
        } else {
            if (equipRecordCommonService.canUpdateEquip(projEquipRecord, ProjProductEnum.AIMS.getProductCode())) {
                // 更新检测状态
                ProjEquipRecord projEquipRecord1 = new ProjEquipRecord();
                projEquipRecord1.setEquipRecordId(projEquipRecord.getEquipRecordId());
                projEquipRecord1.setEquipStatus(dto.getEquipmentState());
                log.info("更新检测状态，检测参数：{}", projEquipRecord1);
                int count = projEquipRecordMapper.updateById(projEquipRecord1);
                log.info("手麻设备自动检测, 更新设备检测状态信息. count: {}, dto: {}, hospitalInfo: {}", count, dto, hospitalInfo);
            } else {
                log.warn("手麻设备自动检测, 查询到设备状态已检测通过, 不再处理. record: {}, dto: {}, hospitalInfo: {}",
                        projEquipRecord, dto, hospitalInfo);
            }
        }
        return Result.success();
    }

    /**
     * 获取可用的项目信息
     * <p>
     * 改方法用与设备自动化检测, 查找手麻所在项目, 且项目未上线, 但已入驻的项目
     * </p>
     *
     * @param surveyMenuEnum 实施产品枚举, 为了获取产品id和产品名称
     * @param hospitalInfo   设备所在医院
     * @return 可用的项目信息
     */
    public List<ProjProjectInfo> findAvailableProjectInfo(ProductEquipSurveyMenuEnum surveyMenuEnum,
                                                          ProjHospitalInfo hospitalInfo) {
        List<ProjProjectInfo> projectInfoList =
                projProjectInfoMapper.selectByHospital(hospitalInfo.getHospitalInfoId());
        // 未查询到项目
        if (CollUtil.isEmpty(projectInfoList)) {
            log.warn("{}设备自动检测, 未查询到项目, 医院: {}", surveyMenuEnum.getName(), JSONUtil.toJsonStr(hospitalInfo));
            throw new CustomException("未查询到项目");
        }
        List<ProjProductDeliverRecord> deliverRecords =
                productDeliverRecordMapper.selectList(new QueryWrapper<ProjProductDeliverRecord>().eq(
                        "yy_order_product_id", surveyMenuEnum.getYyProductId()).in(
                        "project_info_id",
                        projectInfoList.stream().map(ProjProjectInfo::getProjectInfoId).collect(Collectors.toList())));
        // 未查询到手麻产品
        if (CollUtil.isEmpty(deliverRecords)) {
            log.warn(surveyMenuEnum.getName() + "设备自动检测, 未查询到项目, 医院: {}", JSONUtil.toJsonStr(hospitalInfo));
            throw new CustomException("未查询到" + surveyMenuEnum.getName() + "产品");
        }
        log.info(surveyMenuEnum.getName() + "设备自动检测, 查询到项目. infos: {}", JSONUtil.toJsonStr(projectInfoList));
        // 过滤项目
        List<Long> projectInfoIds =
                deliverRecords.stream().map(ProjProductDeliverRecord::getProjectInfoId).distinct().collect(Collectors.toList());
        // 判断项目状态, 若已上线则不再发送, 过滤存在手麻产品的项目
        projectInfoList =
                projectInfoList.stream().filter(e -> projectInfoIds.stream().anyMatch(f -> e.getProjectInfoId().longValue() == f)).collect(Collectors.toList());
        // 过滤未上线的项目, 项目状态 < 5的, 5,6,7,8 是上线及上线后的状态
        projectInfoList =
                projectInfoList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getProjectDeliverStatus())
                        && e.getProjectDeliverStatus() < ProjectDeliverStatusEnums.ONLINE.getCode()
                        && e.getProjectDeliverStatus() >= ProjectDeliverStatusEnums.SETTLED.getCode()).collect(Collectors.toList());
        if (CollUtil.isEmpty(projectInfoList)) {
            log.warn(surveyMenuEnum.getName() + "设备自动检测, 未查询到匹配的项目, 医院: {}", JSONUtil.toJsonStr(hospitalInfo));
            throw new CustomException("未查询到匹配的项目");
        }
        return projectInfoList;
    }

    /**
     * 发送通知给项目相关人员
     * <p>
     * lis, pacs, 手麻设备通用
     * </p>
     *
     * @param projectInfos   设备所在医院
     * @param cloudEquipName 云健康设备名称
     */
    public void sendNoticeToProject(List<ProjProjectInfo> projectInfos, String cloudEquipName, String productName) {
        List<Long> projectInfoIds =
                projectInfos.stream().map(ProjProjectInfo::getProjectInfoId).collect(Collectors.toList());
        // 查询设备里程碑节点负责人信息
        for (Long projectInfoId : projectInfoIds) {
            List<Long> sysUserIds = new ArrayList<>();
            // 查询项目, 消息发送需要用到项目名称
            ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
            // 获取里程碑信息, 查询设备对接负责人使用
            ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(projectInfoId,
                    MilestoneNodeEnum.PREPARAT_DEVICE.getCode());
            if (ObjectUtil.isNotEmpty(milestoneInfo) && ObjectUtil.isNotEmpty(milestoneInfo.getNodeHeadId())) {
                sysUserIds.add(milestoneInfo.getNodeHeadId());
            } else {
                // 指定项目经理（运维平台反馈单提交的三方接口project_info_id=-1）
                sysUserIds.add(projProjectInfo.getProjectLeaderId());
            }
            // 查询可用的账号信息, 若均不可用发送异常通知
            List<SysUser> sysUserList = sysUserMapper.selectList(new QueryWrapper<SysUser>().in(
                    "sys_user_id", sysUserIds));
            if (CollUtil.isEmpty(sysUserList)) {
                exceptionMessageService.sendToSystemManager(-1L, projProjectInfo.getProjectName()
                        + productName
                        + "设备自动检测-新增设备" + cloudEquipName + ", 通知负责人异常, 未查询到相关负责人, 请及时处理.");
            } else {
                //3. 企业微信发送消息
                MessageParam messageParam = new MessageParam();
                messageParam.setSysUserIds(sysUserIds);
                messageParam.setMessageToCategory(-1);
                messageParam.setMessageTypeId(3001L);
                messageParam.setTitle("设备调试提醒");
                String messageContent = projProjectInfo.getProjectName() + "云健康" + productName + "产品的" + cloudEquipName
                        + "设备已检测到调试结果，请及时在交付平台维护云健康设备与交付设备对照关系！";
                messageParam.setContent(messageContent);
                log.info(productName + "设备自动检测 ， 发送企业微信通知责任人 , {}", JSONUtil.toJsonStr(messageParam));
                sendMessageService.sendMessage(messageParam, true);
            }
        }
    }

    /**
     * 保存设备进度
     *
     * @param equipment
     */
    private void batchInsertLisCheck(EquipmentStatusDto equipment) {
        // 查询医院名称
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq(
                "cloud_hospital_id",
                equipment.getHospitalId()).last("limit 1"));
        //先删除原来保存的设备进度记录, * 注意, 这里的equip_record_vs_lis_id字段保存的是云健康设备id, 并非项目记录lis拓展表主键 *
        equipCheckVsLisMapper.delete(new QueryWrapper<ProjEquipCheckVsLis>()
                .eq("equip_record_vs_lis_id", equipment.getId())
                .eq("cloud_hospital_id", hospitalInfo.getCloudHospitalId())
        );
        //保存接口返回的记录
        List<ItemInfoVO> listData = JSON.parseArray(equipment.getItemInfo(), ItemInfoVO.class);
        for (ItemInfoVO item : listData) {
            ProjEquipCheckVsLis equipCheck = new ProjEquipCheckVsLis();
            BeanUtil.copyProperties(item, equipCheck);
            equipCheck.setEquipCheckVsLisId(SnowFlakeUtil.getId());
            if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                equipCheck.setHospitalName(hospitalInfo.getHospitalName());
            }
            equipCheck.setEquipRecordVsLisId(equipment.getId());
            equipCheck.setCloudHospitalId(equipment.getHospitalId());
            log.info("设备检测进度保存参数:{}", JSONUtil.toJsonStr(equipCheck));
            equipCheckVsLisMapper.insert(equipCheck);
        }
    }

    /**
     * 查询设备操作日志
     *
     * @param equipRecordId
     * @param equipRecordBusinessId
     * @return
     */
    @Override
    public Result<List<ProjEquipRecordLogVO>> findEquipOperateLog(Long equipRecordId, Long equipRecordBusinessId) {
        List<ProjEquipRecordLogVO> equipRecordLogs = Lists.newArrayList();
        LambdaQueryWrapper<ProjEquipRecordLog> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.and(wrapper -> wrapper.eq(ProjEquipRecordLog::getEquipRecordBusinessId, equipRecordBusinessId))
                .or().eq(ProjEquipRecordLog::getEquipRecordBusinessId, equipRecordId)
                .orderByAsc(ProjEquipRecordLog::getOperateTime);
        List<ProjEquipRecordLog> equipLogs = projEquipRecordLogMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(equipLogs)) {
            return Result.success(Lists.newArrayList());
        }
        for (ProjEquipRecordLog equipLog : equipLogs) {
            ProjEquipRecordLogVO projEquipRecordLogVO = new ProjEquipRecordLogVO();
            BeanUtil.copyProperties(equipLog, projEquipRecordLogVO);
            equipRecordLogs.add(projEquipRecordLogVO);
        }
        return Result.success(equipRecordLogs);
    }

    /**
     * 催办
     *
     * @param dto
     * @return
     */
    @Override
    public Result urgeProcessing(UrgeProcessingDTO dto) {
        JSONObject jsonObject = lisAnalyManagerClient.urgeProcessing(dto);
        log.info("催办结果: {}", jsonObject);
        if (!(Boolean) jsonObject.get("success")) {
            throw new CustomException("催办失败 , " + jsonObject.get("message"));
        }
        return Result.success();
    }
}
