package com.msun.csm.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

public class Base64MultipartFile implements MultipartFile {

    private final byte[] content;
    private final String name;
    private final String originalFilename;
    private final String contentType;

    public Base64MultipartFile(byte[] content, String name, String originalFilename, String contentType) {
        this.content = content;
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
    }

    public static MultipartFile base64ToMultipart(String base64Data, String originalFilename, String contentType, Long surId) {
        if (base64Data.contains("data:image/png;base64,")) {
            base64Data = base64Data.replace("data:image/png;base64,", "");
            originalFilename = surId + "报表图片.png";
            contentType = "image/png";
        } else if (base64Data.contains("data:image/jpeg;base64,")) {
            base64Data = base64Data.replace("data:image/jpeg;base64,", "");
            originalFilename = surId + "报表图片.jpeg";
            contentType = "image/jpeg";
        } else if (base64Data.contains("data:image/gif;base64,")) {
            base64Data = base64Data.replace("data:image/gif;base64,", "");
            originalFilename = surId + "报表图片.gif";
            contentType = "image/gif";
        } else if (base64Data.contains("data:image/jpg;base64,")) {
            base64Data = base64Data.replace("data:image/jpg;base64,", "");
            originalFilename = surId + "报表图片.jpg";
            contentType = "image/jpg";

        } else if (base64Data.contains("data:application/pdf;base64,")) {
            base64Data = base64Data.replace("data:application/pdf;base64,", "");
            originalFilename = surId + "报表图片.pdf";
            contentType = "application/pdf";
        } else {
            originalFilename = surId + "报表图片.png";
            contentType = "image/png";
        }
        byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
        return new MockMultipartFile("file", originalFilename, contentType, decodedBytes);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public String getOriginalFilename() {
        return this.originalFilename;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public boolean isEmpty() {
        return this.content == null || this.content.length == 0;
    }

    @Override
    public long getSize() {
        return this.content.length;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return this.content;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(this.content);
    }

    @Override
    public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
        try (InputStream inputStream = getInputStream(); java.io.FileOutputStream outputStream = new java.io.FileOutputStream(dest)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }
}
