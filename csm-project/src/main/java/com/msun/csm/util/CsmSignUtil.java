package com.msun.csm.util;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/3
 */
@Component
@Slf4j
public class CsmSignUtil {

    private static String publicKeyStatic;
    private static String appIdStatic;

    @Value("${project.feign.oldImsp.publicKey}")
    private String publicKey;

    public static void setPublicKey(String publicKey) {
        CsmSignUtil.publicKeyStatic = publicKey;
    }

    @Value("${project.feign.oldImsp.appId}")
    private String appId;

    public static void setAppId(String appId) {
        CsmSignUtil.appIdStatic = appId;
    }

    @PostConstruct
    public void init() {
        setPublicKey(publicKey);
        setAppId(appId);
    }

    public static String getHeader() {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串
        String old = "appid=" + appIdStatic + "&appsecret=" + publicKeyStatic + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, publicKeyStatic);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        String headers = "appid=" + appIdStatic + ";sign=" + sign;
        return headers;
    }


    private static String getHeaderStr(String appId, String publicKey) {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串
        String old = "appid=" + appId + "&appsecret=" + publicKey + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, publicKey);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        return "appid=" + appId + ";sign=" + sign;

    }

    /**
     * 运营header
     */
    private static void getYunyingHeader() {
        String appId = "8B1B19930982B59374A10DEEABF87325";
        String publicKey =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlGLt2sGg1YFDuZbKuY6BLCavO6Pvl9SzqxW529UKp5dOdc9RhcIBaTU2NvTYBkVlb3IHyf388TqQUFQySkNGFn6uKEjHfhj1ImtGG0172gurGnpThBhqGjKzg9QSAWXEZpIwqypNpyUhvn4u0eKUFIdfwl9Y9DLpcbeS9Dd51DGwFSXtgy8vrStZqI3BlvnqOMtD42NiKhv0X2kWVYuT5s9IHyQiaf+NchZMQJbxT15F+2Xs4NKToQwYxQI4XgXfLeV4PgTwectHkKDGSsQWBHHudGhnLCugzLcjFxSGy0x+cFwmKDtRkJq5lSrThsy7zDroczbmjS2/s1qTri997wIDAQAB";
        log.info("运营: {}", getHeaderStr(appId, publicKey));
    }

    /**
     * 运维header
     */
    private static void getYunweiHeader() {
        String appId = "116444BCF4B2805C1C98E29469C3E626";
        String publicKey =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAg+ORtcvav3DyQHZ/Wtet4UaiV1ZFN"
                        + "+KLC7Hw0ZH4Hh4G6pXHNsf7ZkbYK/esviflinOT6wSTNuJZ7w49s10nXqe+MfyaF"
                        + "+2m4BsNPfV4uTkKskmgK7CaoWGNTAD+mv2lxz+4QPvmyJDkxXUtXAROZa7+RVRwe0yn"
                        +
                        "+FgMLTddkRnP3JSgTMo7hfLF2QhQfxJhs5zydyBa3DYdF4q2yb2uSAlouOrU1stF1szalf4ZDx2c28ea3HhLQvnIvhR2fr9wBD611r6PZ7MG4meaYqOOscg+5cpD4bFYaSimtWJ8KnbJKgjy0CXA5f0ISEJtP0dzEvcAC+U/yuNi8E8A7gkwawIDAQAB";
        log.info("运维: {}", getHeaderStr(appId, publicKey));
    }

    /**
     * olo-imsp
     */
    private static void getOldImspHeader() {
        String appId = "5CB4D0F459FE12778DE2F6B86E705FEF";
        String publicKey =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtU5owyrE+9/XrrDaL2O6H7mCvUwC9CpgBPiHemIm49l8Zn6fy+ye"
                        +
                        "/PTPdd+q/5na3csN02oQ9/QFAg/tYhvfi6Ag6hCHqBvGJTyjiq3mk3OxtVi8CE9DerR8O8hreIH0/lTc"
                        +
                        "+784PJ9HYk77MqfCZ482WUPeDrC"
                        +
                        "+NsI4AyJAyEx6Rgl9xYEhREGf5rNyTO9zEgzWhSPjJjz0rGOYkJTcyVdNPQW0a7G6J4734RVRavOPzEPY2g7e"
                        +
                        "+jisuKRJiyTuPee"
                        +
                        "/7TLOyZcgo0XFac5wo8V37915ow8sUkgHpv80ulPNdGmRt4AClJTCQ7nfnnTEtqMZ4j5GZzmcQcAdvhcWywIDAQAB";
        log.info("old-imsp: {}", getHeaderStr(appId, publicKey));
    }

    /**
     * 自动化测试
     */
    private static void getAutoTestHeader() {
        String appId = "D3A6402DAA0A56F840B40E29DD513087";
        String publicKey =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi7+p0CoKlK0U7XSuiEruC0nOge5JJw4QQMinGFr/jtPR6LFyWYvG1"
                        +
                        "+6JGYQTmzTnR9oqLo7tTvrupfVqRdx+gHSlLlVVTMw2CTWGOi4tLR1J5zdq07vpUj/E9eqlL3otyOoyURA9C"
                        +
                        "/JynQvtBHa4gYn6KXvU84HfJSeCk2YDFCfNBZbxofk3GADlPhvqX8HaBYfozB9SqMrHb8Jqbs8Kb5"
                        +
                        "+Lvg66YewHazIS6J/0V+cbVZgLpuJqwAljIhWxVS//a2wL/r9zOMkL9KBsCPSN/jn4PYff"
                        +
                        "+j6bXssr35lXwFBTxDdmjyFYaKeEvvOt9OqjeRz8OQcPYV5pG52rcF0ZjQIDAQAB";
        log.info("自动: {}", getHeaderStr(appId, publicKey));
    }


    /**
     * imsp-api
     */
    private static void getApiHeader() {
        String appId = "391f4d04011c440eb0bde1e29f231678";
        String publicKey =
                "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzJJLFWoTKqkV8XbEBOI4S3XJ+h0P3yQVWuzr6W7aoTuvb/a7Aqd/a1PH6tTtp/bDM92ZYk5Hzw3X0j2bzl4zvYZxuUCgQ9hXizRex7NxK/8G8Kx/nsCd1JM37jWn6G/caPsM68eEzD7sfCPSgANcJZkp17Pjey0st2+4GQf7TYpboQsw8z+WOXEX5bka5cUoLrkgGDNToi6gm3LDxH45mu4VxL0Zm+WjGZ2scTheqji0nv6THXiXY3h6UfjfdPUXE0nPe4yAGZv/yjutcOUjIZoBSIIkw6lKo2oPx0JJTB0omYAUMwPRfzliCAa3V9iy1Cvyv6HfEsSm3V5WWiBWRwIDAQAB";
        log.info("imsp-api: {}", getHeaderStr(appId, publicKey));
    }


    public static void main(String[] args) {
        getYunyingHeader();
        getYunweiHeader();
        getAutoTestHeader();
        getOldImspHeader();
        getApiHeader();
    }
}
