package com.msun.csm.util.excel;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.fastjson.JSON;

/**
 * 负责人下拉选择处理器
 */
@Slf4j
public class ResponsiblePersonDropdownHandler implements SheetWriteHandler {

    private final List<String> options;
    private final String errorMsg;
    private final int firstRow;
    private final int lastRow;
    private final int firstCol;
    private final int lastCol;

    /**
     * @param options  下拉选项
     * @param errorMsg 单元格数据校验不通过时的错误提示信息
     * @param firstRow 区域限制开始行号
     * @param lastRow  区域限制结束行号
     * @param firstCol 区域限制开始列号
     * @param lastCol  区域限制结束列号
     */
    public ResponsiblePersonDropdownHandler(List<String> options, String errorMsg, int firstRow, int lastRow, int firstCol, int lastCol) {
        log.info("Excel 渲染区域限制,options={}", JSON.toJSONString(options));
        this.options = options;
        this.errorMsg = errorMsg;
        this.firstRow = firstRow;
        this.lastRow = lastRow;
        this.firstCol = firstCol;
        this.lastCol = lastCol;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (options == null || options.isEmpty()) {
            log.warn("下拉选项为空，跳过创建");
            return;
        }

        Sheet sheet = writeSheetHolder.getSheet();
        Workbook workbook = writeWorkbookHolder.getWorkbook();

        log.info("创建下拉框，选项数量: {}", options.size());

        try {
            // 当选项数量大于50或总字符数超过255时，使用隐藏Sheet方式
            if (options.size() > 50 || getTotalLength() > 255) {
                createDropdownWithHiddenSheet(workbook, sheet);
            } else {
                createSimpleDropdown(sheet);
            }
        } catch (Exception e) {
            log.error("创建下拉框失败", e);
        }
    }

    /**
     * 创建简单下拉框（选项较少时）
     */
    private void createSimpleDropdown(Sheet sheet) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options.toArray(new String[0]));
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);

        // 这种方式下设置为true展示下拉箭头
        dataValidation.setSuppressDropDownArrow(true);
        dataValidation.setShowErrorBox(true);
        dataValidation.createErrorBox("输入错误", errorMsg);

        sheet.addValidationData(dataValidation);
        log.info("简单下拉框创建成功");
    }

    /**
     * 使用隐藏Sheet创建下拉框（选项较多时）
     */
    private void createDropdownWithHiddenSheet(Workbook workbook, Sheet sheet) {
        // 创建隐藏Sheet
        String hiddenSheetName = "HiddenOptions_" + System.currentTimeMillis();
        Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);

        // 在隐藏Sheet中填入选项数据
        for (int i = 0; i < options.size(); i++) {
            Row row = hiddenSheet.createRow(i);
            Cell cell = row.createCell(0);
            cell.setCellValue(options.get(i));
        }

        // 隐藏Sheet
        int hiddenSheetIndex = workbook.getSheetIndex(hiddenSheet);
        workbook.setSheetHidden(hiddenSheetIndex, true);

        // 创建名称管理器
        Name namedRange = workbook.createName();
        String rangeName = "ResponsiblePersonList_" + System.currentTimeMillis();
        namedRange.setNameName(rangeName);
        namedRange.setRefersToFormula(hiddenSheetName + "!$A$1:$A$" + options.size());

        // 创建数据验证，引用隐藏Sheet
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(rangeName);
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);

        // 这种方式下设置为true展示下拉箭头
        dataValidation.setSuppressDropDownArrow(true);
        dataValidation.setShowErrorBox(true);
        dataValidation.createErrorBox("输入错误", errorMsg);

        sheet.addValidationData(dataValidation);
        log.info("隐藏Sheet下拉框创建成功，选项数量: {}", options.size());
    }

    /**
     * 计算所有选项的总字符长度
     */
    private int getTotalLength() {
        return options.stream().mapToInt(String::length).sum();
    }
}