<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.platform.DocMapper">
    <select id="getNewestProductVersions" resultType="com.msun.csm.dao.entity.platform.NewestProductDocDao">
        SELECT DISTINCT ON (product_name) product_name,
                                          doc_version,
                                          CAST(REGEXP_REPLACE(doc_version, '[^0-9.]', '', 'g') AS TEXT) as doc_version_num
        FROM (SELECT
                     pv.version                    as doc_version,
                     COALESCE(p.subname, p.name)   AS product_name
              FROM platform.project_product_doc d
                       LEFT JOIN platform.project_product_doc_file df ON d.id = df.doc_id
                       LEFT JOIN platform.project_product_doc_type_dictionary dtd ON df.file_type = dtd.id
                       LEFT JOIN platform.product p ON d.product_id = p.id AND p.is_cloud = 1 AND p.del_flag = 0
                       LEFT JOIN platform.product p2
                                 ON d.product_modules_id = p2.id AND p2.is_cloud = 1 AND p2.del_flag = 0 and
                                    p2.level = '2'
                       LEFT JOIN platform.product_versions pv ON d.version_id = pv.id
                       LEFT JOIN platform.sys_user su ON d.create_id = su.user_id

              where df.file_path is not null
                and df.file_path != ''
                and df.file_status is not null
                and df.file_status != '') as tbl
        ORDER BY product_name,
                 CAST(REGEXP_REPLACE(doc_version, '[^0-9.]', '', 'g') AS TEXT) DESC
    </select>

    <select id="getAllOldProductDoc" resultType="com.msun.csm.dao.entity.platform.ProductDocDao">
        select *
        from (SELECT d.id,
        d.create_time,
        pv.version,
        COALESCE(p.subname, p.name) AS product_name,
        COALESCE(p2.subname, p2.name) AS product_modules_name,
        dtd.type_name AS doc_type,
        df.id AS doc_id,
        df.file_path AS doc_path,
        df.file_name AS doc_name,
        su."name" AS proposer,
        df.file_status AS file_status,
        df.count as doc_count
        FROM platform.project_product_doc d
        LEFT JOIN platform.project_product_doc_file df ON d.id = df.doc_id
        LEFT JOIN platform.project_product_doc_type_dictionary dtd ON df.file_type = dtd.id
        LEFT JOIN platform.product p ON d.product_id = p.id AND p.is_cloud = 1 AND p.del_flag = 0
        LEFT JOIN platform.product p2
        ON d.product_modules_id = p2.id AND p2.is_cloud = 1 AND p2.del_flag = 0 and
        p2.level = '2'
        LEFT JOIN platform.product_versions pv ON d.version_id = pv.id
        LEFT JOIN platform.sys_user su ON d.create_id = su.user_id

        where df.file_path is not null
        and df.file_path != ''
        and df.file_status is not null
        and df.file_status != '') as tbl
        <where>
            <if test="productName!=null and productName!=''">
                and product_name = #{productName}
            </if>
            <if test="newestVersion!=null and newestVersion!=''">
                and version != #{newestVersion}
            </if>
        </where>
    </select>

    <delete id="deleteDoc">
        delete from
        platform.project_product_doc
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDocFile">
        delete from
        platform.project_product_doc_file
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateFilePath">
        update platform.project_product_doc_file set file_path = #{filePath} where id=#{id}
    </update>
</mapper>
