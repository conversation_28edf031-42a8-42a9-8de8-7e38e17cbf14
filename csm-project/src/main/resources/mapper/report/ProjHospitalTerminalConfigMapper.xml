<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ProjHospitalTerminalConfigMapper">

    <select id="findDataInfoPage"
            resultType="com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigResp">
        SELECT
            *
        FROM csm.proj_hospital_terminal_config
        where is_deleted = 0
            <if test="projectInfoId != null and projectInfoId != ''">
                AND project_info_id = #{projectInfoId}
            </if>
            <if test="hospitalName != null and hospitalName != ''">
                AND hospital_name = #{hospitalName}
            </if>
            <if test="configMeetFlag != null and configMeetFlag != ''">
                AND config_meet_flag = #{configMeetFlag}
            </if>
            <if test="operatSystem != null and operatSystem != ''">
                AND operat_system = #{operatSystem}
            </if>
            <if test="computerNumber != null and computerNumber != ''">
                AND computer_number = #{computerNumber}
            </if>
             <if test="memory != null and memory != ''">
                AND memory = #{memory}
            </if>
            <if test="pcIp != null and pcIp != ''">
                AND pc_ip LIKE CONCAT('%',#{pcIp},'%')
            </if>
        ORDER BY hospital_name,config_meet_flag , memory


    </select>
</mapper>
