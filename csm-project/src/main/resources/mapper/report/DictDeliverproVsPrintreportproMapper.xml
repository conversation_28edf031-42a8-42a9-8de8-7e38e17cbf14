<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.DictDeliverproVsPrintreportproMapper">

    <select id="selectListByDto"
            resultType="com.msun.csm.model.resp.projreport.DictDeliverproVsPrintreportproResp">
        SELECT
        d.*,
        ss.name as "deliverProductName",
        s.user_name as "createrName",
        u.user_name as "updaterName"
        FROM
        csm.dict_deliverpro_vs_printreportpro d
        left join csm.sys_user s on s.sys_user_id = d.creater_id
        left join csm.sys_user u on u.sys_user_id = d.updater_id
        inner join (
            select
                dpvm.yy_module_id as id,
                psio.product_name || '--' || dpvm.yy_module_name as name,
                dpvm.yy_module_id as order_no
            from
            csm.dict_product_vs_modules dpvm
            inner join (
            select
            yy_product_id,
            product_name
            from
            csm.dict_product
            where
            yy_is_cloud = 1
            and is_deleted = 0
            ) psio on dpvm.yy_product_id = psio.yy_product_id
            where
            dpvm.is_deleted = 0
            union all
            select
            yy_product_id as id,
            product_name as name,
            order_no
            from
            csm.dict_product
            where
            yy_is_cloud = 1
            and is_deleted = 0
        ) ss on ss.id = d.deliver_product_id
        WHERE d.is_deleted = 0
        <if test="deliverProductName != null and deliverProductName != '' ">
            and ss.name like CONCAT(CONCAT('%', #{deliverProductName}), '%')
        </if>
        <if test="printProductName != null and printProductName != '' ">
            and d.print_product_name like CONCAT(CONCAT('%', #{printProductName}), '%')
        </if>
        order by ss.order_no
    </select>
    <select id="findProductAllDeliver" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select id, name
        from (
            select
            dpvm.yy_module_id as id,
            psio.product_name || '--' || dpvm.yy_module_name as name,
            dpvm.yy_module_id as order_no
            from
            csm.dict_product_vs_modules dpvm
            inner join (
            select
            yy_product_id,
            product_name
            from
            csm.dict_product
            where
            yy_is_cloud = 1
            and is_deleted = 0
            ) psio on dpvm.yy_product_id = psio.yy_product_id
            where
            dpvm.is_deleted = 0
            union all
            select
            yy_product_id as id,
            product_name as name,
            order_no
            from
            csm.dict_product
            where
            yy_is_cloud = 1
            and is_deleted = 0
            ) ss
        where 1 = 1
        <if test="id != null and id != '' ">
            and ss.id = #{id}
        </if>
        order by order_no
    </select>
</mapper>
