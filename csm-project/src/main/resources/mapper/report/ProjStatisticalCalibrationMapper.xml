<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ProjStatisticalCalibrationMapper">

    <update id="deleteByMainIds">
        update csm.proj_statistical_calibration set is_deleted = 1 where statistical_report_main_id in
        <foreach collection="mainIds" item="mainId" open="(" separator="," close=")">
            #{mainId}
        </foreach>
    </update>
</mapper>
