<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ReportAuthorizationStatisticsMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        <!--@Table csm.report_authorization_statistics-->
        <id column="report_authorization_statistics_id" jdbcType="BIGINT" property="reportAuthorizationStatisticsId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="custom_info_name" jdbcType="VARCHAR" property="customInfoName"/>
        <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId"/>
        <result column="hospital_info_name" jdbcType="VARCHAR" property="hospitalInfoName"/>
        <result column="published_system_menu_code" jdbcType="VARCHAR" property="publishedSystemMenuCode"/>
        <result column="published_system_code" jdbcType="VARCHAR" property="publishedSystemCode"/>
        <result column="published_system_standard_name" jdbcType="VARCHAR" property="publishedSystemStandardName"/>
        <result column="min_time" jdbcType="DATE" property="minTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        report_authorization_statistics_id, custom_info_id, custom_info_name, hospital_info_id,
        hospital_info_name, published_system_menu_code, published_system_code, published_system_standard_name,
        min_time, is_deleted, creater_id, create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.report_authorization_statistics
        where report_authorization_statistics_id = #{reportAuthorizationStatisticsId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.report_authorization_statistics
        where report_authorization_statistics_id = #{reportAuthorizationStatisticsId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        insert into csm.report_authorization_statistics (report_authorization_statistics_id, custom_info_id,
        custom_info_name, hospital_info_id, hospital_info_name,
        published_system_menu_code, published_system_code,
        published_system_standard_name, min_time, is_deleted,
        creater_id, create_time, updater_id,
        update_time)
        values (#{reportAuthorizationStatisticsId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT},
        #{customInfoName,jdbcType=VARCHAR}, #{hospitalInfoId,jdbcType=BIGINT}, #{hospitalInfoName,jdbcType=VARCHAR},
        #{publishedSystemMenuCode,jdbcType=VARCHAR}, #{publishedSystemCode,jdbcType=VARCHAR},
        #{publishedSystemStandardName,jdbcType=VARCHAR}, #{minTime,jdbcType=DATE}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        insert into csm.report_authorization_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportAuthorizationStatisticsId != null">
                report_authorization_statistics_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="customInfoName != null">
                custom_info_name,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="hospitalInfoName != null">
                hospital_info_name,
            </if>
            <if test="publishedSystemMenuCode != null">
                published_system_menu_code,
            </if>
            <if test="publishedSystemCode != null">
                published_system_code,
            </if>
            <if test="publishedSystemStandardName != null">
                published_system_standard_name,
            </if>
            <if test="minTime != null">
                min_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportAuthorizationStatisticsId != null">
                #{reportAuthorizationStatisticsId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoName != null">
                #{customInfoName,jdbcType=VARCHAR},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoName != null">
                #{hospitalInfoName,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemMenuCode != null">
                #{publishedSystemMenuCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemCode != null">
                #{publishedSystemCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemStandardName != null">
                #{publishedSystemStandardName,jdbcType=VARCHAR},
            </if>
            <if test="minTime != null">
                #{minTime,jdbcType=DATE},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        update csm.report_authorization_statistics
        <set>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoName != null">
                custom_info_name = #{customInfoName,jdbcType=VARCHAR},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoName != null">
                hospital_info_name = #{hospitalInfoName,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemMenuCode != null">
                published_system_menu_code = #{publishedSystemMenuCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemCode != null">
                published_system_code = #{publishedSystemCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemStandardName != null">
                published_system_standard_name = #{publishedSystemStandardName,jdbcType=VARCHAR},
            </if>
            <if test="minTime != null">
                min_time = #{minTime,jdbcType=DATE},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where report_authorization_statistics_id = #{reportAuthorizationStatisticsId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        update csm.report_authorization_statistics
        set custom_info_id = #{customInfoId,jdbcType=BIGINT},
        custom_info_name = #{customInfoName,jdbcType=VARCHAR},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        hospital_info_name = #{hospitalInfoName,jdbcType=VARCHAR},
        published_system_menu_code = #{publishedSystemMenuCode,jdbcType=VARCHAR},
        published_system_code = #{publishedSystemCode,jdbcType=VARCHAR},
        published_system_standard_name = #{publishedSystemStandardName,jdbcType=VARCHAR},
        min_time = #{minTime,jdbcType=DATE},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where report_authorization_statistics_id = #{reportAuthorizationStatisticsId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.report_authorization_statistics
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_info_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.customInfoName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.hospitalInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hospital_info_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.hospitalInfoName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_system_menu_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.publishedSystemMenuCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_system_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.publishedSystemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_system_standard_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.publishedSystemStandardName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.minTime,jdbcType=DATE}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_authorization_statistics_id = #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
                    then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where report_authorization_statistics_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.report_authorization_statistics
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_info_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoName != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.customInfoName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalInfoId != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.hospitalInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_info_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalInfoName != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.hospitalInfoName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="published_system_menu_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishedSystemMenuCode != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.publishedSystemMenuCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="published_system_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishedSystemCode != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.publishedSystemCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="published_system_standard_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publishedSystemStandardName != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.publishedSystemStandardName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="min_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.minTime != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then #{item.minTime,jdbcType=DATE}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when report_authorization_statistics_id =
                        #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where report_authorization_statistics_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.report_authorization_statistics
        (report_authorization_statistics_id, custom_info_id, custom_info_name, hospital_info_id,
        hospital_info_name, published_system_menu_code, published_system_code, published_system_standard_name,
        min_time, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.reportAuthorizationStatisticsId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.customInfoName,jdbcType=VARCHAR}, #{item.hospitalInfoId,jdbcType=BIGINT},
            #{item.hospitalInfoName,jdbcType=VARCHAR}, #{item.publishedSystemMenuCode,jdbcType=VARCHAR},
            #{item.publishedSystemCode,jdbcType=VARCHAR}, #{item.publishedSystemStandardName,jdbcType=VARCHAR},
            #{item.minTime,jdbcType=DATE}, #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        insert into csm.report_authorization_statistics
        (report_authorization_statistics_id, custom_info_id, custom_info_name, hospital_info_id,
        hospital_info_name, published_system_menu_code, published_system_code, published_system_standard_name,
        min_time, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        (#{reportAuthorizationStatisticsId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT},
        #{customInfoName,jdbcType=VARCHAR}, #{hospitalInfoId,jdbcType=BIGINT}, #{hospitalInfoName,jdbcType=VARCHAR},
        #{publishedSystemMenuCode,jdbcType=VARCHAR}, #{publishedSystemCode,jdbcType=VARCHAR},
        #{publishedSystemStandardName,jdbcType=VARCHAR}, #{minTime,jdbcType=DATE}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        report_authorization_statistics_id = #{reportAuthorizationStatisticsId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        custom_info_name = #{customInfoName,jdbcType=VARCHAR},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        hospital_info_name = #{hospitalInfoName,jdbcType=VARCHAR},
        published_system_menu_code = #{publishedSystemMenuCode,jdbcType=VARCHAR},
        published_system_code = #{publishedSystemCode,jdbcType=VARCHAR},
        published_system_standard_name = #{publishedSystemStandardName,jdbcType=VARCHAR},
        min_time = #{minTime,jdbcType=DATE},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        <!--@mbg.generated-->
        insert into csm.report_authorization_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportAuthorizationStatisticsId != null">
                report_authorization_statistics_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="customInfoName != null">
                custom_info_name,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="hospitalInfoName != null">
                hospital_info_name,
            </if>
            <if test="publishedSystemMenuCode != null">
                published_system_menu_code,
            </if>
            <if test="publishedSystemCode != null">
                published_system_code,
            </if>
            <if test="publishedSystemStandardName != null">
                published_system_standard_name,
            </if>
            <if test="minTime != null">
                min_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportAuthorizationStatisticsId != null">
                #{reportAuthorizationStatisticsId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoName != null">
                #{customInfoName,jdbcType=VARCHAR},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoName != null">
                #{hospitalInfoName,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemMenuCode != null">
                #{publishedSystemMenuCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemCode != null">
                #{publishedSystemCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemStandardName != null">
                #{publishedSystemStandardName,jdbcType=VARCHAR},
            </if>
            <if test="minTime != null">
                #{minTime,jdbcType=DATE},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="reportAuthorizationStatisticsId != null">
                report_authorization_statistics_id = #{reportAuthorizationStatisticsId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoName != null">
                custom_info_name = #{customInfoName,jdbcType=VARCHAR},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoName != null">
                hospital_info_name = #{hospitalInfoName,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemMenuCode != null">
                published_system_menu_code = #{publishedSystemMenuCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemCode != null">
                published_system_code = #{publishedSystemCode,jdbcType=VARCHAR},
            </if>
            <if test="publishedSystemStandardName != null">
                published_system_standard_name = #{publishedSystemStandardName,jdbcType=VARCHAR},
            </if>
            <if test="minTime != null">
                min_time = #{minTime,jdbcType=DATE},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="deleteByHospitalIdList">
        update csm.report_authorization_statistics
        set is_deleted = 1
        where hospital_info_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
    </update>
</mapper>
