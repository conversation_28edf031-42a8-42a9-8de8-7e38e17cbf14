<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ReportCustomInfoMapper">
    <resultMap type="com.msun.csm.dao.entity.report.ReportCustomInfo" id="ReportCustomInfoMap">
        <result property="reportCustomInfoId" column="report_custom_info_id" jdbcType="INTEGER"/>
        <result property="customName" column="custom_name" jdbcType="VARCHAR"/>
        <result property="settleInTime" column="settle_in_time" jdbcType="TIMESTAMP"/>
        <result property="acceptTime" column="accept_time" jdbcType="TIMESTAMP"/>
        <result property="durationReduction" column="duration_reduction" jdbcType="VARCHAR"/>
        <result property="customDeliverStatus" column="custom_deliver_status" jdbcType="INTEGER"/>
        <result property="telesalesFlag" column="telesales_flag" jdbcType="INTEGER"/>
        <result property="customInfoId" column="custom_info_id" jdbcType="INTEGER"/>
        <result property="customType" column="custom_type" jdbcType="INTEGER"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="upgradationType" column="upgradation_type" jdbcType="INTEGER"/>
        <result property="yyCustomId" column="yy_custom_id" jdbcType="TIMESTAMP"/>
        <result property="saleCenterId" column="sale_center_id" jdbcType="TIMESTAMP"/>
        <result property="deptId" column="dept_id" jdbcType="TIMESTAMP"/>
        <result property="serviceOrgId" column="service_org_id" jdbcType="TIMESTAMP"/>
        <result property="provinceId" column="province_id" jdbcType="TIMESTAMP"/>
        <result property="ctiyId" column="ctiy_id" jdbcType="TIMESTAMP"/>
        <result property="townId" column="town_id" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="deleteOrCancelDeleteData"
            parameterType="com.msun.csm.model.req.projreport.ReportCustomInfoDeleteReq">
        update csm.report_custom_info set is_deleted = #{isDeleted}
            where report_custom_info_id = #{reportCustomInfoId}
    </update>
    <select id="findReportCustomInfoPage" parameterType="com.msun.csm.model.dto.report.ReportCustomInfoPageDTO"
            resultType="com.msun.csm.model.vo.report.ReportCustomInfoPageVO">
        select
        rci.custom_name as 客户名,
        coalesce(rci.project_deliver_status,'') as 客户状态,
        coalesce(rci.settle_in_time ::text,'')as 入住时间,
        coalesce(rci.accept_time ::text,'')as 验收时间,
        coalesce(rci.duration_reduction ::text,'')as 减免工期天数,
        coalesce(rci.custom_deliver_status ::text,
        '')as 客户状态标识,
        case
        when rci.telesales_flag = 0 then '否'
        when rci.telesales_flag = 1 then '是'
        else '否'
        end 是否电销客户,
        '''' || coalesce(rci.custom_info_id ::text,
        '') as 实施地客户ID,
        case when rci.custom_type=1 then '单体'
        when rci.custom_type=2 then '区域'
        else ''
        end 单体区域标识,
        ''''||rci.project_info_id as 客户首期项目ID,
        coalesce(ppi.project_number,'') as 项目编号,
        coalesce(
        case when ppi.project_deliver_status =1 then '已派工'
        when ppi.project_deliver_status =2 then '已调研'
        when ppi.project_deliver_status =3 then '已入驻'
        when ppi.project_deliver_status =4 then '准备完成'
        end,'') as 项目状态,
        coalesce(ppi.survey_complete_time::text,'') as 调研完成时间,
        coalesce(ppi.settle_in_time::text,'') as 入驻时间,
        coalesce(ppi.pre_complete_time::text,'') as 准备完成时间,
        coalesce(ppi.online_time::text,'') as 上线时间,
        coalesce(ppi.apply_accept_time::text,'') as 提交验收申请时间,
        coalesce(ppi.external_accept_time::text,'') as 外部验收时间,
        case
        when rci.upgradation_type = 1 then '新客户'
        when rci.upgradation_type = 2 then '老换新'

        when rci.upgradation_type = 3 then '老体系'
        else ''
        end 老换新标识
        from
        csm.report_custom_info rci
        left join csm.proj_project_info ppi on
        rci.project_info_id = ppi.project_info_id
        <!--实施地客户信息ID-->
        <if test="customInfoId != null and customInfoId != ''">
            and rci.custom_info_id = #{customInfoId}
        </if>
        <!--项目类型【1：单体；2：区域】-->
        <if test="projectType != null and projectType != ''">
            and rci.custom_type = #{projectType}
        </if>
    </select>

    <select id="selectByCustomInfoAndType" resultMap="ReportCustomInfoMap">
        select *
        from csm.report_custom_info
        where (custom_info_id = #{customInfoId} or custom_name = #{customName})
          and custom_type = #{type}
    </select>
    <select id="findReprotCustomInfos" resultType="com.msun.csm.model.resp.projreport.ReportCustomInfoResp">

        SELECT
        rci.report_custom_info_id as "reportCustomInfoId",
        rci.custom_name AS "customname",
        rci.dept_id as "deptYYId",
        sd.dept_name AS "customDeptName",
        CASE
        WHEN rci.telesales_flag = 1 THEN
        '3电销客户'
        WHEN rci.telesales_flag != 1
        AND rci.custom_type = 1 THEN
        '1单体客户'
        WHEN rci.telesales_flag != 1
        AND rci.custom_type = 2 THEN
        '2区域客户'
        END AS "customTypeName",
        rci.custom_type as "customType",
        rci.upgradation_type ,
        CASE
        WHEN rci.upgradation_type = 1 THEN
        '老换新'
        WHEN rci.upgradation_type = 2 THEN
        '新客户'
        WHEN rci.upgradation_type = 3 THEN
        '老体系'
        ELSE ''
        END "projectTypeName",
        sd1.dept_name AS "implementationDeptName",
        ppi.project_deliver_status as "projectDeliverStatus",
        CASE
        WHEN ppi.project_deliver_status = 1 THEN
        '已派工'
        WHEN ppi.project_deliver_status = 2 THEN
        '已调研'
        WHEN ppi.project_deliver_status = 3 THEN
        '已入驻'
        WHEN ppi.project_deliver_status = 4 THEN
        '准备完成'
        WHEN ppi.project_deliver_status = 5 THEN
        '已上线'
        WHEN ppi.project_deliver_status = 6 THEN
        '已验收'
        WHEN ppi.project_deliver_status = 7 THEN
        '已启动'
        END "实施进度",
        ppi.work_time AS "worktime",
        ppi.settle_in_time as "settleInTime",
        ppi.online_time AS "onlineTime",
        ppi.external_accept_time AS "externalAcceptTime",
        ppi.apply_accept_time as "applyAcceptTime",
        ppi.accept_time AS "acceptTime",
        sd2.dept_name AS "saleDeptName",
        case when rci.custom_deliver_status = 2 then 2
        when rci.custom_deliver_status = 3 then 3
        else 1 end AS "customDeliverStatus",
        rci.custom_deliver_status AS "customDeliverStatusId",
        coalesce(rci.online_before_duration, 0) AS "onlineBeforeDuration",
        coalesce(rci.online_after_duration, 0) AS "onlineAfterDuration",
        us.user_name
        FROM csm.report_custom_info rci
        LEFT JOIN csm.sys_dept sd ON rci.dept_id = sd.dept_yunying_id
        LEFT JOIN csm.dict_province dp ON rci.province_id = dp.dict_province_id
        LEFT JOIN csm.dict_city dc ON rci.ctiy_id = dc.dict_city_id
        LEFT JOIN csm.dict_town dt ON rci.town_id = dt.dict_town_id
        LEFT JOIN csm.sys_dept sd1 ON sd1.dept_yunying_id = rci.service_org_id
        LEFT JOIN csm.proj_project_info ppi ON ppi.project_info_id = rci.project_info_id
        LEFT JOIN csm.sys_user us ON ppi.project_leader_id = us.sys_user_id
        LEFT JOIN csm.sys_dept sd2 ON rci.sale_center_id = sd2.dept_yunying_id
        WHERE rci.is_deleted = 0
        <if test="customType != null and customType != 0">
            and rci.custom_type=#{customType}
        </if>
        <if test="customName != null and customName != ''">
            and rci.custom_name like concat('%', #{customName}, '%')
        </if>
        <if test="onlinetimeBegin != null">
            and ppi.online_time >= #{onlinetimeBegin}
        </if>
        <if test="onlinetimeEnd != null">
            and ppi.online_time &lt;= #{onlinetimeEnd}
        </if>
        <if test="worktimeBegin != null">
            and ppi.work_time >= #{worktimeBegin}
        </if>
        <if test="worktimeEnd != null">
            and ppi.work_time &lt;= #{worktimeEnd}
        </if>
        <if test="upgradationTypeList != null">
            and rci.upgradation_type in
            <foreach collection="upgradationTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 1">
            and ( rci.custom_deliver_status is null or rci.custom_deliver_status not in (2,3,4,5) )
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 2">
            and rci.custom_deliver_status = 2
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 3">
            and rci.custom_deliver_status = 3
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 4">
            and rci.custom_deliver_status = 4
        </if>
        <if test="onlineType != null and onlineType == 1 ">
            and ppi.online_time is not null
            and rci.upgradation_type != 3
        </if>
        <if test="onlineType != null and onlineType == 0 ">
            and ppi.online_time is null
            and rci.upgradation_type != 3
        </if>
        <if test="deptName != null and deptName != '' and deptName != '合计'">
            and sd2.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="customDeptName != null and customDeptName != ''">
            and sd2.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="projectDeliverStatus != null ">
            and ppi.project_deliver_status =#{projectDeliverStatus}
        </if>
        <if test="lastApplyAcceptTimeBegin != null">
            and ppi.apply_accept_time >= #{lastApplyAcceptTimeBegin}
        </if>
        <if test="lastApplyAcceptTimeEnd != null">
            and ppi.apply_accept_time &lt;= #{lastApplyAcceptTimeEnd}
        </if>
        ORDER BY work_time DESC
    </select>

    <select id="findReportCustomInfoPageNew" resultType="com.msun.csm.model.vo.report.ReportCustomInfoPageVO">
        select a.report_custom_info_id        ,
        a.custom_name                  ,
        case when a.project_info_id is not null then ppi.settle_in_time else a.settle_in_time end      settle_in_time ,
        case when a.project_info_id is not null then ppi.accept_time else a.accept_time end  accept_time                  ,
        a.duration_reduction           ,
        a.custom_deliver_status        ,
        a.telesales_flag               ,
        a.custom_info_id               ,
        a.custom_type                  ,
        a.project_info_id              ,
        a.creater_id                   ,
        a.create_time                  ,
        a.updater_id                   ,
        a.update_time                  ,
        a.is_deleted                   ,
        a.upgradation_type             ,
        case when a.project_info_id is not null then
        (
        case when ppi.project_deliver_status = 1 then '已派工'
        when ppi.project_deliver_status = 2 then '已调研'
        when ppi.project_deliver_status = 3 then '已入驻'
        when ppi.project_deliver_status = 4 then '准备完成'
        when ppi.project_deliver_status = 5 then '已上线'
        when ppi.project_deliver_status = 6 then '已验收'
        when ppi.project_deliver_status = 7 then '已启动'
        when ppi.project_deliver_status = 8 then '申请验收'
        when ppi.project_deliver_status = 11 then '一次验收通过'
        end
        )
        else a.project_deliver_status end  project_deliver_status ,
        case when a.project_info_id is not null then ppi.apply_accept_time else a.last_apply_accept_time end  last_apply_accept_time       ,
        a.yy_custom_id                 ,
        a.sale_center_id               ,
        a.dept_id                      ,
        a.service_org_id               ,
        a.province_id                  ,
        a.ctiy_id                      ,
        a.town_id                      ,
        a.remark                       ,
        a.online_before_duration       ,
        a.online_after_duration        ,
        case when a.project_info_id is not null then ppi.online_time else a.online_time end online_time   ,
        case when a.project_info_id is not null then ppi.external_accept_time else a.out_accept_time end out_accept_time              ,
        a.plan_online_time             ,
        case when a.project_info_id is not null then ppi.work_time else a.work_time end  work_time                    ,
        case when a.project_info_id is not null then ppi.project_deliver_status else a.project_deliver_status_num end  project_deliver_status_num   ,
        case when a.custom_info_id is null or a.custom_info_id = '-1' then
        0  else 1
        end "customSourceFlag",
        case when a.project_info_id is null or a.project_info_id = '-1' then
        0  else 1
        end "bindProjectFlag",
        case when a.telesales_flag = 1 then
        '电销'
        when a.custom_type = 1 then
        '单体'
        when a.custom_type = 2 then
        '区域'
        end "customTypeName",
        case when a.upgradation_type = 1 then
        '老换新'
        when a.upgradation_type = 2 then
        '新客户'
        when a.upgradation_type = 3 then
        '老体系'
        end "upgradationTypeName",
        a.custom_name
        AS "customTypeName",
        sd.dept_name AS "deptName",
        sd1.dept_name AS "serviceOrgName",
        sd2.dept_name AS "saleDeptName",
        us.user_name as "customerManagerName",
        case when a.project_info_id is not null and ppi.external_accept_time is not null then
        EXTRACT(DAY FROM AGE(ppi.accept_time, ppi.external_accept_time)) + 1 -
        ( case when a.duration_reduction is not null and a.duration_reduction!= ''  then a.duration_reduction::numeric else 0 end )
        when a.project_info_id is null and  a.out_accept_time is not null then
        EXTRACT(DAY FROM AGE(a.accept_time, a.out_accept_time)) + 1 -
        ( case when a.duration_reduction is not null and a.duration_reduction!= ''  then a.duration_reduction::numeric else 0 end )

        end as "workDuration"
        from csm.report_custom_info a
        left join csm.sys_dept sd on a.dept_id = sd.dept_yunying_id
        left join csm.sys_dept sd1 on sd1.dept_yunying_id = a.service_org_id
        left join csm.sys_dept sd2 on a.sale_center_id = sd2.dept_yunying_id
        left join csm.proj_project_info ppi on ppi.project_info_id = a.project_info_id
        left join csm.sys_user us on ppi.project_leader_id = us.sys_user_id
        where 1=1
          <if test="detailType != null and detailType == 1">
             and (case when a.project_info_id is not null then ppi.online_time else a.online_time end) is not null
          </if>
        <if test="isDeleted != null">
            and a.is_deleted = #{isDeleted}
        </if>
        <if test="customOrDeptName != null and customOrDeptName != ''">
            and (a.custom_name like concat('%', #{customOrDeptName}, '%')
            or sd1.dept_name like concat('%', #{customOrDeptName}, '%')
            )
        </if>
        <if test="upgradationType != null and upgradationType != 0">
            and a.upgradation_type = #{upgradationType}
        </if>
        <if test="customType != null and customType != 3 ">
            and a.custom_type = #{customType}
            and a.telesales_flag = 0
        </if>
        <if test="customType != null and customType == 3">
            and a.telesales_flag = 1
        </if>
        <if test="projectDeliverStatusNum != null">
            and (case when a.project_info_id is not null then ppi.project_deliver_status else a.project_deliver_status_num end) in
          <foreach collection="projectDeliverStatusNum" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deptId != null and deptId != 0">
            and a.dept_id = #{deptId}
        </if>
        <if test="saleCenterId != null and saleCenterId != 0">
            and a.sale_center_id =  #{saleCenterId}
        </if>
        <if test="dateType != null and dateType == 1">
            <if test="selectStartTime != null">
                and (case when a.project_info_id is not null then ppi.work_time else a.work_time end)  &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null">
                and (case when a.project_info_id is not null then ppi.work_time else a.work_time end)  &lt;= #{selectEndTime}
            </if>

        </if>
        <if test="dateType != null and dateType == 2">
            <if test="selectStartTime != null">
                and (case when a.project_info_id is not null then ppi.settle_in_time else a.settle_in_time end)  &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null">
                and  (case when a.project_info_id is not null then ppi.settle_in_time else a.settle_in_time end)  &lt;= #{selectEndTime}
            </if>
        </if>
        <if test="dateType != null and dateType == 3">
            <if test="selectStartTime != null">
                and  (case when a.project_info_id is not null then ppi.online_time else a.online_time end)  &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null">
                and (case when a.project_info_id is not null then ppi.online_time else a.online_time end) &lt;= #{selectEndTime}
            </if>

        </if>
        <if test="dateType != null and dateType == 4">
            <if test="selectStartTime != null">
                and (case when a.project_info_id is not null then ppi.accept_time else a.accept_time end) &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null">
                and (case when a.project_info_id is not null then ppi.accept_time else a.accept_time end) &lt;= #{selectEndTime}
            </if>
        </if>

    </select>
    <select id="getProjectInfoById" resultType="com.msun.csm.model.resp.projreport.ReportCustomInfoProjectInfoResp"
            parameterType="java.lang.Long">
        select
            ppi.project_info_id,
            ppi.custom_info_id,
            ppi.project_deliver_status "projectDeliverStatusNum",
            ppi.project_team_id "serviceOrgId",
            pci.custom_dept_id "deptId",
            ppi.work_time,
            ppi.settle_in_time "settleInTime",
            ppi.online_time "onlineTime",
            ppi.accept_time,
            ppi.external_accept_time "outAcceptTime"
        from
            csm.proj_project_info ppi
                inner join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        where ppi.is_deleted =0
          and ppi.project_info_id  = #{projectInfoId}


    </select>
    <!-- 最后提验收申请的项目的
       工期计算： （提验收时间-入住-减免工期）/ 已提验收项目数
     -->
    <select id="findCloudProjectData" resultType="com.msun.csm.model.vo.report.ReportCustomInfoStatisticsVO"
            parameterType="com.msun.csm.model.dto.report.ReportCustomInfoStaticStatisticsDTO">
        select
            rci.dept_id ,
            sd.dept_name "deptName",
            count(*) as "onlineProjectNum",
        sum(
        case when rci.project_info_id is not null and ppi.apply_accept_time is not null then 1
            when rci.project_info_id is not null and rci.last_apply_accept_time is not null then 1
            else 0 end
        ) as nbnnn,
        ROUND (
            (sum((EXTRACT(DAY FROM AGE(
                (case when rci.project_info_id is not null then ppi.apply_accept_time else rci.last_apply_accept_time end),
                (case when rci.project_info_id is not null then ppi.settle_in_time else rci.settle_in_time end) )) + 1 -
                ( case when rci.duration_reduction is not null and rci.duration_reduction != '' then rci.duration_reduction ::numeric else 0 end )
            ))/ sum(
                case when rci.project_info_id is not null and ppi.apply_accept_time is not null then 1
                when rci.project_info_id is not null and rci.last_apply_accept_time is not null then 1
                else 0 end
            )
            )::numeric, 2
        )as "averageDuration"
        from
            csm.report_custom_info rci
                inner join csm.sys_dept sd on rci.dept_id = sd.dept_yunying_id
            left join csm.proj_project_info ppi on ppi.project_info_id = rci.project_info_id and ppi.is_deleted = 0
        where
            rci.is_deleted = 0
          and rci.online_time is not null
        <if test="customType != null and customType != 3 ">
            and rci.custom_type = #{customType}
            and rci.telesales_flag = 0
        </if>
        <if test="customType != null and customType == 3">
            and rci.telesales_flag = 1
        </if>
        <if test="upgradationType != null">
            and rci.upgradation_type = #{upgradationType}
        </if>
        <if test="selectStartTime != null">
            an (case when rci.project_info_id is not null then ppi.online_time else rci.online_time end) &gt;= #{selectStartTime}
        </if>
        <if test="selectEndTime != null">
            and (case when rci.project_info_id is not null then ppi.online_time else rci.online_time end) &lt;= #{selectEndTime}
        </if>
        group by
            rci.dept_id,
            sd.dept_name
    </select>
    <select id="findKeyFocusProjectsData" resultType="com.msun.csm.model.vo.report.ReportCustomInfoStatisticsFocusVO"
            parameterType="com.msun.csm.model.dto.report.ReportCustomInfoStaticStatisticsDTO">
        select
            rci.dept_id ,
            sd.dept_name "deptName",
            rci.custom_name "customName",
        case when ppi.project_info_id is not null then
            (
            case when ppi.project_deliver_status = 1 then '已派工'
            when ppi.project_deliver_status = 2 then '已调研'
            when ppi.project_deliver_status = 3 then '已入驻'
            when ppi.project_deliver_status = 4 then '准备完成'
            when ppi.project_deliver_status = 5 then '已上线'
            when ppi.project_deliver_status = 6 then '已验收'
            when ppi.project_deliver_status = 7 then '已启动'
            when ppi.project_deliver_status = 8 then '申请验收'
            when ppi.project_deliver_status = 11 then '一次验收通过'
            end
            )
        else rci.project_deliver_status end  "projectDeliverStatus",
            case when ppi.project_info_id is not null then ppi.project_deliver_status
                else rci.project_deliver_status_num  end "projectDeliverStatusNum",
            rci.custom_type "customType",
            rci.report_custom_info_id "reportCustomInfoId",
            rci.upgradation_type "upgradationType",
        case when ppi.project_info_id is not null then ppi.settle_in_time else rci.settle_in_time end   "settleInTime",
        case when ppi.project_info_id is not null then ppi.online_time else rci.online_time end "onlineTime",
            case when rci.telesales_flag = 1 then
            '电销'
            when rci.custom_type = 1 then
            '单体'
            when rci.custom_type = 2 then
            '区域'
            end "customTypeName",
            case when rci.upgradation_type = 1 then
            '老换新'
            when rci.upgradation_type = 2 then
            '新客户'
            when rci.upgradation_type = 3 then
            '老体系'
            end "upgradationTypeName",
            case when ppi.project_info_id is not null then ppi.settle_in_time else rci.settle_in_time end AS "settleInTime",
        <if test="projectStatus != null and projectStatus == 0 ">
            case when ppi.project_info_id is not null then ppi.online_time else rci.online_time end as "applyAcceptTime"
        </if>
        <if test="projectStatus != null and projectStatus == 1 ">
            case when ppi.project_info_id is not null then ppi.apply_accept_time else rci.last_apply_accept_time end as "applyAcceptTime"
        </if>

        from
            csm.report_custom_info rci
                inner join csm.sys_dept sd on rci.dept_id = sd.dept_yunying_id
           left join csm.proj_project_info ppi on ppi.project_info_id = rci.project_info_id and ppi.is_deleted = 0
          where rci.is_deleted = 0
        <if test="projectStatus != null and projectStatus == 0 ">
            and (case when ppi.project_info_id is not null then ppi.settle_in_time else rci.settle_in_time end) is not null
            and (case when ppi.project_info_id is not null then ppi.online_time else rci.online_time end) is null
            and (case when ppi.project_info_id is not null then ppi.project_deliver_status
            else rci.project_deliver_status_num  end) in (3,4)
        </if>
        <if test="projectStatus != null and projectStatus == 1 ">
            and (case when ppi.project_info_id is not null then ppi.online_time else rci.online_time end) is not null
            and (case when ppi.project_info_id is not null then ppi.accept_time else rci.accept_time end) is null
            and (case when ppi.project_info_id is not null then ppi.project_deliver_status
            else rci.project_deliver_status_num  end) in (5,8)
        </if>

    </select>

    <update id="updateByProjectInfoId">
        update csm.report_custom_info
        set updater_id = #{updaterId},
        <if test="lastApplyAcceptTime != null">
            last_apply_accept_time = #{lastApplyAcceptTime},
        </if>
        <if test="settleInTime != null">
            settle_in_time = #{settleInTime},
        </if>
        update_time = #{updateTime}
        where project_info_id = #{projectInfoId}
    </update>

    <update id="updateByYYCustom">
        update csm.report_custom_info
        set update_time    = #{updateTime},
            sale_center_id = #{saleCenterId},
            dept_id        = #{deptId},
            service_org_id = #{serviceOrgId},
            custom_name    = #{customName}
        where yy_custom_id = #{yyCustomId}
    </update>
    <update id="updateComparData">
        update csm.report_custom_info e
        set dept_id = s.compar_customer_dept_id
        from csm.tmp_team_dept_compar s
        where e.dept_id = s.custom_dept_id
          and s.is_deleted = 0
    </update>
    <update id="updateByProjectInfoData">
        update csm.report_custom_info
        set upgradation_type = 1
        where project_info_id = #{projectInfoId}
          and upgradation_type = 3

    </update>
    <update id="updateByProjectInfoDetailData" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        update csm.report_custom_info
        set online_time = #{onlineTime},
            out_accept_time =  #{externalAcceptTime},
            work_time = #{workTime} ,
            project_deliver_status_num = #{projectDeliverStatus},
            settle_in_time = #{settleInTime}
        where project_info_id = #{projectInfoId}
    </update>
</mapper>
