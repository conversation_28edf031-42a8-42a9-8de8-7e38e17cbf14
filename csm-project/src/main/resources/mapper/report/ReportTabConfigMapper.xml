<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ReportTabConfigMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        <!--@Table csm.report_tab_config-->
        <id column="report_tab_config_id" jdbcType="BIGINT" property="reportTabConfigId"/>
        <result column="report_tab_config_name" jdbcType="VARCHAR" property="reportTabConfigName"/>
        <result column="report_tab_config_url" jdbcType="VARCHAR" property="reportTabConfigUrl"/>
        <result column="menu_path" jdbcType="VARCHAR" property="menuPath"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        report_tab_config_id, report_tab_config_name, report_tab_config_url, menu_path, sort,
        creater_id, create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.report_tab_config
        where report_tab_config_id = #{reportTabConfigId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.report_tab_config
        where report_tab_config_id = #{reportTabConfigId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        insert into csm.report_tab_config (report_tab_config_id, report_tab_config_name,
        report_tab_config_url, menu_path, sort,
        creater_id, create_time, updater_id,
        update_time, is_deleted)
        values (#{reportTabConfigId,jdbcType=BIGINT}, #{reportTabConfigName,jdbcType=VARCHAR},
        #{reportTabConfigUrl,jdbcType=VARCHAR}, #{menuPath,jdbcType=VARCHAR}, #{sort,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        insert into csm.report_tab_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportTabConfigId != null">
                report_tab_config_id,
            </if>
            <if test="reportTabConfigName != null">
                report_tab_config_name,
            </if>
            <if test="reportTabConfigUrl != null">
                report_tab_config_url,
            </if>
            <if test="menuPath != null">
                menu_path,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportTabConfigId != null">
                #{reportTabConfigId,jdbcType=BIGINT},
            </if>
            <if test="reportTabConfigName != null">
                #{reportTabConfigName,jdbcType=VARCHAR},
            </if>
            <if test="reportTabConfigUrl != null">
                #{reportTabConfigUrl,jdbcType=VARCHAR},
            </if>
            <if test="menuPath != null">
                #{menuPath,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        update csm.report_tab_config
        <set>
            <if test="reportTabConfigName != null">
                report_tab_config_name = #{reportTabConfigName,jdbcType=VARCHAR},
            </if>
            <if test="reportTabConfigUrl != null">
                report_tab_config_url = #{reportTabConfigUrl,jdbcType=VARCHAR},
            </if>
            <if test="menuPath != null">
                menu_path = #{menuPath,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where report_tab_config_id = #{reportTabConfigId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        update csm.report_tab_config
        set report_tab_config_name = #{reportTabConfigName,jdbcType=VARCHAR},
        report_tab_config_url = #{reportTabConfigUrl,jdbcType=VARCHAR},
        menu_path = #{menuPath,jdbcType=VARCHAR},
        sort = #{sort,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where report_tab_config_id = #{reportTabConfigId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.report_tab_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="report_tab_config_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.reportTabConfigName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="report_tab_config_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.reportTabConfigUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="menu_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.menuPath,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.sort,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where report_tab_config_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.reportTabConfigId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.report_tab_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="report_tab_config_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.reportTabConfigName != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.reportTabConfigName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="report_tab_config_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.reportTabConfigUrl != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.reportTabConfigUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="menu_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.menuPath != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.menuPath,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sort != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.sort,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when report_tab_config_id = #{item.reportTabConfigId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where report_tab_config_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.reportTabConfigId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.report_tab_config
        (report_tab_config_id, report_tab_config_name, report_tab_config_url, menu_path,
        sort, creater_id, create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.reportTabConfigId,jdbcType=BIGINT}, #{item.reportTabConfigName,jdbcType=VARCHAR},
            #{item.reportTabConfigUrl,jdbcType=VARCHAR}, #{item.menuPath,jdbcType=VARCHAR},
            #{item.sort,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        insert into csm.report_tab_config
        (report_tab_config_id, report_tab_config_name, report_tab_config_url, menu_path,
        sort, creater_id, create_time, updater_id, update_time, is_deleted)
        values
        (#{reportTabConfigId,jdbcType=BIGINT}, #{reportTabConfigName,jdbcType=VARCHAR},
        #{reportTabConfigUrl,jdbcType=VARCHAR},
        #{menuPath,jdbcType=VARCHAR}, #{sort,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        report_tab_config_id = #{reportTabConfigId,jdbcType=BIGINT},
        report_tab_config_name = #{reportTabConfigName,jdbcType=VARCHAR},
        report_tab_config_url = #{reportTabConfigUrl,jdbcType=VARCHAR},
        menu_path = #{menuPath,jdbcType=VARCHAR},
        sort = #{sort,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.report.ReportTabConfig">
        <!--@mbg.generated-->
        insert into csm.report_tab_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportTabConfigId != null">
                report_tab_config_id,
            </if>
            <if test="reportTabConfigName != null">
                report_tab_config_name,
            </if>
            <if test="reportTabConfigUrl != null">
                report_tab_config_url,
            </if>
            <if test="menuPath != null">
                menu_path,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportTabConfigId != null">
                #{reportTabConfigId,jdbcType=BIGINT},
            </if>
            <if test="reportTabConfigName != null">
                #{reportTabConfigName,jdbcType=VARCHAR},
            </if>
            <if test="reportTabConfigUrl != null">
                #{reportTabConfigUrl,jdbcType=VARCHAR},
            </if>
            <if test="menuPath != null">
                #{menuPath,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="reportTabConfigId != null">
                report_tab_config_id = #{reportTabConfigId,jdbcType=BIGINT},
            </if>
            <if test="reportTabConfigName != null">
                report_tab_config_name = #{reportTabConfigName,jdbcType=VARCHAR},
            </if>
            <if test="reportTabConfigUrl != null">
                report_tab_config_url = #{reportTabConfigUrl,jdbcType=VARCHAR},
            </if>
            <if test="menuPath != null">
                menu_path = #{menuPath,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="getReportTabConfigByPath" resultType="com.msun.csm.dao.entity.report.ReportTabConfig">
        select
        <include refid="Base_Column_List"/>
        from csm.report_tab_config
        where menu_path = #{menuPath}
          and is_deleted = 0
        order by sort
    </select>
</mapper>
