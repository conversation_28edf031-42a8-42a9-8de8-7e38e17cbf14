<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.DictReportFrequencyMapper">

    <select id="queryFrequencyData" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select report_frequency_id as id,
               report_frequency_name as "name"
        from csm.dict_report_frequency
        where is_deleted = 0
        order by order_no, report_frequency_id
    </select>
    <select id="queryStausData" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select report_statis_status_id as id,
               report_statis_status_name as "name"
        from csm.dict_report_statis_status
        where is_deleted = 0
        order by order_no, report_statis_status_id
    </select>
</mapper>
