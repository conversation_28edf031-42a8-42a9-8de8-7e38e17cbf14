<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ConfigProductionMethodMapper">

    <select id="selectListByProjectInfoId"
            resultType="com.msun.csm.model.resp.statis.ConfigProductionMethodResp">
        select
            production_method_id as id,
            production_method_name as "name",
            enable_flag,
            order_no
        from csm.config_production_method
        where   is_deleted = 0
        order by order_no,production_method_id
    </select>
</mapper>
