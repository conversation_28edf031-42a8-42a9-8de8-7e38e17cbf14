<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.DictPaperSizeMapper">

    <select id="getPaperSizeList" resultType="com.msun.csm.common.model.BaseIdCodeNameResp">
        SELECT  max(paper_size_id) as id,
                paper_size_name as "name",
                paper_size_name as "code" FROM csm.dict_paper_size x
        WHERE is_recommend = '是'
        group by paper_size_name
        order by paper_size_name desc
    </select>
</mapper>
