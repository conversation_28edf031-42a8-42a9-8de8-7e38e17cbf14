<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ConfigCustomBackendLimitMapper">

    <select id="queryProjectData" resultType="com.msun.csm.model.resp.custombackendlimit.QueryProjectDataResp">
        select c.project_info_id,c.open_flag,c.create_time,
        c1.custom_name as "customInfoName",
        c2.project_number,
        c2.project_deliver_status,
        c4.sys_user_id as "backendProjectManagerId",
        c4.user_name as "backendProjectManagerName",
        max(case when c5.open_type = 1 then c5.open_flag else 0 end) as "printOpenFlag",
        max(case when c5.open_type = 2 then c5.open_flag else 0 end) as "reportOpenFlag",
        max(case when c5.open_type = 3 then c5.open_flag else 0 end) as "hulidanyuanOpenFlag",
        max(case when c5.open_type = 4 then c5.open_flag else 0 end) as "aimsOpenFlag",
        max(case when c5.open_type = 5 then c5.open_flag else 0 end) as "icuisnewOpenFlag",
        max(case when c5.open_type = 6 then c5.open_flag else 0 end) as "emisOpenFlag",
        max(case when c5.open_type = 7 then c5.open_flag else 0 end) as "interfaceOpenFlag",
        max(case when c5.open_type = 8 then c5.open_flag else 0 end) as "medicalOpenFlag",
        max(case when c5.open_type = 9 then c5.open_flag else 0 end) as "staticReportOpenFlag",
        max(case when c5.open_type = 10 then c5.open_flag else 0 end) as "operationProductSurveyOpenFlag",
        max(case when c5.open_type = 11 then c5.open_flag else 0 end) as "operationPrintReportOpenFlag",
        max(case when c5.open_type = 12 then c5.open_flag else 0 end) as "operationFormOpenFlag",
        max(case when c5.open_type = 13 then c5.open_flag else 0 end) as "businessServerFlag",
        max(case when c5.open_type = 14 then c5.open_flag else 0 end) as "dataServerFlag",
        max(case when c5.open_type = 15 then c5.open_flag else 0 end) as "interfaceServerFlag",
        c4.user_name as "businessServerLeader",
        c7.user_name as "dataServerLeader",
        c9.user_name as "interfaceServerLeader"
        from csm.config_custom_backend_limit c
        left join csm.proj_custom_info c1 on c1.custom_info_id = c.custom_info_id
        left join csm.proj_project_info c2 on c2.project_info_id = c.project_info_id
        left join csm.proj_project_member c3 on c3.project_info_id = c.project_info_id and c3.project_member_role_id = 3 and c3.is_deleted = 0
        left join csm.sys_user c4 on c4.sys_user_id = c3.project_member_id and c4.is_deleted = 0
        left join csm.proj_project_member c6 on c6.project_info_id = c.project_info_id and c6.project_member_role_id = 5 and c6.is_deleted = 0
        left join csm.sys_user c7 on c7.sys_user_id = c6.project_member_id and c7.is_deleted = 0
        left join csm.proj_project_member c8 on c8.project_info_id = c.project_info_id and c8.project_member_role_id = 7 and c8.is_deleted = 0
        left join csm.sys_user c9 on c9.sys_user_id = c8.project_member_id and c9.is_deleted = 0
        left join csm.config_custom_backend_detail_limit c5 on c5.project_info_id = c.project_info_id
        where c.is_deleted = 0
        <if test="keyword != null and keyword != ''">
            and (c1.custom_name like concat('%', #{keyword}, '%') or c2.project_number like concat('%', #{keyword},'%'))
        </if>
        <if test="backendProjectManagerId != null and backendProjectManagerId != ''">
            and (c3.project_member_id = #{backendProjectManagerId} or c6.project_member_id = #{backendProjectManagerId} or c8.project_member_id = #{backendProjectManagerId})
        </if>
        <if test="onlyUnsettled != null and onlyUnsettled == 1">
            and (c2.project_deliver_status = 1 or c2.project_deliver_status = 2 or c2.project_deliver_status = 7)
        </if>
        <if test="onlySettledNotOnline != null and onlySettledNotOnline == 1">
            and (c2.project_deliver_status = 3 or c2.project_deliver_status = 4)
        </if>
        and c1.is_deleted = 0
        and c2.is_deleted = 0
        and c5.is_deleted = 0
        group by 1,2,3,4,5,6,7,8,c7.user_name,c9.user_name
        order by c.create_time desc
    </select>

    <update id="updateByProjectId">
        update config_custom_backend_limit
        set open_flag = #{isOpen}
        where project_info_id = #{projectInfoId}
    </update>

    <select id="selectByProjectIds" resultType="com.msun.csm.model.resp.custombackendlimit.QueryProjectDataResp">
        select c.*,
               c1.custom_name,
               c2.project_number,
               c2.project_deliver_status,
               c4.sys_user_id as "backendProjectManagerId",
               c4.user_name   as "backendProjectManagerName",
               c4.user_name   as "businessServerLeader",
               c7.user_name   as "dataServerLeader",
               c9.user_name   as "interfaceServerLeader",
               c5.open_flag   as "printOpenFlag"
        from csm.config_custom_backend_limit c
                 left join csm.proj_custom_info c1 on c1.custom_info_id = c.custom_info_id
                 left join csm.proj_project_info c2 on c2.project_info_id = c.project_info_id
                 left join csm.proj_project_member c3 on c3.project_info_id = c.project_info_id and c3.project_member_role_id = 3 and c3.is_deleted = 0
                 left join csm.sys_user c4 on c4.sys_user_id = c3.project_member_id and c4.is_deleted = 0
                 left join csm.proj_project_member c6 on c6.project_info_id = c.project_info_id and c6.project_member_role_id = 5 and c6.is_deleted = 0
                 left join csm.sys_user c7 on c7.sys_user_id = c6.project_member_id and c7.is_deleted = 0
                 left join csm.proj_project_member c8 on c8.project_info_id = c.project_info_id and c8.project_member_role_id = 7 and c8.is_deleted = 0
                 left join csm.sys_user c9 on c9.sys_user_id = c8.project_member_id and c9.is_deleted = 0
                 left join csm.config_custom_backend_detail_limit c5 on c5.project_info_id = c.project_info_id
        where c.is_deleted = 0
          and c.project_info_id in
        <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and c1.is_deleted = 0
        and c2.is_deleted = 0
        and c5.is_deleted = 0
        order by c.create_time desc
    </select>

    <select id="selectByProjectId" resultType="com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendLimit">
        select c.*
        from csm.config_custom_backend_limit c
        where c.is_deleted = 0
          and c.project_info_id = #{projectInfoId}
        order by c.create_time desc
    </select>

    <select id="getBackendOperationBoardInfo" resultType="com.msun.csm.model.dto.BackendOperationBoardInfo" parameterType="com.msun.csm.dao.entity.proj.GetBoardRecordParamPO">
        select
        pci.custom_name as "customName",
        ppi.project_number as "projectNumber",
        to_char(ppi.plan_online_time , 'yyyy-MM-dd HH24:mm') "planOnlineTime",
        case
        when pci.telesales_flag = 1 then 3
        else ppi.project_type
        end as "projectType",
        ppi.upgradation_type as "upgradationType",
        ppi.project_deliver_status as "projectDeliverStatus",
        ppi.project_info_id as "projectInfoId",
        pci.custom_info_id as "customInfoId"
        from csm.config_custom_backend_limit as ccbl
        left join csm.proj_project_info ppi on ccbl.project_info_id = ppi.project_info_id and ppi.is_deleted = 0
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id and pci.is_deleted = 0
        where ccbl.is_deleted = 0
        and ccbl.open_flag = 1
        <if test="keyWord != null and keyWord != ''">
        and (pci.custom_name like concat('%', #{keyWord}, '%') or ppi.project_number like concat('%', #{keyWord}, '%'))
        </if>
        <if test="projectDeliverStatus != null">
        and ppi.project_deliver_status = #{projectDeliverStatus}
        </if>
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size > 0 ">
            and ppi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
        and ppi.plan_online_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
        and ppi.plan_online_time &lt;= #{endTime}
        </if>
        order by ppi.plan_online_time asc, pci.custom_name desc
    </select>
</mapper>
