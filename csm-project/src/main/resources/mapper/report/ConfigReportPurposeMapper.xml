<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ConfigReportPurposeMapper">

    <select id="queryPurposeData" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select report_purpose_id as id,
               report_purpose_name as name
        from csm.config_report_purpose
        where is_deleted = 0
            <if test="projectInfoId != null">
                and (project_info_id = #{projectInfoId} or project_info_id = -1 )
            </if>
            <if test="projectInfoId == null">
                and project_info_id = -1
            </if>
        order by order_no, report_purpose_name

    </select>
</mapper>
