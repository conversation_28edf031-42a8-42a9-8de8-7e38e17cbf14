<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ConfigHospitalDeptMapper">

    <select id="selectListByProjectInfoId" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select hospital_dept_id id,
            hospital_dept_name "name"
        from csm.config_hospital_dept
        where is_deleted = 0
        <if test="projectInfoId != null">
            and (project_info_id = #{projectInfoId} or project_info_id = -1 )
        </if>
        <if test="projectInfoId == null">
            and project_info_id = -1
        </if>
        order by order_no, hospital_dept_name

    </select>
</mapper>
