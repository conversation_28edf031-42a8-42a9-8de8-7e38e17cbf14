<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ProjPrinterConfigInfoMapper">

    <select id="selectNotrepeatList"
            resultType="com.msun.csm.dao.entity.proj.projreport.ProjPrinterConfigInfo">
        select
            mac || ip_address || printer_name || printer_driver as "printerName"
        from
            csm.proj_printer_config_info
        where hospital_id = #{hospitalId}
        group by mac , ip_address , printer_name , printer_driver
    </select>
    <select id="queryPrintInfoByJf" resultType="com.msun.csm.dao.entity.proj.projreport.ProjPrinterConfigInfo">
        select
            x.hospital_id          ,
            x.mac                  ,
            x.ip_address           ,
            x.printer_name         ,
            x.printer_status       ,
            x.printer_driver       ,
            x.share_printer_info   ,
            x.is_share             ,
            x.paper_size           ,
            x.paper_width          ,
            x.paper_height         ,
            x.orientation          ,
            x.print_quality        ,
            x.private_data_hex     ,
            x.physical_width       ,
            x.physical_height      ,
            x.physical_offset_x    ,
            x.physical_offset_y    ,
            x.physical_dpi_x       ,
            x.physical_dpi_y       ,
            string_agg(x.dept_name, ', ')  dept_name

        from
            csm.proj_printer_config_info x
                inner join	(
                select distinct phi.*, ppi.project_info_id
                from csm.proj_hospital_info phi
                         inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
                    and phvpt.is_deleted = 0
                         inner join csm.proj_project_info ppi on ppi.custom_info_id = phi.custom_info_id and ppi.project_type =
                                                                                                             phvpt.project_type
                    and ppi.is_deleted = 0
                where phi.is_deleted = 0
            )  ee on x.hospital_id = ee.cloud_hospital_id
        where ee.project_info_id = #{projectInfoId}
        <if test="paperSize != null and paperSize != ''">
            and x.paper_size = #{paperSize}
        </if>
        <if test="orientation != null and orientation != ''">
            and x.orientation = #{orientation}
        </if>
        <if test="printerName != null and printerName != ''">
            and x.printer_name like  '%' || #{printerName} || '%'
        </if>
        <if test="printerDriver != null and printerDriver != ''">
            and x.printer_driver like  '%' || #{printerDriver} || '%'
        </if>
        <if test="deptName != null and deptName != ''">
            and x.dept_name like  '%' || #{deptName} || '%'
        </if>
        group by
            x.hospital_id          ,
            x.mac                  ,
            x.ip_address           ,
            x.printer_name         ,
            x.printer_status       ,
            x.printer_driver       ,
            x.share_printer_info   ,
            x.is_share             ,
            x.paper_size           ,
            x.paper_width          ,
            x.paper_height         ,
            x.orientation          ,
            x.print_quality        ,
            x.private_data_hex     ,
            x.physical_width       ,
            x.physical_height      ,
            x.physical_offset_x    ,
            x.physical_offset_y    ,
            x.physical_dpi_x       ,
            x.physical_dpi_y
    </select>
</mapper>
