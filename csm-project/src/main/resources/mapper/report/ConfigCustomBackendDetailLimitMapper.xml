<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper">

    <update id="updateByProjectId">
        update config_custom_backend_detail_limit
        set open_flag = #{printOpenFlag}
        where project_info_id = #{projectInfoId}
    </update>

    <select id="getCustomBackendDetailLimit" resultType="com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit">
        SELECT ccbdl.*
        FROM csm.config_custom_backend_detail_limit AS ccbdl
        WHERE is_deleted = 0
          and project_info_id = #{projectInfoId}
          and open_type = #{openType}
    </select>
</mapper>
