<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.comm.ProjBusinessExamineLogMapper">
    <select id="selectLogById" resultType="com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp"
            parameterType="java.lang.Long">
        select x.*,
               case
                   when x.examine_status = -1 then '初始化表单'
                   when x.examine_status = 0 then '提交后端审核'
                   when x.examine_status = 1 then '后端审核通过'
                   when x.examine_status = 2 then '后端审核驳回'

                    when x.examine_status = 11 then '新增表单'
                    when x.examine_status = 10 then '修改表单'
                    when x.examine_status = 15 then '引用资源库'
                    when x.examine_status = 16 then '设计表单'

                   when x.examine_status = 20 then '制作完成'
                   when x.examine_status = 21 then '验证通过'
                   when x.examine_status = 22 then '验证驳回'
                   when x.examine_status = 31 then '不使用'
                   when x.examine_status = 41 then '保存打印报表'
                   else '未知操作'
                   end                                         as "logTitle",
               x.examine_opinion                               as operateContent,
               to_char(x.create_time, 'YYYY-MM-DD HH24:MI:SS') as "operateTime",
               y.phone                                         as "operateUserPhone",
               x.creater_id                                    as "operatorSysUserId",
               y.user_name                                     as "operateUserName"
        from csm.proj_business_examine_log x
                 left join csm.sys_user y on x.creater_id = y.sYs_user_id
        where x.business_id = #{id}
          and x.is_deleted = 0
        <if test="examineStatus != null">
            and x.examine_status = #{examineStatus}
        </if>
        order by create_time
    </select>
    <select id="selectListByIds" resultType="com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog">
        select x.* from csm.proj_business_examine_log x
       inner join
        ( select business_id, max(create_time) as create_time
        from csm.proj_business_examine_log
        where
            is_deleted = 0
          <if test="businessIds != null and businessIds.size() > 0">
                      and business_id in
                <foreach collection="businessIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
          </if>
            <if test="businessIds == null or businessIds.size() == 0">
                and 1=2
            </if>
            group by business_id
        ) y on x.business_id = y.business_id and x.create_time = y.create_time
        where x.is_deleted = 0
        and x.examine_status = 1
    </select>
    <select id="selectListByParamer"
            resultType="com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog">
        select x.* from csm.proj_business_examine_log x
        inner join
        ( select business_id, max(create_time) as create_time
        from csm.proj_business_examine_log
        where
        is_deleted = 0
        <if test="businessIds != null and businessIds.size() > 0">
            and business_id in
            <foreach collection="businessIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="businessIds == null or businessIds.size() == 0">
            and 1=2
        </if>
        group by business_id
        ) y on x.business_id = y.business_id and x.create_time = y.create_time
        where x.is_deleted = 0
        <if test="status != null and status.size() > 0">
            and x.examine_status in
            <foreach collection="status" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getLogByBusinessAndStatus" resultType="com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog">
        select *
        from csm.proj_business_examine_log
        where  is_deleted = 0
        and business_type = #{businessType}
        and examine_status = #{examineStatus}
        and business_id = #{businessId}
    </select>

    <insert id="saveBusinessLog" parameterType="com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog">
        insert into csm.proj_business_examine_log (business_examine_log_id,
        business_id,
        business_type,
        examine_status,
        <if test="examineOpinion != null">
            examine_opinion,
        </if>
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted)
        values (#{businessExamineLogId},
        #{businessId},
        #{businessType},
        #{examineStatus},
        <if test="examineOpinion != null">
            #{examineOpinion},
        </if>
        #{createrId},
        #{createTime},
        #{updaterId},
        #{updateTime},
        #{isDeleted})
    </insert>
</mapper>
