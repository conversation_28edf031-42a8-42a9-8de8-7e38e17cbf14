<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.mainpage.MainpageRoleVsModuleMapper">
    <select id="findRoleVsModules" parameterType="com.msun.csm.model.dto.mainpage.MainpageRoleVsModuleDTO"
            resultType="com.msun.csm.model.vo.mainpage.MainpageRoleVsModuleVO">
        select rvm.role_vs_module_id, rvm.role_id,sr.role_name,
               rvm.module_id, dmm.module_code,dmm.module_name,
               rvm.row_num, rvm.order_sort, rvm.width_size, rvm.height_size,
               dmm.module_front_route, dmm.module_server_url, dmm.page_flag
        from csm.mainpage_role_vs_module rvm
        left join csm.sys_role sr on rvm.role_id = sr.sys_role_id and sr.is_deleted = 0
        left join csm.dict_mainpage_module dmm on rvm.module_id = dmm.dict_module_id and dmm.is_deleted = 0
        where 1 = 1
        <if test='roleName != null and roleName != "" '>
            and sr.role_name like concat('%', #{roleName}, '%')
        </if>
        <if test='moduleCode != null and moduleCode != "" '>
            and dmm.module_code like concat('%', #{moduleCode}, '%')
        </if>
        <if test='moduleName != null and moduleName != "" '>
            and dmm.module_name like concat('%', #{moduleName}, '%')
        </if>
        <if test="roleList != null and roleList.size > 0">
            and role_id in
            <foreach close=")" collection="roleList" item="item" open="(" separator=", ">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        and rvm.is_deleted = 0
    </select>

    <select id="findUserMainPageModules" resultType="com.msun.csm.model.vo.mainpage.MainpageRoleVsModuleVO">
        select distinct rvm.role_vs_module_id,
                        rvm.module_id,
                        rvm.order_sort,
                        rvm.width_size,
                        rvm.height_size,
                        dmm.module_code,
                        dmm.module_name
        from (select *
              from csm.sys_user_vs_role suvr
              where suvr.user_id = #{userId}
                and suvr.is_main_role = 1
                and suvr.is_deleted = 0 limit 1)
                 as suvr
                 inner join
             csm.mainpage_role_vs_module rvm on
                 suvr.role_id = rvm.role_id
                     and rvm.is_deleted = 0
                 inner join csm.dict_mainpage_module dmm on
            dmm.dict_module_id = rvm.module_id
                and dmm.is_deleted = 0
        order by rvm.order_sort
    </select>
</mapper>