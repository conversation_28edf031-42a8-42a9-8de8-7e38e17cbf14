<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibrary.ProjSelectApplicationFormMapper">

    <select id="selectDataLogByPage"
            resultType="com.msun.csm.model.resp.formlibrary.ProjSelectApplicationFormResp">
        SELECT x.select_application_form_id,
        x.form_pattern_id,
        x.form_pattern_name,
        x.description,
        x.sort,
        x.form_content_pc,
        x.form_configuration_pc,
        x.hospital_id,
        x.his_org_id,
        x.his_creater_id,
        Case when detail.user_name is not null and detail.user_name != '' then detail.user_name
            else  u.user_name
            end his_creater_name,
        x.his_create_time,
        x.his_updater_id,
        x.version,
        x.invalid_flag,
        x.his_update_time,
        x.form_category_code,
        x.form_content_pad,
        x.form_configuration_pad,
        x.yy_product_id,
        x.is_deleted,
        x.creater_id,
        x.create_time,
        x.updater_id,
        x.update_time,
        x.project_info_id,
        x.hospital_info_id,
        x.form_configuration_web,
        x.form_content_web,
        y.form_category_name as "formCategoryName",
        z.hospital_name as "hospitalName",
        detail.hospital_name as "sendHospitalName"
        FROM csm.proj_select_application_form x
        left join form_library.form_catetory y on x.form_category_code = y.form_category_code
        left join (
        select cloud_hospital_id,min(hospital_name) hospital_name from
        csm.proj_hospital_info
        group by cloud_hospital_id
        ) z on x.hospital_id = z.cloud_hospital_id
        left join csm.sys_user u on x.creater_id = u.sys_user_id

        left join (
        select
            d.select_application_form_id,
            string_agg(distinct d.hospital_name, ', ')  as hospital_name,
            min(s.user_name) user_name
        from csm.proj_select_application_form_detail d
        left join csm.sys_user s on d.creater_id = s.sys_user_id
            where d.is_deleted = 0
            and d.oper_type = 2
            and d.oper_status = 1
            group by d.select_application_form_id

        ) detail on x.select_application_form_id = detail.select_application_form_id

        where x.is_deleted = 0
        and x.project_info_id = #{projectInfoId}
        <if test="hospitalInfoId != null">
            and x.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="formPatternName != null and formPatternName != '' ">
            and x.form_pattern_name like concat('%',#{formPatternName},'%')
        </if>


    </select>
    <select id="selectAimsHospitalFormLibData"
            resultType="com.msun.csm.model.resp.formlibrary.AimsHospitalFormResp">
        select x.form_pattern_id ,
        x.form_pattern_name ,
        x.description ,
        x.sort ,
        x.form_content_pc ,
        x.form_configuration_pc ,
        x.hospital_id ,
        x.his_org_id ,
        x.his_creater_id ,
        x.his_creater_name ,
        x.his_create_time ,
        x.his_updater_id ,
        x.version ,
        x.invalid_flag ,
        x.his_update_time ,
        x.form_category_code,
        x.form_content_pad ,
        x.form_configuration_pad ,
        x.yy_product_id,
        x.form_configuration_web,
        x.form_content_web,
        y.form_category_name as "formCategoryName",
        z.hospital_name as "hospitalName"
        from form_library.form_pattern x
        left join form_library.form_catetory y on x.form_category_code = y.form_category_code
        left join (
        select cloud_hospital_id,min(hospital_name) hospital_name from
        csm.proj_hospital_info
        group by cloud_hospital_id
        ) z on x.hospital_id = z.cloud_hospital_id
        where x.invalid_flag = '0'
        <if test="formCategoryCode != null and formCategoryCode != '' ">
            and x.form_category_code = #{formCategoryCode}
        </if>
        <if test="formPatternName != null and formPatternName != '' ">
            and x.form_pattern_name like concat('%',#{formPatternName},'%')
        </if>
        order by x.hospital_id, x.form_pattern_name
    </select>
    <select id="queryAimsLibFormType" resultType="com.msun.csm.common.model.BaseCodeNameResp">
        SELECT distinct form_category_name as name,
                        form_category_code as id
        FROM form_library.form_catetory x
    </select>
</mapper>
