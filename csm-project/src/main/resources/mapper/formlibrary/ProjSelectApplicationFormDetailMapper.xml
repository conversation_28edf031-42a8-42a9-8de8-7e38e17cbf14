<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibrary.ProjSelectApplicationFormDetailMapper">

    <select id="selectDataListByParam"
            resultType="com.msun.csm.dao.entity.formlibrary.ProjSelectApplicationFormDetail">
        select z.*,
               u.user_name as "operUserName"
        from csm.proj_select_application_form_detail z
                 left join csm.sys_user u on z.creater_id = u.sys_user_id
        where z.select_application_form_id = #{selectApplicationFormId}
          and z.oper_type = 2
          and z.is_deleted = 0
    </select>
</mapper>
