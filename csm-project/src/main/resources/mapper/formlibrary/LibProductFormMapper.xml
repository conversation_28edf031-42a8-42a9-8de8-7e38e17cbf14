<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibrary.LibProductFormMapper">


    <select id="selectDataByPage" resultType="com.msun.csm.model.resp.formlibrary.LibProductFormResp">
        select
            x.product_form_id,
            x.yy_product_id,
            x.form_source,
            x.form_type,
            x.form_id,
            x.form_name,
            x.form_structure,
            x.form_picture_paths,
            x.source_hospital_id,
            x.source_hospital_name,
            x.his_org_id,
            x.creater_id,
            to_char(x.create_time, 'yyyy-MM-dd HH24:mi:ss') create_time,
            x.updater_id,
            to_char(x.update_time, 'yyyy-MM-dd HH24:mi:ss') update_time,
            x."version",
            x.grf,
            x.parsed_text,
            x.designer_grf,
            x.is_deleted,
            y.hospital_name,
            su.user_name,
            dp.product_name
        from
        csm.lib_product_form x

        inner join (
            select pop.special_product_id as yy_product_id
            from csm.proj_special_product_record pop
            left join csm.proj_project_info ppi on
            pop.project_info_id = ppi.project_info_id
            where pop.is_deleted = 0
            and ppi.project_info_id = #{projectInfoId}
            union all
            select pdr.product_deliver_id  yy_product_id
            from csm.proj_product_deliver_record pdr
            left join csm.proj_project_info ppi on pdr.project_info_id = ppi.project_info_id
            where pdr.is_deleted = 0
            and ppi.project_info_id = #{projectInfoId}
        ) sss on x.yy_product_id = sss.yy_product_id
        inner join (select cloud_hospital_id, max(hospital_name) hospital_name from csm.proj_hospital_info
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            where hospital_info_id = #{hospitalInfoId}
        </if>
        group by
        cloud_hospital_id) y on x.source_hospital_id = y.cloud_hospital_id
        left join csm.sys_user su on x.creater_id = su.sys_user_id
        left join csm.dict_product dp on x.yy_product_id = dp.yy_product_id
        where
            x.is_deleted = 0
        <if test="formName != null and formName != ''">
            and x.form_name like concat('%',#{formName},'%')
        </if>
        <if test="formType != null and formType != ''">
            and x.form_type = #{formType}
        </if>
        <if test="surveyFormId != null and surveyFormId != ''">
            and x.product_form_id in (
                select  regexp_split_to_table(recommend_template_ids, ',')::int8 from csm.proj_survey_form where survey_form_id = #{surveyFormId}
            )
        </if>
        order by x.create_time desc, x.source_hospital_id, x.yy_product_id
    </select>
    <select id="selectDataLogByPage" resultType="com.msun.csm.model.resp.formlibrary.ProjProductFormLogResp">
            select
                x.form_library_log_id,
                x.form_name,
                x.form_type,
                x.hospital_name,
                x.product_form_id,
                x.status,
                x.creater_id,
                to_char(x.create_time, 'yyyy-MM-dd HH24:mi:ss') create_time,
                x.updater_id,
                to_char(x.update_time, 'yyyy-MM-dd HH24:mi:ss') update_time,
                su.user_name,
                y.cloud_hospital_id
            from
            csm.proj_form_library_log x
            inner join (select cloud_hospital_id, max(hospital_name) hospital_name from csm.proj_hospital_info
                <if test="hospitalInfoId != null and hospitalInfoId != ''">
                    where hospital_info_id = #{hospitalInfoId}
                </if>
                group by
            cloud_hospital_id) y on x.cloud_hospital_id = y.cloud_hospital_id
            left join csm.sys_user su on x.creater_id = su.sys_user_id
            where
                x.is_deleted = 0
            <if test="formName != null and formName != ''">
                and x.form_name like concat('%',#{formName},'%')
            </if>
            <if test="formType != null and formType != ''">
                and x.form_type = #{formType}
            </if>
            order by x.create_time desc, x.cloud_hospital_id, x.form_name
    </select>
    <select id="getIsToolLimit" resultType="java.util.Map">
        select distinct  tl.* from
            csm.tmp_team_tool_limit tl
                inner join csm.sys_user su on tl.yy_dept_id =su.dept_id
        where su.sys_user_id = #{userId}
          and tl.limit_type =  #{typeCode}
    </select>
    <select id="selectProductListData" resultType="com.msun.csm.dao.entity.dict.DictProduct">
        select dps.* from csm.dict_product dps
              inner join (
            select pop.special_product_id as yy_product_id
            from csm.proj_special_product_record pop
                     left join csm.proj_project_info ppi on
                pop.project_info_id = ppi.project_info_id
            where pop.is_deleted = 0
              and ppi.project_info_id = #{projectInfoId}
            union all
            select pdr.product_deliver_id  yy_product_id
            from csm.proj_product_deliver_record pdr
                     left join csm.proj_project_info ppi on pdr.project_info_id = ppi.project_info_id
            where pdr.is_deleted = 0
              and ppi.project_info_id = #{projectInfoId}
        ) sss on dps.yy_product_id = sss.yy_product_id
        where dps.is_deleted = 0
               and dps.product_name like '%护理%'

    </select>
    <select id="selectAimsProductListData" resultType="com.msun.csm.dao.entity.dict.DictProduct">

        select dps.* from csm.dict_product dps
                              inner join (
            select pop.special_product_id as yy_product_id
            from csm.proj_special_product_record pop
                     left join csm.proj_project_info ppi on
                pop.project_info_id = ppi.project_info_id
            where pop.is_deleted = 0
              and ppi.project_info_id = #{projectInfoId}
            union all
            select pdr.product_deliver_id  yy_product_id
            from csm.proj_product_deliver_record pdr
                     left join csm.proj_project_info ppi on pdr.project_info_id = ppi.project_info_id
            where pdr.is_deleted = 0
              and ppi.project_info_id = #{projectInfoId}
        ) sss on dps.yy_product_id = sss.yy_product_id
        where dps.is_deleted = 0
          and dps.product_name like '%手术%'

    </select>
    <select id="selectListAll" resultType="com.msun.csm.dao.entity.formlibrary.LibProductForm">
        select
            product_form_id,
            designer_grf,
            parsed_text
        from
            csm.lib_product_form
        where
            is_deleted = 0
          and (designer_grf is not null and designer_grf != '')
          and (parsed_text is null or parsed_text = '')
    </select>
    <select id="selectListAllHaveParsedText" resultType="com.msun.csm.dao.entity.formlibrary.LibProductForm">
        select
            product_form_id,
            form_type,
            form_source,
            form_name,
            parsed_text
        from
            csm.lib_product_form
        where
            is_deleted = 0
          and form_source = 'nurs'
          and (parsed_text is not null or parsed_text != '')
    </select>
</mapper>
