<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        <!--@Table csm.tmp_project_new_vs_old-->
        <id column="project_new_vs_old_id" jdbcType="BIGINT" property="projectNewVsOldId"/>
        <result column="new_project_info_id" jdbcType="BIGINT" property="newProjectInfoId"/>
        <result column="new_custom_info_id" jdbcType="BIGINT" property="newCustomInfoId"/>
        <result column="old_project_info_id" jdbcType="BIGINT" property="oldProjectInfoId"/>
        <result column="old_custom_info_id" jdbcType="BIGINT" property="oldCustomInfoId"/>
        <result column="old_custom_id" jdbcType="BIGINT" property="oldCustomId"/>
        <result column="new_project_source" jdbcType="INTEGER" property="newProjectSource"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_new_vs_old_id, new_project_info_id, new_custom_info_id, old_project_info_id,
        old_custom_info_id, old_custom_id,new_project_source
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.tmp_project_new_vs_old
        where project_new_vs_old_id = #{projectNewVsOldId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.tmp_project_new_vs_old
        where project_new_vs_old_id = #{projectNewVsOldId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        insert into csm.tmp_project_new_vs_old (project_new_vs_old_id, new_project_info_id,
        new_custom_info_id, old_project_info_id, old_custom_info_id,
        old_custom_id, new_project_source)
        values (#{projectNewVsOldId,jdbcType=BIGINT}, #{newProjectInfoId,jdbcType=BIGINT},
        #{newCustomInfoId,jdbcType=BIGINT}, #{oldProjectInfoId,jdbcType=BIGINT}, #{oldCustomInfoId,jdbcType=BIGINT},
        #{oldCustomId,jdbcType=BIGINT}, #{newProjectSource,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        insert into csm.tmp_project_new_vs_old
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectNewVsOldId != null">
                project_new_vs_old_id,
            </if>
            <if test="newProjectInfoId != null">
                new_project_info_id,
            </if>
            <if test="newCustomInfoId != null">
                new_custom_info_id,
            </if>
            <if test="oldProjectInfoId != null">
                old_project_info_id,
            </if>
            <if test="oldCustomInfoId != null">
                old_custom_info_id,
            </if>
            <if test="oldCustomId != null">
                old_custom_id,
            </if>
            <if test="newProjectSource != null">
                new_project_source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectNewVsOldId != null">
                #{projectNewVsOldId,jdbcType=BIGINT},
            </if>
            <if test="newProjectInfoId != null">
                #{newProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="newCustomInfoId != null">
                #{newCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldProjectInfoId != null">
                #{oldProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomInfoId != null">
                #{oldCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomId != null">
                #{oldCustomId,jdbcType=BIGINT},
            </if>
            <if test="newProjectSource != null">
                #{newProjectSource,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        update csm.tmp_project_new_vs_old
        <set>
            <if test="newProjectInfoId != null">
                new_project_info_id = #{newProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="newCustomInfoId != null">
                new_custom_info_id = #{newCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldProjectInfoId != null">
                old_project_info_id = #{oldProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomInfoId != null">
                old_custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomId != null">
                old_custom_id = #{oldCustomId,jdbcType=BIGINT},
            </if>
            <if test="newProjectSource != null">
                new_project_source = #{newProjectSource,jdbcType=INTEGER},
            </if>
        </set>
        where project_new_vs_old_id = #{projectNewVsOldId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        update csm.tmp_project_new_vs_old
        set new_project_info_id = #{newProjectInfoId,jdbcType=BIGINT},
        new_custom_info_id = #{newCustomInfoId,jdbcType=BIGINT},
        old_project_info_id = #{oldProjectInfoId,jdbcType=BIGINT},
        old_custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT},
        old_custom_id = #{oldCustomId,jdbcType=BIGINT},
        new_project_source = #{newProjectSource,jdbcType=INTEGER}
        where project_new_vs_old_id = #{projectNewVsOldId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.tmp_project_new_vs_old
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="new_project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                    #{item.newProjectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="new_custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                    #{item.newCustomInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="old_project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                    #{item.oldProjectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="old_custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                    #{item.oldCustomInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="old_custom_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                    #{item.oldCustomId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="new_project_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                    #{item.newProjectSource,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where project_new_vs_old_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectNewVsOldId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.tmp_project_new_vs_old
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="new_project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.newProjectInfoId != null">
                        when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                        #{item.newProjectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="new_custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.newCustomInfoId != null">
                        when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                        #{item.newCustomInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="old_project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.oldProjectInfoId != null">
                        when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                        #{item.oldProjectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="old_custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.oldCustomInfoId != null">
                        when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                        #{item.oldCustomInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="old_custom_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.oldCustomId != null">
                        when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                        #{item.oldCustomId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="new_project_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.newProjectSource != null">
                        when project_new_vs_old_id = #{item.projectNewVsOldId,jdbcType=BIGINT} then
                        #{item.newProjectSource,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_new_vs_old_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectNewVsOldId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.tmp_project_new_vs_old
        (project_new_vs_old_id, new_project_info_id, new_custom_info_id, old_project_info_id,
        old_custom_info_id, old_custom_id, new_project_source)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectNewVsOldId,jdbcType=BIGINT}, #{item.newProjectInfoId,jdbcType=BIGINT},
            #{item.newCustomInfoId,jdbcType=BIGINT}, #{item.oldProjectInfoId,jdbcType=BIGINT},
            #{item.oldCustomInfoId,jdbcType=BIGINT}, #{item.oldCustomId,jdbcType=BIGINT},
            #{item.newProjectSource,jdbcType=INTEGER})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        insert into csm.tmp_project_new_vs_old
        (project_new_vs_old_id, new_project_info_id, new_custom_info_id, old_project_info_id,
        old_custom_info_id, old_custom_id, new_project_source)
        values
        (#{projectNewVsOldId,jdbcType=BIGINT}, #{newProjectInfoId,jdbcType=BIGINT}, #{newCustomInfoId,jdbcType=BIGINT},
        #{oldProjectInfoId,jdbcType=BIGINT}, #{oldCustomInfoId,jdbcType=BIGINT}, #{oldCustomId,jdbcType=BIGINT},
        #{newProjectSource,jdbcType=INTEGER})
        on duplicate key update
        project_new_vs_old_id = #{projectNewVsOldId,jdbcType=BIGINT},
        new_project_info_id = #{newProjectInfoId,jdbcType=BIGINT},
        new_custom_info_id = #{newCustomInfoId,jdbcType=BIGINT},
        old_project_info_id = #{oldProjectInfoId,jdbcType=BIGINT},
        old_custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT},
        old_custom_id = #{oldCustomId,jdbcType=BIGINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld">
        <!--@mbg.generated-->
        insert into csm.tmp_project_new_vs_old
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectNewVsOldId != null">
                project_new_vs_old_id,
            </if>
            <if test="newProjectInfoId != null">
                new_project_info_id,
            </if>
            <if test="newCustomInfoId != null">
                new_custom_info_id,
            </if>
            <if test="oldProjectInfoId != null">
                old_project_info_id,
            </if>
            <if test="oldCustomInfoId != null">
                old_custom_info_id,
            </if>
            <if test="oldCustomId != null">
                old_custom_id,
            </if>
            <if test="newProjectSource != null">
                new_project_source,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectNewVsOldId != null">
                #{projectNewVsOldId,jdbcType=BIGINT},
            </if>
            <if test="newProjectInfoId != null">
                #{newProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="newCustomInfoId != null">
                #{newCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldProjectInfoId != null">
                #{oldProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomInfoId != null">
                #{oldCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomId != null">
                #{oldCustomId,jdbcType=BIGINT},
            </if>
            <if test="newProjectSource != null">
                #{newProjectSource,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectNewVsOldId != null">
                project_new_vs_old_id = #{projectNewVsOldId,jdbcType=BIGINT},
            </if>
            <if test="newProjectInfoId != null">
                new_project_info_id = #{newProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="newCustomInfoId != null">
                new_custom_info_id = #{newCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldProjectInfoId != null">
                old_project_info_id = #{oldProjectInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomInfoId != null">
                old_custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="oldCustomId != null">
                old_custom_id = #{oldCustomId,jdbcType=BIGINT},
            </if>
            <if test="newProjectSource != null">
                new_project_source = #{newProjectSource,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="selectOldProjectIdList" parameterType="long" resultType="java.lang.Long">
        select id
        from platform.project
        where customer_id = #{customerId}
    </select>

    <select id="selectByNewProjectIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.tmp_project_new_vs_old
        where new_project_info_id in
        <foreach close=")" collection="projectIds" item="projectId" open="(" separator=", ">
            #{projectId}
        </foreach>
    </select>

    <select id="selectByOldProjectIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.tmp_project_new_vs_old
        where tmp_project_new_vs_old.old_project_info_id in
        <foreach close=")" collection="projectIds" item="projectId" open="(" separator=", ">
            #{projectId}
        </foreach>
    </select>

    <select id="getAllOldData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.tmp_project_new_vs_old
        where tmp_project_new_vs_old.new_project_source = 2
    </select>

    <select id="queryLoseProjectData" resultMap="BaseResultMap">
        select tmp.*
        from tmp_project_new_vs_old tmp
                 left join proj_project_info ppi on tmp.new_project_info_id = ppi.project_info_id
                 left join platform.project pp on tmp.old_project_info_id = pp.id
        where ppi.is_deleted = 0
          and pp.id is null
    </select>

    <update id="updateByCustomInfoId">
        update csm.tmp_project_new_vs_old
        set new_custom_info_id = #{newCustomInfoId, jdbcType=BIGINT}
        <if test="oldJfNewCustomInfoId != null and oldJfNewCustomInfoId != 0">
            , old_custom_info_id = #{oldJfNewCustomInfoId, jdbcType=BIGINT}
        </if>
        where new_custom_info_id = #{oldCustomInfoId, jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and tmp_project_new_vs_old.new_project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
