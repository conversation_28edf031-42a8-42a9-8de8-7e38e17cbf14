<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tmp.TmpHospitalLimitMapper">

    <insert id="saveDataByProjectInfoId">
        insert into csm.tmp_hospital_limit (cloud_hospital_id,
                                            report_limit_flag,
                                            interface_limit_flag)

        select phi.cloud_hospital_id ,1,1 from csm.proj_hospital_info phi
                                                   inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
                                                   inner join csm.proj_project_info ppi on ppi.custom_info_id = phvpt.custom_info_id and ppi.project_type = phvpt.project_type
        where ppi.project_info_id = #{projectInfoId}
          and phi.cloud_hospital_id is not null
          and phi.cloud_hospital_id not in (
            select cloud_hospital_id from csm.tmp_hospital_limit
        )
        group by phi.cloud_hospital_id
    </insert>
</mapper>
