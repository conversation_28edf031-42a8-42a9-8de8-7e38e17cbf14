<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tmp.TmpProjProductVersionMapper">
    <resultMap type="com.msun.csm.dao.entity.tmp.TmpProjProductVersion" id="TmpProjProductVersionMap">
        <result property="projProductVersionId" column="proj_product_version_id" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="INTEGER"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="productVersion" column="product_version" jdbcType="VARCHAR"/>
    </resultMap>
<sql id="Base_Column_List">
        proj_product_version_id, project_info_id, product_name, product_version, creater_id,
        create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" resultMap="TmpProjProductVersionMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from csm.tmp_proj_product_version
        where proj_product_version_id = #{projProductVersionId,jdbcType=INTEGER}
    </select>
    <select id="selectByProductName" resultMap="TmpProjProductVersionMap" parameterType="com.msun.csm.model.dto.TmpProjProductVersionDTO">
        select
        <include refid="Base_Column_List"/>
        from csm.tmp_proj_product_version
        where is_deleted = 0
        and project_info_id=#{projectInfoId,jdbcType=INTEGER}
        and product_name in
        <foreach collection="productNames" item="productName" open="(" separator="," close=")">
            #{productName,jdbcType=VARCHAR}
        </foreach>
    </select>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.tmp.TmpProjProductVersion">
        insert into csm.tmp_proj_product_version
        (proj_product_version_id, project_info_id, product_name, product_version, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        (
        #{projProductVersionId,jdbcType=INTEGER},
        #{projectInfoId,jdbcType=INTEGER},
        #{productName,jdbcType=VARCHAR},
        #{productVersion,jdbcType=VARCHAR},
        #{createrId,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=INTEGER}
        )
        ON CONFLICT (project_info_id, product_name)
        DO update set
        product_version = #{productVersion,jdbcType=VARCHAR},
        updater_id = #{updaterId,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from csm.tmp_proj_product_version
        where proj_product_version_id = #{projProductVersionId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.tmp.TmpProjProductVersion">
        insert into csm.tmp_proj_product_version (proj_product_version_id, project_info_id,
        product_name, product_version, creater_id,
        create_time, updater_id, update_time,
        is_deleted)
        values (#{projProductVersionId,jdbcType=INTEGER}, #{projectInfoId,jdbcType=INTEGER},
        #{productName,jdbcType=VARCHAR}, #{productVersion,jdbcType=VARCHAR}, #{createrId,jdbcType=INTEGER},
              #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
          #{isDeleted,jdbcType=INTEGER})
    }
    </insert>


</mapper>
