<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibnew.FormlibResourceUniteMapper">
    <update id="updateDataById">
            update csm.formlib_resource_unite
                    <set>
                        <if test="isDeleted != null">
                            is_deleted = #{isDeleted},
                        </if>
                        <if test="formName != null">
                            form_name = #{formName},
                        </if>
                        <if test="formPicturePaths != null">
                            form_picture_paths = #{formPicturePaths},
                        </if>
                        <if test="useCount != null">
                            use_count = #{useCount},
                        </if>
                        <if test="formSaveId != null">
                            form_save_id = #{formSaveId},
                        </if>
                        <if test="remark != null">
                            remark = #{remark},
                        </if>
                        <if test="formStructure != null">
                            form_structure = #{formStructure},
                        </if>
                        <if test="designerGrf != null">
                            designer_grf = #{designerGrf},
                        </if>
                        <if test="designerGrfApp != null">
                            designer_grf_app = #{designerGrfApp},
                        </if>
                        <if test="designerGrfPad != null">
                            designer_grf_pad = #{designerGrfPad},
                        </if>
                        <if test="formConfigurationPc != null">
                            form_configuration_pc = #{formConfigurationPc},
                        </if>
                    </set>
            where formlib_resource_unite_id = #{formlibResourceUniteId}
    </update>

    <select id="findFormLibDataPage"
            resultType="com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp">
        select distinct
                fru.*,
                dp.product_name as "yyProductName",
                su.user_name AS "createUserName",
                su2.user_name AS "updateUserName",
                t1.*
        from  csm.formlib_resource_unite fru
        left join csm.dict_product dp on fru.yy_product_id = dp.yy_product_id
        left join csm.sys_user su on su.sys_user_id = fru.creater_id
        left join csm.sys_user su2 on su2.sys_user_id = fru.updater_id
        left join csm.formlib_resource_type frusw on frusw.formlib_resource_unite_id = fru.formlib_resource_unite_id
        left join (
            SELECT
                frus.formlib_resource_unite_id,
                string_agg(distinct dfs.formlib_standard_name, ',' ORDER BY dfs.formlib_standard_name) AS "formlibStandardName",
                string_agg(distinct dfst.formlib_standard_type_name, ',' ORDER BY dfst.formlib_standard_type_name) AS
                "formlibStandardTypeName"
            FROM
            csm.formlib_resource_type frus
            LEFT JOIN csm.dict_formlib_standard_type dfst
            ON frus.formlib_standard_type_id = dfst.formlib_standard_type_id
            LEFT JOIN csm.dict_formlib_standard dfs
            ON frus.formlib_standard_id = dfs.formlib_standard_id
           where frus.is_deleted = 0
            GROUP BY
            frus.formlib_resource_unite_id
        ) as t1 on t1.formlib_resource_unite_id = fru.formlib_resource_unite_id


        <where>
            <if test="yyProductId != null">
                and fru.yy_product_id = #{yyProductId}
            </if>
            <if test="formName != null">
                and fru.form_name like concat('%',#{formName},'%')
            </if>
            <if test="formlibResourceUniteId != null">
                and fru.formlib_resource_unite_id = #{formlibResourceUniteId}
            </if>
            <if test="formSource != null">
                and fru.form_source = #{formSource}
            </if>

            <if test="ids != null and !ids.isEmpty()">
                and frusw.formlib_standard_id in
                <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="formlibStandardTypeIds != null and !formlibStandardTypeIds.isEmpty()">
                and frusw.formlib_standard_type_id in
                <foreach item="item" collection="formlibStandardTypeIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>

        </where>

    </select>
    <select id="findFormLibDataList" resultType="com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp"
            parameterType="com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq">
        select distinct
        fru.formlib_resource_unite_id    ,
        fru.yy_product_id                ,
        fru.project_info_id              ,
        fru.form_name                    ,
        fru.form_picture_paths           ,
        fru.use_count                    ,
        dp.product_name as "yyProductName",
        su.user_name AS "createUserName",
        su2.user_name AS "updateUserName",
        t1.*
        from  csm.formlib_resource_unite fru
        left join csm.dict_product dp on fru.yy_product_id = dp.yy_product_id
        left join csm.sys_user su on su.sys_user_id = fru.creater_id
        left join csm.sys_user su2 on su2.sys_user_id = fru.updater_id
        left join csm.formlib_resource_type frusw on frusw.formlib_resource_unite_id = fru.formlib_resource_unite_id
        left join (
        SELECT
        frus.formlib_resource_unite_id,
        string_agg(distinct dfs.formlib_standard_name, ',' ORDER BY dfs.formlib_standard_name) AS "formlibStandardName",
        string_agg(distinct dfst.formlib_standard_type_name, ',' ORDER BY dfst.formlib_standard_type_name) AS
        "formlibStandardTypeName"
        FROM
        csm.formlib_resource_type frus
        LEFT JOIN csm.dict_formlib_standard_type dfst
        ON frus.formlib_standard_type_id = dfst.formlib_standard_type_id
        LEFT JOIN csm.dict_formlib_standard dfs
        ON frus.formlib_standard_id = dfs.formlib_standard_id
        where frus.is_deleted = 0
        GROUP BY
        frus.formlib_resource_unite_id
        ) as t1 on t1.formlib_resource_unite_id = fru.formlib_resource_unite_id

        where fru.is_deleted = 0
            and fru.form_source = 0
            <if test="yyProductId != null">
                and fru.yy_product_id = #{yyProductId}
            </if>
            <if test="formlibResourceUniteId != null">
                and fru.formlib_resource_unite_id = #{formlibResourceUniteId}
            </if>

            <if test="formlibStandardId != null">
                and frusw.formlib_standard_id = #{formlibStandardId}
            </if>
            <if test="formlibStandardTypeId != null">
                and frusw.formlib_standard_type_id = #{formlibStandardTypeId}
            </if>
            <if test="formName != null and formName != ''">
                and fru.form_name like concat('%',#{formName},'%')
            </if>


    </select>
    <select id="selectListByFormType"
            resultType="com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductDataVO">
        select
            fru.*,
            fru.form_save_id "formId"
        from  csm.formlib_resource_unite fru
        where fru.is_deleted = 0
           and fru.form_source = 0
          and fru.form_type = #{formType}

    </select>
    <select id="selectBySurveyFormParam" resultType="com.msun.csm.dao.entity.formlibnew.FormlibResourceUnite">
        select
            fru.formlib_resource_unite_id,
            fru.yy_product_id,
            fru.project_info_id,
            fru.form_source,
            fru.form_save_id,
            fru.form_name,
            fru.form_picture_paths,
            fru.use_count,
            fru.remark,
            fru.form_structure,
            fru.grf,
            fru.designer_grf,
            fru.designer_grf_app,
            fru.designer_grf_pad,
            phi.org_id as  his_org_id,
            phi.cloud_hospital_id as source_hospital_id,
            phi.hospital_name source_hospital_name,
            fru.parsed_text,
            fru.is_deleted,
            fru.creater_id,
            fru.create_time,
            fru.updater_id,
            fru.update_time,
            fru.form_type,
            fru.form_configuration_pc
        from  csm.formlib_resource_unite fru
        inner join csm.proj_survey_form sf on sf.formlib_resource_unite_id = fru.formlib_resource_unite_id
        inner join csm.proj_hospital_info phi on phi.hospital_info_id = sf.hospital_info_id
        where sf.is_deleted = 0
            and sf.project_info_id = #{projectInfoId}
            and sf.import_cloud_status = -1
        order by sf.hospital_info_id, fru.yy_product_id
    </select>
    <select id="findFormLibDataListToRecommend"
            resultType="com.msun.csm.model.resp.formlibnew.FormlibResourceUnitePageResp"
            parameterType="com.msun.csm.model.req.formlibnew.FormlibResourceUniteSelectListReq">
    select * from (
        select distinct
        fru.formlib_resource_unite_id ,
        fru.yy_product_id ,
        fru.form_source,
        fru.project_info_id ,
        fru.form_name ,
        fru.form_picture_paths ,
        fru.use_count ,
        dp.product_name as "yyProductName",
        su.user_name AS "createUserName",
        su2.user_name AS "updateUserName",
        t1.*
        from csm.formlib_resource_unite fru
        left join csm.dict_product dp on fru.yy_product_id = dp.yy_product_id
        left join csm.sys_user su on su.sys_user_id = fru.creater_id
        left join csm.sys_user su2 on su2.sys_user_id = fru.updater_id
        left join csm.formlib_resource_type frusw on frusw.formlib_resource_unite_id = fru.formlib_resource_unite_id
        left join (
        SELECT
        frus.formlib_resource_unite_id,
        string_agg(distinct dfs.formlib_standard_name, ',' ORDER BY dfs.formlib_standard_name) AS "formlibStandardName",
        string_agg(distinct dfst.formlib_standard_type_name, ',' ORDER BY dfst.formlib_standard_type_name) AS
        "formlibStandardTypeName"
        FROM
        csm.formlib_resource_type frus
        LEFT JOIN csm.dict_formlib_standard_type dfst
        ON frus.formlib_standard_type_id = dfst.formlib_standard_type_id
        LEFT JOIN csm.dict_formlib_standard dfs
        ON frus.formlib_standard_id = dfs.formlib_standard_id
        where frus.is_deleted = 0
        GROUP BY
        frus.formlib_resource_unite_id
        ) as t1 on t1.formlib_resource_unite_id = fru.formlib_resource_unite_id

        where fru.is_deleted = 0
        and (fru.form_source = 0
            <if test="formlibResourceUniteId != null">
            or  fru.formlib_resource_unite_id = #{formlibResourceUniteId}
            </if>
        )
        <if test="yyProductId != null">
            and fru.yy_product_id = #{yyProductId}
        </if>

        <if test="formlibStandardId != null">
            and frusw.formlib_standard_id = #{formlibStandardId}
        </if>
        <if test="formlibStandardTypeId != null">
            and frusw.formlib_standard_type_id = #{formlibStandardTypeId}
        </if>
        ) sse
        order by form_source desc , use_count desc
    </select>
</mapper>
