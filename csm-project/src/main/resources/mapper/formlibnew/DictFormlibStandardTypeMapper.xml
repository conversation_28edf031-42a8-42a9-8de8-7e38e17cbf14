<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibnew.DictFormlibStandardTypeMapper">
    <update id="updateDataById">
        update csm.dict_formlib_standard_type
        <set>
            <if test="formlibStandardId != null">
                formlib_standard_id = #{formlibStandardId},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId},
            </if>
            <if test="formlibStandardTypeCode != null">
                formlib_standard_type_code = #{formlibStandardTypeCode},
            </if>
            <if test="formlibStandardTypeName != null">
                formlib_standard_type_name = #{formlibStandardTypeName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="onlineEssential != null">
                online_essential = #{onlineEssential},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="pathId != null">
                path_id = #{pathId},
            </if>
        </set>
        where formlib_standard_type_id = #{formlibStandardTypeId}
    </update>

    <select id="selectPageByDto"
            resultType="com.msun.csm.model.resp.formlibnew.DictFormlibStandardTypeSelectResp">
        select  dprt.formlib_standard_type_id   ,
                dprt.yy_product_id              ,
                dprt.formlib_standard_type_code ,
                dprt.formlib_standard_type_name ,
                dprt.remark                     ,
                dprt.order_num                  ,
                dprt.online_essential           ,
                dprt.is_deleted                 ,
                dprt.creater_id                 ,
                dprt.create_time                ,
                dprt.updater_id,
                dprt.update_time,
                dprt.formlib_standard_id as "formlibStandardIdOld",
                dprt.formlib_standard_id::varchar as "formlibStandardId",
                dp.product_name as "yyProductName",
                su.user_name as "createUserName",
                su2.user_name as "updateUserName",
                dt.type_name || '(' || dt.form_page_url || ')' as "pathName",
                dprt.path_id::varchar as "pathId"
        from
            csm.dict_formlib_standard_type dprt
        left join csm.sys_user su on dprt.creater_id = su.sys_user_id
        left join csm.sys_user su2 on dprt.updater_id = su2.sys_user_id
        left join csm.dict_product dp on dprt.yy_product_id = dp.yy_product_id
        left join csm.dict_form_type dt on dprt.path_id = dt.dict_form_type_id
        <where>
            <if test="ids != null and ids.size() > 0">
                and dprt.formlib_standard_id in
                <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="yyProductId != null">
                and dprt.yy_product_id = #{yyProductId}
            </if>
        </where>
        order by dprt.yy_product_id,  dprt.formlib_standard_type_code, dprt.order_num, dprt.create_time desc
    </select>
    <select id="findStandardTypeDictData" resultType="com.msun.csm.common.model.BaseCodeNameResp">
        select  dprt.formlib_standard_type_id   "id",
                dprt.formlib_standard_type_name  "name"
        from
            csm.dict_formlib_standard_type dprt
        where dprt.is_deleted = 0
        <if test="yyProductId != null">
            and dprt.yy_product_id = #{yyProductId}
        </if>
        <if test="formlibStandardId != null">
            and dprt.formlib_standard_id = #{formlibStandardId}
        </if>
        order by dprt.yy_product_id, dprt.order_num, dprt.create_time desc
    </select>
</mapper>
