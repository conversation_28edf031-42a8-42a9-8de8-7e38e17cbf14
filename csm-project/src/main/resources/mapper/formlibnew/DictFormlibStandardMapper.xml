<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibnew.DictFormlibStandardMapper">
    <update id="updateDataById">
        update csm.dict_formlib_standard
        <set>
            <if test="formlibStandardCode != null">
                formlib_standard_code = #{formlibStandardCode},
            </if>
            <if test="formlibStandardName != null">
                formlib_standard_name = #{formlibStandardName},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId},
            </if>
            <if test="isStandard != null">
                is_standard = #{isStandard},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="pathId != null">
                path_id = #{pathId},
            </if>
        </set>
        where formlib_standard_id = #{formlibStandardId}

    </update>

    <select id="getRootNodes" resultType="com.msun.csm.model.resp.formlibnew.DictFormlibStandardTteeResp">
        select
            formlib_standard_id::varchar as "value",
            formlib_standard_name as "label"
        from csm.dict_formlib_standard
        where parent_id = -1
        <if test="yyProductId != null" >
            and yy_product_id = #{yyProductId}
        </if>
        <if test="isDeleted != null" >
            and is_deleted = #{isDeleted}
        </if>
        order by formlib_standard_id
    </select>
    <select id="getChildrenById" resultType="com.msun.csm.model.resp.formlibnew.DictFormlibStandardTteeResp"
            parameterType="java.lang.Long">
            select formlib_standard_id as "value",
                   formlib_standard_name as "label"
            from csm.dict_formlib_standard
            where parent_id = #{pid}
            <if test="isDeleted != null">
                and is_deleted = #{isDeleted}
            </if>
            order by formlib_standard_id

    </select>
    <select id="getNodesByDto" resultType="com.msun.csm.model.resp.formlibnew.DictFormlibStandardResp"
            parameterType="com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq">
        select
            x.formlib_standard_id    ,
            x.parent_id::varchar         parent_id     ,
            x.yy_product_id          ,
            x.formlib_standard_code  ,
            x.formlib_standard_name  ,
            x.remark                 ,
            x.order_num              ,
            x.is_standard            ,
            x.is_deleted             ,
            x.creater_id             ,
            x.create_time            ,
            x.updater_id             ,
            x.update_time            ,
            dp.product_name as "yyProductName",
            su.user_name as "createUserName",
            su2.user_name as "updateUserName",
            dt.type_name || '(' || dt.form_page_url || ')' as "pathName",
            x.path_id::varchar as "pathId"

        from csm.dict_formlib_standard x
        left join csm.dict_product dp on x.yy_product_id = dp.yy_product_id
        left join csm.sys_user su on x.creater_id = su.sys_user_id
        left join csm.sys_user su2 on x.updater_id = su2.sys_user_id
        left join csm.dict_form_type dt on x.path_id = dt.dict_form_type_id
        where
          1 = 1
          <if test="yyProductId != null" >
              and x.yy_product_id = #{yyProductId}
          </if>
          <if test="formlibStandardId != null" >
              and x.formlib_standard_id = #{formlibStandardId}
          </if>
          and x.parent_id = #{pid}
        order by x.formlib_standard_id
    </select>
    <select id="getParentCode" resultType="java.lang.String">
        WITH RECURSIVE cte AS (
            SELECT
                formlib_standard_id id,
                parent_id,
                formlib_standard_code as code,
                formlib_standard_code::character varying AS path_codes
            FROM csm.dict_formlib_standard
            WHERE formlib_standard_id = #{id}

            UNION ALL

            SELECT
                t.formlib_standard_id as id ,
                t.parent_id,
                t.formlib_standard_code code,
                t.formlib_standard_code || '-' || c.path_codes
            FROM csm.dict_formlib_standard t
                     INNER JOIN cte c ON t.formlib_standard_id = c.parent_id
        )

        SELECT path_codes FROM cte
        where parent_id = -1
        limit 1


    </select>
    <select id="getAllChildrenIdByPid" resultType="java.lang.Long">
        WITH RECURSIVE cte AS (
            SELECT
                formlib_standard_id id,
                parent_id
            FROM csm.dict_formlib_standard
            WHERE formlib_standard_id = #{id}

            UNION ALL

            SELECT
                t.formlib_standard_id as id ,
                t.parent_id
            FROM csm.dict_formlib_standard t
                     INNER JOIN cte c ON t.parent_id = c.id
            <where>
                <if test="isDeleted != null">
                    and t.is_deleted = #{isDeleted}
                </if>
            </where>
        )
        SELECT id FROM cte

    </select>
    <select id="getFormlibStandardName" resultType="java.lang.String">
        WITH RECURSIVE cte AS (
            SELECT
                formlib_standard_id id,
                parent_id,
                formlib_standard_name as code,
                formlib_standard_name::character varying AS path_codes
            FROM csm.dict_formlib_standard
            WHERE formlib_standard_id = #{id}

            UNION ALL

            SELECT
                t.formlib_standard_id as id ,
                t.parent_id,
                t.formlib_standard_name code,
                t.formlib_standard_name || '->' || c.path_codes
            FROM csm.dict_formlib_standard t
                     INNER JOIN cte c ON t.formlib_standard_id = c.parent_id
        )

        SELECT path_codes FROM cte
        where parent_id = -1
        limit 1
    </select>
    <select id="selectDictFormTypeList" resultType="com.msun.csm.common.model.BaseCodeNameResp"
            parameterType="com.msun.csm.model.req.formlibnew.DictFormLibStandardPageReq">
            select dict_form_type_id::varchar as "id",
                   type_name || '(' || form_page_url || ')' as "name"
            from csm.dict_form_type
            where is_deleted = 0
            and yy_product_id = #{yyProductId}
    </select>
</mapper>
