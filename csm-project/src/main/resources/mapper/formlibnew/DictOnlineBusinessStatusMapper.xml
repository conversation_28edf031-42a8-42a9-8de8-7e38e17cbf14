<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibnew.DictOnlineBusinessStatusMapper">

    <select id="getOnlineStatusPublicData" parameterType="java.lang.String">
        SELECT
            online_code as  id,
            online_name as  "name"
        FROM csm.dict_online_business_status
        WHERE business_code = #{busCode}
        AND is_deleted = 0
    </select>
</mapper>
