<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibnew.FormlibResourceTypeMapper">

    <select id="selectListByPid" resultType="com.msun.csm.model.resp.formlibnew.FormlibResourceSelectType">
        select frt.*,
            su.user_name AS "createUserName",
            su2.user_name AS "updateUserName",
           dfst.formlib_standard_type_name as "formlibStandardTypeName",
           dfs.formlib_standard_name as "formlibStandardName"
        from csm.formlib_resource_type frt
        left join csm.sys_user su on su.sys_user_id = frt.creater_id
        left join csm.sys_user su2 on su2.sys_user_id = frt.updater_id
        LEFT JOIN csm.dict_formlib_standard_type dfst
                  ON frt.formlib_standard_type_id = dfst.formlib_standard_type_id
        LEFT JOIN csm.dict_formlib_standard dfs
                  ON frt.formlib_standard_id = dfs.formlib_standard_id
        where frt.is_deleted = 0
        and frt.formlib_resource_unite_id = #{formlibResourceUniteId}

    </select>
    <select id="getFormlibStandardTypeByParam" resultType="com.msun.csm.dao.entity.proj.projform.DictFormType"
            parameterType="java.lang.Long">
        select * from csm.dict_form_type dft
        inner join csm.dict_formlib_standard_type dfst
                  on dfst.path_id = dft.dict_form_type_id
        where formlib_standard_type_id = #{formlibStandardTypeId}
    </select>
</mapper>
