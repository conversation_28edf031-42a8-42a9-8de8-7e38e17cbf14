<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdKtJobMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO csm.bd_kt_job (
        id,
        idx,
        job_name,
        vars,
        depend_table_name_en,
        product_name,
        job_type,
        opened,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
        )
        VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.id},
            #{obj.idx},
            #{obj.jobName},
            #{obj.vars},
            #{obj.dependTableNameEn},
            #{obj.productName},
            #{obj.jobType},
            #{obj.opened},
            #{obj.isDeleted},
            #{obj.createrId},
            #{obj.createTime},
            #{obj.updaterId},
            #{obj.updateTime}
            )
        </foreach>
        ON CONFLICT (job_name) DO UPDATE SET
        idx = EXCLUDED.idx,
        vars = EXCLUDED.vars,
        depend_table_name_en = EXCLUDED.depend_table_name_en,
        product_name = EXCLUDED.product_name,
        job_type = EXCLUDED.job_type,
        opened = EXCLUDED.opened,
        is_deleted = EXCLUDED.is_deleted,
        updater_id = EXCLUDED.updater_id,
        update_time = EXCLUDED.update_time;
    </insert>

    <select id="selectJobByTableName" resultType="com.msun.csm.dao.entity.basedata.BdKtJob">
        select a.* from csm.bd_kt_job as a
        inner join csm.bd_kt_job_depend_table as b on a.job_name = b.kt_job_name and b.is_deleted = 0
        where a.job_type = #{jobType} and b.table_name_en = #{tableNameEn} and a.is_deleted = 0 and a.opened = true
        <if test="jobName != null and jobName != ''">
            and a.job_name = #{jobName}
        </if>
        order by a.idx
    </select>
</mapper>
