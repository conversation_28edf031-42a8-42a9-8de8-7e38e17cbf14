<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdImportPlatformRecordMapper">
    <insert id="insertUpdate">
        INSERT INTO csm.bd_import_platform_record (
        id,
        project_number,
        hospital_name,
        hospital_id,
        org_id,
        table_name_en,
        table_name_cn,
        data_schema,
        success,
        err_msg,
        total,
        suc_total,
        valid_status,
        valid_err_msg,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
        )
        VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.id},
            #{obj.projectNumber},
            #{obj.hospitalName},
            #{obj.hospitalId},
            #{obj.orgId},
            #{obj.tableNameEn},
            #{obj.tableNameCn},
            #{obj.dataSchema},
            #{obj.success},
            #{obj.errMsg},
            #{obj.total},
            #{obj.sucTotal},
            #{obj.validStatus},
            #{obj.validErrMsg},
            #{obj.isDeleted},
            #{obj.createrId},
            #{obj.createTime},
            #{obj.updaterId},
            #{obj.updateTime}
            )
        </foreach>
        ON CONFLICT (project_number, hospital_id, table_name_en) DO UPDATE SET
        org_id = EXCLUDED.org_id,
        hospital_name = EXCLUDED.hospital_name,
        table_name_cn = EXCLUDED.table_name_cn,
        data_schema = EXCLUDED.data_schema,
        success = EXCLUDED.success,
        err_msg = EXCLUDED.err_msg,
        total = EXCLUDED.total,
        suc_total = EXCLUDED.suc_total,
        valid_status = EXCLUDED.valid_status,
        valid_err_msg = EXCLUDED.valid_err_msg,
        is_deleted = EXCLUDED.is_deleted,
        updater_id = EXCLUDED.updater_id,
        update_time = EXCLUDED.update_time;
    </insert>

    <select id="findList" parameterType="com.msun.csm.dao.entity.basedata.BdImportPlatformRecord" resultType="com.msun.csm.dao.entity.basedata.BdImportPlatformRecord">
        select a.* from bd_import_platform_record as a
        inner join bd_table_info as b on a.table_name_en = b.table_name_en and b.is_deleted = 0
        where a.is_deleted = 0
        <if test="args.projectNumber != null and args.projectNumber != ''">
            and a.project_number = #{args.projectNumber}
        </if>
        <if test="args.hospitalId != null and args.hospitalId != 0">
            and a.hospital_id = #{args.hospitalId}
        </if>
        <if test="args.success != null">
            and a.success = #{args.success}
        </if>
        <if test="args.validStatus != null">
            and a.valid_status = #{args.validStatus}
        </if>
        order by a.create_time desc
    </select>
</mapper>
