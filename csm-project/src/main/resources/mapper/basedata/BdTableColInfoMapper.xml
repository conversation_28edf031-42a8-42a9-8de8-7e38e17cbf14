<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdTableColInfoMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO csm.bd_table_col_info (
        id,
        table_name_en,
        col_name_cn,
        col_name_en,
        col_type,
        col_length,
        is_unique,
        not_null,
        is_pk,
        dict_code,
        col_desc,
        opened,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
        )
        VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.id},
            #{obj.tableNameEn},
            #{obj.colNameCn},
            #{obj.colNameEn},
            #{obj.colType},
            #{obj.colLength},
            #{obj.isUnique},
            #{obj.notNull},
            #{obj.isPk},
            #{obj.dictCode},
            #{obj.colDesc},
            #{obj.opened},
            #{obj.isDeleted},
            #{obj.createrId},
            #{obj.createTime},
            #{obj.updaterId},
            #{obj.updateTime}
            )
        </foreach>
        ON CONFLICT (table_name_en, col_name_en) DO UPDATE SET
        col_name_cn = EXCLUDED.col_name_cn,
        col_type = EXCLUDED.col_type,
        col_length = EXCLUDED.col_length,
        is_unique = EXCLUDED.is_unique,
        not_null = EXCLUDED.not_null,
        is_pk = EXCLUDED.is_pk,
        dict_code = EXCLUDED.dict_code,
        col_desc = EXCLUDED.col_desc,
        opened = EXCLUDED.opened,
        is_deleted = EXCLUDED.is_deleted,
        updater_id = EXCLUDED.updater_id,
        update_time = EXCLUDED.update_time;
    </insert>
</mapper>
