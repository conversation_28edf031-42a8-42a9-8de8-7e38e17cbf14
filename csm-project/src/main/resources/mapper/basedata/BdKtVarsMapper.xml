<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdKtVarsMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO csm.bd_kt_vars (
        var_key,
        var_val,
        project_number,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
        )
        VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.varKey},
            #{obj.varVal},
            #{obj.projectNumber},
            #{obj.isDeleted},
            #{obj.createrId},
            #{obj.createTime},
            #{obj.updaterId},
            #{obj.updateTime}
            )
        </foreach>
        ON CONFLICT (project_number, var_key) DO UPDATE SET
        var_val = EXCLUDED.var_val,
        is_deleted = EXCLUDED.is_deleted,
        updater_id = EXCLUDED.updater_id,
        update_time = EXCLUDED.update_time;
    </insert>
</mapper>
