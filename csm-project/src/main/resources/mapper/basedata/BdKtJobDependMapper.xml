<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdKtJobDependMapper">
    <insert id="insertUpdate" parameterType="com.msun.csm.dao.entity.basedata.BdKtJobDepend">
        INSERT INTO csm.bd_kt_job_depend (kt_job_name,
                                          parent_kt_job_name,
                                          is_deleted,
                                          creater_id,
                                          create_time,
                                          updater_id,
                                          update_time)
        VALUES (#{ktJobName},
                #{parentKtJobName},
                #{isDeleted},
                #{createrId},
                #{createTime},
                #{updaterId},
                #{updateTime})
        ON CONFLICT (kt_job_name, parent_kt_job_name) DO UPDATE SET is_deleted  = EXCLUDED.is_deleted,
                                                                    updater_id  = EXCLUDED.updater_id,
                                                                    update_time = EXCLUDED.update_time;
    </insert>
</mapper>
