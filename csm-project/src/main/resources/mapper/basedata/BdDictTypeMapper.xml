<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdDictTypeMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO dict_data.dict_type (
            dict_code,
            dict_name,
            opened,
            creater_id
        )
        VALUES
        <foreach collection="data" item="obj" separator=",">
            (
            #{obj.dictCode},
            #{obj.dictName},
            #{obj.opened},
            #{obj.createrId}
            )
        </foreach>
        ON CONFLICT (dict_code) DO UPDATE SET
        dict_name = EXCLUDED.dict_name,
        opened = EXCLUDED.opened,
        updater_id = EXCLUDED.updater_id,
        update_time = now();
    </insert>
</mapper>
