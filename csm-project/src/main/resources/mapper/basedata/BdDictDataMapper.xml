<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdDictDataMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO dict_data.dict_data (
        dict_code,
        dict_value,
        opened,
        creater_id
        )
        VALUES
        <foreach collection="data" item="obj" separator=",">
            (
            #{obj.dictCode},
            #{obj.dictValue},
            #{obj.opened},
            #{obj.createrId}
            )
        </foreach>
        ON CONFLICT (dict_code, dict_value) DO UPDATE SET
        opened = EXCLUDED.opened,
        updater_id = EXCLUDED.updater_id,
        update_time = now();
    </insert>
</mapper>
