<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdKtJobDependTableMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO public.bd_kt_job_depend_table (
        id,
        kt_job_name,
        table_name_en,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
        )
        VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.id},
            #{obj.ktJobName},
            #{obj.tableNameEn},
            #{obj.isDeleted},
            #{obj.createrId},
            #{obj.createTime},
            #{obj.updaterId},
            #{obj.updateTime}
            )
        </foreach>
        ON CONFLICT (kt_job_name, table_name_en) DO UPDATE SET
        is_deleted = EXCLUDED.is_deleted,
        updater_id = EXCLUDED.updater_id,
        update_time = EXCLUDED.update_time;
    </insert>
</mapper>
