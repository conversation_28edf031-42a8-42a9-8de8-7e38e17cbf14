<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.BdTableInfoMapper">
    <insert id="insertUpdate" parameterType="java.util.List">
        INSERT INTO csm.bd_table_info (
        id,

        table_name_cn,
        table_name_en,
        product_name,
        opened,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
        )
        VALUES
        <foreach collection="list" item="obj" separator=",">
            (
            #{obj.id},

            #{obj.tableNameCn},
            #{obj.tableNameEn},
            #{obj.productName},
            #{obj.opened},
            #{obj.isDeleted},
            #{obj.createrId},
            #{obj.createTime},
            #{obj.updaterId},
            #{obj.updateTime}
            )
        </foreach>
        ON CONFLICT (table_name_en) DO UPDATE SET
        table_name_cn = EXCLUDED.table_name_cn,
        product_name = EXCLUDED.product_name,
        opened = EXCLUDED.opened,
        is_deleted = EXCLUDED.is_deleted,
        updater_id = EXCLUDED.updater_id,
        update_time = EXCLUDED.update_time;
    </insert>
</mapper>
