<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.basedata.KtMapper">

    <select id="getAllDir" resultType="com.msun.csm.dao.entity.basedata.KtDir">
        select id_directory as id, id_directory_parent as pid, directory_name as name
        from public.r_directory
        where id_directory > 0
    </select>


    <select id="getAllJob" resultType="com.msun.csm.dao.entity.basedata.KtJob">
        select id_job as id, id_directory as pid, name
        from public.r_job
        where id_job > 0
    </select>
</mapper>
