<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.oas.FormPatternMapper">
    <select id="selectByDiQu" resultType="com.msun.csm.model.csm.OutFormPatternVO">
        select form_pattern_id, form_pattern_name, description, sort, form_content_pc, form_configuration_pc,
        t1.form_category_code, t1.form_content_pad, form_configuration_pad, yy_product_id,
        form_content_web, form_configuration_web , t3.dict_province_name, t4.dict_city_name
        from form_library.form_pattern t1
        left join csm.proj_hospital_info t2 on t1.hospital_id = t2.cloud_hospital_id
        left join csm.dict_province t3 on t2.province_id = t3.dict_province_id
        left join csm.dict_city t4 on t2.city_id = t4.dict_city_id
        where t1.form_category_code = #{formCategoryCode,jdbcType=VARCHAR}
        and t1.invalid_flag = '0'
        <if test="searchText != null and searchText != ''">
            and (
            t3.dict_province_name like concat('%', #{searchText,jdbcType=VARCHAR}, '%')
            or t4.dict_city_name like concat('%', #{searchText,jdbcType=VARCHAR}, '%')
            or t1.form_pattern_name like concat('%', #{searchText,jdbcType=VARCHAR}, '%')
            )
        </if>
    </select>

    <select id="findByPage" resultType="com.msun.csm.model.vo.oas.FormPatternFindByPageVO">
        select form_pattern_id, form_pattern_name, fc.form_category_name, t1.hospital_id,t2.hospital_name, t1.his_creater_name
        from form_library.form_pattern t1
        left join form_library.form_catetory fc
        on t1.form_category_code = fc.form_category_code and fc.invalid_flag = '0' and fc.hospital_id = -1
        left join csm.proj_hospital_info t2 on t1.hospital_id = t2.cloud_hospital_id
        where t1.invalid_flag = '0'
        <!-- 按照产品ID查询       -->
        <if test="yyProductId != null and yyProductId != ''">
            and t1.yy_product_id = #{yyProductId}
        </if>
        <!--  按照医院ID集合查询      -->
        <if test="hospitalInfoIds != null and hospitalInfoIds.size() > 0">
            and t2.hospital_info_id in
            <foreach collection="hospitalInfoIds" item="hospitalInfoId" open="(" separator="," close=")">
                #{hospitalInfoId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <!--  按照表单类型编码集合查询      -->
        <if test="formCategoryCodes != null and formCategoryCodes.size() > 0">
            and t1.form_category_code in
            <foreach collection="formCategoryCodes" item="formCategoryCode" open="(" separator="," close=")">
                #{formCategoryCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <!--        按照hisUpdateTime降序排-->
        order by t1.his_update_time desc
    </select>
</mapper>
