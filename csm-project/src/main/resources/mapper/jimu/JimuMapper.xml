<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.jimu.JimuReportMapper">

    <select id="findProjectPeriod" resultType="com.alibaba.fastjson.JSONObject">
        SELECT rci.custom_name            AS "customname",
               rci.dept_id           as "deptYYId",
               sd.dept_name          AS "部门",
                sd.dept_name          AS "deptName",
               dp.dict_province_name AS "省份",
               dc.dict_city_name     AS "城市",
               dt.dict_town_name          AS "区县",
               CASE
                   WHEN rci.telesales_flag = 1 THEN
                       '3电销客户'
                   WHEN rci.telesales_flag != 1
                            AND rci.custom_type = 1 THEN
                       '1单体客户'
                   WHEN rci.telesales_flag != 1
                            AND rci.custom_type = 2 THEN
                       '2区域客户'
                   END                    AS "客户类型",
               rci.custom_type as "customType",
               rci.upgradation_type       as "upgradationTypeId",
               CASE
                   WHEN rci.upgradation_type = 1 THEN
                       '老换新'
                   WHEN rci.upgradation_type = 2 THEN
                       '新客户'
                   WHEN rci.upgradation_type = 3 THEN
                       '老体系'
                   ELSE ''
                   END                       "upgradationType",
               sd1.dept_name              AS "客服分公司",
                sd1.dept_name          AS "customDeptName",
               ppi.project_deliver_status as "projectDeliverStatus",
               CASE
                   WHEN ppi.project_deliver_status = 1 THEN
                       '已派工'
                   WHEN ppi.project_deliver_status = 2 THEN
                       '已调研'
                   WHEN ppi.project_deliver_status = 3 THEN
                       '已入驻'
                   WHEN ppi.project_deliver_status = 4 THEN
                       '准备完成'
                   WHEN ppi.project_deliver_status = 5 THEN
                       '已上线'
                   WHEN ppi.project_deliver_status = 6 THEN
                       '已验收'
                   WHEN ppi.project_deliver_status = 7 THEN
                       '已启动'
                   WHEN ppi.project_deliver_status = 11 THEN
                       '一次验收通过'
                   END                       "实施进度",
               ppi.work_time              AS "worktime",
               ppi.settle_in_time         AS "入驻时间",
               ppi.settle_in_time as "settleInTime",
               ppi.online_time            AS "onlinetime",
               ppi.external_accept_time   AS "外部验收时间",
                ppi.external_accept_time   AS "externalAcceptTime",
               ppi.apply_accept_time AS "最后提交验收申请时间",
               ppi.apply_accept_time as "lastApplyAcceptTime",
               ppi.accept_time            AS "验收通过时间",
                ppi.accept_time            AS "acceptTime",
               ppi.settle_in_time         AS "入驻时间(统计)",
               ppi.accept_time            AS "验收时间(统计)",
               rci.duration_reduction     AS "工期减免(统计)",
        coalesce(sd2.dept_name ,sd.dept_name  )             AS "销售中心",
        coalesce(rci.sale_center_id,rci.dept_id)             AS "saleCenterId",
               case when rci.custom_deliver_status = 2 then 2
                    when rci.custom_deliver_status = 3 then 3
                   else 1 end AS "customDeliverStatus",
               rci.custom_deliver_status  AS "customDeliverStatusId",
               rci.online_before_duration AS "onlineBeforeDuration",
               rci.online_after_duration AS "onlineAfterDuration",

                case when  ppi.settle_in_time is not null and  ppi.online_time is not null
                then	EXTRACT(DAY FROM (to_char(ppi.online_time,'yyyy-MM-dd')::timestamp - to_char(ppi.settle_in_time,'yyyy-MM-dd')::timestamp)) + 1 - coalesce(rci.online_before_duration,0)
                else  0  end "settleToOnline",

                case when  ppi.external_accept_time is not null and   ppi.online_time is not null
                then	EXTRACT(DAY FROM (to_char(ppi.external_accept_time,'yyyy-MM-dd')::timestamp - to_char(ppi.online_time,'yyyy-MM-dd')::timestamp))
                else  0  end "onlineToExternal",

                case when  ppi.apply_accept_time is not null and   ppi.online_time is not null
                then	EXTRACT(DAY FROM (to_char(ppi.apply_accept_time,'yyyy-MM-dd')::timestamp - to_char(ppi.online_time,'yyyy-MM-dd')::timestamp)) - coalesce(rci.online_after_duration,0)
                else  0  end "onlineToAccept",

                case when  ppi.settle_in_time is not null and   ppi.apply_accept_time is not null
                then	EXTRACT(DAY FROM (to_char(ppi.apply_accept_time,'yyyy-MM-dd')::timestamp - to_char(ppi.settle_in_time,'yyyy-MM-dd')::timestamp)) + 1 - coalesce(rci.online_before_duration,0)- coalesce(rci.online_after_duration,0)
                else  0  end "settleToAccept",

        us.user_name
        FROM csm.report_custom_info rci
                 LEFT JOIN csm.sys_dept sd ON rci.dept_id = sd.dept_yunying_id
                 LEFT JOIN csm.dict_province dp ON rci.province_id = dp.dict_province_id
                 LEFT JOIN csm.dict_city dc ON rci.ctiy_id = dc.dict_city_id
                 LEFT JOIN csm.dict_town dt ON rci.town_id = dt.dict_town_id
                 LEFT JOIN csm.sys_dept sd1 ON sd1.dept_yunying_id = rci.service_org_id
                 LEFT JOIN csm.proj_project_info ppi ON ppi.project_info_id = rci.project_info_id
                LEFT JOIN csm.sys_user us ON ppi.project_leader_id = us.sys_user_id
                 LEFT JOIN csm.sys_dept sd2 ON rci.sale_center_id = sd2.dept_yunying_id
        WHERE rci.is_deleted = 0
        <if test="customType != null and customType != 0 and customType != 3">
            and rci.custom_type=#{customType}
            and rci.telesales_flag != 1
        </if>
        <if test="customType != null and customType == 3">
            and rci.telesales_flag = 1
        </if>
        <if test="customName != null and customName != ''">
            and rci.custom_name like concat('%', #{customName}, '%')
        </if>
        <if test="onlinetimeBegin != null">
            and ppi.online_time >= #{onlinetimeBegin}
        </if>
        <if test="onlinetimeEnd != null">
            and ppi.online_time &lt;= #{onlinetimeEnd}
        </if>
        <if test="worktimeBegin != null">
            and ppi.work_time >= #{worktimeBegin}
        </if>
        <if test="worktimeEnd != null">
            and ppi.work_time &lt;= #{worktimeEnd}
        </if>
      <if test="upgradationTypeList != null">
          and rci.upgradation_type in
          <foreach collection="upgradationTypeList" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 1">
            and ( rci.custom_deliver_status  is null or rci.custom_deliver_status not in (2,3,4,5) )
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 2">
            and rci.custom_deliver_status = 2
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 3">
            and rci.custom_deliver_status = 3
        </if>
        <if test="customDeliverStatus != null and customDeliverStatus == 4">
            and rci.custom_deliver_status = 4
        </if>
        <if test="onlineType != null and onlineType == 1 ">
            and ppi.online_time is not null
            and rci.upgradation_type != 3
        </if>
        <if test="onlineType != null and onlineType == 0 ">
            and ppi.online_time is null
            and rci.upgradation_type != 3
        </if>
        <if test="deptName != null and deptName != '' and deptName != '合计'">
            and coalesce(sd2.dept_name ,sd.dept_name  )  like concat('%', #{deptName}, '%')
        </if>
        <if test="customDeptName != null and customDeptName != ''">
            and sd2.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="projectDeliverStatus != null ">
            and ppi.project_deliver_status =#{projectDeliverStatus}
        </if>
        <if test="lastApplyAcceptTimeBegin != null">
            and ppi.apply_accept_time >= #{lastApplyAcceptTimeBegin}
        </if>
        <if test="lastApplyAcceptTimeEnd != null">
            and ppi.apply_accept_time &lt;= #{lastApplyAcceptTimeEnd}
        </if>
        ORDER BY work_time DESC
    </select>

    <select id="findProjectPeriodByProjectInfo" resultType="com.alibaba.fastjson.JSONObject">
                select
                    distinct
                    pci.custom_name as "customName",
                    sd.dept_name as "deptName",
                    su.user_name as "userName",
                    case
                        when ppi .project_deliver_status = 1 then '已派工'
                        when ppi .project_deliver_status = 2 then '已调研'
                        when ppi .project_deliver_status = 3 then '已入驻'
                        when ppi .project_deliver_status = 4 then '准备完成'
                        when ppi .project_deliver_status = 5 then '已上线'
                        when ppi .project_deliver_status = 6 then '已验收'
                        when ppi .project_deliver_status = 7 then '已启动'
                        when ppi .project_deliver_status = 11 then '一次验收通过'
                        end "实施进度",
                    ppi .project_deliver_status AS "projectDeliverStatus",
                    case
                        when pci.telesales_flag = 1 then '电销客户'
                        when pci.telesales_flag = 0
                            and ppi .project_type = 1 then '单体客户'
                        when pci.telesales_flag != 1
                            and ppi .project_type = 2 then '区域客户'
                        end as "客户类型",
                        CASE
                        WHEN ppi.upgradation_type = 1 THEN
                        '老换新'
                        WHEN ppi.upgradation_type = 2 THEN
                        '新客户'
                        WHEN ppi.upgradation_type = 3 THEN
                        '老体系'
                        ELSE ''
                        END                       "项目类型",
                    ppi.upgradation_type as "upgradationType",
                    ppi.project_type as "projectType",
                    ppi.work_time as "worktime",
                    ppi.receive_time "项目组接收时间" ,
                    ppi.survey_complete_time "调研完成时间" ,
                    ppi.settle_in_time "入驻时间" ,
                    ppi.pre_complete_time "准备完成时间" ,
                    ppi.online_time as onlinetime,
                    ppi.apply_accept_time "最后提验收时间",
                    ppi.external_accept_time "外部验收时间",
                    ppi.accept_time "验收通过时间",
                    ppi.accept_time "acceptTime",
                    ppi.settle_in_time "settleInTime",
                    ppi.apply_accept_time "lastApplyAcceptTime"
                from
                    csm.proj_project_info ppi
                        left join csm.proj_custom_info pci on
                        ppi.custom_info_id = pci .custom_info_id
                       left join csm.sys_dept sd on sd.dept_yunying_id = ppi.project_team_id
                       left join csm.sys_user su on su.sys_user_id = ppi.project_leader_id
                where
                    ppi .his_flag = 1
                  and ppi.is_deleted = 0
                  and pci .is_deleted = 0
                <if test="customName != null and customName != ''">
                    and pci.custom_name like concat('%',#{customName},'%')
                </if>
                <if test="deptName != null and deptName != ''">
                    and sd.dept_name like concat('%',#{deptName},'%')
                </if>
                <if test="onlinetimeBegin != null ">
                    and ppi.online_time >= #{onlinetimeBegin}
                </if>
                <if test="onlinetimeEnd != null">
                    and ppi.online_time &lt;= #{onlinetimeEnd}
                </if>
                <if test="worktimeBegin != null ">
                    and ppi.work_time >= #{worktimeBegin}
                </if>
                <if test="worktimeEnd != null">
                    and ppi.work_time &lt;= #{worktimeEnd}
                </if>
                <if test="lastApplyAcceptTimeBegin != null ">
                    and ppi.apply_accept_time >= #{lastApplyAcceptTimeBegin}
                </if>
                <if test="lastApplyAcceptTimeEnd != null">
                    and ppi.apply_accept_time &lt;= #{lastApplyAcceptTimeEnd}
                </if>
                <if test="upgradationTypeList != null">
                    and ppi.upgradation_type in
                    <foreach collection="upgradationTypeList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="customType != null and customType != 0 and customType != 3">
                    and ppi.project_type=#{customType}
                    and pci.telesales_flag != 1
                </if>
                <if test="projectDeliverStatus != null ">
                    and ppi.project_deliver_status =#{projectDeliverStatus}
                </if>
                <if test="customType != null and customType == 3">
                    and pci.telesales_flag = 1
                </if>
                order by ppi.online_time
    </select>

    <select id="findReportCustomInfo" resultType="com.msun.csm.model.jimu.vo.JmReportCustomInfoVO">
        select
        case
        when rci.upgradation_type = 1 then '老换新'
        when rci.upgradation_type = 2 then '新客户'
        when rci.upgradation_type = 3 then '老体系'
        else ''
        end upgradation_type,
        case when rci.custom_type=1 then '单体'
        when rci.custom_type=2 then '区域'
        else ''
        end custom_type,
        sd1.dept_name as dept_name,
        sd.dept_name as dept_company_name,
        rci.custom_deliver_status,
        case when ppi.project_deliver_status =1 then '已派工'
        when ppi.project_deliver_status =2 then '已调研'
        when ppi.project_deliver_status =3 then '已入驻'
        when ppi.project_deliver_status =4 then '准备完成'
        when ppi.project_deliver_status =5 then '已上线'
        when ppi.project_deliver_status =6 then '已验收'
        end project_deliver_status,
        rci.custom_name as custom_name,
        coalesce(ppi.work_time::text,'') as work_time,
        coalesce(ppi.settle_in_time::text,'') as settle_in_time,
        coalesce(ppi.online_time::text,'') as online_time,
        coalesce(ppi.external_accept_time::text,'') as external_accept_time,
        coalesce(ppi.apply_accept_time::text,'') as apply_accept_time,
        coalesce(ppi.accept_time::text,'') as accept_time,
        coalesce(rci.settle_in_time ::text,'')as rep_settle_in_time,
        coalesce(rci.accept_time ::text,'')as rep_accept_time,
        rci.project_deliver_status as rep_deliver_status,
        sd2.dept_name as sales_center,
        dp.dict_province_name as province,
        dc.dict_city_name as city,
        dt.dict_town_name as town,
        rci.custom_info_id
        from
        csm.report_custom_info rci
        left join csm.proj_project_info ppi on
        rci.project_info_id = ppi.project_info_id
        left join csm.proj_custom_info pci on pci.custom_info_id =rci.custom_info_id
        left join csm.sys_dept sd on pci.custom_dept_id =sd.dept_yunying_id
        left join csm.sys_dept sd1 on pci.custom_team_id =sd1.dept_yunying_id

        <!--销售中心-->
        left join csm.proj_order_info poi on ppi.order_info_id =poi.order_info_id
        left join csm.proj_contract_info pci2 on pci2.contract_info_id =poi.contract_info_id
        left join csm.sys_dept sd2 on sd2.dept_yunying_id =pci2.contract_sign_team_id
        <!--行政区划-->
        left join csm.proj_custom_detail_info pcdi on pcdi.custom_info_id =rci.custom_info_id
        left join csm.dict_province dp  on dp.dict_province_id =pcdi.province_id
        left join csm.dict_city dc on dc.dict_city_id =pcdi.city_id
        left join csm.dict_town dt on dt.dict_town_id =pcdi.town_id
        <!--医院行政区划-->
        <where>
            <!--<if test="customName != null and customName != ''">
                 and pci.custom_name like concat('%',#{customName},'%')
             </if>-->
            <if test="upgradationType != null ">
                and rci.upgradation_type=#{upgradationType}
            </if>
            <if test="customType != null and customType != 0 and customType != 3">
                and rci.custom_type=#{customType}
                and rci.telesales_flag != 1
            </if>
            <if test="customType != null and customType == 3">
                and rci.telesales_flag = 1
            </if>
            <if test="deptName != null and deptName !=''">
                and sd.dept_name like concat('%',#{deptName},'%')
            </if>

            <if test="worktimeBegin != null ">
                and ppi.work_time >= #{worktimeBegin}
            </if>
            <if test="worktimeEnd != null">
                and ppi.work_time &lt;= #{worktimeEnd}
            </if>
            <if test="settleInTimeBegin != null ">
                and ppi.settle_in_time >= #{settleInTimeBegin}
            </if>
            <if test="settleInTimeEnd != null">
                and ppi.settle_in_time &lt;= #{settleInTimeEnd}
            </if>


            <if test="onlineTimeBegin != null ">
                and ppi.online_time >= #{onlineTimeBegin}
            </if>
            <if test="onlineTimeEnd != null">
                and ppi.online_time &lt;= #{onlineTimeEnd}
            </if>
            <if test="externalAcceptTimeBegin != null ">
                and ppi.external_accept_time >= #{externalAcceptTimeBegin}
            </if>
            <if test="externalAcceptTimeEnd != null">
                and ppi.external_accept_time &lt;= #{externalAcceptTimeEnd}
            </if>
            <if test="applyAcceptTimeBegin != null ">
                and ppi.apply_accept_time >= #{applyAcceptTimeBegin}
            </if>
            <if test="applyAcceptTimeEnd != null">
                and ppi.apply_accept_time &lt;= #{applyAcceptTimeEnd}
            </if>
            <if test="acceptTimeBegin != null ">
                and ppi.accept_time >= #{acceptTimeBegin}
            </if>
            <if test="acceptTimeEnd != null">
                and ppi.accept_time &lt;= #{acceptTimeEnd}
            </if>
            <if test="repSettleInTimeBegin != null ">
                and rci.settle_in_time >= #{repSettleInTimeBegin}
            </if>
            <if test="repSettleInTimeEnd != null">
                and rci.settle_in_time &lt;= #{repSettleInTimeEnd}
            </if>
            <if test="repAcceptTimeBegin != null ">
                and rci.accept_time >= #{repAcceptTimeBegin}
            </if>
            <if test="repAcceptTimeEnd != null">
                and rci.accept_time &lt;= #{repAcceptTimeEnd}
            </if>
            <if test="upgradationType != null ">
                and rci.upgradation_type=#{upgradationType}
            </if>

            <if test="deptName != null and deptName != ''">
                and sd.dept_name like concat('%',#{customName},'%')
            </if>
            <if test="projectDeliverStatus != null ">
                and ppi.project_deliver_status =#{projectDeliverStatus}
            </if>
            <if test="province != null ">
                and dp.dict_province_id =#{province}
            </if>
        </where>
        order by ppi.work_time desc
    </select>
    <select id="findProjectData" resultType="com.msun.csm.model.jimu.vo.JmReportProjectStatisticsDataVO">
        select distinct
        pci.custom_name,
        ord.delivery_order_no,
        prd.name as product_name,
        prd.new_product_id yy_product_id,
        case
            when ppi .project_deliver_status = 1 then '已派工'
            when ppi .project_deliver_status = 2 then '已调研'
            when ppi .project_deliver_status = 3 then '已入驻'
            when ppi .project_deliver_status = 4 then '准备完成'
            when ppi .project_deliver_status = 5 then '已上线'
            when ppi .project_deliver_status = 6 then '已验收'
            when ppi .project_deliver_status = 7 then '已启动'
            when ppi .project_deliver_status = 11 then '一次验收通过'
            end project_deliver_status,
        case
            when pci.telesales_flag = 1 then '电销客户'
            when pci.telesales_flag != 1
            and ppi .project_type = 1 then '单体客户'
            when pci.telesales_flag != 1
            and ppi .project_type = 2 then '区域客户'
        end as telesales_flag,
        ppi.work_time,
        ppi.receive_time,
        coalesce(mil.update_time, ppi.survey_complete_time) survey_complete_time,
        ppi.settle_in_time,
        ppi.online_time,
        ppi.pre_complete_time,
        ppi.apply_accept_time,
        ppi.accept_time,
        coalesce(ss.survey_add_equip, 0) survey_add_equip,
        coalesce(sss.survey_add_third, 0) survey_add_third,
        0 survey_add_special_require,
        coalesce(hardware.survey_add_hard_ware, 0) survey_add_hard_ware,

        case when addproduct.add_product_count > 0 then 1 else 0 end as  add_product_count,
        case when author.authorization_count > 0 then 1 else 0 end as authorization_count,

        coalesce(ss.online_finsin_equip, 0 ) online_finsin_equip,
        coalesce(sstask.online_finsin_task, 0) online_finsin_task,
        coalesce(report.online_finsin_report, 0) online_finsin_report,
        coalesce(form.online_finsin_form, 0) online_finsin_form,
        0  online_finsin_third,
        prd.order_no,
        ppi.project_info_id
        from
        csm.proj_project_info ppi
        left join (
        select project_info_id,max(survey_review_time) update_time from csm.proj_project_review_info
        where is_deleted = 0
        and   entry_review_status = 1
        group by project_info_id
        ) mil on mil.project_info_id = ppi.project_info_id
        left join csm.tmp_project_new_vs_old tpnvo on tpnvo.new_project_info_id = ppi.project_info_id
        left join csm.proj_custom_info pci on
        ppi.custom_info_id = pci .custom_info_id
        left join csm.proj_order_info ord on ppi.order_info_id = ord.order_info_id

        left join (
            select
            id as new_product_id,
            name,
            re.project_info_id,
            old_product_id,
            order_no
            from csm.proj_product_deliver_record re
            inner join (
            select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
            ps.id as old_product_id,
            case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
            else dp.product_name end  as name,
            dp.order_no
            from csm.dict_product dp
            left join csm.dict_product_vs_modules dpm
            on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
            left join platform.product ps on dp.yy_product_id = ps.product_yunying_id
            where dp.is_deleted = 0
            ) ss on re.product_deliver_id = ss.id
            where re.is_deleted = 0
            union all
            select
            999999 as new_product_id,
            '其他' as  name,
            ppiss.project_info_id,
            999999  old_product_id,
            9999999999999   order_no
            from
            csm.proj_project_info ppiss
            union all
            select
            ppsrd.yy_product_id  as new_product_id ,
            dp.product_name  as name,
            ppsrd.yy_product_id old_product_id,
            ppi.project_info_id ,
            dp.order_no
            from
            csm.proj_product_supplementary_record ppsr
            inner join csm.proj_product_supplementary_record_detail ppsrd on ppsrd .product_supplementary_record_id = ppsr.product_supplementary_record_id
            inner join csm.proj_project_info ppi on ppsr.custom_info_id = ppi.custom_info_id
            inner join csm.dict_product dp on dp.yy_product_id = ppsrd.yy_product_id
            where ppsr.is_deleted = 0 and ppi.is_deleted =0
        ) prd on prd.project_info_id = ppi.project_info_id
        left join (
            select
                s.yy_product_id product_id ,
                s1.project_info_id,
                sum(case when s.equip_status != '5' and s1.online_time is not null
                and (s.test_pass_time > s1.online_time or s.test_pass_time is null )
                then 1 else 0 end) as
                online_finsin_equip,
                sum(
                case when
                (s.required_flag = 1 and s1.survey_fins_time is not null and s.create_time > s1.survey_fins_time
                and coalesce(s1.online_time, now())  > s.create_time
                )
                or  (s1.survey_fins_time is not null and s.apply_time is not null and
                s.apply_time > s1.survey_fins_time
                and coalesce(s1.online_time, now())  > s.apply_time
                )
                then 1
                else 0
                end) as survey_add_equip
            from csm.proj_equip_record s
            inner join (
            select ppi1.*,tpnvo1.old_project_info_id, mil.update_time as survey_fins_time  from
            csm.proj_project_info ppi1
            left join (
            select project_info_id,max(survey_review_time) update_time from csm.proj_project_review_info
            where is_deleted = 0
            and   entry_review_status = 1
            group by project_info_id
            ) mil on mil.project_info_id = ppi1.project_info_id
            inner join csm.tmp_project_new_vs_old tpnvo1 on tpnvo1.new_project_info_id = ppi1.project_info_id
            ) s1 on s1.project_info_id = s.project_info_id

            where
            s.is_deleted = '0'
            and s.create_time is not null
            group by
            s.yy_product_id,
            s1.project_info_id
        ) ss
        on ss.project_info_id = ppi.project_info_id and ss.product_id = prd.new_product_id

        left join (
            select  s.project_id,
            s.product_id ,
            s1.project_info_id,
            sum(case when s1.survey_fins_time is not null and s.his_create_time > s1.survey_fins_time
                 and coalesce(s1.online_time, now())  > s.his_create_time
                then 1
            else 0
            end) as survey_add_hard_ware
            from platform.survey_hardware_new_resultdata s
            inner join (
            select ppi1.*,tpnvo1.old_project_info_id, mil.update_time as survey_fins_time  from
            csm.proj_project_info ppi1
            left join (
                select project_info_id,max(survey_review_time) update_time from csm.proj_project_review_info
                where is_deleted = 0
                and   entry_review_status = 1
                group by project_info_id
        ) mil on mil.project_info_id = ppi1.project_info_id

        inner join csm.tmp_project_new_vs_old tpnvo1 on tpnvo1.new_project_info_id = ppi1.project_info_id
            ) s1 on s1.old_project_info_id = s.project_id

            where
            s.invalid_flag = '0'
             and s.is_must = '1'
            and s.his_create_time is not null
            group by
            s.project_id,
            s.product_id,
            s1.project_info_id
        ) hardware
        on hardware.project_id = tpnvo.old_project_info_id and hardware.product_id = prd.old_product_id
        -- 准备待办
        left join (
            select  s.project_info_id,
            s.yy_product_id ,
            sum(case when ee.online_time is not null and (s.update_time > ee.online_time or s.task_status !=1) then 1 else 0 end) as online_finsin_task
            from csm.proj_product_task s
            inner join csm.proj_project_info ee on s.project_info_id = ee.project_info_id
            where
            s.is_deleted = '0'
            group by
            s.project_info_id,
            s.yy_product_id
        ) sstask
        on sstask.project_info_id = ppi.project_info_id and sstask.yy_product_id = prd.new_product_id

        left join (
            select
            s.project_info_id,
            999999 product_id ,
            sum(case when s1.survey_fins_time is not null and ((s.online_flag = 1 and s.create_time > s1.survey_fins_time) or s.review_submit_time > s1.survey_fins_time)
            and coalesce(s1.online_time, now()) > s.create_time
            then 1 else 0
            end) as survey_add_third
            from
            csm.proj_third_interface s
            inner join (
            select
            ppi1.*,
            tpnvo1.old_project_info_id,
            mil.update_time as survey_fins_time
            from
            csm.proj_project_info ppi1
            inner join csm.tmp_project_new_vs_old tpnvo1 on
            tpnvo1.new_project_info_id = ppi1.project_info_id
            left join (
            select
            project_info_id,
            max(survey_review_time) update_time
            from
            csm.proj_project_review_info
            where
            is_deleted = 0
            and entry_review_status = 1
            group by
            project_info_id
            ) mil on
            mil.project_info_id = ppi1.project_info_id

            ) s1 on
            s1.project_info_id = s.project_info_id
            where
            s.create_time is not null
            group by
            s.project_info_id,
            s1.project_info_id
        ) sss
        on sss.project_info_id = ppi.project_info_id and sss.product_id = prd.old_product_id

        left join (
            select  s.project_info_id,
            s.yy_product_id ,
            sum(case when ee.online_time is not null and s.commit_finish_time is not null and s.commit_finish_time > ee.online_time then 1 else 0 end) as online_finsin_report
            from csm.proj_survey_report s
            inner join csm.proj_project_info ee on s.project_info_id = ee.project_info_id
            where
            s.is_deleted = '0'
            group by
            s.project_info_id,
            s.yy_product_id
        ) report
        on report.project_info_id = ppi.project_info_id and report.yy_product_id = prd.new_product_id


        left join (
            select  s.project_info_id,
            s.yy_product_id ,
            sum(case when ee.online_time is not null and s.commit_finish_time is not null and s.commit_finish_time > ee.online_time then 1 else 0 end) as online_finsin_form
            from csm.proj_survey_form s
            inner join csm.proj_project_info ee on s.project_info_id = ee.project_info_id
            where
            s.is_deleted = '0'
            group by
            s.project_info_id,
            s.yy_product_id
        ) form
        on form.project_info_id = ppi.project_info_id and form.yy_product_id = prd.new_product_id
        left join
        (
            select
            ppi.project_info_id ,
            ppsrd.yy_product_id,
            sum(case when ppi.settle_in_time is not null and ppsr.create_time > ppi.settle_in_time  then 1 else 0 end) as
            add_product_count
            from
            csm.proj_product_supplementary_record ppsr
            inner join csm.proj_product_supplementary_record_detail ppsrd on ppsrd .product_supplementary_record_id = ppsr.product_supplementary_record_id
            inner join csm.proj_project_info ppi on ppsr.custom_info_id = ppi.custom_info_id
            where ppsr.is_deleted = 0 and ppi.is_deleted =0
            group by ppi.project_info_id ,ppsrd.yy_product_id
        ) addproduct on addproduct.project_info_id = ppi.project_info_id and addproduct.yy_product_id = prd.new_product_id
        left join
        (
            select
            ppi.project_info_id ,
            ppsrd.yy_product_id,
            sum(case when ppi.settle_in_time is not null and ppsr.create_time > ppi.settle_in_time then 1 else 0 end) as
                authorization_count
            from
            csm.proj_product_empower_add_record  ppsr
            inner join csm.proj_product_empower_add_record_detail  ppsrd on ppsrd.product_empower_add_record_id = ppsr.product_empower_add_record_id
            inner join csm.proj_project_info ppi on ppsr.custom_info_id = ppi.custom_info_id
            where ppsr.is_deleted = 0 and ppi.is_deleted =0
            and ppsr.authorization_type = 1
            group by ppi.project_info_id ,ppsrd.yy_product_id
        ) author on author.project_info_id = ppi.project_info_id and author.yy_product_id = prd.new_product_id

        where
        ppi .his_flag = 1
        and ppi.is_deleted = 0
        and pci .is_deleted = 0
        and ppi.settle_in_time > '2024-10-01'
        <if test="customName != null and customName != ''">
            and pci.custom_name like concat('%',#{customName},'%')
        </if>

        <if test="productName != null and productName != ''">
            and prd.name  like concat('%',#{productName},'%')
        </if>

        <if test="worktimeBegin != null ">
            and ppi.work_time >= #{worktimeBegin}
        </if>
        <if test="worktimeEnd != null">
            and ppi.work_time &lt;= #{worktimeEnd}
        </if>
        <if test="settleInTimeBegin != null ">
            and ppi.settle_in_time >= #{settleInTimeBegin}
        </if>
        <if test="settleInTimeEnd != null">
            and ppi.settle_in_time &lt;= #{settleInTimeEnd}
        </if>


        <if test="onlineTimeBegin != null ">
            and ppi.online_time >= #{onlineTimeBegin}
        </if>
        <if test="onlineTimeEnd != null">
            and ppi.online_time &lt;= #{onlineTimeEnd}
        </if>
        <if test="externalAcceptTimeBegin != null ">
            and ppi.external_accept_time >= #{externalAcceptTimeBegin}
        </if>
        <if test="externalAcceptTimeEnd != null">
            and ppi.external_accept_time &lt;= #{externalAcceptTimeEnd}
        </if>
        <if test="applyAcceptTimeBegin != null ">
            and ppi.apply_accept_time >= #{applyAcceptTimeBegin}
        </if>
        <if test="applyAcceptTimeEnd != null">
            and ppi.apply_accept_time &lt;= #{applyAcceptTimeEnd}
        </if>
        <if test="acceptTimeBegin != null ">
            and ppi.accept_time >= #{acceptTimeBegin}
        </if>
        <if test="acceptTimeEnd != null">
            and ppi.accept_time &lt;= #{acceptTimeEnd}
        </if>

        order by ppi.work_time desc, ord.delivery_order_no, prd.order_no


    </select>

    <!-- 调研后新增数量：设备 -->
    <select id="findProjectDataDetail" resultType="com.alibaba.fastjson.JSONObject">
        select

        pci.custom_name,
        s1.project_info_id  as "projectInfoId",
        prod.id as "yyProductId",
        su.user_name as user_name,
        s.create_time ,
        s.equip_type_name equip_name,
        s.equip_model_name equip_type,
        s.equip_factory_name equip_factory,
        case when s.required_flag = 1 then '新增接口' else '提交申请' end as equip_weigui_type,
        prod.product_name,
        s.create_time as finish_time,
        s.apply_time


        from csm.proj_equip_record s
        inner join (
        select ppi1.*,tpnvo1.old_project_info_id, mil.update_time as survey_fin_time  from
        csm.proj_project_info ppi1
        left join (
        select project_info_id,max(survey_review_time) update_time from csm.proj_project_review_info
        where is_deleted = 0
        and   entry_review_status = 1
        group by project_info_id
        ) mil on mil.project_info_id = ppi1.project_info_id
        inner join csm.tmp_project_new_vs_old tpnvo1 on tpnvo1.new_project_info_id = ppi1.project_info_id
        ) s1 on s1.project_info_id = s.project_info_id
        left join csm.proj_custom_info pci on s1.custom_info_id = pci .custom_info_id
        left join csm.sys_user su on s.creater_id  = su.sys_user_id
        inner join (
        select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
        ps.id as old_product_id,
        case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
        else dp.product_name end  as product_name,
        dp.order_no
        from csm.dict_product dp
        left join csm.dict_product_vs_modules dpm
        on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
        left join platform.product ps on dp.yy_product_id = ps.product_yunying_id
        where dp.is_deleted = 0
        ) prod on prod.id = s.yy_product_id
        where
        s.is_deleted = '0'
        and s.create_time is not null
        and s1.survey_fin_time is not null
        <if test="projectInfoId != null">
            AND s1.project_info_id = #{projectInfoId}
        </if>
        <if test="yyProductId != null">
            and prod.id = #{yyProductId}
        </if>
        <if test="type != null and type == 'survey_equipment'">

          and ( (
             s.required_flag = 1
            and s.create_time > s1.survey_fin_time
            and coalesce(s1.online_time, now())  > s.create_time
            )
              or (
                s.apply_time is not null
                and s.apply_time > s1.survey_fin_time
            and coalesce(s1.online_time, now())  > s.apply_time
            )
            )
        </if>
        <if test="type != null and type == 'unfinished_equipment'">
            and s.equip_status != '5'
            and s1.online_time is not null
        </if>
    </select>
    <select id="findProjectProductTaskDetail" resultType="com.alibaba.fastjson.JSONObject">
        select   pci.custom_name,
                s.project_info_id as "projectInfoId",
                s.yy_product_id as "yyProductId",
                 s.task_title,
                 s.task_detail,
                 prod.product_name,
                 phi.hospital_name,
                 su.user_name,
                 s.update_time as finish_time

        from csm.proj_product_task s
                 left join csm.sys_user su on s.user_id  = su.sys_user_id
                 inner join (
            select
                coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
                case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
                     else dp.product_name end  as product_name,
                dp.order_no
            from csm.dict_product dp
                     left join csm.dict_product_vs_modules dpm
                               on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1

        ) prod on prod.id = s.yy_product_id
                 inner join csm.proj_project_info ee on s.project_info_id = ee.project_info_id
                 left join csm.proj_custom_info pci on ee.custom_info_id = pci .custom_info_id
                 inner join csm.proj_hospital_info phi  on s.hospital_info_id = phi.hospital_info_id
        where
            s.is_deleted = '0'
          and ee.his_flag = 1
          and ee.is_deleted = 0
            <if test="projectInfoId != null">
                AND s.project_info_id = #{projectInfoId}
                and ee.online_time is not null
                and (s.update_time > ee.online_time or s.task_status !=1)
            </if>
            <if test="yyProductId != null">
                and s.yy_product_id = #{yyProductId}
            </if>
    </select>
    <select id="findProjectAddProductDetail" resultType="com.alibaba.fastjson.JSONObject">
        select
            ppi.project_info_id as "projectInfoId",
            ppsrd.yy_product_id as "yyProductId",
            prod.product_name,
            pci.custom_name ,
            su.user_name ,
            ppsrd.create_time

        from
            csm.proj_product_supplementary_record ppsr
                inner join csm.proj_product_supplementary_record_detail ppsrd on ppsrd .product_supplementary_record_id = ppsr.product_supplementary_record_id
                inner join csm.proj_project_info ppi on ppsr.custom_info_id = ppi.custom_info_id

                inner join (
                select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
                       ps.id as old_product_id,
                       case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
                            else dp.product_name end  as product_name,
                       dp.order_no
                from csm.dict_product dp
                         left join csm.dict_product_vs_modules dpm
                                   on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
                         left join platform.product ps on dp.yy_product_id = ps.product_yunying_id
                where dp.is_deleted = 0
            ) prod on prod.id = ppsrd.yy_product_id
                left join csm.sys_user su on ppsrd.creater_id  = su.sys_user_id
                left join csm.proj_custom_info pci on ppi.custom_info_id = pci .custom_info_id
        where ppsr.is_deleted = 0
          and ppi.is_deleted =0
        <if test="projectInfoId != null">
            AND ppi.project_info_id = #{projectInfoId}
            and ppi.settle_in_time is not null
            and ppsr.create_time > ppi.settle_in_time
        </if>
        <if test="yyProductId != null">
            and ppsrd.yy_product_id = #{yyProductId}
        </if>

    </select>
    <select id="findProjectFormDataDetail" resultType="com.alibaba.fastjson.JSONObject">

        select
            pci.custom_name,
            su.user_name,
            prod.product_name,
            ee.project_info_id as "projectInfoId",
            prod.id as "yyProductId",
            s.*
        from
            csm.proj_survey_form s
                inner join csm.proj_project_info ee on s.project_info_id = ee.project_info_id
                inner join (
                select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
                       ps.id as old_product_id,
                       case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
                            else dp.product_name end  as product_name,
                       dp.order_no
                from csm.dict_product dp
                         left join csm.dict_product_vs_modules dpm
                                   on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
                         left join platform.product ps on dp.yy_product_id = ps.product_yunying_id
                where dp.is_deleted = 0
            ) prod on prod.id = s.yy_product_id
                left join csm.proj_custom_info pci on ee.custom_info_id = pci .custom_info_id
                left join csm.sys_user su on s.make_user_id  = su.sys_user_id
        where
            s.is_deleted = '0'

        <if test="projectInfoId != null">
            AND s.project_info_id = #{projectInfoId}
            and ee.online_time is not null
            and s.commit_finish_time is not null
            and s.commit_finish_time > ee.online_time
        </if>
        <if test="yyProductId != null">
            and s.yy_product_id = #{yyProductId}
        </if>


    </select>
    <select id="findProjectReportDataDetail" resultType="com.alibaba.fastjson.JSONObject">
        select
            ee.project_info_id as "projectInfoId",
            prod.id as "yyProductId",
            pci.custom_name,
            su.user_name,
            prod.product_name,
            s.*
        from
        csm.proj_survey_report s
        inner join csm.proj_project_info ee on s.project_info_id = ee.project_info_id
        inner join (
        select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
        ps.id as old_product_id,
        case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
        else dp.product_name end  as product_name,
        dp.order_no
        from csm.dict_product dp
        left join csm.dict_product_vs_modules dpm
        on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
        left join platform.product ps on dp.yy_product_id = ps.product_yunying_id
        where dp.is_deleted = 0
        ) prod on prod.id = s.yy_product_id
        left join csm.proj_custom_info pci on ee.custom_info_id = pci .custom_info_id
        left join csm.sys_user su on s.make_user_id  = su.sys_user_id
        where
        s.is_deleted = '0'

        <if test="projectInfoId != null">
            AND s.project_info_id = #{projectInfoId}
            and ee.online_time is not null
            and s.commit_finish_time is not null
            and s.commit_finish_time > ee.online_time
        </if>
        <if test="yyProductId != null">
            and s.yy_product_id = #{yyProductId}
        </if>

    </select>
    <select id="findProjectInterfaceDataDetail" resultType="com.alibaba.fastjson.JSONObject">
        select
        s1.project_info_id as "projectInfoId",
        pci.custom_name,
        s.dict_interface_name third_name,
        s.dict_interface_firm_name firm_dict_name,
        case when s.implements_type = 0 then '三方接口对接'
        when s.implements_type = 1 then '产品对接'
        when s.implements_type = 2 then '项目组对接'
        end
        implements_type,
        case when s.status = '0' then '未申请'
        when s.status = '1' then '提交裁定'
        when  s.status = '11' then '裁定驳回'
        when  s.status = '12' then '待评审'
        when  s.status = '13' then '裁定通过'
        when  s.status = '21' then '测试环境申请授权'
        when  s.status = '22' then '测试环境授权通过'
        when  s.status = '23' then '研发中'
        when  s.status = '24' then '测试环境测试完成'
        when  s.status = '31' then '申请正式环境授权'
        when  s.status = '32' then '正式环境授权通过'
        when  s.status = '33' then '研发完成'
        when  s.status = '50' then '接口完成'
        else '未申请'
        end
        status,
        su.user_name as user_name,
        s.create_time,
        s.review_submit_time  interface_finish_date


        from csm.proj_third_interface s
        inner join (
        select ppi1.*,tpnvo1.old_project_info_id,mil.update_time as survey_fin_time  from
        csm.proj_project_info ppi1
        inner join csm.tmp_project_new_vs_old tpnvo1 on tpnvo1.new_project_info_id = ppi1.project_info_id
        left join (
        select project_info_id,max(survey_review_time) update_time from csm.proj_project_review_info
        where is_deleted = 0
        and   entry_review_status = 1
        group by project_info_id
        ) mil on
        mil.project_info_id = ppi1.project_info_id
        ) s1 on s1.project_info_id = s.project_info_id
        left join csm.proj_custom_info pci on s1.custom_info_id = pci .custom_info_id
        left join csm.sys_user su on su.sys_user_id = s.dir_person_id

        where
        s.create_time is not null
        <if test="projectInfoId != null">
            and s1.survey_fin_time is not null
            and
            ((s.online_flag = 1 and s.create_time > s1.survey_fin_time) or s.review_submit_time > s1.survey_fin_time)
            and coalesce(s1.online_time, now()) > s.create_time
            AND s1.project_info_id = #{projectInfoId}
        </if>


    </select>
    <select id="findProjectHardWareDetail" resultType="com.alibaba.fastjson.JSONObject">

        select
            s1.project_info_id as "projectInfoId",
            pci.custom_name,
            s.*
        from platform.survey_hardware_new_resultdata s
                 inner join (
            select ppi1.*,tpnvo1.old_project_info_id ,mil.update_time as survey_fin_time from
                csm.proj_project_info ppi1
                    inner join csm.tmp_project_new_vs_old tpnvo1 on tpnvo1.new_project_info_id = ppi1.project_info_id
        left join (
            select project_info_id,max(survey_review_time) update_time from csm.proj_project_review_info
            where is_deleted = 0
        and  entry_review_status = 1
            group by project_info_id
        ) mil on
        mil.project_info_id = ppi1.project_info_id
        ) s1 on s1.old_project_info_id = s.project_id
                 left join csm.proj_custom_info pci on s1.custom_info_id = pci .custom_info_id
        inner join (
            select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
            ps.id as old_product_id,
            case when dpm.yy_module_name is not null then  concat(dp.product_name, '-', dpm.yy_module_name)
            else dp.product_name end  as product_name,
            dp.order_no
            from csm.dict_product dp
            left join csm.dict_product_vs_modules dpm
            on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
            left join platform.product ps on dp.yy_product_id = ps.product_yunying_id
            where dp.is_deleted = 0
        ) prod on prod.old_product_id = s.product_id
        where
            s.invalid_flag = '0'
          and s.is_must = '1'
          and s.his_create_time is not null
        <if test="projectInfoId != null">
            AND s1.project_info_id = #{projectInfoId}
            and s1.survey_fin_time is not null
            and s.his_create_time > s1.survey_fin_time
            and coalesce(s1.online_time, now())  > s.his_create_time
        </if>
        <if test="yyProductId != null">
            and prod.id = #{yyProductId}
        </if>
    </select>

    <select id="findByDept" resultType="com.msun.csm.dao.entity.report.ReportCustomInfo">
        select *
        from report_custom_info
        where dept_id in (select dept_yunying_id
                          from sys_dept
                          where pid = 1030
                            and dept_name like '%客服部%')
    </select>

    <select id="findProjectDurationStatistics" resultType="com.msun.csm.model.jimu.resp.ProjectDurationStatisticsResp">
        SELECT ppi.project_name         as "projectName",
        pci.custom_name          AS "customname",
        pci.custom_dept_id              as "deptYYId",
        sd.dept_name             AS "deptName",
        sd1.dept_name       AS "serviceOrgName",
        CASE
        WHEN pci.telesales_flag = 1 THEN
        '电销客户'
        WHEN pci.telesales_flag != 1
        AND ppi.project_type = 1 THEN
        '单体客户'
        WHEN pci.telesales_flag != 1
        AND ppi.project_type = 2 THEN
        '区域客户'
        END                  AS "customTypeName",
        pci.custom_type,
        CASE
        WHEN ppi.upgradation_type = 1 THEN
        '老换新'
        WHEN ppi.upgradation_type = 2 THEN
        '新客户'
        WHEN ppi.upgradation_type = 3 THEN
        '老体系'
        ELSE ''
        END                  "upgradationTypeName",
        CASE
        WHEN ppi.project_deliver_status = 1 THEN
        '已派工'
        WHEN ppi.project_deliver_status = 2 THEN
        '已调研'
        WHEN ppi.project_deliver_status = 3 THEN
        '已入驻'
        WHEN ppi.project_deliver_status = 4 THEN
        '准备完成'
        WHEN ppi.project_deliver_status = 5 THEN
        '已上线'
        WHEN ppi.project_deliver_status = 6 THEN
        '已验收'
        WHEN ppi.project_deliver_status = 7 THEN
        '已启动'
        WHEN ppi.project_deliver_status = 7 THEN
        '一次验收通过'
        END                       "projectStatusName",
        ppi.upgradation_type,
        ppi.work_time         AS "workTime",
        ppi.settle_in_time    AS "settleInTime",
        ppi.online_time       AS "onlineTime",
        ppi.apply_accept_time AS "applyAcceptTime",
        ppi.external_accept_time AS "externalAcceptTime",
        ppi.accept_time          AS "acceptTime"
        FROM     csm.proj_project_info ppi
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        LEFT JOIN csm.sys_dept sd ON pci.custom_dept_id = sd.dept_yunying_id
        left join CSM.SYS_DEPT SD1 on PPI.project_team_id = sd1.dept_yunying_id
        WHERE ppi.is_deleted = 0
        and ppi.his_flag = 0
        and pci.is_deleted = 0
        <if test="customName != null and customName != ''">
            and pci.custom_name like concat('%', #{customName}, '%')
        </if>
        <if test="deptName != null and deptName != ''">
            and sd.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="onlinetimeBegin != null">
            and ppi.online_time >= #{onlinetimeBegin}
        </if>
        <if test="onlinetimeEnd != null">
            and ppi.online_time &lt;= #{onlinetimeEnd}
        </if>
        <if test="worktimeBegin != null">
            and ppi.work_time >= #{worktimeBegin}
        </if>
        <if test="worktimeEnd != null">
            and ppi.work_time &lt;= #{worktimeEnd}
        </if>
        <if test="settleInTimeBegin != null">
            and ppi.settle_in_time >= #{settleInTimeBegin}
        </if>
        <if test="settleInTimeEnd != null">
            and ppi.settle_in_time &lt;= #{settleInTimeEnd}
        </if>
        <if test="applyAcceptTimeBegin != null">
            and ppi.apply_accept_time >= #{applyAcceptTimeBegin}
        </if>
        <if test="applyAcceptTimeEnd != null">
            and ppi.apply_accept_time &lt;= #{applyAcceptTimeEnd}
        </if>
        <if test="acceptTimeBegin != null">
            and ppi.accept_time >= #{acceptTimeBegin}
        </if>
        <if test="acceptTimeEnd != null">
            and ppi.accept_time &lt;= #{acceptTimeEnd}
        </if>
        <if test="upgradationType != null and upgradationType !=''">
            and ppi.upgradation_type::varchar = #{upgradationType}
        </if>
        <if test="customType != null and customType != 3">
            and ppi.project_type = #{customType}
            and pci.telesales_flag != 1
        </if>
        <if test="projectDeliverStatus != null ">
            and ppi.project_deliver_status =#{projectDeliverStatus}
        </if>
        <if test="customType != null and customType == 3">
            and pci.telesales_flag = 1
        </if>
        ORDER BY "customTypeName",ppi.upgradation_type, ppi.project_type,sd.dept_name
    </select>

    <select id="queryProductAuthData" resultType="com.msun.csm.dao.entity.report.ReportAuthorizationStatistics">
        select *
        from report_authorization_statistics
        where is_deleted = 0
        <if test="customInfoName != null and customInfoName != ''">
            and custom_info_name like concat('%', #{customInfoName}, '%')
        </if>
        <if test="hospitalInfoName != null and hospitalInfoName != ''">
            and hospital_info_name like concat('%', #{hospitalInfoName}, '%')
        </if>
        order by custom_info_id, hospital_info_id, published_system_standard_name
    </select>
</mapper>
