<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.knowledge.YjkMaintenanceTeamMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.knowledge.YjkMaintenanceTeam">
        <!--@mbg.generated-->
        <!--@Table csm.dict_city-->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="user_id_leader" property="userIdLeader" jdbcType="BIGINT"/>
        <result column="invalid_flag" property="invalidFlag" jdbcType="INTEGER"/>
        <result column="his_creater_id" property="hisCreaterId" jdbcType="BIGINT"/>
        <result column="his_creater_name" property="hisCreaterName" jdbcType="VARCHAR"/>
        <result column="his_create_time" property="hisCreateTime" jdbcType="TIMESTAMP"/>
        <result column="his_updater_id" property="hisUpdaterId" jdbcType="BIGINT"/>
        <result column="his_update_time" property="hisUpdateTime" jdbcType="TIMESTAMP"/>
        <result column="user_name_leader" property="userNameLeader" jdbcType="VARCHAR"/>
        <result column="his_updater_name" property="hisUpdaterName" jdbcType="VARCHAR"/>
        <result column="team_type_code" property="teamTypeCode" jdbcType="VARCHAR"/>
        <result column="team_type_name" property="teamTypeName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        "name",
        user_id_leader,
        invalid_flag,
        his_creater_id,
        his_creater_name,
        his_create_time,
        his_updater_id,
        his_update_time,
        user_name_leader,
        his_updater_name,
        team_type_code,
        team_type_name
    </sql>

    <select id="getTeamByTypeCode" resultType="com.msun.csm.dao.entity.knowledge.BackendTeamInfo">
        select ymt.id,
               ymt."name",
               ymt.user_id_leader as "userIdLeader",
               ymt.user_name_leader as "userNameLeader",
               ymt.invalid_flag as "invalidFlag",
               ymt.his_creater_id as "hisCreaterId",
               ymt.his_creater_name as "hisCreaterName",
               ymt.his_create_time as "hisCreateTime",
               ymt.his_updater_id as "hisUpdaterId",
               ymt.his_updater_name as "hisUpdaterName",
               ymt.his_update_time as "hisUpdateTime",
               ymt.team_type_code as "teamTypeCode",
               ymt.team_type_name as "teamTypeName",
               su.user_yunying_id as "userYunyingId",
               su.account,
               sd.yunying_id as "yyDeptId",
               sd.simple_name as "yyDeptName"
        from knowledge.yjk_maintenance_team as ymt
                 left join platform.sys_user su on ymt.user_id_leader = su.user_id and su.status = 'ENABLE'
                 left join platform.sys_dept sd on su.dept_id = sd.dept_id
        where ymt.invalid_flag = 0
          and su.user_yunying_id is not null
          and ymt.team_type_code = #{teamTypeCode}
    </select>

</mapper>
