<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectOrderRelationMapper">
    <select id="getCloudResourceRelation" resultType="com.msun.csm.dao.entity.proj.ProjProjectOrderRelation">
        select t.*
        from csm.proj_project_order_relation t
                 inner join csm.proj_custom_cloud_service t1 on
            t.bussiness_info_id = t1.custom_cloud_service_id
                and t.delivery_order_id in (-1, 2, 9)
                and t.project_info_id = #{projectInfoId,jdbcType=BIGINT} and t.is_deleted = 0 and t1.is_deleted = 0
    </select>

    <update id="deleteByYYOrderId">
        update csm.proj_project_order_relation
        set is_deleted = 1
        where yy_order_id = #{yyOrderId,jdbcType=BIGINT}
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_project_order_relation
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
