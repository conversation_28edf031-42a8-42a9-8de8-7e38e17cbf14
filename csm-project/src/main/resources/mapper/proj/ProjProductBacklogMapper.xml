<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper">
    <select id="selectProductBacklog" resultType="com.msun.csm.model.vo.ProjProductBacklogVO"
            parameterType="com.msun.csm.model.dto.ProjProductBacklogDTO">
        select ss.*, COALESCE(dp.order_no, 999999999) order_no from (
        select distinct ppb.*,
        su.sys_user_id as "userId",
        su.user_name,
        backend.sys_user_id as "backendSysUserId",
        backend.user_name as "backendUserName",
        pmtd.complete_status,
        pmtd.milestone_task_detail_id,
        pmtd.actual_comp_time,
        pmtd.start_time as "startTime",
        pmtd.expect_start_time,
        pmtd.expect_comp_time,
        phi.hospital_info_id,
        phi.hospital_name
        from csm.proj_milestone_task pmt
        left join csm.proj_milestone_task_detail pmtd on pmt.milestone_task_id = pmtd.milestone_task_id and
        pmtd.is_deleted = 0
        left join csm.proj_product_backlog ppb on pmt.project_info_id = ppb.project_info_id and
        (ppb.yy_product_id = pmtd.product_deliver_id or
        pmtd.product_deliver_id = ppb.yy_product_module_id)
        and pmt.hospital_info_id = ppb.hospital_info_id
        left join csm.sys_user su on pmtd.leader_id = su.sys_user_id and su.is_deleted = 0
        left join csm.sys_user backend on pmtd.backend_sys_user_id = backend.sys_user_id and backend.is_deleted = 0
        left join csm.proj_hospital_info phi
        on phi.hospital_info_id = ppb.hospital_info_id and phi.is_deleted = 0
        where pmt.project_info_id = #{projectInfoId}
        and pmt.milestone_node_code = #{milestoneNodeCode}
        and ppb.project_info_id = #{projectInfoId}
        and ppb.is_deleted = 0
        <if test="yyProductId != null">
            and ppb.yy_product_id = #{yyProductId}
        </if>
        <if test="baseDataStatus != null">
            and ppb.base_data_status = #{baseDataStatus}
        </if>
        <if test="configDataStatus != null">
            and ppb.config_data_status = #{configDataStatus}
        </if>
        <if test="todoTaskStatus != null">
            and ppb.todo_task_status = #{todoTaskStatus}::varchar
        </if>
        <if test="reportDataStatus != null">
            and ppb.report_data_status = #{reportDataStatus}
        </if>
        <if test="formDataStatus != null">
            and ppb.form_data_status = #{formDataStatus}
        </if>
        <if test="yyProductModuleId != null">
            and ppb.yy_product_module_id = #{yyProductModuleId}
        </if>
        <if test="hospitalInfoId != null">
            and ppb.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="userIdList != null and userIdList.size() > 0 ">
            and su.sys_user_id in
            <foreach collection="userIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="backUserIdList != null and backUserIdList.size() > 0 ">
            and backend.sys_user_id in
            <foreach collection="backUserIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="completeStatus != null">
            and pmtd.complete_status = #{completeStatus}
        </if>
        ) ss
        left join csm.dict_product dp on dp.yy_product_id = ss.yy_product_id
        order by dp.order_no,
        yy_product_module_id,
        hospital_name
    </select>

    <insert id="insertBatch">
        insert into csm.proj_product_backlog(product_backlog_id, project_info_id, hospital_info_id, yy_product_id,
        yy_product_module_id, product_name, base_data_status, config_data_status,
        todo_task_status, is_deleted, creater_id, create_time, updater_id,
        update_time, report_data_status, form_data_status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productBacklogId}, #{item.projectInfoId}, #{item.hospitalInfoId}, #{item.yyProductId},
            #{item.yyProductModuleId},
            #{item.productName}, #{item.baseDataStatus}, #{item.configDataStatus}, #{item.todoTaskStatus},
            #{item.isDeleted}, #{item.createrId}, #{item.createTime}, #{item.updaterId}, #{item.updateTime},
            #{item.reportDataStatus}, #{item.formDataStatus})
        </foreach>
    </insert>

    <update id="deleteByParam">
        update
        csm.proj_product_backlog
        set is_deleted = 1
        where project_info_id = #{projectInfoId}
        and hospital_info_id = #{hospitalInfoId}
        and (yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item.id}
        </foreach>
        or yy_product_module_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")"
                 separator=",">
            #{item.id}
        </foreach>
        )
    </update>

    <select id="selectByParam" resultType="com.msun.csm.dao.entity.proj.ProjProductBacklog">
        select *
        from csm.proj_product_backlog
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and hospital_info_id = #{hospitalInfoId}
          and (yy_product_id = #{yyProductId} or yy_product_module_id = #{yyProductId})
    </select>
    <select id="selectByDataParamer" resultType="com.msun.csm.dao.entity.proj.ProjProductBacklog">
        select *
        from csm.proj_product_backlog
        where project_info_id = #{projectInfoId}
          and (
            yy_product_id = #{productId} or yy_product_module_id = #{productId}
            )
          and  is_deleted = 0
    </select>

    <update id="updateByProjectId">
        update
        csm.proj_product_backlog
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() > 0">
            and (yy_product_id in
            <foreach collection="changeProductList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            or yy_product_module_id in
            <foreach collection="changeProductList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>)
        </if>
    </update>
    <update id="updateMilstoneData">
        update csm.proj_milestone_task_detail
        set complete_status  = #{updateType}
          , actual_comp_time = now()
        where product_deliver_id = #{productId}
          and milestone_task_id in (
            select milestone_task_id
            from csm.proj_milestone_task
            where milestone_node_code = 'preparat_product'
              and project_info_id = #{projectInfoId}
        )
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_product_backlog
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and (yy_product_id in
        <foreach collection="oldProductIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        or yy_product_module_id in
        <foreach collection="oldProductIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
    </update>
</mapper>
