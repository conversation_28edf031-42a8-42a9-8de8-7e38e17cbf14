<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectPlanMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectPlan">
        <!--@mbg.generated-->
        <!--@Table csm.proj_project_plan-->
        <id column="project_plan_id" jdbcType="BIGINT" property="projectPlanId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="project_plan_stage_id" jdbcType="BIGINT" property="projectPlanStageId"/>
        <result column="project_plan_item_id" jdbcType="BIGINT" property="projectPlanItemId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="plan_time" jdbcType="TIMESTAMP" property="planTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="complete_count" jdbcType="SMALLINT" property="completeCount"/>
        <result column="total_count" jdbcType="SMALLINT" property="totalCount"/>
        <result column="jump_type" jdbcType="SMALLINT" property="jumpType"/>
        <result column="jump_path" jdbcType="VARCHAR" property="jumpPath"/>
        <result column="submitter_id" jdbcType="BIGINT" property="submitterId"/>
        <result column="generate_todo_flag" jdbcType="SMALLINT" property="generateTodoFlag"/>
        <result column="plan_type" jdbcType="SMALLINT" property="planType"/>
        <result column="attention_flag" jdbcType="SMALLINT" property="attentionFlag"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="prior_project_plan_item_flag" jdbcType="SMALLINT" property="priorProjectPlanItemFlag"/>
        <result column="prior_project_plan_item_id" jdbcType="VARCHAR" property="priorProjectPlanItemId"/>
        <result column="implementation_engineer_id" jdbcType="BIGINT" property="implementationEngineerId"/>
        <result column="backend_engineer_id" jdbcType="BIGINT" property="backendEngineerId"/>
        <result column="front_flag" jdbcType="SMALLINT" property="frontFlag"/>
        <result column="backend_flag" jdbcType="SMALLINT" property="backendFlag"/>
        <result column="project_plan_item_code" jdbcType="VARCHAR" property="planItemCode"/>
        <result column="milestone_flag" jdbcType="VARCHAR" property="milestoneFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_plan_id,
        project_info_id,
        project_plan_stage_id,
        project_plan_item_id,
        title,
        description,
        sort,
        plan_time,
        "status",
        complete_count,
        total_count,
        jump_type,
        jump_path,
        submitter_id,
        generate_todo_flag,
        plan_type,
        attention_flag,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted,
        prior_project_plan_item_flag,
        prior_project_plan_item_id,
        implementation_engineer_id,
        backend_engineer_id,
        front_flag,
        backend_flag,
        completion_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_plan
        where project_plan_id = #{projectPlanId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_project_plan
        where project_plan_id = #{projectPlanId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectPlan">
        <!--@mbg.generated-->
        insert into csm.proj_project_plan (project_plan_id, project_info_id, project_plan_stage_id,
        project_plan_item_id, title, description,
        sort, plan_time, "status",
        complete_count, total_count, jump_type,
        jump_path, submitter_id, generate_todo_flag,
        plan_type, attention_flag, creater_id,
        create_time, updater_id, update_time,
        is_deleted, prior_project_plan_item_flag,
        prior_project_plan_item_id, implementation_engineer_id,
        backend_engineer_id, front_flag, backend_flag
        )
        values (#{projectPlanId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{projectPlanStageId,jdbcType=BIGINT},
        #{projectPlanItemId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
        #{sort,jdbcType=SMALLINT}, #{planTime,jdbcType=TIMESTAMP}, #{status,jdbcType=SMALLINT},
        #{completeCount,jdbcType=SMALLINT}, #{totalCount,jdbcType=SMALLINT}, #{jumpType,jdbcType=SMALLINT},
        #{jumpPath,jdbcType=VARCHAR}, #{submitterId,jdbcType=BIGINT}, #{generateTodoFlag,jdbcType=SMALLINT},
        #{planType,jdbcType=SMALLINT}, #{attentionFlag,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
        #{priorProjectPlanItemId,jdbcType=VARCHAR}, #{implementationEngineerId,jdbcType=BIGINT},
        #{backendEngineerId,jdbcType=BIGINT}, #{frontFlag,jdbcType=SMALLINT}, #{backendFlag,jdbcType=SMALLINT}
        )
    </insert>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_plan_stage_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanStageId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.projectPlanStageId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_plan_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanItemId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.projectPlanItemId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.title != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.description != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sort != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then #{item.sort,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="plan_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.planTime != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT}
                        then TO_DATE(#{item.planTime,jdbcType=TIMESTAMP}, 'YYYY-MM-DD')
                    </if>
                </foreach>
            </trim>
            <trim prefix="&quot;status&quot; = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.status,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="complete_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.completeCount != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.completeCount,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalCount != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.totalCount,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="jump_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.jumpType != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.jumpType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="jump_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.jumpPath != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.jumpPath,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="submitter_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.submitterId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.submitterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="generate_todo_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.generateTodoFlag != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.generateTodoFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="plan_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.planType != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.planType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attention_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.attentionFlag != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.attentionFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT}
                        then TO_TIMESTAMP(#{item.createTime,jdbcType=TIMESTAMP}, 'YYYY-MM-DD HH24:MI:SS')
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT}
                        then TO_TIMESTAMP(#{item.updateTime,jdbcType=TIMESTAMP}, 'YYYY-MM-DD HH24:MI:SS')
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="prior_project_plan_item_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.priorProjectPlanItemFlag != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.priorProjectPlanItemFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="prior_project_plan_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.priorProjectPlanItemId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.priorProjectPlanItemId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="implementation_engineer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.implementationEngineerId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.implementationEngineerId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="backend_engineer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.backendEngineerId != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.backendEngineerId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="front_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.frontFlag != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.frontFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="backend_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.backendFlag != null">
                        when project_plan_id = #{item.projectPlanId,jdbcType=BIGINT} then
                        #{item.backendFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_plan_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectPlanId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_project_plan
        (project_plan_id, project_info_id, project_plan_stage_id, project_plan_item_id, title,
        description, sort, plan_time, "status", complete_count, total_count, jump_type, jump_path,
        submitter_id, generate_todo_flag, plan_type, attention_flag, creater_id, create_time,
        updater_id, update_time, is_deleted, prior_project_plan_item_flag, prior_project_plan_item_id,
        implementation_engineer_id, backend_engineer_id, front_flag, backend_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectPlanId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.projectPlanStageId,jdbcType=BIGINT},
            #{item.projectPlanItemId,jdbcType=BIGINT}, #{item.title,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
            #{item.sort,jdbcType=SMALLINT}, #{item.planTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=SMALLINT},
            #{item.completeCount,jdbcType=SMALLINT}, #{item.totalCount,jdbcType=SMALLINT},
            #{item.jumpType,jdbcType=SMALLINT}, #{item.jumpPath,jdbcType=VARCHAR}, #{item.submitterId,jdbcType=BIGINT},
            #{item.generateTodoFlag,jdbcType=SMALLINT}, #{item.planType,jdbcType=SMALLINT},
            #{item.attentionFlag,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.priorProjectPlanItemFlag,jdbcType=SMALLINT}, #{item.priorProjectPlanItemId,jdbcType=VARCHAR},
            #{item.implementationEngineerId,jdbcType=BIGINT}, #{item.backendEngineerId,jdbcType=BIGINT},
            #{item.frontFlag,jdbcType=SMALLINT}, #{item.backendFlag,jdbcType=SMALLINT})
        </foreach>
    </insert>

    <select id="getConfigProjectPlanItemByParams" resultMap="BaseResultMap">
        SELECT cppi.project_plan_stage_id,
        cppi.project_plan_item_id,
        dppi.project_plan_item_name as title,
        dppi.project_plan_item_code as "planItemCode",
        cppi.sort as sort,
        case
        when dppi.is_component != null then dppi.is_component
        else dppi.url end as "jump_path",
        cppi.prior_project_plan_item_flag,
        cppi.prior_project_plan_item_id,
        cppi.front_flag,
        cppi.backend_flag,
        cppi.milestone_flag
        FROM config_project_plan_item cppi
        LEFT JOIN dict_project_plan_stage dpps on cppi.project_plan_stage_id = dpps.project_plan_stage_id
        LEFT JOIN dict_project_plan_item dppi on cppi.project_plan_item_id = dppi.project_plan_item_id
        where dpps.is_deleted = 0
        and dppi.is_deleted = 0
        <if test="monomerFlag != null">
            and cppi.monomer_flag = #{monomerFlag,jdbcType=BIGINT}
        </if>
        <if test="regionFlag != null">
            and cppi.region_flag = #{regionFlag,jdbcType=BIGINT}
        </if>
        <if test="upgradationType != null">
            and (cppi.upgradation_type = #{upgradationType,jdbcType=BIGINT} or cppi.upgradation_type = -1)
        </if>
        <if test="hisFlag != null">
            and (cppi.his_flag = #{hisFlag,jdbcType=BIGINT} or cppi.his_flag = -1)
        </if>
        <if test="externalSoftwareFlag != null">
            and cppi.external_software_flag = #{externalSoftwareFlag}
        </if>
        <if test="selfSoftwareFlag != null">
            and cppi.self_software_flag = #{selfSoftwareFlag}
        </if>
        order by cppi.sort
    </select>


    <update id="deleteBatchIds">
        update proj_project_plan
        set is_deleted = 1 where project_plan_id in
        <foreach collection="projectPlanIdList" item="projectPlanId" open="(" separator="," close=")">
            #{projectPlanId}
        </foreach>
    </update>

    <select id="queryDataByProjectInfoId" resultType="com.msun.csm.model.resp.projectplan.ProjectPlanResp">
        select ppp.*,
               front.user_name              as front_executor_name,
               completion.user_name         as completion_user_name,
               backend.user_name            as backend_executor_name,
               cre.user_name                as creater_name,
               upd.user_name                as updater_name,
               sub.user_name                as submitter_name,
               dpps.project_plan_stage_name as project_plan_stage_name,
               dpps.project_plan_stage_code as project_plan_stage_code,
               dppi.project_plan_item_code  as project_plan_item_code,
               dppi.progress_display_mode   as "progressDisplayMode",
               ppp.completion_time          as "completionTime",
               case
                   when ppp.status = 0 then '未开始'
                   when ppp.status = 2 then '进行中'
                   else '已完成' end        as status_name
        from csm.proj_project_plan ppp
                 left join csm.sys_user front on ppp.implementation_engineer_id = front.sys_user_id
                 left join csm.sys_user backend on ppp.backend_engineer_id = backend.sys_user_id
                 left join csm.sys_user cre on ppp.creater_id = cre.sys_user_id
                 left join csm.sys_user completion on ppp.completion_user_id = completion.sys_user_id
                 left join csm.sys_user upd on ppp.updater_id = upd.sys_user_id
                 left join csm.sys_user sub on ppp.submitter_id = sub.sys_user_id
                 left join csm.dict_project_plan_stage dpps on ppp.project_plan_stage_id = dpps.project_plan_stage_id
                 left join csm.dict_project_plan_item dppi on ppp.project_plan_item_id = dppi.project_plan_item_id
        where ppp.is_deleted = 0
          <if test="projectInfoId != null and projectInfoId != 0">
              and ppp.project_info_id = #{projectInfoId}
          </if>
        order by ppp.project_plan_stage_id, ppp.sort
    </select>

    <select id="queryData" resultType="com.msun.csm.model.resp.projectplan.ProjectPlanResp">
        select ppp.*,
        front.user_name as front_executor_name,
        completion.user_name as completion_user_name,
        backend.user_name as backend_executor_name,
        cre.user_name as creater_name,
        upd.user_name as updater_name,
        sub.user_name as submitter_name,
        dpps.project_plan_stage_name as project_plan_stage_name,
        dpps.project_plan_stage_code as project_plan_stage_code,
        dppi.project_plan_item_code as project_plan_item_code,
        dppi.progress_display_mode as "progressDisplayMode",
        ppp.completion_time as "completionTime",
        case
        when ppp.status = 0 then '未开始'
        when ppp.status = 2 then '进行中'
        else '已完成' end as status_name
        from csm.proj_project_plan ppp
        left join csm.sys_user front on ppp.implementation_engineer_id = front.sys_user_id
        left join csm.sys_user backend on ppp.backend_engineer_id = backend.sys_user_id
        left join csm.sys_user cre on ppp.creater_id = cre.sys_user_id
        left join csm.sys_user completion on ppp.completion_user_id = completion.sys_user_id
        left join csm.sys_user upd on ppp.updater_id = upd.sys_user_id
        left join csm.sys_user sub on ppp.submitter_id = sub.sys_user_id
        left join csm.dict_project_plan_stage dpps on ppp.project_plan_stage_id = dpps.project_plan_stage_id
        left join csm.dict_project_plan_item dppi on ppp.project_plan_item_id = dppi.project_plan_item_id
        where ppp.is_deleted = 0
        <if test="req.projectInfoId != null and req.projectInfoId != 0">
            and ppp.project_info_id = #{req.projectInfoId}
        </if>
<!--        <if test="executorId != null">-->
<!--            and (ppp.implementation_engineer_id = #{executorId} or ppp.backend_engineer_id = #{executorId})-->
<!--        </if>-->
        <if test="req.status != null and req.status == 0">
            and ppp.status != 1
        </if>
        <if test="req.status != null and req.status == 1">
            and ppp.status = 1
        </if>
        <if test="req.status != null and req.status == 2">
            and ppp.status = 2
        </if>
        <if test="req.frontOrBackendFlag != null and req.frontOrBackendFlag == 1">
            and ppp.front_flag = 1
        </if>
        <if test="req.frontOrBackendFlag != null and req.frontOrBackendFlag == 2">
            and ppp.backend_flag = 1
        </if>
        <if test="req.overTimeFlag != null and req.overTimeFlag == 1">
            and ppp.plan_time &lt; CURRENT_DATE
        </if>
        <if test="req.attentionFlag != null">
            and ppp.attention_flag = #{req.attentionFlag}
        </if>
        <if test="req.keyWord != null and req.keyWord != ''">
            and (ppp.title like CONCAT('%', #{req.keyWord}, '%')
            or ppp.description like CONCAT('%', #{req.keyWord}, '%'))
        </if>
        <if test="req.projectPlanStageIdList != null and req.projectPlanStageIdList.size > 0">
            and ppp.project_plan_stage_id in
            <foreach collection="req.projectPlanStageIdList" item="stageId" separator="," open="(" close=")">
                #{stageId}
            </foreach>
        </if>
        order by ppp.project_plan_stage_id, ppp.sort
    </select>

    <select id="queryMaxSort" resultType="java.lang.Integer">
        select max(sort)
        from proj_project_plan
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
    </select>

    <select id="selectBatchIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from proj_project_plan
        where project_plan_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
    </select>


    <update id="updateCompleteStatus">
        update csm.proj_project_plan
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="completeCount != null and completeCount != -1">
                complete_count = #{completeCount},
            </if>
            <if test="totalCount != null and totalCount != -1 ">
                total_count = #{totalCount},
            </if>
        </set>
        where project_info_id = #{projectInfoId}
        and is_deleted =0
        and project_plan_item_id in (select project_plan_item_id from dict_project_plan_item where project_plan_item_code = #{itemCode} and is_deleted = 0)
    </update>

    <!--整理 AAAAAAAAAAAAAAAAAAAAAAAAAAA  -->

    <select id="getProjectPlanByPlanId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from csm.proj_project_plan
        where is_deleted = 0
          and project_plan_id = #{planId}
    </select>

    <select id="getProjectPlanByProjectAndItemIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from csm.proj_project_plan
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        and project_plan_item_id in
        <foreach collection="planItemIds" item="planItemId" open="(" separator=", " close=")">
            #{planItemId}
        </foreach>
    </select>

    <select id="getProjectPlanByProject" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from csm.proj_project_plan
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and prior_project_plan_item_flag = 1
          and prior_project_plan_item_id != ''
          and  prior_project_plan_item_id  is not null
    </select>

    <select id="getProjectPlanByProjectInfoIdAndItemCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from csm.proj_project_plan
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and project_plan_item_id = (select project_plan_item_id from dict_project_plan_item where project_plan_item_code = #{itemCode} and is_deleted = 0)
    </select>


    <update id="followOrUnfollow">
        update csm.proj_project_plan
        set attention_flag = #{attentionFlag,jdbcType=SMALLINT},
            updater_id     = #{updaterId,jdbcType=BIGINT},
            update_time    = now()
        where project_plan_id = #{projectPlanId,jdbcType=BIGINT}
          and is_deleted = 0
    </update>

    <update id="updateStatusByProjectPlanId">
        update csm.proj_project_plan
        set "status" = #{status,jdbcType=SMALLINT},
        <if test="status == 1">
            completion_time = now(),
        </if>
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = now()
        where project_plan_id = #{projectPlanId,jdbcType=BIGINT}
        and is_deleted = 0
    </update>

    <update id="updateStatusByProjectInfoIdAndItemCode">
        update csm.proj_project_plan
        set "status" = #{status,jdbcType=SMALLINT},
        <if test="status == 1">
            completion_time = now(),
            completion_user_id = #{updaterId},
        </if>
        updater_id = #{updaterId},
        update_time = now()
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        and project_plan_item_id = (select project_plan_item_id from dict_project_plan_item where project_plan_item_code = #{itemCode} and is_deleted = 0)
    </update>

    <update id="addTotalCountByProjectInfoIdAndItemCode">
        update proj_project_plan
        set total_count = total_count + #{totalCount},
            update_time = now()
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
          and project_plan_item_id = (select project_plan_item_id from dict_project_plan_item where project_plan_item_code = #{itemCode} and is_deleted = 0)
    </update>

    <update id="updateTotalAndCompleteCountByPlanId">
        update proj_project_plan
        set total_count    = #{totalCount},
            complete_count = #{completeCount},
            update_time    = now()
        where is_deleted = 0
          and project_plan_id = #{planId}
    </update>


    <select id="getProjectPlanByProjectInfoId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from csm.proj_project_plan
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        <if test="planType != null">
            and plan_type = #{planType}
        </if>
    </select>

    <select id="getProjectPlanViewByProjectInfoId" resultType="com.msun.csm.dao.entity.proj.ProjProjectPlanVO">
        select ppp.*,
        dppi.project_plan_item_code as "planItemCode",
        dpps.project_plan_stage_code as "planStageCode"
        from csm.proj_project_plan ppp
        left join csm.dict_project_plan_item dppi on ppp.project_plan_item_id = dppi.project_plan_item_id and dppi.is_deleted = 0
        left join csm.dict_project_plan_stage dpps on ppp.project_plan_stage_id = dpps.project_plan_stage_id and dpps.is_deleted = 0
        where ppp.is_deleted = 0
        and ppp.project_info_id = #{projectInfoId}
        <if test="planType != null">
            and ppp.plan_type = #{planType}
        </if>
    </select>


    <update id="cancelPriorProjectPlanItem">
        update csm.proj_project_plan
        set update_time                  = now(),
            updater_id                   = #{updaterId},
            prior_project_plan_item_flag = 0,
            prior_project_plan_item_id   = ''
        where is_deleted = 0
          and project_plan_id = #{projectPlanId}
    </update>

    <update id="updatePlanInfo" parameterType="com.msun.csm.dao.entity.proj.UpdateProjectPlanParam">
        update csm.proj_project_plan
        set
        <if test="implementationEngineerId != null ">
            implementation_engineer_id = #{implementationEngineerId},
        </if>
        <if test="completionTime != null ">
            completion_time = #{completionTime},
        </if>
        <if test="status != null ">
            "status" = #{status},
        </if>
        update_time = now(),
        updater_id = #{updaterId}

        where is_deleted = 0
        and project_plan_id = #{projectPlanId}
    </update>

</mapper>
