<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjAutocheckInfoMapper">

    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjAutocheckInfo">
        <result property="projAutocheckId" column="proj_autocheck_id" jdbcType="BIGINT"/>
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="reportLink" column="report_link" jdbcType="VARCHAR"/>
        <result property="reportLinkDetail" column="report_link_detail" jdbcType="VARCHAR"/>
        <result property="stepNum" column="step_num" jdbcType="SMALLINT"/>
        <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
        <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
        <result property="taskId" column="task_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        proj_autocheck_id,hospital_id,report_link,
        report_link_detail,step_num,creater_id,
        create_time,update_time,is_deleted,
        updater_id,project_info_id
    </sql>


    <update id="updateByParam" parameterType="com.msun.csm.dao.entity.proj.ProjAutocheckInfo">
        update csm.proj_autocheck_info
        <set>
            <if test="cloudHospitalId != null">
                cloud_hospital_id = #{cloudHospitalId},
            </if>
            <if test="reportLink != null">
                report_link = #{reportLink},
            </if>
            <if test="reportLinkDetail != null">
                report_link_detail = #{reportLinkDetail},
            </if>
            <if test="stepNum != null">
                step_num = #{stepNum},
            </if>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="baseTaskId != null">
                base_task_id = #{baseTaskId},
            </if>
            <if test="historyReportLink != null">
                history_report_link = #{historyReportLink}
            </if>
            <if test="businessTaskFlag != null ">
                business_task_flag = #{businessTaskFlag}
            </if>
        </set>
        <where>
            <if test="cloudHospitalId != null">
                cloud_hospital_id = #{cloudHospitalId}
            </if>
            <if test="orgId != null">
                and org_id = #{orgId}
            </if>
            <if test="projectInfoId != null">
                and project_info_id = #{projectInfoId}
            </if>
        </where>
    </update>

    <update id="updateLink" parameterType="com.msun.csm.dao.entity.proj.ProjAutocheckInfo">
        update csm.proj_autocheck_info
        set
        report_link = #{reportLink},
        report_link_detail = #{reportLinkDetail}
        <where>
            <if test="cloudHospitalId != null">
                cloud_hospital_id = #{cloudHospitalId}
            </if>
            <if test="orgId != null">
                and org_id = #{orgId}
            </if>
        </where>
    </update>
</mapper>
