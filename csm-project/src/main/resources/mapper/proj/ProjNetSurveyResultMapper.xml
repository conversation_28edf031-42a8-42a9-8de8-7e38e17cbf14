<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjNetSurveyResultMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        <!--@Table csm.proj_net_survey_result-->
        <id column="net_survey_result_id" jdbcType="BIGINT" property="netSurveyResultId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="net_survey_title_code" jdbcType="VARCHAR" property="netSurveyTitleCode"/>
        <result column="net_survey_title_name" jdbcType="VARCHAR" property="netSurveyTitleName"/>
        <result column="survey_result" jdbcType="VARCHAR" property="surveyResult"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        net_survey_result_id, project_info_id, net_survey_title_code, net_survey_title_name,
        survey_result, order_no, creater_id, create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_net_survey_result
        where net_survey_result_id = #{netSurveyResultId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_net_survey_result
        where net_survey_result_id = #{netSurveyResultId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        insert into csm.proj_net_survey_result (net_survey_result_id, project_info_id, net_survey_title_code,
        net_survey_title_name, survey_result, order_no,
        creater_id, create_time, updater_id,
        update_time, is_deleted)
        values (#{netSurveyResultId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{netSurveyTitleCode,jdbcType=VARCHAR},
        #{netSurveyTitleName,jdbcType=VARCHAR}, #{surveyResult,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        insert into csm.proj_net_survey_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netSurveyResultId != null">
                net_survey_result_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code,
            </if>
            <if test="netSurveyTitleName != null">
                net_survey_title_name,
            </if>
            <if test="surveyResult != null">
                survey_result,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="netSurveyResultId != null">
                #{netSurveyResultId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyTitleCode != null">
                #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleName != null">
                #{netSurveyTitleName,jdbcType=VARCHAR},
            </if>
            <if test="surveyResult != null">
                #{surveyResult,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        update csm.proj_net_survey_result
        <set>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleName != null">
                net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
            </if>
            <if test="surveyResult != null">
                survey_result = #{surveyResult,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where net_survey_result_id = #{netSurveyResultId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        update csm.proj_net_survey_result
        set project_info_id = #{projectInfoId,jdbcType=BIGINT},
        net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
        net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
        survey_result = #{surveyResult,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where net_survey_result_id = #{netSurveyResultId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_net_survey_result
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="net_survey_title_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.netSurveyTitleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="net_survey_title_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.netSurveyTitleName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="survey_result = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.surveyResult,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.orderNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where net_survey_result_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.netSurveyResultId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_net_survey_result
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="net_survey_title_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netSurveyTitleCode != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.netSurveyTitleCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="net_survey_title_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netSurveyTitleName != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.netSurveyTitleName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="survey_result = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.surveyResult != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.surveyResult,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.orderNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when net_survey_result_id = #{item.netSurveyResultId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where net_survey_result_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.netSurveyResultId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_net_survey_result
        (net_survey_result_id, project_info_id, net_survey_title_code, net_survey_title_name,
        survey_result, order_no, creater_id, create_time, updater_id, update_time, is_deleted
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.netSurveyResultId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.netSurveyTitleCode,jdbcType=VARCHAR}, #{item.netSurveyTitleName,jdbcType=VARCHAR},
            #{item.surveyResult,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=INTEGER}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        insert into csm.proj_net_survey_result
        (net_survey_result_id, project_info_id, net_survey_title_code, net_survey_title_name,
        survey_result, order_no, creater_id, create_time, updater_id, update_time, is_deleted
        )
        values
        (#{netSurveyResultId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{netSurveyTitleCode,jdbcType=VARCHAR},
        #{netSurveyTitleName,jdbcType=VARCHAR}, #{surveyResult,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        net_survey_result_id = #{netSurveyResultId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
        net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
        survey_result = #{surveyResult,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyResult">
        <!--@mbg.generated-->
        insert into csm.proj_net_survey_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netSurveyResultId != null">
                net_survey_result_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code,
            </if>
            <if test="netSurveyTitleName != null">
                net_survey_title_name,
            </if>
            <if test="surveyResult != null">
                survey_result,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netSurveyResultId != null">
                #{netSurveyResultId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyTitleCode != null">
                #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleName != null">
                #{netSurveyTitleName,jdbcType=VARCHAR},
            </if>
            <if test="surveyResult != null">
                #{surveyResult,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="netSurveyResultId != null">
                net_survey_result_id = #{netSurveyResultId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleName != null">
                net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
            </if>
            <if test="surveyResult != null">
                survey_result = #{surveyResult,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByProjectId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_net_survey_result
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and is_deleted = 0
        order by net_survey_title_code, order_no
    </select>

    <update id="deleteByProjectId">
        update csm.proj_net_survey_result
        set is_deleted = 1
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </update>
</mapper>
