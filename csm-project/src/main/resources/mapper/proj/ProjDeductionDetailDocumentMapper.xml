<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjDeductionDetailDocumentMapper">


    <select id="getDocumentScoreRecord" resultType="com.msun.csm.dao.entity.proj.DocumentScoreRecordPO">
        select pddd.proj_deduction_detail_info_id as "id",
               dps.project_stage_code,
               dps.project_stage_name,
               dmn.milestone_node_code,
               dmn.milestone_node_name,
               ppf.file_name,
               ppf.file_path,
               pddd.estimated_deduction,
               pddd.practical_deduction,
               pddd.remark,
               pddd.project_info_id,
               pddd.classification_code,
               pddd."source"
        from csm.proj_deduction_detail_info pddd
                 left join csm.dict_milestone_node dmn on
            pddd.milestone_node_id = dmn.milestone_node_id
                and dmn.invalid_flag = 0
                 left join csm.dict_project_stage dps on
            pddd.project_stage_code = dps.project_stage_code
                and dps.invalid_flag = 0
                 left join csm.proj_project_file ppf on
            pddd.project_file_id = ppf.project_file_id
                and ppf.is_deleted = 0
        where pddd.is_deleted = 0
        and operation_type = 'document'
          and pddd.project_info_id = #{projectInfoId}
        order by dps.order_no, dmn.milestone_node_code, pddd.proj_deduction_detail_info_id
    </select>

    <update id="updatePracticalDeductionOrRemarkById">
        update csm.proj_deduction_detail_info
        <set>
            <if test="practicalDeduction != null">
                practical_deduction = #{practicalDeduction},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="deductionType != null">
                deduction_type = #{deductionType},
            </if>
        </set>
        where proj_deduction_detail_info_id = #{id}
    </update>

</mapper>
