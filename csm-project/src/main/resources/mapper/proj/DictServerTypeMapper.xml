<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictServerTypeMapper">
    <select id="getAllServerType" resultType="com.msun.csm.dao.entity.dict.DictServerType">
        select *
        from csm.dict_server_type
        where is_deleted = 0
        order by sort_no
    </select>
</mapper>