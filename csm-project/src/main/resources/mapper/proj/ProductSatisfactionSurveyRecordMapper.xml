<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProductSatisfactionSurveyRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecord">
        <id property="productSatisfactionSurveyRecordId" column="product_satisfaction_survey_record_id" jdbcType="BIGINT"/>
        <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
        <result property="yyProductId" column="yy_product_id" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="score" column="score" jdbcType="DECIMAL"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="sendUserId" column="send_user_id" jdbcType="BIGINT"/>
        <result property="scoreTime" column="score_time" jdbcType="TIMESTAMP"/>
        <result property="scoreUserId" column="score_user_id" jdbcType="BIGINT"/>
        <result property="stopTime" column="stop_time" jdbcType="TIMESTAMP"/>
        <result property="stopUserId" column="stop_user_id" jdbcType="BIGINT"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        product_satisfaction_survey_record_id,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted,
        yy_product_id,
        status,
        score,
        remark,
        send_time,
        send_user_id,
        score_time,
        score_user_id,
        stop_time,
        stop_user_id,
        project_info_id
    </sql>

    <select id="getProductSatisfactionRecord" resultType="com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyRecordVO">
        select pssr.product_satisfaction_survey_record_id,
               pssr.yy_product_id,
               case
                   when dp.product_name is null then concat(product.product_name, '-', dpvm.yy_module_name)
                   else dp.product_name
                   end             as "yyProductName",
               pssr.status,
               pssr.score,
               pssr.remark,
               pssr.send_time,
               pssr.send_user_id,
               sendUser.user_name  as "sendUserName",
               pssr.score_time,
               pssr.score_user_id,
               pssr.score_user_id,
               scoreUser.user_name as "scoreUserName",
               pssr.project_info_id
        from csm.product_satisfaction_survey_record pssr
                 left join csm.dict_product dp on pssr.yy_product_id = dp.yy_product_id
                 left join csm.dict_product_vs_modules dpvm on pssr.yy_product_id = dpvm.yy_module_id
                 left join csm.dict_product product on dpvm.yy_product_id = product.yy_product_id and product.is_deleted = 0
                 left join csm.sys_user sendUser on pssr.send_user_id = sendUser.sys_user_id
                 left join csm.sys_user scoreUser on pssr.score_user_id = scoreUser.sys_user_id
        where pssr.is_deleted = 0
          and pssr.project_info_id = #{projectInfoId}
    </select>

    <insert id="saveProductSatisfactionSurveyRecord">
        insert into csm.product_satisfaction_survey_record (product_satisfaction_survey_record_id,
                                                            creater_id,
                                                            create_time,
                                                            updater_id,
                                                            update_time,
                                                            is_deleted,
                                                            yy_product_id,
                                                            status,
                                                            score,
                                                            remark,
                                                            send_time,
                                                            send_user_id,
                                                            score_time,
                                                            score_user_id,
                                                            stop_time,
                                                            stop_user_id,
                                                            project_info_id)
        values (#{productSatisfactionSurveyRecordId},
                #{createrId},
                #{createTime},
                #{updaterId},
                #{updateTime},
                #{isDeleted},
                #{yyProductId},
                #{status},
                #{score},
                #{remark},
                #{sendTime},
                #{sendUserId},
                #{scoreTime},
                #{scoreUserId},
                #{stopTime},
                #{stopUserId},
                #{projectInfoId})
    </insert>

    <update id="updateScoreById">
        update csm.product_satisfaction_survey_record
        set
        <if test="remark != null">
            remark = #{remark},
        </if>
        <if test="score != null">
            score = #{score},
            score_user_id = #{scoreUserId},
            score_time = now(),
        </if>
        update_time = now()
        where product_satisfaction_survey_record_id = #{id}
    </update>

    <select id="getProductSatisfactionRecordById" resultMap="BaseResultMap">
        select * from csm.product_satisfaction_survey_record where product_satisfaction_survey_record_id = #{id}
    </select>

    <update id="updateStatus">
        update csm.product_satisfaction_survey_record
        set status = #{status},
        <if test="status != null and status == 3">
            stop_user_id = #{operatorId},
            stop_time = now(),
        </if>
        <if test="status != null and status == 3">
            send_user_id = #{operatorId},
            send_time = now(),
        </if>
        update_time = now()
        where product_satisfaction_survey_record_id = #{id}
    </update>
</mapper>
