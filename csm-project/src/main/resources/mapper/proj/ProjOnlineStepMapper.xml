<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper">
    <select id="hasPatient" parameterType="long" resultType="int">
        select count(rprc.hzzn_flag)
        from csm.rule_product_rule_config rprc
            inner join (
        select pop.special_product_id as yy_order_product_id
        from csm.proj_special_product_record pop
                 left join csm.proj_project_info ppi on
            pop.project_info_id = ppi.project_info_id
        where pop.is_deleted = 0
          and ppi.project_info_id = #{projectInfoId}
        union all
        select pdr.product_deliver_id
        from csm.proj_product_deliver_record pdr
                 left join csm.proj_project_info ppi on pdr.project_info_id = ppi.project_info_id
        where pdr.is_deleted = 0
          and ppi.project_info_id = #{projectInfoId}
        <if test="orderInfoId != null and orderInfoId != ''">
            and ppi.order_info_id = #{orderInfoId}
        </if>
        ) sss on rprc.yy_product_id = sss.yy_order_product_id
        where rprc.hzzn_flag = #{hzznFlag}

    </select>

    <insert id="insertList" parameterType="com.msun.csm.dao.entity.proj.ProjOnlineStep">
        insert into csm.proj_online_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projOnlineStepId != null">
                proj_online_step_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="dictOnlineStepId != null">
                dict_online_step_id,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projOnlineStepId != null">
                #{projOnlineStepId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="dictOnlineStepId != null">
                #{dictOnlineStepId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectOnlineStepList" parameterType="com.msun.csm.model.dto.ProjOnlineStepDTO"
            resultType="com.msun.csm.model.vo.ProjOnlineStepVO">
        select pos.proj_online_step_id,
        pos.custom_info_id,
        pos.project_info_id,
        pos.status,
        cos2.config_online_step_id,
        cos2.online_step_name,
        cos2.online_step_code,
        cos2.previous_online_plan_id_array,
        cos2.link_address,
        cos2.operation_type,
        cos2.step_type,
        cos2.required_flag,
        pos.updater_id,
        su.user_name,
        pos.update_time,
        pos.create_time,
        cos2.order_no,
        cos2.pid,
        0 step_detail_status
        from csm.proj_online_step pos
        left join csm.config_online_step cos2 on
        pos.config_online_step_id = cos2.config_online_step_id
        left join csm.sys_user su on pos.updater_id = su.sys_user_id
        where
        pos.is_deleted = 0
        <if test="customInfoId != null and customInfoId != ''">
            and pos.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null and projectInfoId != ''">
            and pos.project_info_id = #{projectInfoId}
        </if>
        order by cos2.order_no
    </select>

    <select id="selectParentOnlineStepById" parameterType="com.msun.csm.model.dto.ProjOnlineStepDTO"
            resultType="com.msun.csm.dao.entity.proj.ProjOnlineStep">
        select pos2.*
        from csm.proj_online_step pos2
        where pos2.config_online_step_id = (
            select cos2.previous_online_plan_id_array
            from csm.proj_online_step pos
                     left join csm.config_online_step cos2 on pos.config_online_step_id = cos2.config_online_step_id
            where pos.proj_online_step_id = #{projOnlineStepId}
        )
          and pos2.custom_info_id = #{customInfoId}
          and pos2.project_info_id = #{projectInfoId}
    </select>

    <select id="findByProjectId" resultType="com.msun.csm.dao.entity.proj.ProjOnlineStep">
        select *
        from csm.proj_online_step
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
    </select>
    <select id="queryProjectOnlineStep" resultType="com.msun.csm.common.model.BaseIdNameExtendResp">
        select cos2.config_online_step_id as id,
               cos2.online_step_name  as "name",
               case when pos.config_online_step_id is not null then 1 else 0 end "isExist",
               case when pos.status is not null then pos.status else 0 end       "isFinish"
        from csm.config_online_step cos2
                 left join csm.proj_online_step pos on cos2.config_online_step_id = pos.config_online_step_id and
                                                       pos.project_info_id = #{projectInfoId}
                                                        and pos.is_deleted = 0
                 inner  join csm.proj_project_info ppi on 1=1 and ppi.project_info_id = #{projectInfoId}
        where cos2.upgradation_type != 3
          and
            case when ppi.project_type = 1 then cos2.monomer_flag = 1
                 when ppi.project_type = 2 then cos2.region_flag = 1
                end
        order by cos2.order_no

    </select>
    <select id="getUnwedDeptHospitalityInfoId" resultType="com.msun.csm.common.model.BaseIdNameResp"
            parameterType="com.msun.csm.model.req.project.ProjectOnlineBeforeReq">
        select
            dt.dept_id as id,
            dt.dept_name || '--' || phi.hospital_name as "name"
        from knowledge.dept dt
        inner join csm.proj_hospital_info phi on dt.hospital_id = phi.cloud_hospital_id
        where phi.is_deleted = 0
            and dt.invalid_flag = '0'
        <if test="hospitalInfoId != null">
            and phi.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="customInfoId != null">
            and phi.custom_info_id = #{customInfoId}
        </if>
        order by phi.hospital_info_id
    </select>
    <select id="getUnwedUserByDeptId" resultType="com.msun.csm.common.model.BaseIdNameResp"
            parameterType="com.msun.csm.model.req.project.ProjectOnlineBeforeReq">
        select user_id as id,
               user_name as "name"
        from knowledge.user
        where invalid_flag = '0'
          and dept_id = #{deptId}

    </select>
    <select id="getUnwedRoles" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select id, role_name as "name"
        from knowledge.sys_roles
        where flag_authorized = 1
        <if test="role != null and role != ''">
            and role = #{role}
        </if>
    </select>
    <select id="selectListOldUserByParams" resultType="com.msun.csm.dao.entity.SysUser"
            parameterType="com.msun.csm.model.req.project.ProjectOnlineBeforeReq">
        select  nsu.sys_user_id ,
                osu.user_id as "userYunyingId",
                osu.name as user_name
        from platform.sys_user osu
        inner join csm.sys_user nsu on osu.user_yunying_id = nsu.user_yunying_id::int8
        where osu.status = 'ENABLE'
        and nsu.is_deleted = 0
        and (nsu.sys_user_id = #{customerUserId}
        <if test="otherUserList!=null and otherUserList.size >0">
           or nsu.sys_user_id in
            <foreach collection="otherUserList" item="otherUser" index="index" open="(" separator="," close=")">
                #{otherUser}
            </foreach>
        </if>
        )

    </select>
    <select id="getUserListByDeptId" resultType="com.msun.csm.common.model.BaseIdNameResp"
            parameterType="com.msun.csm.model.req.project.ProjectOnlineBeforeReq">
        select sys_user_id as id,
               user_name as "name"
        from csm.sys_user
        where is_deleted = 0
          and dept_id = #{customerDeptId}
    </select>

    <update id="deleteByProjectAndStepIds">
        update csm.proj_online_step
        set is_deleted = 1
        where project_info_id = #{projectInfoId}
        and config_online_step_id in
        <foreach collection="stepIds" item="stepId" open="(" separator="," close=")">
                #{stepId}
        </foreach>
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_online_step
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
    <update id="updateRoles" parameterType="com.msun.csm.model.req.project.ProjectOnlineBeforeReq">
        update
            knowledge.sys_user_roles
        set
            <if test="roleId != null">
                role_id_list = #{roleId}::varchar,
            </if>
            his_update_time = now(),
            his_updater_id = -2
        where
            user_id = #{userId}
          and flag_invalid = '0'
          and role_id_list in ( select id
        from knowledge.sys_roles
        where flag_authorized = 1
        and role = 'SERVICE_SUBMIT')

    </update>
</mapper>
