<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectDailyReportDetailMapper">

    <select id="selectReportDetailByReportId" resultType="com.msun.csm.dao.entity.proj.ProjProjectDailyReportDetailVO">
        select
            ppdrd.project_daily_report_detail_id as "projectDailyReportDetailId",
            ppdrd.node_code as "nodeCode",
            case
                when cpdrdi.node_name is not null then cpdrdi.node_name
                else dmn.milestone_node_name
                end as "nodeName",
            ppdrd.report_detail_content as "reportDetailContent"
        from csm.proj_project_daily_report_detail ppdrd
                 left join csm.config_project_daily_report_detail_item cpdrdi on ppdrd.node_code = cpdrdi.node_code
                 left join csm.dict_milestone_node dmn on cpdrdi.node_code = dmn.milestone_node_code
        where ppdrd.is_deleted = 0
          and ppdrd.project_daily_report_record_id = #{reportRecordId}
    </select>


    <update id="updateReportDetailById" parameterType="com.msun.csm.dao.entity.proj.UpdateProjProjectDailyReportDetail">
        update proj_project_daily_report_detail
        set updater_id = #{updaterId},
            update_time = now(),
            report_detail_content = #{reportDetailContent}
        where project_daily_report_detail_id = #{projectDailyReportDetailId}
    </update>



</mapper>
