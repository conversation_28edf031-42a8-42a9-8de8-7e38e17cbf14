<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.proj.OldTbProjectReadyWorkMapper">
    <select id="getCount" resultType="int">
        SELECT
            count(1)
        FROM platform.tb_project_ready_work t
        WHERE project_id = #{projectId}
          and detail_id =  #{detailId}
          and invalid_flag=0
    </select>
</mapper>