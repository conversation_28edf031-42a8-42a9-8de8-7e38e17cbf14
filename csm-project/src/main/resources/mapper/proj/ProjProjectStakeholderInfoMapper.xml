<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectStakeholderInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_project_stakeholder_info-->
        <id column="project_stakeholder_info_id" jdbcType="BIGINT" property="projectStakeholderInfoId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="hospital_id" jdbcType="BIGINT" property="hospitalId"/>
        <result column="project_person_id" jdbcType="BIGINT" property="projectPersonId"/>
        <result column="project_person_name" jdbcType="VARCHAR" property="projectPersonName"/>
        <result column="project_person_type" jdbcType="SMALLINT" property="projectPersonType"/>
        <result column="project_team_id" jdbcType="BIGINT" property="projectTeamId"/>
        <result column="project_team_name" jdbcType="VARCHAR" property="projectTeamName"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="project_person_phone" jdbcType="VARCHAR" property="projectPersonPhone"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_stakeholder_info_id, project_id, hospital_id, project_person_id, project_person_name,
        project_person_type, project_team_id, project_team_name, is_deleted, creater_id,
        create_time, updater_id, update_time, project_person_phone
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_stakeholder_info
        where project_stakeholder_info_id = #{projectStakeholderInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_project_stakeholder_info
        where project_stakeholder_info_id = #{projectStakeholderInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_stakeholder_info (project_stakeholder_info_id, project_id,
        hospital_id, project_person_id, project_person_name,
        project_person_type, project_team_id, project_team_name,
        is_deleted, creater_id, create_time,
        updater_id, update_time, project_person_phone
        )
        values (#{projectStakeholderInfoId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT},
        #{hospitalId,jdbcType=BIGINT}, #{projectPersonId,jdbcType=BIGINT}, #{projectPersonName,jdbcType=VARCHAR},
        #{projectPersonType,jdbcType=SMALLINT}, #{projectTeamId,jdbcType=BIGINT}, #{projectTeamName,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{projectPersonPhone,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_stakeholder_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectStakeholderInfoId != null">
                project_stakeholder_info_id,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="hospitalId != null">
                hospital_id,
            </if>
            <if test="projectPersonId != null">
                project_person_id,
            </if>
            <if test="projectPersonName != null">
                project_person_name,
            </if>
            <if test="projectPersonType != null">
                project_person_type,
            </if>
            <if test="projectTeamId != null">
                project_team_id,
            </if>
            <if test="projectTeamName != null">
                project_team_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="projectPersonPhone != null">
                project_person_phone,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectStakeholderInfoId != null">
                #{projectStakeholderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                #{projectId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonId != null">
                #{projectPersonId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonName != null">
                #{projectPersonName,jdbcType=VARCHAR},
            </if>
            <if test="projectPersonType != null">
                #{projectPersonType,jdbcType=SMALLINT},
            </if>
            <if test="projectTeamId != null">
                #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectPersonPhone != null">
                #{projectPersonPhone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        update csm.proj_project_stakeholder_info
        <set>
            <if test="projectId != null">
                project_id = #{projectId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                hospital_id = #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonId != null">
                project_person_id = #{projectPersonId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonName != null">
                project_person_name = #{projectPersonName,jdbcType=VARCHAR},
            </if>
            <if test="projectPersonType != null">
                project_person_type = #{projectPersonType,jdbcType=SMALLINT},
            </if>
            <if test="projectTeamId != null">
                project_team_id = #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                project_team_name = #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectPersonPhone != null">
                project_person_phone = #{projectPersonPhone,jdbcType=VARCHAR},
            </if>
        </set>
        where project_stakeholder_info_id = #{projectStakeholderInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        update csm.proj_project_stakeholder_info
        set project_id = #{projectId,jdbcType=BIGINT},
        hospital_id = #{hospitalId,jdbcType=BIGINT},
        project_person_id = #{projectPersonId,jdbcType=BIGINT},
        project_person_name = #{projectPersonName,jdbcType=VARCHAR},
        project_person_type = #{projectPersonType,jdbcType=SMALLINT},
        project_team_id = #{projectTeamId,jdbcType=BIGINT},
        project_team_name = #{projectTeamName,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        project_person_phone = #{projectPersonPhone,jdbcType=VARCHAR}
        where project_stakeholder_info_id = #{projectStakeholderInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_stakeholder_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hospital_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.hospitalId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_person_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectPersonId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_person_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectPersonName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_person_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectPersonType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="project_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_team_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectTeamName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="project_person_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                    #{item.projectPersonPhone,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where project_stakeholder_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectStakeholderInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_stakeholder_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectId != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalId != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.hospitalId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_person_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPersonId != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectPersonId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_person_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPersonName != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectPersonName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_person_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPersonType != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectPersonType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectTeamId != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_team_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectTeamName != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectTeamName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_person_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPersonPhone != null">
                        when project_stakeholder_info_id = #{item.projectStakeholderInfoId,jdbcType=BIGINT} then
                        #{item.projectPersonPhone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_stakeholder_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectStakeholderInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_project_stakeholder_info
        (project_stakeholder_info_id, project_id, hospital_id, project_person_id, project_person_name,
        project_person_type, project_team_id, project_team_name, is_deleted, creater_id,
        create_time, updater_id, update_time, project_person_phone)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectStakeholderInfoId,jdbcType=BIGINT}, #{item.projectId,jdbcType=BIGINT},
            #{item.hospitalId,jdbcType=BIGINT}, #{item.projectPersonId,jdbcType=BIGINT},
            #{item.projectPersonName,jdbcType=VARCHAR},
            #{item.projectPersonType,jdbcType=SMALLINT}, #{item.projectTeamId,jdbcType=BIGINT},
            #{item.projectTeamName,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.projectPersonPhone,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_stakeholder_info
        (project_stakeholder_info_id, project_id, hospital_id, project_person_id, project_person_name,
        project_person_type, project_team_id, project_team_name, is_deleted, creater_id,
        create_time, updater_id, update_time, project_person_phone)
        values
        (#{projectStakeholderInfoId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT},
        #{projectPersonId,jdbcType=BIGINT}, #{projectPersonName,jdbcType=VARCHAR},
        #{projectPersonType,jdbcType=SMALLINT},
        #{projectTeamId,jdbcType=BIGINT}, #{projectTeamName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{projectPersonPhone,jdbcType=VARCHAR})
        on duplicate key update
        project_stakeholder_info_id = #{projectStakeholderInfoId,jdbcType=BIGINT},
        project_id = #{projectId,jdbcType=BIGINT},
        hospital_id = #{hospitalId,jdbcType=BIGINT},
        project_person_id = #{projectPersonId,jdbcType=BIGINT},
        project_person_name = #{projectPersonName,jdbcType=VARCHAR},
        project_person_type = #{projectPersonType,jdbcType=SMALLINT},
        project_team_id = #{projectTeamId,jdbcType=BIGINT},
        project_team_name = #{projectTeamName,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        project_person_phone = #{projectPersonPhone,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStakeholderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_stakeholder_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectStakeholderInfoId != null">
                project_stakeholder_info_id,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="hospitalId != null">
                hospital_id,
            </if>
            <if test="projectPersonId != null">
                project_person_id,
            </if>
            <if test="projectPersonName != null">
                project_person_name,
            </if>
            <if test="projectPersonType != null">
                project_person_type,
            </if>
            <if test="projectTeamId != null">
                project_team_id,
            </if>
            <if test="projectTeamName != null">
                project_team_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="projectPersonPhone != null">
                project_person_phone,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectStakeholderInfoId != null">
                #{projectStakeholderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                #{projectId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonId != null">
                #{projectPersonId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonName != null">
                #{projectPersonName,jdbcType=VARCHAR},
            </if>
            <if test="projectPersonType != null">
                #{projectPersonType,jdbcType=SMALLINT},
            </if>
            <if test="projectTeamId != null">
                #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectPersonPhone != null">
                #{projectPersonPhone,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectStakeholderInfoId != null">
                project_stakeholder_info_id = #{projectStakeholderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                project_id = #{projectId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                hospital_id = #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonId != null">
                project_person_id = #{projectPersonId,jdbcType=BIGINT},
            </if>
            <if test="projectPersonName != null">
                project_person_name = #{projectPersonName,jdbcType=VARCHAR},
            </if>
            <if test="projectPersonType != null">
                project_person_type = #{projectPersonType,jdbcType=SMALLINT},
            </if>
            <if test="projectTeamId != null">
                project_team_id = #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                project_team_name = #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectPersonPhone != null">
                project_person_phone = #{projectPersonPhone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>
