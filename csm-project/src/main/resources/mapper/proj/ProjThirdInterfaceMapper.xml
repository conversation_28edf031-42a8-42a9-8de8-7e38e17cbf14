<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        <!--@mbg.generated-->
        <!--@Table csm.proj_third_interface-->
        <id column="third_interface_id" jdbcType="BIGINT" property="thirdInterfaceId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="project_type" jdbcType="SMALLINT" property="projectType"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId"/>
        <result column="dict_interface_id" jdbcType="BIGINT" property="dictInterfaceId"/>
        <result column="dict_interface_name" jdbcType="VARCHAR" property="dictInterfaceName"/>
        <result column="dict_interface_firm_id" jdbcType="BIGINT" property="dictInterfaceFirmId"/>
        <result column="dict_interface_firm_name" jdbcType="VARCHAR" property="dictInterfaceFirmName"/>
        <result column="third_interface_version" jdbcType="VARCHAR" property="thirdInterfaceVersion"/>
        <result column="firm_contact_name" jdbcType="VARCHAR" property="firmContactName"/>
        <result column="firm_contact_phone" jdbcType="VARCHAR" property="firmContactPhone"/>
        <result column="online_flag" jdbcType="SMALLINT" property="onlineFlag"/>
        <result column="expect_time" jdbcType="DATE" property="expectTime"/>
        <result column="business_desc" jdbcType="VARCHAR" property="businessDesc"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="implements_type" jdbcType="SMALLINT" property="implementsType"/>
        <result column="dir_team_id" jdbcType="BIGINT" property="dirTeamId"/>
        <result column="dir_person_id" jdbcType="BIGINT" property="dirPersonId"/>
        <result column="auth_letter_files" jdbcType="VARCHAR" property="authLetterFiles"/>
        <result column="third_contract_files" jdbcType="VARCHAR" property="thirdContractFiles"/>
        <result column="third_interface_files" jdbcType="VARCHAR" property="thirdInterfaceFiles"/>
        <result column="data_set_id" jdbcType="BIGINT" property="dataSetId"/>
        <result column="data_set_name" jdbcType="VARCHAR" property="dataSetName"/>
        <result column="interface_type" jdbcType="SMALLINT" property="interfaceType"/>
        <result column="history_flag" jdbcType="SMALLINT" property="historyFlag"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        third_interface_id,
        custom_info_id,
        project_type,
        project_info_id,
        hospital_info_id,
        dict_interface_id,
        dict_interface_name,
        dict_interface_firm_id,
        dict_interface_firm_name,
        third_interface_version,
        firm_contact_name,
        firm_contact_phone,
        online_flag,
        expect_time,
        business_desc,
        yy_product_id,
        "status",
        implements_type,
        dir_team_id,
        dir_person_id,
        auth_letter_files,
        third_contract_files,
        third_interface_files,
        data_set_id,
        data_set_name,
        interface_type,
        history_flag,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_third_interface
        where third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.proj_third_interface
        where third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        <!--@mbg.generated-->
        insert into csm.proj_third_interface
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="thirdInterfaceId != null">
                third_interface_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="dictInterfaceId != null">
                dict_interface_id,
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name,
            </if>
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id,
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name,
            </if>
            <if test="thirdInterfaceVersion != null">
                third_interface_version,
            </if>
            <if test="firmContactName != null">
                firm_contact_name,
            </if>
            <if test="firmContactPhone != null">
                firm_contact_phone,
            </if>
            <if test="onlineFlag != null">
                online_flag,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="businessDesc != null">
                business_desc,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="implementsType != null">
                implements_type,
            </if>
            <if test="dirTeamId != null">
                dir_team_id,
            </if>
            <if test="dirPersonId != null">
                dir_preson_id,
            </if>
            <if test="authLetterFiles != null">
                auth_letter_files,
            </if>
            <if test="thirdContractFiles != null">
                third_contract_files,
            </if>
            <if test="thirdInterfaceFiles != null">
                third_interface_files,
            </if>
            <if test="dataSetId != null">
                data_set_id,
            </if>
            <if test="dataSetName != null">
                data_set_name,
            </if>
            <if test="interfaceType != null">
                interface_type,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="thirdInterfaceId != null">
                #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceId != null">
                #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="dictInterfaceFirmId != null">
                #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceVersion != null">
                #{thirdInterfaceVersion,jdbcType=VARCHAR},
            </if>
            <if test="firmContactName != null">
                #{firmContactName,jdbcType=VARCHAR},
            </if>
            <if test="firmContactPhone != null">
                #{firmContactPhone,jdbcType=VARCHAR},
            </if>
            <if test="onlineFlag != null">
                #{onlineFlag,jdbcType=SMALLINT},
            </if>
            <if test="expectTime != null">
                #{expectTime,jdbcType=DATE},
            </if>
            <if test="businessDesc != null">
                #{businessDesc,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="implementsType != null">
                #{implementsType,jdbcType=SMALLINT},
            </if>
            <if test="dirTeamId != null">
                #{dirTeamId,jdbcType=BIGINT},
            </if>
            <if test="dirPersonId != null">
                #{dirPersonId,jdbcType=BIGINT},
            </if>
            <if test="authLetterFiles != null">
                #{authLetterFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdContractFiles != null">
                #{thirdContractFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceFiles != null">
                #{thirdInterfaceFiles,jdbcType=VARCHAR},
            </if>
            <if test="dataSetId != null">
                #{dataSetId,jdbcType=BIGINT},
            </if>
            <if test="dataSetName != null">
                #{dataSetName,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                #{interfaceType,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        <!--@mbg.generated-->
        update csm.proj_third_interface
        <set>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceId != null">
                dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceVersion != null">
                third_interface_version = #{thirdInterfaceVersion,jdbcType=VARCHAR},
            </if>
            <if test="firmContactName != null">
                firm_contact_name = #{firmContactName,jdbcType=VARCHAR},
            </if>
            <if test="firmContactPhone != null">
                firm_contact_phone = #{firmContactPhone,jdbcType=VARCHAR},
            </if>
            <if test="onlineFlag != null">
                online_flag = #{onlineFlag,jdbcType=SMALLINT},
            </if>
            <if test="expectTime != null">
                expect_time = #{expectTime,jdbcType=DATE},
            </if>
            <if test="businessDesc != null">
                business_desc = #{businessDesc,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="implementsType != null">
                implements_type = #{implementsType,jdbcType=SMALLINT},
            </if>
            <if test="dirTeamId != null">
                dir_team_id = #{dirTeamId,jdbcType=BIGINT},
            </if>
            <if test="dirPersonId != null">
                dir_person_id = #{dirPersonId,jdbcType=BIGINT},
            </if>
            <if test="authLetterFiles != null">
                auth_letter_files = #{authLetterFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdContractFiles != null">
                third_contract_files = #{thirdContractFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceFiles != null">
                third_interface_files = #{thirdInterfaceFiles,jdbcType=VARCHAR},
            </if>
            <if test="dataSetId != null">
                data_set_id = #{dataSetId,jdbcType=BIGINT},
            </if>
            <if test="dataSetName != null">
                data_set_name = #{dataSetName,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                interface_type = #{interfaceType,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        <!--@mbg.generated-->
        update csm.proj_third_interface
        set custom_info_id = #{customInfoId,jdbcType=BIGINT},
        project_type = #{projectType,jdbcType=SMALLINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT},
        dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
        dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT},
        dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR},
        third_interface_version = #{thirdInterfaceVersion,jdbcType=VARCHAR},
        firm_contact_name = #{firmContactName,jdbcType=VARCHAR},
        firm_contact_phone = #{firmContactPhone,jdbcType=VARCHAR},
        online_flag = #{onlineFlag,jdbcType=SMALLINT},
        expect_time = #{expectTime,jdbcType=DATE},
        business_desc = #{businessDesc,jdbcType=VARCHAR},
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
        "status" = #{status,jdbcType=SMALLINT},
        implements_type = #{implementsType,jdbcType=SMALLINT},
        dir_team_id = #{dirTeamId,jdbcType=BIGINT},
        dir_person_id = #{dirPersonId,jdbcType=BIGINT},
        auth_letter_files = #{authLetterFiles,jdbcType=VARCHAR},
        third_contract_files = #{thirdContractFiles,jdbcType=VARCHAR},
        third_interface_files = #{thirdInterfaceFiles,jdbcType=VARCHAR},
        data_set_id = #{dataSetId,jdbcType=BIGINT},
        data_set_name = #{dataSetName,jdbcType=VARCHAR},
        interface_type = #{interfaceType,jdbcType=SMALLINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_third_interface
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.projectType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.hospitalInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="dict_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dictInterfaceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="dict_interface_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dictInterfaceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dict_interface_firm_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dictInterfaceFirmId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="dict_interface_firm_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dictInterfaceFirmName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="third_interface_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.thirdInterfaceVersion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="firm_contact_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.firmContactName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="firm_contact_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.firmContactPhone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="online_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.onlineFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="expect_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.expectTime,jdbcType=DATE}
                </foreach>
            </trim>
            <trim prefix="business_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.businessDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.yyProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="&quot;status&quot; = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.status,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="implements_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.implementsType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="dir_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dirTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="dir_person_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dirPersonId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="auth_letter_files = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.authLetterFiles,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="third_contract_files = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.thirdContractFiles,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="third_interface_files = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.thirdInterfaceFiles,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_set_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dataSetId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="data_set_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.dataSetName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="interface_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.interfaceType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                    then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where third_interface_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.thirdInterfaceId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_third_interface
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectType != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.projectType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalInfoId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.hospitalInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dict_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictInterfaceId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dictInterfaceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dict_interface_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictInterfaceName != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dictInterfaceName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dict_interface_firm_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictInterfaceFirmId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dictInterfaceFirmId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dict_interface_firm_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictInterfaceFirmName != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dictInterfaceFirmName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="third_interface_version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.thirdInterfaceVersion != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.thirdInterfaceVersion,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="firm_contact_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.firmContactName != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.firmContactName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="firm_contact_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.firmContactPhone != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.firmContactPhone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="online_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.onlineFlag != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.onlineFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="expect_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.expectTime != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.expectTime,jdbcType=DATE}
                    </if>
                </foreach>
            </trim>
            <trim prefix="business_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.businessDesc != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.businessDesc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProductId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.yyProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="&quot;status&quot; = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.status,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="implements_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.implementsType != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.implementsType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dir_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dirTeamId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dirTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dir_person_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dirPersonId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dirPersonId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="auth_letter_files = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.authLetterFiles != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.authLetterFiles,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="third_contract_files = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.thirdContractFiles != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.thirdContractFiles,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="third_interface_files = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.thirdInterfaceFiles != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.thirdInterfaceFiles,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="data_set_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dataSetId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dataSetId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="data_set_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dataSetName != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.dataSetName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="interface_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.interfaceType != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.interfaceType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when third_interface_id = #{item.thirdInterfaceId,jdbcType=BIGINT}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where third_interface_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.thirdInterfaceId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_third_interface
        (third_interface_id, custom_info_id, project_type, project_info_id, hospital_info_id,
        dict_interface_id, dict_interface_name, dict_interface_firm_id, dict_interface_firm_name,
        third_interface_version, firm_contact_name, firm_contact_phone, online_flag, expect_time,
        business_desc, yy_product_id, "status", implements_type, dir_team_id, dir_person_id,
        auth_letter_files, third_contract_files, third_interface_files, data_set_id, data_set_name,
        interface_type, history_flag, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.thirdInterfaceId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.projectType,jdbcType=SMALLINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.hospitalInfoId,jdbcType=BIGINT},
            #{item.dictInterfaceId,jdbcType=BIGINT}, #{item.dictInterfaceName,jdbcType=VARCHAR},
            #{item.dictInterfaceFirmId,jdbcType=BIGINT}, #{item.dictInterfaceFirmName,jdbcType=VARCHAR},
            #{item.thirdInterfaceVersion,jdbcType=VARCHAR}, #{item.firmContactName,jdbcType=VARCHAR},
            #{item.firmContactPhone,jdbcType=VARCHAR}, #{item.onlineFlag,jdbcType=SMALLINT},
            #{item.expectTime,jdbcType=DATE}, #{item.businessDesc,jdbcType=VARCHAR},
            #{item.yyProductId,jdbcType=BIGINT},
            #{item.status,jdbcType=SMALLINT}, #{item.implementsType,jdbcType=SMALLINT},
            #{item.dirTeamId,jdbcType=BIGINT},
            #{item.dirPersonId,jdbcType=BIGINT}, #{item.authLetterFiles,jdbcType=VARCHAR},
            #{item.thirdContractFiles,jdbcType=VARCHAR}, #{item.thirdInterfaceFiles,jdbcType=VARCHAR},
            #{item.dataSetId,jdbcType=BIGINT}, #{item.dataSetName,jdbcType=VARCHAR},
            #{item.interfaceType,jdbcType=SMALLINT},
            #{item.historyFlag,jdbcType=SMALLINT},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        <!--@mbg.generated-->
        insert into csm.proj_third_interface
        (third_interface_id, custom_info_id, project_type, project_info_id, hospital_info_id,
        dict_interface_id, dict_interface_name, dict_interface_firm_id, dict_interface_firm_name,
        third_interface_version, firm_contact_name, firm_contact_phone, online_flag, expect_time,
        business_desc, yy_product_id, "status", implements_type, dir_team_id, dir_person_id,
        auth_letter_files, third_contract_files, third_interface_files, data_set_id, data_set_name,
        interface_type, history_flag, is_deleted, creater_id, create_time, updater_id, update_time)
        values (#{thirdInterfaceId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{projectType,jdbcType=SMALLINT},
        #{projectInfoId,jdbcType=BIGINT}, #{hospitalInfoId,jdbcType=BIGINT}, #{dictInterfaceId,jdbcType=BIGINT},
        #{dictInterfaceName,jdbcType=VARCHAR}, #{dictInterfaceFirmId,jdbcType=BIGINT},
        #{dictInterfaceFirmName,jdbcType=VARCHAR}, #{thirdInterfaceVersion,jdbcType=VARCHAR},
        #{firmContactName,jdbcType=VARCHAR}, #{firmContactPhone,jdbcType=VARCHAR},
        #{onlineFlag,jdbcType=SMALLINT},
        #{expectTime,jdbcType=DATE}, #{businessDesc,jdbcType=VARCHAR}, #{yyProductId,jdbcType=BIGINT},
        #{status,jdbcType=SMALLINT}, #{implementsType,jdbcType=SMALLINT}, #{dirTeamId,jdbcType=BIGINT},
        #{dirPersonId,jdbcType=BIGINT}, #{authLetterFiles,jdbcType=VARCHAR},
        #{thirdContractFiles,jdbcType=VARCHAR},
        #{thirdInterfaceFiles,jdbcType=VARCHAR}, #{dataSetId,jdbcType=BIGINT}, #{dataSetName,jdbcType=VARCHAR},
        #{interfaceType,jdbcType=SMALLINT}, #{item.historyFlag,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}, custom_info_id =
        #{customInfoId,jdbcType=BIGINT}, project_type = #{projectType,jdbcType=SMALLINT}, project_info_id =
        #{projectInfoId,jdbcType=BIGINT}, hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}, dict_interface_id =
        #{dictInterfaceId,jdbcType=BIGINT}, dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
        dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT}, dict_interface_firm_name =
        #{dictInterfaceFirmName,jdbcType=VARCHAR}, third_interface_version = #{thirdInterfaceVersion,jdbcType=VARCHAR},
        firm_contact_name = #{firmContactName,jdbcType=VARCHAR}, firm_contact_phone =
        #{firmContactPhone,jdbcType=VARCHAR}, online_flag = #{onlineFlag,jdbcType=SMALLINT}, expect_time =
        #{expectTime,jdbcType=DATE}, business_desc = #{businessDesc,jdbcType=VARCHAR}, yy_product_id =
        #{yyProductId,jdbcType=BIGINT}, "status" = #{status,jdbcType=SMALLINT}, implements_type =
        #{implementsType,jdbcType=SMALLINT}, dir_team_id = #{dirTeamId,jdbcType=BIGINT}, dir_person_id =
        #{dirPersonId,jdbcType=BIGINT}, auth_letter_files = #{authLetterFiles,jdbcType=VARCHAR}, third_contract_files =
        #{thirdContractFiles,jdbcType=VARCHAR}, third_interface_files = #{thirdInterfaceFiles,jdbcType=VARCHAR},
        data_set_id = #{dataSetId,jdbcType=BIGINT}, data_set_name = #{dataSetName,jdbcType=VARCHAR}, interface_type =
        #{interfaceType,jdbcType=SMALLINT}, history_flag = #{item.historyFlag,jdbcType=SMALLINT}, is_deleted = #{isDeleted,jdbcType=SMALLINT}, creater_id =
        #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id =
        #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        <!--@mbg.generated-->
        insert into csm.proj_third_interface
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="thirdInterfaceId != null">
                third_interface_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="dictInterfaceId != null">
                dict_interface_id,
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name,
            </if>
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id,
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name,
            </if>
            <if test="thirdInterfaceVersion != null">
                third_interface_version,
            </if>
            <if test="firmContactName != null">
                firm_contact_name,
            </if>
            <if test="firmContactPhone != null">
                firm_contact_phone,
            </if>
            <if test="onlineFlag != null">
                online_flag,
            </if>
            <if test="expectTime != null">
                expect_time,
            </if>
            <if test="businessDesc != null">
                business_desc,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="status != null">
                "status",
            </if>
            <if test="implementsType != null">
                implements_type,
            </if>
            <if test="dirTeamId != null">
                dir_team_id,
            </if>
            <if test="dirPersonId != null">
                dir_person_id,
            </if>
            <if test="authLetterFiles != null">
                auth_letter_files,
            </if>
            <if test="thirdContractFiles != null">
                third_contract_files,
            </if>
            <if test="thirdInterfaceFiles != null">
                third_interface_files,
            </if>
            <if test="dataSetId != null">
                data_set_id,
            </if>
            <if test="dataSetName != null">
                data_set_name,
            </if>
            <if test="interfaceType != null">
                interface_type,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="thirdInterfaceId != null">
                #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceId != null">
                #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="dictInterfaceFirmId != null">
                #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceVersion != null">
                #{thirdInterfaceVersion,jdbcType=VARCHAR},
            </if>
            <if test="firmContactName != null">
                #{firmContactName,jdbcType=VARCHAR},
            </if>
            <if test="firmContactPhone != null">
                #{firmContactPhone,jdbcType=VARCHAR},
            </if>
            <if test="onlineFlag != null">
                #{onlineFlag,jdbcType=SMALLINT},
            </if>
            <if test="expectTime != null">
                #{expectTime,jdbcType=DATE},
            </if>
            <if test="businessDesc != null">
                #{businessDesc,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=SMALLINT},
            </if>
            <if test="implementsType != null">
                #{implementsType,jdbcType=SMALLINT},
            </if>
            <if test="dirTeamId != null">
                #{dirTeamId,jdbcType=BIGINT},
            </if>
            <if test="dirPersonId != null">
                #{dirPersonId,jdbcType=BIGINT},
            </if>
            <if test="authLetterFiles != null">
                #{authLetterFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdContractFiles != null">
                #{thirdContractFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceFiles != null">
                #{thirdInterfaceFiles,jdbcType=VARCHAR},
            </if>
            <if test="dataSetId != null">
                #{dataSetId,jdbcType=BIGINT},
            </if>
            <if test="dataSetName != null">
                #{dataSetName,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                #{interfaceType,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="thirdInterfaceId != null">
                third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceId != null">
                dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceVersion != null">
                third_interface_version = #{thirdInterfaceVersion,jdbcType=VARCHAR},
            </if>
            <if test="firmContactName != null">
                firm_contact_name = #{firmContactName,jdbcType=VARCHAR},
            </if>
            <if test="firmContactPhone != null">
                firm_contact_phone = #{firmContactPhone,jdbcType=VARCHAR},
            </if>
            <if test="onlineFlag != null">
                online_flag = #{onlineFlag,jdbcType=SMALLINT},
            </if>
            <if test="expectTime != null">
                expect_time = #{expectTime,jdbcType=DATE},
            </if>
            <if test="businessDesc != null">
                business_desc = #{businessDesc,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="implementsType != null">
                implements_type = #{implementsType,jdbcType=SMALLINT},
            </if>
            <if test="dirTeamId != null">
                dir_team_id = #{dirTeamId,jdbcType=BIGINT},
            </if>
            <if test="dirPersonId != null">
                dir_person_id = #{dirPersonId,jdbcType=BIGINT},
            </if>
            <if test="authLetterFiles != null">
                auth_letter_files = #{authLetterFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdContractFiles != null">
                third_contract_files = #{thirdContractFiles,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceFiles != null">
                third_interface_files = #{thirdInterfaceFiles,jdbcType=VARCHAR},
            </if>
            <if test="dataSetId != null">
                data_set_id = #{dataSetId,jdbcType=BIGINT},
            </if>
            <if test="dataSetName != null">
                data_set_name = #{dataSetName,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                interface_type = #{interfaceType,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectThirdInterface" parameterType="com.msun.csm.model.dto.ProjThirdInterfacePageDTO"
            resultType="com.msun.csm.model.vo.ProjThirdInterfaceVO">
        select distinct pti.*,
        sd.dept_name as dirTeamName,
        su.user_name as dirPersonName,
        pd.product_name as productName,
        phi.hospital_name as hospitalName
        from csm.proj_third_interface pti
        left join csm.sys_dept sd on pti.dir_team_id = sd.sys_dept_id and sd.is_deleted = 0
        left join csm.sys_user su on pti.dir_person_id = su.sys_user_id and su.is_deleted = 0
        left join csm.dict_product pd on pti.yy_product_id = pd.yy_product_id and pd.is_deleted = 0
        left join csm.proj_hospital_info phi on pti.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        left join (
            select pci.*,sd.sys_dept_id from platform.customer cus
            inner join csm.proj_custom_info pci on cus.customer_yunying_id = pci.yy_customer_id and pci.is_deleted=0
            inner join knowledge.yjk_maintenance_team ymt on cus.yjk_interface_team_id = ymt.id
            inner join csm.sys_dept sd on sd.dept_yunying_id = ymt.dept_yunying_id and sd.is_deleted = 0
        union all
            select pci.*,sd.sys_dept_id from
            csm.proj_custom_info pci
            inner join csm.proj_project_info ppi on ppi.custom_info_id = pci.custom_info_id
            inner join csm.proj_project_member ppm on ppi.project_info_id = ppm.project_info_id
            inner join csm.sys_dept sd on sd.dept_yunying_id = ppm.project_team_id and sd.is_deleted = 0
            inner join csm.dict_project_role dpr on ppm.project_member_role_id = dpr.project_role_id
            where dpr.project_role_code = 'interface-leader'
            and ppm.is_deleted =0
            and dpr.is_deleted =0
            and pci.is_deleted =0
            and ppi.is_deleted =0

        ) pcsi on pti.custom_info_id = pcsi.custom_info_id
        where pti.is_deleted = 0
        and pti.hospital_info_id != -1
        <if test="customInfoId != null">
            and pti.custom_info_id = #{customInfoId}
        </if>
        <if test="pageSource != null and pageSource == 1 and dirTeamId != null">
            and pcsi.sys_dept_id = #{dirTeamId}
        </if>
        <if test="(pageSource == null or pageSource == 0) and dirTeamId != null">
            and pti.dir_team_id = #{dirTeamId}
        </if>
        <if test="projectType != null">
            and pti.project_type = #{projectType}
        </if>
        <if test="projectInfoId != null">
            and pti.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and pti.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test='dictInterfaceName != null and dictInterfaceName != "" '>
            and pti.dict_interface_name like concat('%',#{dictInterfaceName},'%')
        </if>
        <if test="status != null">
            and pti.status = #{status}
        </if>
        <if test="implementsType != null">
            and pti.implements_type = #{implementsType}
        </if>
        <if test="onlineFlag != null">
            and pti.online_flag = #{onlineFlag}
        </if>
        <if test="authLetterFlag != null">
            <if test="authLetterFlag == 1">
                and pti.auth_letter_files is not null
            </if>
            <if test="authLetterFlag == 0">
                and pti.auth_letter_files is null
            </if>
        </if>
        <if test="thirdInterfaceId != null">
            and pti.third_interface_id = #{thirdInterfaceId}
        </if>
        <if test="dirPersonId != null">
            and pti.dir_person_id = #{dirPersonId}
        </if>
        <if test= "interfaceCategory != null and interfaceCategory != ''">
            and pti.interface_category = #{interfaceCategory}
        </if>
        order by pti.create_time desc
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_third_interface
        where is_deleted = 0
        and proj_third_interface.third_interface_id in
        <foreach collection="interfaceIdList" item="projThirdInterfaceId" open="(" separator="," close=")">
            #{projThirdInterfaceId}
        </foreach>
    </select>

    <update id="updateAuthLetterFiles">
        update csm.proj_third_interface
        set auth_letter_files = #{fileId}
        where third_interface_id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectPageForReviewInterface" parameterType="com.msun.csm.model.dto.InterfaceReviewPageDTO"
            resultType="com.msun.csm.model.vo.InterfaceReviewVO">
        select
        pti.third_interface_id ,
        pti.dict_interface_name as "interfaceName",
        pci.custom_info_id ,
        pci.custom_name as "customInfoName",
        su.user_name as "reviewSubmitterName",
        pti.review_submit_time as "reviewSubmitTime",
        su2.user_name as "reviewUserName",
        pti.review_date as "reviewTime",
        pti.data_set_id,
        pti.data_set_name,
        pti.status
        from csm.proj_third_interface pti
        left join csm.sys_user su on pti.review_submitter = su.sys_user_id and su.is_deleted = 0
        left join csm.sys_user su2 on pti.review_user_id = su2.sys_user_id and su2.is_deleted = 0
        left join csm.proj_custom_info pci on pti.custom_info_id = pci.custom_info_id and pci.is_deleted = 0
        where pti.is_deleted = 0 and (pti.review_submit_time is not null or pti.review_user_id is null)
        and pti.status > 0
        <if test="interfaceName != null">
            and pti.dict_interface_name like concat('%',#{interfaceName},'%')
        </if>
        <if test="customInfoId != null">
            and pti.custom_info_id = #{customInfoId}
        </if>
        <if test="status != null">
            and pti.status = #{status}
        </if>
        <if test='startTime != null and startTime != "" '>
            and pti.review_date >= to_timestamp(#{startTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test='endTime != null and endTime != "" '>
            and pti.review_date &lt;= to_timestamp(#{endTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        order by pti.review_submit_time desc , pti.create_time desc
    </select>

    <update id="updateStatusByIds">
        update csm.proj_third_interface
        set status = #{status}
        where third_interface_id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectImspInterfaceData" resultType="com.msun.csm.model.vo.ImspInterfaceDataVO">
        select distinct ttr.id,
        tpnvo.new_custom_info_id,
        tpnvo.old_custom_id,
        tpnvo.new_project_info_id,
        tpnvo.old_project_info_id,
        p.is_area,
        ttr.customer_info_id,
        ci.customer_name,
        ttr.third_name,
        pdpa.firm_name,
        pdpa.version_name,
        ttr.online_need,
        ttr.expect_date,
        ttr.service_desc,
        d.product_yunying_id,
        pdpa.status,
        pdpa.implements_type,
        pdpa.dir_person,
        pdpa.review_user_id,
        pdpa.review_date,
        pdpa.create_user_id as reviewCreateUser,
        pdpa.create_time as reviewCreateDate,
        pdpa.dev_port_id as dataSetId,
        pdpa.dev_port_name as dataSetName,
        ttr.create_id,
        ttr.create_time,
        ttr.update_id,
        ttr.update_time,
        ttr.authorcode_status as authorCodeStatus,
        ttr.knowledge_result
        from platform.the_third_result ttr
        left join platform.project p on ttr.project_id = p.id
        left join comm.customer_info ci on ttr.customer_info_id = ci.id
        left join platform.project_develop_port_apply pdpa on pdpa.survey_id = ttr.id
        left join platform.product d on d.id = pdpa.interface_product_id
        left join csm.tmp_project_new_vs_old tpnvo
        on tpnvo.old_custom_id = ttr.customer_id and tpnvo.old_project_info_id = ttr.project_id
        where ttr.third_name is not null
        and ttr.create_time is not null
        and pdpa.create_user_id is not null
        and d.product_yunying_id is not null
        and ttr.csm_import_flag = 0
        and tpnvo.new_project_info_id > 0
        <if test="projectInfoId != null and projectInfoId != -1">
            and tpnvo.new_project_info_id = #{projectInfoId}
        </if>
        <if test="thirdInterfaceId != null and thirdInterfaceId != -1">
            and ttr.id = #{thirdInterfaceId}
        </if>
    </select>

    <select id="selectImspInterfaceAuthorDataForOld" parameterType="long"
            resultType="com.msun.csm.model.vo.ImspInterfaceAuthorDataVO">
        select data_id,
               public_key,
               private_key,
               app_id,
               auth_apply_id,
               test_procedure
        from platform.interface_source_test
        where private_key is not null
          and data_id = #{thirdInterfaceId}
        group by data_id,
                 public_key,
                 private_key,
                 app_id,
                 auth_apply_id,
                 test_procedure
    </select>

    <select id="selectImspInterfaceAuthorDataForNew" parameterType="long"
            resultType="com.msun.csm.model.vo.ImspInterfaceAuthorDataVO">
        select public_key,
               private_key,
               app_id,
               auth_apply_id,
               test_procedure
        from platform.interface_vs_appkey
        where data_id = #{thirdInterfaceId}
          and private_key is not null
        group by public_key,
                 private_key,
                 app_id,
                 auth_apply_id,
                 test_procedure
    </select>
    <update id="updateCsmImportFlag" parameterType="long">
        update platform.the_third_result
        set csm_import_flag = 1
        where id = #{id}
    </update>

    <select id="selectOldImspInterfaceGroupData" parameterType="long"
            resultType="com.msun.csm.model.vo.OldImspGroupInterfaceVO">
        select *
        from platform.group_interface_apply_auth
        where data_id = #{id}
    </select>

    <select id="selectOldInterfaceForFile" parameterType="long"
            resultType="com.msun.csm.model.vo.ImspInterfaceDataVO">
        select toivn.new_interface_id as newInterfaceId,
        sfi.file_data as oldFileUrl ,
        sfi.old_name as oldFileName
        from platform.the_third_result ttr
        left join csm.tmp_old_interface_vs_new toivn on ttr.id = toivn.old_interface_id and toivn.file_flag = 0
        left join platform.sys_file_info sfi on sfi.file_id = ttr.file_id::int8
        where ttr.csm_import_flag = 1
        and ttr.file_id is not null
        <if test="newInterfaceId != null and newInterfaceId != -1">
            and toivn.new_interface_id = #{newInterfaceId}
        </if>
    </select>

    <select id="findThirdInterfaceList" resultType="com.msun.csm.model.imsp.ThirdInterfaceVO">
        select pti.third_interface_id,pti.dict_interface_name,
        case pti.online_flag when '1' then '是' else '否' end as online_flag,
        pti.status,pti.expect_time
        from csm.proj_third_interface pti
        left join csm.proj_hospital_info phi on pti.hospital_info_id = phi.hospital_info_id
        where phi.cloud_hospital_id = #{hospitalId}
    </select>

    <select id="getRequiredInterface" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_third_interface
        where is_deleted = 0
        and online_flag = 1
        and project_info_id = #{projectInfoId}
    </select>

    <update id="updateContractFiles">
        update csm.proj_third_interface
        set third_contract_files = #{fileId}
        where third_interface_id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateInterfaceDir">
        update csm.proj_third_interface
        set dir_team_id   = null,
            dir_person_id = null
        where third_interface_id = #{interfaceId}
    </update>
    <update id="updateByProjectInfoIdAndProjectType">
        update csm.proj_third_interface
        set project_type = #{projectType}
        where project_info_id = #{projectInfoId}
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_third_interface
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
    <update id="updateDeptIdAndUserIdByParamers">
        update csm.proj_third_interface pti
       set  dir_team_id =(select sys_dept_id from csm.sys_dept where is_deleted =0 and dept_yunying_id
        in (
        select yunying_id from platform.sys_dept where dept_id = ss.Customer_Team_Id
        ) limit 1 ) ,
                dir_person_id =  (select sys_user_id from csm.sys_user where is_deleted =0 and user_yunying_id
        in (
        select user_yunying_id::varchar from platform.sys_user   where user_id = ss.Accept_Id_Hd
        ) limit 1 )
       from (
           select * from knowledge.know_question_feedback where id = #{feedbackId}::int8 and is_imsp_send = '6'
            ) ss
        where pti.third_interface_id = ss.third_interface_id

    </update>
    <update id="updateFeedbackByInterfaceId">
        UPDATE knowledge.know_question_feedback
        SET knowledge_status = #{status},
            his_update_time= now()
        WHERE is_imsp_send = '6'
            and
            third_interface_id = #{thirdInterfaceId}
    </update>


    <select id="getInterfaceAfterSurvey" resultType="com.msun.csm.dao.entity.proj.InterfaceAfterSurvey">
        SELECT pti.third_interface_id,
               pti.create_time,
               case
                   when pti.interface_type = 0 then '上传类'
                   when pti.interface_type = 1 then '交互类'
                   when pti.interface_type = 2 then '医保类'
                   else '接口类'
                   end interface_type,
               pti.project_info_id
        FROM csm.proj_third_interface pti
        where pti.is_deleted = 0
          and pti.project_info_id = #{projectInfoId}
          and pti.create_time &gt;= cast(#{completeTime, jdbcType=VARCHAR} as TIMESTAMP)
    </select>
    <select id="selectListToNewTime" resultType="com.msun.csm.dao.entity.proj.ProjThirdInterface">
        SELECT pti.*
        FROM csm.proj_third_interface pti
        where pti.is_deleted = 0
          and pti.interface_type = 2
          order by  pti.create_time desc
    </select>
    <select id="selectInterfaceInOtherSystemInfo" resultType="java.lang.Integer">
        select coalesce((
                            select
                                case when c.use_new_flow in (1,2) and c.yjk_interface_team_id is not null and c.yjk_interface_team_id != 0  then 1
                                     else 0 end
                            from
                                platform.customer c
                                    inner join csm.proj_custom_info pci on c.customer_yunying_id = pci.yy_customer_id
                                    inner join csm.proj_project_info ppi on ppi.custom_info_id = pci.custom_info_id
                            where  ppi.project_info_id = #{projectInfoId}
                            limit 1
                        )  ,0)
    </select>
    <select id="selectInterfaceCategory" resultType="com.msun.csm.common.model.BaseIdCodeNameResp">
        select
            interface_category as code,
            interface_category as "name"
        from
            csm.proj_third_interface pti
        where
            interface_category is not null
          and interface_category != ''
          and is_deleted = 0
        group by
            interface_category
    </select>

    <select id="getInterfaceCount" resultType="int">
        select count(*)
        from csm.proj_third_interface
        where is_deleted = 0
        and online_flag = 1
        and project_info_id = #{projectInfoId}
        <if test="statusList != null and statusList.size() != 0">
            and status not in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
    </select>
</mapper>
