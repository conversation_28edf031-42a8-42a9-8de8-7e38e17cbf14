<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceRuleMapper">

    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectAcceptanceRule">
            <id property="projectAcceptanceRuleId" column="project_acceptance_rule_id" jdbcType="BIGINT"/>
            <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
            <result property="projectRuleCode" column="project_rule_code" jdbcType="VARCHAR"/>
            <result property="projectRuleContent" column="project_rule_content" jdbcType="VARCHAR"/>
            <result property="verityWay" column="verity_way" jdbcType="VARCHAR"/>
            <result property="requiredFlag" column="required_flag" jdbcType="SMALLINT"/>
            <result property="templateFlag" column="template_flag" jdbcType="SMALLINT"/>
            <result property="templateFileCode" column="template_file_code" jdbcType="VARCHAR"/>
            <result property="projectAcceptanceId" column="project_acceptance_id" jdbcType="BIGINT"/>
            <result property="projectFileId" column="project_file_id" jdbcType="BIGINT"/>
            <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
            <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        project_acceptance_rule_id,project_info_id,project_rule_code,
        project_rule_content,verity_way,required_flag,
        template_flag,template_file_code,project_acceptance_id,
        project_file_id,order_no,is_deleted,
        creater_id,create_time,updater_id,
        update_time
    </sql>


    <select id="getAcceptanceRulesByAcceptanceId" resultType="com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO">
        select
        ppf.file_name ,
        ppf.file_path,
        sf.file_path template_url,
        ppac.project_acceptance_rule_id,
        ppac.project_info_id,
        ppac.project_rule_code,
        ppac.project_rule_content,
        ppac.verity_way,required_flag,
        ppac.template_flag,
        ppac.template_file_code,
        ppac.project_acceptance_id,
        ppac.project_file_id,
        ppac.order_no,
        ppac.is_deleted,
        ppac.creater_id,
        ppac.create_time,
        ppac.updater_id,
        ppac.update_time
        from csm.proj_project_acceptance_rule ppac
        left join csm.proj_project_file ppf on ppac.project_file_id =ppf.project_file_id
        left join csm.sys_file sf on ppac.template_file_code =sf.file_code
        where ppac.project_acceptance_id = #{acceptanceId,jdbcType=BIGINT}
        and ppac.is_deleted = 0
        order by ppac.order_no
    </select>

    <insert id="batchInsert" parameterType="map">
       insert into csm.proj_project_acceptance_rule
       ( project_acceptance_rule_id,
         project_info_id,
         project_rule_code,
         project_rule_content,
         verity_way,
         required_flag,
         template_flag,
         template_file_code,
         project_acceptance_id,
         project_file_id,
         order_no,
         is_deleted,
         creater_id,
         create_time)
         values
        <foreach collection="rules" item="item" separator=",">
             (#{item.projectAcceptanceRuleId,jdbcType=BIGINT},
              #{item.projectInfoId,jdbcType=BIGINT},
              #{item.projectRuleCode,jdbcType=VARCHAR},
              #{item.projectRuleContent,jdbcType=VARCHAR},
              #{item.verityWay,jdbcType=VARCHAR},
              #{item.requiredFlag,jdbcType=SMALLINT},
              #{item.templateFlag,jdbcType=SMALLINT},
              #{item.templateFileCode,jdbcType=VARCHAR},
              #{item.projectAcceptanceId,jdbcType=BIGINT},
              #{item.projectFileId,jdbcType=BIGINT},
              #{item.orderNo,jdbcType=SMALLINT},
              #{item.isDeleted,jdbcType=SMALLINT},
              #{item.createrId,jdbcType=BIGINT},
              #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="rules" item="item" index="index" separator=";">
            update csm.proj_project_acceptance_rule
            set
            project_file_id = #{item.projectFileId},
            updater_id = #{item.updaterId},
            update_time = #{item.updateTime},
            project_acceptance_id = #{item.projectAcceptanceId}
            where project_acceptance_rule_id = #{item.projectAcceptanceRuleId}
        </foreach>
    </update>

    <select id="getAcceptanceRulesByProjectInfoId" resultType="com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO">
        select
        ppf.file_name ,
        ppf.file_path,
        sf.file_path template_url,
        ppac.project_acceptance_rule_id,
        ppac.project_info_id,
        ppac.project_rule_code,
        ppac.project_rule_content,
        ppac.verity_way,required_flag,
        ppac.template_flag,
        ppac.template_file_code,
        ppac.project_acceptance_id,
        ppac.project_file_id,
        ppac.order_no,
        ppac.is_deleted,
        ppac.creater_id,
        ppac.create_time,
        ppac.updater_id,
        ppac.update_time
        from csm.proj_project_acceptance_rule ppac
        left join csm.proj_project_file ppf on ppac.project_file_id =ppf.project_file_id
        left join csm.sys_file sf on ppac.template_file_code =sf.file_code
        where ppac.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        and ppac.is_deleted = 0
        order by ppac.required_flag desc, ppac.order_no
    </select>

    <update id="deleteByProjectInfoId">
        update csm.proj_project_acceptance_rule
        set is_deleted = 1
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </update>

    <update id="deleteFileById">
        update csm.proj_project_acceptance_rule
        set project_file_id = null
        where project_acceptance_rule_id = #{acceptanceId,jdbcType=BIGINT}
    </update>
</mapper>
