<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper">
    <select id="selectHospitalInfoList" parameterType="com.msun.csm.model.dto.ProjHospitalInfoPageDTO"
            resultType="com.msun.csm.model.vo.ProjHospitalInfoVO">
        select phi.hospital_info_id,
        phi.hospital_name,
        phi.cloud_hospital_id,
        phi.cloud_domain,
        phi.hospital_open_status,
        phi.terminal_count,
        phi.clinic_count,
        phi.village_doctors_count,
        phi.hospital_bed_count,
        phi.hospital_out_patient_count,
        phi.population,
        phi.health_bureau_flag,
        phi.organization_code,
        phi.social_cred_code,
        phi.hospital_annual_income,
        phi.open_time,
        phi.online_time,
        phi.org_id,
        case when phvpt.project_type is not null and phvpt.project_type != 0 then phvpt.project_type
            else phi.hos_project_type end as project_type
        from csm.proj_hospital_info phi
        left join csm.proj_hospital_vs_project_type phvpt on phvpt.hospital_info_id = phi.hospital_info_id and
        phvpt.is_deleted = 0
        where phi.is_deleted = 0
        <if test="customInfoId != null and customInfoId != ''">
            and phvpt.custom_info_id = #{customInfoId}
        </if>
        <if test="projectType != null and projectType != ''">
            and phvpt.project_type = #{projectType}
        </if>
        <if test="hospitalOpenStatus != null">
            and phi.hospital_open_status = #{hospitalOpenStatus}
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            and phi.hospital_name like concat('%', #{hospitalName}, '%')
        </if>
        <if test="hospitalInfoId != null ">
            and phi.hospital_info_id = #{hospitalInfoId}
        </if>
        order by phi.health_bureau_flag desc , phi.hospital_name asc
    </select>

    <update id="updateHospitalInfo" parameterType="com.msun.csm.model.dto.ProjHospitalInfoDTO">
        update csm.proj_hospital_info
        <set>
            <if test="hospitalOpenStatus != null and hospitalOpenStatus != ''">
                hospital_open_status = #{hospitalOpenStatus},
            </if>
        </set>
        where hospital_info_id = #{hospitalInfoId}
    </update>

    <update id="updateBatchStateByHospitalInfoList" parameterType="java.util.List">
        update csm.proj_hospital_info
        <set>
            hospital_open_status = #{hospitalOpenStatus},
            <if test="hospitalOpenStatus == 0">
                cloud_domain = null,
            </if>
        </set>
        where hospital_info_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchByHospitalInfoList">
        update csm.proj_hospital_info
        <set>
            <if test="info.hospitalOpenStatus != null">
                hospital_open_status = #{info.hospitalOpenStatus,jdbcType=INTEGER},
            </if>
            <if test="info.cloudDomain != null and info.cloudDomain != ''">
                cloud_domain = #{info.cloudDomain,jdbcType=VARCHAR},
            </if>
            <if test="info.envId != null and info.envId != ''">
                env_id = #{info.envId,jdbcType=BIGINT},
            </if>
            <if test="info.envName != null and info.envName != ''">
                env_name = #{info.envName,jdbcType=VARCHAR},
            </if>
            <if test="info.orgId != null">
                org_id = #{info.orgId,jdbcType=BIGINT},
            </if>
            <if test="info.cloudHospitalId != null">
                cloud_hospital_id = #{info.cloudHospitalId,jdbcType=BIGINT},
            </if>
        </set>
        where hospital_info_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchCloudDomainByHospitalInfoList">
        update csm.proj_hospital_info
        <set>
            cloud_domain = #{cloudDomain,jdbcType=VARCHAR},
        </set>
        where hospital_info_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateHospitalInfoByCustomInfoId">
        update csm.proj_hospital_info
        <set>
            <if test="envId != null">
                env_id = #{envId,jdbcType=VARCHAR},
            </if>
            <if test="envName != null">
                env_name = #{envName,jdbcType=VARCHAR},
            </if>
        </set>
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatchByHospitalInfoListByCloudHospitalId">
        update csm.proj_hospital_info
        <set>
            <if test="info.hospitalOpenStatus != null">
                hospital_open_status = #{info.hospitalOpenStatus,jdbcType=INTEGER},
            </if>
            <if test="info.cloudDomain != null and info.cloudDomain != ''">
                cloud_domain = #{info.cloudDomain,jdbcType=VARCHAR},
            </if>
            <if test="info.envId != null and info.envId != ''">
                env_id = #{info.envId,jdbcType=BIGINT},
            </if>
            <if test="info.envName != null and info.envName != ''">
                env_name = #{info.envName,jdbcType=VARCHAR},
            </if>
        </set>
        where cloud_hospital_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchCloudHospitalIdAndOrgIdByCustomInfoId">
        update csm.proj_hospital_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="hospitalList" index="index" item="item">
                    when hospital_info_id = #{item.customerInfoId,jdbcType=BIGINT} then #{item.orgId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="cloud_hospital_id = case" suffix="end,">
                <foreach collection="hospitalList" index="index" item="item">
                    when hospital_info_id = #{item.customerInfoId,jdbcType=BIGINT} then
                    #{item.hospitalId,jdbcType=BIGINT}
                </foreach>
            </trim>
            update_time = current_timestamp,
            hospital_open_status = #{hospitalOpenStatus,jdbcType=INTEGER},
        </trim>
        where custom_info_id = #{customInfoId, jdbcType=BIGINT}
    </update>

    <select id="getHospitalInfoByProjectId" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo"
            parameterType="com.msun.csm.model.dto.SelectHospitalDTO">
        select distinct phi.*
        from csm.proj_hospital_info phi
        inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
        and phvpt.is_deleted = 0
        inner join csm.proj_project_info ppi on ppi.custom_info_id = phi.custom_info_id and ppi.project_type =
        phvpt.project_type
        and ppi.is_deleted = 0
        where phi.is_deleted = 0
        <if test="projectInfoId != null and projectInfoId != ''">
            and ppi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        </if>
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            and phi.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
        </if>
        <if test="customInfoId != null and customInfoId != ''">
            and phi.custom_info_id = #{customInfoId,jdbcType=BIGINT}
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            and phi.hospital_name like concat('%', #{hospitalName}, '%')
        </if>
        <if test="sectionName != null and sectionName != ''">
            and phi.region like concat('%', #{sectionName}, '%')
        </if>
        <if test="sectionName != null and sectionName != ''">
            and phi.region like concat('%', #{sectionName}, '%')
        </if>
        <if test="healthBureauFlag != null ">
            and phi.health_bureau_flag = #{healthBureauFlag,jdbcType=BIGINT}
        </if>
        <if test="hospitalOpenStatus != null ">
            and phi.hospital_open_status = #{hospitalOpenStatus,jdbcType=INTEGER}
        </if>
        order by phi.health_bureau_flag desc , phi.hospital_name asc
    </select>

    <select id="findHospitalInfoByProjIdAndCustomerId" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select t.hospital_info_id,
               t.hospital_name,
               t.region
        from csm.proj_hospital_info t
                 inner join csm.proj_hospital_vs_project_type t1
                            on t.hospital_info_id = t1.hospital_info_id and
                               t1.custom_info_id = #{customInfoId,jdbcType=BIGINT}
                 inner join csm.proj_project_info t2
                            on t1.project_type = t2.project_type and t2.project_info_id = #{projectId,jdbcType=BIGINT}
        order by t.health_bureau_flag desc, t.hospital_name asc
    </select>

    <select id="getHospitalAndLeaderInfoByProjectId" resultType="com.msun.csm.model.dto.HospitalInfoDTO">
        with hospitals as (select phi.region,
                                  phi.hospital_info_id,
                                  phi.hospital_name,
                                  phi.custom_info_id,
                                  phi.health_bureau_flag
                           from csm.proj_hospital_info phi
                                    left join csm.proj_hospital_vs_project_type phvpt on
                               phi.hospital_info_id = phvpt.hospital_info_id
                                   and phvpt.is_deleted = 0
                                    left join csm.proj_project_info ppi on
                               ppi.project_type = phvpt.project_type
                                   and ppi.custom_info_id = phi.custom_info_id
                                   and ppi.is_deleted = 0
                           where phi.is_deleted = 0
                             and ppi.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
                           order by phi.health_bureau_flag desc,
                                    phi.hospital_name asc)
        select distinct hos.region,
                        hos.hospital_info_id,
                        hos.hospital_name,
                        hos.custom_info_id,
                        prp.leader_id,
                        prp.second_leader_id,
                        hos.health_bureau_flag
        from hospitals hos

                 left join csm.proj_milestone_task prp on
            hos.hospital_info_id = prp.hospital_info_id
                and prp.result_source_id = 40004
                and prp.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
        order by hos.health_bureau_flag desc,
                 hos.hospital_name asc
    </select>


    <select id="selectHospitalOnlineDetail" resultType="com.msun.csm.dao.entity.proj.HospitalOnlineInfo">
        select t.hospital_info_id,
               t.hospital_name,
               t.org_id,
               t.cloud_hospital_id,
               t.cloud_domain,
               t.hospital_open_status,
               phod.online_status,
               phod.online_time
        from csm.proj_hospital_info t
                 inner join csm.proj_hospital_vs_project_type t1
                            on t.hospital_info_id = t1.hospital_info_id and
                               t1.custom_info_id = #{customInfoId,jdbcType=BIGINT}
                 inner join csm.proj_project_info t2
                            on t1.project_type = t2.project_type and t2.project_info_id = #{projectId,jdbcType=BIGINT}
                 left join csm.proj_hospital_online_detail phod on t.hospital_info_id = phod.hospital_info_id
            and phod.custom_info_id = t1.custom_info_id
            and phod.project_info_id = t2.project_info_id
            and phod.is_deleted = 0
        where t.is_deleted = 0
        order by t.health_bureau_flag desc, t.hospital_name asc
    </select>
    <select id="getHospitalInfoRelativeByHospitalDTO" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative"
            parameterType="com.msun.csm.model.dto.SelectHospitalDTO">
        select * from (
        select phi.*, w.dict_town_name as town_name,
        pc.dict_province_name||'/'||city.dict_city_name||'/'||w.dict_town_name as administrative_divisions,
        pc.dict_province_id||'/'||city.dict_city_id||'/'||w.dict_town_id||'/' as administrative_code,
        phvpt.project_type,
        ppi.project_name,
        phi.env_name,
        ppi.order_info_id,
        cus.custom_name
        from csm.proj_hospital_info phi
        left join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
        and phvpt.is_deleted = 0
        left join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type
        and ppi.custom_info_id = phi.custom_info_id
        and ppi.is_deleted = 0
        left join csm.dict_province pc on phi.province_id = pc.dict_province_id and pc.invalid_flag = '0'
        left join csm.dict_city city on phi.city_id = city.dict_city_id and city.invalid_flag = '0'
        left join csm.dict_town w on phi.town_id = w.dict_town_id and w.invalid_flag = '0'
        left join csm.proj_custom_info cus on cus.custom_info_id = phi.custom_info_id and cus.is_deleted = 0
        where phi.is_deleted = 0
        <if test="projectInfoId != null and projectInfoId != ''">
            and ppi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        </if>
        <if test="customInfoId != null and customInfoId != ''">
            and ppi.custom_info_id = #{customInfoId,jdbcType=BIGINT}
        </if>
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            and phi.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
        </if>
        <if test="hospitalName != null and hospitalName != ''">
            and phi.hospital_name like concat('%', #{hospitalName}, '%')
        </if>
        <if test="sectionName != null and sectionName != ''">
            and phi.region like concat('%', #{sectionName}, '%')
        </if>
        <if test="projectType != null and projectType != ''">
            and ppi.project_type = #{projectType,jdbcType=INTEGER}
        </if>
        <if test="sectionName != null and sectionName != ''">
            and phi.region like concat('%', #{sectionName}, '%')
        </if>) t where t.administrative_divisions is not null order by health_bureau_flag desc
    </select>
    <select id="findHospitalByCloudHospitalId" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative">
        select t.*, t2.custom_name
        from csm.proj_hospital_info t
                 inner join csm.proj_custom_info t2
                            on t.cloud_hospital_id = #{cloudHospitalId,jdbcType=BIGINT}
                                and t.is_deleted = 0
                                and t2.is_deleted = 0
                                and t.custom_info_id = t2.custom_info_id
    </select>
    <select id="findByCustomInfoId" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select *
        from csm.proj_hospital_info
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
          and is_deleted = 0
    </select>
    <select id="selectListByParamer" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select *
        from csm.proj_hospital_info
        where is_deleted = 0
        and hospital_info_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.hospitalInfoId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectOnlineData" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select hi.*
        from csm.proj_hospital_info hi
        where hi.is_deleted = 0
          and hi.cloud_domain is not null
          and hi.cloud_domain != ''
          and hi.cloud_hospital_id is not null
        order by hi.custom_info_id, hi.health_bureau_flag
    </select>
    <select id="selectHospitalInfoListByParamer" resultType="com.msun.csm.common.model.BaseHospitalNameResp">
        select phi.hospital_info_id as id,
               phi.hospital_name as name,
               case when ppi.project_type = 1 then '单体'
                    when ppi.project_type = 2 then '区域'
               end as "projectType"
        from csm.proj_hospital_info phi
        left join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
            and phvpt.is_deleted = 0
        left join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type
            and ppi.custom_info_id = phi.custom_info_id
            and ppi.is_deleted = 0
        where phi.is_deleted = 0
        <if test="isOnlyDomain != null and isOnlyDomain ">
            and phi.cloud_domain is not null
            and phi.cloud_domain != ''
        </if>
        <if test="customInfoId != null">
            and phi.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null">
            and ppi.project_info_id = #{projectInfoId}
        </if>
        group by  phi.hospital_info_id, phi.hospital_name,ppi.project_type
        order by phi.custom_info_id
    </select>
    <select id="selectListByEntity" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select *
        from csm.proj_hospital_info
        where is_deleted = 0
        and custom_info_id = #{customInfoId,jdbcType=BIGINT}
        and hos_project_type = #{projectType,jdbcType=INTEGER}
        and hospital_open_status = 0
        and
            (cloud_hospital_id is null
               or cloud_domain is null
               or cloud_domain = ''
               or org_id is null
            )

    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_hospital_info
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
    </update>

    <select id="getHospitalInfoById" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select *
        from csm.proj_hospital_info
        where is_deleted = 0
          and hospital_info_id = #{hospitalInfoId}
    </select>

    <select id="findHospitalInfoByProjectId" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo"
            parameterType="com.msun.csm.model.dto.SelectHospitalDTO">
        select distinct phi.*
        from csm.proj_hospital_info phi
        inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
        and phvpt.is_deleted = 0
        inner join csm.proj_project_info ppi on ppi.custom_info_id = phi.custom_info_id and ppi.project_type =
        phvpt.project_type and ppi.is_deleted = 0
        where phi.hospital_open_status > 20
        <if test="projectInfoId != null and projectInfoId != ''">
            and ppi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        </if>
        and phi.is_deleted = 0
        order by phi.health_bureau_flag desc , phi.hospital_name asc
    </select>
</mapper>
