<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper">
    <update id="updateFormStatusData">
        update csm.proj_survey_form
        set finish_status      = #{finishStatus},
            examine_opinion    = #{examineOpinion},
            commit_finish_time = #{commitFinishTime}
        where project_info_id = #{projectInfoId}
          and finish_status not in (1, 3)
        <if test="yyProductId != null">
            and yy_product_id = #{yyProductId}
            and finish_imgs is not null
            and finish_imgs != ''
        </if>
        <if test="hospitalInfoId != null">
            and hospital_info_id = #{hospitalInfoId}
        </if>
    </update>
    <sql id="Base_Column_List">
        psr.survey_form_id            ,
        psr.customer_info_id          ,
        psr.project_info_id           ,
        psr.hospital_info_id          ,
        psr.yy_product_id             ,
        psr.yy_module_id              ,
        psr.form_name                 ,
        psr.online_essential          ,
        psr.finish_status             ,
        psr.survey_imgs               ,
        psr.supplement_imgs           ,
        psr.finish_imgs               ,
        psr.type_code                 ,
        psr.survey_user_id            ,
        psr.survey_finish_time        ,
        psr.make_user_id              ,
        psr.commit_finish_time        ,
        psr.make_finish_time          ,
        psr.form_source               ,
        psr.form_page_url             ,
        psr.cloud_product_code        ,
        psr.examine_opinion           ,
        psr.remark                    ,
        psr.is_deleted                ,
        psr.creater_id                ,
        psr.create_time               ,
        psr.updater_id                ,
        psr.update_time               ,
        psr.reviewer_user_id          ,
        psr.report_task_id            ,
        psr.recommend_template_ids    ,
        psr.identifier_user_id        ,
        psr.formlib_standard_id::varchar    as   formlib_standard_id ,
        psr.formlib_standard_type_id::varchar  as formlib_standard_type_id,
        psr.form_paper_size           ,
        psr.form_survey_method        ,
        psr.formlib_resource_unite_id::varchar  formlib_resource_unite_id,
        psr.import_cloud_status       ,
        psr.import_msg                ,
    </sql>
    <select id="selectSurveyFormByPage" resultType="com.msun.csm.model.resp.projform.ProjSurveyFormResp">
        select * from (
        select distinct dp.order_no,
                        coalesce(dp.product_name, dpvm.yy_module_name) as "productName",
                        case when coalesce(dp.product_name, dpvm.yy_module_name) like '%护理%' then true else false end as "isShowRecommendBtn",
                        <include refid="Base_Column_List" />
                        ci.custom_name  AS "customName",
                        pi.project_name AS "projectName",
                        phi.hospital_name AS "hospitalName",
                        su.user_name                                   as "makeUserName",
                        sure.user_name                                   as "reviewerUserName",
                        psr.identifier_user_id as "identifierUserId",
                        identifier.user_name                                   as "identifierUserName",
                        susurvey.user_name          as "surveyUserName",
                        psreview.examine_opinion as "operationExamineOpinion",
                         case when ss.open_flag = 1 and psreview.examine_status = 0 then true else false end as "isNeedAuditorFlag",
                        case when psreview.examine_status = 1 then '通过'
                                    when psreview.examine_status = 2 then '驳回'
                                    else '待审核' end
                            as "operationExamineStatusStr",
                         psreview.examine_status as "operationExamineStatus",
                        case
                            when psr.form_source = 'cpdy' then '产品调研'
                            when psr.form_source = 'lxt' then '老系统'
                            when psr.form_source = 'xz' then '新增'
                            else '其他' end                            as "formSourceStr",
                        case
                        when psr.finish_Status = -1 then '待提交'
                        when psr.finish_Status = 0 then '未开始'
                        when psr.finish_Status = 1 then '制作完成'
                        when psr.finish_Status = 2 then '制作完成已驳回'
                        when psr.finish_Status = 4 then '提交调研审核'
                        when psr.finish_Status = 5 then '调研审核通过'
                        when psr.finish_Status = 6 then '调研审核驳回'
                        when psr.finish_Status = 8 then '制作完成验证通过'
                        when psr.finish_Status = 11 then '不使用'
                        else '其他' end                            as "finishStatusStr",
                        dfs.formlib_standard_name,
                        dfsst.formlib_standard_type_name,
                        fru.form_picture_paths
        from csm.proj_survey_form psr
            left join csm.dict_formlib_standard dfs on psr.formlib_standard_id = dfs.formlib_standard_id
            left join csm.dict_formlib_standard_type dfsst on psr.formlib_standard_type_id = dfsst.formlib_standard_type_id
            left join (
                select
                    formlib_resource_unite_id,
                    form_picture_paths
                from
                    csm.formlib_resource_unite
             ) fru on psr.formlib_resource_unite_id = fru.formlib_resource_unite_id
            left join (
                select
                 x.*
                from
                csm.proj_business_examine_log x
                inner join (
                select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
                where is_deleted = 0 and business_type = 'form'
                    group by business_id
                ) y on x.business_id = y.business_id and x.create_time = y.create_time
                where x.is_deleted = 0  and x.business_type = 'form'
            ) psreview on psr.survey_form_id = psreview.business_id
            left join csm.proj_hospital_info phi on psr.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
                 left join csm.dict_product dp on psr.yy_product_id = dp.yy_product_id
                 left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = psr.yy_product_id
                 left join csm.sys_user su on psr.make_user_id = su.sys_user_id and su.is_deleted = 0
                left join csm.sys_user susurvey on psr.survey_user_id = susurvey.sys_user_id and susurvey.is_deleted = 0
                left join csm.sys_user sure on psr.reviewer_user_id = sure.sys_user_id and sure.is_deleted = 0
                left join csm.sys_user identifier on psr.identifier_user_id = identifier.sys_user_id and identifier.is_deleted = 0
                left join csm.proj_custom_info ci on psr.customer_info_id = ci.custom_info_id and ci.is_deleted = 0
                left join csm.proj_project_info pi on psr.project_info_id = pi.project_info_id and pi.is_deleted = 0
         left join  csm.config_custom_backend_limit ccbl on psr.project_info_id = ccbl.project_info_id and ccbl.is_deleted = 0
            left join (
                    select ccbdl.open_flag ,
                    ccbl.project_info_id
                    from
                    csm.config_custom_backend_limit ccbl
                    inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
                    where
                    ccbl.is_deleted =0
                    and ccbdl.is_deleted = 0
                    and ccbdl.open_flag = 1
                    and ccbdl.open_type = 12

                ) ss on psr.project_info_id = ss.project_info_id

        where psr.is_deleted = 0

        <if test="pageSource != null and pageSource == 'backend_form_design' ">
            and ccbl.open_flag = 1
        </if>


        <if test="allocateUserId != null and allocateUserId.size() != 0 ">
            and psr.make_user_id in
            <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size() != 0 ">
            and pi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reviewerUserIds != null and reviewerUserIds.size() != 0 ">
            and psr.reviewer_user_id in
            <foreach collection="reviewerUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="identifierUserIds != null and identifierUserIds.size() != 0 ">
            and psr.identifier_user_id in
            <foreach collection="identifierUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="finishStatusList != null and finishStatusList.size > 0">
            and  psr.finish_status in
            <foreach collection="finishStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="surveyFormId != null">
            and psr.survey_form_id = #{surveyFormId}
        </if>
        <if test="projectInfoId != null">
            and psr.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and psr.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null">
            and (psr.yy_product_id = #{yyProductId} or psr.yy_module_id = #{yyProductId})
        </if>
        <if test="yyModuleId != null">
            and psr.yy_module_id = #{yyModuleId}
        </if>
        <if test="finishStatus != null">
            and case when ss.open_flag = 1 then psr.finish_status = #{finishStatus}
            when #{finishStatus} = 0 then psr.finish_status in (0,4,5,6)
            else  psr.finish_status = #{finishStatus} end
        </if>
        <if test="onlineEssential != null">
            and psr.online_essential = #{onlineEssential}
        </if>
        <if test="customInfoId != null">
            and psr.customer_info_id = #{customInfoId}
        </if>
        <if test="formName != null and formName != ''">
            and psr.form_name like concat('%', #{formName}, '%')
        </if>
        <if test="operationExamineStatusNumber != null">
            and psreview.examine_status = #{operationExamineStatusNumber}
        </if>
        ) ased
        order by customer_info_id, project_info_id, ased.order_no, ased.update_time desc, ased.form_name
    </select>
    <select id="selectSurveyFormCount" resultType="com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount">
        select count(*)                                                                              "totalCount",
               sum(case when online_essential = 1 then 1 else 0 end)                                 "preLaunchCompletionCount",
               sum(case when online_essential = 1 and finish_status not in (1, 8) then 1 else 0 end) "incompleteCount",
               sum(case when online_essential = 1 and finish_status = 2 then 1 else 0 end)           "rejectedCount"
        from (
        select distinct dp.order_no,
                        coalesce(dp.product_name, dpvm.yy_module_name) as "productName",
                        psr.*,
                        su.user_name                                   as "makeUserName",
                        case
                            when psr.form_source = 'cpdy' then '产品调研'
                            when psr.form_source = 'lxt' then '老系统'
                            when psr.form_source = 'xz' then '新增'
                            else '其他' end                            as "formSourceStr"
        from csm.proj_survey_form psr
            left join csm.proj_project_info pi on pi.project_info_id=psr.project_info_id
        left join csm.sys_user sure on psr.reviewer_user_id = sure.sys_user_id and sure.is_deleted = 0
         left join csm.dict_product dp on psr.yy_product_id = dp.yy_product_id
         left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = psr.yy_product_id
         left join csm.sys_user su on psr.make_user_id = su.sys_user_id and su.is_deleted = 0
        left join  csm.config_custom_backend_limit ccbl on psr.project_info_id = ccbl.project_info_id and ccbl.is_deleted = 0
        left join (
            select
            x.*
            from
            csm.proj_business_examine_log x
            inner join (
            select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
            where is_deleted = 0 and business_type = 'form'
            group by business_id
            ) y on x.business_id = y.business_id and x.create_time = y.create_time
            where x.is_deleted = 0  and x.business_type = 'form'
        ) psreview on psr.survey_form_id = psreview.business_id

        left join (
            select ccbdl.open_flag ,
            ccbl.project_info_id
            from
            csm.config_custom_backend_limit ccbl
            inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
            where
            ccbl.is_deleted =0
            and ccbdl.is_deleted = 0
            and ccbdl.open_flag = 1
            and ccbdl.open_type = 12

        ) ss on psr.project_info_id = ss.project_info_id

        where psr.is_deleted = 0

        <if test="pageSource != null and pageSource == 'backend_form_design' ">
            and ccbl.open_flag = 1
        </if>


        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size() != 0 ">
            and pi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="finishStatusList != null and finishStatusList.size > 0">
            and  psr.finish_status in
            <foreach collection="finishStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="allocateUserId != null and allocateUserId.size() != 0 ">
            and psr.make_user_id in
            <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reviewerUserIds != null and reviewerUserIds.size() != 0 ">
            and psr.reviewer_user_id in
            <foreach collection="reviewerUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="identifierUserIds != null and identifierUserIds.size() != 0 ">
            and psr.identifier_user_id in
            <foreach collection="identifierUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="surveyFormId != null">
            and psr.survey_form_id = #{surveyFormId}
        </if>
        <if test="projectInfoId != null">
            and psr.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and psr.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null">
            and (psr.yy_product_id = #{yyProductId} or psr.yy_module_id = #{yyProductId})
        </if>
        <if test="yyModuleId != null">
            and psr.yy_module_id = #{yyModuleId}
        </if>
        <if test="finishStatus != null">
            and case when ss.open_flag = 1 then psr.finish_status = #{finishStatus}
            when #{finishStatus} = 0 then psr.finish_status in (0,4,5,6)
                else  psr.finish_status = #{finishStatus} end
        </if>
        <if test="onlineEssential != null">
            and psr.online_essential = #{onlineEssential}
        </if>
        <if test="customInfoId != null">
            and psr.customer_info_id = #{customInfoId}
        </if>
        <if test="formName != null and formName != ''">
            and psr.form_name like concat('%', #{formName}, '%')
        </if>
        <if test="operationExamineStatusNumber != null">
            and psreview.examine_status = #{operationExamineStatusNumber}
        </if>
        ) ased
    </select>

    <update id="updateByProjectId">
        update csm.proj_survey_form
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() != 0">
            and (yy_product_id in
            <foreach collection="changeProductList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            or yy_module_id in
            <foreach collection="changeProductList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_survey_form
        set customer_info_id = #{newCustomInfoId}
        where customer_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_survey_form
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and (yy_product_id in
        <foreach collection="oldProductIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        or yy_module_id in
        <foreach collection="oldProductIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </update>
    <update id="udpateReprotByOldId">
        update csm.proj_survey_form
        set survey_user_id = #{oldId}
        where survey_user_id = #{newOldId}
    </update>
    <update id="udpateReprotMakeByOldId">
        update csm.proj_survey_form
        set make_user_id = #{oldId}
        where make_user_id = #{newOldId}
    </update>
    <select id="selectSurveyForm" resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">
        select
        *
        from
        csm.proj_survey_form
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        and hospital_info_id = #{hospitalInfoId}
        and (
        yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        or yy_module_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="selectInitFormData" resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">
        select distinct
        dfst.yy_product_id,
            -1 yy_module_id,
        dfst.formlib_standard_type_name form_name,
        dfst.online_essential,
            0 finish_status,
            'qt' form_source,
            'bzdyx' form_survey_method,
            '' form_page_url,
            '' cloud_product_code,
            '' remark,
        dfst.formlib_standard_type_id as "formlibStandardTypeId",
        dfst.formlib_standard_id as "formlibStandardId",
            '' form_paper_size,
            psp.survey_user_id,
        -1 "finishStatus"
        from
            csm.dict_formlib_standard_type dfst
        left join (
            select yy_product_id,
                   min(survey_user_id) as survey_user_id
            from csm.proj_survey_plan where project_info_id = #{projectInfoId}
            group by yy_product_id
        ) psp on psp.yy_product_id = dfst.yy_product_id
        left join (
            select  yy_product_id,
                formlib_standard_id ,
                formlib_standard_type_id
        from csm.proj_survey_form
            where project_info_id = #{projectInfoId}
            and is_deleted = 0
            group by yy_product_id,
            formlib_standard_id ,
            formlib_standard_type_id
        ) psf on psf.yy_product_id = dfst.yy_product_id
            and psf.formlib_standard_id = dfst.formlib_standard_id
            and psf.formlib_standard_type_id = dfst.formlib_standard_type_id
        where
            psf.formlib_standard_id is null
          and
        dfst.yy_product_id in
            <foreach collection="productIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and dfst.is_deleted = 0
            and dfst.formlib_standard_id in (
                WITH RECURSIVE cte AS (
                SELECT
                formlib_standard_id id,
                parent_id
                FROM csm.dict_formlib_standard
                WHERE is_standard = 1

                UNION ALL

                SELECT
                t.formlib_standard_id as id ,
                t.parent_id
                FROM csm.dict_formlib_standard t
                INNER JOIN cte c ON t.parent_id = c.id
                where
                t.is_deleted = 0
                )
                SELECT distinct id FROM cte
            )
    </select>
    <select id="findAllProjectProductList" resultType="com.msun.csm.dao.entity.proj.ProductInfo"
            parameterType="com.msun.csm.model.dto.ProductInfoDTO">

        SELECT re.product_arrange_id as yy_order_product_id,
               dp.product_name
        FROM csm.proj_product_arrange_record re
                 left join csm.dict_product dp
                           on dp.yy_product_id = re.product_arrange_id
        where re.is_deleted = 0
          and dp.is_deleted = 0
        <if test="projectInfoId != null and projectInfoId != -1">
            and re.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        </if>

    </select>
    <select id="selectListByStandardTypeInfo" resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm"
            parameterType="com.msun.csm.model.req.projform.ProjSurveyFormAddReq">
            select* from csm.proj_survey_form
            where is_deleted = 0
                and formlib_standard_id = #{formlibStandardId}
                and formlib_standard_type_id = #{formlibStandardTypeId}
                and project_info_id = #{projectInfoId}
                and yy_product_id = #{yyProductId}
                and finish_status = -1

    </select>

    <select id="getSurveyFormById" resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">
        select *
        from csm.proj_survey_form
        where is_deleted = 0
          and survey_form_id = #{surveyFormId}
    </select>
    <select id="getPrintStatusByProjectInfoId" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select
            case when
                     exists(
                         select 1 from csm.proj_survey_form psr
                                           inner join (
                             select ppi.project_info_id from
                                 csm.proj_project_info ppi
                                     inner join csm.proj_project_info ppi2 on ppi.custom_info_id = ppi2.custom_info_id and ppi.project_type = ppi2.project_type
                             where ppi2.project_info_id = #{projectInfoId}
                         ) ppiz on ppiz.project_info_id = psr.project_info_id
                         where psr.is_deleted = 0 and psr.import_cloud_status > -1
                     )
                         or
                     exists(
                         select 1 from csm.proj_hospital_info phi
                                           inner join (
                             select ppi.custom_info_id,ppi.project_type from
                                 csm.proj_project_info ppi
                                     inner join csm.proj_project_info ppi2 on ppi.custom_info_id = ppi2.custom_info_id and ppi.project_type = ppi2.project_type
                             where ppi2.project_info_id = #{projectInfoId}
                         ) ppis on ppis.custom_info_id = phi.custom_info_id and ppis.project_type = phi.hos_project_type
                         where phi.is_deleted = 0 and phi.hospital_open_status >= 21
                     )
                     then true
                 else false
                end as final_status
    </select>

    <update id="updateRecommendTemplateIds">
        update csm.proj_survey_form
        set recommend_template_ids = #{recommendTemplateIds}
        where is_deleted = 0
          and survey_form_id = #{surveyFormId}
    </update>

    <update id="updateReportTaskId">
        update csm.proj_survey_form
        set report_task_id = #{reportTaskId}
        where is_deleted = 0
          and survey_form_id = #{surveyFormId}
    </update>
    <update id="updateDataByParamer" parameterType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">
        update csm.proj_survey_form
        set import_cloud_status = #{importCloudStatus},
            import_msg = #{importMsg}
        where is_deleted = 0
          and formlib_resource_unite_id = #{formlibResourceUniteId}
    </update>
    <update id="updateFormByProjectId">
        update csm.proj_survey_form
        set import_cloud_status = 1,
            import_msg = '部署时依旧未引用资源库数据记录'
        where is_deleted = 0
          and formlib_resource_unite_id is null
          and project_info_id = #{projectInfoId}
    </update>
</mapper>
