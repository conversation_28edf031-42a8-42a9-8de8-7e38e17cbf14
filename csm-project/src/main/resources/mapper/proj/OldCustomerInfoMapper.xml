<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.oldimsp.OldCustomerInfoMapper">
    <update id="updateBatchByHospitalInfoList">
        update comm.customer_info
        <set>
            <if test="info.sendStatus != null">
                send_status = #{info.sendStatus,jdbcType=INTEGER},
            </if>
            <if test="info.productNetwork != null">
                product_network = #{info.productNetwork,jdbcType=VARCHAR},
            </if>
            <if test="info.preProductNetwork != null">
                pre_product_network = #{info.preProductNetwork,jdbcType=VARCHAR},
            </if>
            <if test="info.host != null">
                host = #{info.host,jdbcType=VARCHAR},
            </if>
            <if test="info.preHost != null">
                pre_host = #{info.preHost,jdbcType=VARCHAR},
            </if>
            <if test="info.envId != null">
                env_id = #{info.envId,jdbcType=VARCHAR},
            </if>
            <if test="info.envName != null">
                env_name = #{info.envName,jdbcType=VARCHAR},
            </if>
            <if test="info.orgId != null">
                org_id = #{info.orgId,jdbcType=BIGINT},
            </if>
            <if test="info.hospitalId != null">
                hospital_id = #{info.hospitalId,jdbcType=BIGINT},
            </if>
        </set>
        where csm_hospital_info_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateCustomerInfoByCustomInfoId">
        update comm.customer_info
        <set>
            <if test="envId != null">
                env_id = #{envId,jdbcType=BIGINT},
            </if>
            <if test="envName != null">
                env_name = #{envName,jdbcType=VARCHAR},
            </if>
        </set>
        where customer_id = #{customerId,jdbcType=BIGINT}
    </update>
    <update id="updateBatchByHospitalInfoListByCsmHospitalId">
        update comm.customer_info
        <set>
            <if test="info.sendStatus != null">
                send_status = #{info.sendStatus,jdbcType=BIGINT},
            </if>
            <if test="info.productNetwork != null">
                product_network = #{info.productNetwork,jdbcType=VARCHAR},
            </if>
            <if test="info.preProductNetwork != null">
                pre_product_network = #{info.preProductNetwork,jdbcType=VARCHAR},
            </if>
            <if test="info.host != null">
                host = #{info.host,jdbcType=VARCHAR},
            </if>
            <if test="info.preHost != null">
                pre_host = #{info.preHost,jdbcType=VARCHAR},
            </if>
            <if test="info.envId != null">
                env_id = #{info.envId,jdbcType=BIGINT},
            </if>
            <if test="info.envName != null and info.envName != ''">
                env_name = #{info.envName,jdbcType=VARCHAR},
            </if>
            <if test="info.envStatus != null and info.envStatus != ''">
                env_status = #{info.envStatus,jdbcType=VARCHAR},
            </if>
            <if test="info.hospitalId != null">
                hospital_id = #{info.hospitalId,jdbcType=BIGINT},
            </if>
            <if test="info.orgId != null">
                org_id = #{info.orgId,jdbcType=BIGINT},
            </if>
        </set>
        where csm_hospital_info_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectListByParamer" resultType="com.msun.csm.dao.entity.oldimsp.OldCustomerInfo"
            parameterType="com.msun.csm.dao.entity.oldimsp.OldCustomerInfo">
        select
        ci.id                        ,
        coalesce(ci.hospital_id, phi.cloud_hospital_id)         hospital_id      ,
        ci.create_id                 ,
        ci.create_time               ,
        ci.update_id                 ,
        ci.update_time               ,
        coalesce(ci.org_id, phi.org_id) org_id                    ,
        ci.customer_name             ,
        coalesce(ci.product_network, phi.cloud_domain) product_network           ,
        coalesce(ci.pre_product_network, phi.cloud_domain) pre_product_network       ,
        ci.branch_courts_pattern     ,
        ci.customer_id               ,
        ci.hospital_code             ,
        phi.cloud_domain,
        coalesce(ci.host, 	  (CASE
        WHEN POSITION(':' IN  replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '') ) > 0 THEN
        SUBSTRING(replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '') FROM 1 FOR POSITION(':' IN replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '')) - 1)
        ELSE
        replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '')
        END))  host                      ,
        ci.send_status               ,
        ci.send_time                 ,
        coalesce(ci.pre_host, 	    (CASE
        WHEN POSITION(':' IN  replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '') ) > 0 THEN
        SUBSTRING(replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '') FROM 1 FOR POSITION(':' IN replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '')) - 1)
        ELSE
        replace(REPLACE(REPLACE(phi.cloud_domain, 'http', ''), 'https', ''), '://', '')
        END))   pre_host                  ,
        ci.online_status             ,
        ci.province_id               ,
        ci.city_id                   ,
        ci.town_id                   ,
        ci.hospital_type             ,
        ci.terminal_amount           ,
        ci.hospital_amount           ,
        ci.remark                    ,
        ci.bed_count                 ,
        ci.annual_income             ,
        ci.outpatient_count          ,
        ci.hospital_level            ,
        ci.village_doctor_amount     ,
        ci.env_id                    ,
        ci.env_name                  ,
        ci.env_status                ,
        ci.people_count              ,
        ci.clinic_room_count         ,
        ci.main_hospital_flag        ,
        ci.parent_id                 ,
        ci.org_type_id               ,
        ci.hospital_status           ,
        ci.proxy_net                 ,
        ci.project_id                ,
        ci.front_addr                ,
        ci.csm_hospital_info_id
        from   comm.customer_info ci
        left join csm.proj_hospital_info phi on ci.csm_hospital_info_id = phi.hospital_info_id
        where 1=1

        <if test="customerName != null and customerName != ''">
            and ci.customer_name = #{customerName}
        </if>
        <if test="projectId != null and projectId != ''">
            and ci.project_id = #{projectId}
        </if>

    </select>

    <delete id="deleteOldHospital">
        delete from comm.customer_info where csm_hospital_info_id = #{hospitalInfoId}
    </delete>
</mapper>
