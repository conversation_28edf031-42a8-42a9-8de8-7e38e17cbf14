<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProductSatisfactionSurveyUserMapper">

    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUser">
            <id property="productSatisfactionSurveyUserId" column="product_satisfaction_survey_user_id" jdbcType="BIGINT"/>
        <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
        <result property="yyProductId" column="yy_product_id" jdbcType="BIGINT"/>
        <result property="hospitalInfoId" column="hospital_info_id" jdbcType="BIGINT"/>
        <result property="cloudHospitalDeptId" column="cloud_hospital_dept_id" jdbcType="VARCHAR"/>
        <result property="cloudHospitalDeptName" column="cloud_hospital_dept_name" jdbcType="VARCHAR"/>
        <result property="cloudHospitalUserId" column="cloud_hospital_user_id" jdbcType="BIGINT"/>
        <result property="cloudHospitalUserName" column="cloud_hospital_user_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
        <result property="cloudHospitalUserAccount" column="cloud_hospital_user_account" jdbcType="VARCHAR"/>
        <result property="cloudIdentityId" column="cloud_identity_id" jdbcType="BIGINT"/>
        <result property="surveyUrl" column="survey_url" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getCountByProjectAndProductAndStatus" resultType="int">
        select count(1)
        from csm.product_satisfaction_survey_user
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and yy_product_id = #{yyProductId}
          and status = #{status}
    </select>

    <insert id="saveProductSatisfactionSurveyUser">
        insert into csm.product_satisfaction_survey_user (product_satisfaction_survey_user_id,
                                                          creater_id,
                                                          create_time,
                                                          updater_id,
                                                          update_time,
                                                          is_deleted,
                                                          yy_product_id,
                                                          hospital_info_id,
                                                          cloud_hospital_dept_id,
                                                          cloud_hospital_dept_name,
                                                          cloud_hospital_user_id,
                                                          cloud_hospital_user_name,
                                                          status,
                                                          project_info_id,
                                                          cloud_hospital_user_account,
                                                          cloud_identity_id)
        values (#{productSatisfactionSurveyUserId},
                #{createrId},
                #{createTime},
                #{updaterId},
                #{updateTime},
                #{isDeleted},
                #{yyProductId},
                #{hospitalInfoId},
                #{cloudHospitalDeptId},
                #{cloudHospitalDeptName},
                #{cloudHospitalUserId},
                #{cloudHospitalUserName},
                #{status},
                #{projectInfoId},
                #{cloudHospitalUserAccount},
                #{cloudIdentityId}
               )
    </insert>

    <select id="getProductSatisfactionSurveyUser" resultMap="BaseResultMap">
        select *
        from csm.product_satisfaction_survey_user
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        <if test="yyProductId != null">
            and yy_product_id = #{yyProductId}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="abandonUserById">
        update csm.product_satisfaction_survey_user
        set is_deleted  = 1,
            update_time = now()
        where product_satisfaction_survey_user_id = #{id}
    </update>
    
    <select id="getProductSatisfactionUser" resultType="com.msun.csm.dao.entity.proj.ProductSatisfactionSurveyUserVO">
        select psss.product_satisfaction_survey_user_id,
               psss.yy_product_id,
               case
                   when dp.product_name is null then concat(product.product_name, '-', dpvm.yy_module_name)
                   else dp.product_name
                   end                                         as "yyProductName",
               psss.hospital_info_id,
               phi.hospital_name,
               psss.cloud_hospital_dept_id,
               psss.cloud_hospital_dept_name,
               psss.cloud_hospital_user_id,
               psss.cloud_hospital_user_account,
               psss.cloud_hospital_user_name,
               to_char(psss.create_time, 'YYYY-MM-DD HH24:MI') as "createTime",
               creater.user_name                               as "createrUserName",
               psss.status,
               psss.project_info_id,
               psss.survey_url
        from csm.product_satisfaction_survey_user psss
                 left join csm.dict_product dp on psss.yy_product_id = dp.yy_product_id
                 left join csm.dict_product_vs_modules dpvm on psss.yy_product_id = dpvm.yy_module_id
                 left join csm.dict_product product on dpvm.yy_product_id = product.yy_product_id and product.is_deleted = 0
                 left join csm.proj_hospital_info phi on psss.hospital_info_id = phi.hospital_info_id
                 left join csm.sys_user creater on psss.creater_id = creater.sys_user_id
        where psss.is_deleted = 0
          and psss.project_info_id = #{projectInfoId}
    </select>

    <update id="updateStatusById">
        update csm.product_satisfaction_survey_user
        set status= #{status},
            update_time = now()
        where product_satisfaction_survey_user_id = #{id}
    </update>

    <update id="updateStatus">
        update csm.product_satisfaction_survey_user
        set status= #{status},
            update_time = now()
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and cloud_hospital_user_id = #{cloudUserId}
          and cloud_hospital_dept_name = #{cloudDeptName}
          and hospital_info_id = #{hospitalInfoId}
          and yy_product_id = #{yyProductId}
    </update>

    <update id="updateSurveyUrl">
        update csm.product_satisfaction_survey_user
        set survey_url = #{surveyUrl}
        where product_satisfaction_survey_user_id = #{id}
    </update>
</mapper>
