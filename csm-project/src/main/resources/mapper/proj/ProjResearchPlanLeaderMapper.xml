<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjResearchPlanLeaderMapper">
    <delete id="deleteByPlanIds"
            parameterType="java.util.List">
        delete from csm.proj_research_plan_leader
        <where>
            and plan_id in
            <foreach collection="list" item="id" separator="," open="(" close=")">
                #{id, jdbcType=BIGINT}
            </foreach>
        </where>
    </delete>
</mapper>
