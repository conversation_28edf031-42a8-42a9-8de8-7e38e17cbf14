<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        <!--@Table csm.proj_product_deliver_record-->
        <id column="product_deliver_record_id" jdbcType="BIGINT" property="productDeliverRecordId"/>
        <result column="product_deliver_id" jdbcType="BIGINT" property="productDeliverId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="yy_order_product_id" jdbcType="BIGINT" property="yyOrderProductId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="IdAndNameMap" type="com.msun.csm.common.model.BaseIdNameResp">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_deliver_record_id, product_deliver_id, custom_info_id, yy_order_product_id,
        project_info_id, is_deleted, creater_id, create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_deliver_record
        where product_deliver_record_id = #{productDeliverRecordId,jdbcType=BIGINT}
    </select>
    <select id="findByProjInfoIdAndCustomerInfoId"
            resultType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_deliver_record
        <where>
            and custom_info_id = #{customerInfoId, jdbcType=BIGINT}
            and project_info_id = #{projectInfoId, jdbcType=BIGINT}
        </where>
    </select>
    <select id="findSurveyFlagYyOrderProductIdByProjectInfoId" resultType="java.lang.Long" >
        select
        distinct product_deliver_id
        from csm.proj_product_deliver_record re
                 left join csm.dict_product_extend ex on re.product_deliver_id = ex.yy_product_id
                 left join csm.dict_product dp on re.product_deliver_id =dp.yy_product_id
        where re.project_info_id = #{projectInfoId, jdbcType=BIGINT}
         and (ex.survey_flag = 0 or dp.product_name like '%分院模式%' or dp.product_name like '%升级云健康%')
    </select>
    <select id="findByProjInfoId"
            resultType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        select
            re.*
        from csm.proj_product_deliver_record re
        inner join csm.dict_product_extend ex
                   on re.yy_order_product_id = ex.yy_product_id
        where ex.prepare_data_check_flag=1
            and ex.is_deleted=0
            and re.project_info_id = #{projectInfoId, jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_product_deliver_record
        where product_deliver_record_id = #{productDeliverRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_deliver_record (product_deliver_record_id, product_deliver_id,
        custom_info_id, yy_order_product_id, project_info_id,
        is_deleted, creater_id, create_time,
        updater_id, update_time)
        values (#{productDeliverRecordId,jdbcType=BIGINT}, #{productDeliverId,jdbcType=BIGINT},
        #{customInfoId,jdbcType=BIGINT}, #{yyOrderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_deliver_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productDeliverRecordId != null">
                product_deliver_record_id,
            </if>
            <if test="productDeliverId != null">
                product_deliver_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productDeliverRecordId != null">
                #{productDeliverRecordId,jdbcType=BIGINT},
            </if>
            <if test="productDeliverId != null">
                #{productDeliverId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        update csm.proj_product_deliver_record
        <set>
            <if test="productDeliverId != null">
                product_deliver_id = #{productDeliverId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where product_deliver_record_id = #{productDeliverRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        update csm.proj_product_deliver_record
        set product_deliver_id = #{productDeliverId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where product_deliver_record_id = #{productDeliverRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_product_deliver_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_deliver_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.productDeliverId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.yyOrderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where product_deliver_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productDeliverRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_product_deliver_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_deliver_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productDeliverId != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.productDeliverId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyOrderProductId != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.yyOrderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_deliver_record_id = #{item.productDeliverRecordId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_deliver_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productDeliverRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_product_deliver_record
        (product_deliver_record_id, product_deliver_id, custom_info_id, yy_order_product_id,
        project_info_id, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productDeliverRecordId,jdbcType=BIGINT}, #{item.productDeliverId,jdbcType=BIGINT},
            #{item.customInfoId,jdbcType=BIGINT}, #{item.yyOrderProductId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_deliver_record
        (product_deliver_record_id, product_deliver_id, custom_info_id, yy_order_product_id,
        project_info_id, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        (#{productDeliverRecordId,jdbcType=BIGINT}, #{productDeliverId,jdbcType=BIGINT},
        #{customInfoId,jdbcType=BIGINT}, #{yyOrderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        product_deliver_record_id = #{productDeliverRecordId,jdbcType=BIGINT},
        product_deliver_id = #{productDeliverId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_deliver_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productDeliverRecordId != null">
                product_deliver_record_id,
            </if>
            <if test="productDeliverId != null">
                product_deliver_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productDeliverRecordId != null">
                #{productDeliverRecordId,jdbcType=BIGINT},
            </if>
            <if test="productDeliverId != null">
                #{productDeliverId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productDeliverRecordId != null">
                product_deliver_record_id = #{productDeliverRecordId,jdbcType=BIGINT},
            </if>
            <if test="productDeliverId != null">
                product_deliver_id = #{productDeliverId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByProjectId">
        update csm.proj_product_deliver_record
        set project_info_id = #{newProjectId,jdbcType=BIGINT}
        where project_info_id = #{oldProjectId,jdbcType=BIGINT}
        <if test="splitYYPIdList != null and splitYYPIdList.size() != 0">
            and yy_order_product_id in
            <foreach close=")" collection="splitYYPIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectInfoId">
        update csm.proj_product_deliver_record
        set is_deleted=1
        where project_info_id = #{projectInfoId}
    </update>

    <select id="findByProjectInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_deliver_record
        where is_deleted = 0
          <if test="projectInfoId != null">
            and project_info_id = #{projectInfoId}
          </if>

    </select>

    <select id="findByProjectInfoIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_deliver_record
        where is_deleted = 0
          and project_info_id in
        <foreach close=")" collection="projectInfoIdList" item="item" open="(" separator=",">
            #{item}
        </foreach>
        order by project_info_id
    </select>

    <select id="queryDeliverProductIdAndNameByProjectInfoId" resultMap="IdAndNameMap">
        select distinct dp.yy_product_id as "id",
                        dp.product_name  as "name",
                        dp.order_no
        from csm.proj_product_deliver_record ppdr
                 inner join csm.dict_product dp on ppdr.yy_order_product_id = dp.yy_product_id
        where ppdr.is_deleted = 0
          and ppdr.project_info_id = #{projectInfoId}
        order by dp.order_no
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_product_deliver_record
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
    <select id="findSurveyProductByProjInfoId"
            resultType="com.msun.csm.dao.entity.proj.ProjProductDeliverRecord">
        select
            re.project_info_id ,re.product_deliver_id
        from csm.proj_product_deliver_record re
                 inner join csm.dict_product_extend ex
                            on re.product_deliver_id = ex.yy_product_id
        where ex.survey_flag=1
          and ex.is_deleted=0
          and re.project_info_id = #{projectInfoId, jdbcType=BIGINT}
        group by re.project_info_id ,re.product_deliver_id
    </select>

    <!-- 已梳理-->
    <select id="findProductDeliverRecordByProjectInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_deliver_record
        where is_deleted = 0
          <if test="projectInfoId != null">
            and project_info_id = #{projectInfoId}
          </if>
    </select>

    <select id="findNeedSurveyProductDeliverRecordByProjectInfoId" resultMap="BaseResultMap">
        select ppdr.*
        from csm.proj_product_deliver_record ppdr
                 left join csm.dict_product_vs_modules dpvm on ppdr.product_deliver_id = dpvm.yy_module_id
        where ppdr.is_deleted = 0
          and ppdr.project_info_id = #{projectInfoId}
          and (dpvm.need_survey is null or dpvm.need_survey = 1)
    </select>
</mapper>
