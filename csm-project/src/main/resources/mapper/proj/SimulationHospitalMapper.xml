<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.SimulationHospitalMapper">

    <select id="findSimulationHospital" resultType="com.msun.csm.dao.entity.proj.SimulationHospital">
        select distinct cloud_hospital_id,hospital_name,test_domain
        from csm.simulation_hospital
        where custom_info_id = #{customInfoId}
        and is_delete = 0
    </select>

    <select id="findNotOpenHospital" resultType="com.msun.csm.dao.entity.proj.SimulationHospital">
        select distinct sh.cloud_hospital_id,sh.hospital_name,sh.test_domain
        from csm.simulation_hospital sh
        left join csm.proj_hospital_info phi on sh.exam_hospital_info_id = phi.hospital_info_id
        where sh.custom_info_id = #{customInfoId}
        and sh.is_delete = 0
        and phi.hospital_open_status &lt; 21
    </select>
</mapper>