<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectSplitProcessMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    <!--@Table csm.proj_project_split_process-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="project_file_id" jdbcType="BIGINT" property="projectFileId" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", project_info_id, "status", reason, project_file_id, is_deleted, creater_id,
    create_time, updater_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.proj_project_split_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.proj_project_split_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process (id, "name", project_info_id,
      "status", reason, project_file_id,
      is_deleted, creater_id, create_time,
      updater_id, update_time)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{projectInfoId,jdbcType=BIGINT},
      #{status,jdbcType=SMALLINT}, #{reason,jdbcType=VARCHAR}, #{projectFileId,jdbcType=BIGINT},
      #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="projectFileId != null">
        project_file_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="projectFileId != null">
        #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    update csm.proj_project_split_process
    <set>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="projectFileId != null">
        project_file_id = #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    update csm.proj_project_split_process
    set "name" = #{name,jdbcType=VARCHAR},
      project_info_id = #{projectInfoId,jdbcType=BIGINT},
      "status" = #{status,jdbcType=SMALLINT},
      reason = #{reason,jdbcType=VARCHAR},
      project_file_id = #{projectFileId,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_project_split_process
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="&quot;name&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="&quot;status&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="project_file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.projectFileId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_project_split_process
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="&quot;name&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectInfoId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="&quot;status&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reason != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="project_file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectFileId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.projectFileId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process
    (id, "name", project_info_id, "status", reason, project_file_id, is_deleted, creater_id,
      create_time, updater_id, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR}, #{item.projectInfoId,jdbcType=BIGINT},
        #{item.status,jdbcType=SMALLINT}, #{item.reason,jdbcType=VARCHAR}, #{item.projectFileId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process
    (id, "name", project_info_id, "status", reason, project_file_id, is_deleted, creater_id,
      create_time, updater_id, update_time)
    values
    (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{projectInfoId,jdbcType=BIGINT}, #{status,jdbcType=SMALLINT},
      #{reason,jdbcType=VARCHAR}, #{projectFileId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP})
    on duplicate key update
    id = #{id,jdbcType=BIGINT},
    "name" = #{name,jdbcType=VARCHAR},
    project_info_id = #{projectInfoId,jdbcType=BIGINT},
    "status" = #{status,jdbcType=SMALLINT},
    reason = #{reason,jdbcType=VARCHAR},
    project_file_id = #{projectFileId,jdbcType=BIGINT},
    is_deleted = #{isDeleted,jdbcType=SMALLINT},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcess">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="projectFileId != null">
        project_file_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="projectFileId != null">
        #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="projectFileId != null">
        project_file_id = #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="selectLastByProjectId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
    from csm.proj_project_split_process
    where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    and is_deleted = 0
    order by create_time desc
    limit 1
  </select>
</mapper>
