<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictSatisfactionSurveyRoleMapper">

    <select id="getAllSatisfactionSurveyRole" resultType="com.msun.csm.dao.entity.dict.DictSatisfactionSurveyRole">
        select *
        from csm.dict_satisfaction_survey_role
        where is_delete = 0
    </select>

</mapper>
