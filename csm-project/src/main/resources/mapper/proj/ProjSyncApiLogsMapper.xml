<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSyncApiLogsMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjSyncApiLogs">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="actionname" column="actionname" jdbcType="VARCHAR"/>
        <result property="datas" column="datas" jdbcType="VARCHAR"/>
        <result property="customername" column="customername" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
        <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        actionname,
        datas,
        customername,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
    </sql>
</mapper>
