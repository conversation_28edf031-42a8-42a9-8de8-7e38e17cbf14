<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSimulationDetailMapper">
    <insert id="insertBatch">
        insert into csm.proj_simulation_detail
        (proj_simulation_detail_id, dict_simulation_business_id,proj_simulation_result_id,
         custom_info_id, project_info_id, hospital_info_id, pass_flag, start_time, end_time,
         is_deleted, creater_id, updater_id,create_time,update_time)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.projSimulationDetailId}, #{item.dictSimulationBusinessId}, #{item.projSimulationResultId},
             #{item.customInfoId}, #{item.projectInfoId}, #{item.hospitalInfoId}, #{item.passFlag},
             #{item.startTime}, #{item.endTime}, #{item.isDeleted}, #{item.createrId}, #{item.updaterId},
             #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
</mapper>