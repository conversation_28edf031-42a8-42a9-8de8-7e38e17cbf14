<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductSupplementaryRecordDetailMapper">
    <resultMap type="com.msun.csm.dao.entity.proj.ProjProductSupplementaryRecordDetail" id="BaseResultMap">
        <result property="productSupplementaryRecordDetailId" column="product_supplementary_record_detail_id"
                jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="productSupplementaryRecordId" column="product_supplementary_record_id" jdbcType="INTEGER"/>
        <result property="yyProductId" column="yy_product_id" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap type="com.msun.csm.model.vo.product.ProductSupplementaryRecordDetailVO" id="RecordDetailVO">
        <result property="yyProductId" column="yy_product_id" jdbcType="INTEGER"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="deploymentProductName" column="deployment_product_name" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ProductResultMap" type="com.msun.csm.model.vo.ProjProductSupplementaryRecordVO">
        <result property="productSupplementaryRecordId" column="product_supplementary_record_id" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="orderInfoId" column="order_info_id" jdbcType="INTEGER"/>
        <result property="customInfoId" column="custom_info_id" jdbcType="INTEGER"/>
        <result property="specialApprovalType" column="special_approval_type" jdbcType="INTEGER"/>
        <result property="purchaseMode" column="purchase_mode" jdbcType="INTEGER"/>
        <result property="projectFileId" column="project_file_id" jdbcType="VARCHAR"/>
        <result property="effectiveStartTime" column="effective_start_time" jdbcType="TIMESTAMP"/>
        <result property="effectiveCompTime" column="effective_comp_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="customName" column="custom_name" jdbcType="VARCHAR"/>
        <result property="deploymentProductName" column="deployment_product_name" jdbcType="VARCHAR"/>
        <result property="deliveryOrderNo" column="delivery_order_no" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_product_supplementary_record_detail
        (product_supplementary_record_detail_id, creater_id,
        create_time, updater_id, update_time,
        is_deleted, product_supplementary_record_id,
        yy_product_id)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.productSupplementaryRecordDetailId,jdbcType=BIGINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.productSupplementaryRecordId,jdbcType=BIGINT},
            #{item.yyProductId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <select id="selectListByProductSupplementaryRecordId" resultMap="RecordDetailVO" parameterType="java.lang.Long">
        with records as(
            select
                yy_order_product_id,
                string_agg(distinct product_name, ', ') AS product_name
            from (
                     SELECT re.yy_order_product_id, dp.product_name
                     FROM csm.proj_product_arrange_record re
                              left join csm.dict_product dp
                                        on dp.yy_product_id=re.product_arrange_id
                     where re.is_deleted=0
                       and dp.is_deleted=0
                 )AS expanded_tags
            GROUP BY yy_order_product_id
        )
        select dt.yy_product_id,
               dp.product_name,
            coalesce(rcd.product_name,dp.product_name) as deployment_product_name
        from csm.proj_product_supplementary_record_detail dt
                 left join csm.dict_product dp
                           on dt.yy_product_id = dp.yy_product_id
                 left join records rcd on
            rcd.yy_order_product_id = dt.yy_product_id
        where product_supplementary_record_id = #{productSupplementaryRecordId,jdbcType=BIGINT}
          and dt.is_deleted = 0
    </select>
    <select id="selectProductWithSupplementary" parameterType="com.msun.csm.model.dto.InitProductSupplementaryRecordDTO"
            resultMap="ProductResultMap">
        select
        p.*,
        pci.custom_name ,
        odd.delivery_order_no
        from
        csm.proj_product_supplementary_record p
        left join csm.proj_custom_info pci on
        p.custom_info_id = pci.custom_info_id
        left join csm.proj_order_info odd on odd.order_info_id =p.order_info_id
        where
        p.is_deleted = 0
        <if test="customInfoId != null">
            and p.custom_info_id = #{customInfoId, jdbcType=BIGINT}
        </if>
        <if test="specialApprovalTypeName != null">
            and p.special_approval_type = #{specialApprovalTypeName, jdbcType=BIGINT}
        </if>
        <if test="deliveryOrderNo != null">
            and odd.delivery_order_no like concat('%', #{deliveryOrderNo}, '%')
        </if>
        order by p.create_time desc
    </select>
    <select id="selectProductWithSupplementary111"
            parameterType="com.msun.csm.model.dto.InitProductSupplementaryRecordDTO" resultMap="ProductResultMap">
        with records as(
        select
        yy_order_product_id,
        string_agg(distinct product_name, ', ') AS deployment_product_name
        from (
        SELECT re.yy_order_product_id, dp.product_name
        FROM csm.proj_product_arrange_record re
        left join csm.dict_product dp
        on dp.yy_product_id=re.product_arrange_id
        where re.is_deleted=0
        and dp.is_deleted=0
        )AS expanded_tags
        GROUP BY yy_order_product_id
        )
        select
        p.*,
        pci.custom_name ,
        odd.delivery_order_no,
        coalesce(pop.deployment_product_name,dp.product_name) as deployment_product_name
        from
        csm.proj_product_supplementary_record p
        left join csm.proj_custom_info pci on
        p.custom_info_id = pci.custom_info_id
        left join csm.proj_order_info odd on odd.order_info_id =p.order_info_id
        left join records pop on pop.yy_order_product_id =p.order_info_id
        where
        p.is_deleted = 0
        <if test="customInfoId != null">
            and p.custom_info_id = #{customInfoId, jdbcType=BIGINT}
        </if>
        <if test="specialApprovalType != null">
            and p.special_approval_type = #{specialApprovalType, jdbcType=BIGINT}
        </if>
    </select>
</mapper>
