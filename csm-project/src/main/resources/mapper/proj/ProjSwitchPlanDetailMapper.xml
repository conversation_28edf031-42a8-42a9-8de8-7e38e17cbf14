<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSwitchPlanDetailMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    <!--@Table csm.proj_switch_plan_detail-->
    <id column="switch_plan_detail_id" jdbcType="BIGINT" property="switchPlanDetailId" />
    <result column="switch_plan_id" jdbcType="BIGINT" property="switchPlanId" />
    <result column="hospital_dept_id" jdbcType="BIGINT" property="hospitalDeptId" />
    <result column="hospital_dept_name" jdbcType="VARCHAR" property="hospitalDeptName" />
    <result column="switch_phase_id" jdbcType="BIGINT" property="switchPhaseId" />
    <result column="switch_phase_name" jdbcType="VARCHAR" property="switchPhaseName" />
    <result column="order_no" jdbcType="SMALLINT" property="orderNo" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="is_chosen" jdbcType="SMALLINT" property="isChosen" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    switch_plan_detail_id, switch_plan_id, hospital_dept_id, hospital_dept_name, switch_phase_id,
    switch_phase_name, order_no, content, creater_id, create_time, updater_id, update_time,
    is_deleted, is_chosen
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.proj_switch_plan_detail
    where switch_plan_detail_id = #{switchPlanDetailId,jdbcType=BIGINT}
  </select>
  <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
      <!--@mbg.generated-->
      update csm.proj_switch_plan_detail
      set is_deleted = 1
      where switch_plan_detail_id = #{switchPlanDetailId,jdbcType=BIGINT}
  </update>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    insert into csm.proj_switch_plan_detail (switch_plan_detail_id, switch_plan_id, hospital_dept_id,
      hospital_dept_name, switch_phase_id, switch_phase_name,
      order_no, content, creater_id,
      create_time, updater_id, update_time,
      is_deleted, is_chosen)
    values (#{switchPlanDetailId,jdbcType=BIGINT}, #{switchPlanId,jdbcType=BIGINT}, #{hospitalDeptId,jdbcType=BIGINT},
      #{hospitalDeptName,jdbcType=VARCHAR}, #{switchPhaseId,jdbcType=BIGINT}, #{switchPhaseName,jdbcType=VARCHAR},
      #{orderNo,jdbcType=SMALLINT}, #{content,jdbcType=VARCHAR}, #{createrId,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{isDeleted,jdbcType=SMALLINT}, #{isChosen,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    insert into csm.proj_switch_plan_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="switchPlanDetailId != null">
        switch_plan_detail_id,
      </if>
      <if test="switchPlanId != null">
        switch_plan_id,
      </if>
      <if test="hospitalDeptId != null">
        hospital_dept_id,
      </if>
      <if test="hospitalDeptName != null">
        hospital_dept_name,
      </if>
      <if test="switchPhaseId != null">
        switch_phase_id,
      </if>
      <if test="switchPhaseName != null">
        switch_phase_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="isChosen != null">
        is_chosen,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="switchPlanDetailId != null">
        #{switchPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="switchPlanId != null">
        #{switchPlanId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptId != null">
        #{hospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptName != null">
        #{hospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="switchPhaseId != null">
        #{switchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="switchPhaseName != null">
        #{switchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="isChosen != null">
        #{isChosen,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    update csm.proj_switch_plan_detail
    <set>
      <if test="switchPlanId != null">
        switch_plan_id = #{switchPlanId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptId != null">
        hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptName != null">
        hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="switchPhaseId != null">
        switch_phase_id = #{switchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="switchPhaseName != null">
        switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="isChosen != null">
        is_chosen = #{isChosen,jdbcType=SMALLINT},
      </if>
    </set>
    where switch_plan_detail_id = #{switchPlanDetailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    update csm.proj_switch_plan_detail
    set switch_plan_id = #{switchPlanId,jdbcType=BIGINT},
      hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT},
      hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
      switch_phase_id = #{switchPhaseId,jdbcType=BIGINT},
      switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=SMALLINT},
      content = #{content,jdbcType=VARCHAR},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      is_chosen = #{isChosen,jdbcType=SMALLINT}
    where switch_plan_detail_id = #{switchPlanDetailId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_switch_plan_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="switch_plan_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.switchPlanId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="hospital_dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.hospitalDeptId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="hospital_dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.hospitalDeptName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="switch_phase_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.switchPhaseId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="switch_phase_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.switchPhaseName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="is_chosen = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.isChosen,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where switch_plan_detail_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.switchPlanDetailId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_switch_plan_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="switch_plan_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.switchPlanId != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.switchPlanId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="hospital_dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hospitalDeptId != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.hospitalDeptId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="hospital_dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hospitalDeptName != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.hospitalDeptName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="switch_phase_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.switchPhaseId != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.switchPhaseId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="switch_phase_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.switchPhaseName != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.switchPhaseName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.content != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.content,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_chosen = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isChosen != null">
            when switch_plan_detail_id = #{item.switchPlanDetailId,jdbcType=BIGINT} then #{item.isChosen,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where switch_plan_detail_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.switchPlanDetailId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.proj_switch_plan_detail
    (switch_plan_detail_id, switch_plan_id, hospital_dept_id, hospital_dept_name, switch_phase_id,
      switch_phase_name, order_no, content, creater_id, create_time, updater_id, update_time,
      is_deleted, is_chosen)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.switchPlanDetailId,jdbcType=BIGINT}, #{item.switchPlanId,jdbcType=BIGINT},
        #{item.hospitalDeptId,jdbcType=BIGINT}, #{item.hospitalDeptName,jdbcType=VARCHAR},
        #{item.switchPhaseId,jdbcType=BIGINT}, #{item.switchPhaseName,jdbcType=VARCHAR},
        #{item.orderNo,jdbcType=SMALLINT}, #{item.content,jdbcType=VARCHAR}, #{item.createrId,jdbcType=BIGINT},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=SMALLINT}, #{item.isChosen,jdbcType=SMALLINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    insert into csm.proj_switch_plan_detail
    (switch_plan_detail_id, switch_plan_id, hospital_dept_id, hospital_dept_name, switch_phase_id,
      switch_phase_name, order_no, content, creater_id, create_time, updater_id, update_time,
      is_deleted, is_chosen)
    values
    (#{switchPlanDetailId,jdbcType=BIGINT}, #{switchPlanId,jdbcType=BIGINT}, #{hospitalDeptId,jdbcType=BIGINT},
      #{hospitalDeptName,jdbcType=VARCHAR}, #{switchPhaseId,jdbcType=BIGINT}, #{switchPhaseName,jdbcType=VARCHAR},
      #{orderNo,jdbcType=SMALLINT}, #{content,jdbcType=VARCHAR}, #{createrId,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{isDeleted,jdbcType=SMALLINT}, #{isChosen,jdbcType=SMALLINT})
    on duplicate key update
    switch_plan_detail_id = #{switchPlanDetailId,jdbcType=BIGINT},
    switch_plan_id = #{switchPlanId,jdbcType=BIGINT},
    hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT},
    hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
    switch_phase_id = #{switchPhaseId,jdbcType=BIGINT},
    switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR},
    order_no = #{orderNo,jdbcType=SMALLINT},
    content = #{content,jdbcType=VARCHAR},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{isDeleted,jdbcType=SMALLINT},
    is_chosen = #{isChosen,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlanDetail">
    <!--@mbg.generated-->
    insert into csm.proj_switch_plan_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="switchPlanDetailId != null">
        switch_plan_detail_id,
      </if>
      <if test="switchPlanId != null">
        switch_plan_id,
      </if>
      <if test="hospitalDeptId != null">
        hospital_dept_id,
      </if>
      <if test="hospitalDeptName != null">
        hospital_dept_name,
      </if>
      <if test="switchPhaseId != null">
        switch_phase_id,
      </if>
      <if test="switchPhaseName != null">
        switch_phase_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="isChosen != null">
        is_chosen,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="switchPlanDetailId != null">
        #{switchPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="switchPlanId != null">
        #{switchPlanId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptId != null">
        #{hospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptName != null">
        #{hospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="switchPhaseId != null">
        #{switchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="switchPhaseName != null">
        #{switchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="isChosen != null">
        #{isChosen,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="switchPlanDetailId != null">
        switch_plan_detail_id = #{switchPlanDetailId,jdbcType=BIGINT},
      </if>
      <if test="switchPlanId != null">
        switch_plan_id = #{switchPlanId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptId != null">
        hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="hospitalDeptName != null">
        hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="switchPhaseId != null">
        switch_phase_id = #{switchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="switchPhaseName != null">
        switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="isChosen != null">
        is_chosen = #{isChosen,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

    <select id="queryHosDeptBySwitchPlanId" resultType="com.msun.csm.common.model.BaseDataExtra">
        select distinct(hospital_dept_id)                               as id,
                       hospital_dept_name                               as name,
                       case when is_chosen = 1 then true else false end as chosen
        from csm.proj_switch_plan_detail
        where switch_plan_id = #{switchPlanId}
          and is_deleted = 0
        order by hospital_dept_id
    </select>

    <select id="selectByParam" resultMap="BaseResultMap">
        select pspd.*
        from csm.proj_switch_plan_detail pspd
                 left join csm.dict_hospital_dept dhd on pspd.hospital_dept_id = dhd.dict_hospital_dept_id
                 left join csm.dict_switch_phase dsp on pspd.switch_phase_id = dsp.dict_switch_phase_id
        where pspd.is_deleted = 0
        <if test="isChosen != null and isChosen == 1">
            and pspd.is_chosen = 1
        </if>
        <if test="switchPlanId != null">
            and pspd.switch_plan_id = #{switchPlanId}
        </if>
        <if test="hospitalDeptId != null">
            and pspd.hospital_dept_id = #{hospitalDeptId}
        </if>
        <if test="switchPhaseId != null">
            and pspd.switch_phase_id = #{switchPhaseId}
        </if>
        order by dhd.order_no, dsp.order_no, pspd.order_no
    </select>

    <update id="delCustomHospitalDept">
        update csm.proj_switch_plan_detail
        set is_deleted = 1
        where hospital_dept_id = #{hospitalDeptId}
    </update>

    <update id="changeHospitalDeptChosenStatus">
        update csm.proj_switch_plan_detail
        set is_chosen = #{isChosen}
        where hospital_dept_id = #{hospitalDeptId}
          and switch_plan_id = #{switchPlanId}
    </update>

  <update id="deleteBySwitchPlanId">
    update csm.proj_switch_plan_detail
    set is_deleted = 1
    where switch_plan_id = #{switchPlanId}
  </update>

  <update id="updateOrder">
      update csm.proj_switch_plan_detail
      set order_no    = order_no + 1,
          update_time = now(),
          updater_id  = #{userId}
      where switch_plan_id = #{switchPlanId}
        and order_no > #{currentOrder}
        and is_deleted = 0
  </update>

  <update id="updateSwitchPhase">
    update csm.proj_switch_plan_detail
    set switch_phase_name = #{switchPhaseName},
        update_time       = now(),
        updater_id        = #{updaterId}
    where switch_plan_id = #{switchPlanId}
      and hospital_dept_id = #{hospitalDeptId}
      and switch_phase_id = #{switchPhaseId}
      and is_deleted = 0
    </update>

  <update id="updateCustomHospitalDept">
    update csm.proj_switch_plan_detail
    set hospital_dept_name = #{hospitalDeptName},
        update_time       = now(),
        updater_id        = #{updaterId}
    where switch_plan_id = #{switchPlanId}
      and hospital_dept_id = #{hospitalDeptId}
      and is_deleted = 0
  </update>
</mapper>
