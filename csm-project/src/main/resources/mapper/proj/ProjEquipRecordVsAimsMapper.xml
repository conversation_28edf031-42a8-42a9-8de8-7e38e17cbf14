<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipRecordVsAimsMapper">
    <select id="selectEquipRecordVsAimsList" resultType="com.msun.csm.model.vo.ProjEquipRecordVsAimsVO"
            parameterType="com.msun.csm.model.dto.AimsEquipSelectDTO">
        select
        per.* ,
        perva.*,
        phi.hospital_name as hospitalInfoName,
        phi.cloud_hospital_id,
        phi.cloud_hospital_id as cloud_hospital_id_str,
        su.user_name as createrName,
        (select pass_standard from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as pass_standard
        from
        csm.proj_equip_record_vs_aims perva
        left join csm.proj_equip_record per on perva.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        left join csm.sys_user su on per.creater_id = su.sys_user_id
        where
        perva.is_deleted = 0
        <if test="projectInfoId != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipModelOrFactory != null">
            and (per.equip_model_name ilike concat('%',#{equipModelOrFactory},'%') or per.equip_factory_name ilike
            concat('%',#{equipModelOrFactory},'%'))
        </if>
        <if test="equipStatus != null">
            and per.equip_status = #{equipStatus}
        </if>
        <if test="requiredFlag != null">
            and per.required_flag = #{requiredFlag}
        </if>
        <if test="equipRecordVsAimsId != null">
            and perva.equip_record_vs_aims_id = #{equipRecordVsAimsId}
        </if>
        order by perva.create_time desc
    </select>

    <select id="findToMsunEquipRecord" parameterType="com.msun.csm.model.dto.AimsEquipSelectDTO"
            resultType="com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto">
        select rva.equip_record_vs_aims_id as id,
        phi.cloud_hospital_id as hospitalId,
        phi.org_id as hisOrgId,
        'AIMS' as productCode,
        per.equip_type_id as deviceId,
        per.equip_type_name as deviceName,
        per.equip_factory_name as firm,
        per.equip_model_name as model,
        per.comm_mode_key as communication,
        per.equip_position as location,
        per.equip_factory_phone as firmTel,
        per.equip_position as regionalEquipment,
        per.equip_class_id as monitorClass,
        per.cloud_equip_id as oldEquipId,
        per.yy_product_id as productId
        from csm.proj_equip_record_vs_aims rva
        left join csm.proj_equip_record per on rva.equip_record_id = per.equip_record_id
        left join csm.proj_hospital_info phi on per.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        <where>
            and per.required_flag = 1
<!--            and per.cloud_equip_id is not null-->
            and per.equip_status != 5
            <!-- 2025年7月30日09点10分 董博培要求手麻设备的一键检测允许任何状态的设备进行检测。改造背景是泗水县人民医院的手麻设备检测不通过，因为手麻通常不会发送设备-->
<!--            <if test="projectType == 1">-->
<!--                and per.equip_status not in (0,1,2)-->
<!--            </if>-->
            <if test="projectInfoId !=null">
                and per.project_info_id = #{projectInfoId}
            </if>
            <if test="equipRecordVsAimsId != null">
                and rva.equip_record_vs_aims_id = #{equipRecordVsAimsId}
            </if>
            <if test="equipRecordVsAimsIdList != null and equipRecordVsAimsIdList.size() > 0">
                and rva.equip_record_vs_aims_id in
                <foreach collection="equipRecordVsAimsIdList" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            and rva.is_deleted = 0
            and per.is_deleted = 0
        </where>
    </select>

    <select id="selectAimsEquipSendCloudData" parameterType="com.msun.csm.model.dto.AimsEquipSelectDTO"
            resultType="com.msun.csm.model.vo.AimsEquipSendCloudVO">
        select perva.equip_record_vs_aims_id ,
        phi.cloud_hospital_id ,
        phi.org_id ,
        perva.equip_position_flag ,
        perva.equip_position_name ,
        per.equip_type_id,
        per.equip_type_name,
        per.equip_model_name,
        perva.monitor_code,
        perva.protocol_type,
        perva.listen_port,
        perva.listen_ip,
        perva.mon_device_config_port,
        perva.equip_ip,
        perva.center_mon_device_id,
        perva.series_name,
        perva.center_mon_device_flag
        from csm.proj_equip_record_vs_aims perva
        left join csm.proj_equip_record per on perva.equip_record_id = per.equip_record_id
        left join csm.proj_hospital_info phi on per.hospital_info_id = phi.hospital_info_id
        where per.is_deleted = 0 and phi.is_deleted = 0
        <if test="projectInfoId !=null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="equipRecordVsAimsIdList != null and equipRecordVsAimsIdList.size() > 0">
            and perva.equip_record_vs_aims_id in
            <foreach collection="equipRecordVsAimsIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getNotApplyRecordCount" resultType="java.lang.Integer">
        select count(1)
        from csm.proj_equip_record_vs_aims rvl
        left join csm.proj_equip_record per on rvl.equip_record_id = per.equip_record_id
        where rvl.is_deleted = 0
        and per.is_deleted = 0
        and per.required_flag = 1
        and per.project_info_id = #{projectInfoId}
        and per.equip_status in
        <foreach collection="statusList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <update id="updateAimsById">
        update csm.proj_equip_record_vs_aims
        <set>
            equip_record_id = #{equipRecordId,jdbcType=BIGINT},
            equip_position_flag = #{equipPositionFlag,jdbcType=INTEGER},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            equip_position_name = #{equipPositionName,jdbcType=VARCHAR},
            center_mon_device_flag = #{centerMonDeviceFlag,jdbcType=INTEGER},
            center_mon_device_id = #{centerMonDeviceId,jdbcType=BIGINT},
            equip_ip = #{equipIp,jdbcType=VARCHAR},
            mon_device_config_port = #{monDeviceConfigPort,jdbcType=INTEGER},
            listen_ip = #{listenIp,jdbcType=VARCHAR},
            listen_port = #{listenPort,jdbcType=INTEGER},
            monitor_code = #{monitorCode,jdbcType=VARCHAR},
            protocol_type = #{protocolType,jdbcType=INTEGER},
            series_name = #{seriesName,jdbcType=VARCHAR},
        </set>
        where equip_record_vs_aims_id = #{equipRecordVsAimsId,jdbcType=BIGINT}
    </update>
</mapper>
