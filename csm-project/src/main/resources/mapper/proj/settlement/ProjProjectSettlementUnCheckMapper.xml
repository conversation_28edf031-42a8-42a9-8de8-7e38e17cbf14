<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectSettlementUnCheckMapper">


    <select id="findUncheckData" resultType="com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheckRelative"
            parameterType="java.lang.Long">
        select t12.user_name as commit_user_name,
        t.project_info_id,
        t13.operate_time as commit_time,
        t6.contract_no,
        t4.deploy_node_id,
        t4.deploy_node_name,
        t5.delivery_order_no,
        t7.project_name,
        t7.project_type,
        t7.project_info_id,
        t8.custom_name,
        t8.custom_info_id,
        t10.file_path,
        t10.file_name,
        t13.operate_content as check_content,
        t13.settlement_status,
        t13.operate_node as check_node
        from csm.proj_project_settlement_un_check t
        left join csm.proj_project_order_relation t3
        on t3.project_info_id = t.project_info_id and t3.is_deleted = 0
        left join csm.proj_custom_cloud_service t4
        on t4.custom_cloud_service_id = t3.bussiness_info_id and t4.is_deleted = 0
        left join csm.proj_project_info t7 on t7.project_info_id = t.project_info_id and t7.is_deleted = 0
        left join csm.proj_order_info t5 on t5.order_info_id = t7.order_info_id and t5.is_deleted = 0
        left join csm.proj_contract_info t6 on t6.contract_info_id = t5.contract_info_id and t6.is_deleted = 0
        left join csm.proj_custom_info t8 on t8.custom_info_id = t7.custom_info_id and t8.is_deleted = 0
        left join csm.proj_project_settlement_rule t9 on t9.project_info_id = t.project_info_id and
        (t9.project_rule_code = 'SETTLEMENT_CLOUD_CONFIRM_FORM' or t9.project_rule_code = 'SETTLEMENT_PLAN_CLOUD_FILE')
        and t9.is_deleted = 0
        left join csm.proj_project_file t10 on t10.project_file_id = t9.project_file_id and t10.is_deleted = 0
        left join csm.proj_project_settlement_check t11
        on t11.project_settlement_check_id = t.project_settlement_check_id and t11.is_deleted = 0
        left join csm.sys_user t12 on t12.sys_user_id = t11.check_user_id
        left join csm.proj_project_settlement_log t13 on t13.project_settlement_log_id = t.project_settlement_log_id and
        t13.is_deleted = 0
        left join csm.proj_project_settlement_check t14 on t14.project_settlement_check_id =
        t.project_settlement_check_id and t14.is_deleted = 0
        where t.un_check_node = 3
        and t.un_check_user_id = #{unCheckUserId, jdbcType=BIGINT}
        <if test="customName != null and customName != ''">
            and t8.custom_name like concat('%', #{customName}, '%')
        </if>
        <if test="customInfoId != null">
            and t8.custom_info_id = #{customInfoId, jdbcType=BIGINT}
        </if>
        <if test="checkResult != null">
            <choose>
                <when test="checkResult == 2">
                    and t14.check_result is null
                </when>
                <otherwise>
                    and t14.check_result = #{checkResult, jdbcType=BIGINT}
                </otherwise>
            </choose>
        </if>

        and t.is_deleted = 0
        order by t13.operate_time desc
    </select>
</mapper>
