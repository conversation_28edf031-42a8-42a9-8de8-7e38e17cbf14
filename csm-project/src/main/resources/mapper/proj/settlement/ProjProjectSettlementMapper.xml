<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectSettlementMapper">
    <select id="selectHardwareFile"
            resultType="com.msun.csm.dao.entity.proj.projsettlement.SurveyHardwareNewResultdataEntity">
        SELECT id,
               url,
               customer_id,
               customer_info_id,
               project_id,
               invalid_flag,
               his_creater_id,
               his_creater_name,
               his_create_time,
               his_updater_id,
               his_update_time,
               "version"
        FROM platform.survey_hardware_new_resultdata_file
        WHERE project_id = #{projectInfoId, jdbcType=BIGINT}
          and invalid_flag = '0'
    </select>
    <sql id="findCloudServiceOrderSql">
        select t2.custom_cloud_service_id,
               t2.contract_custom_info_id,
               t.order_info_id,
               t.delivery_order_no,
               t.delivery_order_type,
               t.yy_order_id,
               t5.contract_name,
               t5.contract_no,
               t2.deploy_node_id   as envirId,
               t2.deploy_node_name as envirName,
               t2.subscribe_start_time,
               t6.msun_cloud_flag,
               t2.cloud_service_type
        from csm.proj_custom_cloud_service t2
                 left join csm.proj_project_order_relation t3
                           on t2.custom_cloud_service_id = t3.bussiness_info_id and t3.is_deleted = 0
                 left join csm.proj_order_info t on t2.yy_order_id = t.yy_order_id and t2.is_deleted = 0
                 left join csm.proj_contract_info t5 on t.contract_info_id = t5.contract_info_id and t5.is_deleted = 0
                 left join csm.dict_cloud_environments t6 on t6.envir_id = t2.deploy_node_id
    </sql>
    <sql id="findOrderInfoSql">
        select t2.custom_cloud_service_id,
               t2.contract_custom_info_id,
               t.order_info_id,
               t.delivery_order_no,
               t.delivery_order_type,
               t.yy_order_id,
               t5.contract_name,
               t5.contract_no,
               t2.deploy_node_id   as envirId,
               t2.deploy_node_name as envirName,
               t2.subscribe_start_time,
               t6.msun_cloud_flag
        from csm.proj_order_info t
                 left join csm.proj_project_order_relation t3 on t3.yy_order_id = t.yy_order_id and t3.is_deleted = 0
                 left join csm.proj_custom_cloud_service t2
                           on t2.custom_cloud_service_id = t3.bussiness_info_id and t2.is_deleted = 0
                 left join csm.proj_contract_info t5 on t.contract_info_id = t5.contract_info_id and t5.is_deleted = 0
                 left join csm.dict_cloud_environments t6 on t6.envir_id = t2.deploy_node_id
    </sql>
    <select id="findSettlementOrderInfoByCustomCloudServiceId"
            resultType="com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo">
        <include refid="findCloudServiceOrderSql"/>
        where t2.custom_cloud_service_id = #{customCloudServiceId, jdbcType=BIGINT} and t2.is_deleted = 0
    </select>
    <select id="selectBailingAppOpenStatus" resultType="java.lang.Integer">
        select case when ppr.app_status is null or ppr.app_status = 1 then 1 else 0 end app_status
        from platform.project pro
        left join platform.project_product_relation ppr
        on ppr.yy_productid in
        <foreach collection="productIds" item="id" separator="," open="(" close=")">
            #{id, jdbcType=BIGINT}
        </foreach>
        and ppr.project_id = pro.id
        where pro.id = #{projectInfoId, jdbcType=BIGINT}
    </select>
    <select id="findSettlementOrderInfo"
            resultType="com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo">
        <include refid="findOrderInfoSql"/>
        where t.yy_customer_id = #{yyCustomerId, jdbcType=BIGINT}
        and t.is_deleted = 0
    </select>
    <select id="findSettlementOrderInfoByOrderInfoId"
            resultType="com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo"
            parameterType="java.lang.Long">
        <include refid="findOrderInfoSql"/>
        where t.order_info_id = #{orderInfoId, jdbcType=BIGINT} and t.is_deleted = 0
    </select>
</mapper>
