<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjContractCustomInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_contract_custom_info-->
        <id column="contract_custom_info_id" jdbcType="BIGINT" property="contractCustomInfoId"/>
        <result column="yy_parta_id" jdbcType="BIGINT" property="yyPartaId"/>
        <result column="contract_custom_name" jdbcType="VARCHAR" property="contractCustomName"/>
        <result column="contract_custom_saleperson_id" jdbcType="BIGINT" property="contractCustomSalepersonId"/>
        <result column="contract_custom_saleteam_id" jdbcType="BIGINT" property="contractCustomSaleteamId"/>
        <result column="contract_custom_saleprovince_id" jdbcType="BIGINT" property="contractCustomSaleprovinceId"/>
        <result column="contract_custom_salecenter_id" jdbcType="BIGINT" property="contractCustomSalecenterId"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        contract_custom_info_id, yy_parta_id, contract_custom_name, contract_custom_saleperson_id,
        contract_custom_saleteam_id, contract_custom_saleprovince_id, contract_custom_salecenter_id,
        creater_id, create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_contract_custom_info
        where contract_custom_info_id = #{contractCustomInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_contract_custom_info
        where contract_custom_info_id = #{contractCustomInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_custom_info (contract_custom_info_id, yy_parta_id, contract_custom_name,
        contract_custom_saleperson_id, contract_custom_saleteam_id,
        contract_custom_saleprovince_id, contract_custom_salecenter_id,
        creater_id, create_time, updater_id,
        update_time, is_deleted)
        values (#{contractCustomInfoId,jdbcType=BIGINT}, #{yyPartaId,jdbcType=BIGINT},
        #{contractCustomName,jdbcType=VARCHAR},
        #{contractCustomSalepersonId,jdbcType=BIGINT}, #{contractCustomSaleteamId,jdbcType=BIGINT},
        #{contractCustomSaleprovinceId,jdbcType=BIGINT}, #{contractCustomSalecenterId,jdbcType=BIGINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_custom_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractCustomInfoId != null">
                contract_custom_info_id,
            </if>
            <if test="yyPartaId != null">
                yy_parta_id,
            </if>
            <if test="contractCustomName != null">
                contract_custom_name,
            </if>
            <if test="contractCustomSalepersonId != null">
                contract_custom_saleperson_id,
            </if>
            <if test="contractCustomSaleteamId != null">
                contract_custom_saleteam_id,
            </if>
            <if test="contractCustomSaleprovinceId != null">
                contract_custom_saleprovince_id,
            </if>
            <if test="contractCustomSalecenterId != null">
                contract_custom_salecenter_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractCustomInfoId != null">
                #{contractCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyPartaId != null">
                #{yyPartaId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomName != null">
                #{contractCustomName,jdbcType=VARCHAR},
            </if>
            <if test="contractCustomSalepersonId != null">
                #{contractCustomSalepersonId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleteamId != null">
                #{contractCustomSaleteamId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleprovinceId != null">
                #{contractCustomSaleprovinceId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSalecenterId != null">
                #{contractCustomSalecenterId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        update csm.proj_contract_custom_info
        <set>
            <if test="yyPartaId != null">
                yy_parta_id = #{yyPartaId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomName != null">
                contract_custom_name = #{contractCustomName,jdbcType=VARCHAR},
            </if>
            <if test="contractCustomSalepersonId != null">
                contract_custom_saleperson_id = #{contractCustomSalepersonId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleteamId != null">
                contract_custom_saleteam_id = #{contractCustomSaleteamId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleprovinceId != null">
                contract_custom_saleprovince_id = #{contractCustomSaleprovinceId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSalecenterId != null">
                contract_custom_salecenter_id = #{contractCustomSalecenterId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where contract_custom_info_id = #{contractCustomInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        update csm.proj_contract_custom_info
        set yy_parta_id = #{yyPartaId,jdbcType=BIGINT},
        contract_custom_name = #{contractCustomName,jdbcType=VARCHAR},
        contract_custom_saleperson_id = #{contractCustomSalepersonId,jdbcType=BIGINT},
        contract_custom_saleteam_id = #{contractCustomSaleteamId,jdbcType=BIGINT},
        contract_custom_saleprovince_id = #{contractCustomSaleprovinceId,jdbcType=BIGINT},
        contract_custom_salecenter_id = #{contractCustomSalecenterId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where contract_custom_info_id = #{contractCustomInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_contract_custom_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_parta_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.yyPartaId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="contract_custom_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.contractCustomName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contract_custom_saleperson_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.contractCustomSalepersonId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="contract_custom_saleteam_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.contractCustomSaleteamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="contract_custom_saleprovince_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.contractCustomSaleprovinceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="contract_custom_salecenter_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.contractCustomSalecenterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where contract_custom_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.contractCustomInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_contract_custom_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_parta_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyPartaId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.yyPartaId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_custom_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractCustomName != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.contractCustomName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_custom_saleperson_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractCustomSalepersonId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.contractCustomSalepersonId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_custom_saleteam_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractCustomSaleteamId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.contractCustomSaleteamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_custom_saleprovince_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractCustomSaleprovinceId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.contractCustomSaleprovinceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_custom_salecenter_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractCustomSalecenterId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.contractCustomSalecenterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
<!--            <trim prefix="create_time = case" suffix="end,">-->
<!--                <foreach collection="list" index="index" item="item">-->
<!--                    <if test="item.createTime != null">-->
<!--                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then-->
<!--                        #{item.createTime,jdbcType=TIMESTAMP}-->
<!--                    </if>-->
<!--                </foreach>-->
<!--            </trim>-->
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
<!--            <trim prefix="update_time = case" suffix="end,">-->
<!--                <foreach collection="list" index="index" item="item">-->
<!--                    <if test="item.updateTime != null">-->
<!--                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then-->
<!--                        #{item.updateTime,jdbcType=TIMESTAMP}-->
<!--                    </if>-->
<!--                </foreach>-->
<!--            </trim>-->
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when contract_custom_info_id = #{item.contractCustomInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where contract_custom_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.contractCustomInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_contract_custom_info
        (contract_custom_info_id, yy_parta_id, contract_custom_name, contract_custom_saleperson_id,
        contract_custom_saleteam_id, contract_custom_saleprovince_id, contract_custom_salecenter_id,
        creater_id, create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.contractCustomInfoId,jdbcType=BIGINT}, #{item.yyPartaId,jdbcType=BIGINT},
            #{item.contractCustomName,jdbcType=VARCHAR}, #{item.contractCustomSalepersonId,jdbcType=BIGINT},
            #{item.contractCustomSaleteamId,jdbcType=BIGINT}, #{item.contractCustomSaleprovinceId,jdbcType=BIGINT},
            #{item.contractCustomSalecenterId,jdbcType=BIGINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_custom_info
        (contract_custom_info_id, yy_parta_id, contract_custom_name, contract_custom_saleperson_id,
        contract_custom_saleteam_id, contract_custom_saleprovince_id, contract_custom_salecenter_id,
        creater_id, create_time, updater_id, update_time, is_deleted)
        values
        (#{contractCustomInfoId,jdbcType=BIGINT}, #{yyPartaId,jdbcType=BIGINT}, #{contractCustomName,jdbcType=VARCHAR},
        #{contractCustomSalepersonId,jdbcType=BIGINT}, #{contractCustomSaleteamId,jdbcType=BIGINT},
        #{contractCustomSaleprovinceId,jdbcType=BIGINT}, #{contractCustomSalecenterId,jdbcType=BIGINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        contract_custom_info_id = #{contractCustomInfoId,jdbcType=BIGINT},
        yy_parta_id = #{yyPartaId,jdbcType=BIGINT},
        contract_custom_name = #{contractCustomName,jdbcType=VARCHAR},
        contract_custom_saleperson_id = #{contractCustomSalepersonId,jdbcType=BIGINT},
        contract_custom_saleteam_id = #{contractCustomSaleteamId,jdbcType=BIGINT},
        contract_custom_saleprovince_id = #{contractCustomSaleprovinceId,jdbcType=BIGINT},
        contract_custom_salecenter_id = #{contractCustomSalecenterId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjContractCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_custom_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractCustomInfoId != null">
                contract_custom_info_id,
            </if>
            <if test="yyPartaId != null">
                yy_parta_id,
            </if>
            <if test="contractCustomName != null">
                contract_custom_name,
            </if>
            <if test="contractCustomSalepersonId != null">
                contract_custom_saleperson_id,
            </if>
            <if test="contractCustomSaleteamId != null">
                contract_custom_saleteam_id,
            </if>
            <if test="contractCustomSaleprovinceId != null">
                contract_custom_saleprovince_id,
            </if>
            <if test="contractCustomSalecenterId != null">
                contract_custom_salecenter_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractCustomInfoId != null">
                #{contractCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyPartaId != null">
                #{yyPartaId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomName != null">
                #{contractCustomName,jdbcType=VARCHAR},
            </if>
            <if test="contractCustomSalepersonId != null">
                #{contractCustomSalepersonId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleteamId != null">
                #{contractCustomSaleteamId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleprovinceId != null">
                #{contractCustomSaleprovinceId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSalecenterId != null">
                #{contractCustomSalecenterId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="contractCustomInfoId != null">
                contract_custom_info_id = #{contractCustomInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyPartaId != null">
                yy_parta_id = #{yyPartaId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomName != null">
                contract_custom_name = #{contractCustomName,jdbcType=VARCHAR},
            </if>
            <if test="contractCustomSalepersonId != null">
                contract_custom_saleperson_id = #{contractCustomSalepersonId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleteamId != null">
                contract_custom_saleteam_id = #{contractCustomSaleteamId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSaleprovinceId != null">
                contract_custom_saleprovince_id = #{contractCustomSaleprovinceId,jdbcType=BIGINT},
            </if>
            <if test="contractCustomSalecenterId != null">
                contract_custom_salecenter_id = #{contractCustomSalecenterId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByYyPartaId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_contract_custom_info
        where yy_parta_id = #{yyPartaId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>

    <select id="selectByKeyword" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select contract_custom_info_id "id",
               contract_custom_name    "name"
        from csm.proj_contract_custom_info t
        where is_deleted = 0
        <if test="keyword != null">
            and t.contract_custom_name like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
        order by t.update_time desc
    </select>
</mapper>
