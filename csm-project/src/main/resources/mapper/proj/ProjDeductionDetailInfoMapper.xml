<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjDeductionDetailInfoMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjDeductionDetailInfo">
    <!--@mbg.generated-->
    <!--@Table proj_deduction_detail_info-->
    <id column="proj_deduction_detail_info_id" jdbcType="BIGINT" property="projDeductionDetailInfoId" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId" />
    <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="classification_code" jdbcType="VARCHAR" property="classificationCode" />
    <result column="deduction_type" jdbcType="VARCHAR" property="deductionType" />
    <result column="estimated_deduction" jdbcType="NUMERIC" property="estimatedDeduction" />
    <result column="practical_deduction" jdbcType="NUMERIC" property="practicalDeduction" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="detail_status" jdbcType="INTEGER" property="detailStatus" />
    <result column="backend_remark" jdbcType="VARCHAR" property="backendRemark" />
    <result column="attachment_id" jdbcType="VARCHAR" property="attachmentId" />
    <result column="function_code" jdbcType="VARCHAR" property="functionCode" />
    <result column="use_count" jdbcType="INTEGER" property="useCount" />
    <result column="project_stage_code" jdbcType="VARCHAR" property="projectStageCode" />
    <result column="item_detail" jdbcType="VARCHAR" property="itemDetail" />
    <result column="item_add_time" jdbcType="TIMESTAMP" property="itemAddTime" />
    <result column="milestone_node_code" jdbcType="VARCHAR" property="milestoneNodeCode" />
    <result column="project_file_id" jdbcType="BIGINT" property="projectFileId" />
    <result column="milestone_node_id" jdbcType="BIGINT" property="milestoneNodeId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    proj_deduction_detail_info_id, is_deleted, creater_id, create_time, updater_id, update_time, 
    project_info_id, yy_product_id, "source", classification_code, deduction_type, estimated_deduction, 
    practical_deduction, remark, detail_status, backend_remark, attachment_id, function_code, 
    use_count, project_stage_code, item_detail, item_add_time, milestone_node_code, project_file_id, 
    milestone_node_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from proj_deduction_detail_info
    where proj_deduction_detail_info_id = #{projDeductionDetailInfoId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from proj_deduction_detail_info
    where proj_deduction_detail_info_id = #{projDeductionDetailInfoId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjDeductionDetailInfo">
    <!--@mbg.generated-->
    insert into proj_deduction_detail_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projDeductionDetailInfoId != null">
        proj_deduction_detail_info_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="source != null">
        "source",
      </if>
      <if test="classificationCode != null">
        classification_code,
      </if>
      <if test="deductionType != null">
        deduction_type,
      </if>
      <if test="estimatedDeduction != null">
        estimated_deduction,
      </if>
      <if test="practicalDeduction != null">
        practical_deduction,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="detailStatus != null">
        detail_status,
      </if>
      <if test="backendRemark != null">
        backend_remark,
      </if>
      <if test="attachmentId != null">
        attachment_id,
      </if>
      <if test="functionCode != null">
        function_code,
      </if>
      <if test="useCount != null">
        use_count,
      </if>
      <if test="projectStageCode != null">
        project_stage_code,
      </if>
      <if test="itemDetail != null">
        item_detail,
      </if>
      <if test="itemAddTime != null">
        item_add_time,
      </if>
      <if test="milestoneNodeCode != null">
        milestone_node_code,
      </if>
      <if test="projectFileId != null">
        project_file_id,
      </if>
      <if test="milestoneNodeId != null">
        milestone_node_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projDeductionDetailInfoId != null">
        #{projDeductionDetailInfoId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="classificationCode != null">
        #{classificationCode,jdbcType=VARCHAR},
      </if>
      <if test="deductionType != null">
        #{deductionType,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDeduction != null">
        #{estimatedDeduction,jdbcType=NUMERIC},
      </if>
      <if test="practicalDeduction != null">
        #{practicalDeduction,jdbcType=NUMERIC},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="detailStatus != null">
        #{detailStatus,jdbcType=INTEGER},
      </if>
      <if test="backendRemark != null">
        #{backendRemark,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="functionCode != null">
        #{functionCode,jdbcType=VARCHAR},
      </if>
      <if test="useCount != null">
        #{useCount,jdbcType=INTEGER},
      </if>
      <if test="projectStageCode != null">
        #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="itemDetail != null">
        #{itemDetail,jdbcType=VARCHAR},
      </if>
      <if test="itemAddTime != null">
        #{itemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="milestoneNodeCode != null">
        #{milestoneNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="projectFileId != null">
        #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="milestoneNodeId != null">
        #{milestoneNodeId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjDeductionDetailInfo">
    <!--@mbg.generated-->
    update proj_deduction_detail_info
    <set>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        "source" = #{source,jdbcType=VARCHAR},
      </if>
      <if test="classificationCode != null">
        classification_code = #{classificationCode,jdbcType=VARCHAR},
      </if>
      <if test="deductionType != null">
        deduction_type = #{deductionType,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDeduction != null">
        estimated_deduction = #{estimatedDeduction,jdbcType=NUMERIC},
      </if>
      <if test="practicalDeduction != null">
        practical_deduction = #{practicalDeduction,jdbcType=NUMERIC},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="detailStatus != null">
        detail_status = #{detailStatus,jdbcType=INTEGER},
      </if>
      <if test="backendRemark != null">
        backend_remark = #{backendRemark,jdbcType=VARCHAR},
      </if>
      <if test="attachmentId != null">
        attachment_id = #{attachmentId,jdbcType=VARCHAR},
      </if>
      <if test="functionCode != null">
        function_code = #{functionCode,jdbcType=VARCHAR},
      </if>
      <if test="useCount != null">
        use_count = #{useCount,jdbcType=INTEGER},
      </if>
      <if test="projectStageCode != null">
        project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="itemDetail != null">
        item_detail = #{itemDetail,jdbcType=VARCHAR},
      </if>
      <if test="itemAddTime != null">
        item_add_time = #{itemAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="milestoneNodeCode != null">
        milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="projectFileId != null">
        project_file_id = #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="milestoneNodeId != null">
        milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT},
      </if>
    </set>
    where proj_deduction_detail_info_id = #{projDeductionDetailInfoId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjDeductionDetailInfo">
    <!--@mbg.generated-->
    update proj_deduction_detail_info
    set is_deleted = #{isDeleted,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      project_info_id = #{projectInfoId,jdbcType=BIGINT},
      yy_product_id = #{yyProductId,jdbcType=BIGINT},
      "source" = #{source,jdbcType=VARCHAR},
      classification_code = #{classificationCode,jdbcType=VARCHAR},
      deduction_type = #{deductionType,jdbcType=VARCHAR},
      estimated_deduction = #{estimatedDeduction,jdbcType=NUMERIC},
      practical_deduction = #{practicalDeduction,jdbcType=NUMERIC},
      remark = #{remark,jdbcType=VARCHAR},
      detail_status = #{detailStatus,jdbcType=INTEGER},
      backend_remark = #{backendRemark,jdbcType=VARCHAR},
      attachment_id = #{attachmentId,jdbcType=VARCHAR},
      function_code = #{functionCode,jdbcType=VARCHAR},
      use_count = #{useCount,jdbcType=INTEGER},
      project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      item_detail = #{itemDetail,jdbcType=VARCHAR},
      item_add_time = #{itemAddTime,jdbcType=TIMESTAMP},
      milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
      project_file_id = #{projectFileId,jdbcType=BIGINT},
      milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
    where proj_deduction_detail_info_id = #{projDeductionDetailInfoId,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into proj_deduction_detail_info
    (proj_deduction_detail_info_id, is_deleted, creater_id, create_time, updater_id, 
      update_time, project_info_id, yy_product_id, "source", classification_code, deduction_type, 
      estimated_deduction, practical_deduction, remark, detail_status, backend_remark, 
      attachment_id, function_code, use_count, project_stage_code, item_detail, item_add_time, 
      milestone_node_code, project_file_id, milestone_node_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.projDeductionDetailInfoId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT}, 
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.projectInfoId,jdbcType=BIGINT}, #{item.yyProductId,jdbcType=BIGINT}, 
        #{item.source,jdbcType=VARCHAR}, #{item.classificationCode,jdbcType=VARCHAR}, #{item.deductionType,jdbcType=VARCHAR}, 
        #{item.estimatedDeduction,jdbcType=NUMERIC}, #{item.practicalDeduction,jdbcType=NUMERIC}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.detailStatus,jdbcType=INTEGER}, #{item.backendRemark,jdbcType=VARCHAR}, 
        #{item.attachmentId,jdbcType=VARCHAR}, #{item.functionCode,jdbcType=VARCHAR}, #{item.useCount,jdbcType=INTEGER}, 
        #{item.projectStageCode,jdbcType=VARCHAR}, #{item.itemDetail,jdbcType=VARCHAR}, 
        #{item.itemAddTime,jdbcType=TIMESTAMP}, #{item.milestoneNodeCode,jdbcType=VARCHAR}, 
        #{item.projectFileId,jdbcType=BIGINT}, #{item.milestoneNodeId,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="getBackendTeamDeductionDetailInfo" resultType="com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailInfoVO" parameterType="com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailVO2">
      select pddi.proj_deduction_detail_info_id as "backendTeamDeductionDetailId",
             pddi."source",
             case
                 when pddi."source" = 'first' then '首验'
                 else '终验'
                 end                            as "stageName",
             ddt.server_type                    as "serverTypeCode",
             dst.server_type_name               as "serverTypeName",
             ddt.issue_classification_id        as "issueClassificationId",
             cic."name"                         as "issueClassificationName",
             pddi.yy_product_id,
             case
                 when pddi.yy_product_id = -99999 then '三方接口'
                 when dp.product_name is null then dpvm.yy_module_name
                 else dp.product_name
                 end                            as "yyProductName",
             pddi.deduction_type                as "deductionTypeCode",
             ddt."name"                         as "deductionTypeName",
             pii.description                    as "issueDescription",
             pddi.practical_deduction           as "deductionAmount",
             pddi.detail_status                 as "detailStatus",
             case
                 when pddi.detail_status = 1 then '未确认'
                 when pddi.detail_status = 2 then '已确认'
                 else '已驳回'
                 end                            as "detailStatusName",
             pddi.backend_remark                as "backendRemark",
             pddi.*
      from csm.proj_deduction_detail_info pddi
               left join csm.dict_deduction_type ddt on pddi.deduction_type = ddt.code and ddt.is_deleted = 0
               left join csm.dict_server_type dst on ddt.server_type = dst.server_type_code and dst.is_deleted = 0
               left join csm.config_issue_classification cic on ddt.issue_classification_id = cic.id and cic.is_deleted = 0
               left join csm.dict_product dp on pddi.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
               left join csm.dict_product_vs_modules dpvm on pddi.yy_product_id = dpvm.yy_module_id and dpvm.is_deleted = 0
               left join csm.proj_issue_info pii on pddi.proj_deduction_detail_info_id = pii.business_id and pii.is_deleted = 0
      where pddi.is_deleted = 0
        and pddi.project_info_id = #{projectInfoId}
        and ddt.server_type = #{serverType}
      <if test="stageCode != null and stageCode.size() > 0">
          and pddi."source" in
          <foreach collection="stageCode" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>

      <if test="detailRecordStatus != null and detailRecordStatus.size() > 0">
          and pddi."detail_status" in
          <foreach collection="detailRecordStatus" item="detailStatus" open="(" separator="," close=")">
              #{detailStatus}
          </foreach>
      </if>
    order by pddi.create_time
  </select>
</mapper>