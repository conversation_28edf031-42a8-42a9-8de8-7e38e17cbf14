<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjResearchPlanResSourceMapper">

    <insert id="insertBatch"
            parameterType="java.util.List">
        insert into csm.proj_research_plan_res_source
        (id, source_key, source_content, plan_id)
        values
        <foreach collection="contentDtoList" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},
             #{item.sourceKey,jdbcType=BIGINT},
             #{item.sourceContent,jdbcType=VARCHAR},
             #{item.planId,jdbcType=BIGINT})
        </foreach>
    </insert>


    <delete id="deleteByPlanIds" parameterType="long">
        delete from csm.proj_research_plan_res_source
        <where>
            plan_id in
            <foreach collection="contentDtoList" item="item" separator="," open="(" close=")">
                #{item.planId,jdbcType=BIGINT}
            </foreach>
        </where>
    </delete>

</mapper>
