<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjApplyOrderProductRecordMapper">
    <insert id="insertBatch"
            parameterType="java.util.List">
        insert into csm.proj_apply_order_product_record
        (proj_apply_order_product_record_id, project_info_id,
        custom_info_id, apply_order_id, product_dict_id,
        product_type,yy_order_product_id,creater_id,updater_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projApplyOrderProductRecordId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.customInfoId,jdbcType=BIGINT},
            #{item.applyOrderId,jdbcType=BIGINT},
            #{item.productDictId,jdbcType=BIGINT},
            #{item.productType,jdbcType=VARCHAR},
            #{item.yyOrderProductId,jdbcType=BIGINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.updaterId,jdbcType=BIGINT})
        </foreach>
    </insert>


    <select id="findByApplyOrderId" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrderProductRecordRelative"
            parameterType="java.lang.Long">
        select t2.product_name, t.*
        from csm.proj_apply_order_product_record t
                 left join csm.dict_product t2 on t2.yy_product_id = t.yy_order_product_id and t2.is_deleted = 0
        where t.apply_order_id = #{applyOrderId, jdbcType=BIGINT}
          and t.is_deleted = 0
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_apply_order_product_record
        set custom_info_id = #{newCustomInfoId, jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId, jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
