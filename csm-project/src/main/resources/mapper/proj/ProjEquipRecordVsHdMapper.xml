<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipRecordVsHdMapper">
    <select id="getEquipRecordVsHdDetail" parameterType="com.msun.csm.model.dto.ProjEquipRecordVsHdDTO"
            resultType="com.msun.csm.model.vo.ProjEquipRecordVsHdVO">
        select pervh.*,
        per.*,
        phi.hospital_name,
        (select pass_standard from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as pass_standard
        from csm.proj_equip_record_vs_hd pervh
        left join csm.proj_equip_record per on pervh.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        where
        pervh.is_deleted = 0
        <if test="projectInfoId != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipRecordVsHdId != null">
            and pervh.equip_record_vs_hd_id = #{equipRecordVsHdId}
        </if>
    </select>

    <select id="selectHdEquipData" resultType="com.msun.csm.model.vo.ProjEquipRecordVsHdVO"
            parameterType="com.msun.csm.model.dto.HdEquipSelectDTO">
        select
        per.* ,
        pervh.*,
        phi.hospital_name as hospitalInfoName,
        su.user_name as createrName
        from
        csm.proj_equip_record_vs_hd pervh
        left join csm.proj_equip_record per on pervh.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        left join csm.sys_user su on per.creater_id = su.sys_user_id
        where
        pervh.is_deleted = 0
        <if test="customInfoId != null">
            and per.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipModelOrFactory != null">
            and (per.equip_model_name like concat('%',#{equipModelOrFactory},'%') or per.equip_factory_name like
            concat('%',#{equipModelOrFactory},'%'))
        </if>
        <if test="equipStatus != null">
            and per.equip_status = #{equipStatus}
        </if>
        <if test="requiredFlag != null">
            and per.required_flag = #{requiredFlag}
        </if>
        <if test="equipRecordVsHdId != null">
            and pervh.equip_record_vs_hd_id = #{equipRecordVsHdId}
        </if>
        order by pervh.create_time desc
    </select>

    <select id="getNotApplyRecordCount" resultType="java.lang.Integer">
        select count(1)
        from csm.proj_equip_record_vs_hd rvh
        left join csm.proj_equip_record per on rvh.equip_record_id = per.equip_record_id
        where rvh.is_deleted = 0
        and per.is_deleted = 0
        and per.required_flag = 1
        and per.project_info_id = #{projectInfoId}
        and per.equip_status in
        <foreach collection="statusList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findToMsunEquipRecord" parameterType="com.msun.csm.model.dto.ProjEquipSendToCloudDTO"
            resultType="com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto">
        select rvh.equip_record_vs_hd_id as id,
        phi.cloud_hospital_id as hospitalId,
        phi.org_id as hisOrgId,
        'HD' as productCode,
        per.equip_type_id as deviceId,
        per.equip_type_name as deviceName,
        per.equip_factory_name as firm,
        per.equip_model_name as model,
        per.comm_mode_key as communication,
        per.equip_position as location,
        per.equip_factory_phone as firmTel,
        per.equip_position as regionalEquipment,
        per.equip_class_id as monitorClass,
        per.cloud_equip_id as oldEquipId,
        per.yy_product_id as productId,
        rvh.equip_num as equipmentNumber
        from csm.proj_equip_record_vs_hd rvh
        left join csm.proj_equip_record per on rvh.equip_record_id = per.equip_record_id
        left join csm.proj_hospital_info phi on per.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        where per.equip_status != 5
        <if test="projectInfoId !=null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="equipRecordVsProductId != null">
            and rvh.equip_record_vs_hd_id = #{equipRecordVsProductId}
        </if>
        <if test="equipRecordVsProductIds != null and equipRecordVsProductIds.size() > 0">
            and rvh.equip_record_vs_hd_id in
            <foreach collection="equipRecordVsProductIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and rvh.is_deleted = 0
        and per.is_deleted = 0
    </select>

    <update id="updateSetdToMusnStatus">
        update csm.proj_equip_record_vs_hd
        set send_cloud_flag = 1
        where equip_record_vs_hd_id in
        <foreach collection="equipRecordVsProductIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getNoMappingCount" parameterType="com.msun.csm.model.dto.ProjEquipSendToCloudDTO"
            resultType="java.lang.Integer">
        select count(1)
        from csm.proj_equip_record_vs_hd rvh
        left join csm.proj_equip_record per on rvh.equip_record_id = per.equip_record_id
        where per.project_info_id = #{projectInfoId}
        and per.required_flag = 1
        and rvh.send_cloud_flag = 0
        and rvh.is_deleted = 0
        and per.is_deleted = 0
        <if test="equipRecordVsProductId != null">
            and rvh.equip_record_vs_hd_id = #{equipRecordVsProductId}
        </if>
    </select>

    <select id="selectOldEquipDataToHd" parameterType="long" resultType="com.msun.csm.model.dto.SendCsmEquipToOldHdDTO">
        select distinct
        tpnvo.new_custom_info_id as customInfoId, -- 新系统客户id
        tpnvo.old_custom_id, -- 老系统客户id
        tpnvo.new_project_info_id, -- 新系统项目id
        tpnvo.old_project_info_id, -- oldProjectInfoId
        es.id, --设备id
        es.factory_name_id as dictEquipFactoryId, -- 字典的厂商id
        def.equip_factory_name as dictEquipFactoryName, -- 字典的厂商名称
        es.real_factory_name as equipFactoryName, -- 手动输入的厂商名称
        es.type_name_id as dictEquipTypeId, -- 字典的设备类型id
        dec2.equip_class_name as dictEquipTypeName, -- 字典的设备类型名称
        es.real_type_name as equipTypeName, -- 手动输入的设备类型
        es.model_name_id as dictEquipModelId, -- 字典的设备型号id
        den.equip_name as dictEquipModelName, -- 字典的设备型号名称
        es.real_model_name as equipModelName, -- 手动输入的设备型号
        es.communication_mode as commMode, -- 通讯方式
        es.dup_device as isDouble, -- 是否双工
        es.buff_join as requiredFlag, -- 是否对接
        es.reason as stopReason, -- 不对接原因
        es.status as equipStatus , -- 设备状态
        es.location as equipPosition , -- 设备位置
        es.is_emergency as isEmergency, -- 是否急诊
        es.is_quality_control as isQuality, -- 是否质控
        es.factory_phone as equipFactoryPhone, -- 厂商电话
        es.remarks as memo, -- 备注
        es.is_sent , -- 发送云健康状态
        es.apply_submit_time as applyTime, -- 申请提交时间
        es.old_equip_id as cloudEquipId, -- 云健康设备id
        es.old_yun_model_name as cloudEquipName, -- 云健康设备名称
        es.equipment_number as equipNum, -- 设备编号
        eii.state as equipTestStstus, -- 设备调试状态
        es.check_result as checkResult, -- 检测结果
        es.customer_info_id as hospitalInfoId, -- 老系统的来源医院id【数据迁移时需要进行转换为csm】
        es.create_id as createId, -- 创建人【老系统中人员id】
        es.create_time -- 创建时间
        from platform.equip_search es
        left join anay.dict_equip_factory def on es.factory_name_id = def.equip_factory_id
        left join anay.dict_equip_class dec2 on es.type_name_id = dec2.equip_class_id
        left join anay.dict_equip_name den on es.model_name_id = den.id
        left join platform.equip_item_info eii on es.id = eii.equip_id
        left join platform.project p on es.project_id = p.id
        left join comm.customer_info ci on es.customer_info_id = ci.id
        left join csm.tmp_project_new_vs_old tpnvo on tpnvo.old_custom_id = es.customer_id and tpnvo.old_project_info_id
        = es.project_id
        where es.delete_flag = '0' and es.product_id = 310
        <if test="projectInfoId != -1">
            and tpnvo.new_project_info_id = #{projectInfoId}
        </if>
        <if test="oldEquipId != -1">
            and es.id = #{oldEquipId}
        </if>
    </select>

    <select id="selectHasHdProject" resultType="long" parameterType="long">
        select ppi.project_info_id
        from csm.proj_project_info ppi
                 left join csm.proj_order_product pop on ppi.project_info_id = pop.project_info_id
        where ppi.custom_info_id = #{customInfoId}
          and pop.yy_order_product_id in (4051)
    </select>
</mapper>
