<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjTodoTaskMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjTodoTask">
        <!--@mbg.generated-->
        <!--@Table csm.proj_todo_task-->
        <id column="todo_task_id" jdbcType="BIGINT" property="todoTaskId"/>
        <result column="project_plan_id" jdbcType="BIGINT" property="projectPlanId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="plan_time" jdbcType="DATE" property="planTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="implementation_engineer_id" jdbcType="BIGINT" property="implementationEngineerId"/>
        <result column="backend_engineer_id" jdbcType="BIGINT" property="backendEngineerId"/>
        <result column="complete_count" jdbcType="SMALLINT" property="completeCount"/>
        <result column="total_count" jdbcType="SMALLINT" property="totalCount"/>
        <result column="jump_type" jdbcType="SMALLINT" property="jumpType"/>
        <result column="jump_path" jdbcType="VARCHAR" property="jumpPath"/>
        <result column="attention_flag" jdbcType="SMALLINT" property="attentionFlag"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="front_flag" jdbcType="SMALLINT" property="frontFlag"/>
        <result column="backend_flag" jdbcType="SMALLINT" property="backendFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        todo_task_id, project_plan_id, project_info_id, yy_product_id, title, description, sort,
        plan_time, "status", implementation_engineer_id, backend_engineer_id, complete_count,
        total_count, jump_type, jump_path, attention_flag, creater_id, create_time, updater_id,
        update_time, is_deleted, front_flag, backend_flag, hospital_info_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_todo_task
        where todo_task_id = #{todoTaskId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_todo_task
        where todo_task_id = #{todoTaskId,jdbcType=BIGINT}
    </delete>
    <insert id="addTodoTask" parameterType="com.msun.csm.dao.entity.proj.ProjTodoTask">
        insert into csm.proj_todo_task (todo_task_id,
                                        project_plan_id,
                                        project_info_id,
                                        yy_product_id,
                                        title,
                                        description,
                                        sort,
                                        plan_time,
                                        "status",
                                        implementation_engineer_id,
                                        backend_engineer_id,
                                        complete_count,
                                        total_count,
                                        jump_type,
                                        jump_path,
                                        attention_flag,
                                        creater_id,
                                        create_time,
                                        updater_id,
                                        update_time,
                                        is_deleted,
                                        front_flag,
                                        hospital_info_id,
                                        backend_flag)
        values (#{todoTaskId,jdbcType=BIGINT},
                #{projectPlanId,jdbcType=BIGINT},
                #{projectInfoId,jdbcType=BIGINT},
                #{yyProductId,jdbcType=BIGINT},
                #{title,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR},
                #{sort,jdbcType=SMALLINT},
                #{planTime,jdbcType=DATE},
                #{status,jdbcType=SMALLINT},
                #{implementationEngineerId,jdbcType=BIGINT},
                #{backendEngineerId,jdbcType=BIGINT},
                #{completeCount,jdbcType=SMALLINT},
                #{totalCount,jdbcType=SMALLINT},
                #{jumpType,jdbcType=SMALLINT},
                #{jumpPath,jdbcType=VARCHAR},
                #{attentionFlag,jdbcType=SMALLINT},
                #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT},
                #{updateTime,jdbcType=TIMESTAMP},
                #{isDeleted,jdbcType=SMALLINT},
                #{frontFlag,jdbcType=SMALLINT},
                #{hospitalInfoId,jdbcType=BIGINT},
                #{backendFlag,jdbcType=SMALLINT})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjTodoTask">
        <!--@mbg.generated-->
        update csm.proj_todo_task
        <set>
            <if test="projectPlanId != null">
                project_plan_id = #{projectPlanId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="planTime != null">
                plan_time = #{planTime,jdbcType=DATE},
            </if>
            <if test="status != null">
                "status" = #{status,jdbcType=SMALLINT},
            </if>
            <if test="implementationEngineerId != null">
                implementation_engineer_id = #{implementationEngineerId,jdbcType=BIGINT},
            </if>
            <if test="backendEngineerId != null">
                backend_engineer_id = #{backendEngineerId,jdbcType=BIGINT},
            </if>
            <if test="completeCount != null">
                complete_count = #{completeCount,jdbcType=SMALLINT},
            </if>
            <if test="totalCount != null">
                total_count = #{totalCount,jdbcType=SMALLINT},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType,jdbcType=SMALLINT},
            </if>
            <if test="jumpPath != null">
                jump_path = #{jumpPath,jdbcType=VARCHAR},
            </if>
            <if test="attentionFlag != null">
                attention_flag = #{attentionFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="frontFlag != null">
                front_flag = #{frontFlag,jdbcType=SMALLINT},
            </if>
            <if test="backendFlag != null">
                backend_flag = #{backendFlag,jdbcType=SMALLINT},
            </if>
            <if test="completionUserId != null">
                completion_user_id = #{completionUserId,jdbcType=BIGINT},
            </if>
        </set>
        where todo_task_id = #{todoTaskId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_todo_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_plan_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.projectPlanId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="title = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.title,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.sort,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="plan_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT}
                    then TO_DATE(#{item.planTime,jdbcType=TIMESTAMP}, 'YYYY-MM-DD')
                </foreach>
            </trim>
            <trim prefix="&quot;status&quot; = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.status,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="implementation_engineer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.implementationEngineerId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="backend_engineer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.backendEngineerId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="complete_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.completeCount,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.totalCount,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="jump_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.jumpType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="jump_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.jumpPath,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="attention_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.attentionFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT}
                    then TO_TIMESTAMP(#{item.createTime,jdbcType=TIMESTAMP}, 'YYYY-MM-DD HH24:MI:SS')
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT}
                    then TO_TIMESTAMP(#{item.updateTime,jdbcType=TIMESTAMP}, 'YYYY-MM-DD HH24:MI:SS')
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="front_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.frontFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="backend_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when todo_task_id = #{item.todoTaskId,jdbcType=BIGINT} then #{item.backendFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where todo_task_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.todoTaskId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_todo_task
        (todo_task_id, project_plan_id, project_info_id, yy_product_id, title, description, sort,
        plan_time, "status", implementation_engineer_id, backend_engineer_id, complete_count,
        total_count, jump_type, jump_path, attention_flag, creater_id, create_time, updater_id,
        update_time, is_deleted, front_flag, backend_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.todoTaskId,jdbcType=BIGINT}, #{item.projectPlanId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.yyProductId,jdbcType=BIGINT}, #{item.title,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
            #{item.sort,jdbcType=SMALLINT}, #{item.planTime,jdbcType=DATE}, #{item.status,jdbcType=SMALLINT},
            #{item.implementationEngineerId,jdbcType=BIGINT}, #{item.backendEngineerId,jdbcType=BIGINT},
            #{item.completeCount,jdbcType=SMALLINT}, #{item.totalCount,jdbcType=SMALLINT},
            #{item.jumpType,jdbcType=SMALLINT}, #{item.jumpPath,jdbcType=VARCHAR}, #{item.attentionFlag,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT}, #{item.frontFlag,jdbcType=SMALLINT},
            #{item.backendFlag,jdbcType=SMALLINT})
        </foreach>
    </insert>

    <update id="deleteBatchPIds">
        update proj_todo_task
        set is_deleted = 1
        where proj_todo_task.project_plan_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="queryData" resultType="com.msun.csm.model.resp.todotask.TodoTaskResp">
        select ptt.*,
        case
        when dp.product_name is not null then dp.product_name
        when dpvm.yy_module_name is not null then dpvm.yy_module_name
        else dp.product_name
        end as "productName",
        impl.user_name as front_executor_name,
        back.user_name as backend_executor_name,
        cre.user_name as creater_name,
        completion.user_name as completion_user_name,
        upd.user_name as updater_name,
        dpps.project_plan_stage_name as project_plan_stage_name,
        dpps.project_plan_stage_code as project_plan_stage_code,
        dpps.project_plan_stage_id as "projectPlanStageId",
        dppi.project_plan_item_code as project_plan_item_code,
        ppp.plan_type as plan_type,
        case
        when ptt.status = 0 then '未开始'
        when ptt.status = 2 then '进行中'
        else '已完成' end as status_name,
        ptt.hospital_info_id ,
        phi.hospital_name,
        dppi.web_detail_code as "webDetailCode",
        dppi.progress_display_mode as "progressDisplayMode"
        from csm.proj_todo_task ptt
        left join csm.sys_user impl on ptt.implementation_engineer_id = impl.sys_user_id
        left join csm.sys_user completion on ptt.completion_user_id = completion.sys_user_id
        left join csm.sys_user back on ptt.backend_engineer_id = back.sys_user_id
        left join csm.sys_user cre on ptt.creater_id = cre.sys_user_id
        left join csm.sys_user upd on ptt.updater_id = upd.sys_user_id
        left join csm.proj_project_plan ppp on ptt.project_plan_id = ppp.project_plan_id
        left join csm.dict_project_plan_stage dpps on ppp.project_plan_stage_id = dpps.project_plan_stage_id
        left join csm.dict_project_plan_item dppi on dppi.project_plan_item_id = ppp.project_plan_item_id
        left join csm.dict_product dp on dp.yy_product_id = ptt.yy_product_id and dp.is_deleted = 0
        left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = ptt.yy_product_id and dpvm.is_deleted = 0
        left join csm.proj_hospital_info phi on ptt.hospital_info_id =phi.hospital_info_id
        where ptt.is_deleted = 0
        and ppp.project_plan_id is not null
        and ptt.project_info_id = #{req.projectInfoId}
        <if test="req.projectPlanId != null">
            and ptt.project_plan_id = #{req.projectPlanId}
        </if>
        <if test="req.hospitalInfoIdList != null and req.hospitalInfoIdList.size() > 0">
            and ptt.hospital_info_id in
            <foreach close=")" collection="req.hospitalInfoIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="req.status != null and req.status == 0">
            and ptt.status != 1
        </if>
        <if test="req.status != null and req.status == 1">
            and ptt.status = 1
        </if>
        <if test="req.status != null and req.status == 2">
            and ptt.status = 2
        </if>
        <if test="req.frontOrBackendFlag != null and req.frontOrBackendFlag == 1">
            and ptt.front_flag = 1
        </if>
        <if test="req.frontOrBackendFlag != null and req.frontOrBackendFlag == 2">
            and ptt.backend_flag = 1
        </if>
        <if test="req.overTimeFlag != null and req.overTimeFlag == 1">
            and ptt.plan_time &lt; now()
        </if>
        <if test="req.attentionFlag != null">
            and ptt.attention_flag = #{req.attentionFlag}
        </if>
        <if test="req.keyWord != null and req.keyWord != ''">
            and (ptt.title like CONCAT('%', #{req.keyWord}, '%')
            or ptt.description like CONCAT('%', #{req.keyWord}, '%'))
        </if>
        <if test="req.projectPlanStageIdList != null and req.projectPlanStageIdList.size > 0">
            and ppp.project_plan_stage_id in
            <foreach collection="req.projectPlanStageIdList" item="stageId" separator="," open="(" close=")">
                #{stageId}
            </foreach>
        </if>
        order by ptt.project_plan_id, ptt.sort asc
    </select>


    <select id="selectByPlanAndProduct" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from proj_todo_task
        where project_plan_id = #{projectPlanId}
        <if test="hospitalInfoId != null">
            and hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null and yyProductId != -1 ">
            and yy_product_id = #{yyProductId}
        </if>
        <if test="userId != null and userId != -1 ">
            and (implementation_engineer_id = #{userId} or backend_engineer_id = #{userId} )
        </if>
        and is_deleted = 0
    </select>

    <select id="queryMaxSort" resultType="java.lang.Integer">
        select max(sort)
        from proj_todo_task
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
    </select>


    <select id="getTodoTaskByProjectAndPlan" resultMap="BaseResultMap">
        select *
        from csm.proj_todo_task
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and project_plan_id = #{planId}
    </select>

    <select id="selectTodoTaskByTaskIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_todo_task
        where is_deleted = 0
        and todo_task_id in
        <foreach collection="todoTaskIds" item="todoTaskId" open="(" separator="," close=")">
            #{todoTaskId}
        </foreach>
    </select>

    <select id="getTodoTaskByPlanIdList" resultType="com.msun.csm.model.dto.ProjTodoTaskVsPlan">
        select
        ptt.*,
        ppp.project_plan_item_id
        from csm.proj_todo_task ptt
        left join csm.proj_project_plan ppp on ptt.project_plan_id = ppp.project_plan_id
        where ptt.is_deleted = 0
        and ptt.project_plan_id in
        <foreach collection="planIds" item="planId" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </select>
</mapper>
