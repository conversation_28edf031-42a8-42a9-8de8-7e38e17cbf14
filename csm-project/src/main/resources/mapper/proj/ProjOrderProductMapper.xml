<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjOrderProductMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        <!--@Table csm.proj_order_product-->
        <id column="order_product_id" jdbcType="BIGINT" property="orderProductId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="order_info_id" jdbcType="BIGINT" property="orderInfoId"/>
        <result column="product_buy_mode" jdbcType="VARCHAR" property="productBuyMode"/>
        <result column="product_settle_status" jdbcType="VARCHAR" property="productSettleStatus"/>
        <result column="product_settle_prop" jdbcType="NUMERIC" property="productSettleProp"/>
        <result column="product_settle_amount" jdbcType="NUMERIC" property="productSettleAmount"/>
        <result column="yy_order_product_id" jdbcType="BIGINT" property="yyOrderProductId"/>
        <result column="product_subscribe_term" jdbcType="BIGINT" property="productSubscribeTerm"/>
        <result column="product_subscribe_time" jdbcType="TIMESTAMP" property="productSubscribeTime"/>
        <result column="product_subscribe_status" jdbcType="SMALLINT" property="productSubscribeStatus"/>
        <result column="product_subscribe_type" jdbcType="SMALLINT" property="productSubscribeType"/>
        <result column="product_excution_status" jdbcType="SMALLINT" property="productExcutionStatus"/>
        <result column="product_open_status" jdbcType="SMALLINT" property="productOpenStatus"/>
        <result column="product_resolve_type_id" jdbcType="BIGINT" property="productResolveTypeId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="arrange_status" jdbcType="SMALLINT" property="arrangeStatus"/>
        <result column="empower_status" jdbcType="SMALLINT" property="empowerStatus"/>
        <result column="yy_proj_id" jdbcType="INTEGER" property="yyProjId"/>
        <result column="yy_order_product_number" jdbcType="INTEGER" property="yyOrderProductNumber"/>
        <result column="yy_contract_id" jdbcType="BIGINT" property="yyContractId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        order_product_id,
        project_info_id,
        order_info_id,
        product_buy_mode,
        product_settle_status,
        product_settle_prop,
        product_settle_amount,
        yy_order_product_id,
        product_subscribe_term,
        product_subscribe_time,
        product_subscribe_status,
        product_subscribe_type,
        product_excution_status,
        product_open_status,
        product_resolve_type_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        arrange_status,
        empower_status,
        yy_proj_id,
        yy_order_product_number,
        yy_contract_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_order_product
        where order_product_id = #{orderProductId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.proj_order_product
        where order_product_id = #{orderProductId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        insert into csm.proj_order_product (order_product_id, project_info_id, order_info_id,
        product_buy_mode, product_settle_status, product_settle_prop,
        product_settle_amount, yy_order_product_id, product_subscribe_term,
        product_subscribe_time, product_subscribe_status,
        product_subscribe_type, product_excution_status,
        product_open_status, product_resolve_type_id,
        is_deleted, creater_id, create_time,
        updater_id, update_time, arrange_status,
        empower_status, yy_proj_id, yy_order_product_number,
        yy_contract_id, is_special, special_id)
        values (#{orderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{orderInfoId,jdbcType=BIGINT},
        #{productBuyMode,jdbcType=VARCHAR}, #{productSettleStatus,jdbcType=VARCHAR},
        #{productSettleProp,jdbcType=NUMERIC},
        #{productSettleAmount,jdbcType=NUMERIC}, #{yyOrderProductId,jdbcType=BIGINT},
        #{productSubscribeTerm,jdbcType=BIGINT},
        #{productSubscribeTime,jdbcType=TIMESTAMP}, #{productSubscribeStatus,jdbcType=SMALLINT},
        #{productSubscribeType,jdbcType=SMALLINT}, #{productExcutionStatus,jdbcType=SMALLINT},
        #{productOpenStatus,jdbcType=SMALLINT}, #{productResolveTypeId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{arrangeStatus,jdbcType=SMALLINT},
        #{empowerStatus,jdbcType=SMALLINT}, #{yyProjId,jdbcType=INTEGER},
        #{yyOrderProductNumber,jdbcType=INTEGER},
        #{yyContractId,jdbcType=BIGINT}, #{isSpecial,jdbcType=BOOLEAN}, #{specialId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        insert into csm.proj_order_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="productBuyMode != null">
                product_buy_mode,
            </if>
            <if test="productSettleStatus != null">
                product_settle_status,
            </if>
            <if test="productSettleProp != null">
                product_settle_prop,
            </if>
            <if test="productSettleAmount != null">
                product_settle_amount,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="productSubscribeTerm != null">
                product_subscribe_term,
            </if>
            <if test="productSubscribeTime != null">
                product_subscribe_time,
            </if>
            <if test="productSubscribeStatus != null">
                product_subscribe_status,
            </if>
            <if test="productSubscribeType != null">
                product_subscribe_type,
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status,
            </if>
            <if test="productOpenStatus != null">
                product_open_status,
            </if>
            <if test="productResolveTypeId != null">
                product_resolve_type_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="arrangeStatus != null">
                arrange_status,
            </if>
            <if test="empowerStatus != null">
                empower_status,
            </if>
            <if test="yyProjId != null">
                yy_proj_id,
            </if>
            <if test="yyOrderProductNumber != null">
                yy_order_product_number,
            </if>
            <if test="yyContractId != null">
                yy_contract_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="productBuyMode != null">
                #{productBuyMode,jdbcType=VARCHAR},
            </if>
            <if test="productSettleStatus != null">
                #{productSettleStatus,jdbcType=VARCHAR},
            </if>
            <if test="productSettleProp != null">
                #{productSettleProp,jdbcType=NUMERIC},
            </if>
            <if test="productSettleAmount != null">
                #{productSettleAmount,jdbcType=NUMERIC},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTerm != null">
                #{productSubscribeTerm,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTime != null">
                #{productSubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeStatus != null">
                #{productSubscribeStatus,jdbcType=SMALLINT},
            </if>
            <if test="productSubscribeType != null">
                #{productSubscribeType,jdbcType=SMALLINT},
            </if>
            <if test="productExcutionStatus != null">
                #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                #{productOpenStatus,jdbcType=SMALLINT},
            </if>
            <if test="productResolveTypeId != null">
                #{productResolveTypeId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                #{arrangeStatus,jdbcType=SMALLINT},
            </if>
            <if test="empowerStatus != null">
                #{empowerStatus,jdbcType=SMALLINT},
            </if>
            <if test="yyProjId != null">
                #{yyProjId,jdbcType=INTEGER},
            </if>
            <if test="yyOrderProductNumber != null">
                #{yyOrderProductNumber,jdbcType=INTEGER},
            </if>
            <if test="yyContractId != null">
                #{yyContractId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        update csm.proj_order_product
        <set>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="orderInfoId != null">
                order_info_id = #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="productBuyMode != null">
                product_buy_mode = #{productBuyMode,jdbcType=VARCHAR},
            </if>
            <if test="productSettleStatus != null">
                product_settle_status = #{productSettleStatus,jdbcType=VARCHAR},
            </if>
            <if test="productSettleProp != null">
                product_settle_prop = #{productSettleProp,jdbcType=NUMERIC},
            </if>
            <if test="productSettleAmount != null">
                product_settle_amount = #{productSettleAmount,jdbcType=NUMERIC},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTerm != null">
                product_subscribe_term = #{productSubscribeTerm,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTime != null">
                product_subscribe_time = #{productSubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeStatus != null">
                product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT},
            </if>
            <if test="productSubscribeType != null">
                product_subscribe_type = #{productSubscribeType,jdbcType=SMALLINT},
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                product_open_status = #{productOpenStatus,jdbcType=SMALLINT},
            </if>
            <if test="productResolveTypeId != null">
                product_resolve_type_id = #{productResolveTypeId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
            </if>
            <if test="empowerStatus != null">
                empower_status = #{empowerStatus,jdbcType=SMALLINT},
            </if>
            <if test="yyProjId != null">
                yy_proj_id = #{yyProjId,jdbcType=INTEGER},
            </if>
            <if test="yyOrderProductNumber != null">
                yy_order_product_number = #{yyOrderProductNumber,jdbcType=INTEGER},
            </if>
            <if test="yyContractId != null">
                yy_contract_id = #{yyContractId,jdbcType=BIGINT},
            </if>
        </set>
        where order_product_id = #{orderProductId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        update csm.proj_order_product
        set project_info_id          = #{projectInfoId,jdbcType=BIGINT},
            order_info_id            = #{orderInfoId,jdbcType=BIGINT},
            product_buy_mode         = #{productBuyMode,jdbcType=VARCHAR},
            product_settle_status    = #{productSettleStatus,jdbcType=VARCHAR},
            product_settle_prop      = #{productSettleProp,jdbcType=NUMERIC},
            product_settle_amount    = #{productSettleAmount,jdbcType=NUMERIC},
            yy_order_product_id      = #{yyOrderProductId,jdbcType=BIGINT},
            product_subscribe_term   = #{productSubscribeTerm,jdbcType=BIGINT},
            product_subscribe_time   = #{productSubscribeTime,jdbcType=TIMESTAMP},
            product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT},
            product_subscribe_type   = #{productSubscribeType,jdbcType=SMALLINT},
            product_excution_status  = #{productExcutionStatus,jdbcType=SMALLINT},
            product_open_status      = #{productOpenStatus,jdbcType=SMALLINT},
            product_resolve_type_id  = #{productResolveTypeId,jdbcType=BIGINT},
            is_deleted               = #{isDeleted,jdbcType=SMALLINT},
            creater_id               = #{createrId,jdbcType=BIGINT},
            create_time              = #{createTime,jdbcType=TIMESTAMP},
            updater_id               = #{updaterId,jdbcType=BIGINT},
            update_time              = #{updateTime,jdbcType=TIMESTAMP},
            arrange_status           = #{arrangeStatus,jdbcType=SMALLINT},
            empower_status           = #{empowerStatus,jdbcType=SMALLINT},
            yy_proj_id               = #{yyProjId,jdbcType=INTEGER},
            yy_order_product_number  = #{yyOrderProductNumber,jdbcType=INTEGER},
            yy_contract_id           = #{yyContractId,jdbcType=BIGINT}
        where order_product_id = #{orderProductId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_order_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="order_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.orderInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_buy_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productBuyMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_settle_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSettleStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_settle_prop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSettleProp,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="product_settle_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSettleAmount,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.yyOrderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_term = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSubscribeTerm,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSubscribeTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSubscribeStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productSubscribeType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="product_excution_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productExcutionStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="product_open_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productOpenStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="product_resolve_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.productResolveTypeId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="arrange_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.arrangeStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="empower_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.empowerStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="yy_proj_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.yyProjId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="yy_order_product_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.yyOrderProductNumber,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="yy_contract_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        #{item.yyContractId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderProductId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_order_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderInfoId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.orderInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_buy_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productBuyMode != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productBuyMode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_settle_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSettleStatus != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSettleStatus,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_settle_prop = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSettleProp != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSettleProp,jdbcType=NUMERIC}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_settle_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSettleAmount != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSettleAmount,jdbcType=NUMERIC}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyOrderProductId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.yyOrderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_subscribe_term = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSubscribeTerm != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSubscribeTerm,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_subscribe_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSubscribeTime != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSubscribeTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_subscribe_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSubscribeStatus != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSubscribeStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_subscribe_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productSubscribeType != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productSubscribeType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_excution_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productExcutionStatus != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productExcutionStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_open_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productOpenStatus != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productOpenStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_resolve_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productResolveTypeId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.productResolveTypeId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="arrange_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.arrangeStatus != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.arrangeStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="empower_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.empowerStatus != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.empowerStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_proj_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProjId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.yyProjId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_order_product_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyOrderProductNumber != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.yyOrderProductNumber,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_contract_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyContractId != null">
                        when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                            #{item.yyContractId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderProductId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_order_product
        (order_product_id, project_info_id, order_info_id, product_buy_mode, product_settle_status,
         product_settle_prop, product_settle_amount, yy_order_product_id, product_subscribe_term,
         product_subscribe_time, product_subscribe_status, product_subscribe_type, product_excution_status,
         product_open_status, product_resolve_type_id, is_deleted, creater_id, create_time,
         updater_id, update_time, arrange_status, empower_status, yy_proj_id, yy_order_product_number,
         yy_contract_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderProductId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
             #{item.orderInfoId,jdbcType=BIGINT},
             #{item.productBuyMode,jdbcType=VARCHAR}, #{item.productSettleStatus,jdbcType=VARCHAR},
             #{item.productSettleProp,jdbcType=NUMERIC}, #{item.productSettleAmount,jdbcType=NUMERIC},
             #{item.yyOrderProductId,jdbcType=BIGINT}, #{item.productSubscribeTerm,jdbcType=BIGINT},
             #{item.productSubscribeTime,jdbcType=TIMESTAMP}, #{item.productSubscribeStatus,jdbcType=SMALLINT},
             #{item.productSubscribeType,jdbcType=SMALLINT}, #{item.productExcutionStatus,jdbcType=SMALLINT},
             #{item.productOpenStatus,jdbcType=SMALLINT}, #{item.productResolveTypeId,jdbcType=BIGINT},
             #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.arrangeStatus,jdbcType=SMALLINT},
             #{item.empowerStatus,jdbcType=SMALLINT}, #{item.yyProjId,jdbcType=INTEGER},
             #{item.yyOrderProductNumber,jdbcType=INTEGER},
             #{item.yyContractId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        insert into csm.proj_order_product
        (order_product_id, project_info_id, order_info_id, product_buy_mode, product_settle_status,
         product_settle_prop, product_settle_amount, yy_order_product_id, product_subscribe_term,
         product_subscribe_time, product_subscribe_status, product_subscribe_type, product_excution_status,
         product_open_status, product_resolve_type_id, is_deleted, creater_id, create_time,
         updater_id, update_time, arrange_status, empower_status, yy_proj_id, yy_order_product_number,
         yy_contract_id)
        values (#{orderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{orderInfoId,jdbcType=BIGINT},
                #{productBuyMode,jdbcType=VARCHAR}, #{productSettleStatus,jdbcType=VARCHAR},
                #{productSettleProp,jdbcType=NUMERIC},
                #{productSettleAmount,jdbcType=NUMERIC}, #{yyOrderProductId,jdbcType=BIGINT},
                #{productSubscribeTerm,jdbcType=BIGINT},
                #{productSubscribeTime,jdbcType=TIMESTAMP}, #{productSubscribeStatus,jdbcType=SMALLINT},
                #{productSubscribeType,jdbcType=SMALLINT}, #{productExcutionStatus,jdbcType=SMALLINT},
                #{productOpenStatus,jdbcType=SMALLINT}, #{productResolveTypeId,jdbcType=BIGINT},
                #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{arrangeStatus,jdbcType=SMALLINT},
                #{empowerStatus,jdbcType=SMALLINT}, #{yyProjId,jdbcType=INTEGER},
                #{yyOrderProductNumber,jdbcType=INTEGER},
                #{yyContractId,jdbcType=BIGINT})
        on duplicate key update
            order_product_id = #{orderProductId,jdbcType=BIGINT}, project_info_id = #{projectInfoId,jdbcType=BIGINT}, order_info_id = #{orderInfoId,jdbcType=BIGINT}, product_buy_mode = #{productBuyMode,jdbcType=VARCHAR}, product_settle_status = #{productSettleStatus,jdbcType=VARCHAR}, product_settle_prop = #{productSettleProp,jdbcType=NUMERIC}, product_settle_amount = #{productSettleAmount,jdbcType=NUMERIC}, yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT}, product_subscribe_term = #{productSubscribeTerm,jdbcType=BIGINT}, product_subscribe_time = #{productSubscribeTime,jdbcType=TIMESTAMP}, product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT}, product_subscribe_type = #{productSubscribeType,jdbcType=SMALLINT}, product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT}, product_open_status = #{productOpenStatus,jdbcType=SMALLINT}, product_resolve_type_id = #{productResolveTypeId,jdbcType=BIGINT}, is_deleted = #{isDeleted,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, arrange_status = #{arrangeStatus,jdbcType=SMALLINT}, empower_status = #{empowerStatus,jdbcType=SMALLINT}, yy_proj_id = #{yyProjId,jdbcType=INTEGER}, yy_order_product_number = #{yyOrderProductNumber,jdbcType=INTEGER}, yy_contract_id = #{yyContractId,jdbcType=BIGINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjOrderProduct">
        <!--@mbg.generated-->
        insert into csm.proj_order_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="productBuyMode != null">
                product_buy_mode,
            </if>
            <if test="productSettleStatus != null">
                product_settle_status,
            </if>
            <if test="productSettleProp != null">
                product_settle_prop,
            </if>
            <if test="productSettleAmount != null">
                product_settle_amount,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="productSubscribeTerm != null">
                product_subscribe_term,
            </if>
            <if test="productSubscribeTime != null">
                product_subscribe_time,
            </if>
            <if test="productSubscribeStatus != null">
                product_subscribe_status,
            </if>
            <if test="productSubscribeType != null">
                product_subscribe_type,
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status,
            </if>
            <if test="productOpenStatus != null">
                product_open_status,
            </if>
            <if test="productResolveTypeId != null">
                product_resolve_type_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="arrangeStatus != null">
                arrange_status,
            </if>
            <if test="empowerStatus != null">
                empower_status,
            </if>
            <if test="yyProjId != null">
                yy_proj_id,
            </if>
            <if test="yyOrderProductNumber != null">
                yy_order_product_number,
            </if>
            <if test="yyContractId != null">
                yy_contract_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="productBuyMode != null">
                #{productBuyMode,jdbcType=VARCHAR},
            </if>
            <if test="productSettleStatus != null">
                #{productSettleStatus,jdbcType=VARCHAR},
            </if>
            <if test="productSettleProp != null">
                #{productSettleProp,jdbcType=NUMERIC},
            </if>
            <if test="productSettleAmount != null">
                #{productSettleAmount,jdbcType=NUMERIC},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTerm != null">
                #{productSubscribeTerm,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTime != null">
                #{productSubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeStatus != null">
                #{productSubscribeStatus,jdbcType=SMALLINT},
            </if>
            <if test="productSubscribeType != null">
                #{productSubscribeType,jdbcType=SMALLINT},
            </if>
            <if test="productExcutionStatus != null">
                #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                #{productOpenStatus,jdbcType=SMALLINT},
            </if>
            <if test="productResolveTypeId != null">
                #{productResolveTypeId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                #{arrangeStatus,jdbcType=SMALLINT},
            </if>
            <if test="empowerStatus != null">
                #{empowerStatus,jdbcType=SMALLINT},
            </if>
            <if test="yyProjId != null">
                #{yyProjId,jdbcType=INTEGER},
            </if>
            <if test="yyOrderProductNumber != null">
                #{yyOrderProductNumber,jdbcType=INTEGER},
            </if>
            <if test="yyContractId != null">
                #{yyContractId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="orderInfoId != null">
                order_info_id = #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="productBuyMode != null">
                product_buy_mode = #{productBuyMode,jdbcType=VARCHAR},
            </if>
            <if test="productSettleStatus != null">
                product_settle_status = #{productSettleStatus,jdbcType=VARCHAR},
            </if>
            <if test="productSettleProp != null">
                product_settle_prop = #{productSettleProp,jdbcType=NUMERIC},
            </if>
            <if test="productSettleAmount != null">
                product_settle_amount = #{productSettleAmount,jdbcType=NUMERIC},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTerm != null">
                product_subscribe_term = #{productSubscribeTerm,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeTime != null">
                product_subscribe_time = #{productSubscribeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeStatus != null">
                product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT},
            </if>
            <if test="productSubscribeType != null">
                product_subscribe_type = #{productSubscribeType,jdbcType=SMALLINT},
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                product_open_status = #{productOpenStatus,jdbcType=SMALLINT},
            </if>
            <if test="productResolveTypeId != null">
                product_resolve_type_id = #{productResolveTypeId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
            </if>
            <if test="empowerStatus != null">
                empower_status = #{empowerStatus,jdbcType=SMALLINT},
            </if>
            <if test="yyProjId != null">
                yy_proj_id = #{yyProjId,jdbcType=INTEGER},
            </if>
            <if test="yyOrderProductNumber != null">
                yy_order_product_number = #{yyOrderProductNumber,jdbcType=INTEGER},
            </if>
            <if test="yyContractId != null">
                yy_contract_id = #{yyContractId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <sql id="selectSql">
        <where>
            1 = 1
              AND pop.is_deleted = 0
              AND poi.is_deleted = 0
            <if test="dto.productName != null and dto.productName != ''">
                and dpd.product_name like concat('%', #{dto.productName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dto.productOpenStatus != null">
                and pop.product_open_status = #{dto.productOpenStatus,jdbcType=SMALLINT}
            </if>
            <if test="dto.productExcutionStatus != null">
                and pop.product_excution_status = #{dto.productExcutionStatus,jdbcType=SMALLINT}
            </if>
            <if test="dto.customInfoId != null and dto.customInfoId != -1">
                and ppi.custom_info_id = #{dto.customInfoId,jdbcType=BIGINT}
            </if>
            <if test="dto.projectInfoId != null and dto.projectInfoId != -1">
                and ppi.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
            </if>
            <if test="dto.deliveryOrderNo != null and dto.deliveryOrderNo != ''">
                and poi.delivery_order_no = #{dto.deliveryOrderNo,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <select id="findProductList" resultType="com.msun.csm.dao.entity.proj.ProductInfo">
        select * from (
        with records as(
        select yy_order_product_id,
        string_agg(product_name, ', ') AS product_name
        from (
        SELECT re.yy_order_product_id, dp.product_name
        FROM csm.proj_product_arrange_record re
        left join csm.dict_product dp
        on dp.yy_product_id = re.product_arrange_id
        and dp.is_deleted = 0
        where re.is_deleted = 0
        <if test="dto.customInfoId != null and dto.customInfoId != -1">
            and re.custom_info_id = #{dto.customInfoId,jdbcType=BIGINT}
        </if>
        <if test="dto.projectInfoId != null and dto.projectInfoId != -1">
            and re.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
        </if>
        )AS expanded_tags
        GROUP BY yy_order_product_id
        )
        select dpd.product_dict_id as product_id,
        cast(pop.order_product_id as text) as order_product_id,
        dpd.product_name,
        poi.delivery_order_no,
        pop.product_excution_status,
        pop.product_open_status,
        pop.yy_order_product_id,
        pop.product_resolve_type_id,
        coalesce(rcd.product_name, dpd.product_name) as reProductName,
        act.contract_no as contractNo,
        pop.is_special,
        pop.create_time
        from csm.proj_order_product pop
        left join csm.dict_product dpd on
        pop.yy_order_product_id = dpd.yy_product_id
        left join csm.proj_order_info poi on
        pop.order_info_id = poi.order_info_id
        left join csm.proj_contract_info act on
        poi.contract_info_id = act.contract_info_id
        left join csm.proj_project_info ppi on
        pop.project_info_id = ppi.project_info_id
        left join records rcd on
        rcd.yy_order_product_id = pop.yy_order_product_id
        <include refid="selectSql"/>
        ) as tbl order by create_time asc
    </select>
    <select id="findSpecialProductList" resultType="com.msun.csm.dao.entity.proj.ProductInfo">
        select * from (
        with records as(
        select yy_order_product_id,
        string_agg(product_name, ', ') AS product_name
        from (
        SELECT re.yy_order_product_id, dp.product_name
        FROM csm.proj_product_arrange_record re
        left join csm.dict_product dp
        on dp.yy_product_id = re.product_arrange_id
        where re.is_deleted = 0
        and dp.is_deleted = 0
        <if test="dto.customInfoId != null and dto.customInfoId != -1">
            and re.custom_info_id = #{dto.customInfoId,jdbcType=BIGINT}
        </if>
        <if test="dto.projectInfoId != null and dto.projectInfoId != -1">
            and re.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
        </if>
        )AS expanded_tags
        GROUP BY yy_order_product_id
        )
        select dpd.product_dict_id as product_id,
        cast(pop.order_product_id as text) as order_product_id,
        dpd.product_name,
        poi.delivery_order_no,
        pop.product_excution_status,
        pop.product_open_status,
        pop.project_info_id,
        pop.yy_order_product_id yyOrderProductId,
        coalesce(rcd.product_name, dpd.product_name) as reProductName,
        act.contract_no as contractNo,
        pop.is_special,
        pop.create_time
        from csm.proj_order_product pop
        left join csm.dict_product dpd on
        pop.yy_order_product_id = dpd.yy_product_id
        left join csm.proj_order_info poi on
        pop.order_info_id = poi.order_info_id
        left join csm.proj_contract_info act on
        poi.contract_info_id = act.contract_info_id
        left join csm.proj_project_info ppi on
        pop.project_info_id = ppi.project_info_id
        left join records rcd on
        rcd.yy_order_product_id = pop.yy_order_product_id
        <include refid="selectSql">
        </include>
        and pop.is_special = true
        union all
        select pop.special_product_id as product_id,
        cast(pop.special_product_record_id as text) as order_product_id,
        dpd.product_name,
        poi.delivery_order_no as delivery_order_no,
        pop.product_excution_status,
        pop.product_open_status,
        pop.project_info_id,
        pop.special_product_id yyOrderProductId,
        coalesce(rcd.product_name, dpd.product_name) as reProductName,
        act.contract_no as contractNo,
        true as is_special,
        pop.create_time
        from csm.proj_special_product_record pop
        left join csm.proj_project_info ppi on
        pop.project_info_id = ppi.project_info_id
        left join csm.dict_product dpd on
        pop.special_product_id = dpd.yy_product_id
        left join csm.proj_order_info poi on
        ppi.order_info_id = poi.order_info_id
        left join csm.proj_contract_info act on
        poi.contract_info_id = act.contract_info_id
        left join records rcd on
        rcd.yy_order_product_id = pop.special_product_id
        <include refid="selectSql">
        </include>
        ) as tbl order by create_time asc
    </select>

    <select id="findAllList" resultType="com.msun.csm.dao.entity.proj.ProductInfo">
       select * from (
        with records as(
        select yy_order_product_id,
        string_agg(product_name, ', ') AS product_name
        from (
        SELECT re.yy_order_product_id, dp.product_name
        FROM csm.proj_product_arrange_record re
        left join csm.dict_product dp
        on dp.yy_product_id = re.product_arrange_id
        where re.is_deleted = 0
        and dp.is_deleted = 0
        <if test="dto.customInfoId != null and dto.customInfoId != -1">
            and re.custom_info_id = #{dto.customInfoId,jdbcType=BIGINT}
        </if>
        <if test="dto.projectInfoId != null and dto.projectInfoId != -1">
            and re.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
        </if>
        )AS expanded_tags
        GROUP BY yy_order_product_id
        )
        select dpd.product_dict_id as product_id,
        cast(pop.order_product_id as text) as order_product_id,
        dpd.product_name,
        poi.delivery_order_no,
        pop.product_excution_status,
        pop.product_open_status,
        pop.project_info_id,
        pop.yy_order_product_id yyOrderProductId,
        coalesce(rcd.product_name, dpd.product_name) as reProductName,
        act.contract_no as contractNo,
        pop.is_special,
        pop.create_time
        from csm.proj_order_product pop
        left join csm.dict_product dpd on
        pop.yy_order_product_id = dpd.yy_product_id
        left join csm.proj_order_info poi on
        pop.order_info_id = poi.order_info_id
        left join csm.proj_contract_info act on
        poi.contract_info_id = act.contract_info_id
        left join csm.proj_project_info ppi on
        pop.project_info_id = ppi.project_info_id
        left join records rcd on
        rcd.yy_order_product_id = pop.yy_order_product_id
        <include refid="selectSql">
        </include>
        union all
        select pop.special_product_id as product_id,
        cast(pop.special_product_record_id as text) as order_product_id,
        dpd.product_name,
        poi.delivery_order_no as delivery_order_no,
        pop.product_excution_status,
        pop.product_open_status,
        pop.project_info_id,
        pop.special_product_id yyOrderProductId,
        coalesce(rcd.product_name, dpd.product_name) as reProductName,
        act.contract_no as contractNo,
        true as is_special,
        pop.create_time
        from csm.proj_special_product_record pop
        left join csm.proj_project_info ppi on
        pop.project_info_id = ppi.project_info_id
        left join csm.dict_product dpd on
        pop.special_product_id = dpd.yy_product_id
        left join csm.proj_order_info poi on
        ppi.order_info_id = poi.order_info_id
        left join csm.proj_contract_info act on
        poi.contract_info_id = act.contract_info_id
        left join records rcd on
        rcd.yy_order_product_id = pop.special_product_id
        <include refid="selectSql">
        </include>
                     ) as tbl order by create_time asc
    </select>

    <select id="countByProjectId" resultType="com.msun.csm.model.resp.project.ProjectProductCountResp">
        select count(product_id)                                    as product_count,
               tmp.project_info_id                                  as project_info_id,
               STRING_AGG(product_name, ', ' ORDER BY product_name) AS product_names
        from
        (select pop.yy_order_product_id as product_id,
                pop.project_info_id     as project_info_id,
                dp.product_name         AS product_name
         from csm.proj_order_product pop
                  left join csm.proj_project_info ppi on pop.project_info_id = ppi.project_info_id
                  LEFT JOIN csm.dict_product dp ON pop.yy_order_product_id = dp.yy_product_id
        where pop.project_info_id in
        <foreach close=")" collection="projectIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and pop.order_info_id = ppi.order_info_id
        and pop.is_deleted = 0
        union all
        select sp.special_product_id as product_id,
               sp.project_info_id    as project_info_id,
               dp.product_name       AS product_name
        from csm.proj_special_product_record sp
                 left join csm.proj_project_info ppi on sp.project_info_id = ppi.project_info_id
                 LEFT JOIN csm.dict_product dp ON sp.special_product_id = dp.yy_product_id
        where sp.project_info_id in
        <foreach close=")" collection="projectIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and sp.is_deleted = 0)
            tmp
        group by project_info_id
    </select>

    <select id="getProductName" resultType="java.lang.String">
        select dp.product_name
        from csm.proj_order_product pop
                 left join csm.dict_product dp on
            pop.yy_order_product_id = dp.yy_product_id
        where pop.project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </select>

    <update id="updateExcutionStatusByOrderId">
        update
            csm.proj_order_product
        set
        product_excution_status =
        case
        when yy_order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item,jdbcType=SMALLINT}
        </foreach>
        then #{unPass,jdbcType=SMALLINT}
        when yy_order_product_id not in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item,jdbcType=SMALLINT}
        </foreach>
        then #{pass,jdbcType=SMALLINT}
            end
        where order_info_id = #{orderInfoId,jdbcType=BIGINT}
    </update>

    <select id="selectProductBaseByProjectId" resultType="com.msun.csm.model.resp.project.ProductBase">
        select pop.order_product_id        as id,
               dp.product_name             as name,
               poi.yy_order_id             as woId,
               dp.yy_product_id            as yyId,
               dp.product_name             as yyName,
               pop.product_resolve_type_id as pemcusssolType,
               pop.yy_proj_id              as projProductId,
               case
                   when rpr.his_flag = 1
                       then false
                   else true
                   end                     as canSplit
        from csm.proj_order_product pop
                 left join csm.dict_product dp on pop.yy_order_product_id = dp.yy_product_id
                 left join csm.proj_order_info poi on pop.order_info_id = poi.order_info_id
                 left join csm.rule_product_rule_config rpr on rpr.yy_product_id = dp.yy_product_id
        where pop.project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </select>

    <update id="updateByProjectAndOrder">
        update
            csm.proj_order_product
        set project_info_id = #{newProjectId,jdbcType=BIGINT},
            order_info_id   = #{newOlderId,jdbcType=BIGINT}
        where project_info_id = #{oldProjectId,jdbcType=BIGINT}
          and order_info_id = #{oldOrderId,jdbcType=BIGINT}
        <if test="yyProductIdList != null and yyProductIdList.size() > 0">
            and yy_order_product_id in
            <foreach close=")" collection="yyProductIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </update>
    <update id="updateBatchByYyOrderProductIdsAndProjectInfoId">
        update csm.proj_order_product
        set product_open_status = #{productOpenStatus, jdbcType=INTEGER}
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and yy_order_product_id in
        <foreach close=")" collection="list" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <delete id="deletedOrderProductByYyOrderProductIdsAndProjectInfoId">
        delete
        from csm.proj_order_product
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and yy_order_product_id in
        <foreach close=")" collection="list" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
    <update id="updateBatchByProjectInfoId">
        update csm.proj_order_product
        set product_excution_status = 1
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatchById" parameterType="java.util.List">
    </update>
    <update id="updateByIds" parameterType="java.util.List">
        update csm.proj_order_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_subscribe_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then
                        TO_TIMESTAMP(#{item.productSubscribeTime}, 'YYYY-MM-DD HH24:MI:SS')
                </foreach>
            </trim>
        </trim>
        where order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderProductId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateExcutionStatusByIds" parameterType="java.util.List">
        update csm.proj_order_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_excution_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_product_id = #{item.orderProductId,jdbcType=BIGINT} then #{item.productExcutionStatus}
                </foreach>
            </trim>
        </trim>
        where order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderProductId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findByProjectInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_order_product
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
    </select>

    <select id="findDictProductByProjectInfoId" parameterType="java.lang.Long"
            resultType="com.msun.csm.dao.entity.dict.DictProduct">
        select t1.product_name
        from csm.dict_product t1
                 inner join csm.proj_order_product t2 on t2.yy_order_product_id = t1.yy_product_id
        where t2.project_info_id = #{projectInfoId, jdbcType=BIGINT}
          and t2.is_deleted = 0
    </select>

    <select id="getYyProductByOrderInfoId" resultType="com.msun.csm.feign.entity.yunying.req.ProductReq">
        select distinct (yy_proj_id)            as projProductId,
                        yy_order_product_id     as productId,
                        dp.product_name         as productName,
                        product_resolve_type_id as pemCusSolType
        from csm.proj_order_product op
                 left join csm.dict_product dp on op.yy_order_product_id = dp.yy_product_id
        where order_info_id = #{orderInfoId}
    </select>

    <update id="deleteByOrderInfoId">
        update csm.proj_order_product
        set is_deleted = 1
        where order_info_id = #{orderInfoId}
    </update>

    <update id="deleteByOrderInfoIdAndProductIds">
        update csm.proj_order_product
        set is_deleted = 1
        where order_info_id = #{orderInfoId}
          and yy_order_product_id in
        <foreach close=")" collection="oldProductIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateContractId">
        update csm.proj_order_product
        set yy_contract_id = #{contractId}
        where yy_proj_id in
        <foreach collection="projProductIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
    </update>

    <select id="selectProductData" resultType="com.msun.csm.model.dto.productempower.ProductEmpowerDTO">
        SELECT
            order_product_id  "yyOrderProductId",
            msun_health_module_code  "msunHealthModuleCode",
            msun_health_module  "msunHealthModule"
        FROM csm.dict_product_vs_empower x
        WHERE order_product_id = #{yyProductId}
          and is_deleted = 0

    </select>
</mapper>
