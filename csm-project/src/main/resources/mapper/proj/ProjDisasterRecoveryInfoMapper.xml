<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryInfoMapper">
    <select id="findDisasterRecoveryInfo" resultType="com.msun.csm.dao.entity.proj.ProjDisasterRecoveryInfoRelative">
        select t2.delivery_order_no, coalesce(t4.product_name, '') as product_name, t5.user_name, t6.dept_name,
        t2.create_time as dispatchTime,t7.status,t.*
        from csm.proj_disaster_recovery_info t
        left join csm.proj_order_info t2 on t2.is_deleted = 0 and t.order_info_id = t2.order_info_id
        left join csm.proj_order_product t3 on t3.is_deleted = 0 and t3.order_info_id = t.order_info_id
        left join csm.dict_product t4 on t4.is_deleted = 0 and t4.yy_product_id = t3.yy_order_product_id
        left join csm.sys_user t5 on t5.is_deleted = 0 and t5.user_yunying_id = CAST(t.responsible_person_id AS varchar)
        left join csm.sys_dept t6 on t6.is_deleted = 0 and t6.dept_yunying_id = t5.dept_id
        left join csm.proj_disaster_recovery_apply t7 on t7.is_deleted = 0 and t.proj_disaster_recovery_info_id =
        t7.proj_disaster_recovery_info_id
        <where>
            and t.is_deleted = 0
            <if test="deliveryOrderNo != null and deliveryOrderNo != ''">
                and t2.delivery_order_no like concat('%', #{deliveryOrderNo}, '%')
            </if>
            and custom_info_id = #{customInfoId}
        </where>
    </select>
</mapper>
