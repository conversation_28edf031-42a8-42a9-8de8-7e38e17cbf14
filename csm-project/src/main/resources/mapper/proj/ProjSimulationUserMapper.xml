<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSimulationUserMapper">
    <insert id="insertBatch">
        insert into
        csm.proj_simulation_user
        (proj_simulation_user_id, custom_info_id, project_info_id, hospital_info_id,
        dict_role_id, simulation_dept_id, simulation_dept_name, simulation_user_id,
        simulation_user_name, simulation_user_account, simulation_ward_id, is_deleted, creater_id, updater_id,
        create_time,update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projSimulationUserId},#{entity.customInfoId},#{entity.projectInfoId},#{entity.hospitalInfoId},
            #{entity.dictRoleId},#{entity.simulationDeptId},#{entity.simulationDeptName},#{entity.simulationUserId},#{entity.simulationUserName},
            #{entity.simulationUserAccount},#{entity.simulationWardId},#{entity.isDeleted},#{entity.createrId},#{entity.updaterId},
            #{entity.createTime},#{entity.updateTime})
        </foreach>
    </insert>

    <update id="updateSimulationUser">
        update csm.proj_simulation_user
        set simulation_dept_id = #{simulationDeptId},
        simulation_dept_name = #{simulationDeptName},
        simulation_user_id = #{simulationUserId},
        simulation_user_name = #{simulationUserName},
        simulation_user_account = #{simulationUserAccount},
        simulation_ward_id = #{simulationWardId},
        updater_id = #{updaterId},
        update_time = #{updateTime}
        where proj_simulation_user_id = #{projSimulationUserId}
    </update>
</mapper>