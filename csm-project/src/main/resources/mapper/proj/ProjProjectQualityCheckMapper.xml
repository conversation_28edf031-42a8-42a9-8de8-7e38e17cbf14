<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectQualityCheckMapper">

    <select id="selectDataList" resultType="com.msun.csm.dao.entity.proj.ProjProjectQualityCheck">
        select * from csm.proj_project_quality_check
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        <if test="checkType != null">
            and check_type = #{checkType}
        </if>
        order by create_time desc
    </select>
</mapper>
