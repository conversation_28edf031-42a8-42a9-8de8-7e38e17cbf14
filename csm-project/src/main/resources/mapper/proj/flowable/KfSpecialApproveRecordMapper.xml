<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.flowable.KfSpecialApproveRecordMapper">
    <select id="getKfSpecialApproveList" resultType="com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveRecord">
        select a.*,
        b.custom_name,
        c.project_name,
        d.dept_name,
        e.user_name as creater_name,
        f.user_name as updater_name,
        case when a.creater_id = #{uid} then true else false end as can_edit
        from csm.kf_special_approve_record as a
        inner join csm.proj_custom_info as b on a.custom_info_id = b.custom_info_id and b.is_deleted = 0
        inner join csm.proj_project_info as c on a.project_info_id = c.project_info_id and c.is_deleted = 0
        left join csm.sys_dept as d on a.dept_id = d.dept_yunying_id and d.is_deleted = 0
        left join csm.sys_user as e on a.creater_id = e.sys_user_id and e.is_deleted = 0
        left join csm.sys_user as f on a.updater_id = f.sys_user_id and f.is_deleted = 0
        where a.is_deleted = 0
        <choose>
            <when test="projectNumber != null and projectNumber != ''">
                and a.project_number = #{projectNumber}
            </when>
            <otherwise>
                <if test="queryScope != 'all'">
                    and (
                    a.creater_id = #{uid}
                    <if test="joinedApproveIds != null and joinedApproveIds.size() != 0">
                        or a.id in
                        <foreach collection="joinedApproveIds" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </if>
                    <if test="deptIdList != null and deptIdList.size() != 0">
                        or a.dept_id in
                        <foreach collection="deptIdList" item="deptId" open="(" separator="," close=")">
                            #{deptId}
                        </foreach>
                    </if>
                    )
                </if>
            </otherwise>
        </choose>
        <if test="keyword != null and keyword != ''">
            and (
            c.project_name like concat('%', #{keyword}, '%')
            or a.project_number like concat('%', #{keyword}, '%')
            or b.custom_name like concat('%', #{keyword}, '%')
            or d.dept_name like concat('%', #{keyword}, '%')
            or e.user_name like concat('%', #{keyword}, '%')
            or e.account like concat('%', #{keyword}, '%')
            or f.user_name like concat('%', #{keyword}, '%')
            or f.account like concat('%', #{keyword}, '%')
            )
        </if>
        order by a.create_time desc
    </select>
</mapper>
