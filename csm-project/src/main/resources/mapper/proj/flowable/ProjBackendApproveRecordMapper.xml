<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveRecordMapper">

    <resultMap id="ProjBackendApproveRecord" type="com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveRecord">
        <id property="approveRecordId" column="approve_record_id"/>
        <result property="projectInfoId" column="project_info_id"/>
        <result property="projectNumber" column="project_number"/>
        <result property="projectName" column="project_name"/>
        <result property="approveTypeId" column="approve_type_id"/>
        <result property="approveTypeName" column="approve_type_name"/>
        <result property="approveTypeCode" column="approve_type_code"/>
        <result property="details" column="details"/>
        <result property="lastOpNodeId" column="last_op_node_id"/>
        <result property="completeStatus" column="complete_status"/>
        <result property="completed" column="completed"/>
        <result property="planOnlineTime" column="plan_online_time"/>
        <result property="deptNames" column="dept_names"/>
        <result property="formData" column="form_data"/>
        <result property="timeoutAt" column="timeout_at"/>
        <result property="createrName" column="creater_name"/>
        <result property="createrId" column="creater_id"/>
        <result property="createTime" column="create_time"/>
        <result property="serverType" column="server_type"/>
        <result property="leaderName" column="leader_name"/>
        <collection property="nodes" select="getNodes" javaType="java.util.List"
                    column="{approveRecordId=approve_record_id}"/>
    </resultMap>

    <select id="existsApproveTypeCode" resultType="java.lang.Integer">
        select count(1) as total from csm.proj_backend_approve_record as a
                                          inner join csm.proj_backend_approve_type as b on b.approve_type_id = a.approve_type_id and b.is_deleted = 0
        where a.project_info_id = #{projectInfoId}
          and a.is_deleted = 0
          and a.complete_status &lt;= 2
          and b.approve_type_code = #{approveTypeCode}
    </select>


    <select id="getNodes" resultType="com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveNode">
        select a.approve_node_id,
               a.project_info_id,
               a.approve_record_id,
               a.index_no,
               a.approve_user_id,
               a.leader_user_id,
               a.approve_user_account,
               a.approve_user_name,
               a.status,
               a.details,
               a.approve_at,
               a.creater_id,
               a.create_time
        from csm.proj_backend_approve_node as a
                 inner join csm.sys_user as b on a.approve_user_id = b.sys_user_id
        where a.is_deleted = 0
          and b.is_deleted = 0
          and a.approve_record_id = #{approveRecordId}
        order by a.index_no
    </select>


    <select id="getHistoryByProjectInfoId" resultMap="ProjBackendApproveRecord">
        select a.approve_record_id,
               a.project_info_id,
               a.project_number,
               a.project_name,
               a.approve_type_id,
               a.approve_type_name,
               a.approve_type_code,
               a.details,
               a.last_op_node_id,
               a.complete_status,
               a.completed,
               a.plan_online_time,
               a.dept_names,
               a.form_data,
               a.timeout_at,
               a.creater_id,
               a.create_time,
               a.server_type,
               leader.user_name as "leader_name",
               b.user_name as creater_name
        from csm.proj_backend_approve_record as a
                 inner join csm.sys_user as b on a.creater_id = b.sys_user_id
                 left join csm.sys_dept sd on a.yy_backend_team_id = sd.dept_yunying_id and sd.is_deleted = 0
                 left join csm.sys_user leader on sd.dept_leader_yunying_id = leader.user_yunying_id and leader.is_deleted  = 0
        where a.is_deleted = 0
          and b.is_deleted = 0
          and a.project_info_id = #{projectInfoId}
        order by a.create_time desc
    </select>

    <select id="getApproveRecordById" resultMap="ProjBackendApproveRecord">
        select a.approve_record_id,
               a.project_info_id,
               a.project_number,
               a.project_name,
               a.approve_type_id,
               a.approve_type_name,
               a.approve_type_code,
               a.details,
               a.last_op_node_id,
               a.complete_status,
               a.completed,
               a.plan_online_time,
               a.dept_names,
               a.form_data,
               a.timeout_at,
               a.creater_id,
               a.create_time,
               a.server_type,
               leader.user_name as "leader_name",
               b.user_name as creater_name
        from csm.proj_backend_approve_record as a
                 inner join csm.sys_user as b on a.creater_id = b.sys_user_id
                 left join csm.sys_dept sd on a.yy_backend_team_id = sd.dept_yunying_id and sd.is_deleted = 0
                 left join csm.sys_user leader on sd.dept_leader_yunying_id = leader.user_yunying_id and leader.is_deleted  = 0
        where a.is_deleted = 0
          and b.is_deleted = 0
          and a.approve_record_id = #{approveRecordId}
    </select>

    <select id="getBackendApproveRecordServerType" resultType="java.lang.Integer">
        select count(1) as total
        from csm.proj_backend_approve_record as pbar
        where pbar.is_deleted = 0
          and pbar.approve_type_code = 'frontBackendImpl'
          and pbar.project_info_id = #{projectInfoId}
          and pbar.server_type = #{backendType}
          and pbar.complete_status in (0,1,2,3,4)
    </select>

</mapper>
