<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.flowable.KfSpecialApproveLogMapper">
    <select id="getKfSpecialApproveLogList" resultType="com.msun.csm.dao.entity.proj.flowable.KfSpecialApproveLog">
        select a.*,
               b.user_name as creater_name,
               b.phone
        from csm.kf_special_approve_log as a
                 inner join csm.sys_user as b on a.creater_id = b.sys_user_id and b.is_deleted = 0
        where a.is_deleted = 0
          and a.approve_id = #{approveId}
        order by a.create_time asc
    </select>
</mapper>
