<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjInterfaceRecordLogMapper">

    <select id="selectInterfaceRecordLog" resultType="com.msun.csm.dao.entity.proj.ProjInterfaceRecordLog">
        select interface_record_log_id,
               record_name,
               creater_id,
               creater_name,
               create_time,
               third_interface_id,
               comments,
               operate_user_phone
        from csm.proj_interface_record_log
        where third_interface_id = #{thirdInterfaceId}
          and record_name = '接口裁定驳回'
        order by create_time
    </select>
</mapper>
