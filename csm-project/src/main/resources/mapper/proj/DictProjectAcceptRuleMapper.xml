<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectAcceptRuleMapper">

    <select id="getAcceptRuleByClassificationCode" resultType="com.msun.csm.dao.entity.dict.DictProjectAcceptRule">
        select *
        from csm.dict_project_accept_rule
        where is_delete = 0
          and classification_code = #{classificationCode,jdbcType=VARCHAR}
    </select>

    <select id="getProjectClassificationScore" resultType="com.msun.csm.dao.entity.proj.ProjProjectClassificationScorePO">
        select dpar.classification_code,
               dpar.classification_name,
               dpar.score_weight,
               dpar.parent_code,
               dpar.first_score,
               dpar.final_score,
               dpar.score_standard,
               0  as "practical_deduction",
               0  as "score",
               '' as "remark"
        from csm.dict_project_accept_rule dpar
        where dpar.is_delete = 0
          and dpar.classification_code = #{classificationCode,jdbcType=VARCHAR}
    </select>

</mapper>
