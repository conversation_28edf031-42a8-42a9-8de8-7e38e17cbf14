<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjOnlineStepDetailMapper">
    <select id="selectHospitalOnlineDetail" parameterType="com.msun.csm.model.dto.ProjOnlineStepDetailDTO"
            resultType="com.msun.csm.model.vo.ProjectOnlineToHospitalVO">

        select
        phi.hospital_info_id,
        phi.hospital_name,
        phod.online_status,
        phi.hospital_open_status,
        phod.plan_online_time,
        su.user_name,
        posd.online_step_detail_id,
        posd.updater_id,
        posd.update_time,
        su.user_name,
        posd.step_detail_status,
        posd.update_time
        from
        csm.proj_hospital_info phi
        left join csm.proj_hospital_online_detail phod on
        phi.hospital_info_id = phod.hospital_info_id
        and phod.is_deleted = 0
        left join csm.proj_hospital_vs_project_type phvpt on
        phvpt.hospital_info_id = phi.hospital_info_id
        and phvpt.is_deleted = 0
        left join csm.proj_online_step_detail posd on
        phi.hospital_info_id = posd.hospital_info_id
        and posd.is_deleted = 0
        and posd.proj_online_step_id = #{projOnlineStepId}
        left join csm.sys_user su on
        posd.updater_id = su.sys_user_id
        where
        phi.is_deleted = 0
        and phvpt.custom_info_id = #{customInfoId}
        and phvpt.project_type = #{projectType}
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            and phi.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="onlineStatus != null and onlineStatus == 1">
            and phod.online_status =#{onlineStatus}
        </if>
        <if test="hospitalOpenStatus != null ">
            and phi.hospital_open_status =#{hospitalOpenStatus}
        </if>
        <if test="onlineStatus != null and onlineStatus == 0">
            and phod.online_status is null
        </if>
        order by phi.health_bureau_flag desc , phi.hospital_name asc
    </select>
    <select id="selectOnlineBeforFile" resultType="com.msun.csm.dao.entity.SysFile">
        SELECT online_step_code "fileCode",
               link_address     "filePath"
        FROM csm.config_online_step x
        WHERE online_step_code in ('set_browser')
        union all
        SELECT file_code "fileCode", file_path "filePath"
        FROM csm.sys_file x
        WHERE file_code in (
                            'hospitalinfoSync',
                            'configInfoSync',
                            'mobileUseRemark',
                            'his_cut_tool'
            )
    </select>

    <insert id="batchInsert" parameterType="map">
        insert into csm.proj_online_step_detail
        (online_step_detail_id,
        proj_online_step_id,
        step_detail_status,
        hospital_info_id,
        config_online_step_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.onlineStepDetailId},
            #{item.projOnlineStepId},
            #{item.stepDetailStatus},
            #{item.hospitalInfoId},
            #{item.configOnlineStepId},
            #{item.isDeleted},
            #{item.createrId},
            #{item.createTime},
            #{item.updaterId},
            #{item.updateTime})
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        update csm.proj_online_step_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="step_detail_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when online_step_detail_id = #{item.onlineStepDetailId} then
                    #{item.stepDetailStatus}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when online_step_detail_id = #{item.onlineStepDetailId} then
                    #{item.updaterId}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when online_step_detail_id = #{item.onlineStepDetailId} then
                    now()
                </foreach>
            </trim>

        </trim>
        where online_step_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.onlineStepDetailId}
        </foreach>
    </update>
</mapper>
