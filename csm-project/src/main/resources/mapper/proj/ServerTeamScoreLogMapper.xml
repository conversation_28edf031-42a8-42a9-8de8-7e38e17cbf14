<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ServerTeamScoreLogMapper">
    <insert id="saveServerTeamScoreLog" parameterType="com.msun.csm.dao.entity.proj.ServerTeamScoreLog">
        insert into csm.server_team_score_log (server_team_score_log_id,
                                               is_deleted,
                                               creater_id,
                                               create_time,
                                               updater_id,
                                               update_time,
                                               server_team_deduction_record_id,
                                               operation_status,
                                               operation_content,
                                               operation_remark)
        values (#{serverTeamScoreLogId},
                #{isDeleted},
                #{createrId},
                now(),
                #{updaterId},
                now(),
                #{serverTeamDeductionRecordId},
                #{operationStatus},
                #{operationContent},
                #{operationRemark})
    </insert>

    <select id="queryOperationLogByServerTeamDeductionRecordId"
            resultType="com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp">
        select stsl.server_team_score_log_id                      as "businessExamineLogId",
               stsl.operation_status                              as "examineStatus",
               stsl.server_team_deduction_record_id               as "businessId",
               stsl.operation_content                             as "operateContent",
               to_char(stsl.create_time, 'YYYY-MM-DD HH24:MI:SS') as "operateTime",
               su.user_name                                       as "operateUserName",
               su.phone                                           as "operateUserPhone",
               case
                   when stsl.operation_status = 1 then '质管发送确认单'
                   when stsl.operation_status = 2 then '质管撤回确认单'
                   when stsl.operation_status = 3 then '后端驳回确认单'
                   when stsl.operation_status = 4 then '后端确认'
                   else '未知操作' end
                                                                  as "logTitle",
               stsl.operation_remark                              as "operateRemark",
               stsl.creater_id                                    as "operatorSysUserId"
        from csm.server_team_score_log stsl
                 left join csm.sys_user su on stsl.creater_id = su.sys_user_id and su.is_deleted = 0
        where stsl.is_deleted = 0
          and stsl.server_team_deduction_record_id = #{serverTeamDeductionRecordId}
        order by stsl.create_time
    </select>
</mapper>
