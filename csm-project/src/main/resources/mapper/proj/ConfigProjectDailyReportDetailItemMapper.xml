<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.conf.ConfigProjectDailyReportDetailItemMapper">

    <select id="getConfigProjectDailyReportDetailItem" resultType="com.msun.csm.dao.entity.config.ConfigProjectDailyReportDetailItem">
        select
        DISTINCT ON (cpdrdi.config_project_daily_report_detail_item_id, cpdrdi.node_code)
        cpdrdi.config_project_daily_report_detail_item_id as "configProjectDailyReportDetailItemId",
        cpdrdi.creater_id as "createrId",
        cpdrdi.create_time as "createTime",
        cpdrdi.updater_id as "updaterId",
        cpdrdi.update_time as "updateTime",
        cpdrdi.is_deleted as "isDeleted",
        cpdrdi.node_code as "nodeCode",
        case
        when cpdrdi.node_name is not null and cpdrdi.node_name != '' then cpdrdi.node_name
        else dmn.milestone_node_name
        end as "nodeName",
        cpdrdi.node_type as "nodeType",
        cpdrdi.project_deliver_status as "projectDeliverStatus",
        cpdrdi.sort_no as "sortNo"
        from csm.config_project_daily_report_detail_item AS cpdrdi
        left join csm.dict_milestone_node dmn on cpdrdi.node_code = dmn.milestone_node_code and dmn.invalid_flag = 0
        where cpdrdi.is_deleted = 0
        <if test='projectDeliverStatus != null'>
            and cpdrdi.project_deliver_status = #{projectDeliverStatus}
        </if>
    </select>
</mapper>
