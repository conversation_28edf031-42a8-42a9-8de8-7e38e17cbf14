<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductConfigMapper">

    <insert id="insertBatch">
        insert into csm.proj_product_config (
        product_config_id,
        project_info_id,
        hospital_info_id,
        yy_product_id,
        config_name,
        config_code,
        config_value,
        config_type,
        config_status,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        survey_question,
        survey_answer
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productConfigId}, #{item.projectInfoId}, #{item.hospitalInfoId}, #{item.yyProductId},
            #{item.configName}, #{item.configCode}, #{item.configValue}, #{item.configType}, #{item.configStatus},
            #{item.isDeleted}, #{item.createrId}, #{item.createTime}, #{item.updaterId}, #{item.updateTime},
            #{item.surveyQuestion}, #{item.surveyAnswer})
        </foreach>
    </insert>

    <update id="deleteByParam">
        update
            csm.proj_product_config
        set is_deleted = 1
        where project_info_id = #{projectInfoId}
          and hospital_info_id = #{hospitalInfoId}
          and yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateByProjectId">
        update
            csm.proj_product_config
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() > 0">
            and yy_product_id in
            <foreach collection="changeProductList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_product_config
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and yy_product_id in
        <foreach collection="oldProductIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
