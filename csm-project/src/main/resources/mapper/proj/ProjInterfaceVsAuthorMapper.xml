<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjInterfaceVsAuthorMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor">
        <!--@mbg.generated-->
        <!--@Table csm.proj_interface_vs_author-->
        <id column="interface_vs_author_id" jdbcType="BIGINT" property="interfaceVsAuthorId"/>
        <result column="third_interface_id" jdbcType="BIGINT" property="thirdInterfaceId"/>
        <result column="firm_name" jdbcType="VARCHAR" property="firmName"/>
        <result column="environment" jdbcType="SMALLINT" property="environment"/>
        <result column="pf_public_key" jdbcType="VARCHAR" property="pfPublicKey"/>
        <result column="pf_private_key" jdbcType="VARCHAR" property="pfPrivateKey"/>
        <result column="pf_app_id" jdbcType="VARCHAR" property="pfAppId"/>
        <result column="pf_auth_apply_id" jdbcType="VARCHAR" property="pfAuthApplyId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        interface_vs_author_id, third_interface_id, firm_name, environment, pf_public_key,
        pf_private_key, pf_app_id, pf_auth_apply_id, is_deleted, creater_id, create_time,
        updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_interface_vs_author
        where interface_vs_author_id = #{interfaceVsAuthorId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_interface_vs_author
        where interface_vs_author_id = #{interfaceVsAuthorId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor">
        <!--@mbg.generated-->
        insert into csm.proj_interface_vs_author
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceVsAuthorId != null">
                interface_vs_author_id,
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id,
            </if>
            <if test="firmName != null">
                firm_name,
            </if>
            <if test="environment != null">
                environment,
            </if>
            <if test="pfPublicKey != null">
                pf_public_key,
            </if>
            <if test="pfPrivateKey != null">
                pf_private_key,
            </if>
            <if test="pfAppId != null">
                pf_app_id,
            </if>
            <if test="pfAuthApplyId != null">
                pf_auth_apply_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="interfaceVsAuthorId != null">
                #{interfaceVsAuthorId,jdbcType=BIGINT},
            </if>
            <if test="thirdInterfaceId != null">
                #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="firmName != null">
                #{firmName,jdbcType=VARCHAR},
            </if>
            <if test="environment != null">
                #{environment,jdbcType=SMALLINT},
            </if>
            <if test="pfPublicKey != null">
                #{pfPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="pfPrivateKey != null">
                #{pfPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="pfAppId != null">
                #{pfAppId,jdbcType=VARCHAR},
            </if>
            <if test="pfAuthApplyId != null">
                #{pfAuthApplyId,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor">
        <!--@mbg.generated-->
        update csm.proj_interface_vs_author
        <set>
            <if test="thirdInterfaceId != null">
                third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="firmName != null">
                firm_name = #{firmName,jdbcType=VARCHAR},
            </if>
            <if test="environment != null">
                environment = #{environment,jdbcType=SMALLINT},
            </if>
            <if test="pfPublicKey != null">
                pf_public_key = #{pfPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="pfPrivateKey != null">
                pf_private_key = #{pfPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="pfAppId != null">
                pf_app_id = #{pfAppId,jdbcType=VARCHAR},
            </if>
            <if test="pfAuthApplyId != null">
                pf_auth_apply_id = #{pfAuthApplyId,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where interface_vs_author_id = #{interfaceVsAuthorId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor">
        <!--@mbg.generated-->
        update csm.proj_interface_vs_author
        set third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
        firm_name = #{firmName,jdbcType=VARCHAR},
        environment = #{environment,jdbcType=SMALLINT},
        pf_public_key = #{pfPublicKey,jdbcType=VARCHAR},
        pf_private_key = #{pfPrivateKey,jdbcType=VARCHAR},
        pf_app_id = #{pfAppId,jdbcType=VARCHAR},
        pf_auth_apply_id = #{pfAuthApplyId,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where interface_vs_author_id = #{interfaceVsAuthorId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_interface_vs_author
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="third_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.thirdInterfaceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="firm_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.firmName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="environment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.environment,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="pf_public_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.pfPublicKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pf_private_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.pfPrivateKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pf_app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.pfAppId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pf_auth_apply_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.pfAuthApplyId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where interface_vs_author_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.interfaceVsAuthorId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_interface_vs_author
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="third_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.thirdInterfaceId != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.thirdInterfaceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="firm_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.firmName != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.firmName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="environment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.environment != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.environment,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_public_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfPublicKey != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.pfPublicKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_private_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfPrivateKey != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.pfPrivateKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfAppId != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.pfAppId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_auth_apply_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfAuthApplyId != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.pfAuthApplyId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when interface_vs_author_id = #{item.interfaceVsAuthorId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where interface_vs_author_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.interfaceVsAuthorId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_interface_vs_author
        (interface_vs_author_id, third_interface_id, firm_name, environment, pf_public_key,
        pf_private_key, pf_app_id, pf_auth_apply_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.interfaceVsAuthorId,jdbcType=BIGINT}, #{item.thirdInterfaceId,jdbcType=BIGINT},
            #{item.firmName,jdbcType=VARCHAR}, #{item.environment,jdbcType=SMALLINT},
            #{item.pfPublicKey,jdbcType=VARCHAR},
            #{item.pfPrivateKey,jdbcType=VARCHAR}, #{item.pfAppId,jdbcType=VARCHAR},
            #{item.pfAuthApplyId,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor">
        <!--@mbg.generated-->
        insert into csm.proj_interface_vs_author
        (interface_vs_author_id, third_interface_id, firm_name, environment, pf_public_key,
        pf_private_key, pf_app_id, pf_auth_apply_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        (#{interfaceVsAuthorId,jdbcType=BIGINT}, #{thirdInterfaceId,jdbcType=BIGINT}, #{firmName,jdbcType=VARCHAR},
        #{environment,jdbcType=SMALLINT}, #{pfPublicKey,jdbcType=VARCHAR}, #{pfPrivateKey,jdbcType=VARCHAR},
        #{pfAppId,jdbcType=VARCHAR}, #{pfAuthApplyId,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        interface_vs_author_id = #{interfaceVsAuthorId,jdbcType=BIGINT},
        third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
        firm_name = #{firmName,jdbcType=VARCHAR},
        environment = #{environment,jdbcType=SMALLINT},
        pf_public_key = #{pfPublicKey,jdbcType=VARCHAR},
        pf_private_key = #{pfPrivateKey,jdbcType=VARCHAR},
        pf_app_id = #{pfAppId,jdbcType=VARCHAR},
        pf_auth_apply_id = #{pfAuthApplyId,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor">
        <!--@mbg.generated-->
        insert into csm.proj_interface_vs_author
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceVsAuthorId != null">
                interface_vs_author_id,
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id,
            </if>
            <if test="firmName != null">
                firm_name,
            </if>
            <if test="environment != null">
                environment,
            </if>
            <if test="pfPublicKey != null">
                pf_public_key,
            </if>
            <if test="pfPrivateKey != null">
                pf_private_key,
            </if>
            <if test="pfAppId != null">
                pf_app_id,
            </if>
            <if test="pfAuthApplyId != null">
                pf_auth_apply_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceVsAuthorId != null">
                #{interfaceVsAuthorId,jdbcType=BIGINT},
            </if>
            <if test="thirdInterfaceId != null">
                #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="firmName != null">
                #{firmName,jdbcType=VARCHAR},
            </if>
            <if test="environment != null">
                #{environment,jdbcType=SMALLINT},
            </if>
            <if test="pfPublicKey != null">
                #{pfPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="pfPrivateKey != null">
                #{pfPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="pfAppId != null">
                #{pfAppId,jdbcType=VARCHAR},
            </if>
            <if test="pfAuthApplyId != null">
                #{pfAuthApplyId,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="interfaceVsAuthorId != null">
                interface_vs_author_id = #{interfaceVsAuthorId,jdbcType=BIGINT},
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="firmName != null">
                firm_name = #{firmName,jdbcType=VARCHAR},
            </if>
            <if test="environment != null">
                environment = #{environment,jdbcType=SMALLINT},
            </if>
            <if test="pfPublicKey != null">
                pf_public_key = #{pfPublicKey,jdbcType=VARCHAR},
            </if>
            <if test="pfPrivateKey != null">
                pf_private_key = #{pfPrivateKey,jdbcType=VARCHAR},
            </if>
            <if test="pfAppId != null">
                pf_app_id = #{pfAppId,jdbcType=VARCHAR},
            </if>
            <if test="pfAuthApplyId != null">
                pf_auth_apply_id = #{pfAuthApplyId,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByThirdInterfaceId">
        update
            csm.proj_interface_vs_author
        set pf_app_id      = #{pfAppId},
            pf_public_key  = #{pfPublicKey},
            pf_private_key = #{pfPrivateKey},
            firm_name      = #{firmName},
            update_time    = now(),
            updater_id     = #{updaterId}
        where third_interface_id = #{thirdInterfaceId}
          and environment = #{environment}
          and is_deleted = 0
    </update>


    <select id="selectByThirdInterfaceIdAndEnv" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.proj_interface_vs_author
        where third_interface_id = #{thirdInterfaceId}
        and environment = #{environment}
        and is_deleted = 0
    </select>

    <update id="updateAuthApplyId">
        update csm.proj_interface_vs_author
        set pf_auth_apply_id = #{pfAuthApplyId}
        where third_interface_id = #{thirdInterfaceId}
          and environment = #{environment}
          and is_deleted = 0
    </update>
</mapper>
