<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjRegionOnlineDetailMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        <!--@Table csm.proj_region_online_detail-->
        <id column="region_online_detail_id" jdbcType="BIGINT" property="regionOnlineDetailId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="hospital_id" jdbcType="BIGINT" property="hospitalId"/>
        <result column="online_status" jdbcType="VARCHAR" property="onlineStatus"/>
        <result column="online_time" jdbcType="TIMESTAMP" property="onlineTime"/>
        <result column="plan_online_time" jdbcType="TIMESTAMP" property="planOnlineTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        region_online_detail_id, project_info_id, hospital_id, online_status, online_time,
        plan_online_time, is_deleted, creater_id, create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_region_online_detail
        where region_online_detail_id = #{regionOnlineDetailId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_region_online_detail
        where region_online_detail_id = #{regionOnlineDetailId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        insert into csm.proj_region_online_detail (region_online_detail_id, project_info_id,
        hospital_id, online_status, online_time,
        plan_online_time, is_deleted, creater_id,
        create_time, updater_id, update_time
        )
        values (#{regionOnlineDetailId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{hospitalId,jdbcType=BIGINT}, #{onlineStatus,jdbcType=VARCHAR}, #{onlineTime,jdbcType=TIMESTAMP},
        #{planOnlineTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        insert into csm.proj_region_online_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionOnlineDetailId != null">
                region_online_detail_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="hospitalId != null">
                hospital_id,
            </if>
            <if test="onlineStatus != null">
                online_status,
            </if>
            <if test="onlineTime != null">
                online_time,
            </if>
            <if test="planOnlineTime != null">
                plan_online_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regionOnlineDetailId != null">
                #{regionOnlineDetailId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="onlineStatus != null">
                #{onlineStatus,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="planOnlineTime != null">
                #{planOnlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        update csm.proj_region_online_detail
        <set>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                hospital_id = #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="onlineStatus != null">
                online_status = #{onlineStatus,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="planOnlineTime != null">
                plan_online_time = #{planOnlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where region_online_detail_id = #{regionOnlineDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        update csm.proj_region_online_detail
        set project_info_id = #{projectInfoId,jdbcType=BIGINT},
        hospital_id = #{hospitalId,jdbcType=BIGINT},
        online_status = #{onlineStatus,jdbcType=VARCHAR},
        online_time = #{onlineTime,jdbcType=TIMESTAMP},
        plan_online_time = #{planOnlineTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where region_online_detail_id = #{regionOnlineDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_region_online_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hospital_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.hospitalId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="online_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.onlineStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.onlineTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.planOnlineTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where region_online_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.regionOnlineDetailId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_region_online_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalId != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.hospitalId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="online_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.onlineStatus != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.onlineStatus,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.onlineTime != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.onlineTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="plan_online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.planOnlineTime != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.planOnlineTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when region_online_detail_id = #{item.regionOnlineDetailId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where region_online_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.regionOnlineDetailId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_region_online_detail
        (region_online_detail_id, project_info_id, hospital_id, online_status, online_time,
        plan_online_time, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.regionOnlineDetailId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.hospitalId,jdbcType=BIGINT}, #{item.onlineStatus,jdbcType=VARCHAR},
            #{item.onlineTime,jdbcType=TIMESTAMP},
            #{item.planOnlineTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        insert into csm.proj_region_online_detail
        (region_online_detail_id, project_info_id, hospital_id, online_status, online_time,
        plan_online_time, is_deleted, creater_id, create_time, updater_id, update_time)
        values
        (#{regionOnlineDetailId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT},
        #{onlineStatus,jdbcType=VARCHAR}, #{onlineTime,jdbcType=TIMESTAMP}, #{planOnlineTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        region_online_detail_id = #{regionOnlineDetailId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        hospital_id = #{hospitalId,jdbcType=BIGINT},
        online_status = #{onlineStatus,jdbcType=VARCHAR},
        online_time = #{onlineTime,jdbcType=TIMESTAMP},
        plan_online_time = #{planOnlineTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjRegionOnlineDetail">
        <!--@mbg.generated-->
        insert into csm.proj_region_online_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionOnlineDetailId != null">
                region_online_detail_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="hospitalId != null">
                hospital_id,
            </if>
            <if test="onlineStatus != null">
                online_status,
            </if>
            <if test="onlineTime != null">
                online_time,
            </if>
            <if test="planOnlineTime != null">
                plan_online_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regionOnlineDetailId != null">
                #{regionOnlineDetailId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="onlineStatus != null">
                #{onlineStatus,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="planOnlineTime != null">
                #{planOnlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="regionOnlineDetailId != null">
                region_online_detail_id = #{regionOnlineDetailId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalId != null">
                hospital_id = #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="onlineStatus != null">
                online_status = #{onlineStatus,jdbcType=VARCHAR},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="planOnlineTime != null">
                plan_online_time = #{planOnlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
