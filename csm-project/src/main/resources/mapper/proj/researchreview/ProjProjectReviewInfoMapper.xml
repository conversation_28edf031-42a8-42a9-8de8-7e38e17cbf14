<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectReviewInfoMapper">
    <resultMap type="com.msun.csm.dao.entity.proj.ProjProjectReviewInfo" id="ProjProjectReviewInfoMap">
        <result property="projectReviewInfoId" column="project_review_info_id" jdbcType="INTEGER"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="INTEGER"/>
        <result property="surveyReviewStatus" column="survey_review_status" jdbcType="INTEGER"/>
        <result property="surveyReviewUserId" column="survey_review_user_id" jdbcType="INTEGER"/>
        <result property="surveyReviewTime" column="survey_review_time" jdbcType="TIMESTAMP"/>
        <result property="entryReviewStatus" column="entry_review_status" jdbcType="INTEGER"/>
        <result property="entryReviewUserId" column="entry_review_user_id" jdbcType="INTEGER"/>
        <result property="entryReviewTime" column="entry_review_time" jdbcType="TIMESTAMP"/>
        <result property="preparatReviewStatus" column="preparat_review_status" jdbcType="INTEGER"/>
        <result property="preparatReviewUserId" column="preparat_review_user_id" jdbcType="INTEGER"/>
        <result property="preparatReviewTime" column="preparat_review_time" jdbcType="TIMESTAMP"/>
        <result property="lastApplyUserId" column="last_apply_user_id" jdbcType="INTEGER"/>
        <result property="lastApplyTime" column="last_apply_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insertBatch">
        insert into proj_project_review_info(project_review_info_id, project_info_id, survey_review_status,
        survey_review_user_id,
        survey_review_time, entry_review_status, entry_review_user_id, entry_review_time, preparat_review_status,
        preparat_review_user_id, preparat_review_time, last_apply_user_id, last_apply_time, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectReviewInfoId}, #{entity.projectInfoId}, #{entity.surveyReviewStatus},
            #{entity.surveyReviewUserId},
            #{entity.surveyReviewTime}, #{entity.entryReviewStatus}, #{entity.entryReviewUserId},
            #{entity.entryReviewTime}, #{entity.preparatReviewStatus}, #{entity.preparatReviewUserId},
            #{entity.preparatReviewTime}, #{entity.lastApplyUserId}, #{entity.lastApplyTime}, #{entity.isDeleted},
            #{entity.createrId}, #{entity.createTime}, #{entity.updaterId}, #{entity.updateTime})
        </foreach>
    </insert>
    <select id="selectByCondition" resultType="com.msun.csm.dao.entity.proj.ProjProjectReviewInfoRelative"
            parameterType="com.msun.csm.model.dto.projreview.ProjPmoReviewQueryDTO">
        select * from (
        select t3.custom_name, t3.custom_info_id, t2.order_info_id,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.survey_review_user_id),'') as
        survey_review_user_name,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.entry_review_user_id),'') as
        entry_review_user_name,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.preparat_review_user_id),'') as
        preparat_review_user_name,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.last_apply_user_id),'') as
        last_apply_user_name,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.survey_apply_user_id),'') as
        survey_apply_user_name,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.entry_apply_user_id),'') as
        entry_apply_user_name,
        COALESCE((select t5.user_name from csm.sys_user t5 where t5.sys_user_id = t.preparat_apply_user_id),'') as
        preparat_apply_user_name,
        t.*
        from csm.proj_project_review_info t
        inner join csm.proj_project_info t2 on t2.project_info_id = t.project_info_id and t2.is_deleted = 0
        inner join csm.proj_custom_info t3 on t3.custom_info_id = t2.custom_info_id and t3.is_deleted = 0
        <where>
            and t.is_deleted = 0
            <if test="customInfoId != null">
                and t3.custom_info_id = #{customInfoId, jdbcType=BIGINT}
            </if>
            <if test="commitStartTime != null and commitStartTime != ''">
                and t.last_apply_time >= cast(#{commitStartTime,jdbcType=VARCHAR} as TIMESTAMP)
            </if>
            <if test="commitEndTime != null and commitEndTime != ''">
                and t.last_apply_time &lt;= cast(#{commitEndTime,jdbcType=VARCHAR} as TIMESTAMP)
            </if>
            and t.last_apply_time is not null
        </where>
        ) t4
        <where>
            <if test="stageSurveyStatus != null">
                and t4.survey_review_status = #{stageSurveyStatus, jdbcType=INTEGER}
            </if>
            <if test="stageEntryStatus != null">
                and t4.entry_review_status = #{stageEntryStatus, jdbcType=INTEGER}
            </if>
            <if test="stagePreparatStatus != null">
                and t4.preparat_review_status = #{stagePreparatStatus, jdbcType=INTEGER}
            </if>
        </where>
        order by t4.last_apply_time desc
    </select>
    <select id="selectProductInfoByProjectId" resultType="com.msun.csm.model.dto.projreview.ProjReviewProductAppInfo">
        <if test="code == 'stage_survey'">
            select
            '产品业务调研' as "reviewContent",
            count(a.id) as "finishCount",
            sum(case when b.complete_status != 1 then 1 else 0 end ) as "noFinishCount"
            from
            ( select distinct  coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
            concat(dp.product_name, '-', dpm.yy_module_name) as name
            from csm.dict_product dp
            left join csm.dict_product_vs_modules dpm
            on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
            where dp.is_deleted = 0
            and dp.yy_product_id in (
            select
            product_deliver_id
            from csm.proj_product_deliver_record
            where is_deleted = 0
            and project_info_id = #{projectInfoId}
            )
            or dpm.yy_module_id in  (
            select
            product_deliver_id
            from csm.proj_product_deliver_record
            where is_deleted = 0
            and project_info_id = #{projectInfoId}
            )
            ) a
            left join (
            select  yy_product_id as id,max(complete_status) complete_status
            from csm.proj_survey_plan
            where  is_deleted = 0
            and project_info_id = #{projectInfoId}
            group by yy_product_id
            ) b on a.id = b.id

            union all

            SELECT
            '设备调研' as "reviewContent",
            sum(case when equip_status in (3, 4, 5, 6) then 1 else 0 end) as "finishCount",
            sum(case when equip_status not in (3, 4, 5, 6) then 1 else 0 end ) as "noFinishCount"
            FROM csm.proj_equip_record x
            WHERE
             is_deleted = 0
            and project_info_id = #{projectInfoId}

            union all

            select
                '打印报表'  as "reviewContent",
                sum( case when finish_status in (1,3,5)  then 1 else 0 end )   "finishCount",
                sum( case when finish_status not in (1,3,5)  then 1 else 0 end ) "noFinishCount"
            from csm.proj_survey_report psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}

            union all

            select
            '表单'  as "reviewContent",
            sum( case when finish_status in (1,3,5)  then 1 else 0 end )   "finishCount",
            sum( case when finish_status not in (1,3,5)  then 1 else 0 end ) "noFinishCount"
            from csm.proj_survey_form psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}

            union all

            select
            '统计报表'  as "reviewContent",
            sum( case when report_status >= 13  then 1 else 0 end )   "finishCount",
            sum( case when report_status &lt; 13  then 1 else 0 end ) "noFinishCount"
            from csm.proj_statistical_report_main psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}

            union all

            select
            '三方接口'  as "reviewContent",
            sum( case when status >= 13  then 1 else 0 end )   "finishCount",
            sum( case when status &lt; 13  then 1 else 0 end ) "noFinishCount"
            from csm.proj_third_interface psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}


        </if>

        <if test="code == 'stage_preparat'">
            select DISTINCT
            '产品准备工作'  as "reviewContent",
            0 "finishCount",
            sum( case when ppb.base_data_status    = '1' or
            ppb.config_data_status  = '1' or
            ppb.todo_task_status    = '1' or
            ppb.report_data_status  = '1' or
            ppb.form_data_status    = '1'   then 1 else 0 end ) "noFinishCount"
            from  csm.proj_product_backlog ppb
            inner join csm.proj_project_info ppi on ppi.project_info_id =ppb.project_info_id and ppi.is_deleted =0 and  ppi.project_info_id = 490160349418311680
            inner join csm.proj_hospital_info phi on ppi.custom_info_id = phi.custom_info_id and phi.is_deleted =0 and phi.hos_project_type =ppi.project_type
            and phi.hospital_info_id = ppb.hospital_info_id
            where
            ppb.project_info_id = #{projectInfoId}
            and ppb.is_deleted = 0

            union all

            SELECT
            '设备调研' as "reviewContent",
            sum(case when equip_status in (3, 4, 5, 6) then 1 else 0 end) as "finishCount",
            sum(case when equip_status not in (3, 4, 5, 6) then 1 else 0 end ) as "noFinishCount"
            FROM csm.proj_equip_record x
            WHERE
            is_deleted = 0
            and project_info_id = #{projectInfoId}

            union all

            select
            '打印报表'  as "reviewContent",
            sum( case when online_essential = 1 and finish_status in (1)  then 1 else 0 end )   "finishCount",
            sum( case when  online_essential = 1 and finish_status not in (1)  then 1 else 0 end ) "noFinishCount"
            from csm.proj_survey_report psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}

            union all

            select
            '表单'  as "reviewContent",
            sum( case when online_essential = 1 and  finish_status in (1)  then 1 else 0 end )   "finishCount",
            sum( case when online_essential = 1 and finish_status not in (1)  then 1 else 0 end ) "noFinishCount"
            from csm.proj_survey_form psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}

            union all

            select
            '统计报表'  as "reviewContent",
            sum( case when online_flag=1 and report_status >= 31  then 1 else 0 end )   "finishCount",
            sum( case when online_flag=1 and report_status &lt; 31  then 1 else 0 end ) "noFinishCount"
            from csm.proj_statistical_report_main psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}

            union all

            select
            '三方接口'  as "reviewContent",
            sum( case when online_flag =1 and status >= 31  then 1 else 0 end )   "finishCount",
            sum( case when online_flag =1 and status &lt; 31  then 1 else 0 end ) "noFinishCount"
            from csm.proj_third_interface psr
            where
            psr.is_deleted = 0
            and psr.project_info_id = #{projectInfoId}
        </if>

    </select>
</mapper>

