<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordDetailMapper">

    <resultMap type="com.msun.csm.dao.entity.proj.ProjProjectReviewRecordDetail" id="ProjProjectReviewRecordDetailMap">
        <result property="projectReviewRecordDetailId" column="project_review_record_detail_id" jdbcType="INTEGER"/>
        <result property="projectReviewRecordId" column="project_review_record_id" jdbcType="INTEGER"/>
        <result property="parentItemCode" column="parent_item_code" jdbcType="VARCHAR"/>
        <result property="childRuleCode" column="child_rule_code" jdbcType="VARCHAR"/>
        <result property="childRuleContent" column="child_rule_content" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch">
        insert into
        csm.proj_project_review_record_detail(project_review_record_detail_id, project_review_record_id,
        parent_item_code, child_rule_code,
        child_rule_content, is_deleted,
        creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectReviewRecordDetailId},#{entity.projectReviewRecordId},#{entity.parentItemCode},#{entity.childRuleCode},#{entity.childRuleContent},#{entity.isDeleted},#{entity.createrId},
            #{entity.createTime},#{entity.updaterId},#{entity.updateTime})
        </foreach>
    </insert>

</mapper>

