<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordMapper">

    <resultMap type="com.msun.csm.dao.entity.proj.ProjProjectReviewRecord" id="ProjProjectReviewRecordMap">
        <result property="projectReviewRecordId" column="project_review_record_id" jdbcType="INTEGER"/>
        <result property="projectReviewInfoId" column="project_review_info_id" jdbcType="INTEGER"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="INTEGER"/>
        <result property="projectStageId" column="project_stage_id" jdbcType="INTEGER"/>
        <result property="projectStageCode" column="project_stage_code" jdbcType="VARCHAR"/>
        <result property="classCode" column="class_code" jdbcType="VARCHAR"/>
        <result property="className" column="class_name" jdbcType="VARCHAR"/>
        <result property="itemCode" column="item_code" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="projectRuleCode" column="project_rule_code" jdbcType="VARCHAR"/>
        <result property="projectRuleContent" column="project_rule_content" jdbcType="VARCHAR"/>
        <result property="isPublic" column="is_public" jdbcType="INTEGER"/>
        <result property="templateFlag" column="template_flag" jdbcType="INTEGER"/>
        <result property="templateFileCode" column="template_file_code" jdbcType="VARCHAR"/>
        <result property="projectFileId" column="project_file_id" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="selfReviewResult" column="self_review_result" jdbcType="INTEGER"/>
        <result property="selfReviewMemo" column="self_review_memo" jdbcType="VARCHAR"/>
        <result property="reviewResult" column="review_result" jdbcType="INTEGER"/>
        <result property="reviewMemo" column="review_memo" jdbcType="VARCHAR"/>
        <result property="milestoneNodeFlag" column="milestone_node_flag" jdbcType="INTEGER"/>
        <result property="milestoneNodeCode" column="milestone_node_code" jdbcType="VARCHAR"/>
        <result property="containChildrenFlag" column="contain_children_flag" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <insert id="insertBatch">
        insert into
        csm.proj_project_review_record(project_review_record_id, project_review_info_id, project_info_id,
        project_stage_id, project_stage_code,
        class_code, class_name, item_code,
        item_name, project_rule_code, project_rule_content, is_public, template_flag, template_file_code,
        project_file_id, order_no, self_review_result, self_review_memo, review_result, review_memo,
        milestone_node_flag, milestone_node_code, contain_children_flag, scene_code, is_deleted, creater_id,
        create_time,
        updater_id, update_time, verity_way, required_flag, display_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectReviewRecordId},#{entity.projectReviewInfoId},
            #{entity.projectInfoId},#{entity.projectStageId},#{entity.projectStageCode},#{entity.classCode},#{entity.className},#{entity.itemCode},#{entity.itemName},
            #{entity.projectRuleCode},#{entity.projectRuleContent},#{entity.isPublic},#{entity.templateFlag},#{entity.templateFileCode},#{entity.projectFileId},
            #{entity.orderNo},#{entity.selfReviewResult},#{entity.selfReviewMemo},#{entity.reviewResult},#{entity.reviewMemo},#{entity.milestoneNodeFlag},#{entity.milestoneNodeCode},
            #{entity.containChildrenFlag},#{entity.sceneCode},#{entity.isDeleted},#{entity.createrId},#{entity.createTime},#{entity.updaterId},#{entity.updateTime},
            #{entity.verityWay}, #{entity.requiredFlag}, #{entity.displayType})
        </foreach>
    </insert>
    <select id="selectListByRelation" resultType="com.msun.csm.dao.entity.proj.ProjProjectReviewRecord"
            parameterType="com.msun.csm.model.dto.projreview.ProjReviewQueryDTO">
        select * from (
        select
        <include refid="findRecordField"/>
        t.project_rule_code,
        t.project_rule_content,
        t.display_type
        from csm.proj_project_review_record t
        <include refid="findRecordWhere"/>
        ) t3 order by class_code, item_code
    </select>
    <sql id="findRecordField">
        t.project_review_record_id,
        t.project_review_info_id,
        t.project_info_id,
        t.project_stage_id,
        t.project_stage_code,
        t.class_code,
        t.class_name,
        t.item_code,
        t.item_name,
        t.is_public,
        t.template_flag,
        t.template_file_code,
        t.project_file_id,
        t.order_no,
        t.self_review_result,
        t.self_review_memo,
        t.review_result,
        t.review_memo,
        t.milestone_node_flag,
        t.milestone_node_code,
        t.scene_code,
        t.required_flag,
        t.verity_way,
        t.contain_children_flag,
    </sql>
    <sql id="findRecordWhere">
        <where>
            and t.project_info_id = #{projectInfoId, jdbcType=BIGINT}
            and t.project_stage_code = #{projectStageCode, jdbcType=VARCHAR}
            and t.scene_code = #{sceneCode, jdbcType=VARCHAR}
        </where>
    </sql>


</mapper>

