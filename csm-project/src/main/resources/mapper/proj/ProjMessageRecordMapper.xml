<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjMessageRecordMapper">
    <insert id="insertBatch"
            parameterType="java.util.List">
        insert into csm.proj_message_record
        (id, message_type_id, message_title, message_content, message_content_param,
        message_status, message_to_category, message_to_id, read_user_id, read_time
        )
        values
        <foreach collection="set" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.messageTypeId,jdbcType=BIGINT}, #{item.messageTitle,jdbcType=VARCHAR},
            #{item.messageContent,jdbcType=VARCHAR}, #{item.messageContentParam,jdbcType=VARCHAR},
            #{item.messageStatus,jdbcType=INTEGER},
            #{item.messageToCategory,jdbcType=INTEGER}, #{item.messageToId,jdbcType=BIGINT},
            #{item.readUserId,jdbcType=BIGINT},
            #{item.readTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="selectById" resultType="com.msun.csm.dao.entity.sys.ProjMessageRecord">
        select * from csm.proj_message_record where id = #{id, jdbcType=BIGINT}
    </select>
</mapper>
