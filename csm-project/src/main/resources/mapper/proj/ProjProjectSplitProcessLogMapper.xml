<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectSplitProcessLogMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    <!--@Table csm.proj_project_split_process_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="process_id" jdbcType="BIGINT" property="processId" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="log" jdbcType="VARCHAR" property="log" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, process_id, "status", comments, log, is_deleted, creater_id, create_time, updater_id,
    update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.proj_project_split_process_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.proj_project_split_process_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process_log (id, process_id, "status",
      comments, log, is_deleted,
      creater_id, create_time, updater_id,
      update_time)
    values (#{id,jdbcType=BIGINT}, #{processId,jdbcType=BIGINT}, #{status,jdbcType=SMALLINT},
      #{comments,jdbcType=VARCHAR}, #{log,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="processId != null">
        process_id,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="log != null">
        log,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="processId != null">
        #{processId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="log != null">
        #{log,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    update csm.proj_project_split_process_log
    <set>
      <if test="processId != null">
        process_id = #{processId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="log != null">
        log = #{log,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    update csm.proj_project_split_process_log
    set process_id = #{processId,jdbcType=BIGINT},
      "status" = #{status,jdbcType=SMALLINT},
      comments = #{comments,jdbcType=VARCHAR},
      log = #{log,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_project_split_process_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="process_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="&quot;status&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="comments = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.comments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="log = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.log,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_project_split_process_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="process_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.processId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.processId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="&quot;status&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="comments = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.comments != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.comments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="log = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.log != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.log,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process_log
    (id, process_id, "status", comments, log, is_deleted, creater_id, create_time, updater_id,
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.processId,jdbcType=BIGINT}, #{item.status,jdbcType=SMALLINT},
        #{item.comments,jdbcType=VARCHAR}, #{item.log,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT},
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process_log
    (id, process_id, "status", comments, log, is_deleted, creater_id, create_time, updater_id,
      update_time)
    values
    (#{id,jdbcType=BIGINT}, #{processId,jdbcType=BIGINT}, #{status,jdbcType=SMALLINT},
      #{comments,jdbcType=VARCHAR}, #{log,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP})
    on duplicate key update
    id = #{id,jdbcType=BIGINT},
    process_id = #{processId,jdbcType=BIGINT},
    "status" = #{status,jdbcType=SMALLINT},
    comments = #{comments,jdbcType=VARCHAR},
    log = #{log,jdbcType=VARCHAR},
    is_deleted = #{isDeleted,jdbcType=SMALLINT},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog">
    <!--@mbg.generated-->
    insert into csm.proj_project_split_process_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="processId != null">
        process_id,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="log != null">
        log,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="processId != null">
        #{processId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="log != null">
        #{log,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="processId != null">
        process_id = #{processId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="log != null">
        log = #{log,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="selectByProcessId" resultType="com.msun.csm.model.resp.project.SplitProcessLogDetail">
      select pl.status      as status,
             pl.create_time as "createTime",
             pl.log         as log,
             pl.comments    as comments,
             su.user_name        as "createrName"
      from csm.proj_project_split_process_log pl
               left join csm.sys_user su
                         on pl.creater_id = su.sys_user_id
      where pl.process_id = #{processId,jdbcType=BIGINT}
        and pl.is_deleted = 0
      order by pl.create_time desc
  </select>
</mapper>
