<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectConfigMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectConfig">
        <id column="proj_project_config_id" jdbcType="BIGINT" property="projProjectConfigId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="view_model" jdbcType="VARCHAR" property="viewModel"/>
    </resultMap>
    <sql id="Base_Column_List">
        proj_project_config_id,
        project_info_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        view_model
    </sql>
    <insert id="addProjectConfig" parameterType="com.msun.csm.dao.entity.proj.AddProjProjectConfigParamPO">
        insert into csm.proj_project_config
        (proj_project_config_id,
         project_info_id,
         is_deleted,
         creater_id,
         create_time,
         updater_id,
         update_time,
         view_model)
        values (#{projProjectConfigId},
                #{projectInfoId},
                #{isDeleted},
                #{createrId},
                now(),
                #{updaterId},
                now(),
                #{viewModel})
    </insert>

    <select id="getByConfigId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_config
        where is_deleted = 0
        and proj_project_config_id = #{configId}
    </select>

    <select id="getConfigByProjectInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_config
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
    </select>

    <update id="changeViewModel">
        update csm.proj_project_config
        set view_model = #{viewModel}
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
    </update>

</mapper>
