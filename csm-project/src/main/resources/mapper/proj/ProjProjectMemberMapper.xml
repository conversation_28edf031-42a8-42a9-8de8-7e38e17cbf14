<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        <!--@Table csm.proj_project_member-->
        <id column="project_member_info_id" jdbcType="BIGINT" property="projectMemberInfoId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="project_member_id" jdbcType="BIGINT" property="projectMemberId"/>
        <result column="project_member_name" jdbcType="VARCHAR" property="projectMemberName"/>
        <result column="project_team_id" jdbcType="BIGINT" property="projectTeamId"/>
        <result column="project_team_name" jdbcType="VARCHAR" property="projectTeamName"/>
        <result column="project_member_role_id" jdbcType="BIGINT" property="projectMemberRoleId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_member_info_id, project_info_id, project_member_id, project_member_name,
        project_team_id, project_team_name, project_member_role_id, is_deleted, creater_id,
        create_time, updater_id, update_time, phone
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_member
        where project_member_info_id = #{projectMemberInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_project_member
        where project_member_info_id = #{projectMemberInfoId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByList">
        delete from csm.proj_project_member
        where project_member_info_id in (
            <foreach collection="listMemberData" item="item" separator=",">
                #{item.projectMemberInfoId}
            </foreach>
            )

    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        insert into csm.proj_project_member (project_member_info_id, project_info_id,
        project_member_id, project_member_name, project_team_id,
        project_team_name, project_member_role_id, is_deleted,
        creater_id, create_time, updater_id,
        update_time, phone)
        values (#{projectMemberInfoId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{projectMemberId,jdbcType=BIGINT}, #{projectMemberName,jdbcType=VARCHAR}, #{projectTeamId,jdbcType=BIGINT},
        #{projectTeamName,jdbcType=VARCHAR}, #{projectMemberRoleId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{phone,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        insert into csm.proj_project_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectMemberInfoId != null">
                project_member_info_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="projectMemberId != null">
                project_member_id,
            </if>
            <if test="projectMemberName != null">
                project_member_name,
            </if>
            <if test="projectTeamId != null">
                project_team_id,
            </if>
            <if test="projectTeamName != null">
                project_team_name,
            </if>
            <if test="projectMemberRoleId != null">
                project_member_role_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="phone != null">
                phone,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectMemberInfoId != null">
                #{projectMemberInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberId != null">
                #{projectMemberId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberName != null">
                #{projectMemberName,jdbcType=VARCHAR},
            </if>
            <if test="projectTeamId != null">
                #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="projectMemberRoleId != null">
                #{projectMemberRoleId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        update csm.proj_project_member
        <set>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberId != null">
                project_member_id = #{projectMemberId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberName != null">
                project_member_name = #{projectMemberName,jdbcType=VARCHAR},
            </if>
            <if test="projectTeamId != null">
                project_team_id = #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                project_team_name = #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="projectMemberRoleId != null">
                project_member_role_id = #{projectMemberRoleId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
        </set>
        where project_member_info_id = #{projectMemberInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        update csm.proj_project_member
        set project_info_id = #{projectInfoId,jdbcType=BIGINT},
        project_member_id = #{projectMemberId,jdbcType=BIGINT},
        project_member_name = #{projectMemberName,jdbcType=VARCHAR},
        project_team_id = #{projectTeamId,jdbcType=BIGINT},
        project_team_name = #{projectTeamName,jdbcType=VARCHAR},
        project_member_role_id = #{projectMemberRoleId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        phone = #{phone,jdbcType=VARCHAR}
        where project_member_info_id = #{projectMemberInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_member
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_member_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.projectMemberId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_member_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.projectMemberName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.projectTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_team_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.projectTeamName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_member_role_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.projectMemberRoleId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                    #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where project_member_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectMemberInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_member
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_member_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectMemberId != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.projectMemberId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_member_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectMemberName != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.projectMemberName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectTeamId != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.projectTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_team_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectTeamName != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.projectTeamName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_member_role_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectMemberRoleId != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.projectMemberRoleId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when project_member_info_id = #{item.projectMemberInfoId,jdbcType=BIGINT} then
                        #{item.phone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_member_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectMemberInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_project_member
        (project_member_info_id, project_info_id, project_member_id, project_member_name,
        project_team_id, project_team_name, project_member_role_id, is_deleted, creater_id,
        create_time, updater_id, update_time, phone)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectMemberInfoId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.projectMemberId,jdbcType=BIGINT}, #{item.projectMemberName,jdbcType=VARCHAR},
            #{item.projectTeamId,jdbcType=BIGINT}, #{item.projectTeamName,jdbcType=VARCHAR},
            #{item.projectMemberRoleId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.phone,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        insert into csm.proj_project_member
        (project_member_info_id, project_info_id, project_member_id, project_member_name,
        project_team_id, project_team_name, project_member_role_id, is_deleted, creater_id,
        create_time, updater_id, update_time, phone)
        values
        (#{projectMemberInfoId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{projectMemberId,jdbcType=BIGINT},
        #{projectMemberName,jdbcType=VARCHAR}, #{projectTeamId,jdbcType=BIGINT}, #{projectTeamName,jdbcType=VARCHAR},
        #{projectMemberRoleId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{phone,jdbcType=VARCHAR})
        on duplicate key update
        project_member_info_id = #{projectMemberInfoId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        project_member_id = #{projectMemberId,jdbcType=BIGINT},
        project_member_name = #{projectMemberName,jdbcType=VARCHAR},
        project_team_id = #{projectTeamId,jdbcType=BIGINT},
        project_team_name = #{projectTeamName,jdbcType=VARCHAR},
        project_member_role_id = #{projectMemberRoleId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        phone = #{phone,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        <!--@mbg.generated-->
        insert into csm.proj_project_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectMemberInfoId != null">
                project_member_info_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="projectMemberId != null">
                project_member_id,
            </if>
            <if test="projectMemberName != null">
                project_member_name,
            </if>
            <if test="projectTeamId != null">
                project_team_id,
            </if>
            <if test="projectTeamName != null">
                project_team_name,
            </if>
            <if test="projectMemberRoleId != null">
                project_member_role_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="phone != null">
                phone,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectMemberInfoId != null">
                #{projectMemberInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberId != null">
                #{projectMemberId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberName != null">
                #{projectMemberName,jdbcType=VARCHAR},
            </if>
            <if test="projectTeamId != null">
                #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="projectMemberRoleId != null">
                #{projectMemberRoleId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectMemberInfoId != null">
                project_member_info_id = #{projectMemberInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberId != null">
                project_member_id = #{projectMemberId,jdbcType=BIGINT},
            </if>
            <if test="projectMemberName != null">
                project_member_name = #{projectMemberName,jdbcType=VARCHAR},
            </if>
            <if test="projectTeamId != null">
                project_team_id = #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamName != null">
                project_team_name = #{projectTeamName,jdbcType=VARCHAR},
            </if>
            <if test="projectMemberRoleId != null">
                project_member_role_id = #{projectMemberRoleId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertUserByYunwei">
        INSERT INTO csm.proj_project_member
        (project_member_info_id, project_info_id, project_member_id, project_member_name, project_team_id, project_team_name, project_member_role_id, is_deleted, creater_id, create_time, updater_id, update_time, phone)
        select
            (EXTRACT(EPOCH FROM now())::bigint * 10000 + su.sys_user_id),
            #{projectInfoId},
            su.sys_user_id,
            su.user_name,
            su.dept_id,
            sd.dept_name ,
            case when #{sysUserId} != null and su.sys_user_id = #{sysUserId} then 3 else 4 end,
            0,
            -1,
            now(),
            -1,
            now(),
            su.phone
        from csm.sys_user su
            inner join csm.sys_dept sd  on su.dept_id = sd.dept_yunying_id
        where su.sys_user_id in (
            SELECT suvr.user_id FROM csm.sys_user_vs_role suvr
            inner join csm.sys_role sr on suvr.role_id = sr.sys_role_id and sr.role_code = 'OperationsEngineer'
            where suvr.is_deleted =0 and sr.is_deleted =0
            )
        and su.sys_user_id not in (
            select ppm.project_member_id from csm.proj_project_member ppm
            where ppm.project_info_id = #{projectInfoId}
        )
          and su.is_deleted =0
    </insert>

    <select id="selectByProjectIds" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        distinct
        <include refid="Base_Column_List"/>
        from csm.proj_project_member
        where is_deleted = 0
          and  project_info_id in
        <foreach close=")" collection="projectIds" item="item" open="(" separator=", ">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectMemberVO" parameterType="com.msun.csm.model.dto.ProjProjectMemberDTO"
            resultType="com.msun.csm.model.vo.ProjProjectMemberVO">
        select
        ppm.project_member_info_id,
        ppm.project_info_id,
        ppm.project_member_id,
        ppm.project_member_name,
        ppm.project_team_id,
        ppm.project_team_name,
        ppm.project_member_role_id,
        ppm.is_deleted,
        ppm.creater_id,
        ppm.create_time,
        ppm.updater_id,
        ppm.update_time,
        su.phone,
        dpr.project_role_name as "projectRoleName",
        dpr.project_role_code as "projectRoleCode",
        dpr.role_type as "roleType",
        su.account as "account",
        su.user_yunying_id
        from
        csm.proj_project_member ppm
        left join csm.dict_project_role dpr on
        ppm.project_member_role_id = dpr.project_role_id
        and dpr.is_deleted = 0
        left join csm.sys_user su on
        ppm.project_member_id = su.sys_user_id
        and su.is_deleted = 0
        where
        ppm.is_deleted = 0
        <if test="projectInfoId != null and projectInfoId != ''">
            and ppm.project_info_id = #{projectInfoId}
        </if>
        <if test="projectMemberId != null and projectMemberId != ''">
            and ppm.project_member_id = #{projectMemberId}
        </if>
        <if test="roleType != null and roleType > 0">
            and dpr.role_type = #{roleType}
        </if>
        order by dpr.sort
    </select>

    <update id="deleteByParam">
        update csm.proj_project_member
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and  project_member_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator="">
            #{item}
        </foreach>
    </update>

    <select id="selectByProjectIdAndRole" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_member
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and project_member_role_id = #{roleId}
    </select>

    <select id="queryBackendManager" resultMap="BaseResultMap">
        select distinct on (project_member_id) *
        from csm.proj_project_member
        where is_deleted = 0
          and project_member_role_id in (3, 5, 7)
    </select>

    <select id="selectByProjectIdAndMemberId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_member
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        and project_member_id = #{memberId}
    </select>
    <select id="selectMemberVOAll" resultType="com.msun.csm.model.vo.ProjProjectMemberVO"
            parameterType="com.msun.csm.model.dto.ProjProjectMemberDTO">
        select
            ppm.project_member_id,
            ppm.project_member_name
        from
            csm.proj_project_member ppm
                left join csm.dict_project_role dpr on
                ppm.project_member_role_id = dpr.project_role_id
                    and dpr.is_deleted = 0
                left join csm.sys_user su on
                ppm.project_member_id = su.sys_user_id
                    and su.is_deleted = 0
        where
            ppm.is_deleted = 0
        group by  ppm.project_member_id,
                  ppm.project_member_name
    </select>

    <select id="queryProjectMemberByRoleType" resultType="com.msun.csm.common.model.BaseIdNameResp">
        SELECT ppm.project_member_id   as "id",
               ppm.project_member_name as "name"
        FROM csm.proj_project_member AS ppm
                 left join csm.dict_project_role dpr on ppm.project_member_role_id = dpr.project_role_id
        where ppm.is_deleted = 0
          and ppm.project_info_id = #{projectInfoId}
        <if test="roleType != null">
            and dpr.role_type = #{roleType}
        </if>
    </select>

    <select id="getProjectMemberByUserId" resultType="com.msun.csm.dao.entity.proj.ProjectMemberVO">
        select ppm.project_member_info_id as "projectMemberInfoId",
               ppm.project_info_id        as "projectInfoId",
               ppm.project_member_id      as "projectMemberId",
               ppm.project_member_name    as "projectMemberName",
               ppm.project_team_id        as "projectTeamId",
               ppm.project_team_name      as "projectTeamName",
               ppm.project_member_role_id as "projectMemberRoleId",
               ppm.is_deleted             as "isDeleted",
               ppm.creater_id             as "createrId",
               ppm.create_time            as "createTime",
               ppm.updater_id             as "updaterId",
               ppm.update_time            as "updateTime",
               ppm.phone                  as "phone",
               dpr.project_role_code      as "projectRoleCode",
               dpr.project_role_name      as "projectRoleName",
               dpr.role_type              as "roleType",
               dpr.role_permission        as "rolePermission"
        from csm.proj_project_member ppm
                 left join csm.dict_project_role dpr on ppm.project_member_role_id = dpr.project_role_id
        where ppm.is_deleted = 0
          and ppm.project_info_id = #{projectInfoId}
          and ppm.project_member_id = #{memberId}
    </select>
</mapper>
