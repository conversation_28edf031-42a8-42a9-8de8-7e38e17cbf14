<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjMilestoneInfo">
        <id property="milestoneInfoId" column="milestone_info_id" jdbcType="BIGINT"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
        <result property="projectStageId" column="project_stage_id" jdbcType="INTEGER"/>
        <result property="projectStageCode" column="project_stage_code" jdbcType="VARCHAR"/>
        <result property="milestoneNodeId" column="milestone_node_id" jdbcType="BIGINT"/>
        <result property="milestoneNodeCode" column="milestone_node_code" jdbcType="VARCHAR"/>
        <result property="milestoneNodeName" column="milestone_node_name" jdbcType="VARCHAR"/>
        <result property="milestoneNodeUrl" column="milestone_node_url" jdbcType="VARCHAR"/>
        <result property="nodeHeadId" column="node_head_id" jdbcType="BIGINT"/>
        <result property="expectCompTime" column="expect_comp_time" jdbcType="TIMESTAMP"/>
        <result property="actualCompTime" column="actual_comp_time" jdbcType="TIMESTAMP"/>
        <result property="milestoneStatus" column="milestone_status" jdbcType="SMALLINT"/>
        <result property="taskInfoId" column="task_info_id" jdbcType="BIGINT"/>
        <result property="preNodeFlag" column="pre_node_flag" jdbcType="SMALLINT"/>
        <result property="preNodeId" column="pre_node_id" jdbcType="BIGINT"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="SMALLINT"/>
        <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="isComponent" column="is_component" jdbcType="VARCHAR"/>
        <result property="expectStartTime" column="expect_start_time" jdbcType="TIMESTAMP"/>
        <result property="planFlag" column="plan_flag" jdbcType="SMALLINT"/>
        <result property="checkFlag" column="check_flag" jdbcType="SMALLINT"/>
        <result property="checkUrl" column="check_url" jdbcType="VARCHAR"/>
        <result property="simpleMilestoneNodeName" column="simple_milestone_node_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        milestone_info_id,
        project_info_id,
        project_stage_id,
        project_stage_code,
        milestone_node_id,
        milestone_node_code,
        milestone_node_name,
        milestone_node_url,
        node_head_id,
        expect_comp_time,
        actual_comp_time,
        milestone_status,
        task_info_id,
        pre_node_flag,
        pre_node_id,
        invalid_flag,
        creater_id,
        create_time,
        updater_id,
        update_time,simple_milestone_node_name
    </sql>

    <select id="getMilestoneInfoByProjectInfoId" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneInfo">
        select
        cmn.project_stage_id ,
        cmn.milestone_node_id ,
        cmn.pre_node_flag ,
        cmn.pre_node_id ,
        dmn.milestone_node_code ,
        dmn.milestone_node_name ,
        dmn.milestone_node_url,
        dmn.simple_milestone_node_name,
        COALESCE(dmn.is_component, '') as is_component,
        dps.project_stage_code,
        cmn.order_no,
        cmn.monomer_flag,
        cmn.check_flag,
        cmn.check_url,
        cmn.region_flag,
        cmn.plan_flag
        from
        csm.config_milestone_node cmn
        left join csm.dict_milestone_node dmn on
        cmn.milestone_node_id = dmn.milestone_node_id
        left join csm.dict_project_stage dps on
        cmn.project_stage_id = dps.id
        where dmn.invalid_flag = 0 and dps.invalid_flag = 0
        <if test="monomerFlag != null">
            and cmn.monomer_flag = #{monomerFlag,jdbcType=BIGINT}
        </if>
        <if test="regionFlag != null">
            and cmn.region_flag = #{regionFlag,jdbcType=BIGINT}
        </if>
        <if test="upgradationType != null">
            and (cmn.upgradation_type = #{upgradationType,jdbcType=BIGINT} or cmn.upgradation_type=-1)
        </if>
        <if test="hisFlag != null">
            and (cmn.his_flag = #{hisFlag,jdbcType=BIGINT} or cmn.his_flag=-1)
        </if>
        <if test="externalSoftwareFlag != null">
            and cmn.external_software_flag = #{externalSoftwareFlag}
        </if>
        <if test="selfSoftwareFlag != null">
            and cmn.self_software_flag = #{selfSoftwareFlag}
        </if>
    </select>

    <select id="getNeedDocMilestoneInfo" resultType="com.msun.csm.dao.entity.proj.NeedDocMilestoneInfo">
        select
        cmn.project_stage_id ,
        cmn.milestone_node_id ,
        dmn.milestone_node_code ,
        dmn.milestone_node_name ,
        dps.project_stage_code,
        dps.project_stage_name
        from
        csm.config_milestone_node cmn
        left join csm.dict_milestone_node dmn on
        cmn.milestone_node_id = dmn.milestone_node_id
        left join csm.dict_project_stage dps on
        cmn.project_stage_id = dps.id
        where dmn.invalid_flag = 0 and dps.invalid_flag = 0
        and cmn.doc_flag = 1
        <if test="monomerFlag != null">
            and cmn.monomer_flag = #{monomerFlag,jdbcType=BIGINT}
        </if>
        <if test="regionFlag != null">
            and cmn.region_flag = #{regionFlag,jdbcType=BIGINT}
        </if>
        <if test="upgradationType != null">
            and (cmn.upgradation_type = #{upgradationType,jdbcType=BIGINT} or cmn.upgradation_type=-1)
        </if>
        <if test="hisFlag != null">
            and (cmn.his_flag = #{hisFlag,jdbcType=BIGINT} or cmn.his_flag=-1)
        </if>
        <if test="externalSoftwareFlag != null">
            and cmn.external_software_flag = #{externalSoftwareFlag}
        </if>
        <if test="selfSoftwareFlag != null">
            and cmn.self_software_flag = #{selfSoftwareFlag}
        </if>
    </select>


    <insert id="batchInsert" parameterType="map">
        insert into csm.proj_milestone_info
        (milestone_info_id,
        project_info_id,
        project_stage_id,
        milestone_node_id,
        milestone_node_code,
        milestone_node_name,
        milestone_node_url,
        pre_node_flag,
        pre_node_id,
        creater_id,
        create_time,
        invalid_flag,
        milestone_status,
        project_stage_code,
        order_no,
        is_component,
        expect_start_time,
        expect_comp_time,
        actual_comp_time,
        plan_flag,
        check_flag,
        check_url,
        simple_milestone_node_name
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.milestoneInfoId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.projectStageId,jdbcType=BIGINT},
            #{item.milestoneNodeId,jdbcType=BIGINT},
            #{item.milestoneNodeCode,jdbcType=VARCHAR},
            #{item.milestoneNodeName,jdbcType=VARCHAR},
            #{item.milestoneNodeUrl,jdbcType=VARCHAR},
            #{item.preNodeFlag,jdbcType=SMALLINT},
            #{item.preNodeId,jdbcType=BIGINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.invalidFlag,jdbcType=SMALLINT},
            #{item.milestoneStatus,jdbcType=SMALLINT},
            #{item.projectStageCode,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=SMALLINT},
            #{item.isComponent,jdbcType=VARCHAR},
            #{item.expectStartTime,jdbcType=TIMESTAMP},
            #{item.expectCompTime,jdbcType=TIMESTAMP},
            #{item.actualCompTime,jdbcType=TIMESTAMP},
            #{item.planFlag,jdbcType=SMALLINT},
            #{item.checkFlag,jdbcType=SMALLINT},
            #{item.checkUrl,jdbcType=VARCHAR},
            #{item.simpleMilestoneNodeName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


    <update id="updateMilestone" parameterType="com.msun.csm.model.dto.UpdateMilestoneDTO">
        update csm.proj_milestone_info
        <set>
            <if test="dto.expectCompTime != null">
                expect_comp_time = #{dto.expectCompTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dto.actualCompTime != null">
                actual_comp_time = #{dto.actualCompTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dto.taskInfoId != null and dto.taskInfoId != ''">
                task_info_id = #{dto.taskInfoId,jdbcType=BIGINT},
            </if>
            <if test="dto.milestoneStatus != null and dto.milestoneStatus != ''">
                milestone_status = #{dto.milestoneStatus,jdbcType=SMALLINT},
            </if>
            <if test="dto.nodeHeadId != null and dto.nodeHeadId != ''">
                node_head_id = #{dto.nodeHeadId,jdbcType=BIGINT},
            </if>
        </set>
        where milestone_info_id = #{dto.milestoneInfoId,jdbcType=BIGINT}
    </update>

    <select id="getMilestoneInfo" resultType="com.msun.csm.model.vo.ProjMilestoneInfoVO">
        select pmi.milestone_info_id,
        pmi.project_stage_id,
        pmi.project_stage_code,
        pmi.milestone_node_code,
        pmi.milestone_node_name,
        pmi.milestone_node_url,
        COALESCE(eee.statusone, pmi.milestone_status)  milestone_status,
        pmi.order_no,
        pmi.create_time,
        pmi.expect_comp_time,
        pmi.actual_comp_time,
        pmi.node_head_id,
        su.user_name as node_head_name,
        COALESCE(pmi.is_component, '') as is_component
        from csm.proj_milestone_info pmi

        left join (
        select ppp.project_info_id, ppp.status  as statusone,
        dppi.project_plan_item_code ,
        dpps.project_plan_stage_code
        from csm.proj_project_plan ppp
        left join csm.dict_project_plan_item dppi on ppp.project_plan_item_id = dppi.project_plan_item_id and dppi.is_deleted = 0
        left join csm.dict_project_plan_stage dpps on ppp.project_plan_stage_id = dpps.project_plan_stage_id and dpps.is_deleted = 0
        inner join csm.proj_project_config ppc on  ppc.is_deleted = 0
        and ppc.view_model = 'projectPlan' and ppc.project_info_id  = ppp.project_info_id
        where ppp.is_deleted = 0
        ) eee on eee.project_info_id = pmi.project_info_id and pmi.milestone_node_code = eee.project_plan_item_code

        left join csm.sys_user su on pmi.node_head_id = su.sys_user_id
        where pmi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        <if test="projectStageId != null and projectStageId != ''">
            and pmi.project_stage_id = #{projectStageId,jdbcType=BIGINT}
        </if>
        and pmi.invalid_flag = 0
        order by pmi.order_no
    </select>
    <select id="getMilestoneInfoByParams" resultType="com.msun.csm.model.vo.ProjMilestoneInfoVO">
        select
        pmi.milestone_info_id ,
        pmi.project_info_id,
        pmi.project_stage_id,
        pmi.milestone_node_code ,
        pmi.milestone_node_name ,
        pmi.milestone_node_url ,
        pmi.milestone_status ,
        pmi.pre_node_flag,
        pmi.pre_node_id,
        pmi.order_no,
        pmi.create_time,
        pmi.expect_comp_time,
        pmi.is_component
        from
        csm.proj_milestone_info pmi
        where pmi.invalid_flag =0
        and pmi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        <if test="milestoneNodeId != null and milestoneNodeId != ''">
            and pmi.milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
        </if>
        <if test="milestoneNodeCode != null and milestoneNodeCode != ''">
            and pmi.milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>
    <select id="fingMilestoneInfoByParams" resultType="com.msun.csm.model.vo.ProjMilestoneInfoVO">
        select
        pmi.milestone_info_id ,
        pmi.project_info_id,
        pmi.project_stage_id,
        pmi.project_stage_code,
        pmi.milestone_node_code ,
        pmi.milestone_node_name ,
        pmi.milestone_node_url ,
        pmi.milestone_status ,
        pmi.pre_node_flag,
        pmi.pre_node_id,
        pmi.order_no,
        pmi.create_time,
        pmi.expect_comp_time,
        pmi.is_component,
        pmi.plan_flag,
        pmi.simple_milestone_node_name
        from
        csm.proj_milestone_info pmi
        where pmi.invalid_flag =0
        and pmi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        <if test="milestoneNodeId != null">
            and pmi.milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
        </if>
        <if test="milestoneNodeCode != null and milestoneNodeCode != ''">
            and pmi.milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR}
        </if>
        <if test="planFlag != null ">
            and pmi.plan_flag = #{planFlag,jdbcType=BIGINT}
        </if>
        <if test="projectStageCode != null and projectStageCode != ''">
            and pmi.project_stage_code = #{projectStageCode,jdbcType=VARCHAR}
        </if>
        order by pmi.order_no
    </select>
    <select id="getProjMilestoneInfoVOById" parameterType="java.lang.Long"
            resultType="com.msun.csm.model.vo.ProjMilestoneInfoVO">
        select pmi.milestone_info_id,
               pmi.project_info_id,
               pmi.project_stage_id,
               pmi.milestone_node_code,
               pmi.milestone_node_name,
               pmi.milestone_node_url,
               pmi.milestone_status,
               pmi.pre_node_flag,
               pmi.pre_node_id,
               pmi.order_no,
               pmi.create_time,
               pmi.expect_comp_time,
               pmi.is_component
        from csm.proj_milestone_info pmi
        where pmi.milestone_info_id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getUrlParams" resultType="com.msun.csm.dao.entity.proj.UrlParam">
        select tpnvoo.old_project_info_id projectId,
               tpnvoo.old_custom_id       customerId,
               pci.custom_name            customerName,
               tpnvoo.old_custom_info_id  customerInfoId
        from csm.proj_project_info ppi
                 left join csm.proj_custom_info pci on
            ppi.custom_info_id = pci.custom_info_id
                 left join csm.tmp_project_new_vs_old tpnvoo
                           on ppi.project_info_id = tpnvoo.new_project_info_id
        where ppi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </select>

    <select id="getMilestoneInfoByProjectId" resultType="com.msun.csm.model.vo.ProjMilestoneInfoVO">
        with tasks as (select *
                       from (select an.*,
                                    row_number() over (partition by an.milestone_node_code
                                        order by
                                            an.create_time desc) rn
                             from csm.proj_milestone_task an
                                      left join csm.proj_hospital_info phi on
                                 an.hospital_info_id = phi.hospital_info_id and phi.health_bureau_flag = 1
                             where an.is_deleted = 0
                               and an.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}) t
                       where t.rn = 1)
        select pmi.milestone_info_id,
               pmi.project_info_id,
               pmi.project_stage_id,
               pmi.project_stage_code,
               pmi.milestone_node_code,
               pmi.milestone_node_name,
               pmi.milestone_node_url,
               pmi.milestone_status,
               pmi.pre_node_flag,
               pmi.pre_node_id,
               pmi.order_no,
               pmi.create_time,
               pmi.expect_comp_time,
               pmi.expect_start_time,
               pmi.is_component,
               pmi.simple_milestone_node_name,
               pmt.leader_id,
               pmt.second_leader_id
        from csm.proj_milestone_info pmi
                 left join tasks pmt
                           on pmi.milestone_info_id = pmt.milestone_info_id
                               and pmt.is_deleted = 0

        where pmi.invalid_flag = 0
          and pmi.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
          and pmi.project_stage_code in ('stage_preparat', 'stage_test')
          and pmi.plan_flag = 1
        order by pmi.project_stage_code, pmi.order_no
    </select>

    <update id="batchUpdateMilestoneInfo">
        <foreach collection="list" item="item" separator=";" index="index">
            update csm.proj_milestone_info
            <set>
                <if test="item.expectCompTime != null">
                    expect_comp_time = #{item.expectCompTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectStartTime != null">
                    expect_start_time = #{item.expectStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updaterId != null and item.updaterId != ''">
                    updater_id = #{item.updaterId,jdbcType=BIGINT},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP}
                </if>
            </set>
            where milestone_info_id = #{item.milestoneInfoId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getMilestoneInfoByProjectInfoIdOnlyNeedCheck"
            resultType="com.msun.csm.dao.entity.proj.ProjMilestoneInfo">
        select pmi.milestone_info_id,
               pmi.project_info_id,
               pmi.project_stage_id,
               pmi.project_stage_code,
               pmi.milestone_node_id,
               pmi.milestone_node_code,
               pmi.milestone_node_name,
               pmi.milestone_node_url,
               pmi.node_head_id,
               pmi.expect_comp_time,
               pmi.actual_comp_time,
               pmi.milestone_status,
               pmi.task_info_id,
               pmi.pre_node_flag,
               pmi.pre_node_id,
               pmi.invalid_flag,
               pmi.creater_id,
               pmi.create_time,
               pmi.updater_id,
               pmi.update_time,
               pmi.order_no,
               pmi.is_component,
               pmi.expect_start_time,
               pmi.plan_flag,
               pmi.check_flag,
               pmi.simple_milestone_node_name,
               pmi.check_url
        from csm.proj_milestone_info pmi
        where pmi.project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and pmi.check_flag = 1
          and pmi.invalid_flag = 0
    </select>
    <select id="getReportCount" resultType="java.lang.Integer">
        select
            count(*)
        from csm.proj_survey_report psr
        where project_info_id = #{projectInfoId}
            and online_essential = 1
            and is_deleted = 0
            <if test="projectPlanItemCode != 1">
                and finish_status not in (8)
            </if>
            <if test="projectPlanItemCode != 2">
                and finish_status = 7
            </if>
            <if test="yyProductId != null">
                and yy_product_id = #{yyProductId}
                and (finish_imgs is null or finish_imgs = '')
            </if>
            <if test="hospitalInfoId != null">
                and hospital_info_id = #{hospitalInfoId}
            </if>
       </select>
        <select id="getOldReportCount" resultType="java.lang.Integer">
            select
            count(*)
            from
            platform.survey_report sr
            inner join platform.product p on sr.product_id = p.id
            inner join csm.tmp_project_new_vs_old vos on  vos.old_custom_id = sr.customer_id and vos.old_project_info_id = sr.project_id
            where
            sr.del_flag = '0'
            and sr.status != '3'
            and sr.online_essential = '1'
            and sr.print_type = 'print'
            and vos.new_project_info_id = #{projectInfoId}
            <if test="yyProductId != null">
                and product_yunying_id = #{yyProductId}
            </if>
        </select>
    <select id="getFormCount" resultType="java.lang.Integer">
        select
        count(*)
        from csm.proj_survey_form psr
        where project_info_id = #{projectInfoId}
        and online_essential = 1
          and is_deleted = 0
        and finish_status not in (8)
        <if test="yyProductId != null">
            and yy_product_id = #{yyProductId}
            and (finish_imgs is null or finish_imgs = '')
        </if>
        <if test="hospitalInfoId != null">
            and hospital_info_id = #{hospitalInfoId}
        </if>
    </select>

    <update id="updateMilestoneById">
        update csm.proj_milestone_info
        set node_head_id     = #{nodeHeadId,jdbcType=BIGINT},
            actual_comp_time = #{expectCompTime,jdbcType=TIMESTAMP},
            milestone_status = #{milestoneStatus,jdbcType=INTEGER}
        where milestone_info_id = #{milestoneInfoId,jdbcType=BIGINT}
          and invalid_flag = 0
    </update>

    <select id="findByProjectInfoId" resultMap="BaseResultMap">
        select * from csm.proj_milestone_info
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
        and invalid_flag = 0
    </select>
    <select id="queryProjectMilestone" resultType="com.msun.csm.common.model.BaseIdNameExtendResp">
        select * from (
                          select  dmn.milestone_node_id as id ,
                                  dps.project_stage_name  as "stageName",
                                  dmn.milestone_node_name as "name",
                                  case when pmi.milestone_info_id is not null then 1 else 0 end as "isExist",
                              dps.order_no,cmn.order_no as morder_no
                          from csm.dict_milestone_node dmn
                          inner join csm.config_milestone_node cmn on dmn.milestone_node_id = cmn .milestone_node_id
                          inner  join csm.proj_project_info ppi on 1=1 and ppi.project_info_id = #{projectInfoId}
                          inner join csm.dict_project_stage dps  on cmn.project_stage_id = dps.id and dps.invalid_flag = 0
                          left join csm.proj_milestone_info pmi on dmn.milestone_node_id = pmi.milestone_node_id and
                                                                   pmi.project_info_id = #{projectInfoId}
                                                                     and pmi.invalid_flag = 0
                          where dmn.invalid_flag = 0
                            and
                              case when ppi.project_type = 1 then cmn.monomer_flag = 1
                                   when ppi.project_type = 2 then cmn.region_flag = 1
                                  end
                      ) ss
        group by id,name,order_no,morder_no,"stageName","isExist"
        order by order_no,morder_no

    </select>

    <update id="updateMilestoneByProjectPlan">
        update csm.proj_milestone_info set milestone_status =  #{statusCode}
        where project_info_id = #{projectInfoId}
        and milestone_node_code in (
        select dppi.project_plan_item_code from csm.proj_project_plan ppp
        inner join csm.dict_project_plan_item dppi on ppp.project_plan_item_id  = dppi.project_plan_item_id
        where 1= 1
        and ppp.project_info_id =  #{projectInfoId}
        <if test="projectPlanId != null">
            and  ppp.project_plan_id  = #{projectPlanId}
        </if>
        <if test="planItemCode != null">
            and dppi.project_plan_item_code  = #{planItemCode}
        </if>
        )
    </update>
    <update id="updateTime">
        update csm.proj_milestone_info
        set actual_comp_time = #{completeTime,jdbcType=TIMESTAMP}
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR}
    </update>

    <select id="getProjMilestoneInfoByProjectId" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneInfo">

        select distinct
            cmn.project_stage_id project_stage_id,
            dps.project_stage_code project_stage_code,
            dmn.milestone_node_id milestone_node_id,
            dmn.milestone_node_code milestone_node_code,
            dmn.milestone_node_name milestone_node_name,
            dmn.milestone_node_url milestone_node_url,
            0 milestone_status,
            cmn.pre_node_flag pre_node_flag,
            cmn.pre_node_id pre_node_id,
            cmn.order_no order_no,
            dmn.is_component is_component,
            cmn.plan_flag plan_flag,
            cmn.check_flag check_flag,
            dmn.simple_milestone_node_name simple_milestone_node_name,
            cmn.check_url check_url
        from csm.dict_milestone_node dmn
                 inner join csm.config_milestone_node cmn on dmn.milestone_node_id = cmn .milestone_node_id
                 inner join csm.dict_project_stage dps  on cmn.project_stage_id = dps.id and dps.invalid_flag = 0
                 left join csm.proj_milestone_info pmi on dmn.milestone_node_id = pmi.milestone_node_id and
                                                          pmi.project_info_id = #{projectInfoId}
            and pmi.invalid_flag = 0
        where  dmn.milestone_node_id = #{projmilestoneNodeId}
          and  dmn.invalid_flag  = 0

    </select>

    <select id="selectByProjectAndCode">
        select *
        from csm.proj_milestone_info
        where invalid_flag = 0
          and project_info_id = #{projectInfoId}
          and milestone_node_code = #{code}
    </select>
    <select id="getFormSurveyFinishCount" resultType="java.lang.Integer">
        select
            count(*)
        from csm.proj_survey_form psr
        left join (
            select
            x.*
            from
            csm.proj_business_examine_log x
            inner join (
            select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
            where is_deleted = 0 and business_type = 'form'
            group by business_id
        ) y on x.business_id = y.business_id and x.create_time = y.create_time
        where x.is_deleted = 0  and x.business_type = 'form'
        ) psreview on psr.survey_form_id = psreview.business_id
        where psr.project_info_id = #{projectInfoId}
        and (psreview.examine_status not in (1, 20, 21, 22) or psreview.examine_status is null)
        and psr.is_deleted = 0
        and psr.online_essential = 1
        <if test="yyProductId != null">
            and psr.yy_product_id = #{yyProductId}
        </if>
        <if test="hospitalInfoId != null">
            and psr.hospital_info_id = #{hospitalInfoId}
        </if>

    </select>

</mapper>
