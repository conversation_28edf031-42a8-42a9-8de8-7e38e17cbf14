<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjDeductionDetailProcessMapper">
    <select id="getProcessScoreRecord" resultType="com.msun.csm.dao.entity.proj.ProcessScoreRecordPO">
        select pddp.proj_deduction_detail_info_id as "id",
               pddp.yy_product_id,
               case
                   when pddp.yy_product_id = -99999 then '三方接口'
                   when dp.product_name is null then dpvm.yy_module_name
                   else dp.product_name
                   end                            as "productName",
               dps.project_stage_code,
               dps.project_stage_name,
               ddc.code                           as "deductionType",
               ddc.name                           as "deductionTypeName",
               pddp.item_detail,
               ppi.survey_complete_time,
               ppi.settle_in_time,
               ppi.pre_complete_time,
               ppi.online_time,
               ppi.accept_time,
               pddp.item_add_time,
               pddp.attachment_id,
               pddp.estimated_deduction,
               pddp.practical_deduction,
               pddp.remark,
               pddp.project_info_id
        from csm.proj_deduction_detail_info pddp
                 left join csm.dict_product dp on pddp.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
                 left join csm.dict_product_vs_modules dpvm on pddp.yy_product_id = dpvm.yy_module_id and dpvm.is_deleted = 0
                 left join csm.dict_project_stage dps on pddp.project_stage_code = dps.project_stage_code and dps.invalid_flag = 0
                 left join csm.dict_deduction_type ddc on pddp.deduction_type = ddc.code and ddc.is_deleted = 0
                 left join csm.proj_project_info ppi on pddp.project_info_id = ppi.project_info_id
        where pddp.is_deleted = 0
          and pddp.operation_type = 'process'
          and pddp.project_info_id = #{projectInfoId}
        order by dp.order_no, dps.order_no, ddc.sort_no, pddp.item_add_time
    </select>

    <update id="updateProjDeductionDetailProcessById">
        update csm.proj_deduction_detail_info
        <set>
            updater_id = #{updaterId},
            update_time = now(),
            <if test="practicalDeduction != null">
                practical_deduction = #{practicalDeduction},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="projectStageCode != null">
                project_stage_code = #{projectStageCode},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId},
            </if>
            <if test="itemDetail != null">
                item_detail = #{itemDetail},
            </if>
            <if test="itemAddTime != null">
                item_add_time = #{itemAddTime},
            </if>
            <if test="attachmentId != null">
                attachment_id = #{attachmentId},
            </if>
            <if test="deductionType != null">
                deduction_type = #{deductionType},
            </if>
        </set>
        where proj_deduction_detail_info_id = #{projDeductionDetailInfoId}
    </update>

    <select id="getProcessScoreRecordByYyProductId"
            resultType="com.msun.csm.dao.entity.proj.ProjDeductionDetailProcess">
        select pddp.*
        from csm.proj_deduction_detail_info pddp
        where pddp.is_deleted = 0
          and pddp.operation_type = 'process'
          and pddp.yy_product_id = #{yyProductId}
    </select>
</mapper>
