<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjHardwareProductMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjHardwareProduct">
        <!--@mbg.generated-->
        <!--@Table csm.proj_hardware_product-->
        <id column="hardware_product_id" jdbcType="BIGINT" property="hardwareProductId"/>
        <result column="hardware_info_id" jdbcType="BIGINT" property="hardwareInfoId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="suggest_config" jdbcType="VARCHAR" property="suggestConfig"/>
        <result column="suitable_dept" jdbcType="VARCHAR" property="suitableDept"/>
        <result column="require_flag" jdbcType="BIGINT" property="requireFlag"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        hardware_product_id, hardware_info_id, yy_product_id, is_deleted, creater_id, create_time,
        updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_hardware_product
        where hardware_product_id = #{hardwareProductId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_hardware_product
        where hardware_product_id = #{hardwareProductId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareProduct">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareProductId != null">
                hardware_product_id,
            </if>
            <if test="hardwareInfoId != null">
                hardware_info_id,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hardwareProductId != null">
                #{hardwareProductId,jdbcType=BIGINT},
            </if>
            <if test="hardwareInfoId != null">
                #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareProduct">
        <!--@mbg.generated-->
        update csm.proj_hardware_product
        <set>
            <if test="hardwareInfoId != null">
                hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where hardware_product_id = #{hardwareProductId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareProduct">
        <!--@mbg.generated-->
        update csm.proj_hardware_product
        set hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where hardware_product_id = #{hardwareProductId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_hardware_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hardware_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.hardwareInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.yyProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where hardware_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hardwareProductId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_hardware_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hardware_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hardwareInfoId != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.hardwareInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProductId != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.yyProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when hardware_product_id = #{item.hardwareProductId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where hardware_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hardwareProductId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_product
        (hardware_product_id, hardware_info_id, yy_product_id, suggest_config, suitable_dept, require_flag,
         is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hardwareProductId,jdbcType=BIGINT}, #{item.hardwareInfoId,jdbcType=BIGINT},
            #{item.yyProductId,jdbcType=BIGINT}, #{item.suggestConfig,jdbcType=VARCHAR}, #{item.suitableDept,jdbcType=VARCHAR},
            #{item.requireFlag,jdbcType=BIGINT},#{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareProduct">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_product
        (hardware_product_id, hardware_info_id, yy_product_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        (#{hardwareProductId,jdbcType=BIGINT}, #{hardwareInfoId,jdbcType=BIGINT}, #{yyProductId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        hardware_product_id = #{hardwareProductId,jdbcType=BIGINT},
        hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareProduct">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareProductId != null">
                hardware_product_id,
            </if>
            <if test="hardwareInfoId != null">
                hardware_info_id,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareProductId != null">
                #{hardwareProductId,jdbcType=BIGINT},
            </if>
            <if test="hardwareInfoId != null">
                #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="hardwareProductId != null">
                hardware_product_id = #{hardwareProductId,jdbcType=BIGINT},
            </if>
            <if test="hardwareInfoId != null">
                hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
