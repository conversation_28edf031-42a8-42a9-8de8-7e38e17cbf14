<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjDeductionDetailCommonMapper">

    <select id="getCommonScoreRecord" resultType="com.msun.csm.dao.entity.proj.CommonScoreRecordPO">
        select pddc.proj_deduction_detail_info_id as "id",
               pddc.project_info_id,
               pddc.deduction_type,
               ddc."name"                         as "deductionTypeName",
               pddc.attachment_id,
               pddc.practical_deduction,
               pddc.remark,
               pddc.classification_code,
               pddc."source"
        from csm.proj_deduction_detail_info pddc
                 left join csm.dict_deduction_type ddc on pddc.deduction_type = ddc.code and ddc.is_deleted = 0
        where pddc.is_deleted = 0
          and operation_type = 'common'
          and pddc.project_info_id = #{projectInfoId}
          and pddc.classification_code = #{classificationCode}
        order by ddc.sort_no, pddc.create_time
    </select>

    <update id="updateDeductionDetailCommonById" parameterType="com.msun.csm.dao.entity.proj.ProjDeductionDetailCommon">
        update csm.proj_deduction_detail_info
        <set>
            updater_id = #{updaterId},
            update_time = now(),
            <if test="practicalDeduction != null">
                practical_deduction = #{practicalDeduction},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="deductionType != null">
                deduction_type = #{deductionType},
            </if>
            <if test="attachmentId != null">
                attachment_id = #{attachmentId},
            </if>
        </set>
        where proj_deduction_detail_info_id = #{projDeductionDetailInfoId}
    </update>

    <update id="updateStatusToDeletedById">
        update csm.proj_deduction_detail_info
        set is_deleted = 1
        where proj_deduction_detail_info_id = #{id}
    </update>

    <delete id="deleteDeductionDetailCommonById">
        delete
        from csm.proj_deduction_detail_info
        where proj_deduction_detail_info_id = #{id}
    </delete>
</mapper>
