<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjPrereleaseMsgMapper">
    <insert id="insertData" parameterType="com.msun.csm.dao.entity.proj.ProjPrereleaseMsg">
        insert into csm.proj_prerelease_msg (
        creater_id,
        updater_id,
        plan_item_code,
        <if test="hospitalInfoId != null">
            hospital_info_id,
        </if>
        project_info_id,
        yy_product_id,
        msg_class_id,
        prerelease_msg_id,
        pre_plan_item_code
        )
        values (
        #{createrId,jdbcType=BIGINT},
        #{updaterId,jdbcType=BIGINT},
        #{planItemCode,jdbcType=VARCHAR},
        <if test="hospitalInfoId != null">
            #{hospitalInfoId,jdbcType=BIGINT},
        </if>
        #{projectInfoId,jdbcType=BIGINT},
        #{yyProductId,jdbcType=BIGINT},
        #{msgClassId,jdbcType=BIGINT},
        #{prereleaseMsgId,jdbcType=BIGINT},
        #{prePlanItemCode,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateDataToDeleted">
        update csm.proj_prerelease_msg
        set is_deleted = 1
        where prerelease_msg_id = #{prereleaseMsgId}
    </update>

</mapper>
