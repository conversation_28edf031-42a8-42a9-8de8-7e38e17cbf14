<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjIssueInfoMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjIssueInfo">
    <!--@mbg.generated-->
    <!--@Table csm.proj_issue_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId" />
    <result column="priority" jdbcType="BIGINT" property="priority" />
    <result column="classification" jdbcType="VARCHAR" property="classification" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="status" jdbcType="BIGINT" property="status" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="charge_person" jdbcType="BIGINT" property="chargePerson" />
    <result column="dept" jdbcType="VARCHAR" property="dept" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="submitter_id" jdbcType="BIGINT" property="submitterId" />
    <result column="project_plan_id" jdbcType="BIGINT" property="projectPlanId"/>
    <result column="todo_task_id" jdbcType="BIGINT" property="todoTaskId"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_info_id, priority, classification, description, "status", "result", charge_person,
    dept, product_id, creater_id, create_time, updater_id, update_time, is_deleted, submitter_id,
    project_plan_id, todo_task_id,operation_source,business_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.proj_issue_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.proj_issue_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjIssueInfo">
        <!--@mbg.generated-->
        insert into csm.proj_issue_info (id, project_info_id, priority,
        classification, description, "status",
        "result", charge_person, dept,
        product_id, creater_id, create_time,
        updater_id, update_time, is_deleted,
        submitter_id, project_plan_id, todo_task_id,
        operation_source, business_id
        )
        values (#{id,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{priority,jdbcType=BIGINT},
        #{classification,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{status,jdbcType=BIGINT},
        #{result,jdbcType=VARCHAR}, #{chargePerson,jdbcType=BIGINT}, #{dept,jdbcType=VARCHAR},
        #{productId,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},
        #{submitterId,jdbcType=BIGINT}, #{projectPlanId,jdbcType=BIGINT}, #{todoTaskId,jdbcType=BIGINT},
         #{operationSource,jdbcType=VARCHAR}, #{businessId,jdbcType=BIGINT}
        )
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjIssueInfo">
    <!--@mbg.generated-->
    insert into csm.proj_issue_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="classification != null">
        classification,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="result != null">
        "result",
      </if>
      <if test="chargePerson != null">
        charge_person,
      </if>
      <if test="dept != null">
        dept,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="submitterId != null">
        submitter_id,
      </if>
      <if test="projectPlanId != null">
        project_plan_id,
      </if>
      <if test="todoTaskId != null">
        todo_task_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=BIGINT},
      </if>
      <if test="classification != null">
        #{classification,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIGINT},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        #{chargePerson,jdbcType=BIGINT},
      </if>
      <if test="dept != null">
        #{dept,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="submitterId != null">
        #{submitterId,jdbcType=BIGINT},
      </if>
      <if test="projectPlanId != null">
        #{projectPlanId,jdbcType=BIGINT},
      </if>
      <if test="todoTaskId != null">
        #{todoTaskId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjIssueInfo">
      <!--@mbg.generated-->
      update csm.proj_issue_info
      <set>
          <if test="projectInfoId != null">
              project_info_id = #{projectInfoId,jdbcType=BIGINT},
          </if>
          <if test="priority != null">
              priority = #{priority,jdbcType=BIGINT},
          </if>
          <if test="classification != null">
              classification = #{classification,jdbcType=VARCHAR},
          </if>
          <if test="description != null">
              description = #{description,jdbcType=VARCHAR},
          </if>
          <if test="status != null">
              "status" = #{status,jdbcType=BIGINT},
          </if>
          <if test="result != null">
              "result" = #{result,jdbcType=VARCHAR},
          </if>
          <if test="chargePerson != null">
              charge_person = #{chargePerson,jdbcType=BIGINT},
          </if>
          <if test="dept != null">
              dept = #{dept,jdbcType=VARCHAR},
          </if>
          <if test="productId != null">
              product_id = #{productId,jdbcType=INTEGER},
          </if>
          <if test="createrId != null">
              creater_id = #{createrId,jdbcType=BIGINT},
          </if>
          <if test="createTime != null">
              create_time = #{createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updaterId != null">
              updater_id = #{updaterId,jdbcType=BIGINT},
          </if>
          <if test="updateTime != null">
              update_time = #{updateTime,jdbcType=TIMESTAMP},
          </if>
          <if test="isDeleted != null">
              is_deleted = #{isDeleted,jdbcType=SMALLINT},
          </if>
          <if test="submitterId != null">
              submitter_id = #{submitterId,jdbcType=BIGINT},
          </if>
          <if test="projectPlanId != null">
              project_plan_id = #{projectPlanId,jdbcType=BIGINT},
          </if>
          <if test="todoTaskId != null">
              todo_task_id = #{todoTaskId,jdbcType=BIGINT},
          </if>
          <if test="businessId != null">
              business_id = #{businessId,jdbcType=BIGINT},
          </if>
      </set>
      where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjIssueInfo">
    <!--@mbg.generated-->
    update csm.proj_issue_info
    set project_info_id = #{projectInfoId,jdbcType=BIGINT},
      priority = #{priority,jdbcType=BIGINT},
      classification = #{classification,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
    "status" = #{status,jdbcType=BIGINT},
    "result" = #{result,jdbcType=VARCHAR},
      charge_person = #{chargePerson,jdbcType=BIGINT},
      dept = #{dept,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
    submitter_id = #{submitterId,jdbcType=BIGINT},
    project_plan_id = #{projectPlanId,jdbcType=BIGINT},
    todo_task_id = #{todoTaskId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_issue_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="priority = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priority,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="classification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.classification,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="&quot;status&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="&quot;result&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.result,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="charge_person = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.chargePerson,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="dept = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dept,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.productId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="submitter_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.submitterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="project_plan_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.projectPlanId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="todo_task_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.todoTaskId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_issue_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectInfoId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="priority = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priority != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priority,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="classification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.classification != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.classification,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="&quot;status&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="&quot;result&quot; = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.result != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.result,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="charge_person = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.chargePerson != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.chargePerson,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="dept = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dept != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dept,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.productId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="submitter_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.submitterId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.submitterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="project_plan_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectPlanId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.projectPlanId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="todo_task_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.todoTaskId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.todoTaskId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.proj_issue_info
    (id, project_info_id, priority, classification, description, "status", "result",
    charge_person, dept, product_id, creater_id, create_time, updater_id, update_time,
    is_deleted, submitter_id, project_plan_id, todo_task_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT}, #{item.priority,jdbcType=BIGINT},
      #{item.classification,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
      #{item.status,jdbcType=BIGINT}, #{item.result,jdbcType=VARCHAR}, #{item.chargePerson,jdbcType=BIGINT},
      #{item.dept,jdbcType=VARCHAR}, #{item.productId,jdbcType=INTEGER}, #{item.createrId,jdbcType=BIGINT},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.isDeleted,jdbcType=SMALLINT}, #{item.submitterId,jdbcType=BIGINT}, #{item.projectPlanId,jdbcType=BIGINT},
      #{item.todoTaskId,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjIssueInfo">
    <!--@mbg.generated-->
    insert into csm.proj_issue_info
    (id, project_info_id, priority, classification, description, "status", "result",
    charge_person, dept, product_id, creater_id, create_time, updater_id, update_time,
    is_deleted, submitter_id, project_plan_id, todo_task_id)
    values
    (#{id,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{priority,jdbcType=BIGINT},
    #{classification,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{status,jdbcType=BIGINT},
    #{result,jdbcType=VARCHAR}, #{chargePerson,jdbcType=BIGINT}, #{dept,jdbcType=VARCHAR},
    #{productId,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
    #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},
    #{submitterId,jdbcType=BIGINT}, #{projectPlanId,jdbcType=BIGINT}, #{todoTaskId,jdbcType=BIGINT}
    )
    on duplicate key update
    id = #{id,jdbcType=BIGINT},
    project_info_id = #{projectInfoId,jdbcType=BIGINT},
    priority = #{priority,jdbcType=BIGINT},
    classification = #{classification,jdbcType=VARCHAR},
    description = #{description,jdbcType=VARCHAR},
    "status" = #{status,jdbcType=BIGINT},
    "result" = #{result,jdbcType=VARCHAR},
    charge_person = #{chargePerson,jdbcType=BIGINT},
    dept = #{dept,jdbcType=VARCHAR},
    product_id = #{productId,jdbcType=INTEGER},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{isDeleted,jdbcType=SMALLINT},
    submitter_id = #{submitterId,jdbcType=BIGINT},
    project_plan_id = #{projectPlanId,jdbcType=BIGINT},
    todo_task_id = #{todoTaskId,jdbcType=BIGINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjIssueInfo">
    <!--@mbg.generated-->
    insert into csm.proj_issue_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="classification != null">
        classification,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="result != null">
        "result",
      </if>
      <if test="chargePerson != null">
        charge_person,
      </if>
      <if test="dept != null">
        dept,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="submitterId != null">
        submitter_id,
      </if>
      <if test="projectPlanId != null">
        project_plan_id,
      </if>
      <if test="todoTaskId != null">
        todo_task_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=BIGINT},
      </if>
      <if test="classification != null">
        #{classification,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIGINT},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        #{chargePerson,jdbcType=BIGINT},
      </if>
      <if test="dept != null">
        #{dept,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="submitterId != null">
        #{submitterId,jdbcType=BIGINT},
      </if>
      <if test="projectPlanId != null">
        #{projectPlanId,jdbcType=BIGINT},
      </if>
      <if test="todoTaskId != null">
        #{todoTaskId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=BIGINT},
      </if>
      <if test="classification != null">
        classification = #{classification,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=BIGINT},
      </if>
      <if test="result != null">
        "result" = #{result,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        charge_person = #{chargePerson,jdbcType=BIGINT},
      </if>
      <if test="dept != null">
        dept = #{dept,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="submitterId != null">
        submitter_id = #{submitterId,jdbcType=BIGINT},
      </if>
      <if test="projectPlanId != null">
        project_plan_id = #{projectPlanId,jdbcType=BIGINT},
      </if>
      <if test="todoTaskId != null">
        todo_task_id = #{todoTaskId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

    <update id="deleteBatchIds">
        update csm.proj_issue_info set is_deleted = 1 where id in
      <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="queryData" resultType="com.msun.csm.model.resp.issue.IssueDataResp">
      select pti.*,
             ctp.name                                                                            as priority_name,
             cts.name                                                                            as status_name,
             charge.user_name                                                                    as charge_person_name,-- 问题负责人
             cre.user_name                                                                       as creater_name,-- 问题记录人
             upd.user_name                                                                       as updater_name,-- 更新人
             sub.user_name                                                                       as submitter_name,-- 问题提出人
             ppp.title                                                                           as "planTitle",
             pddi.deduction_type                                                                 as "deductionTypeCode",-- 扣分项分类编码
             ddt."name"                                                                          as "deductionTypeName",-- 扣分项名称
             ddt.issue_classification_id                                                         as "issueClassificationId",-- 问题分类
             cic.acceptance_classification_code                                                  as "acceptanceClassificationCode",-- 验收分类编码
             dac.acceptance_classification_name                                                  as "acceptanceClassificationName",-- 验收分类名称
             pddi.practical_deduction                                                            as "deductionScore",
             pddi."source"                                                                       as "source",
             case when pddi."source" = 'first' then '首验'
                  when pddi."source" = 'final' then '终验'
                  else '' end                         as "stageName",
             case when dp.product_name is null then dpvo.yy_module_name else dp.product_name end as product_name
      from csm.proj_issue_info pti
             left join csm.config_issue_priority ctp on ctp.id = pti.priority
             left join csm.config_issue_status cts on cts.id = pti.status
             left join csm.sys_user charge on pti.charge_person = charge.sys_user_id
             left join csm.sys_user cre on pti.creater_id = cre.sys_user_id
             left join csm.sys_user upd on pti.updater_id = upd.sys_user_id
             left join csm.sys_user sub on pti.submitter_id = sub.sys_user_id
             left join csm.dict_product dp on pti.product_id = dp.yy_product_id
             left join csm.dict_product_vs_modules dpvo on pti.product_id = dpvo.yy_module_id
             left join csm.proj_project_plan ppp on pti.project_plan_id = ppp.project_plan_id
             left join csm.proj_deduction_detail_info pddi on pti.business_id = pddi.proj_deduction_detail_info_id and pddi.is_deleted = 0
             left join csm.dict_deduction_type ddt on pddi.deduction_type = ddt.code and ddt.is_deleted = 0
             left join csm.config_issue_classification cic on ddt.issue_classification_id = cic.id and cic.is_deleted = 0
             left join csm.dict_acceptance_classification dac on cic.acceptance_classification_code = dac.acceptance_classification_code and dac.is_deleted = 0
      where pti.is_deleted = 0
        and pti.project_info_id = #{projectInfoId}
      <if test="operationSource != null">
        and pti.operation_source = #{operationSource}
      </if>
      <if test="source != null and source != ''">
        and pddi."source" = #{source}
      </if>
      <if test="createUserId != null">
        and pti.creater_id = #{createUserId}
      </if>
      <if test="chargePersonId != null">
        and pti.charge_person = #{chargePersonId}
      </if>
      <if test="submitterId != null">
        and pti.submitter_id = #{submitterId}
      </if>
      <if test="status != null">
        and pti.status = #{status}
      </if>
      <if test="classificationList != null and classificationList.size() &gt; 0">
        and pti.classification in
        <foreach close=")" collection="classificationList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="productIdList != null and productIdList.size() &gt; 0">
        and pti.product_id in
        <foreach close=")" collection="productIdList" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="startDate != null">
        and pti.create_time &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        and pti.create_time &lt;= #{endDate}
      </if>
      <if test="finishedFlag != null and finishedFlag == 0">
        and pti.status in (select id from csm.config_issue_status where cts.properties = 1)
      </if>
      <if test="finishedFlag != null and finishedFlag == 1">
        and pti.status in (select id from csm.config_issue_status where cts.properties = 2)
      </if>
      <if test="keyWord != null and keyWord != ''">
        and (pti.classification like CONCAT('%', #{keyWord}, '%')
          or pti.description like CONCAT('%', #{keyWord}, '%')
          or pti.result like CONCAT('%', #{keyWord}, '%'))
      </if>
      <if test="projectPlanId != null">
        and (pti.project_plan_id = #{projectPlanId} or
             todo_task_id in (select todo_task_id from csm.proj_todo_task where project_plan_id = #{projectPlanId}))
      </if>
      <if test="todoTaskId != null">
        and pti.todo_task_id = #{todoTaskId}
      </if>
      order by pti.create_time desc, pti.id desc
    </select>

    <select id="getClassificationList" resultType="java.lang.String">
        select distinct pti.classification
        from csm.proj_issue_info pti
        where pti.is_deleted = 0
          and pti.project_info_id = #{projectInfoId}
          and pti.classification is not null
          and pti.classification != ''
          and pti.classification not in (SELECT name from config_issue_classification where is_deleted = 0)
    </select>

    <select id="queryCount" resultType="int">
        select count(1)
        from csm.proj_issue_info pti
        where pti.is_deleted = 0
          and pti.project_info_id = #{projectInfoId}
    </select>

  <select id="selectByIdList" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from csm.proj_issue_info
      where is_deleted = 0
        and id in
      <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
          #{item}
      </foreach>
  </select>
</mapper>
