<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectFileMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectFile">
    <!--@mbg.generated-->
    <!--@Table csm.proj_project_file-->
    <id column="project_file_id" jdbcType="BIGINT" property="projectFileId" />
    <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId" />
    <result column="project_stage_code" jdbcType="VARCHAR" property="projectStageCode" />
    <result column="milestone_node_code" jdbcType="VARCHAR" property="milestoneNodeCode" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_desc" jdbcType="VARCHAR" property="fileDesc" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    project_file_id, project_info_id, project_stage_code, milestone_node_code, file_name,
    file_path, file_desc, is_deleted, creater_id, create_time, updater_id, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.proj_project_file
    where project_file_id = #{projectFileId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.proj_project_file
    where project_file_id = #{projectFileId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectFile">
    <!--@mbg.generated-->
    insert into csm.proj_project_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectFileId != null">
        project_file_id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="projectStageCode != null">
        project_stage_code,
      </if>
      <if test="milestoneNodeCode != null">
        milestone_node_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileDesc != null">
        file_desc,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectFileId != null">
        #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="projectStageCode != null">
        #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="milestoneNodeCode != null">
        #{milestoneNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileDesc != null">
        #{fileDesc,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectFile">
    <!--@mbg.generated-->
    update csm.proj_project_file
    <set>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="projectStageCode != null">
        project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="milestoneNodeCode != null">
        milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileDesc != null">
        file_desc = #{fileDesc,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where project_file_id = #{projectFileId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectFile">
    <!--@mbg.generated-->
    update csm.proj_project_file
    set project_info_id = #{projectInfoId,jdbcType=BIGINT},
      project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_desc = #{fileDesc,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where project_file_id = #{projectFileId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_project_file
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="project_stage_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.projectStageCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="milestone_node_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.milestoneNodeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.fileName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.filePath,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="file_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.fileDesc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where project_file_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.projectFileId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_project_file
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectInfoId != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="project_stage_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectStageCode != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.projectStageCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="milestone_node_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.milestoneNodeCode != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.milestoneNodeCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="file_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fileName != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.fileName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="file_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.filePath != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.filePath,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="file_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fileDesc != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.fileDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when project_file_id = #{item.projectFileId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where project_file_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.projectFileId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.proj_project_file
    (project_file_id, project_info_id, project_stage_code, milestone_node_code, file_name,
      file_path, file_desc, is_deleted, creater_id, create_time, updater_id, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.projectFileId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT}, #{item.projectStageCode,jdbcType=VARCHAR},
        #{item.milestoneNodeCode,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR},
        #{item.filePath,jdbcType=VARCHAR}, #{item.fileDesc,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT},
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProjectFile">
    <!--@mbg.generated-->
    insert into csm.proj_project_file
    (project_file_id, project_info_id, project_stage_code, milestone_node_code, file_name,
      file_path, file_desc, is_deleted, creater_id, create_time, updater_id, update_time
      )
    values
    (#{projectFileId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{projectStageCode,jdbcType=VARCHAR},
      #{milestoneNodeCode,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR},
      #{fileDesc,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
      )
    on duplicate key update
    project_file_id = #{projectFileId,jdbcType=BIGINT},
    project_info_id = #{projectInfoId,jdbcType=BIGINT},
    project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
    milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
    file_name = #{fileName,jdbcType=VARCHAR},
    file_path = #{filePath,jdbcType=VARCHAR},
    file_desc = #{fileDesc,jdbcType=VARCHAR},
    is_deleted = #{isDeleted,jdbcType=SMALLINT},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectFile">
    <!--@mbg.generated-->
    insert into csm.proj_project_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectFileId != null">
        project_file_id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="projectStageCode != null">
        project_stage_code,
      </if>
      <if test="milestoneNodeCode != null">
        milestone_node_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileDesc != null">
        file_desc,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectFileId != null">
        #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="projectStageCode != null">
        #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="milestoneNodeCode != null">
        #{milestoneNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileDesc != null">
        #{fileDesc,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="projectFileId != null">
        project_file_id = #{projectFileId,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="projectStageCode != null">
        project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="milestoneNodeCode != null">
        milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileDesc != null">
        file_desc = #{fileDesc,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="findProjectFiles" resultType="com.msun.csm.model.vo.ProjProjectFileVO">
    select ppf.project_file_id,ppf.project_info_id,ppf.project_stage_code,
    ppf.milestone_node_code,ppf.file_name,ppf.file_path,ppf.file_desc,
    ppf.create_time,su.user_name as creater_name
    from csm.proj_project_file ppf
    left join csm.sys_user su on ppf.creater_id = su.sys_user_id
    where ppf.project_info_id = #{projectInfoId}
    and ppf.milestone_node_code = #{milestoneNodeCode}
    and ppf.project_stage_code = #{projectStageCode}
    and ppf.is_deleted = 0
  </select>

    <select id="getProjectFilesByProjectInfoId" resultType="com.msun.csm.dao.entity.proj.ProjProjectFile">
        select
            ppf.project_file_id,
            ppf.project_info_id,
            ppf.project_stage_code,
            ppf.milestone_node_code,
            ppf.file_name,
            ppf.file_path,
            ppf.file_desc,
            ppf.create_time
        from
            csm.proj_project_file ppf
        where
            ppf.is_deleted = 0
          and ppf.project_info_id = #{projectInfoId}
    </select>

  <select id="selectProjectFileByIds" resultMap="BaseResultMap">
    select * from csm.proj_project_file
    where is_deleted = 0
    and project_file_id in
    <foreach collection="projectFileIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>
