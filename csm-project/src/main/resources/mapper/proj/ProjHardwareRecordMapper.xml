<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjHardwareRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjHardwareRecord">
        <!--@mbg.generated-->
        <!--@Table csm.proj_hardware_record-->
        <id column="hardware_record_id" jdbcType="BIGINT" property="hardwareRecordId"/>
        <result column="hardware_info_id" jdbcType="BIGINT" property="hardwareInfoId"/>
        <result column="purchase_property" jdbcType="SMALLINT" property="purchaseProperty"/>
        <result column="current_model" jdbcType="VARCHAR" property="currentModel"/>
        <result column="current_amount" jdbcType="INTEGER" property="currentAmount"/>
        <result column="purchase_model" jdbcType="VARCHAR" property="purchaseModel"/>
        <result column="purchase_amount" jdbcType="INTEGER" property="purchaseAmount"/>
        <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectByPage" parameterType="com.msun.csm.model.dto.ProjHardwareRecordPageDTO"
            resultType="com.msun.csm.model.vo.ProjHardwareRecordVO">
        select phr.hardware_record_id,dhi.hardware_name,phr.purchase_property,phr.current_model,
               phr.current_amount,phr.purchase_model,phr.purchase_amount,dhi.hardware_info_id,
               dhi.config_require,phi.hospital_name,phr.memo,phr.hospital_info_id,
               (select string_agg(recommend_model,'/') from csm.proj_hardware_recommend
                  where hardware_info_id = phr.hardware_info_id and is_deleted = 0) as recommend_model
        from csm.proj_hardware_record phr
        left join csm.dict_hardware_info dhi on phr.hardware_info_id = dhi.hardware_info_id and dhi.is_deleted = 0
        left join csm.proj_hospital_info phi on phr.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        where 1 = 1
        <if test="customInfoId != null and customInfoId != 0 ">
            and phr.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null and projectInfoId != 0 ">
            and phr.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null and hospitalInfoId != 0 ">
            and phr.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="purchaseProperty != null and purchaseProperty != 0 ">
            and phr.purchase_property = #{purchaseProperty}
        </if>
        <if test='hardwareName != null and hardwareName != "" '>
            and dhi.hardware_name ilike concat('%',#{hardwareName},'%')
        </if>
        and phr.is_deleted = 0
        order by phr.create_time desc
    </select>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        hardware_record_id, hardware_info_id, purchase_property, current_model, current_amount,
        purchase_model, purchase_amount, hospital_info_id, memo, is_deleted, creater_id,
        create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_hardware_record
        where hardware_record_id = #{hardwareRecordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_hardware_record
        where hardware_record_id = #{hardwareRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareRecord">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareRecordId != null">
                hardware_record_id,
            </if>
            <if test="hardwareInfoId != null">
                hardware_info_id,
            </if>
            <if test="purchaseProperty != null">
                purchase_property,
            </if>
            <if test="currentModel != null">
                current_model,
            </if>
            <if test="currentAmount != null">
                current_amount,
            </if>
            <if test="purchaseModel != null">
                purchase_model,
            </if>
            <if test="purchaseAmount != null">
                purchase_amount,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hardwareRecordId != null">
                #{hardwareRecordId,jdbcType=BIGINT},
            </if>
            <if test="hardwareInfoId != null">
                #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="purchaseProperty != null">
                #{purchaseProperty,jdbcType=SMALLINT},
            </if>
            <if test="currentModel != null">
                #{currentModel,jdbcType=VARCHAR},
            </if>
            <if test="currentAmount != null">
                #{currentAmount,jdbcType=INTEGER},
            </if>
            <if test="purchaseModel != null">
                #{purchaseModel,jdbcType=VARCHAR},
            </if>
            <if test="purchaseAmount != null">
                #{purchaseAmount,jdbcType=INTEGER},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareRecord">
        <!--@mbg.generated-->
        update csm.proj_hardware_record
        <set>
            <if test="hardwareInfoId != null">
                hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="purchaseProperty != null">
                purchase_property = #{purchaseProperty,jdbcType=SMALLINT},
            </if>
            <if test="currentModel != null">
                current_model = #{currentModel,jdbcType=VARCHAR},
            </if>
            <if test="currentAmount != null">
                current_amount = #{currentAmount,jdbcType=INTEGER},
            </if>
            <if test="purchaseModel != null">
                purchase_model = #{purchaseModel,jdbcType=VARCHAR},
            </if>
            <if test="purchaseAmount != null">
                purchase_amount = #{purchaseAmount,jdbcType=INTEGER},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where hardware_record_id = #{hardwareRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareRecord">
        <!--@mbg.generated-->
        update csm.proj_hardware_record
        set hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
        purchase_property = #{purchaseProperty,jdbcType=SMALLINT},
        current_model = #{currentModel,jdbcType=VARCHAR},
        current_amount = #{currentAmount,jdbcType=INTEGER},
        purchase_model = #{purchaseModel,jdbcType=VARCHAR},
        purchase_amount = #{purchaseAmount,jdbcType=INTEGER},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        memo = #{memo,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where hardware_record_id = #{hardwareRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_hardware_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hardware_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.hardwareInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="purchase_property = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.purchaseProperty,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="current_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.currentModel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.currentAmount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="purchase_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.purchaseModel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.purchaseAmount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.hospitalInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="memo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.memo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where hardware_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hardwareRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_hardware_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hardware_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hardwareInfoId != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.hardwareInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="purchase_property = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.purchaseProperty != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.purchaseProperty,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="current_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currentModel != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.currentModel,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="current_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.currentAmount != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.currentAmount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="purchase_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.purchaseModel != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.purchaseModel,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="purchase_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.purchaseAmount != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.purchaseAmount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalInfoId != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.hospitalInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="memo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.memo != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.memo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when hardware_record_id = #{item.hardwareRecordId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where hardware_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hardwareRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_record
        (hardware_record_id, custom_info_id, project_info_id, hardware_info_id,
         purchase_property, current_model, current_amount,purchase_model, purchase_amount,
         hospital_info_id, memo, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hardwareRecordId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},#{item.hardwareInfoId,jdbcType=BIGINT},
            #{item.purchaseProperty,jdbcType=SMALLINT}, #{item.currentModel,jdbcType=VARCHAR},
            #{item.currentAmount,jdbcType=INTEGER}, #{item.purchaseModel,jdbcType=VARCHAR},
            #{item.purchaseAmount,jdbcType=INTEGER}, #{item.hospitalInfoId,jdbcType=BIGINT},
            #{item.memo,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareRecord">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_record
        (hardware_record_id, hardware_info_id, purchase_property, current_model, current_amount,
        purchase_model, purchase_amount, hospital_info_id, memo, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        (#{hardwareRecordId,jdbcType=BIGINT}, #{hardwareInfoId,jdbcType=BIGINT}, #{purchaseProperty,jdbcType=SMALLINT},
        #{currentModel,jdbcType=VARCHAR}, #{currentAmount,jdbcType=INTEGER}, #{purchaseModel,jdbcType=VARCHAR},
        #{purchaseAmount,jdbcType=INTEGER}, #{hospitalInfoId,jdbcType=BIGINT}, #{memo,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        hardware_record_id = #{hardwareRecordId,jdbcType=BIGINT},
        hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
        purchase_property = #{purchaseProperty,jdbcType=SMALLINT},
        current_model = #{currentModel,jdbcType=VARCHAR},
        current_amount = #{currentAmount,jdbcType=INTEGER},
        purchase_model = #{purchaseModel,jdbcType=VARCHAR},
        purchase_amount = #{purchaseAmount,jdbcType=INTEGER},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        memo = #{memo,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjHardwareRecord">
        <!--@mbg.generated-->
        insert into csm.proj_hardware_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareRecordId != null">
                hardware_record_id,
            </if>
            <if test="hardwareInfoId != null">
                hardware_info_id,
            </if>
            <if test="purchaseProperty != null">
                purchase_property,
            </if>
            <if test="currentModel != null">
                current_model,
            </if>
            <if test="currentAmount != null">
                current_amount,
            </if>
            <if test="purchaseModel != null">
                purchase_model,
            </if>
            <if test="purchaseAmount != null">
                purchase_amount,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareRecordId != null">
                #{hardwareRecordId,jdbcType=BIGINT},
            </if>
            <if test="hardwareInfoId != null">
                #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="purchaseProperty != null">
                #{purchaseProperty,jdbcType=SMALLINT},
            </if>
            <if test="currentModel != null">
                #{currentModel,jdbcType=VARCHAR},
            </if>
            <if test="currentAmount != null">
                #{currentAmount,jdbcType=INTEGER},
            </if>
            <if test="purchaseModel != null">
                #{purchaseModel,jdbcType=VARCHAR},
            </if>
            <if test="purchaseAmount != null">
                #{purchaseAmount,jdbcType=INTEGER},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="hardwareRecordId != null">
                hardware_record_id = #{hardwareRecordId,jdbcType=BIGINT},
            </if>
            <if test="hardwareInfoId != null">
                hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="purchaseProperty != null">
                purchase_property = #{purchaseProperty,jdbcType=SMALLINT},
            </if>
            <if test="currentModel != null">
                current_model = #{currentModel,jdbcType=VARCHAR},
            </if>
            <if test="currentAmount != null">
                current_amount = #{currentAmount,jdbcType=INTEGER},
            </if>
            <if test="purchaseModel != null">
                purchase_model = #{purchaseModel,jdbcType=VARCHAR},
            </if>
            <if test="purchaseAmount != null">
                purchase_amount = #{purchaseAmount,jdbcType=INTEGER},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByCustomInfoId">
        update csm.proj_hardware_record
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where hardware_record_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
