<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductSupplementaryRecordMapper">
    <resultMap type="com.msun.csm.dao.entity.proj.ProjProductSupplementaryRecord"
               id="ProjProductSupplementaryRecordMap">
        <result property="productSupplementaryRecordId" column="product_supplementary_record_id" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="orderInfoId" column="order_info_id" jdbcType="INTEGER"/>
        <result property="customInfoId" column="custom_info_id" jdbcType="INTEGER"/>
        <result property="specialApprovalType" column="special_approval_type" jdbcType="INTEGER"/>
        <result property="purchaseMode" column="purchase_mode" jdbcType="INTEGER"/>
        <result property="projectFileId" column="project_file_id" jdbcType="VARCHAR"/>
        <result property="effectiveStartTime" column="effective_start_time" jdbcType="TIMESTAMP"/>
        <result property="effectiveCompTime" column="effective_comp_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <update id="updateByCustomInfoId">
        update proj_product_supplementary_record
        set custom_info_id =#{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="orderInfoIds != null and orderInfoIds.size() != 0">
            and proj_product_supplementary_record.order_info_id in
            <foreach collection="orderInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
