<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipRecordVsLisMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis">
        <!--@mbg.generated-->
        <!--@Table csm.proj_equip_record_vs_lis-->
        <result column="equip_record_vs_lis_id" jdbcType="BIGINT" property="equipRecordVsLisId"/>
        <result column="equip_record_id" jdbcType="BIGINT" property="equipRecordId"/>
        <result column="duplex_flag" jdbcType="SMALLINT" property="duplexFlag"/>
        <result column="emergency_flag" jdbcType="SMALLINT" property="emergencyFlag"/>
        <result column="quality_control_flag" jdbcType="SMALLINT" property="qualityControlFlag"/>
        <result column="equip_test_progress" jdbcType="VARCHAR" property="equipTestProgress"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        equip_record_vs_lis_id, equip_record_id, duplex_flag, emergency_flag, quality_control_flag,
        equip_test_progress, is_deleted, creater_id, create_time, updater_id, update_time
    </sql>
    <update id="updateVsLis">
        update csm.proj_equip_record_vs_lis
        <set>
            equip_record_id = #{equipRecordId,jdbcType=BIGINT},
            duplex_flag = #{duplexFlag,jdbcType=INTEGER},
            emergency_flag = #{emergencyFlag,jdbcType=INTEGER},
            quality_control_flag = #{qualityControlFlag,jdbcType=INTEGER},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            resource_equip_factory_id = #{resourceEquipFactoryId,jdbcType=BIGINT},
            resource_equip_factory_name = #{resourceEquipFactoryName,jdbcType=VARCHAR},
            resource_equip_type_id = #{resourceEquipTypeId,jdbcType=BIGINT},
            resource_equip_type_name = #{resourceEquipTypeName,jdbcType=VARCHAR},
            resource_equip_info_id = #{resourceEquipInfoId,jdbcType=BIGINT},
            resource_equip_model_name = #{resourceEquipModelName,jdbcType=VARCHAR},
            is_recheck_flag = #{isRecheckFlag,jdbcType=INTEGER},
            bar_code = #{barCode,jdbcType=VARCHAR},
            item_name01 = #{itemName01,jdbcType=VARCHAR},
            item_name02 = #{itemName02,jdbcType=VARCHAR},
            item_channel01 = #{itemChannel01,jdbcType=VARCHAR},
            item_channel02 = #{itemChannel02,jdbcType=VARCHAR},
            item_result01 = #{itemResult01,jdbcType=VARCHAR},
            item_result02 = #{itemResult02,jdbcType=VARCHAR},
            query_sql = #{querySql,jdbcType=VARCHAR},
            qua_bar_code = #{quaBarCode,jdbcType=VARCHAR},
            qua_result = #{quaResult,jdbcType=VARCHAR},
            emer_bar_code = #{emerBarCode,jdbcType=VARCHAR},
            db_file_path = #{dbFilePath,jdbcType=VARCHAR},
            db_password = #{dbPassword,jdbcType=VARCHAR},
            report_with_img_flag = #{reportWithImgFlag,jdbcType=INTEGER},
        </set>
        where equip_record_vs_lis_id = #{equipRecordVsLisId,jdbcType=BIGINT}
    </update>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record_vs_lis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipRecordVsLisId != null">
                equip_record_vs_lis_id,
            </if>
            <if test="equipRecordId != null">
                equip_record_id,
            </if>
            <if test="duplexFlag != null">
                duplex_flag,
            </if>
            <if test="emergencyFlag != null">
                emergency_flag,
            </if>
            <if test="qualityControlFlag != null">
                quality_control_flag,
            </if>
            <if test="equipTestProgress != null">
                equip_test_progress,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipRecordVsLisId != null">
                #{equipRecordVsLisId,jdbcType=BIGINT},
            </if>
            <if test="equipRecordId != null">
                #{equipRecordId,jdbcType=BIGINT},
            </if>
            <if test="duplexFlag != null">
                #{duplexFlag,jdbcType=SMALLINT},
            </if>
            <if test="emergencyFlag != null">
                #{emergencyFlag,jdbcType=SMALLINT},
            </if>
            <if test="qualityControlFlag != null">
                #{qualityControlFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipTestProgress != null">
                #{equipTestProgress,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record_vs_lis
        (equip_record_vs_lis_id, equip_record_id, duplex_flag, emergency_flag, quality_control_flag,
        equip_test_progress, is_deleted, creater_id, create_time, updater_id, update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.equipRecordVsLisId,jdbcType=BIGINT}, #{item.equipRecordId,jdbcType=BIGINT},
            #{item.duplexFlag,jdbcType=SMALLINT}, #{item.emergencyFlag,jdbcType=SMALLINT},
            #{item.qualityControlFlag,jdbcType=SMALLINT}, #{item.equipTestProgress,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record_vs_lis
        (equip_record_vs_lis_id, equip_record_id, duplex_flag, emergency_flag, quality_control_flag,
        equip_test_progress, is_deleted, creater_id, create_time, updater_id, update_time
        )
        values
        (#{equipRecordVsLisId,jdbcType=BIGINT}, #{equipRecordId,jdbcType=BIGINT}, #{duplexFlag,jdbcType=SMALLINT},
        #{emergencyFlag,jdbcType=SMALLINT}, #{qualityControlFlag,jdbcType=SMALLINT},
        #{equipTestProgress,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        equip_record_vs_lis_id = #{equipRecordVsLisId,jdbcType=BIGINT},
        equip_record_id = #{equipRecordId,jdbcType=BIGINT},
        duplex_flag = #{duplexFlag,jdbcType=SMALLINT},
        emergency_flag = #{emergencyFlag,jdbcType=SMALLINT},
        quality_control_flag = #{qualityControlFlag,jdbcType=SMALLINT},
        equip_test_progress = #{equipTestProgress,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record_vs_lis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipRecordVsLisId != null">
                equip_record_vs_lis_id,
            </if>
            <if test="equipRecordId != null">
                equip_record_id,
            </if>
            <if test="duplexFlag != null">
                duplex_flag,
            </if>
            <if test="emergencyFlag != null">
                emergency_flag,
            </if>
            <if test="qualityControlFlag != null">
                quality_control_flag,
            </if>
            <if test="equipTestProgress != null">
                equip_test_progress,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipRecordVsLisId != null">
                #{equipRecordVsLisId,jdbcType=BIGINT},
            </if>
            <if test="equipRecordId != null">
                #{equipRecordId,jdbcType=BIGINT},
            </if>
            <if test="duplexFlag != null">
                #{duplexFlag,jdbcType=SMALLINT},
            </if>
            <if test="emergencyFlag != null">
                #{emergencyFlag,jdbcType=SMALLINT},
            </if>
            <if test="qualityControlFlag != null">
                #{qualityControlFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipTestProgress != null">
                #{equipTestProgress,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="equipRecordVsLisId != null">
                equip_record_vs_lis_id = #{equipRecordVsLisId,jdbcType=BIGINT},
            </if>
            <if test="equipRecordId != null">
                equip_record_id = #{equipRecordId,jdbcType=BIGINT},
            </if>
            <if test="duplexFlag != null">
                duplex_flag = #{duplexFlag,jdbcType=SMALLINT},
            </if>
            <if test="emergencyFlag != null">
                emergency_flag = #{emergencyFlag,jdbcType=SMALLINT},
            </if>
            <if test="qualityControlFlag != null">
                quality_control_flag = #{qualityControlFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipTestProgress != null">
                equip_test_progress = #{equipTestProgress,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>


    <select id="selectLisEquipData" resultType="com.msun.csm.model.vo.LisEquipVo"
            parameterType="com.msun.csm.model.dto.LisEquipSelectDTO">
        select
        per.* ,
        pervl.*,
        phi.hospital_name as hospitalInfoName,
        phi.cloud_hospital_id,
        phi.cloud_hospital_id as cloud_hospital_id_str,
        su.user_name as createrName,
        pervl.api_dev_yunying_uid as api_dev_yunying_uid,
        devsu.user_name as api_dev_yunying_uname,
        (select pass_standard from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as pass_standard
        from
        csm.proj_equip_record_vs_lis pervl
        left join csm.proj_equip_record per on pervl.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        left join csm.sys_user su on per.creater_id = su.sys_user_id
        left join csm.sys_user devsu on pervl.api_dev_yunying_uid = devsu.user_yunying_id
        where
        pervl.is_deleted = 0
        <if test="customInfoId != null">
            and per.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipModelOrFactory != null">
            and (per.equip_model_name like concat('%',#{equipModelOrFactory},'%') or per.equip_factory_name like
            concat('%',#{equipModelOrFactory},'%'))
        </if>
        <if test="equipStatus != null">
            and per.equip_status = #{equipStatus}
        </if>
        <if test="requiredFlag != null">
            and per.required_flag = #{requiredFlag}
        </if>
        <if test="equipRecordVsLisId != null">
            and pervl.equip_record_vs_lis_id = #{equipRecordVsLisId}
        </if>
        order by pervl.create_time desc
    </select>

    <select id="getNoMeetRecordCount" resultType="java.lang.Integer">
        select count(1)
        from csm.proj_equip_record_vs_lis rvl
        left join csm.proj_equip_record per on rvl.equip_record_id = per.equip_record_id
        where rvl.is_deleted = 0
        and per.is_deleted = 0
        and per.required_flag = 1
        and per.project_info_id = #{projectInfoId}
        and per.equip_status in
        <foreach collection="statusList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateTaskDetailStatus" parameterType="com.msun.csm.model.dto.ProjEquipVsProductFinishDTO">
        update csm.proj_milestone_task_detail
        set complete_status = 1
        where product_deliver_id = #{productId}
          and milestone_task_id =
              (select milestone_task_id
               from csm.proj_milestone_task
               where project_info_id = #{projectInfoId}
                 and milestone_info_id = #{milestoneInfoId})
          and is_deleted = 0
    </update>

    <select id="findToMsunEquipRecord" parameterType="com.msun.csm.model.dto.ProjEquipSendToCloudDTO"
            resultType="com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto">
        select rvl.equip_record_vs_lis_id as id,
        phi.cloud_hospital_id as hospitalId,
        phi.org_id as hisOrgId,
        'LIS' as productCode,
        per.equip_type_id as deviceId,
        per.equip_type_name as deviceName,
        per.equip_factory_name as firm,
        per.equip_model_name as model,
        per.comm_mode_key as communication,
        per.equip_position as location,
        per.equip_factory_phone as firmTel,
        per.equip_position as regionalEquipment,
        per.equip_class_id as monitorClass,
        per.cloud_equip_id as oldEquipId,
        per.yy_product_id as productId
        from csm.proj_equip_record_vs_lis rvl
        left join csm.proj_equip_record per on rvl.equip_record_id = per.equip_record_id
        left join csm.proj_hospital_info phi on per.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        <where>
            <if test="projectType == 1">
                and per.equip_status not in (0,1,2)
            </if>
            and per.equip_status != 5
            and per.required_flag = 1
<!--            and per.cloud_equip_id is not null-->
            <if test="projectInfoId !=null">
                and per.project_info_id = #{projectInfoId}
            </if>
            <if test="equipRecordVsProductId != null">
                and rvl.equip_record_vs_lis_id = #{equipRecordVsProductId}
            </if>
            <if test="equipRecordVsProductIds != null and equipRecordVsProductIds.size() > 0">
                and rvl.equip_record_vs_lis_id in
                <foreach collection="equipRecordVsProductIds" item="item" index="index" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            and rvl.is_deleted = 0
            and per.is_deleted = 0
        </where>
    </select>

    <select id="selectOldEquipDataToLis" parameterType="long"
            resultType="com.msun.csm.model.dto.SendCsmEquipToOldLisDTO">
        select distinct
        tpnvo.new_custom_info_id as customInfoId, -- 新系统客户id
        tpnvo.old_custom_id, -- 老系统客户id
        tpnvo2.new_project_info_id, -- 新系统项目id
        tpnvo2.old_project_info_id, -- oldProjectInfoId
        es.id, --设备id
        es.factory_name_id as dictEquipFactoryId, -- 字典的厂商id
        def.equip_factory_name as dictEquipFactoryName, -- 字典的厂商名称
        es.real_factory_name as equipFactoryName, -- 手动输入的厂商名称
        es.type_name_id as dictEquipTypeId, -- 字典的设备类型id
        dec2.equip_class_name as dictEquipTypeName, -- 字典的设备类型名称
        es.real_type_name as equipTypeName, -- 手动输入的设备类型
        es.model_name_id as dictEquipModelId, -- 字典的设备型号id
        den.equip_name as dictEquipModelName, -- 字典的设备型号名称
        es.real_model_name as equipModelName, -- 手动输入的设备型号
        es.communication_mode as commMode, -- 通讯方式
        es.dup_device as isDouble, -- 是否双工
        es.buff_join as requiredFlag, -- 是否对接
        es.reason as stopReason, -- 不对接原因
        es.status as equipStatus , -- 设备状态
        es.location as equipPosition , -- 设备位置
        es.is_emergency as isEmergency, -- 是否急诊
        es.is_quality_control as isQuality, -- 是否质控
        es.factory_phone as equipFactoryPhone, -- 厂商电话
        es.remarks as memo, -- 备注
        es.apply_submit_time as applyTime, -- 申请提交时间
        es.old_equip_id as cloudEquipId, -- 云健康设备id
        es.old_yun_model_name as cloudEquipName, -- 云健康设备名称
        es.equip_status as equipTestStstus, -- 设备调试状态
        es.check_result as checkResult, -- 检测结果
        es.customer_info_id as hospitalInfoId, -- 老系统的来源医院id【数据迁移时需要进行转换为csm】
        es.create_id as createId, -- 创建人【老系统中人员id】
        es.create_time -- 创建时间
        from platform.equip_search es
        left join anay.dict_equip_factory def on es.factory_name_id = def.equip_factory_id
        left join anay.dict_equip_class dec2 on es.type_name_id = dec2.equip_class_id
        left join anay.dict_equip_name den on es.model_name_id = den.id
        left join platform.equip_item_info eii on es.id = eii.equip_id
        left join platform.project p on es.project_id = p.id
        left join comm.customer_info ci on es.customer_info_id = ci.id
        left join csm.tmp_project_new_vs_old tpnvo on tpnvo.old_custom_id = es.customer_id
        left join csm.tmp_project_new_vs_old tpnvo2 on tpnvo2.old_project_info_id = es.project_id
        where es.delete_flag = '0' and es.product_id = 332 and tpnvo.old_custom_id is not null
        <if test="projectInfoId != -1">
            and tpnvo.new_project_info_id = #{projectInfoId}
        </if>
        <if test="oldEquipId != -1">
            and es.id = #{oldEquipId}
        </if>
    </select>

    <select id="selectHasLisProject" resultType="long" parameterType="long">
        select ppi.project_info_id
        from csm.proj_project_info ppi
                 left join csm.proj_order_product pop on ppi.project_info_id = pop.project_info_id
        where ppi.custom_info_id = #{customInfoId}
          and pop.yy_order_product_id in (5730, 5727, 4073)
    </select>

    <select id="selectOldEquipImgToLis" parameterType="long" resultType="com.msun.csm.model.dto.OldLisEquipImgDTO">
        select esi.id,
        es.product_id,
        esi.search_id,
        esi.img,
        esi.file_type,
        per.custom_info_id ,
        per.project_info_id ,
        per.hospital_info_id
        from platform.equip_search_img esi
        left join platform.equip_search es on esi.search_id = es.id and es.delete_flag = '0'
        left join csm.proj_equip_record_vs_lis pervl on esi.search_id = pervl.equip_record_vs_lis_id and
        pervl.is_deleted = 0
        left join csm.proj_equip_record per on per.equip_record_id = pervl.equip_record_id and per.is_deleted = 0
        where 1=1
        and per.project_info_id is not null
        <if test="oldEquipId != -1">
            and esi.search_id = #{oldEquipId}
        </if>
        <if test="projectInfoId != -1">
            and es.project_id = #{projectInfoId}
        </if>
        and es.product_id = 332
    </select>

    <select id="selectOldEquipImgToPacs" parameterType="long" resultType="com.msun.csm.model.dto.OldLisEquipImgDTO">
        select esi.id,
        es.product_id,
        esi.search_id,
        esi.img,
        esi.file_type,
        per.custom_info_id ,
        per.project_info_id ,
        per.hospital_info_id
        from platform.equip_search_img esi
        left join platform.equip_search es on esi.search_id = es.id and es.delete_flag = '0'
        left join csm.proj_equip_record_vs_pacs pervl on esi.search_id = pervl.equip_record_vs_pacs_id and
        pervl.is_deleted = 0
        left join csm.proj_equip_record per on per.equip_record_id = pervl.equip_record_id and per.is_deleted = 0
        where 1=1
        and per.project_info_id is not null
        <if test="oldEquipId != -1">
            and esi.search_id = #{oldEquipId}
        </if>
        <if test="projectInfoId != -1">
            and es.project_id = #{projectInfoId}
        </if>
        and es.product_id = 322
    </select>

    <select id="selectOldEquipImgToHd" parameterType="long" resultType="com.msun.csm.model.dto.OldLisEquipImgDTO">
        select esi.id,
        es.product_id,
        esi.search_id,
        esi.img,
        esi.file_type,
        per.custom_info_id ,
        per.project_info_id ,
        per.hospital_info_id
        from platform.equip_search_img esi
        left join platform.equip_search es on esi.search_id = es.id and es.delete_flag = '0'
        left join csm.proj_equip_record_vs_hd pervl on esi.search_id = pervl.equip_record_vs_hd_id and
        pervl.is_deleted = 0
        left join csm.proj_equip_record per on per.equip_record_id = pervl.equip_record_id and per.is_deleted = 0
        where 1=1
        and per.project_info_id is not null
        <if test="oldEquipId != -1">
            and esi.search_id = #{oldEquipId}
        </if>
        <if test="projectInfoId != -1">
            and es.project_id = #{projectInfoId}
        </if>
        and es.product_id = 310
    </select>
</mapper>
