<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjNetPortMappingMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        <!--@Table csm.proj_net_port_mapping-->
        <id column="port_mapping_id" jdbcType="BIGINT" property="portMappingId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="port_mapping_name" jdbcType="VARCHAR" property="portMappingName"/>
        <result column="export_gateway_code" jdbcType="VARCHAR" property="exportGatewayCode"/>
        <result column="mapping_port" jdbcType="VARCHAR" property="mappingPort"/>
        <result column="net_survey_detail_code" jdbcType="VARCHAR" property="netSurveyDetailCode"/>
        <result column="yy_product_id" jdbcType="INTEGER" property="yyProductId"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        port_mapping_id,
        project_info_id,
        port_mapping_name,
        export_gateway_code,
        mapping_port,
        net_survey_detail_code,
        yy_product_id,
        order_no,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_net_port_mapping
        where port_mapping_id = #{portMappingId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.proj_net_port_mapping
        where port_mapping_id = #{portMappingId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        insert into csm.proj_net_port_mapping (port_mapping_id, project_info_id, port_mapping_name,
                                               export_gateway_code, mapping_port, net_survey_detail_code,
                                               yy_product_id, order_no, creater_id,
                                               create_time, updater_id, update_time,
                                               is_deleted)
        values (#{portMappingId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{portMappingName,jdbcType=VARCHAR},
                #{exportGatewayCode,jdbcType=VARCHAR}, #{mappingPort,jdbcType=VARCHAR},
                #{netSurveyDetailCode,jdbcType=VARCHAR},
                #{yyProductId,jdbcType=INTEGER}, #{orderNo,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        insert into csm.proj_net_port_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="portMappingId != null">
                port_mapping_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="portMappingName != null">
                port_mapping_name,
            </if>
            <if test="exportGatewayCode != null">
                export_gateway_code,
            </if>
            <if test="mappingPort != null">
                mapping_port,
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="portMappingId != null">
                #{portMappingId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="portMappingName != null">
                #{portMappingName,jdbcType=VARCHAR},
            </if>
            <if test="exportGatewayCode != null">
                #{exportGatewayCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingPort != null">
                #{mappingPort,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailCode != null">
                #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        update csm.proj_net_port_mapping
        <set>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="portMappingName != null">
                port_mapping_name = #{portMappingName,jdbcType=VARCHAR},
            </if>
            <if test="exportGatewayCode != null">
                export_gateway_code = #{exportGatewayCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingPort != null">
                mapping_port = #{mappingPort,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where port_mapping_id = #{portMappingId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        update csm.proj_net_port_mapping
        set project_info_id        = #{projectInfoId,jdbcType=BIGINT},
            port_mapping_name      = #{portMappingName,jdbcType=VARCHAR},
            export_gateway_code    = #{exportGatewayCode,jdbcType=VARCHAR},
            mapping_port           = #{mappingPort,jdbcType=VARCHAR},
            net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
            yy_product_id          = #{yyProductId,jdbcType=INTEGER},
            order_no               = #{orderNo,jdbcType=INTEGER},
            creater_id             = #{createrId,jdbcType=BIGINT},
            create_time            = #{createTime,jdbcType=TIMESTAMP},
            updater_id             = #{updaterId,jdbcType=BIGINT},
            update_time            = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted             = #{isDeleted,jdbcType=SMALLINT}
        where port_mapping_id = #{portMappingId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_net_port_mapping
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="port_mapping_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.portMappingName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="export_gateway_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.exportGatewayCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mapping_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.mappingPort,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="net_survey_detail_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.netSurveyDetailCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.yyProductId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where port_mapping_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.portMappingId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_net_port_mapping
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="port_mapping_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.portMappingName != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.portMappingName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="export_gateway_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.exportGatewayCode != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.exportGatewayCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mapping_port = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.mappingPort != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.mappingPort,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="net_survey_detail_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netSurveyDetailCode != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.netSurveyDetailCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProductId != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.yyProductId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.orderNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when port_mapping_id = #{item.portMappingId,jdbcType=BIGINT} then
                            #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where port_mapping_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.portMappingId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_net_port_mapping
        (port_mapping_id, project_info_id, port_mapping_name, export_gateway_code, mapping_port,
         net_survey_detail_code, yy_product_id, order_no, creater_id, create_time, updater_id,
         update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.portMappingId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
             #{item.portMappingName,jdbcType=VARCHAR},
             #{item.exportGatewayCode,jdbcType=VARCHAR}, #{item.mappingPort,jdbcType=VARCHAR},
             #{item.netSurveyDetailCode,jdbcType=VARCHAR}, #{item.yyProductId,jdbcType=INTEGER},
             #{item.orderNo,jdbcType=INTEGER},
             #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updaterId,jdbcType=BIGINT},
             #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        insert into csm.proj_net_port_mapping
        (port_mapping_id, project_info_id, port_mapping_name, export_gateway_code, mapping_port,
         net_survey_detail_code, yy_product_id, order_no, creater_id, create_time, updater_id,
         update_time, is_deleted)
        values (#{portMappingId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{portMappingName,jdbcType=VARCHAR},
                #{exportGatewayCode,jdbcType=VARCHAR}, #{mappingPort,jdbcType=VARCHAR},
                #{netSurveyDetailCode,jdbcType=VARCHAR},
                #{yyProductId,jdbcType=INTEGER}, #{orderNo,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
            port_mapping_id = #{portMappingId,jdbcType=BIGINT}, project_info_id = #{projectInfoId,jdbcType=BIGINT}, port_mapping_name = #{portMappingName,jdbcType=VARCHAR}, export_gateway_code = #{exportGatewayCode,jdbcType=VARCHAR}, mapping_port = #{mappingPort,jdbcType=VARCHAR}, net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR}, yy_product_id = #{yyProductId,jdbcType=INTEGER}, order_no = #{orderNo,jdbcType=INTEGER}, creater_id = #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetPortMapping">
        <!--@mbg.generated-->
        insert into csm.proj_net_port_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="portMappingId != null">
                port_mapping_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="portMappingName != null">
                port_mapping_name,
            </if>
            <if test="exportGatewayCode != null">
                export_gateway_code,
            </if>
            <if test="mappingPort != null">
                mapping_port,
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="portMappingId != null">
                #{portMappingId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="portMappingName != null">
                #{portMappingName,jdbcType=VARCHAR},
            </if>
            <if test="exportGatewayCode != null">
                #{exportGatewayCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingPort != null">
                #{mappingPort,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailCode != null">
                #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="portMappingId != null">
                port_mapping_id = #{portMappingId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="portMappingName != null">
                port_mapping_name = #{portMappingName,jdbcType=VARCHAR},
            </if>
            <if test="exportGatewayCode != null">
                export_gateway_code = #{exportGatewayCode,jdbcType=VARCHAR},
            </if>
            <if test="mappingPort != null">
                mapping_port = #{mappingPort,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectBySurveyDetailCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_net_port_mapping
        where is_deleted = 0
          and project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR}
    </select>

    <update id="deleteByProjectId">
        update csm.proj_net_port_mapping
        set is_deleted = 1
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </update>

    <select id="selectByProjectInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_net_port_mapping
        where is_deleted = 0
          and project_info_id = #{projectInfoId,jdbcType=BIGINT}
        order by order_no
    </select>
</mapper>
