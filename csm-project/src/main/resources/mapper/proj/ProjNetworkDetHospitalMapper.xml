<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjNetworkDetHospitalMapper">
    <sql id="findListSql">
        select * from (
            select t.id,t1.health_bureau_flag,t1.hospital_name,t.hospital_info_id,t.detect_port,t.client_count,
            t.detect_domain,case t.detect_status when null then 2 else t.detect_status end as detect_status,
            t.sys_type,
            t.ip_address,
            t.call_cloud_status,
            t.call_front_status,
            t.front_external_Ip,
            t.front_intranet_ip,
            t.product_used_intranet_port,
            t.product_used_external_port,
            t.intranet_ssh_port,
            t.external_ssh_port,
            t.purpose,
            t.root_pwd,
            t.dns
            from csm.proj_network_det_hospital t
            left join csm.proj_hospital_info t1 on t1.hospital_info_id = t.hospital_info_id
            where t.hospital_info_id in (
                select
                t.hospital_info_id
                from csm.proj_hospital_info t
                inner join csm.proj_hospital_vs_project_type t1 on t.hospital_info_id = t1.hospital_info_id
                inner join csm.proj_project_info t2 on t1.project_type = t2.project_type
                and t2.custom_info_id = t1.custom_info_id
                and t2.project_info_id = #{projectInfoId, jdbcType=BIGINT}
                <if test="sysType == 1">
                    and t.health_bureau_flag = 1
                </if>
            )
            and t.sys_type = #{sysType, jdbcType=INTEGER}
            <if test="hospitalInfoId != null">
                and t.hospital_info_id = #{hospitalInfoId, jdbcType=BIGINT}
            </if>
            <if test="domainUrl != null and domainUrl != ''">
                and t.detect_domain = #{domainUrl, jdbcType=VARCHAR}
            </if>
        ) t2 order by t2.health_bureau_flag desc
    </sql>
    <update id="updateByHospitalInfoId">
        update csm.proj_network_det_hospital t
        <set>
            <if test="detectStatus != null">
                detect_status = #{detectStatus,jdbcType=BIGINT},
            </if>
            <if test="detectDomain != null">
                detect_domain = #{detectDomain,jdbcType=VARCHAR},
            </if>
            <if test="detectPort != null">
                detect_port = #{detectPort,jdbcType=BIGINT},
            </if>
            <if test="clientCount != null">
                client_count = #{clientCount,jdbcType=BIGINT},
            </if>
            <if test="statusFlag != null">
                status_flag = #{statusFlag,jdbcType=INTEGER},
            </if>
            <if test="callFrontStatus != null">
                call_front_status = #{callFrontStatus,jdbcType=INTEGER},
            </if>
            <if test="callCloudStatus != null">
                call_cloud_status = #{callCloudStatus,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT} and sys_type = #{sysType,jdbcType=INTEGER}
    </update>
    <update id="updateDetectEventById">
        update csm.proj_network_det_hospital t
        <set>
            detect_event = #{eventCode,jdbcType=INTEGER}
        </set>
        where hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
    </update>
    <delete id="deleteById" parameterType="java.lang.Long">
        delete from csm.proj_network_det_hospital where id = #{id, jdbcType=BIGINT}
    </delete>

    <select id="findNetworkDetHospitalInfoList"
            resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetHospitalRelative">
        <include refid="findListSql"/>
    </select>

    <select id="findHospitalByHospitalId" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetHospital">
        select * from csm.proj_network_det_hospital t where t.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT} and sys_type = #{sysType,jdbcType=INTEGER}
    </select>

    <select id="findDistinctDomainByHospitalInfoIds" resultType="java.lang.String"
            parameterType="java.util.List">
        SELECT DISTINCT(T.DETECT_DOMAIN)
        FROM CSM.PROJ_NETWORK_DET_HOSPITAL T
        WHERE T.HOSPITAL_INFO_ID IN
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>
