<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjExamMapper">
    <select id="findNotApplyExamEnvironment" parameterType="com.msun.csm.model.dto.ProjHospitalInfoExamDTO"
            resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select phi.hospital_info_id,phi.hospital_name,phi.hospital_bed_count,
               phi.hospital_out_patient_count,phi.hospital_annual_income,
               phi.terminal_count,phi.village_doctors_count,phi.clinic_count,
               phi.population
        from csm.proj_hospital_vs_project_type ph
        left join csm.simulation_hospital phve on ph.hospital_info_id = phve.hospital_info_id and phve.is_delete = 0
        left join csm.proj_hospital_info phi on ph.hospital_info_id = phi.hospital_info_id
        where ph.custom_info_id = #{customInfoId} and ph.project_type = #{projectType}
        and phi.is_deleted = 0 and phve.custom_info_id is null
    </select>

    <select id="findApplyExamEnvironment" parameterType="com.msun.csm.model.dto.ProjHospitalInfoExamDTO"
            resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select phi.hospital_info_id,phi.hospital_name,phi.hospital_bed_count,
               phi.hospital_out_patient_count,phi.hospital_annual_income,
               phi.terminal_count,phi.village_doctors_count,phi.clinic_count,
               phi.population,phi.cloud_hospital_id,phi.hospital_open_status
        from csm.proj_hospital_vs_project_type ph
                 left join csm.simulation_hospital phve on ph.hospital_info_id = phve.hospital_info_id and phve.is_delete = 0
                 left join csm.proj_hospital_info phi on phve.exam_hospital_info_id = phi.hospital_info_id
        where ph.custom_info_id = #{customInfoId} and ph.project_type = #{projectType}
          and phi.is_deleted = 0
        order by phi.hospital_open_status desc
    </select>
    
    <select id="findApplyExamHospitals" parameterType="com.msun.csm.model.dto.ProjHospitalInfoExamDTO"
            resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative">
        select  phi.hospital_info_id,phi.hospital_name,
                phi.*,'曹 县' as town_name,'山东省/菏泽市/曹 县' as administrative_divisions,
                '370000/371700/371721/' as administrative_code,ph.project_type,
                #{examCustomInfoName} as custom_name,#{examProjectInfoName} as project_name,
                #{examOrderInfoId} as order_info_id,phi.hospital_open_status
        from csm.proj_hospital_vs_project_type ph
        left join csm.simulation_hospital phve on ph.hospital_info_id = phve.hospital_info_id and phve.is_delete = 0
        left join csm.proj_hospital_info phi on phve.exam_hospital_info_id = phi.hospital_info_id
        where ph.custom_info_id = #{customInfoId} and ph.project_type = #{projectType}
        and phi.is_deleted = 0

        union

        select  phi.hospital_info_id,phi.hospital_name,
                phi.*,'曹 县' as town_name,'山东省/菏泽市/曹 县' as administrative_divisions,
                '370000/371700/371721/' as administrative_code,ph.project_type,
                #{examCustomInfoName} as custom_name,#{examProjectInfoName} as project_name,
                #{examOrderInfoId} as order_info_id,phi.hospital_open_status
        from csm.proj_hospital_vs_project_type ph
        left join csm.proj_hospital_info phi on ph.hospital_info_id = phi.hospital_info_id
        where ph.custom_info_id = #{examCustomInfoId} and ph.project_type = #{examProjectType}
        and phi.health_bureau_flag = 1
        and phi.is_deleted = 0
    </select>
    
    <select id="getProductBusiness" resultType="java.lang.String">
        SELECT id :: varchar
        FROM comm.product_copy_business
        where business_name in
        <foreach close=")" collection="copyDataBusiness" item="businessName" open="(" separator=", ">
            #{businessName}
        </foreach>
        and invalid_flag = 0
    </select>
    
    <select id="getOldMainHospital" resultType="java.lang.Long">
        select min(id) id
        from comm.customer_info
        where csm_hospital_info_id = #{hospitalInfoId}
    </select>

    <select id="findOldHospitals" resultType="java.lang.Long">
        select min(id) id
        from comm.customer_info
        where csm_hospital_info_id in
        <foreach close=")" collection="hospitalInfoIds" item="id" open="(" separator=", ">
            #{id}
        </foreach>
    </select>
</mapper>