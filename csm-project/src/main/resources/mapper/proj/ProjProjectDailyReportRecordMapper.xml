<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectDailyReportRecordMapper">

    <select id="getDailyReportById" resultType="com.msun.csm.dao.entity.proj.ProjProjectDailyReportRecord">
        select *
        from csm.proj_project_daily_report_record
        where is_deleted = 0
          and project_daily_report_record_id = #{id}
    </select>

    <update id="updateDailyReportById" parameterType="com.msun.csm.dao.entity.proj.UpdateProjProjectDailyReportRecord">
        update csm.proj_project_daily_report_record
        set updater_id = #{updaterId},
        <if test="reportTitle != null and reportTitle != ''">
            report_title = #{reportTitle},
        </if>
        <if test="riskLevel != null">
            risk_level = #{riskLevel},
        </if>
        <if test="reportContent != null and reportContent != ''">
            report_content = #{reportContent},
        </if>
        <if test="reportStatus != null">
            report_status = #{reportStatus},
        </if>
        <if test="reportSendTime != null">
            report_send_time = #{reportSendTime},
        </if>
        update_time = now()
        where project_daily_report_record_id = #{projectDailyReportRecordId}
    </update>

    <select id="getSendDailyReportMessageProject" resultType="com.msun.csm.dao.entity.proj.ProjectDailyReportRecordVO">
        select
        ppi.project_info_id as "projectInfoId",
        ppi.project_number as "projectNumber",
        ppi.custom_info_id as "customInfoId",
        pci.custom_name as "customName",
        ppi.project_deliver_status as "projectDeliverStatus",
        ppi.project_type as "projectType",
        ppi.online_time as "onlineTime",
        ppi.project_leader_id as "projectLeaderId" ,
        su.user_name as "projectLeader",
        su.account as "projectLeaderAccount"
        from csm.proj_project_info ppi
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        left join csm.sys_user su on ppi.project_leader_id = su.sys_user_id and su.is_deleted = 0
        where ppi.is_deleted = 0
        and pci.is_deleted = 0
        and ppi.his_flag = 1
        and ppi.project_source_flag != 2
        and ppi.project_deliver_status in
        <foreach collection="deliverStatusList" item="deliverStatus" open="(" separator=", " close=")">
            #{deliverStatus}
        </foreach>
    </select>

    <select id="getSentProjectDailyReportRecord" resultType="com.msun.csm.dao.entity.proj.ProjProjectDailyReportRecord"
            parameterType="com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParamPO2">
        select
        *
        from
        csm.proj_project_daily_report_record ppdrr
        where is_deleted = 0
        and report_status != 1
        <if test="reportSendTimeStart != null">
            and ppdrr.report_send_time &gt;= #{reportSendTimeStart}
        </if>
        <if test="reportSendTimeEnd != null">
            and ppdrr.report_send_time &lt;= #{reportSendTimeEnd}
        </if>
    </select>

    <select id="getWriteProjectDailyReportRecord" resultType="com.msun.csm.dao.entity.proj.ProjProjectDailyReportRecord"
            parameterType="com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParamPO2">
        select
        *
        from
        csm.proj_project_daily_report_record ppdrr
        where is_deleted = 0
        <if test="reportSendTimeStart != null">
            and ppdrr.create_time &gt;= #{reportSendTimeStart}
        </if>
        <if test="reportSendTimeEnd != null">
            and ppdrr.create_time &lt;= #{reportSendTimeEnd}
        </if>
    </select>


    <select id="getProjectDailyReportRecord" resultType="com.msun.csm.dao.entity.proj.ProjectDailyReportRecordVO" parameterType="com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParamPO">
        select
        ppdrr.project_daily_report_record_id as "projectDailyReportRecordId",
        ppi.project_info_id as "projectInfoId",
        ppi.project_number as "projectNumber",
        ppi.custom_info_id as "customInfoId",
        pci.custom_name as "customName",
        ppi.project_deliver_status as "projectDeliverStatus",
        ppi.project_type as "projectType",
        ppi.online_time as "onlineTime",
        ppdrr.report_title as "reportTitle",
        ppdrr.risk_level as "riskLevel",
        ppdrr.report_content as "reportContent",
        case
        when ppdrr.report_status is null then 0
        else ppdrr.report_status
        end as "reportStatus",
        ppi.project_leader_id as "projectLeaderId" ,
        su.user_name as "projectLeader",
        su.account as "projectLeaderAccount",
        ppdrr.report_send_time as "reportSendTime",
        ppdrr.create_time as "createTime"
        from csm.proj_project_daily_report_record ppdrr
        left join csm.proj_project_info ppi on ppdrr.project_info_id = ppi.project_info_id and ppdrr.is_deleted = 0
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        left join csm.sys_user su on ppi.project_leader_id = su.sys_user_id and su.is_deleted = 0
        where ppi.is_deleted = 0
        and pci.is_deleted = 0
        and ppi.his_flag = 1
        <if test="customName != null and customName != ''">
            and pci.custom_name like concat('%', #{customName}, '%')
        </if>
        <if test="onlineTimeStart != null">
            and ppi.online_time &gt;= #{onlineTimeStart}
        </if>
        <if test="onlineTimeEnd != null">
            and ppi.online_time &lt;= #{onlineTimeEnd}
        </if>
        <if test="reportSendTimeStart != null">
            and ppdrr.report_send_time &gt;= #{reportSendTimeStart}
        </if>
        <if test="reportSendTimeEnd != null">
            and ppdrr.report_send_time &lt;= #{reportSendTimeEnd}
        </if>
        <if test="reportStatus == 0">
            and ppdrr.report_status is null
        </if>
        <if test="reportStatus != null and reportStatus != 0">
            and ppdrr.report_status = #{reportStatus}
        </if>
        and ppi.project_deliver_status in
        <foreach collection="deliverStatusList" item="deliverStatus" open="(" separator=", " close=")">
            #{deliverStatus}
        </foreach>
        order by ppdrr.create_time desc
    </select>

    <select id="getNeedWriteDailyReportProject" resultType="com.msun.csm.dao.entity.proj.ProjectDailyReportRecordVO" parameterType="com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParamPO">
        select
        ppi.project_info_id as "projectInfoId",
        ppi.project_number as "projectNumber",
        ppi.custom_info_id as "customInfoId",
        pci.custom_name as "customName",
        ppi.project_deliver_status as "projectDeliverStatus",
        ppi.project_type as "projectType",
        ppi.online_time as "onlineTime",
        ppi.project_leader_id as "projectLeaderId" ,
        su.user_name as "projectLeader",
        su.account as "projectLeaderAccount"
        from csm.proj_project_info ppi
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        left join csm.sys_user su on ppi.project_leader_id = su.sys_user_id and su.is_deleted = 0
        where ppi.is_deleted = 0
        and pci.is_deleted = 0
        and ppi.his_flag = 1
        and ppi.project_source_flag != 2
        and ppi.project_deliver_status in
        <foreach collection="deliverStatusList" item="deliverStatus" open="(" separator=", " close=")">
            #{deliverStatus}
        </foreach>
        <if test="customName != null and customName != ''">
            and pci.custom_name like concat('%', #{customName}, '%')
        </if>
        <if test="onlineTimeStart != null">
            and ppi.online_time &gt;= #{onlineTimeStart}
        </if>
        <if test="onlineTimeEnd != null">
            and ppi.online_time &lt;= #{onlineTimeEnd}
        </if>
        order by pci.custom_name,ppi.project_number
    </select>


</mapper>
