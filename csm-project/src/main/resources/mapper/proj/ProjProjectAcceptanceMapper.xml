<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceMapper">

    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectAcceptance">
            <id property="projectAcceptanceId" column="project_acceptance_id" jdbcType="BIGINT"/>
            <result property="yyOrderId" column="yy_order_id" jdbcType="BIGINT"/>
            <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
            <result property="expectedAcceptanceTime" column="expected_acceptance_time" jdbcType="DATE"/>
            <result property="externalAcceptanceType" column="external_acceptance_type" jdbcType="SMALLINT"/>
            <result property="externalAcceptanceTime" column="external_acceptance_time" jdbcType="DATE"/>
            <result property="applyAcceptanceTime" column="apply_acceptance_time" jdbcType="TIMESTAMP"/>
            <result property="acceptanceStatus" column="acceptance_status" jdbcType="SMALLINT"/>
            <result property="accepterUserId" column="accepter_user_id" jdbcType="BIGINT"/>
            <result property="acceptanceTime" column="acceptance_time" jdbcType="TIMESTAMP"/>
            <result property="acceptanceScore" column="acceptance_score" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
            <result property="requiredAcceptanceTimes" column="required_acceptance_times" jdbcType="SMALLINT"/>
            <result property="currentTimes" column="current_times" jdbcType="SMALLINT"/>
    </resultMap>

    <resultMap id="ProjectAcceptanceRecordMap" type="com.msun.csm.model.vo.ProjectAcceptanceRecord">
        <result property="projectNumber"            column="project_number"             jdbcType="VARCHAR"/>
        <result property="projectInfoId"            column="project_info_id"            jdbcType="BIGINT"/>
        <result property="customName"               column="custom_name"                jdbcType="VARCHAR"/>
        <result property="customInfoId"             column="custom_info_id"             jdbcType="BIGINT"/>
        <result property="acceptanceStatus"         column="acceptance_status"          jdbcType="SMALLINT"/>
        <result property="acceptanceScore"          column="acceptance_score"           jdbcType="VARCHAR"/>
        <result property="settleInTime"             column="settle_in_time"             jdbcType="TIMESTAMP"/>
        <result property="applyAcceptanceTime"      column="apply_acceptance_time"      jdbcType="TIMESTAMP"/>
        <result property="durationReduction"        column="duration_reduction"         jdbcType="VARCHAR"/>
        <result property="deptName"                 column="dept_name"                  jdbcType="VARCHAR"/>
        <result property="projectTeamId"            column="project_team_id"            jdbcType="BIGINT"/>
        <result property="userName"                 column="user_name"                  jdbcType="VARCHAR"/>
        <result property="projectLeaderId"          column="project_leader_id"          jdbcType="BIGINT"/>
        <result property="requiredAcceptanceTimes"  column="required_acceptance_times"  jdbcType="SMALLINT"/>
        <result property="currentTimes"             column="current_times"              jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        project_acceptance_id,yy_order_id,project_info_id,
        expected_acceptance_time,external_acceptance_type,external_acceptance_time,apply_acceptance_time,
        acceptance_status,accepter_user_id,acceptance_time,
        acceptance_score,remark,creater_id,
        create_time,updater_id,update_time,
        is_deleted,acceptance_times,required_acceptance_times,current_times
    </sql>

    <select id="getAcceptanceByProjectId" resultType="com.msun.csm.model.vo.ProjApplyAcceptanceVO">
        select
            ppi.project_name , --项目名称
            ppi.project_number,  --项目编号
            paa.external_acceptance_time ,--外部验收时间
            paa.expected_acceptance_time , --期望验收时间
            paa.creater_id,--申请人
            paa.create_time,-- 创建时间
            paa.apply_acceptance_time ,--申请时间
            paa.accepter_user_id  ,--验收人
            paa.acceptance_time  ,--验收时间
            paa.acceptance_status  ,--验收状态
            paa.acceptance_score  ,--验收得分
            paa.remark,--备注,
            paa.project_acceptance_id,
            paa.acceptance_times,
            paa.required_acceptance_times,
            paa.current_times,
            pci.contract_type,
            pci.yy_contract_id contract_no, --合同编号
            su2.user_name as createrName,
            paa.external_acceptance_type,
            poi.yy_order_id,
            ppi.project_info_id

        from
            csm.proj_project_info ppi
                left join csm.proj_project_acceptance paa on
                ppi.project_info_id = paa.project_info_id and paa.is_deleted = 0
                left join csm.proj_order_info poi on poi.order_info_id =ppi.order_info_id and poi.is_deleted = 0
                left join csm.proj_contract_info pci on poi.contract_info_id =pci.contract_info_id and pci.is_deleted = 0
                left join csm.sys_user su2 on paa.creater_id = su2.sys_user_id
        <where>
            <if test="null != dto.projectInfoId">
              and  ppi.project_info_id = #{dto.projectInfoId,jdbcType=BIGINT}
            </if>
            <if test="null != dto.projectAcceptanceId">
               and project_acceptance_id =#{dto.projectAcceptanceId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <update id="updateAcceptanceStatus">
        update csm.proj_project_acceptance
        set acceptance_status         = #{updateParam.acceptanceStatus,jdbcType=SMALLINT},
            acceptance_score          = #{updateParam.acceptanceScore,jdbcType=VARCHAR},
            accepter_user_id          = #{updateParam.accepterUserId,jdbcType=BIGINT},
            acceptance_time           = #{updateParam.acceptanceTime,jdbcType=TIMESTAMP},
            required_acceptance_times = #{updateParam.requiredAcceptanceTimes},
            current_times             = #{updateParam.currentTimes},
            update_time               = now()
        where project_acceptance_id = #{updateParam.projectAcceptanceId,jdbcType=BIGINT}
    </update>

    <update id="updateById">
        update csm.proj_project_acceptance
        <set>
            <if test="null != dto.expectedAcceptanceTime">
                expected_acceptance_time = #{dto.expectedAcceptanceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="null != dto.externalAcceptanceTime">
                external_acceptance_time = #{dto.externalAcceptanceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="null != dto.updaterId">
                updater_id = #{dto.updaterId,jdbcType=BIGINT},
            </if>
            <if test="null != dto.updateTime">
                update_time = #{dto.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="null != dto.externalAcceptanceType">
                external_acceptance_type = #{dto.externalAcceptanceType,jdbcType=SMALLINT},
            </if>
            <if test="null != dto.acceptanceStatus">
                acceptance_status = #{dto.acceptanceStatus,jdbcType=SMALLINT},
            </if>
            <if test="null != dto.currentTimes">
                current_times = #{dto.currentTimes,jdbcType=SMALLINT},
            </if>
        </set>
        where project_acceptance_id = #{dto.projectAcceptanceId,jdbcType=BIGINT}
    </update>


    <select id="getAcceptance" resultType="com.msun.csm.dao.entity.proj.ProjProjectAcceptance">
        select
            <include refid="Base_Column_List"/>
        from
        csm.proj_project_acceptance  where
        project_info_id = #{projectInfoId,jdbcType=BIGINT}
        and is_deleted = 0
        order by acceptance_times desc
    </select>

    <select id="selectAcceptanceByYyOrderId" resultType="com.msun.csm.model.vo.ProjApplyAcceptanceVO">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_acceptance
        where yy_order_id = #{yyOrderId,jdbcType=BIGINT}
          and project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and is_deleted = 0
        order by acceptance_times desc, create_time desc
    </select>

    <select id="selectOldAcceptFileData" parameterType="long"
            resultType="com.msun.csm.model.dto.OldAcceptFileDataDTO">
        WITH RankedFiles AS (
            SELECT
                paawo.apply_id,
                paawo.info_id,
                paaf.file_id,
                paaf.file_url,
                paaf.file_name,
                paaf.file_type,
                p.id AS project_id,
                p.project_name,
                ppi.project_info_id,
                ppi.project_deliver_status,
                paa.apply_time,
                DENSE_RANK() OVER (PARTITION BY p.id ORDER BY paa.apply_time DESC) AS dr,
                DENSE_RANK() OVER (PARTITION BY p.id ORDER BY paawo.accept_time  DESC) AS dr2
            FROM platform.project p
                     LEFT JOIN csm.tmp_project_new_vs_old tpnvo ON p.id = tpnvo.old_project_info_id AND tpnvo.new_project_source = 1
                     LEFT JOIN csm.proj_project_info ppi ON tpnvo.new_project_info_id = ppi.project_info_id AND ppi.is_deleted = 0
                     LEFT JOIN platform.project_apply_acceptance paa ON p.id = paa.project_id
                     LEFT JOIN platform.project_apply_acceptance_work_order paawo ON paawo.apply_id = paa.apply_id AND paawo.accept_time IS NOT NULL
                     LEFT JOIN platform.project_apply_acceptance_file paaf ON paaf.info_id = paawo.info_id
            WHERE paaf.info_id != -1 AND ppi.project_info_id IS NOT NULL
        )
        SELECT
            rf.file_url,
            rf.file_name,
            rf.file_type,
            rf.project_id as old_project_info_id,
            rf.project_info_id as new_project_info_id
        FROM RankedFiles rf
        WHERE rf.dr = 1 and rf.dr2 = 1
        <if test="projectInfoId != -1">
            AND rf.project_info_id = #{projectInfoId}
        </if>
        ORDER BY rf.project_id
    </select>

    <select id="selectOldAcceptData" parameterType="long" resultType="com.msun.csm.model.dto.OldAcceptDataDTO">
        select
            tpnvo.new_project_info_id       as "newProjectInfoId",
            paa.desire_accept_time          as "desireAcceptTime",
            paa.external_acceptance_time    as "externalAcceptanceTime",
            paa.apply_time                  as "applyTime",
            string_agg(distinct twop.product_name, ', ')    as "yyProductName",
            paawo.status                    as "status",
            su.user_yunying_id              as "yyUserId",
            paawo.work_id ,
            paawo.remark ,
            paawo.accept_score,
            paawo.accept_time
        from
            platform.project p
                left join platform.tr_project_work_order tpwo on
                p.id = tpwo.project_id
                left join platform.tb_work_order_product twop on
                twop.jf_wo_id = tpwo.wo_id
                left join platform.project_apply_acceptance paa on
                p.id = paa.project_id
                left join platform.project_apply_acceptance_work_order paawo on
                paawo.apply_id = paa.apply_id
                left join platform.sys_user su on
                paa.apply_peo_id = su.user_id
                left join csm.tmp_project_new_vs_old tpnvo on
                p.id = tpnvo.old_project_info_id
        where
            tpwo.type in (1, 8)
          and tpnvo.new_project_info_id = #{projectInfoId}
          and paawo.info_id is not null
        group by
            p.id,
            p.project_name,
            paa.desire_accept_time,
            paa.external_acceptance_time,
            paa.apply_time,
            paawo.status,
            su.user_yunying_id,
            paawo.work_id ,
            tpnvo.new_project_info_id,
            paawo.remark ,
            paawo.info_id,
            paawo.accept_score,
            paawo.accept_time
        order by
            paawo.create_time desc
        limit 1
    </select>

    <select id="queryProjectAcceptanceRecord" parameterType="com.msun.csm.model.ProjectAcceptanceRecordParam" resultType="com.msun.csm.model.vo.ProjectAcceptanceRecord" resultMap="ProjectAcceptanceRecordMap">
        SELECT
            DISTINCT ON (ppi.project_info_id, pci.custom_info_id)
            ppi.project_number,-- 工单编号
            ppi.project_info_id, -- 项目ID
            pci.custom_name ,-- 客户名称
            pci.custom_info_id ,-- 客户ID
            ppa.acceptance_status ,-- 原始申请状态
            ppa.acceptance_score ,-- 未处理的验收得分
            ppi.settle_in_time,-- 入驻时间
            ppi.apply_accept_time as "apply_acceptance_time",-- 验收考核时间
            ppi.duration_reduction, -- 工期减免天数
            sd.dept_name,-- 实施团队名称
            ppi.project_team_id,-- 实施团队ID
            su.user_name,-- 项目经理名称
            ppi.project_leader_id,--项目经理ID
            ppa.required_acceptance_times,--质管必须验收次数 默认1次,质管接受申请或者驳回申请之后才会更新此字段
            ppa.current_times -- 当前验收次数
        FROM csm.proj_project_acceptance ppa
                 left join csm.proj_project_info ppi  on ppa.project_info_id  = ppi.project_info_id
                 left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
                 left join csm.sys_dept sd on ppi.project_team_id = sd.dept_yunying_id
                 left join csm.sys_user su on ppi.project_leader_id = su.sys_user_id
        where ppa.is_deleted = 0
        <if test="customInfoId != null">
            and  pci.custom_info_id = #{customInfoId, jdbcType=BIGINT}
        </if>
        <if test="projectNumber != null and projectNumber != ''">
            and ppi.project_number like concat('%',#{projectNumber, jdbcType=VARCHAR},'%')
        </if>
        <if test="startTime != null and startTime != ''">
            and ppa.apply_acceptance_time &gt;= cast(#{startTime, jdbcType=VARCHAR} as TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            and ppa.apply_acceptance_time &lt;= cast(#{endTime, jdbcType=VARCHAR} as TIMESTAMP)
        </if>
        order by ppi.project_info_id, pci.custom_info_id, ppa.apply_acceptance_time desc
    </select>

    <select id="queryProjectAcceptanceRecordOnlyFirst" parameterType="com.msun.csm.model.ProjectAcceptanceRecordParam" resultType="com.msun.csm.model.vo.ProjectAcceptanceRecord" resultMap="ProjectAcceptanceRecordMap">
        SELECT
        DISTINCT ON (ppi.project_info_id, pci.custom_info_id)
        ppi.project_number,-- 工单编号
        ppi.project_info_id, -- 项目ID
        pci.custom_name ,-- 客户名称
        pci.custom_info_id ,-- 客户ID
        ppa.acceptance_status ,-- 原始申请状态
        ppa.acceptance_score ,-- 未处理的验收得分
        ppi.settle_in_time,-- 入驻时间
        ppa.apply_acceptance_time,-- 验收考核时间
        ppi.duration_reduction, -- 工期减免天数
        sd.dept_name,-- 实施团队名称
        ppi.project_team_id,-- 实施团队ID
        su.user_name,-- 项目经理名称
        ppi.project_leader_id,--项目经理ID
        ppa.required_acceptance_times,--质管必须验收次数 默认1次,质管接受申请或者驳回申请之后才会更新此字段
        ppa.current_times -- 当前验收次数
        FROM csm.proj_project_acceptance ppa
        left join csm.proj_project_info ppi  on ppa.project_info_id  = ppi.project_info_id
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        left join csm.sys_dept sd on ppi.project_team_id = sd.dept_yunying_id
        left join csm.sys_user su on ppi.project_leader_id = su.sys_user_id
        where ppa.is_deleted = 0
        and ppa.required_acceptance_times = 2
        and ppa.current_times = 1
        <if test="customInfoId != null">
            and  pci.custom_info_id = #{customInfoId, jdbcType=BIGINT}
        </if>
        <if test="projectNumber != null and projectNumber != ''">
            and ppi.project_number like concat('%',#{projectNumber, jdbcType=VARCHAR},'%')
        </if>
        order by ppi.project_info_id, pci.custom_info_id, ppa.apply_acceptance_time desc
    </select>

    <select id="selectByProjectInfoIdOne" resultMap="BaseResultMap">
        select * from csm.proj_project_acceptance where project_info_id = #{projectInfoId} and is_deleted = 0
        order by create_time desc
        limit 1
    </select>
</mapper>
