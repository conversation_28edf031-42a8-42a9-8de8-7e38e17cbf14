<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductArrangeRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        <!--@Table csm.proj_product_arrange_record-->
        <id column="product_arrange_record_id" jdbcType="BIGINT" property="productArrangeRecordId"/>
        <result column="product_arrange_id" jdbcType="BIGINT" property="productArrangeId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="yy_order_product_id" jdbcType="BIGINT" property="yyOrderProductId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="arrange_status" jdbcType="SMALLINT" property="arrangeStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_arrange_record_id,
        product_arrange_id,
        custom_info_id,
        yy_order_product_id,
        project_info_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        arrange_status
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_arrange_record
        where product_arrange_record_id = #{productArrangeRecordId,jdbcType=BIGINT}
    </select>
    <select id="findByProjectInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_arrange_record
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.proj_product_arrange_record
        where product_arrange_record_id = #{productArrangeRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_arrange_record (product_arrange_record_id, product_arrange_id,
        custom_info_id, yy_order_product_id, project_info_id,
        is_deleted, creater_id, create_time,
        updater_id, update_time, arrange_status)
        values (#{productArrangeRecordId,jdbcType=BIGINT}, #{productArrangeId,jdbcType=BIGINT},
        #{customInfoId,jdbcType=BIGINT}, #{yyOrderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{arrangeStatus,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_arrange_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productArrangeRecordId != null">
                product_arrange_record_id,
            </if>
            <if test="productArrangeId != null">
                product_arrange_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="arrangeStatus != null">
                arrange_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productArrangeRecordId != null">
                #{productArrangeRecordId,jdbcType=BIGINT},
            </if>
            <if test="productArrangeId != null">
                #{productArrangeId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                #{arrangeStatus,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        update csm.proj_product_arrange_record
        <set>
            <if test="productArrangeId != null">
                product_arrange_id = #{productArrangeId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
            </if>
        </set>
        where product_arrange_record_id = #{productArrangeRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        update csm.proj_product_arrange_record
        set product_arrange_id = #{productArrangeId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        arrange_status = #{arrangeStatus,jdbcType=SMALLINT}
        where product_arrange_record_id = #{productArrangeRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_product_arrange_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_arrange_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.productArrangeId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.yyOrderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="arrange_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                    #{item.arrangeStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where product_arrange_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productArrangeRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_product_arrange_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_arrange_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productArrangeId != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.productArrangeId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyOrderProductId != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.yyOrderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="arrange_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.arrangeStatus != null">
                        when product_arrange_record_id = #{item.productArrangeRecordId,jdbcType=BIGINT} then
                        #{item.arrangeStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_arrange_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productArrangeRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_product_arrange_record
        (product_arrange_record_id, product_arrange_id, custom_info_id, yy_order_product_id,
        project_info_id, is_deleted, creater_id, create_time, updater_id, update_time,
        arrange_status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productArrangeRecordId,jdbcType=BIGINT}, #{item.productArrangeId,jdbcType=BIGINT},
            #{item.customInfoId,jdbcType=BIGINT}, #{item.yyOrderProductId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.arrangeStatus,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_arrange_record
        (product_arrange_record_id, product_arrange_id, custom_info_id, yy_order_product_id,
        project_info_id, is_deleted, creater_id, create_time, updater_id, update_time,
        arrange_status)
        values (#{productArrangeRecordId,jdbcType=BIGINT}, #{productArrangeId,jdbcType=BIGINT},
        #{customInfoId,jdbcType=BIGINT}, #{yyOrderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{arrangeStatus,jdbcType=SMALLINT})
        on duplicate key update
        product_arrange_record_id = #{productArrangeRecordId,jdbcType=BIGINT}, product_arrange_id =
        #{productArrangeId,jdbcType=BIGINT}, custom_info_id = #{customInfoId,jdbcType=BIGINT}, yy_order_product_id =
        #{yyOrderProductId,jdbcType=BIGINT}, project_info_id = #{projectInfoId,jdbcType=BIGINT}, is_deleted =
        #{isDeleted,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time =
        #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time =
        #{updateTime,jdbcType=TIMESTAMP}, arrange_status = #{arrangeStatus,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_arrange_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productArrangeRecordId != null">
                product_arrange_record_id,
            </if>
            <if test="productArrangeId != null">
                product_arrange_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="arrangeStatus != null">
                arrange_status,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productArrangeRecordId != null">
                #{productArrangeRecordId,jdbcType=BIGINT},
            </if>
            <if test="productArrangeId != null">
                #{productArrangeId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                #{arrangeStatus,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productArrangeRecordId != null">
                product_arrange_record_id = #{productArrangeRecordId,jdbcType=BIGINT},
            </if>
            <if test="productArrangeId != null">
                product_arrange_id = #{productArrangeId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="arrangeStatus != null">
                arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="getArrangeProducts" resultType="com.msun.csm.dao.entity.proj.ProjProductArrangeRecord">
        select
        <include refid="Base_Column_List">
        </include>
        from csm.proj_product_arrange_record ppar
        where ppar.project_info_id = #{projectId}
        and ppar.project_info_id = #{productId}
        and is_deleted = 0
    </select>
    <select id="findByYyOrderProductIdList" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrderProduct"
            parameterType="java.util.List">
        SELECT T2.product_name,
        T2.yy_product_code,
        T.product_arrange_id,
        T.yy_order_product_id
        FROM csm.proj_product_arrange_record T
        LEFT JOIN csm.dict_product T2 ON T.product_arrange_id = T2.yy_product_id
        <where>
            and T.is_deleted = 0
            and T.project_info_id = #{projectInfoId, jdbcType=BIGINT}
            and T.yy_order_product_id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item.yyOrderProductId}
            </foreach>
        </where>
    </select>

    <update id="updateByProjectId">
        update csm.proj_product_arrange_record
        set project_info_id = #{newProjectId,jdbcType=BIGINT}
        where project_info_id = #{oldProjectId,jdbcType=BIGINT}
        <if test="splitYYPIdList != null and splitYYPIdList.size() != 0">
            and yy_order_product_id in
            <foreach close=")" collection="splitYYPIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectInfoId">
        update csm.proj_product_arrange_record
        set is_deleted=1
        where project_info_id = #{projectInfoId}
    </update>

    <select id="findByProjectInfoIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_arrange_record
        where is_deleted = 0
        and project_info_id in
        <foreach collection="projectIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_product_arrange_record
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
