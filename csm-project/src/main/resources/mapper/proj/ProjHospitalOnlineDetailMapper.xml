<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjHospitalOnlineDetailMapper">
    <resultMap type="com.msun.csm.dao.entity.proj.ProjHospitalOnlineDetail" id="ProjHospitalOnlineDetailMap">
        <result property="hospitalOnlineDetailId" column="hospital_online_detail_id" jdbcType="INTEGER"/>
        <result property="customInfoId" column="custom_info_id" jdbcType="INTEGER"/>
        <result property="hospitalInfoId" column="hospital_info_id" jdbcType="INTEGER"/>
        <result property="onlineStatus" column="online_status" jdbcType="INTEGER"/>
        <result property="onlineTime" column="online_time" jdbcType="TIMESTAMP"/>
        <result property="planOnlineTime" column="plan_online_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="INTEGER"/>
        <result property="prepareCompleteStatus" column="prepare_complete_status" jdbcType="INTEGER"/>
        <result property="pmoCheckStatus" column="pmo_check_status" jdbcType="INTEGER"/>
    </resultMap>

    <update id="updateBatch" parameterType="java.util.List">
        update csm.proj_hospital_online_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="online_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    WHEN hospital_info_id = #{item.hospitalInfoId} AND custom_info_id = #{item.customInfoId} AND project_info_id = #{item.projectInfoId} then
                    #{item.onlineStatus}
                </foreach>
            </trim>
            <trim prefix="online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    WHEN hospital_info_id = #{item.hospitalInfoId} AND custom_info_id = #{item.customInfoId} AND project_info_id = #{item.projectInfoId} then
                    now()
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    WHEN hospital_info_id = #{item.hospitalInfoId} AND custom_info_id = #{item.customInfoId} AND project_info_id = #{item.projectInfoId} then
                    #{item.updaterId}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    WHEN hospital_info_id = #{item.hospitalInfoId} AND custom_info_id = #{item.customInfoId} AND project_info_id = #{item.projectInfoId} then
                    now()
                </foreach>
            </trim>

        </trim>
        where (hospital_info_id, custom_info_id, project_info_id) in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            (#{item.hospitalInfoId}, #{item.customInfoId}, #{item.projectInfoId})
        </foreach>
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_hospital_online_detail
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
