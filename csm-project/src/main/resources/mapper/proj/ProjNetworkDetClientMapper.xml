<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjNetworkDetClientMapper">
    <delete id="deleteNotThieDet">
        delete from csm.proj_network_det_client t
        where hospital_info_id = #{hospitalId,jdbcType=BIGINT} and local_ip_address = #{localIpAddress,jdbcType=VARCHAR}
    </delete>
    <select id="findNetworkDetClientInfoList" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetClientRelative">
        select t1.id, t.hospital_info_id, t1.local_ip_address,
               t.hospital_name, t1.detect_status, t1.ip_count, t1.random_code, t1.ip_count
            from csm.proj_network_det_client t1
                left join csm.proj_hospital_info t
            on t1.hospital_info_id = t.hospital_info_id
        <where>
            <if test="hospitalInfoId != null">
                and t1.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
            </if>
            <if test="localIpAddress != null and localIpAddress != ''">
                and t1.local_ip_address = #{localIpAddress,jdbcType=VARCHAR}
            </if>
        </where>
        order by detect_status desc
    </select>
    <select id="findCountNetworkDetClientInfo" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetClient">
        select * from csm.proj_network_det_client t
        <where>
            and t.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
            and t.local_ip_address = #{localIpAddress,jdbcType=VARCHAR}
            and t.random_code = #{randomCode,jdbcType=BIGINT}
        </where>
    </select>
    <select id="findCountNetworkDetClientInfoByHospitalId" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetClient">
        select * from csm.proj_network_det_client t
        <where>
            and t.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
            and t.random_code = #{randomCode,jdbcType=BIGINT}
        </where>
    </select>
    <select id="getByRandomCode" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetClient">
        select * from csm.proj_network_det_client t where t.random_code = #{randomCode,jdbcType=BIGINT} and is_deleted = 0
    </select>
    <select id="findClient" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetClient"
            parameterType="com.msun.csm.model.dto.networkdetect.ProjNetworkDetClientDTO">
        select * from csm.proj_network_det_client t
         <where>
             <if test="randomCode != null">
                 and t.random_code = #{randomCode,jdbcType=BIGINT}
             </if>
             <if test="hospitalInfoId != null">
                 and t.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
             </if>
             and is_deleted = 0
         </where>

    </select>
</mapper>
