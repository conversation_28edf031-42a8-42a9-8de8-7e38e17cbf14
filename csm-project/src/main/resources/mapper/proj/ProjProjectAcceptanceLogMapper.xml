<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceLogMapper">

    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectAcceptanceLog">
            <id property="projectAcceptanceLogId" column="project_acceptance_log_id" jdbcType="BIGINT"/>
            <result property="projectAcceptanceId" column="project_acceptance_id" jdbcType="BIGINT"/>
            <result property="operaterUserId" column="operater_user_id" jdbcType="BIGINT"/>
            <result property="operaterUserName" column="operater_user_name" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="TIMESTAMP"/>
            <result property="operateTitle" column="operate_title" jdbcType="VARCHAR"/>
            <result property="operateContent" column="operate_content" jdbcType="VARCHAR"/>
            <result property="projectInfoId" column="project_info_id" jdbcType="BIGINT"/>
            <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        project_acceptance_log_id,project_acceptance_id,operater_user_id,
        operater_user_name,operate_time,operate_title,
        operate_content,project_info_id,creater_id,
        create_time,updater_id,update_time,
        is_deleted
    </sql>

    <insert id="batchInsert" parameterType="map">
        insert into csm.proj_project_acceptance_log
        (project_acceptance_log_id,
        project_info_id,
        operater_user_id,
        operater_user_name,
        operate_time,
        is_deleted,
        creater_id,
        create_time,
        operate_title,
        operate_content,
        project_acceptance_id,
        yy_order_id,
        remark
        )
        values
        <foreach collection="logs" item="item" separator=",">
            (#{item.projectAcceptanceLogId,jdbcType=BIGINT},
             #{item.projectInfoId,jdbcType=BIGINT},
             #{item.operaterUserId,jdbcType=BIGINT},
             #{item.operaterUserName,jdbcType=VARCHAR},
             #{item.operateTime,jdbcType=TIMESTAMP},
             #{item.isDeleted,jdbcType=SMALLINT},
             #{item.createrId,jdbcType=BIGINT},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.operateTitle,jdbcType=VARCHAR},
             #{item.operateContent,jdbcType=VARCHAR},
             #{item.projectAcceptanceId,jdbcType=VARCHAR},
             #{item.yyOrderId,jdbcType=BIGINT},
             #{item.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>
