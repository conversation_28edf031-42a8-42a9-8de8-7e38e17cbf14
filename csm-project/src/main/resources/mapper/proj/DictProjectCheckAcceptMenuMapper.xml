<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectCheckAcceptMenuMapper">

    <select id="getMenuByCode" resultType="com.msun.csm.dao.entity.dict.DictProjectCheckAcceptMenu">
        select *
        from csm.dict_project_check_accept_menu
        where is_delete = 0
          and menu_code = #{menuCode,jdbcType=VARCHAR}
    </select>

</mapper>
