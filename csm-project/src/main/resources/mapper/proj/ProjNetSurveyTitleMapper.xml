<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjNetSurveyTitleMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    <!--@Table csm.proj_net_survey_title-->
    <id column="net_survey_title_id" jdbcType="BIGINT" property="netSurveyTitleId" />
    <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId" />
    <result column="net_survey_title_code" jdbcType="VARCHAR" property="netSurveyTitleCode" />
    <result column="net_survey_title_name" jdbcType="VARCHAR" property="netSurveyTitleName" />
    <result column="upgradation_flag" jdbcType="SMALLINT" property="upgradationFlag" />
    <result column="his_flag" jdbcType="SMALLINT" property="hisFlag" />
    <result column="monomer_flag" jdbcType="SMALLINT" property="monomerFlag" />
    <result column="region_flag" jdbcType="SMALLINT" property="regionFlag" />
    <result column="telesales_flag" jdbcType="SMALLINT" property="telesalesFlag" />
    <result column="yy_product_id" jdbcType="INTEGER" property="yyProductId" />
    <result column="title_type" jdbcType="SMALLINT" property="titleType" />
    <result column="order_no" jdbcType="INTEGER" property="orderNo" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="required" jdbcType="SMALLINT" property="required" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    net_survey_title_id, project_info_id, net_survey_title_code, net_survey_title_name, 
    upgradation_flag, his_flag, monomer_flag, region_flag, telesales_flag, yy_product_id, 
    title_type, order_no, creater_id, create_time, updater_id, update_time, is_deleted, 
    required
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from csm.proj_net_survey_title
    where net_survey_title_id = #{netSurveyTitleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.proj_net_survey_title
    where net_survey_title_id = #{netSurveyTitleId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    insert into csm.proj_net_survey_title (net_survey_title_id, project_info_id, net_survey_title_code, 
      net_survey_title_name, upgradation_flag, his_flag, 
      monomer_flag, region_flag, telesales_flag, 
      yy_product_id, title_type, order_no, 
      creater_id, create_time, updater_id, 
      update_time, is_deleted, required
      )
    values (#{netSurveyTitleId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{netSurveyTitleCode,jdbcType=VARCHAR}, 
      #{netSurveyTitleName,jdbcType=VARCHAR}, #{upgradationFlag,jdbcType=SMALLINT}, #{hisFlag,jdbcType=SMALLINT}, 
      #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT}, #{telesalesFlag,jdbcType=SMALLINT}, 
      #{yyProductId,jdbcType=INTEGER}, #{titleType,jdbcType=SMALLINT}, #{orderNo,jdbcType=INTEGER}, 
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}, #{required,jdbcType=SMALLINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    insert into csm.proj_net_survey_title
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="netSurveyTitleId != null">
        net_survey_title_id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="netSurveyTitleCode != null">
        net_survey_title_code,
      </if>
      <if test="netSurveyTitleName != null">
        net_survey_title_name,
      </if>
      <if test="upgradationFlag != null">
        upgradation_flag,
      </if>
      <if test="hisFlag != null">
        his_flag,
      </if>
      <if test="monomerFlag != null">
        monomer_flag,
      </if>
      <if test="regionFlag != null">
        region_flag,
      </if>
      <if test="telesalesFlag != null">
        telesales_flag,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="titleType != null">
        title_type,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="required != null">
        required,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="netSurveyTitleId != null">
        #{netSurveyTitleId,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyTitleCode != null">
        #{netSurveyTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyTitleName != null">
        #{netSurveyTitleName,jdbcType=VARCHAR},
      </if>
      <if test="upgradationFlag != null">
        #{upgradationFlag,jdbcType=SMALLINT},
      </if>
      <if test="hisFlag != null">
        #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="monomerFlag != null">
        #{monomerFlag,jdbcType=SMALLINT},
      </if>
      <if test="regionFlag != null">
        #{regionFlag,jdbcType=SMALLINT},
      </if>
      <if test="telesalesFlag != null">
        #{telesalesFlag,jdbcType=SMALLINT},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="titleType != null">
        #{titleType,jdbcType=SMALLINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="required != null">
        #{required,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    update csm.proj_net_survey_title
    <set>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyTitleCode != null">
        net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyTitleName != null">
        net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
      </if>
      <if test="upgradationFlag != null">
        upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
      </if>
      <if test="hisFlag != null">
        his_flag = #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="monomerFlag != null">
        monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
      </if>
      <if test="regionFlag != null">
        region_flag = #{regionFlag,jdbcType=SMALLINT},
      </if>
      <if test="telesalesFlag != null">
        telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="titleType != null">
        title_type = #{titleType,jdbcType=SMALLINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="required != null">
        required = #{required,jdbcType=SMALLINT},
      </if>
    </set>
    where net_survey_title_id = #{netSurveyTitleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    update csm.proj_net_survey_title
    set project_info_id = #{projectInfoId,jdbcType=BIGINT},
      net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
      net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
      upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
      his_flag = #{hisFlag,jdbcType=SMALLINT},
      monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
      region_flag = #{regionFlag,jdbcType=SMALLINT},
      telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
      yy_product_id = #{yyProductId,jdbcType=INTEGER},
      title_type = #{titleType,jdbcType=SMALLINT},
      order_no = #{orderNo,jdbcType=INTEGER},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      required = #{required,jdbcType=SMALLINT}
    where net_survey_title_id = #{netSurveyTitleId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_net_survey_title
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="net_survey_title_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.netSurveyTitleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="net_survey_title_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.netSurveyTitleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="upgradation_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.upgradationFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="his_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.hisFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="monomer_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.monomerFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="region_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.regionFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="telesales_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.telesalesFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="title_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.titleType,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="required = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.required,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where net_survey_title_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.netSurveyTitleId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.proj_net_survey_title
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="project_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.projectInfoId != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="net_survey_title_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netSurveyTitleCode != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.netSurveyTitleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="net_survey_title_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netSurveyTitleName != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.netSurveyTitleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="upgradation_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.upgradationFlag != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.upgradationFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="his_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hisFlag != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.hisFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="monomer_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.monomerFlag != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.monomerFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="region_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.regionFlag != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.regionFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="telesales_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.telesalesFlag != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.telesalesFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.yyProductId != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="title_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.titleType != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.titleType,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="required = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.required != null">
            when net_survey_title_id = #{item.netSurveyTitleId,jdbcType=BIGINT} then #{item.required,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where net_survey_title_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.netSurveyTitleId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.proj_net_survey_title
    (net_survey_title_id, project_info_id, net_survey_title_code, net_survey_title_name, 
      upgradation_flag, his_flag, monomer_flag, region_flag, telesales_flag, yy_product_id, 
      title_type, order_no, creater_id, create_time, updater_id, update_time, is_deleted, 
      required)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.netSurveyTitleId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT}, 
        #{item.netSurveyTitleCode,jdbcType=VARCHAR}, #{item.netSurveyTitleName,jdbcType=VARCHAR}, 
        #{item.upgradationFlag,jdbcType=SMALLINT}, #{item.hisFlag,jdbcType=SMALLINT}, #{item.monomerFlag,jdbcType=SMALLINT}, 
        #{item.regionFlag,jdbcType=SMALLINT}, #{item.telesalesFlag,jdbcType=SMALLINT}, 
        #{item.yyProductId,jdbcType=INTEGER}, #{item.titleType,jdbcType=SMALLINT}, #{item.orderNo,jdbcType=INTEGER}, 
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT}, #{item.required,jdbcType=SMALLINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    insert into csm.proj_net_survey_title
    (net_survey_title_id, project_info_id, net_survey_title_code, net_survey_title_name, 
      upgradation_flag, his_flag, monomer_flag, region_flag, telesales_flag, yy_product_id, 
      title_type, order_no, creater_id, create_time, updater_id, update_time, is_deleted, 
      required)
    values
    (#{netSurveyTitleId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{netSurveyTitleCode,jdbcType=VARCHAR}, 
      #{netSurveyTitleName,jdbcType=VARCHAR}, #{upgradationFlag,jdbcType=SMALLINT}, #{hisFlag,jdbcType=SMALLINT}, 
      #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT}, #{telesalesFlag,jdbcType=SMALLINT}, 
      #{yyProductId,jdbcType=INTEGER}, #{titleType,jdbcType=SMALLINT}, #{orderNo,jdbcType=INTEGER}, 
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}, #{required,jdbcType=SMALLINT}
      )
    on duplicate key update 
    net_survey_title_id = #{netSurveyTitleId,jdbcType=BIGINT}, 
    project_info_id = #{projectInfoId,jdbcType=BIGINT}, 
    net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR}, 
    net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR}, 
    upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT}, 
    his_flag = #{hisFlag,jdbcType=SMALLINT}, 
    monomer_flag = #{monomerFlag,jdbcType=SMALLINT}, 
    region_flag = #{regionFlag,jdbcType=SMALLINT}, 
    telesales_flag = #{telesalesFlag,jdbcType=SMALLINT}, 
    yy_product_id = #{yyProductId,jdbcType=INTEGER}, 
    title_type = #{titleType,jdbcType=SMALLINT}, 
    order_no = #{orderNo,jdbcType=INTEGER}, 
    creater_id = #{createrId,jdbcType=BIGINT}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    updater_id = #{updaterId,jdbcType=BIGINT}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    is_deleted = #{isDeleted,jdbcType=SMALLINT}, 
    required = #{required,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjNetSurveyTitle">
    <!--@mbg.generated-->
    insert into csm.proj_net_survey_title
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="netSurveyTitleId != null">
        net_survey_title_id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="netSurveyTitleCode != null">
        net_survey_title_code,
      </if>
      <if test="netSurveyTitleName != null">
        net_survey_title_name,
      </if>
      <if test="upgradationFlag != null">
        upgradation_flag,
      </if>
      <if test="hisFlag != null">
        his_flag,
      </if>
      <if test="monomerFlag != null">
        monomer_flag,
      </if>
      <if test="regionFlag != null">
        region_flag,
      </if>
      <if test="telesalesFlag != null">
        telesales_flag,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="titleType != null">
        title_type,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="required != null">
        required,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="netSurveyTitleId != null">
        #{netSurveyTitleId,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyTitleCode != null">
        #{netSurveyTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyTitleName != null">
        #{netSurveyTitleName,jdbcType=VARCHAR},
      </if>
      <if test="upgradationFlag != null">
        #{upgradationFlag,jdbcType=SMALLINT},
      </if>
      <if test="hisFlag != null">
        #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="monomerFlag != null">
        #{monomerFlag,jdbcType=SMALLINT},
      </if>
      <if test="regionFlag != null">
        #{regionFlag,jdbcType=SMALLINT},
      </if>
      <if test="telesalesFlag != null">
        #{telesalesFlag,jdbcType=SMALLINT},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="titleType != null">
        #{titleType,jdbcType=SMALLINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="required != null">
        #{required,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="netSurveyTitleId != null">
        net_survey_title_id = #{netSurveyTitleId,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyTitleCode != null">
        net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyTitleName != null">
        net_survey_title_name = #{netSurveyTitleName,jdbcType=VARCHAR},
      </if>
      <if test="upgradationFlag != null">
        upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
      </if>
      <if test="hisFlag != null">
        his_flag = #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="monomerFlag != null">
        monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
      </if>
      <if test="regionFlag != null">
        region_flag = #{regionFlag,jdbcType=SMALLINT},
      </if>
      <if test="telesalesFlag != null">
        telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="titleType != null">
        title_type = #{titleType,jdbcType=SMALLINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="required != null">
        required = #{required,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

    <select id="selectByProjectId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.proj_net_survey_title
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
        order by order_no
    </select>
</mapper>