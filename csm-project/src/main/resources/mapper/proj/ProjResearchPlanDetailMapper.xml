<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjResearchPlanDetailMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail">
        <id column="milestone_task_detail_id" jdbcType="BIGINT" property="milestoneTaskDetailId" />
        <result column="leader_id" jdbcType="BIGINT" property="leaderId" />
        <result column="second_leader_id" jdbcType="VARCHAR" property="secondLeaderId" />
        <result column="product_deliver_id" jdbcType="BIGINT" property="productDeliverId" />
        <result column="expect_start_time" jdbcType="TIMESTAMP" property="expectStartTime" />
        <result column="expect_comp_time" jdbcType="TIMESTAMP" property="expectCompTime" />
        <result column="milestone_task_id" jdbcType="BIGINT" property="milestoneTaskId" />
        <result column="product_deliver_record_id" jdbcType="BIGINT" property="productDeliverRecordId" />
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
        <result column="creater_id" jdbcType="BIGINT" property="createrId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="result_source_id" jdbcType="BIGINT" property="resultSourceId" />
        <result column="complete_status" jdbcType="SMALLINT" property="completeStatus" />
        <result column="actual_comp_time" jdbcType="VARCHAR" property="actualCompTime" />
    </resultMap>
    <sql id="Base_Column_List">
        milestone_task_detail_id, leader_id, second_leader_id, product_deliver_id, expect_start_time,
    expect_comp_time, milestone_task_id, product_deliver_record_id, is_deleted, creater_id,
    create_time, updater_id, update_time, result_source_id, complete_status, actual_comp_time
    </sql>
    <select id="getProjMilestoneTaskDetail" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail" >
        select <include refid="Base_Column_List" />
        from csm.proj_milestone_task_detail
        where
            is_deleted=0
          and milestone_task_id = #{milestoneTaskId,jdbcType=BIGINT}
          and product_deliver_record_id =#{productDeliverRecordId,jdbcType=BIGINT};
    </select>
    <insert id="insertBatch"
            parameterType="java.util.List">
        insert into csm.proj_milestone_task_detail
        (milestone_task_detail_id, leader_id, second_leader_id,
        product_deliver_id, expect_start_time, expect_comp_time,
        milestone_task_id, product_deliver_record_id,
        is_deleted, creater_id, create_time,result_source_id
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.milestoneTaskDetailId,jdbcType=BIGINT}, #{item.leaderId,jdbcType=BIGINT}, #{item.secondLeaderId,jdbcType=VARCHAR},
            #{item.productDeliverId,jdbcType=BIGINT}, #{item.expectStartTime,jdbcType=TIMESTAMP}, #{item.expectCompTime,jdbcType=TIMESTAMP},
            #{item.milestoneTaskId,jdbcType=BIGINT}, #{item.productDeliverRecordId,jdbcType=BIGINT},
            0, #{item.createrId,jdbcType=BIGINT}, now(),#{item.resultSourceId,jdbcType=BIGINT}
            )

        </foreach>
    </insert>
    <update id="updateBatch">
        <foreach collection="list" item="item" separator=";" index="index">
            update csm.proj_milestone_task_detail
            <set>
                <if test="item.leaderId != null">
                    leader_id = #{item.leaderId,jdbcType=BIGINT},
                </if>
                <if test="item.secondLeaderId != null">
                    second_leader_id = #{item.secondLeaderId,jdbcType=BIGINT},
                </if>

                <if test="item.productDeliverRecordId != null">
                    product_deliver_record_id=#{item.productDeliverRecordId,jdbcType=BIGINT},
                </if>
                <if test="item.expectStartTime != null">
                    expect_start_time =#{item.expectStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.expectCompTime != null">
                    expect_comp_time=#{item.expectCompTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createrId != null">
                    creater_id=#{item.createrId,jdbcType=BIGINT},
                </if>
                <if test="item.createTime != null">
                    create_time=#{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updaterId != null">
                    updater_id=#{item.updaterId,jdbcType=BIGINT},
                </if>
                <if test="item.updateTime != null">
                    update_time=#{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.resultSourceId != null">
                    result_source_id=#{item.resultSourceId,jdbcType=BIGINT},
                </if>
                <if test="item.completeStatus != null">
                    complete_status=#{item.completeStatus,jdbcType=BIGINT},
                </if>
                <if test="item.actualCompTime != null">
                    actual_comp_time=#{item.actualCompTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where
            milestone_task_id=#{item.milestoneTaskId,jdbcType=BIGINT}
            <if test="item.productDeliverId != null">
             and   product_deliver_id =#{item.productDeliverId,jdbcType=BIGINT}
            </if>
        </foreach>
    </update>
</mapper>
