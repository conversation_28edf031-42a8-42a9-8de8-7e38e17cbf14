<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectStageInfoMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectStageInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId" />
    <result column="project_stage_id" jdbcType="BIGINT" property="projectStageId" />
    <result column="project_stage_code" jdbcType="VARCHAR" property="projectStageCode" />
    <result column="project_stage_name" jdbcType="VARCHAR" property="projectStageName" />
    <result column="invalid_flag" jdbcType="SMALLINT" property="invalidFlag" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="INTEGER" property="orderNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_info_id, project_stage_id, project_stage_code, project_stage_name, invalid_flag, 
    creater_id, create_time, updater_id, update_time, order_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from csm.proj_project_stage_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="findProjProjectStageInfoByProjectInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from csm.proj_project_stage_info
    where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    and invalid_flag = 0
    order by order_no
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from csm.proj_project_stage_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStageInfo">
    insert into csm.proj_project_stage_info (id, project_info_id, project_stage_id, 
      project_stage_code, project_stage_name, invalid_flag, 
      creater_id, create_time, updater_id, 
      update_time, order_no)
    values (#{id,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{projectStageId,jdbcType=BIGINT}, 
      #{projectStageCode,jdbcType=VARCHAR}, #{projectStageName,jdbcType=VARCHAR}, #{invalidFlag,jdbcType=SMALLINT}, 
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStageInfo">
    insert into csm.proj_project_stage_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectInfoId != null">
        project_info_id,
      </if>
      <if test="projectStageId != null">
        project_stage_id,
      </if>
      <if test="projectStageCode != null">
        project_stage_code,
      </if>
      <if test="projectStageName != null">
        project_stage_name,
      </if>
      <if test="invalidFlag != null">
        invalid_flag,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectInfoId != null">
        #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="projectStageId != null">
        #{projectStageId,jdbcType=BIGINT},
      </if>
      <if test="projectStageCode != null">
        #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="projectStageName != null">
        #{projectStageName,jdbcType=VARCHAR},
      </if>
      <if test="invalidFlag != null">
        #{invalidFlag,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStageInfo">
    update csm.proj_project_stage_info
    <set>
      <if test="projectInfoId != null">
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
      </if>
      <if test="projectStageId != null">
        project_stage_id = #{projectStageId,jdbcType=BIGINT},
      </if>
      <if test="projectStageCode != null">
        project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      </if>
      <if test="projectStageName != null">
        project_stage_name = #{projectStageName,jdbcType=VARCHAR},
      </if>
      <if test="invalidFlag != null">
        invalid_flag = #{invalidFlag,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectStageInfo">
    update csm.proj_project_stage_info
    set project_info_id = #{projectInfoId,jdbcType=BIGINT},
      project_stage_id = #{projectStageId,jdbcType=BIGINT},
      project_stage_code = #{projectStageCode,jdbcType=VARCHAR},
      project_stage_name = #{projectStageName,jdbcType=VARCHAR},
      invalid_flag = #{invalidFlag,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>