<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipDocMapper">

    <select id="findProjEquipDoc" parameterType="com.msun.csm.model.dto.ProjEquipDocDTO" resultType="com.msun.csm.model.vo.ProjEquipDocVO">
        select sf.file_name as docName,sf.file_path as docPath
        from csm.proj_equip_doc ped
        left join csm.sys_file sf on ped.file_code = sf.file_code
        where ped.yy_product_id = #{productId}
        and ped.doc_type_code = #{docTypeCode}
        <if test="modalKey !=null">
            and ped.modal_key = #{modalKey}
        </if>
        <if test="modalName !=null">
            and ped.modal_name = #{modalName}
        </if>
        <if test="equipFactoryId !=null">
            and ped.equip_factory_id = #{equipFactoryId}
        </if>
        <if test="equipFactoryName !=null">
            and ped.equip_factory_name = #{equipFactoryName}
        </if>
        and ped.is_deleted = 0
    </select>
</mapper>