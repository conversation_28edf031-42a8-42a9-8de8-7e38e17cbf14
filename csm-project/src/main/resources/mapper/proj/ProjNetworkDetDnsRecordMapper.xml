<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjNetworkDetDnsRecordMapper">
    <select id="findNetworkDetDnsInfoList" resultType="com.msun.csm.dao.entity.proj.ProjNetworkDetDnsRecord">
        SELECT T.*
        FROM CSM.PROJ_NETWORK_DET_DNS_RECORD T
        INNER JOIN CSM.PROJ_NETWORK_DET_CLIENT T1
        ON T.LOCAL_IP_ADDRESS = T1.LOCAL_IP_ADDRESS
        AND T.LOG_ID = T1.HOSPITAL_INFO_ID
        AND T.RANDOM_CODE = T1.RANDOM_CODE
        AND T.RANDOM_CODE = #{randomCode,jdbcType=BIGINT}
        <if test="detectStartTime != null">
            AND T1.DETECT_START_TIME = #{detectStartTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY T.CREATE_TIME DESC
    </select>
    <delete id="deleteNotThieDet">
        delete from csm.proj_network_det_dns_record t
        <where>
            and t.log_id = #{logId,jdbcType=BIGINT}
            and t.local_ip_address = #{localIpAddress,jdbcType=VARCHAR}
        </where>
    </delete>


    <delete id="deleteByLocalIpAndLogId">
        delete from csm.proj_network_det_dns_record t
        <where>
            and t.log_id = #{logId,jdbcType=BIGINT}
            and t.local_ip_address = #{localIpAddress,jdbcType=VARCHAR}
        </where>
    </delete>
</mapper>
