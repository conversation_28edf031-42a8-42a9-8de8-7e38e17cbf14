<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSimulationMapper">
    <select id="findSimulationRole" resultType="com.msun.csm.model.vo.projsimulation.SimulationRoleVO"
            parameterType="com.msun.csm.model.dto.projsimulation.ProjSimulationUserDTO">
        select t.* from
        (select dsr.dict_simulaiton_role_id as role_id,
                psu.proj_simulation_user_id,
                max(dsr.role_name) as role_name,
                max(dsr.unite_flag) as unite_flag,
                max(psu.simulation_dept_id) as simulation_dept_id,
                max(psu.simulation_dept_name) as simulation_dept_name,
                max(psu.simulation_user_id) as simulation_user_id,
                max(psu.simulation_user_name) as simulation_user_name,
                max(psu.simulation_user_account) as simulation_user_account,
                max(psu.simulation_ward_id) as simulation_ward_id,
                max(dsb.sort_order) as sort_order
        from csm.dict_simulaiton_role dsr
        left join csm.dict_simulation_business dsb on dsr.dict_simulaiton_role_id = dsb.dict_role_id and dsb.is_deleted = 0
        left join csm.proj_product_deliver_record ppdr on dsb.yy_product_id = ppdr.product_deliver_id and ppdr.is_deleted = 0
        left join csm.dict_product dp on ppdr.product_deliver_id = dp.yy_product_id and dp.is_deleted = 0
        left join csm.proj_simulation_user psu on dsr.dict_simulaiton_role_id = psu.dict_role_id and ppdr.project_info_id = psu.project_info_id and psu.is_deleted = 0
        where ppdr.custom_info_id = #{customInfoId}
        and ppdr.project_info_id = #{projectInfoId}
        and dsr.is_deleted = 0
        and dp.yy_product_id is not null
        group by dsr.dict_simulaiton_role_id,psu.proj_simulation_user_id

        union

        select dsr.dict_simulaiton_role_id as role_id,
               psu.proj_simulation_user_id,
               max(dsr.role_name) as role_name,
               max(dsr.unite_flag) as unite_flag,
               max(psu.simulation_dept_id) as simulation_dept_id,
               max(psu.simulation_dept_name) as simulation_dept_name,
               max(psu.simulation_user_id) as simulation_user_id,
               max(psu.simulation_user_name) as simulation_user_name,
               max(psu.simulation_user_account) as simulation_user_account,
               max(psu.simulation_ward_id) as simulation_ward_id,
               max(dsb.sort_order) as sort_order
        from csm.dict_simulaiton_role dsr
        left join csm.dict_simulation_business dsb on dsr.dict_simulaiton_role_id = dsb.dict_role_id and dsb.is_deleted = 0
        left join csm.proj_product_deliver_record ppdr on dsb.yy_product_id = ppdr.product_deliver_id and ppdr.is_deleted = 0
        left join csm.dict_product_vs_modules dpvm on ppdr.product_deliver_id = dpvm.yy_module_id and dpvm.is_deleted = 0
        left join csm.proj_simulation_user psu on dsr.dict_simulaiton_role_id = psu.dict_role_id and ppdr.project_info_id = psu.project_info_id and psu.is_deleted = 0
        where ppdr.custom_info_id = #{customInfoId}
        and ppdr.project_info_id = #{projectInfoId}
        and dsr.is_deleted = 0
        and dpvm.product_vs_module_id is not null
        group by dsr.dict_simulaiton_role_id,psu.proj_simulation_user_id
        )t order by t.role_id
    </select>

    <select id="findSimulationDetail" resultType="com.msun.csm.model.vo.projsimulation.ProjSimulationDetailVO"
            parameterType="com.msun.csm.model.dto.projsimulation.ProjSimulationUserDTO">
        select t.* from
        (select max(dsb.simulation_scene) as simulation_scene,max(dsr.role_name) as role_name,max(psu.simulation_dept_name) as simulation_dept_name,
                max(psu.simulation_user_name) as simulation_user_name,max(psu.simulation_user_account) as simulation_user_account,
                max(dsb.simulation_business) as simulation_business,dsb.simulation_type,max(dsb.check_sql) as check_sql,max(psu.simulation_user_id) as simulation_user_id,
                string_agg(distinct dp.product_name,'、') as product_name,max(psd.pass_flag) as pass_flag,dsb.sort_order
        from csm.proj_simulation_detail psd
        left join csm.dict_simulation_business dsb on psd.dict_simulation_business_id = dsb.dict_simulation_business_id and dsb.is_deleted = 0
        left join csm.proj_product_deliver_record ppdr on dsb.yy_product_id = ppdr.product_deliver_id and psd.project_info_id = ppdr.project_info_id and ppdr.is_deleted = 0
        left join csm.dict_product dp on ppdr.product_deliver_id = dp.yy_product_id and dp.is_deleted = 0
        left join csm.dict_simulaiton_role dsr on dsb.dict_role_id = dsr.dict_simulaiton_role_id and dsr.is_deleted = 0
        left join csm.proj_simulation_user psu on psd.hospital_info_id = psu.hospital_info_id and dsr.dict_simulaiton_role_id = psu.dict_role_id and psu.is_deleted = 0
        where psd.custom_info_id = #{customInfoId}
        and psd.project_info_id = #{projectInfoId}
        and psd.hospital_info_id = #{hospitalInfoId}
        and psd.proj_simulation_result_id = -1
        and psd.is_deleted = 0
        and dp.yy_product_id is not null
        group by dsb.simulation_type,dsb.sort_order

        union

        select max(dsb.simulation_scene) as simulation_scene,max(dsr.role_name) as role_name,max(psu.simulation_dept_name) as simulation_dept_name,
               max(psu.simulation_user_name) as simulation_user_name,max(psu.simulation_user_account) as simulation_user_account,
               max(dsb.simulation_business) as simulation_business,dsb.simulation_type,max(dsb.check_sql) as check_sql,max(psu.simulation_user_id) as simulation_user_id,
               string_agg(distinct dpvm.yy_module_name,'、') as product_name,max(psd.pass_flag) as pass_flag,dsb.sort_order
        from csm.proj_simulation_detail psd
        left join csm.dict_simulation_business dsb on psd.dict_simulation_business_id = dsb.dict_simulation_business_id and dsb.is_deleted = 0
        left join csm.proj_product_deliver_record ppdr on dsb.yy_product_id = ppdr.product_deliver_id and psd.project_info_id = ppdr.project_info_id and ppdr.is_deleted = 0
        left join csm.dict_product_vs_modules dpvm on ppdr.product_deliver_id = dpvm.yy_module_id and dpvm.is_deleted = 0
        left join csm.dict_simulaiton_role dsr on dsb.dict_role_id = dsr.dict_simulaiton_role_id and dsr.is_deleted = 0
        left join csm.proj_simulation_user psu on psd.hospital_info_id = psu.hospital_info_id and dsr.dict_simulaiton_role_id = psu.dict_role_id and psu.is_deleted = 0
        where psd.custom_info_id = #{customInfoId}
        and psd.project_info_id = #{projectInfoId}
        and psd.hospital_info_id = #{hospitalInfoId}
        and psd.proj_simulation_result_id = -1
        and psd.is_deleted = 0
        and dpvm.yy_module_id is not null
        group by dsb.simulation_type,dsb.sort_order
        )t order by t.simulation_type,t.sort_order
    </select>

    <select id="findHospitalSimulatonDetail" resultType="com.msun.csm.model.vo.projsimulation.HospitalSimulatonDetaiVO"
            parameterType="com.msun.csm.model.dto.projsimulation.ProjSimulationUserDTO">
        select psd.proj_simulation_detail_id,psd.dict_simulation_business_id,psd.proj_simulation_result_id,
               psd.custom_info_id,psd.project_info_id,psd.hospital_info_id,psd.pass_flag,psd.start_time,
               psd.end_time,dsb.check_sql,dsb.simulation_type,psu.simulation_user_id
        from csm.proj_simulation_detail psd
        left join csm.dict_simulation_business dsb on psd.dict_simulation_business_id = dsb.dict_simulation_business_id and dsb.is_deleted = 0
        left join csm.proj_simulation_user psu on psd.hospital_info_id = psu.hospital_info_id and dsb.dict_role_id = psu.dict_role_id and psu.is_deleted = 0
        where psd.custom_info_id = #{customInfoId}
        and psd.project_info_id = #{projectInfoId}
        and psd.hospital_info_id = #{hospitalInfoId}
        and psd.is_deleted = 0
    </select>

    <select id="findSimulationDetailByResultId" resultType="com.msun.csm.model.vo.projsimulation.ProjSimulationDetailVO">
        select t.* from
        (select max(dsb.simulation_scene) as simulation_scene,max(dsr.role_name) as role_name,
                max(psu.simulation_dept_name) as simulation_dept_name,max(psu.simulation_user_name) as simulation_user_name,
                max(psu.simulation_user_account) as simulation_user_account, max(dsb.simulation_business) as simulation_business,
                string_agg(dp.product_name,'、') as product_name,max(psd.pass_flag) as pass_flag,max(dsb.sort_order) as sort_order
        from csm.proj_simulation_detail psd
        left join csm.dict_simulation_business dsb on psd.dict_simulation_business_id = dsb.dict_simulation_business_id and dsb.is_deleted = 0
        left join csm.dict_product dp on dsb.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
        left join csm.dict_simulaiton_role dsr on dsb.dict_role_id = dsr.dict_simulaiton_role_id and dsr.is_deleted = 0
        left join csm.proj_simulation_user psu on psd.hospital_info_id = psu.hospital_info_id and dsr.dict_simulaiton_role_id = psu.dict_role_id and psu.is_deleted = 0
        where psd.proj_simulation_result_id = #{projSimulationResultId}
        and dsb.simulation_type = #{simulationType}
        and psd.is_deleted = 0
        and dp.yy_product_id is not null
        group by dsb.sort_order

        union

         select max(dsb.simulation_scene) as simulation_scene,max(dsr.role_name) as role_name,
                max(psu.simulation_dept_name) as simulation_dept_name,max(psu.simulation_user_name) as simulation_user_name,
                max(psu.simulation_user_account) as simulation_user_account,max(dsb.simulation_business) as simulation_business,
                string_agg(dpvm.yy_module_name,'、') as product_name,max(psd.pass_flag) as pass_flag,max(dsb.sort_order) as sort_order
        from csm.proj_simulation_detail psd
        left join csm.dict_simulation_business dsb on psd.dict_simulation_business_id = dsb.dict_simulation_business_id and dsb.is_deleted = 0
        left join csm.dict_product_vs_modules dpvm on dsb.yy_product_id = dpvm.yy_module_id and dpvm.is_deleted = 0
        left join csm.dict_simulaiton_role dsr on dsb.dict_role_id = dsr.dict_simulaiton_role_id and dsr.is_deleted = 0
        left join csm.proj_simulation_user psu on psd.hospital_info_id = psu.hospital_info_id and dsr.dict_simulaiton_role_id = psu.dict_role_id and psu.is_deleted = 0
        where psd.proj_simulation_result_id = #{projSimulationResultId}
        and dsb.simulation_type = #{simulationType}
        and psd.is_deleted = 0
        and dpvm.yy_module_id is not null
        group by dsb.sort_order
        )t order by t.sort_order
    </select>

    <select id="findProjSimulationBusiness" resultType="com.msun.csm.dao.entity.dict.DictSimulationBusiness">
        select distinct dsb.*
        from csm.dict_simulation_business dsb
        left join csm.proj_product_deliver_record ppdr on dsb.yy_product_id = ppdr.product_deliver_id and ppdr.is_deleted = 0
        where ppdr.project_info_id = #{projectInfoId}
        and dsb.is_deleted = 0
    </select>
</mapper>