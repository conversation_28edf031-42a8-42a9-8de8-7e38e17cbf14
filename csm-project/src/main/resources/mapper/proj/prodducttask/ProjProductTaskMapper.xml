<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductTaskMapper">
    <update id="updateTaskStatus">
        UPDATE csm.proj_product_task
        SET task_status = ot.status,
        task_result = ot.task_result
        FROM (

        select res.hospital_id,
        case when res.status = 1 then 1 else 2 end as status,
        res.task_result,
        h.hospital_info_id,
        res.task_code
        from
        <if test="taskCodeList != null and taskCodeList.size() > 0">
                    <foreach collection="taskCodeList" item="item" open="( " separator="union all " close=")">
                        select
                         #{item.hospitalId} as hospital_id,
                         #{item.status} as status,
                         #{item.taskResult} as task_result,
                         #{item.taskCode} as task_code
                    </foreach>
                </if>
                as res
               inner join csm.proj_hospital_info h on h.cloud_hospital_id = res.hospital_id
            ) ot
        WHERE proj_product_task.hospital_info_id = ot.hospital_info_id
            and proj_product_task.task_status != 1
            and proj_product_task.project_info_id = #{projectInfoId}
            and proj_product_task.task_code = ot.task_code
    </update>
    <update id="deleteDataByParam">
        update
        csm.proj_product_task
        set is_deleted = 1
        where project_info_id = #{projectInfoId}
        and hospital_info_id = #{hospitalInfoId}
        and yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" separator="," close=")">
            #{item.yyProductId}
        </foreach>
        and task_code not in
        <foreach collection="yyProductIdList" item="item" open="(" separator="," close=")">
            #{item.taskCode}
        </foreach>
    </update>
    <select id="selectProductTaskList" resultType="com.msun.csm.model.resp.producttask.ProjProductTaskResp">
        select
            task.product_task_id          ,
            task.product_task_class_code  ,
            task.survey_title             ,
            task.survey_value             ,
            task.task_title               ,
        task.task_detail ,
        task.remark ,
        task.yy_product_id ,
        task.is_deleted ,
        task.hospital_info_id ,
        task.sort_no ,
        task.project_info_id ,
        task.task_page_url ,
        task.cloud_product_code ,
        task.task_status ,
        task.task_explain_link ,
        task.validate_standards ,
        task.validate_detail_sql ,
        case when task.task_status = 0 and (task.task_validate_type is null or task.task_validate_type = '')
        and (task.verify_sql_text is null or task.verify_sql_text = '')
        then '未完成'
        when task.task_status = 0 and task.task_validate_type is not null and task.task_validate_type != ''
        and task.verify_sql_text is not null and task.verify_sql_text != ''
        then '待检测'
        when task.task_status = 1 then '已完成'
        when task.task_status = 2 then '检测失败'
        else '未完成'
        end as task_status_str,
        task.user_id ,
        task.job_plan_time ,
        task.task_result ,
        task.creater_id ,
        task.create_time ,
        task.updater_id ,
        task.update_time ,
        task.verify_sql_text ,
        task.product_task_class_name,
        task.task_validate_type,
        task.progress_desc,
        task.task_code
        from csm.proj_product_task task
        where task.is_deleted = 0
        <if test="productTaskId != null and productTaskId != 0">
            and task.product_task_id = #{productTaskId}
        </if>
        <if test="projectInfoId != null">
            and task.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and task.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null">
            and task.yy_product_id = #{yyProductId}
        </if>
        <if test="taskStatus != null and taskStatus != 99">
            <if test="taskStatus == 1">
                and task.task_status = #{taskStatus}
            </if>
            <if test="taskStatus != 1">
                and task.task_status != 1
            </if>
        </if>
        <if test="taskStatusNot != null and taskStatusNot != 99">
            and task.task_status != #{taskStatusNot}
        </if>
        order by task.product_task_class_code, task.sort_no asc, task.task_status asc
    </select>
    <select id="selectAllFlieList" resultType="com.msun.csm.dao.entity.proj.ProjProjectFile">
        select
            file.*
        from csm.proj_product_task task
        inner join csm.proj_product_task_file_relation fr on task.product_task_id = fr.product_task_id
        inner join csm.proj_project_file file on fr.project_file_id = file.project_file_id
        where  task.is_deleted = 0
          and task.product_task_id = #{productTaskId}

    </select>

    <insert id="insertBatch">
        insert into csm.proj_product_task(product_task_id,
                                          product_task_class_code,
                                          survey_title,
                                          survey_value,
                                          task_title,
                                          task_detail,
                                          remark,
                                          yy_product_id,
                                          is_deleted,
                                          hospital_info_id,
                                          sort_no,
                                          project_info_id,
                                          task_page_url,
                                          cloud_product_code,
                                          task_status,
                                          user_id,
                                          job_plan_time,
                                          task_result,
                                          creater_id,
                                          create_time,
                                          updater_id,
                                          update_time,
                                          verify_sql_text,
                                          progress_desc,
                                          product_task_class_name,
                                          task_validate_type,
                                          task_code)
        values
        <foreach collection="taskList" item="item" separator=",">
        (#{item.productTaskId}, #{item.productTaskClassCode}, #{item.surveyTitle}, #{item.surveyValue},
         #{item.taskTitle},
         #{item.taskDetail}, #{item.remark}, #{item.yyProductId}, #{item.isDeleted}, #{item.hospitalInfoId},
         #{item.sortNo}, #{item.projectInfoId}, #{item.taskPageUrl}, #{item.cloudProductCode},
         #{item.taskStatus}, #{item.userId}, #{item.jobPlanTime}, #{item.taskResult},
         #{item.createrId}, #{item.createTime}, #{item.updaterId}, #{item.updateTime},
         #{item.verifySqlText}, #{item.progressDesc}, #{item.productTaskClassName},
         #{item.taskValidateType}, #{item.taskCode})
    </foreach>
    </insert>

    <update id="deleteByParam">
        update
            csm.proj_product_task
        set is_deleted = 1
        where project_info_id = #{projectInfoId}
          and hospital_info_id = #{hospitalInfoId}
          and yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <insert id="insertOrUpdate">
        insert into csm.proj_product_task(product_task_id,
                                          product_task_class_code,
                                          survey_title,
                                          survey_value,
                                          task_title,
                                          task_detail,
                                          remark,
                                          yy_product_id,
                                          is_deleted,
                                          hospital_info_id,
                                          sort_no,
                                          project_info_id,
                                          task_page_url,
                                          cloud_product_code,
                                          task_status,
                                          user_id,
                                          job_plan_time,
                                          task_result,
                                          creater_id,
                                          create_time,
                                          updater_id,
                                          update_time,
                                          verify_sql_text,
                                          progress_desc,
                                          product_task_class_name,
                                          task_validate_type,
                                          task_code,
                                          task_explain_link,
                                          validate_standards,
                                          validate_detail_sql)
        values (#{task.productTaskId}, #{task.productTaskClassCode}, #{task.surveyTitle}, #{task.surveyValue},
                #{task.taskTitle},
                #{task.taskDetail}, #{task.remark}, #{task.yyProductId}, #{task.isDeleted}, #{task.hospitalInfoId},
                #{task.sortNo}, #{task.projectInfoId}, #{task.taskPageUrl}, #{task.cloudProductCode},
                #{task.taskStatus}, #{task.userId}, #{task.jobPlanTime}, #{task.taskResult},
                #{task.createrId}, #{task.createTime}, #{task.updaterId}, #{task.updateTime},
                #{task.verifySqlText}, #{task.progressDesc}, #{task.productTaskClassName},
                #{task.taskValidateType}, #{task.taskCode}, #{task.taskExplainLink}, #{task.validateStandards},
                #{task.validateDetailSql})
        ON CONFLICT (project_info_id, hospital_info_id, yy_product_id, task_code)
            do update
            set product_task_class_code = #{task.productTaskClassCode},
                survey_title            = #{task.surveyTitle},
                survey_value            = #{task.surveyValue},
                task_title              = #{task.taskTitle},
                task_detail             = #{task.taskDetail},
                remark                  = #{task.remark},
                is_deleted              = #{task.isDeleted},
                sort_no                 = #{task.sortNo},
                task_page_url           = #{task.taskPageUrl},
                cloud_product_code      = #{task.cloudProductCode},
                task_status             = #{task.taskStatus},
                user_id                 = #{task.userId},
                job_plan_time           = #{task.jobPlanTime},
                task_result             = #{task.taskResult},
                updater_id              = #{task.updaterId},
                update_time             = #{task.updateTime},
                verify_sql_text         = #{task.verifySqlText},
                progress_desc           = #{task.progressDesc},
                product_task_class_name = #{task.productTaskClassName},
                task_validate_type      = #{task.taskValidateType},
                task_code               = #{task.taskCode},
                task_explain_link       = #{task.taskExplainLink},
                validate_standards      = #{task.validateStandards},
                validate_detail_sql     = #{task.validateDetailSql}
    </insert>

    <update id="updateByProjectId">
        update
            csm.proj_product_task
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() > 0">
            and yy_product_id in
            <foreach collection="changeProductList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_product_task
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and yy_product_id in
        <foreach collection="oldProductIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="deleteByProjectHospitalProduct">
        update csm.proj_product_task
        set is_deleted  = 1,
            update_time = now()
        WHERE project_info_id = #{projectInfoId}
          and hospital_info_id = #{hospitalInfoId}
          and yy_product_id = #{yyProductId}
          and is_deleted = 0
    </update>
</mapper>
