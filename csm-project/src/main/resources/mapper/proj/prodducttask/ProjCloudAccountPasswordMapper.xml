<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjCloudAccountPasswordMapper">

    <select id="selectDataByCloudHospitalId"
            resultType="com.msun.csm.model.resp.producttask.ProjCustomerProjectTypeResp">
        select distinct ppi.custom_info_id, ppi.project_type, max(phi.hospital_info_id) hospital_info_id
        from csm.proj_hospital_info phi
        inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
        and phvpt.is_deleted = 0
        inner join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type
        and ppi.custom_info_id = phi.custom_info_id
        and ppi.is_deleted = 0
        where phi.is_deleted = 0
        and phi.cloud_hospital_id = #{cloudHospitalId}
        <if test="hospitalInfoId != null">
            and phi.hospital_info_id = #{hospitalInfoId}
        </if>
        group by ppi.custom_info_id, ppi.project_type
    </select>
    <select id="selectAllCustomerData"
            resultType="com.msun.csm.model.resp.producttask.ProjCustomerProjectTypeResp">
        select distinct ppi.custom_info_id, ppi.project_type, max(phi.hospital_info_id) hospital_info_id
        from csm.proj_hospital_info phi
                 inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
            and phvpt.is_deleted = 0
                 inner join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type
            and ppi.custom_info_id = phi.custom_info_id
            and ppi.is_deleted = 0
        where phi.is_deleted = 0
          and phi.cloud_hospital_id is not null
          and phi.cloud_domain != ''
        and phi.org_id is not null
        and phi.cloud_domain is not null
        group by ppi.custom_info_id, ppi.project_type

    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_cloud_account_password
        set customer_info_id = #{newCustomInfoId}
        where customer_info_id = #{oldCustomInfoId}
    </update>
</mapper>
