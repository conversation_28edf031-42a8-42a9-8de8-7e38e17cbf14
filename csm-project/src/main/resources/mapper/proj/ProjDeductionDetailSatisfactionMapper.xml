<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjDeductionDetailSatisfactionMapper">

    <select id="getSatisfactionScoreRecord" resultType="com.msun.csm.dao.entity.proj.SatisfactionSurveyScoreRecordVO">
        select pdds.id,
               case
                   when pdds.revisit_flag = 1 then true
                   else false
                   end as "revisitFlag",
               pdds.role_code,
               dssr.role_name,
               pdds.revisit_people_name,
               pdds.level_code,
               dssl.level_name,
               dssl.level_score,
               pdds.revisit_remark,
               pdds.project_info_id
        from csm.proj_deduction_detail_satisfaction pdds
                 left join csm.dict_satisfaction_survey_role dssr on
            pdds.role_code = dssr.role_code
                and dssr.is_delete = 0
                 left join csm.dict_satisfaction_survey_level dssl on
            pdds.level_code = dssl.level_code
                and dssl.is_delete = 0
        where pdds.is_delete = 0
          and pdds.project_info_id = #{projectInfoId}
        order by dssr.sort_no
    </select>

    <update id="updateSatisfactionSurveyScoreById">
        update csm.proj_deduction_detail_satisfaction
        <set>
            updater_id = #{updaterId},
            update_time = now(),
            <if test="revisitFlag != null">
                revisit_flag = #{revisitFlag},
            </if>
            <if test="revisitPeopleName != null">
                revisit_people_name = #{revisitPeopleName},
            </if>
            <if test="levelCode != null">
                level_code = #{levelCode},
            </if>
            <if test="revisitRemark != null">
                revisit_remark = #{revisitRemark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getRevisitSatisfactionScoreRecord" resultType="com.msun.csm.dao.entity.proj.SatisfactionSurveyScoreRecordVO">
        select pdds.id,
               case
                   when pdds.revisit_flag = 1 then true
                   else false
                   end as "revisitFlag",
               pdds.role_code,
               dssr.role_name,
               pdds.revisit_people_name,
               pdds.level_code,
               dssl.level_name,
               dssl.level_score,
               pdds.revisit_remark,
               pdds.project_info_id
        from csm.proj_deduction_detail_satisfaction pdds
                 left join csm.dict_satisfaction_survey_role dssr on
            pdds.role_code = dssr.role_code
                and dssr.is_delete = 0
                 left join csm.dict_satisfaction_survey_level dssl on
            pdds.level_code = dssl.level_code
                and dssl.is_delete = 0
        where pdds.is_delete = 0
          and pdds.revisit_flag = 1
          and pdds.project_info_id = #{projectInfoId}
        order by dssr.sort_no
    </select>

</mapper>
