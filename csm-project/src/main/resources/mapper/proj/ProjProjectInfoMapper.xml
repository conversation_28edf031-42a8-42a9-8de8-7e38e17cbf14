<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_project_info-->
        <id column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="project_deliver_status" jdbcType="SMALLINT" property="projectDeliverStatus"/>
        <result column="order_info_id" jdbcType="BIGINT" property="orderInfoId"/>
        <result column="project_leader_id" jdbcType="BIGINT" property="projectLeaderId"/>
        <result column="project_team_id" jdbcType="BIGINT" property="projectTeamId"/>
        <result column="work_time" jdbcType="TIMESTAMP" property="workTime"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="survey_complete_time" jdbcType="TIMESTAMP" property="surveyCompleteTime"/>
        <result column="settle_in_time" jdbcType="TIMESTAMP" property="settleInTime"/>
        <result column="pre_complete_time" jdbcType="TIMESTAMP" property="preCompleteTime"/>
        <result column="online_time" jdbcType="TIMESTAMP" property="onlineTime"/>
        <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime"/>
        <result column="upgradation_type" jdbcType="SMALLINT" property="upgradationType"/>
        <result column="his_flag" jdbcType="SMALLINT" property="hisFlag"/>
        <result column="standard_duration" jdbcType="VARCHAR" property="standardDuration"/>
        <result column="duration_reduction" jdbcType="VARCHAR" property="durationReduction"/>
        <result column="settle_status" jdbcType="VARCHAR" property="settleStatus"/>
        <result column="settle_proportion" jdbcType="VARCHAR" property="settleProportion"/>
        <result column="settle_amount" jdbcType="NUMERIC" property="settleAmount"/>
        <result column="supervisor_flag" jdbcType="SMALLINT" property="supervisorFlag"/>
        <result column="member_init_flag" jdbcType="SMALLINT" property="memberInitFlag"/>
        <result column="msun_health_point" jdbcType="VARCHAR" property="msunHealthPoint"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="accept_score" jdbcType="INTEGER" property="acceptScore"/>
        <result column="project_type" jdbcType="SMALLINT" property="projectType"/>
        <result column="project_number" jdbcType="VARCHAR" property="projectNumber"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="online_step_flag" jdbcType="SMALLINT" property="onlineStepFlag"/>
        <result column="yy_apply_accept_time" jdbcType="TIMESTAMP" property="yyApplyAcceptTime"/>
        <result column="project_plan_flag" jdbcType="SMALLINT" property="projectPlanFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_info_id, custom_info_id, project_deliver_status, order_info_id, project_leader_id,
        project_team_id, work_time, receive_time, survey_complete_time, settle_in_time, pre_complete_time,
        online_time, accept_time, upgradation_type, his_flag, standard_duration, duration_reduction,
        settle_status, settle_proportion, settle_amount, supervisor_flag, member_init_flag,
        msun_health_point, is_deleted, creater_id, create_time, updater_id, update_time,
        accept_score,
        project_type,
        project_number,
        project_name,
        online_step_flag,
        project_plan_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_project_info
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_info (project_info_id, custom_info_id, project_deliver_status,
        order_info_id, project_leader_id, project_team_id,
        work_time, receive_time, survey_complete_time,
        settle_in_time, pre_complete_time, online_time,
        accept_time, upgradation_type, his_flag,
        standard_duration, duration_reduction, settle_status,
        settle_proportion, settle_amount, supervisor_flag,
        member_init_flag, msun_health_point, is_deleted,
        creater_id, create_time, updater_id,
        update_time, accept_score, project_type,
        project_number, project_name, apply_accept_time, external_accept_time)
        values (#{projectInfoId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT},
        #{projectDeliverStatus,jdbcType=SMALLINT},
        #{orderInfoId,jdbcType=BIGINT}, #{projectLeaderId,jdbcType=BIGINT}, #{projectTeamId,jdbcType=BIGINT},
        #{workTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP}, #{surveyCompleteTime,jdbcType=TIMESTAMP},
        #{settleInTime,jdbcType=TIMESTAMP}, #{preCompleteTime,jdbcType=TIMESTAMP}, #{onlineTime,jdbcType=TIMESTAMP},
        #{acceptTime,jdbcType=TIMESTAMP}, #{upgradationType,jdbcType=SMALLINT}, #{hisFlag,jdbcType=SMALLINT},
        #{standardDuration,jdbcType=VARCHAR}, #{durationReduction,jdbcType=VARCHAR}, #{settleStatus,jdbcType=VARCHAR},
        #{settleProportion,jdbcType=VARCHAR}, #{settleAmount,jdbcType=NUMERIC}, #{supervisorFlag,jdbcType=SMALLINT},
        #{memberInitFlag,jdbcType=SMALLINT}, #{msunHealthPoint,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{acceptScore,jdbcType=INTEGER}, #{projectType,jdbcType=SMALLINT},
        #{projectNumber,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, #{applyAcceptTime,jdbcType=TIMESTAMP},
        #{externalAcceptTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectDeliverStatus != null">
                project_deliver_status,
            </if>
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="projectLeaderId != null">
                project_leader_id,
            </if>
            <if test="projectTeamId != null">
                project_team_id,
            </if>
            <if test="workTime != null">
                work_time,
            </if>
            <if test="receiveTime != null">
                receive_time,
            </if>
            <if test="surveyCompleteTime != null">
                survey_complete_time,
            </if>
            <if test="settleInTime != null">
                settle_in_time,
            </if>
            <if test="preCompleteTime != null">
                pre_complete_time,
            </if>
            <if test="onlineTime != null">
                online_time,
            </if>
            <if test="acceptTime != null">
                accept_time,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="hisFlag != null">
                his_flag,
            </if>
            <if test="standardDuration != null">
                standard_duration,
            </if>
            <if test="durationReduction != null">
                duration_reduction,
            </if>
            <if test="settleStatus != null">
                settle_status,
            </if>
            <if test="settleProportion != null">
                settle_proportion,
            </if>
            <if test="settleAmount != null">
                settle_amount,
            </if>
            <if test="supervisorFlag != null">
                supervisor_flag,
            </if>
            <if test="memberInitFlag != null">
                member_init_flag,
            </if>
            <if test="msunHealthPoint != null">
                msun_health_point,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="acceptScore != null">
                accept_score,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="projectNumber != null">
                project_number,
            </if>
            <if test="projectName != null">
                project_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectDeliverStatus != null">
                #{projectDeliverStatus,jdbcType=SMALLINT},
            </if>
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectLeaderId != null">
                #{projectLeaderId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamId != null">
                #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="workTime != null">
                #{workTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="surveyCompleteTime != null">
                #{surveyCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settleInTime != null">
                #{settleInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="preCompleteTime != null">
                #{preCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptTime != null">
                #{acceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="standardDuration != null">
                #{standardDuration,jdbcType=VARCHAR},
            </if>
            <if test="durationReduction != null">
                #{durationReduction,jdbcType=VARCHAR},
            </if>
            <if test="settleStatus != null">
                #{settleStatus,jdbcType=VARCHAR},
            </if>
            <if test="settleProportion != null">
                #{settleProportion,jdbcType=VARCHAR},
            </if>
            <if test="settleAmount != null">
                #{settleAmount,jdbcType=NUMERIC},
            </if>
            <if test="supervisorFlag != null">
                #{supervisorFlag,jdbcType=SMALLINT},
            </if>
            <if test="memberInitFlag != null">
                #{memberInitFlag,jdbcType=SMALLINT},
            </if>
            <if test="msunHealthPoint != null">
                #{msunHealthPoint,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptScore != null">
                #{acceptScore,jdbcType=INTEGER},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectNumber != null">
                #{projectNumber,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null">
                #{projectName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        update csm.proj_project_info
        <set>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectDeliverStatus != null">
                project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT},
            </if>
            <if test="orderInfoId != null">
                order_info_id = #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectLeaderId != null">
                project_leader_id = #{projectLeaderId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamId != null">
                project_team_id = #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="workTime != null">
                work_time = #{workTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="surveyCompleteTime != null">
                survey_complete_time = #{surveyCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settleInTime != null">
                settle_in_time = #{settleInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="preCompleteTime != null">
                pre_complete_time = #{preCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptTime != null">
                accept_time = #{acceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                his_flag = #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="standardDuration != null">
                standard_duration = #{standardDuration,jdbcType=VARCHAR},
            </if>
            <if test="durationReduction != null">
                duration_reduction = #{durationReduction,jdbcType=VARCHAR},
            </if>
            <if test="settleStatus != null">
                settle_status = #{settleStatus,jdbcType=VARCHAR},
            </if>
            <if test="settleProportion != null">
                settle_proportion = #{settleProportion,jdbcType=VARCHAR},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=NUMERIC},
            </if>
            <if test="supervisorFlag != null">
                supervisor_flag = #{supervisorFlag,jdbcType=SMALLINT},
            </if>
            <if test="memberInitFlag != null">
                member_init_flag = #{memberInitFlag,jdbcType=SMALLINT},
            </if>
            <if test="msunHealthPoint != null">
                msun_health_point = #{msunHealthPoint,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptScore != null">
                accept_score = #{acceptScore,jdbcType=INTEGER},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectNumber != null">
                project_number = #{projectNumber,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null">
                project_name = #{projectName,jdbcType=VARCHAR},
            </if>
            <if test="applyAcceptTime != null">
                apply_accept_time = #{applyAcceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="externalAcceptTime != null">
                external_accept_time = #{externalAcceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineStepFlag != null">
                online_step_flag = #{onlineStepFlag},
            </if>
        </set>
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        update csm.proj_project_info
        set custom_info_id = #{customInfoId,jdbcType=BIGINT},
        project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT},
        order_info_id = #{orderInfoId,jdbcType=BIGINT},
        project_leader_id = #{projectLeaderId,jdbcType=BIGINT},
        project_team_id = #{projectTeamId,jdbcType=BIGINT},
        work_time = #{workTime,jdbcType=TIMESTAMP},
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
        survey_complete_time = #{surveyCompleteTime,jdbcType=TIMESTAMP},
        settle_in_time = #{settleInTime,jdbcType=TIMESTAMP},
        pre_complete_time = #{preCompleteTime,jdbcType=TIMESTAMP},
        online_time = #{onlineTime,jdbcType=TIMESTAMP},
        accept_time = #{acceptTime,jdbcType=TIMESTAMP},
        upgradation_type = #{upgradationType,jdbcType=SMALLINT},
        his_flag = #{hisFlag,jdbcType=SMALLINT},
        standard_duration = #{standardDuration,jdbcType=VARCHAR},
        duration_reduction = #{durationReduction,jdbcType=VARCHAR},
        settle_status = #{settleStatus,jdbcType=VARCHAR},
        settle_proportion = #{settleProportion,jdbcType=VARCHAR},
        settle_amount = #{settleAmount,jdbcType=NUMERIC},
        supervisor_flag = #{supervisorFlag,jdbcType=SMALLINT},
        member_init_flag = #{memberInitFlag,jdbcType=SMALLINT},
        msun_health_point = #{msunHealthPoint,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        accept_score = #{acceptScore,jdbcType=INTEGER},
        project_type = #{projectType,jdbcType=SMALLINT},
        project_number = #{projectNumber,jdbcType=VARCHAR},
        project_name = #{projectName,jdbcType=VARCHAR},
        apply_accept_time = #{applyAcceptTime,jdbcType=TIMESTAMP},
        external_accept_time = #{externalAcceptTime,jdbcType=TIMESTAMP},
        online_step_flag = #{onlineStepFlag}
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_deliver_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.projectDeliverStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="order_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.orderInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_leader_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.projectLeaderId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.projectTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="work_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.workTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="receive_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.receiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="survey_complete_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.surveyCompleteTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="settle_in_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.settleInTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="pre_complete_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.preCompleteTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.onlineTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="accept_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.acceptTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.upgradationType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="his_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then #{item.hisFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="standard_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.standardDuration,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="duration_reduction = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.durationReduction,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="settle_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.settleStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="settle_proportion = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.settleProportion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="settle_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.settleAmount,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="supervisor_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.supervisorFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="member_init_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.memberInitFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="msun_health_point = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.msunHealthPoint,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="accept_score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.acceptScore,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.projectType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="project_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.projectNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                    #{item.projectName,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where project_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_project_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_deliver_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectDeliverStatus != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.projectDeliverStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderInfoId != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.orderInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_leader_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectLeaderId != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.projectLeaderId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectTeamId != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.projectTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="work_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.workTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.workTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="receive_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.receiveTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.receiveTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="survey_complete_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.surveyCompleteTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.surveyCompleteTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settle_in_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.settleInTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.settleInTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pre_complete_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.preCompleteTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.preCompleteTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="online_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.onlineTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.onlineTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="accept_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.acceptTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.acceptTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.upgradationType != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.upgradationType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisFlag != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.hisFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="standard_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.standardDuration != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.standardDuration,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="duration_reduction = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.durationReduction != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.durationReduction,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settle_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.settleStatus != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.settleStatus,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settle_proportion = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.settleProportion != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.settleProportion,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="settle_amount = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.settleAmount != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.settleAmount,jdbcType=NUMERIC}
                    </if>
                </foreach>
            </trim>
            <trim prefix="supervisor_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.supervisorFlag != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.supervisorFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="member_init_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.memberInitFlag != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.memberInitFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msun_health_point = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msunHealthPoint != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.msunHealthPoint,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="accept_score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.acceptScore != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.acceptScore,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectType != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.projectType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectNumber != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.projectNumber,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectName != null">
                        when project_info_id = #{item.projectInfoId,jdbcType=BIGINT} then
                        #{item.projectName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateDataById">
        update csm.proj_project_info  set project_leader_id= #{oldId},
                                          project_team_id= #{deptId}
        from (
            select project_info_id from csm.proj_project_info where project_leader_id = #{oldId}
            union all
            select project_info_id from csm.proj_project_info where project_team_id = #{newOldId}

             ) ss
        where  proj_project_info.project_info_id = ss.project_info_id

    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_project_info
        (project_info_id, custom_info_id, project_deliver_status, order_info_id, project_leader_id,
        project_team_id, work_time, receive_time, survey_complete_time, settle_in_time,
        pre_complete_time, online_time, accept_time, upgradation_type, his_flag, standard_duration,
        duration_reduction, settle_status, settle_proportion, settle_amount, supervisor_flag,
        member_init_flag, msun_health_point, is_deleted, creater_id, create_time, updater_id,
        update_time, accept_score, project_type, project_number, project_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectInfoId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.projectDeliverStatus,jdbcType=SMALLINT},
            #{item.orderInfoId,jdbcType=BIGINT}, #{item.projectLeaderId,jdbcType=BIGINT},
            #{item.projectTeamId,jdbcType=BIGINT},
            #{item.workTime,jdbcType=TIMESTAMP}, #{item.receiveTime,jdbcType=TIMESTAMP},
            #{item.surveyCompleteTime,jdbcType=TIMESTAMP},
            #{item.settleInTime,jdbcType=TIMESTAMP}, #{item.preCompleteTime,jdbcType=TIMESTAMP},
            #{item.onlineTime,jdbcType=TIMESTAMP}, #{item.acceptTime,jdbcType=TIMESTAMP},
            #{item.upgradationType,jdbcType=SMALLINT},
            #{item.hisFlag,jdbcType=SMALLINT}, #{item.standardDuration,jdbcType=VARCHAR},
            #{item.durationReduction,jdbcType=VARCHAR},
            #{item.settleStatus,jdbcType=VARCHAR}, #{item.settleProportion,jdbcType=VARCHAR},
            #{item.settleAmount,jdbcType=NUMERIC}, #{item.supervisorFlag,jdbcType=SMALLINT},
            #{item.memberInitFlag,jdbcType=SMALLINT}, #{item.msunHealthPoint,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.acceptScore,jdbcType=INTEGER},
            #{item.projectType,jdbcType=SMALLINT}, #{item.projectNumber,jdbcType=VARCHAR},
            #{item.projectName,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_info
        (project_info_id, custom_info_id, project_deliver_status, order_info_id, project_leader_id,
        project_team_id, work_time, receive_time, survey_complete_time, settle_in_time,
        pre_complete_time, online_time, accept_time, upgradation_type, his_flag, standard_duration,
        duration_reduction, settle_status, settle_proportion, settle_amount, supervisor_flag,
        member_init_flag, msun_health_point, is_deleted, creater_id, create_time, updater_id,
        update_time, accept_score, project_type, project_number, project_name)
        values
        (#{projectInfoId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{projectDeliverStatus,jdbcType=SMALLINT},
        #{orderInfoId,jdbcType=BIGINT}, #{projectLeaderId,jdbcType=BIGINT}, #{projectTeamId,jdbcType=BIGINT},
        #{workTime,jdbcType=TIMESTAMP}, #{receiveTime,jdbcType=TIMESTAMP}, #{surveyCompleteTime,jdbcType=TIMESTAMP},
        #{settleInTime,jdbcType=TIMESTAMP}, #{preCompleteTime,jdbcType=TIMESTAMP}, #{onlineTime,jdbcType=TIMESTAMP},
        #{acceptTime,jdbcType=TIMESTAMP}, #{upgradationType,jdbcType=SMALLINT}, #{hisFlag,jdbcType=SMALLINT},
        #{standardDuration,jdbcType=VARCHAR}, #{durationReduction,jdbcType=VARCHAR}, #{settleStatus,jdbcType=VARCHAR},
        #{settleProportion,jdbcType=VARCHAR}, #{settleAmount,jdbcType=NUMERIC}, #{supervisorFlag,jdbcType=SMALLINT},
        #{memberInitFlag,jdbcType=SMALLINT}, #{msunHealthPoint,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{acceptScore,jdbcType=INTEGER}, #{projectType,jdbcType=SMALLINT},
        #{projectNumber,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR})
        on duplicate key update
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT},
        order_info_id = #{orderInfoId,jdbcType=BIGINT},
        project_leader_id = #{projectLeaderId,jdbcType=BIGINT},
        project_team_id = #{projectTeamId,jdbcType=BIGINT},
        work_time = #{workTime,jdbcType=TIMESTAMP},
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
        survey_complete_time = #{surveyCompleteTime,jdbcType=TIMESTAMP},
        settle_in_time = #{settleInTime,jdbcType=TIMESTAMP},
        pre_complete_time = #{preCompleteTime,jdbcType=TIMESTAMP},
        online_time = #{onlineTime,jdbcType=TIMESTAMP},
        accept_time = #{acceptTime,jdbcType=TIMESTAMP},
        upgradation_type = #{upgradationType,jdbcType=SMALLINT},
        his_flag = #{hisFlag,jdbcType=SMALLINT},
        standard_duration = #{standardDuration,jdbcType=VARCHAR},
        duration_reduction = #{durationReduction,jdbcType=VARCHAR},
        settle_status = #{settleStatus,jdbcType=VARCHAR},
        settle_proportion = #{settleProportion,jdbcType=VARCHAR},
        settle_amount = #{settleAmount,jdbcType=NUMERIC},
        supervisor_flag = #{supervisorFlag,jdbcType=SMALLINT},
        member_init_flag = #{memberInitFlag,jdbcType=SMALLINT},
        msun_health_point = #{msunHealthPoint,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        accept_score = #{acceptScore,jdbcType=INTEGER},
        project_type = #{projectType,jdbcType=SMALLINT},
        project_number = #{projectNumber,jdbcType=VARCHAR},
        project_name = #{projectName,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        <!--@mbg.generated-->
        insert into csm.proj_project_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectDeliverStatus != null">
                project_deliver_status,
            </if>
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="projectLeaderId != null">
                project_leader_id,
            </if>
            <if test="projectTeamId != null">
                project_team_id,
            </if>
            <if test="workTime != null">
                work_time,
            </if>
            <if test="receiveTime != null">
                receive_time,
            </if>
            <if test="surveyCompleteTime != null">
                survey_complete_time,
            </if>
            <if test="settleInTime != null">
                settle_in_time,
            </if>
            <if test="preCompleteTime != null">
                pre_complete_time,
            </if>
            <if test="onlineTime != null">
                online_time,
            </if>
            <if test="acceptTime != null">
                accept_time,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="hisFlag != null">
                his_flag,
            </if>
            <if test="standardDuration != null">
                standard_duration,
            </if>
            <if test="durationReduction != null">
                duration_reduction,
            </if>
            <if test="settleStatus != null">
                settle_status,
            </if>
            <if test="settleProportion != null">
                settle_proportion,
            </if>
            <if test="settleAmount != null">
                settle_amount,
            </if>
            <if test="supervisorFlag != null">
                supervisor_flag,
            </if>
            <if test="memberInitFlag != null">
                member_init_flag,
            </if>
            <if test="msunHealthPoint != null">
                msun_health_point,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="acceptScore != null">
                accept_score,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="projectNumber != null">
                project_number,
            </if>
            <if test="projectName != null">
                project_name,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectDeliverStatus != null">
                #{projectDeliverStatus,jdbcType=SMALLINT},
            </if>
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectLeaderId != null">
                #{projectLeaderId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamId != null">
                #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="workTime != null">
                #{workTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="surveyCompleteTime != null">
                #{surveyCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settleInTime != null">
                #{settleInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="preCompleteTime != null">
                #{preCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptTime != null">
                #{acceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="standardDuration != null">
                #{standardDuration,jdbcType=VARCHAR},
            </if>
            <if test="durationReduction != null">
                #{durationReduction,jdbcType=VARCHAR},
            </if>
            <if test="settleStatus != null">
                #{settleStatus,jdbcType=VARCHAR},
            </if>
            <if test="settleProportion != null">
                #{settleProportion,jdbcType=VARCHAR},
            </if>
            <if test="settleAmount != null">
                #{settleAmount,jdbcType=NUMERIC},
            </if>
            <if test="supervisorFlag != null">
                #{supervisorFlag,jdbcType=SMALLINT},
            </if>
            <if test="memberInitFlag != null">
                #{memberInitFlag,jdbcType=SMALLINT},
            </if>
            <if test="msunHealthPoint != null">
                #{msunHealthPoint,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptScore != null">
                #{acceptScore,jdbcType=INTEGER},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectNumber != null">
                #{projectNumber,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null">
                #{projectName,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectDeliverStatus != null">
                project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT},
            </if>
            <if test="orderInfoId != null">
                order_info_id = #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectLeaderId != null">
                project_leader_id = #{projectLeaderId,jdbcType=BIGINT},
            </if>
            <if test="projectTeamId != null">
                project_team_id = #{projectTeamId,jdbcType=BIGINT},
            </if>
            <if test="workTime != null">
                work_time = #{workTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="surveyCompleteTime != null">
                survey_complete_time = #{surveyCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settleInTime != null">
                settle_in_time = #{settleInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="preCompleteTime != null">
                pre_complete_time = #{preCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="onlineTime != null">
                online_time = #{onlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptTime != null">
                accept_time = #{acceptTime,jdbcType=TIMESTAMP},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                his_flag = #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="standardDuration != null">
                standard_duration = #{standardDuration,jdbcType=VARCHAR},
            </if>
            <if test="durationReduction != null">
                duration_reduction = #{durationReduction,jdbcType=VARCHAR},
            </if>
            <if test="settleStatus != null">
                settle_status = #{settleStatus,jdbcType=VARCHAR},
            </if>
            <if test="settleProportion != null">
                settle_proportion = #{settleProportion,jdbcType=VARCHAR},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=NUMERIC},
            </if>
            <if test="supervisorFlag != null">
                supervisor_flag = #{supervisorFlag,jdbcType=SMALLINT},
            </if>
            <if test="memberInitFlag != null">
                member_init_flag = #{memberInitFlag,jdbcType=SMALLINT},
            </if>
            <if test="msunHealthPoint != null">
                msun_health_point = #{msunHealthPoint,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="acceptScore != null">
                accept_score = #{acceptScore,jdbcType=INTEGER},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="projectNumber != null">
                project_number = #{projectNumber,jdbcType=VARCHAR},
            </if>
            <if test="projectName != null">
                project_name = #{projectName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="findProjectInfo" resultType="com.msun.csm.model.vo.ProjProjectInfoVO">
        select * from
        (
        select DISTINCT p.*,
        leader.user_name as project_leader_name,
        ci.custom_name as custom_info_name,
        sd.dept_name as project_team_name,
        sale.user_name as sale_user,
        ci.custom_team_id,
        coalesce(oldp.project_kanban,'false') as "hasKanban"
        from csm.proj_project_info p
        left join csm.sys_user leader on p.project_leader_id = leader.sys_user_id
        left join csm.proj_custom_info ci on p.custom_info_id = ci.custom_info_id
        left join csm.proj_order_product op on p.project_info_id = op.project_info_id
        left join csm.proj_order_info oi on op.order_info_id = oi.order_info_id
        left join csm.proj_contract_info con on oi.contract_info_id = con.contract_info_id
        left join csm.sys_user sale on con.contract_sign_person_id = cast(sale.user_yunying_id as bigint)
        left join csm.proj_special_product_record sp on p.project_info_id = sp.project_info_id
        left join csm.sys_dept sd on sd.dept_yunying_id = p.project_team_id
        left join csm.proj_project_member mem on p.project_info_id = mem.project_info_id
        left join (
        select vold.new_project_info_id,vold.old_project_info_id,
        case when pt.first_project = 1 and dt."name" is not null and dt."name" != ''
        and to_timestamp(dt."name", 'yyyy-MM-dd HH24:mi:ss') &lt;= pt.create_time then 'true' else 'false'
        end project_kanban
        from csm.tmp_project_new_vs_old vold
        inner join platform.project pt on vold.old_project_info_id = pt.id
        left join platform.sys_dict dt on dt.code = 'backwardCompatibility'
        ) oldp on oldp.new_project_info_id = p.project_info_id
        where p.is_deleted = 0
        <if test="customInfoId != null and customInfoId != ''">
            and p.custom_info_id = #{customInfoId,jdbcType=BIGINT}
        </if>
        <if test="projectNumber != null and projectNumber != ''">
            and p.project_number like CONCAT('%', #{projectNumber,jdbcType=VARCHAR}, '%')
        </if>
        <if test="projectDeliverStatus != null and projectDeliverStatus != 0">
            and p.project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT}
        </if>
        <if test="yyOrderProductId != null and yyOrderProductId != 0">
            and (op.yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT}
            or sp.special_product_id = #{yyOrderProductId,jdbcType=BIGINT})
        </if>
        <if test="projectInfoId != null and projectInfoId != ''">
            and p.project_info_id = #{projectInfoId}
        </if>
        <if test="projectMemberId != null and projectMemberId != ''">
            and (mem.project_member_id = #{projectMemberId}
            <!--校验项目团队-->
            or p.project_team_id =(select dept_id from csm.sys_user su where sys_user_id = #{projectMemberId})
            <!--校验部门经理下属团队-->
            or p.project_team_id in(
            select sd.dept_yunying_id from csm.sys_user su left join csm.sys_dept sd on su.dept_id = sd.pid
            where sys_user_id = #{projectMemberId} )
            or ci.custom_dept_id =(select dept_id from csm.sys_user su where sys_user_id = #{projectMemberId})
            or ci.custom_team_id =(select dept_id from csm.sys_user su where sys_user_id = #{projectMemberId})
            )
        </if>
        <if test="dataRange != null and dataRange.size > 0">
            union
            select DISTINCT p.*,
            leader.user_name as project_leader_name,
            ci.custom_name as custom_info_name,
            sd.dept_name as project_team_name,
            sale.user_name as sale_user,
            ci.custom_team_id,
            coalesce(oldp.project_kanban,'false') as "hasKanban"
            from csm.proj_project_info p
            left join csm.sys_user leader on p.project_leader_id = leader.sys_user_id
            left join csm.proj_custom_info ci on p.custom_info_id = ci.custom_info_id
            left join csm.proj_order_product op on p.project_info_id = op.project_info_id
            left join csm.proj_order_info oi on op.order_info_id = oi.order_info_id
            left join csm.proj_contract_info con on oi.contract_info_id = con.contract_info_id
            left join csm.sys_user sale on con.contract_sign_person_id = cast(sale.user_yunying_id as bigint)
            left join csm.proj_special_product_record sp on p.project_info_id = sp.project_info_id
            left join csm.sys_dept sd on sd.dept_yunying_id = p.project_team_id
            left join csm.proj_project_member mem on p.project_info_id = mem.project_info_id
            left join (
            select vold.new_project_info_id,vold.old_project_info_id,
            case when pt.first_project = 1 and dt."name" is not null and dt."name" != ''
            and to_timestamp(dt."name", 'yyyy-MM-dd HH24:mi:ss') &lt;= pt.create_time then 'true' else 'false'
            end project_kanban
            from csm.tmp_project_new_vs_old vold
            inner join platform.project pt on vold.old_project_info_id = pt.id
            left join platform.sys_dict dt on dt.code = 'backwardCompatibility'
            ) oldp on oldp.new_project_info_id = p.project_info_id
            where p.is_deleted = 0
            <if test="customInfoId != null and customInfoId != ''">
                and p.custom_info_id = #{customInfoId,jdbcType=BIGINT}
            </if>
            <if test="projectNumber != null and projectNumber != ''">
                and p.project_number like CONCAT('%', #{projectNumber,jdbcType=VARCHAR}, '%')
            </if>
            <if test="projectDeliverStatus != null and projectDeliverStatus != 0">
                and p.project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT}
            </if>
            <if test="yyOrderProductId != null and yyOrderProductId != 0">
                and (op.yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT}
                or sp.special_product_id = #{yyOrderProductId,jdbcType=BIGINT})
            </if>
            <if test="projectInfoId != null and projectInfoId != ''">
                and p.project_info_id = #{projectInfoId}
            </if>
            and ci.custom_dept_id in
            <foreach collection="dataRange" item="item" open="(" separator=", " close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        ) as temp
        order by project_number desc
    </select>

    <select id="findMemberProjectInfo" resultType="com.msun.csm.model.vo.ProjProjectInfoVO">
        select DISTINCT on (p.project_info_id) p.project_info_id, p.custom_info_id, p.project_deliver_status,
        p.order_info_id,
        p.project_leader_id, p.project_team_id, p.work_time, p.receive_time, p.survey_complete_time, p.settle_in_time,
        p.pre_complete_time, p.online_time, p.accept_time, p.upgradation_type, p.his_flag, p.standard_duration,
        p.duration_reduction,
        p.settle_status, p.settle_proportion, p.settle_amount, p.supervisor_flag, p.member_init_flag,
        p.msun_health_point,
        p.is_deleted, p.creater_id, p.create_time, p.updater_id, p.update_time, p.accept_score, p.project_type,
        p.project_number,
        p.project_name, p.apply_accept_time, p.external_accept_time, p.online_step_flag, p.project_plan_flag,
        p.yy_apply_accept_time, p.control_time, p.plan_online_time, p.why_unknown_online_time, p.project_source_flag,
        leader.user_name as project_leader_name,
        ci.custom_name as custom_info_name,
        sd.dept_name as project_team_name,
        sale.user_name as sale_user,
        coalesce(oldp.project_kanban,false) as has_kanban
        from csm.proj_project_info p
        left join csm.sys_user leader on p.project_leader_id = leader.sys_user_id
        left join csm.proj_custom_info ci on p.custom_info_id = ci.custom_info_id
        left join csm.proj_order_product op on p.project_info_id = op.project_info_id
        left join csm.proj_order_info oi on op.order_info_id = oi.order_info_id
        left join csm.proj_contract_info con on oi.contract_info_id = con.contract_info_id
        left join csm.sys_user sale on con.contract_sign_person_id = cast(sale.user_yunying_id as bigint)
        left join csm.proj_special_product_record sp on p.project_info_id = sp.project_info_id
        left join csm.sys_dept sd on sd.dept_yunying_id = p.project_team_id
        left join csm.proj_project_member mem on p.project_info_id = mem.project_info_id
        left join (
        select vold.new_project_info_id,vold.old_project_info_id,
        case when pt.first_project = 1 and dt."name" is not null and dt."name" != ''
        and to_timestamp(dt."name", 'yyyy-MM-dd HH24:mi:ss') &lt;= pt.create_time then 'true' else 'false'
        end project_kanban
        from csm.tmp_project_new_vs_old vold
        inner join platform.project pt on vold.old_project_info_id = pt.id
        left join platform.sys_dict dt on dt.code = 'backwardCompatibility'
        ) oldp on oldp.new_project_info_id = p.project_info_id
        where p.is_deleted = 0
        <if test="customInfoId != null and customInfoId != ''">
            and p.custom_info_id = #{customInfoId,jdbcType=BIGINT}
        </if>
        <if test="projectNumber != null and projectNumber != ''">
            and p.project_number like CONCAT('%', #{projectNumber,jdbcType=VARCHAR}, '%')
        </if>
        <if test="projectDeliverStatus != null and projectDeliverStatus != 0">
            and p.project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT}
        </if>
        <if test="yyOrderProductId != null and yyOrderProductId != 0">
            and (op.yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT}
            or sp.special_product_id = #{yyOrderProductId,jdbcType=BIGINT})
        </if>
        <if test="projectInfoId != null and projectInfoId != ''">
            and p.project_info_id = #{projectInfoId}
        </if>
        <if test="projectMemberId != null and projectMemberId != ''">
            and (mem.project_member_id = #{projectMemberId}
            <!--校验项目团队-->
            or p.project_team_id = (select dept_id from csm.sys_user su where sys_user_id = #{projectMemberId})
            <!--校验部门经理下属团队-->
            or p.project_team_id in (
            select sd.dept_yunying_id from csm.sys_user su inner join csm.sys_dept sd on su.dept_id = sd.pid
            where sys_user_id = #{projectMemberId}
            )
            or ci.custom_dept_id = (select dept_id from csm.sys_user su where sys_user_id = #{projectMemberId})
            or ci.custom_team_id = (select dept_id from csm.sys_user su where sys_user_id = #{projectMemberId})
            )
        </if>
    </select>

    <select id="findDataRangeProjectInfo" resultType="com.msun.csm.model.vo.ProjProjectInfoVO">
        select DISTINCT on (p.project_info_id) p.project_info_id, p.custom_info_id, p.project_deliver_status,
        p.order_info_id,
        p.project_leader_id, p.project_team_id, p.work_time, p.receive_time, p.survey_complete_time, p.settle_in_time,
        p.pre_complete_time, p.online_time, p.accept_time, p.upgradation_type, p.his_flag, p.standard_duration,
        p.duration_reduction,
        p.settle_status, p.settle_proportion, p.settle_amount, p.supervisor_flag, p.member_init_flag,
        p.msun_health_point,
        p.is_deleted, p.creater_id, p.create_time, p.updater_id, p.update_time, p.accept_score, p.project_type,
        p.project_number,
        p.project_name, p.apply_accept_time, p.external_accept_time, p.online_step_flag, p.project_plan_flag,
        p.yy_apply_accept_time, p.control_time, p.plan_online_time, p.why_unknown_online_time, p.project_source_flag,
        leader.user_name as project_leader_name,
        ci.custom_name as custom_info_name,
        sd.dept_name as project_team_name,
        sale.user_name as sale_user,
        coalesce(oldp.project_kanban,false) as has_kanban
        from csm.proj_project_info p
        left join csm.sys_user leader on p.project_leader_id = leader.sys_user_id
        left join csm.proj_custom_info ci on p.custom_info_id = ci.custom_info_id
        left join csm.proj_order_product op on p.project_info_id = op.project_info_id
        left join csm.proj_order_info oi on op.order_info_id = oi.order_info_id
        left join csm.proj_contract_info con on oi.contract_info_id = con.contract_info_id
        left join csm.sys_user sale on con.contract_sign_person_id = cast(sale.user_yunying_id as bigint)
        left join csm.proj_special_product_record sp on p.project_info_id = sp.project_info_id
        left join csm.sys_dept sd on sd.dept_yunying_id = p.project_team_id
        left join csm.proj_project_member mem on p.project_info_id = mem.project_info_id
        left join (
        select vold.new_project_info_id,vold.old_project_info_id,
        case when pt.first_project = 1 and dt."name" is not null and dt."name" != ''
        and to_timestamp(dt."name", 'yyyy-MM-dd HH24:mi:ss') &lt;= pt.create_time then 'true' else 'false'
        end project_kanban
        from csm.tmp_project_new_vs_old vold
        inner join platform.project pt on vold.old_project_info_id = pt.id
        left join platform.sys_dict dt on dt.code = 'backwardCompatibility'
        ) oldp on oldp.new_project_info_id = p.project_info_id
        where p.is_deleted = 0
        <if test="customInfoId != null and customInfoId != ''">
            and p.custom_info_id = #{customInfoId,jdbcType=BIGINT}
        </if>
        <if test="projectNumber != null and projectNumber != ''">
            and p.project_number like CONCAT('%', #{projectNumber,jdbcType=VARCHAR}, '%')
        </if>
        <if test="projectDeliverStatus != null and projectDeliverStatus != 0">
            and p.project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT}
        </if>
        <if test="yyOrderProductId != null and yyOrderProductId != 0">
            and (op.yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT}
            or sp.special_product_id = #{yyOrderProductId,jdbcType=BIGINT})
        </if>
        <if test="projectInfoId != null and projectInfoId != ''">
            and p.project_info_id = #{projectInfoId}
        </if>
        <if test="dataRange != null and dataRange.size > 0">
            and ci.custom_dept_id in
            <foreach collection="dataRange" item="item" open="(" separator=", " close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>


    <select id="selectDeptUserCompByProjInfoId" resultType="com.msun.csm.dao.entity.sys.DeptUserComp"
            parameterType="long">
        select t2.pid,t2.dept_leader_yunying_id,
        (select s.account from csm.sys_user s where s.user_yunying_id = t2.dept_leader_yunying_id) as account,
        (select s.sys_user_id from csm.sys_user s where s.user_yunying_id = t2.dept_leader_yunying_id) as sysUserId
        from csm.proj_project_info t
        inner join csm.sys_dept t2 on t2.sys_dept_id = t.project_team_id and t.project_info_id =
        #{projectInfoId, jdbcType=BIGINT}
    </select>
    <select id="getSysUserByProjInfoId" resultType="com.msun.csm.dao.entity.SysUser"
            parameterType="long">
        select t2.account, t2.sys_user_id from csm.proj_project_info t
        inner join csm.sys_user t2
        on t2.sys_user_id = t.project_leader_id
        and t.project_info_id = #{projectInfoId, jdbcType=BIGINT}
    </select>

    <select id="selectByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where is_deleted = 0
        <if test="projectInfoId != null">
            and project_info_id = #{projectInfoId,jdbcType=BIGINT}
        </if>
        <if test="customInfoId != null">
            and custom_info_id = #{customInfoId,jdbcType=BIGINT}
        </if>
        <if test="projectDeliverStatus != null">
            and project_deliver_status = #{projectDeliverStatus,jdbcType=SMALLINT}
        </if>
        <if test="projectLeaderId != null">
            and project_leader_id = #{projectLeaderId,jdbcType=BIGINT}
        </if>
        <if test="projectTeamId != null">
            and project_team_id = #{projectTeamId,jdbcType=BIGINT}
        </if>
        <if test="hisFlag == 1">
            and his_flag = 1
        </if>
        <if test="projectName != null and projectName != ''">
            and project_name like CONCAT('%', #{projectName,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getProjectInfoIdByOrderId" resultType="java.lang.Long">
        select
            ppi.project_info_id
        from
            csm.proj_project_info ppi
                left join csm.proj_order_info poi on
                ppi.order_info_id = poi.order_info_id
        where
            poi.yy_order_id = #{yyOrderId,jdbcType=BIGINT}
    </select>

    <select id="getOnlineFlag" resultMap="BaseResultMap">
        select
           <include refid="Base_Column_List"></include>
        from
            csm.proj_project_info ppi
        where
            ppi.custom_info_id in(
                select
                    custom_info_id
                from
                    csm.proj_project_info ppi
                where
                    project_info_id =#{projectInfoId,jdbcType=BIGINT})
    </select>

    <select id="selectByHospital" resultMap="BaseResultMap">
        select
            pi.*
        from
            csm.proj_project_info pi
                left join
            csm.proj_hospital_vs_project_type hvp
            on pi.project_type = hvp.project_type and pi.custom_info_id = hvp.custom_info_id
        where
            hvp.hospital_info_id = #{hospitalInfoId}
          and pi.is_deleted = 0
          and hvp.is_deleted = 0
    </select>
    <select id="selectProjNewAndOldByNewProjId" resultType="com.msun.csm.model.vo.ProjProjectInfoVO">
        select p.*,
                old.old_project_info_id "oldProjectId",
                old.old_custom_info_id "oldCustomInfoId",
                old.old_custom_id "oldCustomerId"
        from
        csm.proj_project_info p
        left join csm.tmp_project_new_vs_old old on p.project_info_id = old.new_project_info_id
        where p.project_info_id = #{projectInfoId}
        limit 1
    </select>

    <select id="findByCustomAndProjectType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where is_deleted = 0
          and custom_info_id = #{customInfoId,jdbcType=BIGINT}
          and project_type = #{projectType,jdbcType=SMALLINT}
          and project_info_id != #{projectInfoId,jdbcType=BIGINT}
    </select>

    <select id="findByOrderNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where is_deleted = 0
          and project_number = #{orderNumber}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where is_deleted = 0
          and project_info_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryOnlineRiskProjects" resultMap="BaseResultMap">
        select *
        from csm.proj_project_info
        where is_deleted = 0
          and project_name not like '测试%'
          and project_number not like 'TMP%'
          and his_flag = 1
          and (project_deliver_status = 3 or project_deliver_status = 4)
          and settle_in_time is not null
          and settle_in_time &lt; now() - interval '3 day'
        order by settle_in_time asc
    </select>

    <select id="queryAcceptRiskProjects" resultMap="BaseResultMap">
        select *
        from csm.proj_project_info
        where is_deleted = 0
          and project_name not like '%测试%'
          and project_number not like 'TMP%'
          and his_flag = 1
          and project_deliver_status = 5
          and apply_accept_time is null
          and settle_in_time is not null
          and online_time is not null
          and online_time &lt; now() - interval '2 day'
        order by online_time asc
    </select>
    <!-- 查询有限制的客户并且一期项目验收时间超30天的数据 -->
    <select id="queryAcceptanceExceedTimeLimit" resultType="java.lang.Integer">

        select
            case when
                     (
                         select
                             case when CURRENT_DATE >= (min(ccfl.accept_time) + INTERVAL '1 days' * min(ccfl.days_after_check))  then 1 else 0 end  as value
                         from csm.config_custom_limit ccl
                                  inner join
                              (
                                  select phi.hospital_info_id,phi.custom_info_id from
                                      csm.proj_hospital_info phi
                                          inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id=phvpt.hospital_info_id  and phvpt.is_deleted = 0
                                          inner join csm.proj_project_info ppi on phvpt.custom_info_id = ppi.custom_info_id and phvpt.project_type = ppi.project_type  and ppi.is_deleted = 0
                                  where
                                      phi.is_deleted = 0
                                    <if test="projectInfoId != null">
                                        and ppi.project_info_id = #{projectInfoId}
                                    </if>
                                    <if test="hospitalInfoId != null">
                                        and phvpt.hospital_info_id = #{hospitalInfoId}
                                    </if>
                              )
                                  ppi on ccl.custom_info_id = ppi.custom_info_id
                                  inner join csm.config_custom_form_limit ccfl on ccl.custom_info_id = ccfl.custom_info_id  and ppi.hospital_info_id = ccfl.hospital_info_id
                         where ccl.is_deleted  = 0
                           and ccl.switch_flag =0
                           and ccfl.is_deleted = 0
                           <if test="hospitalInfoId != null">
                            and ccfl.hospital_info_id = #{hospitalInfoId}
                           </if>
                           and ccfl.msun_health_module_code  = #{code}
                           and ccfl.switch_flag = 0
                         group by ccfl.custom_info_id

                     )  > 0 then 1 else 0 end
    </select>

    <update id="deleteByOrderInfoId">
        update csm.proj_project_info
        set is_deleted = 1
        where order_info_id = #{orderInfoId}
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_project_info
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="orderInfoIds != null and orderInfoIds.size() != 0">
            and order_info_id in
            <foreach collection="orderInfoIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where is_deleted = 0
          and order_info_id = #{orderInfoId}
    </select>
    <select id="selectDataListByIds" resultType="com.msun.csm.dao.entity.proj.ProjProjectMember">
        select * from csm.proj_project_member
                 where project_member_id = #{oldId}
                  or project_member_id = #{newOldId}
    </select>

    <select id="selectByOrderInfoIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_project_info
        where is_deleted = 0
          and order_info_id in
        <foreach collection="orderInfoIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
        </foreach>
    </select>
    <select id="selectProjectListByHospitalId" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        select distinct
            pi.*
        from
            csm.proj_project_info pi
                left join
            csm.proj_hospital_vs_project_type hvp
            on pi.project_type = hvp.project_type and pi.custom_info_id = hvp.custom_info_id
                inner join csm.proj_hospital_info phi on hvp.hospital_info_id = phi.hospital_info_id  and phi.is_deleted =0
        where
           ( hvp.hospital_info_id = #{hospitalInfoId}  or phi.cloud_hospital_id  = #{hospitalInfoId})
          and pi.is_deleted = 0
          and hvp.is_deleted = 0
          and pi.his_flag = 1
        ORDER BY pi.accept_time
    </select>

    <update id="updateProjectPlanFlag">
        update csm.proj_project_info
        set project_plan_flag = 1
        where project_info_id = #{projectInfoId}
    </update>

    <update id="initProjectPlanFlag">
        update csm.proj_project_info
        set project_plan_flag = 0
        where project_info_id = #{projectInfoId}
    </update>

    <select id="findSurveyNotComplete" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        select
            distinct on (project_info_id)
            ppi.*
        from csm.proj_milestone_info pmi
        inner join csm.proj_project_info ppi on pmi.project_info_id = ppi.project_info_id and ppi.is_deleted = 0 and ppi.project_deliver_status = 1
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        left join csm.report_custom_info rci on ppi.custom_info_id = rci.custom_info_id
        where pmi.milestone_node_code = 'survey_summary'
        and pmi.milestone_status = 0
        and pmi.invalid_flag = 0
        and ppi.is_deleted = 0
        and pci.is_deleted = 0
        and rci.is_deleted != 1
        and ppi.project_source_flag = 1
        and ppi.control_time >= #{startTime}
    </select>

    <select id="getOpenSurveyProductAudit" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        SELECT ppi.*
        FROM csm.config_custom_backend_limit AS ccbl
                 left join csm.config_custom_backend_detail_limit ccbdl on ccbl.project_info_id = ccbdl.project_info_id and ccbdl.is_deleted = 0 and ccbdl.open_type = 10
                 left join csm.proj_project_info ppi on ccbl.project_info_id = ppi.project_info_id and ppi.is_deleted = 0
        where ccbl.is_deleted = 0
          and ccbl.open_flag = 1
          and ccbdl.open_type = 10
          and ccbdl.open_flag = 1
    </select>

    <select id="selectDailyReportProject" resultType="com.msun.csm.dao.entity.proj.DailyReportProjectInfoVO">
        select
        ppi.project_info_id as "projectInfoId",
        ppi.project_number as "projectNumber",
        ppi.custom_info_id as "customInfoId",
        pci.custom_name as "customName",
        ppi.project_deliver_status as "projectDeliverStatus",
        ppi.project_type as "projectType",
        ppi.online_time as "onlineTime",
        ppi.project_leader_id as "projectLeaderId"
        from csm.proj_project_info ppi
        left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        where ppi.is_deleted = 0
        and pci.is_deleted = 0
        and ppi.his_flag = 1
        and ppi.project_leader_id = #{projectLeaderId}
        and ppi.project_deliver_status in
        <foreach collection="deliverStatusList" item="deliverStatus" open="(" separator=", " close=")">
            #{deliverStatus}
        </foreach>
    </select>
    <select id="selectListByLeaderId" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        select * from csm.proj_project_info
        where project_leader_id = #{projectLeaderId}
            and is_deleted = 0
            and project_deliver_status != 6
            and accept_time is null
    </select>
    <select id="selectNotOnlineProjectList" resultType="com.msun.csm.model.resp.project.ProjectNotOnlineResp"
            parameterType="com.msun.csm.model.req.project.ProjectSelectNotOnlineReq">
        select pci.custom_info_id,
               pci.custom_name,
               ppi.project_info_id                                   as "projectInfoId",
               ppi.project_deliver_status                             as "projectStatus",
               CASE
                   WHEN ppi.project_deliver_status = 1 THEN
                       '已派工'
                   WHEN ppi.project_deliver_status = 2 THEN
                       '已调研'
                   WHEN ppi.project_deliver_status = 3 THEN
                       '已入驻'
                   WHEN ppi.project_deliver_status = 4 THEN
                       '准备完成'
                   WHEN ppi.project_deliver_status = 5 THEN
                       '已上线'
                   WHEN ppi.project_deliver_status = 6 THEN
                       '已验收'
                   WHEN ppi.project_deliver_status = 7 THEN
                       '已启动'
                   WHEN ppi.project_deliver_status = 11 THEN
                       '一次验收通过'
                   END                                                as "projectStatusStr",

               to_char(ppi.plan_online_time, 'yyyy-MM-dd HH24:mi:ss') as plan_online_time
        from csm.proj_project_info ppi
                 inner join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        where ppi.is_deleted = 0
          and pci.is_deleted = 0
          and ppi.his_flag = 1
          and ppi.project_source_flag != 2
          and ppi.project_deliver_status &lt; 5
          and ppi.project_source_flag in (1,3)
          and ppi.project_number not like '%TMP%'
        order by plan_online_time NULLS LAST

    </select>
    <select id="selectYearFiscalProjectList" resultType="com.msun.csm.model.resp.project.ProjectFiscalYearResp"
            parameterType="com.msun.csm.model.req.project.ProjectSelectNotOnlineReq">
        select
            coalesce(count(*), 0) as "cloudHealthCount",
            coalesce(sum(case when ppi.project_type =1 and pci.telesales_flag != 1  then 1 else 0 end ),0) AS "monomerCount",
            coalesce(sum(case when ppi.project_type =2 then 1 else 0 end ),0) "regionCount",
            coalesce( sum(case when pci.telesales_flag = 1 then 1 else 0 end),0) AS "electricSalesCount"
        from
            csm.proj_project_info ppi
                inner join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id and pci.is_deleted =0
        where ppi.is_deleted = 0
          and ppi.his_flag=1
          and ppi.project_source_flag != 2
          and ppi.online_time  > #{startTime}
          and ppi.online_time &lt; #{endTime}

    </select>
    <select id="selectThisWeekProjectList" resultType="com.msun.csm.model.resp.project.ProjectThisWeekResp"
            parameterType="com.msun.csm.model.req.project.ProjectSelectNotOnlineReq">

        select * from (select coalesce(count(*), 0)     as "planOnlineCount"
                       from csm.proj_project_info ppi
                       where ppi.is_deleted = 0
                         and ppi.project_source_flag != 2
                         and ppi.his_flag = 1
                         and ppi.plan_online_time > #{startTime}
                         and ppi.plan_online_time &lt; #{endTime}
            ) ee
         left join (
            select
                coalesce(count(*), 0)    AS "alreadyOnlineCount"
            from csm.proj_project_info ppi
            where ppi.is_deleted = 0
              and ppi.project_source_flag != 2
              and ppi.his_flag = 1
              and ppi.online_time > #{startTime}
              and ppi.online_time &lt; #{endTime}
        ) vv on 1 = 1


    </select>
    <select id="selectOnlineProjectList" resultType="com.msun.csm.model.resp.project.ProjectNotOnlineResp"
            parameterType="com.msun.csm.model.req.project.ProjectSelectNotOnlineReq">

        select
            pci.custom_info_id ,
            pci.custom_name ,
            ppi.project_deliver_status as "projectStatus",
            CASE
                WHEN ppi.project_deliver_status = 1 THEN
                    '已派工'
                WHEN ppi.project_deliver_status = 2 THEN
                    '已调研'
                WHEN ppi.project_deliver_status = 3 THEN
                    '已入驻'
                WHEN ppi.project_deliver_status = 4 THEN
                    '准备完成'
                WHEN ppi.project_deliver_status = 5 THEN
                    '已上线'
                WHEN ppi.project_deliver_status = 6 THEN
                    '已验收'
                WHEN ppi.project_deliver_status = 7 THEN
                    '已启动'
                WHEN ppi.project_deliver_status = 11 THEN
                    '一次验收通过'
                END                  as "projectStatusStr",

            to_char(ppi.plan_online_time , 'yyyy-MM-dd HH24:mi:ss') as plan_online_time,
              ppi.online_time,
              ppi.settle_in_time,
              ppi.apply_accept_time "lastSubmitTime"
        from csm.proj_project_info ppi
                 inner join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
        where ppi.is_deleted = 0
          and pci.is_deleted=0
          and ppi.his_flag = 1
          and ppi.project_deliver_status >= 5
          and ppi.project_source_flag != 2
          and ppi.online_time  > #{startTime}
          and ppi.online_time &lt; #{endTime}
          and ppi.project_source_flag in (1,3)
          and ppi.project_number not like '%TMP%'
        order by ppi.plan_online_time NULLS LAST
    </select>
    <select id="queryOpenCustomDataFlowLimit" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select
            coalesce (
                    (
                        select
                            case when custom_info_id is not null then 1 else 0 end
                        from
                            (
                                select
                                    distinct custom_info_id
                                from
                                    csm.config_custom_form_limit x
                                where
                                    msun_health_module_code = 'interfacelimit'
                                  and switch_flag = 0
                                  and custom_info_id = #{customInfoId}
                                union
                                select
                                    distinct
                                    ppi.custom_info_id
                                from
                                    csm.config_custom_backend_detail_limit x
                                        inner join csm.proj_project_info ppi on
                                        x.project_info_id = ppi.project_info_id
                                where
                                    x.is_deleted = 0
                                  and x.open_flag = 1
                                  and x.open_type = 7
                                  and ppi.custom_info_id = #{customInfoId}
                            ) ss
                    ) , 0)

    </select>

    <select id="selectByHospitalIdOne" resultMap="BaseResultMap">
        select
            *
        from
            csm.proj_project_info ppi
         inner join csm.proj_hospital_info phi on
                phi.hos_project_type = ppi.project_type and phi.custom_info_id = ppi.custom_info_id
        where
            ppi.is_deleted = 0
          and ppi.his_flag = 1
          and phi.is_deleted = 0
          and phi.cloud_hospital_id = #{hospitalId}
        LIMIT 1
    </select>
    <select id="selectPaymentInfo" resultType="com.msun.csm.model.vo.ProjProjectInfoVO"
            parameterType="com.msun.csm.model.vo.ProjProjectInfoVO">
        select
        case when psg.pay_signage is not  null and psg.pay_signage = 0 then 0
                when psg.pay_signage is not  null and psg.pay_signage in (1,2,3) then 1
            else -1
        end as "conPreFlag",
        psg.con_pre_desc as "conPreDesc"
        from
        csm.proj_project_info ppi
        left join
        (
        select  pay_signage,
        con_pre_desc
        from (
        select    t5.contract_info_id ,
        t5.pay_signage,
        t5.con_pre_desc,
        t.yy_customer_id as yycusid
        from csm.proj_order_info t
        inner join csm.proj_contract_info t5 on t.contract_info_id = t5.contract_info_id and t5.is_deleted = 0
        where t.is_deleted = 0
        and t.delivery_order_type = 9
        and t5.contract_type =1

        union

        select
        t5.contract_info_id ,
        t5.pay_signage,
        t5.con_pre_desc,
        pop.project_info_id  as yycusid
        from csm.proj_order_product pop
        inner join csm.proj_order_info poi  on pop.order_info_id = poi.order_info_id
        inner join csm.proj_contract_info t5 on poi.contract_info_id = t5.contract_info_id and t5.is_deleted = 0
        where   pop.is_deleted = 0
        and t5.contract_type =1
        ) ss
        inner join (
        select p.project_info_id ,pci.yy_customer_id  from csm.proj_project_info p
        inner join csm.proj_custom_info pci on p.custom_info_id = pci.custom_info_id
        where p.is_deleted = 0
        and pci.is_deleted = 0
        <if test="projectInfoId != null">
            and p.project_info_id = #{projectInfoId}
        </if>
        <if test="projectInfoId == null">
            and p.project_info_id = -1
        </if>
        ) pci on pci.yy_customer_id = yycusid or yycusid = pci.project_info_id
        order by  CASE WHEN pay_signage = 0 THEN -5 ELSE 0 END,pay_signage desc
        limit 1
        ) psg on 1 = 1
        where ppi.project_info_id =  #{projectInfoId}
        and ppi.is_deleted = 0
    </select>
    <select id="selectByProjectLists" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo"
            parameterType="java.lang.Long">
        select ppi.* from
                     csm.proj_project_info ppi
                inner join csm.proj_project_info ppi2 on ppi.custom_info_id = ppi2.custom_info_id and ppi.project_type = ppi2.project_type
        where ppi.is_deleted = 0
          and ppi2.is_deleted = 0
          and ppi2.project_info_id = #{projectInfoId}
    </select>
</mapper>
