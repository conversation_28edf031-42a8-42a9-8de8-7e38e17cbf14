<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductEmpowerAddRecordMapper">
    <update id="updateByCustomInfoId">
        update proj_product_empower_add_record
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
    </update>
</mapper>
