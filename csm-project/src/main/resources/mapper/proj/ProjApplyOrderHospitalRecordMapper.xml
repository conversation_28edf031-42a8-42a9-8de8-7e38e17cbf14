<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjApplyOrderHospitalRecordMapper">
    <update id="inserBatch"
            parameterType="java.util.List">
        insert into csm.proj_apply_order_hospital_record
        (proj_apply_order_hospital_record_id, project_info_id,
        custom_info_id, apply_order_id, hospital_info_id,
        hospital_name, province_id, city_id, town_id,
        dict_hospital_org_id,creater_id,updater_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projApplyOrderHospitalRecordId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.customInfoId,jdbcType=BIGINT},
            #{item.applyOrderId,jdbcType=BIGINT},
            #{item.hospitalInfoId,jdbcType=BIGINT},
            #{item.hospitalName,jdbcType=VARCHAR},
            #{item.provinceId,jdbcType=BIGINT},
            #{item.cityId,jdbcType=BIGINT},
            #{item.townId,jdbcType=BIGINT},
            #{item.dictHospitalOrgId,jdbcType=BIGINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.updaterId,jdbcType=BIGINT})
        </foreach>
    </update>


    <select id="findByApplyOrderId" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecordRelative"
            parameterType="java.lang.Long">
        select *
        from (SELECT
                  P.DICT_PROVINCE_NAME || '/' || C.DICT_CITY_NAME || '/' || W.DICT_TOWN_NAME AS ADMINISTRATIVE_DIVISION,
                  T.HOSPITAL_INFO_ID,
                  D.HOSPITAL_ORG_NAME                                                        as dictHospitalOrgTypeName,
                  H.CLOUD_DOMAIN,
                  H.HOSPITAL_NAME,
                  H.CLOUD_HOSPITAL_ID,
                  H.ORG_ID,
                  H.ORG_ID || '/' || H.CLOUD_HOSPITAL_ID                                     as idMerge,
                  H.HEALTH_BUREAU_FLAG,
                  H.ORGANIZATION_CODE,
                  H.SOCIAL_CRED_CODE,
                  H.PROVINCE_ID
              FROM CSM.PROJ_APPLY_ORDER_HOSPITAL_RECORD T
                       LEFT JOIN CSM.DICT_CITY C
                                 ON C.DICT_CITY_ID = T.CITY_ID
                       LEFT JOIN CSM.DICT_PROVINCE P ON P.DICT_PROVINCE_ID = T.PROVINCE_ID
                       LEFT JOIN CSM.DICT_TOWN W ON W.DICT_TOWN_ID = T.TOWN_ID
                       LEFT JOIN CSM.PROJ_HOSPITAL_INFO H
                                 ON H.HOSPITAL_INFO_ID = T.HOSPITAL_INFO_ID AND H.IS_DELETED = 0
                       LEFT JOIN CSM.DICT_HOSPITAL_ORG D ON D.DICT_HOSPITAL_ORG_ID = H.DICT_HOSPITAL_ORG_ID
              WHERE APPLY_ORDER_ID = #{applyOrderId, jdbcType=BIGINT}
                AND T.IS_DELETED = 0) t3
        order by t3.HEALTH_BUREAU_FLAG desc
    </select>
    <select id="selectApplyNoByHosId" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecord">
        SELECT
            paohr.*
        FROM csm.proj_apply_order_hospital_record paohr
                 inner join csm.proj_apply_order pao on paohr.apply_order_id = pao.id and pao.is_deleted =0 and pao.apply_type in (1,2)
        WHERE
            paohr.is_deleted = 0
          and paohr.hospital_info_id = #{hospitalInfoId}
        order by paohr.create_time desc


    </select>
    <select id="selectRecordTimeLong" resultType="java.lang.Double"
            parameterType="com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO">
        select earlywarn_time from csm.proj_apply_order pao
                                       left join csm.config_time_earlywarning cte on pao.cloud_env = cte.cloud_resource_type
            and COALESCE(pao.is_local_room, 0) = cte.local_room_flag
            and COALESCE(pao.first_deploy_node, 0) = cte.first_deployment_flag
            and cte.earlywarn_type = 'deploy'
            and (case when pao.apply_type = 2 then 3
                      when pao.apply_type = 1 and (COALESCE(pao.first_deploy_node, 0)=1 or COALESCE(pao.is_local_room, 0) = 1) then 1
                      when pao.apply_type = 1 and COALESCE(pao.first_deploy_node, 0)=0 and COALESCE(pao.is_local_room, 0) = 0 then 2
                      else 3 end ) = cte.environmental_type
        where pao.is_deleted = 0
          and pao.apply_num = #{deliverPlatformApplyId}
            limit 1


    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_apply_order_hospital_record
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
