<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjectCommonFileVsRuleMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule">
        <id column="project_common_file_id" jdbcType="BIGINT" property="projectCommonFileId"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="scene_code" jdbcType="VARCHAR" property="sceneCode"/>
        <result column="project_rule_code" jdbcType="VARCHAR" property="projectRuleCode"/>
        <result column="node_code" jdbcType="VARCHAR" property="nodeCode"/>
        <result column="class_code" jdbcType="VARCHAR" property="classCode"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="project_file_ids" jdbcType="VARCHAR" property="projectFileIds"/>
        <result column="use_flag" jdbcType="BOOLEAN" property="useFlag"/>
        <result column="check_result" jdbcType="INTEGER" property="checkResult"/>
        <result column="check_result_text" jdbcType="VARCHAR" property="checkResultText"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
    </resultMap>
    <sql id="Base_Column_List">
        project_common_file_id,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted,
        scene_code,
        project_rule_code,
        node_code,
        class_code,
        item_code,
        project_file_ids,
        use_flag,
        check_result,
        check_result_text,
        project_info_id
    </sql>


    <insert id="addProjectCommonFile" parameterType="com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule">
        insert into csm.project_common_file_vs_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            project_common_file_id,
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="sceneCode != null and sceneCode != ''">
                scene_code,
            </if>
            <if test="projectRuleCode != null and projectRuleCode != ''">
                project_rule_code,
            </if>
            <if test="nodeCode != null and nodeCode != ''">
                node_code,
            </if>
            <if test="classCode != null and classCode != ''">
                class_code,
            </if>
            <if test="itemCode != null and itemCode != ''">
                item_code,
            </if>
            <if test="projectFileIds != null and projectFileIds != ''">
                project_file_ids,
            </if>
            <if test="useFlag != null">
                use_flag,
            </if>
            <if test="checkResult != null">
                check_result,
            </if>
            <if test="checkResultText != null and checkResultText != ''">
                check_result_text,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{projectCommonFileId},
            <if test="createrId != null">
                #{createrId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updaterId != null">
                #{updaterId},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="sceneCode != null and sceneCode != ''">
                #{sceneCode},
            </if>
            <if test="projectRuleCode != null and projectRuleCode != ''">
                #{projectRuleCode},
            </if>
            <if test="nodeCode != null and nodeCode != ''">
                #{nodeCode},
            </if>
            <if test="classCode != null and classCode != ''">
                #{classCode},
            </if>
            <if test="itemCode != null and itemCode != ''">
                #{itemCode},
            </if>
            <if test="projectFileIds != null and projectFileIds != ''">
                #{projectFileIds},
            </if>
            <if test="useFlag != null">
                #{useFlag},
            </if>
            <if test="checkResult != null">
                #{checkResult},
            </if>
            <if test="checkResultText != null and checkResultText != ''">
                #{checkResultText},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId},
            </if>
        </trim>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.project_common_file_vs_rule
        where project_common_file_id = #{projectCommonFileId}
    </select>


    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjectCommonFileVsRule">
        update csm.proj_contract_custom_info
        <set>
            <if test="createrId != null">
                creater_id = #{createrId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="is_deleted != null">
                is_deleted = #{is_deleted},
            </if>
            <if test="sceneCode != null and sceneCode != ''">
                scene_code = #{sceneCode},
            </if>
            <if test="projectRuleCode != null and projectRuleCode != ''">
                project_rule_code = #{projectRuleCode},
            </if>
            <if test="nodeCode != null and nodeCode != ''">
                node_code = #{nodeCode},
            </if>
            <if test="classCode != null and classCode != ''">
                class_code = #{classCode},
            </if>
            <if test="itemCode != null and itemCode != ''">
                item_code = #{itemCode},
            </if>
            <if test="projectFileIds != null and projectFileIds != ''">
                project_file_ids = #{projectFileIds},
            </if>
            <if test="useFlag != null">
                use_flag = #{useFlag},
            </if>
            <if test="checkResult != null">
                check_result = #{checkResult},
            </if>
            <if test="checkResultText != null and checkResultText != ''">
                check_result_text = #{checkResultText},
            </if>
        </set>
        where project_common_file_id = #{projectCommonFileId}
    </update>

    <select id="selectByParam" resultMap="BaseResultMap" parameterType="com.msun.csm.model.param.ProjectCommonFileVsRuleParam">
        select
        <include refid="Base_Column_List"/>
        from csm.project_common_file_vs_rule
        where is_deleted = 0
        and scene_code = #{sceneCode}
        and project_rule_code = #{projectRuleCode}
        and node_code = #{nodeCode}
        and class_code = #{classCode}
        and item_code = #{itemCode}
        and project_info_id = #{projectInfoId}
    </select>

    <update id="updateProjectFileIds">
        update csm.project_common_file_vs_rule
        set project_file_ids = #{projectFileIds},
            update_time      = now()
        where project_common_file_id = #{projectCommonFileId}
    </update>

    <update id="updateUseFlag">
        update csm.project_common_file_vs_rule
        <set>
            update_time = now(),
            <if test="useFlag != null">
                use_flag = #{useFlag},
            </if>
        </set>
        where project_common_file_id = #{projectCommonFileId}
    </update>

    <update id="updateCheckInfoById">
        update csm.project_common_file_vs_rule
        <set>
            update_time = now(),
            check_result = #{checkResult},
            <if test="checkResultText != null and checkResultText != ''">
                check_result_text = #{checkResultText},
            </if>
        </set>
        where project_common_file_id = #{projectCommonFileId}
    </update>
</mapper>
