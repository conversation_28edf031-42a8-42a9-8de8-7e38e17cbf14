<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductEmpowerRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        <!--@Table csm.proj_product_empower_record-->
        <id column="product_empower_record_id" jdbcType="BIGINT" property="productEmpowerRecordId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="yy_order_product_id" jdbcType="BIGINT" property="yyOrderProductId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="msun_health_module" jdbcType="VARCHAR" property="msunHealthModule"/>
        <result column="msun_health_module_code" jdbcType="VARCHAR" property="msunHealthModuleCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_empower_record_id,
        custom_info_id,
        yy_order_product_id,
        project_info_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        msun_health_module,
        msun_health_module_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_empower_record
        where product_empower_record_id = #{productEmpowerRecordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.proj_product_empower_record
        where product_empower_record_id = #{productEmpowerRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_empower_record (product_empower_record_id, custom_info_id,
        yy_order_product_id, project_info_id, is_deleted,
        creater_id, create_time, updater_id,
        update_time, msun_health_module, msun_health_module_code)
        values (#{productEmpowerRecordId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT},
        #{yyOrderProductId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{msunHealthModule,jdbcType=VARCHAR},
        #{msunHealthModuleCode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_empower_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productEmpowerRecordId != null">
                product_empower_record_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="msunHealthModule != null">
                msun_health_module,
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productEmpowerRecordId != null">
                #{productEmpowerRecordId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModule != null">
                #{msunHealthModule,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModuleCode != null">
                #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        update csm.proj_product_empower_record
        <set>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModule != null">
                msun_health_module = #{msunHealthModule,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
        </set>
        where product_empower_record_id = #{productEmpowerRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        update csm.proj_product_empower_record
        set custom_info_id = #{customInfoId,jdbcType=BIGINT},
        yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        msun_health_module = #{msunHealthModule,jdbcType=VARCHAR},
        msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR}
        where product_empower_record_id = #{productEmpowerRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_product_empower_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="msun_health_menu_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT} then
                    #{item.msunHealthMenuId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.yyOrderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="msun_health_module = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.msunHealthModule,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="msun_health_module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                    then #{item.msunHealthModuleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where product_empower_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productEmpowerRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_product_empower_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyOrderProductId != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.yyOrderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msun_health_module = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msunHealthModule != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.msunHealthModule,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msun_health_module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msunHealthModuleCode != null">
                        when product_empower_record_id = #{item.productEmpowerRecordId,jdbcType=BIGINT}
                        then #{item.msunHealthModuleCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_empower_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productEmpowerRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_product_empower_record
        (product_empower_record_id, custom_info_id, yy_order_product_id, project_info_id,
        is_deleted, creater_id, create_time, updater_id, update_time, msun_health_module,
        msun_health_module_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productEmpowerRecordId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.yyOrderProductId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.msunHealthModule,jdbcType=VARCHAR},
            #{item.msunHealthModuleCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_empower_record
        (product_empower_record_id, custom_info_id, yy_order_product_id, project_info_id,
        is_deleted, creater_id, create_time, updater_id, update_time, msun_health_module,
        msun_health_module_code)
        values (#{productEmpowerRecordId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT},
        #{yyOrderProductId,jdbcType=BIGINT},
        #{projectInfoId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{msunHealthModule,jdbcType=VARCHAR}, #{msunHealthModuleCode,jdbcType=VARCHAR})
        on duplicate key update
        product_empower_record_id = #{productEmpowerRecordId,jdbcType=BIGINT}, custom_info_id =
        #{customInfoId,jdbcType=BIGINT}, yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT}, project_info_id =
        #{projectInfoId,jdbcType=BIGINT}, is_deleted = #{isDeleted,jdbcType=SMALLINT}, creater_id =
        #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id =
        #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, msun_health_module =
        #{msunHealthModule,jdbcType=VARCHAR}, msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        <!--@mbg.generated-->
        insert into csm.proj_product_empower_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productEmpowerRecordId != null">
                product_empower_record_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="msunHealthModule != null">
                msun_health_module,
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productEmpowerRecordId != null">
                #{productEmpowerRecordId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModule != null">
                #{msunHealthModule,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModuleCode != null">
                #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productEmpowerRecordId != null">
                product_empower_record_id = #{productEmpowerRecordId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderProductId != null">
                yy_order_product_id = #{yyOrderProductId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModule != null">
                msun_health_module = #{msunHealthModule,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="findEmpowerRecord" resultType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        SELECT T2.MSUN_HEALTH_MODULE AS PRODUCT_NAME,
        T2.MSUN_HEALTH_MODULE,
        T2.MSUN_HEALTH_MODULE_CODE,
        T.PRODUCT_EMPOWER_RECORD_ID,
        T.YY_ORDER_PRODUCT_ID
        FROM CSM.PROJ_PRODUCT_EMPOWER_RECORD T
        INNER JOIN CSM.DICT_PRODUCT_VS_EMPOWER T2 ON T2.ORDER_PRODUCT_ID = T.YY_ORDER_PRODUCT_ID AND T2.IS_DELETED = 0
        AND T.MSUN_HEALTH_MODULE_CODE = T2.MSUN_HEALTH_MODULE_CODE
        where
        T.is_deleted = 0
        AND T.project_info_id = #{projectInfoId, jdbcType=BIGINT}
        AND T.yy_order_product_id in
        <foreach item="item" collection="productIdList" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findBranchEmpowerProduct" resultType="com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord">
        select t2.msun_health_module as product_name,
               t2.msun_health_module,
               t2.msun_health_module_code,
               t.product_empower_record_id,
               t.yy_order_product_id
        from csm.proj_product_empower_record t
                 inner join csm.dict_product_vs_empower t2 on t2.order_product_id = t.yy_order_product_id and
                                                              t2.is_deleted = 0
            and t.msun_health_module_code = t2.msun_health_module_code
        where t.is_deleted = 0
          and t.project_info_id = #{projectInfoId, jdbcType=BIGINT}
          and not exists (select 1
                          from csm.proj_order_product pop
                          where pop.project_info_id = #{projectInfoId, jdbcType=BIGINT}
                            and pop.yy_order_product_id = t.yy_order_product_id)
    </select>

    <update id="updateByProjectId">
        update csm.proj_product_empower_record
        set project_info_id = #{newProjectId,jdbcType=BIGINT}
        where is_deleted = 0
        and project_info_id = #{oldProjectId,jdbcType=BIGINT}
        <if test="splitYYPIdList != null and splitYYPIdList.size() != 0">
            and yy_order_product_id in
            <foreach close=")" collection="splitYYPIdList" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectInfoId">
        update csm.proj_product_empower_record
        set is_deleted=1
        where project_info_id = #{projectInfoId}
    </update>

    <select id="findEmpowerRecordByProjectIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_empower_record
        where is_deleted = 0
        and project_info_id in
        <foreach collection="projectInfoIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findByProductIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_product_empower_record
        where is_deleted = 0
        and proj_product_empower_record.yy_order_product_id in
        <foreach collection="productIdList" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_product_empower_record
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
