<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        <!--@mbg.generated-->
        <!--@Table csm.proj_hospital_vs_project_type-->
        <id column="hospital_vs_project_type_id" jdbcType="BIGINT" property="hospitalVsProjectTypeId"/>
        <result column="project_type" jdbcType="SMALLINT" property="projectType"/>
        <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        hospital_vs_project_type_id, project_type, hospital_info_id, is_deleted, creater_id,
        create_time, updater_id, update_time, custom_info_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_hospital_vs_project_type
        where hospital_vs_project_type_id = #{hospitalVsProjectTypeId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_hospital_vs_project_type
        where hospital_vs_project_type_id = #{hospitalVsProjectTypeId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        <!--@mbg.generated-->
        insert into csm.proj_hospital_vs_project_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalVsProjectTypeId != null">
                hospital_vs_project_type_id,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hospitalVsProjectTypeId != null">
                #{hospitalVsProjectTypeId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        <!--@mbg.generated-->
        update csm.proj_hospital_vs_project_type
        <set>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
        </set>
        where hospital_vs_project_type_id = #{hospitalVsProjectTypeId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        <!--@mbg.generated-->
        update csm.proj_hospital_vs_project_type
        set project_type = #{projectType,jdbcType=SMALLINT},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        custom_info_id = #{customInfoId,jdbcType=BIGINT}
        where hospital_vs_project_type_id = #{hospitalVsProjectTypeId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_hospital_vs_project_type
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.projectType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.hospitalInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                    #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where hospital_vs_project_type_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hospitalVsProjectTypeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_hospital_vs_project_type
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectType != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.projectType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalInfoId != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.hospitalInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when hospital_vs_project_type_id = #{item.hospitalVsProjectTypeId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where hospital_vs_project_type_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hospitalVsProjectTypeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_hospital_vs_project_type
        (hospital_vs_project_type_id, project_type, hospital_info_id, is_deleted, creater_id,
        create_time, updater_id, update_time, custom_info_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hospitalVsProjectTypeId,jdbcType=BIGINT}, #{item.projectType,jdbcType=SMALLINT},
            #{item.hospitalInfoId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.customInfoId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        <!--@mbg.generated-->
        insert into csm.proj_hospital_vs_project_type
        (hospital_vs_project_type_id, project_type, hospital_info_id, is_deleted, creater_id,
        create_time, updater_id, update_time, custom_info_id)
        values
        (#{hospitalVsProjectTypeId,jdbcType=BIGINT}, #{projectType,jdbcType=SMALLINT},
        #{hospitalInfoId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{customInfoId,jdbcType=BIGINT}
        )
        on duplicate key update
        hospital_vs_project_type_id = #{hospitalVsProjectTypeId,jdbcType=BIGINT},
        project_type = #{projectType,jdbcType=SMALLINT},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        <!--@mbg.generated-->
        insert into csm.proj_hospital_vs_project_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalVsProjectTypeId != null">
                hospital_vs_project_type_id,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalVsProjectTypeId != null">
                #{hospitalVsProjectTypeId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="hospitalVsProjectTypeId != null">
                hospital_vs_project_type_id = #{hospitalVsProjectTypeId,jdbcType=BIGINT},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByHospitalInfo" parameterType="com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType">
        update csm.proj_hospital_vs_project_type
        <set>
            <if test="projectType != ''">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
        </set>
        where is_deleted = 0
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            and hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="customInfoId != null and customInfoId != ''">
            and custom_info_id = #{customInfoId}
        </if>
    </update>

    <select id="selectByCustomAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_hospital_vs_project_type
        where is_deleted = 0
        <if test="projectType != null and projectType != -1">
            and project_type = #{projectType}
        </if>
        and custom_info_id = #{customInfoId}
    </select>

    <select id="getHospitalType" resultType="java.lang.Integer">
        select phvpt.project_type
        from csm.proj_hospital_vs_project_type phvpt
                 left join csm.proj_custom_info pci on phvpt.custom_info_id = pci.custom_info_id
        where phvpt.hospital_info_id = #{hospitalInfoId}
          and phvpt.project_type in (1, 2)
          and phvpt.is_deleted = 0
          and pci.custom_info_id is not null
        limit 1
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_hospital_vs_project_type
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
    </update>
</mapper>
