<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSpecialProductRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        <!--@mbg.generated-->
        <!--@Table csm.proj_special_product_record-->
        <id column="special_product_record_id" jdbcType="BIGINT" property="specialProductRecordId"/>
        <result column="special_product_id" jdbcType="BIGINT" property="specialProductId"/>
        <result column="special_info" jdbcType="VARCHAR" property="specialInfo"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="product_excution_status" jdbcType="SMALLINT" property="productExcutionStatus"/>
        <result column="product_open_status" jdbcType="SMALLINT" property="productOpenStatus"/>

        <result column="product_buy_mode" jdbcType="BIGINT" property="productBuyMode" />
        <result column="order_info_id" jdbcType="BIGINT" property="orderInfoId" />
        <result column="product_subscribe_start_time" jdbcType="TIMESTAMP" property="productSubscribeStartTime" />
        <result column="product_subscribe_end_time" jdbcType="TIMESTAMP" property="productSubscribeEndTime" />
        <result column="product_subscribe_status" jdbcType="SMALLINT" property="productSubscribeStatus" />
        <result column="product_subscribe_type" jdbcType="SMALLINT" property="productSubscribeType" />
        <result column="arrange_status" jdbcType="SMALLINT" property="arrangeStatus" />
        <result column="empower_status" jdbcType="SMALLINT" property="empowerStatus" />
        <result column="yy_proj_id" jdbcType="BIGINT" property="yyProjId" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        special_product_record_id, special_product_id, special_info, is_deleted, creater_id,
        create_time, updater_id, update_time, project_info_id, product_excution_status, product_open_status,
        product_buy_mode, order_info_id, product_subscribe_start_time, product_subscribe_end_time,
        product_subscribe_status, product_subscribe_type, arrange_status, empower_status,
        yy_proj_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_special_product_record
        where special_product_record_id = #{specialProductRecordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_special_product_record
        where special_product_record_id = #{specialProductRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        insert into csm.proj_special_product_record (special_product_record_id, special_product_id,
                                                     special_info, is_deleted, creater_id,
                                                     create_time, updater_id, update_time,
                                                     project_info_id, product_excution_status, product_open_status,
                                                     product_buy_mode, order_info_id, product_subscribe_start_time,
                                                     product_subscribe_end_time, product_subscribe_status,
                                                     product_subscribe_type, arrange_status,
                                                     empower_status, yy_proj_id)
        values (#{specialProductRecordId,jdbcType=BIGINT}, #{specialProductId,jdbcType=BIGINT},
                #{specialInfo,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{projectInfoId,jdbcType=BIGINT}, #{productExcutionStatus,jdbcType=SMALLINT}, #{productOpenStatus,jdbcType=SMALLINT},
                #{productBuyMode,jdbcType=BIGINT}, #{orderInfoId,jdbcType=BIGINT}, #{productSubscribeStartTime,jdbcType=TIMESTAMP},
                #{productSubscribeEndTime,jdbcType=TIMESTAMP}, #{productSubscribeStatus,jdbcType=SMALLINT},
                #{productSubscribeType,jdbcType=SMALLINT}, #{arrangeStatus,jdbcType=SMALLINT},
                #{empowerStatus,jdbcType=SMALLINT}, #{yyProjId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        insert into csm.proj_special_product_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specialProductRecordId != null">
                special_product_record_id,
            </if>
            <if test="specialProductId != null">
                special_product_id,
            </if>
            <if test="specialInfo != null">
                special_info,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status,
            </if>
            <if test="productOpenStatus != null">
                product_open_status,
            </if>
            <if test="productBuyMode != null">
                product_buy_mode,
            </if>
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="productSubscribeStartTime != null">
                product_subscribe_start_time,
            </if>
            <if test="productSubscribeEndTime != null">
                product_subscribe_end_time,
            </if>
            <if test="productSubscribeStatus != null">
                product_subscribe_status,
            </if>
            <if test="productSubscribeType != null">
                product_subscribe_type,
            </if>
            <if test="arrangeStatus != null">
                arrange_status,
            </if>
            <if test="empowerStatus != null">
                empower_status,
            </if>
            <if test="yyProjId != null">
                yy_proj_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="specialProductRecordId != null">
                #{specialProductRecordId,jdbcType=BIGINT},
            </if>
            <if test="specialProductId != null">
                #{specialProductId,jdbcType=BIGINT},
            </if>
            <if test="specialInfo != null">
                #{specialInfo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="productExcutionStatus != null">
                #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                #{productOpenStatus,jdbcType=SMALLINT},
            </if>
            <if test="productBuyMode != null">
                #{productBuyMode,jdbcType=BIGINT},
            </if>
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeStartTime != null">
                #{productSubscribeStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeEndTime != null">
                #{productSubscribeEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeStatus != null">
                #{productSubscribeStatus,jdbcType=SMALLINT},
            </if>
            <if test="productSubscribeType != null">
                #{productSubscribeType,jdbcType=SMALLINT},
            </if>
            <if test="arrangeStatus != null">
                #{arrangeStatus,jdbcType=SMALLINT},
            </if>
            <if test="empowerStatus != null">
                #{empowerStatus,jdbcType=SMALLINT},
            </if>
            <if test="yyProjId != null">
                #{yyProjId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        <!--@mbg.generated-->
        update csm.proj_special_product_record
        <set>
            <if test="specialProductId != null">
                special_product_id = #{specialProductId,jdbcType=BIGINT},
            </if>
            <if test="specialInfo != null">
                special_info = #{specialInfo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                product_open_status = #{productOpenStatus,jdbcType=SMALLINT},
            </if>
            <if test="productBuyMode != null">
                product_buy_mode = #{productBuyMode,jdbcType=BIGINT},
            </if>
            <if test="orderInfoId != null">
                order_info_id = #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="productSubscribeStartTime != null">
                product_subscribe_start_time = #{productSubscribeStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeEndTime != null">
                product_subscribe_end_time = #{productSubscribeEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productSubscribeStatus != null">
                product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT},
            </if>
            <if test="productSubscribeType != null">
                product_subscribe_type = #{productSubscribeType,jdbcType=SMALLINT},
            </if>
            <if test="arrangeStatus != null">
                arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
            </if>
            <if test="empowerStatus != null">
                empower_status = #{empowerStatus,jdbcType=SMALLINT},
            </if>
            <if test="yyProjId != null">
                yy_proj_id = #{yyProjId,jdbcType=BIGINT},
            </if>
        </set>
        where special_product_record_id = #{specialProductRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        update csm.proj_special_product_record
        set special_product_id = #{specialProductId,jdbcType=BIGINT},
            special_info = #{specialInfo,jdbcType=VARCHAR},
            is_deleted = #{isDeleted,jdbcType=SMALLINT},
            creater_id = #{createrId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            project_info_id = #{projectInfoId,jdbcType=BIGINT},
            product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT},
            product_open_status = #{productOpenStatus,jdbcType=SMALLINT},
            product_buy_mode = #{productBuyMode,jdbcType=BIGINT},
            order_info_id = #{orderInfoId,jdbcType=BIGINT},
            product_subscribe_start_time = #{productSubscribeStartTime,jdbcType=TIMESTAMP},
            product_subscribe_end_time = #{productSubscribeEndTime,jdbcType=TIMESTAMP},
            product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT},
            product_subscribe_type = #{productSubscribeType,jdbcType=SMALLINT},
            arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
            empower_status = #{empowerStatus,jdbcType=SMALLINT},
            yy_proj_id = #{yyProjId,jdbcType=BIGINT}
        where special_product_record_id = #{specialProductRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_special_product_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="special_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.specialProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="special_info = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.specialInfo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_excution_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productExcutionStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="product_open_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productOpenStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>

            <trim prefix="product_buy_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productBuyMode,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="order_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.orderInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productSubscribeStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productSubscribeEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productSubscribeStatus,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_subscribe_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.productSubscribeType,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="arrange_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.arrangeStatus,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="empower_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.empowerStatus,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_proj_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                    #{item.yyProjId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where special_product_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.specialProductRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_special_product_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="special_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.specialProductId != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.specialProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="special_info = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.specialInfo != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.specialInfo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_excution_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productExcutionStatus != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.productExcutionStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_open_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productOpenStatus != null">
                        when special_product_record_id = #{item.specialProductRecordId,jdbcType=BIGINT} then
                        #{item.productOpenStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where special_product_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.specialProductRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_special_product_record
        (special_product_record_id, special_product_id,
        special_info, is_deleted, creater_id,
        create_time, updater_id, update_time,
        project_info_id, product_excution_status, product_open_status,
        product_buy_mode, order_info_id, product_subscribe_start_time,
        product_subscribe_end_time, product_subscribe_status,
        product_subscribe_type, arrange_status,
        empower_status, yy_proj_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.specialProductRecordId,jdbcType=BIGINT}, #{item.specialProductId,jdbcType=BIGINT},
            #{item.specialInfo,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.projectInfoId,jdbcType=BIGINT}, #{item.productExcutionStatus,jdbcType=SMALLINT},
            #{item.productOpenStatus,jdbcType=SMALLINT},#{item.productBuyMode,jdbcType=BIGINT}, #{item.orderInfoId,jdbcType=BIGINT}, #{item.productSubscribeStartTime,jdbcType=TIMESTAMP},
            #{item.productSubscribeEndTime,jdbcType=TIMESTAMP}, #{item.productSubscribeStatus,jdbcType=SMALLINT},
            #{item.productSubscribeType,jdbcType=SMALLINT}, #{item.arrangeStatus,jdbcType=SMALLINT},
            #{item.empowerStatus,jdbcType=SMALLINT}, #{item.yyProjId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        <!--@mbg.generated-->
        insert into csm.proj_special_product_record
        (special_product_record_id, special_product_id,
        special_info, is_deleted, creater_id,
        create_time, updater_id, update_time,
        project_info_id, product_excution_status, product_open_status,
        product_buy_mode, order_info_id, product_subscribe_start_time,
        product_subscribe_end_time, product_subscribe_status,
        product_subscribe_type, arrange_status,
        empower_status, yy_proj_id)
        values
        (#{specialProductRecordId,jdbcType=BIGINT}, #{specialProductId,jdbcType=BIGINT},
        #{specialInfo,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{projectInfoId,jdbcType=BIGINT}, #{productExcutionStatus,jdbcType=SMALLINT}, #{productOpenStatus,jdbcType=SMALLINT},
        #{productBuyMode,jdbcType=BIGINT}, #{orderInfoId,jdbcType=BIGINT}, #{productSubscribeStartTime,jdbcType=TIMESTAMP},
        #{productSubscribeEndTime,jdbcType=TIMESTAMP}, #{productSubscribeStatus,jdbcType=SMALLINT},
        #{productSubscribeType,jdbcType=SMALLINT}, #{arrangeStatus,jdbcType=SMALLINT},
        #{empowerStatus,jdbcType=SMALLINT}, #{yyProjId,jdbcType=BIGINT}
        )
        on duplicate key update
        special_product_record_id = #{specialProductRecordId,jdbcType=BIGINT},
        special_product_id = #{specialProductId,jdbcType=BIGINT},
        special_info = #{specialInfo,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT},
        product_open_status = #{productOpenStatus,jdbcType=SMALLINT},
        product_buy_mode = #{productBuyMode,jdbcType=BIGINT},
        order_info_id = #{orderInfoId,jdbcType=BIGINT},
        product_subscribe_start_time = #{productSubscribeStartTime,jdbcType=TIMESTAMP},
        product_subscribe_end_time = #{productSubscribeEndTime,jdbcType=TIMESTAMP},
        product_subscribe_status = #{productSubscribeStatus,jdbcType=SMALLINT},
        product_subscribe_type = #{productSubscribeType,jdbcType=SMALLINT},
        arrange_status = #{arrangeStatus,jdbcType=SMALLINT},
        empower_status = #{empowerStatus,jdbcType=SMALLINT},
        yy_proj_id = #{yyProjId,jdbcType=BIGINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjSpecialProductRecord">
        <!--@mbg.generated-->
        insert into csm.proj_special_product_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specialProductRecordId != null">
                special_product_record_id,
            </if>
            <if test="specialProductId != null">
                special_product_id,
            </if>
            <if test="specialInfo != null">
                special_info,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status,
            </if>
            <if test="productOpenStatus != null">
                product_open_status,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="specialProductRecordId != null">
                #{specialProductRecordId,jdbcType=BIGINT},
            </if>
            <if test="specialProductId != null">
                #{specialProductId,jdbcType=BIGINT},
            </if>
            <if test="specialInfo != null">
                #{specialInfo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="productExcutionStatus != null">
                #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                #{productOpenStatus,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="specialProductRecordId != null">
                special_product_record_id = #{specialProductRecordId,jdbcType=BIGINT},
            </if>
            <if test="specialProductId != null">
                special_product_id = #{specialProductId,jdbcType=BIGINT},
            </if>
            <if test="specialInfo != null">
                special_info = #{specialInfo,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="productExcutionStatus != null">
                product_excution_status = #{productExcutionStatus,jdbcType=SMALLINT},
            </if>
            <if test="productOpenStatus != null">
                product_open_status = #{productOpenStatus,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <delete id="deletedSpecialByYyOrderProductIdsAndProjectInfoId">
        delete from csm.proj_special_product_record
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
        and special_product_id in
        <foreach close=")" collection="list" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
</mapper>
