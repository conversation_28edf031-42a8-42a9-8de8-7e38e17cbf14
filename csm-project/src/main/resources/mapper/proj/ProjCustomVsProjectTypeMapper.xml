<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjCustomVsProjectTypeMapper">

    <select id="selectCustomTypeDataList" resultType="com.msun.csm.model.resp.ProjCustomVsProjectTypeResp">
        SELECT
            a.custom_vs_project_type_id,
            case when a.project_type is null then  #{projectType}
            else a.project_type end as project_type,
            pci.custom_info_id,
            pci.custom_name ,
            a.cloud_domain,
            case
                 when a.branch_num is not null and a.branch_num >= 0 then a.branch_num
                 when a.branch_num is null and #{projectType} = 1 then 0
                 else ss.branch_num-1 end as branch_num,
            ss.upgradation_type,
            a.supervisor_flag,
            case when a.deployment_status is null then 0 else a.deployment_status end as deployment_status,
            a.custom_level
        from
        csm.proj_custom_info pci
        left join csm.proj_custom_vs_project_type a on pci.custom_info_id = a.custom_info_id and a.project_type = #{projectType} and a.is_deleted = 0
        left join
        (
            select
                ppi.custom_info_id,
                ppi.project_type,
                ppi.upgradation_type,
                max(pop.yy_order_product_number) as branch_num
            from csm.proj_project_info ppi
            inner join csm.proj_order_info poi on ppi.order_info_id=poi.order_info_id
            inner join csm.proj_order_product pop on pop.project_info_id = ppi.project_info_id and poi.order_info_id = pop.order_info_id
            where ppi.is_deleted = 0
            and pop.is_deleted =0
            and ppi.his_flag = 1

            group by ppi.custom_info_id,
                ppi.project_type,
                     ppi.upgradation_type

        ) ss on pci.custom_info_id = ss.custom_info_id and ss.project_type = #{projectType}

        WHERE
        pci.is_deleted = 0
        and pci.custom_info_id = #{customInfoId}
        limit 1
    </select>
</mapper>
