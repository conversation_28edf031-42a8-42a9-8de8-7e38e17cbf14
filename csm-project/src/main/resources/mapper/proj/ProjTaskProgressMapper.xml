<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjTaskProgressMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjTaskProgress">
        <!--@mbg.generated-->
        <!--@Table csm.proj_task_progress-->
        <id column="proj_task_progress_id" jdbcType="BIGINT" property="projTaskProgressId"/>
        <result column="source_type" jdbcType="SMALLINT" property="sourceType"/>
        <result column="source_type_id" jdbcType="BIGINT" property="sourceTypeId"/>
        <result column="progress_desc" jdbcType="VARCHAR" property="progressDesc"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        proj_task_progress_id, source_type, source_type_id, progress_desc, is_deleted, creater_id,
        create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_task_progress
        where proj_task_progress_id = #{projTaskProgressId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_task_progress
        where proj_task_progress_id = #{projTaskProgressId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjTaskProgress">
        <!--@mbg.generated-->
        insert into csm.proj_task_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projTaskProgressId != null">
                proj_task_progress_id,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="sourceTypeId != null">
                source_type_id,
            </if>
            <if test="progressDesc != null">
                progress_desc,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projTaskProgressId != null">
                #{projTaskProgressId,jdbcType=BIGINT},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=SMALLINT},
            </if>
            <if test="sourceTypeId != null">
                #{sourceTypeId,jdbcType=BIGINT},
            </if>
            <if test="progressDesc != null">
                #{progressDesc,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjTaskProgress">
        <!--@mbg.generated-->
        update csm.proj_task_progress
        <set>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=SMALLINT},
            </if>
            <if test="sourceTypeId != null">
                source_type_id = #{sourceTypeId,jdbcType=BIGINT},
            </if>
            <if test="progressDesc != null">
                progress_desc = #{progressDesc,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where proj_task_progress_id = #{projTaskProgressId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjTaskProgress">
        <!--@mbg.generated-->
        update csm.proj_task_progress
        set source_type = #{sourceType,jdbcType=SMALLINT},
        source_type_id = #{sourceTypeId,jdbcType=BIGINT},
        progress_desc = #{progressDesc,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where proj_task_progress_id = #{projTaskProgressId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_task_progress
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.sourceType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="source_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.sourceTypeId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="progress_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.progressDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where proj_task_progress_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projTaskProgressId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_task_progress
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sourceType != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.sourceType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="source_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sourceTypeId != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.sourceTypeId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="progress_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.progressDesc != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.progressDesc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when proj_task_progress_id = #{item.projTaskProgressId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where proj_task_progress_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projTaskProgressId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_task_progress
        (proj_task_progress_id, source_type, source_type_id, progress_desc, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projTaskProgressId,jdbcType=BIGINT}, #{item.sourceType,jdbcType=SMALLINT},
            #{item.sourceTypeId,jdbcType=BIGINT}, #{item.progressDesc,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjTaskProgress">
        <!--@mbg.generated-->
        insert into csm.proj_task_progress
        (proj_task_progress_id, source_type, source_type_id, progress_desc, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        (#{projTaskProgressId,jdbcType=BIGINT}, #{sourceType,jdbcType=SMALLINT}, #{sourceTypeId,jdbcType=BIGINT},
        #{progressDesc,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
        )
        on duplicate key update
        proj_task_progress_id = #{projTaskProgressId,jdbcType=BIGINT},
        source_type = #{sourceType,jdbcType=SMALLINT},
        source_type_id = #{sourceTypeId,jdbcType=BIGINT},
        progress_desc = #{progressDesc,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjTaskProgress">
        <!--@mbg.generated-->
        insert into csm.proj_task_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projTaskProgressId != null">
                proj_task_progress_id,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="sourceTypeId != null">
                source_type_id,
            </if>
            <if test="progressDesc != null">
                progress_desc,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projTaskProgressId != null">
                #{projTaskProgressId,jdbcType=BIGINT},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=SMALLINT},
            </if>
            <if test="sourceTypeId != null">
                #{sourceTypeId,jdbcType=BIGINT},
            </if>
            <if test="progressDesc != null">
                #{progressDesc,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projTaskProgressId != null">
                proj_task_progress_id = #{projTaskProgressId,jdbcType=BIGINT},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=SMALLINT},
            </if>
            <if test="sourceTypeId != null">
                source_type_id = #{sourceTypeId,jdbcType=BIGINT},
            </if>
            <if test="progressDesc != null">
                progress_desc = #{progressDesc,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
