<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipFilesMapper">

    <select id="findEquipFiles" resultType="com.msun.csm.model.vo.ProjEquipFilesVO">
        select distinct pef.equip_files_id,pef.project_file_id,pef.order_no,
        pef.file_item_code,pef.file_type_code,pef.file_type_name,
        pef.required_flag,ppf.file_name,ppf.file_path,prc.limit_type
        from  csm.rule_project_rule_config prc
        left join csm.proj_equip_files pef on  pef.file_type_code = prc.project_rule_code and pef.is_deleted = 0
        left join csm.proj_project_file ppf on pef.project_file_id = ppf.project_file_id and ppf.is_deleted = 0
        where pef.product_equip_record_id = #{productEquipRecordId}
        and prc.is_deleted = 0
        <if test="isMobile == 1">
            and pef.file_type_name like concat('%','图片','%')
        </if>
        order by pef.order_no asc
    </select>

    <update id="updateByProjectId">
        update csm.proj_equip_files
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
    </update>
</mapper>