<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectDailyReportCommentsMapper">

    <select id="selectReportCommentsByReportId" resultType="com.msun.csm.dao.entity.proj.ProjProjectDailyReportCommentsVO">
        select
            ppdrc.proj_project_daily_report_comments_id as "projProjectDailyReportCommentsId",
            ppdrc.project_daily_report_record_id as "projectDailyReportRecordId",
            ppdrc.report_comments_content as "reportCommentsContent",
            su.user_name as "userName",
            su.account as "userAccount",
            ppdrc.create_time as "createTime"
        from csm.proj_project_daily_report_comments ppdrc
                 left join csm.sys_user su on ppdrc.creater_id = su.sys_user_id
        where ppdrc.is_deleted = 0
          and ppdrc.project_daily_report_record_id = #{reportRecordId}
    </select>



</mapper>
