<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectClassificationScoreMapper">

    <select id="getProjectClassificationScore" resultType="com.msun.csm.dao.entity.proj.ProjProjectClassificationScore">
        select
            ppcs.*
        from
            csm.proj_project_classification_score ppcs
        where
            ppcs.is_delete = 0
          and ppcs.project_info_id = #{projectInfoId}
          and ppcs.classification_code = #{classificationCode}
          and ppcs."source" = #{source}
    </select>

    <update id="updateByCustomInfoId">
        update proj_project_classification_score
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="getProjectClassificationScoreInfo" resultType="com.msun.csm.dao.entity.proj.ProjProjectClassificationScorePO">
        select ppcs.id,
               ppcs.classification_code,
               dpar.classification_name,
               dpar.score_weight,
               dpar.parent_code,
               dpar.first_score,
               dpar.final_score,
               dpar.score_standard,
               ppcs.practical_deduction,
               ppcs.final_score as "score",
               ppcs.total_score,
               ppcs.remark
        from csm.proj_project_classification_score ppcs
                 left join csm.dict_project_accept_rule dpar on ppcs.classification_code = dpar.classification_code and dpar.is_delete = 0
        where ppcs.is_delete = 0
          and ppcs.project_info_id = #{projectInfoId}
          and ppcs."source" = #{source}
    </select>
</mapper>
