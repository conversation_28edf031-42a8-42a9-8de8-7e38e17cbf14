<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipRecordVsEcgMapper">
    <select id="selectEcgEquipData" resultType="com.msun.csm.model.vo.ProjEquipRecordVsEcgVO"
            parameterType="com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO">
        select
        per.* ,
        perve.*,
        phi.hospital_name as hospitalInfoName,
        su.user_name as createrName
        from
        csm.proj_equip_record_vs_ecg perve
        left join csm.proj_equip_record per on perve.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        left join csm.sys_user su on per.creater_id = su.sys_user_id
        where
        perve.is_deleted = 0
        <if test="customInfoId != null">
            and per.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipModelOrFactory != null">
            and (per.equip_model_name ilike concat('%',#{equipModelOrFactory},'%') or per.equip_factory_name ilike
            concat('%',#{equipModelOrFactory},'%'))
        </if>
        <if test="equipStatus != null">
            and per.equip_status = #{equipStatus}
        </if>
        <if test="requiredFlag != null">
            and per.required_flag = #{requiredFlag}
        </if>
        <if test="equipRecordVsEcgId != null">
            and perve.equip_record_vs_ecg_id = #{equipRecordVsEcgId}
        </if>
        order by perve.create_time desc
    </select>

    <select id="selectEcgEquipSendCloudData" parameterType="com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO"
            resultType="com.msun.csm.model.vo.EcgEquipSendCloudDataVO">
        select perve.equip_record_vs_ecg_id as id,
        phi.cloud_hospital_id as hospitalId,
        phi.org_id as orgId,
        'ECG' as productCode,
        dei.model_code as deviceId,
        per.equip_model_name as deviceName,
        per.equip_factory_name as firm,
        per.equip_position
        from csm.proj_equip_record_vs_ecg perve
        left join csm.proj_equip_record per
        on perve.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi
        on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        left join csm.dict_equip_info dei on per.equip_info_id = dei.equip_info_id and dei.is_deleted = 0
        where perve.is_deleted = 0
        <if test="projectInfoId !=null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="equipRecordVsEcgIdList != null and equipRecordVsEcgIdList.size() > 0">
            and perve.equip_record_vs_ecg_id in
            <foreach collection="equipRecordVsEcgIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findToMsunEquipRecord" parameterType="com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO"
            resultType="com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto">
        select rva.equip_record_vs_ecg_id as id,
        phi.cloud_hospital_id as hospitalId,
        phi.org_id as hisOrgId,
        'ECG' as productCode,
        per.equip_type_id as deviceId,
        per.equip_type_name as deviceName,
        per.equip_factory_name as firm,
        per.equip_model_name as model,
        per.comm_mode_key as communication,
        per.equip_position as location,
        per.equip_factory_phone as firmTel,
        per.cloud_equip_id as oldEquipId,
        per.yy_product_id as productId
        from csm.proj_equip_record_vs_ecg rva
        left join csm.proj_equip_record per on rva.equip_record_id = per.equip_record_id
        left join csm.proj_hospital_info phi on per.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        where per.equip_status != 5
        and per.required_flag = 1
        <!--and per.cloud_equip_id is not null-->
        <if test="projectInfoId !=null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="equipRecordVsEcgId != null">
            and rva.equip_record_vs_ecg_id = #{equipRecordVsEcgId}
        </if>
        <if test="equipRecordVsEcgIdList != null and equipRecordVsEcgIdList.size() > 0">
            and rva.equip_record_vs_ecg_id in
            <foreach collection="equipRecordVsEcgIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and rva.is_deleted = 0
        and per.is_deleted = 0
    </select>

    <select id="getNotApplyRecordCount" resultType="java.lang.Integer">
        select count(1)
        from csm.proj_equip_record_vs_ecg rvl
        left join csm.proj_equip_record per on rvl.equip_record_id = per.equip_record_id
        where rvl.is_deleted = 0
        and per.is_deleted = 0
        and per.required_flag = 1
        and per.project_info_id = #{projectInfoId}
        and per.equip_status not in
        <foreach collection="statusList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
