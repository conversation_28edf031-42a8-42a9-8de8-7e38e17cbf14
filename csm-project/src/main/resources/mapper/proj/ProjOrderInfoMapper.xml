<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjOrderInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_order_info-->
        <id column="order_info_id" jdbcType="BIGINT" property="orderInfoId"/>
        <result column="contract_info_id" jdbcType="BIGINT" property="contractInfoId"/>
        <result column="yy_order_id" jdbcType="BIGINT" property="yyOrderId"/>
        <result column="delivery_order_no" jdbcType="VARCHAR" property="deliveryOrderNo"/>
        <result column="delivery_order_type" jdbcType="BIGINT" property="deliveryOrderType"/>
        <result column="delivery_order_cloud_type" jdbcType="VARCHAR" property="deliveryOrderCloudType"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="yy_project_number" jdbcType="BIGINT" property="yyProjectNumber"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        order_info_id, contract_info_id, yy_order_id, delivery_order_no, delivery_order_type,
        delivery_order_cloud_type, is_deleted, creater_id, create_time, updater_id, update_time,yy_project_number
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_order_info
        where order_info_id = #{orderInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        update csm.proj_order_info
        set is_deleted = 1
        where order_info_id = #{orderInfoId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjOrderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="contractInfoId != null">
                contract_info_id,
            </if>
            <if test="yyOrderId != null">
                yy_order_id,
            </if>
            <if test="deliveryOrderNo != null">
                delivery_order_no,
            </if>
            <if test="deliveryOrderType != null">
                delivery_order_type,
            </if>
            <if test="deliveryOrderCloudType != null">
                delivery_order_cloud_type,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="contractInfoId != null">
                #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderId != null">
                #{yyOrderId,jdbcType=BIGINT},
            </if>
            <if test="deliveryOrderNo != null">
                #{deliveryOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryOrderType != null">
                #{deliveryOrderType,jdbcType=SMALLINT},
            </if>
            <if test="deliveryOrderCloudType != null">
                #{deliveryOrderCloudType,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjOrderInfo">
        <!--@mbg.generated-->
        update csm.proj_order_info
        <set>
            <if test="contractInfoId != null">
                contract_info_id = #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderId != null">
                yy_order_id = #{yyOrderId,jdbcType=BIGINT},
            </if>
            <if test="deliveryOrderNo != null">
                delivery_order_no = #{deliveryOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryOrderType != null">
                delivery_order_type = #{deliveryOrderType,jdbcType=SMALLINT},
            </if>
            <if test="deliveryOrderCloudType != null">
                delivery_order_cloud_type = #{deliveryOrderCloudType,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_info_id = #{orderInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjOrderInfo">
        <!--@mbg.generated-->
        update csm.proj_order_info
        set contract_info_id = #{contractInfoId,jdbcType=BIGINT},
        yy_order_id = #{yyOrderId,jdbcType=BIGINT},
        delivery_order_no = #{deliveryOrderNo,jdbcType=VARCHAR},
        delivery_order_type = #{deliveryOrderType,jdbcType=SMALLINT},
        delivery_order_cloud_type = #{deliveryOrderCloudType,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where order_info_id = #{orderInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_order_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="contract_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.contractInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.yyOrderId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="delivery_order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                    #{item.deliveryOrderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_order_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                    #{item.deliveryOrderType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="delivery_order_cloud_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                    #{item.deliveryOrderCloudType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where order_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_order_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="contract_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractInfoId != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.contractInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyOrderId != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.yyOrderId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="delivery_order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deliveryOrderNo != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.deliveryOrderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="delivery_order_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deliveryOrderType != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.deliveryOrderType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="delivery_order_cloud_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deliveryOrderCloudType != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.deliveryOrderCloudType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when order_info_id = #{item.orderInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where order_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.orderInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_order_info
        (order_info_id, contract_info_id, yy_order_id, delivery_order_no, delivery_order_type,
        delivery_order_cloud_type, is_deleted, creater_id, create_time, updater_id, update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderInfoId,jdbcType=BIGINT}, #{item.contractInfoId,jdbcType=BIGINT},
            #{item.yyOrderId,jdbcType=BIGINT},
            #{item.deliveryOrderNo,jdbcType=VARCHAR}, #{item.deliveryOrderType,jdbcType=SMALLINT},
            #{item.deliveryOrderCloudType,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjOrderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_order_info
        (order_info_id, contract_info_id, yy_order_id, delivery_order_no, delivery_order_type,
        delivery_order_cloud_type, is_deleted, creater_id, create_time, updater_id, update_time
        )
        values
        (#{orderInfoId,jdbcType=BIGINT}, #{contractInfoId,jdbcType=BIGINT}, #{yyOrderId,jdbcType=BIGINT},
        #{deliveryOrderNo,jdbcType=VARCHAR}, #{deliveryOrderType,jdbcType=SMALLINT},
        #{deliveryOrderCloudType,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        order_info_id = #{orderInfoId,jdbcType=BIGINT},
        contract_info_id = #{contractInfoId,jdbcType=BIGINT},
        yy_order_id = #{yyOrderId,jdbcType=BIGINT},
        delivery_order_no = #{deliveryOrderNo,jdbcType=VARCHAR},
        delivery_order_type = #{deliveryOrderType,jdbcType=SMALLINT},
        delivery_order_cloud_type = #{deliveryOrderCloudType,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjOrderInfo">
        <!--@mbg.generated-->
        insert into csm.proj_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderInfoId != null">
                order_info_id,
            </if>
            <if test="contractInfoId != null">
                contract_info_id,
            </if>
            <if test="yyOrderId != null">
                yy_order_id,
            </if>
            <if test="deliveryOrderNo != null">
                delivery_order_no,
            </if>
            <if test="deliveryOrderType != null">
                delivery_order_type,
            </if>
            <if test="deliveryOrderCloudType != null">
                delivery_order_cloud_type,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderInfoId != null">
                #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="contractInfoId != null">
                #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderId != null">
                #{yyOrderId,jdbcType=BIGINT},
            </if>
            <if test="deliveryOrderNo != null">
                #{deliveryOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryOrderType != null">
                #{deliveryOrderType,jdbcType=SMALLINT},
            </if>
            <if test="deliveryOrderCloudType != null">
                #{deliveryOrderCloudType,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="orderInfoId != null">
                order_info_id = #{orderInfoId,jdbcType=BIGINT},
            </if>
            <if test="contractInfoId != null">
                contract_info_id = #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyOrderId != null">
                yy_order_id = #{yyOrderId,jdbcType=BIGINT},
            </if>
            <if test="deliveryOrderNo != null">
                delivery_order_no = #{deliveryOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="deliveryOrderType != null">
                delivery_order_type = #{deliveryOrderType,jdbcType=SMALLINT},
            </if>
            <if test="deliveryOrderCloudType != null">
                delivery_order_cloud_type = #{deliveryOrderCloudType,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectByYyOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_order_info
        where yy_order_id = #{yyOrderId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>

    <select id="selectByCustomInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_order_info
        where contract_info_id = #{contractInfoId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>

    <select id="findProjOrderInfoByCustomInfoId" resultMap="BaseResultMap">
        select poi.order_info_id,
               ppi.project_name || '-' || poi.delivery_order_no
                   || '(' || case ppi.his_flag
                                 when 1 then '首期'
                                 else '非首期' end || ')' as delivery_order_no
        from csm.proj_project_info ppi
                 inner join csm.proj_order_info poi on ppi.order_info_id = poi.order_info_id
        where ppi.custom_info_id = #{customInfoId,jdbcType=BIGINT}
          and ppi.is_deleted = 0
          and poi.is_deleted = 0
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_order_info
        set yy_customer_id = #{newYYCustomId,jdbcType=BIGINT}
        where yy_customer_id = #{oldYYCustomId,jdbcType=BIGINT}
        <if test="orderInfoIds != null and orderInfoIds.size() != 0 != null and orderInfoIds.size() != 0">
            and proj_order_info.order_info_id in
            <foreach collection="orderInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="getOrderInfoByOrderInfoIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_order_info
        where is_deleted = 0
        and order_info_id in
        <foreach collection="orderInfoIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectOrderListByContractIds" resultType="com.msun.csm.dao.entity.proj.ProjOrderInfo"
            parameterType="com.msun.csm.feign.entity.yunying.req.YunyingDisasterCloudPaysignageDTO">
        select
            *
        from
        csm.proj_order_info
        where
        is_deleted = 0
        and contract_info_id in (
        select
         contract_info_id
        from
        csm.proj_contract_info
        where
        is_deleted = 0
        and yy_contract_id::varchar in (
        <foreach collection="conOgIdList" index="index" item="item" separator=",">
            #{item}
        </foreach>)
        )
    </select>
</mapper>
