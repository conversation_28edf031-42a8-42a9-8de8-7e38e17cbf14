<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSwitchPlanMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        <!--@Table csm.proj_switch_plan-->
        <id column="switch_plan_id" jdbcType="BIGINT" property="switchPlanId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="switch_type" jdbcType="SMALLINT" property="switchType"/>
        <result column="upgradation_type" jdbcType="SMALLINT" property="upgradationType"/>
        <result column="project_type" jdbcType="SMALLINT" property="projectType"/>
        <result column="old_sys_stop_time" jdbcType="TIMESTAMP" property="oldSysStopTime"/>
        <result column="new_sys_start_time" jdbcType="TIMESTAMP" property="newSysStartTime"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="project_file_id" jdbcType="BIGINT" property="projectFileId"/>
        <result column="download_status" jdbcType="SMALLINT" property="downloadStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        switch_plan_id, project_info_id, switch_type, upgradation_type, project_type, old_sys_stop_time,
        new_sys_start_time, creater_id, create_time, updater_id, update_time, is_deleted,
        project_file_id, download_status
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_switch_plan
        where switch_plan_id = #{switchPlanId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        update csm.proj_switch_plan
        set is_deleted = 1
        where switch_plan_id = #{switchPlanId,jdbcType=BIGINT}
    </update>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.proj_switch_plan (switch_plan_id, project_info_id, switch_type,
        upgradation_type, project_type, old_sys_stop_time,
        new_sys_start_time, creater_id, create_time,
        updater_id, update_time, is_deleted,
        project_file_id, download_status)
        values (#{switchPlanId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{switchType,jdbcType=SMALLINT},
        #{upgradationType,jdbcType=SMALLINT}, #{projectType,jdbcType=SMALLINT}, #{oldSysStopTime,jdbcType=TIMESTAMP},
        #{newSysStartTime,jdbcType=TIMESTAMP}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},
        #{projectFileId,jdbcType=BIGINT}, #{downloadStatus,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.proj_switch_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="switchPlanId != null">
                switch_plan_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="switchType != null">
                switch_type,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="oldSysStopTime != null">
                old_sys_stop_time,
            </if>
            <if test="newSysStartTime != null">
                new_sys_start_time,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="projectFileId != null">
                project_file_id,
            </if>
            <if test="downloadStatus != null">
                download_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="switchPlanId != null">
                #{switchPlanId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="switchType != null">
                #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="oldSysStopTime != null">
                #{oldSysStopTime,jdbcType=TIMESTAMP},
            </if>
            <if test="newSysStartTime != null">
                #{newSysStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="projectFileId != null">
                #{projectFileId,jdbcType=BIGINT},
            </if>
            <if test="downloadStatus != null">
                #{downloadStatus,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        update csm.proj_switch_plan
        <set>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="switchType != null">
                switch_type = #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="oldSysStopTime != null">
                old_sys_stop_time = #{oldSysStopTime,jdbcType=TIMESTAMP},
            </if>
            <if test="newSysStartTime != null">
                new_sys_start_time = #{newSysStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="projectFileId != null">
                project_file_id = #{projectFileId,jdbcType=BIGINT},
            </if>
            <if test="downloadStatus != null">
                download_status = #{downloadStatus,jdbcType=SMALLINT},
            </if>
        </set>
        where switch_plan_id = #{switchPlanId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        update csm.proj_switch_plan
        set project_info_id = #{projectInfoId,jdbcType=BIGINT},
        switch_type = #{switchType,jdbcType=SMALLINT},
        upgradation_type = #{upgradationType,jdbcType=SMALLINT},
        project_type = #{projectType,jdbcType=SMALLINT},
        old_sys_stop_time = #{oldSysStopTime,jdbcType=TIMESTAMP},
        new_sys_start_time = #{newSysStartTime,jdbcType=TIMESTAMP},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        project_file_id = #{projectFileId,jdbcType=BIGINT},
        download_status = #{downloadStatus,jdbcType=SMALLINT}
        where switch_plan_id = #{switchPlanId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_switch_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="switch_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then #{item.switchType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.upgradationType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.projectType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="old_sys_stop_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.oldSysStopTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="new_sys_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.newSysStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="project_file_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.projectFileId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="download_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                    #{item.downloadStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where switch_plan_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.switchPlanId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_switch_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="switch_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.switchType != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.switchType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.upgradationType != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.upgradationType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectType != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.projectType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="old_sys_stop_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.oldSysStopTime != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.oldSysStopTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="new_sys_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.newSysStartTime != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.newSysStartTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_file_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectFileId != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.projectFileId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="download_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.downloadStatus != null">
                        when switch_plan_id = #{item.switchPlanId,jdbcType=BIGINT} then
                        #{item.downloadStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where switch_plan_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.switchPlanId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_switch_plan
        (switch_plan_id, project_info_id, switch_type, upgradation_type, project_type, old_sys_stop_time,
        new_sys_start_time, creater_id, create_time, updater_id, update_time, is_deleted,
        project_file_id, download_status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.switchPlanId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
            #{item.switchType,jdbcType=SMALLINT},
            #{item.upgradationType,jdbcType=SMALLINT}, #{item.projectType,jdbcType=SMALLINT},
            #{item.oldSysStopTime,jdbcType=TIMESTAMP}, #{item.newSysStartTime,jdbcType=TIMESTAMP},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.projectFileId,jdbcType=BIGINT},
            #{item.downloadStatus,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.proj_switch_plan
        (switch_plan_id, project_info_id, switch_type, upgradation_type, project_type, old_sys_stop_time,
        new_sys_start_time, creater_id, create_time, updater_id, update_time, is_deleted,
        project_file_id, download_status)
        values
        (#{switchPlanId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{switchType,jdbcType=SMALLINT},
        #{upgradationType,jdbcType=SMALLINT}, #{projectType,jdbcType=SMALLINT}, #{oldSysStopTime,jdbcType=TIMESTAMP},
        #{newSysStartTime,jdbcType=TIMESTAMP}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},
        #{projectFileId,jdbcType=BIGINT}, #{downloadStatus,jdbcType=SMALLINT})
        on duplicate key update
        switch_plan_id = #{switchPlanId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        switch_type = #{switchType,jdbcType=SMALLINT},
        upgradation_type = #{upgradationType,jdbcType=SMALLINT},
        project_type = #{projectType,jdbcType=SMALLINT},
        old_sys_stop_time = #{oldSysStopTime,jdbcType=TIMESTAMP},
        new_sys_start_time = #{newSysStartTime,jdbcType=TIMESTAMP},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        project_file_id = #{projectFileId,jdbcType=BIGINT},
        download_status = #{downloadStatus,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.proj_switch_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="switchPlanId != null">
                switch_plan_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="switchType != null">
                switch_type,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="projectType != null">
                project_type,
            </if>
            <if test="oldSysStopTime != null">
                old_sys_stop_time,
            </if>
            <if test="newSysStartTime != null">
                new_sys_start_time,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="projectFileId != null">
                project_file_id,
            </if>
            <if test="downloadStatus != null">
                download_status,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="switchPlanId != null">
                #{switchPlanId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="switchType != null">
                #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="projectType != null">
                #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="oldSysStopTime != null">
                #{oldSysStopTime,jdbcType=TIMESTAMP},
            </if>
            <if test="newSysStartTime != null">
                #{newSysStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="projectFileId != null">
                #{projectFileId,jdbcType=BIGINT},
            </if>
            <if test="downloadStatus != null">
                #{downloadStatus,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="switchPlanId != null">
                switch_plan_id = #{switchPlanId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="switchType != null">
                switch_type = #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="projectType != null">
                project_type = #{projectType,jdbcType=SMALLINT},
            </if>
            <if test="oldSysStopTime != null">
                old_sys_stop_time = #{oldSysStopTime,jdbcType=TIMESTAMP},
            </if>
            <if test="newSysStartTime != null">
                new_sys_start_time = #{newSysStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="projectFileId != null">
                project_file_id = #{projectFileId,jdbcType=BIGINT},
            </if>
            <if test="downloadStatus != null">
                download_status = #{downloadStatus,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByProjectInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_switch_plan
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and is_deleted = 0
    </select>
</mapper>
