<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjInterfaceGroupApplyDetailMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail">
        <!--@mbg.generated-->
        <!--@Table csm.proj_interface_group_apply_detail-->
        <id column="interface_group_apply_detail_id" jdbcType="BIGINT" property="interfaceGroupApplyDetailId"/>
        <result column="pf_interface_group_id" jdbcType="BIGINT" property="pfInterfaceGroupId"/>
        <result column="pf_interface_group_name" jdbcType="VARCHAR" property="pfInterfaceGroupName"/>
        <result column="pf_interface_id" jdbcType="BIGINT" property="pfInterfaceId"/>
        <result column="pf_interface_name" jdbcType="VARCHAR" property="pfInterfaceName"/>
        <result column="third_interface_id" jdbcType="BIGINT" property="thirdInterfaceId"/>
        <result column="environment" jdbcType="SMALLINT" property="environment"/>
        <result column="auth_flag" jdbcType="SMALLINT" property="authFlag"/>
        <result column="auth_time" jdbcType="TIMESTAMP" property="authTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="pf_interface_group_note" jdbcType="VARCHAR" property="pfInterfaceGroupNote"/>
        <result column="pf_interface_file_url" jdbcType="VARCHAR" property="pfInterfaceFileUrl"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        interface_group_apply_detail_id,
        pf_interface_group_id,
        pf_interface_group_name,
        pf_interface_id,
        pf_interface_name,
        third_interface_id,
        environment,
        auth_flag,
        auth_time,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        pf_interface_group_note,
        pf_interface_file_url
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_interface_group_apply_detail
        where interface_group_apply_detail_id = #{interfaceGroupApplyDetailId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.proj_interface_group_apply_detail
        where interface_group_apply_detail_id = #{interfaceGroupApplyDetailId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail">
        <!--@mbg.generated-->
        insert into csm.proj_interface_group_apply_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceGroupApplyDetailId != null">
                interface_group_apply_detail_id,
            </if>
            <if test="pfInterfaceGroupId != null">
                pf_interface_group_id,
            </if>
            <if test="pfInterfaceGroupName != null">
                pf_interface_group_name,
            </if>
            <if test="pfInterfaceId != null">
                pf_interface_id,
            </if>
            <if test="pfInterfaceName != null">
                pf_interface_name,
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id,
            </if>
            <if test="environment != null">
                environment,
            </if>
            <if test="authFlag != null">
                auth_flag,
            </if>
            <if test="authTime != null">
                auth_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="pfInterfaceGroupNote != null">
                pf_interface_group_note,
            </if>
            <if test="pfInterfaceFileUrl != null">
                pf_interface_file_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="interfaceGroupApplyDetailId != null">
                #{interfaceGroupApplyDetailId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupId != null">
                #{pfInterfaceGroupId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupName != null">
                #{pfInterfaceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceId != null">
                #{pfInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceName != null">
                #{pfInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceId != null">
                #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="environment != null">
                #{environment,jdbcType=SMALLINT},
            </if>
            <if test="authFlag != null">
                #{authFlag,jdbcType=SMALLINT},
            </if>
            <if test="authTime != null">
                #{authTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pfInterfaceGroupNote != null">
                #{pfInterfaceGroupNote,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceFileUrl != null">
                #{pfInterfaceFileUrl,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail">
        <!--@mbg.generated-->
        update csm.proj_interface_group_apply_detail
        <set>
            <if test="pfInterfaceGroupId != null">
                pf_interface_group_id = #{pfInterfaceGroupId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupName != null">
                pf_interface_group_name = #{pfInterfaceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceId != null">
                pf_interface_id = #{pfInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceName != null">
                pf_interface_name = #{pfInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="environment != null">
                environment = #{environment,jdbcType=SMALLINT},
            </if>
            <if test="authFlag != null">
                auth_flag = #{authFlag,jdbcType=SMALLINT},
            </if>
            <if test="authTime != null">
                auth_time = #{authTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pfInterfaceGroupNote != null">
                pf_interface_group_note = #{pfInterfaceGroupNote,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceFileUrl != null">
                pf_interface_file_url = #{pfInterfaceFileUrl,jdbcType=VARCHAR},
            </if>
        </set>
        where interface_group_apply_detail_id = #{interfaceGroupApplyDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail">
        <!--@mbg.generated-->
        update csm.proj_interface_group_apply_detail
        set pf_interface_group_id   = #{pfInterfaceGroupId,jdbcType=BIGINT},
            pf_interface_group_name = #{pfInterfaceGroupName,jdbcType=VARCHAR},
            pf_interface_id         = #{pfInterfaceId,jdbcType=BIGINT},
            pf_interface_name       = #{pfInterfaceName,jdbcType=VARCHAR},
            third_interface_id      = #{thirdInterfaceId,jdbcType=BIGINT},
            environment             = #{environment,jdbcType=SMALLINT},
            auth_flag               = #{authFlag,jdbcType=SMALLINT},
            auth_time               = #{authTime,jdbcType=TIMESTAMP},
            is_deleted              = #{isDeleted,jdbcType=SMALLINT},
            creater_id              = #{createrId,jdbcType=BIGINT},
            create_time             = #{createTime,jdbcType=TIMESTAMP},
            updater_id              = #{updaterId,jdbcType=BIGINT},
            update_time             = #{updateTime,jdbcType=TIMESTAMP},
            pf_interface_group_note = #{pfInterfaceGroupNote,jdbcType=VARCHAR},
            pf_interface_file_url   = #{pfInterfaceFileUrl,jdbcType=VARCHAR}
        where interface_group_apply_detail_id = #{interfaceGroupApplyDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_interface_group_apply_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="pf_interface_group_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.pfInterfaceGroupId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="pf_interface_group_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.pfInterfaceGroupName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pf_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.pfInterfaceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="pf_interface_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.pfInterfaceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="third_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.thirdInterfaceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="environment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.environment,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="auth_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.authFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="auth_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.authTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="pf_interface_group_note = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.pfInterfaceGroupNote,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pf_interface_file_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                        then #{item.pfInterfaceFileUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where interface_group_apply_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_interface_group_apply_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="pf_interface_group_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfInterfaceGroupId != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.pfInterfaceGroupId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_interface_group_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfInterfaceGroupName != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.pfInterfaceGroupName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfInterfaceId != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.pfInterfaceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_interface_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfInterfaceName != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.pfInterfaceName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="third_interface_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.thirdInterfaceId != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.thirdInterfaceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="environment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.environment != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.environment,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="auth_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.authFlag != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.authFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="auth_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.authTime != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.authTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_interface_group_note = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfInterfaceGroupNote != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.pfInterfaceGroupNote,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pf_interface_file_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pfInterfaceFileUrl != null">
                        when interface_group_apply_detail_id = #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
                            then #{item.pfInterfaceFileUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where interface_group_apply_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_interface_group_apply_detail
        (interface_group_apply_detail_id, pf_interface_group_id, pf_interface_group_name,
         pf_interface_id, pf_interface_name, third_interface_id, environment, auth_flag,
         auth_time, is_deleted, creater_id, create_time, updater_id, update_time, pf_interface_group_note,
         pf_interface_file_url)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.interfaceGroupApplyDetailId,jdbcType=BIGINT}, #{item.pfInterfaceGroupId,jdbcType=BIGINT},
             #{item.pfInterfaceGroupName,jdbcType=VARCHAR}, #{item.pfInterfaceId,jdbcType=BIGINT},
             #{item.pfInterfaceName,jdbcType=VARCHAR}, #{item.thirdInterfaceId,jdbcType=BIGINT},
             #{item.environment,jdbcType=SMALLINT}, #{item.authFlag,jdbcType=SMALLINT},
             #{item.authTime,jdbcType=TIMESTAMP},
             #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.pfInterfaceGroupNote,jdbcType=VARCHAR},
             #{item.pfInterfaceFileUrl,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail">
        <!--@mbg.generated-->
        insert into csm.proj_interface_group_apply_detail
        (interface_group_apply_detail_id, pf_interface_group_id, pf_interface_group_name,
         pf_interface_id, pf_interface_name, third_interface_id, environment, auth_flag,
         auth_time, is_deleted, creater_id, create_time, updater_id, update_time, pf_interface_group_note,
         pf_interface_file_url)
        values (#{interfaceGroupApplyDetailId,jdbcType=BIGINT}, #{pfInterfaceGroupId,jdbcType=BIGINT},
                #{pfInterfaceGroupName,jdbcType=VARCHAR}, #{pfInterfaceId,jdbcType=BIGINT},
                #{pfInterfaceName,jdbcType=VARCHAR},
                #{thirdInterfaceId,jdbcType=BIGINT}, #{environment,jdbcType=SMALLINT}, #{authFlag,jdbcType=SMALLINT},
                #{authTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{pfInterfaceGroupNote,jdbcType=VARCHAR}, #{pfInterfaceFileUrl,jdbcType=VARCHAR})
        on duplicate key update
            interface_group_apply_detail_id = #{interfaceGroupApplyDetailId,jdbcType=BIGINT}, pf_interface_group_id = #{pfInterfaceGroupId,jdbcType=BIGINT}, pf_interface_group_name = #{pfInterfaceGroupName,jdbcType=VARCHAR}, pf_interface_id = #{pfInterfaceId,jdbcType=BIGINT}, pf_interface_name = #{pfInterfaceName,jdbcType=VARCHAR}, third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}, environment = #{environment,jdbcType=SMALLINT}, auth_flag = #{authFlag,jdbcType=SMALLINT}, auth_time = #{authTime,jdbcType=TIMESTAMP}, is_deleted = #{isDeleted,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, pf_interface_group_note = #{pfInterfaceGroupNote,jdbcType=VARCHAR}, pf_interface_file_url = #{pfInterfaceFileUrl,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail">
        <!--@mbg.generated-->
        insert into csm.proj_interface_group_apply_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceGroupApplyDetailId != null">
                interface_group_apply_detail_id,
            </if>
            <if test="pfInterfaceGroupId != null">
                pf_interface_group_id,
            </if>
            <if test="pfInterfaceGroupName != null">
                pf_interface_group_name,
            </if>
            <if test="pfInterfaceId != null">
                pf_interface_id,
            </if>
            <if test="pfInterfaceName != null">
                pf_interface_name,
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id,
            </if>
            <if test="environment != null">
                environment,
            </if>
            <if test="authFlag != null">
                auth_flag,
            </if>
            <if test="authTime != null">
                auth_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="pfInterfaceGroupNote != null">
                pf_interface_group_note,
            </if>
            <if test="pfInterfaceFileUrl != null">
                pf_interface_file_url,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="interfaceGroupApplyDetailId != null">
                #{interfaceGroupApplyDetailId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupId != null">
                #{pfInterfaceGroupId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupName != null">
                #{pfInterfaceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceId != null">
                #{pfInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceName != null">
                #{pfInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceId != null">
                #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="environment != null">
                #{environment,jdbcType=SMALLINT},
            </if>
            <if test="authFlag != null">
                #{authFlag,jdbcType=SMALLINT},
            </if>
            <if test="authTime != null">
                #{authTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pfInterfaceGroupNote != null">
                #{pfInterfaceGroupNote,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceFileUrl != null">
                #{pfInterfaceFileUrl,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="interfaceGroupApplyDetailId != null">
                interface_group_apply_detail_id = #{interfaceGroupApplyDetailId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupId != null">
                pf_interface_group_id = #{pfInterfaceGroupId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceGroupName != null">
                pf_interface_group_name = #{pfInterfaceGroupName,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceId != null">
                pf_interface_id = #{pfInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="pfInterfaceName != null">
                pf_interface_name = #{pfInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="thirdInterfaceId != null">
                third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="environment != null">
                environment = #{environment,jdbcType=SMALLINT},
            </if>
            <if test="authFlag != null">
                auth_flag = #{authFlag,jdbcType=SMALLINT},
            </if>
            <if test="authTime != null">
                auth_time = #{authTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="pfInterfaceGroupNote != null">
                pf_interface_group_note = #{pfInterfaceGroupNote,jdbcType=VARCHAR},
            </if>
            <if test="pfInterfaceFileUrl != null">
                pf_interface_file_url = #{pfInterfaceFileUrl,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectByThirdIdAndEnv" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_interface_group_apply_detail
        where third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}
          and environment = #{environment,jdbcType=SMALLINT}
          and is_deleted = 0
    </select>

    <update id="deleteByThirdIdAndEnv">
        update csm.proj_interface_group_apply_detail
        set is_deleted = 1
        where third_interface_id = #{thirdInterfaceId,jdbcType=BIGINT}
          and environment = #{environment,jdbcType=SMALLINT}
    </update>
</mapper>
