<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProCloudConfigResearchMapper">
    <resultMap type="com.msun.csm.dao.entity.proj.ProCloudConfigResearch" id="ProCloudConfigResearchMap">
        <result property="cloudConfigResearchId" column="cloud_config_research_id" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="sysUserId" column="sys_user_id" jdbcType="INTEGER"/>
        <result property="userYunyingId" column="user_yunying_id" jdbcType="VARCHAR"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
        <result property="isResearch" column="is_research" jdbcType="INTEGER"/>
        <result property="isPresent" column="is_present" jdbcType="INTEGER"/>
        <result property="presentContent" column="present_content" jdbcType="VARCHAR"/>
    </resultMap>
</mapper>
