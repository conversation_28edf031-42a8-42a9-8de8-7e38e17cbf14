<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipRecordVsPacsMapper">
    <select id="getEquipRecordVsPacsDetail" parameterType="com.msun.csm.model.dto.ProjEquipRecordVsPacsDTO"
            resultType="com.msun.csm.model.vo.ProjEquipRecordVsPacsVO">
        select pervp.*,
        per.*,
        phi.hospital_name,
        (select pass_standard from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as pass_standard
        from csm.proj_equip_record_vs_pacs pervp
        left join csm.proj_equip_record per on pervp.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        where pervp.is_delete = 0
        <if test="projectInfoId = != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipRecordVsPacsId != null">
            and pervp.equip_record_vs_pacs_id = #{equipRecordVsPacsId}
        </if>
    </select>

    <select id="selectPacsEquipData" resultType="com.msun.csm.model.vo.ProjEquipRecordVsPacsVO"
            parameterType="com.msun.csm.model.dto.PacsEquipSelectDTO">
        select
        per.* ,
        pervp.*,
        phi.hospital_name as hospitalInfoName,
        phi.cloud_hospital_id,
        phi.cloud_hospital_id as cloud_hospital_id_str,
        su.user_name as createrName
        from
        csm.proj_equip_record_vs_pacs pervp
        left join csm.proj_equip_record per on pervp.equip_record_id = per.equip_record_id and per.is_deleted = 0
        left join csm.proj_hospital_info phi on phi.hospital_info_id = per.hospital_info_id and phi.is_deleted = 0
        left join csm.sys_user su on per.creater_id = su.sys_user_id
        where
        pervp.is_deleted = 0
        <if test="customInfoId != null">
            and per.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="equipModelOrFactory != null">
            and (pervp.equip_modal_name ilike concat('%',#{equipModelOrFactory},'%') or per.equip_factory_name like
            concat('%',#{equipModelOrFactory},'%'))
        </if>
        <if test="requiredFlag != null">
            and per.required_flag = #{requiredFlag}
        </if>
        <if test="equipRecordVsPacsId != null">
            and pervp.equip_record_vs_pacs_id = #{equipRecordVsPacsId}
        </if>
        order by pervp.create_time desc
    </select>

    <select id="findToMsunEquipRecord" parameterType="com.msun.csm.model.dto.ProjEquipSendToCloudDTO"
            resultType="com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto">
        select rvp.equip_record_vs_pacs_id as id,
        phi.cloud_hospital_id as hospitalId,
        phi.org_id as hisOrgId,
        'PACS' as productCode,
        per.equip_type_id as deviceId,
        per.equip_type_name as deviceName,
        per.equip_factory_name as firm,
        per.equip_model_name as model,
        per.comm_mode_key as communication,
        per.equip_position as location,
        rvp.equip_ip as equipmentIp,
        per.equip_factory_phone as firmTel,
        rvp.ae_title as equipmentAeTitle,
        per.equip_position as regionalEquipment,
        per.equip_class_id as monitorClass,
        per.cloud_equip_id as oldEquipId,
        per.yy_product_id as productId,
        rvp.equip_modal_name as checkModel
        from csm.proj_equip_record_vs_pacs rvp
        left join csm.proj_equip_record per on rvp.equip_record_id = per.equip_record_id
        left join csm.proj_hospital_info phi on per.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        where per.equip_status != 5 and per.required_flag = 1
        <if test="projectInfoId !=null">
            and per.project_info_id = #{projectInfoId}
        </if>
        <if test="equipRecordVsProductId != null">
            and rvp.equip_record_vs_pacs_id = #{equipRecordVsProductId}
        </if>
        <if test="equipRecordVsProductIds != null and equipRecordVsProductIds.size() > 0">
            and rvp.equip_record_vs_pacs_id in
            <foreach collection="equipRecordVsProductIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and rvp.is_deleted = 0
        and per.is_deleted = 0
    </select>

    <update id="updateSetdToMusnStatus">
        update csm.proj_equip_record_vs_pacs
        set send_cloud_flag = 1
        where equip_record_vs_pacs_id in
        <foreach collection="equipRecordVsProductIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getNoMappingCount" parameterType="com.msun.csm.model.dto.ProjEquipSendToCloudDTO"
            resultType="java.lang.Integer">
        select count(1)
        from csm.proj_equip_record_vs_pacs rvp
        left join csm.proj_equip_record per on rvp.equip_record_id = per.equip_record_id
        where per.project_info_id = #{projectInfoId}
        and per.required_flag = 1
        and rvp.send_cloud_flag = 0
        and rvp.is_deleted = 0
        and per.is_deleted = 0
        <if test="equipRecordVsProductId != null">
            and rvp.equip_record_vs_pacs_id = #{equipRecordVsProductId}
        </if>
    </select>

    <select id="selectOldEquipDataToPacs" parameterType="long"
            resultType="com.msun.csm.model.dto.SendCsmEquipToOldPacsDTO">
        select distinct
        tpnvo.new_custom_info_id as customInfoId, -- 新系统客户id
        tpnvo.old_custom_id, -- 老系统客户id
        tpnvo.new_project_info_id, -- 新系统项目id
        tpnvo.old_project_info_id, -- oldProjectInfoId
        es.id, --设备id
        es.factory_name_id as dictEquipFactoryId, -- 字典的厂商id
        def.equip_factory_name as dictEquipFactoryName, -- 字典的厂商名称
        es.real_factory_name as equipFactoryName, -- 手动输入的厂商名称
        es.model_name_id as dictEquipModelId, -- 字典的设备型号id
        den.equip_name as dictEquipModelName, -- 字典的设备型号名称
        es.real_model_name as equipModelName, -- 手动输入的设备型号
        es.check_model as equipModelKey, -- 检查模态
        es.factory_phone as equipFactoryPhone, -- 厂商电话
        es.equipment_ip as equipIp, -- 设备ip
        es.location as equipPosition , -- 设备位置
        es.equipment_ae_title as aetitle, -- AETitle
        es.buff_join as requiredFlag, -- 是否对接
        es.reason as stopReason, -- 不对接原因
        es.remarks as memo, -- 备注
        es.status as equipStatus , -- 设备状态
        es.is_sent , -- 发送云健康状态
        es.old_equip_id as cloudEquipId, -- 云健康设备id
        es.old_yun_model_name as cloudEquipName, -- 云健康设备名称
        eii.state as equipTestStstus, -- 设备调试状态
        es.check_result as checkResult, -- 检测结果
        es.customer_info_id as hospitalInfoId, -- 老系统的来源医院id【数据迁移时需要进行转换为csm】
        es.create_id as createId, -- 创建人【老系统中人员id】
        es.create_time -- 创建时间
        from platform.equip_search es
        left join anay.dict_equip_factory def on es.factory_name_id = def.equip_factory_id
        left join anay.dict_equip_class dec2 on es.type_name_id = dec2.equip_class_id
        left join anay.dict_equip_name den on es.model_name_id = den.id
        left join platform.equip_item_info eii on es.id = eii.equip_id
        left join platform.project p on es.project_id = p.id
        left join comm.customer_info ci on es.customer_info_id = ci.id
        left join csm.tmp_project_new_vs_old tpnvo on tpnvo.old_custom_id = es.customer_id and tpnvo.old_project_info_id
        = es.project_id
        where es.delete_flag = '0' and es.product_id = 322
        <if test="projectInfoId != -1">
            and tpnvo.new_project_info_id = #{projectInfoId}
        </if>
        <if test="oldEquipId != -1">
            and es.id = #{oldEquipId}
        </if>
    </select>

    <select id="selectNotCheckSuccess" parameterType="long" resultType="int">
        select count(1)
        from csm.proj_equip_record_vs_pacs pervp
                 left join csm.proj_equip_record per on pervp.equip_record_id = per.equip_record_id
        where pervp.is_deleted = 0
          and per.is_deleted = 0
          and per.equip_status != 5
          and per.required_flag = 1
          and per.project_info_id = #{projectInfoId}
    </select>

    <select id="selectHasPacsProject" resultType="long" parameterType="long">
        select ppi.project_info_id
        from csm.proj_project_info ppi
                 left join csm.proj_order_product pop on ppi.project_info_id = pop.project_info_id
        where ppi.custom_info_id = #{customInfoId}
          and pop.yy_order_product_id in (4063)
    </select>
</mapper>
