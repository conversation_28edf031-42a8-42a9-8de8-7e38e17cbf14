<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductFunctionUseInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjProductFunctionUseInfo">
        <id column="proj_product_function_use_info_id" jdbcType="BIGINT" property="projProductFunctionUseInfoId"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="function_code" jdbcType="VARCHAR" property="functionCode"/>
        <result column="use_count" jdbcType="INTEGER" property="useCount"/>
    </resultMap>

    <update id="updateUseInfoById" parameterType="com.msun.csm.dao.entity.proj.UpdateFunctionUseInfoParam">
        update csm.proj_product_function_use_info
        <set>
            update_time = now(),
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId},
            </if>
            <if test="functionCode != null">
                function_code = #{functionCode},
            </if>
            <if test="useCount != null">
                use_count = #{useCount},
            </if>
        </set>
        where proj_product_function_use_info_id = #{projProductFunctionUseInfoId}
    </update>

    <select id="selectUseInfoById" resultMap="BaseResultMap">
        select *
        from csm.proj_product_function_use_info
        where is_deleted = 0
          and project_info_id = #{projectInfoId}
          and yy_product_id = #{yyProductId}
          and function_code = #{functionCode}
    </select>

    <select id="queryProductFunctionUseInfoByProject" parameterType="com.msun.csm.model.param.QueryProductFunctionScoreRecordParam"
            resultType="com.msun.csm.dao.entity.proj.ProductFunctionUseInfoPO">
        select
        ppfui.proj_product_function_use_info_id as "projProductFunctionUseInfoId",
        ppfui.is_deleted as "isDeleted",
        ppfui.creater_id as "createrId",
        ppfui.create_time as "createTime",
        ppfui.updater_id as "updaterId",
        ppfui.update_time as "updateTime",
        ppfui.project_info_id as "projectInfoId",
        ppfui.function_code as "functionCode",
        ppfui.use_count as "useCount",
        dp.product_name as "productName" ,
        dpf.yy_product_id as "yyProductId",
        dpf.function_name as "functionName",
        dpf.function_desc as "functionDesc",
        dpf.maternal_child_hospital_flag as "maternalChildHospitalFlag",
        dpf.peoples_hospital_flag,
        dpf.chinese_hospital_flag,
        dpf.tumor_hospital_flag,
        dpf.stomatology_hospital_flag,
        dpf.eye_hospital_flag,
        dpf.common_function_flag as "commonFunctionFlag"
        from csm.proj_product_function_use_info ppfui
        left join csm.dict_product_function dpf on ppfui.function_code = dpf.function_code and dpf.is_delete = 0
        left join csm.dict_product dp on dpf.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
        where ppfui.is_deleted = 0
        and ppfui.project_info_id = #{projectInfoId}
        <if test="yyProductId != null">
            and dpf.yy_product_id = #{yyProductId}
        </if>
        <if test="useStatus == 1">
            and (ppfui.use_count is null or ppfui.use_count = 0 )
        </if>
        <if test="useStatus == 2">
            and ppfui.use_count is not null
            and ppfui.use_count > 0
        </if>
        order by dp.order_no ,dpf.function_name
    </select>
</mapper>
