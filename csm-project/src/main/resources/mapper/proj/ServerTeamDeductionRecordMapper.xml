<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ServerTeamDeductionRecordMapper">

    <select id="getServerTeamDeductionRecordByProjectInfoIdAndServerType"
            resultType="com.msun.csm.dao.entity.proj.BackendTeamDeductionRecordVO">
        select stdr.server_team_deduction_record_id as "backendTeamDeductionRecordId",
               stdr."source",
               case
                   when stdr."source" = 'first' then '首验'
                   else '终验'
                   end                              as "projectStageName",
               stdr.server_type_code                as "serverTypeCode",
               dst.server_type_name                 as "serverTypeName",
               stdr.server_team_yy_id               as "serverTeamYyId",
               sd.dept_name                         as "serverTeamName",
               sd.dept_leader_yunying_id            as "serverTeamLeaderYyId",
               su.user_name                         as "serverTeamLeaderName",
               stdr.record_status                   as "recordStatus",
               *
        from csm.server_team_deduction_record stdr
                 left join csm.dict_server_type dst
                           on stdr.server_type_code = dst.server_type_code and dst.is_deleted = 0
                 left join csm.sys_dept sd on stdr.server_team_yy_id = sd.dept_yunying_id and sd.is_deleted = 0
                 left join csm.sys_user su on sd.dept_leader_yunying_id = su.user_yunying_id and su.is_deleted = 0
        --                 left join csm.config_custom_backend_detail_limit ccbdl on stdr.project_info_id = ccbdl.project_info_id and (
        --      (stdr.server_type_code = 'bustype' and ccbdl.open_type = 13) or (stdr.server_type_code = 'datatype' and ccbdl.open_type = 14) or (stdr.server_type_code = 'interfacetype' and ccbdl.open_type = 15) )
        where stdr.is_deleted = 0
          and stdr.project_info_id = #{projectInfoId}
          and stdr.server_type_code = #{serverTypeCode}
    </select>

    <insert id="saveServerTeamDeductionRecord" parameterType="com.msun.csm.dao.entity.proj.ServerTeamDeductionRecord">
        insert into csm.server_team_deduction_record (
            server_team_deduction_record_id,
            is_deleted,
            creater_id,
            create_time,
            updater_id,
            update_time,
            source,
            server_type_code,
            server_team_yy_id,
            record_status,
            project_info_id
        )
        values (
            #{serverTeamDeductionRecordId},
            #{isDeleted},
            #{createrId},
            #{createTime},
            #{updaterId},
            #{updateTime},
            #{source},
            #{serverTypeCode},
            #{serverTeamYyId},
            #{recordStatus},
            #{projectInfoId}
        )
    </insert>

    <update id="updateServerTeamDeductionRecord" parameterType="com.msun.csm.dao.entity.proj.UpdateServerTeamDeductionRecordParam">
        update csm.server_team_deduction_record
        <set>
            <if test="updaterId != null">
                updater_id = #{updaterId},
            </if>
            update_time = now(),
            <if test="source != null">
                "source" = #{source},
            </if>
            <if test="serverTypeCode != null">
                server_type_code = #{serverTypeCode},
            </if>
            <if test="serverTeamYyId != null">
                server_team_yy_id = #{serverTeamYyId},
            </if>
            <if test="recordStatus != null">
                record_status = #{recordStatus},
            </if>
            <if test="qualityRemark != null">
                quality_remark = #{qualityRemark},
            </if>
            <if test="backendRemark != null">
                backend_remark = #{backendRemark},
            </if>
            <if test="applyUserId != null">
                apply_user_id = #{applyUserId},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime},
            </if>
            <if test="confirmUserId != null">
                confirm_user_id = #{confirmUserId},
            </if>
            <if test="confirmTime != null">
                confirm_time = #{confirmTime},
            </if>
        </set>
        where server_team_deduction_record_id = #{serverTeamDeductionRecordId}
    </update>

    <update id="clearUserAndTime" >
        update csm.server_team_deduction_record
        <set>
            update_time = now(),
            <if test="type == 'apply'">
                apply_user_id = null,
                apply_time = null,
            </if>
            <if test="type == 'confirm'">
                confirm_user_id = null,
                confirm_time = null,
            </if>
        </set>
        where server_team_deduction_record_id = #{serverTeamDeductionRecordId}
    </update>

    <select id="queryServerTeamDeductionRecordForBackendTeam"
            resultType="com.msun.csm.dao.entity.proj.ServerTeamDeductionRecordForBackendTeamVO" parameterType="com.msun.csm.dao.entity.proj.QueryServerTeamDeductionRecordForBackendTeamParam">
        select stdr."source"                                       as "source",
               stdr.project_info_id                                as "projectInfoId",
               stdr.server_team_deduction_record_id                as "backendTeamDeductionRecordId",
               pci.custom_name                                     as "customName",
               ppi.project_number                                  as "projectNumber",
               stdr.server_type_code                               as "serverTypeCode",
               dst.server_type_name                                as "serverTypeName",
               stdr.server_team_yy_id                              as "serverTeamYyId",
               sd.dept_name                                        as "serverTeamName",
               sd.dept_leader_yunying_id                           as "serverTeamLeaderYyId",
               leader.user_name                                    as "serverTeamLeaderName",
               stdr.record_status                                  as "recordStatus",
               to_char(stdr.apply_time, 'YYYY-MM-DD HH24:MI:SS')   as "applyDate",
               stdr.apply_user_id                                  as "applyUserId",
               applyer.user_name                                   as "applyUserName",
               to_char(stdr.confirm_time, 'YYYY-MM-DD HH24:MI:SS') as "confirmDate",
               stdr.confirm_user_id                                as "confirmUserId",
               confirmer.user_name                                 as "confirmUserName"
        from csm.server_team_deduction_record stdr
                 left join csm.proj_project_info ppi on stdr.project_info_id = ppi.project_info_id and ppi.is_deleted = 0
                 left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id and pci.is_deleted = 0
                 left join csm.dict_server_type dst on stdr.server_type_code = dst.server_type_code and dst.is_deleted = 0
                 left join csm.sys_dept sd on stdr.server_team_yy_id = sd.dept_yunying_id and sd.is_deleted = 0
                 left join csm.sys_user leader on sd.dept_leader_yunying_id = leader.user_yunying_id and leader.is_deleted = 0
                 left join csm.sys_user applyer on stdr.apply_user_id = applyer.sys_user_id and applyer.is_deleted = 0
                 left join csm.sys_user confirmer on stdr.confirm_user_id = confirmer.sys_user_id and confirmer.is_deleted = 0
        where stdr.is_deleted = 0
        <if test="searchKey != null and searchKey != ''">
            and (pci.custom_name ilike concat('%', #{searchKey}, '%') or ppi.project_number ilike concat('%', #{searchKey}, '%'))
        </if>
        <if test="serverTypeCode != null and serverTypeCode.size > 0" >
            and stdr.server_type_code in
            <foreach collection="serverTypeCode" item="serverType" index="index" open="(" separator="," close=")">
                #{serverType}
            </foreach>
        </if>
        <if test="serverTeamId != null">
            and stdr.server_team_yy_id = #{serverTeamId}
        </if>
        <if test="recordStatus == null or recordStatus.size == 0">
            and stdr.record_status != 1
        </if>
        <if test="recordStatus != null and recordStatus.size > 0">
            and stdr.record_status in
            <foreach collection="recordStatus" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by stdr.apply_time desc
    </select>

</mapper>
