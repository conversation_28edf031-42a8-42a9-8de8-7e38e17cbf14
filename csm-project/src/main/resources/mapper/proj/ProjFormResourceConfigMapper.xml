<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjFormResourceConfigMapper">
    <resultMap type="com.msun.csm.dao.entity.proj.ProjFormResourceConfig" id="BaseResultMap">
        <result property="projFormId" column="proj_form_id" jdbcType="INTEGER"/>
        <result property="formCode" column="form_code" jdbcType="VARCHAR"/>
        <result property="formName" column="form_name" jdbcType="VARCHAR"/>
        <result property="enabledFlag" column="enabled_flag" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="projectInfoId" column="project_info_id" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        proj_form_id, form_code, form_name, enabled_flag, creater_id, create_time, updater_id,
    update_time, project_info_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.proj_form_resource_config
        where proj_form_id = #{projFormId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from csm.proj_form_resource_config
        where proj_form_id = #{projFormId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjFormResourceConfig">
        insert into csm.proj_form_resource_config (proj_form_id, form_code, form_name,
                                                   enabled_flag, creater_id, create_time,
                                                   updater_id, update_time, project_info_id
        )
        values (#{projFormId,jdbcType=BIGINT}, #{formCode,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR},
                #{enabledFlag,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{projectInfoId,jdbcType=BIGINT}
               )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjFormResourceConfig">
        insert into csm.proj_form_resource_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projFormId != null">
                proj_form_id,
            </if>
            <if test="formCode != null">
                form_code,
            </if>
            <if test="formName != null">
                form_name,
            </if>
            <if test="enabledFlag != null">
                enabled_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projFormId != null">
                #{projFormId,jdbcType=BIGINT},
            </if>
            <if test="formCode != null">
                #{formCode,jdbcType=VARCHAR},
            </if>
            <if test="formName != null">
                #{formName,jdbcType=VARCHAR},
            </if>
            <if test="enabledFlag != null">
                #{enabledFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjFormResourceConfig">
        update csm.proj_form_resource_config
        <set>
            <if test="formCode != null">
                form_code = #{formCode,jdbcType=VARCHAR},
            </if>
            <if test="formName != null">
                form_name = #{formName,jdbcType=VARCHAR},
            </if>
            <if test="enabledFlag != null">
                enabled_flag = #{enabledFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
        </set>
        where proj_form_id = #{projFormId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjFormResourceConfig">
        update csm.proj_form_resource_config
        set form_code = #{formCode,jdbcType=VARCHAR},
            form_name = #{formName,jdbcType=VARCHAR},
            enabled_flag = #{enabledFlag,jdbcType=SMALLINT},
            creater_id = #{createrId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            project_info_id = #{projectInfoId,jdbcType=BIGINT}
        where proj_form_id = #{projFormId,jdbcType=BIGINT}
    </update>
    <update id="batchUpdateById">
        update csm.proj_form_resource_config
        set
            enabled_flag = #{enabledFlag,jdbcType=SMALLINT}
        where proj_form_id in
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectByProjectInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_form_resource_config
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
    </select>
    <select id="findByPage" parameterType="com.msun.csm.dao.entity.proj.ProjFormResourceConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_form_resource_config
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
        <if test="formCode != null and formCode!=''">
            and form_code like concat('%',#{form_code},'%')
        </if>
        <if test="formName != null and formName!=''">
            and form_name like concat('%',#{formName},'%')
        </if>
        <if test="enabledFlag != null">
            and enabled_flag = #{enabledFlag,jdbcType=SMALLINT}
        </if>
    </select>
</mapper>
