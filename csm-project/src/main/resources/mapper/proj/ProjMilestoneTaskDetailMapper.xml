<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper">
    <insert id="insertBatch">
        insert into proj_milestone_task_detail (milestone_task_detail_id,
                                                leader_id,
                                                second_leader_id,
                                                product_deliver_id,
                                                expect_start_time,
                                                expect_comp_time,
                                                milestone_task_id,
                                                product_deliver_record_id,
                                                is_deleted,
                                                creater_id,
                                                create_time,
                                                updater_id,
                                                update_time,
                                                result_source_id,
                                                complete_status,
                                                actual_comp_time)
        values
        <foreach collection="list" item="detail" separator=",">
            (#{detail.milestoneTaskDetailId},
             #{detail.leaderId},
             #{detail.secondLeaderId},
             #{detail.productDeliverId},
             #{detail.expectStartTime},
             #{detail.expectCompTime},
             #{detail.milestoneTaskId},
             #{detail.productDeliverRecordId},
             #{detail.isDeleted},
             #{detail.createrId},
             #{detail.createTime},
             #{detail.updaterId},
             #{detail.updateTime},
             #{detail.resultSourceId},
             #{detail.completeStatus},
             #{detail.actualCompTime})
        </foreach>
    </insert>

    <select id="selectProductCountByProjectInfoId" parameterType="long" resultType="long">
        select pmtd.product_deliver_id
        from csm.proj_milestone_task_detail pmtd
        where pmtd.milestone_task_id in (select pmt.milestone_task_id
                                         from csm.proj_milestone_task pmt
                                         where pmt.project_info_id = #{projectInfoId}
                                           and pmt.milestone_task_id = #{milestoneTaskId}
                                           and pmt.is_deleted = 0)
          and pmtd.is_deleted = 0
        group by product_deliver_id
    </select>

    <select id="selectProductFinishCountByProductId" parameterType="long" resultType="int">
        select count(1)
        from csm.proj_milestone_task_detail pmtd
        where pmtd.milestone_task_id in (select pmt.milestone_task_id
                                         from csm.proj_milestone_task pmt
                                         where pmt.project_info_id = #{projectInfoId}
                                           and pmt.is_deleted = 0)
          and pmtd.is_deleted = 0
          and pmtd.product_deliver_id = #{productId}
          and pmtd.complete_status = 0
    </select>

    <update id="updateCompleteStatus">
        update csm.proj_milestone_task_detail
        set complete_status = 0
        where milestone_task_id in
        <foreach collection="milestoneTaskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and product_deliver_id in
        <foreach collection="productIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateByPid">
        update csm.proj_milestone_task_detail
        set milestone_task_id = #{newPid}
        where milestone_task_id = #{oldPid}
        <if test="splitYYPIdList != null">
            and product_deliver_id in
            <foreach collection="splitYYPIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="deleteByMTaskAndProducts">
        update csm.proj_milestone_task_detail
        set is_deleted  = 1,
            update_time = now()
        where milestone_task_id = #{milestoneTaskId}
          and product_deliver_id in
        <foreach collection="deliverProductIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
