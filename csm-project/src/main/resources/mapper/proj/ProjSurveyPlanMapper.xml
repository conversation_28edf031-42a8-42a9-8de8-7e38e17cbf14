<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjSurveyPlan">
        <!--@mbg.generated-->
        <!--@Table csm.proj_survey_plan-->
        <id column="survey_plan_id" jdbcType="BIGINT" property="surveyPlanId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="survey_user_id" jdbcType="BIGINT" property="surveyUserId"/>
        <result column="plan_complete_time" jdbcType="TIMESTAMP" property="planCompleteTime"/>
        <result column="complete_status" jdbcType="SMALLINT" property="completeStatus"/>
        <result column="actual_comp_time" jdbcType="TIMESTAMP" property="actualCompTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="order_no" jdbcType="BIGINT" property="orderNo"/>
        <result column="audit_sys_user_id" jdbcType="BIGINT" property="auditSysUserId"/>
        <result column="plan_audit_time" jdbcType="TIMESTAMP" property="planAuditTime"/>
        <result column="actual_audit_time" jdbcType="TIMESTAMP" property="actualAuditTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        survey_plan_id,
        yy_product_id,
        hospital_info_id,
        custom_info_id,
        project_info_id,
        dept_name,
        survey_user_id,
        plan_complete_time,
        complete_status,
        actual_comp_time,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        order_no,
        audit_sys_user_id,
        plan_audit_time,
        actual_audit_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_survey_plan
        where survey_plan_id = #{surveyPlanId,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjSurveyPlan">
        <!--@mbg.generated-->
        insert into csm.proj_survey_plan (survey_plan_id, yy_product_id, hospital_info_id,
                                          custom_info_id, project_info_id, dept_name,
                                          survey_user_id, plan_complete_time, complete_status,
                                          actual_comp_time, is_deleted, creater_id,
                                          create_time, updater_id, update_time)
        values (#{surveyPlanId,jdbcType=BIGINT}, #{yyProductId,jdbcType=BIGINT}, #{hospitalInfoId,jdbcType=BIGINT},
                #{customInfoId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT}, #{deptName,jdbcType=VARCHAR},
                #{surveyUserId,jdbcType=BIGINT}, #{planCompleteTime,jdbcType=TIMESTAMP},
                #{completeStatus,jdbcType=SMALLINT},
                #{actualCompTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjSurveyPlan">
        <!--@mbg.generated-->
        update csm.proj_survey_plan
        <set>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="deptName != null and deptName != ''">
                dept_name = #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="surveyUserId != null">
                survey_user_id = #{surveyUserId,jdbcType=BIGINT},
            </if>
            <if test="planCompleteTime != null">
                plan_complete_time = #{planCompleteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeStatus != null">
                complete_status = #{completeStatus,jdbcType=SMALLINT},
            </if>
            <if test="actualCompTime != null">
                actual_comp_time = #{actualCompTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditSysUserId != null">
                audit_sys_user_id = #{auditSysUserId,jdbcType=BIGINT},
            </if>
            <if test="planAuditTime != null">
                plan_audit_time = #{planAuditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualAuditTime != null">
                actual_audit_time = #{actualAuditTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where survey_plan_id = #{surveyPlanId,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_survey_plan
        (survey_plan_id, yy_product_id, hospital_info_id, custom_info_id, project_info_id,
         dept_name, survey_user_id, plan_complete_time, complete_status, actual_comp_time,
         is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.surveyPlanId,jdbcType=BIGINT}, #{item.yyProductId,jdbcType=BIGINT},
             #{item.hospitalInfoId,jdbcType=BIGINT},
             #{item.customInfoId,jdbcType=BIGINT}, #{item.projectInfoId,jdbcType=BIGINT},
             #{item.deptName,jdbcType=VARCHAR},
             #{item.surveyUserId,jdbcType=BIGINT}, #{item.planCompleteTime,jdbcType=TIMESTAMP},
             #{item.completeStatus,jdbcType=SMALLINT}, #{item.actualCompTime,jdbcType=TIMESTAMP},
             #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>


    <select id="findSurveyPlan" resultType="com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp"
            parameterType="com.msun.csm.model.dto.ProjSurveyPlanDTO">
        select sp.*,
        hi.hospital_name as hospitalInfoName,
        su.user_name as surveyUserName,
        to_char(sp.plan_audit_time, 'yyyy-MM-dd hh:mm') as "planAuditTimeStr",
        audit.user_name as auditUserName
        from csm.proj_survey_plan sp
        left outer join csm.proj_hospital_info hi on sp.hospital_info_id = hi.hospital_info_id
        left outer join csm.sys_user su on sp.survey_user_id = su.sys_user_id
        left outer join csm.sys_user audit on sp.audit_sys_user_id = audit.sys_user_id
        where sp.is_deleted = 0
        and sp.project_info_id = #{projectInfoId}
        <if test="yyProductId != null">
            and sp.yy_product_id = #{yyProductId}
        </if>
        <if test="surveyUserId != null">
            and sp.survey_user_id = #{surveyUserId}
        </if>
        <if test="completeStatus != null">
            and sp.complete_status = #{completeStatus}
        </if>
        <if test="hospitalInfoId != null">
            and sp.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="auditSysUserId != null">
            and sp.audit_sys_user_id = #{auditSysUserId}
        </if>
    </select>
    <select id="findBranchHospitalSurveyPlan" resultMap="BaseResultMap">
        select sp.*
        from csm.proj_survey_plan sp
                 left join csm.dict_product dp on dp.yy_product_id = sp.yy_product_id
        where sp.is_deleted = 0
          and (dp.product_name NOT LIKE '%升级云健康%' or dp.product_name = '' or dp.product_name is null)
          AND (dp.product_name NOT LIKE '%分院模式%' or dp.product_name = '' or dp.product_name is null)
          and sp.project_info_id = #{projectInfoId}
          and sp.complete_status = #{completeStatus}
    </select>
    <select id="findNotNeedSurveyPlan" resultMap="BaseResultMap">
        select yy_product_id
        from csm.dict_product_extend sp
        where sp.is_deleted = 0
          and sp.survey_flag = 0
    </select>
    <select id="findYyProductIdByProjectInfoId" resultType="java.lang.Long"
            parameterType="com.msun.csm.model.dto.ProjSurveyPlanDTO">
        select distinct yy_product_id
        from csm.proj_survey_plan
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
    </select>
    <select id="findSurveyPlanHospitalProduct" resultType="com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO"
            parameterType="com.msun.csm.model.dto.ProjSurveyPlanDTO">
        with products as (select psp.hospital_info_id,
        psp.yy_product_id,
        min(psp.complete_status) as complete_status
        from csm.proj_survey_plan psp
        left join csm.dict_product dp on dp.yy_product_id = psp.yy_product_id
        where psp.is_deleted = 0
        and psp.is_deleted = 0
        and (dp.product_name NOT LIKE '%升级云健康%' or dp.product_name = '' or
        dp.product_name is null)
        AND (dp.product_name NOT LIKE '%分院模式%' or dp.product_name = '' or
        dp.product_name is null)
        and psp.project_info_id = #{projectInfoId}
        group by psp.hospital_info_id,
        psp.yy_product_id),
        hosps as (select distinct phi.hospital_info_id, phi.hospital_name, phi.health_bureau_flag
        from csm.proj_hospital_info phi
        left join csm.proj_hospital_vs_project_type phvpt
        on phi.hospital_info_id = phvpt.hospital_info_id
        and phvpt.is_deleted = 0
        left join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type
        and ppi.custom_info_id = phi.custom_info_id
        and ppi.is_deleted = 0
        where phi.is_deleted = 0
        and ppi.project_info_id = #{projectInfoId})
        select phi.hospital_info_id,
        phi.hospital_name,
        count(psp.hospital_info_id) as productNum,
        min(phi.health_bureau_flag) as healthBureauFlag,
        coalesce(sum(case when complete_status = 1 then 1 else 0 end), 0) as completeProductNum
        from hosps phi
        left join products psp on
        psp.hospital_info_id = phi.hospital_info_id
        <where>
            <if test="hospitalName != null and hospitalName != ''">
                and phi.hospital_name like concat('%', #{hospitalName}, '%')
            </if>
        </where>
        group by phi.hospital_info_id,
        phi.hospital_name;
    </select>

    <update id="updateSurveyPlanStatus">
        update csm.proj_survey_plan
        set complete_status  = #{completeStatus},
            actual_comp_time = now(),
            updater_id       = #{surveyUserId},
            update_time      = now()
        where project_info_id = #{projectInfoId}
          and yy_product_id = #{yyProductId}
          and hospital_info_id = #{hospitalInfoId}
        <if test="deptName != null and deptName != ''">
            and dept_name = #{deptName}
        </if>
    </update>

    <update id="updateByProjectId">
        update
            csm.proj_survey_plan
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() > 0">
            and yy_product_id in
            <foreach collection="changeProductList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectHospitalPlanCount" resultType="long" parameterType="long">
        select COUNT(distinct hospital_info_id)
        from csm.proj_survey_plan psp
        where project_info_id = #{projectInfoId}
          and is_deleted = 0;
    </select>

    <select id="findSurveyPlanByProjectAndHospital" resultMap="BaseResultMap">
        select psp.*
        from csm.proj_survey_plan psp
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
          and hospital_info_id = #{hospitalInfoId}
    </select>
    <select id="findIsBranchOnlineYyProductIdByProjectInfoId" resultType="java.lang.Long">
        select distinct yy_product_id
        from csm.proj_survey_plan
        where project_info_id = #{projectInfoId}
          and is_deleted = 0
          and hospital_info_id not in (select hospital_info_id
                                       from csm.proj_hospital_online_detail hod
                                       where is_deleted = 0
                                         and custom_info_id in (select custom_info_id
                                                                from csm.proj_hospital_online_detail
                                                                where project_info_id = #{projectInfoId})
                                         and project_info_id != #{projectInfoId})
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_survey_plan
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_survey_plan
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and yy_product_id in
        <foreach collection="oldProductIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="getSurveyPlanAuditList" parameterType="com.msun.csm.model.dto.GetSurveyPlanAuditListParam" resultType="com.msun.csm.model.dto.SurveyPlanAuditInfo">
        SELECT psp.survey_plan_id               as "surveyPlanId",
               pci.custom_name                  as "customName",
               ppi.project_number               as "projectNumber",
               phi.hospital_name                as "hospitalName",
               case
                   when dp.product_name is not null then dp.product_name
                   else CONCAT(dp2.product_name, '-', dpvm.yy_module_name) end as "productName",
               su1.user_name                    as "surveyUserName",
               to_char(psp.actual_comp_time, 'YYYY-MM-DD HH24:MI')             as "surveyCompleteTime",
               psp.audit_sys_user_id            as "auditSysUserId",
               su2.user_name                    as "auditUserName",
               to_char(psp.plan_audit_time, 'YYYY-MM-DD HH24:MI') as "planAuditTime",
               to_char(psp.actual_audit_time, 'YYYY-MM-DD HH24:MI')            as "actualAuditTime",
               dbs.status_id                    as "statusId",
               dbs.status_code                  as "statusCode",
               dbs.status_description           as "statusDescription",
               dbs.status_color 				as "statusColor",
               dbs.status_class  				as "statusClass"
        FROM csm.proj_survey_plan AS psp
                 left join csm.proj_project_info ppi on psp.project_info_id = ppi.project_info_id
                 left join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id
                 left join csm.proj_hospital_info phi on psp.hospital_info_id = phi.hospital_info_id
                 left join csm.dict_product dp on psp.yy_product_id = dp.yy_product_id
                 left join csm.dict_product_vs_modules dpvm on psp.yy_product_id = dpvm.yy_module_id
                 left join csm.dict_product dp2 on dpvm.yy_product_id = dp2.yy_product_id
                 left join csm.sys_user su1 on psp.survey_user_id = su1.sys_user_id
                 left join csm.sys_user su2 on psp.audit_sys_user_id = su2.sys_user_id
                 left join csm.dict_business_status dbs on psp.complete_status  = dbs.status_id and dbs.business_code = 'survey_product'
                 left join csm.config_custom_backend_limit ccbl on psp.project_info_id = ccbl.project_info_id and ccbl.is_deleted = 0
                 left join csm.config_custom_backend_detail_limit ccbdl on psp.project_info_id = ccbdl.project_info_id and ccbdl.open_type = 10
        where psp.is_deleted = 0
          and ccbl.open_flag = 1
          and ccbdl.open_flag = 1
        <if test="keyWord != null and keyWord != ''">
          and (pci.custom_name like concat('%',#{keyWord},'%') or ppi.project_name like concat('%',#{keyWord},'%') or phi.hospital_name like concat('%',#{keyWord},'%') or dp.product_name like concat('%',#{keyWord},'%') or dpvm.yy_module_name like concat('%',#{keyWord},'%'))
        </if>
        <if test="customInfoId != null">
          and pci.custom_info_id = #{customInfoId}
        </if>
        <if test="projectInfoId != null">
          and psp.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
          and psp.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null">
          and psp.yy_product_id = #{yyProductId}
        </if>
        <if test="auditSysUserId != null">
          and psp.audit_sys_user_id = #{auditSysUserId}
        </if>
        <if test="completeStatus != null">
          and psp.complete_status = #{completeStatus}
        </if>
        <if test="completeStatusList != null and completeStatusList.size > 0">
            and psp.complete_status in
            <foreach collection="completeStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size > 0 ">
            and ppi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by pci.custom_name,ppi.project_number,phi.hospital_name,psp.yy_product_id
    </select>
</mapper>
