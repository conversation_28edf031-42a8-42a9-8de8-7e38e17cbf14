<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjApplyOrderNodeRecordMapper">
    <insert id="insertOrderNodeRecord" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrderNodeRecord">
        insert into csm.proj_apply_order_node_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projApplyOrderNodeRecordId != null">
                proj_apply_order_node_record_id,
            </if>
            <if test="nodeTypeCode != null">
                node_type_code,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="operateContent != null">
                operate_content,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="applyOrderId != null">
                apply_order_id,
            </if>
            <if test="telephone != null">
                telephone,
            </if>
            <if test="rejectReason != null">
                reject_reason,
            </if>
            <if test="refusedReason != null">
                refused_reason,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projApplyOrderNodeRecordId != null">
                #{projApplyOrderNodeRecordId,jdbcType=BIGINT},
            </if>
            <if test="nodeTypeCode != null">
                #{nodeTypeCode,jdbcType=INTEGER},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operateContent != null">
                #{operateContent,jdbcType=VARCHAR},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=BIGINT},
            </if>
            <if test="applyOrderId != null">
                #{applyOrderId,jdbcType=BIGINT},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="rejectReason != null">
                #{rejectReason,jdbcType=VARCHAR},
            </if>
            <if test="refusedReason != null">
                #{refusedReason,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>


    <select id="findByApplyOrderId" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrderNodeRecord"
            parameterType="java.lang.Long">
        select * from csm.proj_apply_order_node_record where apply_order_id = #{applyOrderId, jdbcType=BIGINT} and is_deleted = 0 order by create_time asc
    </select>
</mapper>
