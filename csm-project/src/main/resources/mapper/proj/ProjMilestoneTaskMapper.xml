<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper">
    <delete id="deleteByPlanIds" parameterType="java.util.List">
        delete from csm.proj_milestone_task
        <where>
            and milestone_task_id in
            <foreach collection="list" item="id" separator="," open="(" close=")">
                #{id, jdbcType=BIGINT}
            </foreach>
        </where>
    </delete>

    <select id="findProjResearchPlanRelativeInfo" resultType="com.msun.csm.dao.entity.proj.ProjResearchPlanRelative"
            parameterType="com.msun.csm.model.param.ProjResearchPlanParam">
        WITH RankedData AS (
        select
        *
        from
        (
        select
        milestone_task_id,
        expect_start_time,
        expect_comp_time,
        project_info_id,
        hospital_info_id,
        customer_info_id,
        leader_id,
        second_leader_id as second_leader_id_str,
        row_number() over(partition by project_info_id,
        customer_info_id,
        hospital_info_id
        order by
        milestone_task_id desc) rn
        from
        csm.proj_milestone_task t
        where
        is_deleted = 0
        and customer_info_id = #{customerInfoId, jdbcType=BIGINT}
        and project_info_id = #{projectInfoId, jdbcType=BIGINT}
        ) t
        where
        t.rn = 1
        )
        select t.region as section_name,t.hospital_name,t.health_bureau_flag,t.hospital_info_id ,t1.*
        ,t3.research_code_str from (
        select
        phi.hospital_info_id,
        phi.hospital_name,
        phi.region,
        phi.health_bureau_flag
        from csm.proj_hospital_info phi
        left join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id = phvpt.hospital_info_id
        and phvpt.is_deleted = 0
        left join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type
        and ppi.custom_info_id = phi.custom_info_id
        and ppi.is_deleted = 0
        where phi.is_deleted = 0
        and ppi.project_info_id = #{projectInfoId, jdbcType=BIGINT}
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            and phi.hospital_info_id= #{hospitalInfoId, jdbcType=BIGINT}
        </if>
        ) t left join RankedData t1 on t.hospital_info_id = t1.hospital_info_id
        left join (select hospital_info_id,string_agg(milestone_node_code::text, ',') as research_code_str
        from csm.proj_milestone_task where customer_info_id = #{customerInfoId, jdbcType=BIGINT}
        and project_info_id = #{projectInfoId, jdbcType=BIGINT}
        group by hospital_info_id) t3 on t1.hospital_info_id = t3.hospital_info_id
        <where>
            <if test="hospitalName != null and hospitalName != ''">
                and t.hospital_name like concat('%', #{hospitalName, jdbcType=VARCHAR}, '%')
            </if>
            <if test="hospitalInfoId != null and hospitalInfoId != ''">
                and t.hospital_info_id =#{hospitalInfoId,jdbcType=BIGINT}
            </if>
            <if test="sectionName != null and sectionName != ''">
                and t.region like concat('%', #{sectionName, jdbcType=VARCHAR}, '%')
            </if>
        </where>
        order by t.health_bureau_flag desc
    </select>
    <select id="findProjResearchPlanInfo" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneTask"
            parameterType="java.util.List">
        select t.region,t.hospital_name,t2.milestone_task_id from csm.proj_hospital_info t
        left join csm.proj_milestone_task t2 on t2.project_info_id = 3 and t2.hospital_info_id = t.hospital_info_id
        order by t2.milestone_task_id

        select t.milestone_task_id from (
        select project_info_id, milestone_task_id from csm.proj_milestone_task
        <where>
            and hospital_info_id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item.hospitalId, jdbcType=BIGINT}
            </foreach>
        </where>
        ) t
        <where>
            and t.project_info_id in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item.projectInfoId, jdbcType=BIGINT}
            </foreach>
        </where>
    </select>
    <select id="findCompleteHospitalInfoByProjectInfoIdCustInfoId"
            resultType="com.msun.csm.model.vo.MilestoneInfoVerifyVO">
        select count(hospital_info_id) as counts,
               sum(complete_status)    as sums,
               hospital_info_id        as hospitalInfoId
        from csm.proj_milestone_task
        where is_deleted = 0
          and project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and customer_info_id = #{customerInfoId, jdbcType=BIGINT}
          and complete_status = 0
          and result_source_id = 0
        group by hospital_info_id
    </select>
    <insert id="insertResearchPlans" parameterType="map">
        insert into csm.proj_milestone_task
        (milestone_task_id,
        hospital_info_id,
        customer_info_id,
        project_info_id,
        milestone_node_code,
        leader_id,
        second_leader_id,
        expect_start_time,
        expect_comp_time,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        result_source_id,
        milestone_info_id,
        project_stage_id,
        project_stage_code
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.projResearchPlanId,jdbcType=BIGINT},
            #{item.hospitalId,jdbcType=BIGINT},
            #{item.customerInfoId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.researchCode,jdbcType=VARCHAR},
            #{item.leaderId,jdbcType=VARCHAR},
            #{item.secondLeaderId,jdbcType=VARCHAR},
            #{item.planStartTime,jdbcType=TIMESTAMP},
            #{item.planEndTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.resultSourceId,jdbcType=BIGINT},
            #{item.milestoneInfoId,jdbcType=BIGINT},
            #{item.projectStageId,jdbcType=BIGINT},
            #{item.projectStageCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertBatch">
        insert into csm.proj_milestone_task
        (milestone_task_id,
        hospital_info_id,
        customer_info_id,
        project_info_id,
        milestone_node_code,
        leader_id,
        second_leader_id,
        expect_start_time,
        expect_comp_time,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        result_source_id,
        milestone_info_id,
        project_stage_id,
        project_stage_code
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.milestoneTaskId,jdbcType=BIGINT},
            #{item.hospitalInfoId,jdbcType=BIGINT},
            #{item.customerInfoId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.milestoneNodeCode,jdbcType=VARCHAR},
            #{item.leaderId},
            #{item.secondLeaderId},
            #{item.expectStartTime,jdbcType=TIMESTAMP},
            #{item.expectCompTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.resultSourceId,jdbcType=BIGINT},
            #{item.milestoneInfoId,jdbcType=BIGINT},
            #{item.projectStageId,jdbcType=BIGINT},
            #{item.projectStageCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getPlanByProjectId" resultType="com.msun.csm.dao.entity.proj.ResearchPlanForTask">
        select milestone_task_id,
               hospital_info_id,
               customer_info_id,
               project_info_id,
               milestone_node_code,
               leader_id,
               second_leader_id,
               expect_start_time,
               expect_comp_time,
               is_deleted,
               creater_id,
               create_time,
               updater_id,
               update_time,
               result_source_id
        from csm.proj_milestone_task prp
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
          and is_deleted = 0
    </select>

    <update id="updateResearchPlan">
        <foreach collection="updatePlans" item="item" separator=";" index="index">
            update csm.proj_milestone_task
            <set>
                <if test="item.leaderId != null">
                    leader_id = #{item.leaderId,jdbcType=BIGINT},
                </if>
                <if test="item.completeStatus != null">
                    complete_status = #{item.completeStatus,jdbcType=BIGINT},
                </if>
                <if test="item.secondLeaderId != null">
                    second_leader_id =#{item.secondLeaderId,jdbcType=VARCHAR},
                </if>
                <if test="item.planStartTime != null">
                    expect_start_time =#{item.planStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.planEndTime != null">
                    expect_comp_time=#{item.planEndTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updaterId != null">
                    updater_id=#{item.updaterId,jdbcType=BIGINT},
                </if>
                <if test="item.updateTime != null">
                    update_time=#{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.milestoneInfoId != null">
                    milestone_info_id=#{item.milestoneInfoId,jdbcType=TIMESTAMP},
                </if>
                <if test="item.projectStageId != null">
                    project_stage_id=#{item.projectStageId,jdbcType=TIMESTAMP},
                </if>
                <if test="item.projectStageCode != null">
                    project_stage_code=#{item.projectStageCode,jdbcType=TIMESTAMP},
                </if>
                <if test="item.actualCompTime != null">
                    actual_comp_time=#{item.actualCompTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where milestone_node_code =#{item.researchCode,jdbcType=VARCHAR}
            <if test="item.projectInfoId != null">
                and project_info_id =#{item.projectInfoId,jdbcType=BIGINT}
            </if>
            <if test="item.hospitalId != null">
                and hospital_info_id =#{item.hospitalId,jdbcType=BIGINT}
            </if>
            <if test="item.resultSourceId != null">
                and result_source_id = #{item.resultSourceId,jdbcType=BIGINT}
            </if>

        </foreach>
    </update>

    <select id="findByHospitalIdsAndCodes" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneTask">
        select * from csm.proj_milestone_task
        where is_deleted = 0
        <if test="hospitalIds != null and hospitalIds.size() > 0">
            and hospital_info_id in
            <foreach collection="hospitalIds" item="item" separator="," open="(" close=")">
                #{item, jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="researchCodes != null and researchCodes.size() != 0">
            and milestone_node_code in
            <foreach collection="researchCodes" item="item" separator="," open="(" close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="findByHospitalIdsAndCodesAndProjectInfoId" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneTask">
        select * from csm.proj_milestone_task
        where is_deleted = 0
        and project_info_id =#{projectInfoId,jdbcType=BIGINT}
        <if test="hospitalIds != null and hospitalIds.size() > 0">
            and hospital_info_id in
            <foreach collection="hospitalIds" item="item" separator="," open="(" close=")">
                #{item, jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="researchCodes != null and researchCodes.size() != 0">
            and milestone_node_code in
            <foreach collection="researchCodes" item="item" separator="," open="(" close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getProjResearchPlanForCheckedByProjectInfoId"
            resultType="com.msun.csm.dao.entity.proj.ProjResearchPlanForChecked">
        select phi.hospital_name as hospitalName,
        su.user_name as leaderName,
        prp.*
        from csm.proj_milestone_task prp
        left join csm.proj_hospital_info phi on prp.hospital_info_id = phi.hospital_info_id
        left join csm.sys_user su on prp.leader_id = su.sys_user_id
        where prp.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        <if test="hospitalInfoId != null and hospitalInfoId != ''">
            and prp.hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT}
        </if>
    </select>

    <update id="updateSourceAndStatusByHospitalIdsAndCode">
        update
        csm.proj_milestone_task
        set result_source_id = #{sourceId},
        complete_status = #{status},
        actual_comp_time = now()
        where
        milestone_node_code = #{code}
        and hospital_info_id in
        <foreach collection="hospitalIds" item="hospitalId" separator="," open="(" close=")">
            #{hospitalId, jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="findByProjectAndCode" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneTask">
        select *
        from csm.proj_milestone_task
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and milestone_node_code = #{code,jdbcType=VARCHAR}
          and is_deleted = 0
    </select>

    <delete id="deleteByKey">
        delete
        from csm.proj_milestone_task
        where proj_milestone_task.milestone_task_id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="updateByCustomInfoId">
        update csm.proj_milestone_task
        set customer_info_id = #{newCustomInfoId}
        where customer_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectByProjectAndCode" resultType="com.msun.csm.dao.entity.proj.ProjMilestoneTask">
        select *
        from csm.proj_milestone_info
        where project_info_id = #{projectInfoId,jdbcType=BIGINT}
          and milestone_node_code = #{code,jdbcType=VARCHAR}
          and invalid_flag = 0
    </select>
</mapper>
