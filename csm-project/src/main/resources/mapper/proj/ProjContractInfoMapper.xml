<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjContractInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_contract_info-->
        <id column="contract_info_id" jdbcType="BIGINT" property="contractInfoId"/>
        <result column="yy_contract_id" jdbcType="BIGINT" property="yyContractId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
        <result column="contract_type" jdbcType="SMALLINT" property="contractType"/>
        <result column="contract_sign_person_id" jdbcType="BIGINT" property="contractSignPersonId"/>
        <result column="contract_sign_team_id" jdbcType="BIGINT" property="contractSignTeamId"/>
        <result column="pay_signage" jdbcType="SMALLINT" property="paySignage"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="yy_contract_custom_id" jdbcType="BIGINT" property="yyContractCustomId"/>
        <result column="contract_presaleteam_id" jdbcType="BIGINT" property="contractPresaleteamId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        contract_info_id, yy_contract_id, contract_no, contract_name, contract_type, contract_sign_person_id,
        contract_sign_team_id, pay_signage, creater_id, create_time, updater_id, update_time,
        is_deleted, yy_contract_custom_id, contract_presaleteam_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_contract_info
        where contract_info_id = #{contractInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_contract_info
        where contract_info_id = #{contractInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_info (contract_info_id, yy_contract_id, contract_no,
        contract_name, contract_type, contract_sign_person_id,
        contract_sign_team_id, pay_signage, creater_id,
        create_time, updater_id, update_time,
        is_deleted, yy_contract_custom_id, contract_presaleteam_id)
        values (#{contractInfoId,jdbcType=BIGINT}, #{yyContractId,jdbcType=BIGINT}, #{contractNo,jdbcType=VARCHAR},
        #{contractName,jdbcType=VARCHAR}, #{contractType,jdbcType=SMALLINT}, #{contractSignPersonId,jdbcType=BIGINT},
        #{contractSignTeamId,jdbcType=BIGINT}, #{paySignage,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{yyContractCustomId,jdbcType=BIGINT}, #{contractPresaleteamId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractInfoId != null">
                contract_info_id,
            </if>
            <if test="yyContractId != null">
                yy_contract_id,
            </if>
            <if test="contractNo != null">
                contract_no,
            </if>
            <if test="contractName != null">
                contract_name,
            </if>
            <if test="contractType != null">
                contract_type,
            </if>
            <if test="contractSignPersonId != null">
                contract_sign_person_id,
            </if>
            <if test="contractSignTeamId != null">
                contract_sign_team_id,
            </if>
            <if test="paySignage != null">
                pay_signage,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="yyContractCustomId != null">
                yy_contract_custom_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractInfoId != null">
                #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyContractId != null">
                #{yyContractId,jdbcType=BIGINT},
            </if>
            <if test="contractNo != null">
                #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="contractName != null">
                #{contractName,jdbcType=VARCHAR},
            </if>
            <if test="contractType != null">
                #{contractType,jdbcType=SMALLINT},
            </if>
            <if test="contractSignPersonId != null">
                #{contractSignPersonId,jdbcType=BIGINT},
            </if>
            <if test="contractSignTeamId != null">
                #{contractSignTeamId,jdbcType=BIGINT},
            </if>
            <if test="paySignage != null">
                #{paySignage,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="yyContractCustomId != null">
                #{yyContractCustomId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        update csm.proj_contract_info
        <set>
            <if test="yyContractId != null">
                yy_contract_id = #{yyContractId,jdbcType=BIGINT},
            </if>
            <if test="contractNo != null">
                contract_no = #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="contractName != null">
                contract_name = #{contractName,jdbcType=VARCHAR},
            </if>
            <if test="contractType != null">
                contract_type = #{contractType,jdbcType=SMALLINT},
            </if>
            <if test="contractSignPersonId != null">
                contract_sign_person_id = #{contractSignPersonId,jdbcType=BIGINT},
            </if>
            <if test="contractSignTeamId != null">
                contract_sign_team_id = #{contractSignTeamId,jdbcType=BIGINT},
            </if>
            <if test="paySignage != null">
                pay_signage = #{paySignage,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="yyContractCustomId != null">
                yy_contract_custom_id = #{yyContractCustomId,jdbcType=BIGINT},
            </if>
        </set>
        where contract_info_id = #{contractInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        update csm.proj_contract_info
        set yy_contract_id = #{yyContractId,jdbcType=BIGINT},
        contract_no = #{contractNo,jdbcType=VARCHAR},
        contract_name = #{contractName,jdbcType=VARCHAR},
        contract_type = #{contractType,jdbcType=SMALLINT},
        contract_sign_person_id = #{contractSignPersonId,jdbcType=BIGINT},
        contract_sign_team_id = #{contractSignTeamId,jdbcType=BIGINT},
        pay_signage = #{paySignage,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        yy_contract_custom_id = #{yyContractCustomId,jdbcType=BIGINT}
        where contract_info_id = #{contractInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_contract_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_contract_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.yyContractId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="contract_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.contractNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contract_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.contractName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="contract_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.contractType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="contract_sign_person_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.contractSignPersonId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="contract_sign_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.contractSignTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="pay_signage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.paySignage,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="yy_contract_custom_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                    #{item.yyContractCustomId,jdbcType=BIGINT}
                </foreach>
            </trim>
        </trim>
        where contract_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.contractInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_contract_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_contract_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyContractId != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.yyContractId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractNo != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.contractNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractName != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.contractName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractType != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.contractType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_sign_person_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractSignPersonId != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.contractSignPersonId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="contract_sign_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contractSignTeamId != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.contractSignTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pay_signage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.paySignage != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.paySignage,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_contract_custom_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyContractCustomId != null">
                        when contract_info_id = #{item.contractInfoId,jdbcType=BIGINT} then
                        #{item.yyContractCustomId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where contract_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.contractInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_contract_info
        (contract_info_id, yy_contract_id, contract_no, contract_name, contract_type, contract_sign_person_id,
        contract_sign_team_id, pay_signage, creater_id, create_time, updater_id, update_time,
        is_deleted, yy_contract_custom_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.contractInfoId,jdbcType=BIGINT}, #{item.yyContractId,jdbcType=BIGINT},
            #{item.contractNo,jdbcType=VARCHAR},
            #{item.contractName,jdbcType=VARCHAR}, #{item.contractType,jdbcType=SMALLINT},
            #{item.contractSignPersonId,jdbcType=BIGINT}, #{item.contractSignTeamId,jdbcType=BIGINT},
            #{item.paySignage,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.yyContractCustomId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_info
        (contract_info_id, yy_contract_id, contract_no, contract_name, contract_type, contract_sign_person_id,
        contract_sign_team_id, pay_signage, creater_id, create_time, updater_id, update_time,
        is_deleted, yy_contract_custom_id)
        values
        (#{contractInfoId,jdbcType=BIGINT}, #{yyContractId,jdbcType=BIGINT}, #{contractNo,jdbcType=VARCHAR},
        #{contractName,jdbcType=VARCHAR}, #{contractType,jdbcType=SMALLINT}, #{contractSignPersonId,jdbcType=BIGINT},
        #{contractSignTeamId,jdbcType=BIGINT}, #{paySignage,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{yyContractCustomId,jdbcType=BIGINT})
        on duplicate key update
        contract_info_id = #{contractInfoId,jdbcType=BIGINT},
        yy_contract_id = #{yyContractId,jdbcType=BIGINT},
        contract_no = #{contractNo,jdbcType=VARCHAR},
        contract_name = #{contractName,jdbcType=VARCHAR},
        contract_type = #{contractType,jdbcType=SMALLINT},
        contract_sign_person_id = #{contractSignPersonId,jdbcType=BIGINT},
        contract_sign_team_id = #{contractSignTeamId,jdbcType=BIGINT},
        pay_signage = #{paySignage,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        yy_contract_custom_id = #{yyContractCustomId,jdbcType=BIGINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        <!--@mbg.generated-->
        insert into csm.proj_contract_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractInfoId != null">
                contract_info_id,
            </if>
            <if test="yyContractId != null">
                yy_contract_id,
            </if>
            <if test="contractNo != null">
                contract_no,
            </if>
            <if test="contractName != null">
                contract_name,
            </if>
            <if test="contractType != null">
                contract_type,
            </if>
            <if test="contractSignPersonId != null">
                contract_sign_person_id,
            </if>
            <if test="contractSignTeamId != null">
                contract_sign_team_id,
            </if>
            <if test="paySignage != null">
                pay_signage,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="yyContractCustomId != null">
                yy_contract_custom_id,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractInfoId != null">
                #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyContractId != null">
                #{yyContractId,jdbcType=BIGINT},
            </if>
            <if test="contractNo != null">
                #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="contractName != null">
                #{contractName,jdbcType=VARCHAR},
            </if>
            <if test="contractType != null">
                #{contractType,jdbcType=SMALLINT},
            </if>
            <if test="contractSignPersonId != null">
                #{contractSignPersonId,jdbcType=BIGINT},
            </if>
            <if test="contractSignTeamId != null">
                #{contractSignTeamId,jdbcType=BIGINT},
            </if>
            <if test="paySignage != null">
                #{paySignage,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="yyContractCustomId != null">
                #{yyContractCustomId,jdbcType=BIGINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="contractInfoId != null">
                contract_info_id = #{contractInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyContractId != null">
                yy_contract_id = #{yyContractId,jdbcType=BIGINT},
            </if>
            <if test="contractNo != null">
                contract_no = #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="contractName != null">
                contract_name = #{contractName,jdbcType=VARCHAR},
            </if>
            <if test="contractType != null">
                contract_type = #{contractType,jdbcType=SMALLINT},
            </if>
            <if test="contractSignPersonId != null">
                contract_sign_person_id = #{contractSignPersonId,jdbcType=BIGINT},
            </if>
            <if test="contractSignTeamId != null">
                contract_sign_team_id = #{contractSignTeamId,jdbcType=BIGINT},
            </if>
            <if test="paySignage != null">
                pay_signage = #{paySignage,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="yyContractCustomId != null">
                yy_contract_custom_id = #{yyContractCustomId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <select id="selectByYyContractId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_contract_info
        where yy_contract_id = #{yyContractId,jdbcType=BIGINT}
        and is_deleted = 0;
    </select>

    <select id="selectContractNoPreSaleId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_contract_info
        where contract_presaleteam_id is null
    </select>
</mapper>
