<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjIssueOperLogMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        <!--@Table csm.proj_issue_oper_log-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="issue_info_id" jdbcType="BIGINT" property="issueInfoId"/>
        <result column="oper_type" jdbcType="SMALLINT" property="operType"/>
        <result column="modify_data" jdbcType="VARCHAR" property="modifyData"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, issue_info_id, oper_type, modify_data, creater_id, create_time, updater_id, update_time,
        is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_issue_oper_log
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_issue_oper_log
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        insert into csm.proj_issue_oper_log (id, issue_info_id, oper_type,
        modify_data, creater_id, create_time,
        updater_id, update_time, is_deleted
        )
        values (#{id,jdbcType=BIGINT}, #{issueInfoId,jdbcType=BIGINT}, #{operType,jdbcType=SMALLINT},
        #{modifyData,jdbcType=VARCHAR}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        insert into csm.proj_issue_oper_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="issueInfoId != null">
                issue_info_id,
            </if>
            <if test="operType != null">
                oper_type,
            </if>
            <if test="modifyData != null">
                modify_data,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="issueInfoId != null">
                #{issueInfoId,jdbcType=BIGINT},
            </if>
            <if test="operType != null">
                #{operType,jdbcType=SMALLINT},
            </if>
            <if test="modifyData != null">
                #{modifyData,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        update csm.proj_issue_oper_log
        <set>
            <if test="issueInfoId != null">
                issue_info_id = #{issueInfoId,jdbcType=BIGINT},
            </if>
            <if test="operType != null">
                oper_type = #{operType,jdbcType=SMALLINT},
            </if>
            <if test="modifyData != null">
                modify_data = #{modifyData,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        update csm.proj_issue_oper_log
        set issue_info_id = #{issueInfoId,jdbcType=BIGINT},
        oper_type = #{operType,jdbcType=SMALLINT},
        modify_data = #{modifyData,jdbcType=VARCHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_issue_oper_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="issue_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.issueInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="oper_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.operType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="modify_data = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.modifyData,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_issue_oper_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="issue_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.issueInfoId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.issueInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="oper_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.operType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modify_data = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modifyData != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.modifyData,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_issue_oper_log
        (id, issue_info_id, oper_type, modify_data, creater_id, create_time, updater_id, update_time,
        is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.issueInfoId,jdbcType=BIGINT}, #{item.operType,jdbcType=SMALLINT},
            #{item.modifyData,jdbcType=VARCHAR}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        insert into csm.proj_issue_oper_log
        (id, issue_info_id, oper_type, modify_data, creater_id, create_time, updater_id, update_time,
        is_deleted)
        values
        (#{id,jdbcType=BIGINT}, #{issueInfoId,jdbcType=BIGINT}, #{operType,jdbcType=SMALLINT},
        #{modifyData,jdbcType=VARCHAR}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
        on duplicate key update
        id = #{id,jdbcType=BIGINT},
        issue_info_id = #{issueInfoId,jdbcType=BIGINT},
        oper_type = #{operType,jdbcType=SMALLINT},
        modify_data = #{modifyData,jdbcType=VARCHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjIssueOperLog">
        <!--@mbg.generated-->
        insert into csm.proj_issue_oper_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="issueInfoId != null">
                issue_info_id,
            </if>
            <if test="operType != null">
                oper_type,
            </if>
            <if test="modifyData != null">
                modify_data,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="issueInfoId != null">
                #{issueInfoId,jdbcType=BIGINT},
            </if>
            <if test="operType != null">
                #{operType,jdbcType=SMALLINT},
            </if>
            <if test="modifyData != null">
                #{modifyData,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="issueInfoId != null">
                issue_info_id = #{issueInfoId,jdbcType=BIGINT},
            </if>
            <if test="operType != null">
                oper_type = #{operType,jdbcType=SMALLINT},
            </if>
            <if test="modifyData != null">
                modify_data = #{modifyData,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
</mapper>
