<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipCheckVsLisMapper">

    <select id="findCheckProgress" parameterType="com.msun.csm.model.dto.ProjEquipSendToCloudDTO"
            resultType="com.msun.csm.model.vo.ProjEquipCheckVsLisVO">
        select pecvl.cloud_hospital_id,
               pecvl.hospital_name,
               pecvl.item_channel,
               pecvl.item_no,
               pecvl.item_name,
               pecvl.state
        from csm.proj_equip_check_vs_lis pecvl
        where pecvl.equip_record_vs_lis_id = #{equipRecordVsProductId}
        <if test='itemChannel != null and itemChannel != "" '>
            and pecvl.item_channel like concat('%', #{itemChannel}, '%')
        </if>
        <if test='cloudHospitalId != null'>
            and pecvl.cloud_hospital_id = #{cloudHospitalId}
        </if>
        <if test='state != null and state != "" '>
            and pecvl.state = #{state}
        </if>
        and pecvl.is_deleted = 0
        order by pecvl.state asc
    </select>
</mapper>