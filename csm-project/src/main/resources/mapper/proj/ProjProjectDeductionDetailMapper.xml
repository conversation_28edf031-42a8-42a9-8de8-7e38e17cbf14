<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProjectDeductionDetailMapper">

    <update id="updateDeductionById" parameterType="com.msun.csm.dao.entity.proj.UpdateDeductionParam">
        update csm.proj_deduction_detail_info
        <set>
            update_time = now(),
            <if test="practicalDeduction != null">
                practical_deduction = #{practicalDeduction},
            </if>
            <if test="estimatedDeduction != null">
                estimated_deduction = #{estimatedDeduction},
            </if>
            <if test="useCount != null">
                use_count = #{useCount},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where proj_deduction_detail_info_id = #{id,jdbcType=BIGINT}
    </update>

    <select id="queryProductFunctionScoreRecord" parameterType="com.msun.csm.model.param.QueryProductFunctionScoreRecordParam" resultType="com.msun.csm.dao.entity.proj.ProductFunctionScoreRecordPO">
        select
            ppdd.proj_deduction_detail_info_id as "id",
<!--            ppdd.custom_info_id ,-->
            ppdd.project_info_id ,
            ppdd.function_code ,
            ppdd.yy_product_id ,
            dp.product_name ,
            dpf.function_name ,
            dpf.function_desc ,
            ppdd.use_count ,
            dpf.tertiary_hospital_flag ,
            dpf.second_hospital_flag ,
            dpf.first_hospital_flag ,
            dpf.maternal_child_hospital_flag ,
            dpf.other_hospital_flag ,
            dpf.common_function_flag ,
            ppdd.estimated_deduction ,
            ppdd.practical_deduction ,
            ppdd.deduction_type as "deductionType",
            ddt."name" as "deductionTypeDesc",
            ppdd.attachment_id as "attachmentId",
            ppdd.remark
        from
            csm.proj_deduction_detail_info ppdd
                left join csm.dict_product dp on ppdd.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
                left join csm.dict_product_function dpf on ppdd.function_code = dpf.function_code and dpf.is_delete = 0
                left join csm.dict_deduction_type ddt on ppdd.deduction_type = ddt.code
        where ppdd.is_deleted = 0
        and ppdd.operation_type = 'function'
          and ppdd.source = #{source,jdbcType=VARCHAR}
        <if test="projectInfoId != null">
            and ppdd.project_info_id = #{projectInfoId,jdbcType=BIGINT}
        </if>
        <if test="classificationCode != null and classificationCode != ''">
            and ppdd.classification_code = #{classificationCode,jdbcType=VARCHAR}
        </if>
        <if test="yyProductId != null">
            and ppdd.yy_product_id = #{yyProductId,jdbcType=BIGINT}
        </if>
        order by dp.order_no ,dpf.function_name
    </select>

    <select id="getFunctionDeductionDetail" resultType="com.msun.csm.dao.entity.proj.ProjProjectDeductionDetail">
        select *
        from csm.proj_deduction_detail_info
        where is_deleted = 0
          and operation_type = 'function'
          and project_info_id = #{projectInfoId}
          and classification_code = #{classificationCode}
          and "source" = #{source}
    </select>

    <!--    <update id="updateByCustomInfoId">-->
<!--        update csm.proj_deduction_detail_info-->
<!--        set custom_info_id = #{newCustomInfoId}-->
<!--        where custom_info_id = #{oldCustomInfoId}-->
<!--        <if test="projectInfoIds != null and projectInfoIds.size() != 0">-->
<!--            and project_info_id in-->
<!--            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </update>-->

</mapper>
