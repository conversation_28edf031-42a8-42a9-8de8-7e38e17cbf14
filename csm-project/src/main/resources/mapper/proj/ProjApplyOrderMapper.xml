<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        <!--@mbg.generated-->
        <!--@Table csm.proj_apply_order-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="apply_num" jdbcType="SMALLINT" property="applyNum"/>
        <result column="apply_type" jdbcType="SMALLINT" property="applyType"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="applicant" jdbcType="VARCHAR" property="applicant"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="customer_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="product_ids" jdbcType="VARCHAR" property="productIds"/>
        <result column="operation_person" jdbcType="VARCHAR" property="operationPerson"/>
        <result column="result_type" jdbcType="VARCHAR" property="resultType"/>
        <result column="resource_file_path" jdbcType="VARCHAR" property="resourceFilePath"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, apply_num, apply_type, apply_time, applicant, project_info_id, custom_info_id,
        product_ids, operation_person, result_type, resource_file_path, is_deleted, creater_id,
        create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_apply_order
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="findProjApplyOrderInfoList" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrderRelative"
            parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        select ses.create_time "deliveryTime",t2.*, t3.product_names from csm.proj_apply_order t2
        left join
        (select string_agg(t1.product_name::text,'，') as product_names,t.apply_order_id
        from csm.proj_apply_order_product_record t
        left join csm.dict_product t1 on t1.yy_product_id = t.product_dict_id
        where t.product_type = 1 and t.is_deleted = 0
        group by t.apply_order_id) t3 on t3.apply_order_id = t2.id
        left join (
        select create_time,apply_order_id from csm.proj_apply_order_node_record where is_deleted = 0 and node_type_code = '5'
        ) ses on ses.apply_order_id = t2.id
        <where>
            <if test="projectInfoId != null">
                and t2.project_info_id = #{projectInfoId, jdbcType=BIGINT}
            </if>
            <if test="customInfoId != null">
                and t2.custom_info_id = #{customInfoId, jdbcType=BIGINT}
            </if>
            and t2.is_deleted = 0
        </where>
        order by t2.apply_num desc
    </select>
    <select id="getApplyOrderByCustomInfoId" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrder"
            parameterType="java.lang.String">
        select *
        from csm.proj_apply_order
        where custom_info_id = #{customInfoId, jdbcType=BIGINT}
    </select>
    <select id="findDeployEvnByCustomInfoId" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        SELECT T.*
        FROM CSM.PROJ_APPLY_ORDER T
        WHERE RESULT_TYPE IN (5, 7)
          AND T.IS_DELETED = 0
          AND T.apply_type = 1
          AND T.CUSTOM_INFO_ID = #{customInfoId,jdbcType=BIGINT}
    </select>
    <select id="selectOrderProductByOrderType" resultType="com.msun.csm.dao.entity.proj.ProjOrderProduct">

    </select>
    <select id="findCloudServiceInfo" resultType="com.msun.csm.dao.entity.proj.ProjCustomCloudService"
            parameterType="java.lang.Long">

    </select>
    <select id="selectByReminder" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        select distinct earlywarn_time,su.sys_user_id "deployUserId", cron,pao.*  from csm.proj_apply_order pao
                                                                                           left join csm.config_time_earlywarning cte on pao.apply_type = cte.environmental_type
            and cte.earlywarn_type = 'audit'
                                                                                           left join csm.sys_user su on pao.deploy_user_name = su.user_name and su.is_deleted = 0
        where pao.is_deleted = 0
          and pao.result_type = 0
          and pao.apply_type in (1,2)
          and  pao.apply_time + earlywarn_time * (interval '1 hour') &lt; CURRENT_TIMESTAMP
    </select>
    <select id="selectByReminderTimeOut" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        select distinct earlywarn_time,su.sys_user_id "deployUserId", cte.cron,pao.*  from csm.proj_apply_order pao
            left join csm.config_time_earlywarning cte on pao.cloud_env = cte.cloud_resource_type
            and COALESCE(pao.is_local_room, 0) = cte.local_room_flag
            and COALESCE(pao.first_deploy_node, 0) = cte.first_deployment_flag
            and cte.earlywarn_type = 'deploy'
            and (case when pao.apply_type = 2 then 3
                      when pao.apply_type = 1 and (COALESCE(pao.first_deploy_node, 0)=1 or COALESCE(pao.is_local_room, 0) = 1) then 1
                      when pao.apply_type = 1 and COALESCE(pao.first_deploy_node, 0)=0 and COALESCE(pao.is_local_room, 0) = 0 then 2
                      else 3 end ) = cte.environmental_type
             left join csm.proj_apply_order_node_record paonr on paonr.node_type_code = 1 and paonr.apply_order_id = pao.id
            and paonr.is_deleted  = 0
            left join csm.sys_user su on pao.deploy_user_name || pao.deploy_phone  = su.user_name ||su.phone  and su.is_deleted = 0
        where pao.is_deleted = 0
          and pao.result_type in (1,3,4)
          and pao.apply_type in (1,2)
          and  paonr.create_time + earlywarn_time * (interval '1 hour') &lt; CURRENT_TIMESTAMP
    </select>
    <select id="selectByReminderTimeWarning" resultType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        select distinct earlywarn_time,su.sys_user_id "deployUserId", cte.cron,pao.*  from csm.proj_apply_order pao
            left join csm.config_time_earlywarning cte on pao.cloud_env = cte.cloud_resource_type
            and COALESCE(pao.is_local_room, 0) = cte.local_room_flag
            and COALESCE(pao.first_deploy_node, 0) = cte.first_deployment_flag
            and cte.earlywarn_type = 'deploy'
            and (case when pao.apply_type = 2 then 3
                      when pao.apply_type = 1 and (COALESCE(pao.first_deploy_node, 0)=1 or COALESCE(pao.is_local_room, 0) = 1) then 1
                      when pao.apply_type = 1 and COALESCE(pao.first_deploy_node, 0)=0 and COALESCE(pao.is_local_room, 0) = 0 then 2
                      else 3 end ) = cte.environmental_type
                                                                                               left join csm.proj_apply_order_node_record paonr on paonr.node_type_code = 1 and paonr.apply_order_id = pao.id
            and paonr.is_deleted  = 0
            left join csm.sys_user su on pao.deploy_user_name || pao.deploy_phone  = su.user_name ||su.phone  and su.is_deleted = 0
        where pao.is_deleted = 0
          and pao.result_type in (1,3,4)
          and pao.apply_type in (1,2)
            and  paonr.create_time  &lt; CURRENT_TIMESTAMP + earlywarn_time * (interval '1 hour')
            and  paonr.create_time > CURRENT_TIMESTAMP +  earlywarn_time * (interval '1 hour')- (interval '0.5 hour')

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_apply_order
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <!--    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">-->
    <!--        &lt;!&ndash;@mbg.generated&ndash;&gt;-->
    <!--        insert into csm.proj_apply_order (id, apply_num, apply_type,-->
    <!--        apply_time, applicant, project_info_id,-->
    <!--        custom_info_id, product_ids,-->
    <!--        operation_person, result_type, resource_file_path,-->
    <!--        is_deleted, creater_id, create_time,-->
    <!--        updater_id, update_time)-->
    <!--        values (#{id,jdbcType=BIGINT}, #{applyNum,jdbcType=SMALLINT}, #{applyType,jdbcType=SMALLINT},-->
    <!--        #{applyTime,jdbcType=TIMESTAMP}, #{applicant,jdbcType=VARCHAR}, #{projectInfoId,jdbcType=BIGINT},-->
    <!--        #{customInfoId,jdbcType=BIGINT}, #{productIds,jdbcType=VARCHAR},-->
    <!--        #{operationPerson,jdbcType=VARCHAR}, #{resultType,jdbcType=VARCHAR}, #{resourceFilePath,jdbcType=VARCHAR},-->
    <!--        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},-->
    <!--        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})-->
    <!--    </insert>-->
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        <!--@mbg.generated-->
        insert into csm.proj_apply_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applyNum != null">
                apply_num,
            </if>
            <if test="applyType != null">
                apply_type,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="applicant != null">
                applicant,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="customerInfoId != null">
                customer_info_id,
            </if>
            <if test="productIds != null">
                product_ids,
            </if>
            <if test="operationPerson != null">
                operation_person,
            </if>
            <if test="resultType != null">
                result_type,
            </if>
            <if test="resourceFilePath != null">
                resource_file_path,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="applyNum != null">
                #{applyNum,jdbcType=SMALLINT},
            </if>
            <if test="applyType != null">
                #{applyType,jdbcType=SMALLINT},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applicant != null">
                #{applicant,jdbcType=VARCHAR},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="customerInfoId != null">
                #{customerInfoId,jdbcType=BIGINT},
            </if>
            <if test="productIds != null">
                #{productIds,jdbcType=VARCHAR},
            </if>
            <if test="operationPerson != null">
                #{operationPerson,jdbcType=VARCHAR},
            </if>
            <if test="resultType != null">
                #{resultType,jdbcType=VARCHAR},
            </if>
            <if test="resourceFilePath != null">
                #{resourceFilePath,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        <!--@mbg.generated-->
        update csm.proj_apply_order
        <set>
            <if test="applyNum != null">
                apply_num = #{applyNum,jdbcType=SMALLINT},
            </if>
            <if test="applyType != null">
                apply_type = #{applyType,jdbcType=SMALLINT},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applicant != null">
                applicant = #{applicant,jdbcType=VARCHAR},
            </if>
            <if test="projectInfoId != null">
                project_inf_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=BIGINT},
            </if>
            <if test="customerInfoId != null">
                customer_info_id = #{customerInfoId,jdbcType=BIGINT},
            </if>
            <if test="productIds != null">
                product_ids = #{productIds,jdbcType=VARCHAR},
            </if>
            <if test="operationPerson != null">
                operation_person = #{operationPerson,jdbcType=VARCHAR},
            </if>
            <if test="resultType != null">
                result_type = #{resultType,jdbcType=VARCHAR},
            </if>
            <if test="resourceFilePath != null">
                resource_file_path = #{resourceFilePath,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        <!--@mbg.generated-->
        update csm.proj_apply_order
        set apply_num = #{applyNum,jdbcType=SMALLINT},
        apply_type = #{applyType,jdbcType=SMALLINT},
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
        applicant = #{applicant,jdbcType=VARCHAR},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        customer_id = #{customerId,jdbcType=BIGINT},
        customer_info_id = #{customerInfoId,jdbcType=BIGINT},
        product_ids = #{productIds,jdbcType=VARCHAR},
        operation_person = #{operationPerson,jdbcType=VARCHAR},
        result_type = #{resultType,jdbcType=VARCHAR},
        resource_file_path = #{resourceFilePath,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_apply_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="apply_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyNum,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="apply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="apply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="applicant = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.applicant,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.customerId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="customer_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.customerInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.productIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_person = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.operationPerson,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="result_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.resultType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_file_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.resourceFilePath,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_apply_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="apply_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyNum != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyNum,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applyTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="applicant = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applicant != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.applicant,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customerId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.customerId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="customer_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customerInfoId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.customerInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productIds != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.productIds,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="operation_person = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operationPerson != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.operationPerson,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="result_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.resultType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.resultType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="resource_file_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.resourceFilePath != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.resourceFilePath,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateDeleteByProductRecordIdList" parameterType="java.util.List">
        update csm.proj_apply_order_product_record set is_deleted = 0
        where
        proj_apply_order_product_record_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_apply_order
        (id, apply_num, apply_type, apply_time, applicant, project_info_id, customer_id, customer_info_id,
        product_ids, operation_person, result_type, resource_file_path, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.applyNum,jdbcType=SMALLINT}, #{item.applyType,jdbcType=SMALLINT},
            #{item.applyTime,jdbcType=TIMESTAMP}, #{item.applicant,jdbcType=VARCHAR},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.customerId,jdbcType=BIGINT}, #{item.customerInfoId,jdbcType=BIGINT},
            #{item.productIds,jdbcType=VARCHAR},
            #{item.operationPerson,jdbcType=VARCHAR}, #{item.resultType,jdbcType=VARCHAR},
            #{item.resourceFilePath,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        <!--@mbg.generated-->
        insert into csm.proj_apply_order
        (id, apply_num, apply_type, apply_time, applicant, project_info_id, customer_id, customer_info_id,
        product_ids, operation_person, result_type, resource_file_path, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        (#{id,jdbcType=BIGINT}, #{applyNum,jdbcType=SMALLINT}, #{applyType,jdbcType=SMALLINT},
        #{applyTime,jdbcType=TIMESTAMP}, #{applicant,jdbcType=VARCHAR}, #{projectInfoId,jdbcType=BIGINT},
        #{customerId,jdbcType=BIGINT}, #{customerInfoId,jdbcType=BIGINT}, #{productIds,jdbcType=VARCHAR},
        #{operationPerson,jdbcType=VARCHAR}, #{resultType,jdbcType=VARCHAR}, #{resourceFilePath,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        id = #{id,jdbcType=BIGINT},
        apply_num = #{applyNum,jdbcType=SMALLINT},
        apply_type = #{applyType,jdbcType=SMALLINT},
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
        applicant = #{applicant,jdbcType=VARCHAR},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        customer_id = #{customerId,jdbcType=BIGINT},
        customer_info_id = #{customerInfoId,jdbcType=BIGINT},
        product_ids = #{productIds,jdbcType=VARCHAR},
        operation_person = #{operationPerson,jdbcType=VARCHAR},
        result_type = #{resultType,jdbcType=VARCHAR},
        resource_file_path = #{resourceFilePath,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjApplyOrder">
        <!--@mbg.generated-->
        insert into csm.proj_apply_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="applyNum != null">
                apply_num,
            </if>
            <if test="applyType != null">
                apply_type,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="applicant != null">
                applicant,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="customerInfoId != null">
                customer_info_id,
            </if>
            <if test="productIds != null">
                product_ids,
            </if>
            <if test="operationPerson != null">
                operation_person,
            </if>
            <if test="resultType != null">
                result_type,
            </if>
            <if test="resourceFilePath != null">
                resource_file_path,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="applyNum != null">
                #{applyNum,jdbcType=SMALLINT},
            </if>
            <if test="applyType != null">
                #{applyType,jdbcType=SMALLINT},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applicant != null">
                #{applicant,jdbcType=VARCHAR},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                #{customerId,jdbcType=BIGINT},
            </if>
            <if test="customerInfoId != null">
                #{customerInfoId,jdbcType=BIGINT},
            </if>
            <if test="productIds != null">
                #{productIds,jdbcType=VARCHAR},
            </if>
            <if test="operationPerson != null">
                #{operationPerson,jdbcType=VARCHAR},
            </if>
            <if test="resultType != null">
                #{resultType,jdbcType=VARCHAR},
            </if>
            <if test="resourceFilePath != null">
                #{resourceFilePath,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="applyNum != null">
                apply_num = #{applyNum,jdbcType=SMALLINT},
            </if>
            <if test="applyType != null">
                apply_type = #{applyType,jdbcType=SMALLINT},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="applicant != null">
                applicant = #{applicant,jdbcType=VARCHAR},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=BIGINT},
            </if>
            <if test="customerInfoId != null">
                customer_info_id = #{customerInfoId,jdbcType=BIGINT},
            </if>
            <if test="productIds != null">
                product_ids = #{productIds,jdbcType=VARCHAR},
            </if>
            <if test="operationPerson != null">
                operation_person = #{operationPerson,jdbcType=VARCHAR},
            </if>
            <if test="resultType != null">
                result_type = #{resultType,jdbcType=VARCHAR},
            </if>
            <if test="resourceFilePath != null">
                resource_file_path = #{resourceFilePath,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByCustomInfoId">
        update csm.proj_apply_order
        set custom_info_id = #{newCustomInfoId, jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId, jdbcType=BIGINT}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
