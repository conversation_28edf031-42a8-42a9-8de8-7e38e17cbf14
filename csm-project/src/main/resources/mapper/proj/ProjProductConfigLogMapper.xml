<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjProductConfigLogMapper">

    <insert id="insertBatch">
        insert into csm.proj_product_config_log (product_config_log_id,
        project_info_id,
        hospital_info_id,
        yy_product_id,
        config_name,
        config_code,
        config_value_old,
        config_value_new,
        config_status,
        creater_id,
        create_time,
        updater_id,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productConfigLogId}, #{item.projectInfoId}, #{item.hospitalInfoId}, #{item.yyProductId},
            #{item.configName}, #{item.configCode}, #{item.configValueOld}, #{item.configValueNew},
            #{item.configStatus}, #{item.createrId}, #{item.createTime}, #{item.updaterId}, #{item.updateTime})
        </foreach>
    </insert>

    <select id="selectLogList" parameterType="com.msun.csm.dao.entity.proj.ProjProductConfigLog"
            resultType="com.msun.csm.model.vo.ProjProductConfigLogVO">
        select ppcl.*,
               su2.user_name as "creater_name",
               su.user_name  as "update_name"

        from csm.proj_product_config_log ppcl
                 left join csm.sys_user su on
                    ppcl.updater_id = su.sys_user_id
                and su.is_deleted = 0
                 left join csm.sys_user su2 on
                    ppcl.creater_id = su2.sys_user_id
                and su2.is_deleted = 0
        where ppcl.project_info_id = #{projectInfoId}
          and ppcl.hospital_info_id = #{hospitalInfoId}
          and ppcl.yy_product_id = #{yyProductId}
        order by ppcl.update_time desc
    </select>

    <delete id="deleteByParam">
        delete
        from csm.proj_product_config_log
        where project_info_id = #{projectInfoId}
          and hospital_info_id = #{hospitalInfoId}
          and yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </delete>

    <update id="updateByProjectId">
        update
            csm.proj_product_config_log
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() > 0">
            and yy_product_id in
            <foreach collection="changeProductList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
