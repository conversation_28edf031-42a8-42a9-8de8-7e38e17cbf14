<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjUpdateDataLogMapper">

    <select id="selectByCondition" resultType="com.msun.csm.dao.entity.proj.ProjUpdateDataLog">
        select
            log.*,
            su.user_name as "operateName"
        from csm.proj_update_data_log log
        left join csm.sys_user su on log.creater_id = su.sys_user_id
        where 1 = 1
        <if test="operateType != null">
            and log.operate_type = #{operateType}
        </if>
        order by log.create_time desc
    </select>
</mapper>
