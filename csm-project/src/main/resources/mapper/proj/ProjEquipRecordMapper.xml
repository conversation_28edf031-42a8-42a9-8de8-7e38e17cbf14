<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjEquipRecord">
        <!--@mbg.generated-->
        <!--@Table csm.proj_equip_record-->
        <id column="equip_record_id" jdbcType="BIGINT" property="equipRecordId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="project_info_id" jdbcType="BIGINT" property="projectInfoId"/>
        <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="equip_class_id" jdbcType="BIGINT" property="equipClassId"/>
        <result column="equip_class_name" jdbcType="VARCHAR" property="equipClassName"/>
        <result column="equip_factory_id" jdbcType="BIGINT" property="equipFactoryId"/>
        <result column="equip_factory_name" jdbcType="VARCHAR" property="equipFactoryName"/>
        <result column="equip_type_id" jdbcType="BIGINT" property="equipTypeId"/>
        <result column="equip_type_name" jdbcType="VARCHAR" property="equipTypeName"/>
        <result column="equip_info_id" jdbcType="BIGINT" property="equipInfoId"/>
        <result column="equip_model_name" jdbcType="VARCHAR" property="equipModelName"/>
        <result column="required_flag" jdbcType="SMALLINT" property="requiredFlag"/>
        <result column="stop_reason" jdbcType="VARCHAR" property="stopReason"/>
        <result column="equip_status" jdbcType="SMALLINT" property="equipStatus"/>
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason"/>
        <result column="equip_position" jdbcType="VARCHAR" property="equipPosition"/>
        <result column="equip_factory_phone" jdbcType="VARCHAR" property="equipFactoryPhone"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="comm_mode" jdbcType="VARCHAR" property="commMode"/>
        <result column="comm_mode_key" jdbcType="VARCHAR" property="commModeKey"/>
        <result column="test_progress" jdbcType="VARCHAR" property="testProgress"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="test_pass_time" jdbcType="TIMESTAMP" property="testPassTime"/>
        <result column="cloud_equip_id" jdbcType="BIGINT" property="cloudEquipId"/>
        <result column="cloud_equip_name" jdbcType="VARCHAR" property="cloudEquipName"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        equip_record_id, custom_info_id, project_info_id, hospital_info_id, yy_product_id,
        equip_class_id, equip_class_name, equip_factory_id, equip_factory_name, equip_type_id,
        equip_type_name, equip_info_id, equip_model_name, required_flag, stop_reason, equip_status,
        reject_reason, equip_position, equip_factory_phone, memo, comm_mode, comm_mode_key, test_progress,
        apply_time, test_pass_time, cloud_equip_id, cloud_equip_name, is_deleted, creater_id,
        create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_equip_record
        where equip_record_id = #{equipRecordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_equip_record
        where equip_record_id = #{equipRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecord">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipRecordId != null">
                equip_record_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="equipClassId != null">
                equip_class_id,
            </if>
            <if test="equipClassName != null">
                equip_class_name,
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id,
            </if>
            <if test="equipFactoryName != null">
                equip_factory_name,
            </if>
            <if test="equipTypeId != null">
                equip_type_id,
            </if>
            <if test="equipTypeName != null">
                equip_type_name,
            </if>
            <if test="equipInfoId != null">
                equip_info_id,
            </if>
            <if test="equipModelName != null">
                equip_model_name,
            </if>
            <if test="requiredFlag != null">
                required_flag,
            </if>
            <if test="stopReason != null">
                stop_reason,
            </if>
            <if test="equipStatus != null">
                equip_status,
            </if>
            <if test="rejectReason != null">
                reject_reason,
            </if>
            <if test="equipPosition != null">
                equip_position,
            </if>
            <if test="equipFactoryPhone != null">
                equip_factory_phone,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="commMode != null">
                comm_mode,
            </if>
            <if test="commModekey != null">
                comm_mode_key,
            </if>
            <if test="testProgress != null">
                test_progress,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="testPassTime != null">
                test_pass_time,
            </if>
            <if test="cloudEquipId != null">
                cloud_equip_id,
            </if>
            <if test="cloudEquipName != null">
                cloud_equip_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipRecordId != null">
                #{equipRecordId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipClassName != null">
                #{equipClassName,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryId != null">
                #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryName != null">
                #{equipFactoryName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipInfoId != null">
                #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="requiredFlag != null">
                #{requiredFlag,jdbcType=SMALLINT},
            </if>
            <if test="stopReason != null">
                #{stopReason,jdbcType=VARCHAR},
            </if>
            <if test="equipStatus != null">
                #{equipStatus,jdbcType=SMALLINT},
            </if>
            <if test="rejectReason != null">
                #{rejectReason,jdbcType=VARCHAR},
            </if>
            <if test="equipPosition != null">
                #{equipPosition,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryPhone != null">
                #{equipFactoryPhone,jdbcType=VARCHAR},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="commMode != null">
                #{commMode,jdbcType=VARCHAR},
            </if>
            <if test="commModeKey != null">
                #{commModeKey,jdbcType=VARCHAR},
            </if>
            <if test="testProgress != null">
                #{testProgress,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="testPassTime != null">
                #{testPassTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cloudEquipId != null">
                #{cloudEquipId,jdbcType=BIGINT},
            </if>
            <if test="cloudEquipName != null">
                #{cloudEquipName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecord">
        <!--@mbg.generated-->
        update csm.proj_equip_record
        <set>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                equip_class_id = #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipClassName != null">
                equip_class_name = #{equipClassName,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryName != null">
                equip_factory_name = #{equipFactoryName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                equip_type_id = #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipInfoId != null">
                equip_info_id = #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                equip_model_name = #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="requiredFlag != null">
                required_flag = #{requiredFlag,jdbcType=SMALLINT},
            </if>
            <if test="stopReason != null">
                stop_reason = #{stopReason,jdbcType=VARCHAR},
            </if>
            <if test="equipStatus != null">
                equip_status = #{equipStatus,jdbcType=SMALLINT},
            </if>
            <if test="rejectReason != null">
                reject_reason = #{rejectReason,jdbcType=VARCHAR},
            </if>
            <if test="equipPosition != null">
                equip_position = #{equipPosition,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryPhone != null">
                equip_factory_phone = #{equipFactoryPhone,jdbcType=VARCHAR},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="commMode != null">
                comm_mode = #{commMode,jdbcType=VARCHAR},
            </if>
            <if test="commModeKey != null">
                comm_mode_key = #{commModeKey,jdbcType=VARCHAR},
            </if>
            <if test="testProgress != null">
                test_progress = #{testProgress,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="testPassTime != null">
                test_pass_time = #{testPassTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cloudEquipId != null">
                cloud_equip_id = #{cloudEquipId,jdbcType=BIGINT},
            </if>
            <if test="cloudEquipName != null">
                cloud_equip_name = #{cloudEquipName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where equip_record_id = #{equipRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecord">
        <!--@mbg.generated-->
        update csm.proj_equip_record
        set custom_info_id = #{customInfoId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
        equip_class_id = #{equipClassId,jdbcType=BIGINT},
        equip_class_name = #{equipClassName,jdbcType=VARCHAR},
        equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
        equip_factory_name = #{equipFactoryName,jdbcType=VARCHAR},
        equip_type_id = #{equipTypeId,jdbcType=BIGINT},
        equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
        equip_info_id = #{equipInfoId,jdbcType=BIGINT},
        equip_model_name = #{equipModelName,jdbcType=VARCHAR},
        required_flag = #{requiredFlag,jdbcType=SMALLINT},
        stop_reason = #{stopReason,jdbcType=VARCHAR},
        equip_status = #{equipStatus,jdbcType=SMALLINT},
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
        equip_position = #{equipPosition,jdbcType=VARCHAR},
        equip_factory_phone = #{equipFactoryPhone,jdbcType=VARCHAR},
        memo = #{memo,jdbcType=VARCHAR},
        comm_mode = #{commMode,jdbcType=VARCHAR},
        comm_mode_key = #{commModeKey,jdbcType=VARCHAR},
        test_progress = #{testProgress,jdbcType=VARCHAR},
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
        test_pass_time = #{testPassTime,jdbcType=TIMESTAMP},
        cloud_equip_id = #{cloudEquipId,jdbcType=BIGINT},
        cloud_equip_name = #{cloudEquipName,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where equip_record_id = #{equipRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_equip_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.projectInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.hospitalInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.yyProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_class_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipClassId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_class_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipClassName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_factory_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipFactoryId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_factory_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipFactoryName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipTypeId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_type_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipTypeName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_model_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipModelName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="required_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.requiredFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="stop_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.stopReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="reject_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.rejectReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipPosition,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_factory_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.equipFactoryPhone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="memo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="comm_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then #{item.commMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="comm_mode_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.commModeKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="test_progress = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.testProgress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="apply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.applyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="test_pass_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.testPassTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="cloud_equip_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.cloudEquipId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="cloud_equip_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.cloudEquipName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where equip_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.equipRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_equip_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectInfoId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.projectInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalInfoId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.hospitalInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProductId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.yyProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_class_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipClassId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipClassId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_class_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipClassName != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipClassName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_factory_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipFactoryId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipFactoryId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_factory_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipFactoryName != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipFactoryName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipTypeId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipTypeId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_type_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipTypeName != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipTypeName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipInfoId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_model_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipModelName != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipModelName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="required_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.requiredFlag != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.requiredFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="stop_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.stopReason != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.stopReason,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipStatus != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="reject_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.rejectReason != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.rejectReason,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_position = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipPosition != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipPosition,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_factory_phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipFactoryPhone != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.equipFactoryPhone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="memo = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.memo != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="comm_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.commMode != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.commMode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="comm_mode_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.commModeKey != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.commModeKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="test_progress = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.testProgress != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.testProgress,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="apply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.applyTime != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.applyTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="test_pass_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.testPassTime != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.testPassTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cloud_equip_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cloudEquipId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.cloudEquipId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cloud_equip_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cloudEquipName != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.cloudEquipName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when equip_record_id = #{item.equipRecordId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where equip_record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.equipRecordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record
        (equip_record_id, custom_info_id, project_info_id, hospital_info_id, yy_product_id,
        equip_class_id, equip_class_name, equip_factory_id, equip_factory_name, equip_type_id,
        equip_type_name, equip_info_id, equip_model_name, required_flag, stop_reason, equip_status,
        reject_reason, equip_position, equip_factory_phone, memo, comm_mode, comm_mode_key, test_progress,
        apply_time, test_pass_time, cloud_equip_id, cloud_equip_name, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.equipRecordId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.projectInfoId,jdbcType=BIGINT},
            #{item.hospitalInfoId,jdbcType=BIGINT}, #{item.yyProductId,jdbcType=BIGINT},
            #{item.equipClassId,jdbcType=BIGINT},
            #{item.equipClassName,jdbcType=VARCHAR}, #{item.equipFactoryId,jdbcType=BIGINT},
            #{item.equipFactoryName,jdbcType=VARCHAR}, #{item.equipTypeId,jdbcType=BIGINT},
            #{item.equipTypeName,jdbcType=VARCHAR}, #{item.equipInfoId,jdbcType=BIGINT},
            #{item.equipModelName,jdbcType=VARCHAR},
            #{item.requiredFlag,jdbcType=SMALLINT}, #{item.stopReason,jdbcType=VARCHAR},
            #{item.equipStatus,jdbcType=SMALLINT},
            #{item.rejectReason,jdbcType=VARCHAR}, #{item.equipPosition,jdbcType=VARCHAR},
            #{item.equipFactoryPhone,jdbcType=VARCHAR}, #{item.memo,jdbcType=VARCHAR},
            #{item.commMode,jdbcType=VARCHAR},
            #{item.commModeKey,jdbcType=VARCHAR},
            #{item.testProgress,jdbcType=VARCHAR}, #{item.applyTime,jdbcType=TIMESTAMP},
            #{item.testPassTime,jdbcType=TIMESTAMP},
            #{item.cloudEquipId,jdbcType=BIGINT}, #{item.cloudEquipName,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecord">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record
        (equip_record_id, custom_info_id, project_info_id, hospital_info_id, yy_product_id,
        equip_class_id, equip_class_name, equip_factory_id, equip_factory_name, equip_type_id,
        equip_type_name, equip_info_id, equip_model_name, required_flag, stop_reason, equip_status,
        reject_reason, equip_position, equip_factory_phone, memo, comm_mode, comm_mode_key, test_progress,
        apply_time, test_pass_time, cloud_equip_id, cloud_equip_name, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        (#{equipRecordId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{projectInfoId,jdbcType=BIGINT},
        #{hospitalInfoId,jdbcType=BIGINT}, #{yyProductId,jdbcType=BIGINT}, #{equipClassId,jdbcType=BIGINT},
        #{equipClassName,jdbcType=VARCHAR}, #{equipFactoryId,jdbcType=BIGINT}, #{equipFactoryName,jdbcType=VARCHAR},
        #{equipTypeId,jdbcType=BIGINT}, #{equipTypeName,jdbcType=VARCHAR}, #{equipInfoId,jdbcType=BIGINT},
        #{equipModelName,jdbcType=VARCHAR}, #{requiredFlag,jdbcType=SMALLINT}, #{stopReason,jdbcType=VARCHAR},
        #{equipStatus,jdbcType=SMALLINT}, #{rejectReason,jdbcType=VARCHAR}, #{equipPosition,jdbcType=VARCHAR},
        #{equipFactoryPhone,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, #{commMode,jdbcType=VARCHAR},
        #{commModeKey,jdbcType=VARCHAR},
        #{testProgress,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, #{testPassTime,jdbcType=TIMESTAMP},
        #{cloudEquipId,jdbcType=BIGINT}, #{cloudEquipName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        equip_record_id = #{equipRecordId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        project_info_id = #{projectInfoId,jdbcType=BIGINT},
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
        equip_class_id = #{equipClassId,jdbcType=BIGINT},
        equip_class_name = #{equipClassName,jdbcType=VARCHAR},
        equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
        equip_factory_name = #{equipFactoryName,jdbcType=VARCHAR},
        equip_type_id = #{equipTypeId,jdbcType=BIGINT},
        equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
        equip_info_id = #{equipInfoId,jdbcType=BIGINT},
        equip_model_name = #{equipModelName,jdbcType=VARCHAR},
        required_flag = #{requiredFlag,jdbcType=SMALLINT},
        stop_reason = #{stopReason,jdbcType=VARCHAR},
        equip_status = #{equipStatus,jdbcType=SMALLINT},
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
        equip_position = #{equipPosition,jdbcType=VARCHAR},
        equip_factory_phone = #{equipFactoryPhone,jdbcType=VARCHAR},
        memo = #{memo,jdbcType=VARCHAR},
        comm_mode = #{commMode,jdbcType=VARCHAR},
        comm_mode_key = #{commModeKey,jdbcType=VARCHAR},
        test_progress = #{testProgress,jdbcType=VARCHAR},
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
        test_pass_time = #{testPassTime,jdbcType=TIMESTAMP},
        cloud_equip_id = #{cloudEquipId,jdbcType=BIGINT},
        cloud_equip_name = #{cloudEquipName,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjEquipRecord">
        <!--@mbg.generated-->
        insert into csm.proj_equip_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipRecordId != null">
                equip_record_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="projectInfoId != null">
                project_info_id,
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="equipClassId != null">
                equip_class_id,
            </if>
            <if test="equipClassName != null">
                equip_class_name,
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id,
            </if>
            <if test="equipFactoryName != null">
                equip_factory_name,
            </if>
            <if test="equipTypeId != null">
                equip_type_id,
            </if>
            <if test="equipTypeName != null">
                equip_type_name,
            </if>
            <if test="equipInfoId != null">
                equip_info_id,
            </if>
            <if test="equipModelName != null">
                equip_model_name,
            </if>
            <if test="requiredFlag != null">
                required_flag,
            </if>
            <if test="stopReason != null">
                stop_reason,
            </if>
            <if test="equipStatus != null">
                equip_status,
            </if>
            <if test="rejectReason != null">
                reject_reason,
            </if>
            <if test="equipPosition != null">
                equip_position,
            </if>
            <if test="equipFactoryPhone != null">
                equip_factory_phone,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="commMode != null">
                comm_mode,
            </if>
            <if test="commModeKey != null">
                comm_mode_key,
            </if>
            <if test="testProgress != null">
                test_progress,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="testPassTime != null">
                test_pass_time,
            </if>
            <if test="cloudEquipId != null">
                cloud_equip_id,
            </if>
            <if test="cloudEquipName != null">
                cloud_equip_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipRecordId != null">
                #{equipRecordId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipClassName != null">
                #{equipClassName,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryId != null">
                #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryName != null">
                #{equipFactoryName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipInfoId != null">
                #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="requiredFlag != null">
                #{requiredFlag,jdbcType=SMALLINT},
            </if>
            <if test="stopReason != null">
                #{stopReason,jdbcType=VARCHAR},
            </if>
            <if test="equipStatus != null">
                #{equipStatus,jdbcType=SMALLINT},
            </if>
            <if test="rejectReason != null">
                #{rejectReason,jdbcType=VARCHAR},
            </if>
            <if test="equipPosition != null">
                #{equipPosition,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryPhone != null">
                #{equipFactoryPhone,jdbcType=VARCHAR},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="commMode != null">
                #{commMode,jdbcType=VARCHAR},
            </if>
            <if test="commModeKey != null">
                #{commModekey,jdbcType=VARCHAR},
            </if>
            <if test="testProgress != null">
                #{testProgress,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="testPassTime != null">
                #{testPassTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cloudEquipId != null">
                #{cloudEquipId,jdbcType=BIGINT},
            </if>
            <if test="cloudEquipName != null">
                #{cloudEquipName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="equipRecordId != null">
                equip_record_id = #{equipRecordId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="projectInfoId != null">
                project_info_id = #{projectInfoId,jdbcType=BIGINT},
            </if>
            <if test="hospitalInfoId != null">
                hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                equip_class_id = #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipClassName != null">
                equip_class_name = #{equipClassName,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryName != null">
                equip_factory_name = #{equipFactoryName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                equip_type_id = #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipInfoId != null">
                equip_info_id = #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                equip_model_name = #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="requiredFlag != null">
                required_flag = #{requiredFlag,jdbcType=SMALLINT},
            </if>
            <if test="stopReason != null">
                stop_reason = #{stopReason,jdbcType=VARCHAR},
            </if>
            <if test="equipStatus != null">
                equip_status = #{equipStatus,jdbcType=SMALLINT},
            </if>
            <if test="rejectReason != null">
                reject_reason = #{rejectReason,jdbcType=VARCHAR},
            </if>
            <if test="equipPosition != null">
                equip_position = #{equipPosition,jdbcType=VARCHAR},
            </if>
            <if test="equipFactoryPhone != null">
                equip_factory_phone = #{equipFactoryPhone,jdbcType=VARCHAR},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="commMode != null">
                comm_mode = #{commMode,jdbcType=VARCHAR},
            </if>
            <if test="commModeKey != null">
                comm_mode_key = #{commModeKey,jdbcType=VARCHAR},
            </if>
            <if test="testProgress != null">
                test_progress = #{testProgress,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="testPassTime != null">
                test_pass_time = #{testPassTime,jdbcType=TIMESTAMP},
            </if>
            <if test="cloudEquipId != null">
                cloud_equip_id = #{cloudEquipId,jdbcType=BIGINT},
            </if>
            <if test="cloudEquipName != null">
                cloud_equip_name = #{cloudEquipName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="findProjEquipSummaryInfo" parameterType="com.msun.csm.model.dto.ProjEquipSummaryDTO"
            resultType="com.msun.csm.model.vo.ProjEquipSummaryVO">
        select
        max(dp.product_name) as product_name,
        count(1) as equip_count,
        count(case per.required_flag when 1 then 1 else null end) as required_count,
        count(case per.required_flag when 0 then 1 else null end) as norequired_count,
        count(case when (per.required_flag = 1 and per.equip_status = 0) then 1 else null end) as not_apply_count,
        --count(case per.equip_status when 1 then 1 else null end) as apply_count,
        count(case per.equip_status when 2 then 1 else null end) as reject_count,
        --count(case per.equip_status when 3 then 1 else null end) as developing_count,
        --count(case per.equip_status when 4 then 1 else null end) as develop_end_count,
        count(case per.equip_status when 5 then 1 else null end) as test_success_count,
        count(case per.equip_status when 6 then 1 else null end) as test_fail_count,
        count(case when per.equip_status in (3,4,5,6) then 1 else null end) as complete_count,
        max(su.user_name) as duty_name,
        max(pmd.complete_status) as complete_status,
        (select survey_remark from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as survey_remark,
        (select pass_standard from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as pass_standard
        from csm.proj_equip_record per
        left join csm.dict_product dp on per.yy_product_id = dp.yy_product_id
        left join csm.proj_milestone_task pmt on per.custom_info_id = pmt.customer_info_id and per.project_info_id =
        pmt.project_info_id
        left join csm.proj_milestone_task_detail pmd on pmt.milestone_task_id = pmd.milestone_task_id and
        per.yy_product_id = pmd.product_deliver_id
        left join csm.sys_user su on pmd.leader_id = su.sys_user_id
        where 1 = 1
        and per.project_info_id = #{projectInfoId}
        <if test="hospitalInfoId != null and hospitalInfoId != 0">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        and pmt.milestone_info_id = #{milestoneInfoId}
        and per.is_deleted = 0
        group by per.yy_product_id,dp.order_no
        order by dp.order_no
    </select>

    <select id="selectSurveyEquipClass" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select equip_class_id   as id,
               equip_class_name as name
        from csm.dict_equip_class
        where yy_product_id = #{projectInfoId}
          and is_deleted = 0
    </select>

    <select id="selectSurveyEquipFactory" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select
        equip_factory_id as id,equip_factory_name as name
        from csm.dict_equip_factory
        where is_deleted = 0
        <if test="yyProductId != null">
            and yy_product_id = #{yyProductId}
        </if>
        <if test="id != null ">
            and equip_factory_id = #{id}
        </if>
    </select>

    <select id="selectSurveyEquipType" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select
        equip_type_id as id,equip_type_name as name
        from csm.dict_equip_type
        where is_deleted = 0
        <if test="id != null">
            and equip_type_id = #{id}
        </if>
        <if test="yyProductId != null">
            and yy_product_id = #{yyProductId}
        </if>
    </select>

    <select id="selectSurveyEquipInfo" resultType="com.msun.csm.model.vo.DictEquipInfoVO">
        select
        dei.equip_info_id,
        dei.equip_model_name,
        dei.equip_type_id,
        dei.equip_factory_id,
        det.equip_type_name,
        def.equip_factory_name,
        dei.no_survey_flag,
        dei.series_name
        from csm.dict_equip_info dei
        left join csm.dict_equip_type det on dei.equip_type_id = det.equip_type_id and det.is_deleted = 0
        left join csm.dict_equip_factory def on dei.equip_factory_id = def.equip_factory_id and def.is_deleted = 0
        where dei.is_deleted = 0
        <if test="id != null">
            and dei.equip_info_id = #{id}
        </if>
        <if test="yyProductId != null">
            and dei.yy_product_id = #{yyProductId}
        </if>
    </select>

    <select id="selectSurveyEquipAttributes" resultType="com.msun.csm.common.model.BaseCodeNameResp">
        select equip_attributes_key   as id,
               equip_attributes_value as name
        from csm.dict_equip_attributes
        where equip_attributes_code = #{equipAttributesCode}
          and is_deleted = 0
    </select>

    <select id="findProjEquipReadySummaryInfo" parameterType="com.msun.csm.model.dto.ProjEquipSummaryDTO"
            resultType="com.msun.csm.model.vo.ProjEquipReadySummaryVO">
        select
        max(dp.product_name) as product_name,
        count(1) as equip_count,
        count(case per.required_flag when 1 then 1 else null end) as required_count,
        count(case per.required_flag when 0 then 1 else null end) as norequired_count,
        count(case per.equip_status when 3 then 1 else null end) as developing_count,
        count(case per.equip_status when 4 then 1 else null end) as develop_end_count,
        count(case per.equip_status when 5 then 1 else null end) as test_success_count,
        count(case per.equip_status when 6 then 1 else null end) as test_fail_count,
        max(su.user_name) as duty_name,
        max(pmd.complete_status) as complete_status,
        (select pass_standard from csm.config_product_job_menu_detail where yy_product_id = per.yy_product_id and
        product_job_menu_id =
        (select product_job_menu_id from csm.config_product_job_menu where menu_code = 'equip')) as survey_remark
        from csm.proj_equip_record per
        left join csm.dict_product dp on per.yy_product_id = dp.yy_product_id
        left join csm.proj_milestone_task pmt on per.custom_info_id = pmt.customer_info_id and per.project_info_id =
        pmt.project_info_id
        left join csm.proj_milestone_task_detail pmd on pmt.milestone_task_id = pmd.milestone_task_id and
        per.yy_product_id = pmd.product_deliver_id
        left join csm.sys_user su on pmd.leader_id = su.sys_user_id
        where 1 = 1
        and per.project_info_id = #{projectInfoId}
        <if test="hospitalInfoId != null and hospitalInfoId != 0">
            and per.hospital_info_id = #{hospitalInfoId}
        </if>
        and pmt.milestone_info_id = #{milestoneInfoId}
        and per.is_deleted = 0
        group by per.yy_product_id,dp.order_no
        order by dp.order_no
    </select>

    <update id="updateEquipCheckResult" parameterType="java.util.Map">
        update csm.proj_equip_record
        set
        check_result = #{checkResult}
        <if test="progress != null">
            ,test_progress = #{progress}
        </if>
        <if test="equipStatus != null">
            ,equip_status = #{equipStatus}
        </if>
        where custom_info_id = #{customInfoId}
        and cloud_equip_id = #{cloudEquipId}
        <if test="hospitalList != null and hospitalList.size() > 0">
            and hospital_info_id in
            <foreach collection="hospitalList" item="item" index="index" open="(" close=")" separator=",">
                #{item.hospitalInfoId}
            </foreach>
        </if>
    </update>

    <select id="getEquipProgressInfo" resultType="com.msun.csm.dao.entity.oldimsp.EquipProgressInfo">
        select es.project_info_id                                    as "projectId",
               p."product_name"                                      as "productName",
               COUNT(es.equip_record_id)                             AS "equipCount",
               SUM(CASE WHEN es.equip_status = 5 THEN 1 ELSE 0 END)  AS "equipFinishedCount",
               SUM(CASE WHEN es.equip_status != 5 THEN 1 ELSE 0 END) AS "equipUnfinishedCount"
        from csm.proj_equip_record es
                 left join csm.dict_product p on es.yy_product_id = p.yy_product_id
        where es.project_info_id = #{projectInfoId}
          and es.required_flag = 1
          and es.is_deleted = 0
        GROUP BY es.project_info_id,
                 p.product_name;
    </select>

    <select id="find">
        select per.equip_status, phi.cloud_hospital_id
        from csm.proj_equip_record per
                 left join csm.proj_hospital_info phi
                           on per.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
    </select>

    <update id="updateByCustomInfoId">
        update csm.proj_equip_record
        set custom_info_id = #{newCustomInfoId}
        where custom_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectCustomNotHasLis" parameterType="long" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        select
        c1.*
        from
        csm.proj_project_info c1
        where
        his_flag = 1
        and custom_info_id not in (
        select
        custom_info_id
        from
        csm.proj_product_deliver_record
        where
        product_deliver_id = 4073
        and is_deleted = 0
        )
        and is_deleted = 0
        and project_info_id in (
        select
        new_project_info_id
        from
        csm.tmp_project_new_vs_old
        where
        new_project_source = 1
        )
        <if test="customInfoId != -1">
            and c1.custom_info_id = #{customInfoId}
        </if>
    </select>

    <select id="selectCustomNotHasPacs" parameterType="long" resultType="com.msun.csm.dao.entity.proj.ProjProjectInfo">
        select
        c1.*
        from
        csm.proj_project_info c1
        where
        his_flag = 1 and custom_info_id not in (
        select
        custom_info_id
        from
        csm.proj_product_deliver_record
        where
        product_deliver_id = 4063
        and is_deleted = 0
        )
        and is_deleted = 0
        and project_info_id in (
        select
        new_project_info_id
        from
        csm.tmp_project_new_vs_old
        where
        new_project_source = 1
        )
        <if test="customInfoId != -1">
            and c1.custom_info_id = #{customInfoId}
        </if>
    </select>

    <select id="getEquipAfterSurvey" resultType="com.msun.csm.dao.entity.proj.EquipAfterSurvey">
        select per.equip_record_id,
               per.equip_factory_name,
               per.equip_type_name,
               per.equip_model_name,
               per.yy_product_id,
               per.create_time
        from csm.proj_equip_record per
        where per.is_deleted = 0
          and per.project_info_id = #{projectInfoId}
          and per.create_time &gt;= cast(#{completeTime, jdbcType=VARCHAR} as TIMESTAMP)
    </select>

    <update id="updateByProjectId">
        update csm.proj_equip_record
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() != 0">
            and yy_product_id in
            <foreach collection="changeProductList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>
