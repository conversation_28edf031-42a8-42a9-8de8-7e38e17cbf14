<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper">
    <select id="getProjCustomCloudService" resultType="com.msun.csm.dao.entity.proj.ProjCustomCloudService">
        select pccs.* from csm.proj_custom_cloud_service pccs
                                      inner join csm.proj_custom_info pci
                                                 on pci.custom_info_id =pccs.custom_info_id
        where pccs.is_deleted =0 and pci.is_deleted =0
          and pci.yy_customer_id = #{yyCustomerId}
          and pccs.solution_type = #{solutionType}
          and pccs.yy_order_id &lt;&gt; #{yyOrderId}
        order by pccs.create_time desc
    </select>
    <select id="getCustomCloudServiceByyyCustomerIdSolutionType" resultType="com.msun.csm.dao.entity.proj.ProjCustomCloudService">
        select pccs.* from csm.proj_custom_cloud_service pccs
                               inner join csm.proj_custom_info pci
                                          on pci.custom_info_id =pccs.custom_info_id
        where pccs.is_deleted =0 and pci.is_deleted =0
          and pci.yy_customer_id = #{yyCustomerId}
          and pccs.solution_type = #{solutionType}
        order by pccs.create_time desc limit 1
    </select>
    <select id="findCustomCloudServiceByCustomInfoIdDescLimitOne" resultType="com.msun.csm.dao.entity.proj.ProjCustomCloudService">
        with services as (
            select * from (
                    select *, row_number() over(partition by an.custom_info_id order by an.create_time desc) rn
                    from csm.proj_custom_cloud_service an
                    where
                        an.is_deleted = 0
                    and subscribe_end_time is not null
                    <if test="solutionType != null ">
                        and an.solution_type=#{solutionType}
                    </if>
                    <if test="customInfoId != null ">
                        and an.custom_info_id=#{customInfoId}
                    </if>
                ) t
            where
                t.rn = 1
        )
        select
            *
        from services ss
                 inner join  csm.proj_custom_cloud_service csde
                             on csde.custom_cloud_service_id =ss.custom_cloud_service_id
        where csde.is_deleted = 0
    </select>
    <select id="selectProjCustomCloudServiceByParamer" resultType="com.msun.csm.dao.entity.proj.ProjCustomCloudService"
            parameterType="com.msun.csm.model.dto.applyorder.ProjApplyOrderDTO">

        select  distinct
            ppi.project_info_id,
            pccs.custom_cloud_service_id custom_cloud_service_id,
            COALESCE(pccs.contract_custom_info_id, pcci.contract_custom_info_id)   contract_custom_info_id,
            ppi.custom_info_id custom_info_id,
            2 cloud_service_type,
            COALESCE(pccs.yy_order_id,-1 )  yy_order_id,
            null as service_subscribe_term,
            null as subscribe_start_time,
            null as service_subscribe_status,
            poi.delivery_order_type as  solution_type,
            dce.envir_id as  deploy_node_id,
            dce.envir_name as  deploy_node_name,
            null subscribe_end_time,
            act.contract_type as  contract_type,
            null as plan_start_time,
            null as send_letter_date
        from
            csm.proj_project_info ppi
                left join csm.proj_order_info poi on ppi.order_info_id = poi.order_info_id
                left join csm.proj_contract_info act on poi.contract_info_id = act.contract_info_id
                left join csm.proj_contract_custom_info pcci on pcci.yy_parta_id = act.yy_contract_custom_id
                left join csm.proj_custom_cloud_service pccs on pccs.custom_info_id = ppi.custom_info_id and pccs.cloud_service_type = 2 and pccs.solution_type = poi.delivery_order_type and pccs.is_deleted=0
                left join csm.dict_cloud_environments dce on dce.envir_id = #{envirId}
        where ppi.custom_info_id = #{customInfoId}
          and  ppi.project_info_id = #{projectInfoId}
          and ppi.his_flag = 1
          and ppi.is_deleted=0


    </select>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjCustomCloudService">
        update csm.proj_custom_cloud_service
        set
        update_time = now(),
        send_letter_date = #{sendLetterDate,jdbcType=TIMESTAMP}
        where custom_cloud_service_id = #{customCloudServiceId,jdbcType=BIGINT}
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_custom_cloud_service
        set custom_info_id = #{newCustomInfoId,jdbcType=BIGINT}
        where custom_info_id = #{oldCustomInfoId,jdbcType=BIGINT}
    </update>
</mapper>
