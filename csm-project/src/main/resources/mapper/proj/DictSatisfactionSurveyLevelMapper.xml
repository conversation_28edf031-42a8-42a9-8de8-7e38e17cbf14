<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictSatisfactionSurveyLevelMapper">

    <select id="getAllEvaluationLevel" resultType="com.msun.csm.dao.entity.dict.DictSatisfactionSurveyLevel">
        select *
        from csm.dict_satisfaction_survey_level
        where is_delete = 0
    </select>


    <select id="getSatisfactionSurveyLevelByCode" resultType="com.msun.csm.dao.entity.dict.DictSatisfactionSurveyLevel">
        select *
        from csm.dict_satisfaction_survey_level
        where is_delete = 0
        and level_code = #{levelCode}
    </select>

</mapper>
