<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper">
    <update id="updateReprotStatusData">
        update csm.proj_survey_report
        set finish_status = #{finishStatus},
            examine_opinion = #{examineOpinion},
            commit_finish_time = #{commitFinishTime}
        where
            project_info_id = #{projectInfoId}
            and finish_status not in (1,3)
            <if test="yyProductId != null">
                and yy_product_id = #{yyProductId}
                and finish_imgs is not null
                and finish_imgs != ''
            </if>
            <if test="hospitalInfoId != null">
                and hospital_info_id = #{hospitalInfoId}
            </if>

    </update>


    <select id="selectSurveyReportByPage" resultType="com.msun.csm.model.resp.projreport.ProjSurveyReportResp">
        select * from (
        select distinct
        dp.order_no ,
        coalesce(dp.product_name ,dpvm.yy_module_name) as "productName" ,
        fr.zong as "needPrintCount",
        fr.yiwancheng as "finishRatio",
        su.user_name as "makeUserName",
        sure.user_name   as "reviewerUserName",
        ci.custom_name AS "customName",
        phi.hospital_name as "hospitalName",
        psreview.examine_opinion as "operationExamineOpinion",

        case
        when psr.finish_Status = 0 then '未开始'
        when psr.finish_Status = 1 then '制作完成'
        when psr.finish_Status = 2 then '制作完成已驳回'
        when psr.finish_Status = 4 then '提交调研审核'
        when psr.finish_Status = 5 then '调研审核通过'
        when psr.finish_Status = 6 then '调研审核驳回'
        when psr.finish_Status = 7 then '待完善调研信息'
        when psr.finish_Status = 8 then '制作完成验证通过'
        else '其他' end                            as "finishStatusStr",

        coalesce(susurvey.user_name ,su.user_name)    as "surveyUserName",
        identifier.user_name                                   as "identifierUserName",
        case when psreview.examine_status = 1 then '通过'
        when psreview.examine_status = 2 then '驳回'
            else '待审核' end
            as "operationExamineStatusStr",
        ppi.project_name as "projectName",
        case when ss.open_flag = 1 and psreview.examine_status = 0 then true else false end as "isNeedAuditorFlag",
        psr.* from csm.proj_survey_report psr
        left join csm.proj_hospital_info phi on psr.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
        left join csm.proj_project_info ppi on ppi.project_info_id = psr.project_info_id
        left join (
            select ccbdl.open_flag ,
                   ccbl.project_info_id
            from
            csm.config_custom_backend_limit ccbl
            inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
            where
            ccbl.is_deleted =0
            and ccbdl.is_deleted = 0
            and ccbdl.open_flag = 1
            and ccbdl.open_type = 11

        ) ss on psr.project_info_id = ss.project_info_id
        left join
        (
            select distinct phin.cloud_hospital_id, phin.hospital_info_id  from
            csm.proj_hospital_info phin
            inner join csm.proj_hospital_vs_project_type phvpt on phin.hospital_info_id = phvpt.hospital_info_id and phvpt.is_deleted = 0
            inner join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type and ppi.custom_info_id = phvpt.custom_info_id and ppi.is_deleted = 0
            where   phin.is_deleted = 0
            and phin.cloud_hospital_id is not null
        ) hosphi on psr.hospital_info_id = hosphi.hospital_info_id
        left join  (
            select cloud_hospital_id, report_code,count(*) as zong,
            round((sum(case when finish_status = 1 then 1 else 0 end) * 100/ coalesce(count(*), 10000)::numeric ), 2) || '%' as yiwancheng
            from
            csm.proj_project_form_terminal
            where is_deleted = 0
                    and status != '21'
            group by cloud_hospital_id, report_code
        ) fr on fr.cloud_hospital_id = hosphi.cloud_hospital_id and fr.report_code = psr.print_data_code
        left join csm.dict_product dp on psr.yy_product_id = dp.yy_product_id
        left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = psr.yy_product_id
        left join csm.sys_user su on psr.make_user_id = su.sys_user_id  and su.is_deleted = 0
        left join csm.sys_user sure on psr.reviewer_user_id = sure.sys_user_id and sure.is_deleted = 0
        left join csm.sys_user susurvey on psr.survey_user_id = susurvey.sys_user_id and susurvey.is_deleted = 0
        left join csm.sys_user identifier on psr.identifier_user_id = identifier.sys_user_id and identifier.is_deleted = 0
        left join csm.proj_custom_info ci on psr.customer_info_id = ci.custom_info_id and ci.is_deleted = 0
        left join  csm.config_custom_backend_limit ccbl on psr.project_info_id = ccbl.project_info_id and ccbl.is_deleted = 0
        left join (
            select
            x.*
            from
            csm.proj_business_examine_log x
            inner join (
            select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
            where is_deleted = 0 and business_type = 'printreport'
            group by business_id
            ) y on x.business_id = y.business_id and x.create_time = y.create_time
            where x.is_deleted = 0  and x.business_type = 'printreport' and x.examine_status in (0,1,2)
        ) psreview on psr.survey_report_id = psreview.business_id
        left join     (
            SELECT
            project_info_id,
            psr.print_data_code,
            COUNT(*) as duplicate_count
            FROM
            csm.proj_survey_report psr
            WHERE
            psr.is_deleted = 0
            GROUP BY
            psr.print_data_code, project_info_id
            HAVING
            COUNT(*) > 1
        ) repeatnode on repeatnode.project_info_id=psr.project_info_id and repeatnode.print_data_code = psr.print_data_code
        where
        psr.is_deleted = 0
        <if test="pageSource != null and pageSource == 'print_report_design' ">
            and ccbl.open_flag = 1
        </if>

        <if test="allocateUserId != null and allocateUserId.size() != 0 ">
            and psr.make_user_id in
            <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                  #{item}
          </foreach>
        </if>
        <if test="identifierUserIds != null and identifierUserIds.size() != 0 ">
            and psr.identifier_user_id in
            <foreach collection="identifierUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size() != 0 ">
            and ppi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="operationExamineStatusNumber != null">
            and psreview.examine_status = #{operationExamineStatusNumber}
        </if>
        <if test="repeatNode != null and repeatNode == 0 ">
            and repeatnode.print_data_code is null
        </if>
        <if test="repeatNode != null and repeatNode == 1 ">
            and repeatnode.print_data_code is not null
        </if>
        <if test="reviewerUserIds != null and reviewerUserIds.size() != 0 ">
            and psr.reviewer_user_id in
            <foreach collection="reviewerUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="finishStatusList != null and finishStatusList.size > 0">
            and  psr.finish_status in
            <foreach collection="finishStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="surveyReportId != null">
          and psr.survey_report_id = #{surveyReportId}
        </if>
        <if test="
        projectInfoId != null">and psr.project_info_id = #{projectInfoId}
            </if>
            <if test="hospitalInfoId != null">
                and psr.hospital_info_id = #{hospitalInfoId}
            </if>
            <if test="yyProductId != null">
                and (psr.yy_product_id = #{yyProductId} or psr.yy_module_id = #{yyProductId} )
            </if>
            <if test="yyModuleId != null">
                and psr.yy_module_id = #{yyModuleId}
            </if>
            <if test="finishStatus != null">
                and case when ss.open_flag = 1 then psr.finish_status = #{finishStatus}
                when #{finishStatus} = 0 then psr.finish_status in (0,4,5,6)
                else  psr.finish_status = #{finishStatus} end
            </if>
            <if test="onlineEssential != null">
                and psr.online_essential = #{onlineEssential}
            </if>
            <if test="customInfoId != null">
                and psr.customer_info_id = #{customInfoId}
            </if>
            <if test="reportName != null and reportName != '' ">
                and (psr.report_name like concat('%',#{reportName},'%') OR  psr.print_data_code like concat('%',#{reportName},'%'))
            </if>
            ) ss
        <if test="repeatNode != null and repeatNode == 1 ">
            order by customer_info_id, project_info_id, ss.print_data_code
        </if>
        <if test="repeatNode == null || repeatNode != 1">
            order by customer_info_id, project_info_id, ss.order_no,ss.update_time desc,ss.report_name
        </if>
    </select>
    <select id="selectSurveyReportCount"
            resultType="com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount">
        select
            count(*) "totalCount",
            sum( case when online_essential =1 then 1 else 0 end )   "preLaunchCompletionCount",
            sum( case when online_essential =1 and finish_status not in (1,3)  then 1 else 0 end ) "incompleteCount",
            sum( case when online_essential =1 and finish_status =2  then 1 else 0 end ) "rejectedCount"
        from (
            select distinct
            dp.order_no ,
            coalesce(dp.product_name ,dpvm.yy_module_name) as "productName" ,psr.* from csm.proj_survey_report psr
        left join csm.proj_project_info ppi on ppi.project_info_id = psr.project_info_id
            left join csm.dict_product dp on psr.yy_product_id = dp.yy_product_id
            left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = psr.yy_product_id
            left join  csm.config_custom_backend_limit ccbl on psr.project_info_id = ccbl.project_info_id and ccbl.is_deleted = 0
            left join (
                select ccbdl.open_flag ,
                ccbl.project_info_id
                from
                csm.config_custom_backend_limit ccbl
                inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
                where
                ccbl.is_deleted =0
                and ccbdl.is_deleted = 0
                and ccbdl.open_flag = 1
                and ccbdl.open_type = 11

            ) ss on psr.project_info_id = ss.project_info_id
            left join (
                select
                x.*
                from
                csm.proj_business_examine_log x
                inner join (
                select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
                where is_deleted = 0 and business_type = 'printreport'
                group by business_id
                ) y on x.business_id = y.business_id and x.create_time = y.create_time
                where x.is_deleted = 0  and x.business_type = 'printreport'
            ) psreview on psr.survey_report_id = psreview.business_id
            left join     (
                SELECT
                project_info_id,
                psr.print_data_code,
                COUNT(*) as duplicate_count
                FROM
                csm.proj_survey_report psr
                WHERE
                psr.is_deleted = 0
                GROUP BY
                psr.print_data_code, project_info_id
                HAVING
                COUNT(*) > 1
            ) repeatnode on repeatnode.project_info_id=psr.project_info_id and repeatnode.print_data_code = psr.print_data_code
            where
            psr.is_deleted = 0

            <if test="pageSource != null and pageSource == 'print_report_design' ">
                and ccbl.open_flag = 1
            </if>

            <if test="allocateUserId != null and allocateUserId.size() != 0 ">
                and psr.make_user_id in
                <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="identifierUserIds != null and identifierUserIds.size() != 0 ">
                and psr.identifier_user_id in
                <foreach collection="identifierUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size() != 0 ">
            and ppi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
            <if test="repeatNode != null and repeatNode == 0 ">
                and repeatnode.print_data_code is null
            </if>
            <if test="repeatNode != null and repeatNode == 1 ">
                and repeatnode.print_data_code is not null
            </if>
            <if test="operationExamineStatusNumber != null">
                and psreview.examine_status = #{operationExamineStatusNumber}
            </if>
            <if test="surveyReportId != null">and psr.survey_report_id = #{surveyReportId}
            </if>
            <if test=" projectInfoId != null">
                  and psr.project_info_id = #{projectInfoId}
            </if>
            <if test="hospitalInfoId != null">
                and psr.hospital_info_id = #{hospitalInfoId}
            </if>
            <if test="yyProductId != null">
                and (psr.yy_product_id = #{yyProductId} or psr.yy_module_id = #{yyProductId} )
            </if>
            <if test="yyModuleId != null">
                and psr.yy_module_id = #{yyModuleId}
            </if>
            <if test="finishStatus != null">
                and case when ss.open_flag = 1 then psr.finish_status = #{finishStatus}
                when #{finishStatus} = 0 then psr.finish_status in (0,4,5,6)
                else  psr.finish_status = #{finishStatus} end
            </if>
              <if test="finishStatusList != null and finishStatusList.size > 0">
                  and  psr.finish_status in
                  <foreach collection="finishStatusList" item="item" index="index" open="(" separator="," close=")">
                      #{item}
                  </foreach>
              </if>
            <if test="onlineEssential != null">
                and psr.online_essential = #{onlineEssential}
            </if>
            <if test="customInfoId != null">
                and psr.customer_info_id = #{customInfoId}
            </if>
            <if test="reportName != null and reportName != '' ">
                and (psr.report_name like concat('%',#{reportName},'%') OR  psr.print_data_code like concat('%',#{reportName},'%'))
            </if>
        ) ss
    </select>
    <select id="getPrintCodeResultListByParamer"
            resultType="com.msun.core.component.implementation.api.csm.vo.HospitalPrintCodeVO">
        SELECT x.print_data_code "printCode",max(x.report_name) report_name,phi.cloud_hospital_id hospital_id FROM
            csm.proj_survey_report x
           inner join csm.proj_hospital_info phi on x.hospital_info_id = phi.hospital_info_id
        where phi.cloud_hospital_id = #{hospitalId}
        and x.print_data_code is not null
        group by  x.print_data_code,phi.cloud_hospital_id

    </select>
    <select id="selectSurveyReportByHospitalIdAndPrintCode"
            resultType="com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport">
        SELECT x.* FROM
            csm.proj_survey_report x
                inner join csm.proj_hospital_info phi on x.hospital_info_id = phi.hospital_info_id
        where phi.cloud_hospital_id =  #{hospitalId}
          and x.print_data_code  = #{printCode}
        and x.is_deleted = 0
        and x.make_user_id is not null

    </select>
    <select id="selectSendMsgData" resultType="com.msun.csm.model.resp.projreport.ProjProjectSendMsgDataResp">
        select
            'report' as "dataType"   ,
            psr.customer_info_id ,
            ci.custom_name AS "customName",
            psr.project_info_id ,
            ppi.project_name as "projectName",
            ppi.his_flag,
            ppi.project_number "projectNumber",

            count(distinct psr.survey_report_id ) as "allCount",
            count(psreview.examine_status  ) as "commitCount",
            sum( case when psreview.examine_status = 1 then 1 else 0 end ) as "operCount",
            sum(case when psreview.examine_status = 2 then 1 else 0 end ) as "errCount",
            string_agg(distinct psr.creater_id::varchar, ',') "userIds",
            string_agg(distinct psr.reviewer_user_id::varchar, ',') "commitUserIds"


        from csm.proj_survey_report psr

                 inner join csm.proj_project_info ppi  on psr.project_info_id = ppi.project_info_id and ppi.is_deleted = 0 and ppi.project_deliver_status &lt; 5 and ppi.his_flag = 1
                 inner join (
            select ccbdl.open_flag ,
                   ccbl.project_info_id
            from
                csm.config_custom_backend_limit ccbl
                    inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
            where
                ccbl.is_deleted =0
              and ccbdl.is_deleted = 0
              and ccbdl.open_flag = 1
              and ccbdl.open_type = 11

        ) ss on psr.project_info_id = ss.project_info_id
                 inner join csm.proj_custom_info ci on psr.customer_info_id = ci.custom_info_id and ci.is_deleted = 0
                 left join (
            select
                x.*
            from
                csm.proj_business_examine_log x
                    inner join (
                    select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
                    where is_deleted = 0 and business_type = 'printreport'
                    group by business_id
                ) y on x.business_id = y.business_id and x.create_time = y.create_time
            where x.is_deleted = 0  and x.business_type = 'printreport'
        ) psreview on psr.survey_report_id = psreview.business_id

        where
            psr.is_deleted = 0

        group by psr.customer_info_id ,
                 ci.custom_name,
                 psr.project_info_id ,
                 ppi.project_name ,
                 ppi.his_flag,
                 ppi.project_number
        having count(distinct psr.survey_report_id ) > sum( case when psreview.examine_status = 1 then 1 else 0 end )


        union all


        select
            'form' as "dataType"   ,
            psr.customer_info_id ,
            ci.custom_name AS "customName",
            psr.project_info_id ,
            ppi.project_name as "projectName",
            ppi.his_flag,
            ppi.project_number "projectNumber",

            count(distinct psr.survey_form_id ) as "allCount",
            count(psreview.examine_status  ) as "commitCount",
            sum( case when psreview.examine_status = 1 then 1 else 0 end ) as "operCount",
            sum(case when psreview.examine_status = 2 then 1 else 0 end ) as "errCount",
            string_agg(distinct psr.creater_id::varchar, ',') "userIds",
            string_agg(distinct psr.reviewer_user_id::varchar, ',') "commitUserIds"


        from csm.proj_survey_form psr
                 inner join csm.proj_project_info ppi  on psr.project_info_id = ppi.project_info_id and ppi.is_deleted = 0 and ppi.project_deliver_status &lt; 5 and ppi.his_flag = 1
                 inner join (
            select ccbdl.open_flag ,
                   ccbl.project_info_id
            from
                csm.config_custom_backend_limit ccbl
                    inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
            where
                ccbl.is_deleted =0
              and ccbdl.is_deleted = 0
              and ccbdl.open_flag = 1
              and ccbdl.open_type = 12

        ) ss on psr.project_info_id = ss.project_info_id
                 inner join csm.proj_custom_info ci on psr.customer_info_id = ci.custom_info_id and ci.is_deleted = 0
                 left join (
            select
                x.*
            from
                csm.proj_business_examine_log x
                    inner join (
                    select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
                    where is_deleted = 0 and business_type = 'form'
                    group by business_id
                ) y on x.business_id = y.business_id and x.create_time = y.create_time
            where x.is_deleted = 0  and x.business_type = 'form'
        ) psreview on psr.survey_form_id = psreview.business_id

        where
            psr.is_deleted = 0

        group by psr.customer_info_id ,
                 ci.custom_name,
                 psr.project_info_id ,
                 ppi.project_name ,
                 ppi.his_flag,
                 ppi.project_number
        having count(distinct psr.survey_form_id ) > sum( case when psreview.examine_status = 1 then 1 else 0 end )
    </select>

    <update id="updateByProjectId">
        update csm.proj_survey_report
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() != 0">
            and (yy_product_id in
            <foreach collection="changeProductList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            or yy_module_id in
            <foreach collection="changeProductList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_survey_report
        set customer_info_id = #{newCustomInfoId}
        where customer_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_survey_report
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and (yy_product_id in
        <foreach collection="oldProductIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        or yy_module_id in
        <foreach collection="oldProductIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </update>
    <update id="udpateReprotByOldId">
        update csm.proj_survey_report
        set survey_user_id = #{oldId}
        where survey_user_id = #{newOldId}
    </update>
    <update id="udpateReprotMakeByOldId">
        update csm.proj_survey_report
        set make_user_id = #{oldId}
        where make_user_id = #{newOldId}
    </update>
    <update id="updateProductBacklogByOldId">
        update csm.proj_milestone_task
        set leader_id = #{oldId}
        where leader_id = #{newOldId}
    </update>
    <update id="updateProductBacklogDetailByOldId">
        update csm.proj_milestone_task_detail
        set leader_id = #{oldId}
        where leader_id = #{newOldId}
    </update>
    <update id="updateFmUserFormDataByOldId">
        update tduckpro.fm_user_form_data
        set sys_user_id = #{oldId}
        where sys_user_id = #{newOldId}
    </update>
    <update id="updateSurveyPlanByOldId">
        update csm.proj_survey_plan
        set survey_user_id = #{oldId}
        where survey_user_id = #{newOldId}
    </update>
    <update id="updateThirdInterfaceByOldId">
        update csm.proj_third_interface
        set dir_person_id = #{oldId}
        where dir_person_id = #{newOldId}
    </update>
    <update id="updateThirdInterfaceDirPersonByOldId">
        update csm.proj_third_interface
        set creater_id = #{oldId}
        where creater_id = #{newOldId}
    </update>

    <update id="deleteSurveyReport">
        update csm.proj_survey_report
        set is_deleted = 1,
        update_time = now(),
        updater_id = #{sysUserId}
        where survey_report_id in
        <foreach collection="surveyReportIdList" item="surveyReportId" open="(" separator="," close=")">
            #{surveyReportId}
        </foreach>
    </update>
    <update id="updatePrintReportStatusData">
        update csm.proj_survey_report
        set print_status = 1
        where is_deleted = 0
            and customer_info_id = #{customInfoId}
    </update>

    <select id="selectBySurveyReportId" resultType="com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport">
        select *
        from csm.proj_survey_report
        where survey_report_id = #{surveyReportId}
    </select>

    <select id="selectSurveyReport" resultType="com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport">
        select
        *
        from
        csm.proj_survey_report
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        and hospital_info_id = #{hospitalInfoId}
        and (
        yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        or yy_module_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <select id="selectListAllNotCompar" resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">
        SELECT x.survey_form_id,fi.file_path as "surveyImgs", x.form_name,COALESCE(x.type_code, 'zzzzz') as type_code, fi.file_name as "supplementImgs"  FROM csm.proj_survey_form x
        left join csm.proj_project_file fi on SPLIT_PART(x.survey_imgs, ',', 1)::int8 = fi.project_file_id
        WHERE x.survey_imgs is not null
        and x.survey_imgs != ''
        and x.survey_imgs not like ','
        and x.project_info_id in (
        select project_info_id from csm.proj_project_info where is_deleted = 0 and project_deliver_status &lt; 4
        )
        and  x.is_deleted = 0
        and x.yy_product_id in (
            SELECT  yy_product_id FROM csm.dict_product
            WHERE product_name like '%护理%'
            )
        and (x.report_task_id = '' or x.report_task_id is null)
          and length(fi.file_path) > 15
    </select>
    <select id="selectListAllNotWorkProcssCompar"
            resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">

        select
            survey_form_id,
            report_task_id
        from
            csm.proj_survey_form
        where
            is_deleted = 0
          and report_task_id is not null
          and report_task_id != ''
          and (recommend_template_ids is null
            or recommend_template_ids = '')
    </select>
    <select id="selectListAllReprtNotCompar"
            resultType="com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport">
        SELECT x.survey_report_id,fi.file_path as "surveyImgs", x.report_name, fi.file_name as "supplementImgs",x.print_data_code  FROM csm.proj_survey_report x
        left join csm.proj_project_file fi on  fi.project_file_id = ANY(
            SELECT regexp_split_to_table(x.survey_imgs, ',')::int8
        )
        WHERE x.survey_imgs is not null
          and x.survey_imgs != ''
          and x.survey_imgs not like ','
          and x.project_info_id in (
            select project_info_id from csm.proj_project_info where is_deleted = 0 and project_deliver_status &lt; 4
        )
          and  x.is_deleted = 0
           and  x.print_data_code is not null
            and x.print_data_code != ''
          and (x.report_task_id = '' or x.report_task_id is null)
          and length(fi.file_path) > 15

    </select>
    <select id="selectListAllReprtNotImgByNodeCode"
            resultType="com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport">
        SELECT x.survey_report_id, x.print_data_code, x.project_info_id, phi.cloud_hospital_id  as "cloudHospitalId" FROM csm.proj_survey_report x
         inner join csm.proj_hospital_info phi on x.hospital_info_id = phi.hospital_info_id
        WHERE (x.finish_imgs is null or x.finish_imgs = '')
          and x.project_info_id in (
            select project_info_id from csm.proj_project_info where is_deleted = 0 and project_deliver_status &lt; 4
        )
          and  x.is_deleted = 0
          and  x.print_data_code is not null
          and x.print_data_code != ''
         and x.finish_status in (1,4,5)
          and  phi.cloud_hospital_id is not null
    </select>
</mapper>
