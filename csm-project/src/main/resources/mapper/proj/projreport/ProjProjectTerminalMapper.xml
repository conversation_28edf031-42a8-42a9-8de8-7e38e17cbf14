<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.formlibrary.ProjProjectFormTerminalMapper">


    <select id="selectListByParamer" resultType="com.msun.csm.model.resp.projreport.ProjProjectFormTerminalResp">
        select
            s.*
        from
        csm.proj_project_form_terminal s
        inner join
        (
        select distinct phin.cloud_hospital_id from
        csm.proj_hospital_info phin
        inner join csm.proj_hospital_vs_project_type phvpt on phin.hospital_info_id = phvpt.hospital_info_id and phvpt.is_deleted = 0
        inner join csm.proj_project_info ppi on ppi.project_type = phvpt.project_type and ppi.custom_info_id = phvpt.custom_info_id and ppi.is_deleted = 0
        where   phin.is_deleted = 0
        and ppi.project_info_id = #{projectInfoId}
            <if test="hospitalInfoId != null">
                and phin.hospital_info_id =  #{hospitalInfoId}
            </if>
        ) phi
        on s.cloud_hospital_id = phi.cloud_hospital_id
        where s.is_deleted = 0
          and s.report_code = #{reportCode}
        <if test="status != null and status != '' ">
            and s.status = #{status}
        </if>
        <if test="finishStatus != null">
            and s.finish_status = #{finishStatus}
        </if>
    </select>
</mapper>
