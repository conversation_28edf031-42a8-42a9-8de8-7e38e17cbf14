<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjOpenprintReportInfoMapper">

    <insert id="insertHospitalInfo" parameterType="java.lang.Long">
        INSERT INTO csm.proj_openprint_report_info
            (hospital_info_id, custom_info_id, create_time)
        select hospital_info_id, custom_info_id , now() from csm.proj_hospital_info
            where custom_info_id = #{customerId}
        and is_deleted = 0
        and hospital_info_id not in (select hospital_info_id from csm.proj_openprint_report_info)
    </insert>
</mapper>
