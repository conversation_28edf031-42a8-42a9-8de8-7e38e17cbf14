<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.msun.csm.dao.mapper.dict.DictDeductionTypeMapper">
    <select id="getDeductionTypeByApplicableScene" resultType="com.msun.csm.dao.entity.dict.DictDeductionType">
        select *
        from csm.dict_deduction_type
        where is_deleted = 0
          and applicable_scene = #{applicableScene}
        order by sort_no desc
    </select>

    <select id="getDeductionTypeByCode" resultType="com.msun.csm.dao.entity.dict.DictDeductionType">
        select *
        from csm.dict_deduction_type
        where is_deleted = 0
          and code = #{code}
        order by sort_no desc
    </select>

    <select id="getDeductionTypeByIssueClassification" resultType="com.msun.csm.dao.entity.dict.DictDeductionType">
        select *
        from csm.dict_deduction_type
        where is_deleted = 0
          and issue_classification_id = #{issueClassificationId}
        order by sort_no desc
    </select>

    <update id="invalidateById">
        update csm.dict_deduction_type
        set is_deleted = 1
        where id = #{id}
    </update>

    <select id="queryDeductionClassificationDict" resultType="com.msun.csm.model.DictDeductionTypeVO"
            parameterType="com.msun.csm.model.param.QueryDeductionClassificationDictParam">
        select ddt.id,
        ddt.issue_classification_id :: varchar as "issueClassificationId",
        cic."name" as "issueClassificationName",
        ddt."name",
        ddt.default_score as "defaultScore",
        ddt.server_type as "serverType",
        dst.server_type_name as "serverTypeName",
        ddt.code
        from csm.dict_deduction_type ddt
        left join csm.config_issue_classification cic on ddt.issue_classification_id = cic.id and cic.is_deleted = 0
        left join csm.dict_server_type dst on ddt.server_type = dst.server_type_code and dst.is_deleted = 0
        where ddt.is_deleted = 0
        <if test="issueClassificationIdList != null and issueClassificationIdList.size > 0">
            and ddt.issue_classification_id in
            <foreach collection="issueClassificationIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and ddt."name" ilike concat('%', #{name}, '%')
        </if>
        <if test="serverType != null and serverType != ''">
            and ddt.server_type = #{serverType}
        </if>
        order by ddt.sort_no
    </select>

    <update id="updateDictDeductionType" parameterType="com.msun.csm.dao.entity.dict.DictDeductionType">
        update csm.dict_deduction_type
        set issue_classification_id = #{issueClassificationId},
            name                    = #{name},
            server_type             = #{serverType},
            default_score           = #{defaultScore},
            updater_id              = #{updaterId},
            update_time             = #{updateTime}
        where id = #{id}
    </update>
</mapper>

