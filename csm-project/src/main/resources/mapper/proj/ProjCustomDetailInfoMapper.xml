<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjCustomDetailInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_custom_detail_info-->
        <id column="custom_detail_info_id" jdbcType="BIGINT" property="customDetailInfoId"/>
        <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="province_id" jdbcType="BIGINT" property="provinceId"/>
        <result column="city_id" jdbcType="BIGINT" property="cityId"/>
        <result column="town_id" jdbcType="BIGINT" property="townId"/>
        <result column="custom_bed_count" jdbcType="BIGINT" property="customBedCount"/>
        <result column="custom_out_patient_count" jdbcType="BIGINT" property="customOutPatientCount"/>
        <result column="custom_annual_income" jdbcType="NUMERIC" property="customAnnualIncome"/>
        <result column="hospital_branch_num" jdbcType="SMALLINT" property="hospitalBranchNum"/>
        <result column="region_custom_bed_count" jdbcType="BIGINT" property="regionCustomBedCount"/>
        <result column="region_custom_out_patient_count" jdbcType="BIGINT" property="regionCustomOutPatientCount"/>
        <result column="region_custom_annual_income" jdbcType="NUMERIC" property="regionCustomAnnualIncome"/>
        <result column="township_hospital_num" jdbcType="SMALLINT" property="townshipHospitalNum"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        custom_detail_info_id, custom_info_id, province_id, city_id, town_id, custom_bed_count,
        custom_out_patient_count, custom_annual_income, hospital_branch_num, region_custom_bed_count,
        region_custom_out_patient_count, region_custom_annual_income, township_hospital_num,
        creater_id, create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_custom_detail_info
        where custom_detail_info_id = #{customDetailInfoId,jdbcType=BIGINT}
    </select>
    <select id="getCustomDetailInfoByCustomInfoId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_custom_detail_info
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.proj_custom_detail_info
        where custom_detail_info_id = #{customDetailInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_detail_info (custom_detail_info_id, custom_info_id, province_id,
        city_id, town_id, custom_bed_count,
        custom_out_patient_count, custom_annual_income,
        hospital_branch_num, region_custom_bed_count,
        region_custom_out_patient_count, region_custom_annual_income,
        township_hospital_num, creater_id, create_time,
        updater_id, update_time, is_deleted
        )
        values (#{customDetailInfoId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{provinceId,jdbcType=BIGINT},
        #{cityId,jdbcType=BIGINT}, #{townId,jdbcType=BIGINT}, #{customBedCount,jdbcType=BIGINT},
        #{customOutPatientCount,jdbcType=BIGINT}, #{customAnnualIncome,jdbcType=NUMERIC},
        #{hospitalBranchNum,jdbcType=SMALLINT}, #{regionCustomBedCount,jdbcType=BIGINT},
        #{regionCustomOutPatientCount,jdbcType=BIGINT}, #{regionCustomAnnualIncome,jdbcType=NUMERIC},
        #{townshipHospitalNum,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_detail_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customDetailInfoId != null">
                custom_detail_info_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="provinceId != null">
                province_id,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="townId != null">
                town_id,
            </if>
            <if test="customBedCount != null">
                custom_bed_count,
            </if>
            <if test="customOutPatientCount != null">
                custom_out_patient_count,
            </if>
            <if test="customAnnualIncome != null">
                custom_annual_income,
            </if>
            <if test="hospitalBranchNum != null">
                hospital_branch_num,
            </if>
            <if test="regionCustomBedCount != null">
                region_custom_bed_count,
            </if>
            <if test="regionCustomOutPatientCount != null">
                region_custom_out_patient_count,
            </if>
            <if test="regionCustomAnnualIncome != null">
                region_custom_annual_income,
            </if>
            <if test="townshipHospitalNum != null">
                township_hospital_num,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customDetailInfoId != null">
                #{customDetailInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="townId != null">
                #{townId,jdbcType=BIGINT},
            </if>
            <if test="customBedCount != null">
                #{customBedCount,jdbcType=BIGINT},
            </if>
            <if test="customOutPatientCount != null">
                #{customOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="customAnnualIncome != null">
                #{customAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="hospitalBranchNum != null">
                #{hospitalBranchNum,jdbcType=SMALLINT},
            </if>
            <if test="regionCustomBedCount != null">
                #{regionCustomBedCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomOutPatientCount != null">
                #{regionCustomOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomAnnualIncome != null">
                #{regionCustomAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="townshipHospitalNum != null">
                #{townshipHospitalNum,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        update csm.proj_custom_detail_info
        <set>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="townId != null">
                town_id = #{townId,jdbcType=BIGINT},
            </if>
            <if test="customBedCount != null">
                custom_bed_count = #{customBedCount,jdbcType=BIGINT},
            </if>
            <if test="customOutPatientCount != null">
                custom_out_patient_count = #{customOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="customAnnualIncome != null">
                custom_annual_income = #{customAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="hospitalBranchNum != null">
                hospital_branch_num = #{hospitalBranchNum,jdbcType=SMALLINT},
            </if>
            <if test="regionCustomBedCount != null">
                region_custom_bed_count = #{regionCustomBedCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomOutPatientCount != null">
                region_custom_out_patient_count = #{regionCustomOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomAnnualIncome != null">
                region_custom_annual_income = #{regionCustomAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="townshipHospitalNum != null">
                township_hospital_num = #{townshipHospitalNum,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where custom_detail_info_id = #{customDetailInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        update csm.proj_custom_detail_info
        set custom_info_id = #{customInfoId,jdbcType=BIGINT},
        province_id = #{provinceId,jdbcType=BIGINT},
        city_id = #{cityId,jdbcType=BIGINT},
        town_id = #{townId,jdbcType=BIGINT},
        custom_bed_count = #{customBedCount,jdbcType=BIGINT},
        custom_out_patient_count = #{customOutPatientCount,jdbcType=BIGINT},
        custom_annual_income = #{customAnnualIncome,jdbcType=NUMERIC},
        hospital_branch_num = #{hospitalBranchNum,jdbcType=SMALLINT},
        region_custom_bed_count = #{regionCustomBedCount,jdbcType=BIGINT},
        region_custom_out_patient_count = #{regionCustomOutPatientCount,jdbcType=BIGINT},
        region_custom_annual_income = #{regionCustomAnnualIncome,jdbcType=NUMERIC},
        township_hospital_num = #{townshipHospitalNum,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where custom_detail_info_id = #{customDetailInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_custom_detail_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.customInfoId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="province_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.provinceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="city_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.cityId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="town_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.townId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_bed_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.customBedCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_out_patient_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.customOutPatientCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_annual_income = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.customAnnualIncome,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="hospital_branch_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.hospitalBranchNum,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="region_custom_bed_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.regionCustomBedCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="region_custom_out_patient_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.regionCustomOutPatientCount,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="region_custom_annual_income = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.regionCustomAnnualIncome,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="township_hospital_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.townshipHospitalNum,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}::timestamp(6)
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}::timestamp(6)
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where custom_detail_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.customDetailInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_custom_detail_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="custom_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customInfoId != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.customInfoId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="province_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.provinceId != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.provinceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="city_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cityId != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.cityId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="town_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.townId != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.townId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_bed_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customBedCount != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.customBedCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_out_patient_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customOutPatientCount != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.customOutPatientCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_annual_income = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customAnnualIncome != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.customAnnualIncome,jdbcType=NUMERIC}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_branch_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalBranchNum != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.hospitalBranchNum,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="region_custom_bed_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.regionCustomBedCount != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.regionCustomBedCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="region_custom_out_patient_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.regionCustomOutPatientCount != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.regionCustomOutPatientCount,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="region_custom_annual_income = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.regionCustomAnnualIncome != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.regionCustomAnnualIncome,jdbcType=NUMERIC}
                    </if>
                </foreach>
            </trim>
            <trim prefix="township_hospital_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.townshipHospitalNum != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.townshipHospitalNum,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <!--<trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}::timestamp(6)
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}::timestamp(6)
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when custom_detail_info_id = #{item.customDetailInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>-->
        </trim>
        where custom_detail_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.customDetailInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_custom_detail_info
        (custom_detail_info_id, custom_info_id, province_id, city_id, town_id, custom_bed_count,
        custom_out_patient_count, custom_annual_income, hospital_branch_num, region_custom_bed_count,
        region_custom_out_patient_count, region_custom_annual_income, township_hospital_num,
        creater_id, create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customDetailInfoId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
            #{item.provinceId,jdbcType=BIGINT}, #{item.cityId,jdbcType=BIGINT}, #{item.townId,jdbcType=BIGINT},
            #{item.customBedCount,jdbcType=BIGINT}, #{item.customOutPatientCount,jdbcType=BIGINT},
            #{item.customAnnualIncome,jdbcType=NUMERIC}, #{item.hospitalBranchNum,jdbcType=SMALLINT},
            #{item.regionCustomBedCount,jdbcType=BIGINT}, #{item.regionCustomOutPatientCount,jdbcType=BIGINT},
            #{item.regionCustomAnnualIncome,jdbcType=NUMERIC}, #{item.townshipHospitalNum,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_detail_info
        (custom_detail_info_id, custom_info_id, province_id, city_id, town_id, custom_bed_count,
        custom_out_patient_count, custom_annual_income, hospital_branch_num, region_custom_bed_count,
        region_custom_out_patient_count, region_custom_annual_income, township_hospital_num,
        creater_id, create_time, updater_id, update_time, is_deleted)
        values
        (#{customDetailInfoId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{provinceId,jdbcType=BIGINT},
        #{cityId,jdbcType=BIGINT}, #{townId,jdbcType=BIGINT}, #{customBedCount,jdbcType=BIGINT},
        #{customOutPatientCount,jdbcType=BIGINT}, #{customAnnualIncome,jdbcType=NUMERIC},
        #{hospitalBranchNum,jdbcType=SMALLINT}, #{regionCustomBedCount,jdbcType=BIGINT},
        #{regionCustomOutPatientCount,jdbcType=BIGINT}, #{regionCustomAnnualIncome,jdbcType=NUMERIC},
        #{townshipHospitalNum,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
        on duplicate key update
        custom_detail_info_id = #{customDetailInfoId,jdbcType=BIGINT},
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        province_id = #{provinceId,jdbcType=BIGINT},
        city_id = #{cityId,jdbcType=BIGINT},
        town_id = #{townId,jdbcType=BIGINT},
        custom_bed_count = #{customBedCount,jdbcType=BIGINT},
        custom_out_patient_count = #{customOutPatientCount,jdbcType=BIGINT},
        custom_annual_income = #{customAnnualIncome,jdbcType=NUMERIC},
        hospital_branch_num = #{hospitalBranchNum,jdbcType=SMALLINT},
        region_custom_bed_count = #{regionCustomBedCount,jdbcType=BIGINT},
        region_custom_out_patient_count = #{regionCustomOutPatientCount,jdbcType=BIGINT},
        region_custom_annual_income = #{regionCustomAnnualIncome,jdbcType=NUMERIC},
        township_hospital_num = #{townshipHospitalNum,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjCustomDetailInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_detail_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customDetailInfoId != null">
                custom_detail_info_id,
            </if>
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="provinceId != null">
                province_id,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="townId != null">
                town_id,
            </if>
            <if test="customBedCount != null">
                custom_bed_count,
            </if>
            <if test="customOutPatientCount != null">
                custom_out_patient_count,
            </if>
            <if test="customAnnualIncome != null">
                custom_annual_income,
            </if>
            <if test="hospitalBranchNum != null">
                hospital_branch_num,
            </if>
            <if test="regionCustomBedCount != null">
                region_custom_bed_count,
            </if>
            <if test="regionCustomOutPatientCount != null">
                region_custom_out_patient_count,
            </if>
            <if test="regionCustomAnnualIncome != null">
                region_custom_annual_income,
            </if>
            <if test="townshipHospitalNum != null">
                township_hospital_num,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customDetailInfoId != null">
                #{customDetailInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="townId != null">
                #{townId,jdbcType=BIGINT},
            </if>
            <if test="customBedCount != null">
                #{customBedCount,jdbcType=BIGINT},
            </if>
            <if test="customOutPatientCount != null">
                #{customOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="customAnnualIncome != null">
                #{customAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="hospitalBranchNum != null">
                #{hospitalBranchNum,jdbcType=SMALLINT},
            </if>
            <if test="regionCustomBedCount != null">
                #{regionCustomBedCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomOutPatientCount != null">
                #{regionCustomOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomAnnualIncome != null">
                #{regionCustomAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="townshipHospitalNum != null">
                #{townshipHospitalNum,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="customDetailInfoId != null">
                custom_detail_info_id = #{customDetailInfoId,jdbcType=BIGINT},
            </if>
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="townId != null">
                town_id = #{townId,jdbcType=BIGINT},
            </if>
            <if test="customBedCount != null">
                custom_bed_count = #{customBedCount,jdbcType=BIGINT},
            </if>
            <if test="customOutPatientCount != null">
                custom_out_patient_count = #{customOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="customAnnualIncome != null">
                custom_annual_income = #{customAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="hospitalBranchNum != null">
                hospital_branch_num = #{hospitalBranchNum,jdbcType=SMALLINT},
            </if>
            <if test="regionCustomBedCount != null">
                region_custom_bed_count = #{regionCustomBedCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomOutPatientCount != null">
                region_custom_out_patient_count = #{regionCustomOutPatientCount,jdbcType=BIGINT},
            </if>
            <if test="regionCustomAnnualIncome != null">
                region_custom_annual_income = #{regionCustomAnnualIncome,jdbcType=NUMERIC},
            </if>
            <if test="townshipHospitalNum != null">
                township_hospital_num = #{townshipHospitalNum,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
</mapper>
