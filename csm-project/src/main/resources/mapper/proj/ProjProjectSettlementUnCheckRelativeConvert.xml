<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.oldimsp.OldProductRelationMapper">

    <update id="updateBatchByYyProductidAndProjectInfoId">
        UPDATE platform.project_product_relation
        <set>
            update_time = current_timestamp,
            <if test="productStatus != null">
                product_status = #{productStatus, jdbcType=INTEGER},
            </if>
        </set>
        <where>
            and yy_productid in
            <foreach close=")" collection="list" item="id" open="(" separator=", ">
                #{id,jdbcType=BIGINT}
            </foreach>
            and project_id = #{projectInfoId, jdbcType=BIGINT}
        </where>
    </update>

    <select id="findByYyProductids" resultType="java.lang.Long"
            parameterType="java.util.List">
        select id from platform.product where product_yunying_id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>
