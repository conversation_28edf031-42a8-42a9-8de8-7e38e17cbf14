<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjAutoTestRecordMapper">
    <sql id="voSql">
        h2.org_id,
        h2.cloud_hospital_id,
        h2.custom_info_id,
        h2.scene_type,
        h2.dept_save_status,
        h2.cloud_domain,
        h2.hospital_name,
        h2.project_type,
        h2.hospital_info_id,
        h2.en_out_pharmacy_name,
        h2.en_out_pharmacy_id,
        h2.cn_out_pharmacy_name,
        h2.cn_out_pharmacy_id,
        h2.en_in_pharmacy_id,
        h2.en_in_pharmacy_name,
        h2.cn_in_pharmacy_name,
        h2.cn_in_pharmacy_id,
        h1.proj_auto_test_record_id,
        COALESCE(h1.status, 0) as status,
        h1.test_fail_reason,
        h1.test_log_id,
        h1.test_report_url,
        h1.test_report_detail_url,
        h1.report_time,
        h1.executor_user_id,
        h1.executor_user_name,
        h1.remark,
         case
            when basedata.passflag is null then false
            else basedata.passflag end
            as "passBaseTestFlag"
    </sql>
    <sql id="recSql">
        t2.proj_auto_test_record_id,
        COALESCE(t2.status, 0) as status,
        t2.test_report_url,
        t2.test_report_detail_url,
        t2.report_time,
        t2.executor_user_id,
        t2.executor_user_name,
        t2.remark,
        t2.scene_type,
        t2.cloud_hospital_id,
        t2.test_fail_reason,
        t2.test_log_id
    </sql>

    <select id="findAutoTestDataList" resultType="com.msun.csm.model.vo.ProjAutoTestRecordVO">
        select * from (
        select
        <include refid="voSql"/>
        from (
            select h.hospital_info_id, h.cloud_hospital_id,#{sceneType,jdbcType=INTEGER} as scene_type,h.hospital_name,
            h.org_id,
            h.cloud_domain,h.custom_info_id,
            h.hos_project_type as project_type,
            case when COALESCE(t3.proj_hospital_pharmacy_record_id, 0) != 0 then 1 else 0
            end as dept_save_status,
            t3.en_out_pharmacy_name,
            t3.en_out_pharmacy_id,
            t3.cn_out_pharmacy_name,
            t3.cn_out_pharmacy_id,
            t3.en_in_pharmacy_id,
            t3.en_in_pharmacy_name,
            t3.cn_in_pharmacy_name,
            t3.cn_in_pharmacy_id
            from csm.proj_hospital_info h
            left join csm.proj_hospital_pharmacy_record t3 on t3.cloud_hospital_id = h.cloud_hospital_id and
            t3.is_deleted = 0
            where h.is_deleted = 0 and h.cloud_hospital_id in
            <foreach collection="openedCloudHospitalIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and h.custom_info_id = #{customInfoId, jdbcType=BIGINT}
            and h.hos_project_type = #{projectType, jdbcType=INTEGER}
        ) h2 left join (
            select
            <include refid="recSql"/>
            from csm.proj_auto_test_record t2
            where t2.custom_info_id = #{customInfoId, jdbcType=BIGINT}
            and t2.is_deleted = 0 and t2.cloud_hospital_id in
            <foreach collection="openedCloudHospitalIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and t2.scene_type = #{sceneType,jdbcType=INTEGER}
            and t2.project_type = #{projectType, jdbcType=INTEGER}
        ) h1 on h1.cloud_hospital_id = h2.cloud_hospital_id and h2.scene_type = h1.scene_type

        left join (
                select
                     cloud_hospital_id,
                    case
                    when t2.status is null then false
                    when t2.status in (4,5) then true
                    else false
                    end as "passflag"
                from  csm.proj_auto_test_record t2
                where t2.custom_info_id = #{customInfoId, jdbcType=BIGINT}
                and  t2.scene_type = 0
                and t2.is_deleted = 0 and t2.cloud_hospital_id in
                <foreach collection="openedCloudHospitalIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                and t2.project_type = #{projectType, jdbcType=INTEGER}
        ) basedata on basedata.cloud_hospital_id = h2.cloud_hospital_id

        where h2.scene_type in (0,1)
        ) h3
        <where>
            <if test="notPassedFlag != null and notPassedFlag == 1">
                and (h3.status not in (4, 5))
            </if>
        </where>
        order by status desc
    </select>

    <update id="updateRecord">
        update csm.proj_auto_test_record
        <set>
            cloud_hospital_id = #{cloudHospitalId,jdbcType=BIGINT},
            status = #{status,jdbcType=INTEGER},
            test_report_url = #{testReportUrl,jdbcType=VARCHAR},
            report_time = #{reportTime,jdbcType=TIMESTAMP},
            executor_user_id = #{executorUserId,jdbcType=BIGINT},
            executor_user_name = #{executorUserName,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            scene_type = #{sceneType,jdbcType=INTEGER},
            test_fail_reason = #{testFailReason,jdbcType=VARCHAR},
            custom_info_id = #{customInfoId,jdbcType=BIGINT},
            project_info_id = #{projectInfoId,jdbcType=BIGINT},
            test_log_id = #{testLogId,jdbcType=BIGINT},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </set>
        where proj_auto_test_record_id = #{projAutoTestRecordId,jdbcType=BIGINT}
    </update>
</mapper>
