<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictHospitalTypeMapper">

    <select id="queryAllHospitalType" resultType="com.msun.csm.dao.entity.dict.DictHospitalType">
        select dict_hospital_type_id,
               is_deleted,
               creater_id,
               create_time,
               updater_id,
               update_time,
               hospital_type_code,
               hospital_type_name
        from csm.dict_hospital_type
        where is_deleted = 0
    </select>
</mapper>
