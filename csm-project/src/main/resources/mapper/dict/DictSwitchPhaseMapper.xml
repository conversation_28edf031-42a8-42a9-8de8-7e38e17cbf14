<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictSwitchPhaseMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    <!--@Table csm.dict_switch_phase-->
    <id column="dict_switch_phase_id" jdbcType="BIGINT" property="dictSwitchPhaseId" />
    <result column="dict_switch_phase_name" jdbcType="VARCHAR" property="dictSwitchPhaseName" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="order_no" jdbcType="SMALLINT" property="orderNo" />
    <result column="dict_hospital_dept_id" jdbcType="BIGINT" property="dictHospitalDeptId" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dict_switch_phase_id, dict_switch_phase_name, memo, order_no, dict_hospital_dept_id,
    creater_id, create_time, updater_id, update_time, is_deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.dict_switch_phase
    where dict_switch_phase_id = #{dictSwitchPhaseId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.dict_switch_phase
    where dict_switch_phase_id = #{dictSwitchPhaseId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    insert into csm.dict_switch_phase (dict_switch_phase_id, dict_switch_phase_name,
      memo, order_no, dict_hospital_dept_id,
      creater_id, create_time, updater_id,
      update_time, is_deleted)
    values (#{dictSwitchPhaseId,jdbcType=BIGINT}, #{dictSwitchPhaseName,jdbcType=VARCHAR},
      #{memo,jdbcType=VARCHAR}, #{orderNo,jdbcType=SMALLINT}, #{dictHospitalDeptId,jdbcType=BIGINT},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    insert into csm.dict_switch_phase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictSwitchPhaseId != null">
        dict_switch_phase_id,
      </if>
      <if test="dictSwitchPhaseName != null">
        dict_switch_phase_name,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dictSwitchPhaseId != null">
        #{dictSwitchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="dictSwitchPhaseName != null">
        #{dictSwitchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="dictHospitalDeptId != null">
        #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    update csm.dict_switch_phase
    <set>
      <if test="dictSwitchPhaseName != null">
        dict_switch_phase_name = #{dictSwitchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </set>
    where dict_switch_phase_id = #{dictSwitchPhaseId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    update csm.dict_switch_phase
    set dict_switch_phase_name = #{dictSwitchPhaseName,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=SMALLINT},
      dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT}
    where dict_switch_phase_id = #{dictSwitchPhaseId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.dict_switch_phase
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="dict_switch_phase_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.dictSwitchPhaseName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="dict_hospital_dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.dictHospitalDeptId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where dict_switch_phase_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.dictSwitchPhaseId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.dict_switch_phase
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="dict_switch_phase_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dictSwitchPhaseName != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.dictSwitchPhaseName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.memo != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="dict_hospital_dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dictHospitalDeptId != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.dictHospitalDeptId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when dict_switch_phase_id = #{item.dictSwitchPhaseId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where dict_switch_phase_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.dictSwitchPhaseId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.dict_switch_phase
    (dict_switch_phase_id, dict_switch_phase_name, memo, order_no, dict_hospital_dept_id,
      creater_id, create_time, updater_id, update_time, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dictSwitchPhaseId,jdbcType=BIGINT}, #{item.dictSwitchPhaseName,jdbcType=VARCHAR},
        #{item.memo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=SMALLINT}, #{item.dictHospitalDeptId,jdbcType=BIGINT},
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    insert into csm.dict_switch_phase
    (dict_switch_phase_id, dict_switch_phase_name, memo, order_no, dict_hospital_dept_id,
      creater_id, create_time, updater_id, update_time, is_deleted)
    values
    (#{dictSwitchPhaseId,jdbcType=BIGINT}, #{dictSwitchPhaseName,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR},
      #{orderNo,jdbcType=SMALLINT}, #{dictHospitalDeptId,jdbcType=BIGINT}, #{createrId,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
      #{isDeleted,jdbcType=SMALLINT})
    on duplicate key update
    dict_switch_phase_id = #{dictSwitchPhaseId,jdbcType=BIGINT},
    dict_switch_phase_name = #{dictSwitchPhaseName,jdbcType=VARCHAR},
    memo = #{memo,jdbcType=VARCHAR},
    order_no = #{orderNo,jdbcType=SMALLINT},
    dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{isDeleted,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictSwitchPhase">
    <!--@mbg.generated-->
    insert into csm.dict_switch_phase
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictSwitchPhaseId != null">
        dict_switch_phase_id,
      </if>
      <if test="dictSwitchPhaseName != null">
        dict_switch_phase_name,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictSwitchPhaseId != null">
        #{dictSwitchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="dictSwitchPhaseName != null">
        #{dictSwitchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="dictHospitalDeptId != null">
        #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="dictSwitchPhaseId != null">
        dict_switch_phase_id = #{dictSwitchPhaseId,jdbcType=BIGINT},
      </if>
      <if test="dictSwitchPhaseName != null">
        dict_switch_phase_name = #{dictSwitchPhaseName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

  <select id="selectList" resultMap="BaseResultMap">
      <!--@mbg.generated-->
      select
      <include refid="Base_Column_List"/>
      from csm.dict_switch_phase
      where is_deleted = 0
      order by order_no
  </select>
</mapper>
