<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductVsArrangeMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProductVsArrange">
        <!--@mbg.generated-->
        <!--@Table csm.dict_product_vs_arrange-->
        <id column="product_vs_arrange_id" jdbcType="BIGINT" property="productVsArrangeId"/>
        <result column="order_product_id" jdbcType="BIGINT" property="orderProductId"/>
        <result column="arrange_product_id" jdbcType="BIGINT" property="arrangeProductId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_vs_arrange_id, order_product_id, arrange_product_id, is_deleted, creater_id,
        create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_product_vs_arrange
        where product_vs_arrange_id = #{productVsArrangeId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_product_vs_arrange
        where product_vs_arrange_id = #{productVsArrangeId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsArrange">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_arrange
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsArrangeId != null">
                product_vs_arrange_id,
            </if>
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="arrangeProductId != null">
                arrange_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productVsArrangeId != null">
                #{productVsArrangeId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="arrangeProductId != null">
                #{arrangeProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsArrange">
        <!--@mbg.generated-->
        update csm.dict_product_vs_arrange
        <set>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="arrangeProductId != null">
                arrange_product_id = #{arrangeProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where product_vs_arrange_id = #{productVsArrangeId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProductVsArrange">
        <!--@mbg.generated-->
        update csm.dict_product_vs_arrange
        set order_product_id = #{orderProductId,jdbcType=BIGINT},
        arrange_product_id = #{arrangeProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where product_vs_arrange_id = #{productVsArrangeId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product_vs_arrange
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.orderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="arrange_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.arrangeProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where product_vs_arrange_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productVsArrangeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product_vs_arrange
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderProductId != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.orderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="arrange_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.arrangeProductId != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.arrangeProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_vs_arrange_id = #{item.productVsArrangeId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_vs_arrange_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productVsArrangeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_arrange
        (product_vs_arrange_id, order_product_id, arrange_product_id, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productVsArrangeId,jdbcType=BIGINT}, #{item.orderProductId,jdbcType=BIGINT},
            #{item.arrangeProductId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProductVsArrange">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_arrange
        (product_vs_arrange_id, order_product_id, arrange_product_id, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        (#{productVsArrangeId,jdbcType=BIGINT}, #{orderProductId,jdbcType=BIGINT}, #{arrangeProductId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        product_vs_arrange_id = #{productVsArrangeId,jdbcType=BIGINT},
        order_product_id = #{orderProductId,jdbcType=BIGINT},
        arrange_product_id = #{arrangeProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsArrange">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_arrange
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsArrangeId != null">
                product_vs_arrange_id,
            </if>
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="arrangeProductId != null">
                arrange_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsArrangeId != null">
                #{productVsArrangeId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="arrangeProductId != null">
                #{arrangeProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productVsArrangeId != null">
                product_vs_arrange_id = #{productVsArrangeId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="arrangeProductId != null">
                arrange_product_id = #{arrangeProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectByProductIds" resultType="com.msun.csm.model.dto.ProductIdContrastDTO">
        select order_product_id as original_product_id, arrange_product_id as contrast_product_id
        from csm.dict_product_vs_arrange
        where is_Deleted = 0
        and order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findDictProductVsArrangeList" resultType="com.msun.csm.model.vo.dict.DictProductVsArrangeVO">
        select dpva.product_vs_arrange_id,
        dpva.order_product_id, dp1.product_name as order_product_name,
        dpva.arrange_product_id, dp2.product_name as arrange_product_name
        from csm.dict_product_vs_arrange dpva
        left join csm.dict_product dp1 on dpva.order_product_id = dp1.yy_product_id
        left join csm.dict_product dp2 on dpva.arrange_product_id = dp2.yy_product_id
        where 1 = 1
        <if test='orderProductName != null and orderProductName != "" '>
            and dpva.order_product_id in
            (select yy_product_id from csm.dict_product where product_name ilike concat('%', #{orderProductName}, '%') and yy_is_cloud = 1 and is_deleted = 0)
        </if>
        <if test='arrangeProductName != null and arrangeProductName != "" '>
            and dpva.arrange_product_id in
            (select yy_product_id from csm.dict_product where product_name ilike concat('%', #{arrangeProductName}, '%') and yy_is_cloud = 1 and is_deleted = 0)
        </if>
        and dpva.is_deleted = 0
        and dp1.yy_is_cloud = 1 and dp1.is_deleted = 0
        and dp2.is_deleted = 0
        order by dp1.yy_product_id,dp1.create_time desc
    </select>
</mapper>
