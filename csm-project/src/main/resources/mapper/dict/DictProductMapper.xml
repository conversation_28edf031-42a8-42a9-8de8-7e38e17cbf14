<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        <!--@Table csm.dict_product-->
        <id column="product_dict_id" jdbcType="BIGINT" property="productDictId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_team_id" jdbcType="BIGINT" property="productTeamId"/>
        <result column="product_leader_yy_id" jdbcType="BIGINT" property="productLeaderYyId"/>
        <result column="product_develop_type" jdbcType="SMALLINT" property="productDevelopType"/>
        <result column="product_resolve_id" jdbcType="BIGINT" property="productResolveId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_dict_id, yy_product_id, product_name, product_type, product_team_id, product_leader_yy_id,
        product_develop_type, product_resolve_id, is_deleted, creater_id, create_time, updater_id,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_product
        where product_dict_id = #{productDictId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_product
        where product_dict_id = #{productDictId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        insert into csm.dict_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productDictId != null">
                product_dict_id,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="productType != null">
                product_type,
            </if>
            <if test="productTeamId != null">
                product_team_id,
            </if>
            <if test="productLeaderYyId != null">
                product_leader_yy_id,
            </if>
            <if test="productDevelopType != null">
                product_develop_type,
            </if>
            <if test="productResolveId != null">
                product_resolve_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productDictId != null">
                #{productDictId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                #{productType,jdbcType=VARCHAR},
            </if>
            <if test="productTeamId != null">
                #{productTeamId,jdbcType=BIGINT},
            </if>
            <if test="productLeaderYyId != null">
                #{productLeaderYyId,jdbcType=BIGINT},
            </if>
            <if test="productDevelopType != null">
                #{productDevelopType,jdbcType=SMALLINT},
            </if>
            <if test="productResolveId != null">
                #{productResolveId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        update csm.dict_product
        <set>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="productTeamId != null">
                product_team_id = #{productTeamId,jdbcType=BIGINT},
            </if>
            <if test="productLeaderYyId != null">
                product_leader_yy_id = #{productLeaderYyId,jdbcType=BIGINT},
            </if>
            <if test="productDevelopType != null">
                product_develop_type = #{productDevelopType,jdbcType=SMALLINT},
            </if>
            <if test="productResolveId != null">
                product_resolve_id = #{productResolveId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where product_dict_id = #{productDictId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        update csm.dict_product
        set yy_product_id = #{yyProductId,jdbcType=BIGINT},
        product_name = #{productName,jdbcType=VARCHAR},
        product_type = #{productType,jdbcType=VARCHAR},
        product_team_id = #{productTeamId,jdbcType=BIGINT},
        product_leader_yy_id = #{productLeaderYyId,jdbcType=BIGINT},
        product_develop_type = #{productDevelopType,jdbcType=SMALLINT},
        product_resolve_id = #{productResolveId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where product_dict_id = #{productDictId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.yyProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.productType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.productTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_leader_yy_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.productLeaderYyId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="product_develop_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.productDevelopType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="product_resolve_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.productResolveId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where product_dict_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productDictId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProductId != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.yyProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productName != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.productName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productType != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.productType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productTeamId != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.productTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_leader_yy_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productLeaderYyId != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.productLeaderYyId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_develop_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productDevelopType != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.productDevelopType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="product_resolve_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productResolveId != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.productResolveId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_dict_id = #{item.productDictId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_dict_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productDictId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_product
        (product_dict_id, yy_product_id, product_name, product_type, product_team_id, product_leader_yy_id,
        product_develop_type, product_resolve_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productDictId,jdbcType=BIGINT}, #{item.yyProductId,jdbcType=BIGINT},
            #{item.productName,jdbcType=VARCHAR},
            #{item.productType,jdbcType=VARCHAR}, #{item.productTeamId,jdbcType=BIGINT},
            #{item.productLeaderYyId,jdbcType=BIGINT},
            #{item.productDevelopType,jdbcType=SMALLINT}, #{item.productResolveId,jdbcType=BIGINT},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        insert into csm.dict_product
        (product_dict_id, yy_product_id, product_name, product_type, product_team_id, product_leader_yy_id,
        product_develop_type, product_resolve_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        (#{productDictId,jdbcType=BIGINT}, #{yyProductId,jdbcType=BIGINT}, #{productName,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR}, #{productTeamId,jdbcType=BIGINT}, #{productLeaderYyId,jdbcType=BIGINT},
        #{productDevelopType,jdbcType=SMALLINT}, #{productResolveId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        product_dict_id = #{productDictId,jdbcType=BIGINT},
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
        product_name = #{productName,jdbcType=VARCHAR},
        product_type = #{productType,jdbcType=VARCHAR},
        product_team_id = #{productTeamId,jdbcType=BIGINT},
        product_leader_yy_id = #{productLeaderYyId,jdbcType=BIGINT},
        product_develop_type = #{productDevelopType,jdbcType=SMALLINT},
        product_resolve_id = #{productResolveId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>

    <select id="selectOneByProductName" resultType="com.msun.csm.dao.entity.dict.DictProduct">
        select product_dict_id,
               yy_product_id,
               product_name,
               product_type,
               product_team_id,
               product_leader_yy_id,
               product_develop_type,
               product_resolve_id,
               is_deleted,
               creater_id,
               create_time,
               updater_id,
               update_time
        from csm.dict_product
        where product_name = #{productName} and is_deleted = 0
    </select>

    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        insert into csm.dict_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productDictId != null">
                product_dict_id,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="productType != null">
                product_type,
            </if>
            <if test="productTeamId != null">
                product_team_id,
            </if>
            <if test="productLeaderYyId != null">
                product_leader_yy_id,
            </if>
            <if test="productDevelopType != null">
                product_develop_type,
            </if>
            <if test="productResolveId != null">
                product_resolve_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productDictId != null">
                #{productDictId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                #{productType,jdbcType=VARCHAR},
            </if>
            <if test="productTeamId != null">
                #{productTeamId,jdbcType=BIGINT},
            </if>
            <if test="productLeaderYyId != null">
                #{productLeaderYyId,jdbcType=BIGINT},
            </if>
            <if test="productDevelopType != null">
                #{productDevelopType,jdbcType=SMALLINT},
            </if>
            <if test="productResolveId != null">
                #{productResolveId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productDictId != null">
                product_dict_id = #{productDictId,jdbcType=BIGINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=BIGINT},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="productTeamId != null">
                product_team_id = #{productTeamId,jdbcType=BIGINT},
            </if>
            <if test="productLeaderYyId != null">
                product_leader_yy_id = #{productLeaderYyId,jdbcType=BIGINT},
            </if>
            <if test="productDevelopType != null">
                product_develop_type = #{productDevelopType,jdbcType=SMALLINT},
            </if>
            <if test="productResolveId != null">
                product_resolve_id = #{productResolveId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="findByProductIds" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
        concat(dp.product_name, '-', dpm.yy_module_name) as name
        from csm.dict_product dp
        left join csm.dict_product_vs_modules dpm
        on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
        where dp.is_deleted = 0
        and dp.yy_product_id in
        <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        or dpm.yy_module_id in
        <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by dp.order_no, dpm.yy_module_code
    </select>

    <select id="findByProjectInfoId" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
        concat(dp.product_name, '-', dpm.yy_module_name) as name
        from csm.dict_product dp
        left join csm.dict_product_vs_modules dpm on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
        where dp.is_deleted = 0
        and dp.yy_product_id in (select product_deliver_id
        from csm.proj_product_deliver_record
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        <if test="productId != null">
            and product_deliver_id = #{productId}
        </if>
        )
        or dpm.yy_module_id in (select product_deliver_id
        from csm.proj_product_deliver_record
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        <if test="productId != null">
            and product_deliver_id = #{productId}
        </if>
        )
        order by dp.order_no, dpm.yy_module_code
    </select>

    <select id="getDictProductByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.dict_product
        where product_name like concat('%',#{productName},'%')

    </select>
    <select id="selectByKeyword" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select yy_product_id "id",
        product_name "name"
        from csm.dict_product t
        where is_deleted = 0
        and product_type = '1'
        and product_level = 1
        <if test="keyword != null">
            and t.product_name like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
        order by t.yy_product_id
    </select>

    <select id="findDictProductList" resultType="com.msun.csm.model.vo.dict.DictProductVO">
        select dp.product_dict_id,dp.yy_product_id,dp.product_name,dp.product_type,
        (select string_agg(yy_module_name,'、') from csm.dict_product_vs_modules
        where yy_product_id = dp.yy_product_id and is_deleted = 0) as product_modules,
        case when dp.product_dict_id > 0 then 1 else 2 end as product_source
        from csm.dict_product dp
        where 1 = 1
        <if test='productName != null and productName != "" '>
            and dp.product_name ilike concat ('%', #{productName}, '%')
        </if>
        <if test="productSource != null and productSource == 1 ">
            and dp.product_dict_id > 0
        </if>
        <if test="productSource != null and productSource == 2 ">
            and dp.product_dict_id &lt; 0
        </if>
        and yy_is_cloud = 1 and is_deleted = 0
        order by yy_product_id,create_time desc
    </select>

    <select id="getMinProductDictId" resultType="java.lang.Long">
        select min(product_dict_id) - 1
        from csm.dict_product
    </select>

    <select id="findDictProducts" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select yy_product_id as id,product_name as name
        from csm.dict_product
        where 1 = 1
        <if test='productName != null and productName != "" '>
            and product_name ilike concat ('%', #{productName}, '%')
        </if>
        and yy_is_cloud = 1 and is_deleted = 0
        order by order_no
    </select>

    <update id="deleteOrderVsDeliver">
        update csm.dict_product_vs_deliver
        set is_deleted = 1
        where order_product_id = #{productId}
           or deliver_product_id = #{productId}
    </update>

    <update id="deleteOrderVsArrange">
        update csm.dict_product_vs_arrange
        set is_deleted = 1
        where order_product_id = #{productId}
           or arrange_product_id = #{productId}
    </update>

    <update id="deleteOrderVsEmpower">
        update csm.dict_product_vs_empower
        set is_deleted = 1
        where order_product_id = #{productId}
    </update>

    <select id="getProductByYyProductId" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select yy_product_id as id,
               product_name  as name
        from csm.dict_product
        where is_deleted = 0
          and yy_product_id = #{yyProductId}
    </select>
</mapper>
