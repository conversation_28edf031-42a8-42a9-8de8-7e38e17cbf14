<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictMessageTypeMapper">
    <select id="getDictMessageTypeById" resultType="com.msun.csm.dao.entity.sys.DictMessageType">
        select * from csm.dict_message_type where id = #{id, jdbcType=BIGINT} and is_deleted = 0
    </select>
</mapper>
