<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictExamImportStepMapper">

    <select id="findExamHospitalStepList" parameterType="com.msun.csm.model.dto.FindExamImportStepDTO"
            resultType="com.msun.csm.model.vo.ProjExamHospitalStepVO">
        select min(eis.step_id) as step_id,min(eis.step_name) as step_name,
        min(eis.step_order) as step_order,min(eis.step_code) as step_code,
        max(case when hsd.step_state is null then 0 else 2 end) as step_state
        from csm.dict_exam_import_step eis
        left join csm.proj_exam_hospital_step_detail hsd on eis.step_code = hsd.step_code
        and hsd.custom_info_id = #{customInfoId} and hsd.exam_hospital_info_id = #{examHospitalInfoId}
        and hsd.is_deleted = 0
        where eis.is_deleted = 0
        group by eis.step_code
        order by min(eis.step_order)
    </select>

    <select id="findNotExcuteSteps" parameterType="com.msun.csm.model.dto.FindExamImportStepDTO"
            resultType="com.msun.csm.model.vo.ProjExamHospitalStepVO">
        select t.* from
        (select min(eis.step_id)                                        as step_id,
                min(eis.step_name)                                      as step_name,
                min(eis.step_order)                                     as step_order,
                min(eis.step_code)                                      as step_code,
                max(case when hsd.step_state is null then 0 else 2 end) as step_state
         from csm.dict_exam_import_step eis
         left join csm.proj_exam_hospital_step_detail hsd on eis.step_code = hsd.step_code
         and hsd.custom_info_id = #{customInfoId} and hsd.exam_hospital_info_id = #{examHospitalInfoId}
         and hsd.is_deleted = 0
         where eis.is_deleted = 0
         group by eis.step_code
         order by min(eis.step_order)
         ) t where t.step_state = 0
    </select>
</mapper>