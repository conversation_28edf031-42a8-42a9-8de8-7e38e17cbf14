<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictEquipTypeMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        <!--@Table csm.dict_equip_type-->
        <id column="equip_type_id" jdbcType="BIGINT" property="equipTypeId"/>
        <result column="equip_type_name" jdbcType="VARCHAR" property="equipTypeName"/>
        <result column="equip_class_id" jdbcType="BIGINT" property="equipClassId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        equip_type_id, equip_type_name, equip_class_id, is_deleted, creater_id, create_time,
        updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_equip_type
        where equip_type_id = #{equipTypeId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_equip_type
        where equip_type_id = #{equipTypeId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        insert into csm.dict_equip_type (equip_type_id, equip_type_name, equip_class_id,
        is_deleted, creater_id, create_time,
        updater_id, update_time)
        values (#{equipTypeId,jdbcType=BIGINT}, #{equipTypeName,jdbcType=VARCHAR}, #{equipClassId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        insert into csm.dict_equip_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipTypeId != null">
                equip_type_id,
            </if>
            <if test="equipTypeName != null">
                equip_type_name,
            </if>
            <if test="equipClassId != null">
                equip_class_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipTypeId != null">
                #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipClassId != null">
                #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        update csm.dict_equip_type
        <set>
            <if test="equipTypeName != null">
                equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipClassId != null">
                equip_class_id = #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where equip_type_id = #{equipTypeId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        update csm.dict_equip_type
        set equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
        equip_class_id = #{equipClassId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where equip_type_id = #{equipTypeId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_equip_type
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="equip_type_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.equipTypeName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_class_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.equipClassId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where equip_type_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.equipTypeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_equip_type
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="equip_type_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipTypeName != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then
                        #{item.equipTypeName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_class_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipClassId != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then
                        #{item.equipClassId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when equip_type_id = #{item.equipTypeId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where equip_type_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.equipTypeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_equip_type
        (equip_type_id, equip_type_name, equip_class_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.equipTypeId,jdbcType=BIGINT}, #{item.equipTypeName,jdbcType=VARCHAR},
            #{item.equipClassId,jdbcType=BIGINT},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        insert into csm.dict_equip_type
        (equip_type_id, equip_type_name, equip_class_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        (#{equipTypeId,jdbcType=BIGINT}, #{equipTypeName,jdbcType=VARCHAR}, #{equipClassId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        equip_type_id = #{equipTypeId,jdbcType=BIGINT},
        equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
        equip_class_id = #{equipClassId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictEquipType">
        <!--@mbg.generated-->
        insert into csm.dict_equip_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipTypeId != null">
                equip_type_id,
            </if>
            <if test="equipTypeName != null">
                equip_type_name,
            </if>
            <if test="equipClassId != null">
                equip_class_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipTypeId != null">
                #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipClassId != null">
                #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="equipTypeId != null">
                equip_type_id = #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipTypeName != null">
                equip_type_name = #{equipTypeName,jdbcType=VARCHAR},
            </if>
            <if test="equipClassId != null">
                equip_class_id = #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
