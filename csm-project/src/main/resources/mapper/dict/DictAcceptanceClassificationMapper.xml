<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictAcceptanceClassificationMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProduct">
        <!--@mbg.generated-->
        <!--@Table csm.dict_product-->
        <id column="product_dict_id" jdbcType="BIGINT" property="productDictId"/>
        <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_team_id" jdbcType="BIGINT" property="productTeamId"/>
        <result column="product_leader_yy_id" jdbcType="BIGINT" property="productLeaderYyId"/>
        <result column="product_develop_type" jdbcType="SMALLINT" property="productDevelopType"/>
        <result column="product_resolve_id" jdbcType="BIGINT" property="productResolveId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_dict_id, yy_product_id, product_name, product_type, product_team_id, product_leader_yy_id,
        product_develop_type, product_resolve_id, is_deleted, creater_id, create_time, updater_id,
        update_time
    </sql>

</mapper>
