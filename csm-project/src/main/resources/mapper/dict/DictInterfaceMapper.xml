<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictInterfaceMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        <!--@Table csm.dict_interface-->
        <id column="dict_interface_id" jdbcType="BIGINT" property="dictInterfaceId"/>
        <result column="dict_interface_name" jdbcType="VARCHAR" property="dictInterfaceName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        dict_interface_id, dict_interface_name, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_interface
        where dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_interface
        where dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        insert into csm.dict_interface (dict_interface_id, dict_interface_name,
        create_time)
        values (#{dictInterfaceId,jdbcType=BIGINT}, #{dictInterfaceName,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        insert into csm.dict_interface
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceId != null">
                dict_interface_id,
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceId != null">
                #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        update csm.dict_interface
        <set>
            <if test="dictInterfaceName != null">
                dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        update csm.dict_interface
        set dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_interface
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_interface_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_interface_id = #{item.dictInterfaceId,jdbcType=BIGINT} then
                    #{item.dictInterfaceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_interface_id = #{item.dictInterfaceId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where dict_interface_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictInterfaceId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_interface
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_interface_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictInterfaceName != null">
                        when dict_interface_id = #{item.dictInterfaceId,jdbcType=BIGINT} then
                        #{item.dictInterfaceName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when dict_interface_id = #{item.dictInterfaceId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where dict_interface_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictInterfaceId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_interface
        (dict_interface_id, dict_interface_name, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictInterfaceId,jdbcType=BIGINT}, #{item.dictInterfaceName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        insert into csm.dict_interface
        (dict_interface_id, dict_interface_name, create_time)
        values
        (#{dictInterfaceId,jdbcType=BIGINT}, #{dictInterfaceName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
        )
        on duplicate key update
        dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT},
        dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictInterface">
        <!--@mbg.generated-->
        insert into csm.dict_interface
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceId != null">
                dict_interface_id,
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceId != null">
                #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="dictInterfaceId != null">
                dict_interface_id = #{dictInterfaceId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceName != null">
                dict_interface_name = #{dictInterfaceName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
