<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductLogMapper">

    <select id="selectDictProductLogByPage" parameterType="com.msun.csm.model.dto.DictProductLogPageDTO"
            resultType="com.msun.csm.model.vo.dict.DictProductLogVO">
        select dict_product_log_id,operate_module,operate_module_name,operate_type,
        case operate_type when 1 then '新增' when 2 then '修改' when 3 then '删除' else '未知' end as operate_type_name,
        operate_content,create_time,create_name
        from csm.dict_product_log
        where 1 = 1
        <if test='operateModule !=null and operateModule != "" '>
            and operate_module = #{operateModule}
        </if>
        <if test="operateType !=null and operateType != 0">
            and operate_type = #{operateType}
        </if>
        <if test='startTime !=null and startTime != "" '>
            and create_time >= to_timestamp(#{startTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test='endTime !=null and endTime != "" '>
            and create_time &lt;= to_timestamp(#{endTime}, 'yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test='createName !=null and createName != "" '>
            and create_name like concat('%', #{createName}, '%')
        </if>
        <if test='operateContent !=null and operateContent != "" '>
            and operate_content ilike concat('%', #{operateContent}, '%')
        </if>
        and is_deleted = 0
        order by create_time desc
    </select>
</mapper>