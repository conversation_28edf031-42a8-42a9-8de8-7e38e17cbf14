<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictMilestoneNodeMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        <!--@Table csm.dict_milestone_node-->
        <id column="milestone_node_id" jdbcType="BIGINT" property="milestoneNodeId"/>
        <result column="milestone_node_name" jdbcType="VARCHAR" property="milestoneNodeName"/>
        <result column="milestone_node_url" jdbcType="VARCHAR" property="milestoneNodeUrl"/>
        <result column="invalid_flag" jdbcType="CHAR" property="invalidFlag"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_component" jdbcType="VARCHAR" property="isComponent"/>
        <result column="simple_milestone_node_name" jdbcType="VARCHAR" property="simpleMilestoneNodeName"/>
        <result column="milestone_node_code" jdbcType="VARCHAR" property="milestoneNodeCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        milestone_node_id, milestone_node_name, milestone_node_url, invalid_flag, creater_id, create_time,
        updater_id, update_time, is_component, simple_milestone_node_name, milestone_node_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_milestone_node
        where milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_milestone_node
        where milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
    </delete>
    <select id="findMilestoneNodeName" parameterType="com.msun.csm.dao.entity.config.ConfigMilestoneNode" resultMap="BaseResultMap">
        select
            dmn.simple_milestone_node_name,
            dmn.milestone_node_code
        from
            csm.config_milestone_node a
                left join csm.dict_milestone_node dmn on
                a.milestone_node_id = dmn.milestone_node_id
                left join csm.dict_project_stage dps on
                a.project_stage_id = dps .id
        where
        dmn.invalid_flag=0
           and  dps.project_stage_code = 'stage_survey'
          and a.plan_flag = 1
        <if test="monomerFlag != null">
            and a.monomer_flag='1'
        </if>
        <if test="regionFlag != null">
            and a.region_flag='1'
        </if>
    </select>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        insert into csm.dict_milestone_node (milestone_node_id, milestone_node_name, milestone_node_url,
        invalid_flag, creater_id, create_time,
        updater_id, update_time, is_component, simple_milestone_node_name, milestone_node_code
        )
        values (#{id,jdbcType=BIGINT}, #{milestoneNodeName,jdbcType=VARCHAR}, #{milestoneNodeUrl,jdbcType=VARCHAR},
        #{invalidFlag,jdbcType=CHAR}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isComponent,jdbcType=VARCHAR},
        #{simpleMilestoneNodeName,jdbcType=VARCHAR}, #{milestoneNodeCode,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        insert into csm.dict_milestone_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="milestoneNodeId != null">
                milestone_node_id,
            </if>
            <if test="milestoneNodeName != null">
                milestone_node_name,
            </if>
            <if test="milestoneNodeUrl != null">
                milestone_node_url,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isComponent != null">
                is_component,
            </if>
            <if test="simpleMilestoneNodeName != null">
                simple_milestone_node_name,
            </if>
            <if test="milestoneNodeCode != null">
                milestone_node_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="milestoneNodeId != null">
                #{milestoneNodeId,jdbcType=BIGINT},
            </if>
            <if test="milestoneNodeName != null">
                #{milestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeUrl != null">
                #{milestoneNodeUrl,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isComponent != null">
                #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="simpleMilestoneNodeName != null">
                #{simpleMilestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeCode != null">
                #{milestoneNodeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        update csm.dict_milestone_node
        <set>
            <if test="milestoneNodeName != null">
                milestone_node_name = #{milestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeUrl != null">
                milestone_node_url = #{milestoneNodeUrl,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isComponent != null">
                is_component = #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="simpleMilestoneNodeName != null">
                simple_milestone_node_name = #{simpleMilestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeCode != null">
                milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
            </if>
        </set>
        where milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        update csm.dict_milestone_node
        set milestone_node_name = #{milestoneNodeName,jdbcType=VARCHAR},
        milestone_node_url = #{milestoneNodeUrl,jdbcType=VARCHAR},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_component = #{isComponent,jdbcType=VARCHAR},
        simple_milestone_node_name = #{simpleMilestoneNodeName,jdbcType=VARCHAR},
        milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR}
        where milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_milestone_node
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="milestone_node_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.milestoneNodeName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="milestone_node_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.milestoneNodeUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_component = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.isComponent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="simple_milestone_node_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.simpleMilestoneNodeName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="milestone_node_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.milestoneNodeCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where milestone_node_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.milestoneNodeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_milestone_node
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="milestone_node_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.milestoneNodeName != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.milestoneNodeName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="milestone_node_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.milestoneNodeUrl != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.milestoneNodeUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidFlag != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_component = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isComponent != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.isComponent,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="simple_milestone_node_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.simpleMilestoneNodeName != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.simpleMilestoneNodeName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="milestone_node_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.milestoneNodeCode != null">
                        when milestone_node_id = #{item.milestoneNodeId,jdbcType=BIGINT} then #{item.milestoneNodeCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where milestone_node_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.milestoneNodeId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_milestone_node
        (milestone_node_id, milestone_node_name, milestone_node_url, invalid_flag, creater_id, create_time,
        updater_id, update_time, is_component, simple_milestone_node_name, milestone_node_code
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.milestoneNodeId,jdbcType=BIGINT}, #{item.milestoneNodeName,jdbcType=VARCHAR},
            #{item.milestoneNodeUrl,jdbcType=VARCHAR},
            #{item.invalidFlag,jdbcType=CHAR}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isComponent,jdbcType=VARCHAR},
            #{item.simpleMilestoneNodeName,jdbcType=VARCHAR}, #{item.milestoneNodeCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        insert into csm.dict_milestone_node
        (milestone_node_id, milestone_node_name, milestone_node_url, invalid_flag, creater_id, create_time,
        updater_id, update_time, is_component, simple_milestone_node_name, milestone_node_code)
        values
        (#{id,jdbcType=BIGINT}, #{milestoneNodeName,jdbcType=VARCHAR}, #{milestoneNodeUrl,jdbcType=VARCHAR},
        #{invalidFlag,jdbcType=CHAR}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isComponent,jdbcType=VARCHAR},#{simpleMilestoneNodeName,jdbcType=VARCHAR},#{milestoneNodeCode,jdbcType=VARCHAR})
        on duplicate key update
        milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT},
        milestone_node_name = #{milestoneNodeName,jdbcType=VARCHAR},
        milestone_node_url = #{milestoneNodeUrl,jdbcType=VARCHAR},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_component = #{isComponent,jdbcType=VARCHAR},
        simple_milestone_node_name = #{simpleMilestoneNodeName,jdbcType=VARCHAR},
        milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictMilestoneNode">
        <!--@mbg.generated-->
        insert into csm.dict_milestone_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="milestoneNodeId != null">
                milestone_node_id,
            </if>
            <if test="milestoneNodeName != null">
                milestone_node_name,
            </if>
            <if test="milestoneNodeUrl != null">
                milestone_node_url,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isComponent != null">
                is_component,
            </if>
            <if test="simpleMilestoneNodeName != null">
                simple_milestone_node_name,
            </if>
            <if test="milestoneNodeCode != null">
                milestone_node_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="milestoneNodeId != null">
                #{milestoneNodeId,jdbcType=BIGINT},
            </if>
            <if test="milestoneNodeName != null">
                #{milestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeUrl != null">
                #{milestoneNodeUrl,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isComponent != null">
                #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="simpleMilestoneNodeName != null">
                #{simpleMilestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeCode != null">
                #{milestoneNodeCode,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="milestoneNodeId != null">
                milestone_node_id = #{milestoneNodeId,jdbcType=BIGINT},
            </if>
            <if test="milestoneNodeName != null">
                milestone_node_name = #{milestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeUrl != null">
                milestone_node_url = #{milestoneNodeUrl,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isComponent != null">
                is_component = #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="simpleMilestoneNodeName != null">
                simple_milestone_node_name = #{simpleMilestoneNodeName,jdbcType=VARCHAR},
            </if>
            <if test="milestoneNodeCode != null">
                milestone_node_code = #{milestoneNodeCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>
