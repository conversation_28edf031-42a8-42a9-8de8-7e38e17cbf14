<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictFormResourceMapper">
    <resultMap type="com.msun.csm.dao.entity.dict.DictFormResource" id="BaseResultMap">
        <result property="formId" column="form_id" jdbcType="INTEGER"/>
        <result property="formCode" column="form_code" jdbcType="VARCHAR"/>
        <result property="formName" column="form_name" jdbcType="VARCHAR"/>
        <result property="invalidFlag" column="invalid_flag" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="regionEnabled" column="region_enabled" jdbcType="INTEGER"/>
        <result property="previewUrl" column="preview_url" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        form_id, form_code, form_name, invalid_flag, creater_id, create_time, updater_id,
    update_time, region_enabled, preview_url
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.dict_form_resource
        where form_id = #{formId,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from csm.dict_form_resource
        where form_id = #{formId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictFormResource">
        insert into csm.dict_form_resource (form_id, form_code, form_name,
                                            invalid_flag, creater_id, create_time,
                                            updater_id, update_time, region_enabled,
                                            preview_url)
        values (#{formId,jdbcType=BIGINT}, #{formCode,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR},
                #{invalidFlag,jdbcType=SMALLINT}, #{createrId,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{regionEnabled,jdbcType=SMALLINT},
                #{previewUrl,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictFormResource">
        insert into csm.dict_form_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formId != null">
                form_id,
            </if>
            <if test="formCode != null">
                form_code,
            </if>
            <if test="formName != null">
                form_name,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="regionEnabled != null">
                region_enabled,
            </if>
            <if test="previewUrl != null">
                preview_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formId != null">
                #{formId,jdbcType=BIGINT},
            </if>
            <if test="formCode != null">
                #{formCode,jdbcType=VARCHAR},
            </if>
            <if test="formName != null">
                #{formName,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="regionEnabled != null">
                #{regionEnabled,jdbcType=SMALLINT},
            </if>
            <if test="previewUrl != null">
                #{previewUrl,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictFormResource">
        update csm.dict_form_resource
        <set>
            <if test="formCode != null">
                form_code = #{formCode,jdbcType=VARCHAR},
            </if>
            <if test="formName != null">
                form_name = #{formName,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="regionEnabled != null">
                region_enabled = #{regionEnabled,jdbcType=SMALLINT},
            </if>
            <if test="previewUrl != null">
                preview_url = #{previewUrl,jdbcType=VARCHAR},
            </if>
        </set>
        where form_id = #{formId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictFormResource">
        update csm.dict_form_resource
        set form_code = #{formCode,jdbcType=VARCHAR},
            form_name = #{formName,jdbcType=VARCHAR},
            invalid_flag = #{invalidFlag,jdbcType=SMALLINT},
            creater_id = #{createrId,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            region_enabled = #{regionEnabled,jdbcType=SMALLINT},
            preview_url = #{previewUrl,jdbcType=VARCHAR}
        where form_id = #{formId,jdbcType=BIGINT}
    </update>


    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.dict_form_resource
        where invalid_flag=0
    </select>
</mapper>
