<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictCustomerClassMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        <!--@Table csm.dict_customer_class-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="customer_class_name" jdbcType="VARCHAR" property="customerClassName"/>
        <result column="invalid_flag" jdbcType="CHAR" property="invalidFlag"/>
        <result column="creater_id" jdbcType="INTEGER" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, customer_class_name, invalid_flag, creater_id, create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_customer_class
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_customer_class
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        insert into csm.dict_customer_class (id, customer_class_name, invalid_flag,
        creater_id, create_time, updater_id,
        update_time)
        values (#{id,jdbcType=BIGINT}, #{customerClassName,jdbcType=VARCHAR}, #{invalidFlag,jdbcType=CHAR},
        #{createrId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        insert into csm.dict_customer_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="customerClassName != null">
                customer_class_name,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="customerClassName != null">
                #{customerClassName,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        update csm.dict_customer_class
        <set>
            <if test="customerClassName != null">
                customer_class_name = #{customerClassName,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        update csm.dict_customer_class
        set customer_class_name = #{customerClassName,jdbcType=VARCHAR},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        creater_id = #{createrId,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_customer_class
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="customer_class_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.customerClassName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_customer_class
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="customer_class_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customerClassName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.customerClassName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidFlag != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_customer_class
        (id, customer_class_name, invalid_flag, creater_id, create_time, updater_id, update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.customerClassName,jdbcType=VARCHAR}, #{item.invalidFlag,jdbcType=CHAR},
            #{item.createrId,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=INTEGER},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        insert into csm.dict_customer_class
        (id, customer_class_name, invalid_flag, creater_id, create_time, updater_id, update_time
        )
        values
        (#{id,jdbcType=BIGINT}, #{customerClassName,jdbcType=VARCHAR}, #{invalidFlag,jdbcType=CHAR},
        #{createrId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        id = #{id,jdbcType=BIGINT},
        customer_class_name = #{customerClassName,jdbcType=VARCHAR},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        creater_id = #{createrId,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictCustomerClass">
        <!--@mbg.generated-->
        insert into csm.dict_customer_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="customerClassName != null">
                customer_class_name,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="customerClassName != null">
                #{customerClassName,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="customerClassName != null">
                customer_class_name = #{customerClassName,jdbcType=VARCHAR},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
