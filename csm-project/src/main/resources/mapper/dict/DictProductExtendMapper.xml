<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductExtendMapper">
    <resultMap type="com.msun.csm.dao.entity.dict.DictProductExtend" id="DictProductExtendMap">
        <result property="productExtendId" column="product_extend_id" jdbcType="INTEGER"/>
        <result property="yyProductId" column="yy_product_id" jdbcType="INTEGER"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="prepareDataCheckFlag" column="prepare_data_check_flag" jdbcType="INTEGER"/>
        <result property="configIsolationByHospitalFlag" column="config_isolation_by_hospital_flag" jdbcType="INTEGER"/>
        <result property="surveyFlag" column="survey_flag" jdbcType="INTEGER"/>
    </resultMap>
</mapper>
