<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictTaskMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        <!--@Table csm.dict_task-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="task_url" jdbcType="VARCHAR" property="taskUrl"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="invalid_flag" jdbcType="CHAR" property="invalidFlag"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_name, task_url, order_no, invalid_flag, creater_id, create_time, updater_id,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_task
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        insert into csm.dict_task (id, task_name, task_url,
        order_no, invalid_flag, creater_id,
        create_time, updater_id, update_time
        )
        values (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{taskUrl,jdbcType=VARCHAR},
        #{orderNo,jdbcType=INTEGER}, #{invalidFlag,jdbcType=CHAR}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        insert into csm.dict_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="taskUrl != null">
                task_url,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskUrl != null">
                #{taskUrl,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        update csm.dict_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskUrl != null">
                task_url = #{taskUrl,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        update csm.dict_task
        set task_name = #{taskName,jdbcType=VARCHAR},
        task_url = #{taskUrl,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=INTEGER},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="task_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.taskUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskName != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.taskUrl != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.taskUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidFlag != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_task
        (id, task_name, task_url, order_no, invalid_flag, creater_id, create_time, updater_id,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.taskName,jdbcType=VARCHAR}, #{item.taskUrl,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=INTEGER}, #{item.invalidFlag,jdbcType=CHAR}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        insert into csm.dict_task
        (id, task_name, task_url, order_no, invalid_flag, creater_id, create_time, updater_id,
        update_time)
        values
        (#{id,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{taskUrl,jdbcType=VARCHAR},
        #{orderNo,jdbcType=INTEGER}, #{invalidFlag,jdbcType=CHAR}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
        )
        on duplicate key update
        id = #{id,jdbcType=BIGINT},
        task_name = #{taskName,jdbcType=VARCHAR},
        task_url = #{taskUrl,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=INTEGER},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictTask">
        <!--@mbg.generated-->
        insert into csm.dict_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskName != null">
                task_name,
            </if>
            <if test="taskUrl != null">
                task_url,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="taskName != null">
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskUrl != null">
                #{taskUrl,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="taskName != null">
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskUrl != null">
                task_url = #{taskUrl,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
