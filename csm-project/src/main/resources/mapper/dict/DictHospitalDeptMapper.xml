<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictHospitalDeptMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    <!--@Table csm.dict_hospital_dept-->
    <id column="dict_hospital_dept_id" jdbcType="BIGINT" property="dictHospitalDeptId" />
    <result column="dict_hospital_dept_name" jdbcType="VARCHAR" property="dictHospitalDeptName" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="order_no" jdbcType="SMALLINT" property="orderNo" />
    <result column="hospital_dept_class" jdbcType="SMALLINT" property="hospitalDeptClass" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dict_hospital_dept_id, dict_hospital_dept_name, memo, order_no, hospital_dept_class,
    creater_id, create_time, updater_id, update_time, is_deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.dict_hospital_dept
    where dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.dict_hospital_dept
    where dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    insert into csm.dict_hospital_dept (dict_hospital_dept_id, dict_hospital_dept_name,
      memo, order_no, hospital_dept_class,
      creater_id, create_time, updater_id,
      update_time, is_deleted)
    values (#{dictHospitalDeptId,jdbcType=BIGINT}, #{dictHospitalDeptName,jdbcType=VARCHAR},
      #{memo,jdbcType=VARCHAR}, #{orderNo,jdbcType=SMALLINT}, #{hospitalDeptClass,jdbcType=SMALLINT},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    insert into csm.dict_hospital_dept
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id,
      </if>
      <if test="dictHospitalDeptName != null">
        dict_hospital_dept_name,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="hospitalDeptClass != null">
        hospital_dept_class,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dictHospitalDeptId != null">
        #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="dictHospitalDeptName != null">
        #{dictHospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="hospitalDeptClass != null">
        #{hospitalDeptClass,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    update csm.dict_hospital_dept
    <set>
      <if test="dictHospitalDeptName != null">
        dict_hospital_dept_name = #{dictHospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="hospitalDeptClass != null">
        hospital_dept_class = #{hospitalDeptClass,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </set>
    where dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    update csm.dict_hospital_dept
    set dict_hospital_dept_name = #{dictHospitalDeptName,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=SMALLINT},
      hospital_dept_class = #{hospitalDeptClass,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT}
    where dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.dict_hospital_dept
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="dict_hospital_dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.dictHospitalDeptName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="hospital_dept_class = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.hospitalDeptClass,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where dict_hospital_dept_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.dictHospitalDeptId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.dict_hospital_dept
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="dict_hospital_dept_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dictHospitalDeptName != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.dictHospitalDeptName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="memo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.memo != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.memo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="hospital_dept_class = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hospitalDeptClass != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.hospitalDeptClass,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when dict_hospital_dept_id = #{item.dictHospitalDeptId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where dict_hospital_dept_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.dictHospitalDeptId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.dict_hospital_dept
    (dict_hospital_dept_id, dict_hospital_dept_name, memo, order_no, hospital_dept_class,
      creater_id, create_time, updater_id, update_time, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dictHospitalDeptId,jdbcType=BIGINT}, #{item.dictHospitalDeptName,jdbcType=VARCHAR},
        #{item.memo,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=SMALLINT}, #{item.hospitalDeptClass,jdbcType=SMALLINT},
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    insert into csm.dict_hospital_dept
    (dict_hospital_dept_id, dict_hospital_dept_name, memo, order_no, hospital_dept_class,
      creater_id, create_time, updater_id, update_time, is_deleted)
    values
    (#{dictHospitalDeptId,jdbcType=BIGINT}, #{dictHospitalDeptName,jdbcType=VARCHAR},
      #{memo,jdbcType=VARCHAR}, #{orderNo,jdbcType=SMALLINT}, #{hospitalDeptClass,jdbcType=SMALLINT},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    on duplicate key update
    dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT},
    dict_hospital_dept_name = #{dictHospitalDeptName,jdbcType=VARCHAR},
    memo = #{memo,jdbcType=VARCHAR},
    order_no = #{orderNo,jdbcType=SMALLINT},
    hospital_dept_class = #{hospitalDeptClass,jdbcType=SMALLINT},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{isDeleted,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictHospitalDept">
    <!--@mbg.generated-->
    insert into csm.dict_hospital_dept
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id,
      </if>
      <if test="dictHospitalDeptName != null">
        dict_hospital_dept_name,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="hospitalDeptClass != null">
        hospital_dept_class,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictHospitalDeptId != null">
        #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="dictHospitalDeptName != null">
        #{dictHospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="hospitalDeptClass != null">
        #{hospitalDeptClass,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="dictHospitalDeptId != null">
        dict_hospital_dept_id = #{dictHospitalDeptId,jdbcType=BIGINT},
      </if>
      <if test="dictHospitalDeptName != null">
        dict_hospital_dept_name = #{dictHospitalDeptName,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=SMALLINT},
      </if>
      <if test="hospitalDeptClass != null">
        hospital_dept_class = #{hospitalDeptClass,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

  <select id="findHospitalDeptList" resultType="com.msun.csm.common.model.BaseIdNameResp">
      select dict_hospital_dept_id   as id,
             dict_hospital_dept_name as name
      from csm.dict_hospital_dept
      where is_deleted = 0
      order by order_no
  </select>
</mapper>
