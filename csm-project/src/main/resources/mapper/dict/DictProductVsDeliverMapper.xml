<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductVsDeliverMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProductVsDeliver">
        <!--@mbg.generated-->
        <!--@Table csm.dict_product_vs_deliver-->
        <id column="product_vs_deliver_id" jdbcType="BIGINT" property="productVsDeliverId"/>
        <result column="order_product_id" jdbcType="BIGINT" property="orderProductId"/>
        <result column="deliver_product_id" jdbcType="BIGINT" property="deliverProductId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_vs_deliver_id,
        order_product_id,
        deliver_product_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_product_vs_deliver
        where product_vs_deliver_id = #{productVsDeliverId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.dict_product_vs_deliver
        where product_vs_deliver_id = #{productVsDeliverId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsDeliver">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_deliver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsDeliverId != null">
                product_vs_deliver_id,
            </if>
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="deliverProductId != null">
                deliver_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productVsDeliverId != null">
                #{productVsDeliverId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="deliverProductId != null">
                #{deliverProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsDeliver">
        <!--@mbg.generated-->
        update csm.dict_product_vs_deliver
        <set>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="deliverProductId != null">
                deliver_product_id = #{deliverProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where product_vs_deliver_id = #{productVsDeliverId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProductVsDeliver">
        <!--@mbg.generated-->
        update csm.dict_product_vs_deliver
        set order_product_id = #{orderProductId,jdbcType=BIGINT},
        deliver_product_id = #{deliverProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where product_vs_deliver_id = #{productVsDeliverId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product_vs_deliver
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.orderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="deliver_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.deliverProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where product_vs_deliver_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productVsDeliverId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product_vs_deliver
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderProductId != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.orderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deliver_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deliverProductId != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.deliverProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_vs_deliver_id = #{item.productVsDeliverId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_vs_deliver_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productVsDeliverId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_deliver
        (product_vs_deliver_id, order_product_id, deliver_product_id, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productVsDeliverId,jdbcType=BIGINT}, #{item.orderProductId,jdbcType=BIGINT},
            #{item.deliverProductId,jdbcType=BIGINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProductVsDeliver">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_deliver
        (product_vs_deliver_id, order_product_id, deliver_product_id, is_deleted, creater_id,
        create_time, updater_id, update_time)
        values (#{productVsDeliverId,jdbcType=BIGINT}, #{orderProductId,jdbcType=BIGINT},
        #{deliverProductId,jdbcType=BIGINT},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        product_vs_deliver_id = #{productVsDeliverId,jdbcType=BIGINT}, order_product_id =
        #{orderProductId,jdbcType=BIGINT}, deliver_product_id = #{deliverProductId,jdbcType=BIGINT}, is_deleted =
        #{isDeleted,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time =
        #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time =
        #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsDeliver">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_deliver
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsDeliverId != null">
                product_vs_deliver_id,
            </if>
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="deliverProductId != null">
                deliver_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsDeliverId != null">
                #{productVsDeliverId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="deliverProductId != null">
                #{deliverProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productVsDeliverId != null">
                product_vs_deliver_id = #{productVsDeliverId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="deliverProductId != null">
                deliver_product_id = #{deliverProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectByProductIds" resultType="com.msun.csm.model.dto.ProductIdContrastDTO">
        select order_product_id as original_product_id, deliver_product_id as contrast_product_id
        from csm.dict_product_vs_deliver
        where is_Deleted = 0
        and order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="findDictProductVsDeliverList" resultType="com.msun.csm.model.vo.dict.DictProductVsDeliverVO">
        select dpvd.product_vs_deliver_id,
        dpvd.order_product_id, dp.product_name as order_product_name,
        dpvd.deliver_product_id,dpvd.deliver_product_type
        from csm.dict_product_vs_deliver dpvd
        left join csm.dict_product dp on dpvd.order_product_id = dp.yy_product_id
        where 1 = 1
        <if test='orderProductName != null and orderProductName != "" '>
            and dpvd.order_product_id in
            (select yy_product_id from csm.dict_product where product_name ilike concat('%', #{orderProductName}, '%') and yy_is_cloud = 1 and is_deleted = 0)
        </if>
        <if test='deliverProductName != null and deliverProductName != "" '>
            and (dpvd.deliver_product_id in
            (select yy_product_id from csm.dict_product where product_name ilike concat('%', #{deliverProductName}, '%')
            and yy_is_cloud = 1 and is_deleted = 0)
            or dpvd.deliver_product_id in
            (select yy_module_id from csm.dict_product_vs_modules where yy_module_name ilike concat('%', #{deliverProductName}, '%')
            and is_deleted = 0)
            )
        </if>
        and dpvd.is_deleted = 0
        and dp.yy_is_cloud = 1 and dp.is_deleted = 0
        order by dp.yy_product_id,dp.create_time desc
    </select>

    <select id="getMenuUrl" resultType="java.lang.String">
        select jmd.menu_url,menu_url
        from csm.config_product_job_menu_detail jmd
        left join csm.config_product_job_menu jm on jmd.product_job_menu_id = jm.product_job_menu_id
        where jmd.yy_product_id = #{productId}
        and jm.menu_code = 'basedata'
        limit 1
    </select>

    <select id="findDictProductModules" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select yy_module_id as id,yy_module_name as name
        from csm.dict_product_vs_modules
        where 1 = 1
        <if test='productName != null and productName != "" '>
            and yy_module_name ilike concat ('%', #{productName}, '%')
        </if>
        and is_deleted = 0
    </select>
</mapper>
