<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductFunctionMapper">
    <select id="queryProductFunctionCount" parameterType="com.msun.csm.model.param.QueryProductFunctionListParam2" resultType="int">
        select count(1)
        from csm.dict_product_function dpf
        <include refid="queryWhere">
        </include>
    </select>

    <select id="queryProductFunctionList" parameterType="com.msun.csm.model.param.QueryProductFunctionListParam2" resultType="com.msun.csm.dao.entity.proj.ProductFunctionVO">
        select dpf.id,
        dpf.yy_product_id,
        case
        when dp.product_name is null then concat(product.product_name, '-', dpvm.yy_module_name)
        else dp.product_name
        end as "yyProductName",
        dpf.function_name,
        dpf.function_desc,
        dpf.marking_standard,
        dpf.check_sql,
        dpf.is_delete,
        dpf.peoples_hospital_flag,
        dpf.chinese_hospital_flag,
        dpf.maternal_child_hospital_flag,
        dpf.tumor_hospital_flag,
        dpf.stomatology_hospital_flag,
        dpf.eye_hospital_flag,
        dpf.creater_id as "functionMaintainerUserId",
        functionMaintainer.user_name as "functionMaintainer",
        to_char(dpf.create_time, 'YYYY-MM-DD HH24:MI:SS') as "functionMaintenanceTime",
        dpf.check_sql_maintainer_user_id,
        checkSqlMaintainer.user_name as "checkSqlMaintainer",
        to_char(dpf.check_sql_maintenance_time, 'YYYY-MM-DD HH24:MI:SS') as "checkSqlMaintenanceTime"
        from csm.dict_product_function dpf
        left join csm.dict_product dp on dpf.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
        left join csm.dict_product_vs_modules dpvm on dpf.yy_product_id = dpvm.yy_module_id and dpvm.is_deleted = 0
        left join csm.dict_product product on dpvm.yy_product_id = product.yy_product_id and product.is_deleted = 0
        left join csm.sys_user functionMaintainer on dpf.creater_id = functionMaintainer.sys_user_id and functionMaintainer.is_deleted = 0
        left join csm.sys_user checkSqlMaintainer on dpf.check_sql_maintainer_user_id = checkSqlMaintainer.sys_user_id and checkSqlMaintainer.is_deleted = 0
        <include refid="queryWhere">
        </include>
    </select>

    <sql id="queryWhere">
        <where>
            <if test="yyProductId != null">
                and dpf.yy_product_id = #{yyProductId}
            </if>
            <if test="enabledFlag != null and enabledFlag">
                and dpf.is_delete = 0
            </if>
            <if test="enabledFlag != null and !enabledFlag">
                and dpf.is_delete = 1
            </if>
            <if test="noCheckSql != null and noCheckSql">
                and (dpf.check_sql is null or dpf.check_sql = '')
            </if>
            <if test="noCheckSql != null and !noCheckSql">
                and dpf.check_sql is not null and dpf.check_sql != ''
            </if>
        </where>
    </sql>

    <update id="updateProductFunctionById" parameterType="com.msun.csm.dao.entity.dict.DictProductFunction">
        update csm.dict_product_function
        <set>
            update_time = now(),
            <if test="isDelete != null">
                is_delete = #{isDelete},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId},
            </if>
            <if test="functionName != null and functionName != '' ">
                function_name = #{functionName},
            </if>
            <if test="functionDesc != null and functionDesc != '' ">
                function_desc = #{functionDesc},
            </if>
            <if test="peoplesHospitalFlag != null">
                peoples_hospital_flag = #{peoplesHospitalFlag},
            </if>
            <if test="chineseHospitalFlag != null">
                chinese_hospital_flag = #{chineseHospitalFlag},
            </if>
            <if test="maternalChildHospitalFlag != null">
                maternal_child_hospital_flag = #{maternalChildHospitalFlag},
            </if>
            <if test="tumorHospitalFlag != null">
                tumor_hospital_flag = #{tumorHospitalFlag},
            </if>
            <if test="stomatologyHospitalFlag != null">
                stomatology_hospital_flag = #{stomatologyHospitalFlag},
            </if>
            <if test="eyeHospitalFlag != null">
                eye_hospital_flag = #{eyeHospitalFlag},
            </if>
            <if test="markingStandard != null">
                marking_standard = #{markingStandard},
            </if>
            <if test="checkSql != null">
                check_sql = #{checkSql},
            </if>
            <if test="checkSqlMaintainerUserId != null">
                check_sql_maintainer_user_id = #{checkSqlMaintainerUserId},
            </if>
            <if test="checkSqlMaintenanceTime != null">
                check_sql_maintenance_time = #{checkSqlMaintenanceTime},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
