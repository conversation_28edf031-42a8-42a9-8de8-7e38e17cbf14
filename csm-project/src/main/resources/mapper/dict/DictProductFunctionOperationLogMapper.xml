<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductFunctionOperationLogMapper">

    <sql id="queryWhere">
        <where>
            <if test="yyProductId != null">
                and dpfpl.yy_product_id = #{yyProductId}
            </if>
            <if test="searchKeyword != null">
                and (dpfpl.function_name like concat('%',#{searchKeyword},'%') or dpfpl.function_desc like concat('%',#{searchKeyword},'%'))
            </if>
        </where>
    </sql>

    <select id="queryProductFunctionOperationLogCount" resultType="int">
        select count(1) from csm.dict_product_function_operation_log dpfpl
        <include refid="queryWhere">
        </include>
    </select>

    <select id="queryProductFunctionOperationLogList" resultType="com.msun.csm.dao.entity.proj.ProductFunctionOperationLogVO">
        select dpfpl.log_id as "id",
        dpfpl.dict_product_function_id as "dictProductFunctionId",
        dpfpl.yy_product_id,
        case
        when dp.product_name is null then concat(product.product_name, '-', dpvm.yy_module_name)
        else dp.product_name
        end as "yyProductName",
        dpfpl.function_name,
        dpfpl.function_desc,
        dpfpl.marking_standard,
        dpfpl.check_sql,
        dpfpl.is_delete,
        dpfpl.peoples_hospital_flag,
        dpfpl.chinese_hospital_flag,
        dpfpl.maternal_child_hospital_flag,
        dpfpl.tumor_hospital_flag,
        dpfpl.stomatology_hospital_flag,
        dpfpl.eye_hospital_flag,
        dpfpl.creater_id as "operatorUserId",
        operator.user_name as "operatorName",
        to_char(dpfpl.create_time, 'YYYY-MM-DD HH24:MI:SS') as "operationTime"
        from csm.dict_product_function_operation_log dpfpl
        left join csm.dict_product dp on dpfpl.yy_product_id = dp.yy_product_id and dp.is_deleted = 0
        left join csm.dict_product_vs_modules dpvm on dpfpl.yy_product_id = dpvm.yy_module_id and dpvm.is_deleted = 0
        left join csm.dict_product product on dpvm.yy_product_id = product.yy_product_id and product.is_deleted = 0
        left join csm.sys_user operator on dpfpl.creater_id = operator.sys_user_id and operator.is_deleted = 0
        <include refid="queryWhere">
        </include>
    </select>


</mapper>
