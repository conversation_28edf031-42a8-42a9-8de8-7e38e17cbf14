<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictCityMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        <!--@Table csm.dict_city-->
        <id column="dict_city_id" jdbcType="BIGINT" property="dictCityId"/>
        <result column="dict_city_name" jdbcType="VARCHAR" property="dictCityName"/>
        <result column="input_code" jdbcType="VARCHAR" property="inputCode"/>
        <result column="full_code" jdbcType="VARCHAR" property="fullCode"/>
        <result column="order_no" jdbcType="BIGINT" property="orderNo"/>
        <result column="invalid_flag" jdbcType="CHAR" property="invalidFlag"/>
        <result column="dict_province_id" jdbcType="BIGINT" property="dictProvinceId"/>
        <result column="wb_code" jdbcType="VARCHAR" property="wbCode"/>
        <result column="his_org_id" jdbcType="BIGINT" property="hisOrgId"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="SMALLINT" property="version"/>
        <result column="city_code" jdbcType="VARCHAR" property="cityCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        dict_city_id, dict_city_name, input_code, full_code, order_no, invalid_flag, dict_province_id,
        wb_code, his_org_id, creater_id, create_time, updater_id, update_time, version, city_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_city
        where dict_city_id = #{dictCityId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_city
        where dict_city_id = #{dictCityId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        insert into csm.dict_city (dict_city_id, dict_city_name, input_code,
        full_code, order_no, invalid_flag,
        dict_province_id, wb_code, his_org_id,
        creater_id, create_time, updater_id,
        update_time, version, city_code
        )
        values (#{dictCityId,jdbcType=BIGINT}, #{dictCityName,jdbcType=VARCHAR}, #{inputCode,jdbcType=VARCHAR},
        #{fullCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=BIGINT}, #{invalidFlag,jdbcType=CHAR},
        #{dictProvinceId,jdbcType=BIGINT}, #{wbCode,jdbcType=VARCHAR}, #{hisOrgId,jdbcType=BIGINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{version,jdbcType=SMALLINT}, #{cityCode,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        insert into csm.dict_city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictCityId != null">
                dict_city_id,
            </if>
            <if test="dictCityName != null">
                dict_city_name,
            </if>
            <if test="inputCode != null">
                input_code,
            </if>
            <if test="fullCode != null">
                full_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="dictProvinceId != null">
                dict_province_id,
            </if>
            <if test="wbCode != null">
                wb_code,
            </if>
            <if test="hisOrgId != null">
                his_org_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictCityId != null">
                #{dictCityId,jdbcType=BIGINT},
            </if>
            <if test="dictCityName != null">
                #{dictCityName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="dictProvinceId != null">
                #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="wbCode != null">
                #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        update csm.dict_city
        <set>
            <if test="dictCityName != null">
                dict_city_name = #{dictCityName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                input_code = #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="dictProvinceId != null">
                dict_province_id = #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="wbCode != null">
                wb_code = #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                his_org_id = #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
        </set>
        where dict_city_id = #{dictCityId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        update csm.dict_city
        set dict_city_name = #{dictCityName,jdbcType=VARCHAR},
        input_code = #{inputCode,jdbcType=VARCHAR},
        full_code = #{fullCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=BIGINT},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        dict_province_id = #{dictProvinceId,jdbcType=BIGINT},
        wb_code = #{wbCode,jdbcType=VARCHAR},
        his_org_id = #{hisOrgId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        version = #{version,jdbcType=SMALLINT},
        city_code = #{cityCode,jdbcType=VARCHAR}
        where dict_city_id = #{dictCityId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_city
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_city_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.dictCityName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.inputCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="full_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.fullCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="dict_province_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.dictProvinceId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="wb_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.wbCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then now()
                </foreach>
            </trim>
            <trim prefix="city_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.cityCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where dict_city_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictCityId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_city
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_city_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictCityName != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then
                        #{item.dictCityName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="input_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inputCode != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.inputCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="full_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fullCode != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.fullCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidFlag != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="dict_province_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictProvinceId != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then
                        #{item.dictProvinceId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="wb_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.wbCode != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.wbCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisOrgId != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.hisOrgId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.version != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.version,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="city_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cityCode != null">
                        when dict_city_id = #{item.dictCityId,jdbcType=BIGINT} then #{item.cityCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where dict_city_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictCityId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_city
        (dict_city_id, dict_city_name, input_code, full_code, order_no, invalid_flag, dict_province_id,
        wb_code, creater_id, create_time, updater_id, update_time,
        city_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictCityId,jdbcType=BIGINT}, #{item.dictCityName,jdbcType=VARCHAR},
            #{item.inputCode,jdbcType=VARCHAR},
            #{item.fullCode,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=BIGINT}, #{item.invalidFlag,jdbcType=CHAR},
            #{item.dictProvinceId,jdbcType=BIGINT}, #{item.wbCode,jdbcType=VARCHAR},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.cityCode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        insert into csm.dict_city
        (dict_city_id, dict_city_name, input_code, full_code, order_no, invalid_flag, dict_province_id,
        wb_code, his_org_id, creater_id, create_time, updater_id, update_time, version,
        city_code)
        values
        (#{dictCityId,jdbcType=BIGINT}, #{dictCityName,jdbcType=VARCHAR}, #{inputCode,jdbcType=VARCHAR},
        #{fullCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=BIGINT}, #{invalidFlag,jdbcType=CHAR},
        #{dictProvinceId,jdbcType=BIGINT}, #{wbCode,jdbcType=VARCHAR}, #{hisOrgId,jdbcType=BIGINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{version,jdbcType=SMALLINT}, #{cityCode,jdbcType=VARCHAR}
        )
        on duplicate key update
        dict_city_id = #{dictCityId,jdbcType=BIGINT},
        dict_city_name = #{dictCityName,jdbcType=VARCHAR},
        input_code = #{inputCode,jdbcType=VARCHAR},
        full_code = #{fullCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=BIGINT},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        dict_province_id = #{dictProvinceId,jdbcType=BIGINT},
        wb_code = #{wbCode,jdbcType=VARCHAR},
        his_org_id = #{hisOrgId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        version = #{version,jdbcType=SMALLINT},
        city_code = #{cityCode,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictCity">
        <!--@mbg.generated-->
        insert into csm.dict_city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictCityId != null">
                dict_city_id,
            </if>
            <if test="dictCityName != null">
                dict_city_name,
            </if>
            <if test="inputCode != null">
                input_code,
            </if>
            <if test="fullCode != null">
                full_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="dictProvinceId != null">
                dict_province_id,
            </if>
            <if test="wbCode != null">
                wb_code,
            </if>
            <if test="hisOrgId != null">
                his_org_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="cityCode != null">
                city_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictCityId != null">
                #{dictCityId,jdbcType=BIGINT},
            </if>
            <if test="dictCityName != null">
                #{dictCityName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="dictProvinceId != null">
                #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="wbCode != null">
                #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="cityCode != null">
                #{cityCode,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="dictCityId != null">
                dict_city_id = #{dictCityId,jdbcType=BIGINT},
            </if>
            <if test="dictCityName != null">
                dict_city_name = #{dictCityName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                input_code = #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="dictProvinceId != null">
                dict_province_id = #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="wbCode != null">
                wb_code = #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                his_org_id = #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>
