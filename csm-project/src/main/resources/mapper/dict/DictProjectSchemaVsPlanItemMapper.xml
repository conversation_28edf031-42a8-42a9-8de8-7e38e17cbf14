<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectSchemaVsPlanItemMapper">
    <select id="checkProjectPlanStageLinked" resultType="java.lang.Integer">
        select count(1)
        from csm.dict_project_schema_vs_plan_item as a
                 inner join csm.dict_project_plan_stage as b
                            on a.plan_stage_code = b.project_plan_stage_code and b.is_deleted = 0
        where a.is_deleted = 0
          and b.project_plan_stage_id = #{id}
    </select>

    <select id="checkProjectPlanItemLinked" resultType="java.lang.Integer">
        select (select count(1)
                from csm.dict_project_schema_vs_plan_item as a
                         inner join csm.dict_project_plan_item as b
                                    on a.plan_item_code = b.project_plan_item_code and b.is_deleted = 0
                where a.is_deleted = 0
                  and b.project_plan_item_id = #{id})
                   + (select count(1)
                      from csm.dict_project_schema_vs_plan_item as a
                               inner join csm.dict_project_plan_item as b
                                          on b.project_plan_item_code = any
                                             (string_to_array(a.pre_plan_item_code, ',')) and b.is_deleted = 0
                      where a.is_deleted = 0
                        and b.project_plan_item_id = #{id})
    </select>

    <select id="checkProjectSchemaLinked" resultType="java.lang.Integer">
        select (select count(1)
                from csm.dict_project_schema_vs_plan_item as a
                         inner join csm.dict_project_schema as b on a.schema_code = b.schema_code and b.is_deleted = 0
                where a.is_deleted = 0
                  and b.id = #{id})
                   + (select count(1)
                      from csm.config_project_schema as a
                               inner join csm.dict_project_schema as b
                                          on a.dict_project_schema_code = b.schema_code and b.is_deleted = 0
                      where a.is_deleted = 0
                        and b.id = #{id})
    </select>

    <select id="getList" resultType="com.msun.csm.dao.entity.dict.DictProjectSchemaVsPlanItem">
        select
        a.*,
        b.schema_name,
        c.project_plan_stage_name as plan_stage_name,
        d.project_plan_item_name as plan_item_name,
        e.user_name as create_name,
        f.user_name as update_name,
        (select string_agg(project_plan_item_name, ',') from csm.dict_project_plan_item where project_plan_item_code =
        ANY(string_to_array(a.pre_plan_item_code, ','))) as pre_plan_item_name
        from csm.dict_project_schema_vs_plan_item as a
        inner join csm.dict_project_schema as b on a.schema_code = b.schema_code and b.is_deleted = 0
        inner join csm.dict_project_plan_stage as c on a.plan_stage_code = c.project_plan_stage_code and c.is_deleted =
        0
        inner join csm.dict_project_plan_item as d on a.plan_item_code = d.project_plan_item_code and d.is_deleted = 0
        left join csm.sys_user as e on a.creater_id = e.sys_user_id and e.is_deleted = 0
        left join csm.sys_user as f on a.updater_id = f.sys_user_id and f.is_deleted = 0
        where a.is_deleted = 0
        <if test="projectSchemaCode!=null and projectSchemaCode!=''">
            and a.schema_code = #{projectSchemaCode}
        </if>
        <if test="keyword!=null and keyword!=''">
            and (
            a.schema_code like concat('%', #{keyword}, '%')
            or a.plan_stage_code like concat('%', #{keyword}, '%')
            or a.plan_item_code like concat('%', #{keyword}, '%')
            or b.schema_name like concat('%', #{keyword}, '%')
            or c.project_plan_stage_name like concat('%', #{keyword}, '%')
            or d.project_plan_item_name like concat('%', #{keyword}, '%')
            )
        </if>
        order by c.sort,a.sort
    </select>


</mapper>
