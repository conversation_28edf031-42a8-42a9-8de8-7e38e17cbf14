<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        <!--@Table csm.dict_project_plan_stage-->
        <id column="project_plan_stage_id" jdbcType="BIGINT" property="projectPlanStageId"/>
        <result column="project_plan_stage_code" jdbcType="VARCHAR" property="projectPlanStageCode"/>
        <result column="project_plan_stage_name" jdbcType="VARCHAR" property="projectPlanStageName"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_plan_stage_id, project_plan_stage_code, project_plan_stage_name, sort, creater_id,
        create_time, updater_id, update_time, is_deleted
    </sql>

    <select id="getList" resultMap="BaseResultMap">
        select *
        from csm.dict_project_plan_stage
        where is_deleted = 0
        order by sort
    </select>

    <select id="getAllPlanStage" resultType="com.msun.csm.common.model.BaseCodeNameResp">
        select project_plan_stage_code as id,
               project_plan_stage_name as name
        from csm.dict_project_plan_stage
        where is_deleted = 0
        order by sort
    </select>
</mapper>
