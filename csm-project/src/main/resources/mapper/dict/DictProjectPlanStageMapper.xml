<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        <!--@Table csm.dict_project_plan_stage-->
        <id column="project_plan_stage_id" jdbcType="BIGINT" property="projectPlanStageId"/>
        <result column="project_plan_stage_code" jdbcType="VARCHAR" property="projectPlanStageCode"/>
        <result column="project_plan_stage_name" jdbcType="VARCHAR" property="projectPlanStageName"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_plan_stage_id, project_plan_stage_code, project_plan_stage_name, sort, creater_id,
        create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_project_plan_stage
        where project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_project_plan_stage
        where project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_stage (project_plan_stage_id, project_plan_stage_code,
        project_plan_stage_name, sort, creater_id,
        create_time, updater_id, update_time,
        is_deleted)
        values (#{projectPlanStageId,jdbcType=BIGINT}, #{projectPlanStageCode,jdbcType=VARCHAR},
        #{projectPlanStageName,jdbcType=VARCHAR}, #{sort,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_stage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectPlanStageId != null">
                project_plan_stage_id,
            </if>
            <if test="projectPlanStageCode != null">
                project_plan_stage_code,
            </if>
            <if test="projectPlanStageName != null">
                project_plan_stage_name,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectPlanStageId != null">
                #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanStageCode != null">
                #{projectPlanStageCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanStageName != null">
                #{projectPlanStageName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        update csm.dict_project_plan_stage
        <set>
            <if test="projectPlanStageCode != null">
                project_plan_stage_code = #{projectPlanStageCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanStageName != null">
                project_plan_stage_name = #{projectPlanStageName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        update csm.dict_project_plan_stage
        set project_plan_stage_code = #{projectPlanStageCode,jdbcType=VARCHAR},
        project_plan_stage_name = #{projectPlanStageName,jdbcType=VARCHAR},
        sort = #{sort,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_project_plan_stage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_plan_stage_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.projectPlanStageCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_plan_stage_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.projectPlanStageName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.sort,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where project_plan_stage_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectPlanStageId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_project_plan_stage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_plan_stage_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanStageCode != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.projectPlanStageCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_plan_stage_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanStageName != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.projectPlanStageName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sort != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.sort,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when project_plan_stage_id = #{item.projectPlanStageId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_plan_stage_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectPlanStageId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_stage
        (project_plan_stage_id, project_plan_stage_code, project_plan_stage_name, sort, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectPlanStageId,jdbcType=BIGINT}, #{item.projectPlanStageCode,jdbcType=VARCHAR},
            #{item.projectPlanStageName,jdbcType=VARCHAR}, #{item.sort,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_stage
        (project_plan_stage_id, project_plan_stage_code, project_plan_stage_name, sort, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        (#{projectPlanStageId,jdbcType=BIGINT}, #{projectPlanStageCode,jdbcType=VARCHAR},
        #{projectPlanStageName,jdbcType=VARCHAR}, #{sort,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT},
        project_plan_stage_code = #{projectPlanStageCode,jdbcType=VARCHAR},
        project_plan_stage_name = #{projectPlanStageName,jdbcType=VARCHAR},
        sort = #{sort,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanStage">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_stage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectPlanStageId != null">
                project_plan_stage_id,
            </if>
            <if test="projectPlanStageCode != null">
                project_plan_stage_code,
            </if>
            <if test="projectPlanStageName != null">
                project_plan_stage_name,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectPlanStageId != null">
                #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanStageCode != null">
                #{projectPlanStageCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanStageName != null">
                #{projectPlanStageName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectPlanStageId != null">
                project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanStageCode != null">
                project_plan_stage_code = #{projectPlanStageCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanStageName != null">
                project_plan_stage_name = #{projectPlanStageName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectList" resultMap="BaseResultMap">
        select *
        from csm.dict_project_plan_stage
        where is_deleted = 0
        order by sort
    </select>

    <select id="getAllPlanStage" resultType="com.msun.csm.common.model.BaseCodeNameResp">
        select project_plan_stage_code as id,
               project_plan_stage_name as name
        from csm.dict_project_plan_stage
        where is_deleted = 0
        order by sort
    </select>
</mapper>
