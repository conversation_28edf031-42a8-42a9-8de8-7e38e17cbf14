<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictBusinessStatusMapper">


    <select id="getBusinessStatusByBusinessCode" resultType="com.msun.csm.dao.entity.dict.DictBusinessStatus">
        select *
        from csm.dict_business_status
        where is_deleted = 0
          and business_code = #{businessCode}
        order by sort_no;
    </select>

    <select id="getBusinessStatusByBusinessCodeAndStatusId" resultType="com.msun.csm.dao.entity.dict.DictBusinessStatus">
        select * from csm.dict_business_status
        where is_deleted = 0
          and business_code = #{businessCode}
          and status_id = #{statusId}
    </select>

    <select id="getBusinessStatusByBusinessCodeAndStatusClass" resultType="com.msun.csm.dao.entity.dict.DictBusinessStatus">
        select *
        from csm.dict_business_status
        where is_deleted = 0
          and business_code = #{businessCode}
          and status_class = #{statusClass}
    </select>

</mapper>
