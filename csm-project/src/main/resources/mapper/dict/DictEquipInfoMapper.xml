<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictEquipInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        <!--@Table csm.dict_equip_info-->
        <id column="equip_info_id" jdbcType="BIGINT" property="equipInfoId"/>
        <result column="equip_model_name" jdbcType="VARCHAR" property="equipModelName"/>
        <result column="equip_type_id" jdbcType="BIGINT" property="equipTypeId"/>
        <result column="equip_class_id" jdbcType="BIGINT" property="equipClassId"/>
        <result column="equip_factory_id" jdbcType="BIGINT" property="equipFactoryId"/>
        <result column="no_survey_flag" jdbcType="SMALLINT" property="noSurveyFlag"/>
        <result column="equip_doc_flag" jdbcType="SMALLINT" property="equipDocFlag"/>
        <result column="equip_doc_id" jdbcType="BIGINT" property="equipDocId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        equip_info_id, equip_model_name, equip_type_id, equip_class_id, equip_factory_id,
        no_survey_flag, equip_doc_flag, equip_doc_id, is_deleted, creater_id, create_time, updater_id,
        update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_equip_info
        where equip_info_id = #{equipInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_equip_info
        where equip_info_id = #{equipInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        insert into csm.dict_equip_info (equip_info_id, equip_model_name, equip_type_id,
        equip_class_id, equip_factory_id, no_survey_flag,
        equip_doc_flag, equip_doc_id, is_deleted,
        creater_id, create_time, updater_id,
        update_time)
        values (#{equipInfoId,jdbcType=BIGINT}, #{equipModelName,jdbcType=VARCHAR}, #{equipTypeId,jdbcType=BIGINT},
        #{equipClassId,jdbcType=BIGINT}, #{equipFactoryId,jdbcType=BIGINT}, #{noSurveyFlag,jdbcType=SMALLINT},
        #{equipDocFlag,jdbcType=SMALLINT}, #{equipDocId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        insert into csm.dict_equip_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipInfoId != null">
                equip_info_id,
            </if>
            <if test="equipModelName != null">
                equip_model_name,
            </if>
            <if test="equipTypeId != null">
                equip_type_id,
            </if>
            <if test="equipClassId != null">
                equip_class_id,
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id,
            </if>
            <if test="noSurveyFlag != null">
                no_survey_flag,
            </if>
            <if test="equipDocFlag != null">
                equip_doc_flag,
            </if>
            <if test="equipDocId != null">
                equip_doc_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="equipInfoId != null">
                #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryId != null">
                #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="noSurveyFlag != null">
                #{noSurveyFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocFlag != null">
                #{equipDocFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocId != null">
                #{equipDocId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        update csm.dict_equip_info
        <set>
            <if test="equipModelName != null">
                equip_model_name = #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                equip_type_id = #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                equip_class_id = #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="noSurveyFlag != null">
                no_survey_flag = #{noSurveyFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocFlag != null">
                equip_doc_flag = #{equipDocFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocId != null">
                equip_doc_id = #{equipDocId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where equip_info_id = #{equipInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        update csm.dict_equip_info
        set equip_model_name = #{equipModelName,jdbcType=VARCHAR},
        equip_type_id = #{equipTypeId,jdbcType=BIGINT},
        equip_class_id = #{equipClassId,jdbcType=BIGINT},
        equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
        no_survey_flag = #{noSurveyFlag,jdbcType=SMALLINT},
        equip_doc_flag = #{equipDocFlag,jdbcType=SMALLINT},
        equip_doc_id = #{equipDocId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where equip_info_id = #{equipInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_equip_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="equip_model_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                    #{item.equipModelName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="equip_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.equipTypeId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_class_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.equipClassId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="equip_factory_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.equipFactoryId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="no_survey_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.noSurveyFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="equip_doc_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.equipDocFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="equip_doc_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.equipDocId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where equip_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.equipInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_equip_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="equip_model_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipModelName != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.equipModelName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_type_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipTypeId != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.equipTypeId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_class_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipClassId != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.equipClassId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_factory_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipFactoryId != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.equipFactoryId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="no_survey_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.noSurveyFlag != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.noSurveyFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_doc_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipDocFlag != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.equipDocFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="equip_doc_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.equipDocId != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.equipDocId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when equip_info_id = #{item.equipInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where equip_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.equipInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_equip_info
        (equip_info_id, equip_model_name, equip_type_id, equip_class_id, equip_factory_id,
        no_survey_flag, equip_doc_flag, equip_doc_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.equipInfoId,jdbcType=BIGINT}, #{item.equipModelName,jdbcType=VARCHAR},
            #{item.equipTypeId,jdbcType=BIGINT},
            #{item.equipClassId,jdbcType=BIGINT}, #{item.equipFactoryId,jdbcType=BIGINT},
            #{item.noSurveyFlag,jdbcType=SMALLINT},
            #{item.equipDocFlag,jdbcType=SMALLINT}, #{item.equipDocId,jdbcType=BIGINT},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        insert into csm.dict_equip_info
        (equip_info_id, equip_model_name, equip_type_id, equip_class_id, equip_factory_id,
        no_survey_flag, equip_doc_flag, equip_doc_id, is_deleted, creater_id, create_time,
        updater_id, update_time)
        values
        (#{equipInfoId,jdbcType=BIGINT}, #{equipModelName,jdbcType=VARCHAR}, #{equipTypeId,jdbcType=BIGINT},
        #{equipClassId,jdbcType=BIGINT}, #{equipFactoryId,jdbcType=BIGINT}, #{noSurveyFlag,jdbcType=SMALLINT},
        #{equipDocFlag,jdbcType=SMALLINT}, #{equipDocId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        equip_info_id = #{equipInfoId,jdbcType=BIGINT},
        equip_model_name = #{equipModelName,jdbcType=VARCHAR},
        equip_type_id = #{equipTypeId,jdbcType=BIGINT},
        equip_class_id = #{equipClassId,jdbcType=BIGINT},
        equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
        no_survey_flag = #{noSurveyFlag,jdbcType=SMALLINT},
        equip_doc_flag = #{equipDocFlag,jdbcType=SMALLINT},
        equip_doc_id = #{equipDocId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictEquipInfo">
        <!--@mbg.generated-->
        insert into csm.dict_equip_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipInfoId != null">
                equip_info_id,
            </if>
            <if test="equipModelName != null">
                equip_model_name,
            </if>
            <if test="equipTypeId != null">
                equip_type_id,
            </if>
            <if test="equipClassId != null">
                equip_class_id,
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id,
            </if>
            <if test="noSurveyFlag != null">
                no_survey_flag,
            </if>
            <if test="equipDocFlag != null">
                equip_doc_flag,
            </if>
            <if test="equipDocId != null">
                equip_doc_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="equipInfoId != null">
                #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryId != null">
                #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="noSurveyFlag != null">
                #{noSurveyFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocFlag != null">
                #{equipDocFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocId != null">
                #{equipDocId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="equipInfoId != null">
                equip_info_id = #{equipInfoId,jdbcType=BIGINT},
            </if>
            <if test="equipModelName != null">
                equip_model_name = #{equipModelName,jdbcType=VARCHAR},
            </if>
            <if test="equipTypeId != null">
                equip_type_id = #{equipTypeId,jdbcType=BIGINT},
            </if>
            <if test="equipClassId != null">
                equip_class_id = #{equipClassId,jdbcType=BIGINT},
            </if>
            <if test="equipFactoryId != null">
                equip_factory_id = #{equipFactoryId,jdbcType=BIGINT},
            </if>
            <if test="noSurveyFlag != null">
                no_survey_flag = #{noSurveyFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocFlag != null">
                equip_doc_flag = #{equipDocFlag,jdbcType=SMALLINT},
            </if>
            <if test="equipDocId != null">
                equip_doc_id = #{equipDocId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
