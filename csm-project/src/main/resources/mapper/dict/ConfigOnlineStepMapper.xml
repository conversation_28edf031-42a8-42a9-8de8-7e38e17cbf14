<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigOnlineStepMapper">
    <resultMap id="configOnlineStepVO" type="com.msun.csm.model.vo.ConfigOnlineStepVO">
        <id column="config_online_step_id" property="configOnlineStepId"/>
        <result column="online_step_name" property="onlineStepName"/>
        <result column="online_step_code" property="onlineStepCode"/>
        <result column="upgradation_type" property="upgradationType"/>
        <result column="his_flag" property="hisFlag"/>
        <result column="monomer_flag" property="monomerFlag"/>
        <result column="region_flag" property="regionFlag"/>
        <result column="telesales_flag" property="telesalesFlag"/>
        <result column="previous_online_plan_id_array" property="previousOnlinePlanIdArray"/>
        <result column="operation_type" property="operationType"/>
        <result column="link_address" property="linkAddress"/>
        <result column="required_flag" property="requiredFlag"/>
        <result column="order_no" property="orderNo"/>
        <result column="pid" property="pid"/>
    </resultMap>
    <select id="selectOnlineStepList" resultMap="configOnlineStepVO"
            parameterType="com.msun.csm.model.dto.ConfigOnlineStepSelectDTO">
        select cos2.config_online_step_id,
               cos2.online_step_name,
               cos2.online_step_code,
               cos2.upgradation_type,
               cos2.his_flag,
               cos2.monomer_flag,
               cos2.region_flag,
               cos2.telesales_flag,
               cos2.previous_online_plan_id_array,
               cos2.operation_type,
               cos2.link_address,
               cos2.required_flag,
               cos2.order_no,
               cos2.pid
        from csm.config_online_step cos2
        <where>
            cos2.upgradation_type != 3 and cos2.pid = 0
            <if test="onlineStepName != null">
                and cos2.online_step_name like concat('%',#{onlineStepName},'%')
            </if>
            <if test="upgradationTypeList !=null ">
                and cos2.upgradation_type in
                <foreach collection="upgradationTypeList" item="item" separator="," open="(" close=")" index="index">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="hisFlagList != null">
            and cos2.his_flag in
                <foreach collection="hisFlagList" item="item" separator="," open="(" close=")" index="index">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="regionFlag != null">
                and cos2.region_flag = #{regionFlag}
            </if>
            <if test="monomerFlag != null">
                and cos2.monomer_flag = #{monomerFlag}
            </if>
            <if test="telesalesFlag != null">
                and cos2.telesales_flag = #{telesalesFlag}
            </if>
        </where>
order by cos2.order_no
    </select>
</mapper>
