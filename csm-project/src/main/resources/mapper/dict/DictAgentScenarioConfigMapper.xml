<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictAgentScenarioConfigMapper">

    <select id="selectByParam" resultType="com.msun.csm.model.resp.DictAgentChatScenarioConfigResp">
        select
            dasc.*,
            dac.agent_name,
            dac.agent_address,
            dac.agent_address_produce,
            dac.agent_key
        from csm.dict_agent_scenario_config dasc
            inner join csm.dict_agent_chat dac on dasc.agent_code = dac.agent_code
        where dasc.is_deleted = 0
          and  dac.is_deleted = 0
          and dasc.scenario_code = #{scenarioCode}
    </select>
</mapper>
