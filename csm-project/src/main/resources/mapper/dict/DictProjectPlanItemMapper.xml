<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProjectPlanItem">
        <!--@mbg.generated-->
        <!--@Table csm.dict_project_plan_item-->
        <id column="project_plan_item_id" jdbcType="BIGINT" property="projectPlanItemId"/>
        <result column="project_plan_item_code" jdbcType="VARCHAR" property="projectPlanItemCode"/>
        <result column="project_plan_item_name" jdbcType="VARCHAR" property="projectPlanItemName"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="is_component" jdbcType="VARCHAR" property="isComponent"/>
        <result column="milestone_flag" jdbcType="SMALLINT" property="milestoneFlag"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="web_detail_code" jdbcType="VARCHAR" property="webDetailCode"/>
        <result column="progress_display_mode" jdbcType="VARCHAR" property="progressDisplayMode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_plan_item_id,
        project_plan_item_code,
        project_plan_item_name,
        sort,
        url,
        is_component,
        milestone_flag,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted,
        web_detail_code,
        progress_display_mode
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_project_plan_item
        where project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.dict_project_plan_item
        where project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_item (
        project_plan_item_id,
        project_plan_item_code,
        project_plan_item_name,
        sort,
        url,
        is_component,
        milestone_flag,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted,
        web_detail_code,
        progress_display_mode
        )
        values (
        #{projectPlanItemId,jdbcType=BIGINT},
        #{projectPlanItemCode,jdbcType=VARCHAR},
        #{projectPlanItemName,jdbcType=VARCHAR},
        #{sort,jdbcType=SMALLINT},
        #{url,jdbcType=VARCHAR},
        #{isComponent,jdbcType=VARCHAR},
        #{milestoneFlag,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT},
        #{webDetailCode,jdbcType=VARCHAR},
        #{progressDisplayMode,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectPlanItemId != null">
                project_plan_item_id,
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code,
            </if>
            <if test="projectPlanItemName != null">
                project_plan_item_name,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="isComponent != null">
                is_component,
            </if>
            <if test="milestoneFlag != null">
                milestone_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="webDetailCode != null">
                web_detail_code,
            </if>
            <if test="progressDisplayMode != null">
                progress_display_mode,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectPlanItemId != null">
                #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemCode != null">
                #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanItemName != null">
                #{projectPlanItemName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="isComponent != null">
                #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="webDetailCode != null">
                #{webDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="progressDisplayMode != null">
                #{progressDisplayMode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanItem">
        <!--@mbg.generated-->
        update csm.dict_project_plan_item
        <set>
            <if test="projectPlanItemCode != null">
                project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanItemName != null">
                project_plan_item_name = #{projectPlanItemName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="isComponent != null">
                is_component = #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                milestone_flag = #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_item
        (project_plan_item_id, project_plan_item_code, project_plan_item_name, sort, url,
         is_component, milestone_flag, creater_id, create_time, updater_id, update_time,
         is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectPlanItemId,jdbcType=BIGINT}, #{item.projectPlanItemCode,jdbcType=VARCHAR},
             #{item.projectPlanItemName,jdbcType=VARCHAR}, #{item.sort,jdbcType=SMALLINT}, #{item.url,jdbcType=VARCHAR},
             #{item.isComponent,jdbcType=VARCHAR}, #{item.milestoneFlag,jdbcType=SMALLINT},
             #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updaterId,jdbcType=BIGINT},
             #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_item
        (project_plan_item_id, project_plan_item_code, project_plan_item_name, sort, url,
         is_component, milestone_flag, creater_id, create_time, updater_id, update_time,
         is_deleted)
        values (#{projectPlanItemId,jdbcType=BIGINT}, #{projectPlanItemCode,jdbcType=VARCHAR},
                #{projectPlanItemName,jdbcType=VARCHAR},
                #{sort,jdbcType=SMALLINT}, #{url,jdbcType=VARCHAR}, #{isComponent,jdbcType=VARCHAR},
                #{milestoneFlag,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
            project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT}, project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR}, project_plan_item_name = #{projectPlanItemName,jdbcType=VARCHAR}, sort = #{sort,jdbcType=SMALLINT}, url = #{url,jdbcType=VARCHAR}, is_component = #{isComponent,jdbcType=VARCHAR}, milestone_flag = #{milestoneFlag,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.dict_project_plan_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectPlanItemId != null">
                project_plan_item_id,
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code,
            </if>
            <if test="projectPlanItemName != null">
                project_plan_item_name,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="isComponent != null">
                is_component,
            </if>
            <if test="milestoneFlag != null">
                milestone_flag,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectPlanItemId != null">
                #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemCode != null">
                #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanItemName != null">
                #{projectPlanItemName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="isComponent != null">
                #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectPlanItemId != null">
                project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="projectPlanItemName != null">
                project_plan_item_name = #{projectPlanItemName,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="isComponent != null">
                is_component = #{isComponent,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                milestone_flag = #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByCode" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_project_plan_item
        where project_plan_item_code = #{projectPlanItemCode} and is_deleted = 0
    </select>
</mapper>
