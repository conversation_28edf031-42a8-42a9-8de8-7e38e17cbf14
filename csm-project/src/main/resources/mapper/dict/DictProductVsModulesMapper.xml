<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper">

    <select id="getModuleByYyModuleId" resultType="com.msun.csm.dao.entity.dict.DictProductVsModules">
        select product_vs_module_id as "productVsModuleId",
               yy_product_id        as "yyProductId",
               yy_module_id         as "yyModuleId",
               yy_module_code       as "yyModuleCode",
               yy_module_name       as "yyModuleName",
               is_deleted           as "isDeleted",
               need_survey          as "needSurvey"
        from csm.dict_product_vs_modules
        where is_deleted = 0
          and yy_module_id = #{yyModuleId}
    </select>

    <select id="findNeedSurveyModule" resultType="com.msun.csm.dao.entity.dict.DictProductVsModules">
        select product_vs_module_id as "productVsModuleId",
               yy_product_id        as "yyProductId",
               yy_module_id         as "yyModuleId",
               yy_module_code       as "yyModuleCode",
               yy_module_name       as "yyModuleName",
               is_deleted           as "isDeleted",
               need_survey          as "needSurvey"
        from csm.dict_product_vs_modules
        where is_deleted = 0
          and need_survey = 1
    </select>

</mapper>
