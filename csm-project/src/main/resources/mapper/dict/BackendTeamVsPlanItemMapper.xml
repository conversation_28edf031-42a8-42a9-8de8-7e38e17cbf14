<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.BackendTeamVsPlanItemMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.BackendTeamVsPlanItem">
        <id column="backend_team_vs_plan_item_id" jdbcType="BIGINT" property="backendTeamVsPlanItemId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="team_type_code" jdbcType="VARCHAR" property="teamTypeCode"/>
        <result column="project_plan_item_code" jdbcType="VARCHAR" property="projectPlanItemCode"/>
        <result column="upgradation_type" jdbcType="INTEGER" property="upgradationType"/>
    </resultMap>
    <sql id="Base_Column_List">
        backend_team_vs_plan_item_id,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        team_type_code,
        project_plan_item_code,
        upgradation_type
    </sql>

    <select id="getByTeamTypeCodeAndUpgradationType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.backend_team_vs_plan_item
        where is_deleted = 0
        <if test="teamTypeCode != null and teamTypeCode != ''">
            and team_type_code = #{teamTypeCode}
        </if>
        <if test="upgradationType == 1">
            and upgradation_type in (-1,1)
        </if>
        <if test="upgradationType == 2">
            and upgradation_type in (-1,2)
        </if>
    </select>
</mapper>
