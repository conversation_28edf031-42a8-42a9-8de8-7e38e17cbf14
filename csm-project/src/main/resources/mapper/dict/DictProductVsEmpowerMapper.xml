<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProductVsEmpowerMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProductVsEmpower">
        <!--@mbg.generated-->
        <!--@Table csm.dict_product_vs_empower-->
        <id column="product_vs_empower_id" jdbcType="BIGINT" property="productVsEmpowerId"/>
        <result column="order_product_id" jdbcType="BIGINT" property="orderProductId"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="msun_health_module_code" jdbcType="VARCHAR" property="msunHealthModuleCode"/>
        <result column="msun_health_module" jdbcType="VARCHAR" property="msunHealthModule"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_vs_empower_id, order_product_id, is_deleted, creater_id, create_time, updater_id,
        update_time, msun_health_module_code, msun_health_module
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_product_vs_empower
        where product_vs_empower_id = #{productVsEmpowerId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_product_vs_empower
        where product_vs_empower_id = #{productVsEmpowerId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsEmpower">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_empower
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsEmpowerId != null">
                product_vs_empower_id,
            </if>
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code,
            </if>
            <if test="msunHealthModule != null">
                msun_health_module,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productVsEmpowerId != null">
                #{productVsEmpowerId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModuleCode != null">
                #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModule != null">
                #{msunHealthModule,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsEmpower">
        <!--@mbg.generated-->
        update csm.dict_product_vs_empower
        <set>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModule != null">
                msun_health_module = #{msunHealthModule,jdbcType=VARCHAR},
            </if>
        </set>
        where product_vs_empower_id = #{productVsEmpowerId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProductVsEmpower">
        <!--@mbg.generated-->
        update csm.dict_product_vs_empower
        set order_product_id = #{orderProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
        msun_health_module = #{msunHealthModule,jdbcType=VARCHAR}
        where product_vs_empower_id = #{productVsEmpowerId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product_vs_empower
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.orderProductId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="msun_health_module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.msunHealthModuleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="msun_health_module = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                    #{item.msunHealthModule,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where product_vs_empower_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productVsEmpowerId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_product_vs_empower
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderProductId != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.orderProductId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msun_health_module_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msunHealthModuleCode != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.msunHealthModuleCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msun_health_module = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msunHealthModule != null">
                        when product_vs_empower_id = #{item.productVsEmpowerId,jdbcType=BIGINT} then
                        #{item.msunHealthModule,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where product_vs_empower_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.productVsEmpowerId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_empower
        (product_vs_empower_id, order_product_id, is_deleted, creater_id, create_time, updater_id,
        update_time, msun_health_module_code, msun_health_module)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productVsEmpowerId,jdbcType=BIGINT}, #{item.orderProductId,jdbcType=BIGINT},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.msunHealthModuleCode,jdbcType=VARCHAR},
            #{item.msunHealthModule,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProductVsEmpower">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_empower
        (product_vs_empower_id, order_product_id, is_deleted, creater_id, create_time, updater_id,
        update_time, msun_health_module_code, msun_health_module)
        values
        (#{productVsEmpowerId,jdbcType=BIGINT}, #{orderProductId,jdbcType=BIGINT}, #{isDeleted,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{msunHealthModuleCode,jdbcType=VARCHAR}, #{msunHealthModule,jdbcType=VARCHAR}
        )
        on duplicate key update
        product_vs_empower_id = #{productVsEmpowerId,jdbcType=BIGINT},
        order_product_id = #{orderProductId,jdbcType=BIGINT},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
        msun_health_module = #{msunHealthModule,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProductVsEmpower">
        <!--@mbg.generated-->
        insert into csm.dict_product_vs_empower
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsEmpowerId != null">
                product_vs_empower_id,
            </if>
            <if test="orderProductId != null">
                order_product_id,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code,
            </if>
            <if test="msunHealthModule != null">
                msun_health_module,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productVsEmpowerId != null">
                #{productVsEmpowerId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModuleCode != null">
                #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModule != null">
                #{msunHealthModule,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="productVsEmpowerId != null">
                product_vs_empower_id = #{productVsEmpowerId,jdbcType=BIGINT},
            </if>
            <if test="orderProductId != null">
                order_product_id = #{orderProductId,jdbcType=BIGINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="msunHealthModuleCode != null">
                msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
            </if>
            <if test="msunHealthModule != null">
                msun_health_module = #{msunHealthModule,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectByProductIds" resultType="com.msun.csm.model.dto.ProductIdContrastDTO">
        select order_product_id as original_product_id,
        msun_health_module as msun_health_module,
        msun_health_module_code as msun_health_module_code
        from csm.dict_product_vs_empower
        where is_Deleted = 0
        and order_product_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectListRelative" resultType="com.msun.csm.dao.entity.dict.DictProductVsEmpowerRelative">
        select coalesce(t2.product_name, '') as product_name, t.*
        from csm.dict_product_vs_empower t
        left join csm.dict_product t2 on t2.yy_product_id = t.order_product_id and t.is_deleted = 0 and
        t2.is_deleted = 0
        <where>
            <if test="list != null and list.size() > 0">
                and product_vs_empower_id in
                <foreach close=")" collection="list" item="item" open="(" separator=", ">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findDictProductVsEmpowerList" resultType="com.msun.csm.model.vo.dict.DictProductVsEmpowerVO">
        select dpve.product_vs_empower_id,dpve.order_product_id,
        dp.product_name as order_product_name,
               dpve.msun_health_module_code,dpve.msun_health_module
        from csm.dict_product_vs_empower dpve
        left join csm.dict_product dp on dpve.order_product_id = dp.yy_product_id
        where 1 = 1
        <if test='orderProductName != null and orderProductName != "" '>
            and dpve.order_product_id in
            (select yy_product_id from csm.dict_product where product_name ilike concat('%', #{orderProductName}, '%') and yy_is_cloud = 1 and is_deleted = 0)
        </if>
        <if test='msunHealthModuleCode != null and msunHealthModuleCode != "" '>
            and dpve.msun_health_module_code ilike concat ('%', #{msunHealthModuleCode}, '%')
        </if>
        <if test='msunHealthModule != null and msunHealthModule != "" '>
            and dpve.msun_health_module ilike concat ('%', #{msunHealthModule}, '%')
        </if>
        and dpve.is_deleted = 0
        and dp.yy_is_cloud = 1 and dp.is_deleted = 0
        order by dp.yy_product_id,dp.create_time desc
    </select>
</mapper>
