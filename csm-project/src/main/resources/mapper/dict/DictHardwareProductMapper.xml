<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictHardwareProductMapper">

    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_hardware_product
        (product_id,
        yy_product_id,
        product_code,
        product_name,
        product_full_name,
        product_category,
        is_self_integration,
        is_deleted,
        creater_id,
        create_time, updater_id,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productId},
            #{item.yyProductId},
            #{item.productCode},
            #{item.productName},
            #{item.productFullName},
            #{item.productCategory},
            #{item.isSelfIntegration},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>
