<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictInterfaceFirmMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictInterfaceFirm">
        <!--@mbg.generated-->
        <!--@Table csm.dict_interface_firm-->
        <id column="dict_interface_firm_id" jdbcType="BIGINT" property="dictInterfaceFirmId"/>
        <result column="dict_interface_firm_name" jdbcType="VARCHAR" property="dictInterfaceFirmName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        dict_interface_firm_id,
        dict_interface_firm_name,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_interface_firm
        where dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.dict_interface_firm
        where dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictInterfaceFirm">
        <!--@mbg.generated-->
        insert into csm.dict_interface_firm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id,
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceFirmId != null">
                #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictInterfaceFirm">
        <!--@mbg.generated-->
        update csm.dict_interface_firm
        <set>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictInterfaceFirm">
        <!--@mbg.generated-->
        update csm.dict_interface_firm
        set dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR},
            create_time              = #{createTime,jdbcType=TIMESTAMP}
        where dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_interface_firm
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_interface_firm_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_interface_firm_id = #{item.dictInterfaceFirmId,jdbcType=BIGINT}
                        then #{item.dictInterfaceFirmName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_interface_firm_id = #{item.dictInterfaceFirmId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where dict_interface_firm_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictInterfaceFirmId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_interface_firm
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_interface_firm_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictInterfaceFirmName != null">
                        when dict_interface_firm_id = #{item.dictInterfaceFirmId,jdbcType=BIGINT}
                            then #{item.dictInterfaceFirmName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when dict_interface_firm_id = #{item.dictInterfaceFirmId,jdbcType=BIGINT}
                            then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where dict_interface_firm_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictInterfaceFirmId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_interface_firm
            (dict_interface_firm_id, dict_interface_firm_name, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictInterfaceFirmId,jdbcType=BIGINT}, #{item.dictInterfaceFirmName,jdbcType=VARCHAR},
             #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictInterfaceFirm">
        <!--@mbg.generated-->
        insert into csm.dict_interface_firm
            (dict_interface_firm_id, dict_interface_firm_name, create_time)
        values (#{dictInterfaceFirmId,jdbcType=BIGINT}, #{dictInterfaceFirmName,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP})
        on duplicate key update
            dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT}, dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR}, create_time = #{createTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictInterfaceFirm">
        <!--@mbg.generated-->
        insert into csm.dict_interface_firm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id,
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictInterfaceFirmId != null">
                #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="dictInterfaceFirmId != null">
                dict_interface_firm_id = #{dictInterfaceFirmId,jdbcType=BIGINT},
            </if>
            <if test="dictInterfaceFirmName != null">
                dict_interface_firm_name = #{dictInterfaceFirmName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
