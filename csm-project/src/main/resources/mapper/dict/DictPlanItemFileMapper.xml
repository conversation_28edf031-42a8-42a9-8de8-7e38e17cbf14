<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictPlanItemFileMapper">
    <select id="findCustomFiles" resultType="com.msun.csm.model.vo.projfileupload.FindCustomFileVO"
            parameterType="com.msun.csm.model.dto.projfileupload.FindCustomFilePageDTO">
        select t.* from
        (select pci.custom_info_id,
               pci.custom_name,
               ppi.project_info_id,
               sd.dept_name as project_team_name,
               su.user_name as project_leader_name,
               ppi.project_deliver_status,
               ppi.work_time,
               ppi.settle_in_time,
               ppi.online_time,
               ppi.accept_time,
               (select count(1) from csm.proj_project_plan_file where project_info_id = ppi.project_info_id and is_deleted = 0) as file_upload_count
        from csm.proj_custom_info pci
        left join csm.proj_project_info ppi on pci.custom_info_id = ppi.custom_info_id and ppi.his_flag = 1 and pci.is_deleted = 0
        left join csm.sys_dept sd on ppi.project_team_id = sd.dept_yunying_id
        left join csm.sys_user su on ppi.project_leader_id = su.sys_user_id
        where pci.is_deleted = 0) t
        where 1=1
        <if test="customInfoId != null and customInfoId != 0 ">
            and t.custom_info_id = #{customInfoId}
        </if>
        <if test="projectDeliverStatus != null and projectDeliverStatus != 0">
            and t.project_deliver_status = #{projectDeliverStatus}
        </if>
        <if test="fileUploadFlag != null and fileUploadFlag == 1">
            and t.file_upload_count > 0
        </if>
        <if test="timeType != null">
          <choose>
              <when test="timeType == 1">
                  <if test="startTime != null and endTime != null">
                      and t.work_time between to_timestamp(#{startTime}, 'yyyy-MM-dd HH24:mi:ss') and to_timestamp(#{endTime}, 'yyyy-MM-dd HH24:mi:ss')
                  </if>
              </when>
              <when test="timeType == 2">
                  <if test="startTime != null and endTime != null">
                      and t.settle_in_time between to_timestamp(#{startTime}, 'yyyy-MM-dd HH24:mi:ss') and to_timestamp(#{endTime}, 'yyyy-MM-dd HH24:mi:ss')
                  </if>
              </when>
              <when test="timeType == 3">
                  <if test="startTime != null and endTime != null">
                      and t.online_time between to_timestamp(#{startTime}, 'yyyy-MM-dd HH24:mi:ss') and to_timestamp(#{endTime}, 'yyyy-MM-dd HH24:mi:ss')
                  </if>
              </when>
              <otherwise>
                  <if test="startTime != null and endTime != null">
                      and t.accept_time between to_timestamp(#{startTime}, 'yyyy-MM-dd HH24:mi:ss') and to_timestamp(#{endTime}, 'yyyy-MM-dd HH24:mi:ss')
                  </if>
              </otherwise>
          </choose>
        </if>
        order by t.file_upload_count desc,t.custom_info_id
    </select>

    <select id="findProjectFiles" resultType="com.msun.csm.model.vo.projfileupload.FindProjectFileVO"
            parameterType="com.msun.csm.model.dto.projfileupload.FindProjectFileDTO">
        select dpps.project_plan_stage_code as "planStageCode",
        dpps.project_plan_stage_name as "planStageName",
        dpif.dict_plan_item_file_id as "dictPlanItemFileId",
        dpif.file_desc as "dictPlanItemFileName",
        dpps.project_plan_stage_id
        from csm.dict_plan_item_file dpif
        left join csm.dict_project_plan_stage dpps on dpps.project_plan_stage_code = dpif.plan_stage_code and dpps.is_deleted = 0
        where 1 = 1
        <if test='planStageCode != null and planStageCode != "" '>
            and dpif.plan_stage_code = #{planStageCode}
        </if>
        <if test='fileDesc != null and fileDesc != "" '>
            and dpif.file_desc like concat('%',#{fileDesc},'%')
        </if>
        and dpif.is_deleted = 0
        group by dpps.project_plan_stage_code,dpps.project_plan_stage_name,
        dpif.file_desc,dpif.dict_plan_item_file_id,dpps.project_plan_stage_id
        order by dpps.project_plan_stage_id
    </select>
</mapper>