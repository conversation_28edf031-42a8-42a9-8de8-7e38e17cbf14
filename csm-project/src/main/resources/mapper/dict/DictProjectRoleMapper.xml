<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProjectRoleMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        <!--@Table csm.dict_project_role-->
        <id column="project_role_id" jdbcType="BIGINT" property="projectRoleId"/>
        <result column="project_role_code" jdbcType="VARCHAR" property="projectRoleCode"/>
        <result column="project_role_name" jdbcType="VARCHAR" property="projectRoleName"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="role_type" jdbcType="SMALLINT" property="roleType"/>
        <result column="role_permission" jdbcType="VARCHAR" property="rolePermission"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        project_role_id, project_role_code, project_role_name, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select *
        from csm.dict_project_role
        where project_role_id = #{projectRoleId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_project_role
        where project_role_id = #{projectRoleId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        insert into csm.dict_project_role (project_role_id, project_role_code, project_role_name,
        is_deleted)
        values (#{projectRoleId,jdbcType=BIGINT}, #{projectRoleCode,jdbcType=VARCHAR},
        #{projectRoleName,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        insert into csm.dict_project_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectRoleId != null">
                project_role_id,
            </if>
            <if test="projectRoleCode != null">
                project_role_code,
            </if>
            <if test="projectRoleName != null">
                project_role_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectRoleId != null">
                #{projectRoleId,jdbcType=BIGINT},
            </if>
            <if test="projectRoleCode != null">
                #{projectRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="projectRoleName != null">
                #{projectRodeName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        update csm.dict_project_role
        <set>
            <if test="projectRoleCode != null">
                project_role_code = #{projectRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="projectRoleName != null">
                project_role_name = #{projectRoleName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where project_role_id = #{projectRoleId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        update csm.dict_project_role
        set project_role_code = #{projectRoleCode,jdbcType=VARCHAR},
        project_role_name = #{projectRoleName,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where project_role_id = #{projectRoleId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_project_role
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_role_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_role_id = #{item.projectRoleId,jdbcType=BIGINT} then
                    #{item.projectRoleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="project_role_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_role_id = #{item.projectRoleId,jdbcType=BIGINT} then
                    #{item.projectRoleName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when project_role_id = #{item.projectRoleId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where project_role_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectRoleId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_project_role
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_role_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectRoleCode != null">
                        when project_role_id = #{item.projectRoleId,jdbcType=BIGINT} then
                        #{item.projectRoleCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_role_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectRoleName != null">
                        when project_role_id = #{item.projectRoleId,jdbcType=BIGINT} then
                        #{item.projectRoleName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when project_role_id = #{item.projectRoleId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where project_role_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.projectRoleId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_project_role
        (project_role_id, project_role_code, project_role_name, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectRoleId,jdbcType=BIGINT}, #{item.projectRoleCode,jdbcType=VARCHAR},
            #{item.projectRoleName,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        insert into csm.dict_project_role
        (project_role_id, project_role_code, project_role_name, is_deleted)
        values
        (#{projectRoleId,jdbcType=BIGINT}, #{projectRoleCode,jdbcType=VARCHAR}, #{projectRoleName,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        project_role_id = #{projectRoleId,jdbcType=BIGINT},
        project_role_code = #{projectRoleCode,jdbcType=VARCHAR},
        project_role_name = #{projectRoleName,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole">
        <!--@mbg.generated-->
        insert into csm.dict_project_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectRoleId != null">
                project_role_id,
            </if>
            <if test="projectRoleCode != null">
                project_role_code,
            </if>
            <if test="projectRoleName != null">
                project_role_name,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectRoleId != null">
                #{projectRoleId,jdbcType=BIGINT},
            </if>
            <if test="projectRoleCode != null">
                #{projectRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="projectRoleName != null">
                #{projectRoleName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="projectRoleId != null">
                project_role_id = #{projectRoleId,jdbcType=BIGINT},
            </if>
            <if test="projectRoleCode != null">
                project_role_code = #{projectRoleCode,jdbcType=VARCHAR},
            </if>
            <if test="projectRoleName != null">
                project_role_name = #{projectRoleName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectDictProjectRole" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole" resultMap="BaseResultMap">
        select
        *
        from
        csm.dict_project_role
        where
        is_deleted != 1
        <if test="roleType != null">
            and role_type = #{roleType}
        </if>
        <if test="projectRoleCode != null and projectRoleCode != ''">
            and project_role_code != #{projectRoleCode}
        </if>
    </select>

    <select id="getProjectRoleByCode" parameterType="com.msun.csm.dao.entity.dict.DictProjectRole" resultMap="BaseResultMap">
        select *
        from csm.dict_project_role
        where is_deleted != 1
        and project_role_code = #{projectRoleCode}
    </select>
</mapper>
