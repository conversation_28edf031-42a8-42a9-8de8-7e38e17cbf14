<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictTownMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        <!--@Table csm.dict_town-->
        <id column="dict_town_id" jdbcType="BIGINT" property="dictTownId"/>
        <result column="dict_town_name" jdbcType="VARCHAR" property="dictTownName"/>
        <result column="input_code" jdbcType="VARCHAR" property="inputCode"/>
        <result column="full_code" jdbcType="VARCHAR" property="fullCode"/>
        <result column="order_no" jdbcType="BIGINT" property="orderNo"/>
        <result column="invalid_flag" jdbcType="CHAR" property="invalidFlag"/>
        <result column="city_id" jdbcType="BIGINT" property="cityId"/>
        <result column="postal_no" jdbcType="VARCHAR" property="postalNo"/>
        <result column="wb_code" jdbcType="VARCHAR" property="wbCode"/>
        <result column="his_org_id" jdbcType="BIGINT" property="hisOrgId"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="SMALLINT" property="version"/>
        <result column="town_code" jdbcType="VARCHAR" property="townCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        dict_town_id, dict_town_name, input_code, full_code, order_no, invalid_flag, city_id,
        postal_no, wb_code, his_org_id, creater_id, create_time, updater_id, update_time,
        version, town_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_town
        where dict_town_id = #{dictTownId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_town
        where dict_town_id = #{dictTownId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        insert into csm.dict_town (dict_town_id, dict_town_name, input_code,
        full_code, order_no, invalid_flag,
        city_id, postal_no, wb_code,
        his_org_id, creater_id, create_time,
        updater_id, update_time, version,
        town_code)
        values (#{dictTownId,jdbcType=BIGINT}, #{dictTownName,jdbcType=VARCHAR}, #{inputCode,jdbcType=VARCHAR},
        #{fullCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=BIGINT}, #{invalidFlag,jdbcType=CHAR},
        #{cityId,jdbcType=BIGINT}, #{postalNo,jdbcType=VARCHAR}, #{wbCode,jdbcType=VARCHAR},
        #{hisOrgId,jdbcType=BIGINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{version,jdbcType=SMALLINT},
        #{townCode,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        insert into csm.dict_town
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictTownId != null">
                dict_town_id,
            </if>
            <if test="dictTownName != null">
                dict_town_name,
            </if>
            <if test="inputCode != null">
                input_code,
            </if>
            <if test="fullCode != null">
                full_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="postalNo != null">
                postal_no,
            </if>
            <if test="wbCode != null">
                wb_code,
            </if>
            <if test="hisOrgId != null">
                his_org_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="townCode != null">
                town_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictTownId != null">
                #{dictTownId,jdbcType=BIGINT},
            </if>
            <if test="dictTownName != null">
                #{dictTownName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="postalNo != null">
                #{postalNo,jdbcType=VARCHAR},
            </if>
            <if test="wbCode != null">
                #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="townCode != null">
                #{townCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        update csm.dict_town
        <set>
            <if test="dictTownName != null">
                dict_town_name = #{dictTownName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                input_code = #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="postalNo != null">
                postal_no = #{postalNo,jdbcType=VARCHAR},
            </if>
            <if test="wbCode != null">
                wb_code = #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                his_org_id = #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="townCode != null">
                town_code = #{townCode,jdbcType=VARCHAR},
            </if>
        </set>
        where dict_town_id = #{dictTownId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        update csm.dict_town
        set dict_town_name = #{dictTownName,jdbcType=VARCHAR},
        input_code = #{inputCode,jdbcType=VARCHAR},
        full_code = #{fullCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=BIGINT},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        city_id = #{cityId,jdbcType=BIGINT},
        postal_no = #{postalNo,jdbcType=VARCHAR},
        wb_code = #{wbCode,jdbcType=VARCHAR},
        his_org_id = #{hisOrgId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        version = #{version,jdbcType=SMALLINT},
        town_code = #{townCode,jdbcType=VARCHAR}
        where dict_town_id = #{dictTownId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_town
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_town_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.dictTownName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.inputCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="full_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.fullCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="city_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.cityId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then now()
                </foreach>
            </trim>
            <trim prefix="town_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.townCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where dict_town_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictTownId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_town
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_town_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictTownName != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then
                        #{item.dictTownName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="input_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inputCode != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.inputCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="full_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fullCode != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.fullCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidFlag != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.invalidFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="city_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cityId != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.cityId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="postal_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.postalNo != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.postalNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="wb_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.wbCode != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.wbCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisOrgId != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.hisOrgId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.version != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.version,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="town_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.townCode != null">
                        when dict_town_id = #{item.dictTownId,jdbcType=BIGINT} then #{item.townCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where dict_town_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictTownId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_town
        (dict_town_id, dict_town_name, input_code, full_code, order_no, invalid_flag, city_id,
        postal_no, wb_code, creater_id, create_time, updater_id, update_time,
         town_code)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictTownId,jdbcType=BIGINT}, #{item.dictTownName,jdbcType=VARCHAR},
            #{item.inputCode,jdbcType=VARCHAR},
            #{item.fullCode,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=BIGINT}, #{item.invalidFlag,jdbcType=CHAR},
            #{item.cityId,jdbcType=BIGINT}, #{item.postalNo,jdbcType=VARCHAR}, #{item.wbCode,jdbcType=VARCHAR},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.townCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        insert into csm.dict_town
        (dict_town_id, dict_town_name, input_code, full_code, order_no, invalid_flag, city_id,
        postal_no, wb_code, his_org_id, creater_id, create_time, updater_id, update_time,
        version, town_code)
        values
        (#{dictTownId,jdbcType=BIGINT}, #{dictTownName,jdbcType=VARCHAR}, #{inputCode,jdbcType=VARCHAR},
        #{fullCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=BIGINT}, #{invalidFlag,jdbcType=CHAR},
        #{cityId,jdbcType=BIGINT}, #{postalNo,jdbcType=VARCHAR}, #{wbCode,jdbcType=VARCHAR},
        #{hisOrgId,jdbcType=BIGINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{version,jdbcType=SMALLINT},
        #{townCode,jdbcType=VARCHAR})
        on duplicate key update
        dict_town_id = #{dictTownId,jdbcType=BIGINT},
        dict_town_name = #{dictTownName,jdbcType=VARCHAR},
        input_code = #{inputCode,jdbcType=VARCHAR},
        full_code = #{fullCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=BIGINT},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        city_id = #{cityId,jdbcType=BIGINT},
        postal_no = #{postalNo,jdbcType=VARCHAR},
        wb_code = #{wbCode,jdbcType=VARCHAR},
        his_org_id = #{hisOrgId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        version = #{version,jdbcType=SMALLINT},
        town_code = #{townCode,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictTown">
        <!--@mbg.generated-->
        insert into csm.dict_town
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictTownId != null">
                dict_town_id,
            </if>
            <if test="dictTownName != null">
                dict_town_name,
            </if>
            <if test="inputCode != null">
                input_code,
            </if>
            <if test="fullCode != null">
                full_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="postalNo != null">
                postal_no,
            </if>
            <if test="wbCode != null">
                wb_code,
            </if>
            <if test="hisOrgId != null">
                his_org_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="townCode != null">
                town_code,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictTownId != null">
                #{dictTownId,jdbcType=BIGINT},
            </if>
            <if test="dictTownName != null">
                #{dictTownName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="postalNo != null">
                #{postalNo,jdbcType=VARCHAR},
            </if>
            <if test="wbCode != null">
                #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="townCode != null">
                #{townCode,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="dictTownId != null">
                dict_town_id = #{dictTownId,jdbcType=BIGINT},
            </if>
            <if test="dictTownName != null">
                dict_town_name = #{dictTownName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                input_code = #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="postalNo != null">
                postal_no = #{postalNo,jdbcType=VARCHAR},
            </if>
            <if test="wbCode != null">
                wb_code = #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                his_org_id = #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="townCode != null">
                town_code = #{townCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
</mapper>
