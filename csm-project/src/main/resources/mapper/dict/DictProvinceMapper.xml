<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictProvinceMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        <!--@Table csm.dict_province-->
        <id column="dict_province_id" jdbcType="BIGINT" property="dictProvinceId"/>
        <result column="dict_province_name" jdbcType="VARCHAR" property="dictProvinceName"/>
        <result column="input_code" jdbcType="VARCHAR" property="inputCode"/>
        <result column="full_code" jdbcType="VARCHAR" property="fullCode"/>
        <result column="order_no" jdbcType="BIGINT" property="orderNo"/>
        <result column="invalid_flag" jdbcType="CHAR" property="invalidFlag"/>
        <result column="wb_code" jdbcType="VARCHAR" property="wbCode"/>
        <result column="his_org_id" jdbcType="BIGINT" property="hisOrgId"/>
        <result column="version" jdbcType="SMALLINT" property="version"/>
        <result column="creater_id" jdbcType="INTEGER" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        dict_province_id, dict_province_name, input_code, full_code, order_no, invalid_flag,
        wb_code, his_org_id, version, creater_id, create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_province
        where dict_province_id = #{dictProvinceId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_province
        where dict_province_id = #{dictProvinceId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        insert into csm.dict_province (dict_province_id, dict_province_name,
        input_code, full_code, order_no,
        invalid_flag, wb_code, his_org_id,
        version, creater_id, create_time,
        updater_id, update_time)
        values (#{dictProvinceId,jdbcType=BIGINT}, #{dictProvinceName,jdbcType=VARCHAR},
        #{inputCode,jdbcType=VARCHAR}, #{fullCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=BIGINT},
        #{invalidFlag,jdbcType=CHAR}, #{wbCode,jdbcType=VARCHAR}, #{hisOrgId,jdbcType=BIGINT},
        #{version,jdbcType=SMALLINT}, #{createrId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        insert into csm.dict_province
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictProvinceId != null">
                dict_province_id,
            </if>
            <if test="dictProvinceName != null">
                dict_province_name,
            </if>
            <if test="inputCode != null">
                input_code,
            </if>
            <if test="fullCode != null">
                full_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="wbCode != null">
                wb_code,
            </if>
            <if test="hisOrgId != null">
                his_org_id,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictProvinceId != null">
                #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="dictProvinceName != null">
                #{dictProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="wbCode != null">
                #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        update csm.dict_province
        <set>
            <if test="dictProvinceName != null">
                dict_province_name = #{dictProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                input_code = #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="wbCode != null">
                wb_code = #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                his_org_id = #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where dict_province_id = #{dictProvinceId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        update csm.dict_province
        set dict_province_name = #{dictProvinceName,jdbcType=VARCHAR},
        input_code = #{inputCode,jdbcType=VARCHAR},
        full_code = #{fullCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=BIGINT},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        wb_code = #{wbCode,jdbcType=VARCHAR},
        his_org_id = #{hisOrgId,jdbcType=BIGINT},
        version = #{version,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where dict_province_id = #{dictProvinceId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_province
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_province_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    #{item.dictProvinceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="input_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    #{item.inputCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="full_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    #{item.fullCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    #{item.invalidFlag,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="wb_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then #{item.wbCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                    now()
                </foreach>
            </trim>
        </trim>
        where dict_province_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictProvinceId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_province
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dict_province_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dictProvinceName != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.dictProvinceName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="input_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.inputCode != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.inputCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="full_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fullCode != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.fullCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.orderNo,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="invalid_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.invalidFlag != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.invalidFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="wb_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.wbCode != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.wbCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisOrgId != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.hisOrgId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="version = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.version != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.version,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when dict_province_id = #{item.dictProvinceId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where dict_province_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.dictProvinceId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_province
        (dict_province_id, dict_province_name, input_code, full_code, order_no, invalid_flag,
        wb_code,  creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.dictProvinceId,jdbcType=BIGINT}, #{item.dictProvinceName,jdbcType=VARCHAR},
            #{item.inputCode,jdbcType=VARCHAR}, #{item.fullCode,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=BIGINT},
            #{item.invalidFlag,jdbcType=CHAR}, #{item.wbCode,jdbcType=VARCHAR},
             #{item.createrId,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=INTEGER}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        insert into csm.dict_province
        (dict_province_id, dict_province_name, input_code, full_code, order_no, invalid_flag,
        wb_code, his_org_id, version, creater_id, create_time, updater_id, update_time)
        values
        (#{dictProvinceId,jdbcType=BIGINT}, #{dictProvinceName,jdbcType=VARCHAR}, #{inputCode,jdbcType=VARCHAR},
        #{fullCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=BIGINT}, #{invalidFlag,jdbcType=CHAR},
        #{wbCode,jdbcType=VARCHAR}, #{hisOrgId,jdbcType=BIGINT}, #{version,jdbcType=SMALLINT},
        #{createrId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        dict_province_id = #{dictProvinceId,jdbcType=BIGINT},
        dict_province_name = #{dictProvinceName,jdbcType=VARCHAR},
        input_code = #{inputCode,jdbcType=VARCHAR},
        full_code = #{fullCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=BIGINT},
        invalid_flag = #{invalidFlag,jdbcType=CHAR},
        wb_code = #{wbCode,jdbcType=VARCHAR},
        his_org_id = #{hisOrgId,jdbcType=BIGINT},
        version = #{version,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictProvince">
        <!--@mbg.generated-->
        insert into csm.dict_province
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictProvinceId != null">
                dict_province_id,
            </if>
            <if test="dictProvinceName != null">
                dict_province_name,
            </if>
            <if test="inputCode != null">
                input_code,
            </if>
            <if test="fullCode != null">
                full_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="invalidFlag != null">
                invalid_flag,
            </if>
            <if test="wbCode != null">
                wb_code,
            </if>
            <if test="hisOrgId != null">
                his_org_id,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictProvinceId != null">
                #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="dictProvinceName != null">
                #{dictProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="wbCode != null">
                #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="dictProvinceId != null">
                dict_province_id = #{dictProvinceId,jdbcType=BIGINT},
            </if>
            <if test="dictProvinceName != null">
                dict_province_name = #{dictProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="inputCode != null">
                input_code = #{inputCode,jdbcType=VARCHAR},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="invalidFlag != null">
                invalid_flag = #{invalidFlag,jdbcType=CHAR},
            </if>
            <if test="wbCode != null">
                wb_code = #{wbCode,jdbcType=VARCHAR},
            </if>
            <if test="hisOrgId != null">
                his_org_id = #{hisOrgId,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
