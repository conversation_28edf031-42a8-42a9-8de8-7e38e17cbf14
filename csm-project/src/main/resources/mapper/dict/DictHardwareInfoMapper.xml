<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.dict.DictHardwareInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.dict.DictHardwareInfo">
        <!--@mbg.generated-->
        <!--@Table csm.dict_hardware_info-->
        <id column="hardware_info_id" jdbcType="BIGINT" property="hardwareInfoId"/>
        <result column="hardware_name" jdbcType="VARCHAR" property="hardwareName"/>
        <result column="config_require" jdbcType="VARCHAR" property="configRequire"/>
        <result column="common_flag" jdbcType="SMALLINT" property="commonFlag"/>
        <result column="joint_flag" jdbcType="SMALLINT" property="jointFlag"/>
        <result column="use_dept" jdbcType="VARCHAR" property="useDept"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <select id="selectHardwareInfo" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select hardware_info_id as id,hardware_name as name
        from csm.dict_hardware_info
        where 1 = 1
        <if test='hardwareName != null and hardwareName != "" '>
            and hardware_name ilike concat('%',hardwareName,'%')
        </if>
        and is_deleted = 0
        order by hardware_info_id
    </select>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        hardware_info_id, hardware_name, config_require, common_flag, joint_flag, use_dept,
        is_deleted, creater_id, create_time, updater_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.dict_hardware_info
        where hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.dict_hardware_info
        where hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.dict.DictHardwareInfo">
        <!--@mbg.generated-->
        insert into csm.dict_hardware_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareInfoId != null">
                hardware_info_id,
            </if>
            <if test="hardwareName != null">
                hardware_name,
            </if>
            <if test="configRequire != null">
                config_require,
            </if>
            <if test="commonFlag != null">
                common_flag,
            </if>
            <if test="jointFlag != null">
                joint_flag,
            </if>
            <if test="useDept != null">
                use_dept,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hardwareInfoId != null">
                #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="hardwareName != null">
                #{hardwareName,jdbcType=VARCHAR},
            </if>
            <if test="configRequire != null">
                #{configRequire,jdbcType=VARCHAR},
            </if>
            <if test="commonFlag != null">
                #{commonFlag,jdbcType=SMALLINT},
            </if>
            <if test="jointFlag != null">
                #{jointFlag,jdbcType=SMALLINT},
            </if>
            <if test="useDept != null">
                #{useDept,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.dict.DictHardwareInfo">
        <!--@mbg.generated-->
        update csm.dict_hardware_info
        <set>
            <if test="hardwareName != null">
                hardware_name = #{hardwareName,jdbcType=VARCHAR},
            </if>
            <if test="configRequire != null">
                config_require = #{configRequire,jdbcType=VARCHAR},
            </if>
            <if test="commonFlag != null">
                common_flag = #{commonFlag,jdbcType=SMALLINT},
            </if>
            <if test="jointFlag != null">
                joint_flag = #{jointFlag,jdbcType=SMALLINT},
            </if>
            <if test="useDept != null">
                use_dept = #{useDept,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.dict.DictHardwareInfo">
        <!--@mbg.generated-->
        update csm.dict_hardware_info
        set hardware_name = #{hardwareName,jdbcType=VARCHAR},
        config_require = #{configRequire,jdbcType=VARCHAR},
        common_flag = #{commonFlag,jdbcType=SMALLINT},
        joint_flag = #{jointFlag,jdbcType=SMALLINT},
        use_dept = #{useDept,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_hardware_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hardware_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.hardwareName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="config_require = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.configRequire,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="common_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.commonFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="joint_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.jointFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="use_dept = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then #{item.useDept,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where hardware_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hardwareInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.dict_hardware_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hardware_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hardwareName != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.hardwareName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="config_require = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.configRequire != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.configRequire,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="common_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.commonFlag != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.commonFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="joint_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.jointFlag != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.jointFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="use_dept = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.useDept != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.useDept,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when hardware_info_id = #{item.hardwareInfoId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where hardware_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.hardwareInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.dict_hardware_info
        (hardware_info_id, hardware_name, config_require, common_flag, joint_flag, use_dept,
        is_deleted, creater_id, create_time, updater_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.hardwareInfoId,jdbcType=BIGINT}, #{item.hardwareName,jdbcType=VARCHAR},
            #{item.configRequire,jdbcType=VARCHAR},
            #{item.commonFlag,jdbcType=SMALLINT}, #{item.jointFlag,jdbcType=SMALLINT}, #{item.useDept,jdbcType=VARCHAR},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.dict.DictHardwareInfo">
        <!--@mbg.generated-->
        insert into csm.dict_hardware_info
        (hardware_info_id, hardware_name, config_require, common_flag, joint_flag, use_dept,
        is_deleted, creater_id, create_time, updater_id, update_time)
        values
        (#{hardwareInfoId,jdbcType=BIGINT}, #{hardwareName,jdbcType=VARCHAR}, #{configRequire,jdbcType=VARCHAR},
        #{commonFlag,jdbcType=SMALLINT}, #{jointFlag,jdbcType=SMALLINT}, #{useDept,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})
        on duplicate key update
        hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
        hardware_name = #{hardwareName,jdbcType=VARCHAR},
        config_require = #{configRequire,jdbcType=VARCHAR},
        common_flag = #{commonFlag,jdbcType=SMALLINT},
        joint_flag = #{jointFlag,jdbcType=SMALLINT},
        use_dept = #{useDept,jdbcType=VARCHAR},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.dict.DictHardwareInfo">
        <!--@mbg.generated-->
        insert into csm.dict_hardware_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareInfoId != null">
                hardware_info_id,
            </if>
            <if test="hardwareName != null">
                hardware_name,
            </if>
            <if test="configRequire != null">
                config_require,
            </if>
            <if test="commonFlag != null">
                common_flag,
            </if>
            <if test="jointFlag != null">
                joint_flag,
            </if>
            <if test="useDept != null">
                use_dept,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hardwareInfoId != null">
                #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="hardwareName != null">
                #{hardwareName,jdbcType=VARCHAR},
            </if>
            <if test="configRequire != null">
                #{configRequire,jdbcType=VARCHAR},
            </if>
            <if test="commonFlag != null">
                #{commonFlag,jdbcType=SMALLINT},
            </if>
            <if test="jointFlag != null">
                #{jointFlag,jdbcType=SMALLINT},
            </if>
            <if test="useDept != null">
                #{useDept,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="hardwareInfoId != null">
                hardware_info_id = #{hardwareInfoId,jdbcType=BIGINT},
            </if>
            <if test="hardwareName != null">
                hardware_name = #{hardwareName,jdbcType=VARCHAR},
            </if>
            <if test="configRequire != null">
                config_require = #{configRequire,jdbcType=VARCHAR},
            </if>
            <if test="commonFlag != null">
                common_flag = #{commonFlag,jdbcType=SMALLINT},
            </if>
            <if test="jointFlag != null">
                joint_flag = #{jointFlag,jdbcType=SMALLINT},
            </if>
            <if test="useDept != null">
                use_dept = #{useDept,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>
