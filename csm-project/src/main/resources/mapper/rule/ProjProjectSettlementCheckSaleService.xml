<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.rule.RuleProductRuleConfigMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    <!--@Table csm.rule_product_rule_config-->
    <id column="product_rule_config_id" jdbcType="BIGINT" property="productRuleConfigId" />
    <result column="yy_product_id" jdbcType="BIGINT" property="yyProductId" />
    <result column="his_flag" jdbcType="SMALLINT" property="hisFlag" />
    <result column="msun_health_point" jdbcType="SMALLINT" property="msunHealthPoint" />
    <result column="msun_update_flag" jdbcType="SMALLINT" property="msunUpdateFlag" />
    <result column="hzzn_flag" jdbcType="SMALLINT" property="hzznFlag" />
    <result column="survey_flag" jdbcType="SMALLINT" property="surveyFlag" />
    <result column="branch_hospital_product_flag" jdbcType="SMALLINT" property="branchHospitalProductFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    product_rule_config_id, yy_product_id, his_flag, msun_health_point, msun_update_flag,
    hzzn_flag, survey_flag, branch_hospital_product_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.rule_product_rule_config
    where product_rule_config_id = #{productRuleConfigId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.rule_product_rule_config
    where product_rule_config_id = #{productRuleConfigId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    insert into csm.rule_product_rule_config (product_rule_config_id, yy_product_id, his_flag,
      msun_health_point, msun_update_flag, hzzn_flag
      )
    values (#{productRuleConfigId,jdbcType=BIGINT}, #{yyProductId,jdbcType=BIGINT}, #{hisFlag,jdbcType=SMALLINT},
      #{msunHealthPoint,jdbcType=SMALLINT}, #{msunUpdateFlag,jdbcType=SMALLINT}, #{hzznFlag,jdbcType=SMALLINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    insert into csm.rule_product_rule_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productRuleConfigId != null">
        product_rule_config_id,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="hisFlag != null">
        his_flag,
      </if>
      <if test="msunHealthPoint != null">
        msun_health_point,
      </if>
      <if test="msunUpdateFlag != null">
        msun_update_flag,
      </if>
      <if test="hzznFlag != null">
        hzzn_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productRuleConfigId != null">
        #{productRuleConfigId,jdbcType=BIGINT},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=BIGINT},
      </if>
      <if test="hisFlag != null">
        #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="msunHealthPoint != null">
        #{msunHealthPoint,jdbcType=SMALLINT},
      </if>
      <if test="msunUpdateFlag != null">
        #{msunUpdateFlag,jdbcType=SMALLINT},
      </if>
      <if test="hzznFlag != null">
        #{hzznFlag,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    update csm.rule_product_rule_config
    <set>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
      </if>
      <if test="hisFlag != null">
        his_flag = #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="msunHealthPoint != null">
        msun_health_point = #{msunHealthPoint,jdbcType=SMALLINT},
      </if>
      <if test="msunUpdateFlag != null">
        msun_update_flag = #{msunUpdateFlag,jdbcType=SMALLINT},
      </if>
      <if test="hzznFlag != null">
        hzzn_flag = #{hzznFlag,jdbcType=SMALLINT},
      </if>
    </set>
    where product_rule_config_id = #{productRuleConfigId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    update csm.rule_product_rule_config
    set yy_product_id = #{yyProductId,jdbcType=BIGINT},
      his_flag = #{hisFlag,jdbcType=SMALLINT},
      msun_health_point = #{msunHealthPoint,jdbcType=SMALLINT},
      msun_update_flag = #{msunUpdateFlag,jdbcType=SMALLINT},
      hzzn_flag = #{hzznFlag,jdbcType=SMALLINT}
    where product_rule_config_id = #{productRuleConfigId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.rule_product_rule_config
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="his_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.hisFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="msun_health_point = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.msunHealthPoint,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="msun_update_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.msunUpdateFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="hzzn_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.hzznFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where product_rule_config_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.productRuleConfigId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.rule_product_rule_config
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.yyProductId != null">
            when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="his_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hisFlag != null">
            when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.hisFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="msun_health_point = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.msunHealthPoint != null">
            when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.msunHealthPoint,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="msun_update_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.msunUpdateFlag != null">
            when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.msunUpdateFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="hzzn_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hzznFlag != null">
            when product_rule_config_id = #{item.productRuleConfigId,jdbcType=BIGINT} then #{item.hzznFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where product_rule_config_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.productRuleConfigId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.rule_product_rule_config
    (product_rule_config_id, yy_product_id, his_flag, msun_health_point, msun_update_flag,
      hzzn_flag)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.productRuleConfigId,jdbcType=BIGINT}, #{item.yyProductId,jdbcType=BIGINT},
        #{item.hisFlag,jdbcType=SMALLINT}, #{item.msunHealthPoint,jdbcType=SMALLINT}, #{item.msunUpdateFlag,jdbcType=SMALLINT},
        #{item.hzznFlag,jdbcType=SMALLINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    insert into csm.rule_product_rule_config
    (product_rule_config_id, yy_product_id, his_flag, msun_health_point, msun_update_flag,
      hzzn_flag)
    values
    (#{productRuleConfigId,jdbcType=BIGINT}, #{yyProductId,jdbcType=BIGINT}, #{hisFlag,jdbcType=SMALLINT},
      #{msunHealthPoint,jdbcType=SMALLINT}, #{msunUpdateFlag,jdbcType=SMALLINT}, #{hzznFlag,jdbcType=SMALLINT}
      )
    on duplicate key update
    product_rule_config_id = #{productRuleConfigId,jdbcType=BIGINT},
    yy_product_id = #{yyProductId,jdbcType=BIGINT},
    his_flag = #{hisFlag,jdbcType=SMALLINT},
    msun_health_point = #{msunHealthPoint,jdbcType=SMALLINT},
    msun_update_flag = #{msunUpdateFlag,jdbcType=SMALLINT},
    hzzn_flag = #{hzznFlag,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.rule.RuleProductRuleConfig">
    <!--@mbg.generated-->
    insert into csm.rule_product_rule_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productRuleConfigId != null">
        product_rule_config_id,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="hisFlag != null">
        his_flag,
      </if>
      <if test="msunHealthPoint != null">
        msun_health_point,
      </if>
      <if test="msunUpdateFlag != null">
        msun_update_flag,
      </if>
      <if test="hzznFlag != null">
        hzzn_flag,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productRuleConfigId != null">
        #{productRuleConfigId,jdbcType=BIGINT},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=BIGINT},
      </if>
      <if test="hisFlag != null">
        #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="msunHealthPoint != null">
        #{msunHealthPoint,jdbcType=SMALLINT},
      </if>
      <if test="msunUpdateFlag != null">
        #{msunUpdateFlag,jdbcType=SMALLINT},
      </if>
      <if test="hzznFlag != null">
        #{hzznFlag,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="productRuleConfigId != null">
        product_rule_config_id = #{productRuleConfigId,jdbcType=BIGINT},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=BIGINT},
      </if>
      <if test="hisFlag != null">
        his_flag = #{hisFlag,jdbcType=SMALLINT},
      </if>
      <if test="msunHealthPoint != null">
        msun_health_point = #{msunHealthPoint,jdbcType=SMALLINT},
      </if>
      <if test="msunUpdateFlag != null">
        msun_update_flag = #{msunUpdateFlag,jdbcType=SMALLINT},
      </if>
      <if test="hzznFlag != null">
        hzzn_flag = #{hzznFlag,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

  <select id="findHisFlag" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from csm.rule_product_rule_config
      where his_flag = 1
  </select>

  <select id="findCloudFlag" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from csm.rule_product_rule_config
      where msun_update_flag = 1
  </select>

  <select id="findBranchHospitalFlag" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from csm.rule_product_rule_config
      where branch_hospital_product_flag = 1
  </select>

  <select id="findSpecialProduct" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from csm.rule_product_rule_config
    where hzzn_flag = #{code}
  </select>
</mapper>
