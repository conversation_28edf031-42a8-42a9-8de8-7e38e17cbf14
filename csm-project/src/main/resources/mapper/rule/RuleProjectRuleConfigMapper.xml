<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper">

    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.rule.RuleProjectRuleConfig">
            <id property="ruleProjectRuleConfigId" column="rule_project_rule_config_id" jdbcType="BIGINT"/>
            <result property="sceneCode" column="scene_code" jdbcType="VARCHAR"/>
            <result property="sceneDesc" column="scene_desc" jdbcType="VARCHAR"/>
            <result property="projectRuleCode" column="project_rule_code" jdbcType="VARCHAR"/>
            <result property="projectRuleContent" column="project_rule_content" jdbcType="VARCHAR"/>
            <result property="verityWay" column="verity_way" jdbcType="VARCHAR"/>
            <result property="hisFlag" column="his_flag" jdbcType="SMALLINT"/>
            <result property="upgradationType" column="upgradation_type" jdbcType="SMALLINT"/>
            <result property="contractType" column="contract_type" jdbcType="SMALLINT"/>
            <result property="requiredFlag" column="required_flag" jdbcType="SMALLINT"/>
            <result property="cloudServiceFlag" column="cloud_service_flag" jdbcType="SMALLINT"/>
            <result property="monomerFlag" column="monomer_flag" jdbcType="SMALLINT"/>
            <result property="regionFlag" column="region_flag" jdbcType="SMALLINT"/>
            <result property="telesalesFlag" column="telesales_flag" jdbcType="SMALLINT"/>
            <result property="templateFlag" column="template_flag" jdbcType="SMALLINT"/>
            <result property="templateFileCode" column="template_file_code" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
            <result property="isDeleted" column="is_deleted" jdbcType="SMALLINT"/>
            <result property="createrId" column="creater_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updaterId" column="updater_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        rule_project_rule_config_id,scene_code,scene_desc,
        project_rule_code,project_rule_content,verity_way,
        his_flag,upgradation_type,contract_type,
        required_flag,cloud_service_flag,monomer_flag,
        region_flag,telesales_flag,template_flag,
        template_file_code,order_no,is_deleted,
        creater_id,create_time,updater_id,
        update_time
    </sql>


    <select id="getTemplateByCode" resultType="com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO">
        select
            ppac.rule_project_rule_config_id projectAcceptanceRuleId,
            sf.file_path template_url,
            ppac.project_rule_code,
            ppac.project_rule_content,
            ppac.verity_way,required_flag,
            ppac.template_flag,
            ppac.template_file_code,
            ppac.order_no,
            ppac.is_deleted,
            ppac.creater_id,
            ppac.create_time,
            ppac.updater_id,
            ppac.update_time
        from csm.rule_project_rule_config ppac
                 left join csm.sys_file sf on ppac.template_file_code =sf.file_code
        where ppac.scene_code ='ACCEPTANCE'
          and ppac.is_deleted = 0
          and (ppac.his_flag = -1 or ppac.his_flag = #{project.hisFlag})
          and (ppac.upgradation_type = -1 or ppac.upgradation_type = #{project.upgradationType})
        order by ppac.required_flag desc, ppac.order_no
    </select>

    <select id="getTemplateByProductCode" resultType="com.msun.csm.model.vo.ProjProjectFileRuleVO">
        select
            ppac.scene_code as fileItemCode,
            ppac.project_rule_code as fileTypeCode,
            ppac.project_rule_content as fileTypeName,
            ppac.required_flag,
            ppac.order_no,
            ppac.limit_type
        from csm.rule_project_rule_config ppac
        left join csm.sys_file sf on ppac.template_file_code = sf.file_code
        where ppac.scene_code = #{sceneCode}
        and ppac.is_deleted = 0
        <if test="isMobile == 1">
            and ppac.project_rule_content like concat('%','图片','%')
        </if>
        order by ppac.order_no
    </select>
</mapper>
