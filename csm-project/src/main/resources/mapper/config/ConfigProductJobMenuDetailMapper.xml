<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper">
    <select id="selectProductJobMenuDetail" resultType="com.msun.csm.model.vo.ConfigProductJobMenuDetailVO"
            parameterType="long">
        select cpjmd.*,
        cpjm.menu_name
        from csm.config_product_job_menu cpjm
        left join csm.config_product_job_menu_detail cpjmd
        on cpjm.product_job_menu_id = cpjmd.product_job_menu_id
        where cpjmd.yy_product_id = #{yyProductId}
        and (cpjmd.upgradation_type = -1
        or cpjmd.upgradation_type in
        (select upgradation_type from csm.proj_project_info where project_info_id = #{projectInfoId})
        )
        <if test="surveyUseFlag != null">
            and cpjmd.survey_use_flag = #{surveyUseFlag}
        </if>
        <if test="prepareUseFlag != null">
            and cpjmd.prepare_use_flag = #{prepareUseFlag}
        </if>
        order by cpjm.menu_sort
    </select>
    <select id="selectProductMenuDetail" resultType="com.msun.csm.common.model.BaseIdNameResp"
            parameterType="java.lang.Long">
        select distinct cpjmd.product_job_menu_id id,
                        cpjm.menu_name as "name"
        from csm.config_product_job_menu cpjm
                 left join csm.config_product_job_menu_detail cpjmd on cpjm.product_job_menu_id = cpjmd.product_job_menu_id
        where  1=1
          and cpjmd.product_job_menu_id in (2,4,6,7,8)
        order by  cpjmd.product_job_menu_id

    </select>

    <delete id="deleteById">
        delete from csm.config_product_job_menu_detail where product_job_menu_detail_id = #{detailId}
    </delete>

    <select id="getProductJobMenuDetail" resultType="com.msun.csm.model.vo.ConfigProductJobMenuDetailVO" parameterType="com.msun.csm.model.dto.GetProductJobMenuDetailParam">
        select cpjmd.*,
        cpjm.menu_name
        from csm.config_product_job_menu cpjm
        left join csm.config_product_job_menu_detail cpjmd
        on cpjm.product_job_menu_id = cpjmd.product_job_menu_id
        where cpjmd.yy_product_id = #{yyProductId}
        and (cpjmd.upgradation_type = -1
        or cpjmd.upgradation_type in ( #{upgradationType}) )
        <if test="surveyUseFlag != null">
            and cpjmd.survey_use_flag = #{surveyUseFlag}
        </if>
        <if test="prepareUseFlag != null">
            and cpjmd.prepare_use_flag = #{prepareUseFlag}
        </if>
        order by cpjm.menu_sort
    </select>
</mapper>
