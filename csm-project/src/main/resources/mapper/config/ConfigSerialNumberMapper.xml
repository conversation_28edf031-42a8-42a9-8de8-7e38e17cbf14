<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigSerialNumberMapper">

    <sql id="BASE_COLUMN">
        config_serial_number_id, key_code, pre_char, date_format, num_length, index_num, is_deleted, creater_id, create_time, updater_id, update_time
    </sql>

    <select id="getNumByKeyCode" resultType="com.msun.csm.dao.entity.config.ConfigSerialNumber"
            parameterType="java.lang.String">
        select <include refid="BASE_COLUMN"/> from csm.config_serial_number where key_code = #{keyCode, jdbcType=VARCHAR}
    </select>
</mapper>
