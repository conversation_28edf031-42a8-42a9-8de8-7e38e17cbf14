<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigProjectSchemaMapper">
    <select id="getList" resultType="com.msun.csm.dao.entity.config.ConfigProjectSchema">
        select * from (
        select a.*,
        b.schema_name as dict_project_schema_name,
        c.user_name as create_name,
        d.user_name as update_name,
        coalesce(case a.config_type
        when '工单类型' then
        (select delivery_order_type_name
        from csm.dict_delivery_order_type
        where delivery_order_type_code = a.config_type_code
        and is_deleted = 0)
        when '产品分类' then
        (select product_type_name
        from csm.dict_product_type
        where product_type_code = a.config_type_code and is_deleted = 0)
        when '业务场景' then
        (select biz_type_name
        from csm.dict_biz_type
        where biz_type_code = a.config_type_code and is_deleted = 0)
        else '无效配置方式' end, '无效配置方式')
        as config_type_name
        from csm.config_project_schema as a
        inner join csm.dict_project_schema as b on a.dict_project_schema_code = b.schema_code and b.is_deleted = 0
        left join csm.sys_user as c on a.creater_id = c.sys_user_id and c.is_deleted = 0
        left join csm.sys_user as d on a.updater_id = d.sys_user_id and d.is_deleted = 0
        where a.is_deleted = 0
        ) as tbl where id > 0
        <if test="keyword!=null and keyword!=''">
            and (
            dict_project_schema_code like concat('%', #{keyword}, '%')
            or dict_project_schema_name like concat('%', #{keyword}, '%')
            or config_type like concat('%', #{keyword}, '%')
            or config_type_code like concat('%', #{keyword}, '%')
            or config_type_name like like concat('%', #{keyword}, '%')
            )
        </if>
        order by update_time desc
    </select>
</mapper>
