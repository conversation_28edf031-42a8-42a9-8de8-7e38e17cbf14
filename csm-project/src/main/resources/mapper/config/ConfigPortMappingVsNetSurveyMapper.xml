<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigPortMappingVsNetSurveyMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    <!--@Table csm.config_port_mapping_vs_net_survey-->
    <id column="port_mapping_vs_survey_id" jdbcType="BIGINT" property="portMappingVsSurveyId" />
    <result column="net_survey_code" jdbcType="VARCHAR" property="netSurveyCode" />
    <result column="yy_product_id" jdbcType="INTEGER" property="yyProductId" />
    <result column="port_mapping_id" jdbcType="BIGINT" property="portMappingId" />
    <result column="order_no" jdbcType="INTEGER" property="orderNo" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    port_mapping_vs_survey_id, net_survey_code, yy_product_id, port_mapping_id, order_no, 
    creater_id, create_time, updater_id, update_time, is_deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from csm.config_port_mapping_vs_net_survey
    where port_mapping_vs_survey_id = #{portMappingVsSurveyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.config_port_mapping_vs_net_survey
    where port_mapping_vs_survey_id = #{portMappingVsSurveyId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    insert into csm.config_port_mapping_vs_net_survey (port_mapping_vs_survey_id, net_survey_code, 
      yy_product_id, port_mapping_id, order_no, 
      creater_id, create_time, updater_id, 
      update_time, is_deleted)
    values (#{portMappingVsSurveyId,jdbcType=BIGINT}, #{netSurveyCode,jdbcType=VARCHAR}, 
      #{yyProductId,jdbcType=INTEGER}, #{portMappingId,jdbcType=BIGINT}, #{orderNo,jdbcType=INTEGER}, 
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    insert into csm.config_port_mapping_vs_net_survey
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="portMappingVsSurveyId != null">
        port_mapping_vs_survey_id,
      </if>
      <if test="netSurveyCode != null">
        net_survey_code,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="portMappingId != null">
        port_mapping_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="portMappingVsSurveyId != null">
        #{portMappingVsSurveyId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyCode != null">
        #{netSurveyCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="portMappingId != null">
        #{portMappingId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    update csm.config_port_mapping_vs_net_survey
    <set>
      <if test="netSurveyCode != null">
        net_survey_code = #{netSurveyCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="portMappingId != null">
        port_mapping_id = #{portMappingId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </set>
    where port_mapping_vs_survey_id = #{portMappingVsSurveyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    update csm.config_port_mapping_vs_net_survey
    set net_survey_code = #{netSurveyCode,jdbcType=VARCHAR},
      yy_product_id = #{yyProductId,jdbcType=INTEGER},
      port_mapping_id = #{portMappingId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=INTEGER},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT}
    where port_mapping_vs_survey_id = #{portMappingVsSurveyId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.config_port_mapping_vs_net_survey
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="net_survey_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.netSurveyCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="port_mapping_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.portMappingId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where port_mapping_vs_survey_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.portMappingVsSurveyId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.config_port_mapping_vs_net_survey
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="net_survey_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netSurveyCode != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.netSurveyCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.yyProductId != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="port_mapping_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.portMappingId != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.portMappingId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when port_mapping_vs_survey_id = #{item.portMappingVsSurveyId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where port_mapping_vs_survey_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.portMappingVsSurveyId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.config_port_mapping_vs_net_survey
    (port_mapping_vs_survey_id, net_survey_code, yy_product_id, port_mapping_id, order_no, 
      creater_id, create_time, updater_id, update_time, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.portMappingVsSurveyId,jdbcType=BIGINT}, #{item.netSurveyCode,jdbcType=VARCHAR}, 
        #{item.yyProductId,jdbcType=INTEGER}, #{item.portMappingId,jdbcType=BIGINT}, #{item.orderNo,jdbcType=INTEGER}, 
        #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    insert into csm.config_port_mapping_vs_net_survey
    (port_mapping_vs_survey_id, net_survey_code, yy_product_id, port_mapping_id, order_no, 
      creater_id, create_time, updater_id, update_time, is_deleted)
    values
    (#{portMappingVsSurveyId,jdbcType=BIGINT}, #{netSurveyCode,jdbcType=VARCHAR}, #{yyProductId,jdbcType=INTEGER}, 
      #{portMappingId,jdbcType=BIGINT}, #{orderNo,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=SMALLINT})
    on duplicate key update 
    port_mapping_vs_survey_id = #{portMappingVsSurveyId,jdbcType=BIGINT}, 
    net_survey_code = #{netSurveyCode,jdbcType=VARCHAR}, 
    yy_product_id = #{yyProductId,jdbcType=INTEGER}, 
    port_mapping_id = #{portMappingId,jdbcType=BIGINT}, 
    order_no = #{orderNo,jdbcType=INTEGER}, 
    creater_id = #{createrId,jdbcType=BIGINT}, 
    create_time = #{createTime,jdbcType=TIMESTAMP}, 
    updater_id = #{updaterId,jdbcType=BIGINT}, 
    update_time = #{updateTime,jdbcType=TIMESTAMP}, 
    is_deleted = #{isDeleted,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigPortMappingVsNetSurvey">
    <!--@mbg.generated-->
    insert into csm.config_port_mapping_vs_net_survey
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="portMappingVsSurveyId != null">
        port_mapping_vs_survey_id,
      </if>
      <if test="netSurveyCode != null">
        net_survey_code,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="portMappingId != null">
        port_mapping_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="portMappingVsSurveyId != null">
        #{portMappingVsSurveyId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyCode != null">
        #{netSurveyCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="portMappingId != null">
        #{portMappingId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="portMappingVsSurveyId != null">
        port_mapping_vs_survey_id = #{portMappingVsSurveyId,jdbcType=BIGINT},
      </if>
      <if test="netSurveyCode != null">
        net_survey_code = #{netSurveyCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="portMappingId != null">
        port_mapping_id = #{portMappingId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
</mapper>