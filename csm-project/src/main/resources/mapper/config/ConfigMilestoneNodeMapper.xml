<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigMilestoneNodeMapper">
    <select id="selectMilestoneNodeList" parameterType="com.msun.csm.model.dto.ConfigMilestoneNodeSelectDTO"
            resultType="com.msun.csm.model.vo.ConfigMilestoneNodeVO" >
        select
            cmn.milestone_node_config_id ,
            dps.id as project_stage_id ,--阶段id
            dps.project_stage_name ,--阶段名称
            dmn.milestone_node_name ,--里程碑节点名称
            cmn.upgradation_type ,--实施类型
            cmn.his_flag , -- 首期、后续
            cmn.telesales_flag ,-- 电销可用
            cmn.monomer_flag ,-- 单体可用
            cmn.region_flag ,-- 区域可用
            cmn.pre_node_id ,-- 前置节点限制id
            cmn.self_software_flag ,-- 自研
            cmn.external_software_flag ,-- 外采
            cmn.order_no
        from csm.config_milestone_node cmn
                 left join csm.dict_milestone_node dmn on cmn.milestone_node_id = dmn.milestone_node_id and dmn.invalid_flag= 0
                 left join csm.dict_project_stage dps on cmn.project_stage_id = dps.id and dps.invalid_flag = 0
    <where>
        <if test="milestoneNodeName != null">
            and dmn.milestone_node_name like concat('%',#{milestoneNodeName},'%')
        </if>
        <if test="upgradationTypeList != null">
            and cmn.upgradation_type in
            <foreach collection="upgradationTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="hisFlagList != null">
            and cmn.his_flag in
            <foreach collection="hisFlagList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="monomerFlag != null">
            and cmn.monomer_flag = #{monomerFlag}
        </if>
        <if test="regionFlag != null">
            and cmn.region_flag = #{regionFlag}
        </if>
        <if test="telesalesFlag != null ">
            and cmn.telesales_flag = #{telesalesFlag}
        </if>
        <if test="selfSoftwareFlag != null">
            and cmn.self_software_flag = #{selfSoftwareFlag}
        </if>
        <if test="externalSoftwareFlag != null">
            and cmn.external_software_flag = #{externalSoftwareFlag}
        </if>
    </where>
order by cmn.order_no
    </select>
</mapper>
