<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigProjectPlanItemMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        <!--@Table csm.config_project_plan_item-->
        <id column="config_project_plan_item_id" jdbcType="BIGINT" property="configProjectPlanItemId"/>
        <result column="project_plan_stage_id" jdbcType="BIGINT" property="projectPlanStageId"/>
        <result column="project_plan_item_id" jdbcType="BIGINT" property="projectPlanItemId"/>
        <result column="upgradation_type" jdbcType="SMALLINT" property="upgradationType"/>
        <result column="his_flag" jdbcType="SMALLINT" property="hisFlag"/>
        <result column="telesales_flag" jdbcType="SMALLINT" property="telesalesFlag"/>
        <result column="monomer_flag" jdbcType="SMALLINT" property="monomerFlag"/>
        <result column="region_flag" jdbcType="SMALLINT" property="regionFlag"/>
        <result column="doc_flag" jdbcType="SMALLINT" property="docFlag"/>
        <result column="plan_flag" jdbcType="SMALLINT" property="planFlag"/>
        <result column="check_flag" jdbcType="SMALLINT" property="checkFlag"/>
        <result column="check_url" jdbcType="VARCHAR" property="checkUrl"/>
        <result column="self_software_flag" jdbcType="SMALLINT" property="selfSoftwareFlag"/>
        <result column="external_software_flag" jdbcType="SMALLINT" property="externalSoftwareFlag"/>
        <result column="prior_project_plan_item_flag" jdbcType="SMALLINT" property="priorProjectPlanItemFlag"/>
        <result column="prior_project_plan_item_id" jdbcType="VARCHAR" property="priorProjectPlanItemId"/>
        <result column="milestone_flag" jdbcType="SMALLINT" property="milestoneFlag"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="front_flag" jdbcType="SMALLINT" property="frontFlag"/>
        <result column="backend_flag" jdbcType="SMALLINT" property="backendFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        config_project_plan_item_id, project_plan_stage_id, project_plan_item_id, upgradation_type,
        his_flag, telesales_flag, monomer_flag, region_flag, doc_flag, plan_flag, check_flag,
        check_url, self_software_flag, external_software_flag, prior_project_plan_item_flag,
        prior_project_plan_item_id, milestone_flag, sort, creater_id, create_time, updater_id,
        update_time, is_deleted, front_flag, backend_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.config_project_plan_item
        where config_project_plan_item_id = #{configProjectPlanItemId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.config_project_plan_item
        where config_project_plan_item_id = #{configProjectPlanItemId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_item (config_project_plan_item_id, project_plan_stage_id,
        project_plan_item_id, upgradation_type, his_flag,
        telesales_flag, monomer_flag, region_flag,
        doc_flag, plan_flag, check_flag,
        check_url, self_software_flag, external_software_flag,
        prior_project_plan_item_flag, prior_project_plan_item_id,
        milestone_flag, sort, creater_id,
        create_time, updater_id, update_time,
        is_deleted, front_flag, backend_flag
        )
        values (#{configProjectPlanItemId,jdbcType=BIGINT}, #{projectPlanStageId,jdbcType=BIGINT},
        #{projectPlanItemId,jdbcType=BIGINT}, #{upgradationType,jdbcType=SMALLINT}, #{hisFlag,jdbcType=SMALLINT},
        #{telesalesFlag,jdbcType=SMALLINT}, #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT},
        #{docFlag,jdbcType=SMALLINT}, #{planFlag,jdbcType=SMALLINT}, #{checkFlag,jdbcType=SMALLINT},
        #{checkUrl,jdbcType=VARCHAR}, #{selfSoftwareFlag,jdbcType=SMALLINT}, #{externalSoftwareFlag,jdbcType=SMALLINT},
        #{priorProjectPlanItemFlag,jdbcType=SMALLINT}, #{priorProjectPlanItemId,jdbcType=VARCHAR},
        #{milestoneFlag,jdbcType=SMALLINT}, #{sort,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{frontFlag,jdbcType=SMALLINT}, #{backendFlag,jdbcType=SMALLINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanItemId != null">
                config_project_plan_item_id,
            </if>
            <if test="projectPlanStageId != null">
                project_plan_stage_id,
            </if>
            <if test="projectPlanItemId != null">
                project_plan_item_id,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="hisFlag != null">
                his_flag,
            </if>
            <if test="telesalesFlag != null">
                telesales_flag,
            </if>
            <if test="monomerFlag != null">
                monomer_flag,
            </if>
            <if test="regionFlag != null">
                region_flag,
            </if>
            <if test="docFlag != null">
                doc_flag,
            </if>
            <if test="planFlag != null">
                plan_flag,
            </if>
            <if test="checkFlag != null">
                check_flag,
            </if>
            <if test="checkUrl != null">
                check_url,
            </if>
            <if test="selfSoftwareFlag != null">
                self_software_flag,
            </if>
            <if test="externalSoftwareFlag != null">
                external_software_flag,
            </if>
            <if test="priorProjectPlanItemFlag != null">
                prior_project_plan_item_flag,
            </if>
            <if test="priorProjectPlanItemId != null">
                prior_project_plan_item_id,
            </if>
            <if test="milestoneFlag != null">
                milestone_flag,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="frontFlag != null">
                front_flag,
            </if>
            <if test="backendFlag != null">
                backend_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanItemId != null">
                #{configProjectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanStageId != null">
                #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemId != null">
                #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="docFlag != null">
                #{docFlag,jdbcType=SMALLINT},
            </if>
            <if test="planFlag != null">
                #{planFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkFlag != null">
                #{checkFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkUrl != null">
                #{checkUrl,jdbcType=VARCHAR},
            </if>
            <if test="selfSoftwareFlag != null">
                #{selfSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="externalSoftwareFlag != null">
                #{externalSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemFlag != null">
                #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemId != null">
                #{priorProjectPlanItemId,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="frontFlag != null">
                #{frontFlag,jdbcType=SMALLINT},
            </if>
            <if test="backendFlag != null">
                #{backendFlag,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        update csm.config_project_plan_item
        <set>
            <if test="projectPlanStageId != null">
                project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemId != null">
                project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                his_flag = #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                region_flag = #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="docFlag != null">
                doc_flag = #{docFlag,jdbcType=SMALLINT},
            </if>
            <if test="planFlag != null">
                plan_flag = #{planFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkFlag != null">
                check_flag = #{checkFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkUrl != null">
                check_url = #{checkUrl,jdbcType=VARCHAR},
            </if>
            <if test="selfSoftwareFlag != null">
                self_software_flag = #{selfSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="externalSoftwareFlag != null">
                external_software_flag = #{externalSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemFlag != null">
                prior_project_plan_item_flag = #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemId != null">
                prior_project_plan_item_id = #{priorProjectPlanItemId,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                milestone_flag = #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="frontFlag != null">
                front_flag = #{frontFlag,jdbcType=SMALLINT},
            </if>
            <if test="backendFlag != null">
                backend_flag = #{backendFlag,jdbcType=SMALLINT},
            </if>
        </set>
        where config_project_plan_item_id = #{configProjectPlanItemId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        update csm.config_project_plan_item
        set project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT},
        project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT},
        upgradation_type = #{upgradationType,jdbcType=SMALLINT},
        his_flag = #{hisFlag,jdbcType=SMALLINT},
        telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
        monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
        region_flag = #{regionFlag,jdbcType=SMALLINT},
        doc_flag = #{docFlag,jdbcType=SMALLINT},
        plan_flag = #{planFlag,jdbcType=SMALLINT},
        check_flag = #{checkFlag,jdbcType=SMALLINT},
        check_url = #{checkUrl,jdbcType=VARCHAR},
        self_software_flag = #{selfSoftwareFlag,jdbcType=SMALLINT},
        external_software_flag = #{externalSoftwareFlag,jdbcType=SMALLINT},
        prior_project_plan_item_flag = #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
        prior_project_plan_item_id = #{priorProjectPlanItemId,jdbcType=VARCHAR},
        milestone_flag = #{milestoneFlag,jdbcType=SMALLINT},
        sort = #{sort,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        front_flag = #{frontFlag,jdbcType=SMALLINT},
        backend_flag = #{backendFlag,jdbcType=SMALLINT}
        where config_project_plan_item_id = #{configProjectPlanItemId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_project_plan_item
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_plan_stage_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.projectPlanStageId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="project_plan_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.projectPlanItemId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.upgradationType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="his_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.hisFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="telesales_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.telesalesFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="monomer_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.monomerFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="region_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.regionFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="doc_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.docFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="plan_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.planFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="check_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.checkFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="check_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.checkUrl,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="self_software_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.selfSoftwareFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="external_software_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.externalSoftwareFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="prior_project_plan_item_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.priorProjectPlanItemFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="prior_project_plan_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.priorProjectPlanItemId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="milestone_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.milestoneFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.sort,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="front_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.frontFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="backend_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                    #{item.backendFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where config_project_plan_item_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configProjectPlanItemId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_project_plan_item
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="project_plan_stage_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanStageId != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.projectPlanStageId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_plan_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanItemId != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.projectPlanItemId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.upgradationType != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.upgradationType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.hisFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="telesales_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.telesalesFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.telesalesFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="monomer_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.monomerFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.monomerFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="region_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.regionFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.regionFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="doc_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.docFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.docFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="plan_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.planFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.planFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="check_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.checkFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.checkFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="check_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.checkUrl != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.checkUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="self_software_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.selfSoftwareFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.selfSoftwareFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="external_software_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.externalSoftwareFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.externalSoftwareFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="prior_project_plan_item_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.priorProjectPlanItemFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.priorProjectPlanItemFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="prior_project_plan_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.priorProjectPlanItemId != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.priorProjectPlanItemId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="milestone_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.milestoneFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.milestoneFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sort != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.sort,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="front_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.frontFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.frontFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="backend_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.backendFlag != null">
                        when config_project_plan_item_id = #{item.configProjectPlanItemId,jdbcType=BIGINT} then
                        #{item.backendFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where config_project_plan_item_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configProjectPlanItemId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_item
        (config_project_plan_item_id, project_plan_stage_id, project_plan_item_id, upgradation_type,
        his_flag, telesales_flag, monomer_flag, region_flag, doc_flag, plan_flag, check_flag,
        check_url, self_software_flag, external_software_flag, prior_project_plan_item_flag,
        prior_project_plan_item_id, milestone_flag, sort, creater_id, create_time, updater_id,
        update_time, is_deleted, front_flag, backend_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.configProjectPlanItemId,jdbcType=BIGINT}, #{item.projectPlanStageId,jdbcType=BIGINT},
            #{item.projectPlanItemId,jdbcType=BIGINT}, #{item.upgradationType,jdbcType=SMALLINT},
            #{item.hisFlag,jdbcType=SMALLINT}, #{item.telesalesFlag,jdbcType=SMALLINT},
            #{item.monomerFlag,jdbcType=SMALLINT},
            #{item.regionFlag,jdbcType=SMALLINT}, #{item.docFlag,jdbcType=SMALLINT}, #{item.planFlag,jdbcType=SMALLINT},
            #{item.checkFlag,jdbcType=SMALLINT}, #{item.checkUrl,jdbcType=VARCHAR},
            #{item.selfSoftwareFlag,jdbcType=SMALLINT},
            #{item.externalSoftwareFlag,jdbcType=SMALLINT}, #{item.priorProjectPlanItemFlag,jdbcType=SMALLINT},
            #{item.priorProjectPlanItemId,jdbcType=VARCHAR}, #{item.milestoneFlag,jdbcType=SMALLINT},
            #{item.sort,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT},
            #{item.frontFlag,jdbcType=SMALLINT}, #{item.backendFlag,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_item
        (config_project_plan_item_id, project_plan_stage_id, project_plan_item_id, upgradation_type,
        his_flag, telesales_flag, monomer_flag, region_flag, doc_flag, plan_flag, check_flag,
        check_url, self_software_flag, external_software_flag, prior_project_plan_item_flag,
        prior_project_plan_item_id, milestone_flag, sort, creater_id, create_time, updater_id,
        update_time, is_deleted, front_flag, backend_flag)
        values
        (#{configProjectPlanItemId,jdbcType=BIGINT}, #{projectPlanStageId,jdbcType=BIGINT},
        #{projectPlanItemId,jdbcType=BIGINT}, #{upgradationType,jdbcType=SMALLINT}, #{hisFlag,jdbcType=SMALLINT},
        #{telesalesFlag,jdbcType=SMALLINT}, #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT},
        #{docFlag,jdbcType=SMALLINT}, #{planFlag,jdbcType=SMALLINT}, #{checkFlag,jdbcType=SMALLINT},
        #{checkUrl,jdbcType=VARCHAR}, #{selfSoftwareFlag,jdbcType=SMALLINT}, #{externalSoftwareFlag,jdbcType=SMALLINT},
        #{priorProjectPlanItemFlag,jdbcType=SMALLINT}, #{priorProjectPlanItemId,jdbcType=VARCHAR},
        #{milestoneFlag,jdbcType=SMALLINT}, #{sort,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT}, #{frontFlag,jdbcType=SMALLINT}, #{backendFlag,jdbcType=SMALLINT}
        )
        on duplicate key update
        config_project_plan_item_id = #{configProjectPlanItemId,jdbcType=BIGINT},
        project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT},
        project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT},
        upgradation_type = #{upgradationType,jdbcType=SMALLINT},
        his_flag = #{hisFlag,jdbcType=SMALLINT},
        telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
        monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
        region_flag = #{regionFlag,jdbcType=SMALLINT},
        doc_flag = #{docFlag,jdbcType=SMALLINT},
        plan_flag = #{planFlag,jdbcType=SMALLINT},
        check_flag = #{checkFlag,jdbcType=SMALLINT},
        check_url = #{checkUrl,jdbcType=VARCHAR},
        self_software_flag = #{selfSoftwareFlag,jdbcType=SMALLINT},
        external_software_flag = #{externalSoftwareFlag,jdbcType=SMALLINT},
        prior_project_plan_item_flag = #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
        prior_project_plan_item_id = #{priorProjectPlanItemId,jdbcType=VARCHAR},
        milestone_flag = #{milestoneFlag,jdbcType=SMALLINT},
        sort = #{sort,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        front_flag = #{frontFlag,jdbcType=SMALLINT},
        backend_flag = #{backendFlag,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanItem">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanItemId != null">
                config_project_plan_item_id,
            </if>
            <if test="projectPlanStageId != null">
                project_plan_stage_id,
            </if>
            <if test="projectPlanItemId != null">
                project_plan_item_id,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="hisFlag != null">
                his_flag,
            </if>
            <if test="telesalesFlag != null">
                telesales_flag,
            </if>
            <if test="monomerFlag != null">
                monomer_flag,
            </if>
            <if test="regionFlag != null">
                region_flag,
            </if>
            <if test="docFlag != null">
                doc_flag,
            </if>
            <if test="planFlag != null">
                plan_flag,
            </if>
            <if test="checkFlag != null">
                check_flag,
            </if>
            <if test="checkUrl != null">
                check_url,
            </if>
            <if test="selfSoftwareFlag != null">
                self_software_flag,
            </if>
            <if test="externalSoftwareFlag != null">
                external_software_flag,
            </if>
            <if test="priorProjectPlanItemFlag != null">
                prior_project_plan_item_flag,
            </if>
            <if test="priorProjectPlanItemId != null">
                prior_project_plan_item_id,
            </if>
            <if test="milestoneFlag != null">
                milestone_flag,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="frontFlag != null">
                front_flag,
            </if>
            <if test="backendFlag != null">
                backend_flag,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanItemId != null">
                #{configProjectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanStageId != null">
                #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemId != null">
                #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="docFlag != null">
                #{docFlag,jdbcType=SMALLINT},
            </if>
            <if test="planFlag != null">
                #{planFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkFlag != null">
                #{checkFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkUrl != null">
                #{checkUrl,jdbcType=VARCHAR},
            </if>
            <if test="selfSoftwareFlag != null">
                #{selfSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="externalSoftwareFlag != null">
                #{externalSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemFlag != null">
                #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemId != null">
                #{priorProjectPlanItemId,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="frontFlag != null">
                #{frontFlag,jdbcType=SMALLINT},
            </if>
            <if test="backendFlag != null">
                #{backendFlag,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="configProjectPlanItemId != null">
                config_project_plan_item_id = #{configProjectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanStageId != null">
                project_plan_stage_id = #{projectPlanStageId,jdbcType=BIGINT},
            </if>
            <if test="projectPlanItemId != null">
                project_plan_item_id = #{projectPlanItemId,jdbcType=BIGINT},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                his_flag = #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                region_flag = #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="docFlag != null">
                doc_flag = #{docFlag,jdbcType=SMALLINT},
            </if>
            <if test="planFlag != null">
                plan_flag = #{planFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkFlag != null">
                check_flag = #{checkFlag,jdbcType=SMALLINT},
            </if>
            <if test="checkUrl != null">
                check_url = #{checkUrl,jdbcType=VARCHAR},
            </if>
            <if test="selfSoftwareFlag != null">
                self_software_flag = #{selfSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="externalSoftwareFlag != null">
                external_software_flag = #{externalSoftwareFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemFlag != null">
                prior_project_plan_item_flag = #{priorProjectPlanItemFlag,jdbcType=SMALLINT},
            </if>
            <if test="priorProjectPlanItemId != null">
                prior_project_plan_item_id = #{priorProjectPlanItemId,jdbcType=VARCHAR},
            </if>
            <if test="milestoneFlag != null">
                milestone_flag = #{milestoneFlag,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="frontFlag != null">
                front_flag = #{frontFlag,jdbcType=SMALLINT},
            </if>
            <if test="backendFlag != null">
                backend_flag = #{backendFlag,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
</mapper>
