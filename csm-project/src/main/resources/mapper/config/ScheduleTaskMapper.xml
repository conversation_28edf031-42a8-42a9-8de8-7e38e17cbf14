<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ScheduleTaskMapper">

    <update id="updateForTask" parameterType="com.msun.csm.model.dto.ScheduleTaskDTO">
        update csm.schedule_task
        <set>
            <if test="taskName != null">
                task_name = #{taskName},
            </if>
            <if test="taskCode != null">
                task_code = #{taskCode},
            </if>
            <if test="enableFlag != null">
                enable_flag = #{enableFlag},
            </if>
            <if test="taskMessage != null">
                task_message = #{taskMessage},
            </if>
            <if test="triggerTime != ''">
                trigger_time = #{triggerTime},
            </if>
            <if test="intervalTimeUnit != null">
                interval_time_unit = #{intervalTimeUnit},
            </if>
            <if test="lastRunTimeFlag == 1">
                plan_run_time = #{planRunTimeForPG},
                task_update_time = now(),
                last_run_time = null
            </if>
        </set>
        where is_deleted = 0 and schedule_task_id = #{scheduleTaskId}
    </update>


    <select id="getRunningCronTasks" resultType="com.msun.csm.dao.entity.config.ScheduleTask">
        select schedule_task_id,
               task_name,
               enable_flag,
               task_message,
               trigger_time,
               interval_time_unit,
               last_run_time,
               is_deleted,
               task_code,
               task_update_time,
               plan_run_time,
               cron_expr
        from csm.schedule_task
        where is_deleted = 0
          and enable_flag = 1
          and plan_run_time &lt;= #{now}
          and cron_expr is not null
          and length(cron_expr) &gt; 0
    </select>
</mapper>
