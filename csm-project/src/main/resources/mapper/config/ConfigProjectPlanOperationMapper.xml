<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigProjectPlanOperationMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        <!--@Table csm.config_project_plan_operation-->
        <id column="config_project_plan_operation_id" jdbcType="BIGINT" property="configProjectPlanOperationId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="is_common" jdbcType="SMALLINT" property="isCommon"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="project_plan_item_code" jdbcType="VARCHAR" property="projectPlanItemCode"/>
        <result column="jump_type" jdbcType="SMALLINT" property="jumpType"/>
        <result column="jump_path" jdbcType="VARCHAR" property="jumpPath"/>
        <result column="enable_project_status" jdbcType="SMALLINT" property="enableProjectStatus"/>
        <result column="enable_project_plan_status" jdbcType="SMALLINT" property="enableProjectPlanStatus"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="enable_project_plan_type" jdbcType="SMALLINT" property="enableProjectPlanType"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        config_project_plan_operation_id, "name", is_common, sort, project_plan_item_code,
        jump_type, jump_path, enable_project_status, enable_project_plan_status, creater_id,
        create_time, updater_id, update_time, is_deleted, enable_project_plan_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.config_project_plan_operation
        where config_project_plan_operation_id = #{configProjectPlanOperationId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.config_project_plan_operation
        where config_project_plan_operation_id = #{configProjectPlanOperationId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_operation (config_project_plan_operation_id, "name",
        is_common, sort, project_plan_item_code,
        jump_type, jump_path, enable_project_status,
        enable_project_plan_status, creater_id, create_time,
        updater_id, update_time, is_deleted,
        enable_project_plan_type)
        values (#{configProjectPlanOperationId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
        #{isCommon,jdbcType=SMALLINT}, #{sort,jdbcType=SMALLINT}, #{projectPlanItemCode,jdbcType=VARCHAR},
        #{jumpType,jdbcType=SMALLINT}, #{jumpPath,jdbcType=VARCHAR}, #{enableProjectStatus,jdbcType=SMALLINT},
        #{enableProjectPlanStatus,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},
        #{enableProjectPlanType,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_operation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanOperationId != null">
                config_project_plan_operation_id,
            </if>
            <if test="name != null">
                "name",
            </if>
            <if test="isCommon != null">
                is_common,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code,
            </if>
            <if test="jumpType != null">
                jump_type,
            </if>
            <if test="jumpPath != null">
                jump_path,
            </if>
            <if test="enableProjectStatus != null">
                enable_project_status,
            </if>
            <if test="enableProjectPlanStatus != null">
                enable_project_plan_status,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="enableProjectPlanType != null">
                enable_project_plan_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanOperationId != null">
                #{configProjectPlanOperationId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="isCommon != null">
                #{isCommon,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="projectPlanItemCode != null">
                #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
                #{jumpType,jdbcType=SMALLINT},
            </if>
            <if test="jumpPath != null">
                #{jumpPath,jdbcType=VARCHAR},
            </if>
            <if test="enableProjectStatus != null">
                #{enableProjectStatus,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanStatus != null">
                #{enableProjectPlanStatus,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanType != null">
                #{enableProjectPlanType,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        update csm.config_project_plan_operation
        <set>
            <if test="name != null">
                "name" = #{name,jdbcType=VARCHAR},
            </if>
            <if test="isCommon != null">
                is_common = #{isCommon,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType,jdbcType=SMALLINT},
            </if>
            <if test="jumpPath != null">
                jump_path = #{jumpPath,jdbcType=VARCHAR},
            </if>
            <if test="enableProjectStatus != null">
                enable_project_status = #{enableProjectStatus,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanStatus != null">
                enable_project_plan_status = #{enableProjectPlanStatus,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanType != null">
                enable_project_plan_type = #{enableProjectPlanType,jdbcType=SMALLINT},
            </if>
        </set>
        where config_project_plan_operation_id = #{configProjectPlanOperationId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        update csm.config_project_plan_operation
        set "name" = #{name,jdbcType=VARCHAR},
        is_common = #{isCommon,jdbcType=SMALLINT},
        sort = #{sort,jdbcType=SMALLINT},
        project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR},
        jump_type = #{jumpType,jdbcType=SMALLINT},
        jump_path = #{jumpPath,jdbcType=VARCHAR},
        enable_project_status = #{enableProjectStatus,jdbcType=SMALLINT},
        enable_project_plan_status = #{enableProjectPlanStatus,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        enable_project_plan_type = #{enableProjectPlanType,jdbcType=SMALLINT}
        where config_project_plan_operation_id = #{configProjectPlanOperationId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_project_plan_operation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="&quot;name&quot; = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_common = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.isCommon,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.sort,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="project_plan_item_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.projectPlanItemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="jump_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.jumpType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="jump_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.jumpPath,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enable_project_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.enableProjectStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="enable_project_plan_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.enableProjectPlanStatus,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="enable_project_plan_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT} then
                    #{item.enableProjectPlanType,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where config_project_plan_operation_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configProjectPlanOperationId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_project_plan_operation
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="&quot;name&quot; = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.name != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.name,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_common = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isCommon != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.isCommon,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sort != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.sort,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="project_plan_item_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.projectPlanItemCode != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.projectPlanItemCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="jump_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.jumpType != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.jumpType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="jump_path = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.jumpPath != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.jumpPath,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="enable_project_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.enableProjectStatus != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.enableProjectStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="enable_project_plan_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.enableProjectPlanStatus != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.enableProjectPlanStatus,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="enable_project_plan_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.enableProjectPlanType != null">
                        when config_project_plan_operation_id = #{item.configProjectPlanOperationId,jdbcType=BIGINT}
                        then #{item.enableProjectPlanType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where config_project_plan_operation_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configProjectPlanOperationId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_operation
        (config_project_plan_operation_id, "name", is_common, sort, project_plan_item_code,
        jump_type, jump_path, enable_project_status, enable_project_plan_status, creater_id,
        create_time, updater_id, update_time, is_deleted, enable_project_plan_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.configProjectPlanOperationId,jdbcType=BIGINT}, #{item.name,jdbcType=VARCHAR},
            #{item.isCommon,jdbcType=SMALLINT}, #{item.sort,jdbcType=SMALLINT},
            #{item.projectPlanItemCode,jdbcType=VARCHAR},
            #{item.jumpType,jdbcType=SMALLINT}, #{item.jumpPath,jdbcType=VARCHAR},
            #{item.enableProjectStatus,jdbcType=SMALLINT},
            #{item.enableProjectPlanStatus,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT}, #{item.enableProjectPlanType,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_operation
        (config_project_plan_operation_id, "name", is_common, sort, project_plan_item_code,
        jump_type, jump_path, enable_project_status, enable_project_plan_status, creater_id,
        create_time, updater_id, update_time, is_deleted, enable_project_plan_type)
        values
        (#{configProjectPlanOperationId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{isCommon,jdbcType=SMALLINT},
        #{sort,jdbcType=SMALLINT}, #{projectPlanItemCode,jdbcType=VARCHAR}, #{jumpType,jdbcType=SMALLINT},
        #{jumpPath,jdbcType=VARCHAR}, #{enableProjectStatus,jdbcType=SMALLINT},
        #{enableProjectPlanStatus,jdbcType=SMALLINT},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}, #{enableProjectPlanType,jdbcType=SMALLINT}
        )
        on duplicate key update
        config_project_plan_operation_id = #{configProjectPlanOperationId,jdbcType=BIGINT},
        "name" = #{name,jdbcType=VARCHAR},
        is_common = #{isCommon,jdbcType=SMALLINT},
        sort = #{sort,jdbcType=SMALLINT},
        project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR},
        jump_type = #{jumpType,jdbcType=SMALLINT},
        jump_path = #{jumpPath,jdbcType=VARCHAR},
        enable_project_status = #{enableProjectStatus,jdbcType=SMALLINT},
        enable_project_plan_status = #{enableProjectPlanStatus,jdbcType=SMALLINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
        enable_project_plan_type = #{enableProjectPlanType,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigProjectPlanOperation">
        <!--@mbg.generated-->
        insert into csm.config_project_plan_operation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanOperationId != null">
                config_project_plan_operation_id,
            </if>
            <if test="name != null">
                "name",
            </if>
            <if test="isCommon != null">
                is_common,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code,
            </if>
            <if test="jumpType != null">
                jump_type,
            </if>
            <if test="jumpPath != null">
                jump_path,
            </if>
            <if test="enableProjectStatus != null">
                enable_project_status,
            </if>
            <if test="enableProjectPlanStatus != null">
                enable_project_plan_status,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="enableProjectPlanType != null">
                enable_project_plan_type,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configProjectPlanOperationId != null">
                #{configProjectPlanOperationId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="isCommon != null">
                #{isCommon,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=SMALLINT},
            </if>
            <if test="projectPlanItemCode != null">
                #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
                #{jumpType,jdbcType=SMALLINT},
            </if>
            <if test="jumpPath != null">
                #{jumpPath,jdbcType=VARCHAR},
            </if>
            <if test="enableProjectStatus != null">
                #{enableProjectStatus,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanStatus != null">
                #{enableProjectPlanStatus,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanType != null">
                #{enableProjectPlanType,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="configProjectPlanOperationId != null">
                config_project_plan_operation_id = #{configProjectPlanOperationId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                "name" = #{name,jdbcType=VARCHAR},
            </if>
            <if test="isCommon != null">
                is_common = #{isCommon,jdbcType=SMALLINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=SMALLINT},
            </if>
            <if test="projectPlanItemCode != null">
                project_plan_item_code = #{projectPlanItemCode,jdbcType=VARCHAR},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType,jdbcType=SMALLINT},
            </if>
            <if test="jumpPath != null">
                jump_path = #{jumpPath,jdbcType=VARCHAR},
            </if>
            <if test="enableProjectStatus != null">
                enable_project_status = #{enableProjectStatus,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanStatus != null">
                enable_project_plan_status = #{enableProjectPlanStatus,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="enableProjectPlanType != null">
                enable_project_plan_type = #{enableProjectPlanType,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="getOperationListByPlan" resultType="com.msun.csm.model.resp.projectplan.OperationResp">
        select config_project_plan_operation_id as id,
               "name"                           as name,
               jump_type                        as jumpType,
               jump_path                        as jumpPath,
               operation_code                   as operationCode
        from config_project_plan_operation
        where is_deleted = 0
          and (enable_project_plan_type = -1 or enable_project_plan_type = #{planType})
          and (is_common = 1 or project_plan_item_code = (select project_plan_item_code
                                                          from dict_project_plan_item
                                                          where project_plan_item_id = #{projectPlanItemId}))
          and enable_project_status = -1
          and (enable_project_plan_status = -1 or enable_project_plan_status = #{status})
        order by sort asc
    </select>

    <select id="getOperationListByTodo" resultType="com.msun.csm.model.resp.projectplan.OperationResp">
        select config_project_plan_operation_id as id,
               "name"                           as name,
               jump_type                        as jumpType,
               jump_path                        as jumpPath,
               operation_code                   as operationCode
        from config_project_plan_operation
        where is_deleted = 0
          and enable_project_plan_type = -1
          and (is_common = 1 or project_plan_item_code = (select project_plan_item_code
                                                          from dict_project_plan_item
                                                          where project_plan_item_id = (select project_plan_item_id
                                                                                        from proj_project_plan
                                                                                        where project_plan_id = #{projectPlanId})))
          and enable_project_status = -1
          and (enable_project_plan_status = -1 or enable_project_plan_status = #{status})
        order by sort asc
    </select>
</mapper>
