<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigYxMapper">

    <update id="discardConfigYxByPlatformId">
        update csm.config_yx
        set is_deleted  = 1,
            update_time = now()
        where platform_id = #{platformId}
    </update>

    <update id="discardConfigYxById">
        update csm.config_yx
        set is_deleted  = 1,
            update_time = now()
        where id = #{id}
    </update>

    <update id="updateStatusById">
        update csm.config_yx
        set config_status = #{configStatus},
            update_time   = now()
        where id = #{id}
    </update>

</mapper>
