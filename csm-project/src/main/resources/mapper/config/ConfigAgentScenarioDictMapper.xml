<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigAgentScenarioDictMapper">

    <!-- 查询AI智能体场景配置 -->
    <select id="getAgentScenarioConfig" resultType="com.msun.csm.model.resp.DictAgentChatScenarioConfigResp">
        SELECT
            dasc.agent_scenario_config_id,
            dasc.agent_code,
            dasc.scenario_code,
            dasc.scenario_desc,
            dasc.scenario_prompt,
            dasc.create_time,
            dasc.update_time,
            dac.agent_chat_id,
            dac.agent_name,
            dac.agent_address,
            dac.agent_address_produce,
            dac.agent_key
        FROM csm.dict_agent_scenario_config dasc
        LEFT JOIN csm.dict_agent_chat dac ON dasc.agent_code = dac.agent_code AND dac.is_deleted = 0
        WHERE dasc.is_deleted = 0
        <if test="req != null and req.scenarioCode != null and req.scenarioCode != ''">
            AND dasc.scenario_code LIKE CONCAT('%', #{req.scenarioCode}, '%')
        </if>
        <if test="req != null and req.scenarioDesc != null and req.scenarioDesc != ''">
            AND dasc.scenario_desc LIKE CONCAT('%', #{req.scenarioDesc}, '%')
        </if>
        ORDER BY dasc.create_time DESC
    </select>

    <!-- 删除AI智能体场景配置（逻辑删除） -->
    <update id="deleteAgentScenarioConfig">
        UPDATE csm.dict_agent_scenario_config
        SET is_deleted = 1,
            update_time = NOW()
        WHERE agent_scenario_config_id = #{agentScenarioConfigId}
          AND is_deleted = 0
    </update>

    <!-- 新增AI智能体场景配置 -->
    <insert id="insertAgentScenarioConfig">
        INSERT INTO csm.dict_agent_scenario_config (
            agent_scenario_config_id,
            agent_code,
            scenario_code,
            scenario_desc,
            scenario_prompt,
            is_deleted,
            creater_id,
            create_time,
            updater_id,
            update_time
        ) VALUES (
            #{agentScenarioConfigId},
            #{agentCode},
            #{scenarioCode},
            #{scenarioDesc},
            #{scenarioPrompt},
            0,
            #{createrId},
            NOW(),
            #{updaterId},
            NOW()
        )
    </insert>

    <!-- 修改AI智能体场景配置 -->
    <update id="updateAgentScenarioConfig">
        UPDATE csm.dict_agent_scenario_config
        SET agent_code = #{agentCode},
            scenario_code = #{scenarioCode},
            scenario_desc = #{scenarioDesc},
            scenario_prompt = #{scenarioPrompt},
            updater_id = #{updaterId},
            update_time = NOW()
        WHERE agent_scenario_config_id = #{agentScenarioConfigId}
          AND is_deleted = 0
    </update>

    <!-- 根据ID查询配置是否存在 -->
    <select id="countById" resultType="int">
        SELECT COUNT(1)
        FROM csm.dict_agent_scenario_config
        WHERE agent_scenario_config_id = #{agentScenarioConfigId}
          AND is_deleted = 0
    </select>

    <!-- 查询智能体下拉列表 -->
    <select id="getAgentChatDropdownList" resultType="com.msun.csm.model.resp.DictAgentChatDropdownResp">
        SELECT
            agent_code as id,
            agent_name as name
        FROM csm.dict_agent_chat
        WHERE is_deleted = 0
        ORDER BY agent_name
    </select>

</mapper>
