<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigCustomFormLimitMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    <!--@Table csm.config_custom_form_limit-->
    <id column="custom_form_limit_id" jdbcType="BIGINT" property="customFormLimitId" />
    <result column="custom_info_id" jdbcType="BIGINT" property="customInfoId" />
    <result column="hospital_info_id" jdbcType="BIGINT" property="hospitalInfoId" />
    <result column="order_product_id" jdbcType="BIGINT" property="orderProductId" />
    <result column="switch_flag" jdbcType="SMALLINT" property="switchFlag" />
    <result column="days_after_check" jdbcType="SMALLINT" property="daysAfterCheck" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="msun_health_module_code" jdbcType="VARCHAR" property="msunHealthModuleCode" />
    <result column="accept_time" jdbcType="TIMESTAMP" property="acceptTime" />
    <result column="has_deal" jdbcType="SMALLINT" property="hasDeal" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    custom_form_limit_id, custom_info_id, hospital_info_id, order_product_id, switch_flag,
    days_after_check, is_deleted, creater_id, create_time, updater_id, update_time, msun_health_module_code,
    accept_time, has_deal
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.config_custom_form_limit
    where custom_form_limit_id = #{customFormLimitId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.config_custom_form_limit
    where custom_form_limit_id = #{customFormLimitId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    insert into csm.config_custom_form_limit (custom_form_limit_id, custom_info_id, hospital_info_id,
      order_product_id, switch_flag, days_after_check,
      is_deleted, creater_id, create_time,
      updater_id, update_time, msun_health_module_code,
      accept_time, has_deal)
    values (#{customFormLimitId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{hospitalInfoId,jdbcType=BIGINT},
      #{orderProductId,jdbcType=BIGINT}, #{switchFlag,jdbcType=SMALLINT}, #{daysAfterCheck,jdbcType=SMALLINT},
      #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{msunHealthModuleCode,jdbcType=VARCHAR},
      #{acceptTime,jdbcType=TIMESTAMP}, #{hasDeal,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    insert into csm.config_custom_form_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customFormLimitId != null">
        custom_form_limit_id,
      </if>
      <if test="customInfoId != null">
        custom_info_id,
      </if>
      <if test="hospitalInfoId != null">
        hospital_info_id,
      </if>
      <if test="orderProductId != null">
        order_product_id,
      </if>
      <if test="switchFlag != null">
        switch_flag,
      </if>
      <if test="daysAfterCheck != null">
        days_after_check,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="msunHealthModuleCode != null">
        msun_health_module_code,
      </if>
      <if test="acceptTime != null">
        accept_time,
      </if>
      <if test="hasDeal != null">
        has_deal,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customFormLimitId != null">
        #{customFormLimitId,jdbcType=BIGINT},
      </if>
      <if test="customInfoId != null">
        #{customInfoId,jdbcType=BIGINT},
      </if>
      <if test="hospitalInfoId != null">
        #{hospitalInfoId,jdbcType=BIGINT},
      </if>
      <if test="orderProductId != null">
        #{orderProductId,jdbcType=BIGINT},
      </if>
      <if test="switchFlag != null">
        #{switchFlag,jdbcType=SMALLINT},
      </if>
      <if test="daysAfterCheck != null">
        #{daysAfterCheck,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msunHealthModuleCode != null">
        #{msunHealthModuleCode,jdbcType=VARCHAR},
      </if>
      <if test="acceptTime != null">
        #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasDeal != null">
        #{hasDeal,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    update csm.config_custom_form_limit
    <set>
      <if test="customInfoId != null">
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
      </if>
      <if test="hospitalInfoId != null">
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
      </if>
      <if test="orderProductId != null">
        order_product_id = #{orderProductId,jdbcType=BIGINT},
      </if>
      <if test="switchFlag != null">
        switch_flag = #{switchFlag,jdbcType=SMALLINT},
      </if>
      <if test="daysAfterCheck != null">
        days_after_check = #{daysAfterCheck,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msunHealthModuleCode != null">
        msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
      </if>
      <if test="acceptTime != null">
        accept_time = #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasDeal != null">
        has_deal = #{hasDeal,jdbcType=SMALLINT},
      </if>
    </set>
    where custom_form_limit_id = #{customFormLimitId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    update csm.config_custom_form_limit
    set custom_info_id = #{customInfoId,jdbcType=BIGINT},
      hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
      order_product_id = #{orderProductId,jdbcType=BIGINT},
      switch_flag = #{switchFlag,jdbcType=SMALLINT},
      days_after_check = #{daysAfterCheck,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
      accept_time = #{acceptTime,jdbcType=TIMESTAMP},
      has_deal = #{hasDeal,jdbcType=SMALLINT}
    where custom_form_limit_id = #{customFormLimitId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.config_custom_form_limit
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="custom_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.customInfoId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="hospital_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.hospitalInfoId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="order_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.orderProductId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="switch_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.switchFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="days_after_check = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.daysAfterCheck,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="msun_health_module_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.msunHealthModuleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="accept_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.acceptTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="has_deal = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.hasDeal,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where custom_form_limit_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.customFormLimitId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.config_custom_form_limit
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="custom_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customInfoId != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.customInfoId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="hospital_info_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hospitalInfoId != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.hospitalInfoId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderProductId != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.orderProductId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="switch_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.switchFlag != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.switchFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="days_after_check = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.daysAfterCheck != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.daysAfterCheck,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="msun_health_module_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.msunHealthModuleCode != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.msunHealthModuleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="accept_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.acceptTime != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.acceptTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="has_deal = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hasDeal != null">
            when custom_form_limit_id = #{item.customFormLimitId,jdbcType=BIGINT} then #{item.hasDeal,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where custom_form_limit_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.customFormLimitId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.config_custom_form_limit
    (custom_form_limit_id, custom_info_id, hospital_info_id, order_product_id, switch_flag,
      days_after_check, is_deleted, creater_id, create_time, updater_id, update_time,
      msun_health_module_code, accept_time, has_deal)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.customFormLimitId,jdbcType=BIGINT}, #{item.customInfoId,jdbcType=BIGINT},
        #{item.hospitalInfoId,jdbcType=BIGINT}, #{item.orderProductId,jdbcType=BIGINT},
        #{item.switchFlag,jdbcType=SMALLINT}, #{item.daysAfterCheck,jdbcType=SMALLINT},
        #{item.isDeleted,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.msunHealthModuleCode,jdbcType=VARCHAR},
        #{item.acceptTime,jdbcType=TIMESTAMP}, #{item.hasDeal,jdbcType=SMALLINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    insert into csm.config_custom_form_limit
    (custom_form_limit_id, custom_info_id, hospital_info_id, order_product_id, switch_flag,
      days_after_check, is_deleted, creater_id, create_time, updater_id, update_time,
      msun_health_module_code, accept_time, has_deal)
    values
    (#{customFormLimitId,jdbcType=BIGINT}, #{customInfoId,jdbcType=BIGINT}, #{hospitalInfoId,jdbcType=BIGINT},
      #{orderProductId,jdbcType=BIGINT}, #{switchFlag,jdbcType=SMALLINT}, #{daysAfterCheck,jdbcType=SMALLINT},
      #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{msunHealthModuleCode,jdbcType=VARCHAR},
      #{acceptTime,jdbcType=TIMESTAMP}, #{hasDeal,jdbcType=SMALLINT})
    on duplicate key update
    custom_form_limit_id = #{customFormLimitId,jdbcType=BIGINT},
    custom_info_id = #{customInfoId,jdbcType=BIGINT},
    hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
    order_product_id = #{orderProductId,jdbcType=BIGINT},
    switch_flag = #{switchFlag,jdbcType=SMALLINT},
    days_after_check = #{daysAfterCheck,jdbcType=SMALLINT},
    is_deleted = #{isDeleted,jdbcType=SMALLINT},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
    accept_time = #{acceptTime,jdbcType=TIMESTAMP},
    has_deal = #{hasDeal,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    <!--@mbg.generated-->
    insert into csm.config_custom_form_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customFormLimitId != null">
        custom_form_limit_id,
      </if>
      <if test="customInfoId != null">
        custom_info_id,
      </if>
      <if test="hospitalInfoId != null">
        hospital_info_id,
      </if>
      <if test="orderProductId != null">
        order_product_id,
      </if>
      <if test="switchFlag != null">
        switch_flag,
      </if>
      <if test="daysAfterCheck != null">
        days_after_check,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="msunHealthModuleCode != null">
        msun_health_module_code,
      </if>
      <if test="acceptTime != null">
        accept_time,
      </if>
      <if test="hasDeal != null">
        has_deal,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customFormLimitId != null">
        #{customFormLimitId,jdbcType=BIGINT},
      </if>
      <if test="customInfoId != null">
        #{customInfoId,jdbcType=BIGINT},
      </if>
      <if test="hospitalInfoId != null">
        #{hospitalInfoId,jdbcType=BIGINT},
      </if>
      <if test="orderProductId != null">
        #{orderProductId,jdbcType=BIGINT},
      </if>
      <if test="switchFlag != null">
        #{switchFlag,jdbcType=SMALLINT},
      </if>
      <if test="daysAfterCheck != null">
        #{daysAfterCheck,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msunHealthModuleCode != null">
        #{msunHealthModuleCode,jdbcType=VARCHAR},
      </if>
      <if test="acceptTime != null">
        #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasDeal != null">
        #{hasDeal,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="customFormLimitId != null">
        custom_form_limit_id = #{customFormLimitId,jdbcType=BIGINT},
      </if>
      <if test="customInfoId != null">
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
      </if>
      <if test="hospitalInfoId != null">
        hospital_info_id = #{hospitalInfoId,jdbcType=BIGINT},
      </if>
      <if test="orderProductId != null">
        order_product_id = #{orderProductId,jdbcType=BIGINT},
      </if>
      <if test="switchFlag != null">
        switch_flag = #{switchFlag,jdbcType=SMALLINT},
      </if>
      <if test="daysAfterCheck != null">
        days_after_check = #{daysAfterCheck,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="msunHealthModuleCode != null">
        msun_health_module_code = #{msunHealthModuleCode,jdbcType=VARCHAR},
      </if>
      <if test="acceptTime != null">
        accept_time = #{acceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hasDeal != null">
        has_deal = #{hasDeal,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

  <select id="selectByCondition" resultType="com.msun.csm.model.resp.projtool.ProjToolCustomLimitDataResp">
    select
    pci.custom_info_id "customInfoId",
    pci.custom_name as "customName",
    phi.hospital_info_id as "hospitalInfoId",
    phi.hospital_name as "hospitalName",
    coalesce(ff.accept_time, ee.accept_time) as "acceptTime",
    coalesce(ccl.switch_flag,1 ) "customSwitchFlag",
    ccl.create_time as "customStartTime",
    coalesce(ff.hulidanyuan ,1) "hulidanyuanFlag",
    coalesce(ff.aims ,1) "aimsFormFlag",
    coalesce(ff.icuisnew ,1) "icuFormFlag",
    coalesce(ff.emis ,1) "emisFormFlag",
    coalesce(ff.reportweb ,1) "reportwebFlag",
    coalesce(ff.baseprint ,1) "baseprintFlag",
    coalesce(ff.reportlimit ,1) "reportlimitFlag",
    coalesce(ff.interfacelimit,1) "interfacelimitFlag",
    coalesce(ff.has_deal,0) "hasDeal",
    case when ff.has_deal = 1 then ff.update_time end "executeTime"

    from csm.proj_custom_info pci
    inner join csm.proj_hospital_info phi on pci.custom_info_id = phi.custom_info_id
    left join (
    select ppi.custom_info_id,ppi.project_type,ppi.accept_time,phvpt.hospital_info_id,count(*) from
    csm.proj_project_info ppi
    inner join csm.proj_hospital_vs_project_type phvpt on ppi.custom_info_id = phvpt.custom_info_id and
    phvpt.project_type = ppi.project_type
    where ppi.is_deleted = 0
    and phvpt.is_deleted = 0
    and ppi.his_flag = 1
    group by ppi.custom_info_id,ppi.project_type,ppi.accept_time,phvpt.hospital_info_id
    ) ee on phi.custom_info_id = ee.custom_info_id and ee.hospital_info_id = phi.hospital_info_id

    left join csm.config_custom_limit ccl on pci.custom_info_id = ccl.custom_info_id and ccl.is_deleted = 0
    left join (
    select
    custom_info_id,
    hospital_info_id,
    min(case when msun_health_module_code = 'hulidanyuan' then switch_flag else 1 end ) hulidanyuan,
    min(case when msun_health_module_code = 'aims' then switch_flag else 1 end ) aims,
    min(case when msun_health_module_code = 'icuisnew' then switch_flag else 1 end ) icuisnew,
    min(case when msun_health_module_code = 'emis' then switch_flag else 1 end ) emis,
    min(case when msun_health_module_code = 'reportweb' then switch_flag else 1 end ) reportweb,
    min(case when msun_health_module_code = 'baseprint' then switch_flag else 1 end ) baseprint,
    min(case when msun_health_module_code = 'reportlimit' then switch_flag else 1 end ) reportlimit,
    min(case when msun_health_module_code = 'interfacelimit' then switch_flag else 1 end ) interfacelimit,
    min(accept_time) accept_time,
    min(has_deal) has_deal,
    min(update_time) update_time

    from csm.config_custom_form_limit
    where is_deleted =0
    group by custom_info_id,hospital_info_id
    ) ff on ff.custom_info_id = phi.custom_info_id and ff.hospital_info_id = phi.hospital_info_id

    where pci.is_deleted = 0
    and phi.is_deleted = 0
    <if test="customerName != null and customerName != '' ">
      and pci.custom_name like concat('%',#{customerName},'%')
    </if>
    <if test="switchFlag != null and switchFlag != 2 ">
      and coalesce(ccl.switch_flag,1 ) = #{switchFlag}
    </if>
    <if test="configValue != null and configValue != 2 ">
      and coalesce(ff.has_deal, 0) = #{configValue}
    </if>

    order by pci.custom_name,ee.project_type, phi.hospital_name


  </select>

  <select id="checkLimitAvailable" resultType="java.lang.Boolean">
    select
    case when
    (
    select
    case when CURRENT_DATE >= (min(ccl.create_time) + INTERVAL '1 days' * min(ccfl.days_after_check))  then 1 else 0 end  as value
    from csm.config_custom_limit ccl
    inner join
    (
    select phi.hospital_info_id,phi.custom_info_id from
    csm.proj_hospital_info phi
    inner join csm.proj_hospital_vs_project_type phvpt on phi.hospital_info_id=phvpt.hospital_info_id  and phvpt.is_deleted = 0
    inner join csm.proj_project_info ppi on phvpt.custom_info_id = ppi.custom_info_id and phvpt.project_type = ppi.project_type  and ppi.is_deleted = 0
    where
    phi.is_deleted = 0
    <if test="hospitalInfoId != null">
      and phvpt.hospital_info_id = #{hospitalInfoId}
    </if>
    )
    ppi on ccl.custom_info_id = ppi.custom_info_id
    inner join csm.config_custom_form_limit ccfl on ccl.custom_info_id = ccfl.custom_info_id  and ppi.hospital_info_id = ccfl.hospital_info_id
    where ccl.is_deleted  = 0
    and ccl.switch_flag =0
    and ccfl.is_deleted = 0
    <if test="hospitalInfoId != null">
      and ccfl.hospital_info_id = #{hospitalInfoId}
    </if>
    and ccfl.msun_health_module_code  = #{code}
    and ccfl.switch_flag = 0
    group by ccfl.custom_info_id

    )  > 0 then true else false end
  </select>

  <select id="isBackendCustomerByProjectInfoId" resultType="java.lang.Boolean">
      select case when count(1) > 0 then true else false end
      from platform.customer as a
               inner join csm.proj_custom_info as b on a.customer_yunying_id = b.yy_customer_id and b.is_deleted = 0
               inner join csm.proj_project_info as c on b.custom_info_id = c.custom_info_id and c.is_deleted = 0
      where a.use_new_flow = 2
        and c.project_info_id = #{projectInfoId}
  </select>



  <select id="isBackendCustomerByByCustomInfoId" resultType="java.lang.Boolean">
    select case when count(1) > 0 then true else false end
    from platform.customer as a
           inner join csm.proj_custom_info as b on a.customer_yunying_id = b.yy_customer_id and b.is_deleted = 0
    where a.use_new_flow = 2
      and b.custom_info_id = #{customInfoId}
  </select>


  <!-- 定时任务，查询未处理的数据 客户是已开启的数据 -->
  <select id="selectNoDealList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.config_custom_form_limit
        where is_deleted = 0
          and has_deal = 0
        and msun_health_module_code not in (
            'reportlimit','interfacelimit'
        )
        and accept_time + (INTERVAL '1 days') * days_after_check &lt; current_timestamp
    </select>

  <select id="selectNoDealListByInfoId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from csm.config_custom_form_limit
    where is_deleted = 0
    and has_deal = 0
    and hospital_info_id = #{hospitalInfoId}
    and custom_info_id = #{customInfoId}
    and msun_health_module_code not in (
    'reportlimit','interfacelimit'
    )
    and accept_time + (INTERVAL '1 days') * days_after_check &lt; current_timestamp
  </select>
  <select id="selectListByParamer" resultType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    select
    <include refid="Base_Column_List"/>
    from csm.config_custom_form_limit
    where is_deleted = 0
    and hospital_info_id in
    <foreach collection="hospitalInfoList" item="item" open="(" close=")" separator=",">
        #{item.hospitalInfoId}
    </foreach>
  </select>
  <select id="selectDataByHospitalInfoIdAndCode"
          resultType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
    select
        <include refid="Base_Column_List"/>
    from csm.config_custom_form_limit
    where is_deleted = 0
    and hospital_info_id = #{hospitalInfoId}
    and msun_health_module_code = #{code}


  </select>
    <select id="selectByCustomerId" resultType="com.msun.csm.dao.entity.config.ConfigCustomFormLimit">
      select
      <include refid="Base_Column_List"/>
      from csm.config_custom_form_limit
      where is_deleted = 0
       and custom_info_id = #{customInfoId}
    </select>

    <update id="updateDealFlag">
      update csm.config_custom_form_limit
      set has_deal = #{dealStatus}
      where custom_form_limit_id in
      <foreach collection="idList" item="item" open="(" close=")" separator=",">
          #{item}
      </foreach>
  </update>
</mapper>
