<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigSendMessageMapper">
    <select id="selectById" resultType="com.msun.csm.dao.entity.sys.ConfigSendMessage">
        select * from csm.config_send_message where id = #{id, jdbcType=BIGINT}
    </select>

    <select id="getByMessageTypeIdAndMessageToCategory" resultType="com.msun.csm.dao.entity.sys.ConfigSendMessage">
        select *
        from csm.config_send_message
        where message_type_id = #{messageTypeId, jdbcType=BIGINT}
          and message_to_category = #{messageToCategory, jdbcType=INTEGER}
          and is_deleted = 0
    </select>
</mapper>
