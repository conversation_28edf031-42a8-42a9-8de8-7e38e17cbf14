<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigNetTemplateContentMapper">
  <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    <!--@Table csm.config_net_template_content-->
    <id column="net_template_content_id" jdbcType="BIGINT" property="netTemplateContentId" />
    <result column="net_template_content_code" jdbcType="VARCHAR" property="netTemplateContentCode" />
    <result column="net_template_content" jdbcType="VARCHAR" property="netTemplateContent" />
    <result column="net_template_title_code" jdbcType="VARCHAR" property="netTemplateTitleCode" />
    <result column="net_survey_detail_code" jdbcType="VARCHAR" property="netSurveyDetailCode" />
    <result column="yy_product_id" jdbcType="INTEGER" property="yyProductId" />
    <result column="excutor_type" jdbcType="SMALLINT" property="excutorType" />
    <result column="implant_flag" jdbcType="SMALLINT" property="implantFlag" />
    <result column="implant_code" jdbcType="VARCHAR" property="implantCode" />
    <result column="order_no" jdbcType="INTEGER" property="orderNo" />
    <result column="creater_id" jdbcType="BIGINT" property="createrId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    net_template_content_id, net_template_content_code, net_template_content, net_template_title_code,
    net_survey_detail_code, yy_product_id, excutor_type, implant_flag, implant_code,
    order_no, creater_id, create_time, updater_id, update_time, is_deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from csm.config_net_template_content
    where net_template_content_id = #{netTemplateContentId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from csm.config_net_template_content
    where net_template_content_id = #{netTemplateContentId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    insert into csm.config_net_template_content (net_template_content_id, net_template_content_code,
      net_template_content, net_template_title_code,
      net_survey_detail_code, yy_product_id, excutor_type,
      implant_flag, implant_code, order_no,
      creater_id, create_time, updater_id,
      update_time, is_deleted)
    values (#{netTemplateContentId,jdbcType=BIGINT}, #{netTemplateContentCode,jdbcType=VARCHAR},
      #{netTemplateContent,jdbcType=VARCHAR}, #{netTemplateTitleCode,jdbcType=VARCHAR},
      #{netSurveyDetailCode,jdbcType=VARCHAR}, #{yyProductId,jdbcType=INTEGER}, #{excutorType,jdbcType=SMALLINT},
      #{implantFlag,jdbcType=SMALLINT}, #{implantCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    insert into csm.config_net_template_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="netTemplateContentId != null">
        net_template_content_id,
      </if>
      <if test="netTemplateContentCode != null">
        net_template_content_code,
      </if>
      <if test="netTemplateContent != null">
        net_template_content,
      </if>
      <if test="netTemplateTitleCode != null">
        net_template_title_code,
      </if>
      <if test="netSurveyDetailCode != null">
        net_survey_detail_code,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="excutorType != null">
        excutor_type,
      </if>
      <if test="implantFlag != null">
        implant_flag,
      </if>
      <if test="implantCode != null">
        implant_code,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="netTemplateContentId != null">
        #{netTemplateContentId,jdbcType=BIGINT},
      </if>
      <if test="netTemplateContentCode != null">
        #{netTemplateContentCode,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateContent != null">
        #{netTemplateContent,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateTitleCode != null">
        #{netTemplateTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyDetailCode != null">
        #{netSurveyDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="excutorType != null">
        #{excutorType,jdbcType=SMALLINT},
      </if>
      <if test="implantFlag != null">
        #{implantFlag,jdbcType=SMALLINT},
      </if>
      <if test="implantCode != null">
        #{implantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    update csm.config_net_template_content
    <set>
      <if test="netTemplateContentCode != null">
        net_template_content_code = #{netTemplateContentCode,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateContent != null">
        net_template_content = #{netTemplateContent,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateTitleCode != null">
        net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyDetailCode != null">
        net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="excutorType != null">
        excutor_type = #{excutorType,jdbcType=SMALLINT},
      </if>
      <if test="implantFlag != null">
        implant_flag = #{implantFlag,jdbcType=SMALLINT},
      </if>
      <if test="implantCode != null">
        implant_code = #{implantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </set>
    where net_template_content_id = #{netTemplateContentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    update csm.config_net_template_content
    set net_template_content_code = #{netTemplateContentCode,jdbcType=VARCHAR},
      net_template_content = #{netTemplateContent,jdbcType=VARCHAR},
      net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
      net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
      yy_product_id = #{yyProductId,jdbcType=INTEGER},
      excutor_type = #{excutorType,jdbcType=SMALLINT},
      implant_flag = #{implantFlag,jdbcType=SMALLINT},
      implant_code = #{implantCode,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=INTEGER},
      creater_id = #{createrId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updater_id = #{updaterId,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=SMALLINT}
    where net_template_content_id = #{netTemplateContentId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.config_net_template_content
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="net_template_content_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netTemplateContentCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="net_template_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netTemplateContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="net_template_title_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netTemplateTitleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="net_survey_detail_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netSurveyDetailCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="excutor_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.excutorType,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="implant_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.implantFlag,jdbcType=SMALLINT}
        </foreach>
      </trim>
      <trim prefix="implant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.implantCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
        </foreach>
      </trim>
    </trim>
    where net_template_content_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.netTemplateContentId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update csm.config_net_template_content
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="net_template_content_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netTemplateContentCode != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netTemplateContentCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="net_template_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netTemplateContent != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netTemplateContent,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="net_template_title_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netTemplateTitleCode != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netTemplateTitleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="net_survey_detail_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.netSurveyDetailCode != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.netSurveyDetailCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="yy_product_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.yyProductId != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.yyProductId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="excutor_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.excutorType != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.excutorType,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="implant_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.implantFlag != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.implantFlag,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="implant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.implantCode != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.implantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.orderNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="creater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createrId != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updater_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterId != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when net_template_content_id = #{item.netTemplateContentId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where net_template_content_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.netTemplateContentId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into csm.config_net_template_content
    (net_template_content_id, net_template_content_code, net_template_content, net_template_title_code,
      net_survey_detail_code, yy_product_id, excutor_type, implant_flag, implant_code,
      order_no, creater_id, create_time, updater_id, update_time, is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.netTemplateContentId,jdbcType=BIGINT}, #{item.netTemplateContentCode,jdbcType=VARCHAR},
        #{item.netTemplateContent,jdbcType=VARCHAR}, #{item.netTemplateTitleCode,jdbcType=VARCHAR},
        #{item.netSurveyDetailCode,jdbcType=VARCHAR}, #{item.yyProductId,jdbcType=INTEGER},
        #{item.excutorType,jdbcType=SMALLINT}, #{item.implantFlag,jdbcType=SMALLINT}, #{item.implantCode,jdbcType=VARCHAR},
        #{item.orderNo,jdbcType=INTEGER}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    insert into csm.config_net_template_content
    (net_template_content_id, net_template_content_code, net_template_content, net_template_title_code,
      net_survey_detail_code, yy_product_id, excutor_type, implant_flag, implant_code,
      order_no, creater_id, create_time, updater_id, update_time, is_deleted)
    values
    (#{netTemplateContentId,jdbcType=BIGINT}, #{netTemplateContentCode,jdbcType=VARCHAR},
      #{netTemplateContent,jdbcType=VARCHAR}, #{netTemplateTitleCode,jdbcType=VARCHAR},
      #{netSurveyDetailCode,jdbcType=VARCHAR}, #{yyProductId,jdbcType=INTEGER}, #{excutorType,jdbcType=SMALLINT},
      #{implantFlag,jdbcType=SMALLINT}, #{implantCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER},
      #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    on duplicate key update
    net_template_content_id = #{netTemplateContentId,jdbcType=BIGINT},
    net_template_content_code = #{netTemplateContentCode,jdbcType=VARCHAR},
    net_template_content = #{netTemplateContent,jdbcType=VARCHAR},
    net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
    net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
    yy_product_id = #{yyProductId,jdbcType=INTEGER},
    excutor_type = #{excutorType,jdbcType=SMALLINT},
    implant_flag = #{implantFlag,jdbcType=SMALLINT},
    implant_code = #{implantCode,jdbcType=VARCHAR},
    order_no = #{orderNo,jdbcType=INTEGER},
    creater_id = #{createrId,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updater_id = #{updaterId,jdbcType=BIGINT},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    is_deleted = #{isDeleted,jdbcType=SMALLINT}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateContent">
    <!--@mbg.generated-->
    insert into csm.config_net_template_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="netTemplateContentId != null">
        net_template_content_id,
      </if>
      <if test="netTemplateContentCode != null">
        net_template_content_code,
      </if>
      <if test="netTemplateContent != null">
        net_template_content,
      </if>
      <if test="netTemplateTitleCode != null">
        net_template_title_code,
      </if>
      <if test="netSurveyDetailCode != null">
        net_survey_detail_code,
      </if>
      <if test="yyProductId != null">
        yy_product_id,
      </if>
      <if test="excutorType != null">
        excutor_type,
      </if>
      <if test="implantFlag != null">
        implant_flag,
      </if>
      <if test="implantCode != null">
        implant_code,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createrId != null">
        creater_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="netTemplateContentId != null">
        #{netTemplateContentId,jdbcType=BIGINT},
      </if>
      <if test="netTemplateContentCode != null">
        #{netTemplateContentCode,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateContent != null">
        #{netTemplateContent,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateTitleCode != null">
        #{netTemplateTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyDetailCode != null">
        #{netSurveyDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="excutorType != null">
        #{excutorType,jdbcType=SMALLINT},
      </if>
      <if test="implantFlag != null">
        #{implantFlag,jdbcType=SMALLINT},
      </if>
      <if test="implantCode != null">
        #{implantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="netTemplateContentId != null">
        net_template_content_id = #{netTemplateContentId,jdbcType=BIGINT},
      </if>
      <if test="netTemplateContentCode != null">
        net_template_content_code = #{netTemplateContentCode,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateContent != null">
        net_template_content = #{netTemplateContent,jdbcType=VARCHAR},
      </if>
      <if test="netTemplateTitleCode != null">
        net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
      </if>
      <if test="netSurveyDetailCode != null">
        net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
      </if>
      <if test="yyProductId != null">
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
      </if>
      <if test="excutorType != null">
        excutor_type = #{excutorType,jdbcType=SMALLINT},
      </if>
      <if test="implantFlag != null">
        implant_flag = #{implantFlag,jdbcType=SMALLINT},
      </if>
      <if test="implantCode != null">
        implant_code = #{implantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=INTEGER},
      </if>
      <if test="createrId != null">
        creater_id = #{createrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updaterId != null">
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>

  <select id="selectByTitleCodeAndSurveyResult" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from csm.config_net_template_content
    where net_template_title_code = #{titleCode,jdbcType=VARCHAR}
      and (net_survey_detail_code in
    <foreach item="item" index="index" collection="detailCodeList" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    or net_survey_detail_code = '-1')
      and is_deleted = 0
      and yy_product_id = -1
    order by order_no
  </select>
</mapper>
