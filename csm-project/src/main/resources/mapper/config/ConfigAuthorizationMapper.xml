<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.common.dao.mapper.config.ConfigAuthorizationMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        <!--@Table csm.config_authorization-->
        <id column="config_authorization_id" jdbcType="BIGINT" property="configAuthorizationId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="public_key" jdbcType="VARCHAR" property="publicKey"/>
        <result column="private_key" jdbcType="VARCHAR" property="privateKey"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="methods" jdbcType="VARCHAR" property="methods"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        config_authorization_id,
        app_id,
        system_code,
        public_key,
        private_key,
        is_deleted,
        creater_id,
        create_time,
        updater_id,
        update_time,
        methods
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.config_authorization
        where config_authorization_id = #{configAuthorizationId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.config_authorization
        where config_authorization_id = #{configAuthorizationId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        insert into csm.config_authorization (config_authorization_id, app_id, system_code,
                                              public_key, private_key, is_deleted,
                                              creater_id, create_time, updater_id,
                                              update_time, methods)
        values (#{configAuthorizationId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{systemCode,jdbcType=VARCHAR},
                #{publicKey,jdbcType=VARCHAR}, #{privateKey,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
                #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
                #{updateTime,jdbcType=TIMESTAMP}, #{methods,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        insert into csm.config_authorization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configAuthorizationId != null">
                config_authorization_id,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="systemCode != null">
                system_code,
            </if>
            <if test="publicKey != null">
                public_key,
            </if>
            <if test="privateKey != null">
                private_key,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="methods != null">
                methods,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configAuthorizationId != null">
                #{configAuthorizationId,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="systemCode != null">
                #{systemCode,jdbcType=VARCHAR},
            </if>
            <if test="publicKey != null">
                #{publicKey,jdbcType=VARCHAR},
            </if>
            <if test="privateKey != null">
                #{privateKey,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="methods != null">
                #{methods,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        update csm.config_authorization
        <set>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="systemCode != null">
                system_code = #{systemCode,jdbcType=VARCHAR},
            </if>
            <if test="publicKey != null">
                public_key = #{publicKey,jdbcType=VARCHAR},
            </if>
            <if test="privateKey != null">
                private_key = #{privateKey,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="methods != null">
                methods = #{methods,jdbcType=VARCHAR},
            </if>
        </set>
        where config_authorization_id = #{configAuthorizationId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        update csm.config_authorization
        set app_id      = #{appId,jdbcType=VARCHAR},
            system_code = #{systemCode,jdbcType=VARCHAR},
            public_key  = #{publicKey,jdbcType=VARCHAR},
            private_key = #{privateKey,jdbcType=VARCHAR},
            is_deleted  = #{isDeleted,jdbcType=SMALLINT},
            creater_id  = #{createrId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater_id  = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            methods     = #{methods,jdbcType=VARCHAR}
        where config_authorization_id = #{configAuthorizationId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_authorization
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.appId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="system_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.systemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="public_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.publicKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="private_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.privateKey,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="methods = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                        then #{item.methods,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where config_authorization_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configAuthorizationId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_authorization
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appId != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.appId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="system_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.systemCode != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.systemCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="public_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.publicKey != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.publicKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="private_key = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.privateKey != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.privateKey,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="methods = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.methods != null">
                        when config_authorization_id = #{item.configAuthorizationId,jdbcType=BIGINT}
                            then #{item.methods,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where config_authorization_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configAuthorizationId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.config_authorization
        (config_authorization_id, app_id, system_code, public_key, private_key, is_deleted,
         creater_id, create_time, updater_id, update_time, methods)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.configAuthorizationId,jdbcType=BIGINT}, #{item.appId,jdbcType=VARCHAR},
             #{item.systemCode,jdbcType=VARCHAR},
             #{item.publicKey,jdbcType=VARCHAR}, #{item.privateKey,jdbcType=VARCHAR},
             #{item.isDeleted,jdbcType=SMALLINT},
             #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updaterId,jdbcType=BIGINT},
             #{item.updateTime,jdbcType=TIMESTAMP}, #{item.methods,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        insert into csm.config_authorization
        (config_authorization_id, app_id, system_code, public_key, private_key, is_deleted,
         creater_id, create_time, updater_id, update_time, methods)
        values (#{configAuthorizationId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{systemCode,jdbcType=VARCHAR},
                #{publicKey,jdbcType=VARCHAR}, #{privateKey,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT},
                #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
                #{updateTime,jdbcType=TIMESTAMP}, #{methods,jdbcType=VARCHAR})
        on duplicate key update
            config_authorization_id = #{configAuthorizationId,jdbcType=BIGINT}, app_id = #{appId,jdbcType=VARCHAR}, system_code = #{systemCode,jdbcType=VARCHAR}, public_key = #{publicKey,jdbcType=VARCHAR}, private_key = #{privateKey,jdbcType=VARCHAR}, is_deleted = #{isDeleted,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, methods = #{methods,jdbcType=VARCHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.common.dao.entity.config.ConfigAuthorization">
        <!--@mbg.generated-->
        insert into csm.config_authorization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configAuthorizationId != null">
                config_authorization_id,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="systemCode != null">
                system_code,
            </if>
            <if test="publicKey != null">
                public_key,
            </if>
            <if test="privateKey != null">
                private_key,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="methods != null">
                methods,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configAuthorizationId != null">
                #{configAuthorizationId,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="systemCode != null">
                #{systemCode,jdbcType=VARCHAR},
            </if>
            <if test="publicKey != null">
                #{publicKey,jdbcType=VARCHAR},
            </if>
            <if test="privateKey != null">
                #{privateKey,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="methods != null">
                #{methods,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="configAuthorizationId != null">
                config_authorization_id = #{configAuthorizationId,jdbcType=BIGINT},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="systemCode != null">
                system_code = #{systemCode,jdbcType=VARCHAR},
            </if>
            <if test="publicKey != null">
                public_key = #{publicKey,jdbcType=VARCHAR},
            </if>
            <if test="privateKey != null">
                private_key = #{privateKey,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="methods != null">
                methods = #{methods,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectByAppId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.config_authorization
        where app_id = #{appId}
          and is_deleted = 0
    </select>
</mapper>
