<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigNetSurveyDetailMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        <!--@Table csm.config_net_survey_detail-->
        <id column="net_survey_detail_id" jdbcType="BIGINT" property="netSurveyDetailId"/>
        <result column="net_survey_detail_code" jdbcType="VARCHAR" property="netSurveyDetailCode"/>
        <result column="net_survey_detail_name" jdbcType="VARCHAR" property="netSurveyDetailName"/>
        <result column="net_survey_title_code" jdbcType="VARCHAR" property="netSurveyTitleCode"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        net_survey_detail_id, net_survey_detail_code, net_survey_detail_name, net_survey_title_code,
        order_no, creater_id, create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.config_net_survey_detail
        where net_survey_detail_id = #{netSurveyDetailId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.config_net_survey_detail
        where net_survey_detail_id = #{netSurveyDetailId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        insert into csm.config_net_survey_detail (net_survey_detail_id, net_survey_detail_code,
        net_survey_detail_name, net_survey_title_code,
        order_no, creater_id, create_time,
        updater_id, update_time, is_deleted
        )
        values (#{netSurveyDetailId,jdbcType=BIGINT}, #{netSurveyDetailCode,jdbcType=VARCHAR},
        #{netSurveyDetailName,jdbcType=VARCHAR}, #{netSurveyTitleCode,jdbcType=VARCHAR},
        #{orderNo,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        insert into csm.config_net_survey_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netSurveyDetailId != null">
                net_survey_detail_id,
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code,
            </if>
            <if test="netSurveyDetailName != null">
                net_survey_detail_name,
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="netSurveyDetailId != null">
                #{netSurveyDetailId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyDetailCode != null">
                #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailName != null">
                #{netSurveyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleCode != null">
                #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        update csm.config_net_survey_detail
        <set>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailName != null">
                net_survey_detail_name = #{netSurveyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where net_survey_detail_id = #{netSurveyDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        update csm.config_net_survey_detail
        set net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
        net_survey_detail_name = #{netSurveyDetailName,jdbcType=VARCHAR},
        net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where net_survey_detail_id = #{netSurveyDetailId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_net_survey_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="net_survey_detail_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.netSurveyDetailCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="net_survey_detail_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.netSurveyDetailName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="net_survey_title_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.netSurveyTitleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.orderNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where net_survey_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.netSurveyDetailId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_net_survey_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="net_survey_detail_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netSurveyDetailCode != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.netSurveyDetailCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="net_survey_detail_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netSurveyDetailName != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.netSurveyDetailName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="net_survey_title_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netSurveyTitleCode != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.netSurveyTitleCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.orderNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when net_survey_detail_id = #{item.netSurveyDetailId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where net_survey_detail_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.netSurveyDetailId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.config_net_survey_detail
        (net_survey_detail_id, net_survey_detail_code, net_survey_detail_name, net_survey_title_code,
        order_no, creater_id, create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.netSurveyDetailId,jdbcType=BIGINT}, #{item.netSurveyDetailCode,jdbcType=VARCHAR},
            #{item.netSurveyDetailName,jdbcType=VARCHAR}, #{item.netSurveyTitleCode,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=INTEGER}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        insert into csm.config_net_survey_detail
        (net_survey_detail_id, net_survey_detail_code, net_survey_detail_name, net_survey_title_code,
        order_no, creater_id, create_time, updater_id, update_time, is_deleted)
        values
        (#{netSurveyDetailId,jdbcType=BIGINT}, #{netSurveyDetailCode,jdbcType=VARCHAR},
        #{netSurveyDetailName,jdbcType=VARCHAR},
        #{netSurveyTitleCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER}, #{createrId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
        #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        net_survey_detail_id = #{netSurveyDetailId,jdbcType=BIGINT},
        net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
        net_survey_detail_name = #{netSurveyDetailName,jdbcType=VARCHAR},
        net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetSurveyDetail">
        <!--@mbg.generated-->
        insert into csm.config_net_survey_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netSurveyDetailId != null">
                net_survey_detail_id,
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code,
            </if>
            <if test="netSurveyDetailName != null">
                net_survey_detail_name,
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netSurveyDetailId != null">
                #{netSurveyDetailId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyDetailCode != null">
                #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailName != null">
                #{netSurveyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleCode != null">
                #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="netSurveyDetailId != null">
                net_survey_detail_id = #{netSurveyDetailId,jdbcType=BIGINT},
            </if>
            <if test="netSurveyDetailCode != null">
                net_survey_detail_code = #{netSurveyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyDetailName != null">
                net_survey_detail_name = #{netSurveyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="netSurveyTitleCode != null">
                net_survey_title_code = #{netSurveyTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByTitleCode" resultMap="BaseResultMap">
        select *
        from csm.config_net_survey_detail
        where is_deleted = 0
        and net_survey_title_code in
        <foreach collection="titleCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
