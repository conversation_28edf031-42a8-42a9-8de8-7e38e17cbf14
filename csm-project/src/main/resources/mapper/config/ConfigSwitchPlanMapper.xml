<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigSwitchPlanMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        <!--@Table csm.config_switch_plan-->
        <id column="config_switch_plan_id" jdbcType="BIGINT" property="configSwitchPlanId"/>
        <result column="hospital_dept_id" jdbcType="BIGINT" property="hospitalDeptId"/>
        <result column="hospital_dept_name" jdbcType="VARCHAR" property="hospitalDeptName"/>
        <result column="telesales_flag" jdbcType="SMALLINT" property="telesalesFlag"/>
        <result column="monomer_flag" jdbcType="SMALLINT" property="monomerFlag"/>
        <result column="region_flag" jdbcType="SMALLINT" property="regionFlag"/>
        <result column="upgradation_type" jdbcType="SMALLINT" property="upgradationType"/>
        <result column="order_no" jdbcType="SMALLINT" property="orderNo"/>
        <result column="switch_phase_id" jdbcType="BIGINT" property="switchPhaseId"/>
        <result column="switch_phase_name" jdbcType="VARCHAR" property="switchPhaseName"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="switch_type" jdbcType="SMALLINT" property="switchType"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        config_switch_plan_id,
        hospital_dept_id,
        hospital_dept_name,
        telesales_flag,
        monomer_flag,
        region_flag,
        upgradation_type,
        order_no,
        switch_phase_id,
        switch_phase_name,
        content,
        switch_type,
        creater_id,
        create_time,
        updater_id,
        update_time,
        is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.config_switch_plan
        where config_switch_plan_id = #{configSwitchPlanId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from csm.config_switch_plan
        where config_switch_plan_id = #{configSwitchPlanId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.config_switch_plan (config_switch_plan_id, hospital_dept_id,
                                            hospital_dept_name, telesales_flag, monomer_flag,
                                            region_flag, upgradation_type, order_no,
                                            switch_phase_id, switch_phase_name, content,
                                            switch_type, creater_id, create_time,
                                            updater_id, update_time, is_deleted)
        values (#{configSwitchPlanId,jdbcType=BIGINT}, #{hospitalDeptId,jdbcType=BIGINT},
                #{hospitalDeptName,jdbcType=VARCHAR}, #{telesalesFlag,jdbcType=SMALLINT},
                #{monomerFlag,jdbcType=SMALLINT},
                #{regionFlag,jdbcType=SMALLINT}, #{upgradationType,jdbcType=SMALLINT}, #{orderNo,jdbcType=SMALLINT},
                #{switchPhaseId,jdbcType=BIGINT}, #{switchPhaseName,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR},
                #{switchType,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.config_switch_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configSwitchPlanId != null">
                config_switch_plan_id,
            </if>
            <if test="hospitalDeptId != null">
                hospital_dept_id,
            </if>
            <if test="hospitalDeptName != null">
                hospital_dept_name,
            </if>
            <if test="telesalesFlag != null">
                telesales_flag,
            </if>
            <if test="monomerFlag != null">
                monomer_flag,
            </if>
            <if test="regionFlag != null">
                region_flag,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="switchPhaseId != null">
                switch_phase_id,
            </if>
            <if test="switchPhaseName != null">
                switch_phase_name,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="switchType != null">
                switch_type,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configSwitchPlanId != null">
                #{configSwitchPlanId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptId != null">
                #{hospitalDeptId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptName != null">
                #{hospitalDeptName,jdbcType=VARCHAR},
            </if>
            <if test="telesalesFlag != null">
                #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=SMALLINT},
            </if>
            <if test="switchPhaseId != null">
                #{switchPhaseId,jdbcType=BIGINT},
            </if>
            <if test="switchPhaseName != null">
                #{switchPhaseName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="switchType != null">
                #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        update csm.config_switch_plan
        <set>
            <if test="hospitalDeptId != null">
                hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptName != null">
                hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
            </if>
            <if test="telesalesFlag != null">
                telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                region_flag = #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=SMALLINT},
            </if>
            <if test="switchPhaseId != null">
                switch_phase_id = #{switchPhaseId,jdbcType=BIGINT},
            </if>
            <if test="switchPhaseName != null">
                switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="switchType != null">
                switch_type = #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where config_switch_plan_id = #{configSwitchPlanId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        update csm.config_switch_plan
        set hospital_dept_id   = #{hospitalDeptId,jdbcType=BIGINT},
            hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
            telesales_flag     = #{telesalesFlag,jdbcType=SMALLINT},
            monomer_flag       = #{monomerFlag,jdbcType=SMALLINT},
            region_flag        = #{regionFlag,jdbcType=SMALLINT},
            upgradation_type   = #{upgradationType,jdbcType=SMALLINT},
            order_no           = #{orderNo,jdbcType=SMALLINT},
            switch_phase_id    = #{switchPhaseId,jdbcType=BIGINT},
            switch_phase_name  = #{switchPhaseName,jdbcType=VARCHAR},
            content            = #{content,jdbcType=VARCHAR},
            switch_type        = #{switchType,jdbcType=SMALLINT},
            creater_id         = #{createrId,jdbcType=BIGINT},
            create_time        = #{createTime,jdbcType=TIMESTAMP},
            updater_id         = #{updaterId,jdbcType=BIGINT},
            update_time        = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted         = #{isDeleted,jdbcType=SMALLINT}
        where config_switch_plan_id = #{configSwitchPlanId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_switch_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hospital_dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.hospitalDeptId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="hospital_dept_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.hospitalDeptName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="telesales_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.telesalesFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="monomer_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.monomerFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="region_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.regionFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.upgradationType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.orderNo,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="switch_phase_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.switchPhaseId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="switch_phase_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.switchPhaseName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.content,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="switch_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.switchType,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                        then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where config_switch_plan_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configSwitchPlanId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_switch_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="hospital_dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalDeptId != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.hospitalDeptId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="hospital_dept_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hospitalDeptName != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.hospitalDeptName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="telesales_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.telesalesFlag != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.telesalesFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="monomer_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.monomerFlag != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.monomerFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="region_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.regionFlag != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.regionFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upgradation_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.upgradationType != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.upgradationType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.orderNo,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="switch_phase_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.switchPhaseId != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.switchPhaseId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="switch_phase_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.switchPhaseName != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.switchPhaseName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.content != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.content,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="switch_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.switchType != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.switchType,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when config_switch_plan_id = #{item.configSwitchPlanId,jdbcType=BIGINT}
                            then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where config_switch_plan_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.configSwitchPlanId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.config_switch_plan
        (config_switch_plan_id, hospital_dept_id, hospital_dept_name, telesales_flag, monomer_flag,
         region_flag, upgradation_type, order_no, switch_phase_id, switch_phase_name, content,
         switch_type, creater_id, create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.configSwitchPlanId,jdbcType=BIGINT}, #{item.hospitalDeptId,jdbcType=BIGINT},
             #{item.hospitalDeptName,jdbcType=VARCHAR}, #{item.telesalesFlag,jdbcType=SMALLINT},
             #{item.monomerFlag,jdbcType=SMALLINT}, #{item.regionFlag,jdbcType=SMALLINT},
             #{item.upgradationType,jdbcType=SMALLINT},
             #{item.orderNo,jdbcType=SMALLINT}, #{item.switchPhaseId,jdbcType=BIGINT},
             #{item.switchPhaseName,jdbcType=VARCHAR},
             #{item.content,jdbcType=VARCHAR}, #{item.switchType,jdbcType=SMALLINT}, #{item.createrId,jdbcType=BIGINT},
             #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
             #{item.updateTime,jdbcType=TIMESTAMP},
             #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.config_switch_plan
        (config_switch_plan_id, hospital_dept_id, hospital_dept_name, telesales_flag, monomer_flag,
         region_flag, upgradation_type, order_no, switch_phase_id, switch_phase_name, content,
         switch_type, creater_id, create_time, updater_id, update_time, is_deleted)
        values (#{configSwitchPlanId,jdbcType=BIGINT}, #{hospitalDeptId,jdbcType=BIGINT},
                #{hospitalDeptName,jdbcType=VARCHAR},
                #{telesalesFlag,jdbcType=SMALLINT}, #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT},
                #{upgradationType,jdbcType=SMALLINT}, #{orderNo,jdbcType=SMALLINT}, #{switchPhaseId,jdbcType=BIGINT},
                #{switchPhaseName,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{switchType,jdbcType=SMALLINT},
                #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
                #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
            config_switch_plan_id = #{configSwitchPlanId,jdbcType=BIGINT}, hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT}, hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR}, telesales_flag = #{telesalesFlag,jdbcType=SMALLINT}, monomer_flag = #{monomerFlag,jdbcType=SMALLINT}, region_flag = #{regionFlag,jdbcType=SMALLINT}, upgradation_type = #{upgradationType,jdbcType=SMALLINT}, order_no = #{orderNo,jdbcType=SMALLINT}, switch_phase_id = #{switchPhaseId,jdbcType=BIGINT}, switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR}, content = #{content,jdbcType=VARCHAR}, switch_type = #{switchType,jdbcType=SMALLINT}, creater_id = #{createrId,jdbcType=BIGINT}, create_time = #{createTime,jdbcType=TIMESTAMP}, updater_id = #{updaterId,jdbcType=BIGINT}, update_time = #{updateTime,jdbcType=TIMESTAMP}, is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigSwitchPlan">
        <!--@mbg.generated-->
        insert into csm.config_switch_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configSwitchPlanId != null">
                config_switch_plan_id,
            </if>
            <if test="hospitalDeptId != null">
                hospital_dept_id,
            </if>
            <if test="hospitalDeptName != null">
                hospital_dept_name,
            </if>
            <if test="telesalesFlag != null">
                telesales_flag,
            </if>
            <if test="monomerFlag != null">
                monomer_flag,
            </if>
            <if test="regionFlag != null">
                region_flag,
            </if>
            <if test="upgradationType != null">
                upgradation_type,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="switchPhaseId != null">
                switch_phase_id,
            </if>
            <if test="switchPhaseName != null">
                switch_phase_name,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="switchType != null">
                switch_type,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configSwitchPlanId != null">
                #{configSwitchPlanId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptId != null">
                #{hospitalDeptId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptName != null">
                #{hospitalDeptName,jdbcType=VARCHAR},
            </if>
            <if test="telesalesFlag != null">
                #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=SMALLINT},
            </if>
            <if test="switchPhaseId != null">
                #{switchPhaseId,jdbcType=BIGINT},
            </if>
            <if test="switchPhaseName != null">
                #{switchPhaseName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="switchType != null">
                #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="configSwitchPlanId != null">
                config_switch_plan_id = #{configSwitchPlanId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptId != null">
                hospital_dept_id = #{hospitalDeptId,jdbcType=BIGINT},
            </if>
            <if test="hospitalDeptName != null">
                hospital_dept_name = #{hospitalDeptName,jdbcType=VARCHAR},
            </if>
            <if test="telesalesFlag != null">
                telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                region_flag = #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="upgradationType != null">
                upgradation_type = #{upgradationType,jdbcType=SMALLINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=SMALLINT},
            </if>
            <if test="switchPhaseId != null">
                switch_phase_id = #{switchPhaseId,jdbcType=BIGINT},
            </if>
            <if test="switchPhaseName != null">
                switch_phase_name = #{switchPhaseName,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="switchType != null">
                switch_type = #{switchType,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByAttrAndHosDepts" resultMap="BaseResultMap">
        select cwp.*
        from csm.config_switch_plan cwp
                 left join csm.dict_hospital_dept dhd on cwp.hospital_dept_id = dhd.dict_hospital_dept_id
                 left join csm.dict_switch_phase dsp on cwp.switch_phase_id = dsp.dict_switch_phase_id
        where  cwp.is_deleted = 0
          and  cwp.hospital_dept_id in
        <foreach collection="hosDeptIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and  (cwp.switch_type = 1 or cwp.switch_type = -1)
        and  (cwp.upgradation_type = #{req.upgradationType} or cwp.upgradation_type = -1)
        <if test="req.projectType != null and req.projectType == 1">
            and  cwp.monomer_flag = 1
        </if>
        <if test="req.projectType != null and req.projectType == 2">
            and  cwp.region_flag = 1
        </if>
        <if test="req.projectType != null and req.projectType == 4">
            and  cwp.telesales_flag = 1
        </if>
        order by dhd.order_no, dsp.order_no, cwp.order_no
    </select>
</mapper>
