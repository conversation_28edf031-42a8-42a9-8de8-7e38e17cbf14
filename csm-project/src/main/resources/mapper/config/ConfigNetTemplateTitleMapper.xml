<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigNetTemplateTitleMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        <!--@Table csm.config_net_template_title-->
        <id column="net_template_title_id" jdbcType="BIGINT" property="netTemplateTitleId"/>
        <result column="net_template_title_code" jdbcType="VARCHAR" property="netTemplateTitleCode"/>
        <result column="net_template_title_name" jdbcType="VARCHAR" property="netTemplateTitleName"/>
        <result column="upgradation_flag" jdbcType="SMALLINT" property="upgradationFlag"/>
        <result column="his_flag" jdbcType="SMALLINT" property="hisFlag"/>
        <result column="monomer_flag" jdbcType="SMALLINT" property="monomerFlag"/>
        <result column="region_flag" jdbcType="SMALLINT" property="regionFlag"/>
        <result column="telesales_flag" jdbcType="SMALLINT" property="telesalesFlag"/>
        <result column="yy_product_id" jdbcType="INTEGER" property="yyProductId"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        net_template_title_id, net_template_title_code, net_template_title_name, upgradation_flag,
        his_flag, monomer_flag, region_flag, telesales_flag, yy_product_id, order_no, creater_id,
        create_time, updater_id, update_time, is_deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.config_net_template_title
        where net_template_title_id = #{netTemplateTitleId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from csm.config_net_template_title
        where net_template_title_id = #{netTemplateTitleId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        insert into csm.config_net_template_title (net_template_title_id, net_template_title_code,
        net_template_title_name, upgradation_flag,
        his_flag, monomer_flag, region_flag,
        telesales_flag, yy_product_id, order_no,
        creater_id, create_time, updater_id,
        update_time, is_deleted)
        values (#{netTemplateTitleId,jdbcType=BIGINT}, #{netTemplateTitleCode,jdbcType=VARCHAR},
        #{netTemplateTitleName,jdbcType=VARCHAR}, #{upgradationFlag,jdbcType=SMALLINT},
        #{hisFlag,jdbcType=SMALLINT}, #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT},
        #{telesalesFlag,jdbcType=SMALLINT}, #{yyProductId,jdbcType=INTEGER}, #{orderNo,jdbcType=INTEGER},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        insert into csm.config_net_template_title
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netTemplateTitleId != null">
                net_template_title_id,
            </if>
            <if test="netTemplateTitleCode != null">
                net_template_title_code,
            </if>
            <if test="netTemplateTitleName != null">
                net_template_title_name,
            </if>
            <if test="upgradationFlag != null">
                upgradation_flag,
            </if>
            <if test="hisFlag != null">
                his_flag,
            </if>
            <if test="monomerFlag != null">
                monomer_flag,
            </if>
            <if test="regionFlag != null">
                region_flag,
            </if>
            <if test="telesalesFlag != null">
                telesales_flag,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="netTemplateTitleId != null">
                #{netTemplateTitleId,jdbcType=BIGINT},
            </if>
            <if test="netTemplateTitleCode != null">
                #{netTemplateTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netTemplateTitleName != null">
                #{netTemplateTitleName,jdbcType=VARCHAR},
            </if>
            <if test="upgradationFlag != null">
                #{upgradationFlag,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        update csm.config_net_template_title
        <set>
            <if test="netTemplateTitleCode != null">
                net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netTemplateTitleName != null">
                net_template_title_name = #{netTemplateTitleName,jdbcType=VARCHAR},
            </if>
            <if test="upgradationFlag != null">
                upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                his_flag = #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                region_flag = #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where net_template_title_id = #{netTemplateTitleId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        update csm.config_net_template_title
        set net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
        net_template_title_name = #{netTemplateTitleName,jdbcType=VARCHAR},
        upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
        his_flag = #{hisFlag,jdbcType=SMALLINT},
        monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
        region_flag = #{regionFlag,jdbcType=SMALLINT},
        telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where net_template_title_id = #{netTemplateTitleId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_net_template_title
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="net_template_title_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.netTemplateTitleCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="net_template_title_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.netTemplateTitleName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="upgradation_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.upgradationFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="his_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.hisFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="monomer_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.monomerFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="region_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.regionFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="telesales_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.telesalesFlag,jdbcType=SMALLINT}
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.yyProductId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.orderNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                    #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where net_template_title_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.netTemplateTitleId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.config_net_template_title
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="net_template_title_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netTemplateTitleCode != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.netTemplateTitleCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="net_template_title_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.netTemplateTitleName != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.netTemplateTitleName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="upgradation_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.upgradationFlag != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.upgradationFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="his_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.hisFlag != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.hisFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="monomer_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.monomerFlag != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.monomerFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="region_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.regionFlag != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.regionFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="telesales_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.telesalesFlag != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.telesalesFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="yy_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyProductId != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.yyProductId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.orderNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when net_template_title_id = #{item.netTemplateTitleId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where net_template_title_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.netTemplateTitleId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.config_net_template_title
        (net_template_title_id, net_template_title_code, net_template_title_name, upgradation_flag,
        his_flag, monomer_flag, region_flag, telesales_flag, yy_product_id, order_no, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.netTemplateTitleId,jdbcType=BIGINT}, #{item.netTemplateTitleCode,jdbcType=VARCHAR},
            #{item.netTemplateTitleName,jdbcType=VARCHAR}, #{item.upgradationFlag,jdbcType=SMALLINT},
            #{item.hisFlag,jdbcType=SMALLINT}, #{item.monomerFlag,jdbcType=SMALLINT},
            #{item.regionFlag,jdbcType=SMALLINT},
            #{item.telesalesFlag,jdbcType=SMALLINT}, #{item.yyProductId,jdbcType=INTEGER},
            #{item.orderNo,jdbcType=INTEGER}, #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updaterId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        insert into csm.config_net_template_title
        (net_template_title_id, net_template_title_code, net_template_title_name, upgradation_flag,
        his_flag, monomer_flag, region_flag, telesales_flag, yy_product_id, order_no, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        (#{netTemplateTitleId,jdbcType=BIGINT}, #{netTemplateTitleCode,jdbcType=VARCHAR},
        #{netTemplateTitleName,jdbcType=VARCHAR}, #{upgradationFlag,jdbcType=SMALLINT},
        #{hisFlag,jdbcType=SMALLINT}, #{monomerFlag,jdbcType=SMALLINT}, #{regionFlag,jdbcType=SMALLINT},
        #{telesalesFlag,jdbcType=SMALLINT}, #{yyProductId,jdbcType=INTEGER}, #{orderNo,jdbcType=INTEGER},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        net_template_title_id = #{netTemplateTitleId,jdbcType=BIGINT},
        net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
        net_template_title_name = #{netTemplateTitleName,jdbcType=VARCHAR},
        upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
        his_flag = #{hisFlag,jdbcType=SMALLINT},
        monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
        region_flag = #{regionFlag,jdbcType=SMALLINT},
        telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
        yy_product_id = #{yyProductId,jdbcType=INTEGER},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.config.ConfigNetTemplateTitle">
        <!--@mbg.generated-->
        insert into csm.config_net_template_title
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netTemplateTitleId != null">
                net_template_title_id,
            </if>
            <if test="netTemplateTitleCode != null">
                net_template_title_code,
            </if>
            <if test="netTemplateTitleName != null">
                net_template_title_name,
            </if>
            <if test="upgradationFlag != null">
                upgradation_flag,
            </if>
            <if test="hisFlag != null">
                his_flag,
            </if>
            <if test="monomerFlag != null">
                monomer_flag,
            </if>
            <if test="regionFlag != null">
                region_flag,
            </if>
            <if test="telesalesFlag != null">
                telesales_flag,
            </if>
            <if test="yyProductId != null">
                yy_product_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netTemplateTitleId != null">
                #{netTemplateTitleId,jdbcType=BIGINT},
            </if>
            <if test="netTemplateTitleCode != null">
                #{netTemplateTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netTemplateTitleName != null">
                #{netTemplateTitleName,jdbcType=VARCHAR},
            </if>
            <if test="upgradationFlag != null">
                #{upgradationFlag,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="yyProductId != null">
                #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="netTemplateTitleId != null">
                net_template_title_id = #{netTemplateTitleId,jdbcType=BIGINT},
            </if>
            <if test="netTemplateTitleCode != null">
                net_template_title_code = #{netTemplateTitleCode,jdbcType=VARCHAR},
            </if>
            <if test="netTemplateTitleName != null">
                net_template_title_name = #{netTemplateTitleName,jdbcType=VARCHAR},
            </if>
            <if test="upgradationFlag != null">
                upgradation_flag = #{upgradationFlag,jdbcType=SMALLINT},
            </if>
            <if test="hisFlag != null">
                his_flag = #{hisFlag,jdbcType=SMALLINT},
            </if>
            <if test="monomerFlag != null">
                monomer_flag = #{monomerFlag,jdbcType=SMALLINT},
            </if>
            <if test="regionFlag != null">
                region_flag = #{regionFlag,jdbcType=SMALLINT},
            </if>
            <if test="telesalesFlag != null">
                telesales_flag = #{telesalesFlag,jdbcType=SMALLINT},
            </if>
            <if test="yyProductId != null">
                yy_product_id = #{yyProductId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectByProject" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.config_net_template_title
        where is_deleted = 0
        and (upgradation_flag = #{projectInfo.upgradationType} or upgradation_flag = -1)
        and (his_flag = #{projectInfo.hisFlag} or his_flag = -1)
        <choose>
            <when test="telesalesFlag == 1">
                and telesales_flag = 1
            </when>
            <otherwise>
                <if test="projectInfo.projectType == 1">
                    and monomer_flag = 1
                </if>
                <if test="projectInfo.projectType == 2">
                    and region_flag = 1
                </if>
            </otherwise>
        </choose>
    </select>
</mapper>
