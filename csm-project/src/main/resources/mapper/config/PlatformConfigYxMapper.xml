<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.PlatformConfigYxMapper">

<select id="selectPlatformConfigYx" resultType="com.msun.csm.dao.entity.config.PlatformConfigYx">
    select id,
           content_id as "contentId",
           value_describe as "valueDescribe",
           item_value as "itemValue",
           customer_id as "customerId",
           department_id as "departmentId",
           create_id as "createId",
           create_time as "createTime",
           update_id as "updateId",
           update_time as "updateTime",
           customer_info_id as "customerInfoId",
           item_code as "itemCode",
           project_id as "projectId",
           item_name as "itemName",
           product_id as "productId",
           config_type as "configType",
           pcode as "pcode",
           config_status as "configStatus",
           complete_status as "completeStatus"
    from platform.config_yx;
</select>

</mapper>
