<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigProjectStageMapper">
    <resultMap type="com.msun.csm.dao.entity.config.ConfigProjectStage" id="ConfigProjectStageMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="upgradationType" column="upgradation_type" jdbcType="INTEGER"/>
        <result property="projectStageId" column="project_stage_id" jdbcType="INTEGER"/>
        <result property="hisFlag" column="his_flag" jdbcType="INTEGER"/>
        <result property="telesalesFlag" column="telesales_flag" jdbcType="INTEGER"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="regionFlag" column="region_flag" jdbcType="INTEGER"/>
        <result property="monomerFlag" column="monomer_flag" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, upgradation_type, project_stage_id, his_flag, telesales_flag, order_no,
        creater_id, create_time, updater_id, update_time, region_flag, monomer_flag
    </sql>
    <select id="selectByPrimaryKey" resultMap="ConfigProjectStageMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from csm.config_project_stage
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getByUpgradationByParam" resultMap="ConfigProjectStageMap">
        select
        <include refid="Base_Column_List"/>
        from csm.config_project_stage
        where 1=1
        <if test="upgradationType != null">
            and (upgradation_type = #{upgradationType,jdbcType=INTEGER} or upgradation_type=-1)
        </if>
        <if test="hisFlag != null">
            and  (his_flag = #{hisFlag,jdbcType=INTEGER} or his_flag=-1)
        </if>
        <if test="telesalesFlag != null">
            and  telesales_flag = #{telesalesFlag,jdbcType=INTEGER}
        </if>
        <if test="monomerFlag != null">
            and monomer_flag = #{monomerFlag,jdbcType=INTEGER}
        </if>
        <if test="regionFlag != null">
            and region_flag = #{regionFlag,jdbcType=TIMESTAMP}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from csm.config_project_stage
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.config.ConfigProjectStage">
        insert into csm.config_project_stage (id, upgradation_type, project_stage_id,
        his_flag, telesales_flag,
        order_no, creater_id,
        create_time, updater_id, update_time,
            region_flag, monomer_flag
        )
        values (#{id,jdbcType=INTEGER}, #{upgradationType,jdbcType=INTEGER}, #{projectStageId,jdbcType=INTEGER},
        #{hisFlag,jdbcType=INTEGER}, #{telesalesFlag,jdbcType=INTEGER},
         #{orderNo,jdbcType=INTEGER}, #{createrId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{regionFlag,jdbcType=INTEGER}, #{monomerFlag,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.config.ConfigProjectStage">
        update csm.config_project_stage
        set upgradation_type = #{upgradationType,jdbcType=INTEGER},
        project_stage_id = #{projectStageId,jdbcType=INTEGER},
        his_flag = #{hisFlag,jdbcType=INTEGER},
        telesales_flag = #{telesalesFlag,jdbcType=INTEGER},
        order_no = #{orderNo,jdbcType=INTEGER},
        creater_id = #{createrId,jdbcType=INTEGER},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
            region_flag = #{regionFlag,jdbcType=INTEGER},
            monomer_flag = #{monomerFlag,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="findConfigProjectStage" resultMap="ConfigProjectStageMap" parameterType="com.msun.csm.dao.entity.config.ConfigProjectStage">
        select
        <include refid="Base_Column_List"/>
        from csm.config_project_stage
        where 1=1
        <if test="id != null">
            and id = #{id,jdbcType=INTEGER}
        </if>
        <if test="upgradationType != null">
            and upgradation_type = #{upgradationType,jdbcType=INTEGER}
        </if>
        <if test="projectStageId != null">
            and project_stage_id = #{projectStageId,jdbcType=INTEGER}
        </if>
        <if test="hisFlag != null">
            and his_flag = #{hisFlag,jdbcType=INTEGER}
        </if>
        <if test="telesalesFlag != null">
            and telesales_flag = #{telesalesFlag,jdbcType=INTEGER}
        </if>
        <if test="orderNo != null">
            and order_no = #{orderNo,jdbcType=INTEGER}
        </if>
        <if test="monomerFlag != null">
            and monomer_flag = #{monomerFlag,jdbcType=INTEGER}
        </if>
        <if test="regionFlag != null">
            and region_flag = #{regionFlag,jdbcType=TIMESTAMP}
        </if>

    </select>
<!--    <update id="updateMcInvalid" parameterType="com.msun.csm.dao.entity.config.ConfigProjectStage">
        update csm.config_project_stage
        set invalid_flag = #{invalidFlag,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>-->
</mapper>
