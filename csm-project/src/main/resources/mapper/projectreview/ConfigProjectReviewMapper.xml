<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewMapper">
    <select id="findDataPage" resultType="com.msun.csm.model.resp.projectreview.ConfigProjectReviewResp">
        select cpr.*,
        dprt.dict_name as review_type_name,
        drm.dict_name as review_method_name,
        su.user_name as creater_name,
        su2.user_name as updater_name,
        case when cpr.custom_type = -1 then '通用'
        when cpr.custom_type = 1 then '单体'
        when cpr.custom_type = 2 then '区域'
        end as custom_type_name,
        case when cpr.telesales_flag = -1 then '通用'
        when cpr.telesales_flag = 1 then '电销'
        when cpr.telesales_flag = 0 then '非电销'
        end as "telesalesFlagName",
        case when cpr.delivery_model = -1 then '通用'
        when cpr.delivery_model = 1 then '前后端模式'
        when cpr.delivery_model = 0 then '非前后端模式'
        end as "deliveryModelName",
        case when cpr.project_type = -1 then '通用'
        when cpr.project_type = 1 then '首期'
        when cpr.project_type = 2 then '非首期'
        end as "projectTypeName"
        from
        csm.config_project_review cpr
        left join csm.dict_project_review_type dprt on cpr.review_type_id = dprt.project_review_type_id
        left join csm.dict_review_method_type drm on cpr.review_method_id = drm.review_method_type_id
        left join csm.sys_user su on cpr.creater_id = su.sys_user_id
        left join csm.sys_user su2 on cpr.updater_id = su2.sys_user_id
        where 1 = 1
        <if test="reviewTypeId != null">
            and cpr.review_type_id = #{reviewTypeId}
        </if>
        <if test="customType != null">
            and cpr.custom_type = #{customType}
        </if>
        <if test="telesalesFlag != null">
            and cpr.telesales_flag = #{telesalesFlag}
        </if>
        <if test="deliveryModel != null">
            and cpr.delivery_model = #{deliveryModel}
        </if>
        <if test="projectType != null">
            and cpr.project_type = #{projectType}
        </if>
        <if test="reviewMethodId != null">
            and cpr.review_method_id = #{reviewMethodId}
        </if>
        <if test="isFineFlag != null">
            and cpr.is_fine_flag = #{isFineFlag}
        </if>
        <if test="fineMoney != null">
            and cpr.fine_money = #{fineMoney}
        </if>
        <if test="isDeleted != null">
            and cpr.is_deleted = #{isDeleted}
        </if>
        order by cpr.review_type_id asc
    </select>

    <!-- 验证重复
       同一审核类型的
       客户类型为通用+传入
       电销属性为通用+传入
       交付模式为通用+传入
       项目类型为通用+传入
        的不允许有重复的
        如果是修改，则排除当前数据id进行验证重复
     -->
    <select id="selectListByParamer" resultType="com.msun.csm.dao.entity.projectreview.ConfigProjectReview">
        select *
        from csm.config_project_review cpr
        where 1=1
        <if test="projectReviewId != null">
            and cpr.project_review_id != #{projectReviewId}
        </if>
        and cpr.review_type_id = #{reviewTypeId}
        and cpr.custom_type in (-1, #{customType})
        and cpr.telesales_flag in(-1, #{telesalesFlag})
        and cpr.delivery_model in(-1, #{deliveryModel})
        and cpr.project_type in(-1,#{projectType})
    </select>
    <select id="selectByIdHand" resultType="com.msun.csm.dao.entity.projectreview.ConfigProjectReview"
            parameterType="java.lang.Long">
        select *
        from csm.config_project_review
        where project_review_id = #{projectReviewId}
    </select>

    <update id="updateByParamer">
        update csm.config_project_review
        set
        <if test="reviewTypeId != null">
            review_type_id = #{reviewTypeId},
        </if>
        <if test="customType != null">
            custom_type = #{customType},
        </if>
        <if test="telesalesFlag != null">
            telesales_flag = #{telesalesFlag},
        </if>
        <if test="deliveryModel != null">
            delivery_model = #{deliveryModel},
        </if>
        <if test="projectType != null">
            project_type = #{projectType},
        </if>
        <if test="reviewMethodId != null">
            review_method_id = #{reviewMethodId},
        </if>
        <if test="reviewTime != null">
            review_time = #{reviewTime},
        </if>
        <if test="warningTime != null">
            warning_time = #{warningTime},
        </if>
        <if test="isFineFlag != null">
            is_fine_flag = #{isFineFlag},
        </if>
        <if test="fineMoney != null">
            fine_money = #{fineMoney},
        </if>
        <if test="isDeleted != null">
            is_deleted = #{isDeleted},
        </if>
        update_time = now(),
        updater_id = #{updaterId}
        where project_review_id = #{projectReviewId}
    </update>
</mapper>
