<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.projectreview.DictProjectReviewTypeMapper">
    <select id="findDataPage" resultType="com.msun.csm.model.resp.projectreview.DictProjectReviewTypeResp">
        select
        dprt.*,
        su.user_name as creater_name,
        su2.user_name as updater_name
        from
        csm.dict_project_review_type dprt
        left join csm.sys_user su on dprt.creater_id = su.sys_user_id
        left join csm.sys_user su2 on dprt.updater_id = su2.sys_user_id
        where dprt.is_deleted = 0
        <if test="searchKey != null and searchKey != ''">
            and (dprt.dict_code ilike concat('%',#{searchKey},'%') or dprt.dict_name ilike concat('%',#{searchKey},'%') or dprt.remark ilike concat('%',#{searchKey},'%') )
        </if>
    </select>

    <select id="selectIfNotUseCount" resultType="java.lang.Integer">
        select sum(cnt) from (
             select count(*) as cnt
             from csm.config_project_review cpr
             where cpr.is_deleted = 0
               and cpr.review_type_id = #{projectReviewTypeId}
             union all
             select count(*) as cnt
             from csm.config_project_review_type_user cprt
             where cprt.is_deleted = 0
               and cprt.project_review_type_id = #{projectReviewTypeId}
         ) as t
    </select>

    <select id="selectByCode" resultType="com.msun.csm.dao.entity.projectreview.DictProjectReviewType">
        select *
        from csm.dict_project_review_type
        where dict_code = #{dictCode}
          and is_deleted = 0
    </select>
</mapper>
