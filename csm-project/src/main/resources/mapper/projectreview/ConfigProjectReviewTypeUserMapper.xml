<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.projectreview.ConfigProjectReviewTypeUserMapper">

    <select id="findDataPage" resultType="com.msun.csm.model.resp.projectreview.ConfigProjectReviewTypeUserResp">
        select cprt.*,
              dprt.dict_name as "projectReviewTypeName",
              drm.dict_name as review_method_name,
              sd.dept_name as review_dept_name,
              su.user_name as creater_name,
              su2.user_name as updater_name
        from
            csm.config_project_review_type_user cprt
        left join csm.dict_project_review_type dprt on cprt.project_review_type_id = dprt.project_review_type_id
        left join csm.dict_review_method_type drm on cprt.review_method_id = drm.review_method_type_id
        left join csm.sys_user su on cprt.creater_id = su.sys_user_id
        left join csm.sys_user su2 on cprt.updater_id = su2.sys_user_id
        left join csm.sys_dept sd on cprt.review_dept_id = sd.sys_dept_id
        where cprt.is_deleted = 0
        <if test="projectReviewTypeId != null">
            and cprt.project_review_type_id = #{projectReviewTypeId}
        </if>
        <if test="reviewMethodId != null ">
            and cprt.review_method_id = #{reviewMethodId}
        </if>
        <if test="reviewDeptId != null">
            and cprt.review_dept_id = #{reviewDeptId}
        </if>
        order by cprt.project_review_type_id
    </select>

    <select id="selectIfNotUseCount" resultType="java.lang.Integer">
        select *
        from csm.config_project_review cpr
        inner join csm.config_project_review_type_user cprt on cpr.project_review_id = cprt.project_review_type_id
        where cpr.is_deleted = 0
          and cprt.is_deleted = 0
          and cprt.project_review_user_id = #{projectReviewTypeId}
    </select>

    <select id="selectListByParamer">
        select count(*)
        from csm.config_project_review_type_user
        where is_deleted = 0
        <if test="projectReviewUserId != null">
            and project_review_user_id != #{projectReviewUserId}
        </if>
        <if test="projectReviewTypeId != null">
            and project_review_type_id = #{projectReviewTypeId}
        </if>
        <if test="reviewMethodId != null ">
            and review_method_id = #{reviewMethodId}
        </if>
        <if test="reviewDeptId != null">
            and review_dept_id = #{reviewDeptId}
        </if>
    </select>

    <select id="getDirUserList" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select
            su.sys_user_id as id,
            sd.dept_name || '---' || su.user_name as "name"
        from
            csm.sys_user su
                inner join csm.sys_dept sd on
                sd.dept_yunying_id = su.dept_id
        where
            su.is_deleted = 0
          and sd.is_deleted = 0
        order by
            sd.dept_name
    </select>

    <select id="findUserModel" resultType="com.msun.csm.model.resp.projectreview.UserModelResp">
        select
            su.sys_user_id "sysUserId",
            sd.dept_name || '--' || su.user_name as "userName"
        from
            csm.config_project_review_type_user cprt
                inner join csm.sys_user su on su.sys_user_id = ANY(string_to_array(cprt.review_user_id, ',')::int8[])
                inner join csm.sys_dept sd on su.dept_id = sd.dept_yunying_id
                inner join csm.dict_project_review_type dprt on
                cprt.project_review_type_id = dprt.project_review_type_id
                inner join (

                select cpr.* from csm.config_project_review cpr
                                      inner join (
                    select
                        ppi.project_info_id,
                        pci.custom_info_id ,
                        ppi.project_type,
                        pci.telesales_flag,
                        case when ppi.his_flag = 1 then 1 else 2 end as his_flag,
                        case
                            when ccbl.open_flag is not null then ccbl.open_flag
                            else 0
                            end as open_flag
                    from
                        csm.proj_project_info ppi
                            inner join csm.proj_custom_info pci on
                            ppi.custom_info_id = pci.custom_info_id
                                and pci.is_deleted = 0
                            left join csm.config_custom_backend_limit ccbl on
                            ppi.project_info_id = ccbl.project_info_id
                                and ccbl.is_deleted = 0
                    where
                        ppi.project_info_id = #{projectInfoId}
                ) ee on cpr.custom_type in (-1, ee.project_type)
                    and cpr.telesales_flag in (-1, ee.telesales_flag)
                    and cpr.delivery_model in (-1, ee.open_flag)
                    and cpr.project_type in (-1, his_flag)
                where cpr.is_deleted = 0

            ) ff on ff.review_type_id = cprt.project_review_type_id
                and ff.review_method_id = cprt.review_method_id
            <if test="reviewMethodCode != null and reviewMethodCode == 'fwtdsh'">
              inner join (
                select distinct
                sd.sys_dept_id
                from
                csm.proj_project_info ppi
                inner join csm.proj_project_member ppm on ppi.project_info_id = ppm.project_info_id  and ppm.project_member_role_id = 3
                inner join csm.config_custom_backend_limit ccbl on  ppi.project_info_id = ccbl.project_info_id
                and ccbl.is_deleted = 0 and ccbl.open_flag = 1
                inner join csm.dict_project_role dpr on ppm.project_member_role_id = dpr.project_role_id
                and ppi.is_deleted = 0
                inner join csm.sys_dept sd on sd.dept_yunying_id = ppm.project_team_id
                and ppi.project_info_id =  #{projectInfoId}
                ) gg on gg.sys_dept_id = cprt.review_dept_id
            </if>
            <if test="reviewMethodCode != null and reviewMethodCode == 'bmjlsh'">
                inner join (
                    select sdbm.sys_dept_id  from
                    csm.proj_project_info ppi
                    inner join csm.proj_custom_info pci on ppi.custom_info_id = pci.custom_info_id  and pci.is_deleted = 0
                    inner join csm.sys_dept sd on sd.dept_yunying_id = pci.custom_team_id
                    inner join csm.sys_dept sdbm on sdbm.dept_yunying_id = sd.pid
                    where ppi.project_info_id =  #{projectInfoId}
                ) hh on hh.sys_dept_id = cprt.review_dept_id
            </if>
        where
            cprt.is_deleted = 0
          and dprt.is_deleted = 0
          and dprt.dict_code =  #{reviewTypeCode}
        group by  su.sys_user_id ,
                    sd.dept_name,
                    su.user_name
    </select>

    <select id="selectListByDto" resultType="com.msun.csm.dao.entity.projectreview.DictReviewMethodType"
            parameterType="com.msun.csm.model.req.projectreview.QueryInfoReq">
        select drmt.* from csm.config_project_review cpr
                               inner join csm.dict_project_review_type dprt on
            cpr.review_type_id = dprt.project_review_type_id
                               inner join csm.dict_review_method_type drmt on drmt.review_method_type_id = cpr.review_method_id
                               inner join (
            select
                ppi.project_info_id,
                pci.custom_info_id ,
                ppi.project_type,
                pci.telesales_flag,
                case when ppi.his_flag = 1 then 1 else 2 end as his_flag,
                case
                    when ccbl.open_flag is not null then ccbl.open_flag
                    else 0
                    end as open_flag
            from
                csm.proj_project_info ppi
                    inner join csm.proj_custom_info pci on
                    ppi.custom_info_id = pci.custom_info_id
                        and pci.is_deleted = 0
                    left join csm.config_custom_backend_limit ccbl on
                    ppi.project_info_id = ccbl.project_info_id
                        and ccbl.is_deleted = 0
            where
                ppi.project_info_id =  #{projectInfoId}
        ) ee on cpr.custom_type in (-1, ee.project_type)
            and cpr.telesales_flag in (-1, ee.telesales_flag)
            and cpr.delivery_model in (-1, ee.open_flag)
            and cpr.project_type in (-1, his_flag)
        where cpr.is_deleted = 0
          and dprt.dict_code =   #{reviewTypeCode}
    </select>
</mapper>
