<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.oldimsp.UserProjectRelationMapper">

    <insert id="insertBatch">
        insert into platform.user_project_relation (user_id, project_id, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.projectId}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>
