<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.oldimsp.OldUserReportMapper">
    <update id="updateByIdData">
        INSERT INTO platform.sys_user_report (user_id, account, level)
        VALUES (#{userId}, #{account}, #{level})
        ON CONFLICT (user_id) DO UPDATE
            SET account = EXCLUDED.account,
                user_id = EXCLUDED.user_id,
                level = EXCLUDED.level;
    </update>
    <update id="updatePlatformUserByIdData">
        update platform.sys_user
        set  user_id = #{newId}
        where user_id = #{userId}
    </update>
    <delete id="deleteByList">
        delete from platform.sys_user_report
        where user_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.userId}
        </foreach>

    </delete>

    <select id="getReportLimitByAccount" resultType="com.msun.csm.dao.entity.oldimsp.OldUserReport">
        select account,
               user_id,
               "level"
        from platform.sys_user_report
        where account = #{oldUserAccount}
           or account = #{newUserAccount}
    </select>
    <select id="getUserAccount" resultType="com.msun.csm.dao.entity.oldimsp.OldUserNewReport">
        select min(su.user_id) user_id, su.account, max("level") "level",
            su.user_yunying_id as "yyId", min(name) "userName"
        from platform.sys_user su
                 left join platform.sys_user_report sur on su.user_id = sur.user_id
        where su.account = #{account}
        group by su.account, su.user_yunying_id

    </select>
    <select id="getUserByAccount" resultType="com.msun.csm.model.resp.projtool.OldSysUserResp">
        select su.user_id, su.account, coalesce(ss.haveuserid, 0) as haveuserid
            from platform.sys_user su
             left join (select distinct user_id as haveuserid
                        from platform.user_project_relation upr
                        where user_id in (select user_id
                                          from platform.sys_user
                                          where account = #{account})
                        ) ss on su.user_id = ss.haveuserid
            where su.account = #{account}
            order by coalesce(ss.haveuserid, 0) desc, su.user_id
            limit 1
    </select>
</mapper>
