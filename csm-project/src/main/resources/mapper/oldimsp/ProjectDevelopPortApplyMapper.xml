<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.oldimsp.ProjectDevelopPortApplyMapper">

    <!--根据id获取人员的acoout和保存的附件地址-->
    <select id="getUserIdAndFileUrl"
            resultType="com.msun.csm.dao.entity.oldimsp.ProjectDevelopPortApply">
        SELECT info.file_data,
               pap.customer_file_id,
               su.name as "userName",
               su.account
        FROM platform.project_develop_port_apply pap
                 left join platform.sys_user su on pap.create_user_id = su.user_id
                 left join platform.sys_file_info info on info.file_id = pap.customer_file_id
        where pap.id = #{id}
    </select>
</mapper>
