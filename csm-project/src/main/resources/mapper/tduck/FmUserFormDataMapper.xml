<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tduck.FmUserFormDataMapper">

    <select id="getFmUserFormDataVO" resultType="com.msun.csm.dao.entity.tduck.FmUserFormDataVO">
        SELECT fufd.id                 as "id",
               fufd.form_key           as "formKey",
               fufd.save_type          as "saveType",
               fufd."source"           as "source",
               fufd.sys_user_id        as "sysUserId",
               fufd.hospital_info_id   as "hospitalInfoId",
               fufd.dept_name          as "deptName",
               fufd.project_info_id    as "projectInfoId",
               fuf.yun_ying_product_id as "yyProductId"
        FROM tduckpro.fm_user_form_data fufd
                 left join tduckpro.fm_user_form fuf on fufd.form_key = fuf.form_key and fuf.is_deleted != 1 and fuf.form_class  = 1
        where fufd.project_info_id = #{projectInfoId}
          and fufd.save_type = #{saveType}
          and fufd."source" = #{source}
          and fuf.form_class = 1
    </select>
</mapper>
