<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tduck.FmUserFormThemeMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.tduck.FmUserFormTheme">
        <!--@mbg.generated-->
        <!--@Table csm.proj_product_deliver_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="form_key" jdbcType="VARCHAR" property="formKey"/>
        <result column="submit_btn_text" jdbcType="VARCHAR" property="submitBtnText"/>
        <result column="logo_img" jdbcType="VARCHAR" property="logoImg"/>
        <result column="logo_position" jdbcType="VARCHAR" property="logoPosition"/>
        <result column="background_color" jdbcType="VARCHAR" property="backgroundColor"/>
        <result column="background_img" jdbcType="VARCHAR" property="backgroundImg"/>
        <result column="show_title" jdbcType="SMALLINT" property="showTitle" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="show_describe" jdbcType="SMALLINT" property="showDescribe" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="theme_color" jdbcType="VARCHAR" property="themeColor"/>
        <result column="show_number" jdbcType="SMALLINT" property="showNumber" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="show_submit_btn" jdbcType="SMALLINT" property="showSubmitBtn" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="head_img_url" jdbcType="VARCHAR" property="headImgUrl"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        <!--@mbg.generated-->
        insert into csm.proj_product_deliver_record
        (
        id,
        form_key,
        submit_btn_text,
        logo_img,
        logo_position,
        background_color,
        background_img,
        show_title,
        show_describe,
        theme_color,
        show_number,
        show_submit_btn,
        head_img_url,
        create_time,
        update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.formKey,jdbcType=VARCHAR},
            #{item.submitBtnText,jdbcType=VARCHAR},
            #{item.logoImg,jdbcType=VARCHAR},
            #{item.logoPosition,jdbcType=VARCHAR},
            #{item.backgroundColor,jdbcType=VARCHAR},
            #{item.backgroundImg,jdbcType=VARCHAR},
            #{item.showTitle,jdbcType=SMALLINT},
            #{item.showDescribe,jdbcType=SMALLINT},
            #{item.themeColor,jdbcType=VARCHAR},
            #{item.showNumber,jdbcType=SMALLINT},
            #{item.showSubmitBtn,jdbcType=SMALLINT},
            #{item.headImgUrl,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
