<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tduck.FmUserFormSettingMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.tduck.FmUserFormSetting">
        <!--@mbg.generated-->
        <!--@Table csm.proj_product_deliver_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="form_key" jdbcType="VARCHAR" property="formKey"/>
        <result column="settings" jdbcType="VARCHAR" property="settings" typeHandler="com.msun.csm.common.config.JacksonTypeHandler"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        <!--@mbg.generated-->
        insert into tduck.fm_user_form_logic
        (
        id,
        form_key,
        settings,
        create_time,
        update_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT},
            #{item.formKey,jdbcType=VARCHAR},
            #{item.settings,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
