<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.tduck.FmUserFormItemMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.tduck.FmUserFormItem">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="form_key" jdbcType="VARCHAR" property="formKey"/>
        <result column="form_item_id" jdbcType="VARCHAR" property="formItemId"/>
        <result column="type" jdbcType="VARCHAR" property="type" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="is_display_type" jdbcType="SMALLINT" property="displayType"  typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="is_hide_type" jdbcType="SMALLINT" property="hideType" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="is_special_type" jdbcType="SMALLINT" property="specialType" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="show_label" jdbcType="SMALLINT" property="showLabel" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="required" jdbcType="SMALLINT" property="required" typeHandler="com.msun.csm.common.config.BooleanTypeHandler"/>
        <result column="placeholder" jdbcType="VARCHAR" property="placeholder"/>
        <result column="sort" jdbcType="BIGINT" property="sort"/>
        <result column="span" jdbcType="INTEGER" property="span"/>
        <result column="scheme" jdbcType="VARCHAR" property="scheme" typeHandler="com.msun.csm.common.config.JacksonTypeHandler"/>
        <result column="reg_list" jdbcType="VARCHAR" property="regList" typeHandler="com.msun.csm.common.config.JacksonTypeHandler"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        <!--@mbg.generated-->
        insert into tduck.fm_user_form_item
        (
        id,
        form_key,
        form_item_id,
        type,
        label,
        is_display_type,
        is_hide_type,
        is_special_type,
        show_label,
        default_value,
        required,
        placeholder,
        sort,
        span,
        scheme,
        reg_list,
        update_time,
        create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.formKey,jdbcType=VARCHAR},
            #{item.formItemId,jdbcType=VARCHAR},
            #{item.type,jdbcType=VARCHAR},
            #{item.label,jdbcType=VARCHAR},
            #{item.displayType,jdbcType=SMALLINT},
            #{item.hideType,jdbcType=SMALLINT},
            #{item.specialType,jdbcType=SMALLINT},
            #{item.showLabel,jdbcType=SMALLINT},
            #{item.defaultValue,jdbcType=VARCHAR},
            #{item.required,jdbcType=SMALLINT},
            #{item.placeholder,jdbcType=VARCHAR},
            #{item.sort,jdbcType=BIGINT},
            #{item.span,jdbcType=INTEGER},
            #{item.scheme,jdbcType=VARCHAR},
            #{item.regList,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

</mapper>
