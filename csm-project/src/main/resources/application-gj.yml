env: local
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://************:10032/imsp_new?currentSchema=csm&useUnicode=true&characterEncoding=utf8&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
          username: imsp
          password: 66A#0%@9QgwD2I17@MB#kK$N
        bd:
          driver-class-name: org.postgresql.Driver
          url: ********************************************************************************************************************************************************************************************************************************************
          username: root
          password: 891806797
  redis:
    host: ************
    port: 6379
    password: 123456
    database: 0
    cacheNamespace: csm
    lettuce:
      pool:
        max-active: 1000
        max-idle: 10
        max-wait: 3000
        min-idle: 5
  main:
    allow-circular-references: true
basedata:
  kettle-host: "http://127.0.0.1:10086/kettle"
  kettle_repo: basedata_dev
  db-host: localhost
  db-port: 5432
  db-name: basedata
  db-uname: root
  db-passwd: 891806797
#swagger
swagger:
  enabled: true
proxy:
  # 出口网关地址
  networkOutUrl: http://imsp-graytest.msunhis.com
#项目自有配置
project:
  # 钉钉机器人token
  dingTalk_token: 49012f85e813e3a304e9fdd7719537ab6774aedd4160dbce26dd4bdab03848a4
  #文件上传路径
  film-path: /msun/data/upload/
  #培训环境客户名称
  exam_custom_name: 无棣县卫生健康局
  #培训环境域名
  exam_domain: http://imsp-graytest.msunhis.com
  #项目日志文件目录
  log-path: ./logs/
  #基础数据结果目录
  data-manager-path: ./dataManage/
  #基础数据上传目录
  basedata-excel-path: ./folder/
  #操作文档目录
  doc-path: ./dataManage/productRecord/
  #chisData
  chis-data: ./CHIS_DATA/
  #华为云OBS配置
  obs:
    endpoint: obs.cn-north-4.myhuaweicloud.com/
    bucketName: chis-other
    accessKey: XDDNEUWDM2DALIM7XIP5
    secretKey: IvvEt7rEWzsGm7N9Ji8LHm67t1TiG1nchLFdImwF
    prePath: imsp/csm/test/
    netdetect_toolpath: imsp/csm/test/file/testnetwork/test/
  feign:
    # 企业微信相关数据
    wechatQy:
      url: https://qyapi.weixin.qq.com
      getAccessToken: /cgi-bin/gettoken
      getUserId: /cgi-bin/auth/getuserinfo
      getUserDetail: /cgi-bin/user/get
      getDeptDetail: /cgi-bin/department/get
    oa:
      #测试环境代理地址
#      url: http://oa.msunsoft.com:6820/msunErp-web/
      url: http://***********:8080/msunErp-web/
      login-method: webservice/autoImplPlatform?wsdl
      userTask-method: data/sync/getUsers?sync=imsp
      productsTask-method: data/sync/getProducts?sync=imsp
      deptTask-method: data/sync/getOrganizations?sync=imsp
      customersTask-method: data/sync/getCustomers?sync=imsp
      customersDetailTask-method: data/sync/getCustomerInfoById?sync=imsp
      divisionProvinceTask-method: data/sync/getDivision?sync=imsp
      check-apply-software: /imsp/proj/checkApply  #软件申请审核验收
      node-status-sync: /imsp/proj/syncProjSchedule
      interface-main-method: /interfaceMain/yun/applyInterfaceMain
      # 三方接口验证
      interface-verify: interfaceVerify/yun/completeInterfaceVerify
      # 向企业微信发送消息
      send-message: /external/message/sendNew?token=imsp
      #初始化工单id
      init-order: /imsp/proj/initProjInfo
      #拆分工单
      split-order: /imsp/proj/projSplit
      #合单
      merge-order: /imsp/proj/projMerge
      # MO审核时也通知运营平台
      notify_pmo_audit: /imsp/proj/prepaymentFeedBack
      # 云订阅开通
      yun-product-open: /imsp/proj/syncSoftDate
      #云资源开通时间
      yun-open: /imsp/proj/syncYzyOpen
      #转需求单
      transferDemand: /imsp/createRequest
      # 云容灾验收申请
      disaster_recovery_cloud_check_apply: imsp/proj/checkApplyCloud
      # 获取云容灾首付款缴纳信息
      get_disaster_cloud_paysignage: imsp/proj/cloudDisasterSatisfy
      # 同步运营平台厂商数据
      syncIsMsunCloud: imsp/proj/isMsunCloud
      # 同步运营平台的项目验收申请时间、工期减免、入驻时间
      synchronizeDuration: imsp/proj/projAcceptanceSubmissionTime
      # 原方案经理查询
      getPreSaleUser: imsp/proj/getPreSaleUser
      # 企业微信消息推送(预警单 罚单)
      sendFineMessage: /external/message/sendFine?token=imsp
      # 私有云免中间件部署申请
      middlewareApply: imsp/proj/middlewareApply
      # 查询是否是项目经理
      selectProjectMemberFlag: imsp/proj/getQualificationsBtUserId
      # 作废项目经理
      deleteProjectMemberFlag: imsp/proj/revokeQualification
      # 客服发起特殊事项审批流
      submitSpecialApproval: imsp/proj/submitSpecialApproval
      # 同步后端得分信息给运营平台
      getProjBackendScore: /imsp/proj/getProjBackendScore
    # 自助运维平台
    yunwei:
      url: http://172.16.2.79  #测试环境地址
      token-method: /devops/auth/getToken
      # 新医院申请
      saveHospitalInfo-method: /devops/deployResourceApply/saveHospitalInfo
      # 新产品申请
      saveProductInfo-method: /devops/deployResourceApply/saveProductInfo
      # 产品schema授权
      schemaGrant-method: /devops/deployResourceApply/schemaGrant
      # 新环境申请
      saveEnv-method: /devops/deployResourceApply/saveEnv
      checkPublicUrl-method: /devops/deployApply/checkPublicUrl
      # 校验预下载excel表格
      preDownload-method: /devops/deployResourceApply/preDownload
      # 下载交付文档
      downDeliver-method: /devops/deployResourceApply/downDeliver?path=
      # 撤销申请
      editStatusByDeliverPlatformApplyId-method: /devops/deployResourceApply/editStatusByDeliverPlatformApplyId
      # 同步云资源开通时间
      syncCloudTime-method: /devops/deployResourceApply/notifyContractTime
      # 验证预资源规划文件
      checkPreExcel-method: /devops/deployResourceApply/checkPreExcel
      # 云容灾申请
      disasterCloudApplyOpen-method: /devops/deployResourceApply/saveDisasterInfo
      # 同步到期时间
      recoverUpdateInfo-method: /devops/deployResourceApply/updateDisasterInfo
      #定时同步环境节点信息
      syncEnvInfoInfo-method: /devops/envHospital/getInfoByOrgHospitalId
      # 提交申请占用域名以及生成hospitalId接口
      preSubmit-method: /devops/deployResourceApply/preSubmit
    #自动化测试平台
    autoCheck:
      url: https://test.160666.xyz/api
      # 配置医院基本信息
      hospital: /api/external/delivery/hospital_info
      # 获取科室模版信息
      deptModel: /api/external/delivery/get_dept_model
      # 获取医院人员账号信息
      hospital_user_info: /api/external/delivery/hospital_user_info
      # 交付人员选择科室模版中对应的科室账号后端自动生成login-user
      dept_list: /api/external/delivery/generate_dept_dataset
      # 组建场景任务，组建两个场景任务一个是只包含基础数据检查的场景任务，一个是所有场景任务
      assemble_task: /api/external/delivery/assemble_task
      # 交付平台运行场景任务，需要校验任务是否组建完任务，运行状态不能为运行中，场景任务状态不能为测试通过
      task_run: /api/external/delivery/task_run
      # 将场景任务置为不可运行状态并且在在线医院列表中新增一条
      task_close: /api/external/delivery/task_close
      # 根据选择的药房药库信息生成login-user
      generate_login_user: /api/external/delivery/generate_dept_dataset_new
      # 生成科室角色的账号模版
      generate_dept_model: /api/external/delivery/generate_login_user_model
      # 生成登录用户, 新版接口, generate_login_user不再使用
      generateLoginUser: /api/external/delivery/generateLoginUser
      # 批量执行测试
      taskBatchRun: /api/external/delivery/taskBatchRun
      # 获取报告Mark说明
      summary_report: /api/external/delivery/summary_report
      # 保存报告Mark说明
      summary_report_mark: /api/external/delivery/summary_report_mark
    # 老项目接口
    oldImsp:
      appId: 5CB4D0F459FE12778DE2F6B86E705FEF
      publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtU5owyrE+9/XrrDaL2O6H7mCvUwC9CpgBPiHemIm49l8Zn6fy+ye/PTPdd+q/5na3csN02oQ9/QFAg/tYhvfi6Ag6hCHqBvGJTyjiq3mk3OxtVi8CE9DerR8O8hreIH0/lTc+784PJ9HYk77MqfCZ482WUPeDrC+NsI4AyJAyEx6Rgl9xYEhREGf5rNyTO9zEgzWhSPjJjz0rGOYkJTcyVdNPQW0a7G6J4734RVRavOPzEPY2g7e+jisuKRJiyTuPee/7TLOyZcgo0XFac5wo8V37915ow8sUkgHpv80ulPNdGmRt4AClJTCQ7nfnnTEtqMZ4j5GZzmcQcAdvhcWywIDAQAB
      #接口地址
      url: http://************:10010/imsp-cloud-unifytest
      #前端页面地址
      front-url: http://127.0.0.1/imsp-cloud-unifytest/
      copySurvey-method: /csmApi/copySurvey
      split-project: /splitAndMerge/splitProjectCSM
      merge-project: /splitAndMerge/mergeProjectCSM
      confirm-entry: /csmApi/confirmEntry
      addDictData: /csmApi/addDictData
      syncCommConfigArea: /csmApi/syncCommConfigArea
      managerConfirmProjectType: /projectManagementCenter/managerConfirmProjectType
      #同步报表表单
      saveOrUpdateReport: surveyReport/saveOrUpdateReportListFromCsm
      saveOrUpdateForm: surveyForm/saveOrUpdateFormListFromCsm
      allEquipProgressDataForCsm: /equipment/allEquipProgressDataForCsm
      #同步工单
      syncWorkOrder: /yunApi/syncWorkOrder
      copyData: /csmApi/copyData
      #复制数据的业务名称
      copyDataBusiness: HIS CODE
    # tduck调研平台
    tduck:
      url: http://************:10012/tduck-api
      # 生成待处理任务
      generate-task: /user/form/task/createTask
      deleteFormData: /user/form/data/deleteFormData
      # 项目拆分合并
      mergeProject: /csm/mergeProject
      splitProject: /csm/splitProject
      #接口开发平台
      getDictTaskInfo: /csm/getDictTaskInfo
    interface-demand:
      url: http://************:10000/third-platform
      #url: http://**********:10000/third-platform
      updateDemand-method: /msun/api/demand/updateDemandByDemandId
      insert-demand: /msun/api/demand/insertDemandNew
      updateDataType-demand: /msun/api/demand/updateDataType
    #接口开发平台--前端使用iframe嵌套
    interface-web-demand:
      url: http://************:10000/third-platform
      selectInterface-method: /static/demand/
    # 知识库客服运维平台
    knowledge:
      url: http://************:10013/knowServer
      appId: cbfbba04-3325-85cf-8d5a-091d493a8341
      publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvJ1lCDOWMYXfH59t+qtO6NWwoHywJmtwU6H0aRESOhw7Tos6aVX3+wkZ2GshsmFOF8pVDA5fO2YrqPae7bRC4KsV1gOo7+WDdDVwaKAjABoiBdRSRURliLClHmo3PXRQBJqCH6grQkJRpYQO102GtDAdOgLl9nu5qhOmRjnXLYhrBjuy5gNtC8E1w2BM/pLsQdrj8VFgbDe9PAPH3jJIsnemFPH419OSzsu6/Ma7MC19Hq31PtfAg8FkRCq7g0/1ZPUNzdCL5xOnHofdmbffn6mJ6L/xMhU9nPIRHHrtVpIxpPZ22srSTOUF3732Vg2XRu2t7xaKxCcipcwJeMb1QwIDAQAB
      # 鉴权
      authJf: /pass/userLoginByJf
      # 回写运维平台反馈单与接口绑定关系
      updateThirdInterfaceId-method: /fromImsp/feedback/updateThirdInterfaceId
      addThirdInter-method: /fromImsp/feedback/addQuestionInfoByKFComeJf
      downloadJfHosDeptUserInfo:  /fromImsp/feedback/downloadJfHosDeptUserInfo
    # 能力开放平台
    openApi:
      url: https://msunsoft-test.msunhis.com/openapi/msun-openapi-platform #测试环境地址
      #url: http://**********:30028
      sign-key: 8XL4RF2UDB74KPWRNS
      header-user_value: ewogICJ1aWQiOjAsCiAgImFjY2Vzc1R5cGUiOiJyb2xlIgp9
      env: test
      orgId: 10033
      hospitalIds: 407545331508854784
      getApiGroupList-method: /deliveryspec/getApiGroupList
      getApiList-method: /deliveryspec/getApiList
      registerApp-method: /deliveryspec/registerApp
      getAppInfoByKey-method: /deliveryspec/getAppInfoByKey
      applyAuth-method: /deliveryspec/applyAuthByGroup
      checkApp-method: /deliveryspec/validate/checkApp
    #授权管理平台
    authorizationManagement:
      sm2: 68a0c74a3797b069b3f65e12cad5365e95ff843ed78a121b4b3c5979a7417f2e
      url: http://************:10010/authorization_management_platform  #测试环境
      #url: http://**********:10010/authorization_management_platform #鲁千旗本地
      createAppAuthorization-method: /external/app/createAppAuthorization
      saveDeployDeviceInfo-method: /external/app/addAppDeviceInfo
      getDeployDeviceInfo-method: /external/app/getDeviceCountByAppId
    # 数据中台
    dataApplication:
      url: http://************:30745/dataapplication
      getResDataClassList-method: /interfacePlateform/getResDataClassList
      saveProductDataList-method: /orgHosInfo/addProductHos
    #Lis开放平台
    LisAnalyManager:
      url: http://************:10099/analyManager
      #url: http://************:9001/analyManager
      #添加设备信息
      insertEquip-method: /InstrumentInformation/submitFromPlatform
      #更新设备信息
      updateEquip-method: /InstrumentInformation/updateSurveyFullInfo
      #删除设备信息
      deleteEquip-method: /InstrumentInformation/deleteInstrument
      #撤销接口申请
      cancelApply-method: /InstrumentInformation/cancelInstrumentFrom
      #设备附件信息上传
      uploadFiles-method: /InstrumentInformation/uploadFiles
      #获取仪器明细
      getEquipItemByEquipNameOrId-method: /InstrumentInformation/getEquipItemByEquipNameOrId
      #催促接口
      urgeProcessing-method: /InstrumentInformation/urgeProcessing
  current:
    url: https://imsp-test.msuncloud.com/csm-front-mobile/
    qyWeChat-Auth-url: https://imsp-test.msuncloud.com/csm/qyWeChat/msunHealthQy
  # 微信相关数据 - 公众号：众阳健康
  wechat:
    appId: wx116a23e5af619f06
    secretKey: nhxTCOnhP4DA98zo7cfmenUdG_G08TR6euNKS3cBdlc
    getCodeUrl: https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect
    # 回调接口
    redirectUri: http://127.0.0.1:10011/csm/qyWeChat/redirectQy
#非云健康体系部署产品授权单独处理
product_auth:
  domainName: https://msunsoft-test.msunhis.com
  icdDomainName: http://************:8102
  #病案首页质控
  mrs-qa: /qc/api/checkAuthorization
  icd_smart: /ALPHA_MEDICAL_ICD/Hospital/icdAuthorization
#设置对接网关地址（API内部已实现鉴权逻辑）
implementation:
  #设置鉴权systemId（网关分配）
  system-id: implementation
  #设置鉴权秘钥（网关分配）
  secret: mKptt4TsedWJbELr
  # 是否使用自动查询(默认false)。查询间隔为10秒。
  auto:
    inject: false
  # 此处配置feign接口name或者value的值，@FeignClient(name = "knowServerFeignClient")
  exclude-req:
    - 'FeignSendMessage'
    - 'OldImspFeignClient'
    - 'ProductAuthFeignClient'
    - 'YunyingFeignClient'
    - 'YunweiFeignClient'
    - 'AutoCheckClient'
    - 'TDuckFeignClient'
    - 'KnowledgeFeignClient'
    - 'OpenApiFeignClient'
    - 'DataApplicationFeignClient'
    - 'AuthorizationManagementFeignClient'
    - 'LisAnalyManagerClient'
    - 'WechatFeignClientQy'
    - 'ThirdPlatformFeignClient'
#netCheck:
#  ip: ************
#  port: 10020
#  userName: msun
#  password: YGcVd0XVBKgs55ky
netCheck:
  ip: *************
  port: 10020
  userName: msun
  password: uxffYxrZkOB4vqxcwv3VcD0Wgr
#his鉴权秘钥
chis:
  systemId: implementation
  secret: mKptt4TsedWJbELr

widthTable:
  url: http://************:30549

tduck:
  domain: http://127.0.0.1/csm-survey/s
  api: http://127.0.0.1/tduck-api
  page: http://127.0.0.1/csm-survey
#域名信息
#requestUrl: https://imsp.msuncloud.com/imsp-cloud
requestUrl: http://127.0.0.1/imsp-cloud-unifytest
##患者智能
hzzn:
  domainName: https://hzzn-thirdapp-dev.msunhis.com/
  appId: heNanConveniencePlatform
  appKey: hhaichia%$^babc*
  realDomain: https://hzzn-dev.msunhis.com

medicalInsurance:
  domain: https://thirdpart2-graytest.msunhis.com/
  interfaceDomain: http://thirdpart2-graytest.msunhis.com:10001/

emredit:
  url: http://************:8890

#当前项目域名信息
thisProjectUrl: https://imsp-test.msuncloud.com/csm

#通用代理地址
AndroidOutApi:
  url: http://***********:20509

yixin:
  domain: https://hzzn-test.msunhis.com

# 调用转换接口：护理文书推荐
printreport:
  # 模板转换将
  getTemplateBrief: http://************:8890/newPrint/getTemplateBrief
  # 使用base64和模板列表进行匹配 同步
  base64templates: http://************:10003/match-template/match-template-by-base64-and-templates
  # 异步使用base64和模板列表进行匹配，返回任务ID
  asyncbase64templates: http://************:10003/match-template/async/match-template-by-base64-and-templates
  # 异步单个接口
  asynctaskstatus: http://************:10003/match-template/async/get-task-status
  # 异步批量接口
  asyncbatch: http://************:10003/match-template/async/match-template-by-base64-and-templates-batch
  #  批量接口
  asynctaskstatusbatch: http://************:10003/match-template/async/get-task-status-batch
cloud:
  forwardGateway: http://172.16.2.173:10080