<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SSE 测试</title>
</head>
<body>
<button id="startJob">开始作业</button>
<br>
<div id="logContainer"></div>


<script>
    document.getElementById('startJob').addEventListener('click', function () {
        const args = 'PXjAF1G1k1n2KDOvRjyRAon03YZ-QPQAyhgHxfwyFJXbz5kSoPZQ8jp-SSsbRp3eDnZc-J-y7rSwLOx1pVJ4ZEm3EVYNxESRAHYoFeZk6CogdYb2d1rkVvQU8xoJ2r1zN5LzVzzcsnHWn-GT1ZeoWnt6y7yIp9a2W80c4C8zefwgt8TrRqs1DUxckLVPu7oax2w8AKmJwsl_jl_ReDRTCpoJQm2zHiv4ecBS2WsnCrHbAQqINcyc8sWjb8CcDigTviSC7RIzpEkGJvHDidD3AsoZlOAigykgqehslEVTT5Sb-OKSelMJivIwJ5KcL-yDw6MfGE7BAZmD9SlEM-W0dRjS2-sUJKyThfv7UPFc2ITY_cFqDlesxJzprK1WoPyu_ho6SW1IRV5c2S0szmVr82cvmD6CFH8-svrxyO564r2Z0dQtdiC5yPTGHkCDOO79BVfFti8mpszHRpkbNN_tM2wWuAvReRqKuNCJjsfVveZ1SvlYYWN75PqJKT9ygABEqhSjZ2OPR50HHlHZtgGFSOW_jhKB0s6IzBUbH0zE3dck2G3KQPqFQpJvSzvIn73uoIok6riDLm0BWjpoqypW5A'

        // const args = 'oJCJTm6puEfHrY7O_D9LPbKxLiroitJYZeTmU4Lx_rtnpMScLbPoGLJvYiyNRydFSrP5YNXyJeh4a9buPmuQ5DyTTyebcQth3NZfEasCKV1yiwtdiuNeG9B8ny15h3u0Mpn8j1onw-pIMJW2dQh20IYFmP36_tg9gOoXxZt33NmEaTPNBnNorn83HXGISFYeSwsSkgvxhrjsaqoyassXUkcDihOrPSdLQnlkLY53ngWHG8vhWAOjfZCf0oNp59VQL9lwUTHV59iwOyaMPDYWIqpT2kxd98Mt-xeoIzAozDqRS_9FCI9KEz2UX7YZVpbVXgi05PXf5myag11hvTtbjM6q4f7bPQoumdEJoNChutsd4gl_24wvUmw-sT4jmcbXnpmlqnOeYWqRULmtC9WG135jo-nSnXOfHCwAJ0crnXDQWCpdu4_01IfVDC3tITIlzYQNwi3vMwmh8BXDduCMvhRBtidT150vNhw5CIjJiR9n7v2oE8Nh5OBm3iv8A8hkGqctvfHrgWKFOCd7jQLySHsoBSsjICe1yRYCxBulZrOLAnPwZKlNjlRxXJpj76wvyJVA2nO3R-1Q0ebCbUumZAA92IxjZpE0ehodE0gMqAD5uimWCtUPJ5Ui-I7BHm89'; // 示例参数
        const eventSource = new EventSource(`http://localhost:10011/csm/importData/execTest/${args}`);

        eventSource.onmessage = function (event) {
            console.error(event.data)
            const logContainer = document.getElementById('logContainer');
            const log = document.createElement('p');
            const obj = JSON.parse(event.data)
            const logText = JSON.stringify(obj)
            if (obj.logLevel === 'Error') {
                log.style.color = 'red';
            } else {
                log.style.color = 'green';
            }
            log.innerHTML = logText
            // log.innerHTML = `${logText}`.replaceAll('\r\n', '<br>');
            logContainer.appendChild(log);
        };

        eventSource.onerror = function (err) {
            console.error('SSE连接错误', err);
            eventSource.close();
        };
    });
</script>
</body>
</html>
