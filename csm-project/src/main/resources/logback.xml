<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <property name="log.home" value="./logs"/>
    <property name="log.charset" value="utf-8"/>
    <property name="log.pattern.console"
              value="%d{yyyy-MM-dd HH:mm:ss} -- 1 -- domain:csm -- gray: -- traceId:%green(%X{traceId}) -- [%thread] %highlight(%-5level) %magenta(%C.%M.%L) - %X{costTime} - %highlight([%X{person}.%X{bizName}.%X{bizDetail}]:%msg) %n%ex"/>
    <property name="log.pattern"
              value="%d{yyyy-MM-dd'T'HH:mm:ss},080+08:00 -- 1 -- domain:csm -- gray:false -- traceId:%X{traceId} -- [%thread] %-5level %C.%M.%L - %X{costTime} - [%X{person}.%X{bizName}.%X{bizDetail}]:%msg %n%ex"/>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern.console}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!-- info级别日志文件输出 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件输出的文件名 -->
        <File>${log.home}/o2o.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每日生成日志文件或日志文件大小超出限制后输出的文件名模板 -->
            <fileNamePattern>${log.home}/%d{yyyyMMdd}/info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 日志文件保留天数 -->
            <maxHistory>180</maxHistory>
            <!-- 日志文件最大大小：100MB -->
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- error级别日志文件输出 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <File>${log.home}/error.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.home}/%d{yyyyMMdd}/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>


    <!--普通日志输出到控制台-->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
    <!--控制不必要的日志打印-->
    <logger name="com.obs" level="INFO"/>
</configuration>
