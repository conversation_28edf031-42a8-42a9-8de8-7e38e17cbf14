# Actuator Web 访问端口
management:
  server:
    port: 8188
  endpoint:
    show-details: always
    health:
      redis:
        enabled: false
  endpoints:
    jmx:
      exposure:
        include: '*'
    web:
      exposure:
        include: '*'
# Spring Boot 端口配置
server:
  port: 10011
  servlet:
    context-path: /csm
spring:
  application:
    name: csm
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: local
  servlet:
    multipart:
      enabled: true
      max-file-size: 2048MB
      max-request-size: 2048MB
  # swagger
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # quartz
  quartz:
    job-store-type: memory  # 使用内存存储任务
    properties:
      org:
        quartz:
          scheduler:
            instanceName: SyncScheduler
          threadPool:
            threadCount: 5  # 线程池大小
mybatis-plus:
  global-config:
    banner: false
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    mapper-locations: classpath:mapper/*.xml
    call-setters-on-nulls: true
logging:
  config: classpath:logback.xml
  level:
    com.msun.csm.dao.mapper: debug




