package com.msun.csm.controller.csmapi;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.validation.Valid;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.csm.dto.CsmDTO;
import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDataOneSaveDto;
import com.msun.core.component.implementation.api.formlib.dto.FormlibDeliverToYjkDTO;
import com.msun.core.component.implementation.api.formlib.dto.FormlibProductDTO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibComeToProductPageVO;
import com.msun.core.component.implementation.api.formlib.vo.FormlibDataVO;
import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsDTO;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsListDTO;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.csm.CsmParamerDTO;
import com.msun.csm.model.csm.HisLoginCsmVo;
import com.msun.csm.model.csm.LongUrlToShortUrlDTO;
import com.msun.csm.model.csm.PrintInfoMainCsmDTO;
import com.msun.csm.model.csm.PrintInfoSendMessageDTO;
import com.msun.csm.model.imsp.CustomerNotOnlineVO;
import com.msun.csm.model.imsp.HospitalNotOnlineVO;
import com.msun.csm.model.statis.ProjCsmHostReq;
import com.msun.csm.model.statis.ProjCustomReq;
import com.msun.csm.model.statis.ProjStatisticalReportMainUpdateStatusReq;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/09/23
 */
@Api(tags = "对外开放接口")
@RequestMapping(value = "/csmOutApi")
public interface CsmOutApi {

    /**
     * 交付平台跳转云健康登录接口
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "交付平台跳转云健康登录接口", notes = "交付平台跳转云健康登录接口")
    @PostMapping(value = "/loginCloud")
    @ResponseBody
    Result<HisLoginCsmVo> verifyLoginBeforeCloudHealthRedirect(@RequestBody CsmParamerDTO param);

    @ApiOperation(value = "补偿接口", notes = "补偿接口")
    @PostMapping(value = "/compensationInterface")
    @ResponseBody
    Result compensationInterface();

    @ApiOperation(value = "打印平台查询交付平台医院下报表打印节点数据",
            notes = "打印平台查询交付平台医院下报表打印节点数据")
    @PostMapping(value = "/getPrintCodeResultListByParamer")
    @ResponseBody
    ResponseData getPrintCodeResultListByParamer(@RequestBody CsmDTO csmDTO);

    @ApiOperation(value = "给负责人发送消息", notes = "给负责人发送消息")
    @PostMapping(value = "/sendReportPrintMsg")
    @ResponseBody
    ResponseData sendReportPrintMsg(@RequestBody CsmDTO csmDTO);

    @ApiOperation(value = "查询负责人手机号", notes = "查询负责人手机号")
    @PostMapping(value = "/getPersonByParamer")
    @ResponseBody
    ResponseData getPersonByParamer(@RequestBody CsmDTO csmDTO);


    @ApiOperation(value = "上传单据打印效果", notes = "上传单据打印效果")
    @PostMapping(value = "/saveMedicalPrinterConfigRenderings")
    @ResponseBody
    ResponseData saveMedicalPrinterConfigRenderings(@RequestBody @Valid ReportRenderingsDTO reportRenderingsDTO);

    /**
     * 各产品设备自动检测
     *
     * @param dto 请求参数, lis, pacs, 手麻调用
     * @return 返回成功或失败
     */
    @ApiOperation("各产品设备自动检测")
    @PostMapping("/productAutoCheckEquip")
    @ResponseBody
    ResponseResult<String> csmAutoCheckEquip(@RequestBody EquipAutoCheckDTO dto);

    /**
     * 上传协助记录附件
     * @param reportRenderingsDTO
     * @return
     */
    @ApiOperation (value = "上传协助记录附件", notes = "上传协助记录附件")
    @PostMapping (value = "/uploadAssistAttachment")
    @ResponseBody
    ResponseData uploadAssistAttachment(@RequestBody @Valid ReportRenderingsListDTO reportRenderingsDTO);

    /**
     * 报表平台下沉后，回更交付平台报表状态为制作完成
     * @param dto
     * @return
     */
    @ApiOperation(value = "报表平台下沉后，回更交付平台报表状态为制作完成", notes = "报表平台下沉后，回更交付平台报表状态为制作完成")
    @PostMapping (value = "/updateStatisticalReportStatus")
    @ResponseBody
    ResponseData updateStatisticalReportStatus(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 报表平台保存后，回更交付平台报表状态为制作中
     * @param dto
     * @return
     */
    @ApiOperation(value = "报表平台保存后，回更交付平台报表状态为制作中", notes = "报表平台保存后，回更交付平台报表状态为制作中")
    @PostMapping (value = "/updateStatisticalReportStatusBySave")
    @ResponseBody
    ResponseData updateStatisticalReportStatusBySave(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 报表平台发布后，回更交付平台报表状态为发布
     * @param dto
     * @return
     */
    @ApiOperation(value = "报表平台发布后，回更交付平台报表状态为发布", notes = "报表平台发布后，回更交付平台报表状态为发布")
    @PostMapping (value = "/updateStatisticalReportStatusByRelease")
    @ResponseBody
    ResponseData updateStatisticalReportStatusByRelease(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 报表平台查看单个报表数据
     * @param dto
     * @return
     */
    @ApiOperation(value = "报表平台查看单个报表数据", notes = "报表平台查看单个报表数据")
    @PostMapping (value = "/getReportById")
    @ResponseBody
    ResponseData getReportById(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 报表平台查看该客户下报表数据
     * @param dto
     * @return
     */
    @ApiOperation(value = "报表平台调研列表信息", notes = "报表平台调研列表信息")
    @PostMapping (value = "/getReportByParamer")
    @ResponseBody
    ResponseData getReportByParamer(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 报表平台作废后，回更交付平台报表关联统计平台主键
     * @param dto
     * @return
     */
    @ApiOperation(value = "报表平台作废后，回更交付平台报表关联统计平台主键", notes = "报表平台作废后，回更交付平台报表关联统计平台主键")
    @PostMapping (value = "/deleteRelationshipByParamer")
    @ResponseBody
    ResponseData deleteRelationshipByParamer(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 长链接转短链接
     * @param dto
     * @return
     */
    @ApiOperation(value = "长链接转短链接", notes = "长链接转短链接")
    @PostMapping (value = "/longUrlToShortUrlFunction")
    @ResponseBody
    Result<String> longUrlToShortUrlFunction(@RequestBody @Valid LongUrlToShortUrlDTO dto);

    /**
     * 短链接转长链接转
     * @param shortCode
     * @return
     */
    @ApiOperation(value = "短链接转长链接转", notes = "短链接转长链接转")
    @GetMapping("/getLongUrl")
    @ResponseBody
    ResponseEntity<Void> getLongUrl(@RequestParam(name = "shortCode", required = false) String shortCode);

    /**
     * 报表平台作废后，回更交付平台报表关联统计平台主键
     * @param dto
     * @return
     */
    @ApiOperation(value = "院内上线产品的接口", notes = "院内上线产品的接口")
    @PostMapping (value = "/selectHospitalOnlineProductByParamer")
    @ResponseBody
    ResponseData selectHospitalOnlineProductByParamer(@RequestBody @Valid ProjStatisticalReportMainUpdateStatusReq dto);

    /**
     * 运维平台获取统计报表制作路径
     * @param operationStatisticsReportId
     * @return
     */
    @ApiOperation(value = "运维平台获取统计报表制作路径", notes = "运维平台获取统计报表制作路径")
    @GetMapping (value = "/getStatisticsReportAddUrl")
    @ResponseBody
    Result<String> getStatisticsReportAddUrl(@RequestParam(name = "operationStatisticsReportId", required = false) String operationStatisticsReportId, @RequestParam(name = "account", required = false) String account);

    /**
     * 获取交付平台测试环境域名或正式环境域名
     * @param dto
     * @return
     */
    @ApiOperation(value = "获取交付平台测试环境域名或正式环境域名", notes = "获取交付平台测试环境域名或正式环境域名")
    @PostMapping (value = "/getCsmHost")
    @ResponseBody
    ResponseData getCsmHost(@RequestBody @Valid ProjCsmHostReq dto);

    /**
     * 查询未上线客户信息
     * @return
     */
    @ApiOperation(value = "查询未上线客户信息", notes = "查询未上线客户信息")
    @PostMapping (value = "/getNotOnlineCustomer")
    @ResponseBody
    Result<List<CustomerNotOnlineVO>> getNotOnlineCustomer();

    /**
     * 查询未上线客户信息
     * @param dto
     * @return
     */
    @ApiOperation(value = "根据客户id查询未上线医院信息", notes = "根据客户id查询未上线医院信息")
    @PostMapping (value = "/getNotOnlineHospitalData")
    @ResponseBody
    Result<List<HospitalNotOnlineVO>> getNotOnlineHospitalData(@RequestBody @Valid ProjCustomReq dto);

    /**
     * 打印报表下发云健康成功后回更交付平台状态
     * @param dto
     * @return
     */
    @ApiOperation(value = "打印报表下发云健康成功后回更交付平台状态", notes = "打印报表下发云健康成功后回更交付平台状态")
    @PostMapping (value = "/updatePrintReportStatusData")
    @ResponseBody
    Result<String> updatePrintReportStatusData(@RequestBody @Valid ProjCustomReq dto);


    /**
     * 保存打印信息
     * @param dto
     * @return
     */
    @ApiOperation(value = "保存打印信息", notes = "保存打印信息")
    @PostMapping (value = "/savePrintInfo")
    @ResponseBody
    ResponseResult<String> savePrintInfo(@RequestBody @Valid PrintInfoMainCsmDTO dto);


    /**
     * 打印平台下沉接口完成/失败后进行发送消息
     * @param dto
     * @return
     */
    @ApiOperation(value = "打印平台下沉接口完成/失败后进行发送消息", notes = "打印平台下沉接口完成/失败后进行发送消息")
    @PostMapping (value = "/sendMessageByPrint")
    @ResponseBody
    ResponseResult sendMessageByPrint(@RequestBody @Valid PrintInfoSendMessageDTO dto);

    /**
     * 根据资源库id查询资源库数据
     *
     * @param dto
     * @return
     */
    @ApiOperation("根据资源库id查询资源库数据")
    @PostMapping("/getFormDataByJfid")
    @ResponseBody
    ResponseResult<FormlibDataVO> getFormDataByJfId(@RequestBody @Valid FormlibProductDTO dto);

    /**
     * 产品保存到交付平台
     * @param dto
     * @return
     */
    @ApiOperation(value = "产品保存到交付平台", notes = "产品保存到交付平台")
    @PostMapping (value = "/saveFormOneDataToDeliver")
    @ResponseBody
    ResponseResult<String> saveFormOneDataToDeliver(@RequestBody @Valid FormlibDataOneSaveDto dto);

    /**
     * 从交付根据类型获取资源库数据
     * @param dto
     * @return
     */
    @ApiOperation(value = "从交付根据类型获取资源库数据", notes = "从交付根据类型获取资源库数据")
    @PostMapping (value = "/getFormDataByDeliverToYjk")
    @ResponseBody
    ResponseResult<FormlibComeToProductPageVO> getFormDataByDeliverToYjk(@RequestBody FormlibDeliverToYjkDTO dto);

}
