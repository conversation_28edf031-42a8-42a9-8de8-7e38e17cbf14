package com.msun.csm.controller.tduck;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.tduck.req.CanEditFormReq;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq2;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/29 15:36
 */
@Api(tags = "运营平台调用交付接口")
@RequestMapping("/tduck")
public interface TduckApi {

    /**
     * 调研提交完成调研计划
     *
     * @param updateSurveyPlanStatus
     * @return
     */
    @ApiOperation(value = "调研提交完成调研计划", notes = "调研提交完成调研计划")
    @PostMapping(value = "/updateSurveyPlanStatus")
    @ResponseBody
    Result updateSurveyPlanStatus(@Valid @RequestBody UpdateSurveyPlanStatusReq updateSurveyPlanStatus);


    /**
     * 更新满意度调查完成状态
     *
     * @param updateSurveyPlanStatus 参数
     * @return 操作结果
     */
    @PostMapping(value = "/updateSatisfactionSurveyStatus")
    @ResponseBody
    Result<Void> updateSatisfactionSurveyStatus(@Valid @RequestBody UpdateSurveyPlanStatusReq2 updateSurveyPlanStatus);

    /**
     * 保存待处理任务和配置数据
     *
     * @param saveBackLogAndDetailReq
     * @return
     */
    @ApiOperation(value = "保存待处理任务和配置数据", notes = "保存待处理任务和配置数据")
    @PostMapping(value = "/saveBackLogAndDetail")
    @ResponseBody
    Result saveBackLogAndDetail(@Valid @RequestBody SaveBackLogAndDetailReq saveBackLogAndDetailReq);


    /**
     * 查询当前登录人是否有调研问卷的填写权限
     *
     * @param param 参数
     * @return true-可以编辑问卷；false-不可编辑问卷
     */
    @ApiOperation(value = "查询当前登录人是否有调研问卷的填写权限", notes = "查询当前登录人是否有调研问卷的填写权限")
    @PostMapping(value = "/canEditProductForm")
    @ResponseBody
    Result<Boolean> canEditProductForm(@Valid @RequestBody CanEditFormReq param);


}
