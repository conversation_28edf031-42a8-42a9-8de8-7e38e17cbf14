package com.msun.csm.model.tduck.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSurveyPlanStatusReq2 {
    /**
     * 项目id
     */
    @NotNull (message = "项目id不能为空")
    private Long projectInfoId;
    /**
     * 医院id
     */
    private Long hospitalInfoId;

    /**
     * 产品Id
     */
    @NotNull (message = "产品id不能为空")
    private Long yyProductId;

    /**
     * 调研人id
     */
    @NotNull(message = "调研人id不能为空")
    private Long surveyUserId;

    /**
     * 调研科室
     */
    @NotNull(message = "调研科室不能为空")
    private String deptName;
}
