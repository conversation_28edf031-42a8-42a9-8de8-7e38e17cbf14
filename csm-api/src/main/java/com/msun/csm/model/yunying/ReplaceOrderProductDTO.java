package com.msun.csm.model.yunying;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/9
 */
@Data
public class ReplaceOrderProductDTO {

    // 工单id
    @NotNull(message = "工单id不能为空")
    private Long orderId;

    // 1 删除  2 新增
    @NotNull(message = "类型不能为空")
    private Integer type;

    @ApiModelProperty("是否特批")
    private Boolean isSpecial = false;

    @ApiModelProperty("特批主键ID")
    private Long specialId;

    @ApiModelProperty("操作账号")
    private String account; // 账号

    // 替换产品列表
    @NotEmpty(message = "替换产品列表不能为空")
    List<ReplaceProductDTO> productList;

}
