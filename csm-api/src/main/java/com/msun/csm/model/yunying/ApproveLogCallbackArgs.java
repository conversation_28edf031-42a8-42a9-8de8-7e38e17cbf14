package com.msun.csm.model.yunying;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/7/14 16:40
 */
@Data
public class ApproveLogCallbackArgs {
    @NotNull
    @ApiModelProperty("审批ID，交付平台发起审批传过去的那个id")
    private Long id;
    @NotNull
    @ApiModelProperty("审批状态：1审批中 2成功 3失败")
    private Integer status;
    @NotBlank
    @ApiModelProperty("审批结果：审批通过；下一审批人：石磊，电话号：***********")
    private String result;
    @ApiModelProperty("是否已经结束，如果该字段为true就标识该审批已经结束，我会去更新审批总记录状态")
    private Boolean isFinish;
    @NotBlank
    @ApiModelProperty("当前审批人账号")
    private String account;
}
