package com.msun.csm.model.yunying;

import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/12
 */
@Data
public class ReplaceProductDTO {

    // 新产品id
    private Long productId;

    // 新产品projId
    private Integer projProductId;

    /**
     * 0、特批产品
     * 1、'买产品' = 永久
     * 2、'买服务' = 有时间限制
     */
    private Integer buyMode;

    /**
     * 购买类型: 1新购 3续费
     */
    private Integer buyType;

    /**
     * 解决方案 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    private Integer pemCusSolType;

    /**
     * 产品数量
     */
    private Integer num;
}
