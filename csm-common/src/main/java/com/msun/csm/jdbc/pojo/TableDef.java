package com.msun.csm.jdbc.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TableDef {

    @Data
    public static class TableColDef {
        private String name;
        private String type;
        private Integer size;
        private Integer decimalDigits;
        private Boolean nullable;
        private Object defaultValue;
        private String comments;
    }
    private String schema;
    private String name;
    private String comments;
    private List<TableColDef> colDefs = new ArrayList<>();
    private List<String> pks = new ArrayList<>();
    private List<String> indexs = new ArrayList<>();
    private Map<String, List<String>> uniqueIndexes = new HashMap<>();
}
