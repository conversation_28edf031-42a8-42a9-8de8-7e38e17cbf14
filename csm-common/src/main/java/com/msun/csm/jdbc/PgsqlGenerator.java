package com.msun.csm.jdbc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import com.msun.csm.jdbc.pojo.DsCfg;
import com.msun.csm.jdbc.pojo.TableDef;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class PgsqlGenerator {
    //生成PgUrl
    public static String buildUrl(DsCfg cfg) {
        if (StrUtil.isNotBlank(cfg.getSchema())) {
            return StrUtil.format("jdbc:postgresql://{}:{}/{}?currentSchema={}&stringtype=unspecified&socketTimeout=300000&?TimeZone=Asia/Shanghai", cfg.getHost(), cfg.getPort(), cfg.getDb(), cfg.getSchema());
        } else {
            return StrUtil.format("jdbc:postgresql://{}:{}/{}?stringtype=unspecified&socketTimeout=300000&?TimeZone=Asia/Shanghai", cfg.getHost(), cfg.getPort(), cfg.getDb());
        }
    }

    //生成 DDL
    public static String generatorDDL(TableDef tableDef) {
        String schema = tableDef.getSchema();
        String table = tableDef.getName();

        // 构建表注释
        List<String> commentsList = tableDef.getColDefs().stream()
                .filter(colDef -> StrUtil.isNotBlank(colDef.getComments()))
                .map(colDef -> StrUtil.format("COMMENT ON COLUMN {}.{}.{} IS '{}';", schema, table, colDef.getName(), colDef.getComments()))
                .collect(Collectors.toList());
        commentsList.add(StrUtil.format("COMMENT ON TABLE {}.{} IS '{}';", schema, table, tableDef.getComments()));


        // 构建索引
        List<String> indexList = tableDef.getIndexs().stream()
                .map(idx -> StrUtil.format("CREATE INDEX IF NOT EXISTS IDX_{}_{}_{} ON {}.{}({});", schema, table, idx, schema, table, idx))
                .collect(Collectors.toList());

        // 构建 DDL
        StrBuilder ddlBuilder = StrBuilder.create("CREATE TABLE IF NOT EXISTS ")
                .append(schema).append(".").append(table).append("(\n");

        tableDef.getColDefs().forEach(colDef -> {
            ddlBuilder.append(colDef.getName()).append(" ").append(colDef.getType());
            if ("VARCHAR".equalsIgnoreCase(colDef.getType()) && colDef.getSize() > 0) {
                ddlBuilder.append("(").append(colDef.getSize()).append(")");
            } else if ("numeric".equalsIgnoreCase(colDef.getType()) || "decimal".equalsIgnoreCase(colDef.getType())) {
                if (colDef.getSize() == null || colDef.getSize() == 0 || colDef.getDecimalDigits() == null || colDef.getDecimalDigits() == 0) {
                    ddlBuilder.append("(34,16)");
                } else {
                    ddlBuilder.append("(").append(colDef.getSize()).append(",").append(colDef.getDecimalDigits()).append(")");
                }
            }
            if (Boolean.FALSE.equals(colDef.getNullable())) {
                ddlBuilder.append(" NOT NULL");
            }
            if (colDef.getDefaultValue() != null) {
                ddlBuilder.append(" DEFAULT ").append(colDef.getDefaultValue());
            }
            ddlBuilder.append(",\n");
        });

        // 添加主键
        if (CollUtil.isNotEmpty(tableDef.getPks())) {
            ddlBuilder.append(StrUtil.format("CONSTRAINT {}_{}_{}_pk PRIMARY KEY ({})", tableDef.getSchema(), tableDef.getName(), String.join("_", tableDef.getPks()), String.join(",", tableDef.getPks()))).append(",\n");
        }

        if (CollUtil.isNotEmpty(tableDef.getUniqueIndexes())) {
            tableDef.getUniqueIndexes().forEach((indexName, columns) -> {
                String uniqueIndex = StrUtil.format("CONSTRAINT {}_{}_{}_unique UNIQUE ({})", tableDef.getSchema(), tableDef.getName(), indexName, String.join(",", columns));
                ddlBuilder.append(uniqueIndex).append(",\n");
            });
        }

        // 移除最后的逗号并关闭括号
        ddlBuilder.del(ddlBuilder.length() - 2, ddlBuilder.length()).append("\n);");

        // 添加注释和索引
        if (CollUtil.isNotEmpty(commentsList)) {
            ddlBuilder.append("\n").append(StrUtil.join("\n", commentsList));
        }
        if (CollUtil.isNotEmpty(indexList)) {
            ddlBuilder.append("\n").append(StrUtil.join("\n", indexList));
        }

        return ddlBuilder.toString();
    }

    //比较新老DDL，找到需要更新的字段，并给到更新的DDL
    public static String compareColDefGenerateDdl(TableDef tableDef, TableDef.TableColDef oldCol, TableDef.TableColDef newCol) {
        if (newCol == null) {
            throw new IllegalArgumentException("newCol is null");
        }
        List<String> alterSqls = new ArrayList<>();
        if (oldCol == null) {
            // 如果字段不存在，生成添加字段的 SQL
            String addColumnSql = StrUtil.format(
                    "ALTER TABLE {}.{} ADD COLUMN {} {}{}{};",
                    tableDef.getSchema(),
                    tableDef.getName(),
                    newCol.getName(),
                    newCol.getType(),
                    (newCol.getSize() != null && newCol.getName().toLowerCase().contains("char") ? "(" + newCol.getSize() + ")" : ""),
                    (Boolean.FALSE.equals(newCol.getNullable()) ? " NOT NULL" : "")
            );
            alterSqls.add(addColumnSql);
            String commentSql = StrUtil.format(
                    "COMMENT ON COLUMN {}.{} IS '{}';",
                    tableDef.getSchema(),
                    newCol.getName(),
                    newCol.getComments()
            );
            alterSqls.add(commentSql);
        } else {
            // 如果字段存在但属性不一致，生成修改字段的 SQL
            if (!newCol.getType().equalsIgnoreCase(oldCol.getType())
                    ||
                    !Objects.equals(newCol.getSize(), oldCol.getSize())
                    ||
                    !Objects.equals(newCol.getComments(), oldCol.getComments())) {
                String modifyColumnSql = StrUtil.format(
                        "ALTER TABLE {}.{} ALTER COLUMN {} TYPE {}{};",
                        tableDef.getSchema(),
                        tableDef.getName(),
                        newCol.getName(),
                        newCol.getType(),
                        (newCol.getSize() != null && newCol.getName().toLowerCase().contains("char") ? "(" + newCol.getSize() + ")" : "")
                );
                alterSqls.add(modifyColumnSql);

                if (StrUtil.isNotBlank(newCol.getComments()) && !StrUtil.equals(newCol.getComments(), oldCol.getComments())) {
                    String commentSql = StrUtil.format(
                            "COMMENT ON COLUMN {}.{} IS '{}';",
                            tableDef.getSchema(),
                            newCol.getName(),
                            newCol.getComments()
                    );
                    alterSqls.add(commentSql);
                }
            }
        }
        if (CollUtil.isEmpty(alterSqls)) {
            return "";
        }
        return StrUtil.join("\n", alterSqls);
    }

    //生成insert语句
    public static String generateInsertSql(String schemaName, String tableName, List<String> fields, List<String> pks) {
        // 使用 StringBuilder 构建 SQL
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(StrUtil.format("INSERT INTO {}.{} ({}) VALUES (", schemaName, tableName, String.join(",", fields)));
        // 构建占位符
        String placeholders = fields.stream().map(f -> "?").collect(Collectors.joining(","));
        sqlBuilder.append(placeholders).append(")");
        // 处理 ON CONFLICT 子句
        if (CollUtil.isNotEmpty(pks)) {
            sqlBuilder.append(StrUtil.format(" ON CONFLICT ({}) DO UPDATE SET ", String.join(",", pks)));
            String updateFields = fields.stream()
                    .filter(field -> !pks.contains(field)) // 排除主键字段
                    .map(field -> StrUtil.format("{} = EXCLUDED.{}", field, field))
                    .collect(Collectors.joining(","));
            sqlBuilder.append(updateFields);
        } else {
            sqlBuilder.append(" ON CONFLICT DO NOTHING");
        }

        return sqlBuilder.toString();
    }

    //构建单表查询语句
    public static String generateSelectSql(String schemaName, String tableName, List<String> fields, String projectNumber, Long hospitalId, Long orgId) {
        if (CollUtil.isEmpty(fields)) {
            throw new RuntimeException("查询字段不能为空");
        }
        // 构建 SELECT 子句
        String selectClause = "SELECT " + String.join(",", fields);
        // 构建 FROM 子句
        String fromClause = StrUtil.format(" FROM {}.{}", schemaName, tableName);
        // 构建 WHERE 子句
        String whereClause = StrUtil.format(" WHERE hospital_id = {} AND org_id = {} and project_number = '{}'", hospitalId, orgId, projectNumber);
        // 拼接完整 SQL
        return selectClause + fromClause + whereClause;
    }

    public static String generateDeleteSql(String schemaName, String tableName, String projectNumber, Long hospitalId, Long orgId) {
        String sql = "DELETE FROM " + schemaName + "." + tableName +
                " WHERE hospital_id = " + hospitalId +
                " AND org_id = " + orgId +
                " AND project_number = '" + projectNumber + "'";
        return sql;
    }
}
