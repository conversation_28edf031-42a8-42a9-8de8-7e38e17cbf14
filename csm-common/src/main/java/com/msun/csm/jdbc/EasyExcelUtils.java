package com.msun.csm.jdbc;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.io.File;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
public class EasyExcelUtils {

    //递归删除临时文件
    public static void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        if (!directory.delete()) {
            log.warn("无法删除文件或目录: {}", directory.getAbsolutePath());
        }
    }

    //递归解析Excel文件
    public static void readExcelFile(File directory, Consumer<File> post) {
        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归处理子目录
                readExcelFile(file, post);
            } else if (file.getName().endsWith(".xlsx")) {
                // 解析 xlsx 文件
                post.accept(file);
            }
        }
    }

    public static class RowHeightConfig extends AbstractRowHeightStyleStrategy {
        /**
         * 默认高度
         */
        private static final Integer DEFAULT_HEIGHT = 300;

        //设置表头行高
        @Override
        protected void setHeadColumnHeight(Row row, int relativeRowIndex) {

        }

        //设置内容行高
        @Override
        protected void setContentColumnHeight(Row row, int relativeRowIndex) {
            Iterator<Cell> cellIterator = row.cellIterator();
            if (!cellIterator.hasNext()) {
                return;
            }
            // 默认为 1行高度
            int maxHeight = 1;
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                if (cell.getCellTypeEnum() == CellType.STRING) {
                    String value = cell.getStringCellValue();
                    int len = value.length();
                    int num = 0;
                    if (len > 50) {
                        num = len % 50 > 0 ? len / 50 : len / 2 - 1;
                    }
                    if (num > 0) {
                        for (int i = 0; i < num; i++) {
                            value = value.substring(0, (i + 1) * 50 + i) + "\n" + value.substring((i + 1) * 50 + i, len + i);
                        }
                    }
                    if (value.contains("\n")) {
                        int length = value.split("\n").length;
                        maxHeight = Math.max(maxHeight, length) + 1;
                    }
                }
            }
            row.setHeight((short) ((maxHeight) * DEFAULT_HEIGHT));
        }
    }

    public static class ColWidthConfig extends AbstractColumnWidthStyleStrategy {
        private final Map<Integer, Map<Integer, Integer>> cache = new HashMap<>();

        //设置列宽
        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer integer, Boolean isHead) {
            boolean needSetWidth = isHead || !(cellDataList == null || cellDataList.isEmpty());
            if (needSetWidth) {
                Map<Integer, Integer> maxColumnWidthMap = cache.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<>());

                Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
                // 单元格文本长度大于60换行
                if (columnWidth >= 0) {
                    if (columnWidth > 60) {
                        columnWidth = 60;
                    }
                    Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
                    if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                        maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
                        Sheet sheet = writeSheetHolder.getSheet();
                        sheet.setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
                    }
                }
            }
        }

        //计算长度
        private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
            if (isHead) {
                return cell.getStringCellValue().getBytes().length;
            } else {
                CellData<?> cellData = cellDataList.get(0);
                CellDataTypeEnum type = cellData.getType();
                if (type == null) {
                    return -1;
                } else {
                    switch (type) {
                        case STRING:
                            // 换行符（数据需要提前解析好）
                            int index = cellData.getStringValue().indexOf("\n");
                            return index != -1
                                    ?
                                    cellData.getStringValue().substring(0, index).getBytes().length + 1
                                    :
                                    cellData.getStringValue().getBytes().length + 1;
                        case BOOLEAN:
                            return cellData.getBooleanValue().toString().getBytes().length;
                        case NUMBER:
                            return cellData.getNumberValue().toString().getBytes().length;
                        default:
                            return -1;
                    }
                }
            }
        }
    }

    //表格基本样式
    public static HorizontalCellStyleStrategy getStyle() {
//自定义表头样式  浅橙色 居中
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());  //表头颜色
        headCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);    //文本居中
        //字体
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("微软雅黑");                                   //字体
        writeFont.setFontHeightInPoints((short) 10);                         //字体大小
        headCellStyle.setWriteFont(writeFont);
        // 自动换行
        headCellStyle.setWrapped(true);

        //内容样式
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); //文本居中
        contentCellStyle.setWriteFont(writeFont);
        //设置边框
        contentCellStyle.setBorderLeft(BorderStyle.THIN);                    //左边框线
        contentCellStyle.setBorderTop(BorderStyle.THIN);                     //顶部框线
        contentCellStyle.setBorderRight(BorderStyle.THIN);                   //右边框线
        contentCellStyle.setBorderBottom(BorderStyle.THIN);                  //底部框线
        ArrayList<WriteCellStyle> contentCells = new ArrayList<>();
        contentCells.add(contentCellStyle);
        //样式策略
        HorizontalCellStyleStrategy handler = new HorizontalCellStyleStrategy();
        handler.setHeadWriteCellStyle(headCellStyle);                        //表头样式
        handler.setContentWriteCellStyleList(contentCells);                  //内容样式
        return new HorizontalCellStyleStrategy(headCellStyle, contentCells);
    }

    //隐藏一列
    public static void colHide(Sheet sheet, int colNo) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        sheet.setColumnHidden(colNo, true); // 隐藏指定列
    }

    //设置下拉选项
    public static void colSelectOptions(Sheet sheet, int colNo, String[] options) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        if (options == null || options.length == 0) {
            throw new RuntimeException("选项列表为空");
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        CellRangeAddressList addressList = new CellRangeAddressList(2, 1048575, colNo, colNo);
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options);
        DataValidation validation = helper.createValidation(constraint, addressList);
        sheet.addValidationData(validation);
    }

    //设置数字范围限制
    public static void colLimitNumber(Sheet sheet, int colNo, Long min, Long max) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        if (min == null) {
            min = 0L;
        }
        if (max == null) {
            max = Long.MAX_VALUE;
        }
        if (max <= min) {
            max = Long.MAX_VALUE;
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        CellRangeAddressList lengthAddressList = new CellRangeAddressList(2, 1048575, colNo, colNo);
        DataValidationConstraint lengthConstraint = helper.createNumericConstraint(
                DataValidationConstraint.ValidationType.INTEGER,
                DataValidationConstraint.OperatorType.BETWEEN,
                String.valueOf(min), String.valueOf(max));
        DataValidation lengthValidation = helper.createValidation(lengthConstraint, lengthAddressList);
        lengthValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        lengthValidation.setShowErrorBox(true);
        lengthValidation.createErrorBox("错误", "请输入有效的数字");
        sheet.addValidationData(lengthValidation);
    }

    //设置日期格式限制
    public static void colLimitDate(Sheet sheet, int colNo, String format) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        if (format == null || format.isEmpty()) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        CellRangeAddressList dateAddressList = new CellRangeAddressList(2, 1048575, colNo, colNo);
        DataValidationConstraint dateConstraint = helper.createDateConstraint(
                DataValidationConstraint.OperatorType.BETWEEN,
                "1900-01-01 00:00:00", "9999-12-31 23:59:59", format);
        DataValidation dateValidation = helper.createValidation(dateConstraint, dateAddressList);
        dateValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        dateValidation.setShowErrorBox(true);
        dateValidation.createErrorBox("错误", String.format("请输入有效的日期（格式：%s）", format));
        sheet.addValidationData(dateValidation);
    }

    //设置文本长度限制
    public static void colLimitTextLength(Sheet sheet, int colNo, long minLength, long maxLength) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        if (minLength < 0) {
            minLength = 0;
        }
        if (maxLength <= minLength) {
            throw new RuntimeException("最大长度必须大于最小长度");
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        CellRangeAddressList addressList = new CellRangeAddressList(2, 1048575, colNo, colNo);
        DataValidationConstraint constraint = helper.createTextLengthConstraint(
                DataValidationConstraint.OperatorType.BETWEEN,
                String.valueOf(minLength), String.valueOf(maxLength));
        DataValidation validation = helper.createValidation(constraint, addressList);
        validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        validation.setShowErrorBox(true);
        validation.createErrorBox("错误", String.format("请输入长度在 %d 到 %d 之间的文本", minLength, maxLength));
        sheet.addValidationData(validation);
    }

    //设置正则表达式限制
    public static void colLimitRegex(Sheet sheet, int colNo, String regex) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        if (regex == null || regex.isEmpty()) {
            throw new RuntimeException("公式不能为空");
        }

        DataValidationHelper helper = sheet.getDataValidationHelper();
        CellRangeAddressList addressList = new CellRangeAddressList(2, 1048575, colNo, colNo);

        // 使用自定义公式进行验证
        DataValidationConstraint constraint = helper.createCustomConstraint(regex);
        DataValidation validation = helper.createValidation(constraint, addressList);

        // 设置错误提示
        validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        validation.setShowErrorBox(true);
        validation.createErrorBox("输入错误", String.format("正则验证失败(%s)", regex));

        sheet.addValidationData(validation);
    }

    //配置列描述信息
    public static void colDescriptions(Sheet sheet, String[] descriptions) {
        if (sheet == null) {
            throw new RuntimeException("无效表格页签");
        }
        if (descriptions == null || descriptions.length == 0) {
            throw new RuntimeException("描述信息不能为空");
        }

        Workbook workbook = sheet.getWorkbook();

        // 创建描述信息的样式
        CellStyle descriptionStyle = workbook.createCellStyle();
        descriptionStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        descriptionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        descriptionStyle.setAlignment(HorizontalAlignment.CENTER);
        descriptionStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        font.setColor(IndexedColors.BLACK.getIndex());
        descriptionStyle.setFont(font);

        // 创建描述信息行
        Row descriptionRow = sheet.createRow(1); // 表头下方的第二行
        descriptionRow.setHeightInPoints(20); // 设置行高

        for (int i = 0; i < descriptions.length; i++) {
            Cell cell = descriptionRow.createCell(i);
            cell.setCellValue(StrUtil.isBlank(descriptions[i]) ? "字段描述" : descriptions[i]);
            cell.setCellStyle(descriptionStyle);
        }

        // **不再使用 shiftRows 方法**
        // 确保数据写入时从第 3 行开始
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExcelColDef {
        //列名称
        private String name;
        //列描述
        private String desc;
        //是否隐藏列
        private Boolean hide = false;
        //下拉框选项
        private String[] options;
        //数字范围限制
        private Long[] numberRange;
        //日期格式限制
        private String dateFormat;
        //文本长度限制
        private Long[] textLengthLimit;
        //正则限制
        private String regexFormat;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExcelSheetDef {
        //页签名称
        private String name;
        //是否隐藏页签
        private Boolean hide = false;
        //页签列定义
        private List<ExcelColDef> colDefs;
        //页签数据
        private List<List<Object>> data;
    }

    //自定义列构建
    public static class CustomColHandler implements SheetWriteHandler {
        private final List<ExcelColDef> colDefs;

        public CustomColHandler(List<ExcelColDef> colDefs) {
            this.colDefs = colDefs;
        }

        @Override
        public void afterSheetCreate(SheetWriteHandlerContext context) {
            Sheet sheet = context.getWriteSheetHolder().getSheet();
            int size = colDefs.size();
            String[] descs = new String[size];
            for (int i = 0; i < size; i++) {
                descs[i] = colDefs.get(i).getDesc();
                if (descs[i] == null) {
                    descs[i] = "";
                }
                ExcelColDef colDef = colDefs.get(i);
                if (Boolean.TRUE.equals(colDef.getHide())) {
                    EasyExcelUtils.colHide(sheet, i);
                }
                if (colDef.getOptions() != null && colDef.getOptions().length > 0) {
                    EasyExcelUtils.colSelectOptions(sheet, i, colDef.getOptions());
                }
                if (colDef.getNumberRange() != null && colDef.getNumberRange().length == 2) {
                    EasyExcelUtils.colLimitNumber(sheet, i, colDef.getNumberRange()[0], colDef.getNumberRange()[1]);
                }
                if (colDef.getTextLengthLimit() != null && colDef.getTextLengthLimit().length == 2) {
                    EasyExcelUtils.colLimitTextLength(sheet, i, colDef.getTextLengthLimit()[0], colDef.getTextLengthLimit()[1]);
                }
                if (colDef.getDateFormat() != null && !colDef.getDateFormat().isEmpty()) {
                    EasyExcelUtils.colLimitDate(sheet, i, colDef.getDateFormat());
                }
                if (colDef.getRegexFormat() != null && !colDef.getRegexFormat().isEmpty()) {
                    EasyExcelUtils.colLimitRegex(sheet, i, colDef.getRegexFormat());
                }
            }
            EasyExcelUtils.colDescriptions(sheet, descs);
        }
    }


    //构建Excel文件输出配置
    public static void buildWriteSheet(ExcelWriter excelWriter, List<ExcelSheetDef> sheetDefs) {
        int i = -1;
        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
        for (ExcelSheetDef sheetDef : sheetDefs) {
            i++;
            List<List<String>> colNames = sheetDef.getColDefs().stream().map(c -> Collections.singletonList(c.getName())).collect(Collectors.toList());
            WriteSheet sheet = EasyExcel.writerSheet(sheetDef.getName()).head(colNames)
                    .registerWriteHandler(EasyExcelUtils.getStyle())
                    .registerWriteHandler(new ColWidthConfig())
                    .registerWriteHandler(new RowHeightConfig())
                    .registerWriteHandler(new CustomColHandler(sheetDef.getColDefs()))
                    .build();
            excelWriter.write(sheetDef.getData(), sheet);
            if (Boolean.TRUE.equals(sheetDef.getHide())) {
                workbook.setSheetHidden(i, true);
            }
        }
    }

    //自定义流式解析Excel文件，并且动态数据类型处理
    public static class ExcelListener extends AnalysisEventListener<LinkedHashMap> {
        List<LinkedHashMap<String, String>> cacheMap = new ArrayList<>();
        @Getter
        private final List<String> headers = new ArrayList<>();
        private final int startRow;
        private int currentRow = 0;

        private final Consumer<List<LinkedHashMap<String, String>>> post;
        private final int batchSize;

        public ExcelListener(int startRow, Consumer<List<LinkedHashMap<String, String>>> post, int batchSize) {
            this.startRow = startRow;
            this.post = post;
            if (batchSize > 0) {
                this.batchSize = batchSize;
            } else {
                this.batchSize = 100;
            }
        }

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            // 获取表头
            headers.addAll(headMap.values());
        }

        @Override
        public void invoke(LinkedHashMap data, AnalysisContext context) {
            currentRow++;
            if (currentRow >= startRow) {
                LinkedHashMap<String, String> rowMap = new LinkedHashMap<>();
                for (int i = 0; i < headers.size(); i++) {
                    String headName = headers.get(i).trim();
                    ReadCellData<?> col = (ReadCellData<?>) data.get(i);
                    if (col == null) {
                        rowMap.put(headName, null);
                        continue;
                    }
                    if (col.getType() == CellDataTypeEnum.EMPTY) {
                        rowMap.put(headName, null);
                    } else if (col.getType() == CellDataTypeEnum.NUMBER) {
                        if (col.getNumberValue() == null) {
                            rowMap.put(headName, null);
                        } else {
                            rowMap.put(headName, col.getNumberValue().toPlainString());
                        }
                    } else if (col.getType() == CellDataTypeEnum.BOOLEAN) {
                        if (col.getBooleanValue() == null) {
                            rowMap.put(headName, null);
                        } else {
                            rowMap.put(headName, col.getBooleanValue().toString());
                        }
                    } else {
                        rowMap.put(headName, col.getStringValue());
                    }
                }
                cacheMap.add(rowMap);
                if (cacheMap.size() >= batchSize) {
                    post.accept(cacheMap);
                    cacheMap.clear();
                }
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
//            int totalRows = context.readRowHolder().getRowIndex(); // 获取总行数（索引从0开始，所以需要+1）
            if (!cacheMap.isEmpty()) {
                post.accept(cacheMap);
                cacheMap.clear();
            }
        }

    }
}
