package com.msun.csm.jdbc;

import cn.hutool.core.util.StrUtil;
import com.msun.csm.jdbc.pojo.DsCfg;

import java.sql.Connection;
import java.sql.DriverManager;

public class PgsqlConn {
    /**
     * 获取数据库连接
     *
     * @param cfg
     * @param autoCommit 是否自动提交默认false
     * @return
     */
    public static Connection getConn(DsCfg cfg, boolean... autoCommit) {
        if (StrUtil.isBlank(cfg.getUrl())) {
            cfg.setUrl(PgsqlGenerator.buildUrl(cfg));
        }
        boolean ac = false;
        if (autoCommit.length > 0 && autoCommit[0]) {
            ac = true;
        }
        try {
            Class.forName(cfg.getDriver());
            Connection conn = DriverManager.getConnection(cfg.getUrl(), cfg.getUser(), cfg.getPasswd());
            conn.setAutoCommit(ac);
            return conn;
        } catch (Exception e) {
            throw new RuntimeException(StrUtil.format("连接数据源失败：{}", cfg), e);
        }
    }

    //关闭可关闭资源，处理了报错
    public static void close(AutoCloseable... resources) {
        if (null != resources) {
            for (AutoCloseable resource : resources) {
                try {
                    if (null != resource) {
                        resource.close();
                    }
                } catch (Exception ignore) {
                    // 静默
                }
            }
        }
    }

}
