package com.msun.csm.jdbc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.msun.csm.jdbc.pojo.TableDef;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

@Slf4j
public class PgsqlExecutor {

    //判断模式是否存在
    public static boolean schemaIsCreated(Connection connection, String schema) {
        try (ResultSet schemas = connection.getMetaData().getSchemas()) {
            while (schemas.next()) {
                String schemaName = schemas.getString("TABLE_SCHEM");
                if (schemaName.equalsIgnoreCase(schema)) {
                    return true;
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("检查模式是否存在失败: " + schema, e);
        }
        return false;
    }

    //建模式
    public static void createSchema(Connection connection, String schemaName) {
        String createSchemaSQL = "CREATE SCHEMA IF NOT EXISTS " + schemaName;
        try (Statement statement = connection.createStatement()) {
            statement.execute(createSchemaSQL);
        } catch (SQLException e) {
            throw new RuntimeException("创建模式失败: " + schemaName, e);
        }
    }

    //判断表是否存在
    public static boolean tableIsCreated(Connection connection, String schema, String tableName) {
        boolean tableExists = false;
        try {
            String catalog = connection.getCatalog();
            try (ResultSet rs = connection.getMetaData().getTables(catalog, schema, tableName, new String[]{"TABLE"})) {
                tableExists = rs.next();
            }
            return tableExists;
        } catch (SQLException e) {
            throw new RuntimeException(StrUtil.format("检查表是否存在失败: {}.{}", schema, tableName), e);
        }
    }

    //执行更新SQL语句，分号分割的那种
    public static void executeSql(Connection connection, String sqls) {
        try (Statement statement = connection.createStatement()) {
            // 按分号分割多条 SQL 语句
            String[] sqlStatements = sqls.split(";");
            for (String sql : sqlStatements) {
                sql = sql.trim();
                if (!sql.isEmpty()) {
                    statement.execute(sql); // 执行每条 SQL 语句
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("执行DDL失败：" + e.getMessage(), e);
        }
    }

    //执行SQL查询，流式查询，OOM概率比较低
    public static long query(Connection conn, String sql, int fetchSize, Consumer<List<LinkedHashMap<String, Object>>> consumer) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        AtomicLong total = new AtomicLong(0);
        try {
            conn.setAutoCommit(false);
            ps = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            ps.setFetchSize(fetchSize);
            rs = ps.executeQuery();
            ResultSetMetaData metaData = rs.getMetaData();
            int colNum = metaData.getColumnCount();
            List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
            while (rs.next()) {
                LinkedHashMap<String, Object> columnMap = new LinkedHashMap<>();
                for (int i = 1; i <= colNum; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    columnMap.put(columnName, value);
                }
                resultList.add(columnMap);
                if (CollUtil.size(resultList) >= fetchSize) {
                    total.addAndGet(CollUtil.size(resultList));
                    try {
                        consumer.accept(resultList);
                    } catch (Exception e) {
                        log.error("消费查询的数据出现异常", e);
                        throw new RuntimeException("消费查询的数据出现异常:" + e.getMessage(), e);
                    }
                    resultList.clear();
                }
            }
            if (CollUtil.isNotEmpty(resultList)) {
                total.addAndGet(CollUtil.size(resultList));
                try {
                    consumer.accept(resultList);
                } catch (Exception e) {
                    log.error("消费查询的数据出现异常", e);
                    throw new RuntimeException("消费查询的数据出现异常:" + e.getMessage(), e);
                }
                resultList.clear();
            }
            return total.get();
        } catch (Throwable e) {
            log.error("查询出错", e);
            throw new RuntimeException("查询出错:" + e.getMessage(), e);
        } finally {
            PgsqlConn.close(rs, ps);
        }
    }

    //获取表定义
    public static TableDef getTableDef(Connection conn, String schema, String table) {
        ResultSet tableRs = null;
        ResultSet pkRs = null;
        ResultSet idxRs = null;
        ResultSet colRs = null;
        try {
            TableDef tableDef = new TableDef();
            tableDef.setSchema(schema);
            tableDef.setName(table);
            DatabaseMetaData metaData = conn.getMetaData();
            tableRs = metaData.getTables(null, schema, table, new String[]{"TABLE"});
            pkRs = metaData.getPrimaryKeys(null, schema, table);
            idxRs = metaData.getIndexInfo(null, schema, table, false, true);
            colRs = metaData.getColumns(null, schema, table, null);
            if (tableRs.next()) {
                tableDef.setComments(tableRs.getString("REMARKS"));
            } else {
                throw new RuntimeException(StrUtil.format("表({}.{})不存在", schema, table));
            }
            List<TableDef.TableColDef> colDefs = new ArrayList<>();
            while (colRs.next()) {
                TableDef.TableColDef colDef = new TableDef.TableColDef();
                colDef.setName(colRs.getString("COLUMN_NAME"));
                colDef.setType(colRs.getString("TYPE_NAME"));
                colDef.setSize(colRs.getInt("COLUMN_SIZE"));
                colDef.setDecimalDigits(colRs.getInt("DECIMAL_DIGITS"));
                colDef.setNullable(colRs.getBoolean("IS_NULLABLE"));
                colDef.setDefaultValue(colRs.getObject("COLUMN_DEF"));
                colDef.setComments(colRs.getString("REMARKS"));
                colDefs.add(colDef);
            }
            tableDef.setColDefs(colDefs);
            List<String> pks = new ArrayList<>();
            while (pkRs.next()) {
                pks.add(pkRs.getString("COLUMN_NAME"));
            }
            tableDef.setPks(pks);
            List<String> indexs = new ArrayList<>();
            Map<String, List<String>> uniqueIndexes = new HashMap<>();

            while (idxRs.next()) {
                boolean nonUnique = idxRs.getBoolean("NON_UNIQUE");
                String indexName = idxRs.getString("INDEX_NAME");
                String columnName = idxRs.getString("COLUMN_NAME");

                // 过滤唯一索引
                if (!nonUnique && indexName != null && !pks.contains(columnName)) {
                    uniqueIndexes.computeIfAbsent(indexName, k -> new ArrayList<>()).add(columnName);
                } else if (!pks.contains(columnName)) {
                    indexs.add(idxRs.getString("COLUMN_NAME"));
                }
            }
            //万一主键也在这里面就要移除
            indexs.removeAll(pks);
            tableDef.setIndexs(indexs);
            tableDef.setUniqueIndexes(uniqueIndexes);
            return tableDef;
        } catch (Throwable e) {
            throw new RuntimeException(StrUtil.format("获取表({}.{})定义错误：{}", schema, table, e.getMessage()), e);
        } finally {
            PgsqlConn.close(tableRs, pkRs, idxRs, colRs);
        }
    }

    //批量插入更新，二分法插入，尽可能的将正确数据插入
    public static void binaryInsert(Connection conn, String sql, LinkedHashMap<String, String> colType, List<LinkedHashMap<String, Object>> data, int start, int end, AtomicReference<Throwable> errMsg, AtomicInteger sucTotal) {
        if (CollUtil.isEmpty(data) || end <= start) {
            return;
        }
        try {
            // 插入数据并统计成功数量
            int insertedCount = jdbcInsert(conn, sql, colType, data.subList(start, end));
            sucTotal.addAndGet(insertedCount);
        } catch (Throwable ex) {
            errMsg.set(ex);
            boolean binary = end - start > 1;
            if (binary) {
                int mid = (start + end) / 2;
                // 插入左半部分
                binaryInsert(conn, sql, colType, data, start, mid, errMsg, sucTotal);
                // 插入右半部分
                binaryInsert(conn, sql, colType, data, mid, end, errMsg, sucTotal);
            }
        }
    }

    //原生jdbc插入
    public static int jdbcInsert(Connection conn, String sql, LinkedHashMap<String, String> colTypes, List<LinkedHashMap<String, Object>> data) throws SQLException {
        if (CollUtil.isEmpty(data)) {
            return 0;
        }
        PreparedStatement ps = null;
        try {
            conn.setAutoCommit(false);
            ps = conn.prepareStatement(sql);
            List<String> keys = new ArrayList<>(colTypes.keySet());
            int size = keys.size();
            for (LinkedHashMap<String, Object> row : data) {
                for (int i = 0; i < size; i++) {
                    String colName = keys.get(i);
                    String colType = colTypes.get(colName);
                    Object colVal = row.get(colName);
                    if (colVal == null) {
                        switch (colType) {
                            case "varchar":
                            case "text":
                                ps.setNull(i + 1, Types.VARCHAR);
                                break;
                            case "int8":
                                ps.setNull(i + 1, Types.BIGINT);
                                break;
                            case "int4":
                            case "int2":
                                ps.setNull(i + 1, Types.INTEGER);
                                break;
                            case "decimal":
                                ps.setNull(i + 1, Types.DECIMAL);
                                break;
                            case "boolean":
                                ps.setNull(i + 1, Types.BOOLEAN);
                                break;
                            case "timestamp":
                                ps.setNull(i + 1, Types.TIMESTAMP);
                                break;
                            case "date":
                                ps.setNull(i + 1, Types.DATE);
                                break;
                            case "time":
                                ps.setNull(i + 1, Types.TIME);
                                break;
                            default:
                                throw new RuntimeException("不支持的数据类型：" + colType);
                        }
                        continue;
                    }
                    switch (colType) {
                        case "varchar":
                        case "text":
                            ps.setString(i + 1, (String) colVal);
                            break;
                        case "int8":
                            ps.setLong(i + 1, (Long) colVal);
                            break;
                        case "int4":
                        case "int2":
                            ps.setInt(i + 1, (Integer) colVal);
                            break;
                        case "decimal":
                            ps.setBigDecimal(i + 1, (BigDecimal) colVal);
                            break;
                        case "boolean":
                            ps.setBoolean(i + 1, (Boolean) colVal);
                            break;
                        case "timestamp":
                            ps.setTimestamp(i + 1, (Timestamp) colVal);
                            break;
                        case "date":
                            ps.setDate(i + 1, (java.sql.Date) colVal);
                            break;
                        case "time":
                            ps.setTime(i + 1, (Time) colVal);
                            break;
                        default:
                            throw new RuntimeException("不支持的数据类型：" + colType);
                    }
                }
                ps.addBatch();
            }
            int[] result = ps.executeBatch();
            conn.commit();
            ps.clearBatch();
            // 返回成功插入的记录数
            return (int) Arrays.stream(result).filter(r -> r >= 0).count();
        } catch (Throwable e) {
            log.error("插入数据报错", e);
            conn.rollback();
            throw new RuntimeException(e);
        } finally {
            PgsqlConn.close(ps);
        }
    }

    public static void batchDelete(Connection conn, String schemaName, String tableName, String pk, String projectNumber, Long hospitalId, Long orgId, int batchSize) {
        if (StrUtil.isBlank(pk)) {
            throw new RuntimeException(StrUtil.format("批量删除【{}.{}】数据缺少主键", schemaName, tableName));
        }
        try {
            conn.setAutoCommit(false);
            String selectSql = PgsqlGenerator.generateSelectSql(schemaName, tableName, Collections.singletonList(pk), projectNumber, hospitalId, orgId) + " LIMIT ?";
            String deleteSql = PgsqlGenerator.generateDeleteSql(schemaName, tableName, projectNumber, hospitalId, orgId) + " AND " + pk + " = ?";

            while (true) {
                List<Object> idsToDelete = new ArrayList<>();

                // 分批查询
                try (PreparedStatement selectStmt = conn.prepareStatement(selectSql)) {
                    selectStmt.setInt(1, batchSize);
                    try (ResultSet rs = selectStmt.executeQuery()) {
                        while (rs.next()) {
                            idsToDelete.add(rs.getObject(1));
                        }
                    }
                }

                // 如果没有数据，退出循环
                if (idsToDelete.isEmpty()) {
                    break;
                }

                // 分批删除
                try (PreparedStatement deleteStmt = conn.prepareStatement(deleteSql)) {
                    for (Object id : idsToDelete) {
                        deleteStmt.setObject(1, id);
                        deleteStmt.addBatch();
                    }
                    deleteStmt.executeBatch();
                }

                conn.commit();
                log.info("成功删除 {} 条记录", idsToDelete.size());
            }
        } catch (Exception e) {
            try {
                conn.rollback();
            } catch (Exception ignored) {
            }
            throw new RuntimeException("分批删除数据失败：" + e.getMessage(), e);
        }
    }
}
