package com.msun.csm.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.ResultEnum.VALIDATE_NULL_OR_EMPTY;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.function.Function;

import org.springframework.web.multipart.MultipartFile;

import com.alibaba.nacos.client.naming.utils.RandomUtils;
import com.msun.csm.common.exception.CustomException;


/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/12/28
 */
@Slf4j
public class FileUtil extends org.apache.tomcat.util.http.fileupload.FileUtils {
    public static String uploadFiles(MultipartFile file, String path) throws IOException {
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            throw new CustomException(VALIDATE_NULL_OR_EMPTY);
        }
        String fullFilePath = path + Md5Util.md5(fileName) + fileName.substring(fileName.lastIndexOf(StrUtil.DOT));
        cn.hutool.core.io.FileUtil.writeFromStream(file.getInputStream(), fullFilePath);
        return fullFilePath;
    }

    /**
     * 递归遍历文件夹下所有文件
     *
     * @param file
     * @param path
     */
    public static void recursionFiles(File file, String path) {
        for (File f : Objects.requireNonNull(file.listFiles())) {
            if (f.isDirectory()) {
                recursionFiles(f, path);
            } else {
                try {
                    File folder = new File(path);
                    if (!folder.exists()) {
                        folder.mkdirs();
                    }
                    FileInputStream fis = new FileInputStream(f.getAbsolutePath());
                    FileOutputStream fos = new FileOutputStream(path + f.getName());
                    byte[] b = new byte[1024];
                    int len;
                    while ((len = fis.read(b)) != -1) {
                        fos.write(b, 0, len);
                    }
                    fos.close();
                    fos.flush();
                    fis.close();
                } catch (IOException e) {
                    log.error("recursionFiles，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 随机获取背景图片
     *
     * @param captchaPath 验证码背景图保存路径
     * @return
     */
    public static File getSourceImage(String captchaPath) {
        String[] list = new File(captchaPath).list();
        String filename = "";
        if (list != null && list.length > 0) {
            filename = list[RandomUtils.nextInt(list.length)];
        }
        return new File(captchaPath + File.separator + filename);
    }

    /**
     * 获取当前jar包目录
     *
     * @return
     */
    public static String getJarFilePath() {
        try {
            // 获取jar包所在目录路径
            String jarPath = new File(FileUtil.class
                    .getProtectionDomain()
                    .getCodeSource()
                    .getLocation()
                    .toURI())
                    .getParentFile()
                    .getAbsolutePath();

            System.out.println("JAR包所在目录: " + jarPath);
            return jarPath;
        } catch (URISyntaxException e) {
            throw new CustomException("获取Jar包路径失败");
        }
    }

    /**
     * 删除directoryPath目录下，cutoffDate之前的文件
     *
     * @param directoryPath 删除根目录
     * @param cutoffDate    cutoffDate天之前的
     * @param dirRegExp     只删除匹配的目录下的内容
     */
    public static void recClearLonglongAgoLogFiles(String directoryPath, LocalDateTime cutoffDate, String... dirRegExp) {
        // 遍历目录
        File directory = new File(directoryPath);
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    try {

                        if (Boolean.FALSE.equals(file.exists())) {
                            continue;
                        }
                        BasicFileAttributes attrs = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                        LocalDateTime creationTime = attrs.creationTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                        if (Boolean.TRUE.equals(file.isDirectory())) {
                            if (dirRegExp != null && dirRegExp.length > 0 && StrUtil.isNotBlank(dirRegExp[0])) {
                                if (Boolean.FALSE.equals(file.getName().matches(dirRegExp[0]))) {
                                    //如果目录名称不符合正则表达式，那么就跳过
                                    continue;
                                }
                            }
                            recClearLonglongAgoLogFiles(file.getAbsolutePath(), cutoffDate);
                            // 检查文件的创建时间是否大于截止日期
                            if (creationTime.isBefore(cutoffDate)) {
                                // 删除文件
                                boolean deleted = file.delete();
                                log.warn("目录【{}】删除结果：{}", file.getAbsolutePath(), deleted);
                            }
                        } else {
                            // 检查文件的创建时间是否大于截止日期
                            if (creationTime.isBefore(cutoffDate)) {
                                // 删除文件
                                boolean deleted = file.delete();
                                log.warn("文件【{}】删除结果：{}", file.getAbsolutePath(), deleted);
                            }
                        }
                    } catch (IOException e) {
                        log.error("递归删除文件【{}】报错：", file.getAbsolutePath(), e);
                    }
                }
            }
        } else {
            try {
                BasicFileAttributes attrs = Files.readAttributes(directory.toPath(), BasicFileAttributes.class);
                LocalDateTime creationTime = attrs.creationTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                if (creationTime.isBefore(cutoffDate)) {
                    // 删除文件
                    boolean deleted = directory.delete();
                    log.warn("跟文件【{}】删除结果：{}", directory.getAbsolutePath(), deleted);
                }
            } catch (IOException e) {
                log.error("递归删除根文件【{}】报错：", directory.getAbsolutePath(), e);
            }
        }
    }

    public static void recDeleteFileConfirm(String directoryPath, Function<File, Boolean> confirm) {
        // 遍历目录
        File directory = new File(directoryPath);
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    try {
                        if (Boolean.FALSE.equals(file.exists())) {
                            continue;
                        }
                        if (Boolean.TRUE.equals(file.isDirectory())) {
                            recDeleteFileConfirm(file.getAbsolutePath(), confirm);
                        } else {
                            if (confirm.apply(file)) {
                                // 删除文件
                                boolean deleted = directory.delete();
                                log.warn("确认删除文件【{}】删除结果：{}", directory.getAbsolutePath(), deleted);
                            }
                        }
                    } catch (Throwable e) {
                        log.error("递归删确认除文件【{}】报错：", file.getAbsolutePath(), e);
                    }
                }
            }
        } else {
            if (confirm.apply(directory)) {
                // 删除文件
                boolean deleted = directory.delete();
                log.warn("确认删除文件【{}】删除结果：{}", directory.getAbsolutePath(), deleted);
            }
        }
    }


    public static void main(String[] args) {

        String pt = "D:\\worker\\msun\\projects\\csm-server\\logs\\20250604";

        File f = new File(pt);
        System.out.println(f.getName());
        System.out.println(f.getName().matches("^\\d+$"));
        // 删除交付平台的日志
//        String csmLogsPath = "D:\\worker\\msun\\projects\\csm-server\\logs";
//        FileUtil.recClearLonglongAgoLogFiles(csmLogsPath, LocalDateTime.now().minus(30, ChronoUnit.DAYS));

    }

}
