package com.msun.csm.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class HttpClientUtils {

    private static final int TIMEOUT = 20000;

    public static HttpResponse cliExe(HttpPost httpPost) {
        CloseableHttpResponse response = null;
        CloseableHttpClient client = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(TIMEOUT)
                .setConnectTimeout(TIMEOUT)
                .setConnectionRequestTimeout(TIMEOUT)
                .build();
        httpPost.setConfig(requestConfig);
        try {
            response = client.execute(httpPost);
            return response;
        } catch (Exception e) {
            log.error("cliExe，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return null;
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
    }


    /**
     * 发送 post请求访问本地应用并根据传递参数不同返回不同结果
     */
    public String post(Map<String, String> param, String doman) throws Exception {
        /*
         * 参数形式：
         * {
         *  systemId: "implementation"  //systemId
         *  secret: "mKptt4TsedWJbELr"  //云健康鉴权密钥
         *  address："http://gwarea-graytest.msunhis.com"  //项目地址
         *  methodUrl："/msun-phpg-app-nbphs/nbphs/delivery/saveAddress" //项目路径
         *  body："{pid:"371728",name:"xxx乡"}"  //接口请求参数的JSON格式
         * }
         *
         */
        //处理参数
        String resourcePoolAddress = param.get("address");
        String resourcePoolMethodUrl = param.get("methodUrl");
        String body = param.get("body");
        String hospitalId = Convert.toStr(param.get("hospitalId"));
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httppost
        HttpPost httppost = new HttpPost(doman + resourcePoolMethodUrl);
        //设置参数体
        httppost.setHeader("Content-Type", "application/json;charset=utf8");
        httppost.setHeader("gateway", "app");
        httppost.setHeader("Authorization", this.createAuthorization(param));
        httppost.setHeader("userSign", "1");
        if (ObjectUtil.isNotEmpty(hospitalId)) {
            httppost.setHeader("hospitalId", hospitalId);
        }
        //装填参数
        StringEntity s = new StringEntity(body, "utf-8");
        //设置参数到请求对象中
        httppost.setEntity(s);
        CloseableHttpResponse response = httpclient.execute(httppost);
        try {
            HttpEntity entity = response.getEntity();
            String reponseResult = null;
            if (entity != null) {
                reponseResult = EntityUtils.toString(entity, "UTF-8");
            }
            return reponseResult;
        } finally {
            response.close();
            httpclient.close();
        }
    }

    /**
     * 发送 get请求访问本地应用并根据传递参数不同返回不同结果
     */

    public static String get(String url, Map<String, String> paramMap) throws IOException {
        HttpClient httpClient = new DefaultHttpClient();
        HttpGet httpGet = new HttpGet();
        List<NameValuePair> formparams = setHttpParams(paramMap);
        String param = URLEncodedUtils.format(formparams, "UTF-8");
        httpGet.setURI(URI.create(url + "?" + param));
        HttpResponse response = httpClient.execute(httpGet);
        String httpEntityContent = getHttpEntityContent(response);
        httpGet.abort();
        return httpEntityContent;
    }

    private static List<NameValuePair> setHttpParams(Map<String, String> paramMap) {
        List<NameValuePair> formparams = new ArrayList<NameValuePair>();
        Set<Map.Entry<String, String>> set = paramMap.entrySet();
        for (Map.Entry<String, String> entry : set) {
            formparams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        return formparams;
    }

    private static String getHttpEntityContent(HttpResponse response) throws IOException {
        //通过HttpResponse 的getEntity()方法获取返回信息
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            InputStream is = entity.getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
            String line = br.readLine();
            StringBuilder sb = new StringBuilder();
            while (line != null) {
                sb.append(line + "\n");
                line = br.readLine();
            }
            br.close();
            is.close();
            return sb.toString();
        }
        return "";
    }
//    public String get(Map<String, String> param, Map<String, String> param22) throws Exception {
//        //处理参数
//        String resourcePoolAddress = param.get("address");
//        String resourcePoolMethodUrl = param.get("methodUrl");
//
//        String body = param.get("body");
//
//        // 创建默认的httpClient实例.
//        CloseableHttpClient httpclient = HttpClients.createDefault();
//        // 创建httppost
//        HttpGet httppost = new HttpGet(resourcePoolAddress + resourcePoolMethodUrl);
//        //设置参数体
//        httppost.setHeader("Content-Type", "application/json;charset=utf8");
//        httppost.setHeader("gateway", "app");
//        httppost.setHeader("Authorization", generateAuthorization(param,body));
//        httppost.setHeader("userSign", "1");
//        for (Map.Entry<String, String> entry : param22.entrySet()) {
//            httppost.setHeader(entry.getKey(), MothedSet.getObjectToString(entry.getValue()));
//        }
//        CloseableHttpResponse response = httpclient.execute(httppost);
//        try {
//            HttpEntity entity = response.getEntity();
//            String reponseResult = null;
//            if (entity != null) {
//                reponseResult = EntityUtils.toString(entity, "UTF-8");
//            }
//            return reponseResult;
//        } finally {
//            response.close();
//            httpclient.close();
//        }
//    }

    public static String createAuthorization(Map<String, String> param) {
        String body = param.get("body");
        String signType = "md5";
        String timestamp = String.valueOf(System.currentTimeMillis());
        HashMap<String, Object> clientInfoMap = new HashMap<>(2);
        clientInfoMap.put("systemId", param.get("systemId"));
        String clientInfo = JSON.toJSONString(clientInfoMap);
        StringBuilder plainText = new StringBuilder();
        plainText.append(signType).append(".");
        plainText.append(timestamp).append(".");
        plainText.append(clientInfo).append(".");
        plainText.append(body).append(".");
        plainText.append(param.get("secret"));
        String sign = DigestUtils.md5DigestAsHex(plainText.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase();
        log.info("plainText:" + plainText.toString());
        StringBuilder authorization = new StringBuilder();
        authorization.append(Base64Encoder.encode(signType.getBytes())).append(".");
        authorization.append(Base64Encoder.encode(timestamp.getBytes())).append(".");
        authorization.append(Base64Encoder.encode(clientInfo.getBytes())).append(".");
        authorization.append(Base64Encoder.encode(sign.getBytes()));
        log.info("authorization:" + authorization.toString());
        return authorization.toString();
    }

/*   public static void main(String[] args)throws Exception {
        Map<String, String> param=new HashMap<>();
        param.put("systemId","implementation");
        param.put("body","{\"hospitalId\":\"10608003\",\"hospitalIdList\":[\"469927888117780480\"],\"orgId\":\"10608\"}");
        param.put("secret","mKptt4TsedWJbELr");
    }*/

    /**
     * 生成鉴权信息
     *
     * @param param 请求字符串
     * @param body  请求体信息
     * @return 鉴权信息
     */
    private String generateAuthorization(Map<String, String> param, String body) {
        java.util.Base64.Encoder encoder = java.util.Base64.getEncoder();
        String signType = "md5";
        String timestamp = String.valueOf(System.currentTimeMillis());
        HashMap<String, Object> clientInfoMap = new HashMap<>(2);
        clientInfoMap.put("systemId", param.get("systemId"));
        //这里的lis换成自己的systemId，其他信息换成实际的信息
        String clientInfo = JSON.toJSONString(clientInfoMap);
        StringBuilder plainText = new StringBuilder();
        plainText.append(signType).append(".");
        plainText.append(timestamp).append(".");
        plainText.append(clientInfo).append(".");
        plainText.append(StringUtils.isEmpty(body) ? "" : body).append(".");
        //这里的5hAwXtYdsjsp91Ic是secret，换成自己的secret
        plainText.append(param.get("secret"));
        String sign = DigestUtils.md5DigestAsHex(plainText.toString().getBytes()).toUpperCase();
        //log.info("FeignInterceptor.createAuthorization.sign,plainText={},md5={}", plainText, sign);
        StringBuilder authorization = new StringBuilder();
        authorization.append(encoder.encode(signType.getBytes())).append(".");
        authorization.append(encoder.encode(timestamp.getBytes())).append(".");
        authorization.append(encoder.encode(clientInfo.getBytes())).append(".");
        authorization.append(encoder.encode(sign.getBytes()));
        //log.info("FeignInterceptor.createAuthorization.plain,plainText={}", authorization);
        return authorization.toString().replace("\r\n", "");
    }

    /**
     * 发送 post请求访问本地应用并根据传递参数不同返回不同结果
     */
    public String post(Map<String, String> param) throws Exception {
        /*
         * 参数形式：
         * {
         *  systemId: "implementation"  //systemId
         *  secret: "mKptt4TsedWJbELr"  //云健康鉴权密钥
         *  address："http://gwarea-graytest.msunhis.com"  //项目地址
         *  methodUrl："/msun-phpg-app-nbphs/nbphs/delivery/saveAddress" //项目路径
         *  body："{pid:"371728",name:"xxx乡"}"  //接口请求参数的JSON格式
         * }
         *
         */
        //处理参数
        String resourcePoolAddress = param.get("address");
        String resourcePoolMethodUrl = param.get("methodUrl");
        String body = param.get("body");
        String hospitalId = Convert.toStr(param.get("hospitalId"));
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httppost
        HttpPost httppost = new HttpPost(resourcePoolAddress + resourcePoolMethodUrl);
        //设置参数体
        httppost.setHeader("Content-Type", "application/json;charset=utf8");
        httppost.setHeader("gateway", "app");
        httppost.setHeader("Authorization", createAuthorization(param));
        httppost.setHeader("userSign", "1");
        if (StrUtil.isNotBlank(hospitalId)) {
            httppost.setHeader("hospitalId", hospitalId);
        }
        //装填参数
        StringEntity s = new StringEntity(body, "utf-8");
        //设置参数到请求对象中
        httppost.setEntity(s);
        CloseableHttpResponse response = httpclient.execute(httppost);
        try {
            HttpEntity entity = response.getEntity();
            String reponseResult = null;
            if (entity != null) {
                reponseResult = EntityUtils.toString(entity, "UTF-8");
            }
            return reponseResult;
        } finally {
            response.close();
            httpclient.close();
        }
    }

    public String get(Map<String, String> param) throws Exception {
        /*
         * 参数形式：
         * {
         *  systemId: "implementation"  //systemId
         *  secret: "mKptt4TsedWJbELr"  //云健康鉴权密钥
         *  address："http://gwarea-graytest.msunhis.com"  //项目地址
         *  methodUrl："/msun-phpg-app-nbphs/nbphs/delivery/saveAddress" //项目路径
         *  body："{pid:"371728",name:"xxx乡"}"  //接口请求参数的JSON格式
         * }
         *
         */
        //处理参数
        String resourcePoolAddress = param.get("address");
        String resourcePoolMethodUrl = param.get("methodUrl");
        String hospitalId = Convert.toStr(param.get("hospitalId"));
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httppost
        HttpGet httpGet = new HttpGet(resourcePoolAddress + resourcePoolMethodUrl);
        //设置参数体
        httpGet.setHeader("gateway", "app");
        httpGet.setHeader("Authorization", createAuthorization(param));
        httpGet.setHeader("userSign", "1");
        if (StrUtil.isNotBlank(hospitalId)) {
            httpGet.setHeader("hospitalId", hospitalId);
        }
        CloseableHttpResponse response = httpclient.execute(httpGet);
        try {
            HttpEntity entity = response.getEntity();
            String reponseResult = null;
            if (entity != null) {
                reponseResult = EntityUtils.toString(entity, "UTF-8");
            }
            return reponseResult;
        } finally {
            response.close();
            httpclient.close();
        }
    }


    /**
     * 发送 post请求访问本地应用并根据传递参数不同返回不同结果
     */
    public String post2(Map<String, String> param, String domain) throws Exception {
        RequestConfig requestConfig = RequestConfig
                .custom()
                // 设置连接超时
                .setConnectTimeout(5000)
                // 设置Socket超时
                .setSocketTimeout(5000)
                // 设置请求超时
                .setConnectionRequestTimeout(5000)
                .build();

        //处理参数
        String resourcePoolMethodUrl = param.get("methodUrl");
        String body = param.get("body");
        String hospitalId = Convert.toStr(param.get("hospitalId"));
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();

        String url = domain + resourcePoolMethodUrl;

        log.info("调用医保核心顶层服务更新数据，真实请求地址={}", url);
        HttpPost httpPost = new HttpPost(url);
        // 配置
        httpPost.setConfig(requestConfig);
        // 设置参数体
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setHeader("gateway", "app");
        httpPost.setHeader("Authorization", createAuthorization(param));
        httpPost.setHeader("userSign", "1");
        if (ObjectUtil.isNotEmpty(hospitalId)) {
            httpPost.setHeader("hospitalId", hospitalId);
        }
        // 装填参数
        StringEntity s = new StringEntity(body, "utf-8");
        //设置参数到请求对象中
        httpPost.setEntity(s);
        CloseableHttpResponse response = httpclient.execute(httpPost);
        try {
            HttpEntity entity = response.getEntity();
            String reponseResult = null;
            if (entity != null) {
                reponseResult = EntityUtils.toString(entity, "UTF-8");
            }
            return reponseResult;
        } finally {
            response.close();
            httpclient.close();
        }
    }
}
