package com.msun.csm.util;

import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;

import org.apache.commons.collections4.CollectionUtils;

/**
 * @Description //TODO
 * @Date 2023/7/31 9:49
 * <AUTHOR>
 **/
public class ListUtils {

    /**
     * 将一个大List分批成N个小List
     *
     * @param source    源List
     * @param stageSize 每批大小
     * @param cb        回调函数
     * @param <T>
     */
    public static <T> void chunked(List<T> source, int stageSize, Consumer<List<T>> cb) {
        int total = source.size();
        List<T> arr = null;
        for (int i = 0; i < total; i++) {
            if (i % stageSize == 0) {
                if (CollectionUtils.isNotEmpty(arr)) {
                    cb.accept(arr);
                }
                arr = new ArrayList<>();
            }
            arr.add(source.get(i));
        }
        if (CollectionUtils.isNotEmpty(arr)) {
            cb.accept(arr);
        }
    }

    public static <T> void chunkedWithSubIndex(List<T> source, int stageSize, Consumer<List<T>> cb) {
        if (CollUtil.isEmpty(source)) {
            return;
        }
        if (stageSize < 1) {
            throw new RuntimeException("批次大小必须大于0");
        }
        double total = source.size();
        int batchs = (int) Math.ceil(total / stageSize);
        for (int i = 0; i < batchs; i++) {
            if (i == batchs - 1) {
                cb.accept(source.subList(i * stageSize, (int) total));
            } else {
                cb.accept(source.subList(i * stageSize, (i + 1) * stageSize));
            }
        }
    }

    public static <T> List<T> reverse(List<T> source) {
        List<T> arr = new ArrayList<>();
        int total = source.size();
        for (int i = total - 1; i >= 0; i--) {
            arr.add(source.get(i));
        }
        return arr;
    }

    public static <T> List<T> intersection(List<List<T>> list) {
        //将第一个数组的值赋给set
        Set<T> res = new HashSet<>(list.get(0));
        //求前i个数组的交集
        for (int i = 1; i < list.size(); i++) {
            Set<T> temp = new HashSet<>();
            for (int j = 0; j < list.get(i).size(); j++) {
                if (res.contains(list.get(i).get(j))) {
                    temp.add(list.get(i).get(j));
                }
            }
            res = temp;
        }
        return new ArrayList<>(res);
    }

    /**
     * 如果需要检测的图片过多，按照6个一组进行分组检测
     *
     * @param originalImageList 需要分组的图片
     * @param groupSize         每组的图片数量
     * @return 分组结果
     */
    public static <T> List<List<T>> splitImageList(List<T> originalImageList, int groupSize) {
        List<List<T>> groupedList = new ArrayList<>();
        // 遍历原始列表，将其拆分成子列表
        for (int i = 0; i < originalImageList.size(); i += groupSize) {
            // 确保不会超出范围
            int end = Math.min(i + groupSize, originalImageList.size());
            groupedList.add(originalImageList.subList(i, end));
        }
        return groupedList;
    }
}
