package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * 项目计划工作项字典
 */
@Getter
public enum ReviewTypeEnum {

    PRODUCT_SUBMIT_AUDIT("ywdysh", "产品业务调研提交调研审核", "proj_survey_plan", "产品业务调研"),
    PRODUCT_RESUBMIT_FOR_REVIEW("ywdybhtj", "产品业务调研审核驳回后重新提交", "proj_survey_plan", "产品业务调研"),

    FORM_SUBMIT_AUDIT("bddysh", "表单调研提交调研审核", "proj_survey_form", "表单调研"),
    FORM_RESUBMIT_FOR_REVIEW("bddybhtj", "表单调研审核驳回后重新提交", "proj_survey_form", "表单调研"),

    PRINT_REPORT_SUBMIT_AUDIT("dybbdysh", "打印报表调研提交调研审核", "proj_survey_report", "打印报表调研"),
    PRINT_REPORT_RESUBMIT_FOR_REVIEW("dybbbhtj", "打印报表调研审核驳回后重新提交", "proj_survey_report", "打印报表调研"),

    STATISTICAL_REPORT_SUBMIT_AUDIT("tjbbcd", "统计报表调研提交裁定", "proj_statistical_report_main", "统计报表调研"),
    STATISTICAL_REPORT_RESUBMIT_FOR_REVIEW("tjbbcdbhtj", "统计报表调研裁定驳回后重新提交", "proj_statistical_report_main", "统计报表调研"),

    THIRD_INTERFACE_SUBMIT_AUDIT("jkcd", "三方接口调研提交裁定", "proj_third_interface", "三方接口调研"),
    THIRD_INTERFACE_RESUBMIT_FOR_REVIEW("jkcdbhtj", "三方接口调研裁定驳回后重新提交", "proj_third_interface", "三方接口调研"),

    SCHEME_NEWWORK_SUBMIT_AUDIT("wlgzfash", "网络改造方案提交审核", "proj_project_plan", "网络改造方案"),
    SCHEME_NEWWORK_RESUBMIT_FOR_REVIEW("wlgzfashbhtj", "网络改造方案审核驳回后重新提交", "proj_project_plan", "网络改造方案"),

    LIS_EQUIP_SUBMIT_AUDIT("lisjksh", "LIS设备调研提交审核", "proj_equip_record", "LIS设备调研"),
    LIS_EQUIP_RESUBMIT_FOR_REVIEW("lisjkshbhtj", "LIS设备调研审核驳回后重新提交", "proj_equip_record", "LIS设备调研"),
    LIS_EQUIP_MAKE_FOR_DG("lisjkzz-dg", "LIS设备接口制作-单工", "proj_equip_record", "LIS设备接口制作-单工"),
    LIS_EQUIP_MAKE_FOR_SG("lisjkzz-sg", "LIS设备接口制作-双工", "proj_equip_record", "LIS设备接口制作-双工"),
    ;

    /**
     * 审核类型编码，与 dict_project_review_type 的 dict_code 字段一致
     */
    private final String reviewTypeCode;

    /**
     * 描述
     */
    private final String description;

    /**
     * 业务表名
     */
    private final String businessTable;

    /**
     * 业务名称
     */
    private final String businessName;

    ReviewTypeEnum(String reviewTypeCode, String description, String businessTable, String businessName) {
        this.reviewTypeCode = reviewTypeCode;
        this.description = description;
        this.businessTable = businessTable;
        this.businessName = businessName;
    }


}
