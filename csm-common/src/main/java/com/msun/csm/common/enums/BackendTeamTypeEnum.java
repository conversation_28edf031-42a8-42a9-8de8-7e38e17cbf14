package com.msun.csm.common.enums;

import lombok.Getter;

@Getter
public enum BackendTeamTypeEnum {

    /**
     * 业务服务团队
     */
    BUSINESS_TEAM("bustype", "业务服务团队", 3),

    /**
     * 数据服务团队
     */
    DATA_TEAM("datatype", "数据服务团队", 1),

    /**
     * 接口服务团队
     */
    INTERFACE_TEAM("interfacetype", "接口服务团队", 2);

    BackendTeamTypeEnum(String code, String name, Integer yyOrgType) {
        this.code = code;
        this.name = name;
        this.yyOrgType = yyOrgType;
    }

    /**
     * 服务团队编码，对应knowledge.dict_know_common表的dict_code
     */
    private final String code;

    /**
     * 团队类型描述
     */
    private final String name;

    /**
     * 同步后端运维评分给运营平台时，运营平台需要的编码
     */
    private final Integer yyOrgType;

    public static BackendTeamTypeEnum getBackendTeamTypeEnumByCode(String code) {
        for (BackendTeamTypeEnum backendTeamTypeEnum : BackendTeamTypeEnum.values()) {
            if (backendTeamTypeEnum.code.equals(code)) {
                return backendTeamTypeEnum;
            }
        }
        throw new IllegalArgumentException("非法的后端服务团队类型，当前类型编码=" + code);
    }
}
