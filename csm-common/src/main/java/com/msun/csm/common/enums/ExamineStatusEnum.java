package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * 项目计划工作项字典
 */
@Getter
public enum ExamineStatusEnum {
    SUBMIT_BACKEND_REVIEW(0, "提交后端审核"),
    BACKEND_PASSED(1, "后端审核通过"),
    BACKEND_REJECTION(2, "后端审核驳回"),
    MADE_DONE(20, "制作完成"),
    VERIFY_PASSED(21, "验证通过"),
    VERIFY_REJECTION(22, "验证驳回");


    /**
     * 日志表状态
     */
    private final Integer examineStatusCode;

    /**
     * 描述
     */
    private final String examineStatusDesc;


    ExamineStatusEnum(Integer examineStatusCode, String examineStatusDesc) {
        this.examineStatusCode = examineStatusCode;
        this.examineStatusDesc = examineStatusDesc;
    }


}
