package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * 项目计划工作项字典
 */
@Getter
public enum FormClassEnum {

    PRODUCT_SURVEY(1, 10001L, "产品业务调研"),
    SATISFACTION_SURVEY(3, 30001L, "满意度调查");


    /**
     * 表单类型
     */
    private final Integer formClass;


    /**
     * 表单文件夹ID
     */
    private final Long folderId;

    /**
     * 业务名称
     */
    private final String business;


    FormClassEnum(Integer formClass, Long folderId, String business) {
        this.formClass = formClass;
        this.folderId = folderId;
        this.business = business;
    }


}
